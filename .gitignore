.vscode/
.idea/
pkg
.DS_Store
go.sum
supply-chain/conf/*
auto-deploy/packge/*
ssh.json
supply-chain/test/http-client.env.json
!supply-chain/conf/config.yaml
supply-chain/latest_log
supply-chain/supply
supply-chain/docker-compose.yaml
supply-chain/config.yaml
supply-chain/cmd/var/master.info
supply-chain/uploads/
supply-chain/chunk/
supply-chain/test.sh
supply-chain/supply-chain
supply-chain/data
*.log
*.pem
temp/
.run/
uploads/
supply-chain/uploads/file
uploads/*

shop-side-shop/.env.development
gocron-master/
cron-master/
ssh.json

go.work
go.work.sum

node_modules/
.vite/
/bin/staticcheck.exe
demo.code-workspace
