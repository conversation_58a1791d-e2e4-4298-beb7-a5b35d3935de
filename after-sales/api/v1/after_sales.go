package v1

import (
	"after-sales/model"
	mq2 "after-sales/mq"
	"after-sales/request"
	"after-sales/service"
	"errors"
	"fmt"
	ufv1 "gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"
	"notification/mq"
	model3 "order/model"
	supplierService "supplier/service"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/source"
)

// RetryMessage
// @Tags 售后
// @Summary 消息重试 目前只有创建消息
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "消息重试 目前只有创建消息"
// @Success 200 {object} model.AfterSales
// @Router /api/afterSales/retryMessage [POst]
func RetryMessage(c *gin.Context) {
	var ra model.AfterSales
	err := c.ShouldBindJSON(&ra)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if ra.ID == 0 {
		yzResponse.FailWithMessage("请提交重试的售后id", c)
		return
	}
	err = mq2.PublishMessage(ra.ID, mq2.AfterSalesCreate, 0)
	if err != nil {
		yzResponse.FailWithMessage("消息重新推送失败"+err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("消息重新推送成功", c)

}

// FindAfterSales
// @Tags 售后
// @Summary 用id查询售后信息
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "用id查询售后信息"
// @Success 200 {object} model.AfterSales
// @Router /api/afterSales/get [get]
func FindAfterSales(c *gin.Context) {
	var ra model.AfterSales
	err := c.ShouldBindQuery(&ra)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, read := service.GetAfterSalesAdmin(ra.ID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"read": read}, c)
	}
}

// FindAfterSalesByOrderItemId
// @Tags 售后
// @Summary 用order_item_id查询售后信息
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "用id查询售后信息"
// @Success 200 {object} model.AfterSales
// @Router /api/afterSales/getByOrderItemId [get]
func FindAfterSalesByOrderItemId(c *gin.Context) {
	var ra model.AfterSales
	err := c.ShouldBindQuery(&ra)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, read := service.GetAfterSalesByOrderItemId(ra.OrderItemID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"read": read}, c)
	}
}

// GetAfterSalesList
// @Tags 售后
// @Summary 分页获取列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body refund.AfterSalesSearch true "分页获取列表"
// @Success 200 {object} yzResponse.PageResult
// @Router /api/afterSales/list [get]
func SupplierGetAfterSalesList(c *gin.Context) {
	var pageInfo request.AfterSalesSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplier := service.GetSupplierByUserId(ufv1.GetUserID(c))
	if err != nil {
		log.Log().Error("供应商不存在", zap.Any("err", err))
		yzResponse.FailWithMessage("供应商不存在", c)
		return
	}
	pageInfo.SupplierId = &supplier.ID
	if err, list, total, afterSalesStatusCounts := service.GetAfterSalesList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{"list": list,
			"total":                  total,
			"page":                   pageInfo.Page,
			"pageSize":               pageInfo.PageSize,
			"afterSalesStatusCounts": afterSalesStatusCounts,
		}, "获取成功", c)
	}
}

// GetAfterSalesList
// @Tags 售后
// @Summary 分页获取列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body refund.AfterSalesSearch true "分页获取列表"
// @Success 200 {object} yzResponse.PageResult
// @Router /api/afterSales/list [get]
func GetAfterSalesList(c *gin.Context) {
	var pageInfo request.AfterSalesSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total, afterSalesStatusCounts := service.GetAfterSalesList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{"list": list,
			"total":                  total,
			"page":                   pageInfo.Page,
			"pageSize":               pageInfo.PageSize,
			"afterSalesStatusCounts": afterSalesStatusCounts,
		}, "获取成功", c)
	}
}

// CreateAfterSales
// @Tags 售后
// @Summary 创建售后申请
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Ad true "创建售后申请"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/afterSales/create [post]
func CreateAfterSales(c *gin.Context) {
	var requestCreateAfterSales request.AfterSales
	err := c.ShouldBindJSON(&requestCreateAfterSales)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if requestCreateAfterSales.Amount == 0 {
		yzResponse.FailWithMessage("请填写退款金额", c)
		return
	}

	var orderItem model3.OrderItem
	err = source.DB().Where("id = ?", requestCreateAfterSales.OrderItemID).First(&orderItem).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("请选择要售后的商品", c)
		return
	}
	if orderItem.CanRefund == 0 {
		log.Log().Error("已退款无法进行售后", zap.Any("err", err))
		yzResponse.FailWithMessage("商品已退款,无需售后", c)
		return
	}
	if orderItem.Amount < requestCreateAfterSales.Amount {
		log.Log().Error("退款金额大于商品金额", zap.Any("err", err))
		yzResponse.FailWithMessage("退款金额大于商品金额", c)
		return
	}
	uid := orderItem.UserID
	err, afterSalesData := service.GetAfterSalesByOrderItemId(requestCreateAfterSales.OrderItemID)
	//如果查询到记录并且记录未被关闭 则新建待审核的审核记录，如果已关闭则全部新建
	if err == nil && afterSalesData.Status != model.ClosedStatus {
		//如果是完成则直接返回
		if afterSalesData.Status == model.CompletedStatus {
			err, statusName := model.GetStatusName(afterSalesData.Status)
			err = errors.New(fmt.Sprintf("售后状态:%s", statusName))
			log.Log().Error("售后申请", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}

		err, AfterSalesAuditData := service.GetAfterSalesAuditByAfterSalesId(afterSalesData.ID)
		if err != nil {
			log.Log().Error("获取审核记录失败", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		if AfterSalesAuditData.Status == model.PendingStatus {
			log.Log().Error("已存在申请", zap.Any("err", AfterSalesAuditData))
			err, statusName := model.GetAuditStatusName(AfterSalesAuditData.Status)
			err = errors.New(fmt.Sprintf("%s", statusName))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}

		afterSalesData.Amount = requestCreateAfterSales.Amount
		afterSalesData.ReasonType = requestCreateAfterSales.ReasonType
		afterSalesData.Description = requestCreateAfterSales.Description
		afterSalesData.IsReceived = requestCreateAfterSales.IsReceived
		afterSalesData.RefundType = requestCreateAfterSales.RefundType
		var logAfterSales request.LogAfterSales
		err, afterSalesData = service.GetLog(logAfterSales, afterSalesData)

		if err = service.UpdateAfterSales(afterSalesData); err != nil {
			log.Log().Error("修改申请记录失败!", zap.Any("err", err))
			yzResponse.FailWithMessage("修改申请记录失败", c)
			return
		}
		//再次创建申请记录
		afterSalesAudit := model.AfterSalesAudit{
			AfterSalesID: afterSalesData.ID,
			AdminID:      0,
			ReasonType:   afterSalesData.ReasonType,
			Status:       model.PendingStatus,
		}
		if err := service.CreateAfterSalesAudit(afterSalesAudit); err != nil {
			log.Log().Error("创建审核记录失败!", zap.Any("err", err))
			yzResponse.FailWithMessage("创建审核记录失败", c)
			return
		}
	} else {
		as := model.AfterSales{
			UserID:       uid,
			OrderItemID:  requestCreateAfterSales.OrderItemID,
			Amount:       requestCreateAfterSales.Amount,
			ReasonType:   requestCreateAfterSales.ReasonType,
			Description:  requestCreateAfterSales.Description,
			IsReceived:   requestCreateAfterSales.IsReceived,
			DetailImages: requestCreateAfterSales.DetailImages,
			RefundType:   requestCreateAfterSales.RefundType,
			OrderID:      orderItem.OrderID,
			ProductID:    orderItem.ProductID,
			SkuID:        orderItem.SkuID,
			Status:       model.WaitAuditStatus,
		}
		if requestCreateAfterSales.ReasonType == model.Others {
			as.Reason = requestCreateAfterSales.Reason
		}
		var logAfterSales request.LogAfterSales
		err, as = service.GetLog(logAfterSales, as)

		err, afterSales := service.CreateAfterSales(as)
		if err != nil {
			log.Log().Error("售后,创建失败!", zap.Any("err", err))
			yzResponse.FailWithMessage("售后,创建失败"+err.Error(), c)
			return
		}

		afterSalesAudit := model.AfterSalesAudit{
			AfterSalesID: afterSales.ID,
			AdminID:      0,
			ReasonType:   afterSales.ReasonType,
			Status:       model.PendingStatus,
		}

		if requestCreateAfterSales.ReasonType == model.Others {
			afterSalesAudit.Reason = requestCreateAfterSales.Reason
		}

		if err = service.CreateAfterSalesAudit(afterSalesAudit); err != nil {
			log.Log().Error("创建审核记录失败!", zap.Any("err", err))
			yzResponse.FailWithMessage("创建审核记录失败", c)
			return
		}

		err = source.DB().Where("id = ?", orderItem.ID).Updates(&model3.OrderItemModel{
			RefundStatus: model3.Refunding,
		}).Error

		if err != nil {
			log.Log().Error("修改OrderItem状态失败!", zap.Any("err", err))
			yzResponse.FailWithMessage("操作失败", c)
			return
		}

	}
	err = mq.PublishMessage(uid, "afterSaleOrder", orderItem.SupplierID)
	yzResponse.OkWithMessage("申请成功，请等待审核", c)
}

// Close
// @Tags 售后
// @Summary 关闭售后
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "审核信息"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /api/afterSales/close [post]
func Close(c *gin.Context) {
	var as model.AfterSales
	err := c.ShouldBindJSON(&as)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	adminId := ufv1.GetUserID(c)

	err = service.Close(as, adminId, 0)

	if err != nil {
		log.Log().Error("修改OrderItem状态失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithMessage("操作成功", c)
	return
}

// @Tags 售后
// @Summary 关闭售后
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "审核信息"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /api/afterSales/close [post]
func SupplierClose(c *gin.Context) {
	var as model.AfterSales
	err := c.ShouldBindJSON(&as)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	adminId := ufv1.GetUserID(c)

	err, supplier := service.GetSupplierByUserId(ufv1.GetUserID(c))
	if err != nil {
		log.Log().Error("供应商不存在", zap.Any("err", err))
		yzResponse.FailWithMessage("供应商不存在", c)
		return
	}

	err = service.Close(as, adminId, supplier.ID)

	if err != nil {
		log.Log().Error("修改OrderItem状态失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithMessage("操作成功", c)
	return
}

// Refund
// @Tags 售后
// @Summary 售后退款
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "审核信息"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /afterSales/supplierRefund [post]
func SupplierRefund(c *gin.Context) {
	var as model.AfterSales
	err := c.ShouldBindJSON(&as)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplierSetting := supplierService.GetSetting()
	if supplierSetting.Values.IsAfterSalesPrice == 1 {
		yzResponse.FailWithMessage("供应商不可操作退款", c)
		return
	}
	adminId := ufv1.GetUserID(c)
	err, supplier := service.GetSupplierByUserId(ufv1.GetUserID(c))
	if err != nil {
		log.Log().Error("供应商不存在", zap.Any("err", err))
		yzResponse.FailWithMessage("供应商不存在", c)
		return
	}
	err, returnMsg := service.Refund(as, adminId, supplier.ID)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage(returnMsg, c)
	return
}

// Refund
// @Tags 售后
// @Summary 售后退款
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "审核信息"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /afterSales/refund [post]
func Refund(c *gin.Context) {
	var as model.AfterSales
	err := c.ShouldBindJSON(&as)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	adminId := ufv1.GetUserID(c)
	err, returnMsg := service.Refund(as, adminId, 0)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage(returnMsg, c)
	return
}

// Send
// @Tags 售后
// @Summary 售后发货
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "发货信息"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /api/afterSales/send [post]
func Send(c *gin.Context) {
	var sendRequest request.SendRequest
	err := c.ShouldBindJSON(&sendRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		msg := "请提交数据"
		if err.Error() != "EOF" {
			msg = request.GetError(err.(validator.ValidationErrors), sendRequest)
		}
		yzResponse.FailWithMessage(msg, c)
		return
	}

	var afterSales model.AfterSales
	if err, afterSales = service.GetAfterSales(sendRequest.AfterSalesID); err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("未找到售后记录", c)
		return
	}
	// 售后申请改为待收货状态 //原model.WaitRefundStatus是待退款才能发货 调整为待发货的时候才可以发货
	if afterSales.Status != model.WaitSendStatus {
		var statusName string
		err, statusName = model.GetStatusName(afterSales.Status)
		err = errors.New(fmt.Sprintf("%s状态的售后申请，无法执行发货操作", statusName))
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 创建退货配送信息
	err, returnOrderExpress := service.CreateReturnOrderExpress(model.ReturnOrderExpress{
		AfterSalesID: sendRequest.AfterSalesID,
		CompanyCode:  sendRequest.CompanyCode,
		ExpressNo:    sendRequest.ExpressNo,
		CompanyName:  sendRequest.CompanyName,
	})
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	afterSales.Status = model.WaitReceiveStatus
	afterSales.ReturnOrderExpressID = returnOrderExpress.ID
	afterSales.ShippingAddressID = sendRequest.ShippingAddressID

	adminId := ufv1.GetUserID(c)
	var logAfterSales request.LogAfterSales
	logAfterSales.AdminId = adminId
	err, afterSales = service.GetLog(logAfterSales, afterSales)

	err = service.UpdateAfterSales(afterSales)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("修改售后记录失败", c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

// Receive
// @Tags 售后
// @Summary 售后收货
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "售后信息"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /afterSales/receive [post]
func Receive(c *gin.Context) {
	var as request.AfterSalesSend
	err := c.ShouldBindJSON(&as)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	adminId := ufv1.GetUserID(c)
	err = service.Receive(as, adminId, 0)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

// Receive
// @Tags 售后
// @Summary 售后用户收货
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "售后信息"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /afterSales/userReceive [post]
func UserReceive(c *gin.Context) {
	var as model.AfterSales
	err := c.ShouldBindJSON(&as)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	adminId := ufv1.GetUserID(c)
	err = service.UserReceive(as, 0, adminId, 0, 0)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

// Receive
// @Tags 售后
// @Summary 售后收货
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "售后信息"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /afterSales/afterSalesShopSend [post]
func AfterSalesShopSend(c *gin.Context) {
	var as request.AfterSalesSend
	err := c.ShouldBindJSON(&as)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	adminId := ufv1.GetUserID(c)
	err = service.AfterSalesShopSend(as, adminId, 0)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

// Receive
// @Tags 售后
// @Summary 售后收货
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "售后信息"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /afterSales/receive [post]
func SupplierReceive(c *gin.Context) {
	var as request.AfterSalesSend
	err := c.ShouldBindJSON(&as)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	adminId := ufv1.GetUserID(c)

	err, supplier := service.GetSupplierByUserId(ufv1.GetUserID(c))
	if err != nil {
		log.Log().Error("供应商不存在", zap.Any("err", err))
		yzResponse.FailWithMessage("供应商不存在", c)
		return
	}
	err = service.Receive(as, adminId, supplier.ID)
	if err != nil {
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

// PassAudit
// @Tags 售后
// @Summary 通过审核
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSalesAudit true "审核信息"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /afterSalesAudit/pass [post]
func PassAudit(c *gin.Context) {
	var resAfterSaleAudit model.AfterSalesAudit
	err := c.ShouldBindJSON(&resAfterSaleAudit)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	adminId := ufv1.GetUserID(c)

	err = service.PassAudit(resAfterSaleAudit, adminId, 0)

	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

// PassAudit
// @Tags 售后
// @Summary 通过审核
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSalesAudit true "审核信息"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /afterSalesAudit/pass [post]
func SupplierPassAudit(c *gin.Context) {
	var resAfterSaleAudit model.AfterSalesAudit
	err := c.ShouldBindJSON(&resAfterSaleAudit)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	adminId := ufv1.GetUserID(c)

	err, supplier := service.GetSupplierByUserId(ufv1.GetUserID(c))
	if err != nil {
		log.Log().Error("供应商不存在", zap.Any("err", err))
		yzResponse.FailWithMessage("供应商不存在", c)
		return
	}
	err = service.PassAudit(resAfterSaleAudit, adminId, supplier.ID)

	if err != nil {
		log.Log().Error("售后审核失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

// RejectAudit
// @Tags 售后
// @Summary 拒绝申请
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSalesAudit true "拒绝申请"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /api/afterSales/audit/reject [post]
func RejectAudit(c *gin.Context) {
	var resAfterSaleAudit model.AfterSalesAudit
	err := c.ShouldBindJSON(&resAfterSaleAudit)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	adminId := ufv1.GetUserID(c)
	err = service.RejectAudit(resAfterSaleAudit, adminId, 0)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

// RejectAudit
// @Tags 售后
// @Summary 拒绝申请
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSalesAudit true "拒绝申请"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /api/afterSales/audit/supplierRejectAudit [post]
func SupplierRejectAudit(c *gin.Context) {
	var resAfterSaleAudit model.AfterSalesAudit
	err := c.ShouldBindJSON(&resAfterSaleAudit)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	adminId := ufv1.GetUserID(c)
	err, supplier := service.GetSupplierByUserId(ufv1.GetUserID(c))
	if err != nil {
		log.Log().Error("供应商不存在", zap.Any("err", err))
		yzResponse.FailWithMessage("供应商不存在", c)
		return
	}
	err = service.RejectAudit(resAfterSaleAudit, adminId, supplier.ID)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

// FindAfterSaleAudit
// @Tags 售后
// @Summary 用id查询审核信息
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "用id查询审核信息"
// @Success 200 {object} model.AfterSalesAudit
// @Router /afterSalesAudit/get [get]
func FindAfterSaleAudit(c *gin.Context) {
	var asa model.AfterSalesAudit
	err := c.ShouldBindQuery(&asa)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, read := service.GetAfterSaleAudit(asa.ID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"read": read}, c)
	}
}

// GetAfterSaleAuditList
// @Tags 售后
// @Summary 分页获取列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body refund.AfterSalesAuditSearch true "分页获取列表"
// @Success 200 {object} yzResponse.PageResult
// @Router /api/afterSales/list [post]
func GetAfterSaleAuditList(c *gin.Context) {
	var pageInfo service.AfterSalesAuditSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetAfterSalesAuditList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func ExportAfterSalesList(c *gin.Context) {
	var pageInfo request.AfterSalesSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	//userID := ufv1.GetUserID(c)

	//pageInfo.UserID = userID
	if err, link := service.ExportAfterSalesList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}

func SupplierExportAfterSalesList(c *gin.Context) {
	var pageInfo request.AfterSalesSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	//userID := ufv1.GetUserID(c)
	//
	//pageInfo.UserID = userID

	err, supplier := service.GetSupplierByUserId(ufv1.GetUserID(c))
	if err != nil {
		log.Log().Error("供应商不存在", zap.Any("err", err))
		yzResponse.FailWithMessage("供应商不存在", c)
		return
	}
	pageInfo.SupplierId = &supplier.ID

	if err, link := service.ExportAfterSalesList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}

// CreateAfterSales
// @Tags 售后
// @Summary 创建售后申请
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Ad true "创建售后申请"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/afterSales/save [post]
func SaveAfterSales(c *gin.Context) {
	var requestCreateAfterSales request.AfterSales
	err := c.ShouldBindJSON(&requestCreateAfterSales)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	requestCreateAfterSales.AfterSaleType = 1
	requestCreateAfterSales.Ip = c.ClientIP()

	err = service.SaveAfterSales(requestCreateAfterSales)

	if err != nil {
		log.Log().Error("修改售后申请失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("修改成功，请等待审核", c)
}

// Receive
// @Tags 售后
// @Summary 供应商-售后用户收货
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "售后信息"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /afterSales/receive [post]
func SupplierUserReceive(c *gin.Context) {
	var as model.AfterSales
	err := c.ShouldBindJSON(&as)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, supplier := service.GetSupplierByUserId(ufv1.GetUserID(c))
	if err != nil {
		log.Log().Error("供应商不存在", zap.Any("err", err))
		yzResponse.FailWithMessage("供应商不存在", c)
		return
	}
	err = service.UserReceive(as, 0, 0, supplier.ID, 0)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

func GetSupplierAfterSalesCount(c *gin.Context) {
	err, supplier := service.GetSupplierByUserId(ufv1.GetUserID(c))
	if err != nil {
		log.Log().Error("供应商不存在", zap.Any("err", err))
		yzResponse.FailWithMessage("供应商不存在", c)
		return
	}
	total := service.GetAfterSalesCount(supplier.ID)

	yzResponse.OkWithData(total, c)

	return
}

func CronBarterSuccess(c *gin.Context) {

	service.CronBarterSuccess()

}

// PassAudit
// @Tags 售后
// @Summary 获取正在进行的售后数量
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSalesAudit true "审核信息"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /afterSalesAudit/getAfterSalesCount [get]
func GetAfterSalesCount(c *gin.Context) {

	total := service.GetAfterSalesCount(0)

	yzResponse.OkWithData(total, c)

	return
}

func AnewSynAfterSale(c *gin.Context) {
	var as model.AfterSales
	err := c.ShouldBindQuery(&as)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var afterSales model.AfterSales
	err, afterSales = service.GetAfterSales(as.ID)
	if err != nil {
		log.Log().Error("获取售后失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取售后失败"+err.Error(), c)
		return
	}
	//其他错误除了没有接收到消息不会有其他原因错误，所以无需重新同步
	switch afterSales.SynType {
	case -1:
		if afterSales.Status == model.WaitAuditStatus {
			err = mq2.PublishMessage(as.ID, mq2.AfterSalesCreate, 0)
		} else {
			err = mq2.PublishMessage(as.ID, mq2.AfterSalesUserSend, 0)
		}
		break
		//case -2:
		//	break
		//case -3:
		//	break
		//case -4:
		//	break
		//case -5:
		//	break
	}
	yzResponse.OkWithData("同步成功", c)
	return
}

func CeshiAfterSales(c *gin.Context) {
	var mqMessage request.MqMessage
	err := c.ShouldBindJSON(&mqMessage)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if mqMessage.AfterSalesID == 0 {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("请填写售后id", c)
		return
	}
	switch mqMessage.Type {
	case 1:
		err = mq2.PublishMessage(mqMessage.AfterSalesID, mq2.AfterSalesCreate, 0)
		break
	case 2:
		err = mq2.PublishMessage(mqMessage.AfterSalesID, mq2.AfterSalesUpdate, 0)
		break
	case 3:
		err = mq2.PublishMessage(mqMessage.AfterSalesID, mq2.AfterSalesPassTheAudit, 0)
		break
	case 4:
		err = mq2.PublishMessage(mqMessage.AfterSalesID, mq2.AfterSalesUserSend, 0)
		break
	case 5:
		err = mq2.PublishMessage(mqMessage.AfterSalesID, mq2.AfterSalesComplete, 0)
		break
	case 6:
		err = mq2.PublishMessage(mqMessage.AfterSalesID, mq2.AfterSalesReceiving, 0)
		break
	case 7:
		err = mq2.PublishMessage(mqMessage.AfterSalesID, mq2.AfterSalesClose, 0)
		break
	case 8:
		err = mq2.PublishMessage(mqMessage.AfterSalesID, mq2.AfterSalesRejectAudit, 0)
		break
	default:
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("请填写正确的售后类型", c)
		return

	}

}
