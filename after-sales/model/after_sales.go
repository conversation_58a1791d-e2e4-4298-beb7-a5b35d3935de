package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"gorm.io/gorm"
	model2 "order/model"
	productModel "product/model"
	regionModel "region/model"
	"yz-go/model"
	"yz-go/source"
)

type AfterSaleStatus int //售后状态

const (
	WaitAuditStatus       AfterSaleStatus = iota // 待审核
	WaitSendStatus                               // 待发货
	WaitReceiveStatus                            // 待收货
	WaitRefundStatus                             // 待退款
	CompletedStatus                              // 已完成
	WaitshopSendStatus                           // 待商家发货
	WaitUserReceiveStatus                        // 待用户收货
	ClosedStatus          AfterSaleStatus = -1   // 已关闭
)

var statusNameMap = map[AfterSaleStatus]string{
	ClosedStatus:          "已关闭",
	WaitAuditStatus:       "待审核",
	WaitSendStatus:        "待退货",
	WaitReceiveStatus:     "待商家收货",
	WaitshopSendStatus:    "待商家发货",
	WaitUserReceiveStatus: "待用户收货",
	WaitRefundStatus:      "待退款",
	CompletedStatus:       "已完成",
}

type RefundWay int //退款状态
const (
	RefundWayPrice RefundWay = iota
	RefundWayNum
)

var RefundWayNameMap = map[RefundWay]string{
	RefundWayPrice: "自定义金额",
	RefundWayNum:   "个数",
}

func GetRefundWayName(status RefundWay) (err error, statusName string) {
	var ok bool
	statusName, ok = RefundWayNameMap[status]
	if !ok {
		err = errors.New(fmt.Sprintf("不存在状态为%d的退款状态", status))
	}
	return
}
func GetStatusName(status AfterSaleStatus) (err error, statusName string) {
	var ok bool
	statusName, ok = statusNameMap[status]
	if !ok {
		err = errors.New(fmt.Sprintf("不存在状态为%d的售后状态", status))
	}
	return
}

type AfterSalesType int // 售后类型

const (
	Refund AfterSalesType = iota // 退款
	Return                       // 退货
	Barter                       // 换货
)

var AfterSalesTypeNameMap = map[AfterSalesType]string{
	Refund: "退款",
	Return: "退货退款",
	Barter: "换货",
}

func GetAfterSalesTypeName(status AfterSalesType) (err error, statusName string) {
	var ok bool
	statusName, ok = AfterSalesTypeNameMap[status]
	fmt.Printf("statusName%s,%status", statusName, status)
	if !ok {
		err = errors.New(fmt.Sprintf("不存在状态为%d的售后类型", status))
	}
	return
}

type PayMethod int // 退款途径

const (
	OriginPay  = iota // 原路返回
	OfflinePay        // 线下支付
)

var PayMethodNameMap = map[PayMethod]string{
	OriginPay:  "原路返回",
	OfflinePay: "线下支付",
}

func GetPayMethodName(status PayMethod) (err error, statusName string) {
	var ok bool
	statusName, ok = PayMethodNameMap[status]
	if !ok {
		err = errors.New(fmt.Sprintf("不存在状态为%d的退款途径", status))
	}
	return
}

type AuditStatus int // 审核状态

const (
	PendingStatus      AuditStatus = iota // 处理中
	PassedStatus                          // 同意通过
	ClosedRefundStatus AuditStatus = -1   // 驳回申请

)

var AuditStatusNameMap = map[AuditStatus]string{
	PendingStatus:      "处理中",
	PassedStatus:       "同意通过",
	ClosedRefundStatus: "已驳回",
}

func GetAuditStatusName(status AuditStatus) (err error, statusName string) {
	var ok bool
	statusName, ok = AuditStatusNameMap[status]
	if !ok {
		err = errors.New(fmt.Sprintf("不存在状态为%d的审核状态", status))
	}
	return
}

type RefundReason int // 退款原因

const (
	DontWant            = iota // 多拍/拍错/不想要
	NotDelivery                // 快递一直未送到
	NotSend                    // 未按约定时间发货
	NoExpressInfo              // 快递无跟踪记录
	EmptyExpressPackage        // 空包裹/少货
	Others                     // 其他
	NotLike                    // 尺码拍错/不喜欢/效果差
	WrongMaterial              // 材质/面料与商品描述不符
	WrongSize                  // 大小尺寸与商品描述不符
	WrongFashion               // 颜色/款式/图案与描述不符
	RefundFreight              // 退运费
	WrongGoods                 // 发错货
	FakeProducts               // 假冒品牌
	BrokenProducts             // 收到商品少件或破损
	QualityProblems            // 质量问题
)

var RefundReasonNameMap = map[RefundReason]string{
	DontWant:            "多拍/拍错/不想要",
	NotDelivery:         "快递一直未送到",
	NotSend:             "未按约定时间发货",
	NoExpressInfo:       "快递无跟踪记录",
	EmptyExpressPackage: "空包裹/少货",
	Others:              "其他",
	NotLike:             "尺码拍错/不喜欢/效果差",
	WrongMaterial:       "材质/面料与商品描述不符",
	WrongSize:           "大小尺寸与商品描述不符",
	WrongFashion:        "颜色/款式/图案与描述不符",
	RefundFreight:       "退运费",
	WrongGoods:          "发错货",
	FakeProducts:        "假冒品牌",
	BrokenProducts:      "收到商品少件或破损",
	QualityProblems:     "质量问题",
}

func GetRefundReasonName(status RefundReason) (err error, statusName string) {
	var ok bool
	statusName, ok = RefundReasonNameMap[status]
	if !ok {
		err = errors.New(fmt.Sprintf("不存在类型为%d的退款原因", status))
	}
	return
}

type AfterSalesPushMessage struct { // 售后消息推送表
	source.Model
	AfterSaleId           uint   `json:"after_sale_id" form:"after_sale_id" gorm:"column:after_sale_id;comment:售后id;type:varchar(20);size:20;"`  // 售后id
	ApplicationID         uint   `json:"application_id" form:"application_id" gorm:"column:application_id;comment:应用id;index;"`                  // 应用id
	ApplicationShopID     uint   `json:"application_shop_id" form:"application_shop_id" gorm:"column:application_shop_id;comment:应用id子集;index;"` // 应用id
	OrderID               uint   `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单idid;index;"`                                  // 应用id
	ThirdOrderSN          string `json:"third_order_sn" form:"third_order_sn" gorm:"column:third_order_sn;comment:编号;"`                          // 采购端单号
	OrderItemId           uint   `json:"order_item_id" form:"order_item_id" gorm:"column:order_item_id;comment:订单子表id;index;"`                   // 应用id
	AfterSalesMessageType string `json:"after_sales_message_type" form:"after_sales_message_type" gorm:"column:after_sales_message_type;comment:消息类型;"`
	Status                int    `json:"status" form:"status" gorm:"column:status;default:1;comment:状态;type:smallint;size:3;"` // 状态 1待推送 2待商城确认 3完成 -1推送失败
	ErrorMsg              string `json:"error_msg" form:"error_msg" gorm:"column:error_msg;comment:错误内容;type:text;"`           //错误内容

}

type AfterSalesMigration struct { // 售后申请
	source.Model
	AfterSaleType int    `json:"after_sale_type" form:"after_sale_type" gorm:"column:after_sale_type;comment:申请来源（1用户申请2采购端申请3小商店);"` // 申请来源（1用户申请2采购端申请3小商店)
	Ip            string `json:"ip" form:"ip"`

	AfterSaleSN          string `json:"after_sale_sn" form:"after_sale_sn" gorm:"column:after_sale_sn;comment:退款编号;type:varchar(20);size:20;index;"` // 退款编号
	UserID               uint   `json:"user_id" form:"user_id" gorm:"column:user_id;comment:申请会员id;index;"`                                          //申请会员id
	Amount               uint   `json:"amount" form:"amount" gorm:"column:amount;comment:退款金额;"`                                                     // 退款金额
	TechnicalServicesFee uint   `json:"technical_services_fee" form:"technical_services_fee" gorm:"column:technical_services_fee;comment:退款的技术服务费;"` // 退款金额
	Freight              uint   `json:"freight" form:"freight" gorm:"column:freight;comment:退款的运费;"`                                                 // 退款金额

	PracticalAmount uint `json:"practical_amount" form:"practical_amount" gorm:"column:practical_amount;comment:实际的退款金额 暂时没有使用的字段;"` // 退款金额

	ReasonType           RefundReason `json:"reason_type" form:"reason_type" gorm:"column:reason_type;comment:退款原因;"`                                             // 退款原因
	Reason               string       `json:"reason" form:"reason" gorm:"column:reason;comment:其他退款原因;"`                                                          // 其他退款原因
	Description          string       `json:"description" form:"description" gorm:"column:description;comment:描述;"`                                               // 描述
	OrderID              uint         `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单id;index;"`                                                // 订单id
	OrderItemID          uint         `json:"order_item_id" form:"order_item_id" gorm:"column:order_item_id;comment:订单商品id;index;"`                               // 订单商品id
	SkuID                uint         `json:"sku_id" form:"sku_id" gorm:"column:sku_id;comment:sku_id;index;"`                                                    // sku id
	ProductID            uint         `json:"product_id" form:"product_id" gorm:"column:product_id;comment:product_id;index;"`                                    // product id
	ShippingAddressID    uint         `json:"shipping_address_id" form:"shipping_address_id" gorm:"column:shipping_address_id;comment:收货地址id;index;"`             // 收货地址id
	NewShippingAddressID uint         `json:"new_shipping_address_id" form:"new_shipping_address_id" gorm:"column:new_shipping_address_id;comment:收货地址id;index;"` // 收货地址id --新版统一使用这个

	IsReceived           int       `json:"is_received"  form:"is_received" gorm:"column:is_received;comment:是否收到货;"`                                       // 是否收到货
	PayMethod            PayMethod `json:"pay_method" form:"pay_method" gorm:"column:pay_method;comment:打款方式;"`                                            // 打款方式
	ReturnOrderExpressID uint      `json:"return_order_express_id" form:"return_order_express_id" gorm:"column:return_order_express_id;comment:退货快递信息id;"` // 退货快递信息id

	Status     AfterSaleStatus `json:"status" form:"status" gorm:"column:status;default:0;comment:状态;type:smallint;size:3;"`   // 状态
	RefundType AfterSalesType  `json:"type" form:"type" gorm:"column:type;comment:服务类型;"`                                      // 服务类型
	RefundWay  RefundWay       `json:"refund_way" form:"refund_way" gorm:"column:refund_way;default:0;comment:退款方式0自定义金额1个数;"` // 退款方式0自定义金额1个数
	Num        uint            `json:"num" form:"num" gorm:"column:num;comment:退款商品个数;"`                                       // 退款商品个数

	DetailImages model.JsonStringList `json:"detail_images" form:"detail_images" gorm:"column:detail_images;comment:图片凭证;type:text;"` // 图片凭证
	Logs         Logs                 `json:"logs" form:"logs" gorm:"column:logs;comment:协商日志;type:text;"`                            // 协商日志

	BarterSkuID    uint   `json:"barter_sku_id" form:"barter_sku_id" gorm:"column:barter_sku_id;comment:换货规格id;"`                                        // sku id
	BarterNum      uint   `json:"barter_num" form:"barter_num" gorm:"column:barter_num;comment:换货数量;"`                                                   // sku id
	BarterSkuTitle string `json:"barter_sku_title" form:"barter_sku_title" gorm:"column:barter_sku_title;comment:换货的规格名称;"`                              // sku id
	ImageUrl       string `json:"image_url" form:"image_url" gorm:"column:image_url;comment:换货商品图片地址;type:varchar(255);size:255;"`                       // 图片地址
	SendStatus     int    `json:"send_status" form:"send_status" gorm:"column:send_status;default:0;comment:发货状态 0未发货 1已发货 2部分发货;type:smallint;size:3;"` // 发货状态
	SendNum        uint   `json:"send_num" form:"send_num" gorm:"column:send_num;comment:已发货数量;"`                                                        // sku id

	RefundReasonName string            `json:"refund_reason_name" gorm:"-"`
	SuccessAt        *source.LocalTime `json:"success_at"`

	StatusName    string `json:"status_name" gorm:"-"`
	RefundWayName string `json:"refund_way_name" gorm:"-"`

	SynType                  int    `json:"syn_type" form:"syn_type" gorm:"column:syn_type;comment:同步状态;default:0"`                                                   //-1同步失败 -2同步审核完成失败 -3同步审核驳回失败 -4同步完成失败 -5 同步商家确认收货并发货失败  0待同步，1同步申请完成 2同步用户发货完成 3售后同步完成
	SynMessage               string `json:"syn_message" form:"syn_message" gorm:"column:syn_message;comment:错误内容;"`                                                   //错误内容
	SynAfterSalesId          uint   `json:"syn_after_sales_id" form:"syn_after_sales_id" gorm:"column:syn_after_sales_id;comment:上级售后id;"`                            //上级售后id
	SynAfterSalesIdString    string `json:"syn_after_sales_id_string" form:"syn_after_sales_id_string" gorm:"column:syn_after_sales_id_string;comment:上级售后idstring;"` //上级售后id
	JushuitanBind            uint   `json:"jushuitan_bind" form:"jushuitan_bind" gorm:"column:jushuitan_bind;default:0;index;"`                                       //  1为已绑定聚水潭
	GatherSupplyAfterSaleSN  string `json:"gather_supply_after_sale_sn" form:"gather_supply_after_sale_sn" `
	Source                   int    `json:"source" form:"source" `
	GatherSupplyStatusString string `json:"gather_supply_status_string"` //上游的售后状态

}

func (AfterSalesMigration) TableName() string {
	return "after_sales"
}

type AfterSales struct { // 售后申请
	source.Model
	AfterSaleType        int    `json:"after_sale_type" form:"after_sale_type" gorm:"column:after_sale_type;comment:申请来源（1用户申请2采购端申请3小商店);"` // 申请来源（1用户申请2采购端申请)
	Ip                   string `json:"ip" form:"ip"`
	AfterSaleSN          string `json:"after_sale_sn" form:"after_sale_sn" gorm:"column:after_sale_sn;comment:退款编号;type:varchar(20);size:20;index;"` // 退款编号
	UserID               uint   `json:"user_id" form:"user_id" gorm:"column:user_id;comment:申请会员id;index;"`                                          //申请会员id
	Amount               uint   `json:"amount" form:"amount" gorm:"column:amount;comment:退款金额;"`                                                     // 退款金额
	TechnicalServicesFee uint   `json:"technical_services_fee" form:"technical_services_fee" gorm:"column:technical_services_fee;comment:退款的技术服务费;"` // 退款金额
	Freight              uint   `json:"freight" form:"freight" gorm:"column:freight;comment:退款的运费;"`                                                 // 退款金额

	PracticalAmount uint `json:"practical_amount" form:"practical_amount" gorm:"column:practical_amount;comment:实际的退款金额 暂时没有使用的字段;"` // 退款金额

	ReasonType           RefundReason `json:"reason_type" form:"reason_type" gorm:"column:reason_type;comment:退款原因;"`                                             // 退款原因
	Reason               string       `json:"reason" form:"reason" gorm:"column:reason;comment:其他退款原因;"`                                                          // 其他退款原因
	Description          string       `json:"description" form:"description" gorm:"column:description;comment:描述;"`                                               // 描述
	OrderID              uint         `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单id;index;"`                                                // 订单id
	OrderItemID          uint         `json:"order_item_id" form:"order_item_id" gorm:"column:order_item_id;comment:订单商品id;index;"`                               // 订单商品id
	SkuID                uint         `json:"sku_id" form:"sku_id" gorm:"column:sku_id;comment:sku_id;index;"`                                                    // sku id
	ProductID            uint         `json:"product_id" form:"product_id" gorm:"column:product_id;comment:product_id;index;"`                                    // product id
	ShippingAddressID    uint         `json:"shipping_address_id" form:"shipping_address_id" gorm:"column:shipping_address_id;comment:收货地址id;index;"`             // 收货地址id
	NewShippingAddressID uint         `json:"new_shipping_address_id" form:"new_shipping_address_id" gorm:"column:new_shipping_address_id;comment:收货地址id;index;"` // 收货地址id --新版统一使用这个

	IsReceived           int       `json:"is_received"  form:"is_received" gorm:"column:is_received;comment:是否收到货;"`                                       // 是否收到货
	PayMethod            PayMethod `json:"pay_method" form:"pay_method" gorm:"column:pay_method;comment:打款方式;"`                                            // 打款方式
	ReturnOrderExpressID uint      `json:"return_order_express_id" form:"return_order_express_id" gorm:"column:return_order_express_id;comment:退货快递信息id;"` // 退货快递信息id

	Status     AfterSaleStatus `json:"status" form:"status" gorm:"column:status;default:0;comment:状态;type:smallint;size:3;"`   // 状态
	RefundType AfterSalesType  `json:"type" form:"type" gorm:"column:type;comment:服务类型;"`                                      // 服务类型
	RefundWay  RefundWay       `json:"refund_way" form:"refund_way" gorm:"column:refund_way;default:0;comment:退款方式0自定义金额1个数;"` // 退款方式0自定义金额1个数
	Num        uint            `json:"num" form:"num" gorm:"column:num;comment:退款商品个数;"`                                       // 退款商品个数

	DetailImages model.JsonStringList `json:"detail_images" form:"detail_images" gorm:"column:detail_images;comment:图片凭证;type:text;"` // 图片凭证
	Logs         Logs                 `json:"logs" form:"logs" gorm:"column:logs;comment:协商日志;type:text;"`                            // 协商日志

	BarterSkuID    uint   `json:"barter_sku_id" form:"barter_sku_id" gorm:"column:barter_sku_id;comment:换货规格id;"`                                        // sku id
	BarterNum      uint   `json:"barter_num" form:"barter_num" gorm:"column:barter_num;comment:换货数量;"`                                                   // sku id
	BarterSkuTitle string `json:"barter_sku_title" form:"barter_sku_title" gorm:"column:barter_sku_title;comment:换货的规格名称;"`                              // sku id
	ImageUrl       string `json:"image_url" form:"image_url" gorm:"column:image_url;comment:图片地址;type:varchar(255);size:255;"`                           // 图片地址
	SendStatus     int    `json:"send_status" form:"send_status" gorm:"column:send_status;default:0;comment:发货状态 0未发货 1已发货 2部分发货;type:smallint;size:3;"` // 发货状态
	SendNum        uint   `json:"send_num" form:"send_num" gorm:"column:send_num;comment:已发货数量;"`                                                        // sku id

	RefundReasonName string            `json:"refund_reason_name" gorm:"-"`
	SuccessAt        *source.LocalTime `json:"success_at"`

	StatusName    string `json:"status_name" gorm:"-"`
	RefundWayName string `json:"refund_way_name" gorm:"-"`

	SynType                  int                   `json:"syn_type" form:"syn_type" gorm:"column:syn_type;comment:同步状态;default:0"`                                                   //-1同步失败 -2同步审核完成失败 -3同步审核驳回失败 -4同步完成失败 -5 同步商家确认收货并发货失败  0待同步，1同步申请完成 2同步用户发货完成 3售后同步完成
	SynMessage               string                `json:"syn_message" form:"syn_message" gorm:"column:syn_message;comment:错误内容;"`                                                   //错误内容
	SynAfterSalesId          uint                  `json:"syn_after_sales_id" form:"syn_after_sales_id" gorm:"column:syn_after_sales_id;comment:上级售后id;"`                            //上级售后id
	SynAfterSalesIdString    string                `json:"syn_after_sales_id_string" form:"syn_after_sales_id_string" gorm:"column:syn_after_sales_id_string;comment:上级售后idstring;"` //上级售后id
	JushuitanBind            uint                  `json:"jushuitan_bind" form:"jushuitan_bind" gorm:"column:jushuitan_bind;default:0;index;"`
	Order                    Order                 `json:"order"`
	OrderItem                OrderItem             `json:"order_item"`
	AfterSalesSend           []AfterSalesSend      `json:"after_sales_send" gorm:"foreignkey:after_sales_id"`
	AfterSalesAudit          AfterSalesAudit       `json:"after_sales_audit" gorm:"foreignkey:after_sales_id"`
	ReturnOrderExpress       ReturnOrderExpress    `json:"return_order_express"`
	User                     User                  `json:"user" form:"user" gorm:"foreignKey:UserID;references:ID"`
	ShopAddress              AfterSalesShopAddress `json:"shop_address" gorm:"foreignkey:new_shipping_address_id"`
	GatherSupplyAfterSaleSN  string                `json:"gather_supply_after_sale_sn" form:"gather_supply_after_sale_sn" `
	GatherSupplyStatusString string                `json:"gather_supply_status_string"` //上游的售后状态
}

type OrderItem struct {
	model2.OrderItem
	Options     model2.Options       `json:"options"`
	Sku         productModel.Sku     `json:"sku"`
	Product     productModel.Product `json:"product"`
	ItemExpress []ItemExpress        `json:"item_express"`
	//gorm:"foreignkey:order_item_id"

}
type ItemExpress struct {
	ID             uint                     `json:"id" form:"id" gorm:"primarykey"`
	OrderItemID    uint                     `json:"order_item_id" form:"order_item_id"`
	OrderExpressID uint                     `json:"express_id" form:"express_id"`
	Num            uint                     `json:"num" form:"num"`
	OrderExpress   model2.OrderExpressModel `json:"order_express"`
}

//售后退货地址

type AfterSalesShopAddressMigration struct {
	source.Model
	Contacts           string               `json:"contacts" form:"contacts" gorm:"column:contacts;comment:联系人;type:varchar(255);size:255;"`
	TelType            int                  `json:"tel_type" form:"tel_type" gorm:"column:tel_type;comment:手机号类型;type:tinyint;size:1;"`
	Tel                string               `json:"tel" form:"tel" gorm:"column:tel;comment:手机号;varchar(20);size:20;"`
	PlaneType          int                  `json:"plane_type" form:"plane_type" gorm:"column:plane_type;comment:座机类型0普通座机1企业座机号;type:tinyint;size:1;"`
	Plane              string               `json:"plane" form:"plane" gorm:"column:plane;comment:座机号;type:varchar(20);size:20;"`
	Country            string               `json:"country" form:"country" gorm:"column:country;comment:国家;type:varchar(20);size:20;"`
	Province           int                  `json:"province" form:"province" gorm:"column:province;comment:省;type:int(11);"`
	City               int                  `json:"city" form:"city" gorm:"column:city;comment:市;type:int(11);"`
	District           int                  `json:"district" form:"district" gorm:"column:district;comment:区;type:int(11);"`
	Address            string               `json:"address" form:"address" gorm:"column:address;comment:详细地址;varchar(255);size:255;"`
	AddressType        model.JsonStringList `json:"address_type" gorm:"column:address_type;comment:地址类型;type:json;"` //地址类型(0,1,2 退货地址，收票地址，发货地址)
	IsDefault          int                  `json:"is_default" form:"is_default" gorm:"column:is_default;comment:-1都不是默认0是退货地址1是收票地址2是发货地址;type:tinyint;size:1;"`
	SupplierID         uint                 `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商id;index;"` // 供应商id
	ProvinceName       regionModel.Region   `json:"province_name" gorm:" foreignKey:Province"`
	CityName           regionModel.Region   `json:"city_name" gorm:" foreignKey:City"`
	DistrictName       regionModel.Region   `json:"district_name" gorm:" foreignKey:District"`
	GatherSupplyID     uint                 `json:"gather_supply_id" form:"gather_supply_id" gorm:"column:gather_supply_id;comment:供应链id;"`
	ThirdShopAddressId uint                 `json:"third_shop_address_id" form:"third_shop_address_id" gorm:"column:third_shop_address_id;comment:上级退货地址id;"`
	IsShow             int                  `json:"is_show" form:"is_show" gorm:"column:is_show;comment:是否显示0显示1不显示;default:0;type:tinyint;size:1;"`
}

func (AfterSalesShopAddressMigration) TableName() string {
	return "after_sales_shop_addresses"
}

type AfterSalesShopAddress struct {
	AfterSalesShopAddressMigration
	Supplier Supplier `json:"supplier" gorm:"foreignKey:supplier_id"`
}

// 收货地址
type ShopAddress struct {
	source.Model
	Contacts           string               `json:"contacts" form:"contacts" gorm:"column:contacts;comment:联系人;type:varchar(255);size:255;"`
	TelType            int                  `json:"tel_type" form:"tel_type" gorm:"column:tel_type;comment:手机号类型;type:tinyint;size:1;"`
	Tel                string               `json:"tel" form:"tel" gorm:"column:tel;comment:手机号;varchar(20);size:20;"`
	PlaneType          int                  `json:"plane_type" form:"plane_type" gorm:"column:plane_type;comment:座机类型0普通座机1企业座机号;type:tinyint;size:1;"`
	Plane              string               `json:"plane" form:"plane" gorm:"column:plane;comment:座机号;type:varchar(20);size:20;"`
	Country            string               `json:"country" form:"country" gorm:"column:country;comment:国家;type:varchar(20);size:20;"`
	Province           int                  `json:"province" form:"province" gorm:"column:province;comment:省;type:int(11);"`
	City               int                  `json:"city" form:"city" gorm:"column:city;comment:市;type:int(11);"`
	District           int                  `json:"district" form:"district" gorm:"column:district;comment:区;type:int(11);"`
	Address            string               `json:"address" form:"address" gorm:"column:address;comment:详细地址;varchar(255);size:255;"`
	AddressType        model.JsonStringList `json:"address_type" gorm:"column:address_type;comment:地址类型;type:json;"` //地址类型(0,1,2 退货地址，收票地址，发货地址)
	IsDefault          int                  `json:"is_default" form:"is_default" gorm:"column:is_default;comment:-1都不是默认0是退货地址1是收票地址2是发货地址;type:tinyint;size:1;"`
	SupplierID         uint                 `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商id;index;"` // 供应商id
	ProvinceName       regionModel.Region   `json:"province_name" gorm:"foreignKey:Province"`
	CityName           regionModel.Region   `json:"city_name" gorm:"foreignKey:City"`
	DistrictName       regionModel.Region   `json:"district_name" gorm:"foreignKey:District"`
	Supplier           Supplier             `json:"supplier" gorm:"foreignKey:supplier_id"`
	GatherSupplyID     uint                 `json:"gather_supply_id" form:"gather_supply_id" gorm:"column:gather_supply_id;comment:供应链id;"`
	ThirdShopAddressId uint                 `json:"third_shop_address_id" form:"third_shop_address_id" gorm:"column:third_shop_address_id;comment:上级退货地址id;"`
	IsShow             int                  `json:"is_show" form:"is_show" gorm:"column:is_show;comment:是否显示0显示1不显示;default:0;type:tinyint;size:1;"`
}

/*
*

	换货发货表
*/
type AfterSalesSends []AfterSalesSend
type AfterSalesSend struct {
	source.Model
	AfterSalesID   uint   `json:"after_sales_id" form:"after_sales_id" gorm:"column:after_sales_id;comment:售后申请id;"`                    // 售后申请id                                         //申请会员id
	BarterSkuID    uint   `json:"barter_sku_id" form:"barter_sku_id" gorm:"column:barter_sku_id;comment:换货规格id;"`                       // sku id
	ProductID      uint   `json:"product_id" form:"product_id" gorm:"column:product_id;comment:product_id;"`                            // product id
	BarterSkuTitle string `json:"barter_sku_title" form:"barter_sku_title" gorm:"column:barter_sku_title;comment:换货的规格名称;"`             // sku id
	ImageUrl       string `json:"image_url" form:"image_url" gorm:"column:image_url;comment:图片地址;type:varchar(255);size:255;"`          // 图片地址
	Num            uint   `json:"num" form:"num" gorm:"column:num;comment:发货数量;"`                                                       // sku id
	CompanyName    string `json:"company_name" form:"company_name" gorm:"column:company_name;comment:快递公司名称;type:varchar(30);size:30;"` // 快递公司名称
	CompanyCode    string `json:"company_code" form:"company_code" gorm:"column:company_code;comment:快递公司码;type:varchar(30);size:30;"`  // 快递公司码
	ExpressNo      string `json:"express_no" form:"express_no" gorm:"column:express_no;comment:快递单号;type:varchar(30);size:30;"`         // 快递单号

}
type User struct {
	ID       uint   `json:"id" form:"id"`
	Avatar   string `json:"avatar" form:"avatar" gorm:"column:avatar;comment:用户头像url;type:varchar(255);size:255;"`
	Username string `json:"username" form:"username" gorm:"comment:用户登录名"`
	NickName string `json:"nickname" form:"nickName" gorm:"column:nick_name;comment:用户昵称;type:varchar(50);size:50;"`
}
type Order struct {
	model2.Order
	StatusName  string      `json:"status_name"` // 状态名
	Supplier    Supplier    `json:"supplier" gorm:"foreignKey:SupplierID"`
	Application Application `json:"application" gorm:"foreignKey:ApplicationID"`
}
type Application struct {
	ID      uint   `json:"id"`
	AppName string `json:"appName"`
}

func (o *Order) AfterFind(tx *gorm.DB) (err error) {
	o.StatusName = model2.GetStatusName(o.Status)

	return
}

func (Application) TableName() string {
	return "application"
}

type Supplier struct {
	ID     uint   `json:"id"`
	Name   string `json:"name"`
	Mobile string `json:"supplier"`
}

func (as *AfterSales) AfterCreate(tx *gorm.DB) (err error) {
	timestamp := uint(as.CreatedAt.Unix())
	afterSaleSn := as.ID + timestamp*110
	err = tx.Model(&as).Update("after_sale_sn", afterSaleSn).Error
	return
}

func (logs Logs) Value() (driver.Value, error) {
	return json.Marshal(logs)
}
func (logs *Logs) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &logs)
}

type Logs []Log // 协商日志
type Log struct {
	CreatedAt            string    `json:"created_at"`
	AdminID              uint      `json:"admin_id"`                                                                                             // 管理员id
	Content              string    `json:"content"`                                                                                              // 操作内容
	Price                uint      `json:"price"`                                                                                                // 退款金额
	TechnicalServicesFee uint      `json:"technical_services_fee" `                                                                              //退款的技术服务费
	Freight              uint      `json:"freight" `                                                                                             //退款的运费
	RefundTypeName       string    `json:"refund_type_name"`                                                                                     //退款类型
	ReasonTypeName       string    `json:"reason_type_name"`                                                                                     //退款原因
	Description          string    `json:"description"`                                                                                          //描述
	BarterSkuID          uint      `json:"barter_sku_id"`                                                                                        //换货的规格id
	BarterNum            uint      `json:"barter_num"`                                                                                           //换货的规格id
	BarterSkuTitle       string    `json:"barter_sku_title"`                                                                                     //换货的规格名称
	ImageUrl             string    `json:"image_url"`                                                                                            // 图片地址
	Num                  uint      `json:"num" form:"num" gorm:"column:num;comment:发货数量;"`                                                       // sku id
	CompanyName          string    `json:"company_name" form:"company_name" gorm:"column:company_name;comment:快递公司名称;type:varchar(30);size:30;"` // 快递公司名称
	CompanyCode          string    `json:"company_code" form:"company_code" gorm:"column:company_code;comment:快递公司码;type:varchar(30);size:30;"`  // 快递公司码
	ExpressNo            string    `json:"express_no" form:"express_no" gorm:"column:express_no;comment:快递单号;type:varchar(30);size:30;"`         // 快递单号
	RefundWay            RefundWay `json:"refund_way" form:"refund_way" gorm:"column:refund_way;default:0;comment:退款方式0自定义金额1个数;"`               // 退款方式0自定义金额1个数
	RefundNum            uint      `json:"refund_num" form:"refund_num" gorm:"column:refund_num;comment:退款商品个数;"`                                // 退款商品个数
	AfterSaleType        int       `json:"after_sale_type" form:"after_sale_type" gorm:"column:after_sale_type;comment:申请来源（1用户申请2采购端申请3小商店);"`  // 申请来源（1用户申请2采购端申请)
	Ip                   string    `json:"ip" form:"ip"`
}
type AfterSalesAudit struct { // 售后审核
	source.Model
	AfterSalesID      uint         `json:"after_sales_id" form:"after_sales_id" gorm:"column:after_sales_id;comment:售后申请id;"`                // 售后申请id                                         //申请会员id
	AdminID           uint         `json:"admin_id" form:"admin_id" gorm:"column:admin_id;comment:管理员id;"`                                   //管理员id
	ReasonType        RefundReason `json:"reason_type" form:"reason_type" gorm:"column:reason_type;comment:原因;"`                             // 原因
	Reason            string       `json:"reason" form:"reason" gorm:"column:reason;comment:其他退款原因;"`                                        // 其他退款原因
	Status            AuditStatus  `json:"status" form:"status" gorm:"column:status;default:0;comment:状态;type:smallint;size:3;"`             // 状态
	Cause             string       `json:"cause" form:"cause" gorm:"column:cause;comment:驳回原因;"`                                             // 其他退款原因
	ShippingAddressID uint         `json:"shipping_address_id" form:"shipping_address_id" gorm:"column:shipping_address_id;comment:收货地址id;"` // 收货地址id

	//以下为忽略的字段不会添加到数据库中
	StatusName string `json:"status_name" gorm:"-"`
	RefundName string `json:"refund_name" gorm:"-"`
}

func (receiver *AfterSales) AfterFind(tx *gorm.DB) (err error) {
	err, receiver.RefundReasonName = GetRefundReasonName(receiver.ReasonType)
	if err != nil {
		return
	}
	err, receiver.StatusName = GetStatusName(receiver.Status)
	if err != nil {
		return
	}
	err, receiver.RefundWayName = GetRefundWayName(receiver.RefundWay)
	if err != nil {
		return
	}
	return
}
func (receiver *AfterSalesAudit) AfterFind(tx *gorm.DB) (err error) {
	err, receiver.StatusName = GetAuditStatusName(receiver.Status)
	if err != nil {
		return
	}
	err, receiver.RefundName = GetRefundReasonName(receiver.ReasonType)
	if err != nil {
		return
	}
	return
}

type ReturnOrderExpress struct { // 退货单物流信息
	source.Model
	AfterSalesID uint   `json:"after_sales_id" gorm:"column:after_sales_id;comment:退货单id;index"`                                        // 退货单id
	CompanyName  string `json:"company_name" form:"company_name" gorm:"column:company_name;comment:快递公司名称;type:varchar(255);size:255;"` // 快递公司名称
	CompanyCode  string `json:"company_code" form:"company_code" gorm:"column:company_code;comment:快递公司码;type:varchar(255);size:255;"`  // 快递公司码
	ExpressNo    string `json:"express_no" form:"express_no" gorm:"column:express_no;comment:快递单号;type:varchar(255);size:255;"`         // 快递单号
}

type AfterSalesAddress struct {
	source.Model
	Name       string `json:"name" gorm:"column:name;comment:收件人;type:varchar(30);size:30;"`                         // 收件人
	Mobile     string `json:"mobile" gorm:"column:mobile;comment:收件人手机号;type:varchar(20);size:20;"`                  // 手机号
	CountryId  int    `json:"country_id" gorm:"column:country_id;default:0;comment:国家id;type:int;"`                  // 国id
	ProvinceId int    `json:"province_id" gorm:"column:province_id;comment:省份id;type:int;"`                          // 省id
	CityId     int    `json:"city_id" gorm:"column:city_id;comment:城市id;type:int;"`                                  // 市id
	CountyId   int    `json:"county_id" gorm:"column:county_id;comment:区id;type:int;"`                               // 区id
	TownId     int    `json:"town_id" gorm:"column:town_id;comment:街道id;type:int;"`                                  // 街道id
	Province   string `json:"province" gorm:"-"`                                                                     // 省
	City       string `json:"city"  gorm:"-"`                                                                        // 市
	County     string `json:"county"  gorm:"-"`                                                                      // 区
	Town       string `json:"town"  gorm:"-"`                                                                        // 街
	Detail     string `json:"detail" validate:"required" form:"detail" gorm:"column:detail;comment:详细地址;type:text;"` //详细地址
}
