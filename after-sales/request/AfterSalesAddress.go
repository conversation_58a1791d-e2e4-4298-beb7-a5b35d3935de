package request

import (
	"after-sales/model"
	"github.com/go-playground/validator/v10"
	"reflect"
	model2 "yz-go/model"
	yzRequest "yz-go/request"
)

type AfterSalesAddressSearch struct {
	yzRequest.PageInfo
	model.AfterSalesAddress
}

type SendRequest struct {
	AfterSalesID      uint   `json:"after_sales_id" comment:"退货单id" binding:"required"`
	CompanyName       string `json:"company_name" form:"company_name" binding:"required" gorm:"column:company_name;comment:快递公司名称;type:varchar(30);size:30;"` // 快递公司名称
	CompanyCode       string `json:"company_code" form:"company_code" binding:"required" comment:"快递公司码"`
	ExpressNo         string `json:"express_no" form:"express_no" binding:"required" comment:"快递单号"`
	ShippingAddressID uint   `json:"shipping_address_id" form:"shipping_address_id" gorm:"column:shipping_address_id;comment:收货地址id;"` // 收货地址id
	//AfterSales         model.AfterSales         `json:"after_sales"`
}
type AfterSales struct {
	Id                   uint                  `json:"id"` //售后id
	OrderID              uint                  `json:"order_id"`
	ThirdOrderSN         string                `json:"third_order_sn" form:"third_order_sn" gorm:"column:third_order_sn;comment:编号;"` // 采购端单号
	OrderItemID          uint                  `json:"order_item_id"`
	Amount               uint                  `json:"amount" `
	TechnicalServicesFee uint                  `json:"technical_services_fee" ` //退款的技术服务费
	Freight              uint                  `json:"freight" `                //退款的运费
	ReasonType           model.RefundReason    `json:"reason_type"`
	Reason               string                `json:"reason" `
	Description          string                `json:"description"`
	IsReceived           int                   `json:"is_received"`
	DetailImages         model2.JsonStringList `json:"detail_images"`
	RefundType           model.AfterSalesType  `json:"refund_type"`
	BarterSkuID          uint                  `json:"barter_sku_id"`                                                                                       //换货的规格id
	BarterNum            uint                  `json:"barter_num"`                                                                                          //换货的规格id
	BarterSkuTitle       string                `json:"barter_sku_title"`                                                                                    //换货的规格名称
	ImageUrl             string                `json:"image_url" form:"image_url" gorm:"column:image_url;comment:图片地址;type:varchar(255);size:255;"`         // 图片地址
	RefundWay            model.RefundWay       `json:"refund_way" form:"refund_way" gorm:"column:refund_way;default:0;comment:退款方式0自定义金额1个数;"`              // 退款方式0自定义金额1个数
	Num                  uint                  `json:"num" form:"num" gorm:"column:num;comment:退款商品个数;"`                                                    // 退款商品个数
	AfterSaleType        int                   `json:"after_sale_type" form:"after_sale_type" gorm:"column:after_sale_type;comment:申请来源（1用户申请2采购端申请3小商店);"` // 申请来源（1用户申请2采购端申请)
	Ip                   string                `json:"ip" form:"ip"`
}

type AfterSalesSearch struct {
	model.AfterSales
	Status       *int   `json:"status" form:"status" gorm:"column:status;default:0;comment:状态;type:smallint;size:3;"` // 状态  -10 申请驳回
	RefundType   *int   `json:"type" form:"type" gorm:"column:type;comment:退款方式;"`                                    // 退款方式
	ProductTitle string `json:"product_title" form:"product_title"`                                                   //商品名称
	OrderSN      uint   `json:"order_sn" form:"order_sn"`                                                             //订单编号
	ThirdOrderSN string `json:"third_order_sn" form:"third_order_sn"`                                                 // 采购端单号

	StartAt        string `json:"start_at" form:"start_at"`                                                                            //开始时间
	EndAt          string `json:"end_at" form:"end_at"`                                                                                //结束时间
	GatherSupplyId *uint  `json:"gather_supplier_id" form:"gather_supplier_id"`                                                        //供应链
	SendStatus     *int   `json:"send_status" form:"send_status"`                                                                      //发货状态
	SupplierId     *uint  `json:"supplier_id" form:"supplier_id"`                                                                      //供货商
	ApplicationId  uint   `json:"application_id" form:"application_id"`                                                                //供货商
	CompanyCode    string `json:"company_code" form:"company_code" gorm:"column:company_code;comment:快递公司码;type:varchar(30);size:30;"` // 快递公司码
	OrderType      int    `json:"order_type" form:"order_type" gorm:"column:order_type;comment:订单类型 1 套餐订单;default:0;index;"`          //订单类型 1 套餐订单
	OrderTypeNote  string `json:"order_type_note" form:"order_type_note" gorm:"column:order_type_note;"`
	yzRequest.PageInfo
}

type MessageSuccess struct {
	OrderItemId  uint   `json:"order_item_id"`
	ThirdOrderSn string `json:"third_order_sn"`
	AfterSalesId uint   `json:"after_sales_id"`
	MessageType  string `json:"message_type"`
	MemberSign   string `json:"member_sign"`
}

type AfterSalesSend struct {
	Id          uint   `json:"id" form:"id" gorm:"column:id;comment:售后id;type:varchar(20);size:20;"`                                 // 售后id
	CompanyName string `json:"company_name" form:"company_name" gorm:"column:company_name;comment:快递公司名称;type:varchar(30);size:30;"` // 快递公司名称
	CompanyCode string `json:"company_code" form:"company_code" gorm:"column:company_code;comment:快递公司码;type:varchar(30);size:30;"`  // 快递公司码
	ExpressNo   string `json:"express_no" form:"express_no" gorm:"column:express_no;comment:快递单号;type:varchar(30);size:30;"`         // 快递单号
	Num         uint   `json:"num"`
}

// 自定义错误消息
func GetError(errs validator.ValidationErrors, r interface{}) string {
	s := reflect.TypeOf(r)
	for _, fieldError := range errs {
		filed, _ := s.FieldByName(fieldError.Field())

		errText := filed.Tag.Get("err")
		if errText != "" {
			return errText
		}
		//定义验证标志的中文名称
		switch fieldError.Tag() {
		case "required":
			errText = "不可为空"
			break
		default:
			errText = fieldError.Tag()
			break
		}
		//获取字段中文名称
		name := filed.Tag.Get("comment")
		if name == "" {
			name = fieldError.Field()
		}
		return name + ":" + errText
	}
	return ""
}

type MqMessage struct {
	AfterSalesID uint `json:"after_sales_id" comment:"退货单id" binding:"required"`
	Type         int  `json:"type" form:"type" binding:"required" gorm:"column:type;comment:消息类型;type:varchar(30);size:30;"` // 1申请 2修改
	//AfterSales         model.AfterSales         `json:"after_sales"`
}

type LogAfterSales struct {
	AdminId       uint   `json:"adminId"`
	Appid         uint   `json:"appid"`
	AfterSaleType int    `json:"after_sale_type" form:"after_sale_type" gorm:"column:after_sale_type;comment:申请来源（1用户申请2采购端申请3小商店);"` // 申请来源（1用户申请2采购端申请)
	ReturnMsg     string `json:"returnMsg"`
}

type AfterSalesTypeSearch struct {
	OrderID     uint `json:"order_id" form:"order_id"`
	OrderItemID uint `json:"order_item_id" form:"order_item_id"` //不一定会有
}
