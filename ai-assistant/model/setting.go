package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"gorm.io/gorm"
	"yz-go/model"
	"yz-go/source"
)

type AiSetting struct {
	model.SysSetting
	Value AiValue `json:"value"`
}

func (i AiSetting) TableName() string {
	return "sys_settings"
}

type AiValue struct {
	SiliconFlowAppKey string `json:"silicon_flow_app_key"`
	AiType            string `json:"ai_type"`
	DefaultInput      string `json:"default_input"` //默认输入
	Prompt            string `json:"prompt"`        //提示词，一行一个
}

func (value AiValue) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *AiValue) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

var aiSetting *AiValue

func getAiSetting(key string) (err error, sysSetting AiSetting) {
	err = source.DB().Where("`key` = ?", key).First(&sysSetting).Error
	return
}
func GetAiSetting() (err error, setting AiValue) {
	if aiSetting == nil {
		var sysSetting AiSetting
		err, sysSetting = getAiSetting("ai_setting")
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil
		}
		if err != nil {
			return
		}
		aiSetting = &sysSetting.Value
	}
	return err, *aiSetting
}

func ResetAi() {
	//重置全局变量 start
	aiSetting = nil
}
