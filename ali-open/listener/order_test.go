package listener

import (
	alimodel "ali-open/model"
	"ali-open/mq"
	"fmt"
	"gorm.io/gorm"
	"testing"
	"yz-go/source"
)

func TestAlibbOrder(t *testing.T) {

	var reqData alimodel.CallBackData

	mq.PublishMessage(1, reqData, 1)

	return

	var OrderMap = make(map[string]alimodel.SkuMap)
	var sku alimodel.Sku
	sku.SpecId = "333"
	sku.Quantity = 1
	sku.OfferId = 2
	OrderMap["aaaa"] = append(OrderMap["aaaa"], sku)
	OrderMap["aaaa"] = append(OrderMap["aaaa"], sku)

	fmt.Println(OrderMap)
	fmt.Println(sku)

	for key, item := range OrderMap {

		fmt.Println(key, item)

	}

	//IsEnable("aliOpenSettingyxa")
	return

	Note := "阿里巴巴已同步下单" + " - 账户:" + "3333" + "- 单号：" + "555"

	//source.DB().Table("orders").Where("order_sn=?", request.OrderSn.OrderSn).UpdateColumn("note", gorm.Expr("CONCAT_WS('-',note,?)", Note))
	source.DB().Table("orders").Where("order_sn=?", "182659848134").Updates(map[string]interface{}{"note": gorm.Expr("CONCAT_WS('-',note,?)", Note, "gather_supply_sn"), "gather_supply_sn": gorm.Expr("CONCAT_WS('-',gather_supply_sn,?)", "555")})

	return
	//var skuMap alimodel.SkuMap
	//var sku alimodel.Sku
	//var OrderMap = make(map[string]alimodel.SkuMap)
	//
	//sku.OfferId = 1
	//sku.SpecId = "333"
	//sku.Quantity = 2
	//
	//OrderMap["aaa"] = append(OrderMap["aaa"], sku)
	//OrderMap["bbb"] = append(OrderMap["bbb"], sku)
	//OrderMap["ccc"] = append(OrderMap["ccc"], sku)
	//OrderMap["aaa"] = append(OrderMap["aaa"], sku)
	//
	//fmt.Println(len(OrderMap))
	//
	//for index, item := range OrderMap {
	//
	//	fmt.Println(index, item)
	//}

	return

	AlibbOrder(180)

}

func TestPushAlibbOrderHandles(t *testing.T) {

}
