package goods

import (
	model3 "ali-selected/model"
	aljxmq "ali-selected/productSync"
	request2 "ali-selected/request"
	"errors"
	gatherSupplyRequest "gather-supply/request"
	"github.com/chenhg5/collection"
	log2 "log"
	"math"
	"product/mq"
	"public-supply/common"
	"public-supply/service"
	gsetting "public-supply/setting"
	"sort"
	model2 "yz-go/model"

	//"ali-selected/common"
	//model3 "ali-selected/model"
	catemodel "category/model"
	"context"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/gogf/gf/frame/g"
	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
	url2 "net/url"
	pmodel "product/model"
	pservice "product/service"
	callback2 "public-supply/callback"
	common2 "public-supply/common"
	"public-supply/model"
	"public-supply/request"
	"strconv"
	"strings"
	"sync"
	"time"
	"yz-go/component/log"
	"yz-go/source"
	"yz-go/utils"
)

type AliJX struct {
	dat      model.SupplySetting
	Setting  model.SupplySetting
	SupplyID uint
	Http     string
}

func (y *AliJX) SynchronizeProductsToLocal() (err error) {
	//TODO implement me
	panic("implement me")
}

func (y *AliJX) ImportSelectGoodsRun(info model.SelectGoods) (err error, list interface{}) {
	orderPN := GetOrderNo()
	common.GlobalOrderSN = orderPN
	goodsRecord := model.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: len(info.List),
		Status:            1,
	}
	var resultInt []int
	var field string
	field = "source_goods_id"

	err = source.DB().Model(pmodel.Product{}).Where("gather_supply_id=?", info.GatherSupplyID).Pluck(field, &resultInt).Error
	if err != nil {
		log2.Println("查询供应链商品id错误", err)
		return
	}

	if len(info.List) <= 0 {
		log2.Println("导入空数据")
		err = errors.New("导入的是空数据")
		return
	}

	var idsArr []int
	idsArr = GetSnList(info.List)

	difference := collection.Collect(idsArr).Diff(resultInt).ToIntArray()
	repeat := len(idsArr) - len(difference)
	goodsRecord.RepeatQuantity = repeat //重复数据
	if len(difference) <= 0 {
		goodsRecord.Status = 2
		goodsRecord.CompletionStatus = 1
		source.DB().Omit("goods_arr").Create(&goodsRecord) //当前没有商品可以导入
		return
	}
	source.DB().Omit("goods_arr").Create(&goodsRecord) //创建导入记录
	var goodsList []model.Goods
	for _, v := range difference {
		for _, item := range info.List {
			sn, _ := strconv.Atoi(item.SN)
			if sn == v {
				goodsList = append(goodsList, item)
			}
		}

	}
	info.List = goodsList
	arrList := splitArray(info.List, 20)
	for index, item := range arrList {
		wg.Add(1)
		fmt.Println("循环", index)
		y.RunSelectGoodsConcurrent(orderPN, item, info.Categorys, info.Key, info.GatherSupplyID)
	}
	wg.Wait()
	err = SetImportRecordCompletion(orderPN)
	fmt.Println("全部完成：")

	return
}

func (y *AliJX) RunSelectGoodsConcurrent(orderPN string, list []model.Goods, category string, key string, gatherSupplyID uint) (err error) {

	defer wg.Done()

	cateList := strings.Split(category, ",")
	var cateId1, cateId2, cateId3 int
	cateId1, err = strconv.Atoi(cateList[0])
	cateId2, err = strconv.Atoi(cateList[1])
	cateId3, err = strconv.Atoi(cateList[2])

	var listGoods []*pmodel.Product

	var recordError []model.SupplyGoodsImportRecordErrors
	err, listGoods, recordError = y.CommodityAssembly(list, cateId1, cateId2, cateId3, gatherSupplyID, 0)
	if err != nil {
		return
	}
	if len(listGoods) > 0 {
		err = service.FinalProcessing(listGoods, orderPN)
		if err != nil {
			return
		}

	}
	if len(recordError) > 0 {
		err = service.FinalProcessingError(recordError, orderPN)
		if err != nil {
			return
		}
	}
	return

}
func (y *AliJX) ValidateConfig(params gatherSupplyRequest.ValidateConfigParams) (err error) {
	// 需要跳转页面获取code，根据code获取token，这里就不加验证了
	return
}

func (y *AliJX) DeleteGoods(id uint) (err error) {
	//TODO implement me
	return
}

var TokenKey = "ALI_SELECTED_TOKEN"

func (y *AliJX) GetToken() (err error) {
	var ctx = context.Background()
	token, _ := source.Redis().Get(ctx, TokenKey).Result()

	if token == "" {
		if y.dat.BaseInfo.Token != "" {
			return
		}

		err = source.Redis().SetEX(ctx, TokenKey, y.dat.BaseInfo.Token, time.Hour*20).Err()

	} else {
		y.dat.BaseInfo.Token = token
	}

	return
}

type ResBalance struct {
	RESPONSESTATUS string `json:"RESPONSE_STATUS"`
	RESULTDATA     struct {
		REMAIN      float64 `json:"REMAIN"`
		CREDITTOTAL float64 `json:"CREDIT_TOTAL"`
		CREDITUSED  float64 `json:"CREDIT_USED"`
	} `json:"RESULT_DATA"`
}

func (y *AliJX) GetSupplyBalance(GatherSupplyID uint) (err error, balance interface{}) {

	balance = 0

	return
}

func (y *AliJX) GoodsPriceAlert(GoodsData callback2.GoodsCallBack) (err error) {
	return
}

//func (y *AliJX) CommodityAssembly(list []model.Goods, cateId1, cateId2, cateId3 int, gatherSupplyID uint, isCreate int) (err error, listGoods []*pmodel.Product) {
//	//panic("implement me")
//}

func (y *AliJX) InitGoods() (err error) {
	return
}

func (y *AliJX) CommodityAssembly(list []model.Goods, cateId1, cateId2, cateId3 int, gatherSupplyID uint, isCreate int) (err error, listGoods []*pmodel.Product, recordErrors []model.SupplyGoodsImportRecordErrors) {
	if len(list) == 0 {
		log.Log().Error("选择可导商品数量为空", zap.Any("err", err))
		return
	}
	for _, item := range list {
		var Goods model3.AliGoods
		err = source.DB().Where("id=?", item.ID).First(&Goods).Error
		if err != nil {
			log.Log().Error("查询yzh商品数据错误", zap.Any("err", err))
			return
		}

		var goodsDetail []*pmodel.Product
		err, goodsDetail = y.CommodityAssemblyLocal(Goods, cateId1, cateId2, cateId3)
		if err != nil {
			log.Log().Error("解析yzh选择商品数据错误", zap.Any("err", err))
			return
		}
		listGoods = append(listGoods, goodsDetail...)
	}
	return
}

func (y *AliJX) GoodsStorageAdd(ids []int, supplyId uint) (err error, info interface{}) {
	//panic("implement me")

	return
}

func (y *AliJX) InitSetting(gatherSupplyID uint) (err error) {
	y.SupplyID = gatherSupplyID

	var setting model2.SysSetting
	err, setting = gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}
	err = json.Unmarshal([]byte(setting.Value), &y.dat)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}

	y.Setting = y.dat
	if y.dat.BaseInfo.AppKey == "" && y.dat.BaseInfo.AppSecret == "" {
		err = errors.New("请先配置供应链key")
		return
	}

	//y.Http = y.dat.BaseInfo.ApiUrl
	//y.Http = y.dat.BaseInfo.ApiUrl

	y.dat.BaseInfo.AppKey = y.dat.BaseInfo.AppKey
	y.dat.BaseInfo.AppSecret = y.dat.BaseInfo.AppSecret
	y.dat.BaseInfo.Token = y.dat.BaseInfo.Token
	//y.dat.BaseInfo.AppKey = "7618374"
	//y.dat.BaseInfo.AppSecret = "j6UTJVfvUTN"
	//y.dat.BaseInfo.Token = "5140b68e-a6f6-4b24-8326-9908739929e2"

	//y.GetToken()

	return
}

func (ali *AliJX) CodeGetToken(code string) (err error, data interface{}) {

	url := "https://gw.open.1688.com/openapi/http/1/system.oauth2/getToken/" + ali.dat.BaseInfo.AppKey + "?grant_type=authorization_code&need_refresh_token=true&client_id=" + ali.dat.BaseInfo.AppKey + "&client_secret=" + ali.dat.BaseInfo.AppSecret + "&redirect_uri=" + ali.dat.BaseInfo.ApiUrl + "&code=" + code
	reqData := url2.Values{}
	var resData []byte
	err, resData = utils.PostForm(url, reqData, nil)
	var token model3.ResToken
	err = json.Unmarshal(resData, &token)
	if token.ErrorInfo != "" {
		err = errors.New(token.ErrorDescription)
		fmt.Println("获取错误", token)
		return
	}
	data = token.AccessToken

	log.Log().Info("info", zap.Any("token", token))
	//ali.SetToken(token.AccessToken)
	return
}

//
//func (y *AliJX) GetStock(pid []string) (goodsStock model3.YzhGoodsStock, err error) {
//
//	url := string(y.Http + "open/api/goods/queryGoodsStockList")
//	var resData []byte
//	headerData := make(map[string]interface{})
//
//	headerData["accessToken"] = y.dat.BaseInfo.Token
//	headerData["goodsSkuCode"] = pid
//
//	reqData, _ := json.Marshal(headerData)
//
//	resData = utils.HttpPostJson(reqData, url)
//
//	json.Unmarshal(resData, &goodsStock)
//	if goodsStock.Success != true {
//		err = errors.New("库存查询错误")
//		return
//	}
//
//	fmt.Println(string(resData))
//
//	return
//}

func (*AliJX) GetGoods(info request.GetGoodsSearch) (err error, data interface{}, total int64, serverRatio int) {

	db := source.DB().Model(model3.AliGoods{})

	if info.SearchWords != "" {
		db = db.Where("`title` LIKE ?", info.SearchWords+"%")
	}
	if info.ShopWords != "" {
		db = db.Where("`wangwang_account` = ?", info.ShopWords)
	}
	if info.CategoryStrID != "" {
		db = db.Where("cate_id1=? or cate_id2=? or category_id=?", info.CategoryStrID, info.CategoryStrID, info.CategoryStrID)
	}

	if info.IsImport == 1 {

		db = db.Where("is_import", 0)

	}
	if info.IsImport == 2 {

		db = db.Where("is_import", 1)

	}

	//if info.Categorys != "" {
	//	db = db.Where("last_category_name LIKE ?", "%"+info.Categorys+"%")
	//}

	if info.Sort != "" {

		if info.Type == "created_time" {
			db = db.Order("created_at" + " " + info.Sort)
		}

	}

	if info.RangeType == "agreement_price" {
		db = db.Where("consign_price between ? and ?", info.RangeForm, info.RangeTo)
	}
	if info.RangeType == "activity_price" || info.RangeType == "guide_price" {
		db = db.Where("retail_price between ? and ?", info.RangeForm, info.RangeTo)
	}

	var productList []int64
	source.DB().Model(pmodel.Product{}).Where("source=? and gather_supply_id=?", common2.ALJX_SOURCE, info.GatherSupplyID).Pluck("source_goods_id", &productList)

	var list []model.Goods
	var yzhList []model3.AliGoods
	var count int64
	limit := info.Limit
	offset := info.Limit * (info.Page - 1)
	if info.RangeType == "promotion_rate" {
		db.Where("((retail_price-consign_price)/consign_price) >=?  and   ((retail_price-consign_price)/consign_price) <=? ", info.RangeForm, info.RangeTo)

	}

	err = db.Count(&count).Error
	db.Limit(limit).Offset(offset).Find(&yzhList)

	for _, item := range yzhList {

		isImport := 0

		rates := item.ProfitRate

		for _, pitem := range productList {

			if pitem == item.ProductID {
				isImport = 1
			}
		}
		sn := strconv.Itoa(int(item.ProductID))

		list = append(list, model.Goods{
			WangWangAccount: item.WangwangAccount,
			Title:           item.Title,
			MarketPrice:     uint(item.RetailPrice * 100),
			Rate:            rates,
			ThirdBrandName:  item.CategoryName,
			Cover:           item.MainImage,
			AgreementPrice:  uint(item.ConsignPrice * 100),
			CostPrice:       uint(item.ConsignPrice * 100),
			GuidePrice:      uint(item.RetailPrice * 100),
			ActivityPrice:   uint(item.RetailPrice * 100),
			SalePrice:       uint(item.RetailPrice * 100),
			ID:              int(item.ID),
			SN:              sn,
			IsImport:        uint(isImport),
		})

	}
	data = list
	total = count
	return
}

var pageSize = "50"

func (y *AliJX) RunUpdateGoodsRun(page string, cate model3.SyncProductData) (err error) {

	err, resData := y.getPageList(page, cate.Cate)

	if err != nil {
		return err
	}

	var modelData model3.ALIJXGoodsList
	err = json.Unmarshal(resData, &modelData)
	if err != nil {
		return err
	}
	if len(modelData.Result.Result) == 0 && modelData.Result.PageInfo.TotalRecords == 0 {
		err = errors.New("notfind")
		return
	}
	if modelData.Result.Success != true {
		return
	}

	var lists []int

	for _, item := range modelData.Result.Result {
		skuCode := item.ItemId
		lists = append(lists, int(skuCode))
	}

	if len(lists) == 0 {
		return
	}

	arrList := SplitArray(lists, 25)

	for _, arrItem := range arrList {
		y.SynergeticProcess(arrItem, cate)

	}

	return
}

func Sign(urlPath, Secret string, str url2.Values) string {

	if urlPath == "" {
		return ""
	}

	urlpath := strings.Split(urlPath, "openapi/")
	if len(urlpath) < 2 {
		return ""
	}
	path := urlpath[1]
	maplist := make(map[string]string)
	for i, item := range str {
		maplist[i] = item[0]
	}
	var signature string
	//按照字母顺序遍历
	traverseMapInStringOrder(maplist, func(key string, value string) {
		signature += key + value
	})

	signStr := path + signature
	sign := HmacSha1(signStr, Secret)
	signUpper := strings.ToUpper(sign)
	fmt.Println(string(signUpper))
	return signUpper

	//return "ACD581CC036CC0D2D46FF892D0B9B455F8BE60B7"
}
func SignMap(urlPath, Secret string, str url2.Values) string {

	if urlPath == "" {
		return ""
	}

	urlpath := strings.Split(urlPath, "openapi/")
	if len(urlpath) < 2 {
		return ""
	}
	path := urlpath[1]
	maplist := make(map[string]string)
	for i, item := range str {
		maplist[i] = item[0]
	}
	var signature string
	//按照字母顺序遍历
	traverseMapInStringOrder(maplist, func(key string, value string) {
		signature += key + value
	})

	signStr := path + signature
	sign := HmacSha1(signStr, Secret)
	signUpper := strings.ToUpper(sign)
	fmt.Println(string(signUpper))
	return signUpper

	//return "ACD581CC036CC0D2D46FF892D0B9B455F8BE60B7"
}
func HmacSha1(data string, secret string) string {
	h := hmac.New(sha1.New, []byte(secret))
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

var Category1ID, Category2ID, Category3ID int
var Category1Name, Category2Name string

func (y *AliJX) GetCategoryGetGoods() (err error) {
	var info request.GetCategoryChild
	_, cate1 := y.GetCategoryInfo(0, info)
	if len(cate1.CategoryInfo) <= 0 {
		return
	}
	for _, cate1Item := range cate1.CategoryInfo[0].ChildCategorys {
		Category1ID = cate1Item.Id
		Category1Name = cate1Item.Name
		_, cate2 := y.GetCategoryInfo(cate1Item.Id, info)
		if len(cate2.CategoryInfo) <= 0 {
			continue
		}
		for _, cate2Item := range cate2.CategoryInfo[0].ChildCategorys {
			Category2ID = cate2Item.Id
			Category2Name = cate2Item.Name

			_, cate3 := y.GetCategoryInfo(cate2Item.Id, info)
			if len(cate3.CategoryInfo) <= 0 {
				continue
			}

			stringsID := strconv.Itoa(cate2Item.Id)
			var SyncProductData model3.SyncProductData
			SyncProductData.Cate = stringsID
			SyncProductData.Cate1 = Category1ID
			SyncProductData.Cate2 = Category2ID
			SyncProductData.Cate1Name = Category1Name
			SyncProductData.Cate2Name = Category2Name
			SyncProductData.SupplyID = y.SupplyID
			aljxmq.PublishMessage(y.SupplyID, SyncProductData, 1)

		}

	}

	return
}

func (y *AliJX) UpdateGoodsRun(info request.GetGoodsSearch) (err error) {

	y.GetCategoryGetGoods()

	//source.DB().Unscoped().Delete(model3.AliGoods{}, "id >=1")

	//url := "https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao/alibaba.pifatuan.product.list/" + y.dat.BaseInfo.AppKey
	//
	//reqData := url2.Values{}
	//reqData.Add("access_token", y.dat.BaseInfo.Token)
	//reqData.Add("categoryId", "122916002")
	//reqData.Add("_aop_signature", Sign(url, y.dat.BaseInfo.AppSecret, reqData))
	//var resData []byte
	//err, resData = utils.PostForm(url, reqData, nil)
	//if err != nil {
	//	return err
	//}

	//var modelData model3.ALIJXGoodsList
	//err = json.Unmarshal(resData, &modelData)
	//if modelData.Result.Success != true {
	//	fmt.Println("请求结果错误")
	//	return
	//}
	//if err != nil {
	//	return err
	//}

	//var count = modelData.Result.Result.TotalRecords
	//orderPN := utils.GetOrderNo()
	//goodsRecord := model.SupplyGoodsImportRecord{
	//	Batch:             orderPN,
	//	EstimatedQuantity: count,
	//	Status:            1,
	//	SearchCriteria:    "all",
	//}
	//pageCount := count / 20
	//source.DB().Omit("goods_arr").Create(&goodsRecord)
	//for i := 1; i <= pageCount; i++ {
	//	page := strconv.Itoa(i)
	//	y.RunUpdateGoodsRun(page)
	//}
	//
	//err = SetImportRecordCompletion(orderPN)
	//if err != nil {
	//	fmt.Println("变更导入记录状态错误", err)
	//}
	//fmt.Println("导入供应链商品全部完成")
	//time.Sleep(time.Second * 5)
	//source.DB().Exec("DROP TABLE IF EXISTS `yzh_product_goods`;")
	//source.DB().Exec("CREATE TABLE yzh_product_goods  select * from yzh_product_temps  ;")
	//log.Log().Info("批量同步yzh商品到本地全部完成！！！")
	return

}

func (y *AliJX) ImportGoodsRun(info request.GetGoodsSearch) (err error, data interface{}) {
	db := source.DB().Model(model3.AliGoods{})
	if info.SearchWords != "" {
		db = db.Where("`subject` LIKE ?", "%"+info.SearchWords+"%")
	}
	if info.Category1ID > 0 {
		db = db.Where("cate_id1=? ", info.Category1ID)
	}
	if info.Category2ID > 0 {
		db = db.Where("cate_id2=? ", info.Category2ID)
	}
	if info.Category3ID > 0 {
		db = db.Where("category_id=? ", info.Category3ID)
	}
	if info.ShopWords != "" {
		db = db.Where("`wangwang_account` LIKE ?", "%"+info.ShopWords+"%")
	}

	if info.IsImport == 1 {
		db = db.Where("not EXISTS(select * from products where source=116 and  products.source_goods_id=ali_goods.product_id)")
	}
	if info.IsImport == 2 {

		db = db.Where("EXISTS(select * from products where source=116 and products.source_goods_id=ali_goods.product_id)")

	}

	var goodsList []model3.AliGoods
	err = db.Find(&goodsList).Error
	if err != nil {
		log.Log().Error("查询导入商品错误", zap.Any("err", err))
		return
	}
	var count = len(goodsList)
	orderPN := utils.GetOrderNo()
	goodsRecord := model.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: count,
		Status:            1,
		SearchCriteria:    "all",
	}

	source.DB().Omit("goods_arr").Create(&goodsRecord)
	//var wg sync.WaitGroup
	for _, item := range goodsList {
		wg.Add(1)
		y.ImportLocalGoods(&wg, item, orderPN, info.Categorys)
	}

	wg.Wait()
	err = SetImportRecordCompletion(orderPN)
	if err != nil {
		fmt.Println("变更导入记录状态错误", err)

	}
	fmt.Println("导入供应链商品全部完成")
	return

}

func (y *AliJX) ImportLocalGoods(wg *sync.WaitGroup, goodsList model3.AliGoods, orderPN string, caters string) {
	defer wg.Done()
	ids := strings.Split(caters, ",")
	var cate1, cate2, cate3 int
	if len(ids) >= 3 {
		cate1, _ = strconv.Atoi(ids[0])
		cate2, _ = strconv.Atoi(ids[1])
		cate3, _ = strconv.Atoi(ids[2])
	}
	//var detail model.YzhGoodsDetail
	//detail.RESULTDATA.PRODUCTDATA = yzhList
	//detail.CategoryNames = yzhList.CateNames

	err, listGoods := y.CommodityAssemblyLocal(goodsList, cate1, cate2, cate3)
	if err != nil {
		log.Log().Error("CommodityAssemblyLocal错误", zap.Any("err", err))
	}

	if len(listGoods) > 0 {
		service.FinalProcessing(listGoods, orderPN)
	}

	return
}

//
//func (y *AliJX) SaleCheck(id []int) (saleStatus model3.YzhSaleStatus) {
//
//	url := y.Http + "/open/api/goods/queryGoodsShelvesList"
//	var resData []byte
//	headerData := make(map[string]interface{})
//	headerData["accessToken"] = y.dat.BaseInfo.Token
//	headerData["goodsSkuCode"] = id
//	reqData, _ := json.Marshal(headerData)
//	resData = utils.HttpPostJson(reqData, url)
//	//var saleStatus model3.YzhSaleStatus
//	json.Unmarshal(resData, &saleStatus)
//
//	return
//
//	//if saleStatus.Success != true {
//	//	err = errors.New(saleStatus.Desc)
//	//	return
//	//}
//	//for _, item := range saleStatus.Result {
//	//	if item.ShelvesStatus != 1001 {
//	//		err = errors.New("商品已经下架")
//	//		return
//	//	}
//	//}
//
//}

func (y *AliJX) GetYzhGoodsDetail(item int64) (detail model.YzhGoodsDetail) {

	return
}

func (y *AliJX) QueryGoodsImageList(item []int64) (imgMap interface{}) {

	return

}

func (y *AliJX) YzhProductDetail(item []int64) (err error, yzhTempGoods model.YzhGoods) {
	url := string(y.Http + "/open/api/goods/queryGoodsDetail")
	var resData []byte
	headerData := make(map[string]interface{})

	headerData["accessToken"] = y.dat.BaseInfo.Token
	headerData["goodsSkuCode"] = item

	reqData, _ := json.Marshal(headerData)

	resData = utils.HttpPostJson(reqData, url)
	if err != nil {
		return
	}
	//var imgList = map[string][]model3.ImageList{}
	//imgList := y.QueryGoodsImageList(item)

	//fmt.Println(imgList)
	//var yzhTempGoods model.YzhGoods

	err = json.Unmarshal(resData, &yzhTempGoods)
	if err != nil {
		return
	}

	return
}
func (y *AliJX) GetProductInfo(item uint) (err error, modelData model3.ALJXNewDetail) {
	url := "https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao/alibaba.fenxiao.productInfo.get/" + y.dat.BaseInfo.AppKey
	reqDataParam := url2.Values{}
	reqDataParam.Add("access_token", y.dat.BaseInfo.Token)
	id := strconv.Itoa(int(item))
	reqDataParam.Add("offerId", id)
	reqDataParam.Add("_aop_signature", Sign(url, y.dat.BaseInfo.AppSecret, reqDataParam))
	var resData []byte
	err, resData = utils.PostForm(url, reqDataParam, nil)
	if err != nil {
		return
	}

	err = json.Unmarshal(resData, &modelData)

	return
}
func (y *AliJX) getPageList(page, cateID string) (err error, resData []byte) {

	url := "https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao/jxhy.product.getPageList/" + y.dat.BaseInfo.AppKey

	reqData := url2.Values{}
	reqData.Add("access_token", y.dat.BaseInfo.Token)
	reqData.Add("pageNum", page)
	reqData.Add("categoryId", cateID)
	reqData.Add("pageSize", pageSize)
	reqData.Add("_aop_signature", Sign(url, y.dat.BaseInfo.AppSecret, reqData))

	err, resData = utils.PostForm(url, reqData, nil)

	fmt.Println(resData)
	if err != nil {
		return
	}

	return

}
func (y *AliJX) SynergeticProcess(item []int, cate model3.SyncProductData) (err error) {
	//url := "https://gw.open.1688.com/openapi/param2/2/com.alibaba.fenxiao/alibaba.pifatuan.product.detail.list/" + y.dat.BaseInfo.AppKey
	//reqDataParam := url2.Values{}
	//reqDataParam.Add("access_token", y.dat.BaseInfo.Token)
	//jsonItem, _ := json.Marshal(&item)
	//reqDataParam.Add("offerIds", string(jsonItem))
	//reqDataParam.Add("_aop_signature", Sign(url, y.dat.BaseInfo.AppSecret, reqDataParam))
	//var resData []byte
	//err, resData = utils.PostForm(url, reqDataParam, nil)
	//
	for _, itemID := range item {
		//var modelData model3.ALJXDetail

		detailErr, dataDetail := y.GetProductInfo(uint(itemID))
		//modelData.Result.Result=
		//err = json.Unmarshal(resData, &modelData)

		if detailErr != nil {
			return
		}
		if dataDetail.Success != true {
			return
		}
		//if err != nil {
		//	return err
		//}
		//if modelData.Result.Success != true {
		//	return
		//}
		var Product []model3.AliGoods
		//for _, goodsItem := range data.ProductInfo {

		goods := model3.AliGoods{}
		goods.Title = dataDetail.ProductInfo.Subject

		var price float64
		if len(dataDetail.ProductInfo.ProductSkuInfos) > 0 {
			price = dataDetail.ProductInfo.ProductSkuInfos[0].ConsignPrice
			if dataDetail.ProductInfo.ProductSkuInfos[0].ConsignPrice < dataDetail.ProductInfo.ProductSkuInfos[0].JxhyPrice {
				price = dataDetail.ProductInfo.ProductSkuInfos[0].JxhyPrice
			}

		} else {
			price = dataDetail.ProductInfo.ProductSaleInfo.ConsignPrice

			if dataDetail.ProductInfo.ProductSaleInfo.ConsignPrice < dataDetail.ProductInfo.ProductSaleInfo.JxhyPrice {
				price = dataDetail.ProductInfo.ProductSaleInfo.JxhyPrice
			}
		}

		goods.ProductID = dataDetail.ProductInfo.ProductID
		goods.Status = dataDetail.ProductInfo.Status
		jsonSku, _ := json.Marshal(dataDetail.ProductInfo.ProductSkuInfos)
		jsonSale, _ := json.Marshal(dataDetail.ProductInfo.ProductSaleInfo)
		JsonImage, _ := json.Marshal(dataDetail.ProductInfo.ProductImage.Images)
		goods.JsonSku = string(jsonSku)
		goods.JsonImage = string(JsonImage)
		goods.JsonSaleInfo = string(jsonSale)
		if len(dataDetail.ProductInfo.ProductImage.Images) > 0 {
			goods.MainImage = dataDetail.ProductInfo.ProductImage.Images[0]
		}

		goods.CategoryName = dataDetail.ProductInfo.CategoryName
		goods.CategoryID = dataDetail.ProductInfo.CategoryID
		goods.CateID2 = cate.Cate2
		goods.CateName2 = Category2Name
		goods.WangwangAccount = dataDetail.ProductInfo.SupplierLoginId
		goods.CateName1 = Category1Name
		goods.CateID1 = cate.Cate1
		goods.ConsignPrice = price
		goods.RetailPrice = price
		Product = append(Product, goods)

		err = source.DB().Create(&Product).Error

	}

	return
}

func (y *AliJX) ImportSeparatelySynergeticProcess(item []int) (err error) {

	for _, itemID := range item {

		detailErr, dataDetail := y.GetProductInfo(uint(itemID))

		if detailErr != nil {
			return
		}
		if dataDetail.Success != true {
			return
		}

		var Product []model3.AliGoods

		goods := model3.AliGoods{}
		goods.Title = dataDetail.ProductInfo.Subject

		var price float64
		if len(dataDetail.ProductInfo.ProductSkuInfos) > 0 {
			price = dataDetail.ProductInfo.ProductSkuInfos[0].ConsignPrice
			if dataDetail.ProductInfo.ProductSkuInfos[0].ConsignPrice < dataDetail.ProductInfo.ProductSkuInfos[0].JxhyPrice {
				price = dataDetail.ProductInfo.ProductSkuInfos[0].JxhyPrice
			}

		} else {
			price = dataDetail.ProductInfo.ProductSaleInfo.ConsignPrice

			if dataDetail.ProductInfo.ProductSaleInfo.ConsignPrice < dataDetail.ProductInfo.ProductSaleInfo.JxhyPrice {
				price = dataDetail.ProductInfo.ProductSaleInfo.JxhyPrice
			}
		}

		goods.ProductID = dataDetail.ProductInfo.ProductID
		goods.Status = dataDetail.ProductInfo.Status
		jsonSku, _ := json.Marshal(dataDetail.ProductInfo.ProductSkuInfos)
		jsonSale, _ := json.Marshal(dataDetail.ProductInfo.ProductSaleInfo)
		JsonImage, _ := json.Marshal(dataDetail.ProductInfo.ProductImage.Images)
		goods.JsonSku = string(jsonSku)
		goods.JsonImage = string(JsonImage)
		goods.JsonSaleInfo = string(jsonSale)
		if len(dataDetail.ProductInfo.ProductImage.Images) > 0 {
			goods.MainImage = dataDetail.ProductInfo.ProductImage.Images[0]
		}

		var info request.GetCategoryChild
		_, cate := y.GetCategoryInfo(dataDetail.ProductInfo.CategoryID, info)

		if len(cate.CategoryInfo) > 0 {

			if len(cate.CategoryInfo[0].ParentIDs) > 0 {
				_, cate2 := y.GetCategoryInfo(cate.CategoryInfo[0].ParentIDs[0], info)
				if len(cate2.CategoryInfo) > 0 {

					goods.CateID2 = cate2.CategoryInfo[0].CategoryID
					goods.CateName2 = cate2.CategoryInfo[0].Name

					if len(cate2.CategoryInfo[0].ParentIDs) > 0 {
						_, cate1 := y.GetCategoryInfo(cate2.CategoryInfo[0].ParentIDs[0], info)

						if len(cate1.CategoryInfo) > 0 {

							goods.CateName1 = cate1.CategoryInfo[0].Name
							goods.CateID1 = cate1.CategoryInfo[0].CategoryID
						}

					}
				}
			}

		}

		goods.CategoryName = dataDetail.ProductInfo.CategoryName
		goods.CategoryID = dataDetail.ProductInfo.CategoryID

		goods.WangwangAccount = dataDetail.ProductInfo.SupplierLoginId

		goods.ConsignPrice = price
		goods.RetailPrice = price
		Product = append(Product, goods)

		err = source.DB().Create(&Product).Error
		if err != nil {
			log.Log().Error("create err aligoods", zap.Any("err", err))
		}
	}

	return
}

var wg sync.WaitGroup

func (y *AliJX) RunGoods(maps []int64, orderPN string) {

	//for index, item := range maps {
	//
	//	wg.Add(1)
	//if index%10 == 0 {
	//	time.Sleep(time.Second * 1)
	//}
	//go y.SynergeticProcess(item)
	//if index >1000 {
	//	return
	//}
	//yzhProduct.ProductImage=goodsDetail.RESULTDATA.PRODUCTIMAGE

	//var resultArr int
	//err = source.DB().Select("id").Model(model.SupplyGoods{}).Where("gather_supply_id=? and supply_goods_id=?", y.SupplyID,item).Pluck("supply_goods_id",&resultArr).Error
	//if err != nil {
	//	log.Log().Error("查询错误",zap.Any("err",err))
	//	continue
	//}

	//var listGoods []*pmodel.Product
	//
	//err, listGoods = y.CommodityAssemblyA(goodsDetail)
	//
	//if len(listGoods) > 0 {
	//	FinalProcessing(listGoods, orderPN)
	//}

	//
	//difference := collection.Collect(resultArr).WhereIn(goodsDetail.RESULTDATA.PRODUCTDATA)

	//}
	//wg.Wait()

}

// 获取胜天半子分类数据
func (y *AliJX) GetCategory(cate request.GetCategorySearch) (err error, data interface{}) {

	var info request.GetCategoryChild
	err, Listdata := y.GetCategoryChildSub(0, info)

	fmt.Println(Listdata)
	if err != nil {
		fmt.Println("获取以及分类错误")
	}
	AllCateData = Listdata.([]model.RESULTDATA)
	for _, item := range AllCateData {

		IsDisplay := 1

		var cate1 catemodel.Category
		cate1.Name = item.Name
		cate1.Source = int(common2.YZH_NEW)
		cate1.Level = 1
		cate1.ParentID = 0
		cate1.IsDisplay = &IsDisplay
		source.DB().Where("level=1 and name =?", item.Name).FirstOrCreate(&cate1)
		err, cdata := y.GetCategoryChildSub(int(item.Code), info)
		if err != nil {
			fmt.Println("获取二级分类错误")
		}
		clist := cdata.([]model.RESULTDATA)
		//AllCateData = append(AllCateData, clist...)
		for _, citem := range clist {
			var cate2 catemodel.Category
			cate2.Name = citem.Name
			cate2.Source = 100
			cate2.Level = 2
			cate2.ParentID = cate1.ID
			cate2.IsDisplay = &IsDisplay
			source.DB().Where("level=2 and name =?", citem.Name).FirstOrCreate(&cate2)

			time.Sleep(time.Microsecond * 100000)
			err, ccdata := y.GetCategoryChildSub(int(citem.Code), info)
			if err != nil {
				fmt.Println("获取二级分类错误")
			}
			cclist := ccdata.([]model.RESULTDATA)

			for _, csitem := range cclist {
				var cate3 catemodel.Category
				cate3.Name = csitem.Name
				cate3.Source = 100
				cate3.Level = 3
				cate3.ParentID = cate2.ID
				cate3.IsDisplay = &IsDisplay
				source.DB().Where("level=3 and name =?", csitem.Name).FirstOrCreate(&cate3)

			}

		}

	}

	return
}

func (y *AliJX) GetGroup() (err error, data interface{}) {

	return

}

var AllCateData []model.RESULTDATA

func (y *AliJX) GetCategoryChild(pid int, info request.GetCategoryChild) (err error, modelData interface{}) {

	err, modelData = y.GetCategoryTotal(pid, info)

	//url := "https://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.category.get/" + y.dat.BaseInfo.AppKey
	//reqDataParam := url2.Values{}
	//reqDataParam.Add("access_token", y.dat.BaseInfo.Token)
	//categoryID := strconv.Itoa(pid)
	//reqDataParam.Add("categoryID", categoryID)
	//reqDataParam.Add("_aop_signature", Sign(url, y.dat.BaseInfo.AppSecret, reqDataParam))
	//var resData []byte
	//err, resData = utils.PostForm(url, reqDataParam, nil)
	//if err != nil {
	//	return
	//}
	//
	//err = json.Unmarshal(resData, &modelData)
	//if modelData.Succes != "true" {
	//	err = errors.New("请求错误")
	//	return
	//}
	//
	//if len(modelData.CategoryInfo) == 0 {
	//	err = errors.New("未查到分类")
	//	return
	//}
	//
	//if modelData.CategoryInfo[0].Name == "" {
	//	err = errors.New("分类名为空")
	//	return
	//}

	if err != nil {
		return
	}

	return

}

func (y *AliJX) GetCategoryInfo(pid int, info request.GetCategoryChild) (err error, modelData model3.ALCategory) {

	url := "https://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.category.get/" + y.dat.BaseInfo.AppKey
	reqDataParam := url2.Values{}
	reqDataParam.Add("access_token", y.dat.BaseInfo.Token)
	categoryID := strconv.Itoa(pid)
	reqDataParam.Add("categoryID", categoryID)
	reqDataParam.Add("_aop_signature", Sign(url, y.dat.BaseInfo.AppSecret, reqDataParam))
	var resData []byte
	err, resData = utils.PostForm(url, reqDataParam, nil)
	if err != nil {
		return
	}

	err = json.Unmarshal(resData, &modelData)
	if modelData.Succes != "true" {
		err = errors.New("请求错误")
		return
	}

	if len(modelData.CategoryInfo) == 0 {
		err = errors.New("未查到分类")
		return
	}

	//if modelData.CategoryInfo[0].Name == "" {
	//	err = errors.New("分类名为空")
	//	return
	//}

	if err != nil {
		return
	}

	return

}
func (y *AliJX) GetCategoryTotal(pid int, info request.GetCategoryChild) (err error, modelData model3.ALCategory) {

	url := "https://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.category.get/" + y.dat.BaseInfo.AppKey
	reqDataParam := url2.Values{}
	reqDataParam.Add("access_token", y.dat.BaseInfo.Token)
	categoryID := strconv.Itoa(pid)
	reqDataParam.Add("categoryID", categoryID)
	reqDataParam.Add("_aop_signature", Sign(url, y.dat.BaseInfo.AppSecret, reqDataParam))
	var resData []byte
	err, resData = utils.PostForm(url, reqDataParam, nil)
	if err != nil {
		return
	}

	err = json.Unmarshal(resData, &modelData)
	//if modelData.Succes != "true" {
	//	err = errors.New("请求错误")
	//	return
	//}

	//if len(modelData.CategoryInfo) == 0 {
	//	err = errors.New("未查到分类")
	//	return
	//}

	//if modelData.CategoryInfo[0].Name == "" {
	//	err = errors.New("分类名为空")
	//	return
	//}

	//if err != nil {
	//	return
	//}

	if categoryID != "0" {
		urlGoods := "https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao/alibaba.pifatuan.product.list/" + y.dat.BaseInfo.AppKey

		reqData := url2.Values{}
		reqData.Add("access_token", y.dat.BaseInfo.Token)
		reqData.Add("pageNo", "1")
		reqData.Add("categoryId", categoryID)
		reqData.Add("pageSize", pageSize)
		reqData.Add("_aop_signature", Sign(urlGoods, y.dat.BaseInfo.AppSecret, reqData))

		var resGoodsData []byte
		err, resGoodsData = utils.PostForm(urlGoods, reqData, nil)
		if err != nil {
			return
		}

		var goodsData model3.ALIJXGoodsList
		err = json.Unmarshal(resGoodsData, &goodsData)
		if err != nil {
			return
		}

		modelData.Total = goodsData.Result.PageInfo.TotalRecords
	}

	return

}

func (y *AliJX) GetCategoryChildSub(pid int, info request.GetCategoryChild) (err error, data interface{}) {

	return

}

func (y *AliJX) GetCategoryDetail(id uint) (err error, cate uint) {

	return

}

func (y *AliJX) FollowProduct(productID string) (err error, cate uint) {

	url := "http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.follow/" + y.dat.BaseInfo.AppKey

	reqData := url2.Values{}
	reqData.Add("access_token", y.dat.BaseInfo.Token)
	reqData.Add("productId", productID)
	reqData.Add("webSite", "1688")

	reqData.Add("_aop_signature", Sign(url, y.dat.BaseInfo.AppSecret, reqData))

	var resData []byte
	err, resData = utils.PostForm(url, reqData, nil)

	var ResOrderData request2.Follow

	json.Unmarshal(resData, &ResOrderData)
	log.Log().Info("info", zap.Any("阿里关注商品", string(resData)), zap.Any("商品id", productID))

	if ResOrderData.Message != "success" {
		log.Log().Error("err", zap.Any("阿里关注商品失败", string(resData)), zap.Any("商品id", productID))
	}

	return

}

type RelationData struct {
	Success bool   `json:"success"`
	Data    string `json:"data"`
}

func UndercarriageProduct(productID uint) (err error) {
	var productMessageType mq.ProductMessageType //队列消息类型
	productMessageType = mq.Undercarriage
	err = mq.PublishMessage(productID, productMessageType, 0)

	var colum = make(map[string]interface{})
	colum["is_display"] = 0

	colum["updated_at"] = time.Now().Format("2006-01-02 15:04:05")

	err = source.DB().Table("products").Where("id=? and source=?", productID, common.ALJX_SOURCE).UpdateColumns(&colum).Error

	return
}
func (y *AliJX) ManuallyProductUpdate(productID uint) (err error) {

	var Product []pservice.ProductForUpdate

	err = source.DB().Preload("Skus").Order("updated_at asc ").Where("id=?", productID).Find(&Product).Error
	if err != nil {
		return
	}

	for _, item := range Product {

		log.Log().Info("info", zap.Any("info", item.ID))

		var updateGoods pservice.ProductForUpdate
		updateGoods = item

		var detail model3.ALJXNewDetail
		//var ids []uint
		//ids = append(ids, item.SourceGoodsID)
		err, detail = y.GetProductInfo(item.SourceGoodsID)
		if err != nil {
			continue
		}
		if detail.Success != true {
			//log.Log().Info("detail.Result.Success != true", zap.Any("info", item.SourceGoodsID))

			UndercarriageProduct(item.ID)
			continue
		}

		if detail.ProductInfo.Status != "published" {
			log.Log().Info("detail.Result.Result[0].ProductInfo.Status", zap.Any("info", item.SourceGoodsID))

			UndercarriageProduct(item.ID)
			continue
		}

		if len(detail.ProductInfo.ProductExtendInfos) > 0 {
			var isOnePsale = false
			for _, jxitem := range detail.ProductInfo.ProductExtendInfos {
				//if jxitem.Key == "isPftOffer" && jxitem.Value == "true" {
				//	isjx = true
				//}
				if jxitem.Key == "isOnePsale" && jxitem.Value == "true" {
					isOnePsale = true
				}
			}

			if isOnePsale == false {
				UndercarriageProduct(item.ID)
				continue
			}
			//fmt.Println(isjx)
		}

		var alishop model3.AliShop
		alishop.Name = strings.TrimSpace(detail.ProductInfo.SupplierLoginId)
		alishop.IsOpen = "0"
		alishop.Strategy = "{\"origin\":\"100\",\"marketing\":\"100\",\"guide\":\"100\",\"price\":\"100\",\"cost\":\"100\"}"
		err = source.DB().Where("name=?", detail.ProductInfo.SupplierLoginId).FirstOrCreate(&alishop).Error
		//if err!=nil{
		//	continue
		//}

		aliSkus := detail.ProductInfo.ProductSkuInfos

		var sku = pservice.Sku{}
		var skuList []pservice.Sku
		var minProfitRate float64

		for sk, skuitem := range aliSkus {
			if skuitem.JxhyPrice == 0 && skuitem.ConsignPrice == 0 {
				continue
			}
			sku = pservice.Sku{}
			var options pmodel.Options
			var option pmodel.Option
			var skuTitle string
			for _, attribute := range skuitem.Attributes {
				option.SpecName = attribute.AttributeName
				option.SpecItemName = attribute.AttributeValue
				options = append(options, option)
				skuTitle += attribute.AttributeValue
			}

			for _, localskuitem := range item.Skus {
				if localskuitem.OriginalSkuID == int(skuitem.SkuId) {
					sku = localskuitem
					break
				}
			}

			//if len(goods.Skus) > 0 {
			//	sku.ID = goods.Skus[0].ID
			//}
			var costPrice, price, originPrice, activityPrice, guidePrice uint
			var skuPrice model.Goods

			skuPrice.CostPrice = uint(math.Floor(skuitem.ConsignPrice * 100))      //成本
			skuPrice.AgreementPrice = uint(math.Floor(skuitem.ConsignPrice * 100)) //成本
			skuPrice.ActivityPrice = uint(math.Floor(skuitem.ConsignPrice * 100))  //成本

			if skuitem.ConsignPrice < skuitem.JxhyPrice {
				skuPrice.CostPrice = uint(math.Floor(skuitem.JxhyPrice * 100))      //成本
				skuPrice.AgreementPrice = uint(math.Floor(skuitem.JxhyPrice * 100)) //成本
				skuPrice.ActivityPrice = uint(math.Floor(skuitem.JxhyPrice * 100))  //成本

			}

			skuPrice.Source = common.ALJX_SOURCE

			if alishop.IsOpen == "0" { // 独立设置
				err, costPrice, price, originPrice, activityPrice, guidePrice = GetShopPricingPrice(skuPrice, alishop)

			} else { //统一设置
				err, costPrice, price, originPrice, activityPrice, guidePrice = GetPricingPrice(skuPrice, "gatherSupply"+strconv.Itoa(int(y.SupplyID)))

			}

			if collection.Collect(y.dat.UpdateInfo.ManuallyUpdateSettings).Contains("currentPrice") {

				sku.Price = price
			}

			if collection.Collect(y.dat.UpdateInfo.ManuallyUpdateSettings).Contains("costPrice") {
				sku.CostPrice = costPrice
			}

			if collection.Collect(y.dat.UpdateInfo.ManuallyUpdateSettings).Contains("guide_price") {
				sku.GuidePrice = guidePrice
			}

			if collection.Collect(y.dat.UpdateInfo.ManuallyUpdateSettings).Contains("originalPrice") {
				sku.OriginPrice = originPrice
			}

			if collection.Collect(y.dat.UpdateInfo.ManuallyUpdateSettings).Contains("activity_price") {
				sku.ActivityPrice = activityPrice
			}

			sku.Title = skuTitle
			sku.Options = options
			sku.Stock = skuitem.AmountOnSale
			sku.IsDisplay = updateGoods.IsDisplay
			sku.OriginalSkuID = int(skuitem.SkuId)
			sku.SpecId = skuitem.SpecId

			if sku.GuidePrice < sku.Price {
				sku.GuidePrice = sku.Price
			}
			if sku.OriginPrice < sku.Price {
				sku.OriginPrice = sku.Price
			}
			if sku.ActivityPrice < sku.Price {
				sku.ActivityPrice = sku.Price
			}

			if collection.Collect(y.dat.UpdateInfo.ManuallyUpdateSettings).Contains("baseInfo") {
				if len(skuitem.Attributes) > 0 {
					if skuitem.Attributes[0].SkuImageUrl != "" {

						if strings.Contains(skuitem.Attributes[0].SkuImageUrl, "https://cbu01.alicdn.com") {
							sku.ImageUrl = skuitem.Attributes[0].SkuImageUrl
						} else {
							sku.ImageUrl = "https://cbu01.alicdn.com/" + skuitem.Attributes[0].SkuImageUrl
						}

					} else {
						sku.ImageUrl = ""
					}
				}
			}

			if sku.GuidePrice > 0 {
				sku.ProfitRate = utils.ExecProfitRate(sku.GuidePrice, sku.Price)
			} else {
				sku.ProfitRate = 0
			}
			if sk == 0 {
				minProfitRate = sku.ProfitRate
			}
			if sku.ProfitRate <= minProfitRate {
				minProfitRate = sku.ProfitRate
			}
			if strings.Contains(sku.Title, "客服") || strings.Contains(sku.Title, "代发") || strings.Contains(sku.Title, "批量") || strings.Contains(sku.Title, "厂家") || strings.Contains(sku.Title, "咨询") {
				sku.IsDisplay = 0

			} else {
				skuList = append(skuList, sku)

			}

		}

		if len(skuList) == 0 {
			for _, localskuitem := range item.Skus {
				if localskuitem.OriginalSkuID == int(detail.ProductInfo.ProductID) {
					sku = localskuitem
					break
				}
			}
			var options pmodel.Options
			var option pmodel.Option
			option.SpecName = "规格"
			option.SpecItemName = "默认"
			options = append(options, option)

			var costPrice, price, originPrice, activityPrice, guidePrice uint

			var skuPrice model.Goods

			skuPrice.CostPrice = uint(utils.Yuan2Fen(detail.ProductInfo.ProductSaleInfo.ConsignPrice))

			if skuPrice.CostPrice == 0 || detail.ProductInfo.ProductSaleInfo.JxhyPrice > detail.ProductInfo.ProductSaleInfo.ConsignPrice {
				skuPrice.CostPrice = uint(utils.Yuan2Fen(detail.ProductInfo.ProductSaleInfo.JxhyPrice))

			}

			skuPrice.Source = common.ALJX_SOURCE
			if alishop.IsOpen == "0" { // 独立设置
				err, costPrice, price, originPrice, activityPrice, guidePrice = GetShopPricingPrice(skuPrice, alishop)

			} else { //统一设置
				err, costPrice, price, originPrice, activityPrice, guidePrice = GetPricingPrice(skuPrice, "gatherSupply"+strconv.Itoa(int(y.SupplyID)))

			}

			if costPrice == 0 || price == 0 {
				sku.IsDisplay = 0

			}
			sku.Title = "默认"
			sku.Options = options

			if collection.Collect(y.dat.UpdateInfo.ManuallyUpdateSettings).Contains("currentPrice") {

				sku.Price = price
			}

			if collection.Collect(y.dat.UpdateInfo.ManuallyUpdateSettings).Contains("costPrice") {
				sku.CostPrice = costPrice
			}

			if collection.Collect(y.dat.UpdateInfo.ManuallyUpdateSettings).Contains("guide_price") {
				sku.GuidePrice = guidePrice
			}

			if collection.Collect(y.dat.UpdateInfo.ManuallyUpdateSettings).Contains("originalPrice") {
				sku.OriginPrice = originPrice
			}

			if collection.Collect(y.dat.UpdateInfo.ManuallyUpdateSettings).Contains("activity_price") {
				sku.ActivityPrice = activityPrice
			}

			sku.Stock = int(detail.ProductInfo.ProductSaleInfo.AmountOnSale)
			sku.IsDisplay = updateGoods.IsDisplay
			sku.OriginalSkuID = int(detail.ProductInfo.ProductID)
			if sku.GuidePrice < sku.Price {
				sku.GuidePrice = sku.Price
			}
			if sku.OriginPrice < sku.Price {
				sku.OriginPrice = sku.Price
			}
			if sku.ActivityPrice < sku.Price {
				sku.ActivityPrice = sku.Price
			}

			productID := strconv.Itoa(int(detail.ProductInfo.ProductID))
			sku.SpecId = productID
			if strings.Contains(sku.Title, "客服") || strings.Contains(sku.Title, "代发") || strings.Contains(sku.Title, "批量") || strings.Contains(sku.Title, "厂家") || strings.Contains(sku.Title, "咨询") {
				sku.IsDisplay = 0

			}
			if sku.GuidePrice > 0 {
				sku.ProfitRate = utils.ExecProfitRate(sku.GuidePrice, sku.Price)
			} else {
				sku.ProfitRate = 0
			}
			minProfitRate = sku.ProfitRate

			skuList = append(skuList, sku)
			updateGoods.SingleOption = 1
		}

		updateGoods.MinBuyQty = 1

		updateGoods.Skus = skuList
		if detail.ProductInfo.Status == "published" {
			updateGoods.IsDisplay = 1
		}

		itemSku := GetMapA(updateGoods.Skus)

		updateGoods.MaxPrice, updateGoods.MinPrice = GetPriceA(updateGoods.Skus)
		updateGoods.CostPrice = itemSku.CostPrice

		updateGoods.Price = itemSku.Price
		if updateGoods.Price == 0 {
			log.Log().Info("updateGoods.Price", zap.Any("info", itemSku))
			UndercarriageProduct(item.ID)
			continue
		}
		if len(updateGoods.Skus) > 0 {
			updateGoods.ProfitRate = minProfitRate
		}
		updateGoods.Stock = uint(itemSku.Stock)
		if updateGoods.Stock == 0 {
			updateGoods.IsDisplay = 0
		}

		if collection.Collect(y.dat.UpdateInfo.ManuallyUpdateSettings).Contains("baseInfo") {
			if len(detail.ProductInfo.ProductImage.Images) > 0 {
				updateGoods.ImageUrl = detail.ProductInfo.ProductImage.Images[0]

			}

			updateGoods.DetailImages = detail.ProductInfo.Description
			var gallery pmodel.Gallery

			updateGoods.Gallery = gallery

			jsonImage := detail.ProductInfo.ProductImage.Images

			for _, itemUrl := range jsonImage {
				var galleryItem pmodel.GalleryItem
				if strings.Contains(itemUrl, "https://cbu01.alicdn.com") {
					galleryItem.Src = itemUrl
				} else {
					galleryItem.Src = "https://cbu01.alicdn.com/" + itemUrl
				}
				galleryItem.Type = 1
				gallery = append(gallery, galleryItem)
			}
			updateGoods.Gallery = gallery

			if updateGoods.ImageUrl == "" {
				if len(updateGoods.Gallery) > 0 {
					updateGoods.ImageUrl = updateGoods.Gallery[0].Src
				}

			}

		}

		updateGoods.ShopName = detail.ProductInfo.SupplierLoginId
		err = pservice.UpdateProduct(updateGoods)
		if err != nil {
			log.Log().Error("更新商品错误 update", zap.Any("err", err))
			return err
		}
	}

	return

}

func (y *AliJX) AddRelation(localProductID, offerId string) (err error) {
	url := "https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao/alibaba.fenxiao.buyer.outproduct.relation.add/" + y.dat.BaseInfo.AppKey

	reqData := url2.Values{}
	reqData.Add("access_token", y.dat.BaseInfo.Token)
	reqData.Add("outItemCode", localProductID)
	reqData.Add("outShopCode", y.dat.BaseInfo.StoreName)
	reqData.Add("offerId", offerId)
	reqData.Add("channel", "other")
	reqData.Add("_aop_signature", Sign(url, y.dat.BaseInfo.AppSecret, reqData))
	var resData []byte
	log.Log().Info("aljx AddRelation request", zap.Any("info", reqData))
	err, resData = utils.PostForm(url, reqData, nil)
	log.Log().Info("aljx AddRelation response", zap.Any("info", string(resData)))
	var relationData RelationData
	err = json.Unmarshal(resData, &relationData)
	if err != nil {
		return
	}
	if relationData.Success != true || relationData.Data != "true" {

		err = errors.New("关联商品错误:" + localProductID)
	}
	return

}

// 商品组装
func (y *AliJX) CommodityAssemblyLocal(detail model3.AliGoods, cateId1, cateId2, cateId3 int) (err error, listGoods []*pmodel.Product) {
	sourceCode := common2.ALJX_SOURCE
	goods := new(pmodel.Product)
	product := pmodel.Product{}
	err = source.DB().Where("source_goods_id =  ? and source=?", detail.ProductID, sourceCode).Preload("Skus").First(&product).Error
	if product.ID > 0 {
		//y.CommodityAssemblyLocalUpdate(detail, cateId1, cateId2, cateId3)
		return
	}

	err, detailInfo := y.GetProductInfo(uint(detail.ProductID))

	goods.SourceGoodsID = uint(detailInfo.ProductInfo.ProductID)
	goods.ShopName = detailInfo.ProductInfo.SupplierLoginId
	//if detail.BrandName != "" {
	//	brand.Name = detail.BrandName
	//	brand.Source = sourceCode
	//	err = source.DB().Where(brand).FirstOrCreate(&brand).Error
	//	goods.BrandID = brand.ID
	//}

	goods.Title = detailInfo.ProductInfo.Subject

	goods.IsDisplay = 0
	//if detail.Status == "published" {
	//	goods.IsDisplay = 1
	//}
	//goods.TaxRate = int(detail.RESULTDATA.PRODUCTDATA.Tax)
	goods.ImageUrl = detailInfo.ProductInfo.ProductImage.Images[0]

	goods.Source = sourceCode
	goods.DetailImages = detailInfo.ProductInfo.Description

	//cateList := strings.Split(detail.CategoryNames, ",")
	var cate1, cate2, cate3 catemodel.Category
	display := 1

	var dat model.SupplySetting
	err, setting := gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(y.SupplyID)))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}

	err = json.Unmarshal([]byte(setting.Value), &dat)
	if err != nil {
		return
	}

	var alishop model3.AliShop
	alishop.Name = strings.TrimSpace(detailInfo.ProductInfo.SupplierLoginId)
	alishop.IsOpen = "0"
	alishop.Strategy = "{\"origin\":\"100\",\"marketing\":\"100\",\"guide\":\"100\",\"price\":\"100\",\"cost\":\"100\"}"
	err = source.DB().Where("name=?", detailInfo.ProductInfo.SupplierLoginId).FirstOrCreate(&alishop).Error
	if err != nil {
		log.Log().Error("阿里cron 更新商品 WangwangAccount err", zap.Any("err", err))
		//continue
	}

	//if alishop.ID == 0 {
	//	var shop model3.AliShop
	//	shop.Name = detail.WangwangAccount
	//	shop.IsOpen = "0"
	//	shop.Strategy = "{\"origin\":\"100\",\"marketing\":\"100\",\"guide\":\"100\",\"price\":\"100\",\"cost\":\"100\"}"
	//	err = CreateAliShop(shop)
	//	if err != nil {
	//		log.Log().Error("err", zap.Any("import CreateAliShop err", err))
	//	}
	//}

	//if alishop.IsOpen == "1" {
	//	continue
	//}

	if dat.UpdateInfo.CateGory == 1 {

		//if len(cateList) > 0 && cateList[0] != "" {

		cate1.IsDisplay = &display
		cate1.ParentID = 0
		cate1.Level = 1
		cate1.Name = detail.CateName1
		source.DB().Where("name=? and level=? and parent_id=?", detail.CateName1, 1, 0).FirstOrCreate(&cate1)
		//}

		//if len(cateList) > 1 && cateList[1] != "" {
		cate2.IsDisplay = &display
		cate2.ParentID = cate1.ID
		cate2.Level = 2
		cate2.Name = detail.CateName2
		source.DB().Where("name=? and level=? and parent_id=?", detail.CateName2, 2, cate1.ID).FirstOrCreate(&cate2)
		//}

		//if len(cateList) > 2 && cateList[2] != "" {
		cate3.IsDisplay = &display
		cate3.ParentID = cate2.ID
		cate3.Level = 3
		cate3.Name = detail.CategoryName
		source.DB().Where("name=? and level=? and parent_id=?", detail.CategoryName, 3, cate2.ID).FirstOrCreate(&cate3)
	}
	//

	goods.Category1ID = cate1.ID
	goods.Category2ID = cate2.ID
	goods.Category3ID = cate3.ID

	if cateId1 > 0 {
		goods.Category1ID = uint(cateId1)
	}
	if cateId2 > 0 {
		goods.Category2ID = uint(cateId2)
	}
	if cateId3 > 0 {
		goods.Category3ID = uint(cateId3)
	}

	//}

	goods.FreightType = 2
	goods.GatherSupplyID = y.SupplyID

	var sku = pmodel.Sku{}
	var skuList []pmodel.Sku
	var gallery pmodel.Gallery

	jsonImage := detailInfo.ProductInfo.ProductImage.Images

	for _, item := range jsonImage {
		var galleryItem pmodel.GalleryItem
		galleryItem.Src = item
		galleryItem.Type = 1
		gallery = append(gallery, galleryItem)
	}

	goods.Gallery = gallery

	if len(goods.Gallery) <= 0 {
		var Gallery pmodel.Gallery
		Gallery = append(Gallery, pmodel.GalleryItem{Type: 1, Src: goods.ImageUrl})
		goods.Gallery = Gallery
	}

	//var aliSkus model3.SkuInfos
	//err = json.Unmarshal([]byte(detailInfo.ProductInfo.ProductSkuInfos), &aliSkus)

	var GoodsStock int
	var minProfitRate float64
	for sk, item := range detailInfo.ProductInfo.ProductSkuInfos {
		var options pmodel.Options
		var option pmodel.Option
		var skuTitle string
		for _, attribute := range item.Attributes {
			option.SpecName = attribute.AttributeName
			option.SpecItemName = attribute.AttributeValue
			options = append(options, option)
			skuTitle += attribute.AttributeValue
		}

		var costPrice, salePrice, originPrice, activityPrice, guidePrice uint
		var skuPrice model.Goods

		//skuPrice.AgreementPrice = uint(math.Floor(item.ConsignPrice * 100))
		//skuPrice.GuidePrice = uint(math.Floor(item.RetailPrice * 100))

		if item.JxhyPrice > item.ConsignPrice {
			item.ConsignPrice = item.JxhyPrice
		}
		skuPrice.CostPrice = uint(math.Floor(item.ConsignPrice * 100))
		skuPrice.Source = sourceCode

		err, costPrice, salePrice, originPrice, activityPrice, guidePrice = GetShopPricingPrice(skuPrice, alishop)

		GoodsStock = GoodsStock + item.AmountOnSale
		sku.Title = skuTitle
		sku.Options = options
		sku.CostPrice = costPrice
		sku.Stock = item.AmountOnSale
		sku.IsDisplay = goods.IsDisplay
		if len(item.Attributes) > 0 {
			if item.Attributes[0].SkuImageUrl != "" {
				sku.ImageUrl = "https://cbu01.alicdn.com/" + item.Attributes[0].SkuImageUrl

				if strings.Contains(item.Attributes[0].SkuImageUrl, "https://cbu01.alicdn.com") {
					sku.ImageUrl = item.Attributes[0].SkuImageUrl
				} else {
					sku.ImageUrl = "https://cbu01.alicdn.com/" + item.Attributes[0].SkuImageUrl
				}

			}
		}
		sku.Price = salePrice
		sku.OriginPrice = originPrice
		sku.GuidePrice = guidePrice
		sku.ActivityPrice = activityPrice
		sku.OriginalSkuID = item.SkuId
		sku.SpecId = item.SpecId
		if sku.GuidePrice > 0 {
			sku.ProfitRate = utils.ExecProfitRate(sku.GuidePrice, sku.Price)
		} else {
			sku.ProfitRate = 0
		}
		if sk == 0 {
			minProfitRate = sku.ProfitRate
		}
		if sku.ProfitRate <= minProfitRate {
			minProfitRate = sku.ProfitRate
		}
		skuList = append(skuList, sku)

	}

	//var saleInfo model3.SaleInfos
	//err = json.Unmarshal([]byte(detail.JsonSaleInfo), &saleInfo)
	//if err != nil {
	//	log.Log().Error("err", zap.Any("解释阿里jx saleInfo错误", err))
	//}
	goods.Unit = "默认"

	if goods.Unit == "" {
		goods.Unit = "单位"
	}

	if len(skuList) == 0 {
		var options pmodel.Options
		var option pmodel.Option
		option.SpecName = "规格"
		option.SpecItemName = "默认"
		options = append(options, option)

		var costPrice, salePrice, originPrice, activityPrice, guidePrice uint
		var skuPrice model.Goods

		if detailInfo.ProductInfo.ProductSaleInfo.ConsignPrice < detailInfo.ProductInfo.ProductSaleInfo.JxhyPrice {
			detailInfo.ProductInfo.ProductSaleInfo.ConsignPrice = detailInfo.ProductInfo.ProductSaleInfo.JxhyPrice
		}
		skuPrice.CostPrice = uint(math.Floor(detailInfo.ProductInfo.ProductSaleInfo.ConsignPrice * 100))
		skuPrice.Source = sourceCode

		err, costPrice, salePrice, originPrice, activityPrice, guidePrice = GetShopPricingPrice(skuPrice, alishop)

		if GoodsStock == 0 {
			GoodsStock = int(detailInfo.ProductInfo.ProductSaleInfo.AmountOnSale)
		}
		sku.Title = "默认"
		sku.Options = options
		sku.CostPrice = costPrice
		sku.Stock = int(detailInfo.ProductInfo.ProductSaleInfo.AmountOnSale)
		sku.IsDisplay = goods.IsDisplay
		sku.Price = salePrice
		sku.OriginPrice = originPrice
		sku.GuidePrice = guidePrice
		sku.ActivityPrice = activityPrice
		sku.OriginalSkuID = detail.ProductID
		productID := strconv.Itoa(int(detail.ProductID))
		sku.SpecId = productID
		if sku.GuidePrice > 0 {
			sku.ProfitRate = utils.ExecProfitRate(sku.GuidePrice, sku.Price)
		} else {
			sku.ProfitRate = 0
		}
		minProfitRate = sku.ProfitRate

		skuList = append(skuList, sku)

		goods.SingleOption = 1

	}

	goods.MinBuyQty = 1

	goods.Skus = skuList
	if len(goods.Skus) > 0 {
		goods.ProfitRate = minProfitRate
	}
	goods.Stock = uint(GoodsStock)
	itemSku := GetMap(goods.Skus)
	goods.MaxPrice, goods.MinPrice = GetPrice(goods.Skus)
	goods.CostPrice = itemSku.CostPrice
	goods.GuidePrice = itemSku.GuidePrice
	goods.OriginPrice = itemSku.OriginPrice
	goods.ActivityPrice = itemSku.ActivityPrice
	goods.Price = itemSku.Price
	if len(goods.Skus) > 0 {
		productID := strconv.Itoa(int(goods.SourceGoodsID))
		y.FollowProduct(productID)

		listGoods = append(listGoods, goods)
	}

	return
}
func CreateAliShop(shop model3.AliShop) (err error) {

	var selectShop model3.AliShop
	source.DB().First(&selectShop)
	if shop.ID == 0 && selectShop.Name == shop.Name {
		return errors.New("店铺已存在,请勿重复添加")

	}

	return source.DB().Save(&shop).Error
}

func GetPrice(skuList []pmodel.Sku) (maxPrice, minPrice uint) {
	var priceList []int
	for _, item := range skuList {
		priceList = append(priceList, int(item.Price))
	}
	if len(priceList) <= 0 {
		maxPrice = 0
		minPrice = 0
		return
	}
	sort.Ints(priceList)
	minPrice = uint(priceList[0])
	//sort.Sort(sort.Reverse(sort.IntSlice(priceList)))
	maxPrice = uint(priceList[len(priceList)-1])

	return
}
func GetPriceA(skuList []pservice.Sku) (maxPrice, minPrice uint) {
	var priceList []int
	for _, item := range skuList {
		priceList = append(priceList, int(item.Price))
	}
	if len(priceList) <= 0 {
		maxPrice = 0
		minPrice = 0
		return
	}
	sort.Ints(priceList)
	minPrice = uint(priceList[0])
	//sort.Sort(sort.Reverse(sort.IntSlice(priceList)))
	maxPrice = uint(priceList[len(priceList)-1])

	return
}

func Decimal(value float64) float64 {
	var err error
	value, err = strconv.ParseFloat(fmt.Sprintf("%0.2f", value), 64)
	if err != nil {
		return 0
	}
	return value
}
func GetMap(skuList []pmodel.Sku) (item pmodel.Sku) {

	var maxPrice float64
	for _, sku := range skuList {
		item = sku
		ProfitRate := Decimal((float64(sku.GuidePrice) - float64(sku.Price)) / float64(sku.GuidePrice) * 100)
		if ProfitRate > maxPrice {
			maxPrice = ProfitRate
			item = sku
		}
	}
	if len(skuList) > 0 && maxPrice == 0 {
		item = skuList[0]
	}
	return

}
func GetMapA(skuList []pservice.Sku) (item pservice.Sku) {

	var maxPrice float64
	for _, sku := range skuList {
		if sku.Desc == "填充规格" {
			continue
		}
		item = sku
		ProfitRate := Decimal((float64(sku.GuidePrice) - float64(sku.Price)) / float64(sku.GuidePrice) * 100)
		if ProfitRate > maxPrice {
			maxPrice = ProfitRate
			item = sku
		}
	}
	if len(skuList) > 0 && maxPrice == 0 {
		item = skuList[0]
	}
	return

}

// 商品组装
func (y *AliJX) CommodityAssemblyLocalUpdate(detail model.YzhProductTemp, goods pservice.ProductForUpdate, cateId1, cateId2, cateId3 int) (err error, listGoods []*pmodel.Product) {
	var skuIds []int64
	skuCode, _ := strconv.Atoi(detail.GoodsSkuCode)
	skuIds = append(skuIds, int64(skuCode))
	//imgList := y.QueryGoodsImageList(skuIds)

	sourceCode := int(common2.YZH_NEW)
	//var goods pservice.ProductForUpdate
	//err = source.DB().Where("source_goods_id =  ? and source=?", detail.GoodsSkuCode, sourceCode).Preload("Skus").First(&goods).Error

	var strSkuCode []string
	strSkuCode = append(strSkuCode, detail.GoodsSkuCode)
	GoodsSkuCode, _ := strconv.Atoi(detail.GoodsSkuCode)
	goods.SourceGoodsID = uint(GoodsSkuCode)
	//var brand = new(catemodel.Brand)
	//if detail.BrandName != "" {
	//	brand.Name = detail.BrandName
	//	brand.Source = sourceCode
	//	err = source.DB().Where(brand).FirstOrCreate(&brand).Error
	//	goods.BrandID = brand.ID
	//}
	var costPrice, salePrice, originPrice, activityPrice, guidePrice uint
	var elem model.Goods

	SellPrice, spriceErr := strconv.ParseFloat(detail.SellPrice, 64)
	if spriceErr != nil {
		fmt.Println("价格错误", spriceErr)
	}
	MarketPrice, mpriceErr := strconv.ParseFloat(detail.MarketPrice, 64)

	MarketPriceUint := uint(MarketPrice * 100)
	SellPriceUint := uint(SellPrice * 100)

	if MarketPriceUint <= 0 || SellPriceUint <= 0 {
		return
	}

	if mpriceErr != nil {
		fmt.Println("价格错误", mpriceErr)
	}
	elem.AgreementPrice = uint(SellPriceUint)
	elem.GuidePrice = uint(MarketPriceUint)
	elem.ActivityPrice = uint(MarketPriceUint)
	elem.Source = sourceCode

	err, costPrice, salePrice, originPrice, activityPrice, guidePrice = GetPricingPrice(elem, "gatherSupply"+strconv.Itoa(int(y.SupplyID)))
	//var dat model.SupplySetting
	//err, setting := gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(y.SupplyID)))
	//if err != nil {
	//	fmt.Println("获取供应链key设置失败")
	//	return
	//}
	//err = json.Unmarshal([]byte(setting.Value), &dat)
	//if err != nil {
	//	return
	//}
	//stock, _ := y.GetStock(strSkuCode)

	//if stock.RESPONSESTATUS != "true" || stock.RESULTDATA.StockStatus != true {
	//	err = errors.New("库存无效")
	//	return
	//}

	goods.Title = detail.GoodsSkuName
	goods.OriginPrice = originPrice
	//if dat.UpdateInfo.CurrentPrice == 1 {
	goods.Price = salePrice
	//}

	//if dat.UpdateInfo.CostPrice == 1 {
	goods.CostPrice = costPrice
	//}

	goods.ActivityPrice = activityPrice
	goods.GuidePrice = guidePrice
	//if len(stock.Result) > 0 {
	//	if stock.Result[0].StockNum > 0 {
	//		goods.Stock = uint(stock.Result[0].StockNum)
	//	}
	//}

	goods.SingleOption = 1
	//goods.Sales = elem.Sale
	//status, _ := strconv.Atoi(detail.RESULTDATA.PRODUCTDATA.Status)
	if detail.ShelvesStatus == 1001 {
		goods.IsDisplay = 1
	} else {
		goods.IsDisplay = 0
	}

	goods.Unit = detail.GoodsUnit
	//goods.SourceGoodsID = uint(detail.RESULTDATA.PRODUCTDATA.ProductId)
	goods.Source = sourceCode
	//goods.DetailImages = detail.Detail

	//cateList := strings.Split(detail.CategoryNames, ",")
	//var cate1, cate2, cate3 catemodel.Category
	//display := 1
	//
	//err = json.Unmarshal([]byte(setting.Value), &dat)
	//if err != nil {
	//	return
	//}
	//
	//if dat.UpdateInfo.CateGory == 1 {
	//
	//	//if len(cateList) > 0 && cateList[0] != "" {
	//
	//	cate1.IsDisplay = &display
	//	cate1.ParentID = 0
	//	cate1.Level = 1
	//	cate1.Name = detail.FirstCategoryName
	//	source.DB().Where("name=? and level=? and parent_id=?", detail.FirstCategoryName, 1, 0).FirstOrCreate(&cate1)
	//	//	}
	//
	//	//if len(cateList) > 1 && cateList[1] != "" {
	//	cate2.IsDisplay = &display
	//	cate2.ParentID = cate1.ID
	//	cate2.Level = 2
	//	cate2.Name = detail.SecondCategoryName
	//	source.DB().Where("name=? and level=? and parent_id=?", detail.SecondCategoryName, 2, cate1.ID).FirstOrCreate(&cate2)
	//	//}
	//
	//	//if len(cateList) > 2 && cateList[2] != "" {
	//	cate3.IsDisplay = &display
	//	cate3.ParentID = cate2.ID
	//	cate3.Level = 3
	//	cate3.Name = detail.LastCategoryName
	//	source.DB().Where("name=? and level=? and parent_id=?", detail.LastCategoryName, 3, cate2.ID).FirstOrCreate(&cate3)
	//	//}
	//	//
	//
	//	goods.Category1ID = cate1.ID
	//	goods.Category2ID = cate2.ID
	//	goods.Category3ID = cate3.ID
	//}
	//
	//if cateId1 > 0 {
	//	goods.Category1ID = uint(cateId1)
	//}
	//if cateId2 > 0 {
	//	goods.Category2ID = uint(cateId2)
	//}
	//if cateId3 > 0 {
	//	goods.Category3ID = uint(cateId3)
	//}

	//}
	goods.Gallery = nil
	goods.FreightType = 2
	goods.GatherSupplyID = y.SupplyID

	var sku = pservice.Sku{}
	var skuList []pservice.Sku

	var galleryList pmodel.Gallery
	//var galleryItem pmodel.GalleryItem
	//if goodsItem.ShelvesStatus != 1001 {
	//	continue
	//}
	//for _, imgItem := range imgList[detail.GoodsSkuCode] {
	//	if imgItem.ImgMain == 1 {
	//		detail.ImgMain = imgItem.ImgUrl
	//	} else {
	//		galleryItem.Type = 1
	//		galleryItem.Src = imgItem.ImgUrl
	//		galleryList = append(galleryList, galleryItem)
	//	}
	//
	//}

	if len(galleryList) > 0 {
		goods.Gallery = galleryList
	}

	//if len(detail.Gallery) > 0 {
	//	goods.Gallery = detail.Gallery
	//}
	//

	if detail.ImgMain != "" {
		goods.ImageUrl = detail.ImgMain
	}
	if len(goods.Gallery) == 0 && detail.ImgMain != "" {
		var item pmodel.GalleryItem
		var gallery pmodel.Gallery
		item.Src = detail.ImgMain
		item.Type = 1
		gallery = append(gallery, item)
		goods.Gallery = gallery
	}

	/**
	处理轮播图结束
	*/

	goods.MinPrice = goods.Price
	goods.MaxPrice = goods.Price

	//if len(goods.Skus) == 0 {
	var options pmodel.Options
	var option pmodel.Option
	option.SpecName = "规格"
	option.SpecItemName = "默认"
	options = append(options, option)
	if len(goods.Skus) > 0 {
		sku.ID = goods.Skus[0].ID
	}

	if len(goods.Skus) > 0 {

		sku.ID = goods.Skus[0].ID
	}

	sku.Title = "默认"
	sku.Options = options
	sku.Weight = 0
	sku.CostPrice = goods.CostPrice
	sku.Stock = int(goods.Stock)
	//sku.IsDisplay = goods.IsDisplay
	sku.Price = goods.Price
	sku.OriginPrice = goods.OriginPrice
	sku.GuidePrice = goods.GuidePrice
	sku.ActivityPrice = goods.ActivityPrice
	sku.OriginalSkuID = int(goods.SourceGoodsID)
	skuList = append(skuList, sku)

	//}

	goods.Skus = skuList

	//---------处理属性json数组结束
	//goods.Desc=detail.Description
	if goods.ID > 0 {
		log.Log().Debug("yunzhonghe修改商品", zap.Any("id", goods.ID))

		err = pservice.UpdateProduct(goods)
		if err != nil {
			log.Log().Error("更新商品错误 update", zap.Any("err", err))
			return
		}
		//source.DB().Model(model.SupplyGoodsImportRecord{}).Where("batch=?", GlobalOrderSN).Updates(map[string]interface{}{
		//	"repeat_quantity": gorm.Expr("repeat_quantity + ?", 1),
		//})
	}

	return
}

var GlobalOrderSN string

// 批量获取商品详情
func (*AliJX) BatchGetGoodsDetails(ids string) (err error, data map[int]model.GoodsDetail) {

	return
}

func (*AliJX) RunConcurrent(wg *sync.WaitGroup, info request.GetCategorySearch, i int) (err error) {

	defer wg.Done()
	var result *stbz.APIResult
	result, err = stbz.API(
		1,
		"/v2/Category/Lists",
		map[string]string{},
		g.Map{"page": i, "limit": info.Limit, "source": info.Source},
	)
	fmt.Println("循环：", i, info.Limit, info.Source)
	if result.Data != nil {
		datas := result.Data.(map[string]interface{})
		var cateItem []model.Category
		cateJson := datas["data"]
		mJson, _ := json.Marshal(cateJson)
		stringJson := string(mJson)
		err = json.Unmarshal([]byte(stringJson), &cateItem)
		mutex.Lock()
		if len(cateItem) > 0 {
			category = append(category, cateItem...)
		}
		mutex.Unlock()

	} else {
		fmt.Println("获取失败的：", i, info.Limit, info.Source)
	}

	return
}
