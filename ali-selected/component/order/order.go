package order

import (
	afterSalesModel "after-sales/model"
	"ali-selected/component/goods"
	model5 "ali-selected/model"
	"encoding/json"
	"errors"
	"fmt"
	url2 "net/url"
	model3 "order/model"
	model4 "product/model"
	callback2 "public-supply/callback"
	common2 "public-supply/common"
	"public-supply/model"
	"public-supply/request"
	"public-supply/response"
	gsetting "public-supply/setting"
	"strconv"
	"strings"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"

	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
)

type AliJX struct {
	dat      model.SupplySetting
	Setting  model.SupplySetting
	SupplyID uint
	Http     string
}

func (y *AliJX) UploadGatherSupplySN(request request.UpdateData) (err error, data interface{}) {
	//TODO implement me
	panic("implement me")
}

func (y *AliJX) GetRefundTypes(orderId uint, orderItemID uint) (err error, data []afterSalesModel.AfterSalesType) {
	return
}

var TokenKey = "ALI_SELECTED_TOKEN"

func (y *AliJX) GetToken() (err error) {

	return
}

func (y *AliJX) AfterSalesBeforeCheck(request request.RequestAfterSale) (err error, info interface{}) {
	return
}

func (y *AliJX) AfterSalesPicture(request request.RequestAfterSalePicture) (err error, info interface{}) {
	return
}

func (y *AliJX) AfterSale(request request.AfterSale) (err error, info interface{}) {
	return
}

func (y *AliJX) OrderDelivery(OrderData callback2.OrderCallBack) (err error) {
	return
}

func (y *AliJX) SyncOrderExpNo(unionIdList []string) (err error, data []response.SyncOrderExpNoResponse) {
	return
}

func (y *AliJX) InitSetting(gatherSupplyID uint) (err error) {
	y.SupplyID = gatherSupplyID

	var setting model2.SysSetting
	err, setting = gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}
	err = json.Unmarshal([]byte(setting.Value), &y.dat)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}

	y.Setting = y.dat
	if y.dat.BaseInfo.AppKey == "" && y.dat.BaseInfo.AppSecret == "" {
		err = errors.New("请先配置供应链key")
		return
	}

	//y.Http = y.dat.BaseInfo.ApiUrl
	//y.Http = y.dat.BaseInfo.ApiUrl

	y.dat.BaseInfo.AppKey = y.dat.BaseInfo.AppKey
	y.dat.BaseInfo.AppSecret = y.dat.BaseInfo.AppSecret
	y.dat.BaseInfo.Token = y.dat.BaseInfo.Token
	return
}

func (y *AliJX) CancelOrder(orderID uint) {

}

func (y *AliJX) QueryGoodsSaleLimitList(request request.RequestSaleBeforeCheck) (err error) { //查询商品的限制销售地区

	return
}

// 订单前置校验  返回运费   //立即购买校验
func (y *AliJX) OrderBeforeCheck(request request.RequestSaleBeforeCheck) (err error, res response.BeforeCheck) {
	res.Code = 1
	log.Log().Info("alijx预览供应链商品准备下单", zap.Any("info", request))
	url := "https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.createOrder.preview/" + y.dat.BaseInfo.AppKey
	var skuMap model5.SkuMap
	var errTitle string
	var errMapList []map[string]string

	for _, skuItem := range request.LocalSkus {

		var sku model4.Sku
		var aliSku model5.Sku
		aliSku.Quantity = int64(skuItem.Number)

		err = source.DB().Where("id=?", skuItem.Sku.Sku).Preload("Product").First(&sku).Error
		if err != nil {
			log.Log().Error("alijx预览供应链商品查询错误", zap.Any("info", err))
			errTitle = errTitle + sku.Title
			continue
		}
		var errMap = make(map[string]string)
		errMap[sku.SpecId] = sku.Title
		errMapList = append(errMapList, errMap)
		aliSku.OfferId = int64(sku.Product.SourceGoodsID)
		specLen := len(sku.SpecId)
		if specLen > 13 {
			aliSku.SpecId = sku.SpecId

		}
		skuMap = append(skuMap, aliSku)
		res.Skus = append(res.Skus, uint(skuItem.Sku.Sku))
	}
	var Consignee string
	if len(request.Address.Consignee) == 3 {
		Consignee = request.Address.Consignee + "*"
	} else {
		Consignee = request.Address.Consignee
	}

	reqData := url2.Values{}
	reqData.Add("access_token", y.dat.BaseInfo.Token)
	reqData.Add("flow", "")
	AddressOrder := make(map[string]string)
	AddressOrder["fullName"] = Consignee
	AddressOrder["mobile"] = request.Address.Phone
	AddressOrder["phone"] = request.Address.Phone
	AddressOrder["postCode"] = "000000"
	AddressOrder["cityText"] = request.Address.City
	AddressOrder["provinceText"] = request.Address.Province
	AddressOrder["areaText"] = request.Address.Area
	AddressOrder["townText"] = request.Address.Street
	AddressOrder["address"] = request.Address.Description
	AddressData, _ := json.Marshal(AddressOrder)

	SkuData, _ := json.Marshal(skuMap)

	reqData.Add("addressParam", string(AddressData))
	reqData.Add("cargoParamList", string(SkuData))
	reqData.Add("_aop_signature", goods.Sign(url, y.dat.BaseInfo.AppSecret, reqData))
	log.Log().Info("aljx订单预览请求数据", zap.Any("info", reqData))

	var resData []byte
	err, resData = utils.PostForm(url, reqData, nil)
	log.Log().Info("aljx下单预览返回数据：", zap.Any("info", string(resData)))

	var preview Preview
	err = json.Unmarshal(resData, &preview)
	if err != nil {
		res.Code = 0
		res.Msg = "阿里订单预览解析失败" + err.Error()
		return
	}

	if len(preview.OrderPreviewResuslt) == 0 {

		res.Code = 0
		res.Msg = "校验商品失败:"

		// 检查错误信息是否包含在规格中
		if preview.ErrorMsg != "" {
			for _, errMap := range errMapList {
				for specId, title := range errMap {
					if strings.Contains(preview.ErrorMsg, specId) {
						res.Msg += title + "-" + preview.ErrorMsg + ","
					}
				}
			}
			// 去除最后一个逗号
			if strings.HasSuffix(res.Msg, ",") {
				res.Msg = res.Msg[:len(res.Msg)-1]
			}
		}

		return
	}
	if preview.OrderPreviewResuslt[0].Status == false && preview.OrderPreviewResuslt[0].ResultCode == "AREA_LIMIT" {
		res.Code = 0
		res.Msg = "该地址限购:"

		return
	}

	if len(preview.OrderPreviewResuslt) > 0 {
		res.Freight = uint(preview.OrderPreviewResuslt[0].SumCarriage)
	}

	return
}

type Preview struct {
	OrderPreviewResuslt []struct {
		TradeModeNameList        []string `json:"tradeModeNameList"`
		Status                   bool     `json:"status"`
		TaoSampleSinglePromotion bool     `json:"taoSampleSinglePromotion"`
		SumPayment               int      `json:"sumPayment"`
		SumCarriage              int      `json:"sumCarriage"`
		ResultCode               string   `json:"resultCode"`
		SumPaymentNoCarriage     int      `json:"sumPaymentNoCarriage"`
		FlowFlag                 string   `json:"flowFlag"`
		CargoList                []struct {
			Amount             float64       `json:"amount"`
			FinalUnitPrice     float64       `json:"finalUnitPrice"`
			SpecId             string        `json:"specId"`
			SkuId              int64         `json:"skuId"`
			OfferId            int64         `json:"offerId"`
			CargoPromotionList []interface{} `json:"cargoPromotionList"`
		} `json:"cargoList"`
		ShopPromotionList []interface{} `json:"shopPromotionList"`
		TradeModelList    []struct {
			Name        string `json:"name"`
			Description string `json:"description"`
			TradeType   string `json:"tradeType"`
			OpSupport   bool   `json:"opSupport"`
		} `json:"tradeModelList"`
	} `json:"orderPreviewResuslt"`
	Success                            bool          `json:"success"`
	ErrorMsg                           string        `json:"errorMsg"`
	UnsupportedCrossBorderPayOfferList []interface{} `json:"unsupportedCrossBorderPayOfferList"`
}

func (y *AliJX) SaleCheck(id []string) (err error) {

	return
}

// 商品是否可售前置校验   下单支付校验
func (y *AliJX) SaleBeforeCheck(request request.RequestSaleBeforeCheck) (err error, res response.ResSaleBeforeCheck) {
	res.Code = 1
	res.IsOriginalSkuID = 1

	for _, item := range request.LocalSkus {
		res.Data.Available = append(res.Data.Available, uint(item.Sku.Sku))
	}

	return

}

func (y *AliJX) GetAreaAddress(Area, pid string) (AreaID string, err error) {

	return

}

func (y *AliJX) GetStreetAddress(Street, pid string) (StreetID string, err error) {

	return

}

func (y *AliJX) GetGoodsPriceList(arr []string) int64 {

	return 0

}

func (y *AliJX) GetCityAddress(City, pid string) (CityID string, err error) {

	return

}

func (y *AliJX) QueryCategoryList(level, pid uint) (data interface{}) {

	return

}

func (y *AliJX) GetAllAddress() (err error, data interface{}) {

	return

}
func (y *AliJX) GetProvinceAddress(Province, City, Area string) (ProvinceID, CityID, AreaID string) {

	return

}

// 地址映射
func (y *AliJX) CreateAddressMapping(address model.AddressMapping) (err error) {

	//var addressMapping model.AddressMapping
	//addressMapping.MyAddress = address.MyAddress
	//addressMapping.OtherAddress = address.OtherAddress
	//addressMapping.GatherSupply = address.GatherSupply
	//
	//err = source.DB().Where("my_address=? and other_address=? and gather_supply=?", address.MyAddress, address.OtherAddress, address.GatherSupply).FirstOrCreate(&addressMapping).Error

	return
}

var aliSelect []uint

type AddShopData struct {
	Success      bool   `json:"success"`
	ErrorCode    string `json:"errorCode"`
	ErrorMessage string `json:"errorMessage"`
	Data         string `json:"data"`
}

func (y *AliJX) AddShop() (err error) {
	url := "https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao/alibaba.fenxiao.buyer.outshop.add/" + y.dat.BaseInfo.AppKey
	reqData := url2.Values{}
	reqData.Add("access_token", y.dat.BaseInfo.Token)
	reqData.Add("outShopCode", y.dat.BaseInfo.StoreName)
	reqData.Add("channel", "other")
	reqData.Add("_aop_signature", goods.Sign(url, y.dat.BaseInfo.AppSecret, reqData))
	log.Log().Info("aljx AddShop req", zap.Any("info", reqData))

	var resData []byte
	err, resData = utils.PostForm(url, reqData, nil)

	log.Log().Info("aljx  AddShop res：", zap.Any("info", string(resData)))

	var ShopData AddShopData

	err = json.Unmarshal(resData, &ShopData)
	if err != nil {
		return
	}

	if ShopData.Success != true {
		err = errors.New("注册店铺错误")
	}

	return

}

// 确认下单
func (y *AliJX) ConfirmOrder(request request.RequestConfirmOrder) (err error, info *stbz.APIResult) {

	err = y.AddShop()
	if err != nil {
		log.Log().Info("aljx ConfirmOrde AddShop err", zap.Any("err", err))
	}
	var IsOrder model3.Order

	err = source.DB().Where("order_sn=?", request.OrderSn.OrderSn).First(&IsOrder).Error
	if IsOrder.GatherSupplySN != "" {
		log.Log().Info("aljx ConfirmOrder 重复下单 跳过", zap.Any("info", request.OrderSn.OrderSn))
		return
	}

	log.Log().Info("alijx供应链商品准备下单", zap.Any("info", request))
	var skuMap model5.SkuMap
	var shopName string
	for _, skuItem := range request.LocalSkus {
		var sku model4.Sku
		var aliSku model5.Sku
		aliSku.Quantity = int64(skuItem.Number)
		err = source.DB().Where("id=?", skuItem.Sku.Sku).Preload("Product").First(&sku).Error
		if err != nil {
			log.Log().Error("alijx预览供应链商品查询错误", zap.Any("info", err))
			continue
		}
		if sku.Product == nil {
			log.Log().Error("alijx预览供应链商品不存在", zap.Any("info", sku.ID))
			return
		}
		shopName = sku.Product.ShopName
		aliSku.OfferId = int64(sku.Product.SourceGoodsID)
		aliSku.Channel = "other"
		localId := strconv.Itoa(int(sku.ProductID))
		aliSku.OutItemCode = localId
		aliSku.OutShopCode = y.dat.BaseInfo.StoreName

		specLen := len(sku.SpecId)
		if specLen > 13 {
			aliSku.SpecId = sku.SpecId
		}
		skuMap = append(skuMap, aliSku)
	}

	if len(skuMap) == 0 {
		log.Log().Error("alijx预览供应链skuMap 0 ", zap.Any("info", request.OrderSn.OrderSn))
		return
	}

	url := "https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.fenxiaoOrder.create/" + y.dat.BaseInfo.AppKey
	var Consignee string
	if len(request.RequestSaleBeforeCheck.Address.Consignee) == 3 {
		Consignee = request.RequestSaleBeforeCheck.Address.Consignee + "*"
	} else {
		Consignee = request.RequestSaleBeforeCheck.Address.Consignee
	}
	reqData := url2.Values{}
	reqData.Add("access_token", y.dat.BaseInfo.Token)
	reqData.Add("flow", "fenxiao")
	AddressOrder := make(map[string]string)
	AddressOrder["fullName"] = Consignee
	AddressOrder["mobile"] = request.RequestSaleBeforeCheck.Address.Phone
	AddressOrder["phone"] = request.RequestSaleBeforeCheck.Address.Phone
	AddressOrder["postCode"] = "000000"
	AddressOrder["cityText"] = request.Address.City
	AddressOrder["provinceText"] = request.Address.Province
	AddressOrder["areaText"] = request.Address.Area
	AddressOrder["townText"] = request.Address.Street
	AddressOrder["address"] = request.Address.Description
	AddressData, _ := json.Marshal(AddressOrder)

	SkuData, _ := json.Marshal(skuMap)

	reqData.Add("addressParam", string(AddressData))
	reqData.Add("cargoParamList", string(SkuData))
	reqData.Add("isvBizTypeStr", "fenxiaoMedia")
	reqData.Add("_aop_signature", goods.Sign(url, y.dat.BaseInfo.AppSecret, reqData))
	log.Log().Info("aljx订单请求数据", zap.Any("info", reqData))

	var resData []byte
	err, resData = utils.PostForm(url, reqData, nil)
	log.Log().Info("aljx下单返回数据：", zap.Any("info", string(resData)))

	var ResOrder OrderRes
	err = json.Unmarshal(resData, &ResOrder)
	if err != nil {
		log.Log().Error("aljx ResOrder 解析失败", zap.Any("err", string(resData)))
		return
	}
	var order model3.Order
	order.GatherSupplyType = common2.SUPPLY_ALJX
	if ResOrder.Success == true {

		var OrderItem model3.OrderItem

		var updateOrder model3.Order

		err = source.DB().Where("order_sn=?", request.OrderSn.OrderSn).First(&updateOrder).Error
		orderID, _ := strconv.Atoi(ResOrder.Result.OrderId)
		OrderItem.GatherSupplySN = ResOrder.Result.OrderId
		source.DB().Where("order_id=?", updateOrder.ID).Updates(&OrderItem)
		Note := "阿里精选已同步下单" + "单号：" + ResOrder.Result.OrderId
		log.Log().Info("aljx下单返回数据2：", zap.Any("info", Note))

		note := updateOrder.Note + "-" + Note
		err = source.DB().Table("orders").Where("id=?", updateOrder.ID).Updates(map[string]interface{}{"gather_supply_type": common2.SUPPLY_ALJX, "note": note, "gather_supply_sn": ResOrder.Result.OrderId}).Error

		if err != nil {
			log.Log().Error("aljx下单返回更新错误：", zap.Any("err", err))

		}
		y.Pay(int64(orderID))
	} else {
		order.GatherSupplyMsg = ResOrder.Message
	}

	order.SourceShopName = shopName
	err = source.DB().Where("order_sn=?", request.OrderSn.OrderSn).Updates(&order).Error
	if err != nil {
		log.Log().Info("aljx 保存三方单号失败", zap.Any("info", err))
	}

	//}
	aliSelect = append(aliSelect, request.OrderSN)

	return

}

func (ali *AliJX) Pay(orderID int64) (err error) {

	url := "http://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.pay.protocolPay.preparePay/" + ali.dat.BaseInfo.AppKey

	reqData := url2.Values{}
	reqData.Add("access_token", ali.dat.BaseInfo.Token)
	var Data = make(map[string]int64)
	Data["orderId"] = orderID

	var jsonData []byte
	jsonData, err = json.Marshal(Data)
	if err != nil {
		log.Log().Error("阿里巴巴支付错误", zap.Any("err", err))
		return
	}

	reqData.Add("tradeWithholdPreparePayParam", string(jsonData))
	reqData.Add("_aop_signature", goods.Sign(url, ali.dat.BaseInfo.AppSecret, reqData))

	var resData []byte
	err, resData = utils.PostForm(url, reqData, nil)
	type PayRes struct {
		Success bool   `json:"success"`
		Code    string `json:"code"`
		Message string `json:"message"`
	}
	var payRes PayRes

	err = json.Unmarshal(resData, &payRes)
	if err != nil {
		log.Log().Error("阿里巴巴支付错误1", zap.Any("err", err))
		return
	}

	log.Log().Info("阿里巴巴支付返回结果", zap.Any("err", payRes))

	return
}

type OrderRes struct {
	Result struct {
		TotalSuccessAmount int    `json:"totalSuccessAmount"`
		OrderId            string `json:"orderId"`
		PostFee            int    `json:"postFee"`
	} `json:"result"`
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

// 物流查询
func (y *AliJX) ExpressQuery(request request.RequestExpress) (err error, data interface{}) {
	return

}
