package cron

import (
	"ali-selected/component/goods"
	model3 "ali-selected/model"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	url2 "net/url"
	pmodel "product/model"
	"product/mq"
	pservice "product/service"
	"public-supply/common"
	"public-supply/model"
	setting2 "public-supply/setting"
	"strconv"
	"strings"
	"time"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
	"yz-go/utils"

	"go.uber.org/zap"
)

func PushAlijxProductUpdateHandle() {

	log.Log().Info("cron更新阿里jx商品上下架状态start")

	var gatherList []model.GatherSupply
	err := source.DB().Where("category_id = ?", common.SUPPLY_ALJX).Where("deleted_at is null").Find(&gatherList).Error
	if err != nil {
		return
	}

	for _, v := range gatherList {

		CreateCronTaskSale(int(v.ID))

	}

}

var datsetting model.SupplySetting

func InitSetting(taskID int) {
	err, setting := setting2.GetSetting("gatherSupply" + strconv.Itoa(taskID))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}

	err = json.Unmarshal([]byte(setting.Value), &datsetting)
	if err != nil {

		return
	}
}
func CreateCronTaskSale(taskID int) {

	InitSetting(taskID)

	var cronStr string

	cronStr = "0 */30 * * * *"

	cron.PushTask(cron.Task{
		Key:  "alijxproductupdate" + strconv.Itoa(taskID),
		Name: "alijxproductupdate供应链发货查询定时任务" + strconv.Itoa(taskID),
		Spec: cronStr, //"0/3 * * * * ?"
		Handle: func(task cron.Task) {
			ProductUpdate(taskID)

		},
		Status: cron.ENABLED,
	})

}

func GetDetail(item []uint) (err error, modelData model3.ALJXDetail) {
	url := "https://gw.open.1688.com/openapi/param2/2/com.alibaba.fenxiao/alibaba.pifatuan.product.detail.list/" + datsetting.BaseInfo.AppKey
	reqDataParam := url2.Values{}
	reqDataParam.Add("access_token", datsetting.BaseInfo.Token)
	jsonItem, _ := json.Marshal(&item)
	reqDataParam.Add("offerIds", string(jsonItem))
	reqDataParam.Add("_aop_signature", goods.Sign(url, datsetting.BaseInfo.AppSecret, reqDataParam))
	var resData []byte
	err, resData = utils.PostForm(url, reqDataParam, nil)
	if err != nil {
		return
	}

	err = json.Unmarshal(resData, &modelData)

	return
}

type RelationData struct {
	Success bool   `json:"success"`
	Data    string `json:"data"`
}

func AddRelation(localProductID, offerId string) (err error) {
	url := "https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao/alibaba.fenxiao.buyer.outproduct.relation.add/" + datsetting.BaseInfo.AppKey

	reqData := url2.Values{}
	reqData.Add("access_token", datsetting.BaseInfo.Token)
	reqData.Add("outItemCode", localProductID)
	reqData.Add("outShopCode", datsetting.BaseInfo.StoreName)
	reqData.Add("offerId", offerId)
	reqData.Add("channel", "other")
	reqData.Add("_aop_signature", goods.Sign(url, datsetting.BaseInfo.AppSecret, reqData))
	var resData []byte
	log.Log().Info("aljx AddRelation request", zap.Any("info", reqData))
	err, resData = utils.PostForm(url, reqData, nil)
	log.Log().Info("aljx AddRelation response", zap.Any("info", string(resData)))
	var relationData RelationData
	err = json.Unmarshal(resData, &relationData)
	if err != nil {
		return
	}
	if relationData.Success != true || relationData.Data != "true" {

		err = errors.New("关联商品错误:" + localProductID)
	}
	return

}

func GetRelation(localProductID, offerId string) (err error) {
	url := "https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao/alibaba.fenxiao.buyer.outproduct.relation.get/" + datsetting.BaseInfo.AppKey

	reqData := url2.Values{}
	reqData.Add("access_token", datsetting.BaseInfo.Token)
	reqData.Add("outItemCode", localProductID)
	reqData.Add("outShopCode", datsetting.BaseInfo.StoreName)
	reqData.Add("offerId", offerId)
	reqData.Add("channel", "other")
	reqData.Add("_aop_signature", goods.Sign(url, datsetting.BaseInfo.AppSecret, reqData))
	var resData []byte
	log.Log().Info("aljx AddRelation request", zap.Any("info", reqData))
	err, resData = utils.PostForm(url, reqData, nil)
	log.Log().Info("aljx AddRelation response", zap.Any("info", string(resData)))
	var relationData RelationData
	err = json.Unmarshal(resData, &relationData)
	if err != nil {
		return
	}
	if relationData.Success != true || relationData.Data != "true" {

		err = errors.New("关联商品错误:" + localProductID)
	}
	return

}
func GetProductInfo(item uint) (err error, modelData model3.ALJXNewDetail) {
	url := "https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao/alibaba.fenxiao.productInfo.get/" + datsetting.BaseInfo.AppKey
	reqDataParam := url2.Values{}
	reqDataParam.Add("access_token", datsetting.BaseInfo.Token)
	//jsonItem, _ := json.Marshal(&item)
	id := strconv.Itoa(int(item))
	reqDataParam.Add("offerId", id)
	reqDataParam.Add("_aop_signature", goods.Sign(url, datsetting.BaseInfo.AppSecret, reqDataParam))
	var resData []byte
	err, resData = utils.PostForm(url, reqDataParam, nil)
	if err != nil {
		return
	}

	err = json.Unmarshal(resData, &modelData)

	return
}

type Area struct {
	Code       string `json:"code"`
	Name       string `json:"name"`
	ParentCode string `json:"parentCode"`
}
type AliArea struct {
	Result []Area `json:"result"`
}

var AreaList []Area

func GetArea(code string) (err error, modelData model3.ALJXDetail) {
	url := "https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.addresscode.getchild/" + datsetting.BaseInfo.AppKey
	reqDataParam := url2.Values{}
	reqDataParam.Add("access_token", datsetting.BaseInfo.Token)
	//jsonItem, _ := json.Marshal(&item)
	if code != "" {
		reqDataParam.Add("areaCode", code)

	}
	reqDataParam.Add("webSite", "1688")
	reqDataParam.Add("_aop_signature", goods.Sign(url, datsetting.BaseInfo.AppSecret, reqDataParam))
	var resData []byte
	err, resData = utils.PostForm(url, reqDataParam, nil)
	if err != nil {
		return
	}
	var aliArea AliArea

	err = json.Unmarshal(resData, &aliArea)

	//AreaList=append(AreaList,aliArea.Result)
	for _, item := range aliArea.Result {
		AreaList = append(AreaList, item)
		GetArea(item.Code)
	}

	return
}

type AddShopData struct {
	Success      bool   `json:"success"`
	ErrorCode    string `json:"errorCode"`
	ErrorMessage string `json:"errorMessage"`
	Data         string `json:"data"`
}

func AddShop() (err error) {
	url := "https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao/alibaba.fenxiao.buyer.outshop.add/" + datsetting.BaseInfo.AppKey
	reqData := url2.Values{}
	reqData.Add("access_token", datsetting.BaseInfo.Token)
	reqData.Add("outShopCode", datsetting.BaseInfo.StoreName)
	reqData.Add("channel", "other")
	reqData.Add("_aop_signature", goods.Sign(url, datsetting.BaseInfo.AppSecret, reqData))
	log.Log().Info("aljx cron AddShop req", zap.Any("info", reqData))

	var resData []byte
	err, resData = utils.PostForm(url, reqData, nil)

	log.Log().Info("aljx croncron  AddShop res：", zap.Any("info", string(resData)))

	var ShopData AddShopData

	err = json.Unmarshal(resData, &ShopData)
	if err != nil {
		return
	}

	if ShopData.Success != true {
		err = errors.New("注册店铺错误")
	}

	return

}

func UpdateRecord(updateGoods pservice.ProductForUpdate, localProduct pservice.ProductForUpdate) (err error) {

	localSkuJson, _ := json.Marshal(localProduct.Skus)
	localSkuMd5 := utils.MD5V(localSkuJson)

	updateSkuJson, _ := json.Marshal(updateGoods.Skus)
	updateSkuMd5 := utils.MD5V(updateSkuJson)

	if localSkuMd5 != updateSkuMd5 {
		var aliProductUpdateRecord model3.AliProductUpdateRecord
		aliProductUpdateRecord.ProductID = localProduct.ID
		aliProductUpdateRecord.Type = "规格变动"
		err = source.DB().Create(&aliProductUpdateRecord).Error
	}

	if strings.Contains(localProduct.ImageUrl, "cbu01.alicdn.com") && localProduct.ImageUrl != updateGoods.ImageUrl { //本地主图是上游图片，检测跟本次更新是否一致
		var aliProductUpdateRecord model3.AliProductUpdateRecord
		aliProductUpdateRecord.ProductID = localProduct.ID
		aliProductUpdateRecord.Type = "主图异常"
		err = source.DB().Create(&aliProductUpdateRecord).Error
	}

	return

}

func ProductUpdate(gatherSupplyID int) (err error) {
	if datsetting.BaseInfo.AppKey == "" {
		InitSetting(gatherSupplyID)
		AddShop()
	}

	if datsetting.BaseInfo.AppKey == "" {
		return
	}

	var Product []pservice.ProductForUpdate

	err = source.DB().Preload("Skus").Where("status_lock=0 and source =?    and gather_supply_id=?", common.SUPPLY_ALJX, gatherSupplyID).Order("updated_at asc ").Limit(1000).Find(&Product).Error
	if err != nil {
		return
	}

	for _, item := range Product {
		//localID := strconv.Itoa(int(item.ID))
		//sourceGoodsID := strconv.Itoa(int(item.SourceGoodsID))

		//AddRelation(localID, sourceGoodsID)
		log.Log().Info("info", zap.Any("info", item.ID))

		var updateGoods pservice.ProductForUpdate
		updateGoods = item

		var detail model3.ALJXNewDetail
		//var ids []uint
		//ids = append(ids, item.SourceGoodsID)
		err, detail = GetProductInfo(item.SourceGoodsID)
		if err != nil {
			continue
		}
		if detail.Success != true {
			//log.Log().Info("detail.Result.Success != true", zap.Any("info", item.SourceGoodsID))

			UndercarriageProduct(item.ID)
			continue
		}
		//if len(detail.ProductInfo) == 0 {
		//	log.Log().Info("len(detail.Result.Result) == 0", zap.Any("info", ids))
		//
		//	UndercarriageProduct(item.ID)
		//	continue
		//}
		if detail.ProductInfo.Status != "published" {
			log.Log().Info("detail.Result.Result[0].ProductInfo.Status", zap.Any("info", item.SourceGoodsID))

			UndercarriageProduct(item.ID)
			continue
		}

		if len(detail.ProductInfo.ProductExtendInfos) > 0 {
			var isOnePsale = false
			for _, jxitem := range detail.ProductInfo.ProductExtendInfos {
				//if jxitem.Key == "isPftOffer" && jxitem.Value == "true" {
				//	isjx = true
				//}
				if jxitem.Key == "isOnePsale" && jxitem.Value == "true" {
					isOnePsale = true
				}
			}

			if isOnePsale == false {
				UndercarriageProduct(item.ID)
				continue
			}
			//fmt.Println(isjx)
		}

		var alishop model3.AliShop
		alishop.Name = strings.TrimSpace(detail.ProductInfo.SupplierLoginId)
		alishop.IsOpen = "0"
		alishop.Strategy = "{\"origin\":\"100\",\"marketing\":\"100\",\"guide\":\"100\",\"price\":\"100\",\"cost\":\"100\"}"
		err = source.DB().Where("name=?", detail.ProductInfo.SupplierLoginId).FirstOrCreate(&alishop).Error
		//if err!=nil{
		//	continue
		//}

		aliSkus := detail.ProductInfo.ProductSkuInfos
		//err = json.Unmarshal([]byte(), &aliSkus)
		var sku = pservice.Sku{}
		var skuList []pservice.Sku
		var minProfitRate float64

		// 收集所有规格属性的可能值
		//specMap := make(map[string][]string)
		//for _, skuitem := range aliSkus {
		//	for _, attribute := range skuitem.Attributes {
		//		if _, exists := specMap[attribute.AttributeName]; !exists {
		//			specMap[attribute.AttributeName] = []string{}
		//		}
		//		// 检查是否已存在该值
		//		valueExists := false
		//		for _, v := range specMap[attribute.AttributeName] {
		//			if v == attribute.AttributeValue {
		//				valueExists = true
		//				break
		//			}
		//		}
		//		if !valueExists {
		//			specMap[attribute.AttributeName] = append(specMap[attribute.AttributeName], attribute.AttributeValue)
		//		}
		//	}
		//}

		// 计算理论上应该存在的规格数量
		//expectedCount := 1
		//for _, values := range specMap {
		//	expectedCount *= len(values)
		//}

		for sk, skuitem := range aliSkus {
			if skuitem.JxhyPrice == 0 && skuitem.ConsignPrice == 0 {
				continue
			}
			sku = pservice.Sku{}
			var options pmodel.Options
			var option pmodel.Option
			var skuTitle string
			for _, attribute := range skuitem.Attributes {
				option.SpecName = attribute.AttributeName
				option.SpecItemName = attribute.AttributeValue
				options = append(options, option)
				skuTitle += attribute.AttributeValue
			}

			for _, localskuitem := range item.Skus {
				if localskuitem.OriginalSkuID == int(skuitem.SkuId) {
					sku = localskuitem
					break
				}
			}

			//if len(goods.Skus) > 0 {
			//	sku.ID = goods.Skus[0].ID
			//}
			var costPrice, price, originPrice, activityPrice, guidePrice uint
			var skuPrice model.Goods

			skuPrice.CostPrice = uint(math.Floor(skuitem.ConsignPrice * 100))      //成本
			skuPrice.AgreementPrice = uint(math.Floor(skuitem.ConsignPrice * 100)) //成本
			skuPrice.ActivityPrice = uint(math.Floor(skuitem.ConsignPrice * 100))  //成本

			if skuitem.ConsignPrice < skuitem.JxhyPrice {
				skuPrice.CostPrice = uint(math.Floor(skuitem.JxhyPrice * 100))      //成本
				skuPrice.AgreementPrice = uint(math.Floor(skuitem.JxhyPrice * 100)) //成本
				skuPrice.ActivityPrice = uint(math.Floor(skuitem.JxhyPrice * 100))  //成本

			}

			skuPrice.Source = common.ALJX_SOURCE

			if alishop.IsOpen == "0" { // 独立设置
				err, costPrice, price, originPrice, activityPrice, guidePrice = goods.GetShopPricingPrice(skuPrice, alishop)

			} else { //统一设置
				err, costPrice, price, originPrice, activityPrice, guidePrice = goods.GetPricingPrice(skuPrice, "gatherSupply"+strconv.Itoa(gatherSupplyID))

			}

			//if costPrice == 0 || price == 0 {
			//	continue
			//}

			if datsetting.UpdateInfo.CurrentPrice == 1 {
				sku.Price = price
			}

			if datsetting.UpdateInfo.CostPrice == 1 {
				sku.CostPrice = costPrice
			}

			if datsetting.UpdateInfo.GuidePrice == 1 {
				sku.GuidePrice = guidePrice
			}

			if datsetting.UpdateInfo.OriginalPrice == 1 {
				sku.OriginPrice = originPrice
			}

			if datsetting.UpdateInfo.ActivityPrice == 1 {
				sku.ActivityPrice = activityPrice
			}

			if sku.OriginPrice == 0 {
				sku.OriginPrice = originPrice
			}
			if sku.GuidePrice == 0 {
				sku.GuidePrice = guidePrice
			}
			if sku.ActivityPrice == 0 {
				sku.ActivityPrice = activityPrice
			}

			sku.Title = skuTitle
			sku.Options = options
			sku.Stock = skuitem.AmountOnSale
			sku.IsDisplay = updateGoods.IsDisplay
			sku.OriginalSkuID = int(skuitem.SkuId)
			sku.SpecId = skuitem.SpecId

			if sku.GuidePrice < sku.Price {
				sku.GuidePrice = sku.Price
			}
			if sku.OriginPrice < sku.Price {
				sku.OriginPrice = sku.Price
			}
			if sku.ActivityPrice < sku.Price {
				sku.ActivityPrice = sku.Price
			}
			if datsetting.UpdateInfo.BaseInfo == 1 {

				if len(skuitem.Attributes) > 0 {
					if skuitem.Attributes[0].SkuImageUrl != "" {

						if strings.Contains(skuitem.Attributes[0].SkuImageUrl, "https://cbu01.alicdn.com") {
							sku.ImageUrl = skuitem.Attributes[0].SkuImageUrl
						} else {
							sku.ImageUrl = "https://cbu01.alicdn.com/" + skuitem.Attributes[0].SkuImageUrl
						}

					} else {
						sku.ImageUrl = ""
					}
				}
			}

			if sku.GuidePrice > 0 {
				sku.ProfitRate = utils.ExecProfitRate(sku.GuidePrice, sku.Price)
			} else {
				sku.ProfitRate = 0
			}
			if sk == 0 {
				minProfitRate = sku.ProfitRate
			}
			if sku.ProfitRate <= minProfitRate {
				minProfitRate = sku.ProfitRate
			}
			if strings.Contains(sku.Title, "客服") || strings.Contains(sku.Title, "代发") || strings.Contains(sku.Title, "批量") || strings.Contains(sku.Title, "厂家") || strings.Contains(sku.Title, "咨询") {
				sku.IsDisplay = 0

			} else {
				skuList = append(skuList, sku)

			}

		}

		//if len(skuList) > 0 {
		//
		//	// 如果实际SKU数量小于期望数量，补充缺失的规格
		//	if len(skuList) < expectedCount {
		//		// 生成所有可能的规格组合
		//		existingCombos := make(map[string]bool)
		//		for _, s := range skuList {
		//			var combo string
		//			for _, opt := range s.Options {
		//				combo += opt.SpecName + ":" + opt.SpecItemName + ";"
		//			}
		//			existingCombos[combo] = true
		//		}
		//
		//		// 使用第一个SKU作为模板
		//		templateSku := skuList[0]
		//
		//		// 生成并检查所有可能的组合
		//		var specNames []string
		//		var specValues [][]string
		//		for name, values := range specMap {
		//			specNames = append(specNames, name)
		//			specValues = append(specValues, values)
		//		}
		//
		//		var generateCombinations func(int, []string)
		//		currentCombo := make([]string, len(specNames))
		//		generateCombinations = func(pos int, current []string) {
		//			if pos == len(specNames) {
		//				var combo string
		//				var options pmodel.Options
		//				for i, name := range specNames {
		//					combo += name + ":" + current[i] + ";"
		//					options = append(options, pmodel.Option{SpecName: name, SpecItemName: current[i]})
		//				}
		//
		//				// 如果这个组合不存在，添加它
		//				if !existingCombos[combo] {
		//					newSku := templateSku
		//					newSku.Options = options
		//					newSku.Stock = 0
		//					newSku.ID = 0
		//					newSku.Desc = "填充规格"
		//					newSku.Title = strings.Join(current, "-")
		//					skuList = append(skuList, newSku)
		//				}
		//				return
		//			}
		//
		//			for _, val := range specValues[pos] {
		//				currentCombo[pos] = val
		//				generateCombinations(pos+1, currentCombo)
		//			}
		//		}
		//
		//		generateCombinations(0, currentCombo)
		//	}
		//
		//}

		if len(skuList) == 0 {
			for _, localskuitem := range item.Skus {
				if localskuitem.OriginalSkuID == int(detail.ProductInfo.ProductID) {
					sku = localskuitem
					break
				}
			}
			var options pmodel.Options
			var option pmodel.Option
			option.SpecName = "规格"
			option.SpecItemName = "默认"
			options = append(options, option)

			var costPrice, price, originPrice, activityPrice, guidePrice uint
			var skuPrice model.Goods

			skuPrice.CostPrice = uint(utils.Yuan2Fen(detail.ProductInfo.ProductSaleInfo.ConsignPrice))

			if skuPrice.CostPrice == 0 || detail.ProductInfo.ProductSaleInfo.JxhyPrice > detail.ProductInfo.ProductSaleInfo.ConsignPrice {
				skuPrice.CostPrice = uint(utils.Yuan2Fen(detail.ProductInfo.ProductSaleInfo.JxhyPrice))

			}

			skuPrice.Source = common.ALJX_SOURCE
			//log.Log().Info(" skuPrice.CostPrice", zap.Any("info", detail.Result.Result[0].ProductInfo.SaleInfo.Retailprice))
			//log.Log().Info(" skuPrice.CostPrice1", zap.Any("info", skuPrice.CostPrice))

			if alishop.IsOpen == "0" { // 独立设置
				err, costPrice, price, originPrice, activityPrice, guidePrice = goods.GetShopPricingPrice(skuPrice, alishop)

			} else { //统一设置
				err, costPrice, price, originPrice, activityPrice, guidePrice = goods.GetPricingPrice(skuPrice, "gatherSupply"+strconv.Itoa(gatherSupplyID))

			}

			log.Log().Info(" goods.GetShopPricingPrice-1", zap.Any("costPrice", costPrice), zap.Any("price", price))

			if costPrice == 0 || price == 0 {
				sku.IsDisplay = 0

			}
			sku.Title = "默认"
			sku.Options = options

			if datsetting.UpdateInfo.CurrentPrice == 1 {
				sku.Price = price
			}

			if datsetting.UpdateInfo.CostPrice == 1 {
				sku.CostPrice = costPrice
			}

			if datsetting.UpdateInfo.GuidePrice == 1 {
				sku.GuidePrice = guidePrice
			}

			if datsetting.UpdateInfo.OriginalPrice == 1 {
				sku.OriginPrice = originPrice
			}

			if datsetting.UpdateInfo.ActivityPrice == 1 {
				sku.ActivityPrice = activityPrice
			}

			//sku.CostPrice = costPrice
			//sku.Price = price
			//
			//fmt.Println(originPrice, activityPrice, guidePrice)
			//if sku.OriginPrice == 0 {
			//	sku.OriginPrice = originPrice
			//}
			//if sku.GuidePrice == 0 {
			//	sku.GuidePrice = guidePrice
			//}
			//if sku.ActivityPrice == 0 {
			//	sku.ActivityPrice = activityPrice
			//}

			sku.Stock = int(detail.ProductInfo.ProductSaleInfo.AmountOnSale)
			sku.IsDisplay = updateGoods.IsDisplay
			sku.OriginalSkuID = int(detail.ProductInfo.ProductID)
			if sku.GuidePrice < sku.Price {
				sku.GuidePrice = sku.Price
			}
			if sku.OriginPrice < sku.Price {
				sku.OriginPrice = sku.Price
			}
			if sku.ActivityPrice < sku.Price {
				sku.ActivityPrice = sku.Price
			}

			productID := strconv.Itoa(int(detail.ProductInfo.ProductID))
			sku.SpecId = productID
			if strings.Contains(sku.Title, "客服") || strings.Contains(sku.Title, "代发") || strings.Contains(sku.Title, "批量") || strings.Contains(sku.Title, "厂家") || strings.Contains(sku.Title, "咨询") {
				sku.IsDisplay = 0

			}
			if sku.GuidePrice > 0 {
				sku.ProfitRate = utils.ExecProfitRate(sku.GuidePrice, sku.Price)
			} else {
				sku.ProfitRate = 0
			}
			minProfitRate = sku.ProfitRate

			skuList = append(skuList, sku)
			updateGoods.SingleOption = 1
		}
		//updateGoods.Title = detail.Result.Result[0].ProductInfo.Subject

		updateGoods.MinBuyQty = 1

		updateGoods.Skus = skuList
		if detail.ProductInfo.Status == "published" {
			updateGoods.IsDisplay = 1
		}

		itemSku := goods.GetMapA(updateGoods.Skus)
		//log.Log().Info(" goods.GetPriceA", zap.Any("info", updateGoods.Skus))

		updateGoods.MaxPrice, updateGoods.MinPrice = goods.GetPriceA(updateGoods.Skus)
		updateGoods.CostPrice = itemSku.CostPrice

		//if item.GuidePrice == 0 {
		//	updateGoods.GuidePrice = itemSku.GuidePrice
		//}
		//if item.OriginPrice == 0 {
		//	updateGoods.OriginPrice = itemSku.OriginPrice
		//}
		//if item.ActivityPrice == 0 {
		//	updateGoods.ActivityPrice = itemSku.ActivityPrice
		//}

		updateGoods.Price = itemSku.Price
		if updateGoods.Price == 0 {
			log.Log().Info("updateGoods.Price", zap.Any("info", itemSku))
			UndercarriageProduct(item.ID)
			continue
		}
		//updateGoods.ProfitRate = pservice.Decimal((float64(updateGoods.GuidePrice) - float64(updateGoods.Price)) / float64(updateGoods.GuidePrice) * 100)
		if len(updateGoods.Skus) > 0 {
			updateGoods.ProfitRate = minProfitRate
		}
		updateGoods.Stock = uint(itemSku.Stock)
		if updateGoods.Stock == 0 {
			updateGoods.IsDisplay = 0
		}
		//if detail.Result.Result[0].ProductInfo.MainImage != "" {
		//	updateGoods.ImageUrl = detail.Result.Result[0].ProductInfo.MainImage
		//}

		//var jsonImage []string

		//if len(detail.Result.Result[0].ProductInfo.JsonImage) > 0 {
		//	err = json.Unmarshal([]byte(detail.Result.Result[0].ProductInfo.JsonImage), &jsonImage)
		//} else {
		//	jsonImage = detail.Result.Result[0].ProductInfo.Image.Images
		//}
		//
		//var gallery pmodel.Gallery
		//for _, gitem := range jsonImage {
		//	var galleryItem pmodel.GalleryItem
		//	galleryItem.Src = "https://cbu01.alicdn.com/" + gitem
		//	galleryItem.Type = 1
		//	gallery = append(gallery, galleryItem)
		//}
		//
		//updateGoods.Gallery = gallery

		//if len(updateGoods.Gallery) <= 0 {
		//	var Gallery pmodel.Gallery
		//	Gallery = append(Gallery, pmodel.GalleryItem{Type: 1, Src: updateGoods.ImageUrl})
		//	updateGoods.Gallery = Gallery
		//}

		//if updateGoods.ImageUrl == "" {
		//	if len(updateGoods.Gallery) > 0 {
		//		updateGoods.ImageUrl = updateGoods.Gallery[0].Src
		//	}
		//
		//}

		updateGoods.ShopName = detail.ProductInfo.SupplierLoginId
		err = pservice.UpdateProduct(updateGoods)
		if err != nil {
			log.Log().Error("更新商品错误 update", zap.Any("err", err))
			return err
		}
		UpdateRecord(updateGoods, item)
	}

	return
}

func ProductUpdateA(gatherSupplyID int) (err error) {
	if datsetting.BaseInfo.AppKey == "" {
		InitSetting(gatherSupplyID)
		AddShop()
	}

	if datsetting.BaseInfo.AppKey == "" {
		return
	}

	var Product []pservice.ProductForUpdate

	err = source.DB().Preload("Skus").Where("status_lock=0 and source =?    and gather_supply_id=? ", common.SUPPLY_ALJX, gatherSupplyID).Order("updated_at asc ").Limit(2000).Find(&Product).Error
	if err != nil {
		return
	}

	for _, item := range Product {
		localID := strconv.Itoa(int(item.ID))
		sourceGoodsID := strconv.Itoa(int(item.SourceGoodsID))

		AddRelation(localID, sourceGoodsID)
		log.Log().Info("info", zap.Any("info", item.ID))

		var updateGoods pservice.ProductForUpdate
		updateGoods = item

		var detail model3.ALJXNewDetail
		//var ids []uint
		//ids = append(ids, item.SourceGoodsID)
		err, detail = GetProductInfo(item.SourceGoodsID)
		if err != nil {
			continue
		}
		if detail.Success != true {
			//log.Log().Info("detail.Result.Success != true", zap.Any("info", item.SourceGoodsID))

			UndercarriageProduct(item.ID)
			continue
		}
		//if len(detail.ProductInfo) == 0 {
		//	log.Log().Info("len(detail.Result.Result) == 0", zap.Any("info", ids))
		//
		//	UndercarriageProduct(item.ID)
		//	continue
		//}
		if detail.ProductInfo.Status != "published" {
			log.Log().Info("detail.Result.Result[0].ProductInfo.Status", zap.Any("info", item.SourceGoodsID))

			UndercarriageProduct(item.ID)
			continue
		}

		if len(detail.ProductInfo.ProductExtendInfos) > 0 {
			var isOnePsale = false
			for _, jxitem := range detail.ProductInfo.ProductExtendInfos {
				//if jxitem.Key == "isPftOffer" && jxitem.Value == "true" {
				//	isjx = true
				//}
				if jxitem.Key == "isOnePsale" && jxitem.Value == "true" {
					isOnePsale = true
				}
			}

			if isOnePsale == false {
				UndercarriageProduct(item.ID)
				continue
			}
			//fmt.Println(isjx)
		}

		var alishop model3.AliShop
		alishop.Name = strings.TrimSpace(detail.ProductInfo.SupplierLoginId)
		alishop.IsOpen = "0"
		alishop.Strategy = "{\"origin\":\"100\",\"marketing\":\"100\",\"guide\":\"100\",\"price\":\"100\",\"cost\":\"100\"}"
		err = source.DB().Where("name=?", detail.ProductInfo.SupplierLoginId).FirstOrCreate(&alishop).Error
		//if err!=nil{
		//	continue
		//}

		aliSkus := detail.ProductInfo.ProductSkuInfos
		//err = json.Unmarshal([]byte(), &aliSkus)
		var sku = pservice.Sku{}
		var skuList []pservice.Sku
		var minProfitRate float64

		// 收集所有规格属性的可能值
		specMap := make(map[string][]string)
		for _, skuitem := range aliSkus {
			for _, attribute := range skuitem.Attributes {
				if _, exists := specMap[attribute.AttributeName]; !exists {
					specMap[attribute.AttributeName] = []string{}
				}
				// 检查是否已存在该值
				valueExists := false
				for _, v := range specMap[attribute.AttributeName] {
					if v == attribute.AttributeValue {
						valueExists = true
						break
					}
				}
				if !valueExists {
					specMap[attribute.AttributeName] = append(specMap[attribute.AttributeName], attribute.AttributeValue)
				}
			}
		}

		// 计算理论上应该存在的规格数量
		expectedCount := 1
		for _, values := range specMap {
			expectedCount *= len(values)
		}

		for sk, skuitem := range aliSkus {
			if skuitem.JxhyPrice == 0 && skuitem.ConsignPrice == 0 {
				continue
			}
			sku = pservice.Sku{}
			var options pmodel.Options
			var option pmodel.Option
			var skuTitle string
			for _, attribute := range skuitem.Attributes {
				option.SpecName = attribute.AttributeName
				option.SpecItemName = attribute.AttributeValue
				options = append(options, option)
				skuTitle += attribute.AttributeValue
			}

			for _, localskuitem := range item.Skus {
				if localskuitem.OriginalSkuID == int(skuitem.SkuId) {
					sku = localskuitem
					break
				}
			}

			//if len(goods.Skus) > 0 {
			//	sku.ID = goods.Skus[0].ID
			//}
			var costPrice, price, originPrice, activityPrice, guidePrice uint
			var skuPrice model.Goods

			skuPrice.CostPrice = uint(math.Floor(skuitem.ConsignPrice * 100)) //成本

			if skuitem.ConsignPrice < skuitem.JxhyPrice {
				skuPrice.CostPrice = uint(math.Floor(skuitem.JxhyPrice * 100)) //成本

			}

			skuPrice.Source = common.ALJX_SOURCE

			err, costPrice, price, originPrice, activityPrice, guidePrice = goods.GetShopPricingPrice(skuPrice, alishop)

			//if costPrice == 0 || price == 0 {
			//	continue
			//}

			if datsetting.UpdateInfo.CurrentPrice == 1 {
				sku.Price = price
			}

			if datsetting.UpdateInfo.CostPrice == 1 {
				sku.CostPrice = costPrice
			}

			if sku.OriginPrice == 0 {
				sku.OriginPrice = originPrice
			}
			if sku.GuidePrice == 0 {
				sku.GuidePrice = guidePrice
			}
			if sku.ActivityPrice == 0 {
				sku.ActivityPrice = activityPrice
			}

			sku.Title = skuTitle
			sku.Options = options
			sku.Stock = skuitem.AmountOnSale
			sku.IsDisplay = updateGoods.IsDisplay
			sku.OriginalSkuID = int(skuitem.SkuId)
			sku.SpecId = skuitem.SpecId

			if sku.GuidePrice < sku.Price {
				sku.GuidePrice = sku.Price
			}
			if sku.OriginPrice < sku.Price {
				sku.OriginPrice = sku.Price
			}
			if sku.ActivityPrice < sku.Price {
				sku.ActivityPrice = sku.Price
			}

			if len(skuitem.Attributes) > 0 {
				if skuitem.Attributes[0].SkuImageUrl != "" {
					sku.ImageUrl = "https://cbu01.alicdn.com/" + skuitem.Attributes[0].SkuImageUrl
				} else {
					sku.ImageUrl = ""
				}
			}
			if sku.GuidePrice > 0 {
				sku.ProfitRate = utils.ExecProfitRate(sku.GuidePrice, sku.Price)
			} else {
				sku.ProfitRate = 0
			}
			if sk == 0 {
				minProfitRate = sku.ProfitRate
			}
			if sku.ProfitRate <= minProfitRate {
				minProfitRate = sku.ProfitRate
			}
			if strings.Contains(sku.Title, "客服") || strings.Contains(sku.Title, "代发") || strings.Contains(sku.Title, "批量") || strings.Contains(sku.Title, "厂家") || strings.Contains(sku.Title, "咨询") {
				sku.IsDisplay = 0

			} else {
				skuList = append(skuList, sku)

			}

		}

		// 如果实际SKU数量小于期望数量，补充缺失的规格
		if len(skuList) < expectedCount {
			// 生成所有可能的规格组合
			existingCombos := make(map[string]bool)
			for _, s := range skuList {
				var combo string
				for _, opt := range s.Options {
					combo += opt.SpecName + ":" + opt.SpecItemName + ";"
				}
				existingCombos[combo] = true
			}

			// 使用第一个SKU作为模板
			templateSku := skuList[0]

			// 生成并检查所有可能的组合
			var specNames []string
			var specValues [][]string
			for name, values := range specMap {
				specNames = append(specNames, name)
				specValues = append(specValues, values)
			}

			var generateCombinations func(int, []string)
			currentCombo := make([]string, len(specNames))
			generateCombinations = func(pos int, current []string) {
				if pos == len(specNames) {
					var combo string
					var options pmodel.Options
					for i, name := range specNames {
						combo += name + ":" + current[i] + ";"
						options = append(options, pmodel.Option{SpecName: name, SpecItemName: current[i]})
					}

					// 如果这个组合不存在，添加它
					if !existingCombos[combo] {
						newSku := templateSku
						newSku.Options = options
						newSku.Title = strings.Join(current, "-")
						skuList = append(skuList, newSku)
					}
					return
				}

				for _, val := range specValues[pos] {
					currentCombo[pos] = val
					generateCombinations(pos+1, currentCombo)
				}
			}

			generateCombinations(0, currentCombo)
		}

		if len(skuList) == 0 {
			for _, localskuitem := range item.Skus {
				if localskuitem.OriginalSkuID == int(detail.ProductInfo.ProductID) {
					sku = localskuitem
					break
				}
			}
			var options pmodel.Options
			var option pmodel.Option
			option.SpecName = "规格"
			option.SpecItemName = "默认"
			options = append(options, option)

			var costPrice, price uint
			var skuPrice model.Goods

			skuPrice.CostPrice = uint(utils.Yuan2Fen(detail.ProductInfo.ProductSaleInfo.ConsignPrice))

			if skuPrice.CostPrice == 0 || detail.ProductInfo.ProductSaleInfo.JxhyPrice > detail.ProductInfo.ProductSaleInfo.ConsignPrice {
				skuPrice.CostPrice = uint(utils.Yuan2Fen(detail.ProductInfo.ProductSaleInfo.JxhyPrice))

			}

			skuPrice.Source = common.ALJX_SOURCE
			//log.Log().Info(" skuPrice.CostPrice", zap.Any("info", detail.Result.Result[0].ProductInfo.SaleInfo.Retailprice))
			//log.Log().Info(" skuPrice.CostPrice1", zap.Any("info", skuPrice.CostPrice))

			err, costPrice, price, _, _, _ = goods.GetShopPricingPrice(skuPrice, alishop)

			log.Log().Info(" goods.GetShopPricingPrice-1", zap.Any("costPrice", costPrice), zap.Any("price", price))

			if costPrice == 0 || price == 0 {
				sku.IsDisplay = 0

			}
			sku.Title = "默认"
			sku.Options = options

			if datsetting.UpdateInfo.CurrentPrice == 1 {
				sku.Price = price
			}

			if datsetting.UpdateInfo.CostPrice == 1 {
				sku.CostPrice = costPrice
			}

			//sku.CostPrice = costPrice
			//sku.Price = price
			//
			//fmt.Println(originPrice, activityPrice, guidePrice)
			//if sku.OriginPrice == 0 {
			//	sku.OriginPrice = originPrice
			//}
			//if sku.GuidePrice == 0 {
			//	sku.GuidePrice = guidePrice
			//}
			//if sku.ActivityPrice == 0 {
			//	sku.ActivityPrice = activityPrice
			//}

			sku.Stock = int(detail.ProductInfo.ProductSaleInfo.AmountOnSale)
			sku.IsDisplay = updateGoods.IsDisplay
			sku.OriginalSkuID = int(detail.ProductInfo.ProductID)
			if sku.GuidePrice < sku.Price {
				sku.GuidePrice = sku.Price
			}
			if sku.OriginPrice < sku.Price {
				sku.OriginPrice = sku.Price
			}
			if sku.ActivityPrice < sku.Price {
				sku.ActivityPrice = sku.Price
			}

			productID := strconv.Itoa(int(detail.ProductInfo.ProductID))
			sku.SpecId = productID
			if strings.Contains(sku.Title, "客服") || strings.Contains(sku.Title, "代发") || strings.Contains(sku.Title, "批量") || strings.Contains(sku.Title, "厂家") || strings.Contains(sku.Title, "咨询") {
				sku.IsDisplay = 0

			}
			if sku.GuidePrice > 0 {
				sku.ProfitRate = utils.ExecProfitRate(sku.GuidePrice, sku.Price)
			} else {
				sku.ProfitRate = 0
			}
			minProfitRate = sku.ProfitRate

			skuList = append(skuList, sku)
			updateGoods.SingleOption = 1
		}
		//updateGoods.Title = detail.Result.Result[0].ProductInfo.Subject

		updateGoods.MinBuyQty = 1

		updateGoods.Skus = skuList
		if detail.ProductInfo.Status == "published" {
			updateGoods.IsDisplay = 1
		}

		itemSku := goods.GetMapA(updateGoods.Skus)
		//log.Log().Info(" goods.GetPriceA", zap.Any("info", updateGoods.Skus))

		updateGoods.MaxPrice, updateGoods.MinPrice = goods.GetPriceA(updateGoods.Skus)
		updateGoods.CostPrice = itemSku.CostPrice

		//if item.GuidePrice == 0 {
		//	updateGoods.GuidePrice = itemSku.GuidePrice
		//}
		//if item.OriginPrice == 0 {
		//	updateGoods.OriginPrice = itemSku.OriginPrice
		//}
		//if item.ActivityPrice == 0 {
		//	updateGoods.ActivityPrice = itemSku.ActivityPrice
		//}

		updateGoods.Price = itemSku.Price
		if updateGoods.Price == 0 {
			log.Log().Info("updateGoods.Price", zap.Any("info", itemSku))
			UndercarriageProduct(item.ID)
			continue
		}
		//updateGoods.ProfitRate = pservice.Decimal((float64(updateGoods.GuidePrice) - float64(updateGoods.Price)) / float64(updateGoods.GuidePrice) * 100)
		if len(updateGoods.Skus) > 0 {
			updateGoods.ProfitRate = minProfitRate
		}
		updateGoods.Stock = uint(itemSku.Stock)
		if updateGoods.Stock == 0 {
			updateGoods.IsDisplay = 0
		}
		//if detail.Result.Result[0].ProductInfo.MainImage != "" {
		//	updateGoods.ImageUrl = detail.Result.Result[0].ProductInfo.MainImage
		//}

		//var jsonImage []string

		//if len(detail.Result.Result[0].ProductInfo.JsonImage) > 0 {
		//	err = json.Unmarshal([]byte(detail.Result.Result[0].ProductInfo.JsonImage), &jsonImage)
		//} else {
		//	jsonImage = detail.Result.Result[0].ProductInfo.Image.Images
		//}
		//
		//var gallery pmodel.Gallery
		//for _, gitem := range jsonImage {
		//	var galleryItem pmodel.GalleryItem
		//	galleryItem.Src = "https://cbu01.alicdn.com/" + gitem
		//	galleryItem.Type = 1
		//	gallery = append(gallery, galleryItem)
		//}
		//
		//updateGoods.Gallery = gallery

		//if len(updateGoods.Gallery) <= 0 {
		//	var Gallery pmodel.Gallery
		//	Gallery = append(Gallery, pmodel.GalleryItem{Type: 1, Src: updateGoods.ImageUrl})
		//	updateGoods.Gallery = Gallery
		//}

		//if updateGoods.ImageUrl == "" {
		//	if len(updateGoods.Gallery) > 0 {
		//		updateGoods.ImageUrl = updateGoods.Gallery[0].Src
		//	}
		//
		//}

		updateGoods.ShopName = detail.ProductInfo.SupplierLoginId
		err = pservice.UpdateProduct(updateGoods)
		if err != nil {
			log.Log().Error("更新商品错误 update", zap.Any("err", err))
			return err
		}
		UpdateRecord(updateGoods, item)
	}

	return
}

func ProductAddRelation(gatherSupplyID int) (err error) {
	if datsetting.BaseInfo.AppKey == "" {
		InitSetting(gatherSupplyID)
		AddShop()
	}

	if datsetting.BaseInfo.AppKey == "" {
		return
	}

	var Product []pservice.ProductForUpdate

	err = source.DB().Where("is_display=1 and status_lock=0 and source =?    and gather_supply_id=?", common.SUPPLY_ALJX, gatherSupplyID).Order("updated_at asc ").Limit(10000).Find(&Product).Error
	if err != nil {
		return
	}

	for _, item := range Product {
		localID := strconv.Itoa(int(item.ID))
		sourceGoodsID := strconv.Itoa(int(item.SourceGoodsID))

		AddRelation(localID, sourceGoodsID)
		log.Log().Info("info", zap.Any("info", item.ID))

	}

	return
}

func UpdateImg(gatherSupplyID int) (err error) {
	if datsetting.BaseInfo.AppKey == "" {
		InitSetting(gatherSupplyID)
		AddShop()
	}

	if datsetting.BaseInfo.AppKey == "" {
		return
	}

	var Product []pservice.ProductForUpdate

	err = source.DB().Preload("Skus").Where("is_display=1 and profit_rate <=0 and status_lock=0 and source =?    and gather_supply_id=?", common.SUPPLY_ALJX, gatherSupplyID).Order("updated_at asc ").Limit(5000).Find(&Product).Error
	if err != nil {
		return
	}

	for _, item := range Product {
		localID := strconv.Itoa(int(item.ID))
		sourceGoodsID := strconv.Itoa(int(item.SourceGoodsID))

		AddRelation(localID, sourceGoodsID)
		log.Log().Info("info", zap.Any("info", item.ID))

		var updateGoods pservice.ProductForUpdate
		updateGoods = item

		var detail model3.ALJXNewDetail
		//var ids []uint
		//ids = append(ids, item.SourceGoodsID)
		err, detail = GetProductInfo(item.SourceGoodsID)
		if err != nil {
			continue
		}
		if detail.Success != true {
			//log.Log().Info("detail.Result.Success != true", zap.Any("info", item.SourceGoodsID))

			UndercarriageProduct(item.ID)
			continue
		}
		//if len(detail.ProductInfo) == 0 {
		//	log.Log().Info("len(detail.Result.Result) == 0", zap.Any("info", ids))
		//
		//	UndercarriageProduct(item.ID)
		//	continue
		//}
		if detail.ProductInfo.Status != "published" {
			log.Log().Info("detail.Result.Result[0].ProductInfo.Status", zap.Any("info", item.SourceGoodsID))

			UndercarriageProduct(item.ID)
			continue
		}

		//if len(detail.ProductInfo.ProductExtendInfos) > 0 {
		//	var isjx bool = false
		//	for _, jxitem := range detail.ProductInfo.ProductExtendInfos {
		//		//if jxitem.Key == "isPftOffer" && jxitem.Value == "true" {
		//		//	isjx = true
		//		//}
		//		if jxitem.Key == "isJxhyOffer" && jxitem.Value == "true" {
		//			isjx = true
		//		}
		//	}
		//
		//	if isjx == false {
		//		UndercarriageProduct(item.ID)
		//		continue
		//	}
		//	//fmt.Println(isjx)
		//}

		var alishop model3.AliShop
		alishop.Name = strings.TrimSpace(detail.ProductInfo.SupplierLoginId)
		alishop.IsOpen = "0"
		alishop.Strategy = "{\"origin\":\"100\",\"marketing\":\"100\",\"guide\":\"100\",\"price\":\"100\",\"cost\":\"100\"}"
		err = source.DB().Where("name=?", detail.ProductInfo.SupplierLoginId).FirstOrCreate(&alishop).Error
		//if err!=nil{
		//	continue
		//}

		aliSkus := detail.ProductInfo.ProductSkuInfos
		//err = json.Unmarshal([]byte(), &aliSkus)
		var sku = pservice.Sku{}
		var skuList []pservice.Sku
		var minProfitRate float64

		// 收集所有规格属性的可能值
		specMap := make(map[string][]string)
		for _, skuitem := range aliSkus {
			for _, attribute := range skuitem.Attributes {
				if _, exists := specMap[attribute.AttributeName]; !exists {
					specMap[attribute.AttributeName] = []string{}
				}
				// 检查是否已存在该值
				valueExists := false
				for _, v := range specMap[attribute.AttributeName] {
					if v == attribute.AttributeValue {
						valueExists = true
						break
					}
				}
				if !valueExists {
					specMap[attribute.AttributeName] = append(specMap[attribute.AttributeName], attribute.AttributeValue)
				}
			}
		}

		// 计算理论上应该存在的规格数量
		expectedCount := 1
		for _, values := range specMap {
			expectedCount *= len(values)
		}

		for sk, skuitem := range aliSkus {
			if skuitem.JxhyPrice == 0 && skuitem.ConsignPrice == 0 {
				continue
			}
			sku = pservice.Sku{}
			var options pmodel.Options
			var option pmodel.Option
			var skuTitle string
			for _, attribute := range skuitem.Attributes {
				option.SpecName = attribute.AttributeName
				option.SpecItemName = attribute.AttributeValue
				options = append(options, option)
				skuTitle += attribute.AttributeValue
			}

			for _, localskuitem := range item.Skus {
				if localskuitem.OriginalSkuID == int(skuitem.SkuId) {
					sku = localskuitem
					break
				}
			}

			//if len(goods.Skus) > 0 {
			//	sku.ID = goods.Skus[0].ID
			//}
			var costPrice, price, originPrice, activityPrice, guidePrice uint
			var skuPrice model.Goods

			skuPrice.CostPrice = uint(math.Floor(skuitem.ConsignPrice * 100)) //成本

			if skuitem.ConsignPrice < skuitem.JxhyPrice {
				skuPrice.CostPrice = uint(math.Floor(skuitem.JxhyPrice * 100)) //成本

			}

			skuPrice.Source = common.ALJX_SOURCE

			err, costPrice, price, originPrice, activityPrice, guidePrice = goods.GetShopPricingPrice(skuPrice, alishop)

			//if costPrice == 0 || price == 0 {
			//	continue
			//}

			if datsetting.UpdateInfo.CurrentPrice == 1 {
				sku.Price = price
			}

			if datsetting.UpdateInfo.CostPrice == 1 {
				sku.CostPrice = costPrice
			}

			if sku.OriginPrice == 0 {
				sku.OriginPrice = originPrice
			}
			if sku.GuidePrice == 0 {
				sku.GuidePrice = guidePrice
			}
			if sku.ActivityPrice == 0 {
				sku.ActivityPrice = activityPrice
			}

			sku.Title = skuTitle
			sku.Options = options
			sku.Stock = skuitem.AmountOnSale
			sku.IsDisplay = updateGoods.IsDisplay
			sku.OriginalSkuID = int(skuitem.SkuId)
			sku.SpecId = skuitem.SpecId

			if sku.GuidePrice < sku.Price {
				sku.GuidePrice = sku.Price
			}
			if sku.OriginPrice < sku.Price {
				sku.OriginPrice = sku.Price
			}
			if sku.ActivityPrice < sku.Price {
				sku.ActivityPrice = sku.Price
			}

			if len(skuitem.Attributes) > 0 {
				if skuitem.Attributes[0].SkuImageUrl != "" {
					sku.ImageUrl = "https://cbu01.alicdn.com/" + skuitem.Attributes[0].SkuImageUrl
				} else {
					sku.ImageUrl = ""
				}
			}
			if sku.GuidePrice > 0 {
				sku.ProfitRate = utils.ExecProfitRate(sku.GuidePrice, sku.Price)
			} else {
				sku.ProfitRate = 0
			}
			if sk == 0 {
				minProfitRate = sku.ProfitRate
			}
			if sku.ProfitRate <= minProfitRate {
				minProfitRate = sku.ProfitRate
			}
			if strings.Contains(sku.Title, "客服") || strings.Contains(sku.Title, "代发") || strings.Contains(sku.Title, "批量") || strings.Contains(sku.Title, "厂家") || strings.Contains(sku.Title, "咨询") {
				sku.IsDisplay = 0

			} else {
				skuList = append(skuList, sku)

			}

		}

		// 如果实际SKU数量小于期望数量，补充缺失的规格
		if len(skuList) < expectedCount {
			// 生成所有可能的规格组合
			existingCombos := make(map[string]bool)
			for _, s := range skuList {
				var combo string
				for _, opt := range s.Options {
					combo += opt.SpecName + ":" + opt.SpecItemName + ";"
				}
				existingCombos[combo] = true
			}

			// 使用第一个SKU作为模板
			templateSku := skuList[0]

			// 生成并检查所有可能的组合
			var specNames []string
			var specValues [][]string
			for name, values := range specMap {
				specNames = append(specNames, name)
				specValues = append(specValues, values)
			}

			var generateCombinations func(int, []string)
			currentCombo := make([]string, len(specNames))
			generateCombinations = func(pos int, current []string) {
				if pos == len(specNames) {
					var combo string
					var options pmodel.Options
					for i, name := range specNames {
						combo += name + ":" + current[i] + ";"
						options = append(options, pmodel.Option{SpecName: name, SpecItemName: current[i]})
					}

					// 如果这个组合不存在，添加它
					if !existingCombos[combo] {
						newSku := templateSku
						newSku.Options = options
						newSku.Title = strings.Join(current, "-")
						skuList = append(skuList, newSku)
					}
					return
				}

				for _, val := range specValues[pos] {
					currentCombo[pos] = val
					generateCombinations(pos+1, currentCombo)
				}
			}

			generateCombinations(0, currentCombo)
		}

		if len(skuList) == 0 {
			for _, localskuitem := range item.Skus {
				if localskuitem.OriginalSkuID == int(detail.ProductInfo.ProductID) {
					sku = localskuitem
					break
				}
			}
			var options pmodel.Options
			var option pmodel.Option
			option.SpecName = "规格"
			option.SpecItemName = "默认"
			options = append(options, option)

			var costPrice, price uint
			var skuPrice model.Goods

			skuPrice.CostPrice = uint(utils.Yuan2Fen(detail.ProductInfo.ProductSaleInfo.ConsignPrice))

			if skuPrice.CostPrice == 0 || detail.ProductInfo.ProductSaleInfo.JxhyPrice > detail.ProductInfo.ProductSaleInfo.ConsignPrice {
				skuPrice.CostPrice = uint(utils.Yuan2Fen(detail.ProductInfo.ProductSaleInfo.JxhyPrice))

			}

			skuPrice.Source = common.ALJX_SOURCE
			//log.Log().Info(" skuPrice.CostPrice", zap.Any("info", detail.Result.Result[0].ProductInfo.SaleInfo.Retailprice))
			//log.Log().Info(" skuPrice.CostPrice1", zap.Any("info", skuPrice.CostPrice))

			err, costPrice, price, _, _, _ = goods.GetShopPricingPrice(skuPrice, alishop)

			log.Log().Info(" goods.GetShopPricingPrice-1", zap.Any("costPrice", costPrice), zap.Any("price", price))

			if costPrice == 0 || price == 0 {
				sku.IsDisplay = 0

			}
			sku.Title = "默认"
			sku.Options = options

			if datsetting.UpdateInfo.CurrentPrice == 1 {
				sku.Price = price
			}

			if datsetting.UpdateInfo.CostPrice == 1 {
				sku.CostPrice = costPrice
			}

			//sku.CostPrice = costPrice
			//sku.Price = price
			//
			//fmt.Println(originPrice, activityPrice, guidePrice)
			//if sku.OriginPrice == 0 {
			//	sku.OriginPrice = originPrice
			//}
			//if sku.GuidePrice == 0 {
			//	sku.GuidePrice = guidePrice
			//}
			//if sku.ActivityPrice == 0 {
			//	sku.ActivityPrice = activityPrice
			//}

			sku.Stock = int(detail.ProductInfo.ProductSaleInfo.AmountOnSale)
			sku.IsDisplay = updateGoods.IsDisplay
			sku.OriginalSkuID = int(detail.ProductInfo.ProductID)
			if sku.GuidePrice < sku.Price {
				sku.GuidePrice = sku.Price
			}
			if sku.OriginPrice < sku.Price {
				sku.OriginPrice = sku.Price
			}
			if sku.ActivityPrice < sku.Price {
				sku.ActivityPrice = sku.Price
			}

			productID := strconv.Itoa(int(detail.ProductInfo.ProductID))
			sku.SpecId = productID
			if strings.Contains(sku.Title, "客服") || strings.Contains(sku.Title, "代发") || strings.Contains(sku.Title, "批量") || strings.Contains(sku.Title, "厂家") || strings.Contains(sku.Title, "咨询") {
				sku.IsDisplay = 0

			}
			if sku.GuidePrice > 0 {
				sku.ProfitRate = utils.ExecProfitRate(sku.GuidePrice, sku.Price)
			} else {
				sku.ProfitRate = 0
			}
			minProfitRate = sku.ProfitRate

			skuList = append(skuList, sku)
			updateGoods.SingleOption = 1
		}
		//updateGoods.Title = detail.Result.Result[0].ProductInfo.Subject

		updateGoods.MinBuyQty = 1

		updateGoods.Skus = skuList
		if detail.ProductInfo.Status == "published" {
			updateGoods.IsDisplay = 1
		}

		itemSku := goods.GetMapA(updateGoods.Skus)
		//log.Log().Info(" goods.GetPriceA", zap.Any("info", updateGoods.Skus))

		updateGoods.MaxPrice, updateGoods.MinPrice = goods.GetPriceA(updateGoods.Skus)
		updateGoods.CostPrice = itemSku.CostPrice

		//if item.GuidePrice == 0 {
		//	updateGoods.GuidePrice = itemSku.GuidePrice
		//}
		//if item.OriginPrice == 0 {
		//	updateGoods.OriginPrice = itemSku.OriginPrice
		//}
		//if item.ActivityPrice == 0 {
		//	updateGoods.ActivityPrice = itemSku.ActivityPrice
		//}

		updateGoods.Price = itemSku.Price
		if updateGoods.Price == 0 {
			log.Log().Info("updateGoods.Price", zap.Any("info", itemSku))
			UndercarriageProduct(item.ID)
			continue
		}
		//updateGoods.ProfitRate = pservice.Decimal((float64(updateGoods.GuidePrice) - float64(updateGoods.Price)) / float64(updateGoods.GuidePrice) * 100)
		if len(updateGoods.Skus) > 0 {
			updateGoods.ProfitRate = minProfitRate
		}
		updateGoods.Stock = uint(itemSku.Stock)
		//if detail.Result.Result[0].ProductInfo.MainImage != "" {
		//	updateGoods.ImageUrl = detail.Result.Result[0].ProductInfo.MainImage
		//}

		//var jsonImage []string

		//if len(detail.Result.Result[0].ProductInfo.JsonImage) > 0 {
		//	err = json.Unmarshal([]byte(detail.Result.Result[0].ProductInfo.JsonImage), &jsonImage)
		//} else {
		//	jsonImage = detail.Result.Result[0].ProductInfo.Image.Images
		//}
		//
		//var gallery pmodel.Gallery
		//for _, gitem := range jsonImage {
		//	var galleryItem pmodel.GalleryItem
		//	galleryItem.Src = "https://cbu01.alicdn.com/" + gitem
		//	galleryItem.Type = 1
		//	gallery = append(gallery, galleryItem)
		//}
		//
		//updateGoods.Gallery = gallery

		//if len(updateGoods.Gallery) <= 0 {
		//	var Gallery pmodel.Gallery
		//	Gallery = append(Gallery, pmodel.GalleryItem{Type: 1, Src: updateGoods.ImageUrl})
		//	updateGoods.Gallery = Gallery
		//}

		//if updateGoods.ImageUrl == "" {
		//	if len(updateGoods.Gallery) > 0 {
		//		updateGoods.ImageUrl = updateGoods.Gallery[0].Src
		//	}
		//
		//}

		updateGoods.ShopName = detail.ProductInfo.SupplierLoginId
		err = pservice.UpdateProduct(updateGoods)
		if err != nil {
			log.Log().Error("更新商品错误 update", zap.Any("err", err))
			return err
		}

	}
	return
}

func UndercarriageProduct(productID uint) (err error) {
	var productMessageType mq.ProductMessageType //队列消息类型
	productMessageType = mq.Undercarriage
	err = mq.PublishMessage(productID, productMessageType, 0)

	var colum = make(map[string]interface{})
	colum["is_display"] = 0

	colum["updated_at"] = time.Now().Format("2006-01-02 15:04:05")

	err = source.DB().Table("products").Where("id=? and source=?", productID, common.ALJX_SOURCE).UpdateColumns(&colum).Error

	return
}
func SaleProduct(productID uint) (err error) {
	var productMessageType mq.ProductMessageType //队列消息类型
	productMessageType = mq.OnSale
	err = mq.PublishMessage(productID, productMessageType, 0)

	var colum = make(map[string]interface{})
	colum["is_display"] = 1

	colum["updated_at"] = time.Now().Format("2006-01-02 15:04:05")

	err = source.DB().Table("products").Where("id=? and source=?", productID, common.ALJX_SOURCE).UpdateColumns(&colum).Error

	return
}

type productArr struct {
	ProductId int `json:"product_id"`
}

func ProductSkuUpdate(gatherSupplyID int, productID []int) (err error) {
	if datsetting.BaseInfo.AppKey == "" {
		InitSetting(gatherSupplyID)
	}
	log.Log().Info("setting", zap.Any("set:", datsetting))

	if datsetting.BaseInfo.AppKey == "" {
		return
	}

	var productArrA []productArr
	jjj := "[{\"product_id\":506026},{\"product_id\":506047},{\"product_id\":506048},{\"product_id\":506555},{\"product_id\":506556},{\"product_id\":506557},{\"product_id\":506558},{\"product_id\":506559},{\"product_id\":506560},{\"product_id\":506561},{\"product_id\":506562},{\"product_id\":506563},{\"product_id\":506564},{\"product_id\":506568},{\"product_id\":506573},{\"product_id\":506579},{\"product_id\":506580},{\"product_id\":506581},{\"product_id\":506582},{\"product_id\":506583},{\"product_id\":506584},{\"product_id\":506587},{\"product_id\":506588},{\"product_id\":507313},{\"product_id\":507670},{\"product_id\":507690},{\"product_id\":507708},{\"product_id\":509890},{\"product_id\":509900},{\"product_id\":510238},{\"product_id\":510257},{\"product_id\":510279},{\"product_id\":510280},{\"product_id\":510286},{\"product_id\":510290},{\"product_id\":510293},{\"product_id\":510296},{\"product_id\":510297},{\"product_id\":511263},{\"product_id\":511268},{\"product_id\":511271},{\"product_id\":511272},{\"product_id\":511273},{\"product_id\":511353},{\"product_id\":511359},{\"product_id\":511361},{\"product_id\":511364},{\"product_id\":511365},{\"product_id\":511367},{\"product_id\":511368},{\"product_id\":511369},{\"product_id\":511370},{\"product_id\":511371},{\"product_id\":511372},{\"product_id\":511373},{\"product_id\":511374},{\"product_id\":511376},{\"product_id\":511377},{\"product_id\":511378},{\"product_id\":511382},{\"product_id\":511572},{\"product_id\":511581},{\"product_id\":511753},{\"product_id\":511769},{\"product_id\":511786},{\"product_id\":511805},{\"product_id\":511808},{\"product_id\":511809},{\"product_id\":511818},{\"product_id\":511995},{\"product_id\":512009},{\"product_id\":512022},{\"product_id\":512281},{\"product_id\":512662},{\"product_id\":512665},{\"product_id\":512670},{\"product_id\":512672},{\"product_id\":512675},{\"product_id\":513089},{\"product_id\":513095},{\"product_id\":513330},{\"product_id\":513331},{\"product_id\":513350},{\"product_id\":513356},{\"product_id\":513823},{\"product_id\":513840},{\"product_id\":513845},{\"product_id\":513846},{\"product_id\":513910},{\"product_id\":513919},{\"product_id\":513922},{\"product_id\":513932},{\"product_id\":513940},{\"product_id\":513945},{\"product_id\":514232},{\"product_id\":514234},{\"product_id\":514235},{\"product_id\":514243},{\"product_id\":514247},{\"product_id\":514248},{\"product_id\":514249},{\"product_id\":514250},{\"product_id\":514251},{\"product_id\":514253},{\"product_id\":514458},{\"product_id\":514907},{\"product_id\":514916},{\"product_id\":514977},{\"product_id\":515069},{\"product_id\":515080},{\"product_id\":515081},{\"product_id\":515086},{\"product_id\":515267},{\"product_id\":515271},{\"product_id\":515272},{\"product_id\":515289},{\"product_id\":516244},{\"product_id\":516279},{\"product_id\":516691},{\"product_id\":516693},{\"product_id\":516695},{\"product_id\":516706},{\"product_id\":516708},{\"product_id\":516709},{\"product_id\":516892},{\"product_id\":516896},{\"product_id\":516897},{\"product_id\":517242},{\"product_id\":517251},{\"product_id\":517600},{\"product_id\":517602},{\"product_id\":517793},{\"product_id\":518067},{\"product_id\":518100},{\"product_id\":518105},{\"product_id\":518109},{\"product_id\":518112},{\"product_id\":518118},{\"product_id\":518119},{\"product_id\":518125},{\"product_id\":518136},{\"product_id\":518235},{\"product_id\":518482},{\"product_id\":518686},{\"product_id\":518694},{\"product_id\":518697},{\"product_id\":519038},{\"product_id\":519097},{\"product_id\":519104},{\"product_id\":519117},{\"product_id\":519118},{\"product_id\":519719},{\"product_id\":519722},{\"product_id\":519723},{\"product_id\":519732},{\"product_id\":520103},{\"product_id\":520104},{\"product_id\":520255},{\"product_id\":520262},{\"product_id\":520276}]"

	json.Unmarshal([]byte(jjj), &productArrA)

	var idarr []int

	for _, item := range productArrA {
		idarr = append(idarr, item.ProductId)
	}

	var Product []pservice.ProductForUpdate

	err = source.DB().Where("id in (?)", idarr).Preload("Skus").Order("updated_at asc ").Find(&Product).Error
	if err != nil {
		return
	}

	for _, item := range Product {
		//SaleProduct(item.ID)
		//continue

		var updateGoods pservice.ProductForUpdate
		updateGoods = item

		var detail model3.ALJXDetail
		var ids []uint
		ids = append(ids, item.SourceGoodsID)
		//ids = append(ids, item.SourceGoodsID)
		err, detail = GetDetail(ids)
		if err != nil {
			continue
		}
		if detail.Result.Success != true {
			UndercarriageProduct(item.ID)
			continue
		}
		if len(detail.Result.Result) == 0 {
			UndercarriageProduct(item.ID)
			continue
		}
		updateGoods.Stock = uint(detail.Result.Result[0].ProductInfo.SaleInfo.AmountOnSale)

		//if "published" == detail.Result.Result[0].ProductInfo.Status {
		//	updateGoods.IsDisplay = 1
		//
		//} else {
		//	UndercarriageProduct(item.ID)
		//	continue
		//}

		aliSkus := detail.Result.Result[0].ProductInfo.ProductSkuInfos
		//err = json.Unmarshal([]byte(), &aliSkus)
		var sku = pservice.Sku{}
		var skuList []pservice.Sku

		for _, skuitem := range aliSkus {
			var options pmodel.Options
			var option pmodel.Option
			var skuTitle string
			for _, attribute := range skuitem.Attributes {
				option.SpecName = attribute.AttributeName
				option.SpecItemName = attribute.AttributeValue
				options = append(options, option)
				skuTitle += attribute.AttributeValue
			}

			for _, localskuitem := range item.Skus {
				if localskuitem.OriginalSkuID == int(skuitem.SkuId) {
					sku = localskuitem
				}
			}

			var costPrice, salePrice, originPrice, activityPrice, guidePrice uint
			var skuPrice model.Goods

			skuPrice.AgreementPrice = uint(math.Floor(skuitem.ConsignPrice * 100))
			skuPrice.GuidePrice = uint(math.Floor(skuitem.ConsignPrice * 100))
			skuPrice.ActivityPrice = uint(math.Floor(skuitem.ConsignPrice * 100))
			skuPrice.Source = common.ALJX_SOURCE

			err, costPrice, salePrice, originPrice, activityPrice, guidePrice = goods.GetPricingPrice(skuPrice, "gatherSupply"+strconv.Itoa(int(gatherSupplyID)))
			if datsetting.UpdateInfo.CurrentPrice == 1 {
				sku.CostPrice = costPrice
				sku.Price = salePrice
				sku.OriginPrice = originPrice
				sku.GuidePrice = guidePrice
				sku.ActivityPrice = activityPrice
			}

			if datsetting.UpdateInfo.BaseInfo == 1 {
				sku.Title = skuTitle
				sku.Options = options
				sku.Stock = skuitem.AmountOnSale
				sku.IsDisplay = updateGoods.IsDisplay
				sku.OriginalSkuID = int(skuitem.SkuId)
			}
			sku.IsDisplay = 1
			sku.SpecId = skuitem.SpecId
			if len(skuitem.Attributes) > 0 {
				if skuitem.Attributes[0].SkuImageUrl != "" {
					sku.ImageUrl = "https://cbu01.alicdn.com/" + skuitem.Attributes[0].SkuImageUrl
				} else {
					sku.ImageUrl = ""
				}
			}
			skuList = append(skuList, sku)

		}

		if len(skuList) == 0 {
			continue
		}
		//if datsetting.UpdateInfo.BaseInfo == 1 {
		//	updateGoods.Title = detail.Result.Result[0].ProductInfo.Subject
		//}

		//updateGoods.MinBuyQty = 1
		//
		updateGoods.Skus = skuList

		//itemSku := goods.GetMapA(updateGoods.Skus)
		//if datsetting.UpdateInfo.CurrentPrice == 1 {
		//	updateGoods.MaxPrice, updateGoods.MinPrice = goods.GetPriceA(updateGoods.Skus)
		//	updateGoods.CostPrice = itemSku.CostPrice
		//	updateGoods.GuidePrice = itemSku.GuidePrice
		//	updateGoods.OriginPrice = itemSku.OriginPrice
		//	updateGoods.ActivityPrice = itemSku.ActivityPrice
		//	updateGoods.Price = itemSku.Price
		//	if updateGoods.Price == 0 {
		//		UndercarriageProduct(item.ID)
		//		continue
		//	}
		//	updateGoods.ProfitRate = pservice.Decimal((float64(updateGoods.GuidePrice) - float64(updateGoods.Price)) / float64(updateGoods.GuidePrice) * 100)
		//
		//}

		//updateGoods.Stock = uint(itemSku.Stock)

		err = pservice.UpdateProduct(updateGoods)
		if err != nil {
			log.Log().Error("更新商品错误 update", zap.Any("err", err))
			return err
		}

	}

	return
}
