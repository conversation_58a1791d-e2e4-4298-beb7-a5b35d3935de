package cron

import (
	"testing"
)

func TestGetDetail(t *testing.T) {
	InitSetting(47)
	//
	//659453737156
	//675403133055
	//560445903500
	//624900144926
	//676098235752
	//618106953638
	//674700144949

	//var item []uint
	//item = append(item, 668114576754)
	GetProductInfo(677254062142)

}

func TestProductUpdate(t *testing.T) {

	InitSetting(47)
	ProductUpdate(47)

}

func TestProductAddRelation(t *testing.T) {

	InitSetting(47)
	ProductAddRelation(47)
}

func TestGetRelation(t *testing.T) {
	InitSetting(47)
	GetRelation("513355", "688643255578")

}

func TestAddRelation(t *testing.T) {
	InitSetting(47)
	AddRelation("513355", "688643255578")

}
