package service

import (
	"ali-selected/model"
	"ali-selected/request"
	"yz-go/source"
)

func GetChangeRecord(info request.SearchShop) (err error, list []model.AliProductUpdateRecord, total int64) {

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(&model.AliProductUpdateRecord{})

	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&list).Error
	return

}

//
//import (
//	"ali-open/model"
//	"ali-open/request"
//	"crypto/hmac"
//	"crypto/sha1"
//	"encoding/hex"
//	"encoding/json"
//	"errors"
//	"fmt"
//	"go.uber.org/zap"
//	url2 "net/url"
//	"sort"
//	"strings"
//	"yz-go/component/log"
//	"yz-go/source"
//	"yz-go/utils"
//)
//
//func (ali *Alibb) Init() {
//
//	err, sysSetting := GetSetting(ali.ShopID)
//	if err != nil {
//		return
//	}
//	var setting model.Setting
//	err = json.Unmarshal([]byte(sysSetting.Value), &setting)
//	if err != nil {
//		return
//	}
//	ali.Key = setting.Key
//	ali.Secret = setting.Secret
//	ali.Token = setting.Token
//	ali.Domain = setting.Domain
//
//	return
//}
//
//func BindSupplier(bind request.BindSupplier) (err error) {
//
//	var comPanyName model.CompanyName
//	comPanyName.Uid = bind.Uid
//	err = source.DB().Where("member_id=?", bind.AliID).Updates(&comPanyName).Error
//
//	return
//
//}
//
//func CancelBindProduct(productID, skuID int64) (err error) {
//	source.DB().Unscoped().Delete(&model.AliProduct{}, "product_id=?", productID)
//	return
//}
//func BindProduct(param request.BindProduct) (err error) {
//
//	var product, whereProduct model.AliProduct
//	product.ProductID = param.ProductID
//	product.AliProductID = param.AliProductID
//	product.SkuID = param.SkuID
//	product.AliSkuID = param.AliSkuID
//	product.ShopID = param.ShopData.ShopID
//	product.AutoPay = param.AutoPay
//	source.DB().Where("product_id=? and sku_id=? ", product.ProductID, product.SkuID).First(&whereProduct)
//	if whereProduct.ID > 0 {
//		err = source.DB().Where("product_id=? and sku_id=? ", product.ProductID, product.SkuID).Updates(&product).Error
//	} else {
//		err = source.DB().Create(&product).Error
//	}
//
//	return
//
//}
//
//func GetSupplierList(id string) (err error, list []model.CompanyName) {
//
//	//err = source.DB().Where("shop_id=?", id).Order("id desc").Find(&list).Error
//	err = source.DB().Where("shop_id=?", id).Order("id desc").Find(&list).Error
//
//	return
//}
//func DeleteBindProduct(param request.BindProduct) (err error) {
//	var product model.AliProduct
//	err = source.DB().Where("product_id=?  and sku_id=?", param.ProductID, param.SkuID).Delete(&product).Error
//	return
//
//}
//
//func CreateDomain(domain request.SupplierDomain) (err error) {
//	var comPanyName model.CompanyName
//	comPanyName.Domain = domain.Domain
//	comPanyName.ShopID = domain.ShopID
//	err = source.DB().Create(&comPanyName).Error
//	return
//
//}
//
//func DeleteDomain(id request.ID) (err error) {
//	var comPanyName model.CompanyName
//	err = source.DB().Where("id=?", id.ID).Delete(&comPanyName).Error
//	return
//
//}
//
//func (ali *Alibb) GetProduct(id string) (err error, product model.Product) {
//
//	url := "http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.simple.get/" + ali.Key
//
//	reqData := url2.Values{}
//	reqData.Add("access_token", ali.Token)
//	reqData.Add("productID", id) //************
//	reqData.Add("webSite", "1688")
//	reqData.Add("_aop_signature", Sign(url, ali.Secret, reqData))
//
//	//log.Log().Info("aliselectproductrequestpost", zap.Any("err", reqData))
//
//	var resData []byte
//	err, resData = utils.PostForm(url, reqData, nil)
//
//	//var product model.Product
//
//	json.Unmarshal(resData, &product)
//	//log.Log().Info("aliselectproduct", zap.Any("err", string(resData)))
//
//	fmt.Println("product", string(resData))
//	return
//}
//
//func (ali *Alibb) GetShop(domain string) (err error) {
//
//	var companyList model.CompanyName
//	url := "https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.member.getRelationUserInfo/" + ali.Key
//	reqData := url2.Values{}
//	reqData.Add("access_token", ali.Token)
//	reqData.Add("domain", domain)
//	reqData.Add("_aop_signature", Sign(url, ali.Secret, reqData))
//	var resData []byte
//	err, resData = utils.PostForm(url, reqData, nil)
//
//	err = json.Unmarshal(resData, &companyList)
//	if err != nil {
//		return
//	}
//	if companyList.CompanyName == "" {
//		var errorType ErrorType
//		err = json.Unmarshal(resData, &errorType)
//		err = errors.New(errorType.ErrorInfo)
//		return
//	}
//	if companyList.Success == true {
//		var companyName = make(map[string]interface{})
//		companyName["company_name"] = companyList.CompanyName
//		companyName["login_id"] = companyList.LoginId
//		companyName["member_id"] = companyList.MemberId
//		err = source.DB().Model(model.CompanyName{}).Where("domain=?", domain).Updates(companyName).Error
//
//		return
//	}
//
//	return
//
//}
//
//type ErrorType struct {
//	ErrorCode string `json:"errorCode"`
//	ErrorInfo string `json:"errorInfo"`
//	Success   bool   `json:"success"`
//}
//
////字符串转为16进制
//
//func HmacSha1(data string, secret string) string {
//	h := hmac.New(sha1.New, []byte(secret))
//	h.Write([]byte(data))
//	return hex.EncodeToString(h.Sum(nil))
//}
//
//type MapEntryHandler func(string, string)
//
//// 按字母顺序遍历map
//func traverseMapInStringOrder(params map[string]string, handler MapEntryHandler) {
//	keys := make([]string, 0)
//	for k, _ := range params {
//		keys = append(keys, k)
//	}
//	sort.Strings(keys)
//	for _, k := range keys {
//		handler(k, params[k])
//	}
//}
//func Sign(urlPath, Secret string, str url2.Values) string {
//
//	if urlPath == "" {
//		return ""
//	}
//
//	urlpath := strings.Split(urlPath, "openapi/")
//	if len(urlpath) < 2 {
//		return ""
//	}
//	path := urlpath[1]
//	maplist := make(map[string]string)
//	for i, item := range str {
//		maplist[i] = item[0]
//	}
//	var signature string
//	//按照字母顺序遍历
//	traverseMapInStringOrder(maplist, func(key string, value string) {
//		signature += key + value
//	})
//
//	signStr := path + signature
//	sign := HmacSha1(signStr, Secret)
//	signUpper := strings.ToUpper(sign)
//	fmt.Println(string(signUpper))
//	return signUpper
//
//	//return "ACD581CC036CC0D2D46FF892D0B9B455F8BE60B7"
//}
//
//func (ali *Alibb) CodeGetToken(code string) (err error) {
//
//	//refresh_token:="e85b97e5-1163-4d2f-8712-a39c01c1dd33"
//
//	url := "https://gw.open.1688.com/openapi/http/1/system.oauth2/getToken/" + ali.Key + "?grant_type=authorization_code&need_refresh_token=true&client_id=" + ali.Key + "&client_secret=" + ali.Secret + "&redirect_uri=" + ali.Domain + "&code=" + code
//
//	reqData := url2.Values{}
//
//	var resData []byte
//	err, resData = utils.PostForm(url, reqData, nil)
//
//	var token model.ResToken
//
//	err = json.Unmarshal(resData, &token)
//	if token.ErrorInfo != "" {
//		err = errors.New(token.ErrorDescription)
//		fmt.Println("获取错误", token)
//		return
//	}
//
//	log.Log().Info("info", zap.Any("token", token))
//	ali.SetToken(token.AccessToken)
//	return
//}
//
//func (ali *Alibb) SetToken(token string) {
//	err, sysSetting := GetSetting(ali.ShopID)
//	if err != nil {
//		return
//	}
//	var setting model.Setting
//	err = json.Unmarshal([]byte(sysSetting.Value), &setting)
//	setting.Token = token
//	sysSetting.SetSetting(ali.ShopID, setting)
//}
