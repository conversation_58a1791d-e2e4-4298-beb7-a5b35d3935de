package app

import (
	"application/request"
	service2 "application/service"
	"github.com/gin-gonic/gin"
	request2 "yz-go/request"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

func DeleteMessage(c *gin.Context) {

	var info request2.IdsReq
	err := c.ShouldBindJSON(&info)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if len(info.Ids) > 100 {
		yzResponse.FailWithMessage("id数量超出限制", c)
		return
	}
	if err := service2.SignMessagePool(info.Ids); err != nil {
		yzResponse.FailWithMessage("失败", c)
		return
	} else {
		yzResponse.OkWithMessage("成功", c)
		return
	}
}

func GetMessage(c *gin.Context) {
	var info request.MessagePoolSearch
	err := c.ShouldBind<PERSON>(&info)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	info.AppID = utils.GetAppID(c)
	info.AppShopID = utils.GetAppShopID(c)
	if err, list := service2.GetMessagePool(info); err != nil {
		yzResponse.FailWithMessage("失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"list": list}, c)
		return
	}
}

func MessagePoolAvailable(c *gin.Context) {
	yzResponse.OkWithMessage("成功", c)
	return
}
