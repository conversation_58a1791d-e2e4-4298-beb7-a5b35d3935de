package cron

import (
	"application/model"
	"context"
	"database/sql"
	"fmt"
	"go.uber.org/zap"
	"sync"
	"time"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func ApplicationProductCollectionHandle() {
	task := cron.Task{
		Key:  "applicationProductCollection",
		Name: "维护采购端选品库",
		// 调整为每30分钟执行一次
		Spec: "0 */30 * * * *",
		Handle: func(task cron.Task) {
			// 添加分布式锁
			lockKey := "lock:maintenance:product_collection"
			if !acquireLock(lockKey, 600) { // 10分钟的锁
				log.Log().Info("上一次任务还在执行中")
				return
			}
			defer releaseLock(lockKey)

			Maintenance()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

type App struct {
	source.Model
}

func (App) TableName() string {
	return "application"
}

type Order struct {
	source.Model
	ApplicationID uint `json:"application_id"`
	Amount        uint `json:"amount"`
}

type ProductWithImage struct {
	ProductID uint   `json:"product_id"`
	ImageUrl  string `json:"image_url" gorm:"column:image_url;type:varchar(255);comment:图片url;size:255;"`
}

type ProductStats struct {
	ProductCount     int64
	ProductSaleCount int64
}

type OrderItemModel struct {
	source.Model
	Amount    uint `json:"amount" form:"amount" gorm:"column:amount;comment:总价;"`
	OrderID   uint `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单id;index;"`
	ProductID uint `json:"product_id" form:"product_id" gorm:"column:product_id;comment:产品id;index;"`
}

func (OrderItemModel) TableName() string {
	return "order_items"
}

func Maintenance() {
	// 添加性能监控 - 开始时间
	startTime := time.Now()
	var createdCount, updatedCount int
	var mu sync.Mutex // 添加互斥锁用于安全更新计数器

	// 性能监控在下方更新

	batchSize := 20
	const maxGoroutines = 10
	db := source.DB()

	var totalApps int64
	if err := db.Model(&App{}).Count(&totalApps).Error; err != nil {
		log.Log().Error("获取应用总数失败", zap.Any("err", err))
		return
	}

	// 更新 defer 函数中的 totalApps 值
	defer func() {
		metrics := map[string]interface{}{
			"execution_time_ms": time.Since(startTime).Milliseconds(),
			"total_apps":        totalApps,
			"created_count":     createdCount,
			"updated_count":     updatedCount,
		}
		log.Log().Info("维护选品库完成", zap.Any("metrics", metrics))
	}()

	if totalApps > 1000 {
		batchSize = 50 // 数据量大时增加批处理大小
	} else if totalApps < 100 {
		batchSize = 10 // 数据量小时减少批处理大小
	}

	sem := make(chan struct{}, maxGoroutines)
	var wg sync.WaitGroup

	for offset := 0; offset < int(totalApps); offset += batchSize {
		wg.Add(1)
		sem <- struct{}{}
		go func(offset int) {
			defer wg.Done()
			defer func() { <-sem }()
			start := time.Now()

			// 获取当前批次的应用ID
			var appIDs []uint
			if err := db.Model(&App{}).
				Select("id").
				Offset(offset).
				Limit(batchSize).
				Pluck("id", &appIDs).Error; err != nil {
				log.Log().Error("获取应用ID批次失败", zap.Any("err", err))
				return
			}

			// 查询现有选品库
			var existingCollections []model.ApplicationCollection
			if err := db.Where("app_id IN ?", appIDs).Find(&existingCollections).Error; err != nil {
				log.Log().Error("获取现有选品库失败", zap.Any("err", err))
				return
			}
			existingMap := make(map[uint]model.ApplicationCollection, len(existingCollections))
			for _, ec := range existingCollections {
				existingMap[ec.AppID] = ec
			}

			// 批量获取热销商品
			type TopProduct struct {
				AppID     uint
				ProductID uint
				ImageUrl  string
				RowNum    int
			}
			var topProducts []TopProduct
			topProductsQuery := `
				WITH RankedProducts AS (
					SELECT
						s.app_id,
						p.id as product_id,
						p.image_url,
						ROW_NUMBER() OVER (PARTITION BY s.app_id ORDER BY p.sales DESC, p.id DESC) as row_num
					FROM storages s
					JOIN products p ON s.product_id = p.id
					WHERE s.app_id IN ?
					AND p.is_display = 1
				)
				SELECT * FROM RankedProducts WHERE row_num <= 3`
			if err := db.Raw(topProductsQuery, appIDs).Scan(&topProducts).Error; err != nil {
				log.Log().Error("获取热销商品失败", zap.Any("err", err))
				return
			}
			topProductsMap := make(map[uint][]model.ProductItem)
			for _, tp := range topProducts {
				if _, ok := topProductsMap[tp.AppID]; !ok {
					topProductsMap[tp.AppID] = make([]model.ProductItem, 0, 3)
				}
				topProductsMap[tp.AppID] = append(topProductsMap[tp.AppID], model.ProductItem{
					ID:       tp.ProductID,
					ImageUrl: tp.ImageUrl,
				})
			}

			// 批量统计数据
			type Stats struct {
				AppID            uint
				ProductCount     int64
				ProductSaleCount int64
				OrderCount       int64
				OrderAmount      sql.NullInt64
				LastSelectionAt  *source.LocalTime
			}
			var statsResults []Stats
			statsQuery := `
				SELECT
				   a.id as app_id,
				   COALESCE(pc.product_count, 0) as product_count,
				   COALESCE(pc.product_sale_count, 0) as product_sale_count,
				   COALESCE(oc.order_count, 0) as order_count,
				   COALESCE(oc.order_amount, 0) as order_amount,
				   MAX(s.created_at) as last_selection_at
				FROM
				   application a
				LEFT JOIN (
					SELECT
						s2.app_id,
						COUNT(DISTINCT p.id) as product_count, -- 统计去重后的商品ID
						SUM(p.sales) as product_sale_count
					FROM storages s2
					JOIN products p ON s2.product_id = p.id
					WHERE s2.app_id IN ?
					AND p.deleted_at IS NULL
					AND s2.deleted_at IS NULL
					GROUP BY s2.app_id
				) pc ON pc.app_id = a.id
				LEFT JOIN (
				   SELECT
					  o.application_id as app_id,
					  COUNT(DISTINCT o.id) as order_count,
					  SUM(o.amount) as order_amount
				   FROM orders o
				   WHERE o.application_id IN ? AND o.status > 0
				   GROUP BY o.application_id
				) oc ON oc.app_id = a.id
				LEFT JOIN storages s ON s.app_id = a.id
				WHERE a.id IN ?
				GROUP BY a.id, pc.product_count, pc.product_sale_count, oc.order_count, oc.order_amount`
			if err := db.Raw(statsQuery, appIDs, appIDs, appIDs).Scan(&statsResults).Error; err != nil {
				log.Log().Error("获取统计数据失败", zap.Any("err", err))
				return
			}
			statsMap := make(map[uint]Stats)
			for _, stat := range statsResults {
				statsMap[stat.AppID] = stat
			}

			// 组装数据
			var toCreate []model.ApplicationCollection
			var toUpdate []model.ApplicationCollection
			for _, appID := range appIDs {
				cacheKey := fmt.Sprintf("app:collection:last_update:%d", appID)
				lastUpdate, err := source.Redis().Get(context.Background(), cacheKey).Time()

				// 如果最近20分钟内已更新过，则跳过
				if err == nil && time.Since(lastUpdate) < 20*time.Minute {
					continue
				}
				stats, hasStats := statsMap[appID]
				if !hasStats {
					continue
				}
				ac, exists := existingMap[appID]
				if !exists {
					ac = model.ApplicationCollection{
						AppID: appID,
					}
				}
				ac.ProductCount = stats.ProductCount
				ac.ProductSaleCount = stats.ProductSaleCount
				ac.OrderCount = stats.OrderCount
				ac.OrderAmount = stats.OrderAmount.Int64
				ac.RecentSelectionAt = stats.LastSelectionAt
				ac.LastStatAt = &source.LocalTime{Time: time.Now()}
				if products, ok := topProductsMap[appID]; ok {
					ac.Products = products
				} else {
					ac.Products = make([]model.ProductItem, 0)
				}
				if ac.ID == 0 {
					toCreate = append(toCreate, ac)
				} else {
					toUpdate = append(toUpdate, ac)
				}
				// 更新完成后设置缓存
				source.Redis().Set(context.Background(), cacheKey, time.Now(), 25*time.Minute)
			}

			// 写入时开启事务
			tx := db.Begin()
			if tx.Error != nil {
				log.Log().Error("开启事务失败", zap.Any("err", tx.Error))
				return
			}
			if len(toCreate) > 0 {
				if err := tx.Create(&toCreate).Error; err != nil {
					tx.Rollback()
					log.Log().Error("批量创建选品库失败", zap.Any("err", err))
					return
				}
				// 更新创建计数器
				mu.Lock()
				createdCount += len(toCreate)
				mu.Unlock()
			}
			if len(toUpdate) > 0 {
				var toUpdateMap, toUpdateNotRatMap []map[string]interface{}
				for _, ac := range toUpdate {
					var recentSelectionAtValue interface{} // 使用 interface{} 类型来兼容 string 或 nil
					if ac.RecentSelectionAt != nil {
						recentSelectionAtValue = ac.RecentSelectionAt.Format("2006-01-02 15:04:05")
					} else {
						recentSelectionAtValue = nil // 如果指针为 nil，则设置为 Go 的 nil，GORM 会将其转为 SQL 的 NULL
					}
					row := map[string]interface{}{
						"id":                 ac.ID,
						"product_count":      ac.ProductCount,
						"product_sale_count": ac.ProductSaleCount,
						"order_count":        ac.OrderCount,
						"order_amount":       ac.OrderAmount,
						"products":           ac.Products,
						"last_stat_at":       ac.LastStatAt.Format("2006-01-02 15:04:05"),
						"updated_at":         time.Now().Format("2006-01-02 15:04:05"),
					}
					if recentSelectionAtValue != nil {
						row["recent_selection_at"] = recentSelectionAtValue
						toUpdateMap = append(toUpdateMap, row)
					} else {
						toUpdateNotRatMap = append(toUpdateNotRatMap, row)
					}
				}
				if len(toUpdateMap) > 0 {
					err := source.BatchUpdate(toUpdateMap, "application_collections", "")
					if err != nil {
						tx.Rollback()
						log.Log().Error("批量更新选品库失败", zap.Any("err", err))
						return
					}
				}
				if len(toUpdateNotRatMap) > 0 {
					err := source.BatchUpdate(toUpdateNotRatMap, "application_collections", "")
					if err != nil {
						tx.Rollback()
						log.Log().Error("批量更新选品库失败", zap.Any("err", err))
						return
					}
				}
				// 更新更新计数器
				mu.Lock()
				updatedCount += len(toUpdate)
				mu.Unlock()
			}
			if err := tx.Commit().Error; err != nil {
				tx.Rollback()
				log.Log().Error("提交事务失败", zap.Any("err", err))
				return
			}

			elapsed := time.Since(start)
			log.Log().Info("Maintenance批次处理完成",
				zap.Int("offset(页码)", offset),
				zap.Int("batch_size(数量)", batchSize),
				zap.Duration("elapsed(秒)", elapsed))
		}(offset)
	}
	wg.Wait()
}

// acquireLock 获取分布式锁
// key: 锁的键名
// ttl: 锁的过期时间(秒)
func acquireLock(key string, ttl int) bool {
	ctx := context.Background()
	redis := source.Redis()

	// 使用 SET key value NX EX ttl 命令尝试获取锁
	// NX: 仅在键不存在时设置
	// EX: 设置过期时间（秒）
	success, err := redis.SetNX(ctx, key, time.Now().Unix(), time.Duration(ttl)*time.Second).Result()
	if err != nil {
		log.Log().Error("获取分布式锁失败",
			zap.String("key", key),
			zap.Error(err))
		return false
	}

	return success
}

// releaseLock 释放分布式锁
func releaseLock(key string) {
	ctx := context.Background()
	redis := source.Redis()

	// 删除锁
	_, err := redis.Del(ctx, key).Result()
	if err != nil {
		log.Log().Error("释放分布式锁失败",
			zap.String("key", key),
			zap.Error(err))
	}
}
