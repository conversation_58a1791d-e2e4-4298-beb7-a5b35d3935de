// 自动生成模板Application
package model

import (
	"gorm.io/gorm"
	"payment/model"
	mq2 "product/mq"
	"product/response"
	"strings"
	"time"
	"yz-go/component/upload"
	"yz-go/source"
)

// 如果含有time.Time 请自行import time包
type ApplicationModel struct {
	source.Model
	CompanyName          string `json:"companyName" form:"companyName" gorm:"column:company_name;comment:公司名称;type:varchar(255);size:255;"`
	CompanyIntro         string `json:"companyIntro" form:"companyIntro" gorm:"column:company_intro;comment:公司介绍;type:text;"`
	ProvinceId           int    `json:"provinceId" form:"provinceId" gorm:"column:province_id;comment:省;type:int(10);size:10;"`
	CityId               int    `json:"cityId" form:"cityId" gorm:"column:city_id;comment:市;type:int(10);size:10;"`
	DistrictId           int    `json:"districtId" form:"districtId" gorm:"column:district_id;comment:区;type:int(10);size:10;"`
	Address              string `json:"address" form:"address" gorm:"column:address;comment:详细地址;type:varchar(255);size:255;"`
	CreditCode           string `json:"creditCode" form:"creditCode" gorm:"column:credit_code;comment:信用代码;type:varchar(255);size:255;"`
	BusinessLicense      string `json:"businessLicense" form:"businessLicense" gorm:"column:business_license;comment:;type:text;"`
	LegalPersonName      string `json:"legalPersonName" form:"legalPersonName" gorm:"column:legal_person_name;comment:;type:varchar(255);size:255;"`
	IdCardNumber         string `json:"idCardNumber" form:"idCardNumber" gorm:"column:id_card_number;comment:;type:varchar(255);size:255;"`
	IdCardFront          string `json:"idCardFront" form:"idCardFront" gorm:"column:id_card_front;comment:;type:text;"`
	IdCardBackend        string `json:"idCardBackend" form:"idCardBackend" gorm:"column:id_card_backend;comment:;type:text;"`
	ContactsName         string `json:"contactsName" form:"contactsName" gorm:"column:contacts_name;comment:;type:varchar(255);size:255;"`
	ContactsPhontnumber  string `json:"contactsPhontnumber" form:"contactsPhontnumber" gorm:"column:contacts_phontnumber;comment:;type:varchar(255);size:255;"`
	ContactsEmail        string `json:"contactsEmail" form:"contactsEmail" gorm:"column:contacts_email;comment:;type:varchar(255);size:255;"`
	AppName              string `json:"appName" form:"appName" gorm:"column:app_name;comment:;type:varchar(255);size:255;"`
	AppLevelID           uint   `json:"appLevelId" form:"appLevelId" gorm:"column:app_level_id;type:int;"`
	CallBackLink         string `json:"callBackLink" form:"callBackLink" gorm:"column:call_back_link;comment:;type:varchar(255);size:255;"`
	CallBackType         int    `json:"callBackType" form:"callBackType" gorm:"column:call_back_type;comment:;type:int;"`
	CallBackLinkValidity int    `json:"call_back_link_validity" form:"call_back_link_validity" gorm:"column:call_back_link_validity;default:1;type:int;"`
	CallBackLinkCps      string `json:"callBackLinkCps" form:"callBackLinkCps" gorm:"column:call_back_link_cps;comment:;type:varchar(255);size:255;"`
	CallBackLinkJhCps    string `json:"callBackLinkJhCps" form:"callBackLinkJhCps" gorm:"column:call_back_link_jh_cps;comment:;type:varchar(255);size:255;"`

	IpList                     string `json:"ipList" form:"ipList" gorm:"column:ip_list;comment:;type:text;"`
	MemberId                   int    `json:"memberId" form:"memberId" gorm:"column:member_id;comment:;type:int;size:10;"`
	AppSecret                  string `json:"appSecret" gorm:"column:app_secret;comment:;type:varchar(255);"`
	SupplierID                 uint   `json:"supplier_id"`
	GroupID                    uint   `json:"group_id" form:"group_id"`
	PetSupplierID              uint   `json:"pet_supplier_id"`
	IsCheckSign                int    `json:"is_check_sign" form:"is_check_sign" gorm:"column:is_check_sign;default:0;comment:是否开启签名验证;type:smallint;size:3;"` // 状态 0关闭 1开启
	ExportAppLink              string `json:"export_link" form:"export_link"`
	PetSupplierIds             []uint `json:"pet_supplier_ids" gorm:"-"`
	IsDownPrice                int    `json:"is_down_price" form:"is_down_price" gorm:"column:is_down_price;default:0;comment:是否获取下游销售单价;type:smallint;size:3;"` // 状态 0关闭 1开启
	Banlist                    int    `json:"banlist" form:"banlist" gorm:"column:banlist;default:0;comment:是否被禁用;type:smallint;size:3;"`
	StatusCode                 int    `json:"status_code" form:"status_code" gorm:"column:status_code;default:1;comment:运营状态 0测试中 1运营中;type:smallint;size:3;"`
	SzbaoIndependence          int    `json:"szbao_independence" gorm:"column:szbao_independence;default:0;comment:永源独立配置 0关闭 1开启;type:smallint;size:3;"`
	SzbaoIndependenceAppKey    string `json:"szbao_independence_app_key"`
	SzbaoIndependenceAppSecret string `json:"szbao_independence_app_secret"`
	IsMultiShop                int    `json:"is_multi_shop" form:"is_multi_shop" gorm:"column:is_multi_shop;default:0;comment:是否多店铺(多密钥) 2关闭 1开启;type:smallint;size:3;"`
	IsMessagePool              int    `json:"is_message_pool" form:"is_message_pool" gorm:"column:is_message_pool;default:0;comment:是否开启消息池;type:smallint;size:3;"`
}

func (ApplicationModel) TableName() string {
	return "application"
}

type Application struct {
	ApplicationModel
	ApplicationLevel   ApplicationLevel           `json:"applicationLevel" gorm:"foreignKey:AppLevelID"`
	ApplicationPaySort []model.ApplicationPaySort `json:"application_pay_sort"`
	PetSuppliers       []response.Supplier        `json:"pet_suppliers" gorm:"many2many:application_pet_suppliers;joinReferences:PetSupplierID;"`
	ApplicationShops   []ApplicationShop          `json:"application_shops" gorm:"foreignKey:ApplicationID;references:ID"`
}
type ApplicationPetSupplierModel struct {
	ID            uint `json:"id" form:"id" gorm:"primarykey"`
	ApplicationID uint `json:"application_id" form:"application_id"`
	PetSupplierID uint `json:"pet_supplier_id" form:"pet_supplier_id"`
}

func (ApplicationPetSupplierModel) TableName() string {
	return "application_pet_suppliers"
}

type ApplicationPetSupplier struct {
	ApplicationPetSupplierModel
	Application Application       `json:"application"`
	PetSupplier response.Supplier `json:"pet_supplier"`
}

func (Application) TableName() string {
	return "application"
}
func (a Application) GetCallBackLink() string {
	if a.CallBackType == 1 {
		// 如果CallBackType为1，则发送到workman接口，客户的采购端的接口格式需要为supply-notify?i=1，商城需要在nginx中增加匹配规则
		callBackLink := strings.Replace(a.CallBackLink, "addons/yun_shop/api.php", "supply-notify", 1)
		return callBackLink
	}
	return a.CallBackLink
}

type ApplicationApplyRecordModel struct {
	source.Model
	ApplicationID uint   `json:"application_id"`
	UserID        uint   `json:"user_id"` //前台用户id
	Status        int    `json:"status"`  //0未审核1通过2驳回
	Reason        string `json:"reason"`  //驳回原因
}

func (ApplicationApplyRecordModel) TableName() string {
	return "application_apply_records"
}

type ApplicationApplyRecord struct {
	ApplicationApplyRecordModel
	Application      Application       `json:"application"`
	ApplicationShops []ApplicationShop `json:"application_shops" gorm:"foreignKey:ApplicationID;references:ApplicationID"`
}

/*
*

	供应链来源名称表
*/
type ApplicationSource struct {
	source.Model
	Type             string `json:"type" form:"type" gorm:"column:type;comment:来源标识（属于哪个供应链）;type:varchar(255);size:255;"`                          //来源标识（属于哪个供应链）
	Name             string `json:"name" form:"name" gorm:"column:name;comment:来源原本名称;type:varchar(255);size:255;"`                                      //来源原本名称
	SourceID         uint   `json:"source_id" form:"source_id" gorm:"column:source_id;comment:来源id 唯一;type:int(10);size:10;"`                              //来源id 唯一
	Title            string `json:"title" form:"title" gorm:"column:title;comment:来源自定义名称;type:varchar(255);size:255;"`                                 //来源自定义名称
	ApplicationTitle string `json:"application_title" form:"application_title" gorm:"column:application_title;comment:供应链名称;type:varchar(255);size:255;"` //供应链名称

}

type BynSupplyOrder struct {
	source.Model
	OutTradeNo string `json:"out_trade_no" gorm:"out_trade_no;comment:外部订单号;"`
}

func (BynSupplyOrder) TableName() string {
	return "byn_supply_orders"
}

type FuluSupplyOrderResult struct {
	source.Model
	CustomerOrderNo string `json:"customer_order_no" form:"customer_order_no" gorm:"customer_order_no;comment:外部订单号，每次请求必须唯一;"`
}

type FuluSupplyGoods struct {
	source.Model
	ProductId       int    `json:"product_id" gorm:"column:product_id;comment:商品Id;"`
	ProductIdString string `json:"product_id_string" gorm:"column:product_id_string;comment:第三方商品id;"`
}

type UserEquityProduct struct {
	source.Model
	EQID int `json:"equity_product_id" gorm:"column:product_id;comment:权益商品Id;"`
}

type PushMessageErr struct {
	source.Model
	JsonData   string            `json:"json_data" gorm:"column:json_data;type:text;"`
	ReSendTime *source.LocalTime `json:"re_send_time"`
	Url        string            `json:"url"`
	Type       int               `json:"type"` // 1商品 2订单
	Err        string            `json:"err"`
}

type RePushProductMessage struct {
	mq2.ProductMessage
	Url string `json:"url"` //重试链接
}
type AppOrderExportRecordModel struct {
	source.Model
	Link          string `json:"link"`
	StatusString  string `json:"status_string"`
	OrderCount    int64  `json:"order_count"`
	ApplicationID uint   `json:"application_id"`
}

func (AppOrderExportRecordModel) TableName() string {
	return "app_order_export_records"
}

type AppOrderExportRecord struct {
	AppOrderExportRecordModel
	Application Application `json:"application"`
}

type ApplicationGroup struct {
	source.Model
	Name   string `json:"name" form:"name" gorm:"column:name;comment:;type:varchar(255);size:255;"`
	Sort   int    `json:"sort" form:"sort" gorm:"column:sort;comment:;type:int;size:11;"`
	IsShow int    `json:"is_show" form:"is_show" gorm:"column:is_show;comment:;type:int;size:11;"`
}

type ApplicationShop struct {
	source.Model
	ApplicationID uint   `json:"application_id" form:"application_id" gorm:"column:application_id;comment:;type:int;size:10;"`
	ShopName      string `json:"shop_name" form:"shop_name"`
	CallbackLink  string `json:"callback_link" form:"callback_link"`
	AppSecret     string `json:"app_secret" form:"app_secret"`
	IsMessagePool int    `json:"is_message_pool" form:"is_message_pool" gorm:"column:is_message_pool;default:0;comment:是否开启消息池;type:smallint;size:3;"`
}

func (a *ApplicationModel) AfterFind(tx *gorm.DB) (err error) {
	ossUrl := upload.NewOssUrl()
	expireTime := time.Hour * 1 // 设置24小时过期时间

	// 处理所有图片字段
	if a.BusinessLicense != "" {
		a.BusinessLicense = ossUrl.GetUrl(a.BusinessLicense, expireTime)
	}
	if a.IdCardFront != "" {
		a.IdCardFront = ossUrl.GetUrl(a.IdCardFront, expireTime)
	}
	if a.IdCardBackend != "" {
		a.IdCardBackend = ossUrl.GetUrl(a.IdCardBackend, expireTime)
	}

	return nil
}

func (a *ApplicationModel) BeforeSave(tx *gorm.DB) (err error) {
	ossUrl := upload.NewUrlConverter()

	// 处理所有图片字段
	if a.BusinessLicense != "" {
		a.BusinessLicense, err = ossUrl.ConvertToNormalUrl(a.BusinessLicense)
		if err != nil {
			return
		}
	}
	if a.IdCardFront != "" {
		a.IdCardFront, err = ossUrl.ConvertToNormalUrl(a.IdCardFront)
		if err != nil {
			return
		}
	}
	if a.IdCardBackend != "" {
		a.IdCardBackend, err = ossUrl.ConvertToNormalUrl(a.IdCardBackend)
		if err != nil {
			return
		}
	}

	return nil
}
