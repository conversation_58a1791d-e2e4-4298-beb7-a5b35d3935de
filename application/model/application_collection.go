package model

import (
	"database/sql/driver"
	"encoding/json"
	"yz-go/source"
)

type ApplicationCollection struct {
	source.Model
	AppID             uint              `json:"app_id" form:"app_id" gorm:"column:app_id;comment:采购端id;uniqueIndex:idx_unique_app_id"` // 加上 uniqueIndex
	Name              string            `json:"name" form:"name" gorm:"column:name;comment:选品库名称;"`
	Share             int               `json:"share" form:"share" gorm:"column:share;comment:是否分享;default:0"`
	ShareAt           *source.LocalTime `json:"share_at" form:"share_at" gorm:"column:share_at;comment:分享时间;"`
	BrowseCount       int64             `json:"browse_count" form:"browse_count" gorm:"column:browse_count;comment:浏览次数;default:0"`
	RecentSelectionAt *source.LocalTime `json:"recent_selection_at" form:"recent_selection_at" gorm:"column:recent_selection_at;comment:最后选品时间;"`
	Products          Products          `json:"products" form:"products" gorm:"column:products;type:longtext"`
	ProductCount      int64             `json:"product_count" form:"product_count" gorm:"column:product_count"`
	ProductSaleCount  int64             `json:"product_sale_count" form:"product_sale_count" gorm:"column:product_sale_count"`
	OrderCount        int64             `json:"order_count" form:"order_count" gorm:"column:order_count"`
	OrderAmount       int64             `json:"order_amount" form:"order_amount" gorm:"column:order_amount"`
	LastStatAt        *source.LocalTime `json:"last_stat_at" form:"last_stat_at" gorm:"column:last_stat_at;comment:最后统计时间;"`
}

type Products []ProductItem

type ProductItem struct {
	ID       uint   `json:"id" form:"id"`
	ImageUrl string `json:"image_url" form:"image_url"`
}

func (value Products) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *Products) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}
