package service

import (
	"application/model"
	"application/mq"
	"application/request"
	"application/response"
	"archive/zip"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"io"
	orderModel "order/model"
	orderResponse "order/response"
	"os"
	"path/filepath"
	mq2 "product/mq"
	"public-supply/common"
	express2 "shipping/express"
	"strconv"
	"strings"
	"time"
	model2 "user/model"
	"yz-go/cache"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	service2 "yz-go/service"
	yzGoSetting "yz-go/setting"

	"yz-go/config"
	"yz-go/source"
	"yz-go/utils"
)

// @Description 验证回调地址
func ValidateCallback(link string) (err error) {
	header := map[string]string{
		"Content-Type": "application/json",
	}

	var messageId string
	var product mq2.ProductMessage
	messageId = "product" + strconv.Itoa(0) + "goods.alter" + strconv.Itoa(int(time.Now().Unix()))
	product.MessageID = "self" + base64.StdEncoding.EncodeToString([]byte(messageId))
	product.MessageType = "goods.alter"
	var res Resp
	err, res = post(link, product, header)
	if err != nil {
		fmt.Println("回调地址请求失败，请修改为有效回调地址：", err)
		log.Log().Info("回调地址请求失败，请修改为有效回调地址!", zap.Any("err", err))
		err = errors.New("回调地址请求失败，请修改为有效回调地址!")
		return
	} else {
		if res.Code != 0 && res.Result != 1 {
			fmt.Println("回调地址请求失败，请修改为有效回调地址：", res)
			log.Log().Info("回调地址请求失败，请修改为有效回调地址!", zap.Any("err", res))
			err = errors.New("回调地址请求失败，请修改为有效回调地址!")
			return
		}
	}
	return
}

// @author: [piexlmax](https://github.com/piexlmax)
// @function: CreateApplication
// @description: 创建Application记录
// @param: application model.Application
// @return: err error
func CreateApplication(application model.Application) (err error, id uint) {
	if application.AppLevelID <= 0 {
		err = errors.New("采购端等级id不能为空")
		return
	}
	// 如果运营状态：运营中，验证回调地址
	if application.StatusCode == 1 && application.IsMultiShop == 2 {
		if err = ValidateCallback(application.CallBackLink); err != nil {
			return
		}
	}
	var applicationCheck []model.Application
	err = source.DB().Where("member_id = ?", application.MemberId).Find(&applicationCheck).Error
	if err != nil || len(applicationCheck) > 0 {
		return errors.New("此用户已绑定其他采购端，请更换用户"), id
	}

	err = source.DB().Create(&application).Error
	if err != nil {
		return
	}
	id = application.ID
	err = source.DB().Create(&model.ApplicationApplyRecordModel{
		ApplicationID: application.ID,
		UserID:        uint(application.MemberId),
		Status:        1,
	}).Error
	if err != nil {
		return
	}
	err = mq.PublishMessage(uint(application.MemberId), mq.Created)
	return
}

/*
*

	不验证回调地址 和用户创建 -- 大昌行-ERP插件使用 （因为自动创建未提供用户和回调需要创建之后客户自行补齐）
*/
func CreateApplicationDoNotVerify(application model.Application) (err error, id uint) {
	err = source.DB().Create(&application).Error
	if err != nil {
		return
	}
	id = application.ID
	err = source.DB().Create(&model.ApplicationApplyRecordModel{
		ApplicationID: application.ID,
		UserID:        uint(application.MemberId),
		Status:        1,
	}).Error
	if err != nil {
		return
	}
	return
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: DeleteApplication
//@description: 删除Application记录
//@param: application model.Application
//@return: err error

func DeleteApplication(application model.Application) (err error) {
	err = source.DB().Delete(&application).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: DeleteApplicationByIds
//@description: 批量删除Application记录
//@param: ids yzRequest.IdsReq
//@return: err error

func DeleteApplicationByIds(ids yzRequest.IdsReq) (err error) {
	err = source.DB().Delete(&[]model.Application{}, "id in ?", ids.Ids).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: UpdateApplication
//@description: 更新Application记录
//@param: application *model.Application
//@return: err error

func UpdateApplication(application model.Application) (err error) {
	var applicationCheck []model.Application
	err = source.DB().Where("member_id = ?", application.MemberId).Where("id <> ?", application.ID).Find(&applicationCheck).Error
	if err != nil || len(applicationCheck) > 0 {
		return errors.New("此用户已绑定其他采购端，请更换用户")
	}

	// 如果运营状态：运营中验证回调地址，测试中不验证
	if application.StatusCode == 1 && application.IsMultiShop == 2 {
		if err = ValidateCallback(application.CallBackLink); err != nil {
			return
		}
	}

	application.CallBackLinkValidity = 1
	err = source.DB().Save(&application).Error
	return err
}

func UpdateApplicationSzbaoIndependence(application model.Application) (err error) {
	err = application.BeforeSave(source.DB())
	if err != nil {
		return
	}
	err = source.DB().Model(&model.ApplicationModel{}).Where("id = ?", application.ID).Updates(&application).Error
	return err
}

func SaveApplication(application model.Application) (err error) {
	var applicationCheck []model.Application
	err = source.DB().Where("member_id = ?", application.MemberId).Where("id <> ?", application.ID).Find(&applicationCheck).Error
	if err != nil || len(applicationCheck) > 0 {
		return errors.New("此用户已绑定其他采购端，请更换用户")
	}

	header := map[string]string{
		"Content-Type": "application/json",
	}

	var messageId string
	var product mq2.ProductMessage
	messageId = "product" + strconv.Itoa(0) + "goods.alter" + strconv.Itoa(int(time.Now().Unix()))
	product.MessageID = "self" + base64.StdEncoding.EncodeToString([]byte(messageId))
	product.MessageType = "goods.alter"
	var respon Resp
	err, respon = post(application.CallBackLink, product, header)
	if err != nil {
		fmt.Println("回调地址请求失败，请修改为有效回调地址：", err)
		log.Log().Info("回调地址请求失败，请修改为有效回调地址!", zap.Any("err", err))
		return
	} else {
		if respon.Code != 0 && respon.Result != 1 {
			fmt.Println("回调地址请求失败，请修改为有效回调地址：", respon)
			log.Log().Info("回调地址请求失败，请修改为有效回调地址!", zap.Any("err", respon))
			err = errors.New("回调地址请求失败，请修改为有效回调地址!")
			return
		}
	}
	err = application.BeforeSave(source.DB())
	if err != nil {
		return
	}
	err = source.DB().Updates(&application).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetApplication
//@description: 根据id获取Application记录
//@param: id uint
//@return: err error, application model.Application

type GatherSupplyApplicationLevel struct {
	source.Model
	GatherSupplyID uint         `json:"gather_supply_id"`
	SourceID       uint         `json:"source_id"`
	SourceName     string       `json:"source_name"`
	GatherSupply   GatherSupply `json:"gather_supply" gorm:"foreignKey:gather_supply_id;references:id"`
}

type GatherSupply struct {
	source.Model
	Name       string `json:"name"`
	CategoryId uint   `json:"category_id"`
}

func GetSupplySource(id uint) (data interface{}) {
	// 获取应用信息
	app, err := cache.GetApplicationFromCache(id)
	if err != nil {
		return
	}

	// 获取应用等级对应的来源列表
	list, err := cache.GetApplicationLevelSourcesFromCache(app.AppLevelID)
	if err != nil {
		return
	}

	// 获取所有应用来源
	applicationSource, err := cache.GetAllApplicationSourcesFromCache()
	if err != nil {
		return
	}

	// 处理数据
	var isValuation = 0 //是否已赋值 1是 0否
	for index, item := range list {
		isValuation = 0
		for _, asItem := range applicationSource {
			if asItem.SourceID == item.SourceID {
				if asItem.Title == "" {
					list[index].SourceName = asItem.Name
				} else {
					list[index].SourceName = asItem.Title
				}
				isValuation = 1
			}
		}
		if isValuation == 0 {
			list[index].SourceName = GetMap(item.SourceID)
		}

	}

	data = list
	return

}

type GatherSupplyApplicationList struct {
	GatherSupplyID uint   `json:"gather_supply_id"`
	SourceID       uint   `json:"source_id"`
	SourceName     string `json:"source_name"`
	Type           int    `json:"type"` //0来源 1供应链id

}

type GettingData struct {
	BaseInfo BaseInfoData `json:"baseInfo"`
}

type BaseInfoData struct {
	StoreName             string `json:"storeName"`
	ShowSourceInMyStorage int    `json:"show_source_in_my_storage"`
}

var ids = [...]uint{common.SUPPLY_STBZ, common.SUPPLY_SELF, common.SUPPLY_CROSS, common.SUPPLY_YZH, common.SUPPLY_YZHNEW, common.SUPPLY_DWD, common.SUPPLY_HEHE, common.SUPPLY_ZYHX, common.SUPPLY_LIJING, common.SUPPLY_ALJX, common.SUPPLY_JUSHUITAN, common.SUPPLY_SZBAO, common.SUPPLY_TIANMA, common.SUPPLY_SHAMA, common.SUPPLY_YIYATONG, common.SUPPLY_JD_VOP, common.SUPPLY_MAIGER, common.SUPPLY_GAT, common.SELF_CAKE, common.SUPPLY_WDT} //需要查询的来源
func GetGatherSupplyAndSource(id uint) (data []GatherSupplyApplicationList) {
	var app model.Application
	err := source.DB().Where("id=?", id).First(&app).Error
	if err != nil {
		return
	}
	var list []GatherSupplyApplicationLevel
	//排除胜天半子的 胜天半子的使用来源
	err = source.DB().Model(&GatherSupplyApplicationLevel{}).Select("DISTINCT gather_supply_application_levels.gather_supply_id,source_id,gather_supplies.category_id").Joins("inner join gather_supplies on gather_supplies.id = gather_supply_application_levels.gather_supply_id").Where("gather_supplies.category_id in ?", ids).Where("gather_supplies.category_id != 1").Preload("GatherSupply").Where("application_level_id=?", app.AppLevelID).Find(&list).Error
	if err != nil {
		return
	}
	var applicationSource []model.ApplicationSource
	source.DB().Find(&applicationSource)
	var isValuation = 0 //是否已赋值 1是 0否
	for _, item := range list {
		var gatherSupplyApplicationList GatherSupplyApplicationList
		var name = ""
		_, settingValue := yzGoSetting.GetSetting("gatherSupply" + strconv.Itoa(int(item.GatherSupplyID)))
		var gettingData GettingData
		_ = json.Unmarshal([]byte(settingValue), &gettingData)
		//如果供应链没设置名称则使用 自定义的来源名称
		if gettingData.BaseInfo.StoreName != "" {
			name = gettingData.BaseInfo.StoreName
		} else if item.GatherSupply.Name != "" {
			name = item.GatherSupply.Name
		} else {
			isValuation = 0
			for _, asItem := range applicationSource {
				if asItem.SourceID == item.SourceID {
					if asItem.Title == "" {
						name = asItem.Name
					} else {
						name = asItem.Title
					}
					isValuation = 1
				}
			}
			if isValuation == 0 {
				name = GetMap(item.SourceID)
			}
		}
		gatherSupplyApplicationList.SourceName = name
		gatherSupplyApplicationList.GatherSupplyID = item.GatherSupplyID
		//gatherSupplyApplicationList.SourceID = item.SourceID
		gatherSupplyApplicationList.Type = 1
		data = append(data, gatherSupplyApplicationList)
	}

	list = []GatherSupplyApplicationLevel{}
	//只查询胜天半子的来源
	err = source.DB().Select("DISTINCT gather_supply_application_levels.source_id").Joins("inner join gather_supplies on gather_supplies.id = gather_supply_application_levels.gather_supply_id").Preload("GatherSupply").Where("application_level_id=?", app.AppLevelID).Where("gather_supplies.category_id = 1").Find(&list).Error
	if len(list) > 0 {
		for _, item := range list {
			var name = ""
			var gatherSupplyApplicationList GatherSupplyApplicationList
			isValuation = 0
			for _, asItem := range applicationSource {
				if asItem.SourceID == item.SourceID {
					if asItem.Title == "" {
						name = asItem.Name
					} else {
						name = asItem.Title
					}
					isValuation = 1
				}
			}
			if isValuation == 0 {
				name = GetMap(item.SourceID)
			}
			gatherSupplyApplicationList.SourceName = name
			//gatherSupplyApplicationList.GatherSupplyID = item.GatherSupplyID
			gatherSupplyApplicationList.SourceID = item.SourceID
			gatherSupplyApplicationList.Type = 0
			data = append(data, gatherSupplyApplicationList)
		}
	}
	return

}

func GetMyGatherSupplyAndSource(id uint) (data []GatherSupplyApplicationList) {
	var list []GatherSupplyApplicationLevel

	//排除胜天半子的 胜天半子的使用来源
	err := source.DB().Model(&GatherSupplyApplicationLevel{}).Select("DISTINCT gather_supply_application_levels.gather_supply_id,source_id,gather_supplies.category_id").Joins("inner join gather_supplies on gather_supplies.id = gather_supply_application_levels.gather_supply_id").Where("gather_supplies.category_id in ?", ids).Where("gather_supplies.category_id != 1").Preload("GatherSupply").Find(&list).Error
	if err != nil {
		return
	}
	var applicationSource []model.ApplicationSource
	source.DB().Find(&applicationSource)
	var isValuation = 0 //是否已赋值 1是 0否
	for _, item := range list {
		var gatherSupplyApplicationList GatherSupplyApplicationList
		var name = ""
		_, settingValue := yzGoSetting.GetSetting("gatherSupply" + strconv.Itoa(int(item.GatherSupplyID)))
		var gettingData GettingData
		_ = json.Unmarshal([]byte(settingValue), &gettingData)
		if gettingData.BaseInfo.ShowSourceInMyStorage == 1 {
			continue
		}
		//如果供应链没设置名称则使用 自定义的来源名称
		if gettingData.BaseInfo.StoreName != "" {
			name = gettingData.BaseInfo.StoreName
		} else if item.GatherSupply.Name != "" {
			name = item.GatherSupply.Name
		} else {
			isValuation = 0
			for _, asItem := range applicationSource {
				if asItem.SourceID == item.SourceID {
					if asItem.Title == "" {
						name = asItem.Name
					} else {
						name = asItem.Title
					}
					isValuation = 1
				}
			}
			if isValuation == 0 {
				name = GetMap(item.SourceID)
			}
		}
		gatherSupplyApplicationList.SourceName = name
		gatherSupplyApplicationList.GatherSupplyID = item.GatherSupplyID
		//gatherSupplyApplicationList.SourceID = item.SourceID
		gatherSupplyApplicationList.Type = 1
		data = append(data, gatherSupplyApplicationList)
	}

	list = []GatherSupplyApplicationLevel{}
	//只查询胜天半子的来源
	err = source.DB().Select("DISTINCT gather_supply_application_levels.source_id,gather_supply_id").Joins("inner join gather_supplies on gather_supplies.id = gather_supply_application_levels.gather_supply_id").Preload("GatherSupply").Where("gather_supplies.category_id = 1").Find(&list).Error
	if len(list) > 0 {
		for _, item := range list {
			_, settingValue := yzGoSetting.GetSetting("gatherSupply" + strconv.Itoa(int(item.GatherSupplyID)))
			var gettingData GettingData
			_ = json.Unmarshal([]byte(settingValue), &gettingData)
			if gettingData.BaseInfo.ShowSourceInMyStorage == 1 {
				continue
			}
			var name = ""
			var gatherSupplyApplicationList GatherSupplyApplicationList
			isValuation = 0
			for _, asItem := range applicationSource {
				if asItem.SourceID == item.SourceID {
					if asItem.Title == "" {
						name = asItem.Name
					} else {
						name = asItem.Title
					}
					isValuation = 1
				}
			}
			if isValuation == 0 {
				name = GetMap(item.SourceID)
			}
			gatherSupplyApplicationList.SourceName = name
			//gatherSupplyApplicationList.GatherSupplyID = item.GatherSupplyID
			gatherSupplyApplicationList.SourceID = item.SourceID
			gatherSupplyApplicationList.Type = 0
			data = append(data, gatherSupplyApplicationList)
		}
	}
	return

}

func GetMap(id uint) (name string) {

	for _, item := range config.SupplyMap {

		if item.ID == int(id) {
			return item.Name
		}

	}
	return
}
func GetApplication(id uint) (err error, application model.Application) {
	err = source.DB().Preload("ApplicationPaySort").Preload("PetSuppliers").Where("id = ?", id).First(&application).Error
	if err != nil {
		return
	}
	var petSupplierRelation []uint
	err = source.DB().Model(&model.ApplicationPetSupplierModel{}).Where("application_id = ?", id).Pluck("pet_supplier_id", &petSupplierRelation).Error
	if err != nil {
		return
	}
	application.PetSupplierIds = petSupplierRelation
	return
}

func GetApplicationByUserID(id uint) (err error, application model.Application) {
	err = source.DB().Preload("ApplicationPaySort").Where("member_id = ?", id).First(&application).Error
	return
}

func ChangeBanlist(appID uint) (err error) {
	var application model.Application
	err = source.DB().Where("id = ?", appID).First(&application).Error
	if err != nil {
		return
	}
	if application.Banlist == 1 {
		err = source.DB().Model(&model.Application{}).Where("id = ?", appID).Update("banlist", 0).Error
	} else {
		err = source.DB().Model(&model.Application{}).Where("id = ?", appID).Update("banlist", 1).Error
	}
	cache.ClearApplication(appID)
	return
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetApplicationInfoList
//@description: 分页获取Application记录
//@param: info request.ApplicationSearch
//@return: err error, list interface{}, total int64

func GetApplicationInfoList(info request.ApplicationSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Preload("DaHangErpItem").Preload("ApplicationLevel").Preload("PetSupplier").Preload("Order", "orders.status > 0").Preload("User.Balance").Preload("User.GoinBalance").Model(&response.ApplicationAdminList{})
	var applications []response.ApplicationAdminList
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.CompanyName != "" {
		db = db.Where("`company_name` LIKE ?", "%"+info.CompanyName+"%")
	}
	if info.AppName != "" {
		db = db.Where("`app_name` LIKE ?", "%"+info.AppName+"%")
	}
	if info.ProvinceId != 0 {
		db = db.Where("`province_id` = ?", info.ProvinceId)
	}
	if info.CityId != 0 {
		db = db.Where("`city_id` = ?", info.CityId)
	}
	if info.GroupID != 0 {
		db = db.Where("`group_id` = ?", info.GroupID)
	}
	if info.DistrictId != 0 {
		db = db.Where("`district_id` = ?", info.DistrictId)
	}
	if info.LegalPersonName != "" {
		db = db.Where("`legal_person_name` LIKE ?", "%"+info.LegalPersonName+"%")
	}
	if info.AppLevelID != 0 {
		db = db.Where("`app_level_id` = ?", info.AppLevelID)
	}
	if info.SzbaoIndependence != nil {
		db = db.Where("`szbao_independence` = ?", info.SzbaoIndependence)
	}

	if info.PetSupplierID != nil {
		var applicationSetting model.ApplicationSetting
		err, applicationSetting = GetApplicationSetting()
		if err != nil {
			return
		}
		if applicationSetting.Value.MultiPetSupplier == 1 {
			var appIDs []uint
			err = source.DB().Model(&model.ApplicationPetSupplier{}).Where("pet_supplier_id = ?", info.PetSupplierID).Pluck("application_id", &appIDs).Error
			if err != nil {
				return
			}
			if len(appIDs) > 0 {
				db = db.Where("`id` in ?", appIDs)

			}
		} else {
			db = db.Where("`pet_supplier_id` = ?", &info.PetSupplierID)

		}
	}
	if info.MemberName != "" {
		var userIds []uint
		err = source.DB().Model(&model2.User{}).Where("username like ?", "%"+info.MemberName+"%").Or("nick_name like ?", "%"+info.MemberName+"%").Pluck("id", &userIds).Error
		db = db.Where("`member_id` in ?", userIds)
	}
	if info.MemberUserName != "" {
		var userIds []uint
		err = source.DB().Model(&model2.User{}).Where("username like ?", "%"+info.MemberUserName+"%").Pluck("id", &userIds).Error
		db = db.Where("`member_id` in ?", userIds)
	}
	if info.APPID != nil {
		db = db.Where("`id` = ?", &info.APPID)
	}
	err = db.Count(&total).Error
	err = db.Order("created_at desc").Preload("GoinBalance", "type = 1").Preload("Balance", "type = 2").Limit(limit).Offset(offset).Find(&applications).Error

	return err, applications, total
}

func GetApplicationOptionList(petSupplierID *uint) (err error, list interface{}) {

	db := source.DB().Model(&model.Application{})
	if petSupplierID != nil {
		db = db.Where("`pet_supplier_id` = ?", &petSupplierID)
	}
	var applications []response.Application
	err = db.Find(&applications).Error
	return err, applications
}

func GetApplicationLevelList() (err error, list interface{}) {

	// 创建db
	db := source.DB().Model(&model.ApplicationLevel{})
	var options []model.ApplicationLevel

	err = db.Find(&options).Error
	return err, options
}

func CreateKeySecret(id uint) (err error, secret string) {
	secret = utils.MD5V([]byte("application" + strconv.Itoa(int(id)) + strconv.Itoa(int(time.Now().Unix()))))
	err = source.DB().Model(&model.Application{}).Where("id = ?", id).Update("app_secret", secret).Error
	return
}

func CreateShopKeySecret(id uint, shopId uint) (err error, secret string) {
	secret = utils.MD5V([]byte("application" + strconv.Itoa(int(id)) + "_" + strconv.Itoa(int(shopId)) + strconv.Itoa(int(time.Now().Unix()))))
	err = source.DB().Model(&model.ApplicationShop{}).Where("id = ?", shopId).Update("app_secret", secret).Error
	return
}

func BindSupplier(id uint) (err error) {
	err = source.DB().Model(&model.Application{}).Where("id = ?", id).Update("pet_supplier_id", 0).Error
	return
}

type SupplierSource struct {
	source.Model
	SupplierID               uint   `json:"supplier_id"`
	Name                     string `json:"name"`
	SupplierSourceCategoryID uint   `json:"supplier_source_category_id"`
}

type SupplierSourceCategory struct {
	source.Model
	SupplierID uint   `json:"supplier_id"`
	Name       string `json:"name"`
}

func ExportApplicationOrdersSync(info request.ExportApplicationOrderInfo) (err error) {
	go ExportApplicationOrders(info)
	return
}
func ExportApplicationOrders(info request.ExportApplicationOrderInfo) (err error, link string) {
	// 创建db
	order := orderResponse.Order{}
	db := source.DB().Model(&order).Preload("OrderExpresss.OrderItems").Preload("OrderItems.AfterSales")
	var orders []orderResponse.Order
	// 如果有条件搜索 下方会自动创建搜索语句

	var exportLink string
	var application model.Application
	err = source.DB().First(&application, info.ApplicationID).Error
	if err != nil {
		return
	}
	exportLink = application.ExportAppLink

	db.Where("`application_id` = ?", info.ApplicationID)

	var timeType string
	timeType = "created_at"
	if info.TimeType != nil {
		switch *info.TimeType {
		case 0:
			timeType = "created_at"
			break
		case 1:
			timeType = "paid_at"
			break
		case 2:
			timeType = "sent_at"
			break
		case 3:
			timeType = "received_at"
			break
		default:
			timeType = "created_at"
			break
		}
	}
	if info.StartAT != "" {
		db.Where("`"+timeType+"` >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db.Where("`"+timeType+"` <= ?", info.EndAT)
	}
	var total int64
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	var productExportRecord model.AppOrderExportRecord

	//if exportLink == "" {
	//	//productExportRecord.OrderCount = total
	//	//productExportRecord.ApplicationID = info.ApplicationID
	//	//productExportRecord.Link = "请填写采购端订单信息接口地址"
	//	//productExportRecord.StatusString = "导出失败"
	//	//err = source.DB().Create(&productExportRecord).Error
	//	//return
	//} else {
	productExportRecord.OrderCount = total
	productExportRecord.StatusString = "导出中"
	productExportRecord.ApplicationID = info.ApplicationID
	err = source.DB().Create(&productExportRecord).Error
	if err != nil {
		return
	}
	//}

	excelPage := total/5000 + 1
	var links []string
	timeString := time.Now().Format("20060102150405")
	path := config.Config().Local.Path + "/export_order"
	for ei := 1; ei <= int(excelPage); ei++ {
		err = db.
			Preload("OrderItems.Sku").
			Preload("OrderItems.Product").
			Preload("OrderItems").
			Preload("PayInfo").
			Preload("User").
			Preload("Supplier").
			Preload("GatherSupply").
			Order("created_at DESC").Limit(5000).Offset(5000 * (ei - 1)).Find(&orders).Error
		if err != nil {
			return
		}
		var orderMap map[string]ApplicationOrderInfoMap
		if exportLink != "" {
			err, orderMap = GetThirdApplicationOrderInfo(orders, strings.Trim(exportLink, " "))
			if err != nil {
				productExportRecord.Link = err.Error()
				productExportRecord.StatusString = "导出失败"
				err = source.DB().Save(&productExportRecord).Error
				return
			}
		} else {
			orderMap = make(map[string]ApplicationOrderInfoMap)
		}
		f := excelize.NewFile()
		// 创建一个工作表
		index := f.NewSheet("Sheet1")
		// 设置单元格的值
		f.SetCellValue("Sheet1", "A1", "订单id")
		f.SetCellValue("Sheet1", "B1", "订单编号")
		f.SetCellValue("Sheet1", "C1", "三方单号")
		f.SetCellValue("Sheet1", "D1", "支付单号")
		f.SetCellValue("Sheet1", "E1", "子订单号")
		f.SetCellValue("Sheet1", "F1", "会员ID")
		f.SetCellValue("Sheet1", "G1", "粉丝昵称")
		f.SetCellValue("Sheet1", "H1", "收货人姓名")
		f.SetCellValue("Sheet1", "I1", "联系电话")
		f.SetCellValue("Sheet1", "J1", "省")
		f.SetCellValue("Sheet1", "K1", "市")
		f.SetCellValue("Sheet1", "L1", "区")
		f.SetCellValue("Sheet1", "M1", "收货地址")
		f.SetCellValue("Sheet1", "N1", "商品名称")
		f.SetCellValue("Sheet1", "O1", "规格条码/商品编号/商品条码")
		f.SetCellValue("Sheet1", "P1", "商品数量")
		f.SetCellValue("Sheet1", "Q1", "支付方式")
		f.SetCellValue("Sheet1", "R1", "技术服务费")
		f.SetCellValue("Sheet1", "S1", "商品单价")
		f.SetCellValue("Sheet1", "T1", "商品小计")
		f.SetCellValue("Sheet1", "U1", "运费")
		f.SetCellValue("Sheet1", "V1", "应收款")
		f.SetCellValue("Sheet1", "W1", "状态")
		f.SetCellValue("Sheet1", "X1", "下单时间")
		f.SetCellValue("Sheet1", "Y1", "付款时间")
		f.SetCellValue("Sheet1", "Z1", "发货时间")
		f.SetCellValue("Sheet1", "AA1", "完成时间")
		f.SetCellValue("Sheet1", "AB1", "快递公司")
		f.SetCellValue("Sheet1", "AC1", "快递单号")
		f.SetCellValue("Sheet1", "AD1", "订单备注")
		f.SetCellValue("Sheet1", "AE1", "用户备注")
		f.SetCellValue("Sheet1", "AF1", "附加")
		f.SetCellValue("Sheet1", "AG1", "供货金额")
		f.SetCellValue("Sheet1", "AH1", "供应链单号")
		f.SetCellValue("Sheet1", "AI1", "供应链名称")
		f.SetCellValue("Sheet1", "AJ1", "供应商名称")
		f.SetCellValue("Sheet1", "AK1", "商品来源")
		f.SetCellValue("Sheet1", "AL1", "来源分类")
		f.SetCellValue("Sheet1", "AM1", "开票名称")
		f.SetCellValue("Sheet1", "AN1", "税收分类编码")
		f.SetCellValue("Sheet1", "AO1", "采购税率")
		f.SetCellValue("Sheet1", "AP1", "供应单价")
		f.SetCellValue("Sheet1", "AQ1", "销售单价")
		f.SetCellValue("Sheet1", "AR1", "商品小计（采购端）")
		f.SetCellValue("Sheet1", "AS1", "应付价（采购端）")
		f.SetCellValue("Sheet1", "AT1", "支付方式（采购端）")
		f.SetCellValue("Sheet1", "AU1", "会员姓名（采购端）")
		f.SetCellValue("Sheet1", "AV1", "手机号（采购端）")
		f.SetCellValue("Sheet1", "AW1", "工号（采购端）")
		i := 2

		var supplierSource []SupplierSource
		var supplierSourceCategory []SupplierSourceCategory
		err = source.DB().Find(&supplierSource).Error
		if err != nil {
			return
		}
		err = source.DB().Find(&supplierSourceCategory).Error
		if err != nil {
			return
		}
		var supplierSourceMap = make(map[uint]SupplierSource)
		var supplierSourceCategoryMap = make(map[uint]SupplierSourceCategory)
		for _, sS := range supplierSource {
			supplierSourceMap[sS.ID] = sS
		}
		for _, sSc := range supplierSourceCategory {
			supplierSourceCategoryMap[sSc.ID] = sSc
		}

		for _, v := range orders {
			for _, item := range v.OrderItems {

				var wValue = "" //W位置的值
				//如果是代发货状态使用子订单的发货状态
				if v.Status == orderModel.WaitSend {
					wValue = orderModel.GetItemSendStatusName(item.SendStatus)
				} else {
					wValue = orderModel.GetStatusName(v.Status)
				}

				//售后状态
				if item.AfterSales.ID != 0 {
					wValue = "售后状态:" + item.AfterSales.StatusName
				}
				var companyName string
				var expressNo string
				//对应子订单与物流
				for _, orderExpressItem := range v.OrderExpresss {
					for _, orderExpressOrderItem := range orderExpressItem.OrderItems {
						if orderExpressOrderItem.ID == item.ID {
							expressNo = orderExpressItem.ExpressNo
							err, companyName = express2.GetCompanyByCode(orderExpressItem.CompanyCode)
							if err != nil {
								companyName = err.Error()
							}
						}
					}
				}

				var sentAt string
				if v.SentAt != nil {
					sentAt = v.SentAt.Format("2006-01-02 15:04:05")
				}
				var createAt string
				if v.CreatedAt != nil {
					createAt = v.CreatedAt.Format("2006-01-02 15:04:05")
				}
				var payAt string
				if v.PaidAt != nil {
					payAt = v.PaidAt.Format("2006-01-02 15:04:05")
				}
				var receivedAt string
				if v.ReceivedAt != nil {
					receivedAt = v.ReceivedAt.Format("2006-01-02 15:04:05")
				}
				var technicalServicesFee decimal.Decimal
				if v.ItemAmount-v.RefundAmount != 0 {
					technicalServicesFee = decimal.NewFromInt(int64(item.Amount * v.TechnicalServicesFee)).Div(decimal.NewFromInt(int64(v.ItemAmount - v.RefundAmount)))
				}
				var skuPrice decimal.Decimal
				if item.Qty != 0 {
					skuPrice = decimal.NewFromInt(int64(item.Amount)).Div(decimal.NewFromInt(int64(item.Qty))).DivRound(decimal.NewFromInt(100), 2)
				} else {
					skuPrice = decimal.NewFromInt(0)
				}

				var supplierSourceName string
				var supplierSourceCategoryName string
				if _, ok := supplierSourceMap[item.Product.SupplierSourceID]; !ok {
					supplierSourceName = ""
				} else {
					supplierSourceName = supplierSourceMap[item.Product.SupplierSourceID].Name
				}
				if _, ok := supplierSourceCategoryMap[item.Product.SupplierSourceCategoryID]; !ok {
					supplierSourceCategoryName = ""
				} else {
					supplierSourceCategoryName = supplierSourceCategoryMap[item.Product.SupplierSourceCategoryID].Name
				}
				var taxProductName, taxCode, taxRate string
				if item.Product.BillPosition == 1 {
					taxProductName = item.Product.TaxProductName
					taxCode = item.Product.TaxCode
					taxRate = strconv.Itoa(item.Product.TaxRate)
				} else {
					taxProductName = item.Sku.TaxProductName
					taxCode = item.Sku.TaxCode
					taxRate = strconv.Itoa(item.Sku.TaxRate)
				}
				var detail ApplicationOrderInfoMap
				if _, ok := orderMap[v.ThirdOrderSN+strconv.Itoa(int(item.SkuID))]; ok {
					detail = orderMap[v.ThirdOrderSN+strconv.Itoa(int(item.SkuID))]
				} else {
					detail = ApplicationOrderInfoMap{}
				}

				f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), v.ID)
				f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), strconv.Itoa(int(v.OrderSN)))
				f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), v.ThirdOrderSN)
				f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), v.PayInfo.PaySn)
				f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), item.ID) //子订单号
				f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), v.User.ID)
				f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), v.User.NickName)
				f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), v.ShippingAddress.Realname)
				f.SetCellValue("Sheet1", "I"+strconv.Itoa(i), v.ShippingAddress.Mobile)
				f.SetCellValue("Sheet1", "J"+strconv.Itoa(i), v.ShippingAddress.Province)
				f.SetCellValue("Sheet1", "K"+strconv.Itoa(i), v.ShippingAddress.City)
				f.SetCellValue("Sheet1", "L"+strconv.Itoa(i), v.ShippingAddress.County)
				f.SetCellValue("Sheet1", "M"+strconv.Itoa(i), v.ShippingAddress.Province+v.ShippingAddress.City+v.ShippingAddress.County+v.ShippingAddress.Town+v.ShippingAddress.Detail)
				f.SetCellValue("Sheet1", "N"+strconv.Itoa(i), item.Title+"["+item.SkuTitle+"]")
				f.SetCellValue("Sheet1", "O"+strconv.Itoa(i), "["+item.Sku.Sn+"]/["+item.Product.Sn+"]/["+item.Product.Barcode+"]")
				f.SetCellValue("Sheet1", "P"+strconv.Itoa(i), strconv.Itoa(int(item.Qty)))
				f.SetCellValue("Sheet1", "Q"+strconv.Itoa(i), v.PayType)
				f.SetCellValue("Sheet1", "R"+strconv.Itoa(i), technicalServicesFee.DivRound(decimal.NewFromInt(100), 2))
				f.SetCellValue("Sheet1", "S"+strconv.Itoa(i), skuPrice)
				f.SetCellValue("Sheet1", "T"+strconv.Itoa(i), float64(item.Amount)/100)
				f.SetCellValue("Sheet1", "U"+strconv.Itoa(i), float64(v.Freight)/100)
				f.SetCellValue("Sheet1", "V"+strconv.Itoa(i), decimal.NewFromInt(int64(item.Amount)).Add(technicalServicesFee).DivRound(decimal.NewFromInt(100), 2))
				f.SetCellValue("Sheet1", "W"+strconv.Itoa(i), wValue)
				f.SetCellValue("Sheet1", "X"+strconv.Itoa(i), createAt)
				f.SetCellValue("Sheet1", "Y"+strconv.Itoa(i), payAt)
				f.SetCellValue("Sheet1", "Z"+strconv.Itoa(i), sentAt)
				f.SetCellValue("Sheet1", "AA"+strconv.Itoa(i), receivedAt)
				f.SetCellValue("Sheet1", "AB"+strconv.Itoa(i), companyName)
				f.SetCellValue("Sheet1", "AC"+strconv.Itoa(i), expressNo)
				f.SetCellValue("Sheet1", "AD"+strconv.Itoa(i), v.Note)
				f.SetCellValue("Sheet1", "AE"+strconv.Itoa(i), v.Remark)
				f.SetCellValue("Sheet1", "AF"+strconv.Itoa(i), "")
				f.SetCellValue("Sheet1", "AG"+strconv.Itoa(i), float64(item.SupplyAmount)/100)
				f.SetCellValue("Sheet1", "AH"+strconv.Itoa(i), v.GatherSupplySN)
				f.SetCellValue("Sheet1", "AI"+strconv.Itoa(i), v.GatherSupply.Name)
				f.SetCellValue("Sheet1", "AJ"+strconv.Itoa(i), v.Supplier.Name)
				f.SetCellValue("Sheet1", "AK"+strconv.Itoa(i), supplierSourceName)
				f.SetCellValue("Sheet1", "AL"+strconv.Itoa(i), supplierSourceCategoryName)
				f.SetCellValue("Sheet1", "AM"+strconv.Itoa(i), taxProductName)
				f.SetCellValue("Sheet1", "AN"+strconv.Itoa(i), taxCode)
				f.SetCellValue("Sheet1", "AO"+strconv.Itoa(i), taxRate)
				f.SetCellValue("Sheet1", "AP"+strconv.Itoa(i), float64(item.Sku.CostPrice)/100)
				f.SetCellValue("Sheet1", "AQ"+strconv.Itoa(i), float64(item.Sku.GuidePrice)/100)
				f.SetCellValue("Sheet1", "AR"+strconv.Itoa(i), detail.GoodsPrice)
				f.SetCellValue("Sheet1", "AS"+strconv.Itoa(i), detail.Price)
				f.SetCellValue("Sheet1", "AT"+strconv.Itoa(i), detail.PayName)
				f.SetCellValue("Sheet1", "AU"+strconv.Itoa(i), detail.Username)
				f.SetCellValue("Sheet1", "AV"+strconv.Itoa(i), detail.Mobile)
				f.SetCellValue("Sheet1", "AW"+strconv.Itoa(i), detail.Nickname)
				i++
			}
		}
		// 设置工作簿的默认工作表
		f.SetActiveSheet(index)
		// 根据指定路径保存文件
		//year, month, day := time.Now().Format("2006-01-02 15:04:05")
		exist, _ := utils.PathExists(path)

		if !exist {
			// 创建文件夹
			err = os.Mkdir(path, os.ModePerm)
			if err != nil {
				fmt.Printf("mkdir failed![%v]\n", err)
			} else {
				fmt.Printf("mkdir success!\n")
			}
		}
		link = path + "/" + timeString + "-" + strconv.Itoa(ei) + "订单导出-采购端id(" + strconv.Itoa(int(info.ApplicationID)) + ").xlsx"
		if err = f.SaveAs(link); err != nil {
			return
		}
		links = append(links, link)

	}

	if excelPage > 1 {
		link = path + "/" + timeString + "订单导出-采购端id(" + strconv.Itoa(int(info.ApplicationID)) + ").zip"
		err = Zip(link, links)
	}
	productExportRecord.Link = link
	productExportRecord.StatusString = "导出完成"
	err = source.DB().Save(&productExportRecord).Error
	if err != nil {
		return
	}
	err = service2.PublishNotify("采购端订单导出完成", "共导出了"+strconv.Itoa(int(total))+"件商品")

	return err, link
}

type ApplicationOrderInfoResponse struct {
	Result int    `json:"result"`
	Msg    string `json:"msg"`
	Data   []struct {
		Price      string `json:"price"`
		GoodsPrice string `json:"goods_price"`
		GoodsTotal int    `json:"goods_total"`
		StatusName string `json:"status_name"`
		PayName    string `json:"pay_name"`
		Nickname   string `json:"nickname"`
		Mobile     string `json:"mobile"`
		Username   string `json:"username"`
		Realname   string `json:"realname"`
		OrderSn    string `json:"order_sn"`
		OrderItem  []struct {
			Total      int    `json:"total"`
			Title      string `json:"title"`
			GoodsPrice string `json:"goods_price"`
			Price      string `json:"price"`
			YzOptionId int    `json:"yz_option_id"`
		} `json:"order_item"`
	} `json:"data"`
}
type ApplicationOrderInfoMap struct {
	StatusName string `json:"status_name" form:"status_name"`
	PayName    string `json:"pay_name" form:"pay_name"`
	Nickname   string `json:"nickname" form:"nickname"`
	Mobile     string `json:"mobile" form:"mobile"`
	Username   string `json:"username" form:"username"`
	Title      string `json:"title" form:"title"`
	GoodsPrice string `json:"goods_price" form:"goods_price"`
	Price      string `json:"price" form:"price"`
	YzOptionId int    `json:"yz_option_id" form:"yz_option_id"`
}

func GetThirdApplicationOrderInfo(orders []orderResponse.Order, link string) (err error, orderMap map[string]ApplicationOrderInfoMap) {
	var orderSns []string
	var head = make(map[string]string)
	head["Content-Type"] = "application/json"
	var data = make(map[string]ApplicationOrderInfoMap)
	for _, order := range orders {
		orderSns = append(orderSns, order.ThirdOrderSN)
		if len(orderSns) == 100 {
			var result []byte
			var requestParams = make(map[string]interface{})
			requestParams["orderSns"] = orderSns

			err, result = utils.Post(link, requestParams, head)
			if err != nil {
				return
			}
			var responsePara ApplicationOrderInfoResponse
			err = json.Unmarshal(result, &responsePara)
			if err != nil {
				return
			}
			if responsePara.Result != 1 {
				err = errors.New(responsePara.Msg)
				return
			}
			for _, v := range responsePara.Data {
				for _, item := range v.OrderItem {
					data[v.OrderSn+strconv.Itoa(item.YzOptionId)] = ApplicationOrderInfoMap{
						Username:   v.Username,
						Nickname:   v.Nickname,
						Mobile:     v.Mobile,
						Price:      item.Price,
						GoodsPrice: item.GoodsPrice,
						PayName:    v.PayName,
					}
				}
			}
			orderSns = []string{}
		}
	}
	if len(orderSns) > 0 {
		var result []byte
		var requestParams = make(map[string]interface{})
		requestParams["orderSns"] = orderSns

		err, result = utils.Post(link, requestParams, head)
		if err != nil {
			return
		}
		var responsePara ApplicationOrderInfoResponse
		err = json.Unmarshal(result, &responsePara)
		if err != nil {
			return
		}
		if responsePara.Result != 1 {
			err = errors.New(responsePara.Msg)
			return
		}
		for _, v := range responsePara.Data {
			for _, item := range v.OrderItem {
				data[v.OrderSn+strconv.Itoa(item.YzOptionId)] = ApplicationOrderInfoMap{
					Username:   v.Username,
					Nickname:   v.Nickname,
					Mobile:     v.Mobile,
					Price:      item.Price,
					GoodsPrice: item.GoodsPrice,
					PayName:    v.PayName,
				}
			}
		}
	}

	return err, data
}
func Zip(dest string, paths []string) error {
	zfile, err := os.Create(dest)
	if err != nil {
		return err
	}
	defer zfile.Close()
	zipWriter := zip.NewWriter(zfile)
	defer zipWriter.Close()
	for _, src := range paths {
		// remove the trailing path sepeartor if it is a directory
		src := strings.TrimSuffix(src, string(os.PathSeparator))
		err = filepath.Walk(src, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}
			// create local file header
			header, err := zip.FileInfoHeader(info)
			if err != nil {
				return err
			}
			// set compression method to deflate
			header.Method = zip.Deflate
			// set relative path of file in zip archive
			header.Name, err = filepath.Rel(filepath.Dir(src), path)
			if err != nil {
				return err
			}
			if info.IsDir() {
				header.Name += string(os.PathSeparator)
			}
			// create writer for writing header
			headerWriter, err := zipWriter.CreateHeader(header)
			if err != nil {
				return err
			}
			if info.IsDir() {
				return nil
			}
			f, err := os.Open(path)
			if err != nil {
				return err
			}
			defer f.Close()
			_, err = io.Copy(headerWriter, f)
			return err
		})
		if err != nil {
			return err
		}
	}
	return nil
}

func GetOrderExportRecordList(info request.AppOrderExportRecordRequest) (err error, total int64, list interface{}) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Preload("Application").Model(&model.AppOrderExportRecord{})
	var applications []model.AppOrderExportRecord
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.StatusString != "" {
		db = db.Where("`status_string` LIKE ?", "%"+info.StatusString+"%")
	}

	err = db.Count(&total).Error
	err = db.Order("created_at desc").Limit(limit).Offset(offset).Find(&applications).Error

	return err, total, applications
}

func DeleteApplicationExport(application model.AppOrderExportRecord) (err error) {
	err = source.DB().Delete(&application).Error
	return err
}

func CreateApplicationPetSupplier(list []model.ApplicationPetSupplier) (err error) {
	if len(list) > 0 {
		err = source.DB().Save(list).Error
		if err != nil {
			return
		}
	}

	return
}

func DeleteApplicationPetSupplier(ids []uint) (err error) {
	var collectionProduct model.ApplicationPetSupplier
	err = source.DB().Where("id in ?", ids).First(&collectionProduct).Error
	if err != nil {
		return
	}
	err = source.DB().Unscoped().Delete(&[]model.ApplicationPetSupplier{}, "id in ?", ids).Error
	if err != nil {
		return
	}
	return err
}

func GetApplicationPetSupplierList(info request.ApplicationPetSupplierSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.ApplicationPetSupplier{})
	var collections []model.ApplicationPetSupplier
	// 如果有条件搜索 下方会自动创建搜索语句
	db.Where("application_id = ?", info.Id)

	err = db.Count(&total).Error
	err = db.Preload("PetSupplier").Limit(limit).Offset(offset).Find(&collections).Error

	return err, collections, total
}

type ExportStruct struct {
	Search   request.ApplicationSearch `json:"search"`
	Total    int64                     `json:"total"`
	ExportDb *gorm.DB                  `json:"exportDb"`
}

func Export(search request.ApplicationSearch) (err error, link string) {
	var exportStruct ExportStruct
	exportStruct.Search = search
	// 创建db
	exportStruct.ExportDb = source.DB().Preload("DaHangErpItem").Preload("ApplicationLevel").Preload("PetSupplier").Preload("Order", "orders.status > 0").Preload("User.Balance").Preload("User.GoinBalance").Model(&response.ApplicationAdminList{})
	// 如果有条件搜索 下方会自动创建搜索语句
	err = exportStruct.ExportSearch()
	if err != nil {
		return
	}
	err = exportStruct.ExportDb.Count(&exportStruct.Total).Error
	if err != nil {
		err = errors.New("获取导出数量失败" + err.Error())
		return
	}
	if exportStruct.Total == 0 {
		err = errors.New("没有需要导出的数据" + err.Error())
		return
	}
	err, link = exportStruct.ExportStart()
	return
}

func (exportStruct ExportStruct) ExportStart() (err error, link string) {
	var applications []response.ApplicationAdminList
	err = exportStruct.ExportDb.Order("created_at desc").Preload("GoinBalance", "type = 1").Preload("Balance", "type = 2").Find(&applications).Error
	if err != nil {
		log.Log().Error("采购端导出数据错误", zap.Any("search", exportStruct.Search), zap.Any("err", err))
		err = errors.New("导出数据获取错误" + err.Error())
		return
	}

	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")
	// 设置单元格的值
	f.SetCellValue("Sheet1", "A1", "采购端id")
	f.SetCellValue("Sheet1", "B1", "创建时间")
	f.SetCellValue("Sheet1", "C1", "会员/手机号")

	f.SetCellValue("Sheet1", "D1", "汇聚余额")
	f.SetCellValue("Sheet1", "E1", "站内余额")
	f.SetCellValue("Sheet1", "F1", "订单数量")
	f.SetCellValue("Sheet1", "G1", "订单金额")
	f.SetCellValue("Sheet1", "H1", "公司名称")
	f.SetCellValue("Sheet1", "I1", "采购端名称")
	f.SetCellValue("Sheet1", "J1", "采购端等级")
	f.SetCellValue("Sheet1", "K1", "供应商名称")
	f.SetCellValue("Sheet1", "L1", "禁用状态")
	f.SetCellValue("Sheet1", "M1", "回调地址")

	f.SetCellValue("Sheet1", "N1", "采购端订单信息地址")

	f.SetCellValue("Sheet1", "O1", "详细地址")
	f.SetCellValue("Sheet1", "P1", "信用代码")
	f.SetCellValue("Sheet1", "Q1", "法人姓名")
	f.SetCellValue("Sheet1", "R1", "法人身份证号码")
	f.SetCellValue("Sheet1", "S1", "联系人姓名")
	f.SetCellValue("Sheet1", "T1", "联系人电话")
	f.SetCellValue("Sheet1", "U1", "联系人邮箱")

	f.SetColWidth("sheet1", "B", "C", 20)
	f.SetColWidth("sheet1", "H", "I", 20)
	f.SetColWidth("sheet1", "M", "N", 20)
	f.SetColWidth("sheet1", "O", "U", 15)

	i := 2
	for _, application := range applications {
		var statusName string
		if application.Banlist == 1 {
			statusName = "禁用"
		} else {
			statusName = "启用"
		}
		f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), application.ID)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), application.CreatedAt.Format("2006-01-02 15:04:05"))
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), application.User.NickName+"/"+application.User.Username)
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), Fen2yuan(application.Balance.SettlementBalance))
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), Fen2yuan(application.Balance.PurchasingBalance))
		f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), application.OrderCount)
		f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), Fen2yuan(uint(application.OrderAmount)))
		f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), application.CompanyName)
		f.SetCellValue("Sheet1", "I"+strconv.Itoa(i), application.AppName)
		f.SetCellValue("Sheet1", "J"+strconv.Itoa(i), application.ApplicationLevel.LevelName)
		f.SetCellValue("Sheet1", "K"+strconv.Itoa(i), application.PetSupplier.ShopName)
		f.SetCellValue("Sheet1", "L"+strconv.Itoa(i), statusName)
		f.SetCellValue("Sheet1", "M"+strconv.Itoa(i), application.CallBackLink)
		f.SetCellValue("Sheet1", "N"+strconv.Itoa(i), application.ExportAppLink)
		f.SetCellValue("Sheet1", "O"+strconv.Itoa(i), application.Address)
		f.SetCellValue("Sheet1", "P"+strconv.Itoa(i), application.CreditCode)
		f.SetCellValue("Sheet1", "Q"+strconv.Itoa(i), application.LegalPersonName)
		f.SetCellValue("Sheet1", "R"+strconv.Itoa(i), application.IdCardNumber)
		f.SetCellValue("Sheet1", "S"+strconv.Itoa(i), application.ContactsName)
		f.SetCellValue("Sheet1", "T"+strconv.Itoa(i), application.ContactsPhontnumber)
		f.SetCellValue("Sheet1", "U"+strconv.Itoa(i), application.ContactsEmail)
		i++
	}
	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)
	// 根据指定路径保存文件
	path := config.Config().Local.Path + "/plugin_export_application"
	exist, _ := utils.PathExists(path)
	if !exist {
		// 创建文件夹
		err = os.Mkdir(path, os.ModePerm)
		if err != nil {
			fmt.Printf("mkdir failed![%v]\n", err)
		} else {
			fmt.Printf("mkdir success!\n")
		}
	}
	timeString := time.Now().Format("20060102150405")

	link = path + "/" + timeString + "采购端导出.xlsx"
	if err = f.SaveAs(link); err != nil {
		return
	}
	return
}

// 分转元
func Fen2yuan(price uint) (priceString string) {
	var res float64
	if price > 0 {
		d := decimal.New(1, 2)
		res, _ = decimal.NewFromInt(int64(price)).DivRound(d, 2).Float64()
	} else {
		res = 0
	}

	return strconv.FormatFloat(res, 'f', 2, 64)
}
func (exportStruct ExportStruct) ExportSearch() (err error) {
	if exportStruct.Search.CompanyName != "" {
		exportStruct.ExportDb = exportStruct.ExportDb.Where("`company_name` LIKE ?", "%"+exportStruct.Search.CompanyName+"%")
	}
	if exportStruct.Search.AppName != "" {
		exportStruct.ExportDb = exportStruct.ExportDb.Where("`app_name` LIKE ?", "%"+exportStruct.Search.AppName+"%")
	}
	if exportStruct.Search.ProvinceId != 0 {
		exportStruct.ExportDb = exportStruct.ExportDb.Where("`province_id` = ?", exportStruct.Search.ProvinceId)
	}
	if exportStruct.Search.CityId != 0 {
		exportStruct.ExportDb = exportStruct.ExportDb.Where("`city_id` = ?", exportStruct.Search.CityId)
	}
	if exportStruct.Search.GroupID != 0 {
		exportStruct.ExportDb = exportStruct.ExportDb.Where("`group_id` = ?", exportStruct.Search.GroupID)
	}
	if exportStruct.Search.DistrictId != 0 {
		exportStruct.ExportDb = exportStruct.ExportDb.Where("`district_id` = ?", exportStruct.Search.DistrictId)
	}
	if exportStruct.Search.LegalPersonName != "" {
		exportStruct.ExportDb = exportStruct.ExportDb.Where("`legal_person_name` LIKE ?", "%"+exportStruct.Search.LegalPersonName+"%")
	}
	if exportStruct.Search.AppLevelID != 0 {
		exportStruct.ExportDb = exportStruct.ExportDb.Where("`app_level_id` = ?", exportStruct.Search.AppLevelID)
	}

	if exportStruct.Search.PetSupplierID != nil {
		var applicationSetting model.ApplicationSetting
		err, applicationSetting = GetApplicationSetting()
		if err != nil {
			err = errors.New("请设置采购端设置" + err.Error())
			return
		}
		if applicationSetting.Value.MultiPetSupplier == 1 {
			var appIDs []uint
			err = source.DB().Model(&model.ApplicationPetSupplier{}).Where("pet_supplier_id = ?", exportStruct.Search.PetSupplierID).Pluck("application_id", &appIDs).Error
			if err != nil {
				err = errors.New("没有采购端绑定这个供应商" + err.Error())
				return
			}
			if len(appIDs) == 0 {
				err = errors.New("没有采购端绑定这个供应商")
				return
			}
			if len(appIDs) > 0 {
				exportStruct.ExportDb = exportStruct.ExportDb.Where("`id` in ?", appIDs)
			}

		} else {
			exportStruct.ExportDb = exportStruct.ExportDb.Where("`pet_supplier_id` = ?", &exportStruct.Search.PetSupplierID)

		}
	}
	if exportStruct.Search.MemberName != "" {
		var userIds []uint
		err = source.DB().Model(&model2.User{}).Where("username like ?", "%"+exportStruct.Search.MemberName+"%").Or("nick_name like ?", "%"+exportStruct.Search.MemberName+"%").Pluck("id", &userIds).Error
		if len(userIds) == 0 {
			err = errors.New("不存在的会员名称")
			return
		}
		exportStruct.ExportDb = exportStruct.ExportDb.Where("`member_id` in ?", userIds)
	}
	if exportStruct.Search.MemberUserName != "" {
		var userIds []uint
		err = source.DB().Model(&model2.User{}).Where("username like ?", "%"+exportStruct.Search.MemberUserName+"%").Pluck("id", &userIds).Error
		if len(userIds) == 0 {
			err = errors.New("不存在的会员手机号")
			return
		}
		exportStruct.ExportDb = exportStruct.ExportDb.Where("`member_id` in ?", userIds)
	}
	if exportStruct.Search.APPID != nil {
		exportStruct.ExportDb = exportStruct.ExportDb.Where("`id` = ?", &exportStruct.Search.APPID)
	}
	return
}
