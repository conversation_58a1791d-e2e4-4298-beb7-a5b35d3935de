package service

import (
	"application/middleware"
	model2 "application/model"
	nmq "application/notify-mq"
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"product/mq"
	"product/service"
	"strconv"
	"strings"
	"sync"
	"time"
	model3 "user/model"
	"yz-go/common_data"
	"yz-go/component/log"
	"yz-go/source"

	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"
)

func CreateDocument(appID uint, productID uint, document interface{}) (err error) {
	es, err := source.ES()
	if err != nil {
		return err
	}
	bulkRequest := es.Bulk().Index("product" + common_data.GetOldProductIndex())
	doc := elastic.NewBulkIndexRequest().Id(strconv.Itoa(int(productID))).Doc(document)
	esClient := bulkRequest.Add(doc)
	_, err = esClient.Do(context.Background())
	if err != nil {
		LogInfo(appID, productID, "执行es创建文档操作出错", err)
	}
	return
}
func EsSave(product service.ProductSync) (err error) {
	//为本地创建文档
	var productElasticSearch service.ProductElasticSearch
	var userLevel []model3.UserLevel
	var level = make(map[int]int)
	err = source.DB().Order("level asc").Find(&userLevel).Error
	if err != nil {
		return
	}
	for k, l := range userLevel {
		level[k] = l.Discount
	}
	err, productElasticSearch = service.HandleData(product)
	if err != nil {
		LogInfo(0, product.ID, "数据格式出现错误", err)
		return
	}
	err = CreateDocument(0, product.ID, productElasticSearch)
	if err != nil {
		LogInfo(0, product.ID, "新建产品出现错误", err)
		return
	}

	return
}
func LogInfo(appID uint, productID uint, messageTitle string, err error) {
	fmt.Println(messageTitle + err.Error())
	log.Log().Info("appID("+strconv.Itoa(int(appID))+")productID("+strconv.Itoa(int(productID))+")"+messageTitle+err.Error(), zap.Any("err", err))
}
func UpdateES(product service.ProductSync) (err error) {
	var data map[string]interface{}
	es, err := source.ES()
	if err != nil {
		log.Log().Info("EsUpdateError()", zap.Any("product", product))
		return
	}
	_, err = es.Get().Index("product" + common_data.GetOldProductIndex()).Id(strconv.Itoa(int(product.ID))).Do(context.Background())
	if err != nil {
		var productElasticSearch service.ProductElasticSearch
		err, productElasticSearch = service.HandleData(product)
		if err != nil {
			LogInfo(0, product.ID, "新建产品消费者出现错误", err)

			return
		}
		err = CreateDocument(0, product.ID, productElasticSearch)
		if err != nil {
			LogInfo(0, product.ID, "新建产品消费者出现错误", err)

			return
		}
	} else {
		err, data = service.HandleUpdateData(product)
		if err != nil {
			LogInfo(0, product.ID, "格式化产品数据", err)
			return
		}
		var res *elastic.UpdateResponse
		res, err = es.Update().
			Index("product" + common_data.GetOldProductIndex()).
			Id(strconv.Itoa(int(product.ID))).
			Doc(data).
			Do(context.Background())
		//log.Log().Info("结果", zap.Any("err", res))
		if err != nil {
			LogInfo(0, product.ID, "编辑产品出现错误", err)
			return
		}
		if res.Result != "updated" {
			err = errors.New(res.Result)
			LogInfo(0, product.ID, "编辑产品出现错误", err)
			return
		}
	}

	return nil
}

func EsUpdate(product service.ProductSync, messageType mq.ProductMessageType, level int, isStock int) (err error) {
	//log.Log().Error("EsUpdate()", zap.Any("product", product))
	var userLevel []model3.UserLevel
	var levels = make(map[int]int)
	err = source.DB().Order("level asc").Find(&userLevel).Error
	if err != nil {
		return
	}
	for k, l := range userLevel {
		levels[k] = l.Discount
	}
	//修改本地文档
	var data map[string]interface{}
	es, err := source.ES()
	if err != nil {
		return
	}
	_, err = es.Get().Index("product" + common_data.GetOldProductIndex()).Id(strconv.Itoa(int(product.ID))).Do(context.Background())
	if err != nil {
		var productElasticSearch service.ProductElasticSearch
		err, productElasticSearch = service.HandleData(product)
		if err != nil {
			LogInfo(0, product.ID, "新建产品消费者出现错误", err)

			return
		}
		err = CreateDocument(0, product.ID, productElasticSearch)
		if err != nil {
			LogInfo(0, product.ID, "新建产品消费者出现错误", err)

			return
		}
	} else {
		err, data = service.HandleUpdateData(product)
		if err != nil {
			LogInfo(0, product.ID, "格式化产品数据出现错误", err)
			return
		}
		var res *elastic.UpdateResponse
		res, err = es.Update().
			Index("product" + common_data.GetOldProductIndex()).
			Id(strconv.Itoa(int(product.ID))).
			Doc(data).
			Do(context.Background())
		//log.Log().Info("结果", zap.Any("err", res))
		if err != nil {
			LogInfo(0, product.ID, "编辑产品出现错误", err)
			return
		}
		if res.Result != "updated" {
			err = errors.New(res.Result)
			LogInfo(0, product.ID, "编辑产品出现错误", err)
			return
		}
	}

	// 2. 处理通知
	return nmq.PublishMessage(product.ID, messageType, level, isStock)
}
func EsUpdateOnly(product service.ProductSync) (err error) {
	// 1. 处理 ES 更新
	return UpdateES(product)
}

//	func EsUpdate(product service.ProductSync, messageType mq.ProductMessageType, level int, isStock int) (err error) {
//		// 1. 处理 ES 更新
//		if err = UpdateES(product); err != nil {
//			return err
//		}
//
//		// 2. 处理通知
//		return SendNotification(product, messageType, level, isStock)
//	}
func InIntSlice(haystack []uint, needle uint) bool {
	for _, e := range haystack {
		if e == needle {
			return true
		}
	}

	return false
}

// DeleteES 处理ES删除操作
func DeleteES(product service.ProductSync) error {
	es, err := source.ES()
	if err != nil {
		return err
	}

	res, err := es.Delete().
		Index("product" + common_data.GetOldProductIndex()).
		Id(strconv.Itoa(int(product.ID))).
		Do(context.Background())

	if err != nil {
		LogInfo(0, product.ID, "删除本地产品出现错误", err)
		return err
	}

	fmt.Printf("delete local result %s\n", res.Result)
	return nil
}

func EsDelete(product service.ProductSync, level int) (err error) {
	// 1. 处理 ES 删除
	if err = DeleteES(product); err != nil {
		return err
	}

	// 2. 处理删除通知
	return nmq.PublishMessage(product.ID, mq.Delete, level, 0)

}

func CheckWhiteUrl(url string) (err error) {
	var messagePoolCheckUrl string
	if strings.Contains(url, "plugin.yz-supply.frontend.index.index") == true {
		messagePoolCheckUrl = strings.Replace(url, "plugin.yz-supply.frontend.index.index", "plugin.yz-supply.frontend.index.index.getAvailable", 1)
	}
	if strings.Contains(url, "/api/gatherSupply/notificationCallBack") == true {
		messagePoolCheckUrl = strings.Replace(url, "/api/gatherSupply/notificationCallBack", "/app/application/getAvailable", 1)
	}
	if messagePoolCheckUrl != "" {
		var result RespMessage
		err, result = postMessagePool(messagePoolCheckUrl, nil, nil)
		if err != nil {
			return
		}
		var code = 0

		if result.Code == nil || *result.Code != code {
			return errors.New("验证失败")
		}
	} else {
		return errors.New("验证失败")
	}
	return
}
func Connection(product mq.ProductMessage, url string, wg *sync.WaitGroup) {
	//log.Log().Error("Connection()", zap.Any("product", product), zap.Any("url", url))
	defer wg.Done()
	header := map[string]string{
		"Content-Type": "application/json",
	}

	var messageId string
	messageId = "product" + strconv.Itoa(int(product.ProductID)) + string(product.MessageType) + strconv.Itoa(int(time.Now().Unix()))
	product.MessageID = "self" + base64.StdEncoding.EncodeToString([]byte(messageId))
	var respon Resp
	jsonBodyData, _ := json.Marshal(product)
	header = middleware.SignMessage(string(jsonBodyData), product.MemberSign, map[string]string{}, header)

	var err error
	err, respon = post(url, product, header)
	//responData, err := json.Marshal(respon)
	//fmt.Println("与商城通信失败：", string(responData))

	if err != nil {
		fmt.Println("与商城通信失败：", err)
		log.Log().Info("与商城通信失败2!", zap.Any("err", err), zap.Any("data", product))
	} else {
		if respon.Code == 0 || respon.Result == 1 {
			fmt.Println("与商城通信成功：", product, url)
			if product.ProductID == 253425 {
				log.Log().Info("推送消息成功!", zap.Any("data", product))
			}
			return
		} else {
			fmt.Println("推送消息失败："+url, respon)
			err = errors.New(respon.Msg)
			log.Log().Info("推送消息失败!"+url, zap.Any("err", respon), zap.Any("data", product))
			return
		}
	}
	//if shopProduct.Source != 100 {
	//	var errString = err.Error()
	//	product.Level++
	//	if product.Level > 5 {
	//		fmt.Println("重新推送次数大于5次，停止推送：", product)
	//		log.Log().Info("重新推送次数大于5次，停止推送：", zap.Any("info", product))
	//		return
	//	}
	//	var errMessage model2.PushMessageErr
	//	var jsonData []byte
	//	jsonData, err = json.Marshal(product)
	//	errMessage.JsonData = string(jsonData)
	//	errMessage.Url = url
	//	errMessage.Type = 1
	//	errMessage.ReSendTime = &source.LocalTime{Time: GetNextTime(product.Level)}
	//	errMessage.Err = errString
	//	err = source.DB().Create(&errMessage).Error
	//	if err != nil {
	//		fmt.Println("重新推送消息入库失败：", err)
	//		log.Log().Info("重新推送消息入库失败：", zap.Any("err", err))
	//		return
	//	}
	//}

	return
}

// todo 挪走
func DistributorSync(product service.ProductSync, level int) (err error) {
	var applications []model2.Application
	err = source.DB().Find(&applications).Error
	if err != nil {
		LogInfo(0, product.ID, "查找采购端列表出现错误", err)
		return err
	}
	var appIds []uint
	for _, storage := range product.Storages {
		appIds = append(appIds, storage.AppID)
	}
	for _, v := range applications {
		if v.CallBackLinkValidity != 0 && v.AppLevelID != 0 && v.MemberId != 0 {
			if InIntSlice(appIds, v.ID) {
				err = DistributorConnection(mq.ProductMessage{ProductID: product.ID, MessageType: mq.DistributorSync, MemberSign: v.AppSecret, Level: level}, v.CallBackLink)
				if err != nil {
					LogInfo(v.ID, product.ID, "删除产品通信出现错误", err)
				}
			}
		}
	}
	return
}

// todo 挪走
func DistributorConnection(product mq.ProductMessage, url string) (err error) {
	header := map[string]string{
		"Content-Type": "application/json",
	}

	var messageId string
	messageId = "product" + strconv.Itoa(int(product.ProductID)) + string(product.MessageType) + strconv.Itoa(int(time.Now().Unix()))
	product.MessageID = "self" + base64.StdEncoding.EncodeToString([]byte(messageId))
	var respon Resp
	jsonBodyData, _ := json.Marshal(product)
	header = middleware.SignMessage(string(jsonBodyData), product.MemberSign, map[string]string{}, header)

	err, respon = post(url, product, header)
	//responData, _ := json.Marshal(respon)
	//fmt.Println("与商城通信失败：", string(responData))

	if err != nil {
		fmt.Println("与商城通信失败：", err)
		//log.Log().Info("与商城通信失败2!", zap.Any("err", err), zap.Any("data", product))
	} else {
		if respon.Code == 0 || respon.Result == 1 {
			fmt.Println("与商城通信成功：", product, url)
			return
		} else {
			fmt.Println("推送消息失败："+url, respon)
			//log.Log().Info("推送消息失败!"+url, zap.Any("err", respon), zap.Any("data", product))
			err = errors.New(respon.Msg)
		}
	}
	return
}

func GetNextTime(level int) (sendTime time.Time) {
	var nowTime = time.Now().Unix()
	switch level {
	case 1:
		//5分钟
		nowTime += 300
		break
	case 2:
		//30分钟
		nowTime += 1800
		break
	case 3:
		//1小时
		nowTime += 3600
		break
	case 4:
		//12小时
		nowTime += 43200
		break
	case 5:
		//24小时
		nowTime += 86400
		break
	default:
		//5分钟
		nowTime += 300
		break
	}
	sendTime = time.Unix(nowTime, 0)
	return
}

type Resp struct {
	Code   int    `json:"code"`
	Result int    `json:"result"`
	Msg    string `json:"msg"`
}

type RespMessage struct {
	Code   *int   `json:"code"`
	Result int    `json:"result"`
	Msg    string `json:"msg"`
}

// 发送POST请求
// url：         请求地址
// data：        POST请求提交的数据
// contentType： 请求体格式，如：application/json
// content：     请求放回的内容
func post(url string, data interface{}, header map[string]string) (error, Resp) {
	var respon Resp // 提前声明 respon

	// 序列化请求体 (保持不变)
	jsonStr, err := json.Marshal(data)
	if err != nil {
		// 返回更具体的错误信息
		return fmt.Errorf("序列化请求数据失败: %w", err), respon
	}

	// 创建请求 (保持不变)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		return fmt.Errorf("创建 HTTP 请求失败: %w", err), respon
	}

	// 设置 header (保持不变)
	for k, v := range header {
		req.Header.Set(k, v)
	}
	// 确保 Content-Type 被设置 (如果 header 中没有)
	if _, ok := header["Content-Type"]; !ok {
		req.Header.Set("Content-Type", "application/json")
	}
	// 设置 User-Agent (保持不变)
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

	// !!! 修改点：使用全局优化的 HTTP Client !!!
	client := GetHttpClient() // 获取共享的 Client 实例

	// 执行请求 (保持不变)
	resp, err := client.Do(req)
	if err != nil {
		// 返回包含原始错误的错误信息
		return fmt.Errorf("发送 HTTP 请求失败: %w", err), respon
	}
	// !!! 重要：确保关闭响应体以释放连接 !!!
	defer resp.Body.Close()

	// 检查 HTTP 状态码，提供更友好的错误信息
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		bodyBytes, _ := io.ReadAll(resp.Body) // 尝试读取响应体以获取更多信息
		return fmt.Errorf("HTTP 请求失败，状态码: %d, URL: %s, 响应: %s, 参数: %s", resp.StatusCode, url, string(bodyBytes), string(jsonStr)), respon
	}

	// 将结果转成结构体 (使用 io.ReadAll 替代 ioutil.ReadAll)
	result, err := io.ReadAll(resp.Body) // ioutil.ReadAll 在 Go 1.16+ 已弃用，建议用 io.ReadAll
	if err != nil {
		return fmt.Errorf("读取响应体失败: %w", err), respon
	}

	// 解析 JSON (保持不变)
	err = json.Unmarshal(result, &respon)
	if err != nil {
		// 返回包含原始响应内容的错误信息，方便调试
		return fmt.Errorf("解析响应 JSON 失败: %w, URL: %s, 响应: %s, 参数: %s", err, url, string(result), string(jsonStr)), respon
	}

	// 返回解析后的结果和 nil 错误
	return nil, respon
}

func postMessagePool(url string, data interface{}, header map[string]string) (error, RespMessage) {

	// 超时时间：5秒
	client := &http.Client{Timeout: 5 * time.Second}
	jsonStr, _ := json.Marshal(data)
	var req, err = http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		return err, RespMessage{}
	}

	//设置header
	for k, v := range header {
		req.Header.Set(k, v)
	}

	// 设置User-Agent头（部分服务器未设置时返回403错误导致校验失败）
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

	//执行请求
	resp, err := client.Do(req)
	if err != nil {
		return err, RespMessage{}
	}
	defer resp.Body.Close()

	//将结果转成结构体
	result, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err, RespMessage{}
	}
	//log.Log().Info("打印请求回调接口的url----"+string(url), zap.Any("info", string(jsonStr)))
	//log.Log().Info("打印请求回调接口的数据----"+string(jsonStr), zap.Any("info", string(jsonStr)))
	//log.Log().Info("打印请求回调接口的返回数据----"+string(result), zap.Any("info", string(result)))
	var respon RespMessage
	err = json.Unmarshal(result, &respon)
	return err, respon
}

func getMessagePoolTypeWithProductMessage(orderStatus mq.ProductMessageType) (messageType int) {
	switch orderStatus {
	case mq.Edit:
		messageType = model2.GoodsEdit
		break
	case mq.Undercarriage:
		messageType = model2.GoodsUndercarriage
		break
	case mq.OnSale:
		messageType = model2.GoodsOnSale
		break
	case mq.Create:
		messageType = model2.GoodsCreate
		break
	case mq.Delete:
		messageType = model2.GoodsDelete
		break
	case mq.DistributorSync:
		messageType = model2.DistributorSync
		break
	}
	return messageType
}
