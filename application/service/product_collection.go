package service

import (
	"application/model"
	"application/request"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"os"
	productModel "product/model"
	productRequest "product/request"
	productResponse "product/response"
	productService "product/service"
	"strconv"
	"strings"
	"time"
	levelModel "user/level"
	"yz-go/common_data"
	"yz-go/component/log"
	"yz-go/config"
	"yz-go/service"
	"yz-go/source"
	"yz-go/utils"
)

func GetProductList(info productRequest.ProductSearch) (err error, list []productResponse.ProductInfoList, total int64) {
	if info.AppID == 0 {
		err = errors.New("采购端ID不能为空")
		return
	}
	err, list, total = GetProductInfoList(info)
	if err != nil {
		return
	}
	return
}

func setProductEsSeachInfo(info productRequest.ProductSearch) (err error, boolQ *elastic.BoolQuery, isSearch int) {
	isSearch = 0
	boolQ = elastic.NewBoolQuery()
	if info.Title != "" {
		boolQ.Should(elastic.NewMatchPhraseQuery("search_title", info.Title).Slop(1), elastic.NewWildcardQuery("title.keyword", "*"+info.Title+"*")).MinimumShouldMatch("1")
		isSearch++
	}
	if info.SourceName != "" {
		boolQ.Must(elastic.NewMatchPhraseQuery("source_name", info.SourceName).Slop(2))
		isSearch++
	}
	if info.ShopName != "" {
		shopNameTrimmed := strings.TrimSpace(info.ShopName)
		shopNameQuery := elastic.NewBoolQuery().Should(
			elastic.NewTermQuery("shop_name.keyword", shopNameTrimmed),                                       // 精确匹配店铺名
			elastic.NewWildcardQuery("jushuitan_distributor_supplier_name.keyword", "*"+shopNameTrimmed+"*"), // 模糊匹配聚水潭供应商名
		).MinimumShouldMatch("1")
		boolQ.Must(shopNameQuery)
		isSearch++
	}
	filterQ := elastic.NewBoolQuery()
	if info.DetailImages != nil && *info.DetailImages == 1 {
		filterQ.Must(elastic.NewTermQuery("detail_images", 0)) // 假设 0 表示没有详情图
		isSearch++
	}
	if info.ImageUrl != nil && *info.ImageUrl == 1 {
		// ImageUrl 在 ProductElasticSearchCut 中是 string 类型，这里我们假设它是一个字符串URL
		// 如果是空字符串，则表示没有图片
		filterQ.Must(elastic.NewTermQuery("image_url.keyword", "")) // 假设 image_url.keyword 为空字符串表示没有图片
		isSearch++
	}
	if info.GalleryImages != nil && *info.GalleryImages == 1 {
		filterQ.Must(elastic.NewTermQuery("gallery_images", 0)) // 假设 0 表示没有画廊图
		isSearch++
	}
	if info.IsVideoShop != 0 {
		val := info.IsVideoShop
		if val == -1 { // -1 映射到 0
			val = 0
		}
		filterQ.Must(elastic.NewTermQuery("is_video_shop", val))
		isSearch++
	}
	if info.SupplierID != nil {
		supplierID := *info.SupplierID
		if supplierID == 999999 {
			filterQ.Must(elastic.NewRangeQuery("supplier_id").Gt(0))
		} else if supplierID == 0 {
			// supplier_id = 0 AND gather_supplier_id = 0
			filterQ.Must(elastic.NewTermQuery("supplier_id", 0))
			filterQ.Must(elastic.NewTermQuery("gather_supplier_id", 0))
		} else {
			filterQ.Must(elastic.NewTermQuery("supplier_id", supplierID))
		}
		isSearch++
	}
	if info.GatherSupplyID != nil {
		gatherSupplyID := *info.GatherSupplyID
		if gatherSupplyID == 0 {
			// supplier_id = 0 AND gather_supplier_id = 0 (与上面 SupplierID == 0 重复，但 ES 不会报错)
			filterQ.Must(elastic.NewTermQuery("supplier_id", 0))
			filterQ.Must(elastic.NewTermQuery("gather_supplier_id", 0))
		} else {
			filterQ.Must(elastic.NewTermQuery("gather_supplier_id", gatherSupplyID))
		}
		isSearch++
	}
	// BrandID
	if info.BrandID != 0 {
		filterQ.Must(elastic.NewTermQuery("brand_id", info.BrandID))
		isSearch++
	}
	// ID (商品 ID)
	if info.ID != 0 {
		filterQ.Must(elastic.NewTermQuery("id", info.ID)) // 假设 ES 中商品 ID 字段是 'id'
		isSearch++
	}
	if info.JushuitanBind != nil {
		filterQ.Must(elastic.NewTermQuery("jushuitan_bind", *info.JushuitanBind))
		isSearch++
	}
	// Category IDs
	if info.Category1ID != 0 {
		filterQ.Must(elastic.NewTermQuery("category_1_id", info.Category1ID)) // 对应 json:"category_1_id"
		isSearch++
	}
	if info.Category2ID != 0 {
		filterQ.Must(elastic.NewTermQuery("category_2_id", info.Category2ID))
		isSearch++
	}
	if info.Category3ID != 0 {
		filterQ.Must(elastic.NewTermQuery("category_3_id", info.Category3ID))
		isSearch++
	}
	if info.IsRecommend != nil {
		filterQ.Must(elastic.NewTermQuery("is_recommend", *info.IsRecommend))
		isSearch++
	}
	if info.IsNew != nil {
		filterQ.Must(elastic.NewTermQuery("is_new", *info.IsNew))
		isSearch++
	}
	if info.IsHot != nil {
		filterQ.Must(elastic.NewTermQuery("is_hot", *info.IsHot))
		isSearch++
	}
	if info.IsPromotion != nil {
		filterQ.Must(elastic.NewTermQuery("is_promotion", *info.IsPromotion))
		isSearch++
	}
	if info.StatusLock != nil {
		filterQ.Must(elastic.NewTermQuery("status_lock", *info.StatusLock))
		isSearch++
	}
	// info.Filter == 1 (已上架) 和 info.Filter == 2 (已下架)
	if info.Filter == 1 {
		filterQ.Must(elastic.NewTermQuery("is_display", 1))
		isSearch++
	}
	if info.Filter == 2 {
		filterQ.Must(elastic.NewTermQuery("is_display", 0))
		isSearch++
	}
	// 价格范围查询 (MinPrice, MaxPrice)
	// 根据 ProductElasticSearchCut，min_price 和 max_price 是 ES 里的字段
	if info.MinPrice != 0 {
		filterQ.Must(elastic.NewRangeQuery("min_price").Gte(info.MinPrice * 100)) // 假设 input price 也要 *100
		isSearch++
	}
	if info.MaxPrice != 0 {
		filterQ.Must(elastic.NewRangeQuery("max_price").Lte(info.MaxPrice * 100)) // 假设 input price 也要 *100
		isSearch++
	}
	// 利润率/价格区间查询 (SectionStart, SectionEnd)
	// 对应 ES 中的 `profit_rate` 或 `price` (可能需要用 `min_price` / `max_price` 的范围)
	if info.SortType == "4" { // profit_rate
		if info.SectionStart > 0 {
			filterQ.Must(elastic.NewRangeQuery("profit_rate").Gte(info.SectionStart))
		}
		if info.SectionEnd > 0 {
			filterQ.Must(elastic.NewRangeQuery("profit_rate").Lte(info.SectionEnd))
		}
		if info.SectionStart > 0 || info.SectionEnd > 0 { // 只有有实际的范围才计入搜索
			isSearch++
		}
	} else if info.SortType == "1" { // price
		// 对于价格区间，ES 中通常查询 min_price 和 max_price 的交叉
		// 这里假设 SectionStart/End 是针对商品整体的价格，可以查询 min_price 或 max_price 字段
		// 更准确的 ES 逻辑可能需要根据你商品价格范围的存储方式来定
		if info.SectionStart > 0 {
			// 商品的最低价 >= 搜索的最低价
			filterQ.Must(elastic.NewRangeQuery("min_price").Gte(info.SectionStart * 100)) // 假设也需要 *100
		}
		if info.SectionEnd > 0 {
			// 商品的最高价 <= 搜索的最高价
			filterQ.Must(elastic.NewRangeQuery("max_price").Lte(info.SectionEnd * 100)) // 假设也需要 *100
		}
		if info.SectionStart > 0 || info.SectionEnd > 0 {
			isSearch++
		}
	}
	// IsBill (发票状态)
	if info.IsBill != nil {
		filterQ.Must(elastic.NewTermQuery("is_bill", *info.IsBill))
		isSearch++
	}

	// Sn (商品编码)
	if info.Sn != "" {
		filterQ.Must(elastic.NewTermQuery("sn.keyword", info.Sn)) // 假设 sn 是 keyword 类型
		isSearch++
	}

	// 赋码状态搜索 (Coding) - 与 IsBill 字段相同
	if info.Coding != nil {
		filterQ.Must(elastic.NewTermQuery("is_bill", *info.Coding))
		isSearch++
	}

	// 会员价独立规则开关搜索 (UserPriceSwitch)
	if info.MemberPrice != nil {
		filterQ.Must(elastic.NewTermQuery("user_price_switch", *info.MemberPrice))
		isSearch++
	}
	if info.Filter == 3 {
		filterQ.Must(elastic.NewMatchQuery("is_display", 1))
		filterQ.Must(elastic.NewRangeQuery("stock").Lte(0))
		isSearch++
	}
	if info.Barcode != "" {
		boolQ.Must(elastic.NewMatchPhraseQuery("barcode", info.Barcode).Slop(2))
		isSearch++
	}
	if info.AppID != 0 {
		boolQ.Must(elastic.NewMatchQuery("import_apps", strconv.Itoa(int(info.AppID))).Analyzer("whitespace"))
		isSearch++
	}
	boolQ.Filter(filterQ)
	return
}

func setProductDbSearchInfo(info productRequest.ProductSearch, db *gorm.DB) (dB *gorm.DB, isSearch int) {
	db = db.Where("deleted_at is NULL")
	//db = db.Where("not exists(SELECT 1  FROM (select id from products where plugin_id=18)  AS temp_table WHERE temp_table.id = products.id)") //排除中台自营商品，仅后端不显示，前端正常显示
	db = db.Where("is_plugin = 0")
	if info.ShopName != "" {
		db = db.Where("(shop_name = ? or jushuitan_distributor_supplier_name like ?)", strings.TrimSpace(info.ShopName), "%"+strings.TrimSpace(info.ShopName)+"%")
	}
	if info.DetailImages != nil {
		if *info.DetailImages == 1 {
			db.Where("detail_images='' or detail_images is null")
		}
	}
	if info.ImageUrl != nil {
		if *info.ImageUrl == 1 {
			db.Where("image_url='' or image_url is null")
		}
	}
	if info.GalleryImages != nil {
		if *info.GalleryImages == 1 {
			db.Where("gallery='' or gallery is null")
		}
	}
	if info.ThousandId != 0 {
		var thousandProductIds []uint
		err := source.DB().Model(&productService.ThousandsPricesProducts{}).Where("thousands_prices_id = ?", info.ThousandId).Pluck("product_id", &thousandProductIds).Error
		if err != nil {
			return
		}
		if len(thousandProductIds) > 0 {
			db = db.Where("id not in ?", thousandProductIds)
		}
	}
	if info.IsVideoShop != 0 {
		if info.IsVideoShop == -1 {
			info.IsVideoShop = 0
		}
		db = db.Where("is_video_shop = ?", info.IsVideoShop)
		isSearch++
	}
	if info.SupplierID != nil {
		if *info.SupplierID == 999999 {
			db = db.Where("supplier_id > 0")
		} else {
			supplierID := *info.SupplierID
			if supplierID == 0 {
				db = db.Where("supplier_id = ?", supplierID)
				db = db.Where("gather_supply_id = ?", supplierID)
			} else {
				db = db.Where("supplier_id = ?", supplierID)
			}
		}
		isSearch++
	}
	if info.GatherSupplyID != nil {
		gatherSupplyID := *info.GatherSupplyID
		if gatherSupplyID == 0 {
			db = db.Where("supplier_id = ?", gatherSupplyID)
			db = db.Where("gather_supply_id = ?", gatherSupplyID)
		} else {
			db = db.Where("gather_supply_id = ?", gatherSupplyID)
		}
		isSearch++
	}
	if info.BrandID != 0 {
		db = db.Where("brand_id = ?", info.BrandID)
		isSearch++
	}
	if info.ID != 0 {
		db = db.Where("id = ?", info.ID)
		isSearch++
	}
	if info.JushuitanBind != nil {
		db = db.Where("jushuitan_bind = ?", info.JushuitanBind)
		isSearch++
	}
	if info.Category1ID != 0 {
		db = db.Where("category1_id = ?", info.Category1ID)
		isSearch++
	}
	if info.Category2ID != 0 {
		db = db.Where("category2_id = ?", info.Category2ID)
		isSearch++
	}
	if info.Category3ID != 0 {
		db = db.Where("category3_id = ?", info.Category3ID)
		isSearch++
	}
	if info.IsRecommend != nil {
		db = db.Where("is_recommend = ?", &info.IsRecommend)
		isSearch++
	}
	if info.IsNew != nil {
		db = db.Where("is_new = ?", &info.IsNew)
		isSearch++
	}
	if info.IsHot != nil {
		db = db.Where("is_hot = ?", &info.IsHot)
		isSearch++
	}
	if info.IsPromotion != nil {
		db = db.Where("is_promotion = ?", &info.IsPromotion)
		isSearch++
	}
	if info.StatusLock != nil {
		db = db.Where("status_lock = ?", &info.StatusLock)
		isSearch++
	}
	if info.Filter == 1 {
		db = db.Where("is_display = 1")
		isSearch++
	}
	if info.Filter == 2 {
		db = db.Where("is_display = 0")
		isSearch++
	}

	if info.MinPrice != 0 {
		db = db.Where("price >= ?", info.MinPrice*100)
		isSearch++
	}
	if info.MaxPrice != 0 {
		db = db.Where("price <= ?", info.MaxPrice*100)
		isSearch++
	}
	if info.SortType == "4" || info.SortType == "1" {
		if info.SectionStart > 0 {
			if info.SortType == "4" {
				db = db.Where("profit_rate >= ?", info.SectionStart)
			} else {
				db = db.Where("price >= ?", info.SectionStart)
			}
		}
		if info.SectionEnd > 0 {
			if info.SortType == "4" {
				db = db.Where("profit_rate <= ?", info.SectionEnd)
			} else {
				db = db.Where("price <= ?", info.SectionEnd)
			}
		}
	}

	if info.IsBill != nil {
		db = db.Where("is_bill = ?", info.IsBill)
		isSearch++

	}
	if info.Sn != "" {
		db = db.Where("sn = ?", info.Sn)
		isSearch++

	}
	// 指定供应链
	if info.GatherSupplyID != nil {
		db = db.Where("gather_supply_id = ?", info.GatherSupplyID)
		isSearch++
	}
	// 赋码状态搜索
	if info.Coding != nil {
		db.Where("is_bill = ?", info.Coding)
	}
	// 会员价独立规则开关搜索
	if info.MemberPrice != nil {
		db.Where("user_price_switch = ?", info.MemberPrice)
	}
	return db, isSearch
}

func GetProductInfoList(info productRequest.ProductSearch) (err error, list []productResponse.ProductInfoList, total int64) {
	// 创建db
	es, err := source.ES()
	if err != nil {
		return
	}
	err, boolQ, isSearch := setProductEsSeachInfo(info)
	if err != nil {
		return
	}
	if isSearch <= 0 {
		err = errors.New("采购端id不能为空")
		return
	}
	limit := info.PageSize
	if limit > 100 {
		limit = 100
	}
	if info.Page > 100 {
		info.Page = 100
	}
	offset := info.PageSize * (info.Page - 1)

	var sortType string
	switch info.SortType {
	case "0":
		sortType = "id"
		break
	case "1":
		sortType = "price"
		break
	case "2":
		sortType = "cost_price"
		break
	case "3":
		sortType = "origin_price"
		break
	case "4":
		sortType = "profit_rate"
		break
	case "5":
		sortType = "sales"
		break
	case "6":
		sortType = "sort"
		break
	default:
		sortType = "id"
		break
	}
	var sort bool
	if info.SortMode == "desc" {
		sort = false
	} else {
		sort = true
	}
	// 查询总数
	total, err = es.Count("product" + common_data.GetOldProductIndex()).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	// 只获取 ID 字段
	res, err := es.Search("product"+common_data.GetOldProductIndex()).
		Size(limit).
		From(offset).
		Sort(sortType, sort).
		Query(boolQ).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include("id")). // 明确指定只包含 'id'
		Do(context.Background())
	if err != nil {
		return
	}
	var resProductIDs []uint
	if res.Hits.TotalHits.Value > 0 {
		for _, hit := range res.Hits.Hits {
			var t productService.ProductElasticSearchCut
			err := json.Unmarshal(hit.Source, &t)
			if err != nil {
				log.Log().Error("反序列化失败", zap.Error(err), zap.String("source", string(hit.Source)))
			}
			resProductIDs = append(resProductIDs, t.ID)
		}
	} else {
		return
	}
	// 根据 resProductIDs 查询数据库中的商品信息
	var products, resProducts []productResponse.ProductInfoList
	err = source.DB().Model(&productResponse.ProductInfoList{}).Preload("Supplier").Preload("Skus").Preload("Brand").Preload("GatherSupply").Omit("detail_images").Where("id in ?", resProductIDs).Find(&products).Error
	if err != nil {
		return
	}
	// 取得的商品转成 map 方便后续查找
	productMap := make(map[uint]productResponse.ProductInfoList, len(products))
	// 遍历 products，计算每个商品的价格、成本价、利润率等信息
	for k, product := range products {
		var maxPrice uint
		var minPrice uint
		var maxGuidePrice uint
		var minGuidePrice uint
		var maxCostPrice uint
		var minCostPrice uint
		var maxOriginPrice uint
		var minOriginPrice uint
		var minProfitRate float64 = 0
		var maxProfitRate float64 = 0
		for pk, psku := range product.Skus {
			if psku.GuidePrice > 0 {
				product.Skus[pk].ProfitRate = utils.ExecProfitRate(psku.GuidePrice, psku.Price)
			} else {
				product.Skus[pk].ProfitRate = 0
			}

		}
		var productStock int
		for sk, sku := range product.Skus {
			productStock += sku.Stock
			if sku.Price > maxPrice {
				maxPrice = sku.Price
			}
			if minPrice == 0 || sku.Price <= minPrice {
				minPrice = sku.Price
			}

			if sku.ExecPrice > maxCostPrice {
				maxCostPrice = sku.ExecPrice
			}
			if minCostPrice == 0 || sku.ExecPrice <= minCostPrice {
				minCostPrice = sku.ExecPrice
			}

			if sku.OriginPrice > maxOriginPrice {
				maxOriginPrice = sku.OriginPrice
			}
			if minOriginPrice == 0 || sku.OriginPrice <= minOriginPrice {
				minOriginPrice = sku.OriginPrice
			}

			if sku.GuidePrice > maxGuidePrice {
				maxGuidePrice = sku.GuidePrice
			}
			if minGuidePrice == 0 || sku.GuidePrice <= minGuidePrice {
				minGuidePrice = sku.GuidePrice
			}

			if sku.ProfitRate > maxProfitRate {
				maxProfitRate = sku.ProfitRate
			}
			if sk == 0 {
				minProfitRate = sku.ProfitRate
			}
			if sku.ProfitRate <= minProfitRate {
				minProfitRate = sku.ProfitRate
			}
		}

		products[k].MinPrice = minPrice
		products[k].MaxPrice = maxPrice
		products[k].MinCostPrice = minCostPrice
		products[k].MaxCostPrice = maxCostPrice
		products[k].MinOriginPrice = minOriginPrice
		products[k].MaxOriginPrice = maxOriginPrice
		products[k].MinProfitRate = minProfitRate
		products[k].MaxProfitRate = maxProfitRate
		products[k].Stock = uint(productStock)
		productMap[products[k].ID] = products[k]
	}
	// 遍历 ES 查询结果，将对应的商品信息，按照 ES 返回的顺序，填充到 resProducts 中
	for _, hit := range res.Hits.Hits {
		var t productService.ProductElasticSearchCut
		err := json.Unmarshal(hit.Source, &t)
		if err != nil {
			// Deserialization failed
		}
		if product, exists := productMap[t.ID]; exists {
			resProducts = append(resProducts, product)
		}
	}
	return err, resProducts, total
}

func GetAC(appID uint) (err error, ac model.ApplicationCollection) {
	if appID == 0 {
		err = errors.New("appID不能为空")
		return
	}
	err = source.DB().Where("app_id = ?", appID).First(&ac).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果没有找到记录，返回一个空的 ApplicationCollection
			ac = model.ApplicationCollection{}
			err = nil
		} else {
			return fmt.Errorf("查询选品库失败 (appID=%d): %w", appID, err), ac
		}
	}
	return
}

type CollectionStatistic struct {
	// 商品数量
	ProductCount int64 `json:"product_count"`
	// 商品销量
	ProductSaleCount int64 `json:"product_sale_count"`
	// 累计订单数量
	OrderCount int64 `json:"order_count"`
	// 累计订单金额
	OrderAmount int64 `json:"order_amount"`
	// 最近选品时间
	RecentSelectionAt *source.LocalTime `json:"recent_selection"`
}

func GetCollectionStatistics(appID uint) (error, *CollectionStatistic) {
	if appID == 0 {
		return errors.New("appID不能为空"), nil
	}

	db := source.DB()

	// 开启事务
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	stats := &CollectionStatistic{}

	// --- 1. 查询销量前3的商品 ---
	var productWithImage []ProductWithImage
	err := tx.Table("storages").
		Select("products.id AS product_id, products.image_url").
		Joins("JOIN products ON storages.product_id = products.id").
		Where("storages.app_id = ?", appID). // 注意这里是否应该是 storages.app_id？
		Order("products.sales DESC, products.id DESC").
		Limit(3).
		Scan(&productWithImage).Error

	if err != nil {
		tx.Rollback()
		return fmt.Errorf("查询销量前3的商品失败 (appID=%d): %w", appID, err), nil
	}

	// --- 2. 获取统计数据 ---
	var ac model.ApplicationCollection
	errAc := tx.Model(&model.ApplicationCollection{}).Where("app_id = ?", appID).First(&ac).Error

	errStats, stats := GetCollectionStatisticsOptimized(appID)
	if errStats != nil {
		tx.Rollback()
		return fmt.Errorf("获取选品库统计数据失败 (appID=%d): %w", appID, errStats), nil
	}

	// 如果没有 ApplicationCollection 记录，先初始化
	if errors.Is(errAc, gorm.ErrRecordNotFound) {
		ac.AppID = appID
	} else if errAc != nil {
		tx.Rollback()
		return fmt.Errorf("查询选品库记录失败 (appID=%d): %w", appID, errAc), nil
	}

	// 更新统计数据
	ac.ProductCount = stats.ProductCount
	ac.OrderCount = stats.OrderCount
	ac.OrderAmount = stats.OrderAmount
	ac.RecentSelectionAt = stats.RecentSelectionAt
	ac.ProductSaleCount = stats.ProductSaleCount

	// 清空并设置热销商品
	ac.Products = make([]model.ProductItem, 0, len(productWithImage))
	for _, w := range productWithImage {
		ac.Products = append(ac.Products, model.ProductItem{
			ID:       w.ProductID,
			ImageUrl: w.ImageUrl,
		})
	}

	// --- 3. 更新或创建 ApplicationCollection ---
	if ac.ID != 0 {
		if err := tx.Model(&ac).Updates(ac).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("更新选品库失败 (appID=%d): %w", appID, err), nil
		}
	} else {
		if err := tx.Create(&ac).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("创建选品库失败 (appID=%d): %w", appID, err), nil
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("提交事务失败 (appID=%d): %w", appID, err), nil
	}

	return nil, stats
}

func GetCollectionStatisticsOptimized(appID uint) (error, *CollectionStatistic) {
	if appID == 0 {
		return errors.New("appID不能为空"), nil
	}

	stats := &CollectionStatistic{}
	db := source.DB()

	// --- 1. 获取去重商品数量 + 总销量 ---
	type ProductStats struct {
		ProductCount     int64
		ProductSaleCount int64
	}
	var productStats ProductStats

	err := db.Model(&productModel.Storage{}).
		Select("COUNT(DISTINCT product_id) as product_count, SUM(products.sales) as product_sale_count").
		Joins("JOIN products ON storages.product_id = products.id").
		Where("storages.app_id = ?", appID).
		Scan(&productStats).Error

	if err != nil {
		return wrapError(appID, "查询商品总数和销量失败", err), nil
	}

	stats.ProductCount = productStats.ProductCount
	stats.ProductSaleCount = productStats.ProductSaleCount

	if stats.ProductCount == 0 {
		return nil, stats
	}

	// --- 2. 获取订单数量和金额（保持原逻辑） ---
	var result struct {
		OrderCount  int64
		OrderAmount sql.NullInt64
	}

	orderQuery := db.Model(&OrderItemModel{}).
		Joins("INNER JOIN storages ON order_items.product_id = storages.product_id").
		Where("storages.app_id = ?", appID).
		Select("COUNT(DISTINCT order_items.order_id), SUM(order_items.amount)")

	if err := orderQuery.Row().Scan(&result.OrderCount, &result.OrderAmount); err != nil {
		if errors.Is(err, sql.ErrNoRows) || errors.Is(err, gorm.ErrRecordNotFound) {
			result.OrderCount = 0
			result.OrderAmount = sql.NullInt64{Valid: true, Int64: 0}
		} else {
			return wrapError(appID, "查询订单数量和金额失败", err), nil
		}
	}

	stats.OrderCount = result.OrderCount
	stats.OrderAmount = 0
	if result.OrderAmount.Valid {
		stats.OrderAmount = result.OrderAmount.Int64
	}

	// --- 3. 获取最近一次选品时间 ---
	var latestStorage productModel.Storage
	if err := db.Where("app_id = ?", appID).
		Order("created_at DESC").
		Limit(1).
		Find(&latestStorage).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return wrapError(appID, "查询最后选品时间失败", err), nil
		}
	} else if latestStorage.ID != 0 {
		stats.RecentSelectionAt = latestStorage.CreatedAt
	}

	return nil, stats
}

// 工具函数：统一错误包装
func wrapError(appID uint, msg string, err error) error {
	return fmt.Errorf("%s (appID=%d): %w", msg, appID, err)
}

type AlbumStats struct {
	TotalSales uint `json:"total_sales"`
}

type ProductWithImage struct {
	ProductID uint   `json:"product_id"`
	ImageUrl  string `json:"image_url" gorm:"column:image_url;type:varchar(255);comment:图片url;size:255;"`
}

type OrderItemModel struct {
	source.Model
	Amount    uint `json:"amount" form:"amount" gorm:"column:amount;comment:总价;"`
	OrderID   uint `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单id;index;"`
	ProductID uint `json:"product_id" form:"product_id" gorm:"column:product_id;comment:产品id;index;"`
}

func (OrderItemModel) TableName() string {
	return "order_items"
}

func UpdateSelection(appID uint, name string, share int) error {
	if appID == 0 {
		return errors.New("appID不能为空")
	}
	if name == "" {
		return errors.New("选品库名称不能为空")
	}

	db := source.DB()

	var ac model.ApplicationCollection

	attrs := model.ApplicationCollection{Name: name, Share: share}
	if share == 1 {
		attrs.ShareAt = &source.LocalTime{Time: time.Now()}
	} else {
		attrs.ShareAt = nil
	}

	// 查找或创建
	result := db.Where(model.ApplicationCollection{AppID: appID}).Attrs(attrs).FirstOrCreate(&ac)
	if result.Error != nil {
		return fmt.Errorf("查找或创建选品库失败 (appID=%d): %w", appID, result.Error)
	}

	// 如果记录已存在且名称不同，则更新
	if ac.Name != name || ac.Share != share {
		upDataMap := map[string]interface{}{
			"name":  name,
			"share": share,
		}
		if share == 1 {
			upDataMap["share_at"] = &source.LocalTime{Time: time.Now()}
		} else {
			upDataMap["share_at"] = nil
		}
		err := db.Model(&ac).Updates(upDataMap).Error
		if err != nil {
			return fmt.Errorf("更新选品库名称失败 (appID=%d): %w", appID, err)
		}
	}

	return nil
}

func GetProductCollectionList(info request.ProductCollectionListRequest) (err error, list interface{}, total int64) {
	var collections []model.ApplicationCollection
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(&model.ApplicationCollection{})
	db = db.Where("share = ?", 1)

	var sortType string
	switch info.SortType {
	case 1:
		sortType = "product_count"
	case 2:
		sortType = "order_count"
	case 3:
		sortType = "order_amount"
	case 4:
		sortType = "recent_selection_at"
	case 5:
		sortType = "share_at"
	case 6:
		sortType = "browse_count"
	default:
		sortType = "product_count"
	}
	var sortMode string
	sortMode = "desc"
	if info.Sort == true {
		sortMode = "asc"
	}

	err = db.Count(&total).Error
	err = db.Order(sortType + " " + sortMode).Limit(limit).Offset(offset).Find(&collections).Error
	return err, collections, total
}

func GetProductCollectionDetail(req request.ProductCollectionDetailRequest, appLevelId uint, PetSupplierID uint, userID uint, userLevel int) (err error, list interface{}, total int64) {
	// 当前选品库的采购端 id
	if req.ApplicationID == 0 {
		err = errors.New("采购端ID不能为空")
		return
	}
	// 增加访问次数
	err = AddCollectionBrowseCount(req.ApplicationID)
	if err != nil {
		err = errors.New("添加访问次数失败")
		return
	}
	info := req.ProductStorageSearch
	limit := info.PageSize
	if limit > 100 {
		limit = 100
	}
	if info.Page > 100 {
		info.Page = 100
	}
	offset := info.PageSize * (info.Page - 1)
	// 获取用户等级排序值
	userLevelSort := levelModel.GetLevelSort(userLevel)
	// 初始化默认值
	var levelKey string
	if userLevelSort >= 1 && userLevelSort <= 10 {
		levelKey = fmt.Sprintf("level_%d", userLevelSort)
	}
	// 根据 info.Type 动态设置值
	if info.Type == "" {
		info.Type = "sort"
	} else if info.Type == "discount" {
		// 如果 userLevelSort 在 1 到 10 的范围内，动态生成列名；否则使用默认值 "sort"
		if levelKey != "" {
			info.Type = levelKey + "_min_discount"
		} else {
			info.Type = "sort"
		}
	}
	// 创建db
	var es *elastic.Client
	es, err = source.ES()
	if err != nil {
		return
	}
	var boolQ *elastic.BoolQuery
	err, boolQ = productService.GetProductStorageBoolQ(info, appLevelId, PetSupplierID, userID, userLevelSort)
	if err != nil {
		return
	}
	// req.ProductStorageSearch的条件是当前浏览会员的条件，要加上 req.ApplicationID的条件才是当前选品库的商品。
	// es搜索默认条件 import_apps中必须包含传入的选品库的采购端 id
	// 确保商品属于当前选品库（通过 import_apps 字段匹配 ApplicationID）
	appIDFilter := elastic.NewBoolQuery().Must(
		elastic.NewMatchQuery("import_apps", strconv.Itoa(int(req.ApplicationID))).Analyzer("whitespace"),
	)
	boolQ.Filter(appIDFilter)
	if info.CollectionID != 0 {
		var collection productModel.Collection
		err = source.DB().First(&collection, info.CollectionID).Error
		if err != nil {
			return
		}
		filterQ := elastic.NewBoolQuery()
		if collection.Type == 1 {
			total = int64(collection.Num)
			if info.PageSize*info.Page >= int(total) {
				limit = int(total) - offset
			}
			var relations []uint
			err = source.DB().Model(&productModel.CollectionProduct{}).Where("collection_id = ?", info.CollectionID).Pluck("product_id", &relations).Error
			if err != nil {
				return
			}
			relationIds := make([]interface{}, len(relations))
			for index, value := range relations {
				relationIds[index] = value
			}
			filterQ.Must(elastic.NewTermsQuery("id", relationIds...))

		} else if collection.Type == 2 {
			total = int64(collection.Filter.CategoryProductNum)
			if info.PageSize*info.Page >= int(total) {
				limit = int(total) - offset
			}
			category1Ids := make([]interface{}, len([]uint(collection.Filter.Category1ID)))
			for index, value := range []uint(collection.Filter.Category1ID) {
				category1Ids[index] = value
			}
			filterQ.Must(elastic.NewTermsQuery("category_1_id", category1Ids...))

			category2Ids := make([]interface{}, len([]uint(collection.Filter.Category2ID)))
			for index, value := range []uint(collection.Filter.Category2ID) {
				category2Ids[index] = value
			}
			filterQ.Must(elastic.NewTermsQuery("category_2_id", category2Ids...))

			category3Ids := make([]interface{}, len([]uint(collection.Filter.Category3ID)))
			for index, value := range []uint(collection.Filter.Category3ID) {
				category3Ids[index] = value
			}
			filterQ.Must(elastic.NewTermsQuery("category_3_id", category3Ids...))

		} else if collection.Type == 3 {
			total = int64(collection.Filter.AttributeProductNum)
			if info.PageSize*info.Page >= int(total) {
				limit = int(total) - offset
			}
			switch collection.Filter.AttributeType {
			case 1:
				filterQ.Must(elastic.NewMatchQuery("is_hot", 1))
				break
			case 2:
				filterQ.Must(elastic.NewMatchQuery("is_recommend", 1))
				break
			case 3:
				filterQ.Must(elastic.NewMatchQuery("is_new", 1))
				break
			case 4:
				filterQ.Must(elastic.NewMatchQuery("is_promotion", 1))
				break
			}

		} else if collection.Type == 4 {
			total = int64(collection.Filter.StatisticProductNum)
			if info.PageSize*info.Page >= int(total) {
				limit = int(total) - offset
			}
			if collection.Filter.StatisticType == 1 {
				info.Type = "sales"
				info.Sort = false
			} else if collection.Filter.StatisticType == 2 {
				info.Type = "created_at"
				info.Sort = false
			}
		}
		boolQ.Filter(filterQ)
	}
	var realTotal int64
	realTotal, err = es.Count("product" + common_data.GetOldProductIndex()).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	if info.CollectionID != 0 {
		if realTotal <= total {
			total = realTotal
		}
	} else {
		total = realTotal
	}
	//es执行搜索
	res, err := es.Search("product"+common_data.GetOldProductIndex()).Size(limit).From(offset).Sort(info.Type, info.Sort).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	//获取es搜索结果
	var listAll []productService.ProductElasticSearch
	listAll, err = productService.GetSearchResult(res)
	var listCut []productService.ProductElasticSearchCut
	err, listCut = productService.ProductTransAppPrice(listAll, userID, appLevelId, info.AppID, 0)

	return err, listCut, total
}

func AddCollectionBrowseCount(appID uint) (err error) {
	if appID == 0 {
		err = errors.New("appID不能为空")
		return
	}
	var ac model.ApplicationCollection
	err = source.DB().Where("app_id = ?", appID).First(&ac).Error
	if err != nil || errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	ac.BrowseCount += 1
	err = source.DB().Model(&ac).Update("browse_count", ac.BrowseCount).Error
	return
}

func GetExportRecordList(info request.GetExportRecordListRequest) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	var records []model.ApplicationExportRecord
	db := source.DB().Model(&model.ApplicationExportRecord{})
	if info.StartAt != "" {
		db.Where("created_at >= ?", info.StartAt)
	}
	if info.EndAt != "" {
		db.Where("created_at <= ?", info.EndAt)
	}

	if info.ErrorStatus == 1 {
		db = db.Where("`status_string` = ?", "导出完成")
	}

	if info.ErrorStatus == 2 {
		db = db.Where("`status_string` != ?", "导出完成")
	}

	err = db.Count(&total).Error
	err = db.Order("id desc").Limit(limit).Offset(offset).Find(&records).Error

	return err, records, total
}

func DeleteExportRecord(id uint) (err error) {
	if id == 0 {
		err = errors.New("id不能为空")
		return
	}
	var record model.ApplicationExportRecord
	err = source.DB().Where("id = ?", id).First(&record).Error
	if err != nil {
		return
	}
	err = source.DB().Delete(&record).Error
	if err != nil {
		return
	}
	return
}

func ExportProducts(info productRequest.ProductSearch) (err error) {
	if info.AppID == 0 {
		err = errors.New("采购端ID不能为空")
		return
	}
	go func() {
		if ExportErr, _ := ExportProductsHandle(info); ExportErr != nil {
			log.Log().Error("导出采购端选品库商品列表失败", zap.Error(ExportErr))
		}
	}()
	return nil
}

func ExportProductsHandle(info productRequest.ProductSearch) (err error, link string) {
	isSearch, pids, err := productService.SearchProductIDFromEs(info)
	// 创建db
	db := source.DB().Model(&productService.ExportProductModel{})
	db = db.Where("deleted_at is NULL")
	if isSearch > 0 {
		if len(pids) > 0 {
			//按照es查询出来的id顺序进行排序
			db.Where("id in ?", pids)
		} else {
			db.Where("id in (0)")
		}
	}
	if info.SupplierID != nil {
		if *info.SupplierID == 999999 {
			db.Where("supplier_id > 0")
		} else {
			db.Where("supplier_id = ?", info.SupplierID)
		}
	}
	if info.BrandID != 0 {
		db = db.Where("brand_id = ?", info.BrandID)
	}
	if info.ID != 0 {
		db = db.Where("id = ?", info.ID)
	}
	if info.Category1ID != 0 {
		db = db.Where("category1_id = ?", info.Category1ID)
	}
	if info.Category2ID != 0 {
		db = db.Where("category2_id = ?", info.Category2ID)
	}
	if info.Category3ID != 0 {
		db = db.Where("category3_id = ?", info.Category3ID)
	}
	if info.ShopName != "" {
		db = db.Where("(shop_name = ? or jushuitan_distributor_supplier_name like ?)", strings.TrimSpace(info.ShopName), "%"+strings.TrimSpace(info.ShopName)+"%")
	}
	if info.IsRecommend != nil {
		db = db.Where("is_recommend = ?", info.IsRecommend)
	}
	if info.IsNew != nil {
		db = db.Where("is_new = ?", info.IsNew)
	}
	if info.IsHot != nil {
		db = db.Where("is_hot = ?", info.IsHot)
	}
	if info.IsPromotion != nil {
		db = db.Where("is_promotion = ?", info.IsPromotion)
	}
	if info.StatusLock != nil {
		db = db.Where("status_lock = ?", info.StatusLock)
	}
	if info.Filter == 1 {
		db = db.Where("is_display = 1")
	}
	if info.Filter == 2 {
		db = db.Where("is_display = 0")
	}
	if info.Filter == 3 {
		db = db.Where("is_display = 1 AND stock <= 0")
	}
	if info.MinPrice != 0 {
		db = db.Where("price >= ?", info.MinPrice*100)
	}
	if info.MaxPrice != 0 {
		db = db.Where("price <= ?", info.MaxPrice*100)
	}

	// 福禄供应链
	var supply GatherSupply
	err = source.DB().Unscoped().Where("category_id = ?", 98).First(&supply).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查询供应链失败")
		return
	}
	// 指定供应链
	if info.GatherSupplyID != nil {
		db = db.Where("gather_supply_id = ?", info.GatherSupplyID)
	} else {
		var specialSupplyIDs []uint
		err, specialSupplyIDs = productService.GetSupplyIDs()
		if len(specialSupplyIDs) > 0 {
			supplyIDs := make([]interface{}, len(specialSupplyIDs))
			for index, value := range specialSupplyIDs {
				supplyIDs[index] = value
			}
			db = db.Where("gather_supply_id not in ?", specialSupplyIDs)
		}
	}
	var total int64
	db.Where("is_plugin = 0")
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	var productExportRecord model.ApplicationExportRecord
	productExportRecord.AppID = info.AppID
	productExportRecord.ProductCount = total
	productExportRecord.StatusString = "导出中"
	err = source.DB().Create(&productExportRecord).Error
	if err != nil {
		return
	}
	excelPage := total/5000 + 1
	var links []string
	timeString := time.Now().Format("20060102150405")
	path := config.Config().Local.Path + "/export_app_product_collection"
	for ei := 1; ei <= int(excelPage); ei++ {
		f := excelize.NewFile()
		// 创建一个工作表
		index := f.NewSheet("Sheet1")
		// 设置单元格的值
		f.SetCellValue("Sheet1", "A1", "商品id")
		f.SetCellValue("Sheet1", "B1", "商品名称")
		f.SetCellValue("Sheet1", "C1", "供应渠道")
		f.SetCellValue("Sheet1", "D1", "状态")
		f.SetCellValue("Sheet1", "E1", "商品单位")
		f.SetCellValue("Sheet1", "F1", "商品规格标题")
		f.SetCellValue("Sheet1", "G1", "库存")
		f.SetCellValue("Sheet1", "H1", "销量")
		f.SetCellValue("Sheet1", "I1", "指导价")
		f.SetCellValue("Sheet1", "J1", "供货价")
		f.SetCellValue("Sheet1", "K1", "成本价")
		f.SetCellValue("Sheet1", "L1", "建议零售价")
		f.SetCellValue("Sheet1", "M1", "营销价")
		f.SetCellValue("Sheet1", "N1", "商品编码")
		f.SetCellValue("Sheet1", "O1", "商品条码")
		f.SetCellValue("Sheet1", "P1", "重量")
		f.SetCellValue("Sheet1", "Q1", "税收分类编码")
		f.SetCellValue("Sheet1", "R1", "税率")
		f.SetCellValue("Sheet1", "S1", "库存")
		f.SetCellValue("Sheet1", "T1", "分类")
		f.SetCellValue("Sheet1", "U1", "发票名称")
		f.SetCellValue("Sheet1", "V1", "商品属性")
		f.SetCellValue("Sheet1", "W1", "供应商商品分类")
		f.SetCellValue("Sheet1", "X1", "供应商商品来源")
		f.SetCellValue("Sheet1", "Y1", "第三方商品ID")
		f.SetCellValue("Sheet1", "Z1", "京东商品连接")
		f.SetCellValue("Sheet1", "AA1", "规格ID")
		i := 2
		var products []productService.ExportProductModel
		err = db.Preload("Supplier").Preload("Skus").Preload("SupplierSource").Preload("SupplierSourceCategory").Preload("Brand").Preload("Category1").Preload("Category2").Preload("Category3").Preload("GatherSupply").Order("created_at desc").Limit(5000).Offset(5000 * (ei - 1)).Find(&products).Error
		for _, v := range products {
			for _, sku := range v.Skus {
				var statusName = "下架"
				if v.IsDisplay == 1 {
					statusName = "上架"
				}
				var sourceGoodsID interface{}
				if v.SourceGoodsID > 0 {
					sourceGoodsID = v.SourceGoodsID
				} else if v.SourceGoodsIDString != "" {
					sourceGoodsID = v.SourceGoodsIDString
				} else if v.Code != "" {
					sourceGoodsID = v.Code
				} else if v.Sn != "" {
					sourceGoodsID = v.Sn
				}
				f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), v.ID)
				f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), v.Title)
				f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), v.GatherSupply.Name+v.Supplier.Name)
				f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), statusName)
				f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), v.Unit)
				f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), sku.Title)
				f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), sku.Stock)
				f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), v.Sales)
				f.SetCellValue("Sheet1", "I"+strconv.Itoa(i), float64(sku.GuidePrice)/100)
				f.SetCellValue("Sheet1", "J"+strconv.Itoa(i), float64(sku.Price)/100)
				f.SetCellValue("Sheet1", "K"+strconv.Itoa(i), float64(sku.CostPrice)/100)
				f.SetCellValue("Sheet1", "L"+strconv.Itoa(i), float64(sku.OriginPrice)/100)
				f.SetCellValue("Sheet1", "M"+strconv.Itoa(i), float64(sku.ActivityPrice)/100)
				if sku.Sn == "" {
					f.SetCellValue("Sheet1", "N"+strconv.Itoa(i), v.Sn)
				} else {
					f.SetCellValue("Sheet1", "N"+strconv.Itoa(i), sku.Sn)
				}
				if sku.Barcode == "" {
					f.SetCellValue("Sheet1", "O"+strconv.Itoa(i), v.Barcode)
				} else {
					f.SetCellValue("Sheet1", "O"+strconv.Itoa(i), sku.Barcode)
				}
				f.SetCellValue("Sheet1", "P"+strconv.Itoa(i), sku.Weight)
				if v.BillPosition == 1 {
					f.SetCellValue("Sheet1", "Q"+strconv.Itoa(i), v.TaxCode)
					f.SetCellValue("Sheet1", "R"+strconv.Itoa(i), v.TaxRate)
				} else {
					f.SetCellValue("Sheet1", "Q"+strconv.Itoa(i), sku.TaxCode)
					f.SetCellValue("Sheet1", "R"+strconv.Itoa(i), sku.TaxRate)
				}
				f.SetCellValue("Sheet1", "S"+strconv.Itoa(i), sku.Stock)
				var categoryStr string
				categoryStr = fmt.Sprintf("%s-%s-%s", v.Category1.Name, v.Category2.Name, v.Category3.Name)
				f.SetCellValue("Sheet1", "T"+strconv.Itoa(i), categoryStr)
				f.SetCellValue("Sheet1", "U"+strconv.Itoa(i), v.TaxProductName)
				var attrsStr string
				for _, attr := range v.Attrs {
					attrsStr += fmt.Sprintf("%s:%s,", attr.Name, attr.Value)
				}
				JdlinK := ""
				if v.Source == 2 && v.StartDateTime != "" {
					JdlinK = "https://item.jd.com/" + v.StartDateTime + ".html"
				}
				f.SetCellValue("Sheet1", "V"+strconv.Itoa(i), attrsStr)
				f.SetCellValue("Sheet1", "W"+strconv.Itoa(i), v.SupplierSourceCategory.Name)
				f.SetCellValue("Sheet1", "X"+strconv.Itoa(i), v.SupplierSource.Name)
				f.SetCellValue("Sheet1", "Y"+strconv.Itoa(i), sourceGoodsID)
				f.SetCellValue("Sheet1", "Z"+strconv.Itoa(i), JdlinK)
				f.SetCellValue("Sheet1", "AA"+strconv.Itoa(i), sku.ID)
				i++
			}

		}
		// 设置工作簿的默认工作表
		f.SetActiveSheet(index)
		// 根据指定路径保存文件
		exist, _ := utils.PathExists(path)
		if !exist {
			// 创建文件夹及其所有父目录
			err = os.MkdirAll(path, os.ModePerm)
			if err != nil {
				fmt.Printf("mkdir failed![%v]\n", err)
			} else {
				fmt.Printf("mkdir success!\n")
			}
		}
		link = path + "/" + timeString + "-" + strconv.Itoa(ei) + "采购端选品库商品导出.xlsx"
		if err = f.SaveAs(link); err != nil {
			return
		}
		links = append(links, link)
	}
	if excelPage > 1 {
		link = path + "/" + timeString + "采购端选品库商品导出.zip"
		err = Zip(link, links)
	}
	productExportRecord.Link = link
	productExportRecord.StatusString = "导出完成"
	err = source.DB().Save(&productExportRecord).Error
	if err != nil {
		return
	}
	err = service.PublishNotify("采购端选品库商品导出完成", "共导出了"+strconv.Itoa(int(total))+"件商品")

	return err, link
}
