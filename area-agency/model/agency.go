package model

import (
	"errors"
	"gorm.io/gorm"
	"region/service"
	"user/model"
	"yz-go/source"
)

const (
	ProvinceLevel int = 1
	CityLevel     int = 2
	CountyLevel   int = 3
	TownLevel     int = 4
)

type AgencyMigration struct {
	source.Model
	Uid                uint              `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;"`
	RealName           string            `json:"real_name" form:"real_name" gorm:"column:real_name;comment:姓名;type:varchar(255);size:255;"`
	Mobile             string            `json:"mobile" form:"mobile" gorm:"column:mobile;comment:电话号;type:varchar(255);size:255;"`
	Level              int               `json:"level" gorm:"column:level;comment:代理级别;type:int;"`
	CountryId          int               `json:"country_id" gorm:"column:country_id;default:0;comment:国家id;type:int;"`
	ProvinceId         int               `json:"province_id" gorm:"column:province_id;comment:省份id;type:int;"`
	CityId             int               `json:"city_id" gorm:"column:city_id;comment:城市id;type:int;"`
	CountyId           int               `json:"county_id" gorm:"column:county_id;comment:区id;type:int;"`
	TownId             int               `json:"town_id" gorm:"column:town_id;comment:街道id;type:int;"`
	Province           string            `json:"province" gorm:"column:province;comment:省;type:string;"`
	City               string            `json:"city"  gorm:"column:city;comment:市;type:string;"`
	County             string            `json:"county"  gorm:"column:county;comment:区;type:string;"`
	Town               string            `json:"town"  gorm:"column:town;comment:街道;type:string;"`
	Status             int               `json:"status" gorm:"column:status;comment:0：待审核；1：通过；-1驳回;type:int;"`
	BecomeAt           *source.LocalTime `json:"become_at"`  // 成为代理时间
	UpgradeAt          *source.LocalTime `json:"upgrade_at"` // 升级时间
	OrderManageSwitch  int               `json:"order_manage_switch" gorm:"column:order_manage_switch;comment:订单管理开关 0：未开启；1：已开启;type:int;"`
	SpecialSwitch      int               `json:"special_switch" gorm:"column:special_switch;comment:独立比例开关 0：未开启；1：已开启;type:int;"`
	SpecialRatio       int               `json:"special_ratio" gorm:"column:special_ratio;comment:独立比例;type:int;"`
	ConsumeTotal       int64             `json:"consume_total" gorm:"column:consume_total;comment:区域消费总额;type:int;"`
	SettleAmountTotal  int64             `json:"settle_amount_total" gorm:"column:settle_amount_total;comment:累计结算金额;type:int;"`
	FinishSettleAmount int64             `json:"finish_settle_amount" gorm:"column:finish_settle_amount;comment:已结算奖励;type:int;"`
	WaitSettleAmount   int64             `json:"wait_settle_amount" gorm:"column:wait_settle_amount;comment:未结算奖励;type:int;"`
}

type Agency struct {
	source.Model
	Uid                uint              `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;"`
	RealName           string            `json:"real_name" form:"real_name" gorm:"column:real_name;comment:姓名;type:varchar(255);size:255;"`
	Mobile             string            `json:"mobile" form:"mobile" gorm:"column:mobile;comment:电话号;type:varchar(255);size:255;"`
	Level              int               `json:"level" gorm:"column:level;comment:代理级别;type:int;"`
	CountryId          int               `json:"country_id" gorm:"column:country_id;default:0;comment:国家id;type:int;"`
	ProvinceId         int               `json:"province_id" gorm:"column:province_id;comment:省份id;type:int;"`
	CityId             int               `json:"city_id" gorm:"column:city_id;comment:城市id;type:int;"`
	CountyId           int               `json:"county_id" gorm:"column:county_id;comment:区id;type:int;"`
	TownId             int               `json:"town_id" gorm:"column:town_id;comment:街道id;type:int;"`
	Province           string            `json:"province" gorm:"column:province;comment:省;type:string;"`
	City               string            `json:"city"  gorm:"column:city;comment:市;type:string;"`
	County             string            `json:"county"  gorm:"column:county;comment:区;type:string;"`
	Town               string            `json:"town"  gorm:"column:town;comment:街道;type:string;"`
	Status             int               `json:"status" gorm:"column:status;comment:0：待审核；1：通过；-1驳回;type:int;"`
	BecomeAt           *source.LocalTime `json:"become_at"`  // 成为代理时间
	UpgradeAt          *source.LocalTime `json:"upgrade_at"` // 升级时间
	OrderManageSwitch  int               `json:"order_manage_switch" gorm:"column:order_manage_switch;comment:订单管理开关 0：未开启；1：已开启;type:int;"`
	SpecialSwitch      int               `json:"special_switch" gorm:"column:special_switch;comment:独立比例开关 0：未开启；1：已开启;type:int;"`
	SpecialRatio       int               `json:"special_ratio" gorm:"column:special_ratio;comment:独立比例;type:int;"`
	ConsumeTotal       int64             `json:"consume_total" gorm:"column:consume_total;comment:区域消费总额;type:int;"`
	SettleAmountTotal  int64             `json:"settle_amount_total" gorm:"column:settle_amount_total;comment:累计结算金额;type:int;"`
	FinishSettleAmount int64             `json:"finish_settle_amount" gorm:"column:finish_settle_amount;comment:已结算奖励;type:int;"`
	WaitSettleAmount   int64             `json:"wait_settle_amount" gorm:"column:wait_settle_amount;comment:未结算奖励;type:int;"`
	UserInfo           model.User        `json:"user" gorm:"foreignKey:Uid"`
}

type AgencyApply struct {
	source.Model
	Uid        uint              `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;"`
	RealName   string            `json:"real_name" form:"real_name" gorm:"column:real_name;comment:姓名;type:varchar(255);size:255;"`
	Mobile     string            `json:"mobile" form:"mobile" gorm:"column:mobile;comment:电话号;type:varchar(255);size:255;"`
	Level      int               `json:"level" gorm:"column:level;comment:代理级别;type:int;"`
	CountryId  int               `json:"country_id" gorm:"column:country_id;default:0;comment:国家id;type:int;"`
	ProvinceId int               `json:"province_id" gorm:"column:province_id;comment:省份id;type:int;"`
	CityId     int               `json:"city_id" gorm:"column:city_id;comment:城市id;type:int;"`
	CountyId   int               `json:"county_id" gorm:"column:county_id;comment:区id;type:int;"`
	TownId     int               `json:"town_id" gorm:"column:town_id;comment:街道id;type:int;"`
	Province   string            `json:"province" gorm:"column:province;comment:省;type:string;"`
	City       string            `json:"city"  gorm:"column:city;comment:市;type:string;"`
	County     string            `json:"county"  gorm:"column:county;comment:区;type:string;"`
	Town       string            `json:"town"  gorm:"column:town;comment:街道;type:string;"`
	Status     int               `json:"status" gorm:"column:status;comment:0：待审核；1：通过；-1驳回;type:int;"`
	StatusName string            `json:"status_name" gorm:"-"`
	BecomeAt   *source.LocalTime `json:"become_at"` // 成为代理时间
	UserInfo   model.User        `json:"user" gorm:"foreignKey:Uid"`
}

type AgencyApplyByList struct {
	source.Model
	Uid        uint              `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;"`
	RealName   string            `json:"real_name" form:"real_name" gorm:"column:real_name;comment:姓名;type:varchar(255);size:255;"`
	Mobile     string            `json:"mobile" form:"mobile" gorm:"column:mobile;comment:电话号;type:varchar(255);size:255;"`
	Level      int               `json:"level" gorm:"column:level;comment:代理级别;type:int;"`
	LevelName  string            `json:"level_name" gorm:"-"`
	CountryId  int               `json:"country_id" gorm:"column:country_id;default:0;comment:国家id;type:int;"`
	ProvinceId int               `json:"province_id" gorm:"column:province_id;comment:省份id;type:int;"`
	CityId     int               `json:"city_id" gorm:"column:city_id;comment:城市id;type:int;"`
	CountyId   int               `json:"county_id" gorm:"column:county_id;comment:区id;type:int;"`
	TownId     int               `json:"town_id" gorm:"column:town_id;comment:街道id;type:int;"`
	Province   string            `json:"province" gorm:"column:province;comment:省;type:string;"`
	City       string            `json:"city"  gorm:"column:city;comment:市;type:string;"`
	County     string            `json:"county"  gorm:"column:county;comment:区;type:string;"`
	Town       string            `json:"town"  gorm:"column:town;comment:街道;type:string;"`
	Status     int               `json:"status" gorm:"column:status;comment:0：待审核；1：通过；-1驳回;type:int;"`
	StatusName string            `json:"status_name" gorm:"-"`
	BecomeAt   *source.LocalTime `json:"become_at"` // 成为代理时间
	UserInfo   model.User        `json:"user" gorm:"foreignKey:Uid"`
}

type SelectAgency struct {
	source.Model
	Uid                uint              `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;"`
	RealName           string            `json:"real_name" form:"real_name" gorm:"column:real_name;comment:姓名;type:varchar(255);size:255;"`
	Mobile             string            `json:"mobile" form:"mobile" gorm:"column:mobile;comment:电话号;type:varchar(255);size:255;"`
	Level              int               `json:"level" gorm:"column:level;comment:代理级别;type:int;"`
	LevelName          string            `json:"level_name" gorm:"-"`
	CountryId          int               `json:"country_id" gorm:"column:country_id;default:0;comment:国家id;type:int;"`
	ProvinceId         int               `json:"province_id" gorm:"column:province_id;comment:省份id;type:int;"`
	CityId             int               `json:"city_id" gorm:"column:city_id;comment:城市id;type:int;"`
	CountyId           int               `json:"county_id" gorm:"column:county_id;comment:区id;type:int;"`
	TownId             int               `json:"town_id" gorm:"column:town_id;comment:街道id;type:int;"`
	Province           string            `json:"province" gorm:"column:province;comment:省;type:string;"`
	City               string            `json:"city"  gorm:"column:city;comment:市;type:string;"`
	County             string            `json:"county"  gorm:"column:county;comment:区;type:string;"`
	Town               string            `json:"town"  gorm:"column:town;comment:街道;type:string;"`
	SpecialSwitch      int               `json:"special_switch" gorm:"column:special_switch;comment:独立比例开关 0：未开启；1：已开启;type:int;"`
	SpecialRatio       int               `json:"special_ratio" gorm:"column:special_ratio;comment:独立比例;type:int;"`
	ConsumeTotal       int64             `json:"consume_total" gorm:"column:consume_total;comment:区域消费总额;type:int;"`
	SettleAmountTotal  int64             `json:"settle_amount_total" gorm:"column:settle_amount_total;comment:累计结算金额;type:int;"`
	FinishSettleAmount int64             `json:"finish_settle_amount" gorm:"column:finish_settle_amount;comment:已结算奖励;type:int;"`
	WaitSettleAmount   int64             `json:"wait_settle_amount" gorm:"column:wait_settle_amount;comment:未结算奖励;type:int;"`
	BecomeAt           *source.LocalTime `json:"become_at"` // 成为代理时间
	UserInfo           model.User        `json:"user" gorm:"foreignKey:Uid"`
}

func getSettleAmountTotal(tx *gorm.DB, uid uint) (err error, amountTotal int64) {
	err = tx.Model(&SettleAward{}).Select("COALESCE(SUM(amount), 0)").Where("uid = ?", uid).First(&amountTotal).Error
	return
}

func getFinishSettleAmount(tx *gorm.DB, uid uint) (err error, amountTotal int64) {
	err = tx.Model(&SettleAward{}).Select("COALESCE(SUM(amount), 0)").Where("uid = ? AND status = ?", uid, 1).First(&amountTotal).Error
	return
}

func getWaitSettleAmount(tx *gorm.DB, uid uint) (err error, amountTotal int64) {
	err = tx.Model(&SettleAward{}).Select("COALESCE(SUM(amount), 0)").Where("uid = ? AND status = ?", uid, 0).First(&amountTotal).Error
	return
}

func (a *Agency) BeforeSave(tx *gorm.DB) (err error) {
	a.Province = service.GetRegionName(a.ProvinceId)
	a.City = service.GetRegionName(a.CityId)
	a.County = service.GetRegionName(a.CountyId)
	a.Town = service.GetRegionName(a.TownId)
	return
}

func (a *AgencyApply) BeforeSave(tx *gorm.DB) (err error) {
	a.Province = service.GetRegionName(a.ProvinceId)
	a.City = service.GetRegionName(a.CityId)
	a.County = service.GetRegionName(a.CountyId)
	a.Town = service.GetRegionName(a.TownId)
	return
}

func (a *AgencyApply) AfterFind(tx *gorm.DB) (err error) {
	if a.Status == 0 {
		a.StatusName = "待审核"
	} else if a.Status == -1 {
		a.StatusName = "已驳回"
	} else {
		a.StatusName = "已审核"
	}
	return
}

func (a *SelectAgency) AfterFind(tx *gorm.DB) (err error) {
	err, a.SettleAmountTotal = getSettleAmountTotal(tx, a.Uid)
	if err != nil {
		return
	}
	err, a.FinishSettleAmount = getFinishSettleAmount(tx, a.Uid)
	if err != nil {
		return
	}
	err, a.WaitSettleAmount = getWaitSettleAmount(tx, a.Uid)
	if err != nil {
		return
	}
	var setting Setting
	err = tx.Where("`key` = ?", "area_agency_setting").First(&setting).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		setting.Values.ProvinceRatio = 0
		setting.Values.CityRatio = 0
		setting.Values.CountyRatio = 0
		setting.Values.TownRatio = 0
	}
	a.LevelName = "无等级"
	if a.Level == 1 {
		a.LevelName = "省级代理"
		if a.SpecialSwitch != 1 {
			a.SpecialRatio = setting.Values.ProvinceRatio
		}
	} else if a.Level == 2 {
		a.LevelName = "市级代理"
		if a.SpecialSwitch != 1 {
			a.SpecialRatio = setting.Values.CityRatio
		}
	} else if a.Level == 3 {
		a.LevelName = "区级代理"
		if a.SpecialSwitch != 1 {
			a.SpecialRatio = setting.Values.CountyRatio
		}
	} else if a.Level == 4 {
		a.LevelName = "街级代理"
		if a.SpecialSwitch != 1 {
			a.SpecialRatio = setting.Values.TownRatio
		}
	}

	return
}

func (a *AgencyApplyByList) AfterFind(tx *gorm.DB) (err error) {
	a.LevelName = "无等级"
	if a.Level == 1 {
		a.LevelName = "省级代理"
	} else if a.Level == 2 {
		a.LevelName = "市级代理"
	} else if a.Level == 3 {
		a.LevelName = "区级代理"
	} else if a.Level == 4 {
		a.LevelName = "街级代理"
	}

	if a.Status == 0 {
		a.StatusName = "待审核"
	} else if a.Status == -1 {
		a.StatusName = "已驳回"
	} else {
		a.StatusName = "已审核"
	}

	return
}

func (AgencyMigration) TableName() string {
	return "area_agency"
}

func (Agency) TableName() string {
	return "area_agency"
}

func (AgencyApply) TableName() string {
	return "area_agency"
}

func (SelectAgency) TableName() string {
	return "area_agency"
}

func (AgencyApplyByList) TableName() string {
	return "area_agency"
}
