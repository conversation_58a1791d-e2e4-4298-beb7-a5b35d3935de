package goods

import (
	pmodel "product/model"
	callback2 "public-supply/callback"
	"public-supply/model"
	pubreq "public-supply/request"
	"sync"
)

var (
	category []model.Category
	mutex    sync.Mutex
)

//@author: [ccfish86](https://github.com/ccfish86)
//@author: [SliverHorn](https://github.com/SliverHorn)
//@interface_name: OSS
//@description: OSS接口

type Goods interface {
	InitSetting(gatherSupplyID uint) (err error)
	GetGoods(info pubreq.GetGoodsSearch) (err error, data interface{}, total int64)
	ImportGoodsRun(info pubreq.GetGoodsSearch) (err error, data interface{})
	GetCategory(info pubreq.GetCategorySearch) (err error, data interface{})
	GetGroup() (err error, data interface{})
	GetCategoryChild(pid int, info pubreq.GetCategoryChild) (err error, data interface{})
	RunConcurrent(wg *sync.WaitGroup, info pubreq.GetCategorySearch, i int) (err error)
	CommodityAssembly(list []model.Goods, cateId1, cateId2, cateId3 int, gatherSupplyID uint, isCreate int) (err error, listGoods []*pmodel.Product)
	GoodsStorageAdd(ids []int, supplyId uint) (err error, info interface{})
	GoodsPriceAlert(GoodsData callback2.GoodsCallBack) (err error)
	InitGoods() (err error)
}
