[{"ID": 440, "menuId": "440", "path": "brandCardIndex", "name": "brandCardIndex", "hidden": true, "parentId": "0", "component": "view/brandCard/index.vue", "meta": {"title": "必应鸟卡券资源", "icon": "user-solid", "defaultMenu": false, "keepAlive": false}, "sort": 40, "parameters": []}, {"ID": 441, "menuId": "441", "path": "brandCardBaseindex", "name": "brandCardBaseindex", "hidden": false, "parentId": "440", "component": "view/brandCard/base/index.vue", "meta": {"title": "基础设置", "icon": "info", "defaultMenu": false, "keepAlive": false}, "sort": 2, "parameters": []}, {"ID": 442, "menuId": "442", "path": "brandCardGoodsCardManageIndex", "name": "brandCardGoodsCardManageIndex", "hidden": false, "parentId": "440", "component": "view/brandCard/goodsCardManage/index.vue", "meta": {"title": "卡卷商品管理", "icon": "info", "defaultMenu": false, "keepAlive": false}, "sort": 3, "parameters": []}, {"ID": 443, "menuId": "443", "path": "brandCardOrderManageIndex", "name": "brandCardOrderManageIndex", "hidden": false, "parentId": "440", "component": "view/brandCard/orderManage/index.vue", "meta": {"title": "订单管理", "icon": "info", "defaultMenu": false, "keepAlive": false}, "sort": 4, "parameters": []}]