package cron

import (
	"cloud/model"
	"yz-go/cron"
	"yz-go/source"
)

// 获取云仓订单，在中台进行下单
func CloudWarehouseOrders() {
	task := cron.Task{
		Key:  "synCloudOrderCreateOrder",
		Name: "CloudWarehouseOrders",
		Spec: "0 0/50 * * * *",
		Handle: func(task cron.Task) {
			UpdateCloudOrderStatus()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func UpdateCloudOrderStatus() {
	var zeroStatusOrders []model.CloudOrder
	// 查询 status=0 且 order_id 为空的数据
	err := source.DB().Where("status = 0 AND order_id = ''").Order("id desc").Find(&zeroStatusOrders).Error
	if err != nil {
		return
	}
	if len(zeroStatusOrders) == 0 {
		return
	}
	var hasHigherStatus bool
	// 验证大于该 id 的数据是否有 status 大于 0 的
	err = source.DB().Model(&model.CloudOrder{}).Where("id > ? AND status > 0", zeroStatusOrders[0].ID).Select("1").Scan(&hasHigherStatus).Error
	if err != nil {
		return
	}

	if hasHigherStatus {
		var ids []uint
		for _, item := range zeroStatusOrders {
			ids = append(ids, item.ID)
		}
		// 更新 status=0 且 order_id 为空的数据的 status 为 -1，msg 为系统错误
		err = source.DB().Model(&model.CloudOrder{}).Where("id in ?", ids).Updates(map[string]interface{}{
			"status":    -1,
			"error_msg": "系统错误",
		}).Error
		if err != nil {
			return
		}
	}

	return
}
