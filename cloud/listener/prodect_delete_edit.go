package listener

import (
	"cloud/service"
	"go.uber.org/zap"
	"product/mq"
	"yz-go/component/log"
)

func ProductDeleteEditListenerHandles() {
	//fmt.Println("云仓监听商品消息删除与修改")

	mq.PushHandles("CloudGoodsEditAndDelete", 1, func(product mq.ProductMessage) error {
		//fmt.Println("云仓接收商品消息：", product)
		var err error
		if product.MessageType == mq.Delete || product.MessageType == mq.Edit || product.MessageType == mq.Undercarriage || product.MessageType == mq.OnSale {
			err = service.ListenerCloudGoods(product.ProductID, product.MessageType)
			if err != nil {
				log.Log().Error(err.Error(), zap.Any("err", err))
			}
		}
		return nil
	})
}
