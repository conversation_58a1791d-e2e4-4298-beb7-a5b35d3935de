package route

import (
	v1 "cloud/api/v1"
	"github.com/gin-gonic/gin"
)

// 云仓
func InitCloudRouter(Router *gin.RouterGroup) {
	cloud := Router.Group("cloud")
	{
		cloud.GET("getProductsList", v1.GetProductsList)         //获取可推送商品列表
		cloud.GET("getFreightList", v1.GetFreightList)           //获取运费模板列表
		cloud.GET("getCateBusinessList", v1.GetCateBusinessList) //获取分类
		cloud.GET("getStagsList", v1.GetStagsList)               //获取服务标签

		cloud.GET("getGoodsList", v1.GetGoodsList) //获取商品列表
		cloud.GET("getGoodsOn", v1.GetGoodsOn)     //取云仓下架的商品与中台上架的进行比对如果中台上架则进行更新

		cloud.GET("getCloudGoodsDetail", v1.GetCloudGoodsDetail) //云仓商品详情

		cloud.POST("updateGoodsOnsale", v1.UpdateGoodsOnsale) //修改商品上下架状态
		cloud.POST("deleteGoods", v1.DeleteGoods)             //删除商品
		cloud.GET("getCloudFreight", v1.GetCloudFreight)      //获取运费模板详情
		cloud.POST("deleteFreight", v1.DeleteFreight)         //删除运费模板详情
		cloud.POST("addFreight", v1.AddFreight)               //添加运费模板详情
		cloud.POST("updateFreight", v1.UpdateFreight)         //修改运费模板详情
		cloud.GET("getCloudOrderList", v1.GetCloudOrderList)  //获取云仓订单列表
		cloud.GET("getDeliverList", v1.GetDeliverList)        //获取物流公司
		cloud.GET("getCloudOrder", v1.GetCloudOrder)          //获取订单详情
		cloud.GET("getCloudDeliver", v1.GetCloudDeliver)      //获取订单商品物流详情

		cloud.POST("cloudOrderSend", v1.CloudOrderSend)             //云仓订单发货（旧）
		cloud.POST("cloudOrderSendNew", v1.CloudOrderSendNew)       //云仓订单发货(新)
		cloud.POST("UpdateCloudOrderSend", v1.UpdateCloudOrderSend) //云仓订单重新发货(新)

		cloud.GET("getRefundList", v1.GetRefundList)       //售后列表
		cloud.GET("getRefundDetatil", v1.GetRefundDetatil) //售后详情
		cloud.GET("getAddressList", v1.GetAddressList)     //售后地址列表
		cloud.GET("getRegionList", v1.GetRegionList)       //获取地区

		cloud.POST("addRefundAddress", v1.AddRefundAddress)       //添加售后地址
		cloud.POST("updateRefundAddress", v1.UpdateRefundAddress) //修改售后地址
		cloud.GET("getRefundAddress", v1.GetRefundAddress)        //获取售后地址详情
		cloud.GET("deleteRefundAddress", v1.DeleteRefundAddress)  //删除退货地址
		cloud.POST("cloudRefundAgree", v1.CloudRefundAgree)       //同意售后
		cloud.POST("cloudRefundReject", v1.CloudRefundReject)     //拒绝售后

		cloud.POST("cloudPushGoods", v1.CloudPushGoods)                     //推送商品
		cloud.POST("cloudUpdateGoods", v1.CloudUpdateGoods)                 //更新商品
		cloud.POST("cloudUpdateAllGoods", v1.CloudUpdateAllGoods)           //一键更新所有待更新商品
		cloud.POST("cloudUpdateGoodsBySource", v1.CloudUpdateGoodsBySource) //根据来源批量更新商品

		cloud.POST("cloudAllPushGoods", v1.CloudAllPushGoods)         //推送全部商品
		cloud.POST("updateCloudGoodsStock", v1.UpdateCloudGoodsStock) //一键更新库存

		cloud.GET("getGatherSupplies", v1.GetGatherSupplies)                       //获取供应链（去掉胜天半子类型的供应链）
		cloud.GET("getCloudPushGoodsMessageList", v1.GetCloudPushGoodsMessageList) //获取全部推送记录列表
		cloud.GET("exportCloudOrder", v1.ExportCloudOrder)                         //导出订单

		cloud.POST("cloudOrderCreate", v1.CloudOrderCreate) //选择订单 下单到中台

		cloud.GET("getCloudOrderRecordList", v1.GetCloudOrderRecordList) //获取获取云仓记录表列表

		cloud.POST("cloudOrderCreateNewStep1APi", v1.CloudOrderCreateNewStep1APi) //指定时间同步下单

		//测试使用
		cloud.POST("cronCloudOrderCreate", v1.CronCloudOrderCreate) //定时任务（测试使用） 获取云仓当天订单，在中台进行下单
		cloud.POST("cronCloudOrderPay", v1.CronCloudOrderPay)       //定时任务（测试使用） 获取获取支付失败的订单进行支付

		cloud.POST("cronCloudSendOrder", v1.CronCloudSendOrder)                         //定时任务（测试使用） 同步云仓发货订单信息到中台
		cloud.POST("cronCloudProductSoldOut", v1.CloudProductSoldOut)                   //  同步中台与云仓商品下架状态
		cloud.POST("cloudProductDeleteSoldOutStep1", v1.CloudProductDeleteSoldOutStep1) // 同步中台与云仓商品下架状态 中台删除product记录也没有发消息导致云仓没有下架 y
		cloud.POST("cloudProductOnStep1", v1.CloudProductOnStep1)                       // 同步中台与云仓商品上架状态 中台删除product记录也没有发消息导致云仓没有下架 y

		cloud.POST("lisOrderSend", v1.LisOrderSend)                               //监听任务（测试使用） 同步云仓发货订单信息到中台
		cloud.POST("getSendErrorOrder", v1.GetSendErrorOrder)                     //获取自定发货失败的订单重新发货
		cloud.POST("updateSendErrorOrder", v1.UpdateSendErrorOrder)               //指定code重新发货
		cloud.POST("clearCloudPushGoodsMessages", v1.ClearCloudPushGoodsMessages) //获取自定发货失败的订单重新发货

		cloud.GET("getCloudOrderProduct", v1.GetCloudOrderProduct)                       //手动填写云仓信息同步云仓订单时的云仓订单详情
		cloud.POST("manualCloudOrderCreate", v1.ManualCloudOrderCreate)                  //手动填写云仓信息同步云仓订单时的云仓订单详情
		cloud.POST("productRelevanceCloudGoods", v1.ProductRelevanceCloudGoods)          //手动填写云仓商品ID与中台商品ID进行关联
		cloud.POST("deleteCloudGoods", v1.DeleteCloudGoods)                              //删除中台推送云仓 推送重复商品
		cloud.GET("updateCloudOrderIsOffline", v1.UpdateCloudOrderIsOffline)             //修改云仓订单记录的状态变为已下线处理
		cloud.GET("updateCloudPushGoodsMessageType", v1.UpdateCloudPushGoodsMessageType) //修改推送/更新商品记录变为已处理

		cloud.POST("createMiddlegroundCloudExpressMatching", v1.CreateMiddlegroundCloudExpressMatching)  //创建对应关系
		cloud.POST("updateMiddlegroundCloudExpressMatching", v1.UpdateMiddlegroundCloudExpressMatching)  //修改对应关系
		cloud.POST("deleteMiddlegroundCloudExpressMatching", v1.DeleteMiddlegroundCloudExpressMatching)  //删除对应关系
		cloud.GET("getMiddlegroundCloudExpressMatchingList", v1.GetMiddlegroundCloudExpressMatchingList) //获取对应关系列表

	}
}
