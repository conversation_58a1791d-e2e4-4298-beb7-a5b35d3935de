package service

import (
	"cloud/common"
	model2 "cloud/model"
	cloudMq "cloud/mq"
	"encoding/json"
	"errors"
	"github.com/gogf/gf/frame/g"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"math"
	orderModel "order/model"
	orderPay "order/pay"
	payment "payment/entrance"
	paymentModel "payment/model"
	shoppingCartModel "shopping-cart/model"
	shoppingCartService "shopping-cart/service"
	"strconv"
	"time"
	"trade/checkout"
	"trade/confirm"
	tradeRequest "trade/request"
	"trade/service"
	"yz-go/cache"
	"yz-go/component/log"
	"yz-go/source"
)

// 指定时间同步订单
func CloudOrderCreateNewStep1APi(config common.SupplySetting, page int, GatherSuppliesId uint, createdStartTime, createdEndTime int64) {
	var body = make(map[string]interface{})
	var limit = 100
	body["page"] = page
	body["limit"] = limit

	body["created_start_time"] = createdStartTime //今天开始的时间
	body["created_end_time"] = createdEndTime     //当前时间
	//body["order_sn"] = "SN20220628d573452afe7b_1"
	//log.Log().Error("获取云仓订单列表条件!", zap.Any("where", body))
	res, err := common.RequestApi(string(common.GetCloudOrderList), "POST", nil, body, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		log.Log().Error("获取云仓订单列表失败!", zap.Any("err", res.Message))
		return
	}
	if res.Data == "" {
		log.Log().Error("暂时云仓订单!", zap.Any("err", "暂无订单"))
		return
	}

	datas := res.Data.(map[string]interface{})
	var cloudOrderList common.CloudOrderList
	json1, _ := json.Marshal(datas)
	_ = json.Unmarshal(json1, &cloudOrderList)

	CloudOrderCreateNewStep2(config, cloudOrderList, GatherSuppliesId)

	if cloudOrderList.Count > page*limit {
		page++
		CloudOrderCreateNewStep1APi(config, page, GatherSuppliesId, createdStartTime, createdEndTime)
	}
}

// (云仓订单：自动下单步骤1  获取订单列表)
func CloudOrderCreateNewStep1(config common.SupplySetting, page int, GatherSuppliesId uint) {
	var body = make(map[string]interface{})
	var limit = 100
	body["page"] = page
	body["limit"] = limit
	t := time.Now()
	tm2 := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
	var hour = t.Hour()
	if hour == 0 || hour == 24 {
		tm2 = time.Date(t.Year(), t.Month(), t.Day()-1, 0, 0, 0, 0, t.Location())
	}

	body["created_start_time"] = tm2.Unix() - 1 //今天开始的时间
	body["created_end_time"] = t.Unix()         //当前时间
	//body["order_sn"] = "SN20220628d573452afe7b_1"
	//log.Log().Error("获取云仓订单列表条件!", zap.Any("where", body))
	res, err := common.RequestApi(string(common.GetCloudOrderList), "POST", nil, body, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		log.Log().Error("获取云仓订单列表失败!", zap.Any("err", res.Message))
		return
	}
	if res.Data == "" {
		log.Log().Error("暂时云仓订单!", zap.Any("err", "暂无订单"))
		return
	}

	datas := res.Data.(map[string]interface{})
	var cloudOrderList common.CloudOrderList
	json1, _ := json.Marshal(datas)
	_ = json.Unmarshal(json1, &cloudOrderList)

	CloudOrderCreateNewStep2(config, cloudOrderList, GatherSuppliesId)

	if cloudOrderList.Count > page*limit {
		page++
		CloudOrderCreateNewStep1(config, page, GatherSuppliesId)
	}
}

// (云仓订单：自动下单步骤2  循环云仓订单列表)
func CloudOrderCreateNewStep2(config common.SupplySetting, cloudOrderList common.CloudOrderList, GatherSuppliesId uint) {
	var err error
	for _, item := range cloudOrderList.List {
		//不获取详情直接使用列表的数据
		//res,err := common.RequestApi(string(common.GetOrder), "POST", nil, g.Map{"order_id": item.Order.Id}, config)
		//if err != nil {
		//	err = errors.New(err.Error())
		//	return
		//}
		//if res.Code != 1 {
		//	log.Log().Error("获取云仓订单详情失败!", zap.Any("err", res.Message))
		//	return
		//}
		//
		//var cloudOrderDetail CloudOrderDetail //云仓订单详情数据
		//datas := res.Data.(map[string]interface{})
		//json1, _ := json.Marshal(datas)
		//_ = json.Unmarshal(json1, &cloudOrderDetail)

		err = CloudOrderCreateNewStep3(config, item, GatherSuppliesId)
		if err != nil {
			log.Log().Error("云仓下单:错误", zap.Any("err", err))
		}
	}
}

// (云仓订单：自动下单步骤3  判断是否已经下单过 如果待支付则支付，单独触发直接获取详情调用步骤3即可)
func CloudOrderCreateNewStep3(config common.SupplySetting, cloudOrderDetail common.CloudOrder, GatherSuppliesId uint) (err error) {
	//测试数据
	//cloudOrderDetail.OrderGoodsDetail[0].GoodsId = 27654
	//cloudOrderDetail.OrderGoodsDetail[0].GoodsOptionId = 1932690
	//config.Cloud.CloudUserId = 35dd

	var cloudOrderModel model2.CloudOrder //云仓记录表数据
	//如果中台订单存在 直接跳过
	err = source.DB().Where("cloud_order_id = ?", cloudOrderDetail.Order.Id).First(&cloudOrderModel).Error
	//数据库出错跳过
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Log().Error("云仓自动下单,数据库错误-直接跳过", zap.Any("err", err))
		err = nil
		return
	}
	//已创建记录直接跳过
	if cloudOrderModel.ID != 0 {
		return
	}
	var afterCount = 0 //存在售后的数量
	for _, itemOrderGoods := range cloudOrderDetail.OrderGoods {
		var cloudGoods model2.CloudGoods //云仓推送商品记录表数据
		source.DB().Where("cloud_product_id = ?", itemOrderGoods.GoodsId).First(&cloudGoods)
		//如果不是中台推送的商品直接跳过
		if cloudGoods.ID == 0 {
			err = errors.New("云仓下单:不是中台推送的商品，云仓商品id" + strconv.Itoa(itemOrderGoods.GoodsId))
			return
		}
		if itemOrderGoods.PayStatus != 1 {
			var payStatusText = ""
			switch itemOrderGoods.PayStatus {
			case 0:
				payStatusText = "待付款"
				break
			case 2:
				payStatusText = "申请退款"
				break
			case 3:
				payStatusText = "退款中"
				break
			case 4:
				payStatusText = "已退款"
				break
			case 5:
				payStatusText = "退款申请失败"
				break
			}
			err = errors.New("云仓下单:云仓订单状态不满足下单条件:" + payStatusText)
			afterCount++
			continue
		}
	}
	if afterCount == len(cloudOrderDetail.OrderGoods) {
		return
	}
	//创建记录
	var cloudOrder model2.CloudOrder
	cloudOrder.Status = 0
	cloudOrder.CloudOrderId = cloudOrderDetail.Order.Id
	cloudOrder.CloudOrderSn = cloudOrderDetail.Order.OrderSn
	cloudOrder.CloudOrderTotalPrice = cloudOrderDetail.Order.OrderTotalPrice
	cloudOrder.GatherSuppliesId = GatherSuppliesId
	err = source.DB().Create(&cloudOrder).Error
	if err != nil {
		log.Log().Error("云仓下单:创建记录失败", zap.Any("err", err))
		return
	}
	err = cloudMq.PublishMessage(cloudMq.CloudCreateOrder, cloudOrderDetail, config.Cloud.CloudUserId, cloudOrder.ID, config.BaseInfo.AppKey, config.BaseInfo.AppSecret)
	if err != nil {
		log.Log().Error("云仓下单:推送队列失败", zap.Any("err", err))
		return
	}
	//此处变为队列执行
	//err, code := CloudOrderCreateNewStep4(config, cloudOrderDetail, cloudOrder)
	//if err != nil {
	//	log.Log().Error("云仓下单:下单支付发货错误", zap.Any("err", err))
	//	var cloudOrderUpdate model2.CloudOrder
	//	//支付错误 不变为-1 再次请求时直接执行支付
	//	if code == 1 {
	//		cloudOrderUpdate.Status = -1
	//		cloudOrderUpdate.ErrorMsg = err.Error()
	//		//cloudOrder.SynStatus = 1 //因为同步失败状态变为待同步
	//		source.DB().Where("id = ?", cloudOrder.ID).Updates(&cloudOrderUpdate)
	//	} else {
	//		cloudOrderUpdate.ErrorMsg = err.Error()
	//		source.DB().Where("id = ?", cloudOrder.ID).Updates(&cloudOrderUpdate)
	//	}
	//}
	return
}

// (云仓订单：自动下单步骤4  下单支付)
// code 错误类型 1正常错误,2支付错误  用于返回判断是否改云仓订单记录表状态为-1 1改 2不改
func CloudOrderCreateNewStep4(config common.SupplySetting, cloudOrderDetail common.CloudOrder, cloudOrder model2.CloudOrder) (err error, code int) {
	one := 1
	zero := 0
	code = 1
	var confirmOrder ConfirmOrder

	for _, itemOrderGoods := range cloudOrderDetail.OrderGoods {
		//状态不对直接跳过ser
		if itemOrderGoods.PayStatus != 1 {
			continue
		}

		var sku CloudSku

		////测试数据
		//var specsGroup model2.SpecsGroup
		//specsGroup.Id = 1932690
		//specsGroup.OutOptionId = 18771
		//var cloudGoodsDetail model2.CloudPushGoods
		//cloudGoodsDetail.SpecsGroup = append(cloudGoodsDetail.SpecsGroup,specsGroup)

		var res *common.APIResult
		res, err = common.RequestApi(string(common.CloudGetGoods), "POST", nil, g.Map{"goods_id": itemOrderGoods.GoodsId}, config)
		if err != nil {
			err = errors.New(err.Error())
			return
		}
		//详情查询失败直接退出
		if res.Code != 1 {
			err = errors.New("云仓下单:获取云仓商品详情失败!" + strconv.Itoa(itemOrderGoods.GoodsId))
			log.Log().Error("获取云仓商品详情失败!"+strconv.Itoa(itemOrderGoods.GoodsId), zap.Any("err", res.Message))
			return
		}

		var cloudGoodsDetail model2.CloudPushGoods
		CloudGoodsData := res.Data.(map[string]interface{})
		CloudGoodsData1, _ := json.Marshal(CloudGoodsData)
		_ = json.Unmarshal(CloudGoodsData1, &cloudGoodsDetail)

		for _, itemGoodsSpecsGroup := range cloudGoodsDetail.SpecsGroup {
			if itemGoodsSpecsGroup.OutOptionId == 0 {
				err = errors.New("云仓下单:云仓商品详情第三方规格是0,云仓商品id" + strconv.Itoa(itemOrderGoods.GoodsId))
				return
			}
			//测试用完就删除
			//if itemGoodsSpecsGroup.Id == 13306932 {
			//	itemGoodsSpecsGroup.Id = 13166733
			//}
			//if itemOrderGoods.GoodsId == 70619 {
			//	itemGoodsSpecsGroup.OutOptionId = 130981 //测试用完就删除
			//}
			////130981
			//if itemOrderGoods.GoodsId == 101708 {
			//	itemGoodsSpecsGroup.OutOptionId = 133408 //测试用完就删除
			//}
			//获取商品规格id参数
			if itemGoodsSpecsGroup.Id == itemOrderGoods.GoodsOptionId {

				sku.SkuId = itemGoodsSpecsGroup.OutOptionId
				sku.Number = itemOrderGoods.Total
				sku.CloudGoodsOptionTitle = itemOrderGoods.GoodsOptionTitle
				sku.CloudGoodsId = uint(itemOrderGoods.GoodsId)
				sku.CloudGoodsOptionId = itemOrderGoods.GoodsOptionId
				sku.CloudGoodsTitle = itemOrderGoods.Title
				sku.CloudGoodsOrderSn = itemOrderGoods.GoodsOrderSn

				sku.Status = itemOrderGoods.GoodsStatus
				sku.PayStatus = itemOrderGoods.PayStatus

				confirmOrder.Sku = append(confirmOrder.Sku, sku)
			}
		}
		//如果没有匹配上
		if sku.SkuId == 0 {
			//此处应该插入记录表数据 说明这个商品的规格找不到
			err = errors.New("云仓下单:云仓订单商品规格与商品详情规格匹配不上,云仓商品id" + strconv.Itoa(itemOrderGoods.GoodsId))
			return
		}

	}
	if confirmOrder.Sku == nil {
		err = errors.New("没有匹配到商品，订单id" + strconv.Itoa(cloudOrderDetail.Order.Id))
		return
	}
	//地区匹配
	cloudOrderDetail = OrderAddressNew(cloudOrderDetail)
	//下单开始
	confirmOrder.Address.Consignee = cloudOrderDetail.Order.RealName
	confirmOrder.Address.Phone = cloudOrderDetail.Order.Mobile
	confirmOrder.Address.Province = cloudOrderDetail.Order.Provice
	confirmOrder.Address.City = cloudOrderDetail.Order.City
	confirmOrder.Address.Area = cloudOrderDetail.Order.District
	confirmOrder.Address.Street = cloudOrderDetail.Order.Street
	confirmOrder.Address.Description = cloudOrderDetail.Order.City + "," + cloudOrderDetail.Order.District + "," + cloudOrderDetail.Order.Street + "," + cloudOrderDetail.Order.Address

	//获取地址id
	err, addressId := service.GetOrSetAddress(tradeRequest.Address(confirmOrder.Address))

	if err != nil {
		log.Log().Error("云仓下单:云仓下单获取地址出错!", zap.Any("err", err))
		err = errors.New("云仓下单:云仓下单获取地址出错:" + err.Error())
		return
	}

	id, err := cache.GetID("api_buy")
	if err != nil {
		err = errors.New("云仓下单:buy_id生成失败:" + err.Error())
		log.Log().Error("云仓下单:buy_id生成失败!", zap.Any("err", err))
		return
	}
	buyID := id*int64(math.Pow(10, 10)) + time.Now().Unix()
	//插入购物车记录
	for _, item := range confirmOrder.Sku {
		shoppingCart := shoppingCartModel.ShoppingCart{
			UserID:    config.Cloud.CloudUserId,
			SkuID:     item.SkuId,
			Qty:       item.Number,
			Status:    &zero,
			Checked:   &one,
			BuyID:     uint(buyID),
			BuyWay:    3,
			AddressID: addressId,
		}
		err = shoppingCartService.CreateShoppingCart(shoppingCart)
		if err != nil {
			log.Log().Error("云仓下单:购物车添加失败,SkuId:"+strconv.Itoa(int(item.SkuId)), zap.Any("err", err))
			err = errors.New("云仓下单:购物车添加失败,SkuId:" + strconv.Itoa(int(item.SkuId)) + "" + err.Error())
			return
		}
	}
	// 读取购物车记录
	err, shoppingCarts := checkout.GetCheckedShoppingCarts(checkout.ShoppingCart{UserID: config.Cloud.CloudUserId, BuyID: uint(buyID)})
	if err != nil {
		log.Log().Error("云仓下单:获取购物车记录失败", zap.Any("err", err))
		err = errors.New("云仓下单:获取购物车记录失败:" + err.Error())
		return
	}

	if len(shoppingCarts) == 0 {
		log.Log().Error("云仓下单:没有购物车记录")
		err = errors.New("云仓下单:没有购物车记录")
		return
	}
	var shoppingCartIDs []uint
	for _, shoppingCart := range shoppingCarts {
		shoppingCartIDs = append(shoppingCartIDs, shoppingCart.ID)
	}
	err = shoppingCartService.CheckBeforeSale(shoppingCartIDs)
	if err != nil {
		log.Log().Error("云仓下单:购物车检验失败", zap.Any("err", err))
		err = errors.New("云仓下单:购物车检验失败:" + err.Error())
		return
	}
	// 结算信息
	err, checkoutInfo := checkout.ShoppingCartCheckout(config.Cloud.CloudUserId, shoppingCarts)
	if err != nil {
		log.Log().Error("云仓下单:结算信息获取失败", zap.Any("err", err))
		err = errors.New("云仓下单:结算信息获取失败:" + err.Error())
		return
	}

	// 下单
	checkoutInfo.ThirdOrderSn = cloudOrderDetail.Order.OrderSn

	//如果云仓运费不是0则买家备注增加运费备注
	if cloudOrderDetail.Order.DispatchPrice != 0 {
		dispatchPrice := Fen2Yuan(cloudOrderDetail.Order.DispatchPrice)

		var remark = "云仓运费：" + dispatchPrice + "元"

		for orderK, _ := range checkoutInfo.Orders {
			checkoutInfo.Orders[orderK].Remark = remark
		}
	}

	err, confirmInfo := confirm.ShoppingCartConfirm(checkoutInfo)

	if err != nil {
		log.Log().Error("云仓下单:下单失败", zap.Any("err", err))
		err = errors.New("云仓下单:下单失败:" + err.Error())
		return
	}
	err = checkout.ClearCheckedShoppingCarts(checkout.ShoppingCart{UserID: config.Cloud.CloudUserId, BuyID: uint(buyID), BuyWay: 3})
	if err != nil {
		log.Log().Error("云仓下单:清空购物车记录", zap.Any("err", err))
		err = errors.New("云仓下单:清空购物车记录:" + err.Error())
		return
	}
	//下单结束
	//优先存储云仓子订单记录表数据
	for _, confirmInfoItem := range confirmInfo.Orders {
		cloudOrder.OrderSn += strconv.Itoa(int(confirmInfoItem.OrderSN)) + ","
		cloudOrder.OrderId += strconv.Itoa(int(confirmInfoItem.ID)) + ","

		for _, confirmInfoOrderItem := range confirmInfoItem.OrderItems {
			//var cloudOrderData  model2.CloudOrder
			//source.DB().Where("order_id = ",confirmInfoItem.ID).First(&cloudOrderData)
			var cloudOrderItem model2.CloudOrderItem
			cloudOrderItem.CloudOrderId = cloudOrder.ID
			cloudOrderItem.OrderId = confirmInfoItem.ID
			cloudOrderItem.OrderSn = confirmInfoItem.OrderSN
			cloudOrderItem.OrderItemId = confirmInfoOrderItem.ID
			cloudOrderItem.Total = confirmInfoOrderItem.Qty
			cloudOrderItem.ProductId = confirmInfoOrderItem.ProductID
			cloudOrderItem.SkuId = confirmInfoOrderItem.SkuID
			//拼接云仓商品订单号 用于发货 其他字段作用待定
			for _, confirmOrderSku := range confirmOrder.Sku {
				if confirmOrderSku.SkuId == confirmInfoOrderItem.SkuID {
					cloudOrderItem.CloudGoodsOptionId = confirmOrderSku.CloudGoodsOptionId
					cloudOrderItem.CloudGoodsTitle = confirmOrderSku.CloudGoodsTitle
					cloudOrderItem.CloudGoodsOptionTitle = confirmOrderSku.CloudGoodsOptionTitle
					cloudOrderItem.CloudGoodsOrderSn = confirmOrderSku.CloudGoodsOrderSn
					cloudOrderItem.CloudGoodsId = confirmOrderSku.CloudGoodsId
					cloudOrderItem.Status = confirmOrderSku.Status
					cloudOrderItem.PayStatus = confirmOrderSku.PayStatus
					break
				}
			}
			err = source.DB().Create(&cloudOrderItem).Error
			if err != nil {
				//如果记录表创建失败 直接关闭订单 并标记出错原因
				log.Log().Error("云仓下单:云仓订单记录子表创建失败", zap.Any("err", err), zap.Any("cloudOrderSn", cloudOrderDetail.Order.OrderSn), zap.Any("出错的记录:cloudOrderItem", cloudOrderItem))

				confirmInfoItem.Status = -1
				confirmInfoItem.Note = "云仓下单订单号" + cloudOrderDetail.Order.OrderSn + ":云仓子订单记录表创建失败" + err.Error()
				source.DB().Model(&orderModel.Order{}).Where("id = ?", confirmInfoItem.ID).Updates(&confirmInfoItem)

				err = errors.New("云仓下单:订单号:" + cloudOrderDetail.Order.OrderSn + ":云仓订单子记录表创建失败" + err.Error())
				return
			}
		}
	}

	cloudOrder.Status = 0
	err = source.DB().Where("id = ?", cloudOrder.ID).Updates(&cloudOrder).Error
	if err != nil {
		log.Log().Error("云仓下单:云仓订单记录表创建失败", zap.Any("err", err), zap.Any("cloudOrderSn", cloudOrderDetail.Order.OrderSn), zap.Any("出错的记录:cloudOrderItem", cloudOrder))
		err = errors.New("云仓下单:订单号" + cloudOrderDetail.Order.OrderSn + ":云仓订单记录表创建失败" + err.Error())
		return
	}
	for _, confirmInfoItem := range confirmInfo.Orders {

		//var payInfo orderPay.PayInfo
		//err, payInfo = orderPay.GetPayInfo(config.Cloud.CloudUserId, []uint{confirmInfoItem.ID})
		//if err != nil {
		//	log.Log().Error("云仓下单:获取支付信息失败", zap.Any("err", err))
		//	err = errors.New("云仓下单:获取支付信息失败:" + err.Error())
		//	code = 2
		//	return
		//}
		//err, _ = payment.PayService(paymentModel.UpdateBalance{
		//	Uid:       confirmInfoItem.UserID,
		//	Amount:    confirmInfoItem.Amount,
		//	PayType:   2,
		//	PayInfoID: int(payInfo.ID),
		//})
		//if err != nil {
		//	log.Log().Error("云仓下单:支付失败", zap.Any("err", err))
		//	err = errors.New("云仓下单:支付失败:" + err.Error())
		//	err = source.DB().Model(&model2.CloudOrderItem{}).Where("order_id = ?", confirmInfoItem.ID).Updates(&model2.CloudOrderItem{
		//		SupplyPayStatus: 1,
		//	}).Error
		//	code = 2
		//	return
		//}
		err = SynCloudOrderPay(config, confirmInfoItem)
		if err != nil {
			err = source.DB().Model(&model2.CloudOrderItem{}).Where("order_id = ?", confirmInfoItem.ID).Updates(&model2.CloudOrderItem{
				SupplyPayStatus: -1,
			}).Error
			log.Log().Error("云仓下单:支付失败保存失败状态失败", zap.Any("err", err), zap.Any("order_id", confirmInfoItem.ID))
			code = 2
			return
		}
		//支付成功改变云仓记录表状态变为云仓订单状态
		cloudOrder.Status = cloudOrderDetail.Order.Status
		source.DB().Where("id = ?", cloudOrder.ID).Updates(&cloudOrder)
	}
	code = 2 //无论发货结果是什么都不需要改变云仓订单记录表状态为错误
	return
}
func SynCloudOrderPay(config common.SupplySetting, confirmInfoItem orderModel.Order) (err error) {
	var payInfo orderPay.PayInfo
	err, payInfo = orderPay.GetPayInfo(config.Cloud.CloudUserId, []uint{confirmInfoItem.ID})
	if err != nil {
		log.Log().Error("云仓下单:获取支付信息失败", zap.Any("err", err))
		err = errors.New("云仓下单:获取支付信息失败:" + err.Error())
		return
	}
	err, _ = payment.PayService(paymentModel.UpdateBalance{
		Uid:       confirmInfoItem.UserID,
		Amount:    confirmInfoItem.Amount,
		PayType:   2,
		PayInfoID: int(payInfo.ID),
	})
	if err != nil {
		log.Log().Error("云仓下单:支付失败", zap.Any("err", err))
		err = errors.New("云仓下单:支付失败:" + err.Error())
		return
	}
	return
}

/*
*

	定时 支付云仓中没有支付的订单
*/
func CronOrderPay(config common.SupplySetting, GatherSuppliesId uint) {
	var err error
	var cloudOrderDatas []model2.CloudOrder //云仓订单记录表数据
	err = source.DB().Where("status = 0 and order_id is not null").Where("gather_supplies_id = ?", GatherSuppliesId).Find(&cloudOrderDatas).Error
	if err != nil {
		log.Log().Error("云仓自动支付：没有待支付的订单", zap.Any("err", err))
	}
	for _, cloudOrderData := range cloudOrderDatas {
		if cloudOrderData.Status == 0 && cloudOrderData.OrderId != "" {
			var confirmInfoItem []orderModel.Order
			//因为存在中台拆单 所以这里查询多个
			err = source.DB().Where("third_order_sn = ?", cloudOrderData.CloudOrderSn).Find(&confirmInfoItem).Error
			if err != nil {
				log.Log().Error("云仓下单:获取订单详情失败", zap.Any("err", err))
				err = errors.New("云仓下单:获取订单详情失败:" + err.Error())
				continue
			}
			var isPass = 2
			for _, confirmInfoItemV := range confirmInfoItem {
				if confirmInfoItemV.Status == orderModel.WaitPay {
					err = CloudOrderItemPay(confirmInfoItemV, config)
					if err == nil {
						isPass = 1
						err = source.DB().Model(&model2.CloudOrderItem{}).Where("order_id = ?", confirmInfoItemV.ID).Updates(&model2.CloudOrderItem{
							SupplyPayStatus: 1,
						}).Error
						if err != nil {
							log.Log().Error("云仓下单:支付成功保存记录失败", zap.Any("err", err), zap.Any("cloudOrderItem.ID", confirmInfoItemV.ID))
						}
					}
				}

			}
			if isPass == 1 {
				cloudOrderData.Status = 1
				source.DB().Where("id = ?", cloudOrderData.ID).Updates(&cloudOrderData)
			}
		}
	}

	var cloudOrderItemDatas []model2.CloudOrderItem //云仓订单记录表数据
	err = source.DB().Where("cloud_order_items.supply_pay_status = -1").Joins("left join cloud_orders on cloud_orders.id = cloud_order_items.cloud_order_id").Where("cloud_orders.gather_supplies_id = ?", GatherSuppliesId).Find(&cloudOrderItemDatas).Error
	if err != nil {
		log.Log().Error("云仓自动支付：没有待支付的订单", zap.Any("err", err))
		return
	}
	for _, cloudOrderItem := range cloudOrderItemDatas {
		if cloudOrderItem.OrderId > 0 {
			var confirmInfoItem orderModel.Order
			//因为存在中台拆单 所以这里查询多个
			err = source.DB().Where("id = ?", cloudOrderItem.OrderId).First(&confirmInfoItem).Error
			if err != nil {
				log.Log().Error("云仓下单:获取订单详情失败", zap.Any("err", err))
				continue
			}

			err = CloudOrderItemPay(confirmInfoItem, config)
			if err == nil {
				err = source.DB().Model(&model2.CloudOrderItem{}).Where("id = ?", cloudOrderItem.ID).Updates(&model2.CloudOrderItem{
					SupplyPayStatus: 1,
				}).Error
				if err != nil {
					log.Log().Error("云仓下单:支付成功保存记录失败", zap.Any("err", err), zap.Any("cloudOrderItem.ID", cloudOrderItem.ID))
				}
			}
		}
	}
}
func CloudOrderItemPay(confirmInfoItem orderModel.Order, config common.SupplySetting) (err error) {
	if confirmInfoItem.Status == orderModel.WaitPay {
		var payInfo orderPay.PayInfo
		err, payInfo = orderPay.GetPayInfo(config.Cloud.CloudUserId, []uint{confirmInfoItem.ID})
		if err != nil {
			log.Log().Error("云仓下单:获取支付信息失败", zap.Any("err", err))
			err = errors.New("云仓下单:获取支付信息失败:" + err.Error())
			return
		}
		err, _ = payment.PayService(paymentModel.UpdateBalance{
			Uid:       confirmInfoItem.UserID,
			Amount:    confirmInfoItem.Amount,
			PayType:   2,
			PayInfoID: int(payInfo.ID),
		})
		if err != nil {
			log.Log().Error("云仓下单:支付失败", zap.Any("err", err))
			err = errors.New("云仓下单:支付失败:" + err.Error())
			return
		}
	}

	if confirmInfoItem.Status == orderModel.Closed {
		err = errors.New("云仓下单:中台订单已关闭" + strconv.Itoa(int(confirmInfoItem.OrderSN)))
		return nil
	}
	return
}
func Fen2Yuan(price int) string {
	d := decimal.New(1, 2) //分除以100得到元

	result := decimal.NewFromInt(int64(price)).DivRound(d, 2).String()
	//result := decimal.NewFromInt(int64(price)).String()

	return result
}
