package service

import (
	"convergence/model"
	"testing"
)

func TestMigrate(t *testing.T) {

	//支付方法
	var parm model.PayNotify

	parm.Status = "100"
	parm.OrderNo = "123123234234460021"

	//退款
	//var parm model.RefundNotify
	//parm.Status="100"
	//parm.OrderNo="2021071513500002"
	//
	//RefundNotify(parm)
	//_, node := leaf.NewNode(20)
	//_, id := node.NextId()
	//var param = usertopup.PurchasingBalance{
	//	OrderID:   id,
	//	Uid:       "112313",
	//	Amount:    11,
	//	PayType:   1,
	//	PayStatus: 1,
	//	Balance: 22,
	//	BusinessType: 2,
	//}
	//err:=BalanceChange(param)
	//if err!=nil{
	//	return
	//}

	//_, node := leaf.NewNode(20)
	//_, id := node.NextId()
	//var param = usertopup.SettlementBalance{
	//	OrderID:   id,
	//	Uid:       1,
	//	Amount:    11,
	//
	//	Balance: 22,
	//	BusinessType: 1,
	//}
	//err:=SettlementBalanceChange(param)
	//if err!=nil{
	//	return
	//}

	//
	//order_id:="kmvtli6mk0"
	//err:=UpdateUserBalance(order_id)
	//
	//fmt.Println("返回错误：",err)

	//var param=usertopup.UpdateBalance{
	//	Uid: "2",
	//	Amount: 5,
	//
	//
	//}
	//order_id:="kmvtli6mk0"
	//err:=UpdateUserBalance(order_id)
	//
	//fmt.Println("返回错误：",err)

	//_, node := leaf.NewNode(20)
	//_, id := node.NextId()
	//fmt.Println("订单号",string(id))
	//
	//for i := 0; i < 40; i++ {
	//	err, id := node.NextId()
	//	if err != nil {
	//		return
	//	}
	//	fmt.Println("多订单id:",id)
	//}

	//color.Info.Println(user.Username)

}
