package request

import (
	"order/model"
	yzRequest "yz-go/request"
)

type OrderAdminSearch struct {
	PluginID             uint               `json:"plugin_id" form:"plugin_id"`
	LecturerID           uint               `json:"lecturer_id" form:"lecturer_id"`
	CurriculumName       string             `json:"curriculum_name" form:"curriculum_name"`
	Status               *model.OrderStatus `json:"status" form:"status"`
	RefundStatus         *int               `json:"refund_status" form:"refund_status"`
	StartAT              string             `json:"start_at" form:"start_at"`
	EndAT                string             `json:"end_at" form:"end_at"`
	TimeType             *int               `json:"time_type" form:"time_type"` //0下单时间 1付款时间 2发货时间 3完成时间
	OrderSN              string             `json:"order_sn" form:"order_sn"`
	SupplySN             string             `json:"supply_sn" form:"supply_sn"`
	ThirdOrderSN         string             `json:"third_order_sn" form:"third_order_sn"`
	PaySN                string             `json:"pay_sn" form:"pay_sn"`
	ShippingSN           string             `json:"shipping_sn" form:"shipping_sn"`
	PayTypeID            int                `json:"pay_type_id" form:"pay_type_id"`
	UserID               uint               `json:"user_id" form:"user_id"`
	NickName             string             `json:"nick_name" form:"nick_name"`
	UserName             string             `json:"user_name" form:"user_name"`
	UserMobile           string             `json:"user_mobile" form:"user_mobile"`
	SupplierID           *uint              `json:"supplier_id" form:"supplier_id"`
	ApplicationID        uint               `json:"application_id" form:"application_id"`
	GatherSupplierID     *uint              `json:"gather_supplier_id" form:"gather_supplier_id"`
	GatherSupplierStatus string             `json:"gather_supplier_status" form:"gather_supplier_status"`
	ProductTitle         string             `json:"product_title" form:"product_title"`
	CloudOrderId         uint               `json:"cloud_order_id" form:"cloud_order_id"`
	SendCodeStatus       *uint              `json:"sendCodeStatus" form:"sendCodeStatus"`
	WriteStatus          *uint              `json:"writeStatus" form:"writeStatus"`

	yzRequest.PageInfo
}
