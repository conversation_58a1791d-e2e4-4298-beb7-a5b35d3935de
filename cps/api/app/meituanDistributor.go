package app

import (
	"cps/request"
	"cps/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"net/http"
	"strconv"
	"strings"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

func GetMeituanProvince(c *gin.Context) {
	var info request.MeituanDistirbutorRequest
	err := c.ShouldBindQuery(&info)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var customize []string
	customize = append(customize, strconv.Itoa(int(utils.GetAppID(c))))
	if info.ThirdUserID > 0 {
		customize = append(customize, strconv.Itoa(info.ThirdUserID))
	} else {
		yzResponse.FailWithMessage("third_user_id不能为空", c)
		return
	}
	info.RequestId = strings.Join(customize, "_")
	if err, data := service.GetMeituanProvince(info); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		c.JSON(http.StatusOK, data)
		return

	}
}

func GetMeituanCityCategories(c *gin.Context) {
	var info request.MeituanDistirbutorRequest
	err := c.ShouldBindQuery(&info)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var customize []string
	customize = append(customize, strconv.Itoa(int(utils.GetAppID(c))))
	if info.ThirdUserID > 0 {
		customize = append(customize, strconv.Itoa(info.ThirdUserID))
	} else {
		yzResponse.FailWithMessage("third_user_id不能为空", c)
		return
	}
	info.RequestId = strings.Join(customize, "_")
	if err, data := service.GetMeituanCityCategories(info); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		c.JSON(http.StatusOK, data)
		return

	}
}

func GetMeituanCity(c *gin.Context) {
	var info request.MeituanDistirbutorRequest
	err := c.ShouldBindQuery(&info)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var customize []string
	customize = append(customize, strconv.Itoa(int(utils.GetAppID(c))))
	if info.ThirdUserID > 0 {
		customize = append(customize, strconv.Itoa(info.ThirdUserID))
	} else {
		yzResponse.FailWithMessage("third_user_id不能为空", c)
		return
	}
	info.RequestId = strings.Join(customize, "_")
	if err, data := service.GetMeituanCity(info); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		c.JSON(http.StatusOK, data)
		return

	}
}

func GetMeituanRegions(c *gin.Context) {
	var info request.MeituanDistirbutorRequest
	err := c.ShouldBindQuery(&info)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var customize []string
	customize = append(customize, strconv.Itoa(int(utils.GetAppID(c))))
	if info.ThirdUserID > 0 {
		customize = append(customize, strconv.Itoa(info.ThirdUserID))
	} else {
		yzResponse.FailWithMessage("third_user_id不能为空", c)
		return
	}
	info.RequestId = strings.Join(customize, "_")
	if err, data := service.GetMeituanRegions(info); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		c.JSON(http.StatusOK, data)
		return

	}
}

func GetMeituanSeckill(c *gin.Context) {
	var info request.MeituanDistirbutorRequest
	err := c.ShouldBindQuery(&info)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var customize []string
	customize = append(customize, strconv.Itoa(int(utils.GetAppID(c))))
	if info.ThirdUserID > 0 {
		customize = append(customize, strconv.Itoa(info.ThirdUserID))
	} else {
		yzResponse.FailWithMessage("third_user_id不能为空", c)
		return
	}
	info.RequestId = strings.Join(customize, "_")
	if err, data := service.GetMeituanSeckill(info); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		c.JSON(http.StatusOK, data)
		return

	}
}

func GetMeituanSeckillList(c *gin.Context) {
	var info request.MeituanDistirbutorRequest
	err := c.ShouldBindQuery(&info)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var customize []string
	customize = append(customize, strconv.Itoa(int(utils.GetAppID(c))))
	if info.ThirdUserID > 0 {
		customize = append(customize, strconv.Itoa(info.ThirdUserID))
	} else {
		yzResponse.FailWithMessage("third_user_id不能为空", c)
		return
	}
	info.RequestId = strings.Join(customize, "_")
	if err, data := service.GetMeituanSeckillList(info); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		c.JSON(http.StatusOK, data)
		return

	}
}

func GetMeituanSearchDeals(c *gin.Context) {
	var info request.MeituanDistirbutorRequest
	err := c.ShouldBindJSON(&info)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var customize []string
	customize = append(customize, strconv.Itoa(int(utils.GetAppID(c))))
	if info.ThirdUserID > 0 {
		customize = append(customize, strconv.Itoa(info.ThirdUserID))
	} else {
		yzResponse.FailWithMessage("third_user_id不能为空", c)
		return
	}
	info.RequestId = strings.Join(customize, "_")
	if err, data := service.GetMeituanSearchDeals(info); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		c.JSON(http.StatusOK, data)
		return

	}
}

func GetMeituanSearchDealsDetail(c *gin.Context) {
	var info request.MeituanDistirbutorRequest
	err := c.ShouldBindJSON(&info)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var customize []string
	customize = append(customize, strconv.Itoa(int(utils.GetAppID(c))))
	if info.ThirdUserID > 0 {
		customize = append(customize, strconv.Itoa(info.ThirdUserID))
	} else {
		yzResponse.FailWithMessage("third_user_id不能为空", c)
		return
	}
	info.RequestId = strings.Join(customize, "_")
	if err, data := service.GetMeituanSearchDealsDetail(info); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		c.JSON(http.StatusOK, data)
		return

	}
}
