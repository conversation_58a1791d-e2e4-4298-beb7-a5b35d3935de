package meituan

import (
	"bytes"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strings"
	"time"
)

// SignHeaders 接口签名需要的头部字段
type SignHeaders struct {
	SCaApp              string `json:"S-Ca-App"`
	SCaTimestamp        string `json:"S-Ca-Timestamp"`
	SCaSignature        string `json:"S-Ca-Signature"`
	SCaSignatureHeaders string `json:"S-Ca-Signature-Headers"`
	ContentMD5          string `json:"Content-MD5"`
}

// SignUtil 工具类
type SignUtil struct {
	AppKey    string
	AppSecret string
}

// NewSignUtil 构造函数
func NewSignUtil(appKey, appSecret string) *SignUtil {
	return &SignUtil{AppKey: appKey, AppSecret: appSecret}
}

// GetSignHeaders 获取签名头部字段
func (s *SignUtil) GetSignHeaders(config map[string]interface{}) SignHeaders {
	timestamp := fmt.Sprintf("%d", time.Now().UnixMilli())
	signHeaders := SignHeaders{
		SCaApp:              s.AppKey,
		SCaTimestamp:        timestamp,
		SCaSignatureHeaders: "S-Ca-App,S-Ca-Timestamp",
		ContentMD5:          s.contentMD5(config),
	}
	signHeaders.SCaSignature = s.sign(config, signHeaders)
	return signHeaders
}

// Sign 计算签名
func (s *SignUtil) sign(config map[string]interface{}, signHeaders SignHeaders) string {
	strSign := fmt.Sprintf("%s\n%s\n%s%s",
		s.httpMethod(config),
		s.contentMD5(config),
		s.headers(signHeaders),
		s.url(config),
	)
	fmt.Println("待签名字符串:", strSign)
	hash := hmac.New(sha256.New, []byte(s.AppSecret))
	hash.Write([]byte(strSign))
	return base64.StdEncoding.EncodeToString(hash.Sum(nil))
}

// httpMethod 请求方式大写
func (s *SignUtil) httpMethod(config map[string]interface{}) string {
	method, _ := config["method"].(string)
	return strings.ToUpper(method)
}

// contentMD5 请求参数执行base64+md5的值
func (s *SignUtil) contentMD5(config map[string]interface{}) string {
	if method, ok := config["method"].(string); ok && method == "post" {
		if data, ok := config["data"]; ok {
			jsonData, _ := json.Marshal(data)
			md5Hash := md5.New()
			md5Hash.Write(jsonData)
			return base64.StdEncoding.EncodeToString(md5Hash.Sum(nil))
		}
	}
	return ""
}

// headers 签名计算Header的Key拼接
func (s *SignUtil) headers(signHeaders SignHeaders) string {
	headersMap := map[string]string{
		"S-Ca-App":               signHeaders.SCaApp,
		"S-Ca-Timestamp":         signHeaders.SCaTimestamp,
		"S-Ca-Signature-Headers": signHeaders.SCaSignatureHeaders,
	}
	keys := make([]string, 0, len(headersMap))
	for k := range headersMap {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	var result strings.Builder
	for _, k := range keys {
		if k != "S-Ca-Signature-Headers" && k != "Content-MD5" && k != "S-Ca-Signature" {
			result.WriteString(fmt.Sprintf("%s:%s\n", k, headersMap[k]))
		}
	}
	return result.String()
}

// url url拼接
func (s *SignUtil) url(config map[string]interface{}) string {
	url, _ := config["url"].(string)
	reqData := config["params"]
	if reqData == nil {
		reqData = config["data"]
	}
	path := "/" + strings.SplitN(url, "/", 4)[3]
	if method, ok := config["method"].(string); ok && method == "get" && reqData != nil {
		sortObj := s.objSort(reqData.(map[string]interface{}))
		var query strings.Builder
		keys := make([]string, 0, len(sortObj))
		for k := range sortObj {
			keys = append(keys, k)
		}
		sort.Strings(keys)
		for i, k := range keys {
			value := sortObj[k]
			if i > 0 {
				query.WriteString("&")
			}
			query.WriteString(fmt.Sprintf("%s=%v", k, value))
		}
		return path + "?" + query.String()
	}
	return path
}

// objSort 对象Key按照字典排序
func (s *SignUtil) objSort(arys map[string]interface{}) map[string]interface{} {
	keys := make([]string, 0, len(arys))
	for k := range arys {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	sorted := make(map[string]interface{})
	for _, k := range keys {
		sorted[k] = arys[k]
	}
	return sorted
}

func Test() {
	config := map[string]interface{}{
		"method": "post",
		"url":    "https://media.meituan.com/cps_open/common/api/v1/query_order", //接口按照实际访问接口替换
		"data": map[string]interface{}{ //参数按照实际访问接口替换
			"platform": 1, //参数按照实际访问接口替换
			"limit":    3, //参数按照实际访问接口替换
		},
	}

	signUtil := NewSignUtil("b77409c6509b64e225d4819c7cb44993", "7d7f5acf2f7e438da55e1bc69a559134") //替换自己的appkey和秘钥
	signHeaders := signUtil.GetSignHeaders(config)
	fmt.Println("签名头部字段:", signHeaders)
	// 目标URL
	url := config["url"].(string)

	// 创建POST请求
	req, err := http.NewRequest("POST", url, nil)
	if err != nil {
		fmt.Println(err)
		return
	}
	// 设置请求头，例如内容类型为JSON
	req.Header.Set("Content-Type", "application/json; charset=utf-8")
	req.Header.Set("S-Ca-App", signHeaders.SCaApp)
	req.Header.Set("S-Ca-Signature", signHeaders.SCaSignature)
	req.Header.Set("S-Ca-Timestamp", signHeaders.SCaTimestamp)
	req.Header.Set("Content-MD5", signHeaders.ContentMD5)
	req.Header.Set("S-Ca-Signature-Headers", signHeaders.SCaSignatureHeaders)

	jsonData, err := json.Marshal(config["data"])
	if err != nil {
		fmt.Println(err)
		return
	}
	// 设置请求体
	req.Body = io.NopCloser(bytes.NewBuffer(jsonData))
	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer resp.Body.Close()
	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Println(err)
		return
	}
	//处理
	fmt.Println("result:", string(body))
}
