package request

import (
	"cps/model"
	"yz-go/request"
	"yz-go/source"
)

type GenerateLinkRequest struct {
	ActivityID    string             `json:"activity_id" form:"activity_id" query:"activity_id"`
	Customize     string             `json:"customize" form:"customize" query:"customize"`
	LinkType      model.CpsOrderType `json:"link_type" form:"link_type" query:"link_type"`
	ThirdUserID   int                `json:"third_user_id" form:"third_user_id" query:"third_user_id"`
	JumpType      int                `json:"jump_type" form:"jump_type" query:"jump_type"`
	SkuViewId     string             `json:"skuViewId" form:"skuViewId"`
	PlatForm      int                `json:"platform" form:"platform"`
	BizLine       int                `json:"bizLine" form:"bizLine"`
	IsDistributor int                `json:"is_distributor" form:"is_distributor"`
	// 小商店id
	ShopID uint `json:"shop_id" form:"shop_id"`
}

type JhCpsOrderSearch struct {
	request.PageInfo
	source.Model
	OrderSN         uint              `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:编号;"` //订单编号
	CpsOrderId      string            `json:"order_id" form:"order_id" gorm:"column:order_id;index;"`      //cps订单号
	DataString      string            `json:"data_string" gorm:"type:text;"`                               //元数据
	ApplicationID   uint              `json:"application_id"`                                              //采购端id
	UserID          uint              `json:"user_id"`                                                     //用户id
	ThirdUserID     uint              `json:"third_user_id"`                                               //采购端用户id
	ActivityID      string            `json:"activity_id"`                                                 //活动id
	Price           uint              `json:"price"`                                                       //订单价格
	RefundPrice     uint              `json:"refund_price"`                                                //退款金额
	Ratio           uint              `json:"ratio"`                                                       //佣金比例
	CommissionPrice uint              `json:"commission_price"`                                            //cps佣金 分成基数
	Type            string            `json:"type"`                                                        //订单类型 meituan美团 didi滴滴
	Title           string            `json:"title"`                                                       //订单标题
	LocalSettleAt   *source.LocalTime `json:"local_settle_at" form:"local_settle_at"`                      //结算时间
	PayAt           *source.LocalTime `json:"pay_at" form:"pay_at"`                                        //付款时间
	CompleteAt      *source.LocalTime `json:"complete_at" form:"complete_at"`                              //完成时间
	RefundAt        *source.LocalTime `json:"refund_at" form:"refund_at"`                                  //退款时间
	IsConnected     int               `json:"is_connected" form:"is_connected" gorm:"default:0;"`          //1同步 0未同步
	TimeType        string            `json:"time_type"  form:"time_type"`
	StartAT         string            `json:"start_at"  form:"start_at"`
	EndAT           string            `json:"end_at" form:"end_at"`
	Username        string            `json:"username" form:"username"`
	IDs             []string          `json:"ids" form:"ids"`
	Alliance        string            `json:"alliance" form:"alliance"`
	Status          string            `json:"status" form:"status"`
	IsDistributor   int               `json:"is_distributor" form:"is_distributor"`
}

type MockOrderCallBackRequest struct {
	Dsi      string `json:"dsi" form:"dsi"`
	SourceID string `json:"source_id" form:"source_id"`
}
