package callback

import (
	"yz-go/source"
)

type OrderStatus string
type GoodsStatus OrderStatus

const (
	ORDER_DELIVERY  OrderStatus = "order.delivery"
	ORDER_DELIVERED OrderStatus = "order.delivered"
	ORDER_SUCCESS   OrderStatus = "order.success"
	ORDER_CANCEL    OrderStatus = "order.cancel"
)

const (
	GOODS_UNDER       GoodsStatus = "goods.undercarriage"
	GOODS_ONSALE      GoodsStatus = "goods.on.sale"
	GOODS_ALERT       GoodsStatus = "goods.alter"
	GOODS_PRICE_ALERT GoodsStatus = "goods.price.alter"
)
const (
	AFTERSALE_AGREE  GoodsStatus = "afterSale.agree"
	AFTERSALE_REFUSE GoodsStatus = "afterSale.refuse"
)

const (
	OrderStatusDeliveryRouting  OrderStatus = "order.status.delivery"
	OrderStatusDeliveredRouting OrderStatus = "order.status.delivered"
	OrderStatusSuccessRouting   OrderStatus = "order.status.success"
	OrderStatusCancelRouting    OrderStatus = "order.status.cancel"
)

func DeleteOrderMsg(msgId string) (err error) {
	err = source.DB().Delete(&OrderCallBackMessage{}, "msg_id=?", msgId).Error
	return
}

func DeleteGoodsMsg(msgId string) (err error) {
	err = source.DB().Delete(&GoodsCallBackMessage{}, "msg_id=?", msgId).Error
	return
}

type CallBackType struct {
	MsgID string      `json:"id"`
	Type  string      `json:"type" form:"type"`
	Data  interface{} `json:"data" form:"data"`
}

type OrderCallBack struct {
	MsgID    string    `json:"id"`
	PushTime int64     `json:"pushTime" form:"pushTime"`
	Type     string    `json:"type" form:"type"`
	Data     OrderData `json:"data" form:"data"`
}

type GoodsCallBack struct {
	MsgID    string   `json:"id"`
	PushTime int64    `json:"pushTime" form:"pushTime"`
	Type     string   `json:"type" form:"type"`
	Data     GoodsIds `json:"data" form:"data"`
}

type GoodsIds struct {
	GoodsIds []int `json:"goodsIds" form:"goodsIds"`
}

type OrderItemSN struct {
	OrderSn string `json:"orderSn" form:"orderSn"`
}
type OrderData struct {
	OrderSn string `json:"orderSn" form:"orderSn"`
	Sku     int64  `json:"sku" form:"sku"`
}

type OrderCallBackMessage struct {
	MsgID string `json:"id" gorm:"column:msg_id;"`
	source.Model
	MsgType string `json:"msg_type" form:"msg_type"`
	OrderData
}

type GoodsCallBackMessage struct {
	source.Model
	MsgID    string `json:"id" gorm:"column:msg_id;"`
	MsgType  string `json:"msg_type" form:"msg_type"`
	GoodsIds int64  `json:"goods_ids" form:"goods_ids"`
}
