package request

import (
	"dahang-erp/model"
	productModel "product/model"
	yzRequest "yz-go/request"
)

type SaveSaveSysDaHangErpSetting struct {
	Data string `json:"data" query:"data" form:"data"`
}

type PushOrder struct {
	OrderId uint `json:"order_id" query:"order_id" form:"order_id"`
}

type PushOrderByApplicationId struct {
	ApplicationId uint `json:"application_id" query:"application_id" form:"application_id"`
}

type GetDaHangErpProductSearch struct {
	model.DaHangErpProduct        //第三方参数
	ProductName            string `json:"product_name" form:"product_name" gorm:"column:product_name;comment:中台商品名称;"`                // 商品名称
	SupplierID             uint   `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:中台供应商id;index;"`            // 供应商id
	IsDisplay              int    `json:"is_display" form:"is_display" gorm:"column:is_display;comment:;type:smallint;size:1;index;"` // 999全部1上架0下架

	yzRequest.PageInfo
}

type ProductSearch struct {
	productModel.Product
	yzRequest.PageInfo
	SupplierID     uint   `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商id;"`                // 供应商id
	GatherSupplyID *uint  `json:"gather_supply_id" form:"gather_supply_id" gorm:"column:gather_supply_id;comment:供应链id;"` // 供应链id
	Filter         int    `json:"filter" form:"filter" query:"filter"`
	IsDisplay      *int   `json:"is_display" form:"is_display"`
	IsNew          *int   `json:"is_new" form:"is_new" gorm:"column:is_new;comment:新品（1是0否）;type:smallint;size:1;"`                   // 新品（1是0否）
	IsRecommend    *int   `json:"is_recommend" form:"is_recommend" gorm:"column:is_recommend;comment:推荐（1是0否）;type:smallint;size:1;"` // 推荐（1是0否）
	IsHot          *int   `json:"is_hot" form:"is_hot" gorm:"column:is_hot;comment:热销（1是0否）;type:smallint;size:1;"`                   // 热销（1是0否）
	IsPromotion    *int   `json:"is_promotion" form:"is_promotion" gorm:"column:is_promotion;comment:促销（1是0否）;type:smallint;size:1;"` // 促销（1是0否）
	StatusLock     *int   `json:"status_lock" form:"status_lock" gorm:"column:status_lock;comment:锁定（1是0否）;type:smallint;size:1;"`    // 锁定（1是0否）
	SortType       string `json:"sortType" form:"sortType" `
	SortMode       string `json:"sortMode" form:"sortMode" `
	GoodsCity      string `json:"goodsCity" form:"goodsCity" `
	IsBill         *int   `json:"is_bill" form:"is_bill"`
	JushuitanBind  *int   `json:"jushuitan_bind" form:"jushuitan_bind"`
	ShopName       string `json:"shop_name"  form:"shop_name"`
	SysUserID      *uint  `json:"sys_user_id" json:"sys_user_id" form:"sys_user_id" gorm:"column:sys_user_id;comment:后台账号id;"`
}

type ProductExportRecordRequest struct {
	yzRequest.PageInfo
	model.DaHangErpProductExportRecord
}

type DaHangErpApplicationListSearch struct {
	yzRequest.PageInfo
	ApplicationID   uint   `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商id;"`   // 采购端id
	ApplicationName string `json:"shop_name"  form:"shop_name"`                                               //采购端名称
	EventCode       string `json:"event_code" form:"event_code" gorm:"column:event_code;comment:活动编码;index;"` //活动编码

}

// 大昌行API客户信息
type DaHangErpCustListSearch struct {
	yzRequest.PageInfo
	EventCode string `json:"event_code" form:"event_code" gorm:"column:event_code;comment:活动编码;index;"` //活动编码
	CustName  string `json:"cust_name" form:"cust_name" gorm:"column:cust_name;comment:客户名称;"`          // 客户名称

}
