package route

import (
	"dahang-erp/api/a"
	"dahang-erp/api/v1"
	"github.com/gin-gonic/gin"
)

func InitAdminPrivateRouter(Router *gin.RouterGroup) {
	daHangErpRouter := Router.Group("daHangErp")
	{
		daHangErpRouter.GET("getSysDaHangErpSetting", a.GetSysDaHangErpSetting)    //获取基础设置
		daHangErpRouter.POST("saveSysDaHangErpSetting", a.SaveSysDaHangErpSetting) //保存基础设置

		daHangErpRouter.POST("sync", a.Sync) //同步供应商/商品

		daHangErpRouter.POST("syncApp", a.SyncApp) //同步采购端

		daHangErpRouter.POST("pushOrder", a.PushOrder) //主动推送订单到大昌行

		daHangErpRouter.POST("pushOrderByApplicationId", a.PushOrderByApplicationId) //获取根据采购端id推送订单到大昌行

		daHangErpRouter.POST("uploadExcelPushOrder", a.UploadExcelPushOrder) //上传表格推送到大昌行

		daHangErpRouter.GET("getDaHangErpProductList", a.GetDaHangErpProductList) //已同步商品列表

		daHangErpRouter.POST("deleteDahangErpId", a.DeleteDahangErpId) //删除大昌行已导入的商品记录

		daHangErpRouter.POST("updateDahangErpProcudeProductId", a.UpdateDahangErpProcudeProductId) //修改第三方记录表中台商品id

		daHangErpRouter.POST("getThirdProductList", a.GetThirdProductList) //第三方商品列表

		daHangErpRouter.GET("getProductList", a.GetProductList) //中台商品列表API

		daHangErpRouter.GET("export", a.Export) //导出筛选商品

		daHangErpRouter.GET("exportProductRecordList", a.ExportProductRecordList) //导出记录

		daHangErpRouter.POST("deleteProductExportRecord", a.DeleteProductExportRecord) //导出记录删除

		daHangErpRouter.GET("getDaHangErpApplicationList", a.GetDaHangErpApplicationList) //ERP导入的采购端列表

		daHangErpRouter.POST("daHangErpItemDelete", a.DaHangErpItemDelete) //删除

		daHangErpRouter.POST("daHangErpItemSave", a.DaHangErpItemSave) //修改

		daHangErpRouter.GET("getDaHangErpCustListByEventCode", a.GetDaHangErpCustListByEventCode) //通过活动编码获取关联的客户

	}

}

// 前端公共
func InitAdminPublicRouter(Router *gin.RouterGroup) {
	daHangErpRouter := Router.Group("daHangErp")
	{
		daHangErpRouter.POST("public/sync", v1.Sync) //同步供应商/商品
	}

}

// 前端私有
func InitUserPrivateRouter(Router *gin.RouterGroup) {

}

// 前端公共
func InitUserPublicRouter(Router *gin.RouterGroup) {

}

// 采购端API
func InitAppPrivateRouter(Router *gin.RouterGroup) {

}
