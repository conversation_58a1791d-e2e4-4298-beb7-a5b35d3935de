package service

import (
	"github.com/360EntSecGroup-Skylar/excelize"
	"yz-go/config"

	applicationModel "application/model"
	applicationService "application/service"
	"dahang-erp/request"
	dahangErpResponse "dahang-erp/response"
	"encoding/json"
	"fmt"
	orderModel "order/model"
	orderResponse "order/response"
	"os"
	"payment/service"
	publicCommon "public-supply/common"
	publicModel "public-supply/model"
	"strconv"
	supplierModel "supplier/model"
	supplierService "supplier/service"
	"time"
	"yz-go/response"
	"yz-go/utils"

	productModel "product/model"
	productService "product/service"

	"dahang-erp/common"
	"dahang-erp/model"
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	userModel "user/model"
	userService "user/service"
	userSetting "user/setting"

	shippingExpress "shipping/express"

	"yz-go/component/log"
	"yz-go/source"
)

/*
*

	同步供应商 供应商商品 采购端
*/
func Sync(request common.ErpRequest) (err error) {
	var gatherSupply publicModel.GatherSupply
	err = source.DB().Unscoped().Where("category_id = ?", publicCommon.DACHANGHANGERP).First(&gatherSupply).Error
	if err != nil {
		err = errors.New("获取插件失败,请重启服务：" + err.Error())
		return
	}
	err, rd := common.Initial()
	if err != nil {
		return
	}

	err, res := rd.Item(request)
	if err != nil {
		return
	}

	go SyncStep1(rd, gatherSupply, res, request) //数据获取正常开始进行 创建会员，采购端 供应商，供应商商品
	return
}

/*
*

	同步采购端
*/
func SyncApp(request common.ErpRequest) (err error) {
	var gatherSupply publicModel.GatherSupply
	err = source.DB().Unscoped().Where("category_id = ?", publicCommon.DACHANGHANGERP).First(&gatherSupply).Error
	if err != nil {
		err = errors.New("获取插件失败,请重启服务：" + err.Error())
		return
	}
	err, rd := common.Initial()
	if err != nil {
		return
	}

	err, res := rd.Item(request)
	if err != nil {
		return
	}

	go func() {
		err = SynApplication(rd, gatherSupply, res, request)
		if err != nil {

		}
	}() //数据获取正常开始进行 创建会员，采购端 供应商，供应商商品
	return
}
func SyncStep1(rd common.RequestData, gatherSupply publicModel.GatherSupply, res common.ItemDatas, request common.ErpRequest) {
	var err error
	//, supplyRes common.SupplierDatas, GoodsList common.GoodsListDatas
	err, supplyRes := rd.Supplier(common.ErpRequest{})
	if err != nil {
		return
	}
	//同步项目
	_ = SynApplication(rd, gatherSupply, res, request)
	var daHangErpSuppliers []model.DaHangErpSupplier
	//source.DB().Find(&daHangErpSuppliers)

	//导入供应商
	for _, item := range supplyRes {
		var daHangErpSupplier model.DaHangErpSupplier
		err = source.DB().Where("supp_code = ?", item.SuppCode).First(&daHangErpSupplier).Error
		//如果错误不是没有查询结构直接跳过
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("dh_erp同步供应商创建：查询供应商关系表错误" + err.Error())
			continue
		}
		if daHangErpSupplier.ID == 0 {
			daHangErpSupplier.SuppCode = item.SuppCode
			daHangErpSupplier.SuppName = item.SuppName
			daHangErpSupplier.OuName = item.OuName
			daHangErpSupplier.OuCode = item.OuCode
			err = source.DB().Create(&daHangErpSupplier).Error
			if err != nil {
				err = errors.New("dh_erp同步供应商创建：查询供应商关系表错误" + err.Error())
				continue
			}
		} else {
			daHangErpSupplier.SuppName = item.SuppName
			daHangErpSupplier.OuName = item.OuName
			daHangErpSupplier.OuCode = item.OuCode
			source.DB().Model(&model.DaHangErpSupplier{}).Where("id = ?", daHangErpSupplier.ID).Save(&daHangErpSupplier)
		}
		var name = item.SuppName + "_" + item.SuppCode //默认供应商名称
		if daHangErpSupplier.SupplierId == 0 {
			var saveSupplier supplierModel.SaveSupplier
			err = source.DB().Where("name = ?", name).First(&saveSupplier).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				err = errors.New("dh_erp同步供应商创建：查询供应商表错误" + err.Error())
				continue
			}
			//没有则创建
			if saveSupplier.ID == 0 {
				saveSupplier.Name = name                  //默认供应商
				saveSupplier.UserInfo.Password = "888888" //默认密码
				saveSupplier.UserInfo.Username = item.SuppCode
				err, saveSupplier.ID = supplierService.CreateSupplierDoNotVerify(saveSupplier)
				if err != nil {
					log.Log().Error("dh_erp同步供应商创建失败", zap.Any("err", err), zap.Any("item", item))
					continue
				}
			}
			//log.Log().Error("dh_erp同步供应商创建", zap.Any("err", err), zap.Any("item", item))
			daHangErpSupplier.SupplierId = saveSupplier.ID
			err = source.DB().Where("id = ?", daHangErpSupplier.ID).Updates(&model.DaHangErpSupplier{
				SupplierId: saveSupplier.ID,
			}).Error
			if err != nil {
				log.Log().Error("dh_erp同步供应商保存供应商id失败", zap.Any("err", err), zap.Any("item", item))
				continue
			}
		}
		daHangErpSuppliers = append(daHangErpSuppliers, daHangErpSupplier)
	}
	request.Current = 1
	request.Size = 500
	SynGoodsList(rd, gatherSupply, res, request, daHangErpSuppliers)

}

func SynApplication(rd common.RequestData, gatherSupply publicModel.GatherSupply, res common.ItemDatas, request common.ErpRequest) (err error) {
	//导入会员和采购端
	for _, item := range res {
		var companyName string
		var user userModel.User
		//客户要求公司名称是 API返回的客户名称（客户说这里只有一条数据，所以直接赋值多条那就最后一条）
		//客户要求只创建一个会员 采购端绑定那个会员 这里判断如果创建成功其他的只做记录不创建会员
		var isOne = 0
		for _, customer := range item.Customers {
			customer.EventCode = item.EventCode
			customer.EventName = item.EventName
			companyName = customer.CustName
			if isOne == 0 {
				err, user = SyncCreateUser(customer, isOne) //创建会员 和 会员和 项目活动的关系表
			} else {
				err, _ = SyncCreateUser(customer, isOne) //创建会员 和 会员和 项目活动的关系表
			}
			//错误方法内记录
			if err != nil {
				log.Log().Error("dh_erp同步导入会员和采购端,错误", zap.Any("err", err), zap.Any("item", item))
				continue
			}
			isOne = 1
		}
		if user.ID == 0 {
			log.Log().Error("dh_erp创建会员失败,错误", zap.Any("err", err), zap.Any("item", item))
			continue
		}
		err = SyncCreateApplication(item, companyName, user) //  创建采购端 和关系表
		if err != nil {
			continue
		}
	}
	return
}
func SynGoodsList(rd common.RequestData, gatherSupply publicModel.GatherSupply, res common.ItemDatas, request common.ErpRequest, daHangErpSuppliers []model.DaHangErpSupplier) {
	err, GoodsList := rd.GoodsPageList(request)
	if err != nil {
		return
	}
	//导入商品
	for _, item := range GoodsList.Records {
		//没有供应商编码直接跳过
		if item.SuppCode == "" || item.ItemCode == "" {
			log.Log().Error("dh_erp同步供应商商品没有供应商编码或者没有商品编码", zap.Any("item", item))
			continue
		}
		//50430002 CD2310080006
		var productForUpdate productService.ProductForUpdate
		//如果不是基础设置配置的供应商编码则进行比对
		if rd.Config.Value.SuppCode != item.SuppCode {
			for _, daHangErpSupplier := range daHangErpSuppliers {
				if daHangErpSupplier.SuppCode == item.SuppCode {
					productForUpdate.SupplierID = daHangErpSupplier.SupplierId
					break
				}
			}
			//未匹配到供应商id直接跳过
			if productForUpdate.SupplierID == 0 {
				log.Log().Error("dh_erp同步供应商商品没有匹配到供应商", zap.Any("item", item))
				continue
			}
		}

		var daHangErpProduct model.DaHangErpProduct
		err = source.DB().Where("item_code = ?", item.ItemCode).First(&daHangErpProduct).Error
		//如果错误不是没有查询结构直接跳过
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Log().Error("dh_erp同步商品创建：查询商品关系表错误，", zap.Any("err", err), zap.Any("item", item))
			//err = errors.New("dh_erp同步商品创建：查询商品关系表错误"+err.Error())
			continue
		}
		if daHangErpProduct.ID == 0 {
			//这个为1 代表是中台先有的商品 企业购后创建的不需要创建商品
			var productCat productModel.Product
			err = source.DB().Model(&productModel.Product{}).Where("sn = ?", item.ItemCode).First(&productCat).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				log.Log().Error("dh_erp同步商品创建：查询商品表错误，", zap.Any("err", err), zap.Any("item", item))
				//err = errors.New("dh_erp同步商品创建：查询商品关系表错误"+err.Error())
				continue
			}
			daHangErpProduct.ProductId = productCat.ID
			daHangErpProduct.ItemCode = item.ItemCode
			daHangErpProduct.ItemName = item.ItemName
			daHangErpProduct.IsHelpFarming = item.IsHelpFarming
			daHangErpProduct.Cat4 = item.Cat4
			daHangErpProduct.ItemCateCode2 = item.ItemCateCode2
			daHangErpProduct.SuppName = item.SuppName //记录一下企业购的供应商信息 无论中台商品怎么改变都使用企业的供应商信息
			daHangErpProduct.SuppCode = item.SuppCode //记录一下企业购的供应商信息 无论中台商品怎么改变都使用企业的供应商信息

			err = source.DB().Create(&daHangErpProduct).Error
			if err != nil {
				log.Log().Error("dh_erp同步供应商创建：查询供应商关系表错误，", zap.Any("err", err), zap.Any("item", item))

				//err = errors.New("dh_erp同步供应商创建：查询供应商关系表错误"+err.Error())
				continue
			}
		} else {
			daHangErpProduct.ItemCode = item.ItemCode
			daHangErpProduct.ItemName = item.ItemName
			daHangErpProduct.IsHelpFarming = item.IsHelpFarming
			daHangErpProduct.Cat4 = item.Cat4
			daHangErpProduct.ItemCateCode2 = item.ItemCateCode2
			daHangErpProduct.SuppName = item.SuppName //记录一下企业购的供应商信息 无论中台商品怎么改变都使用企业的供应商信息
			daHangErpProduct.SuppCode = item.SuppCode //记录一下企业购的供应商信息 无论中台商品怎么改变都使用企业的供应商信息
			err = source.DB().Model(&model.DaHangErpProduct{}).Where("id = ?", daHangErpProduct.ID).Save(&daHangErpProduct).Error
		}
		//这个为1 代表是中台先有的商品 企业购后创建的不需要创建商品 && item.Cat4 != "1"  //不用了
		//如果没有商品id就创建
		if daHangErpProduct.ProductId == 0 {
			var product productModel.Product
			err = source.DB().Where("sn = ?", item.ItemCode).First(&product).Error
			//如果错误不是没有查询结构直接跳过
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				log.Log().Error("dh_erp同步商品创建：查询商品关系表错误，", zap.Any("err", err), zap.Any("item", item))

				//errors.New("dh_erp同步商品创建：查询商品关系表错误"+err.Error())
				continue
			}
			if product.ID == 0 {
				sourceGoodsID, _ := strconv.Atoi(item.ItemCode)
				productForUpdate.Title = item.ItemName
				//productForUpdate.ChildTitle = item.ItemAbbr
				productForUpdate.Sn = item.ItemCode
				productForUpdate.SingleOption = 1
				productForUpdate.SourceGoodsID = uint(sourceGoodsID)

				sku := productService.Sku{
					Title:         "默认",
					Price:         0,
					CostPrice:     0,
					OriginPrice:   0,
					GuidePrice:    0,
					ActivityPrice: 0,
					Sn:            item.ItemCode,
					Stock:         0,
					Weight:        0,
					Options:       productModel.Options{{SpecName: "规格", SpecItemName: "默认"}},
				}
				productForUpdate.Skus = append(productForUpdate.Skus, sku)
				err, product.ID = productService.CreateProduct(productForUpdate)
				if err != nil {
					log.Log().Error("dh_erp同步商品保存商品失败", zap.Any("err", err), zap.Any("productForUpdate", productForUpdate))
					continue
				}
				//还有一种情况是中台先有商品，之后企业购创建通过API返回之后编码匹配之后关联一下，那种情况没有插件id，所以要做列表使用关联方式不要使用插件id
				err = source.DB().Where("id = ?", product.ID).Updates(&productModel.ProductModel{PluginID: model.PLUGINID}).Error
				if err != nil {
					log.Log().Error("dh_erp同步商品保存商品插件id失败", zap.Any("err", err), zap.Any("id", product.ID))
					continue
				}
			}

			err = source.DB().Where("id = ?", daHangErpProduct.ID).Updates(&model.DaHangErpProduct{
				ProductId: product.ID,
			}).Error
			if err != nil {
				log.Log().Error("dh_erp同步商品保存商品id失败", zap.Any("err", err), zap.Any("item", item))
				continue
			}

			//log.Log().Error("测试创建商品",zap.Any("测试创建商品",product.ID),zap.Any("商品",productForUpdate))
		}
	}
	total, _ := strconv.Atoi(GoodsList.Total)
	CurrentTotal := request.Current * request.Size
	if CurrentTotal < total {
		request.Current += 1
		SynGoodsList(rd, gatherSupply, res, request, daHangErpSuppliers)
	}
}

/*
*

	创建采购端 和关系表
*/
func SyncCreateApplication(item common.ItemData, companyName string, user userModel.User) (err error) {
	var daHangErpItem model.DaHangErpItem
	err = source.DB().Where("event_code = ?", item.EventCode).First(&daHangErpItem).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Log().Error("dh_erp同步采购端创建：查询关联表daHangErpItem失败", zap.Any("err", err.Error()))
		err = errors.New("dh_erp同步采购端创建：查询关联表daHangErpItem失败" + err.Error())
		return
	}
	if daHangErpItem.ID == 0 {
		daHangErpItem.EventCode = item.EventCode
		daHangErpItem.EventName = item.EventName
		daHangErpItem.OuCode = item.OuCode
		daHangErpItem.OuName = item.OuName
		err = source.DB().Create(&daHangErpItem).Error
		if err != nil {
			log.Log().Error("dh_erp同步采购端创建：创建关联表daHangErpItem失败", zap.Any("err", err.Error()))

			err = errors.New("dh_erp同步采购端创建：创建关联表daHangErpItem失败" + err.Error())
			return
		}
	} else {
		err = source.DB().Model(&model.DaHangErpItem{}).Where("id = ?", daHangErpItem.ID).Updates(&model.DaHangErpItem{
			OuCode: item.OuCode,
			OuName: item.OuName,
		}).Error
		if err != nil {
			log.Log().Error("dh_erp同步采购端修改：修改关联表daHangErpItem失败", zap.Any("err", err.Error()))

			err = errors.New("dh_erp同步采购端修改：修改关联表daHangErpItem失败" + err.Error())
			return
		}
	}
	if daHangErpItem.ApplicationId == 0 {
		var applicationModelData applicationModel.Application
		var appName = item.EventName + "_" + item.EventCode //项目名称+项目编码
		err = source.DB().Where("app_name = ?", appName).First(&applicationModelData).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Log().Error("dh_erp同步采购端创建：查询采购端失败", zap.Any("err", err.Error()))

			err = errors.New("dh_erp同步采购端创建：查询采购端失败" + err.Error())
			return
		}

		//如果不存在,创建采购端
		if applicationModelData.ID == 0 {
			applicationModelData.CompanyName = companyName
			applicationModelData.AppName = appName
			applicationModelData.CompanyIntro = item.EventCode //项目编码
			applicationModelData.MemberId = int(user.ID)
			var appId uint
			err, appId = applicationService.CreateApplicationDoNotVerify(applicationModelData)
			if err != nil {
				log.Log().Error("dHerp自动创建采购端失败", zap.Any("err", err.Error()))
				err = errors.New("dh_erp同步采购端创建：dHerp自动创建采购端失败" + err.Error())
				return
			}
			applicationModelData.ID = appId
		}
		//保存采购端id 关联第三方活动
		err = source.DB().Where("id = ?", daHangErpItem.ID).Updates(&model.DaHangErpItem{
			ApplicationId: applicationModelData.ID,
		}).Error
		if err != nil {
			log.Log().Error("dh_erp同步采购端创建：保存关联表daHangErpItem失败", zap.Any("err", err.Error()))
			err = errors.New("dh_erp同步采购端创建：保存关联表daHangErpItem失败" + err.Error())
			return
		}
	}
	return
}

/*
*

	创建会员 和 会员和 项目活动的关系表
*/
func SyncCreateUser(data common.Customer, isOne int) (err error, userData userModel.User) {

	var daHangErpCust model.DaHangErpCust
	err = source.DB().Where("cust_code = ?", data.CustCode).Where("event_code = ?", data.EventCode).First(&daHangErpCust).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("dh_erp同步会员创建：创建关联表daHangErpCust失败" + err.Error())
		return
	}
	if daHangErpCust.ID == 0 {
		daHangErpCust.CustName = data.CustName
		daHangErpCust.CustCode = data.CustCode
		daHangErpCust.EventName = data.EventName
		daHangErpCust.EventCode = data.EventCode
		err = source.DB().Create(&daHangErpCust).Error
		if err != nil {
			err = errors.New("dh_erp同步会员创建：创建关联表daHangErpCust失败" + err.Error())
			return
		}
	} else {
		daHangErpCust.CustName = data.CustName
		daHangErpCust.EventName = data.EventName
		source.DB().Model(&model.DaHangErpCust{}).Where("id = ?", daHangErpCust.ID).Save(&daHangErpCust)
	}
	//客户要求只创建一个会员 采购端绑定那个会员 这里判断如果创建成功其他的只做记录不创建会员
	if daHangErpCust.UserId == 0 && isOne == 0 {
		var username = data.EventCode + "_" + data.CustCode
		var user userModel.User
		err = source.DB().Where("username = ?", username).First(&user).Error
		//如果错误不是没有查询结构直接跳过
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("dh_erp同步会员创建：查询会员错误" + err.Error())
			return
		}
		if user.ID == 0 {
			var createUser userModel.User
			createUser.Username = username
			createUser.NickName = data.EventName
			createUser.Password = "888888"

			var userSettingData userSetting.SysSetting
			//查出会员设置信息
			err = source.DB().Where("`key` = ?", "user_setting").First(&userSettingData).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return
			}
			if userSettingData.Value.NeedCheck == 1 {
				createUser.Status = 0
			} else {
				createUser.Status = 1
			}
			if userSettingData.Value.DefaultLevelID != 0 {
				createUser.LevelID = userSettingData.Value.DefaultLevelID
			}
			err, user.ID = userService.CreateUser(createUser)
			if err != nil {
				err = errors.New("dh_erp同步会员创建：创建会员错误" + err.Error())
				return
			}
		}
		err = source.DB().Where("id = ?", daHangErpCust.ID).Updates(&model.DaHangErpCust{UserId: user.ID}).Error
		if err != nil {
			err = errors.New("dh_erp同步会员创建：保存关系失败" + err.Error())
			return
		}
		userData.ID = user.ID
	} else {
		userData.ID = daHangErpCust.UserId
	}

	return
}

type Order struct {
	orderModel.Order
	DaHangErpOrder    model.DaHangErpOrder         `json:"da_hang_erp_order" gorm:"foreignKey:id;references:order_id"`                  //大昌行推送信息
	OrderExpresss     []orderModel.OrderExpress    `json:"order_expresss"`                                                              //order内的只查询一个无法对应子订单 这里重写一个
	DaHangErpItem     DaHangErpItem                `json:"da_hang_erp_item" gorm:"foreignKey:application_id;references:application_id"` //大昌行活动编码 以及客户编码
	DaHangErpSupplier model.DaHangErpSupplier      `json:"da_hang_erp_supplier" gorm:"foreignKey:supplier_id;references:supplier_id"`   //大昌行供应商编码
	PayType           string                       `json:"pay_type"`
	StatusName        string                       `json:"status_name"` // 状态名
	PayInfo           orderResponse.PayInfo        `json:"pay_info" gorm:"foreignKey:PayInfoID"`
	User              User                         `json:"user"`
	Application       applicationModel.Application `json:"application"`
	AmountDetail      response.AmountDetail        `json:"amount_detail"`
	OrderItems        OrderItems                   `json:"order_items"`
}

type OrderItems []OrderItem
type OrderItem struct {
	orderModel.OrderItemModel
	Options            orderModel.Options       `json:"options"`
	Sku                productModel.Sku         `json:"sku"`
	Product            productModel.Product     `json:"product"`
	DaHangErpOrderItem model.DaHangErpOrderItem `json:"da_hang_erp_order_item"`
	DaHangErpProduct   DaHangErpProduct         `json:"da_hang_erp_product" gorm:"foreignKey:product_id;references:product_id"` //大昌行供应商编码

	//gorm:"foreignkey:order_item_id"

}

// 大昌行供应商信息记录表
type DaHangErpProduct struct {
	source.Model
	ItemCode      string `json:"item_code" form:"item_code" gorm:"column:item_code;comment:商品编码;index;"`                 // 商品名称
	ItemName      string `json:"item_name" form:"item_name" gorm:"column:item_name;comment:商品名称;"`                       // 商品名称
	IsHelpFarming string `json:"is_help_farming" form:"is_help_farming" gorm:"column:is_help_farming;comment:是否助农0是1否;"` // 是否助农0是1否

	ProductId uint `json:"product_id" form:"product_id" gorm:"column:product_id;comment:商品id;"` // 商品id

	Cat4 string `json:"cat_4" form:"cat_4" gorm:"column:cat_4;comment:是否第三方编码1是 第三方API是string返回的;"` // 是否第三方编码

	ItemCateCode2 string `json:"item_cate_code_2" form:"item_cate_code_2" gorm:"column:item_cate_code_2;comment:第三方编码"` // 第三方编码

	SuppCode          string                  `json:"supp_code" form:"supp_code" gorm:"column:supp_code;comment:供应商编码;index;"` // 供应商编码
	SuppName          string                  `json:"supp_name" form:"supp_name" gorm:"column:supp_name;comment:供应商名称;"`       // 供应商名称
	DaHangErpSupplier model.DaHangErpSupplier `json:"da_hang_erp_supplier" gorm:"foreignKey:supp_code;references:supp_code"`   //大昌行供应商编码

}

type User struct {
	ID       uint   `json:"id"`
	NickName string `json:"nickname"`
	Mobile   string `json:"mobile"`
	Avatar   string `json:"avatar"`
	Username string `json:"username"`
}

// 大昌行API项目信息
type DaHangErpItem struct {
	ID            uint                `json:"id"`
	EventCode     string              `json:"event_code" form:"event_code" gorm:"column:event_code;comment:活动编码;index;"`        //活动编码
	EventName     string              `json:"event_name" form:"event_name" gorm:"column:event_name;comment:活动名称;"`              // 活动名称
	ApplicationId uint                `json:"application_id" form:"application_id" gorm:"column:application_id;comment:采购端id;"` //采购端id
	DaHangErpCust model.DaHangErpCust `json:"da_hang_erp_cust" gorm:"foreignKey:event_code;references:event_code"`              //客户信息
	OuCode        string              `json:"ou_code" form:"ou_code" gorm:"column:ou_code;comment:公司编码;index;"`                 // 公司编码
	OuName        string              `json:"ou_name" form:"ou_name" gorm:"column:ou_name;comment:公司名称;"`                       // 公司名称
}

func (b *Order) AfterFind(tx *gorm.DB) (err error) {
	b.StatusName = orderModel.GetStatusName(b.Status)
	if b.ApplicationID > 0 {
		var accountType []orderResponse.AccountType
		err = source.DB().Find(&accountType).Error

		accountType = append(accountType, orderResponse.AccountType{
			ID:          1,
			AccountName: "汇聚余额",
		})
		accountType = append(accountType, orderResponse.AccountType{
			ID:          2,
			AccountName: "站内余额",
		})
		if b.PayTypeID == -3 {
			b.PayType = "采购端库存"
		}
		//if collection.Collect(gva.GlobalAuth.ToolsPlugin).Contains(28) {
		//	accountType = append(accountType, AccountType{
		//		ID:          -3,
		//		AccountName: "采购端库存",
		//	})
		//}
		for _, v := range accountType {
			if b.PayTypeID == int(v.ID) {
				b.PayType = v.AccountName
			}
		}

	} else {
		_, payType := service.GetPayType()
		for _, v := range payType {
			if b.PayTypeID == v.Code {
				b.PayType = v.Name
			}
		}
	}

	if b.PayTypeID == -1 {
		b.PayType = "后台支付"
	}

	if b.PayType == "" {
		b.PayType = "未知"
	}
	return
}

//	type SupplierSource struct {
//		source.Model
//		SupplierID               uint   `json:"supplier_id"`
//		Name                     string `json:"name"`
//		SupplierSourceCategoryID uint   `json:"supplier_source_category_id"`
//	}
//
//	type SupplierSourceCategory struct {
//		source.Model
//		SupplierID uint   `json:"supplier_id"`
//		Name       string `json:"name"`
//	}
type Product struct {
	productModel.Product
	SupplierSource         supplierModel.SupplierSource         `json:"supplier_source"`
	SupplierSourceCategory supplierModel.SupplierSourceCategory `json:"supplier_source_category"`
}

// 推送订单
func PushOrder(pushOrder request.PushOrder, rd common.RequestData) (err error) {
	var orderData Order
	err = source.DB().Where("id = ?", pushOrder.OrderId).Preload("Application").Preload("User").Preload("ShippingAddress").Preload("PayInfo").Preload("DaHangErpSupplier").Preload("DaHangErpItem.DaHangErpCust").Preload("DaHangErpItem").Preload("OrderExpresss").Preload("OrderExpresss.OrderItems").Preload("DaHangErpOrder").Preload("OrderItems").Preload("OrderItems.DaHangErpOrderItem").Preload("OrderItems.Sku").Preload("OrderItems.DaHangErpProduct").Preload("OrderItems.DaHangErpProduct.DaHangErpSupplier").Preload("OrderItems.Product").First(&orderData).Error
	if err != nil {
		log.Log().Error("订单不存在", zap.Any("err", err))
		err = errors.New("订单不存在" + err.Error())
		return
	}
	if orderData.Status == -1 {
		err = errors.New("已关闭订单无法推送")
		return
	}
	if orderData.DaHangErpOrder.ID == 0 {
		log.Log().Error("不可推送", zap.Any("order_id", orderData.ID))
		err = errors.New("不可推送")
		return
	}
	if orderData.DaHangErpOrder.Status == -2 {
		log.Log().Error("不可推送", zap.Any("ErrorMsg", orderData.DaHangErpOrder.ErrorMsg))

		err = errors.New("不可推送" + orderData.DaHangErpOrder.ErrorMsg)
		return
	}
	if orderData.Application.ExportAppLink == "" {
		log.Log().Error("请去采购端配置-采购端订单信息地址", zap.Any("order_id", orderData.ID))

		err = errors.New("请去采购端配置-采购端订单信息地址")
		return
	}

	var pushOrderRequsets common.PushOrderRequsets
	var msg string
	var orders []string
	orders = append(orders, orderData.ThirdOrderSN)

	var isCreateDaHangErpOrder = 0 //是否需要创建大昌行记录 0不要1 需要
	if orderData.DaHangErpOrder.ID == 0 || orderData.OrderItems[0].DaHangErpOrderItem.ID == 0 {
		isCreateDaHangErpOrder = 1
	}
	for _, item := range orderData.OrderItems {
		if item.DaHangErpOrderItem.ID == 0 {
			isCreateDaHangErpOrder = 1
			break
		}
	}

	if isCreateDaHangErpOrder == 1 {
		var PushCount int
		var createMsg string
		var daHangErpOrder model.DaHangErpOrder
		err, PushCount, createMsg = CreateDaHangErpOrder(orderData)
		daHangErpOrder.OrderId = orderData.ID
		if err != nil {
			err = errors.New("保存子订单记录失败:" + err.Error())
			return
		} else {
			daHangErpOrder.Status = 0
			if PushCount == 0 {
				err = errors.New("下单商品都不是大昌行API返回的商品")
				return
			}
			if createMsg != "" {
				daHangErpOrder.ErrorMsg = createMsg
			}
		}
		if orderData.DaHangErpOrder.ID != 0 {
			err = source.DB().Where("id = ?", orderData.DaHangErpOrder.ID).Omit("created_at", "status").Save(&daHangErpOrder).Error
		} else {
			err = source.DB().Create(&daHangErpOrder).Error
		}
		if err != nil {
			err = errors.New("创建订单记录失败:" + err.Error())
			return
		}
		err = source.DB().Where("id = ?", pushOrder.OrderId).Preload("Application").Preload("User").Preload("ShippingAddress").Preload("PayInfo").Preload("DaHangErpSupplier").Preload("DaHangErpItem.DaHangErpCust").Preload("DaHangErpItem").Preload("OrderExpresss").Preload("OrderExpresss.OrderItems").Preload("DaHangErpOrder").Preload("OrderItems").Preload("OrderItems.DaHangErpOrderItem").Preload("OrderItems.Sku").Preload("OrderItems.DaHangErpProduct").Preload("OrderItems.DaHangErpProduct.DaHangErpSupplier").Preload("OrderItems.Product").First(&orderData).Error
		if err != nil {
			log.Log().Error("订单不存在", zap.Any("err", err))
			err = errors.New("订单不存在" + err.Error())
			return
		}
	}

	//var shopdata common.GetShopOrderDataResponse

	err, shopdata := common.GetShopOrderData(orders, orderData.Application.ExportAppLink)
	if err != nil {
		return
	}
	var daHangErpOrderItemIds []uint //记录推送的子订单记录表id 用来更新是否推送
	for _, item := range orderData.OrderItems {
		if item.Qty == 0 || item.Amount == 0 {
			log.Log().Error("金额/个数为0的子订单不推送")
			continue
		}
		var pushOrderRequset common.PushOrderRequset
		pushOrderRequset.OrderCode = strconv.Itoa(int(orderData.OrderSN))
		pushOrderRequset.ThirdOrderId = strconv.Itoa(int(orderData.ID))
		pushOrderRequset.MallOrderNo = orderData.ThirdOrderSN
		pushOrderRequset.SonOrderNo = strconv.Itoa(int(item.ID))
		pushOrderRequset.OrderType = "1"
		pushOrderRequset.EventCode = orderData.DaHangErpItem.EventCode
		pushOrderRequset.EventName = orderData.DaHangErpItem.EventName
		pushOrderRequset.PayMethod = orderData.PayType
		pushOrderRequset.OrderStatus = orderData.StatusName
		pushOrderRequset.PayOrderNo = orderData.PayInfo.PaySn
		pushOrderRequset.OuCode = orderData.DaHangErpItem.OuCode
		pushOrderRequset.OuName = orderData.DaHangErpItem.OuName

		if item.DaHangErpOrderItem.IsPush == 1 {
			pushOrderRequset.IsRepush = "Y"
		} else {
			pushOrderRequset.IsRepush = "N"
		}
		for _, shopOrder := range shopdata.Data {
			pushOrderRequset.EmpName = shopOrder.Username
			pushOrderRequset.EmpPhone = shopOrder.Mobile
			//如果没有分组使用之前的逻辑,如果返回了分组名称 则编码直接使用分组名称
			if shopOrder.GroupName == "" {
				err = errors.New("获取商城分组名称失败")
				log.Log().Error("获取商城分组名称失败", zap.Any("shopOrder", shopOrder))

				return
				//pushOrderRequset.CustCode = orderData.DaHangErpItem.DaHangErpCust.CustCode
				//pushOrderRequset.CustName = orderData.DaHangErpItem.DaHangErpCust.CustName
			} else {
				pushOrderRequset.CustCode = shopOrder.GroupName
			}
			for _, shopOrderItem := range shopOrder.OrderItem {
				if shopOrderItem.YzOptionID == item.SkuID {
					PaymentAmount, _ := strconv.ParseFloat(shopOrderItem.PaymentAmount, 64)
					Point, _ := strconv.ParseFloat(shopOrderItem.Point, 64)
					pushOrderRequset.PayAmt = PaymentAmount
					pushOrderRequset.PayPoints = Point

					purPrice, _ := strconv.ParseFloat(shopOrderItem.GoodsCostPrice, 64)
					salePrice, _ := strconv.ParseFloat(shopOrderItem.GoodsPrice, 64)
					var pushOrderRequsetPurPrice float64 = 0
					if purPrice > 0 {
						pushOrderRequsetPurPrice = purPrice / float64(shopOrderItem.Total)
					}

					var pushOrderRequsetsalePrice float64 = 0
					if salePrice > 0 {
						pushOrderRequsetsalePrice = salePrice / float64(shopOrderItem.Total) //销售单价
					}

					pushOrderRequset.PurPrice = pushOrderRequsetPurPrice
					pushOrderRequset.PurAmt = purPrice
					pushOrderRequset.SalePrice = pushOrderRequsetsalePrice //销售单价
					pushOrderRequset.SaleAmt = salePrice                   //销售总额
				}
			}
		}
		pushOrderRequset.OrderTime = orderData.CreatedAt.Format("2006-01-02 15:04:05")
		if orderData.PaidAt != nil {
			pushOrderRequset.PayTime = orderData.PaidAt.Format("2006-01-02 15:04:05")
			pushOrderRequset.PayStatus = "已支付"
		}
		if orderData.SentAt != nil {
			pushOrderRequset.DeliveryTime = orderData.SentAt.Format("2006-01-02 15:04:05")
		}
		if orderData.ReceivedAt != nil {
			pushOrderRequset.CloseTime = orderData.ReceivedAt.Format("2006-01-02 15:04:05")
		}
		pushOrderRequset.SuppCode = item.DaHangErpProduct.SuppCode
		pushOrderRequset.SuppName = item.DaHangErpProduct.SuppName

		pushOrderRequset.RecipientName = orderData.ShippingAddress.Realname
		pushOrderRequset.RecipientMobile = orderData.ShippingAddress.Mobile
		pushOrderRequset.Province = orderData.ShippingAddress.Province
		pushOrderRequset.City = orderData.ShippingAddress.City
		pushOrderRequset.District = orderData.ShippingAddress.County
		pushOrderRequset.Address = orderData.ShippingAddress.Detail

		//-2代表不需要推送的商品
		if item.DaHangErpOrderItem.Status == -2 || item.DaHangErpOrderItem.ID == 0 {
			continue
		}
		//var DaHangErpProduct model.DaHangErpProduct
		//err = source.DB().Where("product_id = ?", item.ProductID).First(&DaHangErpProduct).Error
		if item.DaHangErpProduct.ID == 0 {
			msg = "查询大昌行商品信息失败"
			item.DaHangErpOrderItem.ErrorMsg = item.DaHangErpOrderItem.ErrorMsg + "查询大昌行商品信息失败"
			source.DB().Model(&model.DaHangErpOrderItem{}).Where("id = ?", item.DaHangErpOrderItem.ID).Updates(&model.DaHangErpOrderItem{
				ErrorMsg: item.DaHangErpOrderItem.ErrorMsg,
			})
			continue
		}
		var product Product
		err = source.DB().Model(&Product{}).Where("id = ?", item.ProductID).Preload("SupplierSource").Preload("SupplierSourceCategory").First(&product).Error
		if err != nil {
			msg = "查询商品信息失败" + err.Error()
			item.DaHangErpOrderItem.ErrorMsg = item.DaHangErpOrderItem.ErrorMsg + "查询商品信息失败" + err.Error()
			source.DB().Model(&model.DaHangErpOrderItem{}).Where("id = ?", item.DaHangErpOrderItem.ID).Updates(&model.DaHangErpOrderItem{
				ErrorMsg: item.DaHangErpOrderItem.ErrorMsg,
			})
			continue
		}
		pushOrderRequset.IsHelpFarming = item.DaHangErpProduct.IsHelpFarming
		pushOrderRequset.ItemCode = item.DaHangErpProduct.ItemCode
		pushOrderRequset.ItemName = item.DaHangErpProduct.ItemName
		pushOrderRequset.Qty = item.Qty

		var taxProductName, taxCode, taxShortCode string
		var taxRate int
		if item.Product.BillPosition == 1 {
			taxShortCode = item.Product.ShortCode
			taxProductName = item.Product.TaxProductName
			taxCode = item.Product.TaxCode
			taxRate = item.Product.TaxRate
		} else {
			taxShortCode = item.Sku.ShortCode
			taxProductName = item.Sku.TaxProductName
			taxCode = item.Sku.TaxCode
			taxRate = item.Sku.TaxRate
		}
		pushOrderRequset.ItemAbbr = taxShortCode
		pushOrderRequset.PurRate = taxRate
		pushOrderRequset.InvoiceCode = taxCode
		pushOrderRequset.InvoiceName = taxProductName
		// 商品单价
		//var skuPriceYuan,amountYuan float64
		//var skuPriceFen,amountFen uint
		//
		//if item.Qty != 0 {
		//	skuPriceFen = item.Amount/ item.Qty
		//} else {
		//	skuPriceFen = 0
		//}
		//skuPriceYuan = common.Fen2Yuan(skuPriceFen)
		//// 应收款(元)
		//amountFen = item.Amount+item.TechnicalServicesFee
		//amountYuan = common.Fen2Yuan(amountFen)

		pushOrderRequset.Uom = item.Product.Unit
		pushOrderRequset.Spec = item.SkuTitle
		//pushOrderRequset.LineNo = item.ID
		//pushOrderRequset.PayAmt = amountYuan
		//pushOrderRequset.PayPoints = 0
		pushOrderRequset.SaleRate = taxRate

		pushOrderRequset.ItemSource = product.SupplierSource.Name
		pushOrderRequset.SourceCategory = product.SupplierSourceCategory.Name
		pushOrderRequset.MemberID = strconv.Itoa(int(orderData.UserID))
		pushOrderRequset.MemberNickName = orderData.User.Username

		for _, express := range orderData.OrderExpresss {
			for _, expressItem := range express.OrderItems {
				if expressItem.ID == item.ID {
					var thirdExpressSaveVO common.ThirdExpressSaveVO
					var companyName string
					err, companyName = shippingExpress.GetCompanyByCode(express.CompanyCode)
					if err != nil {
						companyName = express.CompanyCode
					}
					thirdExpressSaveVO.ExpressCode = express.CompanyCode
					thirdExpressSaveVO.ExpressName = companyName
					thirdExpressSaveVO.ExpressNo = express.ExpressNo

					pushOrderRequset.ThirdExpressSaveVOS = append(pushOrderRequset.ThirdExpressSaveVOS, thirdExpressSaveVO)
				}

			}
		}
		pushOrderRequsets = append(pushOrderRequsets, pushOrderRequset)
		//记录推送的子订单记录表id
		daHangErpOrderItemIds = append(daHangErpOrderItemIds, item.DaHangErpOrderItem.ID)

	}

	if len(pushOrderRequsets) > 0 {
		err = rd.PushOrder(pushOrderRequsets)
		if err != nil {
			msg += err.Error()
			err = nil
		}
	}
	requestData, _ := json.Marshal(pushOrderRequsets)

	//msg = ""
	if msg != "" {
		log.Log().Error("订单推送失败", zap.Any("msg", msg))
		err = errors.New(msg)
		source.DB().Model(&model.DaHangErpOrder{}).Where("id = ?", orderData.DaHangErpOrder.ID).Updates(&model.DaHangErpOrder{
			PushErrorMsg:  msg,
			Status:        -1,
			PushErrorData: string(requestData),
		})
		return
	} else {
		err = source.DB().Model(&model.DaHangErpOrder{}).Where("id = ?", orderData.DaHangErpOrder.ID).Updates(&model.DaHangErpOrder{
			Status:        2,
			PushErrorData: string(requestData),
		}).Error

		if err != nil {
			log.Log().Error("订单推送成功保存状态失败", zap.Any("err", err))
			err = errors.New("订单推送成功保存状态失败,可再次点击推送更新推送记录表的状态" + err.Error())
			return
		}

		err = source.DB().Model(&model.DaHangErpOrderItem{}).Where("id in ?", daHangErpOrderItemIds).Updates(&model.DaHangErpOrderItem{
			IsPush: 1,
		}).Error

		if err != nil {
			log.Log().Error("订单推送成功保存子订单记录状态失败", zap.Any("err", err))
			err = errors.New("订单推送成功子表保存状态失败,可再次点击推送更新推送记录表的状态" + err.Error())
			return
		}

	}

	//pushOrderRequset.ItemAbbr  //商品简码 可选 暂定商品id
	//pushOrderRequset.

	//反复推送是更新
	//if orderData.DaHangErpOrder.Status == 2 {
	//	errors.New("已全部推送")
	//	return
	//}
	return
}

// 推送订单
func PushOrderByApplicationId(pushOrder request.PushOrderByApplicationId) (err error) {

	err, rd := common.Initial()
	if err != nil {
		return
	}
	var orderDatas []Order
	err = source.DB().Select("orders.*").Joins("left join da_hang_erp_orders on da_hang_erp_orders.order_id = orders.id").Where("da_hang_erp_orders.status in ?", []int{0, 1}).Where("orders.application_id = ?", pushOrder.ApplicationId).Where("orders.status = 3").Preload("Application").Preload("User").Preload("ShippingAddress").Preload("PayInfo").Preload("DaHangErpSupplier").Preload("DaHangErpItem.DaHangErpCust").Preload("DaHangErpItem").Preload("OrderExpresss").Preload("OrderExpresss.OrderItems").Preload("DaHangErpOrder").Preload("OrderItems").Preload("OrderItems.DaHangErpOrderItem").Preload("OrderItems.Sku").Preload("OrderItems.Product").Find(&orderDatas).Error
	if err != nil {
		err = errors.New("订单不存在" + err.Error())
		return
	}
	if len(orderDatas) == 0 {
		err = errors.New("没有需要推送的订单")
		return
	}
	go PushOrderByApplicationIdStep1(orderDatas, rd)
	return
}
func PushOrderByApplicationIdStep1(orderDatas []Order, rd common.RequestData) {
	var err error
	for _, order := range orderDatas {
		err = PushOrder(request.PushOrder{OrderId: order.ID}, rd)
		if err != nil {
			log.Log().Error("订单推送失败", zap.Any("err", err))
			continue
		}
	}
}

// 表格推送订单
func ExcelPushOrder(rows [][]string, rd common.RequestData) (err error) {
	var msg = ""
	var orderIds []uint
	for key, row := range rows {
		//第一行跳过
		if key == 0 {
			continue
		}
		var orderData Order
		err = source.DB().Where("order_sn = ?", row[0]).Preload("Application").Preload("User").Preload("ShippingAddress").Preload("PayInfo").Preload("DaHangErpSupplier").Preload("DaHangErpItem.DaHangErpCust").Preload("DaHangErpItem").Preload("OrderExpresss").Preload("OrderExpresss.OrderItems").Preload("DaHangErpOrder").Preload("OrderItems").Preload("OrderItems.DaHangErpOrderItem").Preload("OrderItems.Sku").Preload("OrderItems.Product").First(&orderData).Error
		if err != nil {
			log.Log().Error("大昌行表格推送-订单不存在", zap.Any("err", err), zap.Any("row", row))
			msg += "订单不存在:" + row[0] + "" + err.Error()
			continue
		}
		//if orderData.DaHangErpOrder.ID == 0 {
		//	log.Log().Error("不可推送", zap.Any("order_id", orderData.ID))
		//	msg += "没有记录不可推送:" + row[0] + ""
		//	continue
		//}
		var isCreateDaHangErpOrder = 0 //是否需要创建大昌行记录 0不要1 需要
		if orderData.DaHangErpOrder.ID == 0 || orderData.OrderItems[0].DaHangErpOrderItem.ID == 0 {
			isCreateDaHangErpOrder = 1
		}
		for _, item := range orderData.OrderItems {
			if item.DaHangErpOrderItem.ID == 0 {
				isCreateDaHangErpOrder = 1
				break
			}
		}
		//如果没有记录则保存记录
		if isCreateDaHangErpOrder == 1 {
			var PushCount int
			var createMsg string
			var daHangErpOrder model.DaHangErpOrder
			err, PushCount, createMsg = CreateDaHangErpOrder(orderData)
			daHangErpOrder.OrderId = orderData.ID
			if err != nil {
				msg += "保存子订单记录失败:" + row[0] + "" + err.Error()
				continue
			} else {
				daHangErpOrder.Status = 0
				if PushCount == 0 {
					msg += "下单商品都不是大昌行API返回的商品:" + row[0] + ""
					continue
				}
				if createMsg != "" {
					daHangErpOrder.ErrorMsg = createMsg
				}
			}
			if orderData.DaHangErpOrder.ID != 0 {
				err = source.DB().Where("id = ?", orderData.DaHangErpOrder.ID).Omit("created_at", "status").Save(&daHangErpOrder).Error
			} else {
				err = source.DB().Create(&daHangErpOrder).Error
			}
			if err != nil {
				log.Log().Error("创建订单记录失败", zap.Any("err", err))
				err = nil
				msg += "创建订单记录失败:" + row[0] + "" + err.Error()
				continue
			}
			err = source.DB().Where("order_sn = ?", row[0]).Preload("Application").Preload("User").Preload("ShippingAddress").Preload("PayInfo").Preload("DaHangErpSupplier").Preload("DaHangErpItem.DaHangErpCust").Preload("DaHangErpItem").Preload("OrderExpresss").Preload("OrderExpresss.OrderItems").Preload("DaHangErpOrder").Preload("OrderItems").Preload("OrderItems.DaHangErpOrderItem").Preload("OrderItems.Sku").Preload("OrderItems.Product").First(&orderData).Error
			if err != nil {
				log.Log().Error("大昌行表格推送-订单不存在", zap.Any("err", err), zap.Any("row", row))
				msg += "订单不存在:" + row[0] + "" + err.Error()
				continue
			}
		}
		itemId, _ := strconv.Atoi(row[1])
		var isCun = 0
		var IsPush = 0
		for _, item := range orderData.OrderItems {
			if item.ID == uint(itemId) {
				isCun = 1
				IsPush = item.DaHangErpOrderItem.IsPush
				break
			}
		}
		if isCun == 0 {
			log.Log().Error("子订单不属于这个订单无法推送", zap.Any("row", row))
			msg += "子订单不属于这个订单无法推送:" + row[0] + ""
			continue
		}
		if orderData.ThirdOrderSN != row[2] {
			log.Log().Error("商城订单号不属于这个订单无法推送", zap.Any("row", row))
			msg += "商城订单号不属于这个订单无法推送:" + row[0] + ""
			continue
		}
		var pushOrderRequsets common.PushOrderRequsets
		var pushOrderRequset common.PushOrderRequset
		if IsPush == 1 {
			pushOrderRequset.IsRepush = "Y"
		} else {
			pushOrderRequset.IsRepush = "N"
		}

		pushOrderRequset.ThirdOrderId = strconv.Itoa(int(orderData.ID))
		pushOrderRequset.OuCode = "18122" //固定这个
		pushOrderRequset.OrderCode = row[0]
		pushOrderRequset.SonOrderNo = row[1]
		pushOrderRequset.MallOrderNo = row[2]
		pushOrderRequset.OrderType = row[3]
		pushOrderRequset.EventCode = row[4]
		pushOrderRequset.EventName = row[5]
		pushOrderRequset.PayMethod = row[6]
		pushOrderRequset.OrderStatus = row[7]
		ShippingFee, _ := strconv.ParseFloat(row[8], 64)
		pushOrderRequset.ShippingFee = ShippingFee
		pushOrderRequset.PayOrderNo = row[9]
		pushOrderRequset.PayStatus = row[10]
		pushOrderRequset.CustCode = row[11]
		pushOrderRequset.CustName = row[12]
		pushOrderRequset.OrderTime = row[13]
		pushOrderRequset.PayTime = row[14]
		pushOrderRequset.DeliveryTime = row[15]
		pushOrderRequset.CloseTime = row[16]
		pushOrderRequset.SuppCode = row[17]
		pushOrderRequset.SuppName = row[18]
		pushOrderRequset.ItemAbbr = row[19]
		pushOrderRequset.ItemName = row[20]
		pushOrderRequset.ItemCode = row[21]
		qty, _ := strconv.Atoi(row[22])
		pushOrderRequset.Qty = uint(qty)
		pushOrderRequset.InvoiceName = row[23]
		pushOrderRequset.InvoiceCode = row[24]
		purRate, _ := strconv.Atoi(row[25])
		pushOrderRequset.PurRate = purRate
		purPrice, _ := strconv.ParseFloat(row[26], 64)
		pushOrderRequset.PurPrice = purPrice
		purAmt, _ := strconv.ParseFloat(row[27], 64)
		pushOrderRequset.PurAmt = purAmt
		salePrice, _ := strconv.ParseFloat(row[28], 64)
		pushOrderRequset.SalePrice = salePrice
		saleAmt, _ := strconv.ParseFloat(row[29], 64)
		pushOrderRequset.SaleAmt = saleAmt
		pushOrderRequset.Uom = row[30]
		pushOrderRequset.Spec = row[31]
		lineNo, _ := strconv.Atoi(row[32])
		pushOrderRequset.LineNo = int64(lineNo)
		payAmt, _ := strconv.ParseFloat(row[33], 64)
		pushOrderRequset.PayAmt = payAmt
		payPoints, _ := strconv.ParseFloat(row[34], 64)
		pushOrderRequset.PayPoints = payPoints
		saleRate, _ := strconv.Atoi(row[35])
		pushOrderRequset.SaleRate = saleRate
		pushOrderRequset.RecipientName = row[36]
		pushOrderRequset.RecipientMobile = row[37]
		pushOrderRequset.Province = row[38]
		pushOrderRequset.City = row[39]
		pushOrderRequset.District = row[40]
		pushOrderRequset.Address = row[41]
		pushOrderRequset.ItemSource = row[42]
		pushOrderRequset.SourceCategory = row[43]
		pushOrderRequset.MemberID = row[44]
		pushOrderRequset.MemberNickName = row[45]
		pushOrderRequset.IsHelpFarming = row[46]
		pushOrderRequset.OuName = row[47]
		pushOrderRequset.OuCode = row[48]
		pushOrderRequset.EmpName = row[49]
		pushOrderRequset.EmpPhone = row[50]

		for _, express := range orderData.OrderExpresss {
			for _, expressItem := range express.OrderItems {
				if expressItem.ID == uint(itemId) {
					var thirdExpressSaveVO common.ThirdExpressSaveVO
					var companyName string
					err, companyName = shippingExpress.GetCompanyByCode(express.CompanyCode)
					if err != nil {
						companyName = express.CompanyCode
					}
					thirdExpressSaveVO.ExpressCode = express.CompanyCode
					thirdExpressSaveVO.ExpressName = companyName
					thirdExpressSaveVO.ExpressNo = express.ExpressNo
					pushOrderRequset.ThirdExpressSaveVOS = append(pushOrderRequset.ThirdExpressSaveVOS, thirdExpressSaveVO)
				}
			}
		}
		pushOrderRequsets = append(pushOrderRequsets, pushOrderRequset)
		err = rd.PushOrder(pushOrderRequsets)
		if err != nil {
			log.Log().Error("订单推送错误", zap.Any("pushOrderRequsets", pushOrderRequsets), zap.Any("err", err))
			msg += err.Error()
			requestData, _ := json.Marshal(pushOrderRequsets)

			orderData.DaHangErpOrder.PushErrorMsg = err.Error()
			//保存错误
			err = source.DB().Model(&model.DaHangErpOrder{}).Where("id = ?", orderData.DaHangErpOrder.ID).Updates(&model.DaHangErpOrder{
				PushErrorMsg:  orderData.DaHangErpOrder.PushErrorMsg,
				Status:        -1,
				PushErrorData: string(requestData),
			}).Error
			if err != nil {
				log.Log().Error("订单推送错误-错误保存记录失败", zap.Any("pushOrderRequsets", pushOrderRequsets), zap.Any("err", err))
			}
			err = nil
			continue
		}
		//变更状态为已推送
		err = source.DB().Model(&model.DaHangErpOrderItem{}).Where("order_item_id = ?", itemId).Updates(&model.DaHangErpOrderItem{
			Status: 1,
			IsPush: 1,
		}).Error
		if err != nil {
			log.Log().Error("订单推送成功-错误保存记录失败", zap.Any("pushOrderRequsets", pushOrderRequsets), zap.Any("err", err))
			err = nil
		}
		orderIds = append(orderIds, orderData.ID) //记录推送订单id
	}
	for _, orderId := range orderIds {
		var daHangErpOrderItem model.DaHangErpOrderItem
		err = source.DB().Model(&model.DaHangErpOrderItem{}).Where("order_id = ? and (status = 0 or status = -1)").First(&daHangErpOrderItem).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Log().Error("大昌行改变订单状态-查询记录表非空错误", zap.Any("err", err))
			return nil
		}
		if daHangErpOrderItem.ID == 0 {
			err = source.DB().Model(&model.DaHangErpOrder{}).Where("order_id = ?", orderId).Updates(&model.DaHangErpOrder{
				Status: 2,
			}).Error
			if err != nil {
				log.Log().Error("大昌行改变推送状态为全部推送失败", zap.Any("err", err), zap.Any("order_id", orderId))
				msg += "大昌行改变推送状态为部分推送失败" + strconv.Itoa(int(orderId)) + "" + err.Error()
			}
		} else {
			//err = source.DB().Model(&model.DaHangErpOrder{}).Where("order_id = ?",orderId).Updates(&model.DaHangErpOrder{
			//	Status: 1,
			//}).Error
			//if err != nil {
			//	log.Log().Error("大昌行改变推送状态为部分推送失败", zap.Any("err", err),zap.Any("order_id", orderId))
			//	msg += "大昌行改变推送状态为部分推送失败"+ strconv.Itoa(int(orderId)) +""+err.Error()
			//}
		}
	}
	if msg != "" {
		err = errors.New(msg)
		return
	}

	return
}

//已同步商品列表
/*
*
	已同步商品列表
*/
func GetDaHangErpProductList(info request.GetDaHangErpProductSearch) (err error, list []dahangErpResponse.DaHangErpProductList, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&dahangErpResponse.DaHangErpProductList{}).Preload("DaHangErpSupplier").Preload("Product").Preload("Product.Supplier").Preload("Product.Brand").Preload("Product.Skus")
	db = Search(info, db)
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	err = db.Limit(limit).Order("da_hang_erp_products.id desc").Offset(offset).Find(&list).Error
	if err != nil {
		return
	}
	for key, item := range list {
		if len(item.Product.Skus) > 1 {
			list[key].Product.ProductBill = item.Product.Skus[0].ProductBill
		}
	}
	return err, list, total
}

func DeleteDahangErpId(data model.DaHangErpProduct) (err error) {
	if data.ID == 0 {
		err = errors.New("请提交id")
		return
	}

	err = source.DB().Model(&model.DaHangErpProduct{}).Where("id = ?", data.ID).Delete(&model.DaHangErpProduct{}).Error
	if err != nil {
		err = errors.New("修改失败" + err.Error())
		return

	}

	return
}
func UpdateDahangErpProcudeProductId(data model.DaHangErpProduct) (err error) {
	if data.ID == 0 {
		err = errors.New("请提交id")
		return
	}
	if data.ProductId == 0 {
		err = errors.New("请提交要修改的商品id")
		return
	}
	err = source.DB().Model(&model.DaHangErpProduct{}).Where("id = ?", data.ID).Updates(&model.DaHangErpProduct{ProductId: data.ProductId}).Error
	if err != nil {
		err = errors.New("修改失败" + err.Error())
		return

	}

	return
}

// GetProductInfoList
// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetProductInfoList
// @description: 分页获取Product完整记录列表
// @param: info request.ProductSearch
// @return: err error, list []model.Product, total int64
func GetProductInfoList(info request.ProductSearch) (err error, list []dahangErpResponse.ProductInfoList, total int64) {

	//.Preload("Skus").Preload("Brand").Preload("GatherSupply")
	db := source.DB().Model(&dahangErpResponse.ProductInfoList{}).Preload("SupplierSource").Preload("Supplier").Omit("detail_images")
	db = db.Where("deleted_at is NULL")
	var products []dahangErpResponse.ProductInfoList

	if info.Title != "" {
		db = db.Where("title like ?", "%"+info.Title+"%")
	}

	if info.SupplierID != 0 {
		db = db.Where("supplier_id = ?", info.SupplierID)
	}
	if info.GatherSupplyID != nil {

		db = db.Where("gather_supply_id = ?", info.GatherSupplyID)
	}
	if info.BrandID != 0 {
		db = db.Where("brand_id = ?", info.BrandID)

	}
	if info.ID != 0 {

		db = db.Where("id = ?", info.ID)

	}
	if info.JushuitanBind != nil {

		db = db.Where("jushuitan_bind = ?", info.JushuitanBind)

	}
	if info.Category1ID != 0 {
		db = db.Where("category1_id = ?", info.Category1ID)

	}
	if info.Category2ID != 0 {
		db = db.Where("category2_id = ?", info.Category2ID)
	}
	if info.Category3ID != 0 {
		db = db.Where("category3_id = ?", info.Category3ID)
	}

	if info.IsRecommend != nil {
		db = db.Where("is_recommend = ?", &info.IsRecommend)
	}
	if info.IsNew != nil {
		db = db.Where("is_new = ?", &info.IsNew)
	}
	if info.IsHot != nil {
		db = db.Where("is_hot = ?", &info.IsHot)

	}
	if info.IsPromotion != nil {
		db = db.Where("is_promotion = ?", &info.IsPromotion)

	}
	if info.StatusLock != nil {
		db = db.Where("status_lock = ?", &info.StatusLock)

	}
	if info.Filter == 1 {
		db = db.Where("is_display = 1")

	}
	if info.Filter == 2 {
		db = db.Where("is_display = 0")

	}
	if info.Filter == 3 {

		db = db.Where("is_display = 1 and stock <= 0")

	}
	if info.MinPrice != 0 {
		db = db.Where("price >= ?", info.MinPrice*100)

	}
	if info.MaxPrice != 0 {
		//filterQ.Must(elastic.NewRangeQuery("agreement_price").Lte(info.MaxPrice * 100))
		db = db.Where("price <= ?", info.MaxPrice*100)

	}

	if info.IsBill != nil {
		db = db.Where("is_bill = ?", info.IsBill)
	}
	if info.Sn != "" {
		db = db.Where("sn = ?", info.Sn)
	}
	// 指定供应链
	if info.GatherSupplyID != nil {
		db = db.Where("gather_supply_id = ?", info.GatherSupplyID)
	}

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	db.Where("is_plugin = 0")
	err = db.Count(&total).Error
	var sortType string
	switch info.SortType {
	case "0":
		sortType = "id"
		break
	case "1":
		sortType = "price"
		break
	case "2":
		sortType = "cost_price"
		break
	case "3":
		sortType = "origin_price"
		break
	case "4":
		sortType = "profit_rate"
		break
	case "5":
		sortType = "sales"
		break
	case "6":
		sortType = "sort desc, id"
		break
	default:
		sortType = "id"
		break
	}
	if info.SortMode == "" {
		info.SortMode = "desc"
	}
	err = db.Order(sortType + " " + info.SortMode).Limit(limit).Offset(offset).Find(&products).Error

	for k, product := range products {
		var maxPrice uint
		var minPrice uint
		var maxGuidePrice uint
		var minGuidePrice uint
		var maxCostPrice uint
		var minCostPrice uint
		var maxOriginPrice uint
		var minOriginPrice uint
		var minProfitRate float64 = 0
		var maxProfitRate float64 = 0
		for pk, psku := range product.Skus {
			if psku.GuidePrice > 0 {
				product.Skus[pk].ProfitRate = utils.ExecProfitRate(psku.GuidePrice, psku.Price)
			} else {
				product.Skus[pk].ProfitRate = 0
			}

		}
		var productStock int
		for sk, sku := range product.Skus {
			productStock += sku.Stock
			if sku.Price > maxPrice {
				maxPrice = sku.Price
			}
			if minPrice == 0 || sku.Price <= minPrice {
				minPrice = sku.Price
			}

			if sku.ExecPrice > maxCostPrice {
				maxCostPrice = sku.ExecPrice
			}
			if minCostPrice == 0 || sku.ExecPrice <= minCostPrice {
				minCostPrice = sku.ExecPrice
			}

			if sku.OriginPrice > maxOriginPrice {
				maxOriginPrice = sku.OriginPrice
			}
			if minOriginPrice == 0 || sku.OriginPrice <= minOriginPrice {
				minOriginPrice = sku.OriginPrice
			}

			if sku.GuidePrice > maxGuidePrice {
				maxGuidePrice = sku.GuidePrice
			}
			if minGuidePrice == 0 || sku.GuidePrice <= minGuidePrice {
				minGuidePrice = sku.GuidePrice
			}

			if sku.ProfitRate > maxProfitRate {
				maxProfitRate = sku.ProfitRate
			}
			if sk == 0 {
				minProfitRate = sku.ProfitRate
			}
			if sku.ProfitRate <= minProfitRate {
				minProfitRate = sku.ProfitRate
			}
		}

		products[k].MinPrice = minPrice
		products[k].MaxPrice = maxPrice
		products[k].MinCostPrice = minCostPrice
		products[k].MaxCostPrice = maxCostPrice
		products[k].MinOriginPrice = minOriginPrice
		products[k].MaxOriginPrice = maxOriginPrice
		products[k].MinProfitRate = minProfitRate
		products[k].MaxProfitRate = maxProfitRate
		products[k].Stock = uint(productStock)

	}
	//err = db.Count(&total).Error

	return err, products, total
}

type ExportStruct struct {
	Search   request.GetDaHangErpProductSearch `json:"search"`
	Total    int64                             `json:"total"`
	ExportDb *gorm.DB                          `json:"exportDb"`
}

func Export(search request.GetDaHangErpProductSearch) (err error, total int64) {
	var exportStruct ExportStruct
	exportStruct.Search = search
	// 创建db
	exportStruct.ExportDb = source.DB().Model(&dahangErpResponse.DaHangErpProductList{}).Preload("Product").Preload("Product.Supplier").Preload("Product.Brand").Preload("Product.Skus")
	// 如果有条件搜索 下方会自动创建搜索语句
	exportStruct.ExportDb = Search(search, exportStruct.ExportDb)

	err = exportStruct.ExportDb.Count(&exportStruct.Total).Error
	if err != nil {
		err = errors.New("获取导出数量失败" + err.Error())
		return
	}
	if exportStruct.Total == 0 {
		err = errors.New("没有需要导出的数据")
		return
	}
	total = exportStruct.Total
	go ExportRecordAndExportStart(exportStruct)
	return
}
func ExportRecordAndExportStart(exportStruct ExportStruct) {
	var err error
	var ExportData model.DaHangErpProductExportRecord
	ExportData.ProductCount = exportStruct.Total
	ExportData.StatusString = "导出中"
	err = source.DB().Create(&ExportData).Error
	if err != nil {
		log.Log().Error("大昌行商品关系导出数据错误创建导出记录失败", zap.Any("search", exportStruct.Search), zap.Any("err", err))
		return
	}
	err, link := exportStruct.ExportStart()
	if err != nil {
		ExportData.StatusString = "导出失败"
		ExportData.ErrorMsg = err.Error()
	} else {
		ExportData.StatusString = "导出成功"
		ExportData.Link = link
	}
	err = source.DB().Save(ExportData).Error
	if err != nil {
		log.Log().Error("大昌行商品关系导出数据错误保存导出记录失败", zap.Any("ExportData", ExportData), zap.Any("err", err))
	}
}
func (exportStruct ExportStruct) ExportStart() (err error, link string) {

	var products []dahangErpResponse.DaHangErpProductList
	err = exportStruct.ExportDb.Order("da_hang_erp_products.id desc").Find(&products).Error
	if err != nil {
		log.Log().Error("大昌行商品关系导出数据错误", zap.Any("search", exportStruct.Search), zap.Any("err", err))
		err = errors.New("导出数据获取错误" + err.Error())
		return
	}

	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")
	// 设置单元格的值
	f.SetCellValue("Sheet1", "A1", "商品编码")
	f.SetCellValue("Sheet1", "B1", "商品id(关联商品)")
	f.SetCellValue("Sheet1", "C1", "商品名称")

	f.SetCellValue("Sheet1", "D1", "商品名称(关联商品)")
	f.SetCellValue("Sheet1", "E1", "供应商编码")
	f.SetCellValue("Sheet1", "F1", "商品编码(关联商品)")
	f.SetCellValue("Sheet1", "G1", "供应商名称")
	f.SetCellValue("Sheet1", "H1", "供应商名称(关联商品)")
	f.SetCellValue("Sheet1", "I1", "商品品牌")
	f.SetCellValue("Sheet1", "J1", "是否助农")
	f.SetCellValue("Sheet1", "K1", "成本价")
	f.SetCellValue("Sheet1", "L1", "指导价")
	f.SetCellValue("Sheet1", "M1", "税率")
	f.SetCellValue("Sheet1", "N1", "税收编码")
	f.SetCellValue("Sheet1", "O1", "开票名称")
	f.SetCellValue("Sheet1", "P1", "市场价")
	f.SetCellValue("Sheet1", "Q1", "上下架")

	f.SetColWidth("sheet1", "B", "C", 20)
	f.SetColWidth("sheet1", "H", "I", 20)
	f.SetColWidth("sheet1", "M", "Q", 20)

	i := 2
	for _, item := range products {
		if len(item.Product.Skus) > 1 {
			item.Product.ProductBill = item.Product.Skus[0].ProductBill
		}
		var statusName, IsDisplayName string
		if item.IsHelpFarming == "1" {
			statusName = "不是"
		} else {
			statusName = "是"
		}
		if item.Product.ProductModel.IsDisplay == 1 {
			IsDisplayName = "上架"
		} else {
			IsDisplayName = "下架"
		}
		f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), item.ItemCode)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), item.ProductId)
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), item.ItemName)
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), item.Product.Title)
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), item.SuppCode)
		f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), item.Product.Sn)
		f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), item.SuppName)
		f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), item.Product.Supplier.Name)
		f.SetCellValue("Sheet1", "I"+strconv.Itoa(i), item.Product.Brand.Name)
		f.SetCellValue("Sheet1", "J"+strconv.Itoa(i), statusName)
		f.SetCellValue("Sheet1", "K"+strconv.Itoa(i), item.Product.ProductModel.CostPrice)
		f.SetCellValue("Sheet1", "L"+strconv.Itoa(i), item.Product.ProductModel.GuidePrice)
		f.SetCellValue("Sheet1", "M"+strconv.Itoa(i), item.Product.ProductModel.ProductBill.TaxRate)
		f.SetCellValue("Sheet1", "N"+strconv.Itoa(i), item.Product.ProductModel.ProductBill.TaxCode)
		f.SetCellValue("Sheet1", "O"+strconv.Itoa(i), item.Product.ProductModel.ProductBill.TaxProductName)
		f.SetCellValue("Sheet1", "P"+strconv.Itoa(i), item.Product.ProductModel.OriginPrice)
		f.SetCellValue("Sheet1", "Q"+strconv.Itoa(i), IsDisplayName)

		i++
	}
	var fullPath string
	if config.Config().Local.FullPath != "" {
		fullPath = config.Config().Local.FullPath + "/uploads/file"
	} else {
		// 默认部署的目录
		fullPath = "/data/goSupply/uploads/file"
	}
	//fullPath = config.Config().Local.Path
	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)
	// 根据指定路径保存文件
	path := fullPath + "/plugin_export_daErpProduct"
	exist, _ := utils.PathExists(path)
	if !exist {
		// 创建文件夹
		err = os.Mkdir(path, os.ModePerm)
		if err != nil {
			fmt.Printf("mkdir failed![%v]\n", err)
		} else {
			fmt.Printf("mkdir success!\n")
		}
	}
	timeString := time.Now().Format("20060102150405")
	var name = timeString + "商品关系导出.xlsx"
	link = path + "/" + name
	if err = f.SaveAs(link); err != nil {
		return
	}
	link = "uploads/file/plugin_export_daErpProduct/" + name
	return
}

func Search(info request.GetDaHangErpProductSearch, db *gorm.DB) (resDb *gorm.DB) {

	// 如果有条件搜索 下方会自动创建搜索语句
	if info.ItemCode != "" {
		db.Where("da_hang_erp_products.item_code like '%" + info.ItemCode + "%'")
	}
	if info.ItemName != "" {
		db.Where("da_hang_erp_products.item_name like '%" + info.ItemName + "%'")
	}
	if info.ProductId != 0 {
		db.Where("da_hang_erp_products.product_id  = ?", info.ProductId)
	}
	if info.SuppCode != "" {
		db.Where("da_hang_erp_products.supp_code like '%" + info.SuppCode + "%'")
	}
	if info.SuppName != "" {
		db.Where("da_hang_erp_products.supp_name like '%" + info.SuppName + "%'")
	}
	//如果中台商品id 或者中台供应商id有参数则关联商品表
	if info.ProductName != "" || info.SupplierID != 0 || info.IsDisplay != 999 {
		db.Joins("left join products on products.id = da_hang_erp_products.product_id")
	}
	if info.ProductName != "" {
		db.Where("products.title like '%" + info.ProductName + "%'")
	}
	if info.SupplierID != 0 {
		db.Where("products.supplier_id  = ?", info.SupplierID)
	}
	if info.IsDisplay != 999 {
		db.Where("products.is_display  = ?", info.IsDisplay)
	}
	return db
}

// 导出记录列表
func GetProductExportRecordList(info request.ProductExportRecordRequest) (err error, total int64, list interface{}) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.DaHangErpProductExportRecord{})
	var applications []model.DaHangErpProductExportRecord
	// 总后台导出记录sys_user_id为0，供应商为supplier表的user_id

	// 如果有条件搜索 下方会自动创建搜索语句
	if info.StatusString != "" {
		db = db.Where("`status_string` LIKE ?", "%"+info.StatusString+"%")
	}

	err = db.Count(&total).Error
	err = db.Order("created_at desc").Limit(limit).Offset(offset).Find(&applications).Error

	return err, total, applications
}

/*
*
删除导出记录
*/
func DeleteProductExportRecord(id uint) (err error) {
	var data model.DaHangErpProductExportRecord
	err = source.DB().Where("id = ?", id).First(&data).Error
	if err != nil {
		err = errors.New("记录不存在" + err.Error())
		return
	}
	err = source.DB().Where("id =?", data.ID).Delete(&model.DaHangErpProductExportRecord{}).Error
	if err != nil {
		err = errors.New("删除失败" + err.Error())
		return
	}
	if data.Link != "" {
		// 检查文件是否存在
		if _, err = os.Stat(data.Link); err == nil {
			// 文件存在，删除文件
			err = os.Remove(data.Link)
			if err != nil {
				err = nil
			}
		}
	}

	return
}

// 导出记录列表
func GetDaHangErpApplicationList(info request.DaHangErpApplicationListSearch) (err error, list []dahangErpResponse.DaHangErpItem, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&dahangErpResponse.DaHangErpItem{}).Preload("Application")
	// 总后台导出记录sys_user_id为0，供应商为supplier表的user_id

	// 如果有条件搜索 下方会自动创建搜索语句
	if info.EventCode != "" {
		db = db.Where("`event_code` = ?", info.EventCode)
	}
	if info.ApplicationID != 0 || info.ApplicationName != "" {
		db.Joins("left join application  on application.id = da_hang_erp_items.application_id")
		if info.ApplicationID != 0 {
			db.Where("application.id = ?", info.ApplicationID)
		}
		if info.ApplicationName != "" {
			db.Where("application.app_name like ?", "%"+info.ApplicationName+"%")
		}
	}

	err = db.Count(&total).Error
	err = db.Order("created_at desc").Limit(limit).Offset(offset).Find(&list).Error

	return
}
func DaHangErpItemDelete(id uint) (err error) {
	err = source.DB().Where("id = ?", id).Delete(&model.DaHangErpItem{}).Error
	return
}

func DaHangErpItemSave(data model.DaHangErpItem) (err error) {
	if data.ID == 0 {
		err = errors.New("id必传")
		return
	}
	err = source.DB().Model(&model.DaHangErpItem{}).Where("id = ?", data.ID).Updates(&model.DaHangErpItem{
		EventCode: data.EventCode,
		EventName: data.EventName,
	}).Error
	return
}

// 通过活动编码获取关联的客户
func GetDaHangErpCustListByEventCode(info request.DaHangErpCustListSearch) (err error, data []model.DaHangErpCust, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.DaHangErpCust{})
	// 总后台导出记录sys_user_id为0，供应商为supplier表的user_id

	// 如果有条件搜索 下方会自动创建搜索语句
	if info.EventCode != "" {
		db = db.Where("`event_code` = ?", info.EventCode)
	}
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.CustName != "" {
		db = db.Where("`cust_name` like ?", "%"+info.CustName+"%")
	}

	err = db.Count(&total).Error
	err = db.Order("created_at desc").Limit(limit).Offset(offset).Find(&data).Error

	return
}

func CreateDaHangErpOrder(order Order) (err error, PushCount int, msg string) {
	//var daHangErpSupplier model.DaHangErpSupplier
	//if order.SupplierID != 0 {
	//	err = source.DB().Where("supplier_id = ?", order.SupplierID).First(&daHangErpSupplier).Error
	//	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
	//		log.Log().Error("查询供应商记录表非空错误", zap.Any("err", err))
	//		err = errors.New("查询供应商记录表非空错误" + err.Error())
	//		return
	//	}
	//	if daHangErpSupplier.ID == 0 {
	//		log.Log().Error("查询供应商记录表并非大昌行创建的供应商下的订单", zap.Any("order", order.ID))
	//		err = errors.New("查询供应商记录表并非大昌行创建的供应商下的订单")
	//		return
	//	}
	//}
	for _, item := range order.OrderItems {
		var daHangErpProduct model.DaHangErpProduct
		err = source.DB().Where("product_id = ?", item.ProductID).First(&daHangErpProduct).Error
		//商品不是企业购的直接跳过
		if err != nil {
			log.Log().Error("不是企业购的商品", zap.Any("err", err), zap.Any("item", item))
			msg += item.Title + ":不是企业购的商品" + err.Error() + ","
			err = nil
			continue
		}
		//创建子订单记录
		var daHangErpOrderItem model.DaHangErpOrderItem
		err = source.DB().Model(&model.DaHangErpOrderItem{}).Where("order_item_id = ?", item.ID).First(&daHangErpOrderItem).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Log().Error("查询子订单记录表非空错误", zap.Any("err", err))
			msg += "查询子订单记录表非空错误" + err.Error() + ","
			continue
		}
		if daHangErpOrderItem.ID == 0 {
			daHangErpOrderItem.Status = 0
			if daHangErpProduct.ID == 0 {
				log.Log().Error("查询商品记录表并非大昌行创建的商品下的订单", zap.Any("OrderItems", item.ID))
				msg += "查询商品记录表并非大昌行创建的商品下的订单,子订单id:" + strconv.Itoa(int(item.ID)) + ","
				daHangErpOrderItem.Status = -2
				daHangErpOrderItem.ErrorMsg = "查询商品记录表并非大昌行创建的商品下的订单"
			} else {
				PushCount = PushCount + 1
			}
			daHangErpOrderItem.OrderId = item.OrderID
			daHangErpOrderItem.OrderItemId = item.ID
			err = source.DB().Create(&daHangErpOrderItem).Error
			if err != nil {
				log.Log().Error("创建子订单记录失败", zap.Any("err", err))
				msg += "创建子订单记录失败,子订单id:" + strconv.Itoa(int(item.ID)) + err.Error() + ","
				continue
			}
		}

	}

	return
}
