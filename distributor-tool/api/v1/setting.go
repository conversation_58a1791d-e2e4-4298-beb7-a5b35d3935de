package v1

import (
	"distributor-tool/model"
	"distributor-tool/service"
	"github.com/gin-gonic/gin"
	yzResponse "yz-go/response"
)

func FindSetting(c *gin.Context) {
	err, setting := service.GetSetting()
	if err != nil {
		yzResponse.FailWithMessage("获取设置失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"setting": setting}, c)
}

func UpdateSetting(c *gin.Context) {
	var setting model.Setting
	err := c.ShouldBindJSON(&setting)
	if err != nil {
		return
	}
	err = service.SaveSetting(setting)
	if err == nil {
		yzResponse.OkWithMessage("修改成功", c)
	} else {
		yzResponse.FailWithMessage("修改失败", c)
	}
}
