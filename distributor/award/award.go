package award

import (
	model3 "cps/model"
	douyinGroupModel "douyin-group/model"
	model5 "ec-cps-ctrl/model"

	"distributor/award_mq"
	"distributor/model"
	"distributor/service"
	"distributor/user"
	model2 "douyin-cps/model"
	"encoding/json"
	"errors"
	model4 "meituan-distributor/model"
	orderModel "order/model"
	"strconv"
	"yz-go/source"

	"gorm.io/gorm"
)

type GatherSupply struct {
	ID         uint `json:"id" form:"id" gorm:"primarykey"`
	CategoryID uint `json:"category_id" form:"category_id" gorm:"column:category_id;default:0;"`
}

func Handle(orderId uint, canSettle int) (err error, isAward bool) {
	// log.Log().Error("分销监听订单完成后事件Handle()", zap.Any("orderId", orderId))
	// 查询订单
	var order Order
	err, order = GetOrderById(orderId)
	if err != nil {
		// log.Log().Error("分销监听订单完成后事件Handle()查询订单失败", zap.Any("err", err))
		return
	}
	// 通过order.UserID查询会员ParentID 查询上级是否为分销商
	var distributor model.Distributor
	err, distributor = getParentDistributorByUserId(order.UserID)
	if err != nil {
		// log.Log().Error("分销监听订单完成后事件Handle()查询上级分销商失败", zap.Any("err", err))
		// log.Log().Error("分销监听订单完成后事件Handle()查询上级分销商失败-uid", zap.Any("uid", order.UserID))
		return
	}
	if distributor.ID == 0 {
		// log.Log().Error("分销监听订单完成后事件Handle()上级不是分销商")
		err = errors.New("上级不是分销商")
		return
	}
	// 修改分销商的累计推荐订单总数和累计推荐订单总额
	distributor.RecommendOrderCountTotal += 1
	distributor.RecommendOrderAmountTotal += order.Amount
	err = updateDistributor(distributor)
	if err != nil {
		// log.Log().Error("分销监听订单完成后事件Handle()修改分销商失败", zap.Any("err", err))
		return
	}
	// 判断订单类型
	// order.supplier_id 有值 为供应商订单
	if order.SupplierID != 0 {
		// 供应商订单奖励
		err, isAward = supplierAward(distributor, order, canSettle)
		if err != nil {
			// log.Log().Error("分销监听订单完成后事件Handle()供应商订单奖励失败", zap.Any("err", err))
			return
		}
	}
	// order.gather_supply_id 有值 为供应链订单
	if order.GatherSupplyID != 0 {
		// 判断是否为会员升级订单
		var supply GatherSupply
		err = source.DB().Unscoped().Model(&GatherSupply{}).Where("category_id = ?", 12).First(&supply).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		if supply.ID == order.GatherSupplyID {
			// log.Log().Error("分销监听订单完成后事件Handle()会员升级订单奖励")
			// 会员升级订单奖励
			err, isAward = userUpgradeAward(distributor, order, canSettle)
			if err != nil {
				// log.Log().Error("分销监听订单完成后事件Handle()会员升级订单奖励失败", zap.Any("err", err))
				return
			}
		} else {
			// log.Log().Error("分销监听订单完成后事件Handle()供应链订单奖励")
			// 供应链订单奖励
			err, isAward = supplyAward(distributor, order, canSettle)
			if err != nil {
				// log.Log().Error("分销监听订单完成后事件Handle()供应链订单奖励失败", zap.Any("err", err))
				return
			}
		}
	}
	// order.supplier_id = 0 order.gather_supply_id = 0 为自营订单
	if order.SupplierID == 0 && order.GatherSupplyID == 0 {
		// log.Log().Error("分销监听订单完成后事件Handle()自营订单奖励")
		// 自营订单奖励
		err, isAward = shopAward(distributor, order, canSettle)
		if err != nil {
			// log.Log().Error("分销监听订单完成后事件Handle()自营订单奖励失败", zap.Any("err", err))
			return
		}
	}
	return
}

func HandleCps(orderId uint) (err error, isAward bool) {
	// 查询订单
	var order model2.CpsOrder
	err = source.DB().Where("order_id = ?", orderId).First(&order).Error
	if err != nil {
		return
	}
	// 通过order.UserID查询会员ParentID 查询上级是否为分销商
	var distributor model.Distributor
	err, distributor = getParentDistributorByUserId(order.UserID)
	if err != nil {
		return
	}
	if distributor.ID == 0 {
		err = errors.New("上级不是分销商")
		return
	}
	// 修改分销商的累计推荐订单总数和累计推荐订单总额
	distributor.RecommendOrderCountTotal += 1
	distributor.RecommendOrderAmountTotal += uint(order.AwardAmount)
	err = updateDistributor(distributor)
	if err != nil {
		return
	}
	err, isAward = cpsAward(distributor, order)

	return
}

func HandleEcCps(orderId uint) (err error, isAward bool) {
	// 查询订单
	var order model5.EcCpsOrder
	err = source.DB().Where("order_id = ?", orderId).First(&order).Error
	if err != nil {
		return
	}
	// 通过order.UserID查询会员ParentID 查询上级是否为分销商
	var distributor model.Distributor
	err, distributor = getParentDistributorByUserId(order.UserID)
	if err != nil {
		return
	}
	if distributor.ID == 0 {
		err = errors.New("上级不是分销商")
		return
	}
	// 修改分销商的累计推荐订单总数和累计推荐订单总额
	distributor.RecommendOrderCountTotal += 1
	distributor.RecommendOrderAmountTotal += uint(order.AwardAmount)
	err = updateDistributor(distributor)
	if err != nil {
		return
	}
	err, isAward = ecCpsAward(distributor, order)

	return
}

func HandleDouYinGroup(orderId uint) (err error, isAward bool) {
	_, setting := service.GetSetting()
	if setting.Values.PluginSwitch == 0 {
		// log.Log().Error("抖音团购,因分销关闭，不进行分销", zap.Any("orderId", orderId))
		return
	}
	// 查询订单
	var order douyinGroupModel.DouyinGroupOrderModel
	err = source.DB().Where("id = ?", orderId).First(&order).Error
	if err != nil {
		return
	}
	// 通过order.UserID查询会员ParentID 查询上级是否为分销商
	var distributor model.Distributor
	err, distributor = getParentDistributorByUserId(order.UserID)
	if err != nil {
		return
	}
	if distributor.ID == 0 {
		err = errors.New("上级不是分销商")
		return
	}
	// 修改分销商的累计推荐订单总数和累计推荐订单总额
	distributor.RecommendOrderCountTotal += 1
	distributor.RecommendOrderAmountTotal += uint(order.SettleAmount)
	err = updateDistributor(distributor)
	if err != nil {
		return
	}
	err, isAward = douyinGroupAward(distributor, order)

	return
}

func HandleJhCps(orderId uint) (err error, isAward bool) {
	// 查询订单
	var order model3.JhCpsOrder
	err = source.DB().Where("id = ?", orderId).First(&order).Error
	if err != nil {
		return
	}
	// 通过order.UserID查询会员ParentID 查询上级是否为分销商
	var distributor model.Distributor
	err, distributor = getParentDistributorByUserId(order.UserID)
	if err != nil {
		return
	}
	if distributor.ID == 0 {
		err = errors.New("上级不是分销商")
		return
	}
	// 修改分销商的累计推荐订单总数和累计推荐订单总额
	distributor.RecommendOrderCountTotal += 1
	distributor.RecommendOrderAmountTotal += uint(order.CommissionPrice)
	err = updateDistributor(distributor)
	if err != nil {
		return
	}
	err, isAward = jhcpsAward(distributor, order)

	return
}

func HandleMeituanDistributor(orderId uint) (err error, isAward bool) {
	// 查询订单
	var order model4.MeituanDistributorOrder
	err = source.DB().Where("unique_item_id = ?", orderId).First(&order).Error
	if err != nil {
		return
	}
	// 通过order.UserID查询会员ParentID 查询上级是否为分销商
	var distributor model.Distributor
	err, distributor = getParentDistributorByUserId(order.UserID)
	if err != nil {
		return
	}
	if distributor.ID == 0 {
		err = errors.New("上级不是分销商")
		return
	}
	// 修改分销商的累计推荐订单总数和累计推荐订单总额
	distributor.RecommendOrderCountTotal += 1
	var commissionPrice float64
	commissionPrice, err = strconv.ParseFloat(order.BalanceAmount, 64)
	if err != nil {
		err = errors.New("价格转换失败")
		return
	}
	distributor.RecommendOrderAmountTotal += uint(commissionPrice * 100)
	err = updateDistributor(distributor)
	if err != nil {
		return
	}
	err, isAward = meituanDistributorAward(distributor, order)

	return
}
func douyinGroupAward(distributor model.Distributor, order douyinGroupModel.DouyinGroupOrderModel) (err error, isAward bool) {
	// 分销基础设置
	var setting model.Setting
	err, setting = service.GetSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}

	// 分销商等级->cps订单奖励设置
	cpsSettleInfo := distributor.LevelInfo.DouYinGroupSettleInfo
	var awards []model.DistributorAward
	// 订单实付金额

	// 分成基数
	settleAmount := order.SettleAmount

	if settleAmount <= 0 {
		return
	}
	// 比例
	if cpsSettleInfo.AmountRatio <= 0 {
		return
	}
	// 分成金额
	awardAmount := settleAmount * int64(cpsSettleInfo.AmountRatio) / 10000
	if awardAmount <= 0 {
		return
	}
	var award model.DistributorAward
	award.Uid = distributor.Uid
	award.ChildUid = order.UserID
	award.LevelID = distributor.LevelID
	award.LevelName = distributor.LevelInfo.Name
	award.OrderID = order.ID
	award.OrderType = model.OrderTypeDouYinGroup
	award.OrderSN = order.OrderSn
	award.OrderAmount = uint(order.PayAmount)
	award.SettleAmount = uint(settleAmount)
	award.SettleType = model.SettleTypeOrder
	award.Ratio = cpsSettleInfo.AmountRatio
	award.Amount = uint(awardAmount)
	award.Status = model.Wait
	award.SettleDays = setting.Values.SettleDays
	awards = append(awards, award)

	if len(awards) > 0 {
		// 产生奖励
		err = source.DB().Create(&awards).Error
		if err != nil {
			return
		}
		if setting.Values.Layers == 2 {
			var o douyinGroupModel.DouyinGroupOrderModel
			err = source.DB().Where("id = ?", order.ID).First(&o).Error
			if err != nil {
				// log.Log().Error("抖音团购间推奖励查询订单失败", zap.Any("order_id", order.ID))
				return
			}
			err = IndirectAwards(distributor, awards, Order{
				Amount: uint(o.SettleAmount),
			})
		} else {
			err = sendMq(order.ID, awards)
		}
		isAward = true
	}
	return
}

// 支付金额 order.amount 成本 order.cost_amount 运费 order.freight  采购技术服务费 order.technical_services_fee 协议价 supply_amount
func cpsAward(distributor model.Distributor, order model2.CpsOrder) (err error, isAward bool) {
	// 分销基础设置
	var setting model.Setting
	err, setting = service.GetSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}

	// 分销商等级->cps订单奖励设置
	cpsSettleInfo := distributor.LevelInfo.CpsSettleInfo
	var awards []model.DistributorAward
	// 订单实付金额

	// 分成基数
	settleAmount := int64(order.EstimatedCommission - order.EstimatedTechServiceFee)

	if settleAmount <= 0 {
		return
	}
	// 比例
	if cpsSettleInfo.AmountRatio <= 0 {
		return
	}
	// 分成金额
	awardAmount := settleAmount * int64(cpsSettleInfo.AmountRatio) / 10000
	if awardAmount <= 0 {
		return
	}
	var award model.DistributorAward
	award.Uid = distributor.Uid
	award.ChildUid = order.UserID
	award.LevelID = distributor.LevelID
	award.LevelName = distributor.LevelInfo.Name
	award.OrderID = order.ID
	award.OrderType = model.OrderTypeCps
	award.OrderSN = order.OrderSN
	award.OrderAmount = uint(order.TotalPayAmount)
	award.SettleAmount = uint(settleAmount)
	award.SettleType = model.SettleTypeOrder
	award.Ratio = cpsSettleInfo.AmountRatio
	award.Amount = uint(awardAmount)
	award.Status = model.Wait
	award.SettleDays = setting.Values.SettleDays
	awards = append(awards, award)

	if len(awards) > 0 {
		// 产生奖励
		err = source.DB().Create(&awards).Error
		if err != nil {
			return
		}
		if setting.Values.Layers == 2 {
			var o Order
			err = source.DB().Where("id = ?", order.ID).First(&o).Error
			if err != nil {
				return
			}
			err = IndirectAwards(distributor, awards, o)
		} else {
			err = sendMq(order.ID, awards)
		}
		isAward = true
	}
	return
}

func ecCpsAward(distributor model.Distributor, order model5.EcCpsOrder) (err error, isAward bool) {
	// 分销基础设置
	var setting model.Setting
	err, setting = service.GetSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}

	// 分销商等级->cps订单奖励设置
	cpsSettleInfo := distributor.LevelInfo.CpsSettleInfo
	var awards []model.DistributorAward
	// 订单实付金额

	// 分成基数
	settleAmount := int64(order.EstimatedCommission - order.EstimatedTechServiceFee)

	if settleAmount <= 0 {
		return
	}
	// 比例
	if cpsSettleInfo.AmountRatio <= 0 {
		return
	}
	// 分成金额
	awardAmount := settleAmount * int64(cpsSettleInfo.AmountRatio) / 10000
	if awardAmount <= 0 {
		return
	}
	var award model.DistributorAward
	award.Uid = distributor.Uid
	award.ChildUid = order.UserID
	award.LevelID = distributor.LevelID
	award.LevelName = distributor.LevelInfo.Name
	award.OrderID = order.ID
	award.OrderType = model.OrderTypeCps
	award.OrderSN = order.OrderSN
	award.OrderAmount = uint(order.TotalPayAmount)
	award.SettleAmount = uint(settleAmount)
	award.SettleType = model.SettleTypeOrder
	award.Ratio = cpsSettleInfo.AmountRatio
	award.Amount = uint(awardAmount)
	award.Status = model.Wait
	award.SettleDays = setting.Values.SettleDays
	awards = append(awards, award)

	if len(awards) > 0 {
		// 产生奖励
		err = source.DB().Create(&awards).Error
		if err != nil {
			return
		}
		if setting.Values.Layers == 2 {
			var o Order
			err = source.DB().Where("id = ?", order.ID).First(&o).Error
			if err != nil {
				return
			}
			err = IndirectAwards(distributor, awards, o)
		} else {
			err = sendMq(order.ID, awards)
		}
		isAward = true
	}
	return
}

// 支付金额 order.amount 成本 order.cost_amount 运费 order.freight  采购技术服务费 order.technical_services_fee 协议价 supply_amount
func jhcpsAward(distributor model.Distributor, order model3.JhCpsOrder) (err error, isAward bool) {
	// 分销基础设置
	var setting model.Setting
	err, setting = service.GetSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}

	// 分销商等级->cps订单奖励设置
	cpsSettleInfo := distributor.LevelInfo.JhCpsSettleInfo
	var awards []model.DistributorAward
	// 订单实付金额

	// 比例
	if cpsSettleInfo.AmountRatio <= 0 {
		return
	}
	// 分成金额
	awardAmount := int64(order.CommissionPrice) * int64(cpsSettleInfo.AmountRatio) / 10000
	if awardAmount <= 0 {
		return
	}
	var award model.DistributorAward
	award.Uid = distributor.Uid
	award.ChildUid = order.UserID
	award.LevelID = distributor.LevelID
	award.LevelName = distributor.LevelInfo.Name
	award.OrderID = order.ID
	award.OrderType = model.OrderTypeJhCps
	award.OrderSN = order.OrderSN
	award.OrderAmount = uint(order.Price)
	award.SettleAmount = uint(order.CommissionPrice)
	award.SettleType = model.SettleTypeOrder
	award.Ratio = cpsSettleInfo.AmountRatio
	award.Amount = uint(awardAmount)
	award.Status = model.Wait
	award.SettleDays = setting.Values.SettleDays
	awards = append(awards, award)

	if len(awards) > 0 {
		// 产生奖励
		err = source.DB().Create(&awards).Error
		if err != nil {
			return
		}
		if setting.Values.Layers == 2 {
			var o Order
			err = source.DB().Where("id = ?", order.ID).First(&o).Error
			if err != nil {
				return
			}
			err = IndirectAwards(distributor, awards, o)
		} else {
			err = sendMq(order.ID, awards)
		}
		isAward = true
	}
	return

}

func meituanDistributorAward(distributor model.Distributor, order model4.MeituanDistributorOrder) (err error, isAward bool) {
	// 分销基础设置
	var setting model.Setting
	err, setting = service.GetSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}

	// 分销商等级->cps订单奖励设置
	cpsSettleInfo := distributor.LevelInfo.MeituanDisSettleInfo
	var awards []model.DistributorAward
	// 订单实付金额

	// 比例
	if cpsSettleInfo.AmountRatio <= 0 {
		return
	}
	// 分成金额
	var commissionPrice float64
	commissionPrice, err = strconv.ParseFloat(order.BalanceAmount, 64)
	if err != nil {
		err = errors.New("价格转换失败")
		return
	}
	awardAmount := int64(commissionPrice*100) * int64(cpsSettleInfo.AmountRatio) / 10000
	if awardAmount <= 0 {
		return
	}
	var award model.DistributorAward
	award.Uid = distributor.Uid
	award.ChildUid = order.UserID
	award.LevelID = distributor.LevelID
	award.LevelName = distributor.LevelInfo.Name
	award.OrderID = order.ID
	award.OrderType = model.OrderTypeMeituanDistributor
	award.OrderSN = uint(order.UniqueItemId)
	var orderPrice float64
	orderPrice, err = strconv.ParseFloat(order.ActualOrderAmount, 64)
	if err != nil {
		err = errors.New("价格转换失败")
		return
	}
	award.OrderAmount = uint(orderPrice * 100)
	award.SettleAmount = uint(commissionPrice * 100)
	award.SettleType = model.SettleTypeOrder
	award.Ratio = cpsSettleInfo.AmountRatio
	award.Amount = uint(awardAmount)
	award.Status = model.Wait
	award.SettleDays = setting.Values.SettleDays
	awards = append(awards, award)

	if len(awards) > 0 {
		// 产生奖励
		err = source.DB().Create(&awards).Error
		if err != nil {
			return
		}
		if setting.Values.Layers == 2 {
			var o Order
			err = source.DB().Where("id = ?", order.ID).First(&o).Error
			if err != nil {
				return
			}
			err = IndirectAwards(distributor, awards, o)
		} else {
			err = sendMq(order.ID, awards)
		}
		isAward = true
	}
	return

}

func shopAward(distributor model.Distributor, order Order, canSettle int) (err error, isAward bool) {
	// 分销基础设置
	var setting model.Setting
	err, setting = service.GetSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		// log.Log().Error("分销监听订单完成后事件shopAward()查询setting失败", zap.Any("err", err))
		return
	}
	// 基础设置:商品分销是否开启
	if setting.Values.ProductSwitch != 1 {
		// log.Log().Error("分销监听订单完成后事件shopAward()商品分销未开启")
		return
	}
	// 分销商等级->自营订单奖励设置
	shopSettleInfo := distributor.LevelInfo.ShopSettleInfo
	var awards []model.DistributorAward
	// 订单实付金额
	if shopSettleInfo.AmountSwitch == 1 {
		// 分成基数
		settleAmount := int64(order.Amount)
		// 减成本
		if shopSettleInfo.CostSwitch == 1 {
			settleAmount -= int64(order.CostAmount)
		}
		// 减运费
		if shopSettleInfo.FreightSwitch == 1 {
			settleAmount -= int64(order.Freight)
		}
		if settleAmount <= 0 {
			return
		}
		// 比例
		if shopSettleInfo.FormulaRatio <= 0 {
			return
		}
		// 分成金额
		awardAmount := settleAmount * int64(shopSettleInfo.FormulaRatio) / 10000
		if awardAmount <= 0 {
			return
		}
		var award model.DistributorAward
		award.Uid = distributor.Uid
		award.ChildUid = order.UserID
		award.LevelID = distributor.LevelID
		award.LevelName = distributor.LevelInfo.Name
		award.OrderID = order.ID
		award.OrderType = model.OrderTypeShop
		award.OrderSN = order.OrderSN
		award.OrderAmount = order.Amount
		award.SettleAmount = uint(settleAmount)
		award.SettleType = model.SettleTypeOrder
		award.Ratio = shopSettleInfo.FormulaRatio
		award.Amount = uint(awardAmount)
		award.Status = model.Wait
		award.SettleDays = setting.Values.SettleDays
		award.CanSettle = canSettle
		awards = append(awards, award)
	}
	// 采购技术服务费
	if shopSettleInfo.BuyServiceSwitch == 1 {
		// 分成基数
		settleAmount := order.TechnicalServicesFee
		if settleAmount > 0 && shopSettleInfo.BuyServiceRatio > 0 {
			// 分成金额
			awardAmount := settleAmount * uint(shopSettleInfo.BuyServiceRatio) / 10000
			if awardAmount > 0 {
				var award model.DistributorAward
				award.Uid = distributor.Uid
				award.ChildUid = order.UserID
				award.LevelID = distributor.LevelID
				award.LevelName = distributor.LevelInfo.Name
				award.OrderID = order.ID
				award.OrderType = model.OrderTypeShop
				award.OrderSN = order.OrderSN
				award.OrderAmount = order.Amount
				award.SettleAmount = settleAmount
				award.SettleType = model.SettleTypeService
				award.Ratio = shopSettleInfo.BuyServiceRatio
				award.Amount = awardAmount
				award.Status = model.Wait
				award.SettleDays = setting.Values.SettleDays
				award.CanSettle = canSettle
				awards = append(awards, award)
			}
		}
	}
	if len(awards) > 0 {
		// log.Log().Error("分销监听订单完成后事件shopAward()产生奖励", zap.Any("awards", awards))
		// 产生奖励
		err = source.DB().Create(&awards).Error
		if err != nil {
			return
		}
		if setting.Values.Layers == 2 {
			err = IndirectAwards(distributor, awards, order)
		} else {
			err = sendMq(order.ID, awards)
		}
		isAward = true
	}
	return
}

// 供应商订单奖励
func supplierAward(distributor model.Distributor, order Order, canSettle int) (err error, isAward bool) {
	// 分销基础设置
	var setting model.Setting
	err, setting = service.GetSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	// 基础设置:商品分销是否开启
	if setting.Values.ProductSwitch != 1 {
		return
	}
	// 分销商等级->供应商订单奖励设置
	supplierSettleInfo := distributor.LevelInfo.SupplierSettleInfo
	var awards []model.DistributorAward
	// 订单实付金额
	if supplierSettleInfo.AmountSwitch == 1 {
		// 分成基数
		settleAmount := int64(order.Amount)
		// 减成本
		if supplierSettleInfo.CostSwitch == 1 {
			settleAmount -= int64(order.CostAmount)
		}
		// 减运费
		if supplierSettleInfo.FreightSwitch == 1 {
			settleAmount -= int64(order.Freight)
		}
		if settleAmount <= 0 {
			return
		}
		// 比例
		if supplierSettleInfo.FormulaRatio <= 0 {
			return
		}
		// 分成金额
		awardAmount := settleAmount * int64(supplierSettleInfo.FormulaRatio) / 10000
		if awardAmount <= 0 {
			return
		}
		var award model.DistributorAward
		award.Uid = distributor.Uid
		award.ChildUid = order.UserID
		award.LevelID = distributor.LevelID
		award.LevelName = distributor.LevelInfo.Name
		award.OrderID = order.ID
		award.OrderType = model.OrderTypeSupplier
		award.OrderSN = order.OrderSN
		award.OrderAmount = order.Amount
		award.SettleAmount = uint(settleAmount)
		award.SettleType = model.SettleTypeOrder
		award.Ratio = supplierSettleInfo.FormulaRatio
		award.Amount = uint(awardAmount)
		award.Status = model.Wait
		award.SettleDays = setting.Values.SettleDays
		award.CanSettle = canSettle
		awards = append(awards, award)
	}
	// 供应商扣点
	if supplierSettleInfo.SupplierRebateSwitch == 1 {
		// 分成基数
		var settleAmount int64
		err, settleAmount = getSupplierSettleAmount(order)
		if err != nil {
			return
		}
		if settleAmount > 0 && supplierSettleInfo.SupplierRebateRatio > 0 {
			awardAmount := settleAmount * int64(supplierSettleInfo.SupplierRebateRatio) / 10000
			if awardAmount > 0 {
				var award model.DistributorAward
				award.Uid = distributor.Uid
				award.ChildUid = order.UserID
				award.LevelID = distributor.LevelID
				award.LevelName = distributor.LevelInfo.Name
				award.OrderID = order.ID
				award.OrderType = model.OrderTypeSupplier
				award.OrderSN = order.OrderSN
				award.OrderAmount = order.Amount
				award.SettleAmount = uint(settleAmount)
				award.SettleType = model.SettleTypeSupplier
				award.Ratio = supplierSettleInfo.SupplierRebateRatio
				award.Amount = uint(awardAmount)
				award.Status = model.Wait
				award.SettleDays = setting.Values.SettleDays
				award.CanSettle = canSettle
				awards = append(awards, award)
			}
		}
	}
	// 采购技术服务费
	if supplierSettleInfo.BuyServiceSwitch == 1 {
		// 分成基数
		settleAmount := order.TechnicalServicesFee
		if settleAmount > 0 && supplierSettleInfo.BuyServiceRatio > 0 {
			awardAmount := settleAmount * uint(supplierSettleInfo.BuyServiceRatio) / 10000
			if awardAmount > 0 {
				var award model.DistributorAward
				award.Uid = distributor.Uid
				award.ChildUid = order.UserID
				award.LevelID = distributor.LevelID
				award.LevelName = distributor.LevelInfo.Name
				award.OrderID = order.ID
				award.OrderType = model.OrderTypeSupplier
				award.OrderSN = order.OrderSN
				award.OrderAmount = order.Amount
				award.SettleAmount = settleAmount
				award.SettleType = model.SettleTypeService
				award.Ratio = supplierSettleInfo.BuyServiceRatio
				award.Amount = awardAmount
				award.Status = model.Wait
				award.SettleDays = setting.Values.SettleDays
				award.CanSettle = canSettle
				awards = append(awards, award)
			}
		}
	}
	if len(awards) > 0 {
		// 产生奖励
		err = source.DB().Create(&awards).Error
		if err != nil {
			return
		}
		if setting.Values.Layers == 2 {
			err = IndirectAwards(distributor, awards, order)
		} else {
			err = sendMq(order.ID, awards)
		}
		isAward = true
	}
	return
}

// 会员升级订单奖励
func userUpgradeAward(distributor model.Distributor, order Order, canSettle int) (err error, isAward bool) {
	// log.Log().Error("分销监听订单完成后事件userUpgradeAward()")
	// 分销基础设置
	var setting model.Setting
	err, setting = service.GetSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		// log.Log().Error("分销监听订单完成后事件userUpgradeAward()查询setting失败", zap.Any("err", err))
		return
	}
	// 基础设置:商品分销是否开启
	if setting.Values.ProductSwitch != 1 {
		// log.Log().Error("分销监听订单完成后事件userUpgradeAward()商品分销未开启")
		return
	}
	var supplySettleInfo model.MeituanDisSettleInfo
	// 分销商等级->会员升级订单奖励设置
	supplySettleInfo = distributor.LevelInfo.UserUpgradeSettleInfo
	// 分成基数
	settleAmount := int64(order.Amount)
	// 分成金额
	awardAmount := settleAmount * int64(supplySettleInfo.AmountRatio) / 10000
	// log.Log().Error("分销监听订单完成后事件userUpgradeAward()", zap.Any("settleAmount", settleAmount))
	// log.Log().Error("分销监听订单完成后事件userUpgradeAward()", zap.Any("awardAmount", awardAmount))
	// log.Log().Error("分销监听订单完成后事件userUpgradeAward()", zap.Any("AmountRatio", supplySettleInfo.AmountRatio))
	if awardAmount <= 0 {
		// log.Log().Error("分销监听订单完成后事件userUpgradeAward()分成金额小于等于0")
		return
	}
	var awards []model.DistributorAward
	// 产生奖励
	var award model.DistributorAward
	award.Uid = distributor.Uid
	award.ChildUid = order.UserID
	award.LevelID = distributor.LevelID
	award.LevelName = distributor.LevelInfo.Name
	award.OrderID = order.ID
	award.OrderType = model.OrderTypeUserUpgrade
	award.OrderSN = order.OrderSN
	award.OrderAmount = order.Amount
	award.SettleAmount = uint(settleAmount)
	award.SettleType = model.SettleTypeOrder
	award.Ratio = supplySettleInfo.AmountRatio
	award.Amount = uint(awardAmount)
	award.Status = model.Wait
	award.SettleDays = setting.Values.SettleDays
	award.CanSettle = canSettle
	awards = append(awards, award)
	if len(awards) > 0 {
		// log.Log().Error("分销监听订单完成后事件userUpgradeAward()产生奖励", zap.Any("awards", awards))
		// 产生奖励
		err = source.DB().Create(&awards).Error
		if err != nil {
			// log.Log().Error("分销监听订单完成后事件userUpgradeAward()产生奖励失败", zap.Any("err", err))
			return
		}
		if setting.Values.Layers == 2 {
			err = IndirectAwards(distributor, awards, order)
		} else {
			err = sendMq(order.ID, awards)
		}
		isAward = true
	}

	return
}

// 供应链订单奖励
func supplyAward(distributor model.Distributor, order Order, canSettle int) (err error, isAward bool) {
	// 分销基础设置
	var setting model.Setting
	err, setting = service.GetSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	// 基础设置:商品分销是否开启
	if setting.Values.ProductSwitch != 1 {
		return
	}
	// 数字权益订单
	var supply GatherSupply
	err = source.DB().Unscoped().Where("category_id = ?", 98).First(&supply).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	var supplySettleInfo model.SupplySettleInfo
	if supply.ID == order.GatherSupplyID {
		// 分销商等级->数字权益订单奖励设置
		supplySettleInfo = distributor.LevelInfo.EquitySettleInfo
	} else {
		// 分销商等级->供应链订单奖励设置
		supplySettleInfo = distributor.LevelInfo.SupplySettleInfo
	}

	var awards []model.DistributorAward
	// 订单实付金额
	if supplySettleInfo.AmountSwitch == 1 {
		// 分成基数
		settleAmount := int64(order.Amount)
		// 减协议价
		if supplySettleInfo.DealSwitch == 1 {
			settleAmount -= int64(order.SupplyAmount)
		}
		// 减运费
		if supplySettleInfo.FreightSwitch == 1 {
			settleAmount -= int64(order.Freight)
		}
		if settleAmount <= 0 {
			return
		}
		// 比例
		if supplySettleInfo.FormulaRatio <= 0 {
			return
		}
		// 分成金额
		awardAmount := settleAmount * int64(supplySettleInfo.FormulaRatio) / 10000
		if awardAmount <= 0 {
			return
		}
		var award model.DistributorAward
		award.Uid = distributor.Uid
		award.ChildUid = order.UserID
		award.LevelID = distributor.LevelID
		award.LevelName = distributor.LevelInfo.Name
		award.OrderID = order.ID
		award.OrderType = model.OrderTypeSupply
		award.OrderSN = order.OrderSN
		award.OrderAmount = order.Amount
		award.SettleAmount = uint(settleAmount)
		award.SettleType = model.SettleTypeOrder
		award.Ratio = supplySettleInfo.FormulaRatio
		award.Amount = uint(awardAmount)
		award.Status = model.Wait
		award.SettleDays = setting.Values.SettleDays
		award.CanSettle = canSettle
		awards = append(awards, award)
	}
	// 采购技术服务费
	if supplySettleInfo.BuyServiceSwitch == 1 {
		// 分成基数
		settleAmount := order.TechnicalServicesFee
		if settleAmount > 0 && supplySettleInfo.BuyServiceRatio > 0 {
			awardAmount := settleAmount * uint(supplySettleInfo.BuyServiceRatio) / 10000
			if awardAmount > 0 {
				var award model.DistributorAward
				award.Uid = distributor.Uid
				award.ChildUid = order.UserID
				award.LevelID = distributor.LevelID
				award.LevelName = distributor.LevelInfo.Name
				award.OrderID = order.ID
				award.OrderType = model.OrderTypeSupply
				award.OrderSN = order.OrderSN
				award.OrderAmount = order.Amount
				award.SettleAmount = settleAmount
				award.SettleType = model.SettleTypeService
				award.Ratio = supplySettleInfo.BuyServiceRatio
				award.Amount = awardAmount
				award.Status = model.Wait
				award.SettleDays = setting.Values.SettleDays
				award.CanSettle = canSettle
				awards = append(awards, award)
			}
		}
	}
	if len(awards) > 0 {
		// 产生奖励
		err = source.DB().Create(&awards).Error
		if err != nil {
			return
		}
		if setting.Values.Layers == 2 {
			var o Order
			o.ID = order.ID
			err = IndirectAwards(distributor, awards, o)
		} else {
			err = sendMq(order.ID, awards)
		}
		isAward = true
	}
	return
}

// 通过会员id获取上级分销商
func getParentDistributorByUserId(userId uint) (err error, distributor model.Distributor) {
	var parentId uint
	err, parentId = user.GetParentIdByUserId(userId)
	if err != nil {
		return
	}
	err, distributor = service.GetDistributorByUserId(parentId)
	if distributor.Blacklist == 1 {
		return errors.New("上级分销商已被拉黑"), model.Distributor{}
	}
	return
}

// 修改分销商
func updateDistributor(distributor model.Distributor) (err error) {
	err = source.DB().Omit("LevelInfo").Updates(&distributor).Error
	return
}

type Order struct {
	source.Model
	CreatedAt            *source.LocalTime      `json:"created_at" gorm:"index;"`
	UpdatedAt            *source.LocalTime      `json:"updated_at" gorm:"index;"`
	OrderSN              uint                   `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:编号;"`                                                     // 编号
	ThirdOrderSN         string                 `json:"third_order_sn" form:"third_order_sn" gorm:"column:third_order_sn;comment:编号;"`                                   // 编号
	Key                  string                 `json:"key" form:"key" gorm:"column:key;comment:标识;type:varchar(255);size:255;"`                                         // 标识
	Title                string                 `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(255);size:255;"`                                   // 标题
	Status               orderModel.OrderStatus `json:"status" form:"status" gorm:"column:status;default:0;comment:状态;type:smallint;size:3;"`                            // 订单状态
	Amount               uint                   `json:"amount" form:"amount" gorm:"column:amount;comment:订单总金额(分);"`                                                 // 订单总金额
	ItemAmount           uint                   `json:"item_amount" form:"item_amount" gorm:"column:item_amount;comment:商品市场价(分);"`                                  // 商品市场价
	SupplyAmount         uint                   `json:"supply_amount" form:"supply_amount" gorm:"column:supply_amount;comment:供货金额(分);"`                              // 供货金额
	CostAmount           uint                   `json:"cost_amount" form:"cost_amount" gorm:"column:cost_amount;comment:成本金额(分);"`                                    // 成本金额
	Freight              uint                   `json:"freight" form:"freight" gorm:"column:freight;comment:运费(分);"`                                                    // 运费(单位:分)
	ServiceFee           uint                   `json:"service_fee" form:"service_fee" gorm:"column:service_fee;comment:服务费(分);"`                                      // 服务费
	GoodsCount           uint                   `json:"goods_count" form:"goods_count" gorm:"column:goods_count;default:0;comment:商品总数;"`                              // 商品总数
	TechnicalServicesFee uint                   `json:"technical_services_fee" form:"technical_services_fee" gorm:"column:technical_services_fee;comment:技术服务费(分);"` // 技术服务费
	UserID               uint                   `json:"user_id" form:"user_id" gorm:"column:user_id;comment:用户id;"`
	SupplierID           uint                   `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商id;"`
	GatherSupplyID       uint                   `json:"gather_supply_id" form:"gather_supply_id" gorm:"column:gather_supply_id;comment:供应链id;index;"`
	ApplicationID        uint                   `json:"application_id" form:"application_id" gorm:"column:application_id;comment:应用id;index;"`
}

// GetOrderById 通过id获取订单
func GetOrderById(id uint) (err error, order Order) {
	err = source.DB().Where("id = ?", id).First(&order).Error
	return
}

type SupplierSettlement struct {
	source.Model
	OrderID        uint `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单id;"`
	SettlementType int  `json:"settlement_type"  gorm:"default:0"` //   0订单   1  供货价
	DeductionRatio int  `json:"deduction_ratio"  gorm:"default:0"` //  供应商扣点
}

// 获取供应商订单结算金额
func getSupplierSettleAmount(order Order) (err error, settleAmount int64) {
	var supplierOrder SupplierSettlement
	err, supplierOrder = getSupplierOrder(order.ID)
	if err != nil {
		return
	}
	// 结算方式 0:订单金额 1:供货价 + 运费
	if supplierOrder.SettlementType == 2 {
		// (110 - 5) * 500
		settleAmount = (int64(order.Amount) - int64(order.TechnicalServicesFee)) * int64(supplierOrder.DeductionRatio) / 10000
	} else {
		// (110 + 0 - 5) * 500
		settleAmount = (int64(order.SupplyAmount) + int64(order.Freight) - int64(order.TechnicalServicesFee)) * int64(supplierOrder.DeductionRatio) / 10000
	}
	return
}

// 通过订单id获取供应商订单
func getSupplierOrder(orderId uint) (err error, supplierOrder SupplierSettlement) {
	err = source.DB().Where("order_id = ?", orderId).First(&supplierOrder).Error
	return
}

/*func IndirectAward(distributor model.Distributor, awards []model.DistributorAward, gatherSupplyID uint) (err error) {
	if len(awards) == 0 {
		return
	}
	var parentDistributor model.Distributor
	err, parentDistributor = getParentDistributorByUserId(distributor.Uid)
	if err != nil || parentDistributor.ID == 0 {
		return
	}
	var parentAwards []model.DistributorAward
	for _, award := range awards {
		ratio := 0
		// 计算极差比例
		switch award.OrderType {
		case model.OrderTypeShop:
			shopSettleInfo := parentDistributor.LevelInfo.ShopSettleInfo
			if award.SettleType == model.SettleTypeOrder {
				if shopSettleInfo.FormulaRatio > 0 && shopSettleInfo.FormulaRatio-award.Ratio > 0 {
					ratio = shopSettleInfo.FormulaRatio - award.Ratio
				}
			} else {
				if shopSettleInfo.BuyServiceRatio > 0 && shopSettleInfo.BuyServiceRatio-award.Ratio > 0 {
					ratio = shopSettleInfo.BuyServiceRatio - award.Ratio
				}
			}
		case model.OrderTypeSupplier:
			supplierSettleInfo := parentDistributor.LevelInfo.SupplierSettleInfo
			if award.SettleType == model.SettleTypeOrder {
				if supplierSettleInfo.FormulaRatio > 0 && supplierSettleInfo.FormulaRatio-award.Ratio > 0 {
					ratio = supplierSettleInfo.FormulaRatio - award.Ratio
				}
			} else if award.SettleType == model.SettleTypeSupplier {
				if supplierSettleInfo.SupplierRebateRatio > 0 && supplierSettleInfo.SupplierRebateRatio-award.Ratio > 0 {
					ratio = supplierSettleInfo.SupplierRebateRatio - award.Ratio
				}
			} else {
				if supplierSettleInfo.BuyServiceRatio > 0 && supplierSettleInfo.BuyServiceRatio-award.Ratio > 0 {
					ratio = supplierSettleInfo.BuyServiceRatio - award.Ratio
				}
			}
		case model.OrderTypeSupply:
			// 数字权益订单
			var supply GatherSupply
			err = source.DB().Unscoped().Where("category_id = ?", 98).First(&supply).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				continue
			}
			var supplySettleInfo model.SupplySettleInfo
			if supply.ID == gatherSupplyID {
				// 分销商等级->数字权益订单奖励设置
				supplySettleInfo = parentDistributor.LevelInfo.EquitySettleInfo
			} else {
				// 分销商等级->供应链订单奖励设置
				supplySettleInfo = parentDistributor.LevelInfo.SupplySettleInfo
			}
			if award.SettleType == model.SettleTypeOrder {
				if supplySettleInfo.FormulaRatio > 0 && supplySettleInfo.FormulaRatio-award.Ratio > 0 {
					ratio = supplySettleInfo.FormulaRatio - award.Ratio
				}
			} else {
				if supplySettleInfo.BuyServiceRatio > 0 && supplySettleInfo.BuyServiceRatio-award.Ratio > 0 {
					ratio = supplySettleInfo.BuyServiceRatio - award.Ratio
				}
			}
		case model.OrderTypeCps:
			cpsSettleInfo := parentDistributor.LevelInfo.CpsSettleInfo
			if cpsSettleInfo.AmountRatio > 0 && cpsSettleInfo.AmountRatio-award.Ratio > 0 {
				ratio = cpsSettleInfo.AmountRatio - award.Ratio
			}
		case model.OrderTypeJhCps:
			cpsSettleInfo := parentDistributor.LevelInfo.JhCpsSettleInfo
			if cpsSettleInfo.AmountRatio > 0 && cpsSettleInfo.AmountRatio-award.Ratio > 0 {
				ratio = cpsSettleInfo.AmountRatio - award.Ratio
			}
		}
		if ratio <= 0 {
			continue
		}
		// 提成金额
		awardAmount := int64(award.SettleAmount) * int64(ratio) / 10000
		if awardAmount <= 0 {
			continue
		}
		// 组装奖励
		var parentAward model.DistributorAward
		parentAward.Uid = parentDistributor.Uid
		parentAward.ChildUid = award.ChildUid
		parentAward.LevelID = parentDistributor.LevelID
		parentAward.LevelName = parentDistributor.LevelInfo.Name
		parentAward.OrderID = award.OrderID
		parentAward.OrderType = award.OrderType
		parentAward.OrderSN = award.OrderSN
		parentAward.OrderAmount = award.OrderAmount
		parentAward.SettleAmount = award.SettleAmount
		parentAward.SettleType = award.SettleType
		parentAward.Ratio = ratio
		parentAward.Amount = uint(awardAmount)
		parentAward.Status = model.Wait
		parentAward.Layers = model.Indirect
		parentAward.SettleDays = award.SettleDays
		parentAwards = append(parentAwards, parentAward)
	}
	// 插入
	if len(parentAwards) > 0 {
		// 产生奖励
		err = source.DB().Create(&parentAwards).Error
	}
	return
}*/

func IndirectAwards(distributor model.Distributor, awards []model.DistributorAward, order Order) (err error) {
	if len(awards) == 0 {
		return
	}
	var parentDistributor model.Distributor
	err, parentDistributor = getParentDistributorByUserId(distributor.Uid)
	if err != nil || parentDistributor.ID == 0 {
		// 发送奖励消息
		err = sendMq(order.ID, awards)
		if err != nil {
			return
		}
		return
	}
	var parentAwards, childAwards []model.DistributorAward
	for _, award := range awards {
		ratio := 0
		settleAmount := 0
		// 计算极差比例
		switch award.OrderType {
		case model.OrderTypeShop:
			shopSettleInfo := parentDistributor.LevelInfo.IShopSettleInfo
			if award.SettleType == model.SettleTypeOrder {
				if shopSettleInfo.FormulaRatio > 0 && shopSettleInfo.FormulaRatio-award.Ratio > 0 {
					ratio = shopSettleInfo.FormulaRatio - award.Ratio
				}
				if shopSettleInfo.AmountSwitch == 1 {
					// 分成基数
					settleAmount = int(order.Amount)
					// 减成本
					if shopSettleInfo.CostSwitch == 1 {
						settleAmount -= int(order.CostAmount)
					}
					// 减运费
					if shopSettleInfo.FreightSwitch == 1 {
						settleAmount -= int(order.Freight)
					}
				}
			} else {
				if shopSettleInfo.BuyServiceRatio > 0 && shopSettleInfo.BuyServiceRatio-award.Ratio > 0 {
					ratio = shopSettleInfo.BuyServiceRatio - award.Ratio
				}
				settleAmount = int(order.TechnicalServicesFee)
			}
		case model.OrderTypeSupplier:
			supplierSettleInfo := parentDistributor.LevelInfo.ISupplierSettleInfo
			if award.SettleType == model.SettleTypeOrder {
				if supplierSettleInfo.FormulaRatio > 0 && supplierSettleInfo.FormulaRatio-award.Ratio > 0 {
					ratio = supplierSettleInfo.FormulaRatio - award.Ratio
				}
				if supplierSettleInfo.AmountSwitch == 1 {
					// 分成基数
					settleAmount = int(order.Amount)
					// 减成本
					if supplierSettleInfo.CostSwitch == 1 {
						settleAmount -= int(order.CostAmount)
					}
					// 减运费
					if supplierSettleInfo.FreightSwitch == 1 {
						settleAmount -= int(order.Freight)
					}
				}
			} else if award.SettleType == model.SettleTypeSupplier {
				if supplierSettleInfo.SupplierRebateRatio > 0 && supplierSettleInfo.SupplierRebateRatio-award.Ratio > 0 {
					ratio = supplierSettleInfo.SupplierRebateRatio - award.Ratio
				}
				settleAmount = int(award.SettleAmount)
			} else {
				if supplierSettleInfo.BuyServiceRatio > 0 && supplierSettleInfo.BuyServiceRatio-award.Ratio > 0 {
					ratio = supplierSettleInfo.BuyServiceRatio - award.Ratio
				}
				settleAmount = int(order.TechnicalServicesFee)
			}
		case model.OrderTypeSupply:
			// 数字权益订单
			var supply GatherSupply
			err = source.DB().Unscoped().Where("category_id = ?", 98).First(&supply).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				continue
			}
			var supplySettleInfo model.SupplySettleInfo
			if supply.ID == order.GatherSupplyID {
				// 分销商等级->数字权益订单奖励设置
				supplySettleInfo = parentDistributor.LevelInfo.IEquitySettleInfo
			} else {
				// 分销商等级->供应链订单奖励设置
				supplySettleInfo = parentDistributor.LevelInfo.ISupplySettleInfo
			}
			if award.SettleType == model.SettleTypeOrder {
				if supplySettleInfo.FormulaRatio > 0 && supplySettleInfo.FormulaRatio-award.Ratio > 0 {
					ratio = supplySettleInfo.FormulaRatio - award.Ratio
				}
				// 订单实付金额
				if supplySettleInfo.AmountSwitch == 1 {
					// 分成基数
					settleAmount = int(order.Amount)
					// 减协议价
					if supplySettleInfo.DealSwitch == 1 {
						settleAmount -= int(order.SupplyAmount)
					}
					// 减运费
					if supplySettleInfo.FreightSwitch == 1 {
						settleAmount -= int(order.Freight)
					}
				}
			} else {
				if supplySettleInfo.BuyServiceRatio > 0 && supplySettleInfo.BuyServiceRatio-award.Ratio > 0 {
					ratio = supplySettleInfo.BuyServiceRatio - award.Ratio
				}
				settleAmount = int(order.TechnicalServicesFee)
			}
		case model.OrderTypeCps:
			cpsSettleInfo := parentDistributor.LevelInfo.ICpsSettleInfo
			if cpsSettleInfo.AmountRatio > 0 && cpsSettleInfo.AmountRatio-award.Ratio > 0 {
				ratio = cpsSettleInfo.AmountRatio - award.Ratio
			}
			settleAmount = int(award.SettleAmount)
		case model.OrderTypeJhCps:
			cpsSettleInfo := parentDistributor.LevelInfo.IJhCpsSettleInfo
			if cpsSettleInfo.AmountRatio > 0 && cpsSettleInfo.AmountRatio-award.Ratio > 0 {
				ratio = cpsSettleInfo.AmountRatio - award.Ratio
			}
			settleAmount = int(award.SettleAmount)
		case model.OrderTypeMeituanDistributor:
			cpsSettleInfo := parentDistributor.LevelInfo.IMeituanDisSettleInfo
			if cpsSettleInfo.AmountRatio > 0 && cpsSettleInfo.AmountRatio-award.Ratio > 0 {
				ratio = cpsSettleInfo.AmountRatio - award.Ratio
			}
			settleAmount = int(award.SettleAmount)
		case model.OrderTypeUserUpgrade:
			cpsSettleInfo := parentDistributor.LevelInfo.IUserUpgradeSettleInfo
			if cpsSettleInfo.AmountRatio > 0 && cpsSettleInfo.AmountRatio-award.Ratio > 0 {
				ratio = cpsSettleInfo.AmountRatio - award.Ratio
			}
			settleAmount = int(award.SettleAmount)
		case model.OrderTypeDouYinGroup:
			cpsSettleInfo := parentDistributor.LevelInfo.IDouYinGroupSettleInfo
			if cpsSettleInfo.AmountRatio > 0 && cpsSettleInfo.AmountRatio-award.Ratio > 0 {
				ratio = cpsSettleInfo.AmountRatio - award.Ratio
			}
			settleAmount = int(order.Amount)
		default:
			ratio = 0
			settleAmount = 0
		}
		if ratio <= 0 || settleAmount <= 0 {
			childAwards = append(childAwards, award)
			continue
		}
		// 提成金额
		awardAmount := int64(settleAmount) * int64(ratio) / 10000
		if awardAmount <= 0 {
			childAwards = append(childAwards, award)
			continue
		}
		// 组装奖励
		var parentAward model.DistributorAward
		parentAward.Uid = parentDistributor.Uid
		parentAward.ChildUid = award.ChildUid
		parentAward.LevelID = parentDistributor.LevelID
		parentAward.LevelName = parentDistributor.LevelInfo.Name
		parentAward.OrderID = award.OrderID
		parentAward.OrderType = award.OrderType
		parentAward.OrderSN = award.OrderSN
		parentAward.OrderAmount = award.OrderAmount
		parentAward.SettleAmount = award.SettleAmount
		parentAward.SettleType = award.SettleType
		parentAward.Ratio = ratio
		parentAward.ChildRatio = award.Ratio
		parentAward.Amount = uint(awardAmount)
		parentAward.Status = model.Wait
		parentAward.Layers = model.Indirect
		parentAward.SettleDays = award.SettleDays
		parentAward.CanSettle = award.CanSettle
		parentAwards = append(parentAwards, parentAward)
	}
	// 插入
	if len(parentAwards) > 0 {
		// 产生奖励
		err = source.DB().Create(&parentAwards).Error
		if err != nil {
			return
		}
		if len(childAwards) > 0 {
			// 把childAwards合并到parentAwards
			parentAwards = append(parentAwards, childAwards...)
		}
		// 发送奖励消息
		err = sendMq(order.ID, parentAwards)
		if err != nil {
			return
		}
	} else {
		// 发送奖励消息
		err = sendMq(order.ID, awards)
		if err != nil {
			return
		}
	}
	return
}

// 发送奖励消息
func sendMq(orderID uint, awards []model.DistributorAward) (err error) {
	// log.Log().Error("分销监听订单完成后事件sendMq()", zap.Any("awards", awards))
	// log.Log().Error("分销监听订单完成后事件sendMq()", zap.Any("orderID", orderID))
	var canSettle int
	canSettle = 2
	var ratios []int
	for _, row := range awards {
		if row.CanSettle == 1 {
			canSettle = 1
		}
		if row.Layers == 1 {
			ratios = append(ratios, row.Ratio)
		} else {
			ratios = append(ratios, row.Ratio+row.ChildRatio)
		}
	}
	var mqByte []byte
	mqByte, _ = json.Marshal(ratios)
	mqStr := string(mqByte)
	err = award_mq.PublishMessage(orderID, mqStr, canSettle, award_mq.Award)
	if err != nil {
		return
	}
	return err
}
