package award

import (
	"distributor/award_mq"
	"encoding/json"
	"testing"
)

func TestHandle(t *testing.T) {

	var ratios []int
	ratios = append(ratios, 1)
	ratios = append(ratios, 2)
	ratios = append(ratios, 3)
	var ratioStr []byte
	ratioStr, _ = json.Marshal(ratios)
	_ = award_mq.PublishMessage(1, string(ratioStr), 1, 1)
	//
	//res := string(ratioStr)
	//var rr []int
	//_ = json.Unmarshal(ratioStr, &rr)
	//fmt.Println(rr[0])
	//fmt.Println(len(rr))
	//for _, r := range rr {
	//	fmt.Println(r)
	//}
	//fmt.Println(rr)
}
