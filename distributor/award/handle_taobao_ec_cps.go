package award

import (
	"distributor/model"
	"distributor/service"
	model5 "ec-cps-ctrl/model"
	"errors"
	"gorm.io/gorm"
	"strconv"
	"yz-go/source"
)

func HandleTaobaoEcCps(orderId uint) (err error, isAward bool) {
	// 查询订单
	var order model5.TaobaoOrderModel
	err = source.DB().Preload("Application.User.UserLevel").Where("id = ?", orderId).First(&order).Error
	if err != nil {
		return
	}

	// 将佣金字符串转换为浮点数
	var commissionFloat float64
	commissionFloat, err = strconv.ParseFloat(order.PubShareFee, 64)
	if err != nil {
		return
	}

	awardAmount := int(commissionFloat*100) * order.Application.User.UserLevelInfo.CpsRatio / 10000 // 假设分成比例为70%

	// 通过order.UserID查询会员ParentID 查询上级是否为分销商
	var distributor model.Distributor
	err, distributor = getParentDistributorByUserId(uint(order.AppUserID))
	if err != nil {
		return
	}
	if distributor.ID == 0 {
		err = errors.New("上级不是分销商")
		return
	}

	// 修改分销商的累计推荐订单总数和累计推荐订单总额
	distributor.RecommendOrderCountTotal += 1
	distributor.RecommendOrderAmountTotal += uint(awardAmount)
	err = updateDistributor(distributor)
	if err != nil {
		return
	}

	err, isAward = taobaoEcCpsAward(distributor, order)

	return
}

func taobaoEcCpsAward(distributor model.Distributor, order model5.TaobaoOrderModel) (err error, isAward bool) {
	// 分销基础设置
	var setting model.Setting
	err, setting = service.GetSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}

	// 分销商等级->cps订单奖励设置
	cpsSettleInfo := distributor.LevelInfo.CpsSettleInfo
	var awards []model.DistributorAward

	// 将订单金额字符串转换为浮点数
	var alipayTotalPriceFloat float64
	alipayTotalPriceFloat, err = strconv.ParseFloat(order.PubShareFee, 64)
	if err != nil {
		return
	}

	var commissionFloat float64
	commissionFloat, err = strconv.ParseFloat(order.PubShareFee, 64)
	if err != nil {
		return
	}

	// 分成基数（订单实付金额，单位为分）
	settleAmount := int64(commissionFloat * 100)

	if settleAmount <= 0 {
		return
	}

	// 比例
	if cpsSettleInfo.AmountRatio <= 0 {
		return
	}

	// 分成金额
	awardAmount := settleAmount * int64(cpsSettleInfo.AmountRatio) / 10000
	if awardAmount <= 0 {
		return
	}

	var award model.DistributorAward
	award.Uid = distributor.Uid
	award.ChildUid = uint(order.Application.MemberId)
	award.LevelID = distributor.LevelID
	award.LevelName = distributor.LevelInfo.Name
	award.OrderID = order.ID
	award.OrderType = model.OrderTypeTaobaoEcCps
	award.OrderSN = order.ID
	award.OrderAmount = uint(alipayTotalPriceFloat * 100)
	award.SettleAmount = uint(settleAmount)
	award.SettleType = model.SettleTypeOrder
	award.Ratio = cpsSettleInfo.AmountRatio
	award.Amount = uint(awardAmount)
	award.Status = model.Wait
	award.SettleDays = setting.Values.SettleDays
	awards = append(awards, award)

	if len(awards) > 0 {
		// 产生奖励
		err = source.DB().Create(&awards).Error
		if err != nil {
			return
		}
		if setting.Values.Layers == 2 {
			var o Order
			err = source.DB().Where("id = ?", order.ID).First(&o).Error
			if err != nil {
				return
			}
			err = IndirectAwards(distributor, awards, o)
		} else {
			err = sendMq(order.ID, awards)
		}
		isAward = true
	}
	return
}
