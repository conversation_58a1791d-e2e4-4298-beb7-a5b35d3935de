package cps_listener

import (
	"distributor/award"
	"distributor/award_mq"
	"douyin-cps/mq"
	"go.uber.org/zap"
	"yz-go/component/log"
)

func PushTaobaoCpsOrderSettleHandles() {
	mq.PushHandles("taobaoOrderSettleDistributorAward", func(orderMsg mq.CpsOrderMessage) (err error) {
		// 检查消息类型
		if orderMsg.MessageType != mq.Settle {
			return nil
		}

		// 检查订单类型
		if orderMsg.OrderType != mq.TaobaoCps {
			return nil
		}

		// 分销商是否有分成
		var isAward bool

		// 产生奖励
		err, isAward = award.HandleTaobaoCps(orderMsg.OrderID)
		if !isAward {
			log.Log().Info("cps taobaoOrderSettleDistributorAward没有产生分销奖励,发送消息给机构")
			// 发送消息给机构
			err = award_mq.PublishMessage(orderMsg.OrderID, "", 1, award_mq.Award)
			if err != nil {
				return
			}
		}

		if err != nil {
			log.Log().Error("cps产生分销奖励失败", zap.Error(err))
			return nil
		}

		return nil
	})
}
