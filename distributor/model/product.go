package model

import (
	"errors"
	"gorm.io/gorm"
	"yz-go/source"
)

type Sku struct {
	ID        uint `json:"id" form:"id" gorm:"primarykey"`
	ProductID uint `json:"product_id" form:"product_id" gorm:"index"`
}

func GetSkuID(productID uint) (err error, skuID uint) {
	var sku Sku
	err = source.DB().Model(&sku).Where("product_id = ?", productID).First(&sku).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err, skuID
	}
	if sku.ID == 0 {
		err = errors.New("查询规格失败")
		return err, skuID
	}
	return err, sku.ID
}
