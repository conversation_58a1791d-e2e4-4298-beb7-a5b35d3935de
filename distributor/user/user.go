package user

import "yz-go/source"

type User struct {
	source.Model
	Mobile   string `json:"mobile" form:"mobile" gorm:"column:mobile;comment:手机号;type:char(20);size:20;"`
	Avatar   string `json:"avatar" form:"avatar" gorm:"column:avatar;comment:用户头像url;type:varchar(255);size:255;"`
	Username string `json:"username" form:"username" gorm:"comment:用户登录名"`
	NickName string `json:"nickname" form:"nickName" gorm:"column:nick_name;comment:用户昵称;type:varchar(50);size:50;"`
	Status   int    `json:"status" form:"status" gorm:"column:status;comment:状态（-1拉黑0待审核1正常）;type:smallint;size:1;"`
	LevelID  uint   `json:"level_id" form:"level_id"`
	ParentId uint   `json:"parent_id" form:"parent_id" gorm:"comment:推荐会员id;"`
}

// 获取全部会员ids
func GetAllUserIds() (err error, userIds []uint) {
	err = source.DB().Model(&User{}).Pluck("id", &userIds).Error
	return
}

// 获取不是分销商的会员ids
func GetUserIdsNotByDistributorUserIds(distributorUserIds []uint) (err error, userIds []uint) {
	err = source.DB().Model(&User{}).Where("id NOT IN ?", distributorUserIds).Pluck("id", &userIds).Error
	return
}

// 通过等级id获取会员ids
func GetUserIdsByLevelId(levelId uint) (err error, userIds []uint) {
	err = source.DB().Model(&User{}).Where("level_id = ?", levelId).Pluck("id", &userIds).Error
	return
}

// 通过会员等级获取不是分销商的会员ids
func GetUserIdsByLevelIdAndNotByDistributorUserIds(levelId uint, distributorUserIds []uint) (err error, userIds []uint) {
	err = source.DB().Model(&User{}).Where("level_id = ? AND id NOT IN ?", levelId, distributorUserIds).Pluck("id", &userIds).Error
	return
}

// 通过会员id获取下级会员ids
func GetChildIdsByUserId(userId uint) (err error, childIds []uint) {
	err = source.DB().Model(&User{}).Where("parent_id = ?", userId).Pluck("id", &childIds).Error
	return
}

// 通过会员id获取上级会员id
func GetParentIdByUserId(userId uint) (err error, parentId uint) {
	err = source.DB().Select("parent_id").Model(&User{}).Where("id = ?", userId).First(&parentId).Error
	return
}
