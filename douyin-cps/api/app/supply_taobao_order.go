package app

import (
	"douyin-cps/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"time"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

// GetSupplyTaobaoOrderList 获取供应链淘宝订单列表
// @Tags 供应链淘宝订单
// @Summary 获取供应链淘宝订单列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query service.SupplyTaobaoOrderListRequest true "分页获取供应链淘宝订单列表"
// @Success 200 {object} yzResponse.Response{data=service.SupplyTaobaoOrderListResult} "成功"
// @Router /supply/taobaoOrder/list [get]
func GetSupplyTaobaoOrderList(c *gin.Context) {
	var req service.SupplyTaobaoOrderListRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Log().Error("参数绑定失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	req.AppID = utils.GetAppID(c)
	req.UserID = utils.GetAppUserID(c)
	// 调用服务层获取淘宝订单列表
	err, result := service.GetSupplyTaobaoOrderList(req)
	if err != nil {
		log.Log().Error("查询供应链淘宝订单列表失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithData(result, c)
}

// ExportSupplyTaobaoOrder 导出供应链淘宝订单
// @Tags 供应链淘宝订单
// @Summary 导出供应链淘宝订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query service.SupplyTaobaoOrderListRequest true "导出供应链淘宝订单"
// @Success 200 {object} yzResponse.Response "成功"
// @Router /supply/taobaoOrder/export [post]
func ExportSupplyTaobaoOrder(c *gin.Context) {
	var req service.SupplyTaobaoOrderListRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Log().Error("参数绑定失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 设置较大的页面大小以导出更多数据
	req.PageSize = 5000
	req.Page = 1

	// 调用服务层导出淘宝订单
	err = service.ExportSupplyTaobaoOrders(req)
	if err != nil {
		log.Log().Error("导出供应链淘宝订单失败", zap.Error(err))
		yzResponse.FailWithMessage("导出失败: "+err.Error(), c)
		return
	}

	yzResponse.OkWithMessage("导出任务已提交，请稍后查看导出文件", c)
}

// GetSupplyTaobaoOrderDetail 获取供应链淘宝订单详情
// @Tags 供应链淘宝订单
// @Summary 获取供应链淘宝订单详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "订单ID"
// @Success 200 {object} yzResponse.Response{data=model.SupplyTaobaoOrder} "成功"
// @Router /supply/taobaoOrder/detail/{id} [get]
func GetSupplyTaobaoOrderDetail(c *gin.Context) {
	id := c.Param("id")

	// 调用服务层获取淘宝订单详情
	err, order := service.GetSupplyTaobaoOrderDetail(id)
	if err != nil {
		log.Log().Error("查询供应链淘宝订单详情失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithData(order, c)
}

// SyncSupplyTaobaoOrder 同步供应链淘宝订单
// @Tags 供应链淘宝订单
// @Summary 手动同步供应链淘宝订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query service.SupplyTaobaoOrderListRequest true "同步供应链淘宝订单"
// @Success 200 {object} yzResponse.Response "成功"
// @Router /supply/taobaoOrder/sync [post]
func SyncSupplyTaobaoOrder(c *gin.Context) {
	var req service.SupplyTaobaoOrderListRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Log().Error("参数绑定失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认时间范围
	if req.StartTime == "" {
		// 默认同步过去24小时的订单
		req.StartTime = time.Now().Add(-24 * time.Hour).Format("2006-01-02 15:04:05")
	}
	if req.EndTime == "" {
		req.EndTime = time.Now().Format("2006-01-02 15:04:05")
	}

	// 调用同步服务
	go func() {
		// 调用服务层同步淘宝订单
		service.SyncTaobaoOrdersByDateRange(req.StartTime, req.EndTime)
	}()

	yzResponse.OkWithMessage("同步任务已提交，请稍后查看结果", c)
}
