package app

import (
	"douyin-cps/service"
	"github.com/gin-gonic/gin"
	"net/http"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

func TcGeneralConvert(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJ<PERSON>N(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.TcGeneralConvert(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}

}

func IdPrivilege(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.<PERSON>rror(), c)
		return
	}

	if err, data := service.IdPrivilege(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}

}

func TklPrivilege(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.TklPrivilege(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}

}

func ShopConvert(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.ShopConvert(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}

}

func OrderDetails(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.OrderDetails(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}

}
func ScPunishOrder(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.ScPunishOrder(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}

}

func InvitecodeGet(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.InvitecodeGet(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}

}

func SuperSearchMaterial(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage("cps传参错误："+err.Error(), c)
		return
	}

	if err, data := service.SuperSearchMaterial(request, utils.GetAppUserID(c)); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}

}

func MaterialRecommend(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.MaterialRecommend(request, utils.GetAppUserID(c)); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}

}

func TbItemInfo(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.TbItemInfo(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}

}
