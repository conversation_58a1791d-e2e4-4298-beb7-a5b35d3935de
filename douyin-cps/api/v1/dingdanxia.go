package v1

import (
	"douyin-cps/service"
	"github.com/gin-gonic/gin"
	. "yz-go/response"
	utils2 "yz-go/utils"
)

func GetAccessTokensByAppIDs(c *gin.Context) {

	response, err := service.GetAccessTokensByAppIDs(utils2.GetAppID(c))
	if err != nil {
		FailWithMessage("获取失败", c)
		return
	} else {
		OkWithData(response, c)
		return
	}
}

func GetFiles(c *gin.Context) {
	err, response := service.GetFiles()
	if err != nil {
		FailWithMessage("获取失败", c)
		return
	} else {
		OkWithData(response, c)
		return
	}
}
