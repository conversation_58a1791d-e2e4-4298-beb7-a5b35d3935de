package v1

import (
	"douyin-cps/service"
	"github.com/gin-gonic/gin"
	v1 "user/api/f/v1"
	yzResponse "yz-go/response"
)

type ProductPosterRequest struct {
	ProductID    uint   `json:"product_id" form:"product_id" query:"product_id"`
	ProductTitle string `json:"product_title"`
	ProductImage string `json:"product_image"`
	ProductLink  string `json:"product_link"`
	ProductPrice string `json:"product_price"`
}

func GetPosterInfo(c *gin.Context) {
	var req ProductPosterRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		//log.log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	domain := c.Request.Header.Get("X-Forwarded-Proto") + "://" + c.Request.Host

	//err, qrCodeLink := service.GetQrCode(product.ID, smallShop.ID, domain)
	err, qrCodeLink := service.GetProductPoster(v1.GetUserID(c), req.ProductID, req.ProductImage, req.ProductTitle, req.ProductPrice, domain, req.ProductLink)
	if err != nil {
		yzResponse.FailWithMessage("生成二维码失败", c)
		return
	}
	yzResponse.OkWithDetailed(gin.H{
		"qrcode": qrCodeLink,
	}, "成功", c)
}
