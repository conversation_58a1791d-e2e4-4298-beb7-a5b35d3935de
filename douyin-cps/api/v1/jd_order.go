package v1

import (
	"douyin-cps/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"yz-go/component/log"
	yzResponse "yz-go/response"
)

// GetJDOrderList 获取京东订单列表
// @Tags 京东订单
// @Summary 获取京东订单列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body map[string]interface{} true "查询参数"
// @Success 200 {object} yzResponse.Response{data=yzResponse.PageResult} "成功"
// @Router /jdOrder/list [post]
func GetJDOrderList(c *gin.Context) {
	var req map[string]interface{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Log().Error("参数绑定失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 调用service层获取京东订单列表
	err, total, list := service.GetJDOrderList(req)
	if err != nil {
		log.Log().Error("查询京东订单列表失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 获取分页参数
	page := 1
	if p, ok := req["page"].(float64); ok {
		page = int(p)
	}

	pageSize := 10
	if ps, ok := req["page_size"].(float64); ok {
		pageSize = int(ps)
	}

	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List:     list,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	}, "获取成功", c)
}

// GetJDOrderDetail 获取京东订单详情
// @Tags 京东订单
// @Summary 获取京东订单详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param order_id query string true "订单ID"
// @Success 200 {object} yzResponse.Response{data=model.JDOrder} "成功"
// @Router /jdOrder/detail [get]
func GetJDOrderDetail(c *gin.Context) {
	orderID := c.Query("order_id")
	if orderID == "" {
		yzResponse.FailWithMessage("订单ID不能为空", c)
		return
	}

	// 调用service层获取京东订单详情
	err, order := service.GetJDOrderDetail(orderID)
	if err != nil {
		log.Log().Error("查询京东订单详情失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithData(order, c)
}

// ExportJDOrders 导出京东订单
// @Tags 京东订单
// @Summary 导出京东订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body map[string]interface{} true "查询参数"
// @Success 200 {object} yzResponse.Response "成功"
// @Router /jdOrder/export [post]
func ExportJDOrders(c *gin.Context) {
	var req map[string]interface{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Log().Error("参数绑定失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 调用service层导出京东订单
	err = service.ExportJDOrders(req)
	if err != nil {
		log.Log().Error("导出京东订单失败", zap.Error(err))
		yzResponse.FailWithMessage("导出失败: "+err.Error(), c)
		return
	}

	yzResponse.OkWithMessage("导出成功", c)
}
