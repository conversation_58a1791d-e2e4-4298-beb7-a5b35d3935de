package v1

import (
	"douyin-cps/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

// GetPddOrderList 获取拼多多订单列表
// @Tags 拼多多订单
// @Summary 获取拼多多订单列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body map[string]interface{} true "查询参数"
// @Success 200 {object} yzResponse.Response{data=yzResponse.PageResult} "成功"
// @Router /ecCps/pdd/orderlist [post]
func GetPddOrderList(c *gin.Context) {
	var req map[string]interface{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Log().Error("参数绑定失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 调用service层获取拼多多订单列表
	err, total, list := service.GetPddOrderList(req)
	if err != nil {
		log.Log().Error("查询拼多多订单列表失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 获取分页参数
	page := 1
	if p, ok := req["page"].(float64); ok {
		page = int(p)
	}

	pageSize := 10
	if ps, ok := req["page_size"].(float64); ok {
		pageSize = int(ps)
	}

	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List:     list,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	}, "获取成功", c)
}

// GetPddOrderDetail 获取拼多多订单详情
// @Tags 拼多多订单
// @Summary 获取拼多多订单详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param order_sn query string true "订单号"
// @Success 200 {object} yzResponse.Response{data=model.PddOrder} "成功"
// @Router /ecCps/pdd/orderdetail [get]
func GetPddOrderDetail(c *gin.Context) {
	orderSn := c.Query("order_sn")
	if orderSn == "" {
		yzResponse.FailWithMessage("订单号不能为空", c)
		return
	}

	// 调用service层获取拼多多订单详情
	err, order := service.GetPddOrderDetail(orderSn)
	if err != nil {
		log.Log().Error("查询拼多多订单详情失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithData(order, c)
}

// ExportPddOrders 导出拼多多订单
// @Tags 拼多多订单
// @Summary 导出拼多多订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body map[string]interface{} true "查询参数"
// @Success 200 {object} yzResponse.Response "成功"
// @Router /ecCps/pdd/export [post]
func ExportPddOrders(c *gin.Context) {
	var req map[string]interface{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Log().Error("参数绑定失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 获取应用ID
	req["app_id"] = utils.GetAppID(c)

	// 调用service层导出拼多多订单
	err = service.ExportPddOrders(req)
	if err != nil {
		log.Log().Error("导出拼多多订单失败", zap.Error(err))
		yzResponse.FailWithMessage("导出失败: "+err.Error(), c)
		return
	}

	yzResponse.OkWithMessage("导出成功", c)
}
