package cron

import (
	"douyin-cps/model"
	"fmt"
	"strconv"

	"go.uber.org/zap"
	"gorm.io/gorm"
	"yz-go/component/log"
	"yz-go/source"
)

// OrderMapper 订单映射器接口
type OrderMapper interface {
	GetOrderID() string
	GetAppID() int
	GetAppUserID() int
	GetType() string
}

// processCpsOrdersGeneric 通用的CpsOrderModel处理函数
func processCpsOrdersGeneric(orders []OrderMapper, mapperFunc func(OrderMapper) model.CpsOrderModel, batchSize int, workerID int, orderType string) {
	if len(orders) == 0 {
		return
	}

	log.Log().Info("开始处理CpsOrderModel映射",
		zap.Int("协程ID", workerID),
		zap.Int("订单数量", len(orders)),
		zap.String("订单类型", orderType))

	// 收集所有订单ID
	var orderIds []string
	for _, order := range orders {
		orderIds = append(orderIds, order.GetOrderID())
	}

	// 查询数据库中已存在的CpsOrderModel
	var existingCpsOrders []model.CpsOrderModel
	result := source.DB().Where("order_id IN ? AND type = ?", orderIds, orderType).Find(&existingCpsOrders)
	if result.Error != nil && result.Error != gorm.ErrRecordNotFound {
		log.Log().Error("查询现有CpsOrderModel失败", zap.Int("协程ID", workerID), zap.Error(result.Error))
		return
	}

	// 将现有订单映射到map中便于快速查找
	existingCpsOrderMap := make(map[string]model.CpsOrderModel)
	for _, order := range existingCpsOrders {
		existingCpsOrderMap[order.OrderId] = order
	}

	// 准备新订单和需要更新的订单
	var newCpsOrders []model.CpsOrderModel
	var updateCpsOrders []model.CpsOrderModel

	for _, order := range orders {
		// 映射订单到CpsOrderModel
		cpsOrder := mapperFunc(order)

		if _, exists := existingCpsOrderMap[cpsOrder.OrderId]; !exists {
			// 新订单
			newCpsOrders = append(newCpsOrders, cpsOrder)
		} else {
			// 需要更新的订单
			dbCpsOrder := existingCpsOrderMap[cpsOrder.OrderId]
			// 更新关键字段
			updateCpsOrder := updateCpsOrderFields(dbCpsOrder, cpsOrder)
			updateCpsOrders = append(updateCpsOrders, updateCpsOrder)
		}
	}

	// 批量创建新的CpsOrderModel
	if len(newCpsOrders) > 0 {
		createCpsOrdersBatch(newCpsOrders, batchSize, workerID)
	}

	// 批量更新CpsOrderModel
	if len(updateCpsOrders) > 0 {
		updateCpsOrdersBatch(updateCpsOrders, batchSize, workerID, orderType)
	}

	log.Log().Info("完成CpsOrderModel处理",
		zap.Int("协程ID", workerID),
		zap.Int("新增数量", len(newCpsOrders)),
		zap.Int("更新数量", len(updateCpsOrders)),
		zap.String("订单类型", orderType))
}

// updateCpsOrderFields 更新CpsOrderModel字段
func updateCpsOrderFields(dbOrder, newOrder model.CpsOrderModel) model.CpsOrderModel {
	// 更新关键字段
	dbOrder.TotalPayAmount = newOrder.TotalPayAmount
	dbOrder.PayGoodsAmount = newOrder.PayGoodsAmount
	dbOrder.EstimatedCommission = newOrder.EstimatedCommission
	dbOrder.SplitRate = newOrder.SplitRate
	dbOrder.FlowPoint = newOrder.FlowPoint
	dbOrder.AfterSalesStatus = newOrder.AfterSalesStatus
	dbOrder.ConfirmTime = newOrder.ConfirmTime
	dbOrder.SettleTime = newOrder.SettleTime
	dbOrder.RefundTime = newOrder.RefundTime
	dbOrder.EstimatedTechServiceFee = newOrder.EstimatedTechServiceFee
	dbOrder.ExternalInfo = newOrder.ExternalInfo
	return dbOrder
}

// createCpsOrdersBatch 批量创建CpsOrderModel
func createCpsOrdersBatch(orders []model.CpsOrderModel, batchSize int, workerID int) {
	for i := 0; i < len(orders); i += batchSize {
		end := i + batchSize
		if end > len(orders) {
			end = len(orders)
		}

		batch := orders[i:end]
		if err := source.DB().Create(&batch).Error; err != nil {
			log.Log().Error("批量创建CpsOrderModel失败", zap.Int("协程ID", workerID), zap.Error(err))
		} else {
			log.Log().Info("成功创建新CpsOrderModel", zap.Int("协程ID", workerID), zap.Int("数量", len(batch)))
		}
	}
}

// updateCpsOrdersBatch 批量更新CpsOrderModel
func updateCpsOrdersBatch(orders []model.CpsOrderModel, batchSize int, workerID int, orderType string) {
	for i := 0; i < len(orders); i += batchSize {
		end := i + batchSize
		if end > len(orders) {
			end = len(orders)
		}

		batch := orders[i:end]
		for _, order := range batch {
			if err := source.DB().Model(&model.CpsOrderModel{}).Where("order_id = ? AND type = ?", order.OrderId, orderType).Updates(map[string]interface{}{
				"total_pay_amount":           order.TotalPayAmount,
				"pay_goods_amount":           order.PayGoodsAmount,
				"estimated_commission":       order.EstimatedCommission,
				"split_rate":                 order.SplitRate,
				"flow_point":                 order.FlowPoint,
				"after_sales_status":         order.AfterSalesStatus,
				"confirm_time":               order.ConfirmTime,
				"settle_time":                order.SettleTime,
				"refund_time":                order.RefundTime,
				"estimated_tech_service_fee": order.EstimatedTechServiceFee,
				"external_info":              order.ExternalInfo,
			}).Error; err != nil {
				log.Log().Error("更新CpsOrderModel失败", zap.Int("协程ID", workerID), zap.String("订单ID", order.OrderId), zap.Error(err))
			}
		}
		log.Log().Info("成功更新CpsOrderModel", zap.Int("协程ID", workerID), zap.Int("数量", len(batch)))
	}
}

// JDOrderMapper JD订单映射器
type JDOrderMapper struct {
	Order model.JDOrder
}

func (j JDOrderMapper) GetOrderID() string { return strconv.Itoa(int(j.Order.OrderId)) }
func (j JDOrderMapper) GetAppID() int      { return j.Order.AppID }
func (j JDOrderMapper) GetAppUserID() int  { return j.Order.AppUserID }
func (j JDOrderMapper) GetType() string    { return "jd" }

// TaobaoOrderMapper 淘宝订单映射器
type TaobaoOrderMapper struct {
	Order model.SupplyTaobaoOrder
}

func (t TaobaoOrderMapper) GetOrderID() string { return t.Order.TradeId }
func (t TaobaoOrderMapper) GetAppID() int      { return t.Order.AppID }
func (t TaobaoOrderMapper) GetAppUserID() int  { return t.Order.AppUserID }
func (t TaobaoOrderMapper) GetType() string    { return "taobao" }

// PddOrderMapper 拼多多订单映射器
type PddOrderMapper struct {
	Order model.PddOrder
}

func (p PddOrderMapper) GetOrderID() string { return p.Order.OrderSn }
func (p PddOrderMapper) GetAppID() int      { return p.Order.AppID }
func (p PddOrderMapper) GetAppUserID() int  { return p.Order.AppUserID }
func (p PddOrderMapper) GetType() string    { return "pdd" }

// VipOrderMapper 唯品会订单映射器
type VipOrderMapper struct {
	Order model.VipOrder
}

func (v VipOrderMapper) GetOrderID() string { return v.Order.OrderSn }
func (v VipOrderMapper) GetAppID() int      { return int(v.Order.AppID) }
func (v VipOrderMapper) GetAppUserID() int  { return int(v.Order.AppUserID) }
func (v VipOrderMapper) GetType() string    { return "vip" }

// mapJDOrderToCpsOrder 将JDOrder映射到CpsOrderModel
func mapJDOrderToCpsOrder(mapper OrderMapper) model.CpsOrderModel {
	jdMapper := mapper.(JDOrderMapper)
	jdOrder := jdMapper.Order
	var application model.Application
	source.DB().Where("id = ?", jdOrder.AppID).First(&application)
	cpsOrder := model.CpsOrderModel{
		OrderId:       strconv.Itoa(int(jdOrder.OrderId)),
		ApplicationID: uint(jdOrder.AppID),
		UserID:        uint(application.MemberId),
		ThirdUserID:   uint(jdOrder.AppUserID),
		ProductId:     strconv.Itoa(int(jdOrder.SkuId)),
		ProductName:   jdOrder.SkuName,
		Type:          "jd",
	}

	// 设置支付金额（转换为分）
	if jdOrder.Price > 0 && jdOrder.SkuNum > 0 {
		cpsOrder.TotalPayAmount = int(jdOrder.Price * float64(jdOrder.SkuNum) * 100)
		cpsOrder.PayGoodsAmount = int(jdOrder.ActualCosPrice * 100)
	}

	// 设置佣金（转换为分）
	cpsOrder.EstimatedCommission = int(jdOrder.EstimateFee * 100)

	// 设置佣金比例（转换为万分比）
	if jdOrder.EstimateFee > 0 && jdOrder.EstimateCosPrice > 0 {
		rate := jdOrder.EstimateFee / jdOrder.EstimateCosPrice
		cpsOrder.SplitRate = int(rate * 10000)
	}

	// 设置时间字段
	cpsOrder.PaySuccessTime = jdOrder.OrderTime
	cpsOrder.ConfirmTime = jdOrder.FinishTime
	cpsOrder.SettleTime = jdOrder.PayMonth

	// 设置订单状态
	cpsOrder.FlowPoint = mapJDValidCodeToFlowPoint(jdOrder.ValidCode)

	// 设置售后状态
	if jdOrder.SkuReturnNum > 0 || jdOrder.SkuFrozenNum > 0 {
		cpsOrder.AfterSalesStatus = 2            // 产生退款
		cpsOrder.RefundTime = jdOrder.ModifyTime // 使用修改时间作为退款时间
	} else {
		cpsOrder.AfterSalesStatus = 1 // 空
	}

	// 设置外部信息
	cpsOrder.ExternalInfo = fmt.Sprintf("position_id:%s,union_id:%s,sku_num:%d", jdOrder.PositionId, jdOrder.UnionId, jdOrder.SkuNum)

	// 设置显示状态
	cpsOrder.IsDisplay = 1

	// 设置分成状态
	cpsOrder.Status = 0 // 默认未分成

	// 设置技术服务费（预估）
	if jdOrder.EstimateFee > 0 {
		cpsOrder.EstimatedTechServiceFee = int(jdOrder.EstimateFee * 0.1 * 100) // 假设技术服务费为佣金的10%
	}

	return cpsOrder
}

// mapTaobaoOrderToCpsOrder 将SupplyTaobaoOrder映射到CpsOrderModel
func mapTaobaoOrderToCpsOrder(mapper OrderMapper) model.CpsOrderModel {
	taobaoMapper := mapper.(TaobaoOrderMapper)
	taobaoOrder := taobaoMapper.Order
	var application model.Application
	source.DB().Where("id = ?", taobaoOrder.AppID).First(&application)
	cpsOrder := model.CpsOrderModel{
		OrderId:       taobaoOrder.TradeId,
		ApplicationID: uint(taobaoOrder.AppID),
		UserID:        uint(application.MemberId),
		ThirdUserID:   uint(taobaoOrder.AppUserID),
		ProductId:     taobaoOrder.ItemId,
		ProductName:   taobaoOrder.ItemTitle,
		ProductImg:    taobaoOrder.ItemImg,
		Type:          "taobao",
	}

	// 设置支付金额（转换为分）
	if itemPrice, err := strconv.ParseFloat(taobaoOrder.ItemPrice, 64); err == nil {
		cpsOrder.TotalPayAmount = int(itemPrice * float64(taobaoOrder.ItemNum) * 100)
	}

	if alipayTotalPrice, err := strconv.ParseFloat(taobaoOrder.AlipayTotalPrice, 64); err == nil {
		cpsOrder.PayGoodsAmount = int(alipayTotalPrice * 100)
	}

	// 设置佣金（转换为分）
	if pubShareFee, err := strconv.ParseFloat(taobaoOrder.PubShareFee, 64); err == nil {
		cpsOrder.EstimatedCommission = int(pubShareFee * 100)
	}

	// 设置佣金比例（转换为万分比）
	if tkTotalRate, err := strconv.ParseFloat(taobaoOrder.TkTotalRate, 64); err == nil {
		cpsOrder.SplitRate = int(tkTotalRate * 10000)
	}

	// 设置时间字段
	cpsOrder.PaySuccessTime = taobaoOrder.TkPaidTime
	cpsOrder.ConfirmTime = taobaoOrder.TkEarningTime
	cpsOrder.SettleTime = taobaoOrder.TkEarningTime // 淘宝没有单独的结算时间，使用确认收货时间

	// 设置订单状态
	cpsOrder.FlowPoint = mapTaobaoStatusToFlowPoint(taobaoOrder.TkStatus)

	// 设置售后状态
	if taobaoOrder.RefundTag == 1 {
		cpsOrder.AfterSalesStatus = 2                   // 产生退款
		cpsOrder.RefundTime = taobaoOrder.TkEarningTime // 使用确认收货时间作为退款时间
	} else {
		cpsOrder.AfterSalesStatus = 1 // 空
	}

	// 设置外部信息
	cpsOrder.ExternalInfo = fmt.Sprintf("special_id:%s,relation_id:%s,item_num:%d", taobaoOrder.SpecialId, taobaoOrder.RelationId, taobaoOrder.ItemNum)

	// 设置显示状态
	cpsOrder.IsDisplay = 1

	// 设置分成状态
	cpsOrder.Status = 0 // 默认未分成

	// 设置技术服务费（预估）
	if pubShareFee, err := strconv.ParseFloat(taobaoOrder.PubShareFee, 64); err == nil {
		cpsOrder.EstimatedTechServiceFee = int(pubShareFee * 0.1 * 100) // 假设技术服务费为佣金的10%
	}

	return cpsOrder
}

// mapJDValidCodeToFlowPoint 将京东订单状态映射到流程状态
func mapJDValidCodeToFlowPoint(validCode int) string {
	switch validCode {
	case 15: // 已付款
		return "PAY_SUCC"
	case 16: // 已完成
		return "CONFIRM"
	case 17: // 已结算
		return "SETTLE"
	case -1: // 已退款
		return "REFUND"
	default:
		return "PAY_SUCC" // 默认为支付完成
	}
}

// mapTaobaoStatusToFlowPoint 将淘宝订单状态映射到流程状态
func mapTaobaoStatusToFlowPoint(tkStatus int) string {
	switch tkStatus {
	case 12: // 付款
		return "PAY_SUCC"
	case 13: // 结算
		return "SETTLE"
	case 14: // 成功
		return "CONFIRM"
	case 3: // 失效
		return "REFUND"
	default:
		return "PAY_SUCC" // 默认为支付完成
	}
}
