package cron

import (
	"douyin-cps/model"
	"fmt"
	"strconv"
	"time"
	"yz-go/source"
)

// mapPddOrderToCpsOrder 将PddOrder映射到CpsOrderModel
func mapPddOrderToCpsOrder(mapper OrderMapper) model.CpsOrderModel {
	pddMapper := mapper.(PddOrderMapper)
	pddOrder := pddMapper.Order
	var application model.Application
	source.DB().Preload("ApplicationLevel").Where("id = ?", pddOrder.AppID).First(&application)
	cpsOrder := model.CpsOrderModel{
		OrderId:       pddOrder.OrderSn,
		UserID:        uint(application.MemberId),
		ApplicationID: uint(pddOrder.AppID),
		ThirdUserID:   uint(pddOrder.AppUserID),
		ProductId:     strconv.FormatInt(pddOrder.GoodsId, 10),
		ProductName:   pddOrder.GoodsName,
		ProductImg:    pddOrder.GoodsThumbnailUrl,
		Type:          "pdd",
	}

	// 设置支付金额（已经是分）
	cpsOrder.TotalPayAmount = int(pddOrder.OrderAmount)
	cpsOrder.PayGoodsAmount = int(pddOrder.OrderAmount)

	// 设置佣金（已经是分）
	cpsOrder.EstimatedCommission = int(pddOrder.PromotionAmount)

	// 设置佣金比例（转换为万分比）
	if pddOrder.OrderAmount > 0 {
		rate := float64(pddOrder.PromotionAmount) / float64(pddOrder.OrderAmount)
		cpsOrder.SplitRate = int(rate * 10000)
	}

	// 设置时间字段
	if pddOrder.OrderPayTime > 0 {
		cpsOrder.PaySuccessTime = time.Unix(pddOrder.OrderPayTime, 0).Format("2006-01-02 15:04:05")
	}
	if pddOrder.OrderVerifyTime > 0 {
		cpsOrder.ConfirmTime = time.Unix(pddOrder.OrderVerifyTime, 0).Format("2006-01-02 15:04:05")
	}
	if pddOrder.OrderSettleTime > 0 {
		cpsOrder.SettleTime = time.Unix(pddOrder.OrderSettleTime, 0).Format("2006-01-02 15:04:05")
	}

	// 设置订单状态
	cpsOrder.FlowPoint = mapPddStatusToFlowPoint(pddOrder.OrderStatus)

	// 设置售后状态
	if pddOrder.OrderStatus == -1 {
		cpsOrder.AfterSalesStatus = 2 // 产生退款
		if pddOrder.OrderModifyAt > 0 {
			cpsOrder.RefundTime = time.Unix(pddOrder.OrderModifyAt, 0).Format("2006-01-02 15:04:05")
		}
	} else {
		cpsOrder.AfterSalesStatus = 1 // 空
	}

	// 设置外部信息
	cpsOrder.ExternalInfo = fmt.Sprintf("goods_quantity:%d,order_status_desc:%s", pddOrder.GoodsQuantity, pddOrder.OrderStatusDesc)

	// 设置显示状态
	cpsOrder.IsDisplay = 1

	// 设置分成状态
	cpsOrder.Status = 0 // 默认未分成

	// 设置技术服务费（预估）
	cpsOrder.EstimatedTechServiceFee = 0

	return cpsOrder
}

// mapVipOrderToCpsOrder 将VipOrder映射到CpsOrderModel
func mapVipOrderToCpsOrder(mapper OrderMapper) model.CpsOrderModel {
	vipMapper := mapper.(VipOrderMapper)
	vipOrder := vipMapper.Order
	var application model.Application
	source.DB().Preload("ApplicationLevel").Where("id = ?", vipOrder.AppID).First(&application)
	cpsOrder := model.CpsOrderModel{
		OrderId:       vipOrder.OrderSn,
		UserID:        uint(application.MemberId),
		ApplicationID: vipOrder.AppID,
		ThirdUserID:   vipOrder.AppUserID,
		ProductId:     vipOrder.OrderSn, // 唯品会没有单独的商品ID，使用订单号
		ProductName:   "唯品会订单",     // 唯品会订单可能包含多个商品
		Type:          "vip",
	}

	// 设置支付金额（转换为分）
	if totalCost, err := strconv.ParseFloat(vipOrder.TotalCost, 64); err == nil {
		cpsOrder.TotalPayAmount = int(totalCost * 100)
		cpsOrder.PayGoodsAmount = int(totalCost * 100)
	}

	// 设置佣金（转换为分）
	if commission, err := strconv.ParseFloat(vipOrder.Commission, 64); err == nil {
		cpsOrder.EstimatedCommission = int(commission * 100)
	}

	// 设置佣金比例（转换为万分比）
	if totalCost, err1 := strconv.ParseFloat(vipOrder.TotalCost, 64); err1 == nil {
		if commission, err2 := strconv.ParseFloat(vipOrder.Commission, 64); err2 == nil && totalCost > 0 {
			rate := commission / totalCost
			cpsOrder.SplitRate = int(rate * 10000)
		}
	}

	// 设置时间字段
	if vipOrder.OrderTime > 0 {
		cpsOrder.PaySuccessTime = time.Unix(vipOrder.OrderTime/1000, 0).Format("2006-01-02 15:04:05")
	}
	if vipOrder.SignTime > 0 {
		cpsOrder.ConfirmTime = time.Unix(vipOrder.SignTime/1000, 0).Format("2006-01-02 15:04:05")
	}
	if vipOrder.SettledTime > 0 {
		cpsOrder.SettleTime = time.Unix(vipOrder.SettledTime/1000, 0).Format("2006-01-02 15:04:05")
	}

	// 设置订单状态
	cpsOrder.FlowPoint = mapVipStatusToFlowPoint(vipOrder.Status)

	// 设置售后状态
	if vipOrder.Status == 0 {
		cpsOrder.AfterSalesStatus = 2 // 不合格，可能是退款
		if vipOrder.LastUpdateTime > 0 {
			cpsOrder.RefundTime = time.Unix(vipOrder.LastUpdateTime/1000, 0).Format("2006-01-02 15:04:05")
		}
	} else {
		cpsOrder.AfterSalesStatus = 1 // 空
	}

	// 设置外部信息
	cpsOrder.ExternalInfo = fmt.Sprintf("channel_tag:%s,new_customer:%d,self_buy:%d", vipOrder.ChannelTag, vipOrder.NewCustomer, vipOrder.SelfBuy)

	// 设置显示状态
	cpsOrder.IsDisplay = 1

	// 设置分成状态
	cpsOrder.Status = 0 // 默认未分成

	// 设置技术服务费（预估）
	cpsOrder.EstimatedTechServiceFee = 0

	return cpsOrder
}

// mapPddStatusToFlowPoint 将拼多多订单状态映射到流程状态
func mapPddStatusToFlowPoint(orderStatus int) string {
	switch orderStatus {
	case 0: // 已支付
		return "PAY_SUCC"
	case 1: // 已成团
		return "CONFIRM"
	case 2: // 确认收货
		return "CONFIRM"
	case 3: // 审核成功
		return "SETTLE"
	case 4: // 审核失败
		return "REFUND"
	case 5: // 已经结算
		return "SETTLE"
	case -1: // 未支付
		return "REFUND"
	default:
		return "PAY_SUCC" // 默认为支付完成
	}
}

// mapVipStatusToFlowPoint 将唯品会订单状态映射到流程状态
func mapVipStatusToFlowPoint(status int16) string {
	switch status {
	case 0: // 不合格
		return "REFUND"
	case 1: // 待定
		return "PAY_SUCC"
	case 2: // 已完结
		return "SETTLE"
	default:
		return "PAY_SUCC" // 默认为支付完成
	}
}
