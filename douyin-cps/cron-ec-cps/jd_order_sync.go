package cron

import (
	"douyin-cps/model"
	"douyin-cps/utils"
	"encoding/json"
	"go.uber.org/zap"
	"time"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

// 推送京东订单同步任务
func PushSyncJDOrderListHandle() {
	task := cron.Task{
		Key:  "syncJDOrderList",
		Name: "定时拉取京东订单列表",
		Spec: "0 */30 * * * *", // 每30分钟执行一次
		Handle: func(task cron.Task) {
			SyncJDOrderList()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)

	log.Log().Info("京东订单列表同步定时任务已启动", zap.String("执行规则", "每30分钟"))
}

// JDOrderListResponse 京东订单列表响应结构
type JDOrderListResponse struct {
	Code int `json:"code"`
	Data struct {
		List      []model.JDOrder `json:"list"`
		Total     int             `json:"total"`
		Page      int             `json:"page"`
		PageSize  int             `json:"page_size"`
		Statistic struct {
			Total      int `json:"total"`
			WaitSettle int `json:"wait_settle"`
			Settled    int `json:"settled"`
			Invalid    int `json:"invalid"`
		} `json:"statistic"`
		Status struct {
			Field1 string `json:"-1"`
			Field2 string `json:"0"`
			Field3 string `json:"1"`
			Field4 string `json:"2"`
			Field5 string `json:"3"`
			Field6 string `json:"4"`
			Field7 string `json:"5"`
			Field8 string `json:"8"`
		} `json:"status"`
	} `json:"data"`
	Msg string `json:"msg"`
}

// 同步京东订单列表
func SyncJDOrderList() {
	startTime := time.Now()
	log.Log().Info("开始拉取京东订单列表")

	// 获取API密钥
	err, setting := model.GetCpsSetting()
	if err != nil {
		log.Log().Error("获取API密钥失败", zap.Error(err))
		return
	}

	// 设置查询参数
	now := time.Now()
	queryStartTime := now.Add(-24 * time.Hour) // 查询过去24小时的订单

	// 构建请求参数
	requestParams := map[string]interface{}{
		"pageIndex": 1,
		"pageSize":  100,
		"startTime": queryStartTime.Format("2006-01-02 15:04:05"),
		"endTime":   now.Format("2006-01-02 15:04:05"),
	}

	// 发送请求
	err, result := utils.NewSelfAPIRequest(setting.EcCpsHost, setting.EcCpsAppKey, setting.EcCpsAppSecret).Execute("/app/ecCpsCtrl/jdOrder/list", requestParams)
	if err != nil {
		log.Log().Error("请求京东订单列表接口失败", zap.Error(err))
		return
	}

	// 解析响应
	var response JDOrderListResponse
	err = json.Unmarshal(result, &response)
	if err != nil {
		log.Log().Error("解析响应数据失败", zap.Error(err))
		return
	}

	// 检查响应状态
	if response.Code != 0 {
		log.Log().Error("接口返回错误", zap.String("错误信息", response.Msg))
		return
	}

	// 处理订单数据
	orders := response.Data.List
	if len(orders) == 0 {
		log.Log().Info("没有新的订单数据")
		return
	}

	log.Log().Info("获取到订单数据", zap.Int("数量", len(orders)))

	// 获取现有订单ID
	var existingOrders []model.JDOrder
	err = source.DB().Select("id, order_id").Find(&existingOrders).Error
	if err != nil {
		log.Log().Error("查询现有订单失败", zap.Error(err))
		return
	}

	// 将现有订单ID映射到map中便于快速查找
	existingOrderMap := make(map[int64]model.JDOrder)
	for _, order := range existingOrders {
		existingOrderMap[order.OrderId] = order
	}

	// 分离新订单和需要更新的订单
	var newOrders []model.JDOrder
	var updateOrders []model.JDOrder

	for _, order := range orders {
		if _, exists := existingOrderMap[order.OrderId]; !exists {
			// 新订单
			order.SyncTime = time.Now()
			order.ProcessStatus = 0
			newOrders = append(newOrders, order)
		} else {
			// 需要更新的订单
			dbOrder := existingOrderMap[order.OrderId]
			dbOrder.ValidCode = order.ValidCode
			dbOrder.SkuReturnNum = order.SkuReturnNum
			dbOrder.SkuFrozenNum = order.SkuFrozenNum
			dbOrder.FinishTime = order.FinishTime
			dbOrder.ModifyTime = order.ModifyTime
			dbOrder.EstimateCosPrice = order.EstimateCosPrice
			dbOrder.EstimateFee = order.EstimateFee
			dbOrder.ActualCosPrice = order.ActualCosPrice
			dbOrder.ActualFee = order.ActualFee
			dbOrder.PayMonth = order.PayMonth
			dbOrder.ExpressStatus = order.ExpressStatus
			dbOrder.SyncTime = time.Now()
			updateOrders = append(updateOrders, dbOrder)
		}
	}

	// 批量创建新订单
	if len(newOrders) > 0 {
		err = source.DB().CreateInBatches(&newOrders, 50).Error
		if err != nil {
			log.Log().Error("批量创建订单失败", zap.Error(err))
		} else {
			log.Log().Info("成功创建新订单", zap.Int("数量", len(newOrders)))
		}
	}

	// 批量更新订单
	if len(updateOrders) > 0 {
		for _, order := range updateOrders {
			err = source.DB().Model(&model.JDOrder{}).Where("order_id = ?", order.OrderId).Updates(map[string]interface{}{
				"valid_code":         order.ValidCode,
				"sku_return_num":     order.SkuReturnNum,
				"sku_frozen_num":     order.SkuFrozenNum,
				"finish_time":        order.FinishTime,
				"modify_time":        order.ModifyTime,
				"estimate_cos_price": order.EstimateCosPrice,
				"estimate_fee":       order.EstimateFee,
				"actual_cos_price":   order.ActualCosPrice,
				"actual_fee":         order.ActualFee,
				"pay_month":          order.PayMonth,
				"express_status":     order.ExpressStatus,
				"sync_time":          time.Now(),
			}).Error

			if err != nil {
				log.Log().Error("更新订单失败", zap.Int64("订单ID", order.OrderId), zap.Error(err))
			}
		}
		log.Log().Info("成功更新订单", zap.Int("数量", len(updateOrders)))
	}

	// 处理CpsOrderModel映射
	processJDCpsOrders(orders)

	elapsedTime := time.Since(startTime)
	log.Log().Info("京东订单同步完成", zap.Duration("耗时", elapsedTime))
}

// 处理订单数据
func processJDOrders(orders []model.JDOrder) {
	if len(orders) == 0 {
		return
	}

	// 获取现有订单ID
	var existingOrders []model.JDOrder
	err := source.DB().Select("id, order_id").Where("order_id IN ?", getOrderIds(orders)).Find(&existingOrders).Error
	if err != nil {
		log.Log().Error("查询现有订单失败", zap.Error(err))
		return
	}

	// 将现有订单ID映射到map中便于快速查找
	existingOrderMap := make(map[int64]model.JDOrder)
	for _, order := range existingOrders {
		existingOrderMap[order.OrderId] = order
	}

	// 分离新订单和需要更新的订单
	var newOrders []model.JDOrder
	var updateOrders []model.JDOrder

	for _, order := range orders {
		if _, exists := existingOrderMap[order.OrderId]; !exists {
			// 新订单
			order.SyncTime = time.Now()
			order.ProcessStatus = 0
			newOrders = append(newOrders, order)
		} else {
			// 需要更新的订单
			dbOrder := existingOrderMap[order.OrderId]
			dbOrder.ValidCode = order.ValidCode
			dbOrder.SkuReturnNum = order.SkuReturnNum
			dbOrder.SkuFrozenNum = order.SkuFrozenNum
			dbOrder.FinishTime = order.FinishTime
			dbOrder.ModifyTime = order.ModifyTime
			dbOrder.EstimateCosPrice = order.EstimateCosPrice
			dbOrder.EstimateFee = order.EstimateFee
			dbOrder.ActualCosPrice = order.ActualCosPrice
			dbOrder.ActualFee = order.ActualFee
			dbOrder.PayMonth = order.PayMonth
			dbOrder.ExpressStatus = order.ExpressStatus
			dbOrder.SyncTime = time.Now()
			updateOrders = append(updateOrders, dbOrder)
		}
	}

	// 批量创建新订单
	if len(newOrders) > 0 {
		err = source.DB().CreateInBatches(&newOrders, 50).Error
		if err != nil {
			log.Log().Error("批量创建订单失败", zap.Error(err))
		} else {
			log.Log().Info("成功创建新订单", zap.Int("数量", len(newOrders)))
		}
	}

	// 批量更新订单
	if len(updateOrders) > 0 {
		for _, order := range updateOrders {
			err = source.DB().Model(&model.JDOrder{}).Where("order_id = ?", order.OrderId).Updates(map[string]interface{}{
				"valid_code":         order.ValidCode,
				"sku_return_num":     order.SkuReturnNum,
				"sku_frozen_num":     order.SkuFrozenNum,
				"finish_time":        order.FinishTime,
				"modify_time":        order.ModifyTime,
				"estimate_cos_price": order.EstimateCosPrice,
				"estimate_fee":       order.EstimateFee,
				"actual_cos_price":   order.ActualCosPrice,
				"actual_fee":         order.ActualFee,
				"pay_month":          order.PayMonth,
				"express_status":     order.ExpressStatus,
				"sync_time":          time.Now(),
			}).Error

			if err != nil {
				log.Log().Error("更新订单失败", zap.Int64("订单ID", order.OrderId), zap.Error(err))
			}
		}
		log.Log().Info("成功更新订单", zap.Int("数量", len(updateOrders)))
	}
}

// 获取订单ID列表
func getOrderIds(orders []model.JDOrder) []int64 {
	var orderIds []int64
	for _, order := range orders {
		orderIds = append(orderIds, order.OrderId)
	}
	return orderIds
}

// processJDCpsOrders 处理京东订单的CpsOrderModel映射
func processJDCpsOrders(jdOrders []model.JDOrder) {
	// 转换为OrderMapper接口
	var mappers []OrderMapper
	for _, order := range jdOrders {
		mappers = append(mappers, JDOrderMapper{Order: order})
	}

	// 使用通用函数处理
	processCpsOrdersGeneric(mappers, mapJDOrderToCpsOrder, 50, 0, "jd")
}
