package cron

import (
	"douyin-cps/model"
	"douyin-cps/mq"
	incomeModel "finance/model"
	incomeService "finance/service"
	"strconv"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// 初始化订单分成定时任务
func InitOrderPercentageSync() {
	// 注册京东订单分成任务
	PushJDOrderPercentageHandle()

	// 注册拼多多订单分成任务
	PushPddOrderPercentageHandle()

	// 注册淘宝订单分成任务
	PushTaobaoOrderPercentageHandle()

	// 注册唯品会订单分成任务
	PushVipOrderPercentageHandle()
}

// 京东订单分成定时任务
func PushJDOrderPercentageHandle() {
	task := cron.Task{
		Key:  "jdOrderPercentageCps",
		Name: "定时执行京东订单分成操作Cps",
		Spec: "0 15 */2 * * *", // 每2小时的15分执行
		Handle: func(task cron.Task) {
			JDOrderPercentageHandle()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

// 拼多多订单分成定时任务
func PushPddOrderPercentageHandle() {
	task := cron.Task{
		Key:  "pddOrderPercentageCps",
		Name: "定时执行拼多多订单分成操作Cps",
		Spec: "0 25 */2 * * *", // 每2小时的25分执行
		Handle: func(task cron.Task) {
			PddOrderPercentageHandle()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

// 淘宝订单分成定时任务
func PushTaobaoOrderPercentageHandle() {
	task := cron.Task{
		Key:  "taobaoOrderPercentageCps",
		Name: "定时执行淘宝订单分成操作Cps",
		Spec: "0 35 */2 * * *", // 每2小时的35分执行
		Handle: func(task cron.Task) {
			TaobaoOrderPercentageHandle()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

// 唯品会订单分成定时任务
func PushVipOrderPercentageHandle() {
	task := cron.Task{
		Key:  "vipOrderPercentageCps",
		Name: "定时执行唯品会订单分成操作Cps",
		Spec: "0 45 */2 * * *", // 每2小时的45分执行
		Handle: func(task cron.Task) {
			VipOrderPercentageHandle()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

// 京东订单分成处理
func JDOrderPercentageHandle() {
	var waitPercentOrders []model.JDOrderModel
	// 查询未处理且已结算的订单
	err := source.DB().Preload("Application.User.UserLevel").Where("process_status = 1 and app_process_status = 0").Where("valid_code = 17").Limit(1000).Find(&waitPercentOrders).Error
	if err != nil {
		log.Log().Error("查询京东待分成订单失败", zap.Error(err))
		return
	}

	for _, order := range waitPercentOrders {
		log.Log().Info("京东订单分成结算,订单ID[" + strconv.Itoa(int(order.OrderId)) + "]")

		// 计算分成金额（实际佣金的一定比例）
		awardAmount := int(order.ActualFee*100) * order.Application.User.UserLevelInfo.CpsRatio / 10000 // 假设分成比例为70%

		err = source.DB().Transaction(func(tx *gorm.DB) error {
			// 更新订单处理状态
			if err := tx.Model(&order).Update("app_process_status", 1).Error; err != nil {
				log.Log().Error("更新京东订单状态失败", zap.Error(err))
				return err
			}

			// 增加用户收入
			var income incomeModel.UserIncomeDetails
			income.UserID = int(order.Application.MemberId)
			income.OrderSn = int(order.OrderId)
			income.IncomeType = incomeModel.Cps
			income.Amount = uint(awardAmount)
			err = incomeService.IncreaseIncome(income)
			if err != nil {
				log.Log().Error("增加收入失败", zap.Error(err))
				return err
			}
			var ecCpsOrder model.CpsOrderModel
			ecCpsOrder.AwardAmount = awardAmount
			ecCpsOrder.AwardRatio = order.Application.User.UserLevelInfo.CpsRatio
			err = tx.Model(&model.CpsOrderModel{}).Where("order_id = ?", order.OrderId).Updates(&ecCpsOrder).Error
			if err != nil {
				return err
			}
			//执行分销
			err = mq.PublishMessage(strconv.Itoa(int(order.OrderId)), 0, mq.Settle, 0, mq.Jd)
			if err != nil {
				log.Log().Info("cps分销失败,返回")
				return err
			}
			return err
		})

		if err != nil {
			log.Log().Error("京东订单分成结算失败", zap.Error(err))
			continue
		}

		log.Log().Info("京东订单分成结算成功,订单ID[" + strconv.Itoa(int(order.OrderId)) + "]")
	}
}

// 拼多多订单分成处理
func PddOrderPercentageHandle() {
	var waitPercentOrders []model.PddOrderModel
	// 查询未处理且已结算的订单（订单状态5表示已结算）
	err := source.DB().Preload("Application.User.UserLevel").Where("process_status = 1 and app_process_status = 0").Where("order_status = 5").Limit(1000).Find(&waitPercentOrders).Error
	if err != nil {
		log.Log().Error("查询拼多多待分成订单失败", zap.Error(err))
		return
	}

	for _, order := range waitPercentOrders {
		if order.Application.User.ID == 0 || order.Application.User.UserLevelInfo.ID == 0 {
			continue
		}
		log.Log().Info("拼多多订单分成结算,订单ID[" + order.OrderSn + "]")

		// 计算分成金额（佣金的一定比例）
		awardAmount := int(order.PromotionAmount*100) * order.Application.User.UserLevelInfo.CpsRatio / 10000 // 假设分成比例为70%

		err = source.DB().Transaction(func(tx *gorm.DB) error {
			// 更新订单处理状态
			if err := tx.Model(&order).Update("app_process_status", 1).Error; err != nil {
				log.Log().Error("更新拼多多订单状态失败", zap.Error(err))
				return err
			}

			// 增加用户收入
			var income incomeModel.UserIncomeDetails
			income.UserID = int(order.Application.MemberId)
			income.OrderSn = int(order.ID)
			income.IncomeType = incomeModel.Cps
			income.Amount = uint(awardAmount)
			err = incomeService.IncreaseIncome(income)
			if err != nil {
				log.Log().Error("增加收入失败", zap.Error(err))
				return err
			}
			var ecCpsOrder model.CpsOrderModel
			ecCpsOrder.AwardAmount = awardAmount
			ecCpsOrder.AwardRatio = order.Application.User.UserLevelInfo.CpsRatio
			err = tx.Model(&model.CpsOrderModel{}).Where("order_id = ?", order.OrderSn).Updates(&ecCpsOrder).Error
			if err != nil {
				return err
			}
			//执行分销
			err = mq.PublishMessage(order.OrderSn, 0, mq.Settle, 0, mq.Jd)
			if err != nil {
				log.Log().Info("cps分销失败,返回")
				return err
			}
			return err
		})

		if err != nil {
			log.Log().Error("拼多多订单分成结算失败", zap.Error(err))
			continue
		}

		log.Log().Info("拼多多订单分成结算成功,订单ID[" + order.OrderSn + "]")
	}
}

// 淘宝订单分成处理
func TaobaoOrderPercentageHandle() {
	var waitPercentOrders []model.SupplyTaobaoOrderModel
	// 查询未处理且已结算的订单（tk_status=3表示已结算）
	err := source.DB().Preload("Application.User.UserLevel").Where("process_status = 1 and app_process_status = 0").Where("tk_status = 3").Limit(1000).Find(&waitPercentOrders).Error
	if err != nil {
		log.Log().Error("查询淘宝待分成订单失败", zap.Error(err))
		return
	}

	for _, order := range waitPercentOrders {
		log.Log().Info("淘宝订单分成结算,订单ID[" + order.TradeId + "]")

		// 解析佣金金额并计算分成
		awardAmount := int(order.PubShareCommission*100) * order.Application.User.UserLevelInfo.CpsRatio / 10000 // 假设分成比例为70%

		err = source.DB().Transaction(func(tx *gorm.DB) error {
			// 更新订单处理状态
			if err := tx.Model(&order).Update("app_process_status", 1).Error; err != nil {
				log.Log().Error("更新淘宝订单状态失败", zap.Error(err))
				return err
			}

			// 增加用户收入
			var income incomeModel.UserIncomeDetails
			income.UserID = int(order.Application.MemberId)
			income.OrderSn = int(order.ID)
			income.IncomeType = incomeModel.Cps
			income.Amount = uint(awardAmount)
			err = incomeService.IncreaseIncome(income)
			if err != nil {
				log.Log().Error("增加收入失败", zap.Error(err))
				return err
			}

			var ecCpsOrder model.CpsOrderModel
			ecCpsOrder.AwardAmount = awardAmount
			ecCpsOrder.AwardRatio = order.Application.User.UserLevelInfo.CpsRatio
			err = tx.Model(&model.CpsOrderModel{}).Where("order_id = ?", order.TradeId).Updates(&ecCpsOrder).Error
			if err != nil {
				return err
			}
			//执行分销
			err = mq.PublishMessage(order.TradeId, 0, mq.Settle, 0, mq.Taobao)
			if err != nil {
				log.Log().Info("cps分销失败,返回")
				return err
			}
			return err
		})

		if err != nil {
			log.Log().Error("淘宝订单分成结算失败", zap.Error(err))
			continue
		}

		log.Log().Info("淘宝订单分成结算成功,订单ID[" + order.TradeId + "]")
	}
}

// 唯品会订单分成处理
func VipOrderPercentageHandle() {
	var waitPercentOrders []model.VipOrderModel
	// 查询未处理且已结算的订单（settled=1表示已结算）
	err := source.DB().Preload("Application.User.UserLevel").Where("process_status = 1 and app_process_status = 0").Where("settled = 1").Limit(1000).Find(&waitPercentOrders).Error
	if err != nil {
		log.Log().Error("查询唯品会待分成订单失败", zap.Error(err))
		return
	}

	for _, order := range waitPercentOrders {
		log.Log().Info("唯品会订单分成结算,订单ID[" + order.OrderSn + "]")

		// 解析佣金金额并计算分成
		awardAmount := int(order.ActualCommission*100) * order.Application.User.UserLevelInfo.CpsRatio / 10000 // 假设分成比例为70%

		err = source.DB().Transaction(func(tx *gorm.DB) error {
			// 更新订单处理状态
			if err := tx.Model(&order).Update("app_process_status", 1).Error; err != nil {
				log.Log().Error("更新唯品会订单状态失败", zap.Error(err))
				return err
			}

			// 增加用户收入
			var income incomeModel.UserIncomeDetails
			income.UserID = int(order.Application.MemberId)
			income.OrderSn = int(order.ID)
			income.IncomeType = incomeModel.Cps
			income.Amount = uint(awardAmount)
			err = incomeService.IncreaseIncome(income)
			if err != nil {
				log.Log().Error("增加收入失败", zap.Error(err))
				return err
			}
			var ecCpsOrder model.CpsOrderModel
			ecCpsOrder.AwardAmount = awardAmount
			ecCpsOrder.AwardRatio = order.Application.User.UserLevelInfo.CpsRatio
			err = tx.Model(&model.CpsOrderModel{}).Where("order_id = ?", order.OrderSn).Updates(&ecCpsOrder).Error
			if err != nil {
				return err
			}
			//执行分销
			err = mq.PublishMessage(order.OrderSn, 0, mq.Settle, 0, mq.Vip)
			if err != nil {
				log.Log().Info("cps分销失败,返回")
				return err
			}
			return err
		})

		if err != nil {
			log.Log().Error("唯品会订单分成结算失败", zap.Error(err))
			continue
		}

		log.Log().Info("唯品会订单分成结算成功,订单ID[" + order.OrderSn + "]")
	}
}
