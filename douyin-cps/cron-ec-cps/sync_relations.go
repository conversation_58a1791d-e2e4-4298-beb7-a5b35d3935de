package cron

import (
	"douyin-cps/model"
	"douyin-cps/utils"
	"encoding/json"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"time"
	"yz-go/cron"
	"yz-go/source"
)

func PushSyncCpsRelationHandle() {
	task := cron.Task{
		Key:  "syncCpsRelation",
		Name: "定时同步relation",
		Spec: "10 */3 * * * *",
		Handle: func(task cron.Task) {
			SyncRelation()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func SyncRelation() {
	err, setting := model.GetCpsSetting()
	if err != nil {
		return
	}

	// 获取上次同步时间
	var lastSyncTime time.Time
	var lastSync model.SyncRecord
	err = source.DB().Where("sync_type = ?", "relation").Order("sync_time DESC").First(&lastSync).Error
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		// 如果没有找到记录，设置为24小时前
		lastSyncTime = time.Now().Add(-24 * time.Hour)
	} else {
		lastSyncTime = lastSync.SyncTime
	}

	// 设置分页参数
	page := 1
	pageSize := 100
	hasMore := true
	endTime := time.Now()

	for hasMore {
		// 定义一个结束时间变量
		// 构建请求参数
		params := map[string]interface{}{
			"page":       page,
			"page_size":  pageSize,
			"start_time": lastSyncTime.Format("2006-01-02 15:04:05"),
			"end_time":   endTime.Format("2006-01-02 15:04:05"),
		}

		// 发送请求
		err, result := utils.NewSelfAPIRequest(setting.EcCpsHost, setting.EcCpsAppKey, setting.EcCpsAppSecret).Execute("/app/ecCpsCtrl/openTb/getRelations", params)
		if err != nil {
			return
		}

		var resultData struct {
			Data struct {
				List  []model.ApplicationOpenTbRelation `json:"list"`
				Total int                               `json:"total"`
				Page  int                               `json:"page"`
			} `json:"data"`
			Code int    `json:"code"`
			Msg  string `json:"msg"`
		}

		err = json.Unmarshal(result, &resultData)
		if err != nil {
			return
		}

		if resultData.Code != 0 {
			err = errors.New(resultData.Msg)
			return
		}

		// 处理获取到的数据
		if len(resultData.Data.List) > 0 {
			// 使用事务处理数据
			err = source.DB().Transaction(func(tx *gorm.DB) error {
				// 收集新记录和需要更新的记录
				var newRelations []model.ApplicationOpenTbRelation
				var updateMap = make(map[uint]model.ApplicationOpenTbRelation)

				// 先获取所有可能存在的记录
				var appIds []uint
				var appUserIds []uint
				for _, relation := range resultData.Data.List {
					appIds = append(appIds, relation.AppID)
					appUserIds = append(appUserIds, relation.AppUserID)
				}

				// 批量查询已存在的记录
				var existingRelations []model.ApplicationOpenTbRelation
				err := tx.Where("app_id IN ? AND app_user_id IN ?", appIds, appUserIds).Find(&existingRelations).Error
				if err != nil {
					return err
				}

				// 创建查找映射
				existingMap := make(map[string]model.ApplicationOpenTbRelation)
				for _, existing := range existingRelations {
					key := fmt.Sprintf("%d_%d", existing.AppID, existing.AppUserID)
					existingMap[key] = existing
				}

				// 分类为新增或更新
				for _, relation := range resultData.Data.List {
					key := fmt.Sprintf("%d_%d", relation.AppID, relation.AppUserID)
					if existing, found := existingMap[key]; found {
						// 存在，标记为更新
						relation.ID = existing.ID
						updateMap[existing.ID] = relation
					} else {
						// 不存在，添加到新记录列表
						newRelations = append(newRelations, relation)
					}
				}

				// 批量创建新记录
				if len(newRelations) > 0 {
					if err := tx.CreateInBatches(newRelations, 100).Error; err != nil {
						return err
					}
				}

				// 批量更新现有记录
				//for _, relation := range updateMap {
				//	if err := tx.Model(&model.ApplicationOpenTbRelation{}).Where("id = ?", relation.ID).Updates(relation).Error; err != nil {
				//		return err
				//	}
				//}
				return nil
			})

			if err != nil {
				return
			}
		}

		// 判断是否还有更多数据
		hasMore = len(resultData.Data.List) == pageSize
		page++
	}

	// 更新同步记录
	syncRecord := model.SyncRecord{
		SyncType: "relation",
		SyncTime: endTime,
	}
	err = source.DB().Create(&syncRecord).Error
	if err != nil {
		return
	}

	return
}
