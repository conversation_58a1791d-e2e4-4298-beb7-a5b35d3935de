package cron

import (
	"douyin-cps/model"
	"douyin-cps/utils"
	"encoding/json"
	"go.uber.org/zap"
	"time"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

// 推送淘宝订单同步任务
func PushSyncTaobaoOrderListHandle() {
	task := cron.Task{
		Key:  "syncTaobaoOrderList",
		Name: "定时拉取淘宝订单列表",
		Spec: "0 */30 * * * *", // 每30分钟执行一次
		Handle: func(task cron.Task) {
			SyncTaobaoOrderList()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)

	log.Log().Info("淘宝订单列表同步定时任务已启动", zap.String("执行规则", "每30分钟"))
}

// 淘宝订单列表响应结构
type TaobaoOrderListResponse struct {
	Code int `json:"code"`
	Data struct {
		List      []model.SupplyTaobaoOrder `json:"list"`
		Total     int                       `json:"total"`
		Page      int                       `json:"page"`
		PageSize  int                       `json:"page_size"`
		Statistic struct {
			Total      int `json:"total"`
			WaitSettle int `json:"wait_settle"`
			Settled    int `json:"settled"`
			Invalid    int `json:"invalid"`
		} `json:"statistic"`
		Status struct {
			Field1 string `json:"-1"`
			Field2 string `json:"0"`
			Field3 string `json:"1"`
			Field4 string `json:"2"`
			Field5 string `json:"3"`
			Field6 string `json:"4"`
			Field7 string `json:"5"`
			Field8 string `json:"8"`
		} `json:"status"`
	} `json:"data"`
	Msg string `json:"msg"`
}

// 同步淘宝订单列表
func SyncTaobaoOrderList() {
	startTime := time.Now()
	log.Log().Info("开始拉取淘宝订单列表")

	// 获取API密钥
	err, setting := model.GetCpsSetting()
	if err != nil {
		log.Log().Error("获取API密钥失败", zap.Error(err))
		return
	}

	// 设置查询参数
	now := time.Now()
	queryStartTime := now.Add(-24 * time.Hour) // 查询过去24小时的订单

	// 构建请求参数
	requestParams := map[string]interface{}{
		"page":       1,
		"page_size":  100,
		"start_time": queryStartTime.Format("2006-01-02 15:04:05"),
		"end_time":   now.Format("2006-01-02 15:04:05"),
	}

	// 发送请求
	err, result := utils.NewSelfAPIRequest(setting.EcCpsHost, setting.EcCpsAppKey, setting.EcCpsAppSecret).Execute("/app/ecCpsCtrl/taobaoOrder/list", requestParams)
	if err != nil {
		log.Log().Error("请求淘宝订单列表接口失败", zap.Error(err))
		return
	}

	// 解析响应
	var response TaobaoOrderListResponse
	err = json.Unmarshal(result, &response)
	if err != nil {
		log.Log().Error("解析响应数据失败", zap.Error(err))
		return
	}

	// 检查响应状态
	if response.Code != 0 {
		log.Log().Error("接口返回错误", zap.String("错误信息", response.Msg))
		return
	}

	// 处理订单数据
	orders := response.Data.List
	if len(orders) == 0 {
		log.Log().Info("没有新的订单数据")
		return
	}

	log.Log().Info("获取到订单数据", zap.Int("数量", len(orders)))

	// 获取现有订单ID
	var existingOrders []model.SupplyTaobaoOrder
	err = source.DB().Select("id, trade_id").Find(&existingOrders).Error
	if err != nil {
		log.Log().Error("查询现有订单失败", zap.Error(err))
		return
	}

	// 创建订单ID映射
	existingOrderMap := make(map[string]model.SupplyTaobaoOrder)
	for _, order := range existingOrders {
		existingOrderMap[order.TradeId] = order
	}

	// 分离新订单和需要更新的订单
	var newOrders []model.SupplyTaobaoOrder
	var updateOrders []model.SupplyTaobaoOrder

	for _, order := range orders {
		if _, exists := existingOrderMap[order.TradeId]; !exists {
			// 新订单
			order.SyncTime = time.Now()
			newOrders = append(newOrders, order)
		} else {
			// 需要更新的订单
			dbOrder := existingOrderMap[order.TradeId]
			dbOrder.TkStatus = order.TkStatus
			dbOrder.RefundTag = order.RefundTag
			dbOrder.TkEarningTime = order.TkEarningTime
			dbOrder.PubShareFee = order.PubShareFee
			dbOrder.PubShareCommission = order.PubShareCommission
			dbOrder.SyncTime = time.Now()
			updateOrders = append(updateOrders, dbOrder)
		}
	}

	// 批量创建新订单
	if len(newOrders) > 0 {
		err = source.DB().CreateInBatches(&newOrders, 50).Error
		if err != nil {
			log.Log().Error("批量创建订单失败", zap.Error(err))
		} else {
			log.Log().Info("成功创建新订单", zap.Int("数量", len(newOrders)))
		}
	}

	// 批量更新订单
	if len(updateOrders) > 0 {
		for _, order := range updateOrders {
			err = source.DB().Model(&model.SupplyTaobaoOrder{}).Where("trade_id = ?", order.TradeId).Updates(map[string]interface{}{
				"tk_status":            order.TkStatus,
				"refund_tag":           order.RefundTag,
				"tk_earning_time":      order.TkEarningTime,
				"pub_share_fee":        order.PubShareFee,
				"pub_share_commission": order.PubShareCommission,
				"sync_time":            time.Now(),
			}).Error

			if err != nil {
				log.Log().Error("更新订单失败", zap.String("订单ID", order.TradeId), zap.Error(err))
			}
		}
		log.Log().Info("成功更新订单", zap.Int("数量", len(updateOrders)))
	}

	// 处理CpsOrderModel映射
	processTaobaoCpsOrders(orders)

	elapsedTime := time.Since(startTime)
	log.Log().Info("淘宝订单列表同步完成",
		zap.Int("新增订单", len(newOrders)),
		zap.Int("更新订单", len(updateOrders)),
		zap.Duration("耗时", elapsedTime))
}

// processTaobaoCpsOrders 处理淘宝订单的CpsOrderModel映射
func processTaobaoCpsOrders(taobaoOrders []model.SupplyTaobaoOrder) {
	// 转换为OrderMapper接口
	var mappers []OrderMapper
	for _, order := range taobaoOrders {
		mappers = append(mappers, TaobaoOrderMapper{Order: order})
	}

	// 使用通用函数处理
	processCpsOrdersGeneric(mappers, mapTaobaoOrderToCpsOrder, 50, 0, "taobao")
}
