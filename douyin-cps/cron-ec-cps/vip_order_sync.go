package cron

import (
	"douyin-cps/model"
	"douyin-cps/utils"
	"encoding/json"
	"go.uber.org/zap"
	"time"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

// 推送唯品会订单同步任务
func PushSyncVipOrderListHandle() {
	task := cron.Task{
		Key:  "syncVipOrderList",
		Name: "定时拉取唯品会订单列表",
		Spec: "0 */30 * * * *", // 每30分钟执行一次
		Handle: func(task cron.Task) {
			SyncVipOrderList()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)

	log.Log().Info("唯品会订单列表同步定时任务已启动", zap.String("执行规则", "每30分钟"))
}

// 同步唯品会订单列表
func SyncVipOrderList() {
	startTime := time.Now()
	log.Log().Info("开始拉取唯品会订单列表")

	// 获取API密钥
	err, setting := model.GetCpsSetting()
	if err != nil {
		log.Log().Error("获取API密钥失败", zap.Error(err))
		return
	}

	// 设置查询参数
	now := time.Now()
	queryStartTime := now.Add(-24 * time.Hour) // 查询过去24小时的订单

	// 构建请求参数
	requestParams := map[string]interface{}{
		"page":       1,
		"page_size":  100,
		"start_time": queryStartTime.Format("2006-01-02 15:04:05"),
		"end_time":   now.Format("2006-01-02 15:04:05"),
	}

	// 发送请求
	err, result := utils.NewSelfAPIRequest(setting.EcCpsHost, setting.EcCpsAppKey, setting.EcCpsAppSecret).Execute("/app/ecCpsCtrl/vipOrder/list", requestParams)
	if err != nil {
		log.Log().Error("请求唯品会订单列表接口失败", zap.Error(err))
		return
	}

	// 解析响应
	var response model.VipOrderListResponse
	err = json.Unmarshal(result, &response)
	if err != nil {
		log.Log().Error("解析响应数据失败", zap.Error(err))
		return
	}

	// 检查响应状态
	if response.Code != 0 {
		log.Log().Error("接口返回错误", zap.String("错误信息", response.Msg))
		return
	}

	// 处理订单数据
	orders := response.Data.List
	if len(orders) == 0 {
		log.Log().Info("没有新的订单数据")
		return
	}

	log.Log().Info("获取到订单数据", zap.Int("数量", len(orders)))

	// 处理订单数据
	processVipOrders(orders)

	// 处理CpsOrderModel映射
	processVipCpsOrders(orders)

	elapsedTime := time.Since(startTime)
	log.Log().Info("唯品会订单同步完成", zap.Duration("耗时", elapsedTime))
}

// 处理唯品会订单数据
func processVipOrders(orders []model.VipOrder) {
	if len(orders) == 0 {
		return
	}

	// 获取现有订单ID
	var existingOrders []model.VipOrder
	err := source.DB().Select("id, order_sn").Where("order_sn IN ?", getVipOrderSns(orders)).Find(&existingOrders).Error
	if err != nil {
		log.Log().Error("查询现有订单失败", zap.Error(err))
		return
	}

	// 将现有订单ID映射到map中便于快速查找
	existingOrderMap := make(map[string]model.VipOrder)
	for _, order := range existingOrders {
		existingOrderMap[order.OrderSn] = order
	}

	// 分离新订单和需要更新的订单
	var newOrders []model.VipOrder
	var updateOrders []model.VipOrder

	for _, order := range orders {
		// 解析StatParam获取AppID、ParentAppID和AppUserID

		if _, exists := existingOrderMap[order.OrderSn]; !exists {
			// 新订单
			order.SyncTime = time.Now()
			newOrders = append(newOrders, order)
		} else {
			// 需要更新的订单
			dbOrder := existingOrderMap[order.OrderSn]
			dbOrder.Status = order.Status
			dbOrder.NewCustomer = order.NewCustomer
			dbOrder.SignTime = order.SignTime
			dbOrder.SettledTime = order.SettledTime
			dbOrder.LastUpdateTime = order.LastUpdateTime
			dbOrder.Settled = order.Settled
			dbOrder.OrderSubStatusName = order.OrderSubStatusName
			dbOrder.Commission = order.Commission
			dbOrder.ActualCommission = order.ActualCommission
			dbOrder.AfterSaleChangeCommission = order.AfterSaleChangeCommission
			dbOrder.AfterSaleChangeGoodsCount = order.AfterSaleChangeGoodsCount
			dbOrder.CommissionEnterTime = order.CommissionEnterTime
			dbOrder.AppID = order.AppID
			dbOrder.AppUserID = order.AppUserID
			dbOrder.SyncTime = time.Now()
			updateOrders = append(updateOrders, dbOrder)
		}
	}

	// 批量创建新订单
	if len(newOrders) > 0 {
		err = source.DB().CreateInBatches(&newOrders, 50).Error
		if err != nil {
			log.Log().Error("批量创建订单失败", zap.Error(err))
		} else {
			log.Log().Info("成功创建新订单", zap.Int("数量", len(newOrders)))
		}
	}

	// 批量更新订单
	if len(updateOrders) > 0 {
		for _, order := range updateOrders {
			err = source.DB().Model(&model.VipOrder{}).Where("order_sn = ?", order.OrderSn).Updates(map[string]interface{}{
				"status":                        order.Status,
				"new_customer":                  order.NewCustomer,
				"sign_time":                     order.SignTime,
				"settled_time":                  order.SettledTime,
				"last_update_time":              order.LastUpdateTime,
				"settled":                       order.Settled,
				"order_sub_status_name":         order.OrderSubStatusName,
				"commission":                    order.Commission,
				"actual_commission":             order.ActualCommission,
				"after_sale_change_commission":  order.AfterSaleChangeCommission,
				"after_sale_change_goods_count": order.AfterSaleChangeGoodsCount,
				"commission_enter_time":         order.CommissionEnterTime,
				"app_id":                        order.AppID,
				"app_user_id":                   order.AppUserID,
				"sync_time":                     time.Now(),
			}).Error

			if err != nil {
				log.Log().Error("更新订单失败", zap.String("订单ID", order.OrderSn), zap.Error(err))
			}
		}
		log.Log().Info("成功更新订单", zap.Int("数量", len(updateOrders)))
	}
}

// 获取唯品会订单号列表
func getVipOrderSns(orders []model.VipOrder) []string {
	var orderSns []string
	for _, order := range orders {
		orderSns = append(orderSns, order.OrderSn)
	}
	return orderSns
}

// processVipCpsOrders 处理唯品会订单的CpsOrderModel映射
func processVipCpsOrders(vipOrders []model.VipOrder) {
	// 转换为OrderMapper接口
	var mappers []OrderMapper
	for _, order := range vipOrders {
		mappers = append(mappers, VipOrderMapper{Order: order})
	}

	// 使用通用函数处理
	processCpsOrdersGeneric(mappers, mapVipOrderToCpsOrder, 50, 0, "vip")
}
