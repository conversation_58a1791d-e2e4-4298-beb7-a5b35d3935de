package cron

import (
	"douyin-cps/model"
	"douyin-cps/mq"
	"douyin-cps/service"
	incomeModel "finance/model"
	incomeService "finance/service"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"strconv"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func PushCpsPercentageHandle() {
	task := cron.Task{
		Key:  "cpsPercentage",
		Name: "定时执行分成操作",
		Spec: "0 35 */1 * * *",
		Handle: func(task cron.Task) {
			PercentageHandle()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func PercentageHandle() {
	var waitPercentOrders []model.CpsOrder
	err := source.DB().Preload("User.UserLevelInfo").Where("status = 0").Where("flow_point = 'SETTLE'").Limit(1000).Find(&waitPercentOrders).Error
	if err != nil {
		return
	}

	for _, order := range waitPercentOrders {
		if order.User.ID == 0 || order.User.UserLevelInfo.ID == 0 {
			continue
		}
		log.Log().Info("cps分成结算,奖励id[" + strconv.Itoa(int(order.ID)) + "]")
		err = source.DB().Transaction(func(tx *gorm.DB) error {
			// 奖励结算
			err = service.SettleAwardByTx(tx, order)
			if err != nil {
				log.Log().Info("结算失败,返回")
				log.Log().Error(err.Error(), zap.Any("err", err))
				return err
			}

			// 增加收入
			var income incomeModel.UserIncomeDetails
			income.UserID = int(order.UserID)
			income.OrderSn = int(order.OrderSN)
			income.IncomeType = incomeModel.Cps
			income.Amount = uint(order.AwardAmount)
			err = incomeService.IncreaseIncome(income)
			if err != nil {
				log.Log().Info("增加收入失败,返回")
				log.Log().Error(err.Error(), zap.Any("err", err))
				return err
			}
			//执行分销
			err = mq.PublishMessage(order.ID, mq.Settle, 0)
			if err != nil {
				log.Log().Info("cps分销失败,返回")
				return err
			}
			return err
		})
		if err != nil {
			log.Log().Info("结算失败,返回")
			log.Log().Error(err.Error(), zap.Any("err", err))
			return
		}

		log.Log().Info("cps分成结算,成功")
	}
	return
}
