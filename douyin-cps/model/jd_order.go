package model

import (
	"time"
	"yz-go/source"
)

// JDOrder 京东订单模型
type JDOrder struct {
	source.Model
	// 基础订单信息
	ID            string `json:"id" gorm:"column:id;uniqueIndex"`            // 标记唯一订单行
	OrderId       int64  `json:"orderId" gorm:"column:order_id;index"`       // 订单号
	ParentId      int64  `json:"parentId" gorm:"column:parent_id"`           // 父单的订单ID，仅当发生订单拆分时返回，0：未拆分
	OrderTime     string `json:"orderTime" gorm:"column:order_time"`         // 下单时间,格式yyyy-MM-dd HH:mm:ss
	FinishTime    string `json:"finishTime" gorm:"column:finish_time"`       // 完成时间（购买用户确认收货时间）
	ModifyTime    string `json:"modifyTime" gorm:"column:modify_time;index"` // 更新时间,格式yyyy-MM-dd HH:mm:ss
	OrderEmt      int    `json:"orderEmt" gorm:"column:order_emt"`           // 下单设备 1.pc 2.无线
	Plus          int    `json:"plus" gorm:"column:plus"`                    // plus会员 1:是，0:否
	ExpressStatus int    `json:"expressStatus" gorm:"column:express_status"` // 发货状态（10：待发货，20：已发货）

	// 推客信息
	UnionId    int64  `json:"unionId" gorm:"column:union_id;index"`  // 推客ID
	SubUnionId string `json:"subUnionId" gorm:"column:sub_union_id"` // 子联盟ID(需要联系运营开放白名单才能拿到数据)
	UnionAlias string `json:"unionAlias" gorm:"column:union_alias"`  // 母账号简称
	UnionRole  int    `json:"unionRole" gorm:"column:union_role"`    // 站长角色：1 推客 2 团长
	UnionTag   string `json:"unionTag" gorm:"column:union_tag"`      // 联盟标签数据
	Pid        string `json:"pid" gorm:"column:pid"`                 // 格式:子推客ID_子站长应用ID_子推客推广位ID
	PositionId int64  `json:"positionId" gorm:"column:position_id"`  // 推广位ID
	SiteId     int64  `json:"siteId" gorm:"column:site_id"`          // 应用id（网站id、appid、社交媒体id）
	Ext1       string `json:"ext1" gorm:"column:ext1"`               // 推客生成推广链接时传入的扩展字段

	// 商品信息
	SkuId        int64   `json:"skuId" gorm:"column:sku_id;index"`          // 商品ID
	SkuName      string  `json:"skuName" gorm:"column:sku_name"`            // 商品名称
	SkuNum       int     `json:"skuNum" gorm:"column:sku_num"`              // 商品数量
	SkuReturnNum int     `json:"skuReturnNum" gorm:"column:sku_return_num"` // 商品已退货数量
	SkuFrozenNum int     `json:"skuFrozenNum" gorm:"column:sku_frozen_num"` // 商品售后中数量
	Price        float64 `json:"price" gorm:"column:price"`                 // 商品单价
	PopId        int64   `json:"popId" gorm:"column:pop_id"`                // 商家ID

	// 类目信息
	Cid1 int64 `json:"cid1" gorm:"column:cid1"` // 一级类目id
	Cid2 int64 `json:"cid2" gorm:"column:cid2"` // 二级类目id
	Cid3 int64 `json:"cid3" gorm:"column:cid3"` // 三级类目id

	// 佣金信息
	CommissionRate   float64 `json:"commissionRate" gorm:"column:commission_rate"`      // 佣金比例(投放的广告主计划比例)
	SubSideRate      float64 `json:"subSideRate" gorm:"column:sub_side_rate"`           // 一级分成比例
	SubsidyRate      float64 `json:"subsidyRate" gorm:"column:subsidy_rate"`            // 一级补贴比例
	FinalRate        float64 `json:"finalRate" gorm:"column:final_rate"`                // 最终比例( (一级分佣比例+一级分成比例)*二级分佣比例)
	EstimateCosPrice float64 `json:"estimateCosPrice" gorm:"column:estimate_cos_price"` // 预估计佣金额
	EstimateFee      float64 `json:"estimateFee" gorm:"column:estimate_fee"`            // 推客的预估佣金
	ActualCosPrice   float64 `json:"actualCosPrice" gorm:"column:actual_cos_price"`     // 实际计算佣金的金额
	ActualFee        float64 `json:"actualFee" gorm:"column:actual_fee"`                // 推客分得的实际佣金

	// 订单状态
	ValidCode int `json:"validCode" gorm:"column:valid_code;index"` // sku维度的有效码
	TraceType int `json:"traceType" gorm:"column:trace_type"`       // 同跨店：2同店 3跨店
	PayMonth  int `json:"payMonth" gorm:"column:pay_month"`         // 预估结算时间 格式：yyyyMMdd

	// 活动信息
	CpActId             int64   `json:"cpActId" gorm:"column:cp_act_id"`                          // 招商团活动id
	GiftCouponKey       string  `json:"giftCouponKey" gorm:"column:gift_coupon_key"`              // 礼金批次ID
	GiftCouponOcsAmount float64 `json:"giftCouponOcsAmount" gorm:"column:gift_coupon_ocs_amount"` // 礼金分摊金额
	BalanceExt          string  `json:"balanceExt" gorm:"column:balance_ext"`                     // 计佣扩展信息

	// 数据签名
	Sign string `json:"sign" gorm:"column:sign"` // 数据签名，用来核对出参数据是否被修改

	// 业务字段
	AppID     int `json:"app_id" gorm:"column:app_id;index"`     // 商城ID
	AppUserID int `json:"app_user_id" gorm:"column:app_user_id"` // 商城会员ID

	// 系统字段
	SyncTime      time.Time `json:"sync_time" gorm:"column:sync_time;index"`                     // 同步时间
	ProcessStatus int       `json:"process_status" gorm:"column:process_status;default:0;index"` // 处理状态：0-未处理，1-已处理
}

// TableName 设置表名
func (JDOrder) TableName() string {
	return "jd_orders"
}

type JDOrderModel struct {
	source.Model
	JDOrder
	AppID       int         `json:"app_id" gorm:"column:app_id;index"` // 商城ID
	Application Application `json:"application" gorm:"foreignKey:AppID;references:ID"`
}

// TableName 设置表名
func (JDOrderModel) TableName() string {
	return "jd_orders"
}

// JDOrderResponse 京东订单查询响应结构
type JDOrderResponse struct {
	Code         int       `json:"code"`
	Msg          string    `json:"msg"`
	TotalResults int       `json:"total_results"`
	HasMore      bool      `json:"hasMore"`
	Data         []JDOrder `json:"data"`
}
