package model

import (
	"finance/model"
	"gorm.io/gorm"
	"yz-go/source"
)

type CpsOrderModel struct {
	source.Model
	OrderId                 string            `json:"order_id" form:"order_id" gorm:"column:order_id;index;"`                //cps订单号
	AppId                   string            `json:"app_id" form:"app_id"`                                                  //cps_appid
	UserID                  uint              `json:"user_id"`                                                               // 会员id
	ApplicationID           uint              `json:"application_id"`                                                        // 应用id
	ThirdUserID             uint              `json:"third_user_id"`                                                         // 第三方会员id
	ProductId               string            `json:"product_id" form:"product_id"`                                          //cps商品id
	ProductName             string            `json:"product_name" form:"product_name"`                                      //cps商品名称
	ProductImg              string            `json:"product_img" form:"product_img"`                                        //cps商品图片
	TotalPayAmount          int               `json:"total_pay_amount" form:"total_pay_amount"`                              //总支付金额
	PaySuccessTime          string            `json:"pay_success_time" form:"pay_success_time"`                              //支付时间
	RefundTime              string            `json:"refund_time" form:"refund_time"`                                        //退款时间
	PayGoodsAmount          int               `json:"pay_goods_amount" form:"pay_goods_amount"`                              //预估结算金额
	EstimatedCommission     int               `json:"estimated_commission" form:"estimated_commission"`                      //预估佣金
	SplitRate               int               `json:"split_rate" form:"split_rate"`                                          //推广费率
	AfterSalesStatus        int               `json:"after_sales_status" form:"after_sales_status"`                          //售后状态 1-空，2-产生退款
	ExternalInfo            string            `json:"external_info" form:"external_info" gorm:"column:external_info;"`       //自定义传输字段
	FlowPoint               string            `json:"flow_point" form:"flow_point" gorm:"column:flow_point;index;"`          //订单状态 PAY_SUCC:支付完成 REFUND:退款 SETTLE:结算。此状态代表商家确定会结算佣金 CONFIRM: 确认收货
	SettleTime              string            `json:"settle_time" form:"settle_time"`                                        //结算时间
	ConfirmTime             string            `json:"confirm_time" form:"confirm_time"`                                      //确认收货时间
	EstimatedTechServiceFee int               `json:"estimated_tech_service_fee" form:"estimated_tech_service_fee"`          //预估技术服务费
	IsDisplay               int               `json:"is_display" form:"is_display"`                                          //是否显示
	Status                  int               `json:"status" form:"status" gorm:"column:status;default:0;comment:0未分成1已分成;"` // 0未分成  1已分成
	LocalSettleAt           *source.LocalTime `json:"local_settle_at"`                                                       //中台分成时间
	OrderSN                 uint              `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:编号;"`           //订单编号
	AwardAmount             int               `json:"award_amount"`                                                          //分成金额
	AwardRatio              int               `json:"award_ratio"`                                                           //分成比例
}

func (CpsOrderModel) TableName() string {
	return "cps_orders"
}

type CpsOrder struct {
	CpsOrderModel
	User model.User `json:"user" gorm:"foreignKey:UserID"`
}

func (o *CpsOrder) AfterCreate(tx *gorm.DB) (err error) {
	timestamp := uint(o.CreatedAt.Unix())
	orderSn := o.ID + timestamp*110
	err = tx.Model(&o).Update("order_sn", orderSn).Error
	return
}
