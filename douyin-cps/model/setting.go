package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"gorm.io/gorm"
	"yz-go/model"
	"yz-go/source"
)

type CpsSetting struct {
	model.SysSetting
	Value CpsValue `json:"value"`
}

func (i CpsSetting) TableName() string {
	return "sys_settings"
}

type CpsValue struct {
	SyncOrder   int    `json:"sync_order"` //同步订单 1允许 2不允许
	SecurityKey string `json:"security_key"`
	RoleId      int    `json:"role_id"`
}

func (value CpsValue) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *CpsValue) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

var cpsSetting *CpsValue

func getCpsSetting(key string) (err error, sysSetting CpsSetting) {
	err = source.DB().Where("`key` = ?", key).First(&sysSetting).Error
	return
}
func GetCpsSetting() (err error, setting CpsValue) {
	if cpsSetting == nil {
		var sysSetting CpsSetting
		err, sysSetting = getCpsSetting("cps_setting")
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil
		}
		if err != nil {
			return
		}
		cpsSetting = &sysSetting.Value
	}
	return err, *cpsSetting
}

func ResetCps() {
	//重置全局变量 start
	cpsSetting = nil
}
