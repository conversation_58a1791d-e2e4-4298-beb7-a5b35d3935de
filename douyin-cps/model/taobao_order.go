package model

import (
	"time"
	"yz-go/source"
)

// SupplyTaobaoOrder 中台淘宝订单模型
type SupplyTaobaoOrder struct {
	source.Model
	TradeId            string  `json:"trade_id" gorm:"column:trade_id;index"`
	TradeParentId      string  `json:"trade_parent_id" gorm:"column:trade_parent_id"`
	ItemId             string  `json:"item_id" gorm:"column:item_id"`
	ItemTitle          string  `json:"item_title" gorm:"column:item_title;type:varchar(255)"`
	ItemImg            string  `json:"item_img" gorm:"column:item_img;type:varchar(255)"`
	ItemPrice          string  `json:"item_price" gorm:"column:item_price"`
	ItemNum            int     `json:"item_num" gorm:"column:item_num"`
	TkStatus           int     `json:"tk_status" gorm:"column:tk_status"`
	OrderType          string  `json:"order_type" gorm:"column:order_type"`
	FlowSource         string  `json:"flow_source" gorm:"column:flow_source"`
	TkCreateTime       string  `json:"tk_create_time" gorm:"column:tk_create_time"`
	TkPaidTime         string  `json:"tk_paid_time" gorm:"column:tk_paid_time"`
	TkEarningTime      string  `json:"tk_earning_time" gorm:"column:tk_earning_time"`
	AlipayTotalPrice   string  `json:"alipay_total_price" gorm:"column:alipay_total_price"`
	PubShareFee        string  `json:"pub_share_fee" gorm:"column:pub_share_fee"`
	PubShareCommission float64 `json:"pub_share_commission"`
	SellerShopTitle    string  `json:"seller_shop_title" gorm:"column:seller_shop_title;type:varchar(255)"`
	IncomeRate         string  `json:"income_rate" gorm:"column:income_rate"`
	PubId              string  `json:"pub_id" gorm:"column:pub_id"`
	UnId               string  `json:"unid" gorm:"column:unid"`
	SiteId             string  `json:"site_id" gorm:"column:site_id"`
	AdZoneId           string  `json:"adzone_id" gorm:"column:adzone_id"`
	SiteName           string  `json:"site_name" gorm:"column:site_name;type:varchar(255)"`
	AdzoneName         string  `json:"adzone_name" gorm:"column:adzone_name;type:varchar(255)"`
	RefundTag          int     `json:"refund_tag" gorm:"column:refund_tag"`
	TerminalType       string  `json:"terminal_type" gorm:"column:terminal_type"`
	ClickTime          string  `json:"click_time" gorm:"column:click_time"`
	TkTotalRate        string  `json:"tk_total_rate" gorm:"column:tk_total_rate"`
	ItemCategoryName   string  `json:"item_category_name" gorm:"column:item_category_name;type:varchar(255)"`
	SellerNick         string  `json:"seller_nick" gorm:"column:seller_nick;type:varchar(255)"`
	SpecialId          string  `json:"special_id" gorm:"column:special_id"`
	RelationId         string  `json:"relation_id" gorm:"column:relation_id"`
	AppID              int     `json:"app_id"`      // 商城ID
	AppUserID          int     `json:"app_user_id"` // 商城会员ID
	// 额外字段
	SyncTime         time.Time `json:"sync_time" gorm:"column:sync_time"`                     // 同步时间
	ProcessStatus    int       `json:"process_status" gorm:"column:process_status;default:0"` // 处理状态：0-未处理，1-已处理
	AppProcessStatus int       `json:"app_process_status" gorm:"default:0"`
	AlimamaRate      string    `json:"alimama_rate"`
	AlimamaShareFee  string    `json:"alimama_share_fee"`
}

type SupplyTaobaoOrderModel struct {
	source.Model
	SupplyTaobaoOrder
	Application Application `json:"application" gorm:"foreignKey:AppID;references:ID"`
}

func (SupplyTaobaoOrderModel) TableName() string {
	return "supply_taobao_orders"
}
