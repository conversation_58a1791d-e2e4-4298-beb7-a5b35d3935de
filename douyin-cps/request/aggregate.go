package request

type GetAggregateInfo struct {
	MaterialId   string `json:"material_id"`
	ExternalInfo string `json:"external_info"`
	ProductUrl   string `json:"product_url"`
	Platform     int    `json:"platform"`
	AppUserID    uint   `json:"app_user_id"`
}

type GetOpenTbTokenInfo struct {
	Code  string `json:"code" query:"code" form:"code"`
	State string `json:"state" query:"state" form:"state"`
}
