package request

import (
	"douyin-cps/model"
	"yz-go/request"
)

type OrderSearch struct {
	request.PageInfo
	model.CpsOrder
	AppID    uint   `json:"app_id"`
	UserID   uint   `json:"user_id"`
	UID uint `json:"uid" form:"uid"`
	UserWord string `json:"user_words" form:"user_words"`
	StartAT  string `json:"start_at" form:"start_at"`
	EndAT    string `json:"end_at" form:"end_at"`
	TimeType *int   `json:"time_type" form:"time_type"` //1付款时间 2确认收货时间 3结算时间 4退款时间
	FlowPoints []string `json:"flow_points" form:"flow_points"`
}

type OrderDetailSearch struct {
	CpsOrderIds []string `json:"cps_order_ids"`
}

type OrderSetDisplay struct {
	ID uint `json:"id"`
}