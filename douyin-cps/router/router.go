package router

import (
	"douyin-cps/api/app"
	v1 "douyin-cps/api/v1"
	"github.com/gin-gonic/gin"
)

func InitAdminPrivateRouter(Router *gin.RouterGroup) {
	CpsRouter := Router.Group("cps")
	{
		CpsRouter.POST("saveSetting", v1.UpdateCpsSetting)                //
		CpsRouter.POST("getSetting", v1.FindCpsSetting)                   //
		CpsRouter.POST("getOrderList", v1.GetOrderList)                   //
		CpsRouter.POST("exportOrderList", v1.ExportOrderList)             //
		CpsRouter.POST("setOrderDisplay", v1.SetOrderDisplay)             //
		CpsRouter.POST("taobao/orderlist", v1.GetSupplyTaobaoOrderList)   // 获取供应链淘宝订单列表
		CpsRouter.POST("taobao/export", v1.ExportSupplyTaobaoOrder)       // 导出供应链淘宝订单
		CpsRouter.GET("taobao/detail/:id", v1.GetSupplyTaobaoOrderDetail) // 获取供应链淘宝订单详情
		CpsRouter.POST("taobao/sync", v1.SyncSupplyTaobaoOrder)           // 手动同步供应链淘宝订单
		CpsRouter.POST("jd/orderlist", v1.GetJDOrderList)                 // 获取京东订单列表
		CpsRouter.POST("jd/export", v1.ExportJDOrders)                    // 导出京东订单
		CpsRouter.GET("jd/detail", v1.GetJDOrderDetail)                   // 获取京东订单详情
		CpsRouter.POST("pdd/orderlist", v1.GetPddOrderList)               // 获取拼多多订单列表
		CpsRouter.POST("pdd/export", v1.ExportPddOrders)                  // 导出拼多多订单
		CpsRouter.GET("pdd/orderdetail", v1.GetPddOrderDetail)            // 获取拼多多订单详情
		CpsRouter.POST("vip/orderlist", v1.GetVipOrderList)               // 获取唯品会订单列表
		CpsRouter.POST("vip/export", v1.ExportVipOrders)                  // 导出唯品会订单
		CpsRouter.GET("vip/detail", v1.GetVipOrderDetail)                 // 获取唯品会订单详情
	}
	EcCpsRouter := Router.Group("ecCps")
	{
		EcCpsRouter.POST("openTb/getFiles", v1.GetFiles) //获取授权物料

	}

}

func InitUserPrivateRouter(Router *gin.RouterGroup) {
	CpsRouter := Router.Group("cps")
	{
		CpsRouter.POST("getOrderList", v1.GetUserOrderList)   //
		CpsRouter.POST("setOrderDisplay", v1.SetOrderDisplay) //

	}

}

func InitAppPrivateRouter(Router *gin.RouterGroup) {
	CpsAppRouter := Router.Group("cps")
	{
		CpsAppRouter.POST("product/list", v1.GetProductList)            //
		CpsAppRouter.POST("product/detail", v1.GetProductDetail)        //
		CpsAppRouter.POST("product/link", v1.GetProductLink)            //
		CpsAppRouter.POST("commandParse", v1.CpsCommandParse)           //
		CpsAppRouter.POST("product/category", v1.GetCategory)           //
		CpsAppRouter.POST("order/list", v1.GetAppOrderList)             //
		CpsAppRouter.POST("order/detailList", v1.GetAppOrderDetailList) //
		CpsAppRouter.POST("live/list", v1.GetCpsLiveList)               //
		CpsAppRouter.POST("live/link", v1.GetCpsLiveLink)               //
		CpsAppRouter.POST("aggregate/h5", v1.GetAggregate)              //

	}

	EcCpsAppRouter := Router.Group("ecCps")
	{
		EcCpsAppRouter.POST("openTb/getRelations", v1.GetAccessTokensByAppIDs) //
		EcCpsAppRouter.POST("openTb/getFiles", v1.GetFiles)                    //获取授权物料

	}

	CpsRouter := Router.Group("ecCps/taobao")
	{
		CpsRouter.POST("tcGeneralConvert", app.TcGeneralConvert)       //
		CpsRouter.POST("idPrivilege", app.IdPrivilege)                 //
		CpsRouter.POST("tklPrivilege", app.TklPrivilege)               //
		CpsRouter.POST("shopConvert", app.ShopConvert)                 //
		CpsRouter.POST("orderDetails", app.OrderDetails)               //
		CpsRouter.POST("scPunishOrder", app.ScPunishOrder)             //
		CpsRouter.POST("invitecodeGet", app.InvitecodeGet)             //
		CpsRouter.POST("superSearchMaterial", app.SuperSearchMaterial) //
		CpsRouter.POST("itemInfo", app.TbItemInfo)                     //
		CpsRouter.POST("materialRecommend", app.MaterialRecommend)     //
		CpsRouter.POST("orderlist", app.GetSupplyTaobaoOrderList)      // 获取供应链淘宝订单列表
		CpsRouter.POST("export", app.ExportSupplyTaobaoOrder)          // 导出供应链淘宝订单
		CpsRouter.GET("detail/:id", app.GetSupplyTaobaoOrderDetail)    // 获取供应链淘宝订单详情
		CpsRouter.POST("sync", app.SyncSupplyTaobaoOrder)              // 手动同步供应链淘宝订单
	}

	JDRouter := Router.Group("ecCps/jd")
	{
		JDRouter.POST("goodsCategory", app.GoodsCategory)           //
		JDRouter.POST("queryJingfenGoods", app.QueryJingfenGoods)   //
		JDRouter.POST("queryGoods", app.QueryGoods)                 //
		JDRouter.POST("getJdSkuid", app.GetJdSkuid)                 //
		JDRouter.POST("itemDetail", app.ItemDetail)                 //
		JDRouter.POST("materialQuery", app.MaterialQuery)           //
		JDRouter.POST("activityQuery", app.ActivityQuery)           //
		JDRouter.POST("orderDetails2", app.OrderDetails2)           //
		JDRouter.POST("urlPrivilege", app.UrlPrivilege)             //
		JDRouter.POST("byUnionidPromotion", app.ByUnionidPromotion) //
		JDRouter.POST("orderlist", app.GetJDOrderList)              // 获取京东订单列表
		JDRouter.POST("export", app.ExportJDOrders)                 // 导出京东订单
		JDRouter.GET("detail", app.GetJDOrderDetail)                // 获取京东订单详情
	}

	PDDRouter := Router.Group("ecCps/pdd")
	{
		PDDRouter.POST("convert", app.PddConvert)                 // 商品转链
		PDDRouter.POST("urlGenerate", app.PddUrlGenerate)         // 多多进宝推广链接生成
		PDDRouter.POST("urlConvert", app.PddUrlConvert)           // 链接解析转链
		PDDRouter.POST("resourceConvert", app.PddResourceConvert) // 活动转链
		PDDRouter.POST("pidGenerate", app.PddPidGenerate)         // 创建推广位
		PDDRouter.POST("pidQuery", app.PddPidQuery)               // 查询推广位
		PDDRouter.POST("promUrlGenerate", app.PromUrlGenerate)    // 订单详情
		PDDRouter.POST("cats", app.Cats)                          // 订单详情
		PDDRouter.POST("goodsSearch", app.GoodsSearch)            // 订单详情
		PDDRouter.POST("goodsDetail2", app.GoodsDetail2)          // 订单详情
		PDDRouter.POST("orderlist", app.GetPddOrderList)          // 获取拼多多订单列表
		PDDRouter.POST("export", app.ExportPddOrders)             // 导出拼多多订单
		PDDRouter.GET("orderdetail", app.GetPddOrderDetail)       // 获取拼多多订单详情
	}

	VIPRouter := Router.Group("ecCps/vip")
	{
		VIPRouter.POST("orderlist", app.GetVipOrderList)         // 获取唯品会订单列表
		VIPRouter.POST("export", app.ExportVipOrders)            // 导出唯品会订单
		VIPRouter.GET("detail", app.GetVipOrderDetail)           // 获取唯品会订单详情
		VIPRouter.POST("goodsList", app.GoodsList)               // 商品列表
		VIPRouter.POST("itemInfo", app.ItemInfo)                 // 商品信息
		VIPRouter.POST("query", app.VipQuery)                    // 查询
		VIPRouter.POST("vipLinkCheck", app.ViplinkCheck)         // 链接检查
		VIPRouter.POST("similarRecommend", app.SimilarRecommend) // 相似推荐
		VIPRouter.POST("userRecommend", app.UserRecommend)       // 用户推荐
		VIPRouter.POST("itemInfo2", app.ItemInfo2)               // 商品信息2
		VIPRouter.POST("orderDetails", app.VipOrderDetails)      // 订单详情
		VIPRouter.POST("orderDetails2", app.VipOrderDetails2)    // 订单详情2
		VIPRouter.POST("idPrivilege", app.VipIdPrivilege)        // ID权限
		VIPRouter.POST("urlPrivilege", app.VipUrlPrivilege)      // URL权限
		VIPRouter.POST("getVipAccess", app.GetVipAccess)         // 获取VIP访问权限
		VIPRouter.POST("refreshToken", app.RefreshToken)         // 刷新令牌
	}
}
