package router

import (
	v1 "douyin-cps/api/v1"
	"github.com/gin-gonic/gin"
)

func InitAdminPrivateRouter(Router *gin.RouterGroup) {
	CpsRouter := Router.Group("cps")
	{
		CpsRouter.POST("saveSetting", v1.UpdateCpsSetting)    //
		CpsRouter.POST("getSetting", v1.FindCpsSetting)       //
		CpsRouter.POST("getOrderList", v1.GetOrderList)       //
		CpsRouter.POST("exportOrderList", v1.ExportOrderList) //
		CpsRouter.POST("setOrderDisplay", v1.SetOrderDisplay) //
	}

}

func InitUserPrivateRouter(Router *gin.RouterGroup) {
	CpsRouter := Router.Group("cps")
	{
		CpsRouter.POST("getOrderList", v1.GetUserOrderList)   //
		CpsRouter.POST("setOrderDisplay", v1.SetOrderDisplay) //

	}

}

func InitAppPrivateRouter(Router *gin.RouterGroup) {
	CpsAppRouter := Router.Group("cps")
	{
		CpsAppRouter.POST("product/list", v1.GetProductList)            //
		CpsAppRouter.POST("product/detail", v1.GetProductDetail)        //
		CpsAppRouter.POST("product/link", v1.GetProductLink)            //
		CpsAppRouter.POST("commandParse", v1.CpsCommandParse)           //
		CpsAppRouter.POST("product/category", v1.GetCategory)           //
		CpsAppRouter.POST("order/list", v1.GetAppOrderList)             //
		CpsAppRouter.POST("order/detailList", v1.GetAppOrderDetailList) //
		CpsAppRouter.POST("live/list", v1.GetCpsLiveList)               //
		CpsAppRouter.POST("live/link", v1.GetCpsLiveLink)               //
		CpsAppRouter.POST("aggregate/h5", v1.GetAggregate)              //

	}
}
