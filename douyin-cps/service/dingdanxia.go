package service

import (
	"douyin-cps/model"
	"douyin-cps/utils"
	"encoding/json"
	"errors"
	"gorm.io/gorm"
	"yz-go/source"
)

func GetAccessTokensByAppIDs(appID uint) (tokens []model.ApplicationOpenTbRelation, err error) {
	err = source.DB().Where("app_id = ?", appID).Find(&tokens).Error
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("查询 AccessToken 列表失败: " + err.Error())
		}

		return []model.ApplicationOpenTbRelation{}, nil
	}
	return tokens, nil
}

func GetFiles() (err error, data interface{}) {
	err, setting := model.GetCpsSetting()
	if err != nil {
		return
	}
	err, result := utils.NewSelfAPIRequest(setting.EcCpsHost, setting.EcCpsAppKey, setting.EcCpsAppSecret).Execute("/app/ecCpsCtrl/openTb/getSetting", nil)
	if err != nil {
		return
	}
	var resultData struct {
		Data map[string]interface{} `json:"data"`
		Code int                    `json:"code"`
		Msg  string                 `json:"msg"`
	}
	err = json.Unmarshal(result, &resultData)
	if err != nil {
		return
	}
	if resultData.Code != 0 {
		err = errors.New(resultData.Msg)
		return
	}
	resultData.Data["state"] = setting.EcCpsAppKey[11:]
	data = resultData.Data

	return
}
