package service

import (
	"encoding/json"
	model2 "finance/model"
	"math"
	"yz-go/source"
	"yz-go/utils"
)

const jdBaseURL = "/app/ecCpsCtrl/jd/"

// 各个接口实现
func GoodsCategory(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(jdBaseURL+"goodsCategory", request)
}

func QueryJingfenGoods(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(jdBaseURL+"queryJingfenGoods", request)
}

type CommissionInfo struct {
	Commission          float64 `json:"commission"`
	CommissionShare     float64 `json:"commissionShare"`
	CouponCommission    float64 `json:"couponCommission"`
	EndTime             int64   `json:"endTime"`
	IsLock              int     `json:"isLock"`
	PlusCommissionShare float64 `json:"plusCommissionShare"`
	StartTime           int64   `json:"startTime"`
}
type TbkJdGoodsResponse struct {
	Code int `json:"code"`
	Data []struct {
		BrandCode    string `json:"brandCode"`
		BrandName    string `json:"brandName"`
		CategoryInfo struct {
			Cid1     int    `json:"cid1"`
			Cid1Name string `json:"cid1Name"`
			Cid2     int    `json:"cid2"`
			Cid2Name string `json:"cid2Name"`
			Cid3     int    `json:"cid3"`
			Cid3Name string `json:"cid3Name"`
		} `json:"categoryInfo"`
		Comments       int            `json:"comments"`
		CommissionInfo CommissionInfo `json:"commissionInfo"`
		EcCpsInfo      struct {
			CommissionAmount float64 `json:"commission_amount"`
			CommissionRate   float64 `json:"commission_rate"`
		} `json:"ec_cps_info"`
		CouponInfo struct {
			CouponList []struct {
				BindType      int     `json:"bindType"`
				CouponStatus  int     `json:"couponStatus"`
				CouponStyle   int     `json:"couponStyle"`
				Discount      float64 `json:"discount"`
				GetEndTime    int64   `json:"getEndTime"`
				GetStartTime  int64   `json:"getStartTime"`
				HotValue      int     `json:"hotValue,omitempty"`
				IsBest        int     `json:"isBest"`
				IsInputCoupon int     `json:"isInputCoupon"`
				Link          string  `json:"link"`
				PlatformType  int     `json:"platformType"`
				Quota         int     `json:"quota"`
				UseEndTime    int64   `json:"useEndTime"`
				UseStartTime  int64   `json:"useStartTime"`
			} `json:"couponList"`
		} `json:"couponInfo"`
		DeliveryType      int   `json:"deliveryType"`
		EliteType         []int `json:"eliteType"`
		ForbidTypes       []int `json:"forbidTypes"`
		GoodCommentsShare int   `json:"goodCommentsShare"`
		ImageInfo         struct {
			ImageList []struct {
				Url string `json:"url"`
			} `json:"imageList"`
			WhiteImage string `json:"whiteImage,omitempty"`
		} `json:"imageInfo"`
		InOrderComm30Days     float64       `json:"inOrderComm30Days"`
		InOrderCount30Days    int           `json:"inOrderCount30Days"`
		InOrderCount30DaysSku int           `json:"inOrderCount30DaysSku"`
		IsHot                 int           `json:"isHot"`
		IsJdSale              int           `json:"isJdSale"`
		IsOversea             int           `json:"isOversea"`
		ItemId                string        `json:"itemId"`
		JxFlags               []interface{} `json:"jxFlags"`
		MaterialUrl           string        `json:"materialUrl"`
		Owner                 string        `json:"owner"`
		PinGouInfo            []interface{} `json:"pinGouInfo"`
		PingGouInfo           []interface{} `json:"pingGouInfo"`
		PriceInfo             struct {
			LowestCouponPrice float64 `json:"lowestCouponPrice"`
			LowestPrice       float64 `json:"lowestPrice"`
			LowestPriceType   int     `json:"lowestPriceType"`
			Price             float64 `json:"price"`
		} `json:"priceInfo"`
		PurchasePriceInfo struct {
			BasisPriceType int `json:"basisPriceType"`
			Code           int `json:"code"`
			CouponList     []struct {
				BindType     int     `json:"bindType"`
				CouponStatus int     `json:"couponStatus"`
				CouponStyle  int     `json:"couponStyle"`
				Discount     float64 `json:"discount"`
				IsBest       int     `json:"isBest"`
				Link         string  `json:"link"`
				PlatformType int     `json:"platformType"`
				Quota        int     `json:"quota"`
			} `json:"couponList"`
			Message                string  `json:"message"`
			PurchaseNum            int     `json:"purchaseNum"`
			PurchasePrice          float64 `json:"purchasePrice"`
			ThresholdPrice         float64 `json:"thresholdPrice"`
			PromotionLabelInfoList []struct {
				EndTime          int64  `json:"endTime"`
				PromotionLabel   string `json:"promotionLabel"`
				PromotionLabelId int64  `json:"promotionLabelId"`
				StartTime        int64  `json:"startTime"`
				LabelName        string `json:"labelName,omitempty"`
			} `json:"promotionLabelInfoList,omitempty"`
		} `json:"purchasePriceInfo"`
		ShopInfo struct {
			AfsFactorScoreRankGrade       string  `json:"afsFactorScoreRankGrade,omitempty"`
			AfterServiceScore             string  `json:"afterServiceScore,omitempty"`
			CommentFactorScoreRankGrade   string  `json:"commentFactorScoreRankGrade,omitempty"`
			LogisticsFactorScoreRankGrade string  `json:"logisticsFactorScoreRankGrade,omitempty"`
			LogisticsLvyueScore           string  `json:"logisticsLvyueScore,omitempty"`
			ScoreRankRate                 string  `json:"scoreRankRate,omitempty"`
			ShopId                        int     `json:"shopId"`
			ShopLabel                     string  `json:"shopLabel"`
			ShopLevel                     float64 `json:"shopLevel"`
			ShopName                      string  `json:"shopName"`
			UserEvaluateScore             string  `json:"userEvaluateScore,omitempty"`
		} `json:"shopInfo"`
		SkuName    string `json:"skuName"`
		SkuTagList []struct {
			Index int    `json:"index"`
			Name  string `json:"name"`
			Type  int    `json:"type"`
		} `json:"skuTagList"`
		SpecialSkuUrlInfo []interface{} `json:"specialSkuUrlInfo"`
		Spuid             int64         `json:"spuid"`
		VideoInfo         []interface{} `json:"videoInfo"`
		PreSaleInfo       struct {
			BalanceEndTime   int64 `json:"balanceEndTime"`
			BalanceStartTime int64 `json:"balanceStartTime"`
			CurrentPrice     int   `json:"currentPrice"`
			DiscountType     int   `json:"discountType"`
			Earnest          int   `json:"earnest"`
			PreSaleEndTime   int64 `json:"preSaleEndTime"`
			PreSalePayType   int   `json:"preSalePayType"`
			PreSaleStartTime int64 `json:"preSaleStartTime"`
			PreSaleStatus    int   `json:"preSaleStatus"`
			ShipTime         int64 `json:"shipTime"`
		} `json:"preSaleInfo,omitempty"`
	} `json:"data"`
	HotWords       string        `json:"hotWords"`
	Msg            string        `json:"msg"`
	SimilarSkuList []interface{} `json:"similarSkuList"`
	TotalResults   int           `json:"total_results"`
}

func QueryGoods(request map[string]interface{}, userID uint) (err error, response interface{}) {
	err, result := TbxRequest(jdBaseURL+"queryGoods", request)
	if err != nil {
		return
	}

	var materialResponse TbkJdGoodsResponse
	jsonData, err := json.Marshal(result)
	if err != nil {
		return
	}

	err = json.Unmarshal(jsonData, &materialResponse)
	if err != nil {
		return
	}
	var userModel model2.User
	err = source.DB().Preload("UserLevelInfo").First(&userModel, userID).Error
	if err != nil {
		return
	}
	for key, item := range materialResponse.Data {
		// 计算新的佣金金额并保留两位小数
		newCommissionAmount := utils.Decimal(item.EcCpsInfo.CommissionAmount * float64(userModel.UserLevelInfo.CpsRatio) / 10000)
		newCommissionRate := math.Round(item.EcCpsInfo.CommissionRate * float64(userModel.UserLevelInfo.CpsRatio) / 10000)

		// 将计算结果转回字符串，保留两位小数
		materialResponse.Data[key].EcCpsInfo.CommissionAmount = newCommissionAmount
		materialResponse.Data[key].EcCpsInfo.CommissionRate = newCommissionRate
	}
	return nil, materialResponse
}

func GetJdSkuid(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(jdBaseURL+"getJdSkuid", request)
}

func ItemDetail(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(jdBaseURL+"itemDetail", request)
}

func MaterialQuery(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(jdBaseURL+"materialQuery", request)
}

func ActivityQuery(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(jdBaseURL+"activityQuery", request)
}

func OrderDetails2(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(jdBaseURL+"orderDetails2", request)
}

func UrlPrivilege(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(jdBaseURL+"urlPrivilege", request)
}

func ByUnionidPromotion(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(jdBaseURL+"byUnionidPromotion", request)
}
