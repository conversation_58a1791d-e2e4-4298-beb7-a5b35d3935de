package service

import (
	"douyin-cps/model"
	"encoding/json"
	"errors"
	model2 "finance/model"
	"go.uber.org/zap"
	"yz-go/component/log"
	"yz-go/source"
)

// GetJDOrderList 获取京东订单列表
func GetJDOrderList(req map[string]interface{}) (error, int64, []model.JDOrder) {
	var orders []model.JDOrder
	var total int64

	// 构建查询条件
	query := source.DB().Model(&model.JDOrder{})

	// 添加查询条件
	if appID, ok := req["app_id"].(uint); ok && appID > 0 {
		query = query.Where("app_id = ?", appID)
	} else {
		return errors.New("身份ID不能为空"), 0, nil
	}

	// 添加查询条件
	if orderID, ok := req["order_id"].(string); ok && orderID != "" {
		query = query.Where("order_id = ?", orderID)
	}

	if skuID, ok := req["sku_id"].(string); ok && skuID != "" {
		query = query.Where("sku_id = ?", skuID)
	}

	if validCode, ok := req["valid_code"].(int); ok && validCode > 0 {
		query = query.Where("valid_code = ?", validCode)
	}

	if startTime, ok := req["start_time"].(string); ok && startTime != "" {
		query = query.Where("sync_time >= ?", startTime)
	}

	if endTime, ok := req["end_time"].(string); ok && endTime != "" {
		query = query.Where("sync_time <= ?", endTime)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		log.Log().Error("查询京东订单总数失败", zap.Error(err))
		return err, 0, nil
	}

	// 分页
	page := 1
	if p, ok := req["page"].(int); ok && p > 0 {
		page = p
	}

	pageSize := 10
	if ps, ok := req["page_size"].(int); ok && ps > 0 {
		pageSize = ps
	}

	offset := (page - 1) * pageSize

	// 排序
	orderBy := "id DESC"
	if ob, ok := req["order_by"].(string); ok && ob != "" {
		orderBy = ob
	}

	// 查询数据
	err = query.Order(orderBy).Offset(offset).Limit(pageSize).Find(&orders).Error
	if err != nil {
		log.Log().Error("查询京东订单列表失败", zap.Error(err))
		return err, 0, nil
	}
	if appID, ok := req["app_id"].(uint); ok && appID > 0 {
		var userModel model2.User
		err = source.DB().Preload("UserLevelInfo").First(&userModel, req["app_user_id"]).Error
		if err != nil {
			return err, 0, nil
		}
		for key, item := range orders {
			// 将计算结果转回字符串，保留两位小数
			orders[key].ActualFee = item.ActualFee * float64(userModel.UserLevelInfo.CpsRatio) / 10000
			orders[key].EstimateFee = item.EstimateFee * float64(userModel.UserLevelInfo.CpsRatio) / 10000
		}
	}

	return nil, total, orders
}

// GetJDOrderDetail 获取京东订单详情
func GetJDOrderDetail(orderID string) (error, model.JDOrder) {
	var order model.JDOrder
	err := source.DB().Where("order_id = ?", orderID).First(&order).Error
	if err != nil {
		log.Log().Error("查询京东订单详情失败", zap.String("订单ID", orderID), zap.Error(err))
		return err, model.JDOrder{}
	}
	return nil, order
}

// ExportJDOrders 导出京东订单
func ExportJDOrders(req map[string]interface{}) error {
	// 获取订单列表
	err, _, orders := GetJDOrderList(req)
	if err != nil {
		return err
	}

	// 导出数据
	data, err := json.Marshal(orders)
	if err != nil {
		log.Log().Error("序列化京东订单数据失败", zap.Error(err))
		return err
	}

	// 这里可以根据需要将数据保存为文件或发送到其他地方
	log.Log().Info("导出京东订单数据成功", zap.Int("数量", len(orders)), zap.Int("数据大小", len(data)))

	return nil
}
