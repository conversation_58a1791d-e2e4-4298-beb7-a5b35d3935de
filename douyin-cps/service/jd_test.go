package service

import (
	"testing"
)

func TestJDSafeUnmarshalJSON(t *testing.T) {
	// 测试包含额外未知字段的JSON
	jsonWithExtra := `{
		"code": 200,
		"data": [
			{
				"itemId": "123456",
				"skuName": "测试商品",
				"unknown_field": "unknown_value",
				"ec_cps_info": {
					"commission_amount": 10.5,
					"commission_rate": 5.0,
					"extra_field": "extra_value"
				},
				"commissionInfo": {
					"commission": 15.0,
					"commissionShare": 8.0,
					"couponCommission": 12.0,
					"unknown_commission_field": "unknown_value"
				},
				"nested_unknown": {
					"field1": "value1",
					"field2": 123
				}
			}
		],
		"msg": "success",
		"extra_top_level": "extra_value"
	}`

	var response TbkJdGoodsResponse
	err := SafeUnmarshalJSON([]byte(jsonWithExtra), &response)
	if err != nil {
		t.Errorf("包含额外字段的JSON解析失败: %v", err)
	}

	if response.Code != 200 {
		t.Errorf("期望code为200，实际为%d", response.Code)
	}

	if len(response.Data) != 1 {
		t.Errorf("期望data长度为1，实际为%d", len(response.Data))
	}

	if response.Data[0].ItemId != "123456" {
		t.Errorf("期望itemId为123456，实际为%s", response.Data[0].ItemId)
	}

	if response.Data[0].SkuName != "测试商品" {
		t.Errorf("期望skuName为测试商品，实际为%s", response.Data[0].SkuName)
	}

	if response.Data[0].EcCpsInfo.CommissionAmount != 10.5 {
		t.Errorf("期望commission_amount为10.5，实际为%f", response.Data[0].EcCpsInfo.CommissionAmount)
	}

	if response.Data[0].CommissionInfo.Commission != 15.0 {
		t.Errorf("期望commission为15.0，实际为%f", response.Data[0].CommissionInfo.Commission)
	}

	t.Logf("测试通过：成功解析包含未知字段的JSON")

	// 验证RawData字段存在（虽然不会被JSON填充，但结构体应该有这个字段）
	if response.RawData == nil {
		// 初始化RawData字段用于存储原始数据
		response.RawData = make(map[string]interface{})
	}

	// 验证Data项的RawData字段
	if len(response.Data) > 0 && response.Data[0].RawData == nil {
		response.Data[0].RawData = make(map[string]interface{})
	}

	t.Logf("RawData字段验证通过")
}

func TestJDMalformedJSON(t *testing.T) {
	// 测试格式错误的JSON
	malformedJSON := `{
		"code": 200,
		"data": [
			{
				"itemId": "123456",
				"skuName": "测试商品",
				"ec_cps_info": {
					"commission_amount": "invalid_number",
					"commission_rate": 5.0
				}
			}
		],
		"msg": "success"
	}`

	var response TbkJdGoodsResponse
	err := SafeUnmarshalJSON([]byte(malformedJSON), &response)

	// 这个测试应该能够处理格式错误的字段，而不是完全失败
	if err != nil {
		t.Logf("预期的解析错误: %v", err)
		// 这是预期的行为，因为commission_amount字段类型不匹配
	} else {
		t.Logf("成功解析了包含类型错误字段的JSON")
	}
}

func TestJDCompletelyInvalidJSON(t *testing.T) {
	// 测试完全无效的JSON
	invalidJSON := `{invalid json`

	var response TbkJdGoodsResponse
	err := SafeUnmarshalJSON([]byte(invalidJSON), &response)

	if err == nil {
		t.Errorf("期望解析完全无效的JSON时返回错误，但没有返回错误")
	}

	t.Logf("正确处理了无效JSON: %v", err)
}
