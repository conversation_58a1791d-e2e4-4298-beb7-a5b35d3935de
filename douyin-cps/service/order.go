package service

import (
	"bytes"
	"douyin-cps/model"
	"douyin-cps/request"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"io/ioutil"
	"net/http"
	"os"
	"strconv"
	"time"
	model2 "user/model"
	"yz-go/component/log"
	"yz-go/config"
	"yz-go/source"
	"yz-go/utils"
)

func GetAppOrderList(info request.OrderSearch) (err error, total int64, list interface{}) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	var db = source.DB().Model(&model.CpsOrder{})
	var data []model.CpsOrder
	err, setting := model.GetCpsSetting()
	if err != nil {
		return
	}
	db = db.Where("application_id = ?", info.AppID)
	if setting.SyncOrder == 2 {
		db = db.Where("is_display = 1")
	}

	if len(info.FlowPoints) > 0 {

		db = db.Where("flow_point in ?", info.FlowPoints)

	}

	if info.UID != 0 {
		db = db.Where("third_user_id = ?", info.UID)
	}

	if info.OrderSN > 0 {
		db = db.Where("order_sn = ?", info.OrderSN)

	}

	var timeType string
	timeType = "pay_success_time"
	if info.TimeType != nil {
		switch *info.TimeType {
		case 1:
			timeType = "pay_success_time"
			break
		case 2:
			timeType = "confirm_time"
			break
		case 3:
			timeType = "settle_time"
			break
		case 4:
			timeType = "refund_time"
			break
		default:
			timeType = "pay_success_time"
			break
		}
	}
	if info.StartAT != "" {
		db.Where("`"+timeType+"` >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db.Where("`"+timeType+"` <= ?", info.EndAT)
	}

	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Order("created_at desc").Find(&data).Error
	return err, total, data
}

func GetAppOrderDetailList(ids []string) (err error, list interface{}) {

	var db = source.DB().Model(&model.CpsOrder{})
	var data []model.CpsOrder
	if len(ids) > 0 {
		err = db.Where("order_id in ?", ids).Order("created_at desc").Find(&data).Error
	}
	return err, data
}

type Application struct {
	source.Model
	MemberId          int    `json:"memberId" form:"memberId" gorm:"column:member_id;comment:;type:int;size:10;"`
	CallBackLinkJhCps string `json:"callBackLinkJhCps" form:"callBackLinkJhCps" gorm:"column:call_back_link_jh_cps;comment:;type:varchar(255);size:255;"`
	CallBackLinkCps   string `json:"callBackLinkCps" form:"callBackLinkCps" gorm:"column:call_back_link_cps;comment:;type:varchar(255);size:255;"`
}

func (Application) TableName() string {
	return "application"
}

func GetOrderList(info request.OrderSearch) (err error, total int64, list interface{}) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	var db = source.DB().Model(&model.CpsOrder{}).Preload("User")
	var data []model.CpsOrder
	if info.AppID > 0 {
		db = db.Where("application_id = ?", info.AppID)
	}

	if info.FlowPoint != "" {
		db = db.Where("flow_point = ?", info.FlowPoint)
	}

	if info.OrderSN > 0 {
		db = db.Where("order_sn = ?", info.OrderSN)

	}

	if info.UserID > 0 {
		db = db.Where("user_id = ?", info.UserID)

	}

	if info.UserWord != "" {
		userIds := []uint{}
		err = source.DB().Model(model2.User{}).Where("username like ?", "%"+info.UserWord+"%").Or("nick_name like ?", "%"+info.UserWord+"%").Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		db = db.Where("user_id in ?", userIds)

	}
	var timeType string
	timeType = "pay_success_time"
	if info.TimeType != nil {
		switch *info.TimeType {
		case 1:
			timeType = "pay_success_time"
			break
		case 2:
			timeType = "confirm_time"
			break
		case 3:
			timeType = "settle_time"
			break
		case 4:
			timeType = "refund_time"
			break
		default:
			timeType = "pay_success_time"
			break
		}
	}
	if info.StartAT != "" {
		db.Where("`"+timeType+"` >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db.Where("`"+timeType+"` <= ?", info.EndAT)
	}
	if err != nil {
		return
	}
	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Order("created_at desc").Find(&data).Error
	return err, total, data
}

func ExportOrderList(info request.OrderSearch) (err error, link string) {
	var db = source.DB().Model(&model.CpsOrder{}).Preload("User")
	var data []model.CpsOrder
	if info.AppID > 0 {
		db = db.Where("application_id = ?", info.AppID)
	}

	if info.FlowPoint != "" {
		db = db.Where("flow_point = ?", info.FlowPoint)
	}

	if info.OrderSN > 0 {
		db = db.Where("order_sn = ?", info.OrderSN)

	}

	if info.UserID > 0 {
		db = db.Where("user_id = ?", info.UserID)

	}

	if info.UserWord != "" {
		userIds := []uint{}
		err = source.DB().Model(model2.User{}).Where("username like ?", "%"+info.UserWord+"%").Or("nickname like ?", "%"+info.UserWord+"%").Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		db = db.Where("user_id in ?", userIds)

	}
	var timeType string
	timeType = "pay_success_time"
	if info.TimeType != nil {
		switch *info.TimeType {
		case 1:
			timeType = "pay_success_time"
			break
		case 2:
			timeType = "confirm_time"
			break
		case 3:
			timeType = "settle_time"
			break
		case 4:
			timeType = "refund_time"
			break
		default:
			timeType = "pay_success_time"
			break
		}
	}
	if info.StartAT != "" {
		db.Where("`"+timeType+"` >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db.Where("`"+timeType+"` <= ?", info.EndAT)
	}
	if err != nil {
		return
	}
	err = db.Order("created_at desc").Find(&data).Error

	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")
	// 设置单元格的值
	f.SetCellValue("Sheet1", "A1", "cps订单号")
	f.SetCellValue("Sheet1", "B1", "cps_appid")
	f.SetCellValue("Sheet1", "C1", "会员id")
	f.SetCellValue("Sheet1", "D1", "中台应用id")
	f.SetCellValue("Sheet1", "E1", "cps商品id")
	f.SetCellValue("Sheet1", "F1", "cps商品名称")
	f.SetCellValue("Sheet1", "G1", "cps商品图片")
	f.SetCellValue("Sheet1", "H1", "总支付金额")
	f.SetCellValue("Sheet1", "I1", "支付时间")
	f.SetCellValue("Sheet1", "J1", "退款时间")
	f.SetCellValue("Sheet1", "K1", "预估结算金额")
	f.SetCellValue("Sheet1", "L1", "预估佣金")
	f.SetCellValue("Sheet1", "M1", "售后状态 1-空，2-产生退款")
	f.SetCellValue("Sheet1", "N1", "订单状态")
	f.SetCellValue("Sheet1", "O1", "结算时间")
	f.SetCellValue("Sheet1", "P1", "确认收货时间")
	f.SetCellValue("Sheet1", "Q1", "预估技术服务费")
	f.SetCellValue("Sheet1", "R1", "是否显示")
	f.SetCellValue("Sheet1", "S1", "0未分成 1已分成")
	f.SetCellValue("Sheet1", "T1", "中台分成时间")
	f.SetCellValue("Sheet1", "U1", "订单编号")
	f.SetCellValue("Sheet1", "V1", "分成金额")
	f.SetCellValue("Sheet1", "W1", "分成比例")
	i := 2
	for _, v := range data {

		f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), v.OrderId)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), v.AppId)
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), v.UserID)
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), v.ApplicationID)
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), v.ProductId) //子订单号
		f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), v.ProductName)
		f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), v.ProductImg)
		f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), v.TotalPayAmount)
		f.SetCellValue("Sheet1", "I"+strconv.Itoa(i), v.PaySuccessTime)
		f.SetCellValue("Sheet1", "J"+strconv.Itoa(i), v.RefundTime)
		f.SetCellValue("Sheet1", "K"+strconv.Itoa(i), v.PayGoodsAmount)
		f.SetCellValue("Sheet1", "L"+strconv.Itoa(i), v.EstimatedCommission)
		f.SetCellValue("Sheet1", "M"+strconv.Itoa(i), v.AfterSalesStatus)
		f.SetCellValue("Sheet1", "N"+strconv.Itoa(i), v.FlowPoint)
		f.SetCellValue("Sheet1", "O"+strconv.Itoa(i), v.SettleTime)
		f.SetCellValue("Sheet1", "P"+strconv.Itoa(i), v.ConfirmTime)
		f.SetCellValue("Sheet1", "Q"+strconv.Itoa(i), v.EstimatedTechServiceFee)
		f.SetCellValue("Sheet1", "R"+strconv.Itoa(i), v.IsDisplay)
		f.SetCellValue("Sheet1", "S"+strconv.Itoa(i), v.Status)
		f.SetCellValue("Sheet1", "T"+strconv.Itoa(i), v.LocalSettleAt)
		f.SetCellValue("Sheet1", "U"+strconv.Itoa(i), v.OrderSN)
		f.SetCellValue("Sheet1", "V"+strconv.Itoa(i), v.AwardAmount)
		f.SetCellValue("Sheet1", "W"+strconv.Itoa(i), v.AwardRatio)
		i++

	}
	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)
	// 根据指定路径保存文件
	//year, month, day := time.Now().Format("2006-01-02 15:04:05")
	time := time.Now().Format("20060102150405")
	path := config.Config().Local.Path + "/export_cps_order"
	exist, _ := utils.PathExists(path)

	if !exist {
		// 创建文件夹
		err = os.Mkdir(path, os.ModePerm)
		if err != nil {
			fmt.Printf("mkdir failed![%v]\n", err)
		} else {
			fmt.Printf("mkdir success!\n")
		}
	}
	link = path + "/" + time + "订单导出.xlsx"
	if err = f.SaveAs(link); err != nil {
		return
	}

	return err, link
}

func SettleAwardByTx(tx *gorm.DB, award model.CpsOrder) (err error) {
	award.Status = 1
	award.LocalSettleAt = &source.LocalTime{Time: time.Now()}
	err = tx.Updates(&award).Error
	return
}

type CpsOrderMessage struct {
	OperationType string   `json:"type"`
	Ids           []string `json:"ids"`
}

func NotifyCpsOrders(orders []model.CpsOrder, operationType string) (err error) {
	header := map[string]string{
		"Content-Type": "application/json",
	}
	var orderIdsByAppID = make(map[uint][]string)
	for _, order := range orders {
		orderIdsByAppID[order.ApplicationID] = append(orderIdsByAppID[order.ApplicationID], order.OrderId)
	}
	for app, ids := range orderIdsByAppID {
		var message CpsOrderMessage
		message.OperationType = operationType
		message.Ids = ids
		var application Application
		err = source.DB().First(&application, app).Error
		if err != nil {
			continue
		}
		if application.CallBackLinkCps != "" {
			err, _ = post(application.CallBackLinkCps, message, header)
			if err != nil {
				log.Log().Error("通知下游失败", zap.Any("application", application))
				continue
			}
		}
	}
	return
}

type Resp struct {
	Code   int    `json:"code"`
	Result int    `json:"result"`
	Msg    string `json:"msg"`
}

func post(url string, data interface{}, header map[string]string) (error, Resp) {

	// 超时时间：5秒
	client := &http.Client{Timeout: 5 * time.Second}
	jsonStr, _ := json.Marshal(data)
	var req, err = http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		return err, Resp{}
	}

	//设置header
	for k, v := range header {
		req.Header.Set(k, v)
	}

	//执行请求
	resp, err := client.Do(req)
	if err != nil {
		return err, Resp{}
	}
	defer resp.Body.Close()

	//将结果转成结构体
	result, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err, Resp{}
	}
	//log.Log().Info("打印请求回调接口的url----"+string(url), zap.Any("info", string(jsonStr)))
	//log.Log().Info("打印请求回调接口的数据----"+string(jsonStr), zap.Any("info", string(jsonStr)))
	//log.Log().Info("打印请求回调接口的返回数据----"+string(result), zap.Any("info", string(result)))
	var respon Resp
	err = json.Unmarshal(result, &respon)
	return err, respon
}
func SetOrderDisplay(id uint) (err error) {
	err = source.DB().Model(&model.CpsOrder{}).Where("id = ?", id).Update("is_display", 1).Error
	return
}
