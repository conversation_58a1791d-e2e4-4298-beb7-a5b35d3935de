package service

import (
	"bytes"
	"embed"
	"errors"
	"image"
	"image/color"
	"image/png"
	"net/http"
	"os"
	"strconv"

	"github.com/golang/freetype"
	"github.com/golang/freetype/truetype"
	"github.com/nfnt/resize"
	"github.com/skip2/go-qrcode"
	"golang.org/x/image/draw"
)

var bgImageData []byte
var FontBytes embed.FS

type circle struct { // 这里需要自己实现一个圆形遮罩，实现接口里的三个方法
	p image.Point // 圆心位置
	r int
}
type radius struct {
	p image.Point // 矩形右下角位置
	r int
}

func (c *radius) ColorModel() color.Model {
	return color.AlphaModel
}

func (c *radius) Bounds() image.Rectangle {
	return image.Rect(0, 0, c.p.X, c.p.Y)
}

func (c *radius) At(x, y int) color.Color {
	var dx, dy int
	r := c.r
	if x < r {
		dx = r - x
	} else if x > c.p.X-r {
		dx = x - (c.p.X - r)
	}
	if y < r {
		dy = r - y
	} else if y > c.p.Y-r {
		dy = y - (c.p.Y - r)
	}

	if dx*dx+dy*dy > r*r {
		return color.Alpha{0}
	}
	return color.Alpha{255}
}

func ImageResize(src image.Image, w, h int) image.Image {
	return resize.Resize(uint(w), uint(h), src, resize.Lanczos3)
}

func GetProductPoster(userID uint, productID uint, productImage string, productTitle string, productPrice string, domain string, productLink string) (err error, link string) {

	bgImg, err := png.Decode(bytes.NewReader(bgImageData))
	if err != nil {
		return err, ""
	}

	templateFileImage := bgImg.Bounds()

	newTemplateImage := image.NewRGBA(templateFileImage)

	// 合成新的背景图
	draw.Draw(newTemplateImage, templateFileImage, bgImg, image.Point{X: 0, Y: 0}, draw.Src)

	// 从嵌入的字体文件中读取字体数据
	fontData, err := FontBytes.ReadFile("simsun.ttf")
	if err != nil {
		return err, ""
	}
	// 将字体数据解析为 truetype.Font 类型的字体
	font, err := truetype.Parse(fontData)
	if err != nil {
		return err, ""
	}

	miniRes, err := http.Get(domain + "/uploads/ec_cps_product_share/" + strconv.Itoa(int(productID)) + "_" + strconv.Itoa(int(userID)) + "ec_cps_product_share.jpg")
	if err != nil || miniRes.StatusCode != 200 {
		err, _ = GetQrCode(productID, userID, domain, productLink)
		if err != nil {
			return
		}
		miniRes, err = http.Get(domain + "/uploads/ec_cps_product_share/" + strconv.Itoa(int(productID)) + "_" + strconv.Itoa(int(userID)) + "ec_cps_product_share.jpg")
		if err != nil || miniRes.StatusCode != 200 {
			return
		}
	}
	defer miniRes.Body.Close()
	miniImg, _, err := image.Decode(miniRes.Body)
	if err != nil {
		return
	}
	miniImg = ImageResize(miniImg, 150, 150)
	// 合成二维码到背景图
	draw.Draw(newTemplateImage, miniImg.Bounds().Add(image.Pt(380, 820)), miniImg, image.Point{X: 0, Y: 0}, draw.Over)

	// 商品图片
	res, err := http.Get(productImage)
	if err != nil || res.StatusCode != 200 {
		return
	}
	defer res.Body.Close()
	productImg, _, err := image.Decode(res.Body)
	if err != nil {
		return
	}
	productImg = ImageResize(productImg, 500, 480)
	w := productImg.Bounds().Dx()
	h := productImg.Bounds().Dy()
	c := radius{p: image.Point{X: w, Y: h}, r: int(20)}
	radiusImg := image.NewRGBA(image.Rect(0, 0, w, h))
	draw.DrawMask(radiusImg, radiusImg.Bounds(), productImg, image.Point{}, &c, image.Point{}, draw.Over)
	// 处理图片圆角
	// 合成商品图到背景图
	draw.Draw(newTemplateImage, radiusImg.Bounds().Add(image.Pt(35, 150)), radiusImg, image.Point{X: 0, Y: 0}, draw.Over)
	//originPrice := fmt.Sprintf("%0.2f", float64(product.OriginPrice)/100)

	priceString := productPrice
	context := freetype.NewContext()
	context.SetClip(newTemplateImage.Bounds())
	context.SetDst(newTemplateImage)
	context.SetSrc(image.NewUniform(color.RGBA{R: 255, A: 255})) // 设置字体颜色 红色
	context.SetDPI(72)                                           // 设置字体分辨率
	context.SetFontSize(40)                                      // 设置字体大小
	context.SetFont(font)                                        // 设置字体样式，就是我们上面加载的字体
	_, err = context.DrawString("券后¥"+priceString, freetype.Pt(40, 710))
	/*context.SetFontSize(36)
	context.SetSrc(image.NewUniform(color.RGBA{R: 102, G: 102, B: 102, A: 1})) // 设置字体颜色 灰色
	_, err = context.DrawString("¥"+originPrice, freetype.Pt(250, 710))*/
	context.SetFontSize(30)
	context.SetSrc(image.NewUniform(color.RGBA{R: 53, G: 53, B: 53, A: 1})) // 设置字体颜色 黑色
	if len(productTitle) > 35 {
		productTitle = productTitle[0:34] + "..."
	}
	_, err = context.DrawString(productTitle, freetype.Pt(40, 750))
	/*for x := 235; x <= 425; x++ {
		newTemplateImage.Set(x, 716, image.White)
	}*/
	/*// 新图片
	file, _ := os.Create("/Users/<USER>/go/src/demo/small-shop/service/tools/poster.jpg")
	defer file.Close()

	png.Encode(file, newTemplateImage)

	//saveFile(newBgImg)
	return*/
	var path = "./data/goSupply/uploads/ec_cps_product_poster"
	var fileName string
	fileName = strconv.Itoa(int(productID)) + "_" + strconv.Itoa(int(userID)) + "ec_cps_product_poster.jpg"
	mkdirErr := os.MkdirAll(path, os.ModePerm)
	if mkdirErr != nil {
		err = errors.New("function os.MkdirAll() Filed, err:" + mkdirErr.Error())
		return
	}
	// 新图片
	file, _ := os.Create(path + "/" + fileName)
	defer file.Close()

	png.Encode(file, newTemplateImage)

	return err, domain + "/uploads/ec_cps_product_poster/" + fileName
}
func GetQrCode(productID, userID uint, domain string, urlLink string) (err error, link string) {
	var path = "uploads/ec_cps_product_share"
	var fileName string
	fileName = strconv.Itoa(int(productID)) + "_" + strconv.Itoa(int(userID)) + "ec_cps_product_share.jpg"
	mkdirErr := os.MkdirAll(path, os.ModePerm)
	if mkdirErr != nil {
		err = errors.New("function os.MkdirAll() Filed, err:" + mkdirErr.Error())
		return
	}

	filePath := path + "/" + fileName
	err = qrcode.WriteFile(urlLink, qrcode.Medium, 256, filePath)
	if err != nil {
		return
	}

	return err, domain + "/uploads/ec_cps_product_share/" + fileName
}
