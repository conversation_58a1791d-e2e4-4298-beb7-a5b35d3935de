package service

import (
	"errors"
	"image"
	"image/color"
	_ "image/gif"  // 支持GIF格式
	_ "image/jpeg" // 支持JPEG格式
	"image/png"
	_ "image/png" // 支持PNG格式
	"net/http"
	"os"
	"strconv"

	"github.com/golang/freetype"
	"github.com/golang/freetype/truetype"
	"github.com/nfnt/resize"
	"github.com/skip2/go-qrcode"
	"golang.org/x/image/draw"
)

// 移除了不再使用的全局变量

type circle struct { // 这里需要自己实现一个圆形遮罩，实现接口里的三个方法
	p image.Point // 圆心位置
	r int
}
type radius struct {
	p image.Point // 矩形右下角位置
	r int
}

func (c *radius) ColorModel() color.Model {
	return color.AlphaModel
}

func (c *radius) Bounds() image.Rectangle {
	return image.Rect(0, 0, c.p.X, c.p.Y)
}

func (c *radius) At(x, y int) color.Color {
	var dx, dy int
	r := c.r
	if x < r {
		dx = r - x
	} else if x > c.p.X-r {
		dx = x - (c.p.X - r)
	}
	if y < r {
		dy = r - y
	} else if y > c.p.Y-r {
		dy = y - (c.p.Y - r)
	}

	if dx*dx+dy*dy > r*r {
		return color.Alpha{0}
	}
	return color.Alpha{255}
}

func ImageResize(src image.Image, w, h int) image.Image {
	return resize.Resize(uint(w), uint(h), src, resize.Lanczos3)
}

func GetProductPoster(userID uint, productID uint, productImage string, productTitle string, productPrice string, domain string, productLink string) (err error, link string) {
	// 创建海报画布 (宽度400, 高度600)
	posterWidth := 400
	posterHeight := 600
	newTemplateImage := image.NewRGBA(image.Rect(0, 0, posterWidth, posterHeight))

	// 填充白色背景
	for y := 0; y < posterHeight; y++ {
		for x := 0; x < posterWidth; x++ {
			newTemplateImage.Set(x, y, color.RGBA{255, 255, 255, 255})
		}
	}

	// 加载字体文件
	font, err := loadFont()
	if err != nil {
		return err, ""
	}

	// 1. 绘制商品图片 (占据海报上半部分，约300px高度)
	productImg, err := loadProductImage(productImage)
	if err != nil {
		return err, ""
	}

	// 调整商品图片大小，保持比例，填充上半部分
	productImg = ImageResize(productImg, 380, 280)
	productImgBounds := productImg.Bounds()

	// 居中放置商品图片
	productX := (posterWidth - productImgBounds.Dx()) / 2
	productY := 20
	draw.Draw(newTemplateImage, productImgBounds.Add(image.Pt(productX, productY)), productImg, image.Point{X: 0, Y: 0}, draw.Over)

	// 2. 绘制黄色分割线 (在商品图片下方)
	yellowLineY := productY + productImgBounds.Dy() + 20
	for x := 10; x < posterWidth-10; x++ {
		for y := yellowLineY; y < yellowLineY+3; y++ {
			newTemplateImage.Set(x, y, color.RGBA{255, 255, 0, 255}) // 黄色
		}
	}
	// 3. 绘制商品标题 (在黄色分割线下方)
	context := freetype.NewContext()
	context.SetClip(newTemplateImage.Bounds())
	context.SetDst(newTemplateImage)
	context.SetFont(font)
	context.SetDPI(72)

	// 商品标题 - 黑色，较小字体
	titleY := yellowLineY + 25
	context.SetFontSize(16)
	context.SetSrc(image.NewUniform(color.RGBA{R: 0, G: 0, B: 0, A: 255})) // 黑色

	// 处理标题换行 (每行最多20个字符)
	titleLines := wrapText(productTitle, 20)
	for i, line := range titleLines {
		if i >= 2 { // 最多显示2行
			break
		}
		_, err = context.DrawString(line, freetype.Pt(20, titleY+i*25))
		if err != nil {
			return err, ""
		}
	}

	// 4. 绘制价格信息
	priceY := titleY + 60

	// 券后价格 - 红色，较大字体
	context.SetFontSize(24)
	context.SetSrc(image.NewUniform(color.RGBA{R: 255, G: 0, B: 0, A: 255})) // 红色
	_, err = context.DrawString("券后 ¥"+productPrice, freetype.Pt(20, priceY))
	if err != nil {
		return err, ""
	}

	// 原价 - 灰色，较小字体，带删除线
	context.SetFontSize(16)
	context.SetSrc(image.NewUniform(color.RGBA{R: 128, G: 128, B: 128, A: 255})) // 灰色
	_, err = context.DrawString("原价 ¥"+productPrice, freetype.Pt(20, priceY+25))
	if err != nil {
		return err, ""
	}

	// 5. 生成并绘制二维码
	qrCodeY := priceY + 50
	err = drawQRCode(newTemplateImage, productID, userID, domain, productLink, qrCodeY)
	if err != nil {
		return err, ""
	}

	// 6. 绘制二维码说明文字
	qrTextY := qrCodeY + 120
	context.SetFontSize(12)
	context.SetSrc(image.NewUniform(color.RGBA{R: 0, G: 0, B: 0, A: 255})) // 黑色
	_, err = context.DrawString("长按识别发现好货", freetype.Pt(280, qrTextY))
	if err != nil {
		return err, ""
	}

	// 7. 保存海报图片
	var path = "./data/goSupply/uploads/file/ec_cps_product_poster"
	var fileName = strconv.Itoa(int(productID)) + "_" + strconv.Itoa(int(userID)) + "ec_cps_product_poster.png"

	mkdirErr := os.MkdirAll(path, os.ModePerm)
	if mkdirErr != nil {
		return errors.New("function os.MkdirAll() Filed, err:" + mkdirErr.Error()), ""
	}

	file, err := os.Create(path + "/" + fileName)
	if err != nil {
		return err, ""
	}
	defer file.Close()

	err = png.Encode(file, newTemplateImage)
	if err != nil {
		return err, ""
	}

	return nil, domain + "/uploads/ec_cps_product_poster/" + fileName
}

// wrapText 文本换行处理
func wrapText(text string, maxCharsPerLine int) []string {
	var lines []string
	runes := []rune(text)

	for i := 0; i < len(runes); i += maxCharsPerLine {
		end := i + maxCharsPerLine
		if end > len(runes) {
			end = len(runes)
		}
		lines = append(lines, string(runes[i:end]))
	}

	return lines
}

// drawQRCode 绘制二维码到指定位置
func drawQRCode(img *image.RGBA, productID, userID uint, domain, productLink string, y int) error {
	// 生成二维码
	qrCode, err := qrcode.New(productLink, qrcode.Medium)
	if err != nil {
		return err
	}

	// 设置二维码大小
	qrCode.DisableBorder = true
	qrImg := qrCode.Image(100)

	// 将二维码绘制到海报右侧
	qrX := 280 // 右侧位置
	draw.Draw(img, qrImg.Bounds().Add(image.Pt(qrX, y)), qrImg, image.Point{X: 0, Y: 0}, draw.Over)

	return nil
}

// loadFont 加载字体文件，支持多种路径查找
func loadFont() (*truetype.Font, error) {
	// 定义可能的字体文件路径
	fontPaths := []string{
		"douyin-cps/service/simsun.ttf", // 相对于项目根目录
		"service/simsun.ttf",            // 相对于douyin-cps目录
		"./simsun.ttf",                  // 当前目录
		"../service/simsun.ttf",         // 上级目录的service
		"C:/Users/<USER>/GolandProjects/demo/douyin-cps/service/simsun.ttf", // 绝对路径（备用）
	}

	var lastErr error
	for _, fontPath := range fontPaths {
		fontData, err := os.ReadFile(fontPath)
		if err != nil {
			lastErr = err
			continue // 尝试下一个路径
		}

		// 解析字体数据
		font, err := truetype.Parse(fontData)
		if err != nil {
			lastErr = err
			continue
		}

		return font, nil // 成功加载字体
	}

	// 所有路径都失败了
	return nil, errors.New("无法加载字体文件 simsun.ttf，尝试的路径都失败了，最后错误: " + lastErr.Error())
}

// loadProductImage 加载商品图片，支持错误处理和默认图片
func loadProductImage(productImageURL string) (image.Image, error) {
	// 尝试从URL加载图片
	res, err := http.Get(productImageURL)
	if err != nil {
		return createDefaultProductImage(), nil // 使用默认图片
	}

	if res.StatusCode != 200 {
		res.Body.Close()
		return createDefaultProductImage(), nil // 使用默认图片
	}
	defer res.Body.Close()

	// 检查Content-Type
	//contentType := res.Header.Get("Content-Type")

	productImg, _, err := image.Decode(res.Body)
	if err != nil {
		// 如果解码失败，记录错误信息但使用默认图片
		return createDefaultProductImage(), nil
	}

	// 成功加载图片
	return productImg, nil
}

// createDefaultProductImage 创建默认的商品图片
func createDefaultProductImage() image.Image {
	// 创建一个380x280的默认图片
	width, height := 380, 280
	img := image.NewRGBA(image.Rect(0, 0, width, height))

	// 填充浅灰色背景
	lightGray := color.RGBA{240, 240, 240, 255}
	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			img.Set(x, y, lightGray)
		}
	}

	// 在中心绘制一个简单的图标或文字提示
	// 这里可以添加更复杂的默认图片设计
	centerX, centerY := width/2, height/2
	darkGray := color.RGBA{128, 128, 128, 255}

	// 绘制一个简单的矩形框作为占位符
	for x := centerX - 50; x <= centerX+50; x++ {
		img.Set(x, centerY-30, darkGray)
		img.Set(x, centerY+30, darkGray)
	}
	for y := centerY - 30; y <= centerY+30; y++ {
		img.Set(centerX-50, y, darkGray)
		img.Set(centerX+50, y, darkGray)
	}

	return img
}
func GetQrCode(productID, userID uint, domain string, urlLink string) (err error, link string) {
	var path = "uploads/ec_cps_product_share"
	var fileName string
	fileName = strconv.Itoa(int(productID)) + "_" + strconv.Itoa(int(userID)) + "ec_cps_product_share.jpg"
	mkdirErr := os.MkdirAll(path, os.ModePerm)
	if mkdirErr != nil {
		err = errors.New("function os.MkdirAll() Filed, err:" + mkdirErr.Error())
		return
	}

	filePath := path + "/" + fileName
	err = qrcode.WriteFile(urlLink, qrcode.Medium, 256, filePath)
	if err != nil {
		return
	}

	return err, domain + "/uploads/ec_cps_product_share/" + fileName
}
