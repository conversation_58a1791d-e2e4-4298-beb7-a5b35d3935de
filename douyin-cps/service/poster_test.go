package service

import (
	"fmt"
	"os"
	"testing"
)

// TestLoadFont 测试字体加载功能
func TestLoadFont(t *testing.T) {
	font, err := loadFont()
	if err != nil {
		t.<PERSON><PERSON><PERSON>("字体加载失败: %v", err)
		return
	}

	if font == nil {
		t.<PERSON>rror("字体加载成功但返回nil")
		return
	}

	fmt.Printf("字体加载成功: %s\n", font.Name(0))
}

// TestGetProductPoster 测试海报生成功能
func TestGetProductPoster(t *testing.T) {
	err, posterURL := GetProductPoster(
		1,
		1,
		"https://images.sursung.com/dis/dis/2515B53D689448A78ADE91D9BBA21F85_s286944_h800_w800.jpg",
		"五谷磨房小天才黑芝麻核桃丸婴儿童独立包装营养专食小天才芝麻丸1盒*84g",
		"48",
		"http://127.0.0.1",
		"https://www.baidu.com",
	)

	if err != nil {
		t.<PERSON><PERSON><PERSON>("海报生成失败: %v", err)
		return
	}

	fmt.Printf("海报生成成功: %s\n", posterURL)
}

// TestFontPaths 测试字体路径查找
func TestFontPaths(t *testing.T) {
	fontPaths := []string{
		"douyin-cps/service/simsun.ttf",
		"service/simsun.ttf",
		"./simsun.ttf",
		"../service/simsun.ttf",
	}

	fmt.Println("测试字体文件路径:")
	for i, path := range fontPaths {
		if _, err := os.Stat(path); err == nil {
			fmt.Printf("✓ 路径 %d: %s (存在)\n", i+1, path)
		} else {
			fmt.Printf("✗ 路径 %d: %s (不存在: %v)\n", i+1, path, err)
		}
	}
}

// TestWrapText 测试文本换行功能
func TestWrapText(t *testing.T) {
	testCases := []struct {
		input    string
		maxChars int
		expected int // 期望的行数
	}{
		{"短标题", 20, 1},
		{"这是一个比较长的商品标题，需要换行显示", 20, 2},
		{"五谷磨房小天才黑芝麻核桃丸婴儿童独立包装营养专食小天才芝麻丸1盒*84g", 20, 4},
	}

	for i, tc := range testCases {
		lines := wrapText(tc.input, tc.maxChars)

		fmt.Printf("测试用例 %d:\n", i+1)
		fmt.Printf("原文: %s\n", tc.input)
		fmt.Printf("换行结果 (%d行):\n", len(lines))
		for j, line := range lines {
			fmt.Printf("  第%d行: %s (长度: %d)\n", j+1, line, len([]rune(line)))
		}
		fmt.Println()
	}
}
