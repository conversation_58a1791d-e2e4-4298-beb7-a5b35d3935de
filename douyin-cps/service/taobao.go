package service

import (
	"encoding/json"
	model2 "finance/model"
	"math"
	"yz-go/source"
)

const tbkBaseURL = "/app/ecCpsCtrl/taobao/"

// 各个接口实现
func TcGeneralConvert(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(tbkBaseURL+"tcGeneralConvert", request)
}

func IdPrivilege(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(tbkBaseURL+"idPrivilege", request)
}

func TklPrivilege(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(tbkBaseURL+"tklPrivilege", request)
}

func ShopConvert(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(tbkBaseURL+"shopConvert", request)
}

func OrderDetails(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(tbkBaseURL+"orderDetails", request)
}

func ScPunishOrder(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(tbkBaseURL+"scPunishOrder", request)
}

func InvitecodeGet(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(tbkBaseURL+"invitecodeGet", request)
}

func SuperSearchMaterial(request map[string]interface{}, userID uint) (err error, response interface{}) {
	err, result := TbxRequest(tbkBaseURL+"superSearchMaterial", request)
	if err != nil {
		return
	}

	var materialResponse TbkMaterialRecommendResponse
	jsonData, err := json.Marshal(result)
	if err != nil {
		return
	}

	err = json.Unmarshal(jsonData, &materialResponse)
	if err != nil {
		return
	}
	var userModel model2.User
	err = source.DB().Preload("UserLevelInfo").First(&userModel, userID).Error
	if err != nil {
		return
	}
	for key, item := range materialResponse.Data {
		// 计算新的佣金金额并保留两位小数
		newCommissionAmount := math.Round(item.EcCpsInfo.CommissionAmount*float64(userModel.UserLevelInfo.CpsRatio)/10000*100) / 100
		newCommissionRate := math.Round(item.EcCpsInfo.CommissionRate*float64(userModel.UserLevelInfo.CpsRatio)/10000*100) / 100

		// 将计算结果转回字符串，保留两位小数
		materialResponse.Data[key].EcCpsInfo.CommissionAmount = newCommissionAmount
		materialResponse.Data[key].EcCpsInfo.CommissionRate = newCommissionRate
	}
	return nil, materialResponse
}

func TbItemInfo(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(tbkBaseURL+"itemInfo", request)
}

type TbkMaterialRecommendResponse struct {
	Code int `json:"code"`
	Data []struct {
		ItemBasicInfo struct {
			AnnualVol            string `json:"annual_vol"`
			BrandName            string `json:"brand_name"`
			CategoryId           int    `json:"category_id"`
			CategoryName         string `json:"category_name"`
			LevelOneCategoryId   int    `json:"level_one_category_id"`
			LevelOneCategoryName string `json:"level_one_category_name"`
			PictUrl              string `json:"pict_url"`
			RealPostFee          string `json:"real_post_fee"`
			SellerId             int64  `json:"seller_id"`
			ShopTitle            string `json:"shop_title"`
			ShortTitle           string `json:"short_title"`
			SmallImages          struct {
				String []string `json:"string"`
			} `json:"small_images"`
			SubTitle   string `json:"sub_title"`
			Title      string `json:"title"`
			UserType   int    `json:"user_type"`
			Volume     int    `json:"volume"`
			WhiteImage string `json:"white_image"`
		} `json:"item_basic_info"`
		ItemId      string `json:"item_id"`
		PresaleInfo struct {
			PresaleDeposit string `json:"presale_deposit"`
		} `json:"presale_info"`
		PricePromotionInfo struct {
			FinalPromotionPathList struct {
				FinalPromotionPathMapData []struct {
					PromotionDesc      string `json:"promotion_desc"`
					PromotionEndTime   string `json:"promotion_end_time"`
					PromotionFee       string `json:"promotion_fee"`
					PromotionId        string `json:"promotion_id"`
					PromotionStartTime string `json:"promotion_start_time"`
					PromotionTitle     string `json:"promotion_title"`
				} `json:"final_promotion_path_map_data"`
			} `json:"final_promotion_path_list"`
			FinalPromotionPrice string `json:"final_promotion_price"`
			PromotionTagList    struct {
				PromotionTagMapData []struct {
					TagName string `json:"tag_name"`
				} `json:"promotion_tag_map_data"`
			} `json:"promotion_tag_list"`
			ReservePrice string `json:"reserve_price"`
			ZkFinalPrice string `json:"zk_final_price"`
		} `json:"price_promotion_info"`
		PublishInfo struct {
			ClickUrl            string `json:"click_url"`
			DailyPromotionSales int    `json:"daily_promotion_sales"`
			IncomeInfo          struct {
				CommissionAmount string `json:"commission_amount"`
				CommissionRate   string `json:"commission_rate"`
				SubsidyAmount    string `json:"subsidy_amount"`
				SubsidyRate      string `json:"subsidy_rate"`
			} `json:"income_info"`
			IncomeRate            string `json:"income_rate"`
			TwoHourPromotionSales int    `json:"two_hour_promotion_sales"`
		} `json:"publish_info"`
		ScopeInfo struct {
			SuperiorBrand interface{} `json:"superior_brand"`
		} `json:"scope_info"`
		EcCpsInfo struct {
			CommissionAmount float64 `json:"commission_amount"`
			CommissionRate   float64 `json:"commission_rate"`
		} `json:"ec_cps_info"`
	} `json:"data"`
	IsDefault    string `json:"is_default"`
	Msg          string `json:"msg"`
	TotalResults int    `json:"total_results"`
}

func MaterialRecommend(request map[string]interface{}, userID uint) (err error, response interface{}) {
	err, result := TbxRequest(tbkBaseURL+"materialRecommend", request)
	if err != nil {
		return
	}

	var materialResponse TbkMaterialRecommendResponse
	jsonData, err := json.Marshal(result)
	if err != nil {
		return
	}

	err = json.Unmarshal(jsonData, &materialResponse)
	if err != nil {
		return
	}
	var userModel model2.User
	err = source.DB().Preload("UserLevelInfo").First(&userModel, userID).Error
	if err != nil {
		return
	}
	for key, item := range materialResponse.Data {
		// 计算新的佣金金额并保留两位小数
		newCommissionAmount := math.Round(item.EcCpsInfo.CommissionAmount*float64(userModel.UserLevelInfo.CpsRatio)/10000*100) / 100
		newCommissionRate := math.Round(item.EcCpsInfo.CommissionRate*float64(userModel.UserLevelInfo.CpsRatio)/10000*100) / 100

		// 将计算结果转回字符串，保留两位小数
		materialResponse.Data[key].EcCpsInfo.CommissionAmount = newCommissionAmount
		materialResponse.Data[key].EcCpsInfo.CommissionRate = newCommissionRate
	}
	return nil, materialResponse
}
