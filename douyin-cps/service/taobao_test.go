package service

import (
	"testing"
)

func TestSafeUnmarshalJSON(t *testing.T) {
	// 测试正常的JSON解析
	normalJSON := `{
		"code": 200,
		"data": [
			{
				"item_id": "123456",
				"ec_cps_info": {
					"commission_amount": 10.5,
					"commission_rate": 5.0
				}
			}
		],
		"msg": "success"
	}`

	var response TbkMaterialRecommendResponse
	err := SafeUnmarshalJSON([]byte(normalJSON), &response)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("正常JSON解析失败: %v", err)
	}

	if response.Code != 200 {
		t.<PERSON><PERSON><PERSON>("期望code为200，实际为%d", response.Code)
	}

	if len(response.Data) != 1 {
		t.<PERSON><PERSON>("期望data长度为1，实际为%d", len(response.Data))
	}

	// 测试缺少字段的JSON
	incompleteJSON := `{
		"code": 200,
		"data": [
			{
				"item_id": "123456"
			}
		]
	}`

	var incompleteResponse TbkMaterialRecommendResponse
	err = SafeUnmarshalJSON([]byte(incompleteJSON), &incompleteResponse)
	if err != nil {
		t.Errorf("不完整JSON解析失败: %v", err)
	}

	if incompleteResponse.Code != 200 {
		t.Errorf("期望code为200，实际为%d", incompleteResponse.Code)
	}

	// 验证缺少的字段为nil
	if len(incompleteResponse.Data) > 0 && incompleteResponse.Data[0].EcCpsInfo != nil {
		t.Error("期望EcCpsInfo为nil，但不是")
	}
}

func TestSafePointerFunctions(t *testing.T) {
	// 测试SafeStringValue
	str := "test"
	if SafeStringValue(&str) != "test" {
		t.Error("SafeStringValue失败")
	}
	if SafeStringValue(nil) != "" {
		t.Error("SafeStringValue nil处理失败")
	}

	// 测试SafeFloat64Value
	f := 10.5
	if SafeFloat64Value(&f) != 10.5 {
		t.Error("SafeFloat64Value失败")
	}
	if SafeFloat64Value(nil) != 0.0 {
		t.Error("SafeFloat64Value nil处理失败")
	}

	// 测试指针创建函数
	strPtr := StringPtr("test")
	if *strPtr != "test" {
		t.Error("StringPtr失败")
	}

	floatPtr := Float64Ptr(10.5)
	if *floatPtr != 10.5 {
		t.Error("Float64Ptr失败")
	}
}

func TestJSONWithExtraFields(t *testing.T) {
	// 测试包含额外未知字段的JSON
	jsonWithExtra := `{
		"code": 200,
		"data": [
			{
				"item_id": "123456",
				"unknown_field": "unknown_value",
				"ec_cps_info": {
					"commission_amount": 10.5,
					"commission_rate": 5.0,
					"extra_field": "extra_value"
				},
				"nested_unknown": {
					"field1": "value1",
					"field2": 123
				}
			}
		],
		"msg": "success",
		"extra_top_level": "extra_value"
	}`

	var response TbkMaterialRecommendResponse
	err := SafeUnmarshalJSON([]byte(jsonWithExtra), &response)
	if err != nil {
		t.Errorf("包含额外字段的JSON解析失败: %v", err)
	}

	if response.Code != 200 {
		t.Errorf("期望code为200，实际为%d", response.Code)
	}

	if len(response.Data) != 1 {
		t.Errorf("期望data长度为1，实际为%d", len(response.Data))
	}

	// 验证已知字段正确解析
	item := response.Data[0]
	if item.ItemId == nil || *item.ItemId != "123456" {
		t.Error("ItemId解析失败")
	}

	if item.EcCpsInfo == nil {
		t.Error("EcCpsInfo为nil")
	} else {
		if item.EcCpsInfo.CommissionAmount == nil || *item.EcCpsInfo.CommissionAmount != 10.5 {
			t.Error("CommissionAmount解析失败")
		}
		if item.EcCpsInfo.CommissionRate == nil || *item.EcCpsInfo.CommissionRate != 5.0 {
			t.Error("CommissionRate解析失败")
		}
	}
}

func TestMalformedJSON(t *testing.T) {
	// 测试格式错误的JSON
	malformedJSON := `{
		"code": 200,
		"data": [
			{
				"item_id": "123456",
				"ec_cps_info": {
					"commission_amount": "not_a_number",
					"commission_rate": 5.0
				}
			}
		],
		"msg": "success"
	}`

	var response TbkMaterialRecommendResponse
	err := SafeUnmarshalJSON([]byte(malformedJSON), &response)

	// 这种情况下应该能解析，但commission_amount字段会被忽略或设为默认值
	if err != nil {
		t.Logf("格式错误的JSON解析结果: %v", err)
		// 这是可以接受的，因为类型不匹配
	}
}
