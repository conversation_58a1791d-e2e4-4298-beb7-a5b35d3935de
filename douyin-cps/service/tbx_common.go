package service

import (
	"douyin-cps/model"
	"douyin-cps/utils"
	"encoding/json"
)

// 通用请求处理函数
func TbxRequest(apiUrl string, request map[string]interface{}) (err error, response interface{}) {
	var setting model.CpsValue
	err, setting = model.GetCpsSetting()
	if err != nil {
		return
	}

	// 使用 utils.NewSelfAPIRequest 创建请求对象并调用 Execute 方法
	// 注意：这里假设 TbxRequest 也需要使用 setting 中的 EcCpsHost、EcCpsAppKey 和 EcCpsAppSecret
	// 如果需要使用其他参数，请相应调整
	err, result := utils.NewSelfAPIRequest(setting.EcCpsHost, setting.EcCpsAppKey, setting.EcCpsAppSecret).Execute(apiUrl, request)
	if err != nil {
		return
	}
	err = json.Unmarshal(result, &response)
	return
}
