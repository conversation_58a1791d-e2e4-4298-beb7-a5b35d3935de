package service

import (
	"encoding/json"
	model2 "finance/model"
	"math"
	"yz-go/source"
)

const vipBaseURL = "/app/ecCpsCtrl/"

// VIP接口实现
func GoodsList(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(vipBaseURL+"vip/goodsList", request)
}

type TbkVipItemInfo struct {
	Code int `json:"code"`
	Data struct {
		AdCode                 string `json:"adCode"`
		BrandId                int    `json:"brandId"`
		BrandLogoFull          string `json:"brandLogoFull"`
		BrandName              string `json:"brandName"`
		BrandStoreSn           string `json:"brandStoreSn"`
		CampaignCommissionInfo struct {
			IsCampaignCommission int `json:"isCampaignCommission"`
		} `json:"campaignCommissionInfo"`
		Cat1StId     int    `json:"cat1stId"`
		Cat1StName   string `json:"cat1stName"`
		Cat2NdId     int    `json:"cat2ndId"`
		Cat2NdName   string `json:"cat2ndName"`
		CategoryId   int    `json:"categoryId"`
		CategoryName string `json:"categoryName"`
		CommentsInfo struct {
			Comments          int    `json:"comments"`
			CommentsText      string `json:"commentsText"`
			GoodCommentsShare string `json:"goodCommentsShare"`
		} `json:"commentsInfo"`
		Commission            string   `json:"commission"`
		CommissionRate        string   `json:"commissionRate"`
		CouponPriceType       int      `json:"couponPriceType"`
		DestUrl               string   `json:"destUrl"`
		DestUrlPc             string   `json:"destUrlPc"`
		Discount              string   `json:"discount"`
		EstimatePrice         string   `json:"estimatePrice"`
		GoodsCarouselPictures []string `json:"goodsCarouselPictures"`
		GoodsDetailPictures   []string `json:"goodsDetailPictures"`
		GoodsId               string   `json:"goodsId"`
		GoodsMainPicture      string   `json:"goodsMainPicture"`
		GoodsName             string   `json:"goodsName"`
		GoodsPromotionInfo    struct {
			Discount      string `json:"discount"`
			LowPriceTag   string `json:"lowPriceTag"`
			MarketPrice   string `json:"marketPrice"`
			SalePrice     string `json:"salePrice"`
			SalePriceDesc string `json:"salePriceDesc"`
		} `json:"goodsPromotionInfo"`
		GoodsThumbUrl          string `json:"goodsThumbUrl"`
		HaiTao                 int    `json:"haiTao"`
		IsAllowanceGoods       int    `json:"isAllowanceGoods"`
		IsSubsidyActivityGoods bool   `json:"isSubsidyActivityGoods"`
		JoinedActivities       []struct {
			ActName           string `json:"actName"`
			ActType           int    `json:"actType"`
			BeginTime         int64  `json:"beginTime"`
			EndTime           int64  `json:"endTime"`
			ForeShowBeginTime int64  `json:"foreShowBeginTime"`
		} `json:"joinedActivities"`
		MarketPrice string `json:"marketPrice"`
		PrepayInfo  struct {
			IsPrepay int `json:"isPrepay"`
		} `json:"prepayInfo"`
		ProductSales    string `json:"productSales"`
		SaleStockStatus int    `json:"saleStockStatus"`
		SchemeEndTime   int64  `json:"schemeEndTime"`
		SchemeStartTime int64  `json:"schemeStartTime"`
		SellTimeFrom    int64  `json:"sellTimeFrom"`
		SellTimeTo      int64  `json:"sellTimeTo"`
		Sn              string `json:"sn"`
		SourceType      int    `json:"sourceType"`
		SpuId           string `json:"spuId"`
		Status          int    `json:"status"`
		StoreInfo       struct {
			StoreId   string `json:"storeId"`
			StoreName string `json:"storeName"`
		} `json:"storeInfo"`
		StoreServiceCapability struct {
			StoreRankRate string `json:"storeRankRate"`
			StoreScore    string `json:"storeScore"`
		} `json:"storeServiceCapability"`
		VipPrice  string `json:"vipPrice"`
		Weight    int    `json:"weight"`
		EcCpsInfo struct {
			CommissionAmount float64 `json:"commission_amount"`
			CommissionRate   float64 `json:"commission_rate"`
		} `json:"ec_cps_info"`
	} `json:"data"`
	Msg string `json:"msg"`
}

func ItemInfo(request map[string]interface{}, userID uint) (err error, response interface{}) {
	err, result := TbxRequest(vipBaseURL+"vip/itemInfo", request)
	if err != nil {
		return
	}

	var materialResponse TbkVipItemInfo
	jsonData, err := json.Marshal(result)
	if err != nil {
		return
	}

	err = json.Unmarshal(jsonData, &materialResponse)
	if err != nil {
		return
	}
	var userModel model2.User
	err = source.DB().Preload("UserLevelInfo").First(&userModel, userID).Error
	if err != nil {
		return
	}
	// 计算新的佣金金额并保留两位小数
	newCommissionAmount := math.Round(materialResponse.Data.EcCpsInfo.CommissionAmount*float64(userModel.UserLevelInfo.CpsRatio)/10000*100) / 100
	newCommissionRate := math.Round(materialResponse.Data.EcCpsInfo.CommissionRate*float64(userModel.UserLevelInfo.CpsRatio)/10000*100) / 100
	// 将计算结果转回字符串，保留两位小数
	materialResponse.Data.EcCpsInfo.CommissionAmount = newCommissionAmount
	materialResponse.Data.EcCpsInfo.CommissionRate = newCommissionRate

	return nil, materialResponse
}

type TbkVipQueryRequest struct {
	Code int `json:"code"`
	Data []struct {
		AdCode                 string `json:"adCode"`
		BrandId                int    `json:"brandId"`
		BrandLogoFull          string `json:"brandLogoFull"`
		BrandName              string `json:"brandName"`
		BrandStoreSn           string `json:"brandStoreSn"`
		CampaignCommissionInfo struct {
			IsCampaignCommission int `json:"isCampaignCommission"`
		} `json:"campaignCommissionInfo"`
		Cat1StId              int      `json:"cat1stId"`
		Cat1StName            string   `json:"cat1stName"`
		Cat2NdId              int      `json:"cat2ndId"`
		Cat2NdName            string   `json:"cat2ndName"`
		CategoryId            int      `json:"categoryId"`
		CategoryName          string   `json:"categoryName"`
		Commission            string   `json:"commission"`
		CommissionRate        string   `json:"commissionRate"`
		CouponPriceType       int      `json:"couponPriceType"`
		DestUrl               string   `json:"destUrl"`
		DestUrlPc             string   `json:"destUrlPc"`
		Discount              string   `json:"discount"`
		EstimatePrice         string   `json:"estimatePrice"`
		GoodsCarouselPictures []string `json:"goodsCarouselPictures"`
		GoodsDetailPictures   []string `json:"goodsDetailPictures"`
		GoodsId               string   `json:"goodsId"`
		GoodsMainPicture      string   `json:"goodsMainPicture"`
		GoodsName             string   `json:"goodsName"`
		GoodsPromotionInfo    struct {
			Discount      string `json:"discount"`
			LowPriceTag   string `json:"lowPriceTag"`
			MarketPrice   string `json:"marketPrice"`
			SalePrice     string `json:"salePrice"`
			SalePriceDesc string `json:"salePriceDesc"`
		} `json:"goodsPromotionInfo"`
		GoodsThumbUrl          string `json:"goodsThumbUrl"`
		HaiTao                 int    `json:"haiTao"`
		IsAllowanceGoods       int    `json:"isAllowanceGoods"`
		IsSubsidyActivityGoods bool   `json:"isSubsidyActivityGoods"`
		MarketPrice            string `json:"marketPrice"`
		ProductSales           string `json:"productSales"`
		SchemeEndTime          int64  `json:"schemeEndTime"`
		SchemeStartTime        int64  `json:"schemeStartTime"`
		SellTimeFrom           int64  `json:"sellTimeFrom"`
		SellTimeTo             int64  `json:"sellTimeTo"`
		Sn                     string `json:"sn"`
		SourceType             int    `json:"sourceType"`
		SpuId                  string `json:"spuId"`
		Status                 int    `json:"status"`
		StoreInfo              struct {
			StoreId   string `json:"storeId"`
			StoreName string `json:"storeName"`
		} `json:"storeInfo"`
		VipPrice  string `json:"vipPrice"`
		Weight    int    `json:"weight"`
		EcCpsInfo struct {
			CommissionAmount float64 `json:"commission_amount"`
			CommissionRate   float64 `json:"commission_rate"`
		} `json:"ec_cps_info"`
	} `json:"data"`
	Msg          string `json:"msg"`
	TotalResults int    `json:"total_results"`
}

func VipQuery(request map[string]interface{}, userID uint) (err error, response interface{}) {
	err, result := TbxRequest(vipBaseURL+"vip/query", request)
	if err != nil {
		return
	}

	var materialResponse TbkVipQueryRequest
	jsonData, err := json.Marshal(result)
	if err != nil {
		return
	}

	err = json.Unmarshal(jsonData, &materialResponse)
	if err != nil {
		return
	}
	var userModel model2.User
	err = source.DB().Preload("UserLevelInfo").First(&userModel, userID).Error
	if err != nil {
		return
	}
	for key, item := range materialResponse.Data {
		// 计算新的佣金金额并保留两位小数
		newCommissionAmount := math.Round(item.EcCpsInfo.CommissionAmount*float64(userModel.UserLevelInfo.CpsRatio)/10000*100) / 100
		newCommissionRate := math.Round(item.EcCpsInfo.CommissionRate*float64(userModel.UserLevelInfo.CpsRatio)/10000*100) / 100

		// 将计算结果转回字符串，保留两位小数
		materialResponse.Data[key].EcCpsInfo.CommissionAmount = newCommissionAmount
		materialResponse.Data[key].EcCpsInfo.CommissionRate = newCommissionRate
	}
	return nil, materialResponse
}

func ViplinkCheck(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(vipBaseURL+"vip/viplinkCheck", request)
}

func SimilarRecommend(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(vipBaseURL+"vip/similarRecommend", request)
}

func UserRecommend(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(vipBaseURL+"vip/userRecommend", request)
}

func ItemInfo2(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(vipBaseURL+"vip/itemInfo2", request)
}

func VipOrderDetails(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(vipBaseURL+"vip/orderFetails", request)
}

func VipOrderDetails2(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(vipBaseURL+"vip/orderFetails2", request)
}

func VipIdPrivilege(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(vipBaseURL+"vip/idPrivilege", request)
}

func VipUrlPrivilege(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest("vip/urlPrivilege", request)
}

func GetVipAccess(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest("user/getVipAccess", request)
}

func RefreshToken(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest("vip/refreshToken", request)
}
