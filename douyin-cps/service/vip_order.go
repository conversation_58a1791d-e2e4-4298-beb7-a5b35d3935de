package service

import (
	"douyin-cps/model"
	model2 "finance/model"
	"fmt"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"time"
	"yz-go/component/log"
	"yz-go/source"
)

// GetVipOrderList 获取唯品会订单列表
func GetVipOrderList(req map[string]interface{}) (error, int64, []model.VipOrder) {
	var orders []model.VipOrder
	var total int64

	// 构建查询
	query := source.DB().Model(&model.VipOrder{})

	// 添加应用ID过滤
	if appID, ok := req["app_id"].(float64); ok && appID > 0 {
		query = query.Where("app_id = ?", int(appID))
	}

	// 添加商城会员ID过滤
	if appUserID, ok := req["app_user_id"].(float64); ok && appUserID > 0 {
		query = query.Where("app_user_id = ?", int(appUserID))
	}

	// 添加订单号过滤
	if orderSn, ok := req["order_sn"].(string); ok && orderSn != "" {
		query = query.Where("order_sn = ?", orderSn)
	}

	// 添加订单状态过滤
	if status, ok := req["status"].(float64); ok {
		query = query.Where("status = ?", int16(status))
	}

	// 添加时间范围过滤
	if startTime, ok := req["start_time"].(string); ok && startTime != "" {
		// 将时间字符串转换为毫秒时间戳
		st, err := time.Parse("2006-01-02 15:04:05", startTime)
		if err == nil {
			query = query.Where("sync_time >= ?", st.UnixMilli())
		}
	}
	if endTime, ok := req["end_time"].(string); ok && endTime != "" {
		// 将时间字符串转换为毫秒时间戳
		et, err := time.Parse("2006-01-02 15:04:05", endTime)
		if err == nil {
			query = query.Where("sync_time <= ?", et.UnixMilli())
		}
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		log.Log().Error("查询唯品会订单总数失败", zap.Error(err))
		return err, 0, nil
	}

	// 获取分页参数
	page := 1
	if p, ok := req["page"].(float64); ok {
		page = int(p)
	}

	pageSize := 10
	if ps, ok := req["page_size"].(float64); ok {
		pageSize = int(ps)
	}

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 添加排序
	orderBy := "sync_time DESC"
	if ob, ok := req["order_by"].(string); ok && ob != "" {
		orderBy = ob
	}

	// 查询数据
	err = query.Order(orderBy).Offset(offset).Limit(pageSize).Find(&orders).Error
	if err != nil {
		log.Log().Error("查询唯品会订单列表失败", zap.Error(err))
		return err, 0, nil
	}
	if userID, ok := req["user_id"].(int); ok && userID > 0 {
		var userModel model2.User
		err = source.DB().Preload("UserLevelInfo").First(&userModel, req["user_id"]).Error
		if err != nil {
			return fmt.Errorf("查询唯品会订单列表失败: %v", err), 0, nil
		}
		for key, item := range orders {
			orders[key].ActualCommission = item.ActualCommission * float64(userModel.UserLevelInfo.CpsRatio) / 10000
		}
	}

	return nil, total, orders
}

// GetVipOrderDetail 获取唯品会订单详情
func GetVipOrderDetail(orderSn string) (error, model.VipOrder) {
	var order model.VipOrder
	err := source.DB().Where("order_sn = ?", orderSn).First(&order).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("订单不存在"), model.VipOrder{}
		}
		log.Log().Error("查询唯品会订单详情失败", zap.String("订单号", orderSn), zap.Error(err))
		return err, model.VipOrder{}
	}

	// 查询订单详情
	var details []model.VipOrderDetail
	err = source.DB().Where("order_sn = ?", orderSn).Find(&details).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		log.Log().Error("查询订单详情失败", zap.Error(err))
	}

	// 将详情添加到订单中
	order.DetailList = details

	return nil, order
}

// ExportVipOrders 导出唯品会订单
func ExportVipOrders(req map[string]interface{}) error {
	// 构建查询
	query := source.DB().Model(&model.VipOrder{})

	// 添加应用ID过滤
	if appID, ok := req["app_id"].(float64); ok && appID > 0 {
		query = query.Where("app_id = ?", int(appID))
	}

	// 添加中台ID过滤
	if parentAppID, ok := req["parent_app_id"].(float64); ok && parentAppID > 0 {
		query = query.Where("parent_app_id = ?", int(parentAppID))
	}

	// 添加商城会员ID过滤
	if appUserID, ok := req["app_user_id"].(float64); ok && appUserID > 0 {
		query = query.Where("app_user_id = ?", int(appUserID))
	}

	// 添加订单号过滤
	if orderSn, ok := req["order_sn"].(string); ok && orderSn != "" {
		query = query.Where("order_sn = ?", orderSn)
	}

	// 添加订单状态过滤
	if status, ok := req["status"].(float64); ok {
		query = query.Where("status = ?", int16(status))
	}

	// 添加时间范围过滤
	if startTime, ok := req["start_time"].(string); ok && startTime != "" {
		// 将时间字符串转换为毫秒时间戳
		st, err := time.Parse("2006-01-02 15:04:05", startTime)
		if err == nil {
			query = query.Where("order_time >= ?", st.UnixMilli())
		}
	}
	if endTime, ok := req["end_time"].(string); ok && endTime != "" {
		// 将时间字符串转换为毫秒时间戳
		et, err := time.Parse("2006-01-02 15:04:05", endTime)
		if err == nil {
			query = query.Where("order_time <= ?", et.UnixMilli())
		}
	}

	// 添加排序
	orderBy := "sync_time DESC"
	if ob, ok := req["order_by"].(string); ok && ob != "" {
		orderBy = ob
	}

	// 查询数据
	var orders []model.VipOrder
	err := query.Order(orderBy).Find(&orders).Error
	if err != nil {
		log.Log().Error("查询唯品会订单列表失败", zap.Error(err))
		return err
	}

	// 这里可以实现导出功能，或者调用已有的导出功能
	// 为简化示例，这里只记录日志
	log.Log().Info("导出唯品会订单", zap.Int("数量", len(orders)))
	return nil
}
