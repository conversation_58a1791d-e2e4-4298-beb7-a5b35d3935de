package utils

import (
    "crypto/hmac"
    "crypto/md5"
    "crypto/sha256"
    "encoding/hex"
    "sort"
    "strings"
)

const (
    SignMethodMD5        = "md5"
    SignMethodHMAC       = "hmac"
    SignMethodHMACSHA256 = "hmac-sha256"
)

// GenerateSign 生成TOP签名
func GenerateSign(params map[string]string, secret string, signMethod string) string {
    // 1. 删除sign参数
    delete(params, "sign")

    // 2. 按照参数名ASCII码顺序排序
    keys := make([]string, 0, len(params))
    for k := range params {
        keys = append(keys, k)
    }
    sort.Strings(keys)

    // 3. 拼接参数名和参数值
    var query strings.Builder
    if signMethod == SignMethodMD5 {
        query.WriteString(secret)
    }
    for _, k := range keys {
        v := params[k]
        if k != "" && v != "" {
            query.WriteString(k)
            query.WriteString(v)
        }
    }

    // 4. 根据不同的签名方法进行加密
    var signBytes []byte
    switch signMethod {
    case SignMethodHMAC:
        signBytes = encryptHMAC(query.String(), secret)
    case SignMethodHMACSHA256:
        signBytes = encryptHMACSHA256(query.String(), secret)
    default: // SignMethodMD5
        query.WriteString(secret)
        signBytes = encryptMD5(query.String())
    }

    // 5. 将二进制转化为大写的十六进制
    return strings.ToUpper(hex.EncodeToString(signBytes))
}

// encryptMD5 MD5加密
func encryptMD5(data string) []byte {
    h := md5.New()
    h.Write([]byte(data))
    return h.Sum(nil)
}

// encryptHMAC HMAC-MD5加密
func encryptHMAC(data, secret string) []byte {
    h := hmac.New(md5.New, []byte(secret))
    h.Write([]byte(data))
    return h.Sum(nil)
}

// encryptHMACSHA256 HMAC-SHA256加密
func encryptHMACSHA256(data, secret string) []byte {
    h := hmac.New(sha256.New, []byte(secret))
    h.Write([]byte(data))
    return h.Sum(nil)
}