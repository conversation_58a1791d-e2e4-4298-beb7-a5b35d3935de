package utils

import (
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"time"
)

const (
	TaobaoGatewayURL = "https://gw.api.taobao.com/router/rest"
)

type TaobaoAPIRequest struct {
	Method     string
	AppKey     string
	AppSecret  string
	Session    string
	Format     string
	Version    string
	SignMethod string
	Timestamp  string
	BizParams  map[string]string
}

// NewTaobaoAPIRequest 创建新的淘宝API请求
func NewTaobaoAPIRequest(method, appKey, appSecret string) *TaobaoAPIRequest {
	return &TaobaoAPIRequest{
		Method:     method,
		AppKey:     appKey,
		AppSecret:  appSecret,
		Format:     "json",
		Version:    "2.0",
		SignMethod: SignMethodMD5,
		Timestamp:  time.Now().Format("2006-01-02 15:04:05"),
		BizParams:  make(map[string]string),
	}
}

// Execute 执行API请求
func (r *TaobaoAPIRequest) Execute() ([]byte, error) {
	// 1. 构建参数map
	params := r.buildParams()

	// 2. 生成签名
	sign := GenerateSign(params, r.AppSecret, r.SignMethod)
	params["sign"] = sign

	// 3. 构建请求URL
	var requestURL string
	var requestBody url.Values

	if r.isGetRequest() {
		// GET请求：所有参数放在URL中
		queryString := r.buildQueryString(params)
		requestURL = fmt.Sprintf("%s?%s", TaobaoGatewayURL, queryString)
	} else {
		// POST请求：系统参数放在URL中，业务参数放在body中
		sysParams, bizParams := r.splitParams(params)
		queryString := r.buildQueryString(sysParams)
		requestURL = fmt.Sprintf("%s?%s", TaobaoGatewayURL, queryString)
		requestBody = r.buildRequestBody(bizParams)
	}

	// 4. 发送请求
	var resp *http.Response
	var err error

	if r.isGetRequest() {
		resp, err = http.Get(requestURL)
	} else {
		resp, err = http.PostForm(requestURL, requestBody)
	}

	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	// 5. 读取响应
	return ioutil.ReadAll(resp.Body)
}

// buildParams 构建所有参数
func (r *TaobaoAPIRequest) buildParams() map[string]string {
	params := map[string]string{
		"method":      r.Method,
		"app_key":     r.AppKey,
		"timestamp":   r.Timestamp,
		"format":      r.Format,
		"v":           r.Version,
		"sign_method": r.SignMethod,
	}

	if r.Session != "" {
		params["session"] = r.Session
	}

	// 添加业务参数
	for k, v := range r.BizParams {
		params[k] = v
	}

	return params
}

// buildQueryString 构建URL查询字符串
func (r *TaobaoAPIRequest) buildQueryString(params map[string]string) string {
	values := url.Values{}
	for k, v := range params {
		values.Add(k, v)
	}
	return values.Encode()
}

// isGetRequest 判断是否应该使用GET请求
func (r *TaobaoAPIRequest) isGetRequest() bool {
	// 计算请求URL的预计长度
	totalLen := 0
	for k, v := range r.buildParams() {
		totalLen += len(k) + len(v)
	}
	return totalLen < 1024
}

// splitParams 分离系统参数和业务参数
func (r *TaobaoAPIRequest) splitParams(params map[string]string) (sysParams, bizParams map[string]string) {
	sysParams = make(map[string]string)
	bizParams = make(map[string]string)

	sysParamNames := map[string]bool{
		"method": true, "app_key": true, "timestamp": true,
		"format": true, "v": true, "sign_method": true,
		"sign": true, "session": true,
	}

	for k, v := range params {
		if sysParamNames[k] {
			sysParams[k] = v
		} else {
			bizParams[k] = v
		}
	}

	return
}

// buildRequestBody 构建POST请求的body
func (r *TaobaoAPIRequest) buildRequestBody(params map[string]string) url.Values {
	values := url.Values{}
	for k, v := range params {
		values.Add(k, v)
	}
	return values
}
