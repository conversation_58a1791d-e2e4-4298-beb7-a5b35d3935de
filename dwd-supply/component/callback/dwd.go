package callback

import (
	"dwd-supply/component/order"
	"errors"
	"go.uber.org/zap"
	pubmodel "public-supply/model"
	"strconv"
	"strings"
	"yz-go/component/log"
	"yz-go/model"
	"yz-go/source"
)

type Dwd struct{}

func (*Dwd) CallBackMessage(request pubmodel.RequestCallBackType) (err error) {

	log.Log().Info("dwd CallBackMessage 消息", zap.Any("info", request))
	var sysSettings []model.SysSetting
	err = source.DB().Where("`key` like '%gatherSupply%'").Find(&sysSettings).Error
	if err != nil {
		return
	}
	var gatherSupplyId string
	for _, sysSetting := range sysSettings {
		if strings.Contains(sysSetting.Value, request.Appid) {
			gatherSupplyId = sysSetting.Key[12:]
			break
		}
	}
	if gatherSupplyId == "" {
		err = errors.New("未找到供应链信息")
		return
	}
	var dwdOrder = order.Dwd{}
	var gsID int
	gsID, err = strconv.Atoi(gatherSupplyId)
	if err != nil {
		return
	}
	err = dwdOrder.InitSetting(uint(gsID))
	if err != nil {
		return
	}

	if request.Subject == "ORDER" && request.Event == "DELIVERY" {
		//用stbz的data数据（避免冲突）
		request.DwdData = source.Strval(request.Data)
		err = dwdOrder.DwdOrderSend(request.DwdCallBackType)
	}
	if request.Subject == "REFUND" && request.Event == "RESULT" {
		request.DwdData = source.Strval(request.Data)
		err = dwdOrder.DwdAfterSaleHandle(request.DwdCallBackType)
	}
	return

}
