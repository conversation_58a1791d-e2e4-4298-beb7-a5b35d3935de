package goods

import (
	model3 "category/model"
	"dwd-supply/model"
	response2 "dwd-supply/response"
	"encoding/json"
	"errors"
	"fmt"
	gatherSupplyRequest "gather-supply/request"
	log2 "log"
	url2 "net/url"
	pmodel "product/model"
	"product/mq"
	service2 "product/service"
	callback2 "public-supply/callback"
	"public-supply/common"
	publicModel "public-supply/model"
	"public-supply/request"
	"public-supply/service"
	"public-supply/setting"
	"strconv"
	"strings"
	"sync"
	"time"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"

	"github.com/chenhg5/collection"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type Dwd struct{}

func (self *Dwd) ManuallyProductUpdate(productID uint) (err error) {
	//TODO implement me
	panic("implement me")
}

func (self *Dwd) SynchronizeProductsToLocal() (err error) {
	//TODO implement me
	panic("implement me")
}

var wg sync.WaitGroup

func (self *Dwd) ImportSelectGoodsRun(info publicModel.SelectGoods) (err error, list interface{}) {
	orderPN := GetOrderNo()
	common.GlobalOrderSN = orderPN
	goodsRecord := publicModel.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: len(info.List),
		Status:            1,
	}
	var resultInt []int
	var field string
	field = "source_goods_id"

	err = source.DB().Model(pmodel.Product{}).Where("gather_supply_id=?", info.GatherSupplyID).Pluck(field, &resultInt).Error
	if err != nil {
		log2.Println("查询供应链商品id错误", err)
		return
	}

	if len(info.List) <= 0 {
		log2.Println("导入空数据")
		err = errors.New("导入的是空数据")
		return
	}

	var idsArr []int
	idsArr = GetIdArr(info.List)

	difference := collection.Collect(idsArr).Diff(resultInt).ToIntArray()
	repeat := len(idsArr) - len(difference)
	goodsRecord.RepeatQuantity = repeat //重复数据
	if len(difference) <= 0 {
		goodsRecord.Status = 2
		goodsRecord.CompletionStatus = 1
		source.DB().Omit("goods_arr").Create(&goodsRecord) //当前没有商品可以导入
		return
	}
	source.DB().Omit("goods_arr").Create(&goodsRecord) //创建导入记录
	var goodsList []publicModel.Goods
	for _, v := range difference {
		for _, item := range info.List {
			if item.ID == v {
				goodsList = append(goodsList, item)
			}
		}

	}
	info.List = goodsList
	arrList := splitArray(info.List, 20)
	for index, item := range arrList {
		wg.Add(1)
		fmt.Println("循环", index)
		self.RunSelectGoodsConcurrent(orderPN, item, info.Categorys, info.Key, info.GatherSupplyID)
	}
	wg.Wait()
	err = SetImportRecordCompletion(orderPN)
	fmt.Println("全部完成：")

	return
}

func (self *Dwd) RunSelectGoodsConcurrent(orderPN string, list []publicModel.Goods, category string, key string, gatherSupplyID uint) (err error) {

	defer wg.Done()

	cateList := strings.Split(category, ",")
	var cateId1, cateId2, cateId3 int
	cateId1, err = strconv.Atoi(cateList[0])
	cateId2, err = strconv.Atoi(cateList[1])
	cateId3, err = strconv.Atoi(cateList[2])

	var listGoods []*pmodel.Product
	var recordError []publicModel.SupplyGoodsImportRecordErrors
	err, listGoods, recordError = self.CommodityAssembly(list, cateId1, cateId2, cateId3, gatherSupplyID, 0)
	if err != nil {
		return
	}
	if len(listGoods) > 0 {
		err = service.FinalProcessing(listGoods, orderPN)
		if err != nil {
			return
		}

	}
	if len(recordError) > 0 {
		err = service.FinalProcessingError(recordError, orderPN)
		if err != nil {
			return
		}
	}
	return

}
func splitArray(arr []publicModel.Goods, num int64) [][]publicModel.Goods {
	max := int64(len(arr))
	//判断数组大小是否小于等于指定分割大小的值，是则把原数组放入二维数组返回
	if max <= num {
		return [][]publicModel.Goods{arr}
	}
	//获取应该数组分割为多少份
	var quantity int64
	if max%num == 0 {
		quantity = max / num
	} else {
		quantity = (max / num) + 1
	}

	fmt.Println("quantity数量：", quantity)
	//声明分割好的二维数组
	var segments = make([][]publicModel.Goods, 0)
	//声明分割数组的截止下标
	var start, end, i int64
	for i = 1; i <= quantity; i++ {
		end = i * num
		if i != quantity {
			segments = append(segments, arr[start:end])
		} else {
			segments = append(segments, arr[start:])
		}
		start = i * num
	}
	return segments
}
func (s *Dwd) ValidateConfig(params gatherSupplyRequest.ValidateConfigParams) (err error) {
	if err, _ = FetchToken(params.ApiUrl, params.AppKey, params.AppSecret); err != nil {
		return
	}
	return
}

func FetchToken(host, appKey, appSecret string) (err error, token string) {
	timestamp := strconv.Itoa(int(time.Now().Unix()))
	reqData := url2.Values{
		"appid":     {appKey},
		"appsecret": {appSecret},
		"timestamp": {timestamp},
	}

	params := map[string]string{
		"appid":     appKey,
		"appsecret": appSecret,
		"timestamp": timestamp,
	}

	var signature string
	//按照字母顺序遍历
	signature = appKey
	TraverseMapInStringOrder(params, func(key string, value string) {
		signature += key + value
	})
	signature += appSecret

	signature = utils.MD5V([]byte(signature))

	reqData.Add("signature", signature)

	err, result := utils.PostForm(host+"/token", reqData, nil)
	if err != nil {
		return
	}

	var tokenResponseJson []byte
	err, tokenResponseJson = GetResponseParams(result)
	if err != nil {
		return
	}

	var tokenResponse DwdToken
	if err = json.Unmarshal(tokenResponseJson, &tokenResponse); err != nil {
		return
	}

	token = tokenResponse.AccessToken
	//TODO time.Now().Unix() + tokenResponse.ExpireTime 用redis实现token过期时间

	return
}

func (s *Dwd) DeleteGoods(id uint) (err error) {
	//TODO implement me
	return
}

func (s *Dwd) GetSupplyBalance(GatherSupplyID uint) (err error, balance interface{}) {

	return
}

type BaseInfoData struct {
	AccessToken string `json:"access_token"`
	ApiUrl      string `json:"apiUrl"`
	AppId       string `json:"appKey"`
	AppSecret   string `json:"appSecret"`
	ExpireTime  int64  `json:"expire_time"`
	StoreName   string `json:"storeName"`
}

type DwdSupplySetting struct {
	BaseInfo   BaseInfoData           `json:"baseInfo"`
	UpdateInfo setting.UpdateInfoData `json:"update"`
	Pricing    setting.PricingData    `json:"pricing"`
	Management setting.Management     `json:"management"`
}

var DwdData *DwdSupplySetting

var GatherSupplyID uint

func GetRequestParams(params interface{}, action string) (err error, result url2.Values) {

	reqData := url2.Values{}
	var jsonBiz string
	var jsonData []byte
	jsonData, _ = json.Marshal(params)
	jsonBiz = string(jsonData)
	appId := DwdData.BaseInfo.AppId
	appSecret := DwdData.BaseInfo.AppSecret
	if DwdData.BaseInfo.ExpireTime == 0 || DwdData.BaseInfo.ExpireTime <= time.Now().Unix() {
		err, DwdData.BaseInfo.AccessToken = GetToken()
		if err != nil {
			return
		}
	}
	token := DwdData.BaseInfo.AccessToken
	reqData.Add("biz", jsonBiz)
	key := utils.MD5V([]byte(token))
	key = key[:16]
	var encryptedBiz = string(Base64Encode([]byte(AesEncryptCBC(jsonBiz, key))))
	reqData.Add("encrypted_biz", encryptedBiz)
	reqData.Add("action", action)
	reqData.Add("appid", appId)
	reqData.Add("access_token", token)
	reqData.Add("timestamp", strconv.Itoa(int(time.Now().Unix())))
	var signatureParams = make(map[string]string)
	signatureParams["appid"] = appId
	signatureParams["action"] = action
	signatureParams["biz"] = jsonBiz
	signatureParams["encrypted_biz"] = encryptedBiz
	signatureParams["access_token"] = token
	signatureParams["timestamp"] = strconv.Itoa(int(time.Now().Unix()))
	var signature string
	//按照字母顺序遍历
	signature = appId
	TraverseMapInStringOrder(signatureParams, func(key string, value string) {
		signature += key + value
	})
	signature += appSecret
	signature = utils.MD5V([]byte(signature))
	reqData.Add("signature", signature)
	return err, reqData
}

func GetResponseParams(requestParams []byte) (err error, result []byte) {

	var resp response2.DwdBatchResponse
	err = json.Unmarshal(requestParams, &resp)
	if err != nil {
		return
	}
	if resp.Errno != 0 {
		err = errors.New(resp.Errmsg)
		return
	}

	//验证签名
	err = CheckSignature(resp)
	if err != nil {
		return
	}
	//获取解密的数据
	err, result = CheckAndDecryptResponse(resp)
	if err != nil {
		return
	}
	return err, result
}

func GetToken() (err error, token string) {

	reqData := url2.Values{}
	appId := DwdData.BaseInfo.AppId
	appSecret := DwdData.BaseInfo.AppSecret

	reqData.Add("appid", appId)
	reqData.Add("appsecret", appSecret)
	reqData.Add("timestamp", strconv.Itoa(int(time.Now().Unix())))
	var signatureParams = make(map[string]string)
	signatureParams["appid"] = appId
	signatureParams["appsecret"] = appSecret
	signatureParams["timestamp"] = strconv.Itoa(int(time.Now().Unix()))
	var signature string
	//按照字母顺序遍历
	signature = appId
	TraverseMapInStringOrder(signatureParams, func(key string, value string) {
		signature += key + value
	})
	signature += appSecret
	signature = utils.MD5V([]byte(signature))
	reqData.Add("signature", signature)
	err, result := utils.PostForm(DwdData.BaseInfo.ApiUrl+"/token", reqData, nil)
	if err != nil {
		return
	}
	var tokenResponseJson []byte
	err, tokenResponseJson = GetResponseParams(result)
	if err != nil {
		return
	}
	var tokenResponse DwdToken
	err = json.Unmarshal(tokenResponseJson, &tokenResponse)
	if err != nil {
		return
	}
	token = tokenResponse.AccessToken
	DwdData.BaseInfo.AccessToken = tokenResponse.AccessToken
	DwdData.BaseInfo.ExpireTime = time.Now().Unix() + tokenResponse.ExpireTime
	err = SetSetting(DwdData, GatherSupplyID)
	return
}
func SetSetting(mapData *DwdSupplySetting, id uint) (err error) {

	var sysSetting model2.SysSetting
	sysSetting.Key = "gatherSupply" + strconv.Itoa(int(id))
	value, _ := json.Marshal(&mapData)
	sysSetting.Value = string(value)
	err = source.DB().Where("`key` = ?", "gatherSupply"+strconv.Itoa(int(id))).First(&sysSetting).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		source.DB().Where("`key` = ?", "gatherSupply"+strconv.Itoa(int(id))).Create(&sysSetting)
	} else {
		sysSetting.Value = string(value)
		err = source.DB().Where("`key` = ?", "gatherSupply"+strconv.Itoa(int(id))).Updates(&sysSetting).Error
	}
	err = nil

	return

}

type DwdToken struct {
	AccessToken string `json:"access_token"`
	ExpireTime  int64  `json:"expires_in"`
}

func CheckAndDecryptResponse(response response2.DwdBatchResponse) (err error, decryptedResponse []byte) {
	var base64String []byte
	if response.EncryptedData == "" {
		return err, []byte(response.Data)
	}
	key := utils.MD5V([]byte(DwdData.BaseInfo.AccessToken))
	key = key[:16]
	var decryptData string
	decryptData, err = AesDecryptCBC(string(base64String), key)

	return err, []byte(decryptData)
}

func CheckSignature(response response2.DwdBatchResponse) (err error) {
	if response.EncryptedData != "" {
		var jsonData []byte
		jsonData, err = json.Marshal(response)
		if err != nil {
			return
		}
		var params = make(map[string]string)
		err = json.Unmarshal(jsonData, &params)
		var checkParams = make(map[string]string)
		for k, v := range params {
			if k != "signature" {
				checkParams[k] = v
			}
		}
		var signature string
		signature = DwdData.BaseInfo.AppId
		TraverseMapInStringOrder(checkParams, func(key string, value string) {
			signature += key + value
		})
		signature += DwdData.BaseInfo.AppSecret
		signature = utils.MD5V([]byte(signature))
		if signature != response.Signature {
			err = errors.New("签名验证失败")
			return
		}
	}

	return
}
func (*Dwd) InitSetting(gatherSupplyID uint) (err error) {
	var sysSetting model2.SysSetting
	err, sysSetting = setting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		return
	}
	GatherSupplyID = gatherSupplyID
	err = json.Unmarshal([]byte(sysSetting.Value), &DwdData)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	if DwdData.BaseInfo.AppId == "" || DwdData.BaseInfo.AppSecret == "" || DwdData.BaseInfo.ApiUrl == "" {
		err = errors.New("请先配置供应链key")
		return
	}
	return
}

func InitToken() (err error) {

	return
}

type SliceMock struct {
	addr uintptr
	len  int
	cap  int
}
type InitGoodsResp struct {
	List     []model.DwdProduct `json:"list"`
	ScrollId int                `json:"scroll_id"`
	PageSize int                `json:"page_size"`
	TotalCnt int                `json:"total_cnt"`
}

func (*Dwd) InitGoods() (err error) {
	//去dwd拉取全量商品开始
	type InitGoodsRequest struct {
		PageSize int `json:"page_size"`
		PageNum  int `json:"page_num"`
	}

	var initGoodsRequest InitGoodsRequest
	initGoodsRequest.PageNum = 1
	initGoodsRequest.PageSize = 100

	var requestParam url2.Values
	err, requestParam = GetRequestParams(initGoodsRequest, "goods.list")
	if err != nil {
		return
	}
	//先获取总数
	err, result := utils.PostForm(DwdData.BaseInfo.ApiUrl, requestParam, nil)
	if err != nil {
		return
	}

	err, result = GetResponseParams(result)
	if err != nil {
		return
	}

	var initGoodsResp InitGoodsResp
	err = json.Unmarshal(result, &initGoodsResp)
	if err != nil {
		return
	}
	for k, v := range initGoodsResp.List {
		var jsonData []byte
		jsonData, err = json.Marshal(v)
		v.MD5 = utils.MD5V(jsonData)
		v.GatherSupplyID = GatherSupplyID
		for _, initSku := range v.Skus {
			if v.MainSkuId == initSku.SkuId {
				v.GuidePrice = initSku.GuidePrice
				v.Price = initSku.Price
				v.MarketPrice = initSku.MarketPrice
				v.Sn = initSku.OutId
			}
			v.Stock += initSku.Stock
		}
		initGoodsResp.List[k] = v
	}
	//log.Log().Info("dwd总数", zap.Any("info", initGoodsResp.TotalCnt))

	count := initGoodsResp.TotalCnt / initGoodsResp.PageSize

	for i := 2; i <= count+1; i++ {
		//分页获取商品
		initGoodsRequest.PageNum = i
		err, requestParam = GetRequestParams(initGoodsRequest, "goods.list")

		var pageResp = InitGoodsResp{}
		err, result = utils.PostForm(DwdData.BaseInfo.ApiUrl, requestParam, nil)
		if err != nil {
			return
		}

		err, result = GetResponseParams(result)
		if err != nil {
			return
		}

		err = json.Unmarshal(result, &pageResp)
		if err != nil {
			return
		}

		for _, pv := range pageResp.List {
			var jsonData []byte
			jsonData, err = json.Marshal(pv)
			pv.MD5 = utils.MD5V(jsonData)
			pv.GatherSupplyID = GatherSupplyID
			for _, pSku := range pv.Skus {
				if pv.MainSkuId == pSku.SkuId {
					pv.GuidePrice = pSku.GuidePrice
					pv.Price = pSku.Price
					pv.MarketPrice = pSku.MarketPrice
					pv.Sn = pSku.OutId

				}
				pv.Stock += pSku.Stock

			}
			initGoodsResp.List = append(initGoodsResp.List, pv)
		}
		time.Sleep(800 * time.Microsecond)
	}
	//去dwd拉取全量商品结束

	//判断是否全部新建
	var dwdProductCount int64

	err = source.DB().Model(&model.DwdProduct{}).Where("gather_supply_id = ?", GatherSupplyID).Count(&dwdProductCount).Error
	if err != nil {
		return
	}

	if dwdProductCount == 0 {
		err = source.DB().CreateInBatches(&initGoodsResp.List, 1000).Error
		return
	}
	//判断是否全部新建结束

	//找出需要更新、新建、删除的商品开始
	var oldDwdProducts []model.DwdProduct
	err = source.DB().Where("gather_supply_id = ?", GatherSupplyID).Find(&oldDwdProducts).Error
	if err != nil {
		return
	}
	//旧的本地商品map
	var oldDwdProductsMap = make(map[uint]model.DwdProduct)
	for _, odp := range oldDwdProducts {
		oldDwdProductsMap[odp.ProductId] = odp
	}

	//新拉取的商品map
	var newDwdProductsMap = make(map[uint]model.DwdProduct)
	for _, ndp := range initGoodsResp.List {
		newDwdProductsMap[ndp.ProductId] = ndp
	}

	var needCreateDwdProduct []model.DwdProduct
	var needUpdateDwdProduct []model.DwdProduct
	var needDeleteDwdProductIds []uint

	for nk, np := range oldDwdProductsMap {
		if _, ok := newDwdProductsMap[nk]; !ok {
			//需要删除
			needDeleteDwdProductIds = append(needDeleteDwdProductIds, np.ProductId)
		}
	}

	for dk, dp := range newDwdProductsMap {
		if _, ok := oldDwdProductsMap[dk]; !ok {
			//需要新增
			needCreateDwdProduct = append(needCreateDwdProduct, dp)
		} else {
			if oldDwdProductsMap[dp.ProductId].MD5 != dp.MD5 {
				//需要修改
				needUpdateDwdProduct = append(needUpdateDwdProduct, dp)
			}
		}
	}
	//找出需要更新、新建、删除的商品结束

	//处理商品开始
	//删除dwd商品
	if len(needDeleteDwdProductIds) > 0 {
		err = source.DB().Where("product_id in ?", needDeleteDwdProductIds).Delete(&model.DwdProduct{}).Error
		if err != nil {
			return
		}
	}

	//删除本地商品
	if len(needDeleteDwdProductIds) > 0 {
		var needDeleteProductIds []uint
		err = source.DB().Model(&pmodel.Product{}).Where("source_goods_id in ?", needDeleteDwdProductIds).Pluck("id", &needDeleteProductIds).Error
		if err != nil {
			return
		}
		err = source.DB().Model(&pmodel.Product{}).Where("id in ?", needDeleteProductIds).Update("is_display", 0).Error
		if err != nil {
			return
		}
		for _, deleteProductId := range needDeleteProductIds {
			err = mq.PublishMessage(deleteProductId, mq.Undercarriage, 0)
			if err != nil {
				return
			}
		}
	}

	//新建dwd商品
	if len(needCreateDwdProduct) > 0 {
		err = source.DB().CreateInBatches(&needCreateDwdProduct, 1000).Error
		if err != nil {
			return
		}
	}

	//修改dwd和本地商品开始
	//var updateProductMap []map[string]interface{}
	//var createSkuMap []pmodel.Sku
	var updateDwdProductMap []map[string]interface{}
	var needUpdateProductWhereIds []uint
	var needUpdateDwdProductMap = make(map[uint]model.DwdProduct)
	for _, needUpdateDwdProductItem := range needUpdateDwdProduct {
		var updateDwdProductItem = make(map[string]interface{})
		updateDwdProductItem["product_id"] = needUpdateDwdProductItem.ProductId
		updateDwdProductItem["category_id"] = needUpdateDwdProductItem.CategoryId
		updateDwdProductItem["category_name"] = needUpdateDwdProductItem.CategoryName
		updateDwdProductItem["tax_rate"] = needUpdateDwdProductItem.TaxRate
		updateDwdProductItem["tax_code"] = needUpdateDwdProductItem.TaxCode
		updateDwdProductItem["title"] = needUpdateDwdProductItem.Title
		updateDwdProductItem["type"] = needUpdateDwdProductItem.Type
		updateDwdProductItem["desc"] = needUpdateDwdProductItem.Desc
		updateDwdProductItem["tags"] = needUpdateDwdProductItem.Tags
		updateDwdProductItem["place_of_production"] = needUpdateDwdProductItem.PlaceOfProduction
		var itemImgs []byte
		itemImgs, err = model.JSONMarshal(needUpdateDwdProductItem.ItemImgs)
		updateDwdProductItem["item_imgs"] = string(itemImgs)
		updateDwdProductItem["main_sku_id"] = needUpdateDwdProductItem.MainSkuId
		updateDwdProductItem["enabled"] = needUpdateDwdProductItem.Enabled
		updateDwdProductItem["main_sku_price"] = needUpdateDwdProductItem.MainSkuPrice
		updateDwdProductItem["guide_price"] = needUpdateDwdProductItem.GuidePrice
		updateDwdProductItem["market_price"] = needUpdateDwdProductItem.MarketPrice
		updateDwdProductItem["Price"] = needUpdateDwdProductItem.Price
		updateDwdProductItem["main_sku_pic"] = needUpdateDwdProductItem.MainSkuPic
		updateDwdProductItem["thumbnail"] = needUpdateDwdProductItem.Thumbnail
		updateDwdProductItem["is_invoice"] = needUpdateDwdProductItem.IsInvoice
		updateDwdProductItem["is_not_expired_date"] = needUpdateDwdProductItem.IsNotExpiredDate
		var skusJson []byte
		skusJson, err = model.JSONMarshal(needUpdateDwdProductItem.Skus)
		if err != nil {
			continue
		}
		var sku = string(skusJson)
		if strings.Contains(string(skusJson), "'") {
			sku = strings.Replace(string(skusJson), "'", "", -1)
		}
		updateDwdProductItem["skus"] = sku

		updateDwdProductItem["md5"] = needUpdateDwdProductItem.MD5
		updateDwdProductItem["stock"] = needUpdateDwdProductItem.Stock
		updateDwdProductItem["sn"] = needUpdateDwdProductItem.Sn
		updateDwdProductItem["updated_at"] = time.Now().Format("2006-01-02 15:04:05")
		updateDwdProductMap = append(updateDwdProductMap, updateDwdProductItem)
		needUpdateProductWhereIds = append(needUpdateProductWhereIds, needUpdateDwdProductItem.ProductId)
		needUpdateDwdProductMap[needUpdateDwdProductItem.ProductId] = needUpdateDwdProductItem
	}

	//var needUpdateProducts []pmodel.Product
	//if len(needUpdateProductWhereIds) > 0 {
	//	err = source.DB().Preload("Skus").Where("source_goods_id in ?", needUpdateProductWhereIds).Where("gather_supply_id = ?", GatherSupplyID).Find(&needUpdateProducts).Error
	//	if err != nil {
	//		return
	//	}
	//}

	//var needUpdateProductIDs []uint
	//for _, v := range needUpdateProducts {
	//	needUpdateProductIDs = append(needUpdateProductIDs, v.ID)
	//	var updateProductMapItem = make(map[string]interface{})
	//	updateProductMapItem["id"] = v.ID
	//	if DwdData.UpdateInfo.BaseInfo == 1 {
	//		updateProductMapItem["title"] = needUpdateDwdProductMap[v.SourceGoodsID].Title
	//		updateProductMapItem["is_display"] = needUpdateDwdProductMap[v.SourceGoodsID].Enabled
	//		updateProductMapItem["image_url"] = needUpdateDwdProductMap[v.SourceGoodsID].MainSkuPic
	//		updateProductMapItem["stock"] = needUpdateDwdProductMap[v.SourceGoodsID].Stock
	//		updateProductMapItem["sn"] = needUpdateDwdProductMap[v.SourceGoodsID].Sn
	//		var detailImgs string
	//		detailImgs = "<p>"
	//		for _, detailImg := range needUpdateDwdProductMap[v.SourceGoodsID].ItemImgs {
	//			detailImgs += "<img src=\"" + detailImg + "\">"
	//		}
	//		detailImgs += "</p>"
	//		updateProductMapItem["detail_images"] = detailImgs
	//
	//		//新旧skuid对比的map
	//		var SkuAndOriginSkuIdMap = make(map[uint]uint)
	//		for _, pSku := range v.Skus {
	//			SkuAndOriginSkuIdMap[uint(pSku.OriginalSkuID)] = pSku.ID
	//		}
	//
	//		var createSkuMapItem pmodel.Sku
	//		for _, sku := range needUpdateDwdProductMap[v.SourceGoodsID].Skus {
	//			if _, ok := SkuAndOriginSkuIdMap[uint(sku.SkuId)]; ok {
	//				createSkuMapItem.ID = SkuAndOriginSkuIdMap[uint(sku.SkuId)]
	//			}
	//			createSkuMapItem.ProductID = v.ID
	//			createSkuMapItem.Title = sku.SkuName
	//			createSkuMapItem.GuidePrice = sku.GuidePrice
	//			createSkuMapItem.OriginalSkuID = int64(sku.SkuId)
	//			createSkuMapItem.Stock = sku.Stock
	//			createSkuMapItem.IsDisplay = sku.Enabled
	//			createSkuMapItem.Code = sku.OutId
	//			createSkuMapItem.Sn = sku.OutId
	//			createSkuMapItem.ImageUrl = sku.Thumbnail
	//			for _, skuSaleAttribute := range sku.SaleAttribute {
	//				createSkuMapItem.Options = append(createSkuMapItem.Options, pmodel.Option{
	//					SpecName:     skuSaleAttribute.AttributeName,
	//					SpecItemName: skuSaleAttribute.AttributeValue,
	//				})
	//			}
	//
	//			var intX uint64
	//			if DwdData.Pricing.SupplyAdvice == 1 {
	//				intX, err = strconv.ParseUint(DwdData.Pricing.SupplyAdviceGuide, 10, 32)
	//				createSkuMapItem.OriginPrice = sku.MarketPrice * uint(intX) / 100
	//			} else if DwdData.Pricing.SupplyAdvice == 2 {
	//				intX, err = strconv.ParseUint(DwdData.Pricing.SupplyAdviceAgreement, 10, 32)
	//				createSkuMapItem.OriginPrice = sku.Price * uint(intX) / 100
	//			} else {
	//				createSkuMapItem.OriginPrice = sku.MarketPrice
	//			}
	//			var intXS uint64
	//			if DwdData.Pricing.SupplySales == 1 {
	//				intXS, err = strconv.ParseUint(DwdData.Pricing.SupplySalesGuide, 10, 32)
	//				createSkuMapItem.Price = sku.MarketPrice * uint(intXS) / 100
	//			} else if DwdData.Pricing.SupplySales == 2 {
	//				intXS, err = strconv.ParseUint(DwdData.Pricing.SupplySalesAgreement, 10, 32)
	//				createSkuMapItem.Price = sku.Price * uint(intXS) / 100
	//			} else {
	//				createSkuMapItem.Price = sku.Price
	//			}
	//			createSkuMapItem.CostPrice = createSkuMapItem.Price
	//			createSkuMapItem.GuidePrice = createSkuMapItem.OriginPrice
	//			createSkuMapItem.ActivityPrice = createSkuMapItem.OriginPrice
	//			createSkuMap = append(createSkuMap, createSkuMapItem)
	//			if sku.SkuId == needUpdateDwdProductMap[v.SourceGoodsID].MainSkuId {
	//				var gallery []pmodel.GalleryItem
	//				for _, img := range sku.SkuImgs {
	//					gallery = append(gallery, pmodel.GalleryItem{
	//						Type: 1,
	//						Src:  img,
	//					})
	//				}
	//				var galleryJson []byte
	//				galleryJson, err = json.Marshal(gallery)
	//				if err != nil {
	//					updateProductMapItem["gallery"] = ""
	//				} else {
	//					updateProductMapItem["gallery"] = string(galleryJson)
	//				}
	//
	//				updateProductMapItem["guide_price"] = sku.GuidePrice
	//				updateProductMapItem["min_price"] = sku.GuidePrice
	//				updateProductMapItem["max_price"] = sku.GuidePrice
	//
	//				var intX uint64
	//				if DwdData.Pricing.SupplyAdvice == 1 {
	//					intX, err = strconv.ParseUint(DwdData.Pricing.SupplyAdviceGuide, 10, 32)
	//					updateProductMapItem["origin_price"] = sku.MarketPrice * uint(intX) / 100
	//				} else if DwdData.Pricing.SupplyAdvice == 2 {
	//					intX, err = strconv.ParseUint(DwdData.Pricing.SupplyAdviceAgreement, 10, 32)
	//					updateProductMapItem["origin_price"] = sku.Price * uint(intX) / 100
	//				} else {
	//					updateProductMapItem["origin_price"] = sku.MarketPrice
	//				}
	//			}
	//		}
	//
	//	}
	//
	//	updateProductMapItem["updated_at"] = time.Now().Format("2006-01-02 15:04:05")
	//	updateProductMap = append(updateProductMap, updateProductMapItem)
	//}

	if len(updateDwdProductMap) > 0 {
		err = source.BatchUpdate(updateDwdProductMap, "dwd_products", "product_id")
		if err != nil {
			return
		}
	}

	//if len(updateProductMap) > 0 {
	//	err = source.BatchUpdate(updateProductMap, "products", "")
	//	if err != nil {
	//		return
	//	}
	//}

	//if len(needUpdateProductIDs) > 0 {
	//	err = source.DB().Where("product_id in ?", needUpdateProductIDs).Delete(&pmodel.Sku{}).Error
	//	if err != nil {
	//		return
	//	}
	//	//for _, needUpdateProductID := range needUpdateProductIDs {
	//	//	err = mq.PublishMessage(needUpdateProductID, mq.Edit, 0)
	//	//	if err != nil {
	//	//		return
	//	//	}
	//	//}
	//}

	//if len(createSkuMap) > 0 {
	//	err = source.DB().CreateInBatches(&createSkuMap, 1000).Error
	//	if err != nil {
	//		return
	//	}
	//}
	//修改dwd和本地商品结束

	//处理商品结束
	return
}

func (dwd *Dwd) InitProducts() (err error) {
	return dwd.InitProductsBatch(1000) // 默认每批处理1000个商品
}

// InitProductsBatch 分批初始化商品
func (dwd *Dwd) InitProductsBatch(batchSize int) (err error) {
	log.Log().Info("开始分批初始化商品", zap.Int("batch_size", batchSize))

	// 获取总商品数量
	var totalCount int64
	err = source.DB().Model(&service2.ProductForUpdate{}).Where("gather_supply_id = ?", GatherSupplyID).Count(&totalCount).Error
	if err != nil {
		log.Log().Error("获取商品总数失败", zap.Error(err))
		return
	}

	if totalCount == 0 {
		log.Log().Info("没有需要处理的商品")
		return nil
	}

	log.Log().Info("商品总数统计", zap.Int64("total_count", totalCount), zap.Int("batch_size", batchSize))

	// 计算批次数
	totalBatches := int((totalCount + int64(batchSize) - 1) / int64(batchSize))

	// 预加载DWD商品数据映射（这个相对较小，可以一次性加载）
	var allDwdProducts []model.DwdProduct
	var allDwdProductsMap = make(map[uint]model.DwdProduct)
	err = source.DB().Where("gather_supply_id = ?", GatherSupplyID).Find(&allDwdProducts).Error
	if err != nil {
		log.Log().Error("获取DWD商品数据失败", zap.Error(err))
		return
	}
	for _, dwdProduct := range allDwdProducts {
		allDwdProductsMap[dwdProduct.ProductId] = dwdProduct
	}

	// 预加载分类数据映射
	var categoryTree []model.DwdCategory
	err = source.DB().Find(&categoryTree).Error
	if err != nil {
		log.Log().Error("获取分类数据失败", zap.Error(err))
		return
	}
	var categoryTreeMap = make(map[int]model.DwdCategory)
	for _, ct := range categoryTree {
		categoryTreeMap[ct.Id] = ct
	}

	// 分批处理商品
	var totalProcessed int
	var totalUpdated int
	var allRiskManageRecord []publicModel.RiskManagementRecord

	for batch := 0; batch < totalBatches; batch++ {
		offset := batch * batchSize

		log.Log().Info("处理批次",
			zap.Int("batch", batch+1),
			zap.Int("total_batches", totalBatches),
			zap.Int("offset", offset),
			zap.Int("batch_size", batchSize),
		)

		// 分批查询商品
		var batchProducts []service2.ProductForUpdate
		err = source.DB().Preload("Skus").
			Where("gather_supply_id = ?", GatherSupplyID).
			Limit(batchSize).
			Offset(offset).
			Find(&batchProducts).Error
		if err != nil {
			log.Log().Error("获取批次商品失败",
				zap.Int("batch", batch+1),
				zap.Error(err),
			)
			continue
		}

		if len(batchProducts) == 0 {
			log.Log().Info("当前批次没有商品", zap.Int("batch", batch+1))
			break
		}

		// 处理当前批次的商品
		batchUpdated, batchRiskRecords, err := dwd.processBatchProducts(
			batchProducts,
			allDwdProductsMap,
			categoryTreeMap,
		)
		if err != nil {
			log.Log().Error("处理批次商品失败",
				zap.Int("batch", batch+1),
				zap.Error(err),
			)
			continue
		}

		totalProcessed += len(batchProducts)
		totalUpdated += batchUpdated
		allRiskManageRecord = append(allRiskManageRecord, batchRiskRecords...)

		log.Log().Info("批次处理完成",
			zap.Int("batch", batch+1),
			zap.Int("processed", len(batchProducts)),
			zap.Int("updated", batchUpdated),
			zap.Int("total_processed", totalProcessed),
			zap.Int("total_updated", totalUpdated),
		)

		// 添加短暂延迟，避免数据库压力过大
		time.Sleep(100 * time.Millisecond)
	}

	// 批量创建风险管理记录
	if len(allRiskManageRecord) > 0 {
		err = source.DB().CreateInBatches(&allRiskManageRecord, 1000).Error
		if err != nil {
			log.Log().Error("创建风险管理记录失败", zap.Error(err))
		}
	}

	log.Log().Info("商品初始化完成",
		zap.Int("total_processed", totalProcessed),
		zap.Int("total_updated", totalUpdated),
		zap.Int("risk_records", len(allRiskManageRecord)),
	)

	return nil
}

// processBatchProducts 处理单个批次的商品
func (dwd *Dwd) processBatchProducts(
	batchProducts []service2.ProductForUpdate,
	allDwdProductsMap map[uint]model.DwdProduct,
	categoryTreeMap map[int]model.DwdCategory,
) (updatedCount int, riskManageRecord []publicModel.RiskManagementRecord, err error) {

	var needUpdateProducts []service2.ProductForUpdate
	var updateLimit = 1000 // 每批最多更新1000个商品

	for _, product := range batchProducts {

		if _, ok := allDwdProductsMap[product.SourceGoodsID]; !ok {
			continue
		}
		if product.MD5 == "" || product.MD5 != allDwdProductsMap[product.SourceGoodsID].MD5 || product.IsDisplay != allDwdProductsMap[product.SourceGoodsID].Enabled {
			if updateLimit == 1000 {
				continue
			}

			var detail = allDwdProductsMap[product.SourceGoodsID]
			product.MD5 = detail.MD5
			if len(detail.Skus) > 0 && (detail.MarketPrice == 0 || detail.Price == 0) {
				detail.MarketPrice = detail.Skus[0].MarketPrice
				detail.Price = detail.Skus[0].Price
			}

			var intXAdvice uint64
			if DwdData.Pricing.SupplyAdvice == 1 {
				intXAdvice, err = strconv.ParseUint(DwdData.Pricing.SupplyAdviceGuide, 10, 32)
				product.OriginPrice = detail.MarketPrice * uint(intXAdvice) / 100
			} else if DwdData.Pricing.SupplyAdvice == 2 {
				intXAdvice, err = strconv.ParseUint(DwdData.Pricing.SupplyAdviceAgreement, 10, 32)
				product.OriginPrice = uint(detail.Price) * uint(intXAdvice) / 100
			} else {
				product.OriginPrice = detail.MarketPrice
			}
			if DwdData.UpdateInfo.CurrentPrice == 1 {

				var intX uint64
				if DwdData.Pricing.SupplySales == 1 {
					intX, err = strconv.ParseUint(DwdData.Pricing.SupplySalesGuide, 10, 32)
					product.Price = detail.MarketPrice * uint(intX) / 100
				} else if DwdData.Pricing.SupplySales == 2 {
					intX, err = strconv.ParseUint(DwdData.Pricing.SupplySalesAgreement, 10, 32)
					product.Price = uint(detail.Price) * uint(intX) / 100
				} else {
					product.Price = uint(detail.Price)
				}
			}
			product.GuidePrice = product.OriginPrice
			product.ActivityPrice = product.GuidePrice

			if DwdData.UpdateInfo.CostPrice == 1 {
				//是否更新成本价
				product.CostPrice = uint(detail.Price)
			}
			var riskRecord publicModel.RiskManagementRecord
			riskRecord.ProductID = product.ID
			riskRecord.SourceGoodsID = product.SourceGoodsID
			riskRecord.GatherSupplyID = product.GatherSupplyID
			if DwdData.Management.ProductPriceStatus == 1 {
				if product.Price < product.CostPrice*(DwdData.Management.Products/100) {
					product.IsDisplay = 0
					riskManageRecord = append(riskManageRecord, riskRecord)
				}
			} else if DwdData.Management.ProductPriceStatus == 2 {
				if (product.Price-product.CostPrice)/product.CostPrice < DwdData.Management.Profit/100 {
					product.IsDisplay = 0
					riskManageRecord = append(riskManageRecord, riskRecord)
				}
			}

			if DwdData.UpdateInfo.CateGory == 1 {
				//是否更新分类
				var category1 model.DwdCategory
				var category2 model.DwdCategory
				var category3 model.DwdCategory
				var categortId int
				categortId, err = strconv.Atoi(detail.CategoryId)
				var ok bool
				category3, ok = categoryTreeMap[categortId]
				if !ok {
					continue
				}
				category2, ok = categoryTreeMap[category3.Pid]
				if !ok {
					continue
				}
				category1, ok = categoryTreeMap[category2.Pid]
				if !ok {
					continue
				}
				var categoryIdsString []string
				categoryIdsString = append(categoryIdsString, strconv.Itoa(category1.Id))
				categoryIdsString = append(categoryIdsString, strconv.Itoa(category2.Id))
				categoryIdsString = append(categoryIdsString, strconv.Itoa(category3.Id))

				var thirdCategoryName []string
				thirdCategoryName = append(thirdCategoryName, category1.Name)
				thirdCategoryName = append(thirdCategoryName, category2.Name)
				thirdCategoryName = append(thirdCategoryName, category3.Name)
				cateList := thirdCategoryName
				var cate1, cate2, cate3 model3.Category
				display := 1
				if len(cateList) > 0 && cateList[0] != "" {

					cate1.IsDisplay = &display
					cate1.ParentID = 0
					cate1.Level = 1
					cate1.Name = cateList[0]
					source.DB().Where("name=? and level=? and parent_id=?", cateList[0], 1, 0).FirstOrCreate(&cate1)
				}

				if len(cateList) > 1 && cateList[1] != "" {
					cate2.IsDisplay = &display
					cate2.ParentID = cate1.ID
					cate2.Level = 2
					cate2.Name = cateList[1]
					source.DB().Where("name=? and level=? and parent_id=?", cateList[1], 2, cate1.ID).FirstOrCreate(&cate2)
				}

				if len(cateList) > 2 && cateList[2] != "" {
					cate3.IsDisplay = &display
					cate3.ParentID = cate2.ID
					cate3.Level = 3
					cate3.Name = cateList[2]
					source.DB().Where("name=? and level=? and parent_id=?", cateList[2], 3, cate2.ID).FirstOrCreate(&cate3)
				}

				product.Category1ID = cate1.ID
				product.Category2ID = cate2.ID
				product.Category3ID = cate3.ID

			}
			if DwdData.UpdateInfo.CreateBrand == 1 {
				//是否更新品牌
				var brand model3.Brand
				brand.Name = detail.BrandName
				source.DB().Where("`name` = ?", brand.Name).FirstOrCreate(&brand)
				product.BrandID = brand.ID
			}
			if DwdData.UpdateInfo.BaseInfo == 1 {
				//是否更新基本信息
				product.Title = detail.Title
				product.Stock = uint(detail.Stock)
				if product.StatusLock == 0 {
					product.IsDisplay = detail.Enabled
				}
				product.ImageUrl = detail.MainSkuPic
				product.Sn = detail.Sn
				product.DetailImages = "<p>"
				for _, detailImg := range detail.ItemImgs {
					product.DetailImages += "<img src=\"" + detailImg + "?x-oss-process=style/normal\">"
				}
				product.DetailImages += "</p>"
				product.Gallery = pmodel.Gallery{}
				for _, dSku := range detail.Skus {
					if dSku.SkuId == detail.MainSkuId {
						for _, img := range dSku.SkuImgs {
							product.Gallery = append(product.Gallery, pmodel.GalleryItem{
								Type: 1,
								Src:  img + "?x-oss-process=style/normal",
							})
						}
						product.Freight = dSku.DeliveryFee
					}
				}

				if len(product.Gallery) == 0 {
					if len(detail.Skus) > 0 {
						for _, img := range detail.Skus[0].SkuImgs {
							product.Gallery = append(product.Gallery, pmodel.GalleryItem{
								Type: 1,
								Src:  img + "?x-oss-process=style/normal",
							})
						}
					}

				}

				if len(product.Gallery) == 0 {
					product.Gallery = append(product.Gallery, pmodel.GalleryItem{
						Type: 1,
						Src:  product.ImageUrl,
					})

				}
			}

			//更新sku
			product.MinPrice = uint(int(product.Price))
			product.MaxPrice = uint(int(product.Price))
			var totalStock int
			//var spec = make(map[string][]string)
			//var specKeys []string
			//for _, detailSku := range detail.Skus {
			//	for _, skuSaleAttr := range detailSku.SaleAttribute {
			//		specKeys = append(specKeys, skuSaleAttr.AttributeName)
			//	}
			//	break
			//}
			//var canFixSku = 0
			var newSkus []service2.Sku
			var attrs []pmodel.Attr
			var minProfitRate float64
			var attributeCount int                      //判断是否每个sku中都有规格项
			var skuNameUnique = make(map[string]string) //判断是否每个sku中的name都不重复
			for _, detailSkuCheck := range detail.Skus {

				if len(detailSkuCheck.SaleAttribute) > 0 {
					attributeCount++
				}
				if detailSkuCheck.SkuName != "" {
					skuNameUnique[detailSkuCheck.SkuName] = detailSkuCheck.SkuName
				}
			}

			for sk, detailSku := range detail.Skus {
				var sku = service2.Sku{}
				for _, oldSku := range product.Skus {
					//优先级：规格值 > 规格id&&规格sn，规格值匹配失败再进行规格id和规格sn的匹配（因为历史数据导致的可能会有sku原始id重复的情况）

					if oldSku.OriginalSkuID == detailSku.SkuId || (strconv.Itoa(detailSku.SkuId) != "" && oldSku.Sn != "" && oldSku.Sn == strconv.Itoa(detailSku.SkuId)) {
						sku = oldSku
					}

				}
				//if len(detailSku.SaleAttribute) == 0 {
				//canFixSku++
				var skuName string
				if detailSku.SkuName == "" {
					skuName = "编码：" + strconv.Itoa(detailSku.SkuId)
				} else {
					skuName = detailSku.SkuName
				}
				var options []pmodel.Option

				if attributeCount == len(detail.Skus) {
					for _, skuSaleAttr := range detailSku.SaleAttribute {
						var option pmodel.Option
						option.SpecName = skuSaleAttr.AttributeName
						option.SpecItemName = skuSaleAttr.AttributeValue
						options = append(options, option)

					}
				} else if len(skuNameUnique) == len(detail.Skus) {
					options = append(options, pmodel.Option{
						SpecName:     "默认规格",
						SpecItemName: detailSku.SkuName,
					})
				} else {
					options = append(options, pmodel.Option{
						SpecName:     "默认规格",
						SpecItemName: detailSku.SkuName + "(" + strconv.Itoa(detailSku.SkuId) + ")",
					})
				}

				sku.Options = options
				var intXSku uint64
				if DwdData.Pricing.SupplyAdvice == 1 {
					intXSku, err = strconv.ParseUint(DwdData.Pricing.SupplyAdviceGuide, 10, 32)
					sku.OriginPrice = detailSku.MarketPrice * uint(intXSku) / 100
				} else if DwdData.Pricing.SupplyAdvice == 2 {
					intXSku, err = strconv.ParseUint(DwdData.Pricing.SupplyAdviceAgreement, 10, 32)
					sku.OriginPrice = detailSku.Price * uint(intXSku) / 100
				} else {
					sku.OriginPrice = detailSku.MarketPrice
				}
				var intXS uint64
				if DwdData.Pricing.SupplySales == 1 {
					intXS, err = strconv.ParseUint(DwdData.Pricing.SupplySalesGuide, 10, 32)
					sku.Price = detailSku.MarketPrice * uint(intXS) / 100
				} else if DwdData.Pricing.SupplySales == 2 {
					intXS, err = strconv.ParseUint(DwdData.Pricing.SupplySalesAgreement, 10, 32)
					sku.Price = detailSku.Price * uint(intXS) / 100
				} else {
					sku.Price = detailSku.Price
				}
				sku.Title = skuName
				sku.Weight = 0
				sku.CostPrice = detailSku.Price
				sku.IsDisplay = detailSku.Enabled
				sku.Stock = detailSku.Stock
				if sku.IsDisplay == 0 {
					sku.Stock = 0
				}
				totalStock += sku.Stock
				sku.GuidePrice = sku.OriginPrice
				sku.ActivityPrice = sku.OriginPrice
				sku.OriginalSkuID = detailSku.SkuId
				sku.Sn = strconv.Itoa(detailSku.SkuId)
				sku.ProductID = product.ID
				var attrName = sku.Title
				if attrName == "" {
					attrName = sku.Sn
				}
				if sku.GuidePrice > 0 {
					sku.ProfitRate = utils.ExecProfitRate(sku.GuidePrice, sku.Price)
				} else {
					sku.ProfitRate = 0
				}
				if sk == 0 {
					minProfitRate = sku.ProfitRate
				}
				if sku.ProfitRate <= minProfitRate {
					minProfitRate = sku.ProfitRate
				}
				newSkus = append(newSkus, sku)
				attrs = append(attrs, pmodel.Attr{
					Name:  attrName + "的过期时间",
					Value: time.Unix(int64(detailSku.ExpiredDate), 0).Format("2006-01-02 15:04:05"),
				})
			}
			product.Attrs = attrs
			//if canFixSku == 0 {
			//	//补全sku
			//	var options [][]pmodel.Option
			//	for _, specKey := range specKeys {
			//		var optionS []pmodel.Option
			//		for _, o := range spec[specKey] {
			//			optionS = append(optionS, pmodel.Option{
			//				SpecName:     specKey,
			//				SpecItemName: o,
			//			})
			//		}
			//		fmt.Println(optionS)
			//		options = append(options, optionS)
			//	}
			//	var completeOptions [][]pmodel.Option
			//	completeOptions = GetSku(options)
			//	var fixOptions [][]pmodel.Option
			//
			//	for _, completeOption := range completeOptions {
			//		var completeOptionJson []byte
			//		completeOptionJson, err = model.JSONMarshal(completeOption)
			//		var exitsNum = 0
			//		for _, sku := range newSkus {
			//			var skuOptionJson []byte
			//			skuOptionJson, err = model.JSONMarshal(sku.Options)
			//			if string(completeOptionJson) == string(skuOptionJson) {
			//				exitsNum++
			//			}
			//		}
			//		if exitsNum == 0 {
			//			fixOptions = append(fixOptions, completeOption)
			//		}
			//	}
			//
			//	for _, fixOption := range fixOptions {
			//		newSkus = append(newSkus, service2.Sku{
			//			Options:       fixOption,
			//			Title:         "暂无",
			//			Price:         product.Price,
			//			OriginPrice:   product.OriginPrice,
			//			GuidePrice:    product.GuidePrice,
			//			ActivityPrice: product.ActivityPrice,
			//			CostPrice:     product.CostPrice,
			//			ProductID:     product.ID,
			//		})
			//	}
			//
			//}

			//for k, detailSku := range newSkus {
			//
			//	//if detailSku.Title != "暂无" {
			//	//补充的规格不需要匹配原来的skuid，
			//	for _, sku := range product.Skus {
			//		//优先级：规格值 > 规格id&&规格sn，规格值匹配失败再进行规格id和规格sn的匹配（因为历史数据导致的可能会有sku原始id重复的情况）
			//		//var optionCheck int
			//		//for _, dAttr := range detailSku.Options {
			//		//	for _, doption := range sku.Options {
			//		//		if dAttr.SpecItemName == doption.SpecItemName {
			//		//			optionCheck++
			//		//		}
			//		//	}
			//		//}
			//		//if optionCheck == len(sku.Options) {
			//		//	newSkus[k].ID = sku.ID
			//		//}
			//		if newSkus[k].ID == 0 {
			//			if sku.OriginalSkuID == detailSku.OriginalSkuID || (detailSku.Sn != "" && sku.Sn != "" && sku.Sn == detailSku.Sn) {
			//				newSkus[k].ID = sku.ID
			//			}
			//		}
			//
			//	}
			//	//}
			//
			//}

			product.Skus = newSkus
			if len(product.Skus) > 0 {
				product.ProfitRate = minProfitRate
			}
			if totalStock == 0 {
				product.IsDisplay = 0
			}
			//处理资质json图片数组

			needUpdateProducts = append(needUpdateProducts, product)
			updateLimit++
		}
	}

	// 批量更新商品
	for _, updateProduct := range needUpdateProducts {
		log.Log().Debug("DWD修改商品", zap.Any("id", updateProduct.ID))
		err = service2.UpdateProduct(updateProduct)
		if err != nil {
			log.Log().Error("更新商品失败",
				zap.Uint("product_id", updateProduct.ID),
				zap.Error(err),
			)
			continue
		}
		time.Sleep(50 * time.Millisecond) // 减少延迟时间
	}

	updatedCount = len(needUpdateProducts)
	return updatedCount, riskManageRecord, nil
}

// InitProductsWithConfig 使用自定义配置初始化商品
func (dwd *Dwd) InitProductsWithConfig(config InitProductsConfig) (err error) {
	log.Log().Info("开始使用自定义配置初始化商品",
		zap.Int("batch_size", config.BatchSize),
		zap.Int("max_concurrent", config.MaxConcurrent),
		zap.Bool("enable_progress", config.EnableProgress),
	)

	if config.BatchSize <= 0 {
		config.BatchSize = 1000
	}
	if config.MaxConcurrent <= 0 {
		config.MaxConcurrent = 1
	}

	return dwd.InitProductsBatch(config.BatchSize)
}

// InitProductsConfig 初始化商品的配置
type InitProductsConfig struct {
	BatchSize      int  `json:"batch_size"`      // 每批处理的商品数量
	MaxConcurrent  int  `json:"max_concurrent"`  // 最大并发数
	EnableProgress bool `json:"enable_progress"` // 是否启用进度监控
	DelayMs        int  `json:"delay_ms"`        // 批次间延迟毫秒数
}

// GetInitProductsProgress 获取初始化进度
func (dwd *Dwd) GetInitProductsProgress() (progress InitProductsProgress, err error) {
	// 获取总商品数量
	var totalCount int64
	err = source.DB().Model(&service2.ProductForUpdate{}).Where("gather_supply_id = ?", GatherSupplyID).Count(&totalCount).Error
	if err != nil {
		return
	}

	// 获取需要更新的商品数量（简化计算，实际可能需要更复杂的逻辑）
	var needUpdateCount int64
	err = source.DB().Model(&service2.ProductForUpdate{}).
		Where("gather_supply_id = ? AND (md5 = '' OR md5 IS NULL)", GatherSupplyID).
		Count(&needUpdateCount).Error
	if err != nil {
		return
	}

	progress = InitProductsProgress{
		TotalCount:      totalCount,
		NeedUpdateCount: needUpdateCount,
		ProcessedCount:  totalCount - needUpdateCount,
		ProgressPercent: float64(totalCount-needUpdateCount) / float64(totalCount) * 100,
	}

	return
}

// InitProductsProgress 初始化进度信息
type InitProductsProgress struct {
	TotalCount      int64   `json:"total_count"`       // 总商品数量
	NeedUpdateCount int64   `json:"need_update_count"` // 需要更新的商品数量
	ProcessedCount  int64   `json:"processed_count"`   // 已处理的商品数量
	ProgressPercent float64 `json:"progress_percent"`  // 进度百分比
}

// InitProductsAsync 异步初始化商品
func (dwd *Dwd) InitProductsAsync(batchSize int, callback func(progress InitProductsProgress)) (err error) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Log().Error("异步初始化商品发生panic", zap.Any("recover", r))
			}
		}()

		err := dwd.InitProductsBatch(batchSize)
		if err != nil {
			log.Log().Error("异步初始化商品失败", zap.Error(err))
		}

		// 获取最终进度
		if callback != nil {
			finalProgress, err := dwd.GetInitProductsProgress()
			if err == nil {
				callback(finalProgress)
			}
		}
	}()

	return nil
}
func (dwd *Dwd) GetGoods(info request.GetGoodsSearch) (err error, data interface{}, total int64, serverRatio int) {
	limit := info.Limit
	offset := info.Limit * (info.Page - 1)
	var dwdGoods []model.DwdProduct
	db := source.DB().Model(&model.DwdProduct{})
	db.Where("gather_supply_id = ?", info.GatherSupplyID)
	//if info.IsFreeShipping == 1 {
	//	db.Where("`supplierFreightPayer` = 0")
	//}
	if info.IsDisplay != nil {
		db.Where("`enabled` = ?", &info.IsDisplay)
	}
	//if info.IsFreeShipping == 2 {
	//	db.Where("`supplierFreightPayer` = 2")
	//}
	if info.SearchWords != "" {
		db.Where("`title` like ?", "%"+info.SearchWords+"%")
	}
	if info.IsImport > 0 {
		var dwdProductIds []uint
		err = source.DB().Model(&pmodel.Product{}).Where("gather_supply_id = ?", info.GatherSupplyID).Where("deleted_at is NULL").Pluck("source_goods_id", &dwdProductIds).Error
		if err != nil {
			return
		}

		if info.IsImport == 1 {
			db.Where("`product_id` in ?", dwdProductIds)

		} else if info.IsImport == 2 {
			if len(dwdProductIds) > 0 {
				db.Where("`product_id` not in ?", dwdProductIds)
			}
		}

	}
	if info.CategoryID > 0 {
		db.Where("`category_id` = ?", info.CategoryID)
	}
	if info.RangeType != "" {
		if info.RangeType == "guide_price" {
			db.Where("`guide_price` >= ?", info.RangeForm*100)
			db.Where("`guide_price` <= ?", info.RangeTo*100)
		}
		if info.RangeType == "agreement_price" {
			db.Where("`main_sku_price` >= ?", info.RangeForm*100)
			db.Where("`main_sku_price` <= ?", info.RangeTo*100)
		}

	}

	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&dwdGoods).Error

	data = dwd.ProductToGoods(dwdGoods, info.GatherSupplyID)
	return
}

func (*Dwd) ProductToGoods(data []model.DwdProduct, gatherID uint) (list []publicModel.Goods) {
	var categoryTree []model.DwdCategory
	err := source.DB().Find(&categoryTree).Error
	if err != nil {
		return
	}
	var categoryTreeMap = make(map[int]model.DwdCategory)
	for _, ct := range categoryTree {
		categoryTreeMap[ct.Id] = ct
	}
	for _, v := range data {
		var category1 model.DwdCategory
		var category2 model.DwdCategory
		var category3 model.DwdCategory
		var categortId int
		categortId, err = strconv.Atoi(v.CategoryId)
		var ok bool
		category3, ok = categoryTreeMap[categortId]
		if !ok {
			category3.Id = 0
			category3.Name = "其他"
		}
		category2, ok = categoryTreeMap[category3.Pid]
		if !ok {
			category2.Id = 0
			category2.Name = "其他"
		}
		category1, ok = categoryTreeMap[category2.Pid]
		if !ok {
			category1.Id = 0
			category1.Name = "其他"
		}
		var categoryIdsString []string
		categoryIdsString = append(categoryIdsString, strconv.Itoa(category1.Id))
		categoryIdsString = append(categoryIdsString, strconv.Itoa(category2.Id))
		categoryIdsString = append(categoryIdsString, strconv.Itoa(category3.Id))

		var thirdCategoryName []string
		thirdCategoryName = append(thirdCategoryName, category1.Name)
		thirdCategoryName = append(thirdCategoryName, category2.Name)
		thirdCategoryName = append(thirdCategoryName, category3.Name)
		var rate float64
		if v.Skus[0].MarketPrice > v.Skus[0].Price {
			rate = service2.Decimal((float64(v.Skus[0].MarketPrice) - float64(v.Skus[0].Price)) / float64(v.Skus[0].MarketPrice) * 100)
		}
		var intXS uint64
		var salePrice uint
		if DwdData.Pricing.SupplySales == 1 {
			intXS, err = strconv.ParseUint(DwdData.Pricing.SupplySalesGuide, 10, 32)
			salePrice = v.Skus[0].MarketPrice * uint(intXS) / 100
		} else if DwdData.Pricing.SupplySales == 2 {
			intXS, err = strconv.ParseUint(DwdData.Pricing.SupplySalesAgreement, 10, 32)
			salePrice = v.Skus[0].Price * uint(intXS) / 100
		} else {
			salePrice = v.Skus[0].Price
		}
		var item publicModel.Goods
		item = publicModel.Goods{
			GatherSupplyID:    gatherID,
			ThirdCategoryName: strings.Join(thirdCategoryName, ","),
			ThirdBrandName:    v.BrandName,
			MarketPrice:       v.Skus[0].MarketPrice,
			ID:                int(v.ID),
			ProductID:         int(v.ProductId),
			TotalStock:        v.Stock,
			Cover:             v.MainSkuPic,
			Status:            v.Enabled,
			Stock:             uint(v.Stock),
			Title:             v.Title,
			CategoryIds:       categoryIdsString,
			CostPrice:         uint(v.Skus[0].Price),
			AgreementPrice:    uint(v.Skus[0].Price),
			GuidePrice:        v.Skus[0].MarketPrice,
			Rate:              rate,
			SalePrice:         salePrice,
		}
		if item.MarketPrice < item.AgreementPrice {
			item.Rate = 0
		}
		list = append(list, item)
	}
	return

}

func (dwd *Dwd) ImportGoodsRun(info request.GetGoodsSearch) (err error, data interface{}) {
	db := source.DB().Model(&model.DwdProduct{})
	db.Where("gather_supply_id = ?", info.GatherSupplyID)
	//if info.IsFreeShipping == 1 {
	//	db.Where("`supplierFreightPayer` = 0")
	//}
	if info.IsDisplay != nil {
		db.Where("`enabled` = ?", &info.IsDisplay)
	}
	//if info.IsFreeShipping == 2 {
	//	db.Where("`supplierFreightPayer` = 2")
	//}
	if info.SearchWords != "" {
		db.Where("`title` like ?", "%"+info.SearchWords+"%")
	}
	if info.CategoryID > 0 {
		db.Where("`category_id` = ?", info.CategoryID)
	}
	if info.RangeType != "" {
		if info.RangeType == "guide_price" {
			db.Where("`guide_price` >= ?", info.RangeForm*100)
			db.Where("`guide_price` <= ?", info.RangeTo*100)
		}
		if info.RangeType == "agreement_price" {
			db.Where("`main_sku_price` >= ?", info.RangeForm*100)
			db.Where("`main_sku_price` <= ?", info.RangeTo*100)
		}

	}

	var total int64
	err = db.Count(&total).Error
	searchText, err := json.Marshal(info)

	orderPN := GetOrderNo()
	goodsRecord := publicModel.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: int(total),
		Status:            1,
		SearchCriteria:    string(searchText),
	}

	source.DB().Omit("goods_arr").CreateInBatches(&goodsRecord, 500)

	err = dwd.RunGoodsConcurrent(nil, info, db, 1, orderPN)
	if err != nil {
		return
	}
	err = SetImportRecordCompletion(orderPN)
	if err != nil {
		fmt.Println("变更导入记录状态错误", err)

	}
	fmt.Println("导入供应链商品全部完成")
	return

}

func (dwd *Dwd) RunGoodsConcurrent(wg *sync.WaitGroup, info request.GetGoodsSearch, db *gorm.DB, i int, orderPN string) (err error) {

	var response []model.DwdProduct
	err = db.Find(&response).Error
	if err != nil {
		return
	}
	if len(response) > 0 {

		cateList := strings.Split(info.Categorys, ",")
		var cateId1, cateId2, cateId3 int
		if len(cateList) >= 2 {
			cateId1, err = strconv.Atoi(cateList[0])
			cateId2, err = strconv.Atoi(cateList[1])
		}

		var ProductItem []model.DwdProduct
		var Item []publicModel.Goods
		ProductItem = response

		var resultArr []int
		err = source.DB().Model(pmodel.Product{}).Where("gather_supply_id = ?", info.GatherSupplyID).Pluck("source_goods_id", &resultArr).Error
		if err != nil {
			return
		}

		if len(ProductItem) <= 0 {
			fmt.Println("没有选择可导入的数据")

			return
		}

		Item = dwd.ProductToGoods(ProductItem, info.GatherSupplyID)
		idsArr := GetIdArrs(Item)

		difference := collection.Collect(idsArr).Diff(resultArr).ToIntArray()

		fmt.Println("查询到的导入数据：", idsArr)
		fmt.Println("已经存在的数据：", resultArr)
		fmt.Println("目前可以导入的数据：", difference)

		err = SetImportRepeat(orderPN, len(idsArr)-len(difference))

		if len(difference) <= 0 {
			fmt.Println("没有可以导入的数据")
			err = source.DB().Model(publicModel.SupplyGoodsImportRecord{}).Where("batch=?", orderPN).Update("status", 2).Error

			err = errors.New("当前数据已经存在，请导入新数据")
			return
		}
		var goodsList []publicModel.Goods

		for _, v := range difference {

			for _, item := range Item {
				if int(item.ProductID) == v {
					goodsList = append(goodsList, item)
				}
			}

		}

		var listGoods []*pmodel.Product
		err, listGoods, _ = dwd.CommodityAssembly(goodsList, cateId1, cateId2, cateId3, info.GatherSupplyID, 0)

		if len(listGoods) > 0 {
			service.FinalProcessing(listGoods, orderPN)
		}

	} else {
		fmt.Println("获取失败的：", i, info.Limit, info.Source)
	}

	return
}

// 商品组装
func (dwd *Dwd) CommodityAssembly(list []publicModel.Goods, cateId1, cateId2, cateId3 int, gatherSupplyID uint, isUpdate int) (err error, listGoods []*pmodel.Product, recordErrors []publicModel.SupplyGoodsImportRecordErrors) {
	idArr := GetIdArrs(list)
	var detailData = make(map[uint]model.DwdProduct)
	var data map[uint]model.DwdProduct
	err, data = dwd.BatchGetGoodsDetails(idArr, isUpdate)
	for _, detailItem := range data {
		detailData[detailItem.ProductId] = detailItem

	}

	//_, detailData := BatchGetGoodsDetails(ids) //批量获取详情
	var riskManageRecord []publicModel.RiskManagementRecord

	for _, elem := range list {
		detail, ok := detailData[uint(elem.ProductID)]
		if !ok && isUpdate == 0 {
			continue
		}
		goods := new(pmodel.Product)

		if isUpdate == 1 {
			goods.ID = uint(elem.ID)
		}
		goods.Title = elem.Title
		goods.Desc = detail.Desc
		if len(detail.Skus) > 0 && (detail.MarketPrice == 0 || detail.Price == 0) {
			detail.MarketPrice = detail.Skus[0].MarketPrice
			detail.Price = detail.Skus[0].Price
		}

		var intXAdvice uint64
		if DwdData.Pricing.SupplyAdvice == 1 {
			intXAdvice, err = strconv.ParseUint(DwdData.Pricing.SupplyAdviceGuide, 10, 32)
			goods.OriginPrice = detail.MarketPrice * uint(intXAdvice) / 100
		} else if DwdData.Pricing.SupplyAdvice == 2 {
			intXAdvice, err = strconv.ParseUint(DwdData.Pricing.SupplyAdviceAgreement, 10, 32)
			goods.OriginPrice = uint(detail.Price) * uint(intXAdvice) / 100
		} else {
			goods.OriginPrice = detail.MarketPrice
		}
		var intX uint64
		if DwdData.Pricing.SupplySales == 1 {
			intX, err = strconv.ParseUint(DwdData.Pricing.SupplySalesGuide, 10, 32)
			goods.Price = detail.MarketPrice * uint(intX) / 100
		} else if DwdData.Pricing.SupplySales == 2 {
			intX, err = strconv.ParseUint(DwdData.Pricing.SupplySalesAgreement, 10, 32)
			goods.Price = uint(detail.Price) * uint(intX) / 100
		} else {
			goods.Price = uint(detail.Price)
		}
		goods.GuidePrice = goods.OriginPrice
		goods.CostPrice = detail.Price
		goods.ActivityPrice = goods.GuidePrice
		goods.Stock = elem.Stock
		goods.IsDisplay = elem.Status
		var riskRecord publicModel.RiskManagementRecord
		riskRecord.ProductID = goods.ID
		riskRecord.SourceGoodsID = goods.SourceGoodsID
		riskRecord.GatherSupplyID = goods.GatherSupplyID
		if DwdData.Management.ProductPriceStatus == 1 {
			if goods.Price < goods.CostPrice*(DwdData.Management.Products/100) {
				goods.IsDisplay = 0
				riskManageRecord = append(riskManageRecord, riskRecord)

			}
		} else if DwdData.Management.ProductPriceStatus == 2 {
			if (goods.Price-goods.CostPrice)/goods.CostPrice < DwdData.Management.Profit/100 {
				goods.IsDisplay = 0
				riskManageRecord = append(riskManageRecord, riskRecord)

			}
		}
		goods.ImageUrl = elem.Cover + "?x-oss-process=style/normal"
		goods.Unit = elem.Unit
		goods.Sn = detail.Sn
		goods.Unit = "默认"
		goods.Source = common.DWD_SOURCE
		goods.SourceGoodsID = detail.ProductId
		goods.MD5 = detail.MD5
		if cateId1 == 0 || cateId2 == 0 || cateId3 == 0 {
			cateList := strings.Split(elem.ThirdCategoryName, ",")
			var cate1, cate2, cate3 model3.Category
			display := 1
			if len(cateList) > 0 && cateList[0] != "" {

				cate1.IsDisplay = &display
				cate1.ParentID = 0
				cate1.Level = 1
				cate1.Name = cateList[0]
				source.DB().Where("name=? and level=? and parent_id=?", cateList[0], 1, 0).FirstOrCreate(&cate1)
			} else {
				cate2.IsDisplay = &display
				cate2.ParentID = 0
				cate2.Level = 1
				cate2.Name = "默认"
				source.DB().Where("name=? and level=? and parent_id=?", cate1.Name, cate1.Level, cate1.ParentID).FirstOrCreate(&cate1)

			}

			if len(cateList) > 1 && cateList[1] != "" {
				cate2.IsDisplay = &display
				cate2.ParentID = cate1.ID
				cate2.Level = 2
				cate2.Name = cateList[1]
				source.DB().Where("name=? and level=? and parent_id=?", cateList[1], 2, cate1.ID).FirstOrCreate(&cate2)
			} else {
				cate2.IsDisplay = &display
				cate2.ParentID = cate1.ID
				cate2.Level = 2
				cate2.Name = "默认"
				source.DB().Where("name=? and level=? and parent_id=?", cate2.Name, cate2.Level, cate2.ParentID).FirstOrCreate(&cate2)

			}

			if len(cateList) > 2 && cateList[2] != "" {
				cate3.IsDisplay = &display
				cate3.ParentID = cate2.ID
				cate3.Level = 3
				cate3.Name = cateList[1]
				source.DB().Where("name=? and level=? and parent_id=?", cateList[1], 3, cate2.ID).FirstOrCreate(&cate3)
			} else {
				cate3.IsDisplay = &display
				cate3.ParentID = cate2.ID
				cate3.Level = 3
				cate3.Name = "默认"
				source.DB().Where("name=? and level=? and parent_id=?", cate3.Name, cate3.Level, cate3.ParentID).FirstOrCreate(&cate3)
			}

			goods.Category1ID = cate1.ID
			goods.Category2ID = cate2.ID
			goods.Category3ID = cate3.ID
		} else {
			goods.Category1ID = uint(cateId1)
			goods.Category2ID = uint(cateId2)
			goods.Category3ID = uint(cateId3)
		}
		var brand model3.Brand
		brand.Name = detail.BrandName
		source.DB().Where("`name` = ?", brand.Name).FirstOrCreate(&brand)
		goods.BrandID = brand.ID
		goods.FreightType = 0
		goods.GatherSupplyID = elem.GatherSupplyID

		/**
		处理轮播图
		*/
		for _, dSku := range detail.Skus {
			if dSku.SkuId == detail.MainSkuId {
				for _, img := range dSku.SkuImgs {
					goods.Gallery = append(goods.Gallery, pmodel.GalleryItem{
						Type: 1,
						Src:  img + "?x-oss-process=style/normal",
					})
				}
				goods.Freight = dSku.DeliveryFee
			}
		}

		if len(goods.Gallery) == 0 {
			if len(detail.Skus) > 0 {
				for _, img := range detail.Skus[0].SkuImgs {
					goods.Gallery = append(goods.Gallery, pmodel.GalleryItem{
						Type: 1,
						Src:  img + "?x-oss-process=style/normal",
					})
				}
			}

		}

		if len(goods.Gallery) == 0 {
			goods.Gallery = append(goods.Gallery, pmodel.GalleryItem{
				Type: 1,
				Src:  goods.ImageUrl,
			})

		}
		/**
		处理轮播图结束
		*/

		goods.MinPrice = uint(int(goods.Price))
		goods.MaxPrice = uint(int(goods.Price))
		var totalStock int

		var attributeCount int                      //判断是否每个sku中都有规格项
		var skuNameUnique = make(map[string]string) //判断是否每个sku中的name都不重复
		for _, detailSkuCheck := range detail.Skus {

			if len(detailSkuCheck.SaleAttribute) > 0 {
				attributeCount++
			}
			if detailSkuCheck.SkuName != "" {
				skuNameUnique[detailSkuCheck.SkuName] = detailSkuCheck.SkuName
			}
		}

		var minProfitRate float64
		for sk, detailSku := range detail.Skus {
			var sku = pmodel.Sku{}
			//canFixSku++

			if attributeCount == len(detail.Skus) {
				for _, skuSaleAttr := range detailSku.SaleAttribute {
					var option pmodel.Option
					option.SpecName = skuSaleAttr.AttributeName
					option.SpecItemName = skuSaleAttr.AttributeValue
					sku.Options = append(sku.Options, option)

				}
			} else if len(skuNameUnique) == len(detail.Skus) {
				sku.Options = append(sku.Options, pmodel.Option{
					SpecName:     "默认规格",
					SpecItemName: detailSku.SkuName,
				})
			} else {
				sku.Options = append(sku.Options, pmodel.Option{
					SpecName:     "默认规格",
					SpecItemName: detailSku.SkuName + "(" + strconv.Itoa(detailSku.SkuId) + ")",
				})
			}

			var intXSku uint64
			if DwdData.Pricing.SupplyAdvice == 1 {
				intXSku, err = strconv.ParseUint(DwdData.Pricing.SupplyAdviceGuide, 10, 32)
				sku.OriginPrice = detailSku.MarketPrice * uint(intXSku) / 100
			} else if DwdData.Pricing.SupplyAdvice == 2 {
				intXSku, err = strconv.ParseUint(DwdData.Pricing.SupplyAdviceAgreement, 10, 32)
				sku.OriginPrice = detailSku.Price * uint(intXSku) / 100
			} else {
				sku.OriginPrice = detailSku.MarketPrice
			}
			var intXS uint64
			if DwdData.Pricing.SupplySales == 1 {
				intXS, err = strconv.ParseUint(DwdData.Pricing.SupplySalesGuide, 10, 32)
				sku.Price = detailSku.MarketPrice * uint(intXS) / 100
			} else if DwdData.Pricing.SupplySales == 2 {
				intXS, err = strconv.ParseUint(DwdData.Pricing.SupplySalesAgreement, 10, 32)
				sku.Price = detailSku.Price * uint(intXS) / 100
			} else {
				sku.Price = detailSku.Price
			}
			sku.Title = detailSku.SkuName
			if sku.Title == "" {
				sku.Title = "编码：" + strconv.Itoa(detailSku.SkuId)
			}
			sku.Weight = 0
			sku.CostPrice = detailSku.Price
			sku.IsDisplay = detailSku.Enabled
			sku.Stock = detailSku.Stock
			if sku.IsDisplay == 0 {
				sku.Stock = 0
			}
			totalStock += sku.Stock
			sku.GuidePrice = sku.OriginPrice
			sku.ActivityPrice = sku.OriginPrice
			sku.OriginalSkuID = int64(detailSku.SkuId)
			sku.Sn = strconv.Itoa(detailSku.SkuId)
			if sku.GuidePrice > 0 {
				sku.ProfitRate = utils.ExecProfitRate(sku.GuidePrice, sku.Price)
			} else {
				sku.ProfitRate = 0
			}
			if sk == 0 {
				minProfitRate = sku.ProfitRate
			}
			if sku.ProfitRate <= minProfitRate {
				minProfitRate = sku.ProfitRate
			}
			goods.Skus = append(goods.Skus, sku)
			var attrName = sku.Title
			if attrName == "" {
				attrName = sku.Sn
			}
			goods.Attrs = append(goods.Attrs, pmodel.Attr{
				Name:  attrName + "的过期时间",
				Value: time.Unix(int64(detailSku.ExpiredDate), 0).Format("2006-01-02 15:04:05"),
			})
		}
		if len(goods.Skus) > 0 {
			goods.ProfitRate = minProfitRate
		}
		//for _, detailSku := range goods.Skus {
		//	for _, skuSaleAttr := range detailSku.Options {
		//		if !InArray(skuSaleAttr.SpecName, specKeys) {
		//			specKeys = append(specKeys, skuSaleAttr.SpecName)
		//
		//		}
		//	}
		//}
		//
		//for _, detailSku := range goods.Skus {
		//	for _, option := range detailSku.Options {
		//		if _, sok := spec[option.SpecName]; !sok || !InArray(option.SpecItemName, spec[option.SpecName]) {
		//			spec[option.SpecName] = append(spec[option.SpecName], option.SpecItemName)
		//		}
		//	}
		//}
		//if canFixSku > 0 {
		//	//补全sku
		//	var options [][]pmodel.Option
		//	for _, specKey := range specKeys {
		//		var optionS []pmodel.Option
		//		for _, o := range spec[specKey] {
		//			optionS = append(optionS, pmodel.Option{
		//				SpecName:     specKey,
		//				SpecItemName: o,
		//			})
		//		}
		//		fmt.Println(optionS)
		//		options = append(options, optionS)
		//	}
		//	var completeOptions [][]pmodel.Option
		//	completeOptions = GetSku(options)
		//	var fixOptions [][]pmodel.Option
		//
		//	for _, completeOption := range completeOptions {
		//		var completeOptionJson []byte
		//		completeOptionJson, err = model.JSONMarshal(completeOption)
		//		var exitsNum = 0
		//		for _, sku := range goods.Skus {
		//			var skuOptionJson []byte
		//			skuOptionJson, err = model.JSONMarshal(sku.Options)
		//			if string(completeOptionJson) == string(skuOptionJson) {
		//				exitsNum++
		//			}
		//		}
		//		if exitsNum == 0 {
		//			fixOptions = append(fixOptions, completeOption)
		//		}
		//	}
		//
		//	for _, fixOption := range fixOptions {
		//		goods.Skus = append(goods.Skus, pmodel.Sku{
		//			Options:       fixOption,
		//			Title:         "暂无",
		//			Price:         goods.Price,
		//			OriginPrice:   goods.OriginPrice,
		//			GuidePrice:    goods.GuidePrice,
		//			ActivityPrice: goods.ActivityPrice,
		//			CostPrice:     goods.CostPrice,
		//		})
		//	}
		//
		//}
		if totalStock == 0 {
			goods.IsDisplay = 0
		}
		//处理资质json图片数组

		goods.DetailImages = "<p>"
		for _, detailImg := range detail.ItemImgs {
			goods.DetailImages += "<img src=\"" + detailImg + "?x-oss-process=style/normal\">"
		}
		goods.DetailImages += "</p>"
		//--------处理详情json图片数组结束

		//处----------------理属性json数组

		//---------处理属性json数组结束
		//goods.Desc=detail.Description

		if len(goods.Skus) > 0 {
			listGoods = append(listGoods, goods)
		} else {
			fmt.Println("无规格商品，不导入", goods.ID)
		}

	}
	err = source.DB().CreateInBatches(&riskManageRecord, 1000).Error

	return
}
func GetSku(goods_attrs [][]pmodel.Option) [][]pmodel.Option {
	arrlen := len(goods_attrs) //列

	sku := make([][]pmodel.Option, 0)
	if arrlen == 0 {
		return sku
	}
	for _, val := range goods_attrs[0] {
		temps := make([]pmodel.Option, 0)
		temps = append(temps, val)
		sku = append(sku, temps)
	}
	for i := 0; i < arrlen-1; i++ {
		skuarr := make([][]pmodel.Option, 0)
		for _, val := range sku {
			for _, vals := range goods_attrs[i+1] {
				temp := make([]pmodel.Option, 0)
				temp = append(temp, val...)
				temp = append(temp, vals)
				skuarr = append(skuarr, temp)
			}
		}
		sku = skuarr
	}
	return sku
}
func InArray(need interface{}, haystack interface{}) bool {
	switch key := need.(type) {
	case int:
		for _, item := range haystack.([]int) {
			if item == key {
				return true
			}
		}
	case string:
		for _, item := range haystack.([]string) {
			if item == key {
				return true
			}
		}
	case int64:
		for _, item := range haystack.([]int64) {
			if item == key {
				return true
			}
		}
	case float64:
		for _, item := range haystack.([]float64) {
			if item == key {
				return true
			}
		}
	default:
		return false
	}
	return false
}

// 批量获取商品详情
func (*Dwd) BatchGetGoodsDetails(ids []int, isUpdate int) (err error, data map[uint]model.DwdProduct) {
	var detailList = make(map[uint]model.DwdProduct)

	fmt.Println("BatchGetGoodsDetails:", ids)
	var list []model.DwdProduct
	err = source.DB().Where("`product_id` in ?", ids).Where("`gather_supply_id` = ?", GatherSupplyID).Find(&list).Error
	if err != nil {
		return
	}

	var exitsList []pmodel.Product
	err = source.DB().Where("`source_goods_id` in ?", ids).Where("`gather_supply_id` = ?", GatherSupplyID).Find(&exitsList).Error
	if err != nil {
		return
	}
	var exitsMap = make(map[uint]pmodel.Product)
	for _, e := range exitsList {
		exitsMap[e.SourceGoodsID] = e
	}
	fmt.Println("总解析数量：", len(list))
	for _, item := range list {
		if _, ok := exitsMap[item.ProductId]; ok && isUpdate == 0 {
			continue
		}
		detailList[item.ProductId] = item
	}
	fmt.Println("总解析数量1：", len(detailList))

	data = detailList
	return
}

func (*Dwd) GetGroup() (err error, data interface{}) {

	return

}

func (*Dwd) GetCategory(info request.GetCategorySearch) (err error, data interface{}) {

	return
}

func (*Dwd) GetCategoryChild(pid int, info request.GetCategoryChild) (err error, data interface{}) {
	var categorys []model.DwdCategory
	err = source.DB().Where("pid = ?", pid).Find(&categorys).Error
	if err != nil {
		return
	}
	type DwdCategory struct {
		Id    int    `json:"id"`
		Pid   int    `json:"parent_id"`
		Depth int    `json:"level"`
		Name  string `json:"name"`
	}
	var dwdCategorys []DwdCategory
	for _, v := range categorys {
		dwdCategorys = append(dwdCategorys, DwdCategory{
			Id:    v.Id,
			Pid:   v.Pid,
			Depth: v.Depth,
			Name:  v.Name,
		})
	}
	return err, dwdCategorys

}
func (*Dwd) RunConcurrent(wg *sync.WaitGroup, info request.GetCategorySearch, i int) (err error) {

	return
}

// 选品库增加商品
func (*Dwd) GoodsStorageAdd(ids []int, supplyId uint) (err error, info interface{}) {
	for _, item := range ids {

		err = source.DB().Where(publicModel.GoodsStorage{SourceGoodsID: uint(item), SupplyID: supplyId}).FirstOrCreate(&publicModel.GoodsStorage{SourceGoodsID: uint(item), SupplyID: supplyId}).Error
		if err != nil {
			log.Log().Error("供应链选品入库错误", zap.Any("info", info))
		}
	}

	return

}

func (*Dwd) GoodsPriceAlert(GoodsData callback2.GoodsCallBack) (err error) {

	return
}
