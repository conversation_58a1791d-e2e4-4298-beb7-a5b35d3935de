package order

import (
	afterSalesModel "after-sales/model"
	request3 "after-sales/request"
	"after-sales/service"
	dwd "dwd-supply/component/goods"
	model5 "dwd-supply/model"
	response2 "dwd-supply/response"
	_ "embed"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
	url2 "net/url"
	v1 "order/api/v1"
	orderModel "order/model"
	request2 "order/request"
	model4 "product/model"
	callback2 "public-supply/callback"
	"public-supply/model"
	"public-supply/request"
	"public-supply/response"
	pubres "public-supply/response"
	setting2 "public-supply/setting"
	model3 "region/model"
	"strconv"
	"strings"
	"time"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

type Dwd struct{}

func (dwd *Dwd) UploadGatherSupplySN(request request.UpdateData) (err error, data interface{}) {
	//TODO implement me
	panic("implement me")
}

func (dwd *Dwd) GetRefundTypes(orderId uint, orderItemID uint) (err error, data []afterSalesModel.AfterSalesType) {
	return
}

func (dwd *Dwd) GetAllAddress() (err error, data interface{}) {
	return
}

type GoodsParamsList struct {
	Code     string `json:"code"`
	GoodsNum int    `json:"goodsNum"`
}
type DwdResponse struct {
	Code   int         `json:"code"`
	Result interface{} `json:"result"`
	Msg    string      `json:"msg"`
}
type DwdComfirmOrderResponse struct {
	BatchNo        string `json:"batch_no"`
	OutOrderId     string `json:"out_order_id"`
	TotalPrice     int    `json:"total_price"`
	PayPrice       int    `json:"pay_price"`
	ConsigneeName  string `json:"consignee_name"`
	ConsigneePhone string `json:"consignee_phone"`
	Province       string `json:"province"`
	City           string `json:"city"`
	District       string `json:"district"`
	Address        string `json:"address"`
	DeliveryPrice  int    `json:"delivery_price"`
	Orders         []struct {
		OrderId       int       `json:"order_id"`
		TotalPrice    int       `json:"total_price"`
		PayPrice      int       `json:"pay_price"`
		Amount        int       `json:"amount"`
		DeliveryPrice int       `json:"delivery_price"`
		UserMem       string    `json:"user_mem"`
		SubOrders     SubOrders `json:"sub_orders"`
	} `json:"orders"`
}
type DwdAfterSaleCreateResponse struct {
	BatchNo       string `json:"batch_no"`
	OrderId       int    `json:"order_id"`
	RefundOrderId int    `json:"refund_order_id"`
}
type DwdAfterSaleDetailResponse struct {
	BatchNo       string `json:"batch_no"`
	OrderId       int64  `json:"order_id"`
	RefundOrderId int    `json:"refund_order_id"`
	RefundStatus  int    `json:"refund_status"`
	Type          int    `json:"type"`
	Address       string `json:"address"`
	Remarks       string `json:"remarks"`
	RefundMoney   int    `json:"refund_money"`
	AuditStatus   int    `json:"audit_status"`
	AuditTime     int    `json:"audit_time"`
}
type DwdConfirmRequest struct {
	OutOrderId     string    `json:"out_order_id"`
	ConsigneeName  string    `json:"consignee_name"`
	ConsigneePhone string    `json:"consignee_phone"`
	Province       string    `json:"province"`
	City           string    `json:"city"`
	District       string    `json:"district"`
	Address        string    `json:"address"`
	SubOrders      SubOrders `json:"sub_orders"`
}
type SubOrders []SubOrder
type SubOrder struct {
	SubOrderId int    `json:"sub_order_id"`
	SkuId      int    `json:"sku_id"`
	SkuName    string `json:"sku_name"`
	UnitPrice  int    `json:"unit_price"`
	Amount     int    `json:"amount"`
	TotalPrice int    `json:"total_price"`
	PayPrice   int    `json:"pay_price"`
	ProductId  int    `json:"product_id"`
	Thumbnail  string `json:"thumbnail"`
}

var DwdData *dwd.DwdSupplySetting

var GatherSupplyID uint

func (*Dwd) InitSetting(gatherSupplyID uint) (err error) {
	var sysSetting model2.SysSetting
	err, sysSetting = setting2.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		return
	}
	GatherSupplyID = gatherSupplyID
	err = json.Unmarshal([]byte(sysSetting.Value), &DwdData)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	if DwdData.BaseInfo.AppId == "" || DwdData.BaseInfo.AppSecret == "" || DwdData.BaseInfo.ApiUrl == "" {
		err = errors.New("请先配置供应链key")
		return
	}

	return
}

// 订单前置校验  返回运费
func (dwd *Dwd) OrderBeforeCheck(request request.RequestSaleBeforeCheck) (err error, data response.BeforeCheck) {
	var notSendArea []string
	notSendArea = append(notSendArea, "新疆")
	notSendArea = append(notSendArea, "西藏")
	notSendArea = append(notSendArea, "青海")
	notSendArea = append(notSendArea, "甘肃")
	notSendArea = append(notSendArea, "宁夏")
	notSendArea = append(notSendArea, "内蒙")
	notSendArea = append(notSendArea, "海南")
	for _, nsa := range notSendArea {
		if strings.Contains(request.Address.Province, nsa) {
			err = errors.New("此地区不支持配送")
			return
		}
	}
	var dwdSkusIdsString string
	var dwdSkusIds []int64
	var skuIds []int64
	for _, v := range request.LocalSkus {
		skuIds = append(skuIds, v.Sku.Sku)
	}
	err = source.DB().Model(&model4.Sku{}).Where("id in ?", skuIds).Pluck("original_sku_id", &dwdSkusIds).Error
	if err != nil {
		return
	}

	var dwdSkusIds2 []string
	for _, dwdid := range dwdSkusIds {
		dwdSkusIds2 = append(dwdSkusIds2, strconv.Itoa(int(dwdid)))
	}
	dwdSkusIdsString = strings.Join(dwdSkusIds2, ",")
	var dwdSkuStock []model5.DwdStock
	err, dwdSkuStock = dwd.GetGoodsStock(dwdSkusIdsString)
	if err != nil {
		return
	}
	for _, v := range request.LocalSkus {
		var sku model4.Sku
		err = source.DB().First(&sku, v.Sku.Sku).Error
		if err != nil {
			return
		}
		var skuStock = 0
		for _, dwdStock := range dwdSkuStock {
			if sku.OriginalSkuID == int64(dwdStock.SkuId) {
				if dwdStock.Enabled == 0 {
					log.Log().Info("dwd sku已下架，id为"+strconv.Itoa(int(sku.OriginalSkuID)), zap.Any("info", err))
					err = errors.New("sku" + strconv.Itoa(int(sku.OriginalSkuID)) + "已下架")
					return
				}
				skuStock = dwdStock.Stock
			}
		}
		if skuStock < v.Number {
			log.Log().Info("dwd sku库存不足，id为"+strconv.Itoa(int(sku.OriginalSkuID)), zap.Any("info", err))
			err = errors.New("sku" + strconv.Itoa(int(sku.OriginalSkuID)) + "库存不足")
			return
		}

	}
	data.Code = 1
	return

}

// 商品是否可售前置校验
func (*Dwd) SaleBeforeCheck(request request.RequestSaleBeforeCheck) (err error, resData response.ResSaleBeforeCheck) {
	for _, v := range request.Skus {
		resData.Data.Available = append(resData.Data.Available, uint(v.Sku.Sku))
	}

	return

}

// 确认下单
func (*Dwd) ConfirmOrder(requestParam request.RequestConfirmOrder) (err error, info *stbz.APIResult) {

	log.Log().Info("dwd供应链商品准备下单", zap.Any("info", requestParam))
	//err, requestParam.Address = getDwdRegion(requestParam.Address)
	//if err != nil {
	//	return
	//}
	var notSendArea []string
	notSendArea = append(notSendArea, "新疆")
	notSendArea = append(notSendArea, "西藏")
	notSendArea = append(notSendArea, "青海")
	notSendArea = append(notSendArea, "甘肃")
	notSendArea = append(notSendArea, "宁夏")
	notSendArea = append(notSendArea, "内蒙")
	notSendArea = append(notSendArea, "海南")
	for _, nsa := range notSendArea {
		if strings.Contains(requestParam.Address.Province, nsa) {
			err = errors.New("此地区不支持配送")
			return
		}
	}
	var requestData DwdConfirmRequest
	requestData.OutOrderId = requestParam.OrderSn.OrderSn
	requestData.ConsigneeName = requestParam.Address.Consignee
	requestData.ConsigneePhone = requestParam.Address.Phone
	requestData.Province = requestParam.Address.Province
	requestData.City = requestParam.Address.City
	requestData.District = requestParam.Address.Area
	requestData.Address = requestParam.Address.Description
	var checkSameSku = make(map[int]bool)
	for _, v := range requestParam.Skus {
		if _, ok := checkSameSku[int(v.Sku.Sku)]; ok {
			for k, v2 := range requestData.SubOrders {
				if v2.SkuId == int(v.Sku.Sku) {
					requestData.SubOrders[k].Amount += v.Number
				}
			}
			continue
		}
		var sku model4.Sku
		err = source.DB().Where("original_sku_id = ?", int(v.Sku.Sku)).First(&sku).Error
		if err != nil {
			return
		}
		requestData.SubOrders = append(requestData.SubOrders, SubOrder{
			SkuId:  int(sku.OriginalSkuID),
			Amount: v.Number,
		})
		checkSameSku[int(v.Sku.Sku)] = true
	}
	var requestParams url2.Values
	err, requestParams = GetRequestParams(requestData, "order.create")

	if err != nil {
		return
	}
	err, result := utils.PostForm(DwdData.BaseInfo.ApiUrl, requestParams, nil)
	if err != nil {
		err = errors.New("dwd供应链下单错误1:" + err.Error())
		log.Log().Info("dwd供应链下单错误1", zap.Any("info", err))
		return
	}
	var resp DwdComfirmOrderResponse
	err, result = GetResponseParams(result)
	if err != nil {
		if strings.Contains(err.Error(), "50130004") {
			resp.OutOrderId = requestParam.OrderSn.OrderSn
		} else {
			err = errors.New("dwd供应链下单获取参数错误2:" + err.Error())
			log.Log().Info("dwd供应链下单获取参数错误2", zap.Any("info", err))
			return
		}

	} else {
		log.Log().Info("dwd供应链下单返回结果", zap.Any("info", string(result)))
		err = json.Unmarshal(result, &resp)
		if err != nil {
			return
		}
		err = source.DB().Model(&orderModel.Order{}).Where("`order_sn` = ?", requestParam.OrderSn.OrderSn).Update("gather_supply_sn", resp.BatchNo).Error
		if err != nil {
			err = errors.New("dwd供应链下单错误3:" + err.Error())
			log.Log().Info("dwd供应链下单错误3", zap.Any("info", err))
			return
		}
		var orderData orderModel.Order
		err = source.DB().Preload("OrderItems").Where("order_sn = ?", requestParam.OrderSn.OrderSn).First(&orderData).Error
		if err != nil {
			return
		}
		var updateproduct []map[string]interface{}
		source.BatchUpdate(updateproduct, "products", "")
		for _, orderItem := range orderData.OrderItems {
			for _, dwdOrderItem := range resp.Orders {
				for _, dwdOrderSubItem := range dwdOrderItem.SubOrders {
					if dwdOrderSubItem.SkuId == int(orderItem.OriginalSkuID) {
						updateproductRow := make(map[string]interface{})
						updateproductRow["id"] = orderItem.ID
						updateproductRow["gather_supply_sn"] = dwdOrderItem.OrderId
						updateproductRow["updated_at"] = time.Now().Format("2006-01-02 15:04:05")
						updateproduct = append(updateproduct, updateproductRow)
						break
					}

				}
			}
		}
		source.BatchUpdate(updateproduct, "order_items", "")
		fmt.Println("下单回调数据", string(result))

	}

	//支付
	var dwdOrder = Dwd{}
	err = dwdOrder.InitSetting(GatherSupplyID)
	if err != nil {
		err = errors.New("dwd供应链下单错误4:" + err.Error())
		log.Log().Info("dwd供应链下单错误4", zap.Any("info", err))
		return
	}
	err = dwdOrder.PayOrder(resp.BatchNo, resp.OutOrderId)
	if err != nil {
		err = errors.New("dwd供应链支付错误:" + err.Error())
		log.Log().Info("dwd供应链支付错误", zap.Any("info", err))
		return
	}
	return
}

func (*Dwd) PayOrder(batchNo string, outOrderId string) (err error) {
	//支付
	var payRequestData = make(map[string]string)
	var filterColumn string
	var filterValue string
	if batchNo != "" {
		payRequestData["batch_no"] = batchNo
		filterColumn = "gather_supply_sn"
		filterValue = batchNo
	} else if outOrderId != "" {
		payRequestData["out_order_id"] = outOrderId
		filterColumn = "sn"
		filterValue = outOrderId
	} else {
		err = errors.New("dwd参数错误，支付失败")
		return
	}
	var payRequestParams url2.Values
	err, payRequestParams = GetRequestParams(payRequestData, "order.pay")
	var payResult []byte
	err, payResult = utils.PostForm(DwdData.BaseInfo.ApiUrl, payRequestParams, nil)
	if err != nil {
		return
	}

	var finalResult []byte
	err, finalResult = GetResponseParams(payResult)
	if err != nil {
		if batchNo != "" {
			err = source.DB().Model(&orderModel.Order{}).Where(filterColumn+" = ?", filterValue).Update("gather_supply_msg", err.Error()).Error
			if err != nil {
				return
			}
		}

		return
	}

	var payResp = make(map[string]interface{})
	err = json.Unmarshal(finalResult, &payResp)
	if err != nil {
		return
	}
	//if _, ok := payResp["res"]; !ok || payResp["res"] != "true" {
	//	err = source.DB().Model(&orderModel.Order{}).Where("`gather_supply_sn` = ?", batchNo).Or("order_sn = ?", outOrderId).Update("gather_supply_msg", "支付失败").Error
	//	if err != nil {
	//		return
	//	}
	//	err = errors.New("支付失败，请重试")
	//} else {
	err = source.DB().Model(&orderModel.Order{}).Where(filterColumn+" = ?", filterValue).Update("gather_supply_msg", "支付成功,待发货").Error
	//}
	return
}

// 物流查询
func (*Dwd) ExpressQuery(request request.RequestExpress) (err error, info interface{}) {

	return

}

func (*Dwd) AfterSalesBeforeCheck(request request.RequestAfterSale) (err error, info interface{}) {

	return

}

func (*Dwd) AfterSalesPicture(request request.RequestAfterSalePicture) (err error, info interface{}) {

	return

}

func (*Dwd) AfterSale(request request.AfterSale) (err error, info interface{}) {

	return

}

// 发货
func (*Dwd) OrderDelivery(OrderData callback2.OrderCallBack) (err error) {

	return
}

func (*Dwd) SyncOrderExpNo(unionIdList []string) (err error, data []pubres.SyncOrderExpNoResponse) {

	return
}

//go:embed szbaoRegion.json
var szbaoRegion string

//go:embed dwdKd.json
var dwdKd string

func getDwdRegion(data request.ReceivingInformation) (err error, result request.ReceivingInformation) {
	result = data
	var province, city, area model3.Region
	err = source.DB().Where("name = ?", data.Province).Find(&province).Error
	err = source.DB().Where("name = ?", data.City).Where("parent_id = ?", province.ID).Find(&city).Error
	err = source.DB().Where("name = ?", data.Area).Where("parent_id = ?", city.ID).Find(&area).Error
	if err != nil {
		return
	}
	var szbaopRegions map[string]map[int]string
	err = json.Unmarshal([]byte(szbaoRegion), &szbaopRegions)

	result.Province = szbaopRegions["province_list"][province.ID*10000]

	result.City = szbaopRegions["city_list"][city.ID*100]

	result.Area = szbaopRegions["county_list"][area.ID]

	return
}

func GetRequestParams(params interface{}, action string) (err error, result url2.Values) {

	reqData := url2.Values{}
	var jsonBiz string
	var jsonData []byte
	jsonData, _ = json.Marshal(params)
	jsonBiz = string(jsonData)
	appId := DwdData.BaseInfo.AppId
	appSecret := DwdData.BaseInfo.AppSecret
	if DwdData.BaseInfo.ExpireTime <= time.Now().Unix() {
		err, DwdData.BaseInfo.AccessToken = GetToken()
		if err != nil {
			return
		}
	}
	token := DwdData.BaseInfo.AccessToken
	reqData.Add("biz", jsonBiz)
	key := utils.MD5V([]byte(token))
	key = key[:16]
	var encryptedBiz = string(dwd.Base64Encode([]byte(dwd.AesEncryptCBC(jsonBiz, key))))
	reqData.Add("encrypted_biz", encryptedBiz)
	reqData.Add("action", action)
	reqData.Add("appid", appId)
	reqData.Add("access_token", token)
	reqData.Add("timestamp", strconv.Itoa(int(time.Now().Unix())))
	var signatureParams = make(map[string]string)
	signatureParams["appid"] = appId
	signatureParams["action"] = action
	signatureParams["biz"] = jsonBiz
	signatureParams["encrypted_biz"] = encryptedBiz
	signatureParams["access_token"] = token
	signatureParams["timestamp"] = strconv.Itoa(int(time.Now().Unix()))
	var signature string
	//按照字母顺序遍历
	signature = appId
	dwd.TraverseMapInStringOrder(signatureParams, func(key string, value string) {
		signature += key + value
	})
	signature += appSecret
	signature = utils.MD5V([]byte(signature))
	reqData.Add("signature", signature)
	return err, reqData
}

func GetResponseParams(requestParams []byte) (err error, result []byte) {

	var resp response2.DwdBatchResponse
	err = json.Unmarshal(requestParams, &resp)
	if err != nil {
		return
	}
	if resp.Errno != 0 {
		log.Log().Info("dwd供应链下单获取参数错误2.5", zap.Any("info", resp))
		err = errors.New(strconv.Itoa(resp.Errno) + ":" + resp.Errmsg)
		return
	}

	//验证签名
	err = CheckSignature(resp)
	if err != nil {
		return
	}
	//获取解密的数据
	err, result = CheckAndDecryptResponse(resp)
	if err != nil {
		return
	}
	return err, result
}

func GetToken() (err error, token string) {

	reqData := url2.Values{}
	appId := DwdData.BaseInfo.AppId
	appSecret := DwdData.BaseInfo.AppSecret

	reqData.Add("appid", appId)
	reqData.Add("appsecret", appSecret)
	reqData.Add("timestamp", strconv.Itoa(int(time.Now().Unix())))
	var signatureParams = make(map[string]string)
	signatureParams["appid"] = appId
	signatureParams["appsecret"] = appSecret
	signatureParams["timestamp"] = strconv.Itoa(int(time.Now().Unix()))
	var signature string
	//按照字母顺序遍历
	signature = appId
	dwd.TraverseMapInStringOrder(signatureParams, func(key string, value string) {
		signature += key + value
	})
	signature += appSecret
	signature = utils.MD5V([]byte(signature))
	reqData.Add("signature", signature)
	err, result := utils.PostForm(DwdData.BaseInfo.ApiUrl+"/token", reqData, nil)
	if err != nil {
		return
	}
	var tokenResponseJson []byte
	err, tokenResponseJson = GetResponseParams(result)
	if err != nil {
		return
	}
	var tokenResponse DwdToken
	err = json.Unmarshal(tokenResponseJson, &tokenResponse)
	if err != nil {
		return
	}
	token = tokenResponse.AccessToken
	DwdData.BaseInfo.AccessToken = tokenResponse.AccessToken
	DwdData.BaseInfo.ExpireTime = time.Now().Unix() + tokenResponse.ExpireTime

	return
}

type DwdToken struct {
	AccessToken string `json:"access_token"`
	ExpireTime  int64  `json:"expire_in"`
}

func CheckAndDecryptResponse(response response2.DwdBatchResponse) (err error, decryptedResponse []byte) {
	var base64String []byte
	if response.Data != "" {
		return err, []byte(response.Data)
	}
	key := utils.MD5V([]byte(DwdData.BaseInfo.AccessToken))
	key = key[:16]
	var decryptData string
	decryptData, err = dwd.AesDecryptCBC(string(base64String), key)

	return err, []byte(decryptData)
}

func CheckSignature(response response2.DwdBatchResponse) (err error) {
	if response.EncryptedData != "" {
		var jsonData []byte
		jsonData, err = json.Marshal(response)
		if err != nil {
			return
		}
		var params = make(map[string]string)
		err = json.Unmarshal(jsonData, &params)
		var checkParams = make(map[string]string)
		for k, v := range params {
			if k != "signature" {
				checkParams[k] = v
			}
		}
		var signature string
		signature = DwdData.BaseInfo.AppId
		dwd.TraverseMapInStringOrder(checkParams, func(key string, value string) {
			signature += key + value
		})
		signature += DwdData.BaseInfo.AppSecret
		signature = utils.MD5V([]byte(signature))
		if signature != response.Signature {
			err = errors.New("签名验证失败")
			return
		}
	}

	return
}

func (*Dwd) DwdOrderSend(data model.DwdCallBackType) (err error) {
	//var jsonData []byte
	//jsonData, err = json.Marshal(data)
	var result []byte
	//err = CheckSignature(jsonData)
	//if err != nil {
	//	return
	//}
	//获取解密的数据
	err, result = CheckAndDecryptResponse(response2.DwdBatchResponse{EncryptedData: data.EncryptedData, Data: data.DwdData})
	if err != nil {
		return
	}

	var dwdResp response2.DwdCallBackData
	err = json.Unmarshal(result, &dwdResp)
	if err != nil {
		return
	}
	log.Log().Info("dwd发货物流信息", zap.Any("info", dwdResp))

	//处理订单逻辑
	var orderRequest v1.HandleOrderRequest
	orderRequest.ExpressNo = dwdResp.DeliveryNo

	//获取快递公司code
	var kdJson = make(map[string]string)
	err = json.Unmarshal([]byte(dwdKd), &kdJson)
	if err != nil {
		return
	}
	if _, ok := kdJson[dwdResp.DeliveryComCode]; !ok {
		err = errors.New("未匹配到此快递公司")
		return
	}

	var companyName string
	companyName = kdJson[dwdResp.DeliveryComCode]

	kds := GetCompanyList()
	for _, kdItem := range kds {
		if strings.Contains(kdItem.Name, companyName) || strings.Contains(companyName, kdItem.Name) {
			orderRequest.CompanyCode = kdItem.Code
			break
		}
	}
	if dwdResp.DeliveryComCode == "youzhengguonei" {
		orderRequest.CompanyCode = "EMS"
	}
	if dwdResp.DeliveryComCode == "EYB" {
		orderRequest.CompanyCode = "EMS"
	}
	if dwdResp.DeliveryComCode == "ems" {
		orderRequest.CompanyCode = "EMS"
	}
	if dwdResp.DeliveryComCode == "jtexpress" {
		orderRequest.CompanyCode = "JITU"
	}

	//获取快递公司code结束

	var orderData orderModel.Order
	err = source.DB().Preload("OrderItems").Where("order_sn = ?", dwdResp.OutOrderId).First(&orderData).Error
	if err != nil {
		log.Log().Info("dwd自动发货失败", zap.Any("data", dwdResp))
		return
	}

	orderRequest.OrderID = orderData.ID
	for _, dwdSku := range dwdResp.SkuList {

		if orderData.OrderItems != nil && len(orderData.OrderItems) > 0 {
			for _, orderItem := range orderData.OrderItems {
				if dwdSku.SkuId == strconv.Itoa(int(orderItem.OriginalSkuID)) {
					orderRequest.OrderItemIDs = append(orderRequest.OrderItemIDs, request2.OrderItemSendInfo{
						ID:  orderItem.ID,
						Num: orderItem.Qty,
					})
				}
			}
		}
	}
	err = ExpressSent(orderRequest)
	if err != nil {
		return
	}
	err = source.DB().Model(&orderModel.Order{}).Where("id = ?", orderData.ID).Update("gather_supply_msg", "已发货").Error
	if err != nil {
		return
	}

	return
}

func (*Dwd) DwdAfterSaleHandle(data model.DwdCallBackType) (err error) {
	//var jsonData []byte
	//jsonData, err = json.Marshal(data)
	var results []byte
	//err = CheckSignature(jsonData)
	//if err != nil {
	//	return
	//}
	//获取解密的数据
	err, results = CheckAndDecryptResponse(response2.DwdBatchResponse{EncryptedData: data.EncryptedData, Data: data.DwdData})
	if err != nil {
		return
	}

	var dwdResp response2.DwdAfCallBackData
	err = json.Unmarshal(results, &dwdResp)
	if err != nil {
		return
	}
	log.Log().Info("dwd售后同步信息", zap.Any("info", dwdResp))
	var as afterSalesModel.AfterSales
	err = source.DB().Preload("OrderItem").Where("syn_after_sales_id_string = ?", dwdResp.RefundOrderId).First(&as).Error
	if err != nil {
		log.Log().Info("dwd接到售后审核通过消息,执行错误1", zap.Any("err", err))
		return nil
	}
	//审核通过
	var resAfterSaleAudit afterSalesModel.AfterSalesAudit
	err = source.DB().Where("after_sales_id = ?", as.ID).Last(&resAfterSaleAudit).Error
	if err != nil {
		log.Log().Info("dwd接到售后审核通过消息,执行错误2", zap.Any("err", err))
		return nil
	}
	//获取售后地址
	var requestParamMap = make(map[string]interface{})
	requestParamMap["order_id"] = as.OrderItem.GatherSupplySN
	var requestParams url2.Values
	err, requestParams = GetRequestParams(requestParamMap, "refund.info")

	if err != nil {
		return
	}
	err, result := utils.PostForm(DwdData.BaseInfo.ApiUrl, requestParams, nil)
	if err != nil {
		err = errors.New("dwd供应链售后审核同步错误1:" + err.Error())
		log.Log().Info("dwd供应链售后审核同步错误1", zap.Any("info", err))
		return
	}
	log.Log().Info("dwd供应链同步售后审核同步", zap.Any("info", string(result)))

	var resp DwdAfterSaleDetailResponse
	err, result = GetResponseParams(result)
	if err != nil {

		err = errors.New("dwd供应链售后申请错误2:" + err.Error())
		log.Log().Info("dwd供应链售后申请错误2", zap.Any("info", err))
		return

	} else {
		err = json.Unmarshal(result, &resp)
		if err != nil {
			return
		}
		if resp.AuditStatus == 2 {
			//审核通过
			var shopAddress afterSalesModel.ShopAddress
			shopAddress.Address = resp.Address
			shopAddress.Tel = ""
			shopAddress.Contacts = ""
			err = source.DB().Model(&afterSalesModel.ShopAddress{}).FirstOrCreate(&shopAddress).Error
			if err != nil {
				log.Log().Info("dwd接到售后审核通过消息,执行错误7", zap.Any("err", err))
				return nil
			}
			resAfterSaleAudit.ShippingAddressID = shopAddress.ID
			err = service.PassAudit(resAfterSaleAudit, 0, 0)
			if err != nil {
				log.Log().Info("dwd接到售后审核通过消息,执行错误8", zap.Any("err", err))
			}
			if as.RefundType == 0 {
				err, _ = service.Refund(as, 0, 0)
				if err != nil {
					log.Log().Info("dwd接到售后审核通过消息,执行错误9", zap.Any("err", err))
					return nil
				}
			}

		} else if resp.AuditStatus == 3 {
			//审核驳回
			err = source.DB().Model(&afterSalesModel.AfterSales{}).Where("syn_after_sales_id_string = ?", dwdResp.RefundOrderId).Update("syn_message", "上游驳回售后审核:"+resp.Remarks).Error
			if err != nil {
				log.Log().Info("shama接到售后关闭/驳回申请的消息,执行错误3", zap.Any("err", err))
				return
			}
			//resAfterSaleAudit.Cause = resp.Remarks
			//err = service.RejectAudit(resAfterSaleAudit, 0, 0)
			//if err != nil {
			//	log.Log().Info("shama接到售后关闭/驳回申请的消息,执行错误3", zap.Any("err", err))
			//	return nil
			//}
		}

	}
	return
}
func (*Dwd) GetGoodsStock(idsString string) (err error, resp []model5.DwdStock) {
	var requestData = make(map[string]string)
	requestData["sku_ids"] = idsString
	var requestParams url2.Values
	err, requestParams = GetRequestParams(requestData, "goods.stock")

	if err != nil {
		return
	}
	err, result := utils.PostForm(DwdData.BaseInfo.ApiUrl, requestParams, nil)
	if err != nil {
		//log.Log().Info("dwd获取商品详情错误", zap.Any("info", err))
		return
	}

	err, result = GetResponseParams(result)
	if err != nil {
		//log.Log().Info("dwd获取商品详情错误", zap.Any("info", err))

		return
	}
	err = json.Unmarshal(result, &resp)
	if err != nil {
		return
	}
	return
}
func (dwd *Dwd) AfterSalesCreate(requestParam request3.AfterSales) (err error, info *stbz.APIResult) {

	log.Log().Info("dwd供应链同步售后", zap.Any("info", requestParam))
	var orderItem orderModel.OrderItem
	err = source.DB().Preload("Sku").First(&orderItem, requestParam.OrderItemID).Error
	if err != nil {
		log.Log().Info("dwd退货申请出错1:本地订单不存在")
		return
	}
	if orderItem.GatherSupplySN == "" {
		err = errors.New("dwd退货申请出错22:本地订单无上游订单id")
		log.Log().Info("dwd退货申请出错22:本地订单无上游订单id", zap.Any("request", requestParam))
		return
	}
	var orderData orderModel.Order
	err = source.DB().First(&orderData, requestParam.OrderID).Error
	if err != nil {
		log.Log().Info("dwd退货申请出错2:本地订单不存在1")
		return
	}
	var afterSale orderModel.AfterSales
	err = source.DB().First(&afterSale, requestParam.Id).Error
	if err != nil {
		log.Log().Info("dwd退货申请出错4:本地订单不存在1")
		return
	}
	var requestParamMap = make(map[string]interface{})
	requestParamMap["order_id"] = orderItem.GatherSupplySN
	if afterSale.Amount > orderItem.Sku.CostPrice*orderItem.Qty {
		requestParamMap["refund_money"] = orderItem.Sku.CostPrice * orderItem.Qty
	} else {
		requestParamMap["refund_money"] = afterSale.Amount
	}
	requestParamMap["reason_id"] = getReasonCode(afterSale.ReasonType, orderData.Status)
	requestParamMap["refund_reason_detail"] = afterSale.Reason
	requestParamMap["pics"] = afterSale.DetailImages
	log.Log().Info("dwd供应链同步售后请求参数", zap.Any("info", requestParamMap))

	var requestParams url2.Values
	err, requestParams = GetRequestParams(requestParamMap, "refund.apply")

	if err != nil {
		return
	}
	err, result := utils.PostForm(DwdData.BaseInfo.ApiUrl, requestParams, nil)
	if err != nil {
		err = errors.New("dwd供应链售后申请错误1:" + err.Error())
		log.Log().Info("dwd供应链售后申请错误1", zap.Any("info", err))
		return
	}
	log.Log().Info("dwd供应链同步售后请求结果", zap.Any("info", string(result)))

	var resp DwdAfterSaleCreateResponse
	err, result = GetResponseParams(result)
	if err != nil {

		err = errors.New("dwd供应链售后申请错误2:" + err.Error())
		log.Log().Info("dwd供应链售后申请错误2", zap.Any("info", err))
		return

	} else {
		err = json.Unmarshal(result, &resp)
		if err != nil {
			return
		}
		err = source.DB().Model(&orderModel.AfterSales{}).Where("after_sale_sn = ?", afterSale.AfterSaleSN).Update("syn_after_sales_id_string", resp.RefundOrderId).Error
		if err != nil {
			log.Log().Info("dwd售后申请失败2:" + err.Error())
			return
		}

	}

	return
}
func getReasonCode(reasonType int, orderStatus orderModel.OrderStatus) (reasonCode int) {
	if orderStatus == orderModel.WaitReceive || orderStatus == orderModel.WaitSend {
		switch reasonType {
		case 0:
			reasonCode = 100
			break
		case 1:
			reasonCode = 101
			break
		case 2:
			reasonCode = 101
			break

		case 3:
			reasonCode = 101
			break
		case 4:
			reasonCode = 103
			break
		case 5:
			reasonCode = 103
			break
		case 6:
			reasonCode = 103
			break
		case 7:
			reasonCode = 103
			break
		case 8:
			reasonCode = 103
			break
		case 9:
			reasonCode = 103
			break
		case 10:
			reasonCode = 103
			break
		case 11:
			reasonCode = 103
			break
		case 12:
			reasonCode = 103
			break
		case 13:
			reasonCode = 103
			break
		case 14:
			reasonCode = 103
			break
		}
	} else if orderStatus == orderModel.Completed {
		switch reasonType {
		case 0:
			reasonCode = 104
			break
		case 1:
			reasonCode = 108
			break
		case 2:
			reasonCode = 108
			break

		case 3:
			reasonCode = 108
			break
		case 4:
			reasonCode = 105
			break
		case 5:
			reasonCode = 108
			break
		case 6:
			reasonCode = 108
			break
		case 7:
			reasonCode = 108
			break
		case 8:
			reasonCode = 108
			break
		case 9:
			reasonCode = 108
			break
		case 10:
			reasonCode = 108
			break
		case 11:
			reasonCode = 106
			break
		case 12:
			reasonCode = 104
			break
		case 13:
			reasonCode = 105
			break
		case 14:
			reasonCode = 104
			break
		}
	}

	return
}
