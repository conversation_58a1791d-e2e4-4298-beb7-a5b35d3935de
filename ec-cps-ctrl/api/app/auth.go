package app

import (
	"ec-cps-ctrl/service"
	"yz-go/response"
	"yz-go/utils"

	"github.com/gin-gonic/gin"
)

// GetAccessTokens 获取AccessToken列表
func GetAccessTokens(c *gin.Context) {
	var req service.OpenTbAccessTokenListRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		// 如果绑定查询参数失败，尝试绑定JSON参数
		err = c.ShouldBindJSON(&req)
		if err != nil {
			// 使用默认参数
			req.Page = 1
			req.PageSize = 100
		}
	}

	// 获取中台ID
	req.ParentAppID = int(utils.GetAppID(c))

	// 调用service层获取AccessToken列表
	err, result := service.GetOpenTbAccessTokenList(req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}

func GetSetting(c *gin.Context) {

	err, tokens := service.GetSetting()
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithData(tokens, c)
}

func GetApiKey(c *gin.Context) {

	err, tokens := service.GetApiKey()
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithData(tokens, c)
}
