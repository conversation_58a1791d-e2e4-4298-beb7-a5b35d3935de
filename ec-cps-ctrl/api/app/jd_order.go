package app

import (
	"ec-cps-ctrl/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

// GetJDOrderList 获取京东订单列表
// @Tags 京东订单
// @Summary 获取京东订单列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query service.JDOrderListRequest true "分页获取京东订单列表"
// @Success 200 {object} yzResponse.Response{data=service.JDOrderListResult} "成功"
// @Router /jdOrder/list [get]
func GetJDOrderList(c *gin.Context) {
	var req service.JDOrderListRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Log().Error("参数绑定失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	req.ParentAppID = utils.GetAppID(c)
	req.ParentAppUserID = utils.GetAppUserID(c)
	// 调用service层获取京东订单列表
	err, result := service.GetJDOrderList(req)
	if err != nil {
		log.Log().Error("查询京东订单列表失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithData(result, c)
}

// QueryJDOrders 直接查询京东订单接口
// @Tags 京东订单
// @Summary 直接查询京东订单接口
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query service.JDOrderQueryRequest true "查询京东订单"
// @Success 200 {object} yzResponse.Response{data=service.JDOrderQueryResponse} "成功"
// @Router /jdOrder/query [get]
func QueryJDOrders(c *gin.Context) {
	var req service.JDOrderQueryRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		log.Log().Error("参数绑定失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 调用service层查询京东订单
	err, resp, _ := service.QueryJDOrders(req)
	if err != nil {
		log.Log().Error("查询京东订单失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithData(resp, c)
}

// ExportJDOrder 导出京东订单
// @Tags 京东订单
// @Summary 导出京东订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query service.JDOrderListRequest true "导出京东订单"
// @Success 200 {object} yzResponse.Response "成功"
// @Router /jdOrder/export [post]
func ExportJDOrder(c *gin.Context) {
	var req service.JDOrderListRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Log().Error("参数绑定失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 设置较大的页面大小以导出更多数据
	req.PageSize = 5000
	req.Page = 1

	// 调用service层导出京东订单
	err = service.ExportJDOrdersByQuery(req)
	if err != nil {
		log.Log().Error("导出京东订单失败", zap.Error(err))
		yzResponse.FailWithMessage("导出失败: "+err.Error(), c)
		return
	}

	yzResponse.OkWithMessage("导出成功，请在导出目录查看文件", c)
}

// GetJDOrderDetail 获取京东订单详情
// @Tags 京东订单
// @Summary 获取京东订单详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "订单ID"
// @Success 200 {object} yzResponse.Response{data=model.JDOrder} "成功"
// @Router /jdOrder/detail/{id} [get]
func GetJDOrderDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		log.Log().Error("参数转换失败", zap.Error(err))
		yzResponse.FailWithMessage("无效的订单ID", c)
		return
	}

	// 调用service层获取京东订单详情
	err, order := service.GetJDOrderDetail(id)
	if err != nil {
		log.Log().Error("查询京东订单详情失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithData(order, c)
}
