package app

import (
	"douyin-cps/request"
	"ec-cps-ctrl/service"
	"yz-go/component/log"
	yzResponse "yz-go/response"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func RedirectUri(c *gin.Context) {
	var info request.GetOpenTbTokenInfo
	err := c.ShouldBindQuery(&info)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err = service.RedirectUri(info)
	if err != nil {
		yzResponse.FailWithMessage("获取失败:"+err.<PERSON>rror(), c)
		return
	} else {
		yzResponse.OkWithMessage("获取成功", c)
		return
	}
}
