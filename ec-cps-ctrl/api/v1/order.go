package v1

import (
	"ec-cps-ctrl/request"
	"ec-cps-ctrl/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	v1 "user/api/f/v1"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

func GetOrderList(c *gin.Context) {
	var pageInfo request.OrderSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, total, list := service.GetOrderList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
			Total:    total,
			List:     list,
		}, "获取成功", c)
	}
}

func GetUserOrderList(c *gin.Context) {
	var pageInfo request.OrderSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	pageInfo.UserID = v1.GetUserID(c)
	if err, total, list := service.GetOrderList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
			Total:    total,
			List:     list,
		}, "获取成功", c)
	}
}

func ExportOrderList(c *gin.Context) {
	var pageInfo request.OrderSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, link := service.ExportOrderList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}

func GetAppOrderList(c *gin.Context) {
	var pageInfo request.OrderSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	pageInfo.AppID = utils.GetAppID(c)
	if err, total, list := service.GetAppOrderList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
			Total:    total,
			List:     list,
		}, "获取成功", c)
	}
}

func GetAppOrderDetailList(c *gin.Context) {
	var pageInfo request.OrderDetailSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list := service.GetAppOrderDetailList(pageInfo.CpsOrderIds); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List: list,
		}, "获取成功", c)
	}
}

func SetOrderDisplay(c *gin.Context) {
	var pageInfo request.OrderSetDisplay
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.SetOrderDisplay(pageInfo.ID); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}
