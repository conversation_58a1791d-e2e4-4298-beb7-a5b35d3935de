package v1

import (
	"ec-cps-ctrl/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
	"yz-go/component/log"
	yzResponse "yz-go/response"
)

// GetPddOrderList 获取拼多多订单列表
// @Tags 拼多多订单
// @Summary 获取拼多多订单列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query service.PddOrderListRequest true "分页获取拼多多订单列表"
// @Success 200 {object} yzResponse.Response{data=service.PddOrderListResult} "成功"
// @Router /pddOrder/list [get]
func GetPddOrderList(c *gin.Context) {
	var req service.PddOrderListRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		log.Log().Error("参数绑定失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 调用service层获取拼多多订单列表
	err, result := service.GetPddOrderList(req)
	if err != nil {
		log.Log().Error("查询拼多多订单列表失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithData(result, c)
}

// QueryPddOrders 直接查询拼多多订单接口
// @Tags 拼多多订单
// @Summary 直接查询拼多多订单接口
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query service.PddOrderQueryRequest true "查询拼多多订单"
// @Success 200 {object} yzResponse.Response{data=service.PddOrderQueryResponse} "成功"
// @Router /pddOrder/query [get]
func QueryPddOrders(c *gin.Context) {
	var req service.PddOrderQueryRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		log.Log().Error("参数绑定失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 调用service层查询拼多多订单
	err, resp, _ := service.QueryPddOrders(req)
	if err != nil {
		log.Log().Error("查询拼多多订单失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithData(resp, c)
}

// ExportPddOrder 导出拼多多订单
// @Tags 拼多多订单
// @Summary 导出拼多多订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query service.PddOrderListRequest true "导出拼多多订单"
// @Success 200 {object} yzResponse.Response "成功"
// @Router /pddOrder/export [post]
func ExportPddOrder(c *gin.Context) {
	var req service.PddOrderListRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Log().Error("参数绑定失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 设置较大的页面大小以导出更多数据
	req.PageSize = 5000
	req.Page = 1

	// 调用service层导出拼多多订单
	err = service.ExportPddOrdersByQuery(req)
	if err != nil {
		log.Log().Error("导出拼多多订单失败", zap.Error(err))
		yzResponse.FailWithMessage("导出失败: "+err.Error(), c)
		return
	}

	yzResponse.OkWithMessage("导出成功，请在导出目录查看文件", c)
}

// GetPddOrderDetail 获取拼多多订单详情
// @Tags 拼多多订单
// @Summary 获取拼多多订单详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "订单ID"
// @Success 200 {object} yzResponse.Response{data=model.PddOrder} "成功"
// @Router /pddOrder/detail/{id} [get]
func GetPddOrderDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		log.Log().Error("参数转换失败", zap.Error(err))
		yzResponse.FailWithMessage("无效的订单ID", c)
		return
	}

	// 调用service层获取拼多多订单详情
	err, order := service.GetPddOrderDetail(id)
	if err != nil {
		log.Log().Error("查询拼多多订单详情失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithData(order, c)
}
