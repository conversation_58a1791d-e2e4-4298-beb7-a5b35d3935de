package v1

import (
	"ec-cps-ctrl/model"
	"ec-cps-ctrl/service"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	yzResponse "yz-go/response"
)

func UpdateCpsSetting(c *gin.Context) {
	var sysSetting model.CpsSetting
	err := c.ShouldBindJSON(&sysSetting)
	if err != nil {
		return
	}
	fmt.Println(sysSetting.Value)
	sysSetting.Key = "ec_cps_setting"
	err = service.SaveCpsSetting(sysSetting)

	if err != nil {
		yzResponse.FailWithMessage("修改失败", c)
		return
	}
	model.ResetCps()
	yzResponse.OkWithMessage("修改成功", c)

}

func FindCpsSetting(c *gin.Context) {
	err, tradeSetting := service.GetCpsSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"setting": tradeSetting}, c)

}
