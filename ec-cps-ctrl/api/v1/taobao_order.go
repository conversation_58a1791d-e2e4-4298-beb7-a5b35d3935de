package v1

import (
	"ec-cps-ctrl/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
	"yz-go/component/log"
	yzResponse "yz-go/response"
)

// GetTaobaoOrderList 获取淘宝订单列表
// @Tags 淘宝订单
// @Summary 获取淘宝订单列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query service.TaobaoOrderListRequest true "分页获取淘宝订单列表"
// @Success 200 {object} yzResponse.Response{data=service.TaobaoOrderListResult} "成功"
// @Router /taobaoOrder/list [get]
func GetTaobaoOrderList(c *gin.Context) {
	var req service.TaobaoOrderListRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		log.Log().Error("参数绑定失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 调用service层获取淘宝订单列表
	err, result := service.GetTaobaoOrderList(req)
	if err != nil {
		log.Log().Error("查询淘宝订单列表失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 返回结果
	yzResponse.OkWithData(result, c)
}

// QueryTaobaoOrders 直接查询淘宝订单接口
// @Tags 淘宝订单
// @Summary 直接查询淘宝订单接口
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query service.TaobaoOrderQueryRequest true "查询淘宝订单"
// @Success 200 {object} yzResponse.Response{data=service.TaobaoOrderQueryResponse} "成功"
// @Router /taobaoOrder/query [get]
func QueryTaobaoOrders(c *gin.Context) {
	var req service.TaobaoOrderQueryRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		log.Log().Error("参数绑定失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 调用service层查询淘宝订单
	err, resp, _ := service.QueryTaobaoOrders(req)
	if err != nil {
		log.Log().Error("查询淘宝订单失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 返回结果
	yzResponse.OkWithData(resp, c)
}

// ExportTaobaoOrder 导出淘宝订单
// @Tags 淘宝订单
// @Summary 导出淘宝订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query service.TaobaoOrderListRequest true "导出淘宝订单"
// @Success 200 {object} yzResponse.Response "成功"
// @Router /taobaoOrder/export [post]
func ExportTaobaoOrder(c *gin.Context) {
	var req service.TaobaoOrderListRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Log().Error("参数绑定失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 设置较大的页面大小以导出更多数据
	req.PageSize = 5000
	req.Page = 1

	// 调用service层导出淘宝订单
	err = service.ExportTaobaoOrdersByQuery(req)
	if err != nil {
		log.Log().Error("导出淘宝订单失败", zap.Error(err))
		yzResponse.FailWithMessage("导出失败: "+err.Error(), c)
		return
	}

	yzResponse.OkWithMessage("导出任务已提交，请稍后查看导出文件", c)
}

// GetTaobaoOrderDetail 获取淘宝订单详情
// @Tags 淘宝订单
// @Summary 获取淘宝订单详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "订单ID"
// @Success 200 {object} yzResponse.Response{data=model.TaobaoOrder} "成功"
// @Router /taobaoOrder/detail/{id} [get]
func GetTaobaoOrderDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		log.Log().Error("参数转换失败", zap.Error(err))
		yzResponse.FailWithMessage("无效的订单ID", c)
		return
	}

	// 调用service层获取淘宝订单详情
	err, order := service.GetTaobaoOrderDetail(id)
	if err != nil {
		log.Log().Error("查询淘宝订单详情失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithData(order, c)
}
