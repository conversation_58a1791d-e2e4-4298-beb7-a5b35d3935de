package v1

import (
	"ec-cps-ctrl/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
	"yz-go/component/log"
	yzResponse "yz-go/response"
)

// GetVipOrderList 获取唯品会订单列表
// @Tags 唯品会订单
// @Summary 获取唯品会订单列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query service.VipOrderListRequest true "分页获取唯品会订单列表"
// @Success 200 {object} yzResponse.Response{data=service.VipOrderListResult} "成功"
// @Router /vipOrder/list [get]
func GetVipOrderList(c *gin.Context) {
	var req service.VipOrderListRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		log.Log().Error("参数绑定失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 调用service层获取唯品会订单列表
	err, result := service.GetVipOrderList(req)
	if err != nil {
		log.Log().Error("查询唯品会订单列表失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithData(result, c)
}

// QueryVipOrders 直接查询唯品会订单接口
// @Tags 唯品会订单
// @Summary 直接查询唯品会订单接口
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query service.VipOrderQueryRequest true "查询唯品会订单"
// @Success 200 {object} yzResponse.Response{data=service.VipOrderQueryResponse} "成功"
// @Router /vipOrder/query [get]
func QueryVipOrders(c *gin.Context) {
	var req service.VipOrderQueryRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		log.Log().Error("参数绑定失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 调用service层查询唯品会订单
	err, resp, _ := service.QueryVipOrders(req)
	if err != nil {
		log.Log().Error("查询唯品会订单失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithData(resp, c)
}

// ExportVipOrder 导出唯品会订单
// @Tags 唯品会订单
// @Summary 导出唯品会订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query service.VipOrderListRequest true "导出唯品会订单"
// @Success 200 {object} yzResponse.Response "成功"
// @Router /vipOrder/export [post]
func ExportVipOrder(c *gin.Context) {
	var req service.VipOrderListRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Log().Error("参数绑定失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 设置较大的页面大小以导出更多数据
	req.PageSize = 5000
	req.Page = 1

	// 调用service层导出唯品会订单
	err = service.ExportVipOrdersByQuery(req)
	if err != nil {
		log.Log().Error("导出唯品会订单失败", zap.Error(err))
		yzResponse.FailWithMessage("导出失败: "+err.Error(), c)
		return
	}

	yzResponse.OkWithMessage("导出成功，请在导出目录查看文件", c)
}

// GetVipOrderDetail 获取唯品会订单详情
// @Tags 唯品会订单
// @Summary 获取唯品会订单详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "订单ID"
// @Success 200 {object} yzResponse.Response{data=model.VipOrder} "成功"
// @Router /vipOrder/detail/{id} [get]
func GetVipOrderDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		log.Log().Error("参数转换失败", zap.Error(err))
		yzResponse.FailWithMessage("无效的订单ID", c)
		return
	}

	// 调用service层获取唯品会订单详情
	err, order := service.GetVipOrderDetail(id)
	if err != nil {
		log.Log().Error("查询唯品会订单详情失败", zap.Error(err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithData(order, c)
}
