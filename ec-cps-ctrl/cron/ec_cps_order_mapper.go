package cron

import (
	"ec-cps-ctrl/model"
	"fmt"
	"strconv"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
	"yz-go/component/log"
	"yz-go/source"
)

// OrderMapper 订单映射器接口
type OrderMapper interface {
	GetOrderID() string
	GetAppID() int
	GetAppUserID() int
	GetType() string
}

// processEcCpsOrdersGeneric 通用的EcCpsOrderModel处理函数
func processEcCpsOrdersGeneric(orders []OrderMapper, mapperFunc func(OrderMapper) model.EcCpsOrderModel, batchSize int, workerID int, orderType string) {
	if len(orders) == 0 {
		return
	}

	log.Log().Info("开始处理EcCpsOrderModel映射",
		zap.Int("协程ID", workerID),
		zap.Int("订单数量", len(orders)),
		zap.String("订单类型", orderType))

	// 收集所有订单ID
	var orderIds []string
	for _, order := range orders {
		orderIds = append(orderIds, order.GetOrderID())
	}

	// 查询数据库中已存在的EcCpsOrderModel
	var existingEcOrders []model.EcCpsOrderModel
	result := source.DB().Where("order_id IN ? AND type = ?", orderIds, orderType).Find(&existingEcOrders)
	if result.Error != nil && result.Error != gorm.ErrRecordNotFound {
		log.Log().Error("查询现有EcCpsOrderModel失败", zap.Int("协程ID", workerID), zap.Error(result.Error))
		return
	}

	// 将现有订单映射到map中便于快速查找
	existingEcOrderMap := make(map[string]model.EcCpsOrderModel)
	for _, order := range existingEcOrders {
		existingEcOrderMap[order.OrderId] = order
	}

	// 准备新订单和需要更新的订单
	var newEcOrders []model.EcCpsOrderModel
	var updateEcOrders []model.EcCpsOrderModel

	for _, order := range orders {
		// 映射订单到EcCpsOrderModel
		ecOrder := mapperFunc(order)

		if _, exists := existingEcOrderMap[ecOrder.OrderId]; !exists {
			// 新订单
			newEcOrders = append(newEcOrders, ecOrder)
		} else {
			// 需要更新的订单
			dbEcOrder := existingEcOrderMap[ecOrder.OrderId]
			// 更新关键字段
			updateEcOrder := updateEcCpsOrderFields(dbEcOrder, ecOrder)
			updateEcOrders = append(updateEcOrders, updateEcOrder)
		}
	}

	// 批量创建新的EcCpsOrderModel
	if len(newEcOrders) > 0 {
		createEcCpsOrdersBatch(newEcOrders, batchSize, workerID)
	}

	// 批量更新EcCpsOrderModel
	if len(updateEcOrders) > 0 {
		updateEcCpsOrdersBatch(updateEcOrders, batchSize, workerID, orderType)
	}

	log.Log().Info("完成EcCpsOrderModel处理",
		zap.Int("协程ID", workerID),
		zap.Int("新增数量", len(newEcOrders)),
		zap.Int("更新数量", len(updateEcOrders)),
		zap.String("订单类型", orderType))
}

// updateEcCpsOrderFields 更新EcCpsOrderModel字段
func updateEcCpsOrderFields(dbOrder, newOrder model.EcCpsOrderModel) model.EcCpsOrderModel {
	// 更新关键字段
	dbOrder.TotalPayAmount = newOrder.TotalPayAmount
	dbOrder.PayGoodsAmount = newOrder.PayGoodsAmount
	dbOrder.EstimatedCommission = newOrder.EstimatedCommission
	dbOrder.SplitRate = newOrder.SplitRate
	dbOrder.FlowPoint = newOrder.FlowPoint
	dbOrder.AfterSalesStatus = newOrder.AfterSalesStatus
	dbOrder.ConfirmTime = newOrder.ConfirmTime
	dbOrder.SettleTime = newOrder.SettleTime
	dbOrder.RefundTime = newOrder.RefundTime
	dbOrder.EstimatedTechServiceFee = newOrder.EstimatedTechServiceFee
	dbOrder.ExternalInfo = newOrder.ExternalInfo
	return dbOrder
}

// createEcCpsOrdersBatch 批量创建EcCpsOrderModel
func createEcCpsOrdersBatch(orders []model.EcCpsOrderModel, batchSize int, workerID int) {
	for i := 0; i < len(orders); i += batchSize {
		end := i + batchSize
		if end > len(orders) {
			end = len(orders)
		}

		batch := orders[i:end]
		if err := source.DB().Create(&batch).Error; err != nil {
			log.Log().Error("批量创建EcCpsOrderModel失败", zap.Int("协程ID", workerID), zap.Error(err))
		} else {
			log.Log().Info("成功创建新EcCpsOrderModel", zap.Int("协程ID", workerID), zap.Int("数量", len(batch)))
		}
	}
}

// updateEcCpsOrdersBatch 批量更新EcCpsOrderModel
func updateEcCpsOrdersBatch(orders []model.EcCpsOrderModel, batchSize int, workerID int, orderType string) {
	for i := 0; i < len(orders); i += batchSize {
		end := i + batchSize
		if end > len(orders) {
			end = len(orders)
		}

		batch := orders[i:end]
		for _, order := range batch {
			if err := source.DB().Model(&model.EcCpsOrderModel{}).Where("order_id = ? AND type = ?", order.OrderId, orderType).Updates(map[string]interface{}{
				"total_pay_amount":           order.TotalPayAmount,
				"pay_goods_amount":           order.PayGoodsAmount,
				"estimated_commission":       order.EstimatedCommission,
				"split_rate":                 order.SplitRate,
				"flow_point":                 order.FlowPoint,
				"after_sales_status":         order.AfterSalesStatus,
				"confirm_time":               order.ConfirmTime,
				"settle_time":                order.SettleTime,
				"refund_time":                order.RefundTime,
				"estimated_tech_service_fee": order.EstimatedTechServiceFee,
				"external_info":              order.ExternalInfo,
			}).Error; err != nil {
				log.Log().Error("更新EcCpsOrderModel失败", zap.Int("协程ID", workerID), zap.String("订单ID", order.OrderId), zap.Error(err))
			}
		}
		log.Log().Info("成功更新EcCpsOrderModel", zap.Int("协程ID", workerID), zap.Int("数量", len(batch)))
	}
}

// JDOrderMapper JD订单映射器
type JDOrderMapper struct {
	Order model.JDOrder
}

func (j JDOrderMapper) GetOrderID() string { return j.Order.OrderId }
func (j JDOrderMapper) GetAppID() int      { return j.Order.AppID }
func (j JDOrderMapper) GetAppUserID() int  { return j.Order.AppUserID }
func (j JDOrderMapper) GetType() string    { return "jd" }

// TaobaoOrderMapper 淘宝订单映射器
type TaobaoOrderMapper struct {
	Order model.TaobaoOrder
}

func (t TaobaoOrderMapper) GetOrderID() string { return t.Order.TradeId }
func (t TaobaoOrderMapper) GetAppID() int      { return t.Order.AppID }
func (t TaobaoOrderMapper) GetAppUserID() int  { return t.Order.AppUserID }
func (t TaobaoOrderMapper) GetType() string    { return "taobao" }

// PddOrderMapper 拼多多订单映射器
type PddOrderMapper struct {
	Order model.PddOrder
}

func (p PddOrderMapper) GetOrderID() string { return p.Order.OrderSn }
func (p PddOrderMapper) GetAppID() int      { return p.Order.AppID }
func (p PddOrderMapper) GetAppUserID() int  { return p.Order.AppUserID }
func (p PddOrderMapper) GetType() string    { return "pdd" }

// VipOrderMapper 唯品会订单映射器
type VipOrderMapper struct {
	Order model.VipOrder
}

func (v VipOrderMapper) GetOrderID() string { return v.Order.OrderSn }
func (v VipOrderMapper) GetAppID() int      { return int(v.Order.AppID) }
func (v VipOrderMapper) GetAppUserID() int  { return int(v.Order.AppUserID) }
func (v VipOrderMapper) GetType() string    { return "vip" }

// mapJDOrderToEcCpsOrder 将JDOrder映射到EcCpsOrderModel
func mapJDOrderToEcCpsOrder(mapper OrderMapper) model.EcCpsOrderModel {
	jdMapper := mapper.(JDOrderMapper)
	jdOrder := jdMapper.Order

	ecOrder := model.EcCpsOrderModel{
		OrderId:       jdOrder.OrderId,
		AppId:         strconv.Itoa(jdOrder.AppID),
		UserID:        uint(jdOrder.AppUserID),
		ApplicationID: uint(jdOrder.AppID),
		ThirdUserID:   uint(jdOrder.AppUserID),
		ProductId:     jdOrder.SkuId,
		ProductName:   jdOrder.SkuName,
		Type:          "jd",
	}

	// 设置支付金额（转换为分）
	if jdOrder.Price > 0 && jdOrder.SkuNum > 0 {
		ecOrder.TotalPayAmount = int(jdOrder.Price * float64(jdOrder.SkuNum) * 100)
		ecOrder.PayGoodsAmount = int(jdOrder.ActualCosPrice * 100)
	}

	// 设置佣金（转换为分）
	ecOrder.EstimatedCommission = int(jdOrder.EstimateFee * 100)

	// 设置佣金比例（转换为万分比）
	if jdOrder.CommissionRate > 0 {
		ecOrder.SplitRate = int(jdOrder.CommissionRate * 10000)
	}

	// 设置时间字段
	ecOrder.PaySuccessTime = jdOrder.OrderTime
	ecOrder.ConfirmTime = jdOrder.FinishTime
	ecOrder.SettleTime = jdOrder.PayMonth

	// 设置订单状态
	ecOrder.FlowPoint = mapJDValidCodeToFlowPoint(jdOrder.ValidCode)

	// 设置售后状态
	if jdOrder.SkuReturnNum > 0 || jdOrder.SkuFrozenNum > 0 {
		ecOrder.AfterSalesStatus = 2 // 产生退款
		ecOrder.RefundTime = jdOrder.ModifyTime // 使用修改时间作为退款时间
	} else {
		ecOrder.AfterSalesStatus = 1 // 空
	}

	// 设置外部信息
	ecOrder.ExternalInfo = fmt.Sprintf("position_id:%s,union_id:%s,sku_num:%d", jdOrder.PositionId, jdOrder.UnionId, jdOrder.SkuNum)

	// 设置显示状态
	ecOrder.IsDisplay = 1

	// 设置分成状态
	ecOrder.Status = 0 // 默认未分成

	// 设置技术服务费（预估）
	if jdOrder.EstimateFee > 0 {
		ecOrder.EstimatedTechServiceFee = int(jdOrder.EstimateFee * 0.1 * 100) // 假设技术服务费为佣金的10%
	}

	return ecOrder
}

// mapTaobaoOrderToEcCpsOrder 将TaobaoOrder映射到EcCpsOrderModel
func mapTaobaoOrderToEcCpsOrder(mapper OrderMapper) model.EcCpsOrderModel {
	taobaoMapper := mapper.(TaobaoOrderMapper)
	taobaoOrder := taobaoMapper.Order

	ecOrder := model.EcCpsOrderModel{
		OrderId:       taobaoOrder.TradeId,
		AppId:         strconv.Itoa(taobaoOrder.AppID),
		UserID:        uint(taobaoOrder.AppUserID),
		ApplicationID: uint(taobaoOrder.AppID),
		ThirdUserID:   uint(taobaoOrder.AppUserID),
		ProductId:     taobaoOrder.ItemId,
		ProductName:   taobaoOrder.ItemTitle,
		ProductImg:    taobaoOrder.ItemImg,
		Type:          "taobao",
	}

	// 设置支付金额（转换为分）
	if itemPrice, err := strconv.ParseFloat(taobaoOrder.ItemPrice, 64); err == nil {
		ecOrder.TotalPayAmount = int(itemPrice * float64(taobaoOrder.ItemNum) * 100)
	}

	if alipayTotalPrice, err := strconv.ParseFloat(taobaoOrder.AlipayTotalPrice, 64); err == nil {
		ecOrder.PayGoodsAmount = int(alipayTotalPrice * 100)
	}

	// 设置佣金（转换为分）
	if pubShareFee, err := strconv.ParseFloat(taobaoOrder.PubShareFee, 64); err == nil {
		ecOrder.EstimatedCommission = int(pubShareFee * 100)
	}

	// 设置佣金比例（转换为万分比）
	if tkTotalRate, err := strconv.ParseFloat(taobaoOrder.TkTotalRate, 64); err == nil {
		ecOrder.SplitRate = int(tkTotalRate * 10000)
	}

	// 设置时间字段
	ecOrder.PaySuccessTime = taobaoOrder.TkPaidTime
	ecOrder.ConfirmTime = taobaoOrder.TkEarningTime
	ecOrder.SettleTime = taobaoOrder.TkEarningTime // 淘宝没有单独的结算时间，使用确认收货时间

	// 设置订单状态
	ecOrder.FlowPoint = mapTaobaoStatusToFlowPoint(taobaoOrder.TkStatus)

	// 设置售后状态
	if taobaoOrder.RefundTag == 1 {
		ecOrder.AfterSalesStatus = 2 // 产生退款
		ecOrder.RefundTime = taobaoOrder.TkEarningTime // 使用确认收货时间作为退款时间
	} else {
		ecOrder.AfterSalesStatus = 1 // 空
	}

	// 设置外部信息
	ecOrder.ExternalInfo = fmt.Sprintf("special_id:%s,relation_id:%s,item_num:%d", taobaoOrder.SpecialId, taobaoOrder.RelationId, taobaoOrder.ItemNum)

	// 设置显示状态
	ecOrder.IsDisplay = 1

	// 设置分成状态
	ecOrder.Status = 0 // 默认未分成

	// 设置技术服务费（预估）
	if pubShareFee, err := strconv.ParseFloat(taobaoOrder.PubShareFee, 64); err == nil {
		ecOrder.EstimatedTechServiceFee = int(pubShareFee * 0.1 * 100) // 假设技术服务费为佣金的10%
	}

	return ecOrder
}

// mapPddOrderToEcCpsOrder 将PddOrder映射到EcCpsOrderModel
func mapPddOrderToEcCpsOrder(mapper OrderMapper) model.EcCpsOrderModel {
	pddMapper := mapper.(PddOrderMapper)
	pddOrder := pddMapper.Order

	ecOrder := model.EcCpsOrderModel{
		OrderId:       pddOrder.OrderSn,
		AppId:         strconv.Itoa(pddOrder.AppID),
		UserID:        uint(pddOrder.AppUserID),
		ApplicationID: uint(pddOrder.AppID),
		ThirdUserID:   uint(pddOrder.AppUserID),
		ProductId:     strconv.FormatInt(pddOrder.GoodsId, 10),
		ProductName:   pddOrder.GoodsName,
		ProductImg:    pddOrder.GoodsThumbnailUrl,
		Type:          "pdd",
	}

	// 设置支付金额（已经是分）
	ecOrder.TotalPayAmount = int(pddOrder.OrderAmount)
	ecOrder.PayGoodsAmount = int(pddOrder.OrderAmount)

	// 设置佣金（已经是分）
	ecOrder.EstimatedCommission = int(pddOrder.PromotionAmount)

	// 设置佣金比例（转换为万分比）
	if pddOrder.OrderAmount > 0 {
		rate := float64(pddOrder.PromotionAmount) / float64(pddOrder.OrderAmount)
		ecOrder.SplitRate = int(rate * 10000)
	}

	// 设置时间字段
	if pddOrder.OrderPayTime > 0 {
		ecOrder.PaySuccessTime = time.Unix(pddOrder.OrderPayTime, 0).Format("2006-01-02 15:04:05")
	}
	if pddOrder.OrderVerifyTime > 0 {
		ecOrder.ConfirmTime = time.Unix(pddOrder.OrderVerifyTime, 0).Format("2006-01-02 15:04:05")
	}
	if pddOrder.OrderSettleTime > 0 {
		ecOrder.SettleTime = time.Unix(pddOrder.OrderSettleTime, 0).Format("2006-01-02 15:04:05")
	}

	// 设置订单状态
	ecOrder.FlowPoint = mapPddStatusToFlowPoint(pddOrder.OrderStatus)

	// 设置售后状态
	if pddOrder.OrderStatus == -1 {
		ecOrder.AfterSalesStatus = 2 // 产生退款
		if pddOrder.OrderModifyAt > 0 {
			ecOrder.RefundTime = time.Unix(pddOrder.OrderModifyAt, 0).Format("2006-01-02 15:04:05")
		}
	} else {
		ecOrder.AfterSalesStatus = 1 // 空
	}

	// 设置外部信息
	ecOrder.ExternalInfo = fmt.Sprintf("goods_quantity:%d,order_status_desc:%s", pddOrder.GoodsQuantity, pddOrder.OrderStatusDesc)

	// 设置显示状态
	ecOrder.IsDisplay = 1

	// 设置分成状态
	ecOrder.Status = 0 // 默认未分成

	// 设置技术服务费（预估）
	if pddOrder.PromotionAmount > 0 {
		ecOrder.EstimatedTechServiceFee = int(float64(pddOrder.PromotionAmount) * 0.1) // 假设技术服务费为佣金的10%
	}

	return ecOrder
}

// mapVipOrderToEcCpsOrder 将VipOrder映射到EcCpsOrderModel
func mapVipOrderToEcCpsOrder(mapper OrderMapper) model.EcCpsOrderModel {
	vipMapper := mapper.(VipOrderMapper)
	vipOrder := vipMapper.Order

	ecOrder := model.EcCpsOrderModel{
		OrderId:       vipOrder.OrderSn,
		AppId:         strconv.Itoa(int(vipOrder.AppID)),
		UserID:        vipOrder.AppUserID,
		ApplicationID: vipOrder.AppID,
		ThirdUserID:   vipOrder.AppUserID,
		ProductId:     vipOrder.OrderSn, // 唯品会没有单独的商品ID，使用订单号
		ProductName:   "唯品会订单",        // 唯品会订单可能包含多个商品
		Type:          "vip",
	}

	// 设置支付金额（转换为分）
	if totalCost, err := strconv.ParseFloat(vipOrder.TotalCost, 64); err == nil {
		ecOrder.TotalPayAmount = int(totalCost * 100)
		ecOrder.PayGoodsAmount = int(totalCost * 100)
	}

	// 设置佣金（转换为分）
	if commission, err := strconv.ParseFloat(vipOrder.Commission, 64); err == nil {
		ecOrder.EstimatedCommission = int(commission * 100)
	}

	// 设置佣金比例（转换为万分比）
	if totalCost, err1 := strconv.ParseFloat(vipOrder.TotalCost, 64); err1 == nil {
		if commission, err2 := strconv.ParseFloat(vipOrder.Commission, 64); err2 == nil && totalCost > 0 {
			rate := commission / totalCost
			ecOrder.SplitRate = int(rate * 10000)
		}
	}

	// 设置时间字段
	if vipOrder.OrderTime > 0 {
		ecOrder.PaySuccessTime = time.Unix(vipOrder.OrderTime/1000, 0).Format("2006-01-02 15:04:05")
	}
	if vipOrder.SignTime > 0 {
		ecOrder.ConfirmTime = time.Unix(vipOrder.SignTime/1000, 0).Format("2006-01-02 15:04:05")
	}
	if vipOrder.SettledTime > 0 {
		ecOrder.SettleTime = time.Unix(vipOrder.SettledTime/1000, 0).Format("2006-01-02 15:04:05")
	}

	// 设置订单状态
	ecOrder.FlowPoint = mapVipStatusToFlowPoint(vipOrder.Status)

	// 设置售后状态
	if vipOrder.Status == 0 {
		ecOrder.AfterSalesStatus = 2 // 不合格，可能是退款
		if vipOrder.LastUpdateTime > 0 {
			ecOrder.RefundTime = time.Unix(vipOrder.LastUpdateTime/1000, 0).Format("2006-01-02 15:04:05")
		}
	} else {
		ecOrder.AfterSalesStatus = 1 // 空
	}

	// 设置外部信息
	ecOrder.ExternalInfo = fmt.Sprintf("channel_tag:%s,new_customer:%d,self_buy:%d", vipOrder.ChannelTag, vipOrder.NewCustomer, vipOrder.SelfBuy)

	// 设置显示状态
	ecOrder.IsDisplay = 1

	// 设置分成状态
	ecOrder.Status = 0 // 默认未分成

	// 设置技术服务费（预估）
	if commission, err := strconv.ParseFloat(vipOrder.Commission, 64); err == nil && commission > 0 {
		ecOrder.EstimatedTechServiceFee = int(commission * 0.1 * 100) // 假设技术服务费为佣金的10%
	}

	return ecOrder
}

// mapJDValidCodeToFlowPoint 将京东订单状态映射到流程状态
func mapJDValidCodeToFlowPoint(validCode int) string {
	switch validCode {
	case 15: // 已付款
		return "PAY_SUCC"
	case 16: // 已完成
		return "CONFIRM"
	case 17: // 已结算
		return "SETTLE"
	case -1: // 已退款
		return "REFUND"
	default:
		return "PAY_SUCC" // 默认为支付完成
	}
}

// mapTaobaoStatusToFlowPoint 将淘宝订单状态映射到流程状态
func mapTaobaoStatusToFlowPoint(tkStatus int) string {
	switch tkStatus {
	case 12: // 付款
		return "PAY_SUCC"
	case 13: // 结算
		return "SETTLE"
	case 14: // 成功
		return "CONFIRM"
	case 3: // 失效
		return "REFUND"
	default:
		return "PAY_SUCC" // 默认为支付完成
	}
}

// mapPddStatusToFlowPoint 将拼多多订单状态映射到流程状态
func mapPddStatusToFlowPoint(orderStatus int) string {
	switch orderStatus {
	case 0: // 已支付
		return "PAY_SUCC"
	case 1: // 已成团
		return "CONFIRM"
	case 2: // 确认收货
		return "CONFIRM"
	case 3: // 审核成功
		return "SETTLE"
	case 4: // 审核失败
		return "REFUND"
	case 5: // 已经结算
		return "SETTLE"
	case -1: // 未支付
		return "REFUND"
	default:
		return "PAY_SUCC" // 默认为支付完成
	}
}

// mapVipStatusToFlowPoint 将唯品会订单状态映射到流程状态
func mapVipStatusToFlowPoint(status int16) string {
	switch status {
	case 0: // 不合格
		return "REFUND"
	case 1: // 待定
		return "PAY_SUCC"
	case 2: // 已完结
		return "SETTLE"
	default:
		return "PAY_SUCC" // 默认为支付完成
	}
}