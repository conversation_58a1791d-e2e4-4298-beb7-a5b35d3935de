package cron

import (
	"ec-cps-ctrl/model"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/go-resty/resty/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

// 同步配置
type JDSyncConfig struct {
	LookBackHours int           // 查询过去多少小时的订单
	PageSize      int           // 每页数量
	RetryCount    int           // 重试次数
	RetryInterval time.Duration // 重试间隔
	WorkerCount   int           // 工作协程数量
	BatchSize     int           // 批量插入大小
	SyncInterval  time.Duration // 同步间隔
}

// 默认配置
var jdDefaultConfig = JDSyncConfig{
	LookBackHours: 1,
	PageSize:      100,
	RetryCount:    3,
	RetryInterval: time.Second * 2,
	WorkerCount:   5,
	BatchSize:     50,
	SyncInterval:  time.Minute * 5,
}

// 同步京东订单
func SyncJDOrders(config JDSyncConfig) error {
	startTime := time.Now()
	log.Log().Info("开始同步京东订单", zap.Int("查询过去小时", config.LookBackHours))

	// 创建HTTP客户端
	client := resty.New().SetRetryCount(config.RetryCount).SetRetryWaitTime(config.RetryInterval)

	// 设置接口参数
	now := time.Now()
	queryStartTime := now.Add(-time.Duration(config.LookBackHours) * time.Hour)

	// 获取API密钥
	err, setting := model.GetCpsSetting()
	if err != nil {
		return fmt.Errorf("获取API密钥失败: %v", err)
	}

	// 创建订单通道和等待组
	orderChan := make(chan []model.JDOrder, 10)
	var wg sync.WaitGroup

	// 启动工作协程处理订单
	for i := 0; i < config.WorkerCount; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			processJDOrdersWorker(orderChan, config.BatchSize, workerID)
		}(i)
	}

	// 获取第一页数据
	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetQueryParams(map[string]string{
			"apikey":    setting.ApiKey,
			"pageIndex": "1",
			"pageSize":  fmt.Sprintf("%d", config.PageSize),
			"type":      "3", // 更新时间
			"startTime": queryStartTime.Format("2006-01-02 15:04:05"),
			"endTime":   now.Format("2006-01-02 15:04:05"),
		}).
		Post("http://api.tbk.dingdanxia.com/jd/order_details2")

	if err != nil {
		close(orderChan) // 关闭通道，让工作协程退出
		return fmt.Errorf("请求京东订单接口失败: %v", err)
	}

	var orderResp model.JDOrderResponse
	if err := json.Unmarshal(resp.Body(), &orderResp); err != nil {
		close(orderChan) // 关闭通道，让工作协程退出
		return fmt.Errorf("解析响应数据失败: %v", err)
	}

	if orderResp.Code != 200 {
		close(orderChan) // 关闭通道，让工作协程退出
		return fmt.Errorf("接口返回错误: %s", orderResp.Msg)
	}

	// 发送第一页数据到通道
	if len(orderResp.Data) > 0 {
		orderChan <- orderResp.Data
		log.Log().Info("获取数据成功", zap.Int("页码", 1), zap.Int("订单数量", len(orderResp.Data)))
	}

	// 获取总页数
	totalPages := 1
	pageIndex := 2

	// 如果有更多数据，继续获取
	for orderResp.HasMore {
		totalPages++
		resp, err = client.R().
			SetHeader("Content-Type", "application/json").
			SetQueryParams(map[string]string{
				"apikey":    setting.ApiKey,
				"pageIndex": fmt.Sprintf("%d", pageIndex),
				"pageSize":  fmt.Sprintf("%d", config.PageSize),
				"type":      "3", // 更新时间
				"startTime": queryStartTime.Format("2006-01-02 15:04:05"),
				"endTime":   now.Format("2006-01-02 15:04:05"),
			}).
			Post("http://api.tbk.dingdanxia.com/jd/order_details2")

		if err != nil {
			log.Log().Error("请求数据失败", zap.Int("页码", pageIndex), zap.Error(err))
			break
		}

		if err := json.Unmarshal(resp.Body(), &orderResp); err != nil {
			log.Log().Error("解析数据失败", zap.Int("页码", pageIndex), zap.Error(err))
			break
		}

		if orderResp.Code != 200 {
			log.Log().Error("接口返回错误", zap.Int("页码", pageIndex), zap.String("错误信息", orderResp.Msg))
			break
		}

		if len(orderResp.Data) > 0 {
			orderChan <- orderResp.Data
			log.Log().Info("获取数据成功", zap.Int("页码", pageIndex), zap.Int("订单数量", len(orderResp.Data)))
		}

		pageIndex++
	}

	// 关闭通道，表示没有更多数据
	close(orderChan)

	// 等待所有工作协程完成
	wg.Wait()

	elapsedTime := time.Since(startTime)
	log.Log().Info("京东订单同步完成", zap.Int("总页数", totalPages), zap.Duration("耗时", elapsedTime))
	return nil
}

// 工作协程处理订单
func processJDOrdersWorker(orderChan <-chan []model.JDOrder, batchSize int, workerID int) {
	log.Log().Info("工作协程启动", zap.Int("协程ID", workerID))

	for orders := range orderChan {
		// 分批处理订单
		processJDBatchOrders(orders, batchSize, workerID)
	}

	log.Log().Info("工作协程完成", zap.Int("协程ID", workerID))
}

// 批量处理订单
func processJDBatchOrders(orders []model.JDOrder, batchSize int, workerID int) {
	if len(orders) == 0 {
		return
	}

	log.Log().Info("开始处理订单", zap.Int("协程ID", workerID), zap.Int("订单数量", len(orders)))

	// 收集所有订单ID
	var orderIds []string
	for _, order := range orders {
		orderIds = append(orderIds, order.OrderId)
	}

	// 查询数据库中已存在的订单
	var existingOrders []model.JDOrder
	result := source.DB().Where("order_id IN ?", orderIds).Find(&existingOrders)
	if result.Error != nil && result.Error != gorm.ErrRecordNotFound {
		log.Log().Error("查询现有订单失败", zap.Int("协程ID", workerID), zap.Error(result.Error))
		return
	}

	// 将现有订单映射到map中便于快速查找
	existingOrderMap := make(map[string]model.JDOrder)
	for _, order := range existingOrders {
		existingOrderMap[order.OrderId] = order
	}

	// 准备新订单和需要更新的订单
	var newOrders []model.JDOrder
	var updateOrders []model.JDOrder

	for _, order := range orders {
		// 设置同步时间
		order.SyncTime = time.Now()

		if _, exists := existingOrderMap[order.OrderId]; !exists {
			// 新订单
			var jdRelation model.CpsJDRelation
			source.DB().Where("id = ?", order.PositionId).First(&jdRelation)
			order.AppID = jdRelation.AppID
			order.ParentAppID = jdRelation.ParentAppId
			order.AppUserID = jdRelation.AppUserID
			newOrders = append(newOrders, order)
		} else {
			// 需要更新的订单
			dbOrder := existingOrderMap[order.OrderId]
			dbOrder.ValidCode = order.ValidCode
			dbOrder.SkuReturnNum = order.SkuReturnNum
			dbOrder.SkuFrozenNum = order.SkuFrozenNum
			dbOrder.FinishTime = order.FinishTime
			dbOrder.ModifyTime = order.ModifyTime
			dbOrder.EstimateCosPrice = order.EstimateCosPrice
			dbOrder.EstimateFee = order.EstimateFee
			dbOrder.ActualCosPrice = order.ActualCosPrice
			dbOrder.ActualFee = order.ActualFee
			dbOrder.PayMonth = order.PayMonth
			dbOrder.ExpressStatus = order.ExpressStatus
			dbOrder.SyncTime = time.Now()
			updateOrders = append(updateOrders, dbOrder)
		}
	}

	// 批量创建新订单
	if len(newOrders) > 0 {
		// 分批插入以避免一次插入过多数据
		for i := 0; i < len(newOrders); i += batchSize {
			end := i + batchSize
			if end > len(newOrders) {
				end = len(newOrders)
			}

			batch := newOrders[i:end]
			if err := source.DB().Create(&batch).Error; err != nil {
				log.Log().Error("批量创建订单失败", zap.Int("协程ID", workerID), zap.Error(err))
			} else {
				log.Log().Info("成功创建新订单", zap.Int("协程ID", workerID), zap.Int("数量", len(batch)))
			}
		}
	}

	// 批量更新订单
	if len(updateOrders) > 0 {
		// 分批更新
		for i := 0; i < len(updateOrders); i += batchSize {
			end := i + batchSize
			if end > len(updateOrders) {
				end = len(updateOrders)
			}

			batch := updateOrders[i:end]
			for _, order := range batch {
				// 使用条件更新来避免并发问题
				if err := source.DB().Model(&model.JDOrder{}).Clauses(clause.Returning{}).Where("order_id = ?", order.OrderId).Updates(map[string]interface{}{
					"valid_code":         order.ValidCode,
					"sku_return_num":     order.SkuReturnNum,
					"sku_frozen_num":     order.SkuFrozenNum,
					"finish_time":        order.FinishTime,
					"modify_time":        order.ModifyTime,
					"estimate_cos_price": order.EstimateCosPrice,
					"estimate_fee":       order.EstimateFee,
					"actual_cos_price":   order.ActualCosPrice,
					"actual_fee":         order.ActualFee,
					"pay_month":          order.PayMonth,
					"express_status":     order.ExpressStatus,
					"sync_time":          time.Now(),
				}).Error; err != nil {
					log.Log().Error("更新订单失败", zap.Int("协程ID", workerID), zap.String("订单ID", order.OrderId), zap.Error(err))
				}
			}
			log.Log().Info("成功更新订单", zap.Int("协程ID", workerID), zap.Int("数量", len(batch)))
		}
	}

	// 处理EcCpsOrderModel映射
	processEcCpsOrders(orders, batchSize, workerID)

	log.Log().Info("完成订单处理", zap.Int("协程ID", workerID), zap.Int("新增数量", len(newOrders)), zap.Int("更新数量", len(updateOrders)))
}

// 创建京东订单同步定时任务
func CreateJDOrderSyncTask(taskID int, cronSpec string) {
	// 使用默认配置
	config := jdDefaultConfig

	// 如果没有指定cron表达式，使用默认的每5分钟执行一次
	if cronSpec == "" {
		cronSpec = "0 */5 * * * *"
	}

	// 注册定时任务
	cron.PushTask(cron.Task{
		Key:  "jdOrderSync" + fmt.Sprintf("%d", taskID),
		Name: "京东订单同步定时任务" + fmt.Sprintf("%d", taskID),
		Spec: cronSpec,
		Handle: func(task cron.Task) {
			if err := SyncJDOrders(config); err != nil {
				log.Log().Error("同步京东订单失败", zap.Int("taskID", taskID), zap.Error(err))
			}
		},
		Status: cron.ENABLED,
	})

	log.Log().Info("京东订单同步定时任务已启动", zap.Int("taskID", taskID), zap.String("执行规则", cronSpec))
}

// 定时任务入口
func InitJDOrderSync() {
	// 使用默认配置
	config := jdDefaultConfig

	// 立即执行一次同步
	go func() {
		log.Log().Info("系统启动，立即执行京东订单同步")
		if err := SyncJDOrders(config); err != nil {
			log.Log().Error("同步京东订单失败", zap.Error(err))
		}
	}()

	// 创建默认的定时任务（ID为0）
	CreateJDOrderSyncTask(0, "0 */5 * * * *")
}

// processEcCpsOrders 处理EcCpsOrderModel的新增和更新
func processEcCpsOrders(jdOrders []model.JDOrder, batchSize int, workerID int) {
	if len(jdOrders) == 0 {
		return
	}

	log.Log().Info("开始处理EcCpsOrderModel映射", zap.Int("协程ID", workerID), zap.Int("订单数量", len(jdOrders)))

	// 收集所有订单ID
	var orderIds []string
	for _, order := range jdOrders {
		orderIds = append(orderIds, order.OrderId)
	}

	// 查询数据库中已存在的EcCpsOrderModel
	var existingEcOrders []model.EcCpsOrderModel
	result := source.DB().Where("order_id IN ? AND type = ?", orderIds, "jd").Find(&existingEcOrders)
	if result.Error != nil && result.Error != gorm.ErrRecordNotFound {
		log.Log().Error("查询现有EcCpsOrderModel失败", zap.Int("协程ID", workerID), zap.Error(result.Error))
		return
	}

	// 将现有订单映射到map中便于快速查找
	existingEcOrderMap := make(map[string]model.EcCpsOrderModel)
	for _, order := range existingEcOrders {
		existingEcOrderMap[order.OrderId] = order
	}

	// 准备新订单和需要更新的订单
	var newEcOrders []model.EcCpsOrderModel
	var updateEcOrders []model.EcCpsOrderModel

	for _, jdOrder := range jdOrders {
		// 映射JDOrder到EcCpsOrderModel
		ecOrder := mapJDOrderToEcCpsOrder(jdOrder)

		if _, exists := existingEcOrderMap[ecOrder.OrderId]; !exists {
			// 新订单
			newEcOrders = append(newEcOrders, ecOrder)
		} else {
			// 需要更新的订单
			dbEcOrder := existingEcOrderMap[ecOrder.OrderId]
			// 更新关键字段
			dbEcOrder.TotalPayAmount = ecOrder.TotalPayAmount
			dbEcOrder.PayGoodsAmount = ecOrder.PayGoodsAmount
			dbEcOrder.EstimatedCommission = ecOrder.EstimatedCommission
			dbEcOrder.SplitRate = ecOrder.SplitRate
			dbEcOrder.FlowPoint = ecOrder.FlowPoint
			dbEcOrder.AfterSalesStatus = ecOrder.AfterSalesStatus
			dbEcOrder.ConfirmTime = ecOrder.ConfirmTime
			dbEcOrder.SettleTime = ecOrder.SettleTime
			dbEcOrder.RefundTime = ecOrder.RefundTime
			dbEcOrder.EstimatedTechServiceFee = ecOrder.EstimatedTechServiceFee
			dbEcOrder.ExternalInfo = ecOrder.ExternalInfo
			updateEcOrders = append(updateEcOrders, dbEcOrder)
		}
	}

	// 批量创建新的EcCpsOrderModel
	if len(newEcOrders) > 0 {
		for i := 0; i < len(newEcOrders); i += batchSize {
			end := i + batchSize
			if end > len(newEcOrders) {
				end = len(newEcOrders)
			}

			batch := newEcOrders[i:end]
			if err := source.DB().Create(&batch).Error; err != nil {
				log.Log().Error("批量创建EcCpsOrderModel失败", zap.Int("协程ID", workerID), zap.Error(err))
			} else {
				log.Log().Info("成功创建新EcCpsOrderModel", zap.Int("协程ID", workerID), zap.Int("数量", len(batch)))
			}
		}
	}

	// 批量更新EcCpsOrderModel
	if len(updateEcOrders) > 0 {
		for i := 0; i < len(updateEcOrders); i += batchSize {
			end := i + batchSize
			if end > len(updateEcOrders) {
				end = len(updateEcOrders)
			}

			batch := updateEcOrders[i:end]
			for _, order := range batch {
				if err := source.DB().Model(&model.EcCpsOrderModel{}).Where("order_id = ? AND type = ?", order.OrderId, "jd").Updates(map[string]interface{}{
					"total_pay_amount":           order.TotalPayAmount,
					"pay_goods_amount":           order.PayGoodsAmount,
					"estimated_commission":       order.EstimatedCommission,
					"split_rate":                 order.SplitRate,
					"flow_point":                 order.FlowPoint,
					"after_sales_status":         order.AfterSalesStatus,
					"confirm_time":               order.ConfirmTime,
					"settle_time":                order.SettleTime,
					"refund_time":                order.RefundTime,
					"estimated_tech_service_fee": order.EstimatedTechServiceFee,
					"external_info":              order.ExternalInfo,
				}).Error; err != nil {
					log.Log().Error("更新EcCpsOrderModel失败", zap.Int("协程ID", workerID), zap.String("订单ID", order.OrderId), zap.Error(err))
				}
			}
			log.Log().Info("成功更新EcCpsOrderModel", zap.Int("协程ID", workerID), zap.Int("数量", len(batch)))
		}
	}

	log.Log().Info("完成EcCpsOrderModel处理", zap.Int("协程ID", workerID), zap.Int("新增数量", len(newEcOrders)), zap.Int("更新数量", len(updateEcOrders)))
}

// mapJDOrderToEcCpsOrder 将JDOrder映射到EcCpsOrderModel
func mapJDOrderToEcCpsOrder(jdOrder model.JDOrder) model.EcCpsOrderModel {
	ecOrder := model.EcCpsOrderModel{
		OrderId:       jdOrder.OrderId,
		AppId:         strconv.Itoa(jdOrder.AppID),
		UserID:        uint(jdOrder.AppUserID),
		ApplicationID: uint(jdOrder.AppID),
		ThirdUserID:   uint(jdOrder.AppUserID),
		ProductId:     jdOrder.SkuId,
		ProductName:   jdOrder.SkuName,
		Type:          "jd",
	}

	// 设置支付金额（转换为分）
	if jdOrder.Price > 0 && jdOrder.SkuNum > 0 {
		ecOrder.TotalPayAmount = int(jdOrder.Price * float64(jdOrder.SkuNum) * 100)
		ecOrder.PayGoodsAmount = int(jdOrder.ActualCosPrice * 100)
	}

	// 设置佣金（转换为分）
	ecOrder.EstimatedCommission = int(jdOrder.EstimateFee * 100)

	// 设置佣金比例（转换为万分比）
	if jdOrder.CommissionRate > 0 {
		ecOrder.SplitRate = int(jdOrder.CommissionRate * 10000)
	}

	// 设置时间字段
	ecOrder.PaySuccessTime = jdOrder.OrderTime
	ecOrder.ConfirmTime = jdOrder.FinishTime
	ecOrder.SettleTime = jdOrder.PayMonth

	// 设置订单状态
	ecOrder.FlowPoint = mapJDValidCodeToFlowPoint(jdOrder.ValidCode)

	// 设置售后状态
	if jdOrder.SkuReturnNum > 0 || jdOrder.SkuFrozenNum > 0 {
		ecOrder.AfterSalesStatus = 2 // 产生退款
		ecOrder.RefundTime = jdOrder.ModifyTime // 使用修改时间作为退款时间
	} else {
		ecOrder.AfterSalesStatus = 1 // 空
	}

	// 设置外部信息
	ecOrder.ExternalInfo = fmt.Sprintf("position_id:%s,union_id:%s,sku_num:%d", jdOrder.PositionId, jdOrder.UnionId, jdOrder.SkuNum)

	// 设置显示状态
	ecOrder.IsDisplay = 1

	// 设置分成状态
	ecOrder.Status = 0 // 默认未分成

	// 设置技术服务费（预估）
	if jdOrder.EstimateFee > 0 {
		ecOrder.EstimatedTechServiceFee = int(jdOrder.EstimateFee * 0.1 * 100) // 假设技术服务费为佣金的10%
	}

	return ecOrder
}

// mapJDValidCodeToFlowPoint 将京东订单状态映射到流程状态
func mapJDValidCodeToFlowPoint(validCode int) string {
	switch validCode {
	case 15: // 已付款
		return "PAY_SUCC"
	case 16: // 已完成
		return "CONFIRM"
	case 17: // 已结算
		return "SETTLE"
	case -1: // 已退款
		return "REFUND"
	default:
		return "PAY_SUCC" // 默认为支付完成
	}
}
