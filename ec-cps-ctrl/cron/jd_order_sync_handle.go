package cron

import (
	"ec-cps-ctrl/model"
	"fmt"
	"sync"
	"time"

	"github.com/go-resty/resty/v2"
	"go.uber.org/zap"
	"yz-go/component/log"
)

// 同步配置

// 同步京东订单 - 支持48小时分时段查询
func HandleSyncJDOrders(config JDSyncConfig) error {
	startTime := time.Now()
	log.Log().Info("开始同步京东订单",
		zap.Int("查询过去小时", config.LookBackHours),
		zap.String("策略", "分时段查询(每次1小时)"))

	// 获取API密钥
	err, setting := model.GetCpsSetting()
	if err != nil {
		return fmt.Errorf("获取API密钥失败: %v", err)
	}

	// 创建订单通道和等待组
	orderChan := make(chan []model.JDOrder, 20) // 增加缓冲区，支持更多时段
	var wg sync.WaitGroup

	// 启动工作协程处理订单
	for i := 0; i < config.WorkerCount; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			processJDOrdersWorker(orderChan, config.BatchSize, workerID)
		}(i)
	}

	// 分时段获取订单数据
	go func() {
		defer close(orderChan)
		totalOrders, totalPages, err := fetchHandleJDOrdersInHourlySegments(setting, config, orderChan)
		if err != nil {
			log.Log().Error("获取京东订单失败", zap.Error(err))
		} else {
			log.Log().Info("订单获取完成",
				zap.Int("总订单数", totalOrders),
				zap.Int("总页数", totalPages))
		}
	}()

	// 等待所有工作协程完成
	wg.Wait()

	elapsedTime := time.Since(startTime)
	log.Log().Info("京东订单同步完成", zap.Duration("耗时", elapsedTime))
	return nil
}

// fetchJDOrdersInHourlySegments 分时段获取京东订单（每次查询1小时）
func fetchHandleJDOrdersInHourlySegments(setting model.CpsValue, config JDSyncConfig, orderChan chan<- []model.JDOrder) (int, int, error) {
	// 创建HTTP客户端
	client := resty.New().SetRetryCount(config.RetryCount).SetRetryWaitTime(config.RetryInterval)

	// 计算总的查询时间范围
	endTime := time.Now()
	totalStartTime := endTime.Add(-time.Duration(config.LookBackHours) * time.Hour)

	log.Log().Info("开始分时段查询",
		zap.String("总开始时间", totalStartTime.Format("2006-01-02 15:04:05")),
		zap.String("总结束时间", endTime.Format("2006-01-02 15:04:05")),
		zap.Int("总时段数", config.LookBackHours))

	totalPages := 0
	totalOrders := 0

	// 按小时分段查询（从最新时间往前推）
	for i := 0; i < config.LookBackHours; i++ {
		// 计算当前时段的时间范围（每段1小时）
		segmentEndTime := endTime.Add(-time.Duration(i) * time.Hour)
		segmentStartTime := segmentEndTime.Add(-1 * time.Hour)

		log.Log().Info("查询时段",
			zap.Int("时段", i+1),
			zap.String("开始时间", segmentStartTime.Format("2006-01-02 15:04:05")),
			zap.String("结束时间", segmentEndTime.Format("2006-01-02 15:04:05")))

		// 查询当前时段的订单
		segmentPages, segmentOrders, err := fetchJDOrdersForTimeSegment(
			client, setting, config, segmentStartTime, segmentEndTime, orderChan)

		if err != nil {
			log.Log().Error("查询时段失败",
				zap.Int("时段", i+1),
				zap.Error(err))
			// 继续查询下一个时段，不中断整个过程
			continue
		}

		totalPages += segmentPages
		totalOrders += segmentOrders

		log.Log().Info("时段查询完成",
			zap.Int("时段", i+1),
			zap.Int("页数", segmentPages),
			zap.Int("订单数", segmentOrders))

		// 添加短暂延迟，避免API调用过于频繁
		time.Sleep(200 * time.Millisecond)
	}

	log.Log().Info("所有时段查询完成",
		zap.Int("总页数", totalPages),
		zap.Int("总订单数", totalOrders))

	return totalOrders, totalPages, nil
}
