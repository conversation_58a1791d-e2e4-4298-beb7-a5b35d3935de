package cron

import (
	"ec-cps-ctrl/model"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"
	utils "yz-go/utils"

	"github.com/go-resty/resty/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

// 同步配置
type VipSyncConfig struct {
	LookBackHours int           // 查询过去多少小时的订单
	PageSize      int           // 每页数量
	RetryCount    int           // 重试次数
	RetryInterval time.Duration // 重试间隔
	WorkerCount   int           // 工作协程数量
	BatchSize     int           // 批量插入大小
	SyncInterval  time.Duration // 同步间隔
}

// 默认配置
var vipDefaultConfig = VipSyncConfig{
	LookBackHours: 1,
	PageSize:      20,
	RetryCount:    3,
	RetryInterval: time.Second * 2,
	WorkerCount:   5,
	BatchSize:     50,
	SyncInterval:  time.Minute * 5,
}

// 同步唯品会订单
func SyncVipOrders(config VipSyncConfig) error {
	startTime := time.Now()
	log.Log().Info("开始同步唯品会订单", zap.Int("查询过去小时", config.LookBackHours))

	// 创建HTTP客户端
	client := resty.New().SetRetryCount(config.RetryCount).SetRetryWaitTime(config.RetryInterval)

	// 设置接口参数
	now := time.Now()
	queryStartTime := now.Add(-time.Duration(config.LookBackHours) * time.Hour)

	// 获取API密钥
	err, setting := model.GetCpsSetting()
	if err != nil {
		return fmt.Errorf("获取API密钥失败: %v", err)
	}

	// 创建订单通道和等待组
	orderChan := make(chan []model.VipOrderData, 10)
	var wg sync.WaitGroup

	// 启动工作协程处理订单
	for i := 0; i < config.WorkerCount; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			processVipOrdersWorker(orderChan, config.BatchSize, workerID)
		}(i)
	}

	// 获取第一页数据
	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetQueryParams(map[string]string{
			"apikey":          setting.ApiKey,
			"page":            "1",
			"pageSize":        fmt.Sprintf("%d", config.PageSize),
			"updateTimeStart": fmt.Sprintf("%d", queryStartTime.UnixMilli()),
			"updateTimeEnd":   fmt.Sprintf("%d", now.UnixMilli()),
		}).
		Post("http://api.tbk.dingdanxia.com/vip/order_details2")

	if err != nil {
		close(orderChan) // 关闭通道，让工作协程退出
		return fmt.Errorf("请求唯品会订单接口失败: %v", err)
	}

	var orderResp model.VipOrderResponse
	if err := json.Unmarshal(resp.Body(), &orderResp); err != nil {
		close(orderChan) // 关闭通道，让工作协程退出
		return fmt.Errorf("解析响应数据失败: %v", err)
	}

	if orderResp.Code != 200 {
		close(orderChan) // 关闭通道，让工作协程退出
		return fmt.Errorf("接口返回错误: %s", orderResp.Msg)
	}

	// 发送第一页数据到通道
	if len(orderResp.Data) > 0 {
		orderChan <- orderResp.Data
		log.Log().Info("获取数据成功", zap.Int("页码", 1), zap.Int("订单数量", len(orderResp.Data)))
	}

	// 获取总页数
	totalPages := 1
	totalResults := orderResp.TotalResults
	pageCount := (totalResults + config.PageSize - 1) / config.PageSize

	// 如果有更多页，继续获取
	for page := 2; page <= pageCount; page++ {
		totalPages++
		resp, err = client.R().
			SetHeader("Content-Type", "application/json").
			SetQueryParams(map[string]string{
				"apikey":          setting.ApiKey,
				"page":            fmt.Sprintf("%d", page),
				"pageSize":        fmt.Sprintf("%d", config.PageSize),
				"updateTimeStart": fmt.Sprintf("%d", queryStartTime.UnixMilli()),
				"updateTimeEnd":   fmt.Sprintf("%d", now.UnixMilli()),
			}).
			Post("http://api.tbk.dingdanxia.com/vip/order_details2")

		if err != nil {
			log.Log().Error("请求数据失败", zap.Int("页码", page), zap.Error(err))
			break
		}

		if err := json.Unmarshal(resp.Body(), &orderResp); err != nil {
			log.Log().Error("解析数据失败", zap.Int("页码", page), zap.Error(err))
			break
		}

		if orderResp.Code != 200 {
			log.Log().Error("接口返回错误", zap.Int("页码", page), zap.String("错误信息", orderResp.Msg))
			break
		}

		if len(orderResp.Data) > 0 {
			orderChan <- orderResp.Data
			log.Log().Info("获取数据成功", zap.Int("页码", page), zap.Int("订单数量", len(orderResp.Data)))
		}
	}

	// 关闭通道，表示没有更多数据
	close(orderChan)

	// 等待所有工作协程完成
	wg.Wait()

	elapsedTime := time.Since(startTime)
	log.Log().Info("唯品会订单同步完成", zap.Int("总页数", totalPages), zap.Duration("耗时", elapsedTime))
	return nil
}

// 工作协程处理订单
func processVipOrdersWorker(orderChan <-chan []model.VipOrderData, batchSize int, workerID int) {
	log.Log().Info("工作协程启动", zap.Int("协程ID", workerID))

	for orders := range orderChan {
		// 分批处理订单
		processVipBatchOrders(orders, batchSize, workerID)
	}

	log.Log().Info("工作协程完成", zap.Int("协程ID", workerID))
}

// 批量处理订单
func processVipBatchOrders(orders []model.VipOrderData, batchSize int, workerID int) {
	if len(orders) == 0 {
		return
	}

	log.Log().Info("开始处理订单", zap.Int("协程ID", workerID), zap.Int("订单数量", len(orders)))

	// 收集所有订单ID
	var orderSns []string
	for _, order := range orders {
		orderSns = append(orderSns, order.OrderSn)
	}

	// 查询数据库中已存在的订单
	var existingOrders []model.VipOrder
	result := source.DB().Where("order_sn IN ?", orderSns).Find(&existingOrders)
	if result.Error != nil && result.Error != gorm.ErrRecordNotFound {
		log.Log().Error("查询现有订单失败", zap.Int("协程ID", workerID), zap.Error(result.Error))
		return
	}

	// 将现有订单映射到map中便于快速查找
	existingOrderMap := make(map[string]model.VipOrder)
	for _, order := range existingOrders {
		existingOrderMap[order.OrderSn] = order
	}

	// 准备新订单和需要更新的订单
	var newOrders []model.VipOrder
	var updateOrders []model.VipOrder
	var newOrderDetails []model.VipOrderDetail
	var updateOrderDetails []model.VipOrderDetail

	// 查询现有订单详情
	var existingOrderDetails []model.VipOrderDetail
	result = source.DB().Where("order_sn IN ?", orderSns).Find(&existingOrderDetails)
	if result.Error != nil && result.Error != gorm.ErrRecordNotFound {
		log.Log().Error("查询现有订单详情失败", zap.Int("协程ID", workerID), zap.Error(result.Error))
		return
	}

	// 将现有订单详情映射到map中便于快速查找
	existingOrderDetailMap := make(map[string]map[string]model.VipOrderDetail)
	for _, detail := range existingOrderDetails {
		if _, exists := existingOrderDetailMap[detail.OrderSn]; !exists {
			existingOrderDetailMap[detail.OrderSn] = make(map[string]model.VipOrderDetail)
		}
		existingOrderDetailMap[detail.OrderSn][detail.GoodsId] = detail
	}

	for _, orderData := range orders {
		// 转换为模型订单
		order := convertToVipOrder(orderData)

		// 设置同步时间
		order.SyncTime = time.Now()

		if _, exists := existingOrderMap[order.OrderSn]; !exists {
			// 新订单
			newOrders = append(newOrders, order)

			// 处理订单详情
			for _, detailData := range orderData.DetailList {
				detail := convertToVipOrderDetail(detailData, order.OrderSn)
				newOrderDetails = append(newOrderDetails, detail)
			}
		} else {
			// 需要更新的订单
			dbOrder := existingOrderMap[order.OrderSn]
			dbOrder.Status = order.Status
			dbOrder.NewCustomer = order.NewCustomer
			dbOrder.SignTime = order.SignTime
			dbOrder.SettledTime = order.SettledTime
			dbOrder.LastUpdateTime = order.LastUpdateTime
			dbOrder.Settled = order.Settled
			dbOrder.OrderSubStatusName = order.OrderSubStatusName
			dbOrder.Commission = order.Commission
			dbOrder.AfterSaleChangeCommission = order.AfterSaleChangeCommission
			dbOrder.AfterSaleChangeGoodsCount = order.AfterSaleChangeGoodsCount
			dbOrder.CommissionEnterTime = order.CommissionEnterTime
			// 更新解析出的ID字段
			//dbOrder.AppID = order.AppID
			//dbOrder.ParentAppID = order.ParentAppID
			//dbOrder.AppUserID = order.AppUserID
			dbOrder.SyncTime = time.Now()
			updateOrders = append(updateOrders, dbOrder)

			// 处理订单详情
			for _, detailData := range orderData.DetailList {
				detail := convertToVipOrderDetail(detailData, order.OrderSn)

				// 检查是否存在该订单详情
				if existingDetails, exists := existingOrderDetailMap[order.OrderSn]; exists {
					if existingDetail, exists := existingDetails[detail.GoodsId]; exists {
						// 更新现有详情
						existingDetail.GoodsCount = detail.GoodsCount
						existingDetail.CommissionTotalCost = detail.CommissionTotalCost
						existingDetail.CommissionRate = detail.CommissionRate
						existingDetail.Commission = detail.Commission
						existingDetail.AfterSaleChangedCommission = detail.AfterSaleChangedCommission
						existingDetail.AfterSaleChangedGoodsCount = detail.AfterSaleChangedGoodsCount
						existingDetail.AfterSaleSn = detail.AfterSaleSn
						existingDetail.AfterSaleStatus = detail.AfterSaleStatus
						existingDetail.AfterSaleType = detail.AfterSaleType
						existingDetail.AfterSaleFinishTime = detail.AfterSaleFinishTime
						existingDetail.Status = detail.Status
						existingDetail.GoodsFinalPrice = detail.GoodsFinalPrice
						updateOrderDetails = append(updateOrderDetails, existingDetail)
					} else {
						// 新增详情
						newOrderDetails = append(newOrderDetails, detail)
					}
				} else {
					// 新增详情
					newOrderDetails = append(newOrderDetails, detail)
				}
			}
		}
	}

	// 批量创建新订单
	if len(newOrders) > 0 {
		// 分批插入以避免一次插入过多数据
		for i := 0; i < len(newOrders); i += batchSize {
			end := i + batchSize
			if end > len(newOrders) {
				end = len(newOrders)
			}

			batch := newOrders[i:end]
			if err := source.DB().Create(&batch).Error; err != nil {
				log.Log().Error("批量创建订单失败", zap.Int("协程ID", workerID), zap.Error(err))
			} else {
				log.Log().Info("成功创建新订单", zap.Int("协程ID", workerID), zap.Int("数量", len(batch)))
			}
		}
	}

	// 批量创建新订单详情
	if len(newOrderDetails) > 0 {
		// 分批插入以避免一次插入过多数据
		for i := 0; i < len(newOrderDetails); i += batchSize {
			end := i + batchSize
			if end > len(newOrderDetails) {
				end = len(newOrderDetails)
			}

			batch := newOrderDetails[i:end]
			if err := source.DB().Create(&batch).Error; err != nil {
				log.Log().Error("批量创建订单详情失败", zap.Int("协程ID", workerID), zap.Error(err))
			} else {
				log.Log().Info("成功创建新订单详情", zap.Int("协程ID", workerID), zap.Int("数量", len(batch)))
			}
		}
	}

	// 批量更新订单
	if len(updateOrders) > 0 {
		// 分批更新
		for i := 0; i < len(updateOrders); i += batchSize {
			end := i + batchSize
			if end > len(updateOrders) {
				end = len(updateOrders)
			}

			batch := updateOrders[i:end]
			for _, order := range batch {
				// 使用条件更新来避免并发问题
				if err := source.DB().Model(&model.VipOrder{}).Clauses(clause.Returning{}).Where("order_sn = ?", order.OrderSn).Updates(map[string]interface{}{
					"status":                        order.Status,
					"new_customer":                  order.NewCustomer,
					"sign_time":                     order.SignTime,
					"settled_time":                  order.SettledTime,
					"last_update_time":              order.LastUpdateTime,
					"settled":                       order.Settled,
					"order_sub_status_name":         order.OrderSubStatusName,
					"commission":                    order.Commission,
					"after_sale_change_commission":  order.AfterSaleChangeCommission,
					"after_sale_change_goods_count": order.AfterSaleChangeGoodsCount,
					"commission_enter_time":         order.CommissionEnterTime,
					//"app_id":                        order.AppID,
					//"parent_app_id":                 order.ParentAppID,
					//"app_user_id":                   order.AppUserID,
					"sync_time": time.Now(),
				}).Error; err != nil {
					log.Log().Error("更新订单失败", zap.Int("协程ID", workerID), zap.String("订单ID", order.OrderSn), zap.Error(err))
				}
			}
			log.Log().Info("成功更新订单", zap.Int("协程ID", workerID), zap.Int("数量", len(batch)))
		}
	}

	// 批量更新订单详情
	if len(updateOrderDetails) > 0 {
		// 分批更新
		for i := 0; i < len(updateOrderDetails); i += batchSize {
			end := i + batchSize
			if end > len(updateOrderDetails) {
				end = len(updateOrderDetails)
			}

			batch := updateOrderDetails[i:end]
			for _, detail := range batch {
				// 使用条件更新来避免并发问题
				if err := source.DB().Model(&model.VipOrderDetail{}).Clauses(clause.Returning{}).Where("order_sn = ? AND goods_id = ?", detail.OrderSn, detail.GoodsId).Updates(map[string]interface{}{
					"goods_count":                    detail.GoodsCount,
					"commission_total_cost":          detail.CommissionTotalCost,
					"commission_rate":                detail.CommissionRate,
					"commission":                     detail.Commission,
					"after_sale_changed_commission":  detail.AfterSaleChangedCommission,
					"after_sale_changed_goods_count": detail.AfterSaleChangedGoodsCount,
					"after_sale_sn":                  detail.AfterSaleSn,
					"after_sale_status":              detail.AfterSaleStatus,
					"after_sale_type":                detail.AfterSaleType,
					"after_sale_finish_time":         detail.AfterSaleFinishTime,
					"status":                         detail.Status,
					"goods_final_price":              detail.GoodsFinalPrice,
				}).Error; err != nil {
					log.Log().Error("更新订单详情失败", zap.Int("协程ID", workerID), zap.String("订单ID", detail.OrderSn), zap.String("商品ID", detail.GoodsId), zap.Error(err))
				}
			}
			log.Log().Info("成功更新订单详情", zap.Int("协程ID", workerID), zap.Int("数量", len(batch)))
		}
	}

	// 处理EcCpsOrderModel映射
	processVipEcCpsOrders(orders, batchSize, workerID)

	log.Log().Info("完成订单处理", zap.Int("协程ID", workerID),
		zap.Int("新增订单数量", len(newOrders)),
		zap.Int("更新订单数量", len(updateOrders)),
		zap.Int("新增订单详情数量", len(newOrderDetails)),
		zap.Int("更新订单详情数量", len(updateOrderDetails)))
}

// 将API订单数据转换为模型订单
func convertToVipOrder(orderData model.VipOrderData) model.VipOrder {
	// 解析StatParam获取AppID、ParentAppID和AppUserID
	var parentAppID, appID, appUserID uint

	// StatParam格式可能是"parentAppID_appID_appUserID"或其他格式
	// 这里假设格式为"parentAppID_appID_appUserID"
	if orderData.StatParam != "" {
		parts := strings.Split(orderData.StatParam, "_")
		if len(parts) >= 3 {
			parentAppID = utils.StrToUInt(parts[0])
			appID = utils.StrToUInt(parts[1])
			appUserID = utils.StrToUInt(parts[2])
		} else if len(parts) == 2 {
			// 如果只有两部分，假设是"appID_appUserID"
			appID = utils.StrToUInt(parts[0])
			appUserID = utils.StrToUInt(parts[1])
		} else if len(parts) == 1 {
			// 如果只有一部分，尝试解析为appID
			appID = utils.StrToUInt(parts[0])
		}
	}

	return model.VipOrder{
		OrderSn:                   orderData.OrderSn,
		Status:                    orderData.Status,
		NewCustomer:               orderData.NewCustomer,
		ChannelTag:                orderData.ChannelTag,
		OrderTime:                 orderData.OrderTime,
		SignTime:                  orderData.SignTime,
		SettledTime:               orderData.SettledTime,
		LastUpdateTime:            orderData.LastUpdateTime,
		Settled:                   orderData.Settled,
		SelfBuy:                   orderData.SelfBuy,
		OrderSubStatusName:        orderData.OrderSubStatusName,
		Commission:                orderData.Commission,
		AfterSaleChangeCommission: orderData.AfterSaleChangeCommission,
		AfterSaleChangeGoodsCount: orderData.AfterSaleChangeGoodsCount,
		CommissionEnterTime:       orderData.CommissionEnterTime,
		OrderSource:               orderData.OrderSource,
		Pid:                       orderData.Pid,
		IsPrepay:                  orderData.IsPrepay,
		StatParam:                 orderData.StatParam,
		TotalCost:                 orderData.TotalCost,
		OrderTrackReason:          orderData.OrderTrackReason,
		IsSplit:                   orderData.IsSplit,
		// 设置解析出的ID
		ParentAppID: parentAppID,
		AppID:       appID,
		AppUserID:   appUserID,
		SyncTime:    time.Now(),
	}
}

// 将API订单详情数据转换为模型订单详情
func convertToVipOrderDetail(detailData model.VipOrderDetailData, orderSn string) model.VipOrderDetail {
	return model.VipOrderDetail{
		OrderSn:                    orderSn,
		GoodsId:                    detailData.GoodsId,
		GoodsName:                  detailData.GoodsName,
		GoodsThumb:                 detailData.GoodsThumb,
		GoodsCount:                 detailData.GoodsCount,
		CommissionTotalCost:        detailData.CommissionTotalCost,
		CommissionRate:             detailData.CommissionRate,
		Commission:                 detailData.Commission,
		CommCode:                   detailData.CommCode,
		CommName:                   detailData.CommName,
		OrderSource:                detailData.OrderSource,
		AfterSaleChangedCommission: detailData.AfterSaleChangedCommission,
		AfterSaleChangedGoodsCount: detailData.AfterSaleChangedGoodsCount,
		AfterSaleSn:                detailData.AfterSaleSn,
		AfterSaleStatus:            detailData.AfterSaleStatus,
		AfterSaleType:              detailData.AfterSaleType,
		AfterSaleFinishTime:        detailData.AfterSaleFinishTime,
		SizeId:                     detailData.SizeId,
		Status:                     detailData.Status,
		GoodsFinalPrice:            detailData.GoodsFinalPrice,
		BrandStoreSn:               detailData.BrandStoreSn,
		BrandStoreName:             detailData.BrandStoreName,
		SpuId:                      detailData.SpuId,
	}
}

// 创建唯品会订单同步定时任务
func CreateVipOrderSyncTask(taskID int, cronSpec string) {
	// 使用默认配置
	config := vipDefaultConfig

	// 如果没有指定cron表达式，使用默认的每5分钟执行一次
	if cronSpec == "" {
		cronSpec = "0 */5 * * * *"
	}

	// 注册定时任务
	cron.PushTask(cron.Task{
		Key:  "vipOrderSync" + fmt.Sprintf("%d", taskID),
		Name: "唯品会订单同步定时任务" + fmt.Sprintf("%d", taskID),
		Spec: cronSpec,
		Handle: func(task cron.Task) {
			if err := SyncVipOrders(config); err != nil {
				log.Log().Error("同步唯品会订单失败", zap.Int("taskID", taskID), zap.Error(err))
			}
		},
		Status: cron.ENABLED,
	})

	log.Log().Info("唯品会订单同步定时任务已启动", zap.Int("taskID", taskID), zap.String("执行规则", cronSpec))
}

// 定时任务入口
func InitVipOrderSync() {
	// 使用默认配置
	config := vipDefaultConfig

	// 立即执行一次同步
	go func() {
		log.Log().Info("系统启动，立即执行唯品会订单同步")
		if err := SyncVipOrders(config); err != nil {
			log.Log().Error("同步唯品会订单失败", zap.Error(err))
		}
	}()

	// 创建默认的定时任务（ID为0）
	CreateVipOrderSyncTask(0, "0 */5 * * * *")
}

// processVipEcCpsOrders 处理唯品会订单的EcCpsOrderModel映射
func processVipEcCpsOrders(vipOrdersData []model.VipOrderData, batchSize int, workerID int) {
	// 转换为model.VipOrder
	var modelOrders []model.VipOrder
	for _, orderData := range vipOrdersData {
		modelOrders = append(modelOrders, convertToVipOrder(orderData))
	}

	// 转换为OrderMapper接口
	var mappers []OrderMapper
	for _, order := range modelOrders {
		mappers = append(mappers, VipOrderMapper{Order: order})
	}

	// 使用通用函数处理
	processEcCpsOrdersGeneric(mappers, mapVipOrderToEcCpsOrder, batchSize, workerID, "vip")
}
