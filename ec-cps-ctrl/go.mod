module ec-cps-ctrl

go 1.21

require (
	finance v1.0.0
	gin-vue-admin v1.0.0
	github.com/360EntSecGroup-Skylar/excelize v1.4.1
	github.com/chenhg5/collection v0.0.0-20200925143926-f403b87088f9
	github.com/gin-gonic/gin v1.6.3
	github.com/google/uuid v1.3.0
	github.com/streadway/amqp v1.1.0
	go.uber.org/zap v1.16.0
	gorm.io/gorm v1.25.5
	user v1.0.0
	yz-go v1.0.0
)

replace (
	convergence => ../convergence-pay
	finance => ../finance
	gin-vue-admin v1.0.0 => ../gin-vue-admin/server
	notification => ../notification
	order => ../order
	payment => ../payment
	product => ../product
	region => ../region
	shipping => ../shipping
	user => ../user
	yz-go => ../yz-go

)
