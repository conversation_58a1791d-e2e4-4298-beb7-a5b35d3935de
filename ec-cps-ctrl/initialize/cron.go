package initialize

import (
	"ec-cps-ctrl/cron"
	"time"
)

// 初始化所有定时任务
func InitCron() {
	// 初始化淘宝订单同步
	cron.InitTaobaoOrderSync()

	// 初始化京东订单同步
	cron.InitJDOrderSync()

	// 初始化唯品会订单同步
	cron.InitVipOrderSync()

	// 初始化拼多多订单同步
	cron.InitPddOrderSync()

	// 初始化订单分成定时任务
	cron.InitOrderPercentageSync()
	// 默认配置
	var jdDefaultConfigHandle = cron.JDSyncConfig{
		LookBackHours: 48,
		PageSize:      100,
		RetryCount:    3,
		RetryInterval: time.Second * 2,
		WorkerCount:   5,
		BatchSize:     50,
		SyncInterval:  time.Minute * 5,
	}
	cron.HandleSyncJDOrders(jdDefaultConfigHandle)
}
