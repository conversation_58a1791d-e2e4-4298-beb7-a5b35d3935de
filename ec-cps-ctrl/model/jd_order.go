package model

import (
	"time"
	"yz-go/source"
)

// JDOrder 京东订单模型
type JDOrder struct {
	source.Model
	OrderId             string  `json:"order_id" gorm:"column:order_id;index"`                       // 订单号
	ParentId            string  `json:"parent_id" gorm:"column:parent_id"`                           // 父单的订单ID
	OrderTime           string  `json:"order_time" gorm:"column:order_time"`                         // 下单时间
	FinishTime          string  `json:"finish_time" gorm:"column:finish_time"`                       // 完成时间
	ModifyTime          string  `json:"modify_time" gorm:"column:modify_time"`                       // 更新时间
	OrderEmt            int     `json:"order_emt" gorm:"column:order_emt"`                           // 下单设备 1.pc 2.无线
	Plus                int     `json:"plus" gorm:"column:plus"`                                     // plus会员 1:是，0:否
	UnionId             string  `json:"union_id" gorm:"column:union_id"`                             // 推客ID
	SkuId               string  `json:"sku_id" gorm:"column:sku_id"`                                 // 商品ID
	SkuName             string  `json:"sku_name" gorm:"column:sku_name;type:varchar(255)"`           // 商品名称
	SkuNum              int     `json:"sku_num" gorm:"column:sku_num"`                               // 商品数量
	SkuReturnNum        int     `json:"sku_return_num" gorm:"column:sku_return_num"`                 // 商品已退货数量
	SkuFrozenNum        int     `json:"sku_frozen_num" gorm:"column:sku_frozen_num"`                 // 商品售后中数量
	Price               float64 `json:"price" gorm:"column:price"`                                   // 商品单价
	CommissionRate      float64 `json:"commission_rate" gorm:"column:commission_rate"`               // 佣金比例
	SubSideRate         float64 `json:"sub_side_rate" gorm:"column:sub_side_rate"`                   // 一级分成比例
	SubsidyRate         float64 `json:"subsidy_rate" gorm:"column:subsidy_rate"`                     // 一级补贴比例
	FinalRate           float64 `json:"final_rate" gorm:"column:final_rate"`                         // 最终比例
	EstimateCosPrice    float64 `json:"estimate_cos_price" gorm:"column:estimate_cos_price"`         // 预估计佣金额
	EstimateFee         float64 `json:"estimate_fee" gorm:"column:estimate_fee"`                     // 推客的预估佣金
	ActualCosPrice      float64 `json:"actual_cos_price" gorm:"column:actual_cos_price"`             // 实际计算佣金的金额
	ActualFee           float64 `json:"actual_fee" gorm:"column:actual_fee"`                         // 推客分得的实际佣金
	ValidCode           int     `json:"valid_code" gorm:"column:valid_code"`                         // sku维度的有效码
	TraceType           int     `json:"trace_type" gorm:"column:trace_type"`                         // 同跨店：2同店 3跨店
	PositionId          string  `json:"position_id" gorm:"column:position_id"`                       // 推广位ID
	SiteId              string  `json:"site_id" gorm:"column:site_id"`                               // 应用id
	UnionAlias          string  `json:"union_alias" gorm:"column:union_alias;type:varchar(255)"`     // 母账号简称
	Pid                 string  `json:"pid" gorm:"column:pid"`                                       // 格式:子推客ID_子站长应用ID_子推客推广位ID
	Cid1                string  `json:"cid1" gorm:"column:cid1"`                                     // 一级类目id
	Cid2                string  `json:"cid2" gorm:"column:cid2"`                                     // 二级类目id
	Cid3                string  `json:"cid3" gorm:"column:cid3"`                                     // 三级类目id
	SubUnionId          string  `json:"sub_union_id" gorm:"column:sub_union_id"`                     // 子联盟ID
	UnionTag            string  `json:"union_tag" gorm:"column:union_tag"`                           // 联盟标签数据
	PopId               string  `json:"pop_id" gorm:"column:pop_id"`                                 // 商家ID
	Ext1                string  `json:"ext1" gorm:"column:ext1"`                                     // 推客生成推广链接时传入的扩展字段
	PayMonth            string  `json:"pay_month" gorm:"column:pay_month"`                           // 预估结算时间
	CpActId             string  `json:"cp_act_id" gorm:"column:cp_act_id"`                           // 招商团活动id
	UnionRole           int     `json:"union_role" gorm:"column:union_role"`                         // 站长角色：1 推客 2 团长
	GiftCouponOcsAmount float64 `json:"gift_coupon_ocs_amount" gorm:"column:gift_coupon_ocs_amount"` // 礼金分摊金额
	GiftCouponKey       string  `json:"gift_coupon_key" gorm:"column:gift_coupon_key"`               // 礼金批次ID
	BalanceExt          string  `json:"balance_ext" gorm:"column:balance_ext"`                       // 计佣扩展信息
	ExpressStatus       int     `json:"express_status" gorm:"column:express_status"`                 // 发货状态（10：待发货，20：已发货）
	AppID               int     `json:"app_id" gorm:"column:app_id"`                                 // 商城ID
	ParentAppID         int     `json:"parent_app_id" gorm:"column:parent_app_id"`                   // 中台ID
	AppUserID           int     `json:"app_user_id" gorm:"column:app_user_id"`                       // 商城会员ID
	// 额外字段
	SyncTime      time.Time `json:"sync_time" gorm:"column:sync_time"`                     // 同步时间
	ProcessStatus int       `json:"process_status" gorm:"column:process_status;default:0"` // 处理状态：0-未处理，1-已处理
}

// TableName 设置表名
func (JDOrder) TableName() string {
	return "ec_cps_jd_orders"
}

type JDOrderModel struct {
	source.Model
	OrderId             string  `json:"order_id" gorm:"column:order_id;index"`                       // 订单号
	ParentId            string  `json:"parent_id" gorm:"column:parent_id"`                           // 父单的订单ID
	OrderTime           string  `json:"order_time" gorm:"column:order_time"`                         // 下单时间
	FinishTime          string  `json:"finish_time" gorm:"column:finish_time"`                       // 完成时间
	ModifyTime          string  `json:"modify_time" gorm:"column:modify_time"`                       // 更新时间
	OrderEmt            int     `json:"order_emt" gorm:"column:order_emt"`                           // 下单设备 1.pc 2.无线
	Plus                int     `json:"plus" gorm:"column:plus"`                                     // plus会员 1:是，0:否
	UnionId             string  `json:"union_id" gorm:"column:union_id"`                             // 推客ID
	SkuId               string  `json:"sku_id" gorm:"column:sku_id"`                                 // 商品ID
	SkuName             string  `json:"sku_name" gorm:"column:sku_name;type:varchar(255)"`           // 商品名称
	SkuNum              int     `json:"sku_num" gorm:"column:sku_num"`                               // 商品数量
	SkuReturnNum        int     `json:"sku_return_num" gorm:"column:sku_return_num"`                 // 商品已退货数量
	SkuFrozenNum        int     `json:"sku_frozen_num" gorm:"column:sku_frozen_num"`                 // 商品售后中数量
	Price               float64 `json:"price" gorm:"column:price"`                                   // 商品单价
	CommissionRate      float64 `json:"commission_rate" gorm:"column:commission_rate"`               // 佣金比例
	SubSideRate         float64 `json:"sub_side_rate" gorm:"column:sub_side_rate"`                   // 一级分成比例
	SubsidyRate         float64 `json:"subsidy_rate" gorm:"column:subsidy_rate"`                     // 一级补贴比例
	FinalRate           float64 `json:"final_rate" gorm:"column:final_rate"`                         // 最终比例
	EstimateCosPrice    float64 `json:"estimate_cos_price" gorm:"column:estimate_cos_price"`         // 预估计佣金额
	EstimateFee         float64 `json:"estimate_fee" gorm:"column:estimate_fee"`                     // 推客的预估佣金
	ActualCosPrice      float64 `json:"actual_cos_price" gorm:"column:actual_cos_price"`             // 实际计算佣金的金额
	ActualFee           float64 `json:"actual_fee" gorm:"column:actual_fee"`                         // 推客分得的实际佣金
	ValidCode           int     `json:"valid_code" gorm:"column:valid_code"`                         // sku维度的有效码
	TraceType           int     `json:"trace_type" gorm:"column:trace_type"`                         // 同跨店：2同店 3跨店
	PositionId          string  `json:"position_id" gorm:"column:position_id"`                       // 推广位ID
	SiteId              string  `json:"site_id" gorm:"column:site_id"`                               // 应用id
	UnionAlias          string  `json:"union_alias" gorm:"column:union_alias;type:varchar(255)"`     // 母账号简称
	Pid                 string  `json:"pid" gorm:"column:pid"`                                       // 格式:子推客ID_子站长应用ID_子推客推广位ID
	Cid1                string  `json:"cid1" gorm:"column:cid1"`                                     // 一级类目id
	Cid2                string  `json:"cid2" gorm:"column:cid2"`                                     // 二级类目id
	Cid3                string  `json:"cid3" gorm:"column:cid3"`                                     // 三级类目id
	SubUnionId          string  `json:"sub_union_id" gorm:"column:sub_union_id"`                     // 子联盟ID
	UnionTag            string  `json:"union_tag" gorm:"column:union_tag"`                           // 联盟标签数据
	PopId               string  `json:"pop_id" gorm:"column:pop_id"`                                 // 商家ID
	Ext1                string  `json:"ext1" gorm:"column:ext1"`                                     // 推客生成推广链接时传入的扩展字段
	PayMonth            string  `json:"pay_month" gorm:"column:pay_month"`                           // 预估结算时间
	CpActId             string  `json:"cp_act_id" gorm:"column:cp_act_id"`                           // 招商团活动id
	UnionRole           int     `json:"union_role" gorm:"column:union_role"`                         // 站长角色：1 推客 2 团长
	GiftCouponOcsAmount float64 `json:"gift_coupon_ocs_amount" gorm:"column:gift_coupon_ocs_amount"` // 礼金分摊金额
	GiftCouponKey       string  `json:"gift_coupon_key" gorm:"column:gift_coupon_key"`               // 礼金批次ID
	BalanceExt          string  `json:"balance_ext" gorm:"column:balance_ext"`                       // 计佣扩展信息
	ExpressStatus       int     `json:"express_status" gorm:"column:express_status"`                 // 发货状态（10：待发货，20：已发货）
	AppID               int     `json:"app_id" gorm:"column:app_id"`                                 // 商城ID
	ParentAppID         int     `json:"parent_app_id" gorm:"column:parent_app_id"`                   // 中台ID
	AppUserID           int     `json:"app_user_id" gorm:"column:app_user_id"`                       // 商城会员ID
	// 额外字段
	SyncTime      time.Time   `json:"sync_time" gorm:"column:sync_time"`                     // 同步时间
	ProcessStatus int         `json:"process_status" gorm:"column:process_status;default:0"` // 处理状态：0-未处理，1-已处理
	Application   Application `json:"application" gorm:"foreignKey:AppID;references:ID"`
}

// TableName 设置表名
func (JDOrderModel) TableName() string {
	return "ec_cps_jd_orders"
}

// JDOrderResponse 京东订单查询响应结构
type JDOrderResponse struct {
	Code         int       `json:"code"`
	Msg          string    `json:"msg"`
	TotalResults int       `json:"total_results"`
	HasMore      bool      `json:"hasMore"`
	Data         []JDOrder `json:"data"`
}
