package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"gorm.io/gorm"
	"yz-go/model"
	"yz-go/source"
)

type CpsSetting struct {
	model.SysSetting
	Value CpsValue `json:"value"`
}

func (i CpsSetting) TableName() string {
	return "sys_settings"
}

type CpsValue struct {
	ApiKey              string `json:"apikey"`
	RedirectUriHost     string `json:"redirect_uri_host"`
	InviteCode          string `json:"invite_code"`
	OpenTaobaoAppKey    string `json:"open_taobao_app_key"`
	OpenTaobaoAppSecret string `json:"open_taobao_app_secret"`
	OpenTaobaoPid       string `json:"open_taobao_pid"`
	PddMediaId          string `json:"pdd_media_id"`
}

func (value CpsValue) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *CpsValue) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

var cpsSetting *CpsValue

func getCpsSetting(key string) (err error, sysSetting CpsSetting) {
	err = source.DB().Where("`key` = ?", key).First(&sysSetting).Error
	return
}
func GetCpsSetting() (err error, setting CpsValue) {
	if cpsSetting == nil {
		var sysSetting CpsSetting
		err, sysSetting = getCpsSetting("ec_cps_setting")
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil
		}
		if err != nil {
			return
		}
		cpsSetting = &sysSetting.Value
	}
	return err, *cpsSetting
}

func ResetCps() {
	//重置全局变量 start
	cpsSetting = nil
}
