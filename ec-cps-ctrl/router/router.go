package router

import (
	"ec-cps-ctrl/api/app"
	v1 "ec-cps-ctrl/api/v1"

	"github.com/gin-gonic/gin"
)

func InitAdminPrivateRouter(Router *gin.RouterGroup) {
	CpsRouter := Router.Group("ecCpsCtrl")
	{
		CpsRouter.POST("saveSetting", v1.UpdateCpsSetting)    //
		CpsRouter.POST("getSetting", v1.FindCpsSetting)       //
		CpsRouter.POST("orderList", v1.GetOrderList)          //
		CpsRouter.POST("exportOrderList", v1.ExportOrderList) //

	}

	// 淘宝订单管理
	TaobaoOrderRouter := Router.Group("taobaoOrder")
	{
		TaobaoOrderRouter.GET("list", v1.GetTaobaoOrderList)         // 获取淘宝订单列表
		TaobaoOrderRouter.POST("export", v1.ExportTaobaoOrder)       // 导出淘宝订单
		TaobaoOrderRouter.GET("detail/:id", v1.GetTaobaoOrderDetail) // 获取淘宝订单详情
		TaobaoOrderRouter.GET("query", v1.QueryTaobaoOrders)         // 直接查询淘宝订单接口
	}

	JDOrderRouter := Router.Group("jdOrder")
	{
		JDOrderRouter.GET("list", v1.GetJDOrderList)         // 获取京东订单列表
		JDOrderRouter.POST("export", v1.ExportJDOrder)       // 导出京东订单
		JDOrderRouter.GET("detail/:id", v1.GetJDOrderDetail) // 获取京东订单详情
		JDOrderRouter.GET("query", v1.QueryJDOrders)         // 直接查询京东订单接口
	}

	VipOrderRouter := Router.Group("vipOrder")
	{
		VipOrderRouter.GET("list", v1.GetVipOrderList)         // 获取唯品会订单列表
		VipOrderRouter.POST("export", v1.ExportVipOrder)       // 导出唯品会订单
		VipOrderRouter.GET("detail/:id", v1.GetVipOrderDetail) // 获取唯品会订单详情
		VipOrderRouter.GET("query", v1.QueryVipOrders)         // 直接查询唯品会订单接口
	}

	PddOrderRouter := Router.Group("pddOrder")
	{
		PddOrderRouter.GET("list", v1.GetPddOrderList)         // 获取拼多多订单列表
		PddOrderRouter.POST("export", v1.ExportPddOrder)       // 导出拼多多订单
		PddOrderRouter.GET("detail/:id", v1.GetPddOrderDetail) // 获取拼多多订单详情
		PddOrderRouter.GET("query", v1.QueryPddOrders)         // 直接查询拼多多订单接口
	}
}

func InitAppPrivateRouter(Router *gin.RouterGroup) {
	CpsRouter := Router.Group("ecCpsCtrl/taobao")
	{
		CpsRouter.POST("getTaobaoRelationId", app.PublisherSave)       //
		CpsRouter.POST("tcGeneralConvert", app.TcGeneralConvert)       //
		CpsRouter.POST("idPrivilege", app.IdPrivilege)                 //
		CpsRouter.POST("tklPrivilege", app.TklPrivilege)               //
		CpsRouter.POST("shopConvert", app.ShopConvert)                 //
		CpsRouter.POST("orderDetails", app.OrderDetails)               //
		CpsRouter.POST("scPunishOrder", app.ScPunishOrder)             //
		CpsRouter.POST("invitecodeGet", app.InvitecodeGet)             //
		CpsRouter.POST("superSearchMaterial", app.SuperSearchMaterial) //
		CpsRouter.POST("itemInfo", app.TbItemInfo)                     //
		CpsRouter.POST("materialRecommend", app.MaterialRecommend)     //

	}

	JDRouter := Router.Group("ecCpsCtrl/jd")
	{
		JDRouter.POST("goodsCategory", app.GoodsCategory)           //
		JDRouter.POST("queryJingfenGoods", app.QueryJingfenGoods)   //
		JDRouter.POST("queryGoods", app.QueryGoods)                 //
		JDRouter.POST("getJdSkuid", app.GetJdSkuid)                 //
		JDRouter.POST("itemDetail", app.ItemDetail)                 //
		JDRouter.POST("materialQuery", app.MaterialQuery)           //
		JDRouter.POST("activityQuery", app.ActivityQuery)           //
		JDRouter.POST("orderDetails2", app.OrderDetails2)           //
		JDRouter.POST("urlPrivilege", app.UrlPrivilege)             //
		JDRouter.POST("byUnionidPromotion", app.ByUnionidPromotion) //
	}

	PDDRouter := Router.Group("ecCpsCtrl/pdd")
	{
		PDDRouter.POST("convert", app.PddConvert)                 // 商品转链
		PDDRouter.POST("urlGenerate", app.PddUrlGenerate)         // 多多进宝推广链接生成
		PDDRouter.POST("urlConvert", app.PddUrlConvert)           // 链接解析转链
		PDDRouter.POST("resourceConvert", app.PddResourceConvert) // 活动转链
		PDDRouter.POST("pidGenerate", app.PddPidGenerate)         // 创建推广位
		PDDRouter.POST("pidQuery", app.PddPidQuery)               // 查询推广位
		PDDRouter.POST("orderList", app.PddOrderList)             // 订单列表
		PDDRouter.POST("orderDetail", app.PddOrderDetail)         // 订单详情
		PDDRouter.POST("goodsSearch", app.GoodsSearch)            // 订单详情
		PDDRouter.POST("goodsDetail2", app.GoodsDetail2)          // 订单详情
		PDDRouter.POST("cats", app.Cats)                          // 订单详情
		PDDRouter.POST("promUrlGenerate", app.PromUrlGenerate)    // 订单详情
	}

	VIPRouter := Router.Group("ecCpsCtrl/vip")
	{
		VIPRouter.POST("goodsList", app.GoodsList)               // 商品列表
		VIPRouter.POST("itemInfo", app.ItemInfo)                 // 商品信息
		VIPRouter.POST("query", app.VipQuery)                    // 查询
		VIPRouter.POST("viplinkCheck", app.ViplinkCheck)         // 链接检查
		VIPRouter.POST("similarRecommend", app.SimilarRecommend) // 相似推荐
		VIPRouter.POST("userRecommend", app.UserRecommend)       // 用户推荐
		VIPRouter.POST("itemInfo2", app.ItemInfo2)               // 商品信息2
		VIPRouter.POST("orderDetails", app.VipOrderDetails)      // 订单详情
		VIPRouter.POST("orderDetails2", app.VipOrderDetails2)    // 订单详情2
		VIPRouter.POST("idPrivilege", app.VipIdPrivilege)        // ID权限
		VIPRouter.POST("urlPrivilege", app.VipUrlPrivilege)      // URL权限
		VIPRouter.POST("getVipAccess", app.GetVipAccess)         // 获取VIP访问权限
		VIPRouter.POST("refreshToken", app.RefreshToken)         // 刷新令牌
	}

	OpenTbRouter := Router.Group("ecCpsCtrl/openTb")
	{
		OpenTbRouter.POST("getRelations", app.GetAccessTokens) // 获取AccessToken列表
		OpenTbRouter.POST("getSetting", app.GetSetting)        // 获取AccessToken列表
		OpenTbRouter.POST("getApiKey", app.GetSetting)         // 获取AccessToken列表

	}
	TaobaoOrderRouter := Router.Group("ecCpsCtrl/taobaoOrder")
	{
		TaobaoOrderRouter.POST("list", app.GetTaobaoOrderList)        // 获取淘宝订单列表
		TaobaoOrderRouter.POST("export", app.ExportTaobaoOrder)       // 导出淘宝订单
		TaobaoOrderRouter.GET("detail/:id", app.GetTaobaoOrderDetail) // 获取淘宝订单详情
		TaobaoOrderRouter.GET("query", app.QueryTaobaoOrders)         // 直接查询淘宝订单接口
	}

	JDOrderRouter := Router.Group("ecCpsCtrl/jdOrder")
	{
		JDOrderRouter.POST("list", app.GetJDOrderList)        // 获取京东订单列表
		JDOrderRouter.POST("export", app.ExportJDOrder)       // 导出京东订单
		JDOrderRouter.GET("detail/:id", app.GetJDOrderDetail) // 获取京东订单详情
		JDOrderRouter.GET("query", app.QueryJDOrders)         // 直接查询京东订单接口
	}

	VipOrderRouter := Router.Group("ecCpsCtrl/vipOrder")
	{
		VipOrderRouter.POST("list", app.GetVipOrderList)        // 获取唯品会订单列表
		VipOrderRouter.POST("export", app.ExportVipOrder)       // 导出唯品会订单
		VipOrderRouter.GET("detail/:id", app.GetVipOrderDetail) // 获取唯品会订单详情
		VipOrderRouter.GET("query", app.QueryVipOrders)         // 直接查询唯品会订单接口
	}

	PddOrderRouter := Router.Group("ecCpsCtrl/pddOrder")
	{
		PddOrderRouter.POST("list", app.GetPddOrderList)        // 获取拼多多订单列表
		PddOrderRouter.POST("export", app.ExportPddOrder)       // 导出拼多多订单
		PddOrderRouter.GET("detail/:id", app.GetPddOrderDetail) // 获取拼多多订单详情
		PddOrderRouter.GET("query", app.QueryPddOrders)         // 直接查询拼多多订单接口
	}
}

func InitAdminPublicRouter(Router *gin.RouterGroup) {
	CpsRouter := Router.Group("ecCps")
	{
		CpsRouter.GET("opentbRedirectUri", app.RedirectUri) //
	}

}
