package service

import (
	"ec-cps-ctrl/model"
	"encoding/csv"
	"encoding/json"
	model2 "finance/model"
	"fmt"
	"github.com/go-resty/resty/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"os"
	"path/filepath"
	"strconv"
	"time"
	"yz-go/component/log"
	"yz-go/source"
)

// 京东订单列表请求参数
type JDOrderListRequest struct {
	Page            int    `form:"page" json:"page" binding:"required"`           // 页码
	PageSize        int    `form:"page_size" json:"page_size" binding:"required"` // 每页数量
	OrderId         string `form:"order_id" json:"order_id"`                      // 订单号
	ValidCode       *int   `form:"valid_code" json:"valid_code"`                  // 订单状态
	StartTime       string `form:"start_time" json:"start_time"`                  // 开始时间
	EndTime         string `form:"end_time" json:"end_time"`                      // 结束时间
	SkuId           string `form:"sku_id" json:"sku_id"`                          // 商品ID
	ParentAppID     uint   `json:"parent_app_id"`
	ParentAppUserID uint   `json:"parent_app_user_id"`
}

// 京东订单列表结果
type JDOrderListResult struct {
	List      []JDOrder        `json:"list"`      // 订单列表
	Total     int64            `json:"total"`     // 总数
	Page      int              `json:"page"`      // 当前页码
	PageSize  int              `json:"page_size"` // 每页数量
	Statistic JDOrderStatistic `json:"statistic"` // 统计数据
	Status    map[int]string   `json:"status"`    // 状态映射
}

// 京东订单统计
type JDOrderStatistic struct {
	Total      int64 `json:"total"`       // 总订单数
	WaitSettle int64 `json:"wait_settle"` // 待结算订单数
	Settled    int64 `json:"settled"`     // 已结算订单数
	Invalid    int64 `json:"invalid"`     // 无效/已失效订单数
}

// 京东订单查询请求
type JDOrderQueryRequest struct {
	ApiKey    string `form:"apikey" json:"apikey"`         // API密钥
	StartTime string `form:"start_time" json:"start_time"` // 开始时间
	EndTime   string `form:"end_time" json:"end_time"`     // 结束时间
	PageIndex string `form:"page_index" json:"page_index"` // 页码
	PageSize  string `form:"page_size" json:"page_size"`   // 每页数量
	Type      string `form:"type" json:"type"`             // 查询类型：1-下单时间，2-完成时间，3-更新时间
}

// 京东订单查询响应
type JDOrderQueryResponse struct {
	Code         int             `json:"code"`
	Msg          string          `json:"msg"`
	TotalResults int             `json:"total_results"`
	HasMore      bool            `json:"hasMore"`
	Data         []model.JDOrder `json:"data"`
}

// 获取订单状态映射
func getJDOrderStatusMap() map[int]string {
	return map[int]string{
		15: "已下单",
		16: "已付款",
		17: "已完成",
		18: "已取消",
	}
}

type JDOrder struct {
	source.Model
	OrderId             string  `json:"order_id" gorm:"column:order_id;index"`                       // 订单号
	ParentId            string  `json:"parent_id" gorm:"column:parent_id"`                           // 父单的订单ID
	OrderTime           string  `json:"order_time" gorm:"column:order_time"`                         // 下单时间
	FinishTime          string  `json:"finish_time" gorm:"column:finish_time"`                       // 完成时间
	ModifyTime          string  `json:"modify_time" gorm:"column:modify_time"`                       // 更新时间
	OrderEmt            int     `json:"order_emt" gorm:"column:order_emt"`                           // 下单设备 1.pc 2.无线
	Plus                int     `json:"plus" gorm:"column:plus"`                                     // plus会员 1:是，0:否
	UnionId             string  `json:"union_id" gorm:"column:union_id"`                             // 推客ID
	SkuId               string  `json:"sku_id" gorm:"column:sku_id"`                                 // 商品ID
	SkuName             string  `json:"sku_name" gorm:"column:sku_name;type:varchar(255)"`           // 商品名称
	SkuNum              int     `json:"sku_num" gorm:"column:sku_num"`                               // 商品数量
	SkuReturnNum        int     `json:"sku_return_num" gorm:"column:sku_return_num"`                 // 商品已退货数量
	SkuFrozenNum        int     `json:"sku_frozen_num" gorm:"column:sku_frozen_num"`                 // 商品售后中数量
	Price               float64 `json:"price" gorm:"column:price"`                                   // 商品单价
	EstimateCosPrice    float64 `json:"estimate_cos_price" gorm:"column:estimate_cos_price"`         // 预估计佣金额
	EstimateFee         float64 `json:"estimate_fee" gorm:"column:estimate_fee"`                     // 推客的预估佣金
	ActualCosPrice      float64 `json:"actual_cos_price" gorm:"column:actual_cos_price"`             // 实际计算佣金的金额
	ActualFee           float64 `json:"actual_fee" gorm:"column:actual_fee"`                         // 推客分得的实际佣金
	ValidCode           int     `json:"valid_code" gorm:"column:valid_code"`                         // sku维度的有效码
	TraceType           int     `json:"trace_type" gorm:"column:trace_type"`                         // 同跨店：2同店 3跨店
	PositionId          string  `json:"position_id" gorm:"column:position_id"`                       // 推广位ID
	SiteId              string  `json:"site_id" gorm:"column:site_id"`                               // 应用id
	UnionAlias          string  `json:"union_alias" gorm:"column:union_alias;type:varchar(255)"`     // 母账号简称
	Pid                 string  `json:"pid" gorm:"column:pid"`                                       // 格式:子推客ID_子站长应用ID_子推客推广位ID
	Cid1                string  `json:"cid1" gorm:"column:cid1"`                                     // 一级类目id
	Cid2                string  `json:"cid2" gorm:"column:cid2"`                                     // 二级类目id
	Cid3                string  `json:"cid3" gorm:"column:cid3"`                                     // 三级类目id
	SubUnionId          string  `json:"sub_union_id" gorm:"column:sub_union_id"`                     // 子联盟ID
	UnionTag            string  `json:"union_tag" gorm:"column:union_tag"`                           // 联盟标签数据
	PopId               string  `json:"pop_id" gorm:"column:pop_id"`                                 // 商家ID
	Ext1                string  `json:"ext1" gorm:"column:ext1"`                                     // 推客生成推广链接时传入的扩展字段
	PayMonth            string  `json:"pay_month" gorm:"column:pay_month"`                           // 预估结算时间
	CpActId             string  `json:"cp_act_id" gorm:"column:cp_act_id"`                           // 招商团活动id
	UnionRole           int     `json:"union_role" gorm:"column:union_role"`                         // 站长角色：1 推客 2 团长
	GiftCouponOcsAmount float64 `json:"gift_coupon_ocs_amount" gorm:"column:gift_coupon_ocs_amount"` // 礼金分摊金额
	GiftCouponKey       string  `json:"gift_coupon_key" gorm:"column:gift_coupon_key"`               // 礼金批次ID
	BalanceExt          string  `json:"balance_ext" gorm:"column:balance_ext"`                       // 计佣扩展信息
	ExpressStatus       int     `json:"express_status" gorm:"column:express_status"`                 // 发货状态（10：待发货，20：已发货）
	AppID               int     `json:"app_id" gorm:"column:app_id"`                                 // 商城ID
	ParentAppID         int     `json:"parent_app_id" gorm:"column:parent_app_id"`                   // 中台ID
	AppUserID           int     `json:"app_user_id" gorm:"column:app_user_id"`                       // 商城会员ID
	// 额外字段
	SyncTime      time.Time `json:"sync_time" gorm:"column:sync_time"`                     // 同步时间
	ProcessStatus int       `json:"process_status" gorm:"column:process_status;default:0"` // 处理状态：0-未处理，1-已处理
}

func (JDOrder) TableName() string {
	return "ec_cps_jd_orders"
}

// GetJDOrderList 获取京东订单列表
func GetJDOrderList(req JDOrderListRequest) (error, JDOrderListResult) {
	var result JDOrderListResult

	// 构建查询
	db := source.DB().Model(&model.JDOrder{})
	db = db.Where("parent_app_id = ?", req.ParentAppID)

	// 条件查询
	if req.OrderId != "" {
		db = db.Where("order_id = ?", req.OrderId)
	}
	if req.ValidCode != nil {
		db = db.Where("valid_code = ?", *req.ValidCode)
	}
	if req.StartTime != "" {
		db = db.Where("sync_time >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		db = db.Where("sync_time <= ?", req.EndTime)
	}
	if req.SkuId != "" {
		db = db.Where("sku_id = ?", req.SkuId)
	}

	// 统计数据
	var statistic JDOrderStatistic
	// 总订单数
	db.Count(&statistic.Total)
	// 待结算订单数 (有效码为16-付款)
	source.DB().Model(&model.JDOrder{}).Where("valid_code = ?", 16).Count(&statistic.WaitSettle)
	// 已结算订单数 (有效码为17-完成)
	source.DB().Model(&model.JDOrder{}).Where("valid_code = ?", 17).Count(&statistic.Settled)
	// 无效/已失效订单数 (有效码为18-取消)
	source.DB().Model(&model.JDOrder{}).Where("valid_code = ?", 18).Count(&statistic.Invalid)

	// 分页查询
	var list []JDOrder
	err := db.Order("sync_time DESC").Limit(req.PageSize).Offset((req.Page - 1) * req.PageSize).Find(&list).Error
	if err != nil {
		return fmt.Errorf("查询京东订单列表失败: %v", err), result
	}
	var userModel model2.User
	err = source.DB().Preload("UserLevelInfo").First(&userModel, req.ParentAppUserID).Error
	if err != nil {
		return fmt.Errorf("查询京东订单列表失败: %v", err), result
	}
	if req.ParentAppID > 0 {
		for key, item := range list {
			// 将计算结果转回字符串，保留两位小数
			list[key].ActualFee = item.ActualFee * float64(userModel.UserLevelInfo.CpsRatio) / 10000
			list[key].EstimateFee = item.EstimateFee * float64(userModel.UserLevelInfo.CpsRatio) / 10000
		}
	}

	// 设置结果
	result.List = list
	result.Total = statistic.Total
	result.Page = req.Page
	result.PageSize = req.PageSize
	result.Statistic = statistic
	result.Status = getJDOrderStatusMap()

	return nil, result
}

// GetJDOrderDetail 获取京东订单详情
func GetJDOrderDetail(id int) (error, model.JDOrder) {
	var order model.JDOrder
	err := source.DB().First(&order, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("订单不存在"), order
		}
		return fmt.Errorf("查询京东订单详情失败: %v", err), order
	}
	return nil, order
}

// ExportJDOrders 导出京东订单
func ExportJDOrders(orders []model.JDOrder) error {
	if len(orders) == 0 {
		return fmt.Errorf("没有可导出的订单数据")
	}

	// 创建导出目录
	exportDir := "./export"
	if _, err := os.Stat(exportDir); os.IsNotExist(err) {
		err = os.MkdirAll(exportDir, 0755)
		if err != nil {
			log.Log().Error("创建导出目录失败", zap.Error(err))
			return err
		}
	}

	// 创建CSV文件
	timestamp := time.Now().Format("20060102150405")
	filename := fmt.Sprintf("jd_orders_%s.csv", timestamp)
	filepath := filepath.Join(exportDir, filename)

	file, err := os.Create(filepath)
	if err != nil {
		log.Log().Error("创建CSV文件失败", zap.Error(err))
		return err
	}
	defer file.Close()

	// 创建CSV写入器
	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 写入表头
	headers := []string{
		"订单号", "父订单号", "商品ID", "商品名称", "商品单价",
		"商品数量", "商品已退货数量", "商品售后中数量", "订单状态", "下单时间",
		"完成时间", "更新时间", "下单设备", "推客ID", "佣金比例",
		"预估佣金", "实际佣金", "店铺ID", "推广位ID", "同步时间",
		"处理状态",
	}
	err = writer.Write(headers)
	if err != nil {
		log.Log().Error("写入CSV表头失败", zap.Error(err))
		return err
	}

	// 获取订单状态映射
	statusMap := getJDOrderStatusMap()

	// 写入数据行
	for _, order := range orders {
		// 格式化时间
		syncTime := ""
		if !order.SyncTime.IsZero() {
			syncTime = order.SyncTime.Format("2006-01-02 15:04:05")
		}

		// 获取状态文本
		statusText := statusMap[order.ValidCode]
		if statusText == "" {
			statusText = "未知状态"
		}

		// 下单设备
		orderEmt := "PC"
		if order.OrderEmt == 2 {
			orderEmt = "无线"
		}

		row := []string{
			strconv.Itoa(int(order.OrderId)),
			strconv.Itoa(int(order.ParentId)),
			strconv.Itoa(int(order.SkuId)),
			order.SkuName,
			fmt.Sprintf("%.2f", order.Price),
			strconv.Itoa(order.SkuNum),
			strconv.Itoa(order.SkuReturnNum),
			strconv.Itoa(order.SkuFrozenNum),
			statusText,
			order.OrderTime,
			order.FinishTime,
			order.ModifyTime,
			orderEmt,
			strconv.Itoa(int(order.UnionId)),
			fmt.Sprintf("%.2f%%", order.CommissionRate),
			fmt.Sprintf("%.2f", order.EstimateFee),
			fmt.Sprintf("%.2f", order.ActualFee),
			strconv.Itoa(int(order.PopId)),
			strconv.Itoa(int(order.PositionId)),
			syncTime,
			strconv.Itoa(order.ProcessStatus),
		}

		err = writer.Write(row)
		if err != nil {
			log.Log().Error("写入CSV数据行失败", zap.Error(err))
			return err
		}
	}

	log.Log().Info("导出京东订单成功", zap.String("文件路径", filepath), zap.Int("订单数量", len(orders)))
	return nil
}

// ExportJDOrdersByQuery 根据查询条件导出京东订单
func ExportJDOrdersByQuery(req JDOrderListRequest) error {
	// 构建查询
	db := source.DB().Model(&model.JDOrder{})

	// 条件查询
	if req.OrderId != "" {
		db = db.Where("order_id = ?", req.OrderId)
	}
	if req.ValidCode != nil {
		db = db.Where("valid_code = ?", *req.ValidCode)
	}
	if req.StartTime != "" {
		db = db.Where("order_time >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		db = db.Where("order_time <= ?", req.EndTime)
	}
	if req.SkuId != "" {
		db = db.Where("sku_id = ?", req.SkuId)
	}

	// 查询数据
	var list []model.JDOrder
	err := db.Order("sync_time DESC").Find(&list).Error
	if err != nil {
		return fmt.Errorf("查询京东订单列表失败: %v", err)
	}

	// 导出数据
	return ExportJDOrders(list)
}

// QueryJDOrders 直接查询京东订单接口
func QueryJDOrders(req JDOrderQueryRequest) (err error, resp JDOrderQueryResponse, syncErr error) {
	// 获取API密钥
	if req.ApiKey == "" {
		err, setting := GetCpsSetting()
		if err != nil {
			return err, resp, nil
		}
		req.ApiKey = setting.Value.ApiKey
	}

	// 设置默认值
	if req.Type == "" {
		req.Type = "3" // 默认按更新时间查询
	}
	if req.PageIndex == "" {
		req.PageIndex = "1" // 默认第一页
	}
	if req.PageSize == "" {
		req.PageSize = "100" // 默认每页100条
	}

	// 构建查询参数
	queryParams := map[string]string{
		"apikey":    req.ApiKey,
		"type":      req.Type,
		"pageIndex": req.PageIndex,
		"pageSize":  req.PageSize,
	}

	// 添加可选参数
	if req.StartTime != "" {
		queryParams["startTime"] = req.StartTime
	}
	if req.EndTime != "" {
		queryParams["endTime"] = req.EndTime
	}

	// 创建HTTP客户端
	client := resty.New().SetRetryCount(3).SetRetryWaitTime(2 * time.Second)

	// 发送请求
	response, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetQueryParams(queryParams).
		Post("http://api.tbk.dingdanxia.com/jd/order_details2")

	if err != nil {
		return fmt.Errorf("请求京东订单接口失败: %v", err), resp, nil
	}

	// 解析响应
	if err := json.Unmarshal(response.Body(), &resp); err != nil {
		return fmt.Errorf("解析响应数据失败: %v", err), resp, nil
	}

	// 检查响应状态
	if resp.Code != 200 {
		return fmt.Errorf("接口返回错误: %s", resp.Msg), resp, nil
	}

	// 同步订单到数据库（异步处理）
	go func() {
		syncErr = SyncJDOrdersToDatabase(resp.Data)
	}()

	return nil, resp, syncErr
}

// SyncJDOrdersToDatabase 同步京东订单到数据库
func SyncJDOrdersToDatabase(orders []model.JDOrder) error {
	if len(orders) == 0 {
		return nil
	}

	log.Log().Info("开始同步京东订单到数据库", zap.Int("订单数量", len(orders)))

	// 收集所有订单ID
	var orderIds []int64
	for _, order := range orders {
		orderIds = append(orderIds, order.OrderId)
	}

	// 查询数据库中已存在的订单
	var existingOrders []model.JDOrder
	result := source.DB().Where("order_id IN ?", orderIds).Find(&existingOrders)
	if result.Error != nil && result.Error != gorm.ErrRecordNotFound {
		log.Log().Error("查询现有订单失败", zap.Error(result.Error))
		return result.Error
	}

	// 将现有订单映射到map中便于快速查找
	existingOrderMap := make(map[int64]model.JDOrder)
	for _, order := range existingOrders {
		existingOrderMap[order.OrderId] = order
	}

	// 准备新订单和需要更新的订单
	var newOrders []model.JDOrder
	var updateOrders []model.JDOrder

	for _, order := range orders {
		// 设置同步时间
		order.SyncTime = time.Now()

		if _, exists := existingOrderMap[order.OrderId]; !exists {
			// 新订单
			newOrders = append(newOrders, order)
		} else {
			// 需要更新的订单
			dbOrder := existingOrderMap[order.OrderId]
			dbOrder.ValidCode = order.ValidCode
			dbOrder.SkuReturnNum = order.SkuReturnNum
			dbOrder.SkuFrozenNum = order.SkuFrozenNum
			dbOrder.FinishTime = order.FinishTime
			dbOrder.ModifyTime = order.ModifyTime
			dbOrder.EstimateCosPrice = order.EstimateCosPrice
			dbOrder.EstimateFee = order.EstimateFee
			dbOrder.ActualCosPrice = order.ActualCosPrice
			dbOrder.ActualFee = order.ActualFee
			dbOrder.PayMonth = order.PayMonth
			dbOrder.ExpressStatus = order.ExpressStatus
			dbOrder.SyncTime = time.Now()
			updateOrders = append(updateOrders, dbOrder)
		}
	}

	// 批量创建新订单
	if len(newOrders) > 0 {
		// 分批插入以避免一次插入过多数据
		batchSize := 50
		for i := 0; i < len(newOrders); i += batchSize {
			end := i + batchSize
			if end > len(newOrders) {
				end = len(newOrders)
			}

			batch := newOrders[i:end]
			if err := source.DB().Create(&batch).Error; err != nil {
				log.Log().Error("批量创建订单失败", zap.Error(err))
				return err
			}
		}
		log.Log().Info("成功创建新订单", zap.Int("数量", len(newOrders)))
	}

	// 批量更新订单
	if len(updateOrders) > 0 {
		// 分批更新
		batchSize := 50
		for i := 0; i < len(updateOrders); i += batchSize {
			end := i + batchSize
			if end > len(updateOrders) {
				end = len(updateOrders)
			}

			batch := updateOrders[i:end]
			for _, order := range batch {
				// 使用条件更新来避免并发问题
				if err := source.DB().Model(&model.JDOrder{}).Where("order_id = ?", order.OrderId).Updates(map[string]interface{}{
					"valid_code":         order.ValidCode,
					"sku_return_num":     order.SkuReturnNum,
					"sku_frozen_num":     order.SkuFrozenNum,
					"finish_time":        order.FinishTime,
					"modify_time":        order.ModifyTime,
					"estimate_cos_price": order.EstimateCosPrice,
					"estimate_fee":       order.EstimateFee,
					"actual_cos_price":   order.ActualCosPrice,
					"actual_fee":         order.ActualFee,
					"pay_month":          order.PayMonth,
					"express_status":     order.ExpressStatus,
					"sync_time":          time.Now(),
				}).Error; err != nil {
					log.Log().Error("更新订单失败", zap.Int64("订单ID", order.OrderId), zap.Error(err))
					return err
				}
			}
		}
		log.Log().Info("成功更新订单", zap.Int("数量", len(updateOrders)))
	}

	return nil
}
