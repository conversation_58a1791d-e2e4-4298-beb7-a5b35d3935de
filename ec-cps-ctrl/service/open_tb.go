package service

import (
	"douyin-cps/request"
	"douyin-cps/utils"
	model2 "ec-cps-ctrl/model"
	request2 "ec-cps-ctrl/request"
	"encoding/json"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"strconv"
	"strings"
	"time"
	"yz-go/model"
	"yz-go/source"
)

type TokenResponse struct {
	TopAuthTokenCreateResponse struct {
		TokenResult string `json:"token_result"`
		RequestId   string `json:"request_id"`
	} `json:"top_auth_token_create_response"`

	ErrorResponse struct {
		Code      int    `json:"code"`
		Msg       string `json:"msg"`
		SubCode   string `json:"sub_code"`
		SubMsg    string `json:"sub_msg"`
		RequestId string `json:"request_id"`
	} `json:"error_response"`
}

func GetOpenTbToken(info request.GetOpenTbTokenInfo) (err error, data interface{}) {
	err, setting := model2.GetCpsSetting()
	if err != nil {
		return
	}
	// 创建API请求
	request := utils.NewTaobaoAPIRequest(
		"taobao.top.auth.token.create",
		setting.OpenTaobaoAppKey,
		setting.OpenTaobaoAppSecret,
	)

	// 设置业务参数
	request.BizParams = map[string]string{
		"code": info.Code,
	}

	// 执行请求
	response, err := request.Execute()
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	fmt.Printf("Response: %s\n", string(response))

	var responseStruct TokenResponse
	err = json.Unmarshal(response, &responseStruct)
	if err != nil {
		return
	}
	if responseStruct.ErrorResponse.Code != 0 {
		err = errors.New(responseStruct.ErrorResponse.Msg)
		return
	}
	data = responseStruct.TopAuthTokenCreateResponse.TokenResult
	return
}

type TokenResult struct {
	W1ExpiresIn           int    `json:"w1_expires_in"`
	RefreshTokenValidTime int64  `json:"refresh_token_valid_time"`
	TaobaoUserNick        string `json:"taobao_user_nick"`
	ReExpiresIn           int    `json:"re_expires_in"`
	ExpireTime            int64  `json:"expire_time"`
	TokenType             string `json:"token_type"`
	AccessToken           string `json:"access_token"`
	TaobaoOpenUid         string `json:"taobao_open_uid"`
	W1Valid               int64  `json:"w1_valid"`
	RefreshToken          string `json:"refresh_token"`
	W2ExpiresIn           int    `json:"w2_expires_in"`
	W2Valid               int64  `json:"w2_valid"`
	R1ExpiresIn           int    `json:"r1_expires_in"`
	R2ExpiresIn           int    `json:"r2_expires_in"`
	R2Valid               int64  `json:"r2_valid"`
	R1Valid               int64  `json:"r1_valid"`
	TaobaoUserId          string `json:"taobao_user_id"`
	ExpiresIn             int    `json:"expires_in"`
}

func RedirectUri(info request.GetOpenTbTokenInfo) (err error) {
	err, data := GetOpenTbToken(info)
	if err != nil {
		return
	}
	jsonData := source.Strval(data)
	var tokenResult TokenResult
	err = json.Unmarshal([]byte(jsonData), &tokenResult)
	if err != nil {
		return
	}
	err, relationId := PublisherSave(request2.GetTaobaoRelationIdRequest{
		AccessToken: tokenResult.AccessToken,
	})
	if err != nil {
		return
	}
	var apps []string
	apps = strings.Split(info.State, "_")
	if len(apps) == 1 {
		//中台
		err = source.DB().Model(&model.Application{}).Where("id = ?", apps[0]).Updates(&map[string]interface{}{
			"open_tb_token":             tokenResult.AccessToken,
			"open_tb_token_expire_time": tokenResult.ExpiresIn,
			"open_tb_relation_id":       relationId,
		}).Error
		var relation model2.OpenTbAccessToken
		relation.RelationId = relationId
		relation.AccessToken = tokenResult.AccessToken
		relation.ExpiresIn = &source.LocalTime{Time: time.Unix(time.Now().Unix()+int64(tokenResult.ExpiresIn), 0)}
		relation.AppID = 0
		relation.AppUserID = 0
		if err != nil {
			return
		}
		relation.ParentAppId, err = strconv.Atoi(apps[0])
		if err != nil {
			return
		}
		err = source.DB().Where("app_id = 0 and parent_app_id = ? and app_user_id = 0", relation.ParentAppId).Delete(&model2.OpenTbAccessToken{}).Error
		if err != nil {
			return
		}
		err = source.DB().Create(&relation).Error
		if err != nil {
			return
		}
	} else if len(apps) == 2 {
		//商城
		var relation model2.OpenTbAccessToken
		relation.RelationId = relationId
		relation.AccessToken = tokenResult.AccessToken
		relation.ExpiresIn = &source.LocalTime{Time: time.Unix(time.Now().Unix()+int64(tokenResult.ExpiresIn), 0)}
		relation.AppID, err = strconv.Atoi(apps[1])
		if err != nil {
			return
		}
		relation.ParentAppId, err = strconv.Atoi(apps[0])
		if err != nil {
			return
		}
		err = source.DB().Where("app_id = ? and parent_app_id = ? and app_user_id = 0", relation.AppID, relation.ParentAppId).Delete(&model2.OpenTbAccessToken{}).Error
		if err != nil {
			return
		}
		err = source.DB().Create(&relation).Error
		if err != nil {
			return
		}
	} else if len(apps) == 3 {
		//商城的用户
		var relation model2.OpenTbAccessToken
		relation.RelationId = relationId
		relation.AccessToken = tokenResult.AccessToken
		relation.ExpiresIn = &source.LocalTime{Time: time.Unix(time.Now().Unix()+int64(tokenResult.ExpiresIn), 0)}
		relation.AppUserID, err = strconv.Atoi(apps[2])
		if err != nil {
			return
		}
		relation.AppID, err = strconv.Atoi(apps[1])
		if err != nil {
			return
		}
		relation.ParentAppId, err = strconv.Atoi(apps[0])
		if err != nil {
			return
		}
		err = source.DB().Where("app_id = ? and parent_app_id = ? and app_user_id = ?", relation.AppID, relation.ParentAppId, relation.AppUserID).Delete(&model2.OpenTbAccessToken{}).Error
		if err != nil {
			return
		}
		err = source.DB().Create(&relation).Error
		if err != nil {
			return
		}
	}

	return
}

func GetOpenTbAccessTokensByAppIDs(appID uint) (tokens []model2.OpenTbAccessToken, err error) {
	err = source.DB().Where("parent_app_id = ?", appID).Find(&tokens).Error
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("查询 AccessToken 列表失败: " + err.Error())
		}

		return []model2.OpenTbAccessToken{}, nil
	}
	return tokens, nil
}

// GetOpenTbAccessTokenList 获取AccessToken列表（支持分页和按更新时间查询）
func GetOpenTbAccessTokenList(req OpenTbAccessTokenListRequest) (error, OpenTbAccessTokenListResult) {
	var result OpenTbAccessTokenListResult

	// 构建查询
	db := source.DB().Model(&model2.OpenTbAccessToken{})

	// 添加中台ID过滤
	if req.ParentAppID > 0 {
		db = db.Where("parent_app_id = ?", req.ParentAppID)
	}

	// 添加商城ID过滤
	if req.AppID > 0 {
		db = db.Where("app_id = ?", req.AppID)
	}

	// 添加商城会员ID过滤
	if req.AppUserID > 0 {
		db = db.Where("app_user_id = ?", req.AppUserID)
	}

	// 添加时间范围过滤
	if req.StartTime != "" {
		db = db.Where("updated_at >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		db = db.Where("updated_at <= ?", req.EndTime)
	}

	// 获取总数
	var total int64
	db.Count(&total)

	// 分页查询
	var dbTokens []model2.OpenTbAccessToken
	err := db.Order("updated_at DESC").Limit(req.PageSize).Offset((req.Page - 1) * req.PageSize).Find(&dbTokens).Error
	if err != nil {
		return fmt.Errorf("查询AccessToken列表失败: %v", err), result
	}

	// 设置结果
	result.List = dbTokens
	result.Total = total
	result.Page = req.Page
	result.PageSize = req.PageSize

	return nil, result
}

func GetSetting() (err error, data interface{}) {
	err, setting := model2.GetCpsSetting()
	if err != nil {
		return
	}
	var dataMap = make(map[string]interface{})
	dataMap["client_id"] = setting.OpenTaobaoAppKey
	dataMap["redirect_uri"] = setting.RedirectUriHost + "/supplyapi/ecCps/opentbRedirectUri"
	dataMap["pid"] = setting.OpenTaobaoPid
	data = dataMap
	return
}

func GetApiKey() (err error, data interface{}) {
	err, setting := model2.GetCpsSetting()
	if err != nil {
		return
	}
	var dataMap = make(map[string]interface{})
	dataMap["api_key"] = setting.ApiKey
	data = dataMap
	return
}
