package service

import "ec-cps-ctrl/model"

// OpenTbAccessTokenListRequest 获取AccessToken列表请求参数
type OpenTbAccessTokenListRequest struct {
	Page        int    `form:"page" json:"page" binding:"required"`           // 页码
	PageSize    int    `form:"page_size" json:"page_size" binding:"required"` // 每页数量
	StartTime   string `form:"start_time" json:"start_time"`                  // 开始时间
	EndTime     string `form:"end_time" json:"end_time"`                      // 结束时间
	AppID       int    `form:"app_id" json:"app_id"`                          // 商城ID
	AppUserID   int    `form:"app_user_id" json:"app_user_id"`                // 商城会员ID
	ParentAppID int    `form:"parent_app_id" json:"parent_app_id"`            // 中台ID
}

// OpenTbAccessTokenListResult 获取AccessToken列表结果
type OpenTbAccessTokenListResult struct {
	List     []model.OpenTbAccessToken `json:"list"`      // AccessToken列表
	Total    int64                     `json:"total"`     // 总数
	Page     int                       `json:"page"`      // 当前页码
	PageSize int                       `json:"page_size"` // 每页数量
}

// OpenTbAccessToken 用于API响应的AccessToken结构
type OpenTbAccessToken struct {
	ID          uint   `json:"id"`
	AppID       int    `json:"app_id"`        // 商城ID
	ParentAppID int    `json:"parent_app_id"` // 中台ID
	AppUserID   int    `json:"app_user_id"`   // 商城会员ID
	AccessToken string `json:"access_token"`
	ExpiresIn   string `json:"expires_in"`
	RelationID  int    `json:"relation_id"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}
