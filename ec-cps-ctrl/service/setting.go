package service

import (
	"ec-cps-ctrl/model"
	"yz-go/source"
)

// SaveSysSetting
// @author: [piexlmax](https://github.com/piexlmax)
// @function: SaveSysSetting
// @description: 保存SysTop记录
// @param: data *setting.SysSetting
// @return: err error
func SaveCpsSetting(data model.CpsSetting) (err error) {
	if data.ID != 0 {
		err = source.DB().Updates(&data).Error
	} else {
		err = source.DB().Create(&data).Error
	}
	return err
}
func UpdateCpsSetting(key string, value model.CpsValue) (err error) {
	var settings model.CpsSetting
	err = source.DB().Where("`key` = ?", key).First(&settings).Error
	//if err!=nil{
	//	fmt.Println(err.Error())
	//	return
	//}
	settings.Key = key
	settings.Value = value
	if settings.ID == 0 {
		err = source.DB().Create(&settings).Error
		if err != nil {
			return
		}
	} else {
		err = source.DB().Save(&settings).Error
		if err != nil {
			return
		}
	}
	return
}

func GetCpsSetting() (err error, sysSetting model.CpsSetting) {

	err = source.DB().Where("`key` = ?", "ec_cps_setting").First(&sysSetting).Error

	return
}
