package service

import (
	"ec-cps-ctrl/model"
	"ec-cps-ctrl/request"
	"encoding/json"
	"errors"
	model2 "finance/model"
	"go.uber.org/zap"
	"math"
	"strconv"
	"yz-go/component/log"
	"yz-go/source"
	"yz-go/utils"
)

const tbkBaseURL = "http://api.tbk.dingdanxia.com/tbk/"

// 各个接口实现
func TcGeneralConvert(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(tbkBaseURL+"tc_general_convert", request)
}
func getInviteCode() (code string) {
	err, setting := GetCpsSetting()
	if err != nil {
		return
	}
	if setting.Value.InviteCode != "" {
		code = setting.Value.InviteCode
		return
	} else {
		var requestMap = make(map[string]interface{})
		requestMap["relation_app"] = "common"
		requestMap["code_type"] = 1
		var data interface{}
		err, data = InvitecodeGet(requestMap)
		if err != nil {
			return
		}
		var jsonData []byte
		jsonData, err = json.Marshal(data)
		if err != nil {
			return
		}
		log.Log().Info("invitecode_get result", zap.Any("result", string(jsonData)))

		var response struct {
			Code int    `json:"code"`
			Msg  string `json:"msg"`
			Data struct {
				InviterCode string `json:"inviter_code"`
			} `json:"data"`
		}
		err = json.Unmarshal(jsonData, &response)
		if err != nil {
			return
		}
		if response.Code != 200 {
			err = errors.New(response.Msg)
			return
		}
		code = response.Data.InviterCode
		setting.Value.InviteCode = response.Data.InviterCode
		err = SaveCpsSetting(setting)
	}
	return
}
func PublisherSave(request request.GetTaobaoRelationIdRequest) (err error, relationId int) {
	if request.AccessToken == "" {
		return errors.New("access_token不能为空"), 0
	}
	err, setting := model.GetCpsSetting()
	if err != nil {
		return
	}
	var requestParamsMap = make(map[string]interface{})
	requestParamsMap["inviter_code"] = getInviteCode()
	requestParamsMap["info_type"] = 1
	requestParamsMap["app_key"] = setting.OpenTaobaoAppKey
	requestParamsMap["app_secret"] = setting.OpenTaobaoAppSecret
	requestParamsMap["access_token"] = request.AccessToken
	var responseStructConfirm struct {
		Code int         `json:"code"`
		Msg  string      `json:"msg"`
		Data interface{} `json:"data"`
	}
	var responseStruct struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			AccountName string `json:"account_name"`
			Desc        string `json:"desc"`
			RelationId  int    `json:"relation_id"`
		} `json:"data"`
	}

	requestParamsMap["apikey"] = setting.ApiKey
	var requestParams string
	for k, v := range requestParamsMap {
		requestParams += k + "=" + source.Strval(v) + "&"
	}

	var result []byte
	err, result = utils.Post("http://api.tbk.dingdanxia.com/tbk/publisher_save?"+requestParams, request, nil)
	if err != nil {
		return
	}
	log.Log().Info("publisher_save result", zap.Any("result", string(result)))
	err = json.Unmarshal(result, &responseStructConfirm)
	if err != nil {
		return
	}
	if responseStructConfirm.Code != 200 {
		err = errors.New(responseStructConfirm.Msg)
		return
	}
	err = json.Unmarshal(result, &responseStruct)
	if err != nil {
		return
	}
	relationId = responseStruct.Data.RelationId
	return
}

func IdPrivilege(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(tbkBaseURL+"id_privilege", request)
}

func TklPrivilege(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(tbkBaseURL+"tkl_privilege", request)
}

func ShopConvert(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(tbkBaseURL+"shop_convert", request)
}

func OrderDetails(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(tbkBaseURL+"order_details", request)
}

func ScPunishOrder(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(tbkBaseURL+"sc_punish_order", request)
}

func InvitecodeGet(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(tbkBaseURL+"invitecode_get", request)
}

func SuperSearchMaterial(request map[string]interface{}, userID uint) (err error, response interface{}) {
	err, result := TbxRequest(tbkBaseURL+"super_search_material", request)
	if err != nil {
		return
	}

	var materialResponse TbkMaterialRecommendResponse
	jsonData, err := json.Marshal(result)
	if err != nil {
		return
	}

	err = json.Unmarshal(jsonData, &materialResponse)
	if err != nil {
		return
	}
	var userModel model2.User
	err = source.DB().Preload("UserLevelInfo").First(&userModel, userID).Error
	if err != nil {
		return
	}
	for key, item := range materialResponse.Data {
		commissionAmount, err := strconv.ParseFloat(item.PublishInfo.IncomeInfo.CommissionAmount, 64)
		if err != nil {
			continue // 如果转换失败，跳过当前项
		}
		// 将佣金金额从字符串转换为float64
		commissionRate, err := strconv.ParseFloat(item.PublishInfo.IncomeInfo.CommissionRate, 64)
		if err != nil {
			continue // 如果转换失败，跳过当前项
		}

		// 计算新的佣金金额
		newCommissionAmount := commissionAmount * float64(userModel.UserLevelInfo.CpsRatio) / 10000
		newCommissionRate := commissionRate * float64(userModel.UserLevelInfo.CpsRatio) / 10000

		// 将计算结果转回字符串，保留两位小数
		materialResponse.Data[key].EcCpsInfo.CommissionAmount = newCommissionAmount
		materialResponse.Data[key].EcCpsInfo.CommissionRate = newCommissionRate
		materialResponse.Data[key].PublishInfo.IncomeInfo.CommissionAmount = ""
		materialResponse.Data[key].PublishInfo.IncomeInfo.CommissionRate = ""
		materialResponse.Data[key].PublishInfo.IncomeRate = ""
	}
	return nil, materialResponse
}

func TbItemInfo(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(tbkBaseURL+"item_info", request)
}

type TbkMaterialRecommendResponse struct {
	Code int `json:"code"`
	Data []struct {
		ItemBasicInfo struct {
			AnnualVol            string `json:"annual_vol"`
			BrandName            string `json:"brand_name"`
			CategoryId           int    `json:"category_id"`
			CategoryName         string `json:"category_name"`
			LevelOneCategoryId   int    `json:"level_one_category_id"`
			LevelOneCategoryName string `json:"level_one_category_name"`
			PictUrl              string `json:"pict_url"`
			RealPostFee          string `json:"real_post_fee"`
			SellerId             int64  `json:"seller_id"`
			ShopTitle            string `json:"shop_title"`
			ShortTitle           string `json:"short_title"`
			SmallImages          struct {
				String []string `json:"string"`
			} `json:"small_images"`
			SubTitle   string `json:"sub_title"`
			Title      string `json:"title"`
			UserType   int    `json:"user_type"`
			Volume     int    `json:"volume"`
			WhiteImage string `json:"white_image"`
		} `json:"item_basic_info"`
		ItemId      string `json:"item_id"`
		PresaleInfo struct {
			PresaleDeposit string `json:"presale_deposit"`
		} `json:"presale_info"`
		PricePromotionInfo struct {
			FinalPromotionPathList struct {
				FinalPromotionPathMapData []struct {
					PromotionDesc      string `json:"promotion_desc"`
					PromotionEndTime   string `json:"promotion_end_time"`
					PromotionFee       string `json:"promotion_fee"`
					PromotionId        string `json:"promotion_id"`
					PromotionStartTime string `json:"promotion_start_time"`
					PromotionTitle     string `json:"promotion_title"`
				} `json:"final_promotion_path_map_data"`
			} `json:"final_promotion_path_list"`
			FinalPromotionPrice string `json:"final_promotion_price"`
			PromotionTagList    struct {
				PromotionTagMapData []struct {
					TagName string `json:"tag_name"`
				} `json:"promotion_tag_map_data"`
			} `json:"promotion_tag_list"`
			ReservePrice string `json:"reserve_price"`
			ZkFinalPrice string `json:"zk_final_price"`
		} `json:"price_promotion_info"`
		PublishInfo struct {
			ClickUrl            string `json:"click_url"`
			DailyPromotionSales int    `json:"daily_promotion_sales"`
			IncomeInfo          struct {
				CommissionAmount string `json:"commission_amount"`
				CommissionRate   string `json:"commission_rate"`
				SubsidyAmount    string `json:"subsidy_amount"`
				SubsidyRate      string `json:"subsidy_rate"`
			} `json:"income_info"`
			IncomeRate            string `json:"income_rate"`
			TwoHourPromotionSales int    `json:"two_hour_promotion_sales"`
		} `json:"publish_info"`
		ScopeInfo struct {
			SuperiorBrand interface{} `json:"superior_brand"`
		} `json:"scope_info"`
		EcCpsInfo struct {
			CommissionAmount float64 `json:"commission_amount"`
			CommissionRate   float64 `json:"commission_rate"`
		} `json:"ec_cps_info"`
	} `json:"data"`
	IsDefault    string `json:"is_default"`
	Msg          string `json:"msg"`
	TotalResults int    `json:"total_results"`
}

func MaterialRecommend(request map[string]interface{}, userID uint) (err error, response interface{}) {
	//strings.Trim(request["param"].(string), " ")
	err, result := TbxRequest(tbkBaseURL+"material_recommend", request)
	if err != nil {
		return
	}

	var materialResponse TbkMaterialRecommendResponse
	jsonData, err := json.Marshal(result)
	if err != nil {
		return
	}

	err = json.Unmarshal(jsonData, &materialResponse)
	if err != nil {
		return
	}
	var userModel model2.User
	err = source.DB().Preload("UserLevelInfo").First(&userModel, userID).Error
	if err != nil {
		return
	}
	for key, item := range materialResponse.Data {
		// 将佣金金额从字符串转换为float64
		commissionAmount, err := strconv.ParseFloat(item.PublishInfo.IncomeInfo.CommissionAmount, 64)
		if err != nil {
			continue // 如果转换失败，跳过当前项
		}
		// 将佣金金额从字符串转换为float64
		commissionRate, err := strconv.ParseFloat(item.PublishInfo.IncomeInfo.CommissionRate, 64)
		if err != nil {
			continue // 如果转换失败，跳过当前项
		}

		// 计算新的佣金金额并保留两位小数
		newCommissionAmount := math.Round(commissionAmount * float64(userModel.UserLevelInfo.CpsRatio) / 10000)
		newCommissionRate := math.Round(commissionRate * float64(userModel.UserLevelInfo.CpsRatio) / 10000)

		// 将计算结果转回字符串，保留两位小数
		materialResponse.Data[key].EcCpsInfo.CommissionAmount = newCommissionAmount
		materialResponse.Data[key].EcCpsInfo.CommissionRate = newCommissionRate
		materialResponse.Data[key].PublishInfo.IncomeInfo.CommissionAmount = ""
		materialResponse.Data[key].PublishInfo.IncomeInfo.CommissionRate = ""
		materialResponse.Data[key].PublishInfo.IncomeRate = ""
	}
	return nil, materialResponse
}
