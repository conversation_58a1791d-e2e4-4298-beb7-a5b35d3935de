package service

import (
	"ec-cps-ctrl/model"
	"encoding/csv"
	"encoding/json"
	model2 "finance/model"
	"fmt"
	"github.com/go-resty/resty/v2"
	"github.com/spf13/cast"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"os"
	"path/filepath"
	"strconv"
	"time"
	"yz-go/component/log"
	"yz-go/source"
)

// 淘宝订单列表请求参数
type TaobaoOrderListRequest struct {
	Page            int    `form:"page" json:"page" binding:"required"`           // 页码
	PageSize        int    `form:"page_size" json:"page_size" binding:"required"` // 每页数量
	TradeId         string `form:"trade_id" json:"trade_id"`                      // 订单号
	Status          *int   `form:"status" json:"status"`                          // 订单状态
	StartTime       string `form:"start_time" json:"start_time"`                  // 开始时间
	EndTime         string `form:"end_time" json:"end_time"`                      // 结束时间
	ParentAppID     uint   `json:"parent_app_id"`
	ParentAppUserID uint   `json:"parent_app_user_id"` // 中台ID

}

// 淘宝订单统计数据
type TaobaoOrderStatistic struct {
	Total      int64 `json:"total"`       // 总订单数
	WaitSettle int64 `json:"wait_settle"` // 待结算订单数
	Settled    int64 `json:"settled"`     // 已结算订单数
	Invalid    int64 `json:"invalid"`     // 无效/已失效订单数
}

// 淘宝订单列表结果
type TaobaoOrderListResult struct {
	Orders    interface{}          `json:"orders"`    // 订单列表（包含分页信息）
	Statistic TaobaoOrderStatistic `json:"statistic"` // 统计数据
}
type TaobaoOrder struct {
	source.Model
	TradeId            string  `json:"trade_id" gorm:"column:trade_id;index"`
	TradeParentId      string  `json:"trade_parent_id" gorm:"column:trade_parent_id"`
	ItemId             string  `json:"item_id" gorm:"column:item_id"`
	ItemTitle          string  `json:"item_title" gorm:"column:item_title;type:varchar(255)"`
	ItemImg            string  `json:"item_img" gorm:"column:item_img;type:varchar(255)"`
	ItemPrice          string  `json:"item_price" gorm:"column:item_price"`
	ItemNum            int     `json:"item_num" gorm:"column:item_num"`
	TkStatus           int     `json:"tk_status" gorm:"column:tk_status"`
	OrderType          string  `json:"order_type" gorm:"column:order_type"`
	FlowSource         string  `json:"flow_source" gorm:"column:flow_source"`
	TkCreateTime       string  `json:"tk_create_time" gorm:"column:tk_create_time"`
	TkPaidTime         string  `json:"tk_paid_time" gorm:"column:tk_paid_time"`
	TkEarningTime      string  `json:"tk_earning_time" gorm:"column:tk_earning_time"`
	AlipayTotalPrice   string  `json:"alipay_total_price" gorm:"column:alipay_total_price"`
	PubShareFee        string  `json:"pub_share_fee" gorm:"column:pub_share_fee"`
	PubShareCommission float64 `json:"pub_share_commission" gorm:"-"`
	SellerShopTitle    string  `json:"seller_shop_title" gorm:"column:seller_shop_title;type:varchar(255)"`
	IncomeRate         string  `json:"income_rate" gorm:"column:income_rate"`
	PubId              string  `json:"pub_id" gorm:"column:pub_id"`
	UnId               string  `json:"unid" gorm:"column:unid"`
	SiteId             string  `json:"site_id" gorm:"column:site_id"`
	AdZoneId           string  `json:"adzone_id" gorm:"column:adzone_id"`
	SiteName           string  `json:"site_name" gorm:"column:site_name;type:varchar(255)"`
	AdzoneName         string  `json:"adzone_name" gorm:"column:adzone_name;type:varchar(255)"`
	RefundTag          int     `json:"refund_tag" gorm:"column:refund_tag"`
	TerminalType       string  `json:"terminal_type" gorm:"column:terminal_type"`
	ClickTime          string  `json:"click_time" gorm:"column:click_time"`
	TkTotalRate        string  `json:"tk_total_rate" gorm:"column:tk_total_rate"`
	ItemCategoryName   string  `json:"item_category_name" gorm:"column:item_category_name;type:varchar(255)"`
	SellerNick         string  `json:"seller_nick" gorm:"column:seller_nick;type:varchar(255)"`
	SpecialId          string  `json:"special_id" gorm:"column:special_id"`
	RelationId         string  `json:"relation_id" gorm:"column:relation_id"`
	AppID              int     `json:"app_id"`        // 商城ID
	ParentAppID        int     `json:"parent_app_id"` // 中台ID
	AppUserID          int     `json:"app_user_id"`   // 商城会员ID
	// 额外字段
	SyncTime      time.Time `json:"sync_time" gorm:"column:sync_time"`                     // 同步时间
	ProcessStatus int       `json:"process_status" gorm:"column:process_status;default:0"` // 处理状态：0-未处理，1-已处理
}

func (TaobaoOrder) TableName() string {
	return "ec_cps_taobao_orders"
}

// GetTaobaoOrderList 获取淘宝订单列表
func GetTaobaoOrderList(req TaobaoOrderListRequest) (error, TaobaoOrderListResult) {
	var result TaobaoOrderListResult

	// 构建查询
	db := source.DB().Model(&model.TaobaoOrder{})

	db = db.Where("parent_app_id = ?", req.ParentAppID)

	// 条件查询
	if req.TradeId != "" {
		db = db.Where("trade_id = ?", req.TradeId)
	}
	if req.Status != nil {
		db = db.Where("tk_status = ?", *req.Status)
	}
	if req.StartTime != "" {
		db = db.Where("sync_time >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		db = db.Where("sync_time <= ?", req.EndTime)
	}

	// 统计数据
	var statistic TaobaoOrderStatistic
	// 总订单数
	db.Count(&statistic.Total)
	// 待结算订单数 (状态为12-付款，且未维权的订单)
	source.DB().Model(&model.TaobaoOrder{}).Where("tk_status = ? AND refund_tag = ?", 12, 0).Count(&statistic.WaitSettle)
	// 已结算订单数 (状态为3-结算)
	source.DB().Model(&model.TaobaoOrder{}).Where("tk_status = ?", 3).Count(&statistic.Settled)
	// 无效/已失效订单数 (状态为13-失效)
	source.DB().Model(&model.TaobaoOrder{}).Where("tk_status = ?", 13).Count(&statistic.Invalid)

	// 分页查询
	var list []TaobaoOrder
	err := db.Order("sync_time DESC").Limit(req.PageSize).Offset((req.Page - 1) * req.PageSize).Find(&list).Error
	if err != nil {
		return fmt.Errorf("查询淘宝订单列表失败: %v", err), result
	}
	var userModel model2.User
	err = source.DB().Preload("UserLevelInfo").First(&userModel, req.ParentAppUserID).Error
	if err != nil {
		return fmt.Errorf("查询taobao订单列表失败: %v", err), result
	}
	if req.ParentAppID > 0 {
		for key, item := range list {
			// 将计算结果转回字符串，保留两位小数
			newCommissionAmount := cast.ToFloat64(item.PubShareFee)
			list[key].PubShareCommission = newCommissionAmount * float64(userModel.UserLevelInfo.CpsRatio) / 10000
			list[key].PubShareFee = ""
		}
	}

	// 返回结果
	pageResult := map[string]interface{}{
		"list":      list,
		"total":     statistic.Total,
		"page":      req.Page,
		"page_size": req.PageSize,
	}

	result.Orders = pageResult
	result.Statistic = statistic

	return nil, result
}

// GetTaobaoOrderDetail 获取淘宝订单详情
func GetTaobaoOrderDetail(id int) (error, model.TaobaoOrder) {
	var order model.TaobaoOrder
	err := source.DB().Where("id = ?", id).First(&order).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("订单不存在"), order
		}
		return fmt.Errorf("查询淘宝订单详情失败: %v", err), order
	}
	return nil, order
}

// ExportTaobaoOrders 导出淘宝订单到CSV文件
func ExportTaobaoOrders(orders []model.TaobaoOrder) error {
	if len(orders) == 0 {
		return fmt.Errorf("没有数据可导出")
	}

	// 创建导出目录
	exportDir := "./export"
	if _, err := os.Stat(exportDir); os.IsNotExist(err) {
		err = os.MkdirAll(exportDir, 0755)
		if err != nil {
			log.Log().Error("创建导出目录失败", zap.Error(err))
			return err
		}
	}

	// 创建CSV文件
	timestamp := time.Now().Format("20060102150405")
	filename := filepath.Join(exportDir, fmt.Sprintf("taobao_orders_%s.csv", timestamp))
	file, err := os.Create(filename)
	if err != nil {
		log.Log().Error("创建CSV文件失败", zap.Error(err))
		return err
	}
	defer file.Close()

	// 写入UTF-8 BOM，以便Excel正确显示中文
	_, err = file.Write([]byte{0xEF, 0xBB, 0xBF})
	if err != nil {
		log.Log().Error("写入BOM失败", zap.Error(err))
		return err
	}

	// 创建CSV写入器
	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 写入表头
	headers := []string{
		"订单号", "父订单号", "商品ID", "商品标题", "商品主图", "商品单价",
		"商品数量", "订单状态", "订单类型", "流量来源", "订单创建时间",
		"订单付款时间", "结算时间", "付款金额", "预估收入", "店铺名称",
		"佣金比例", "推广者ID", "推广位ID", "媒体ID", "广告位ID",
		"媒体名称", "广告位名称", "是否维权", "终端类型", "点击时间",
		"佣金比率", "佣金金额", "总佣金比率", "总佣金金额", "商品类目名称",
		"卖家昵称", "会员运营ID", "渠道关系ID", "同步时间", "处理状态",
	}
	err = writer.Write(headers)
	if err != nil {
		log.Log().Error("写入CSV表头失败", zap.Error(err))
		return err
	}

	// 获取订单状态映射
	statusMap := getOrderStatusMap()

	// 写入数据行
	for _, order := range orders {
		// 格式化时间
		syncTime := ""
		if !order.SyncTime.IsZero() {
			syncTime = order.SyncTime.Format("2006-01-02 15:04:05")
		}

		// 获取状态文本
		statusText := statusMap[order.TkStatus]

		// 是否维权
		refundTag := "否"
		if order.RefundTag == 1 {
			refundTag = "是"
		}

		row := []string{
			order.TradeId,
			order.TradeParentId,
			order.ItemId,
			order.ItemTitle,
			order.ItemImg,
			order.ItemPrice,
			strconv.Itoa(order.ItemNum),
			statusText,
			order.OrderType,
			order.FlowSource,
			order.TkCreateTime,
			order.TkPaidTime,
			order.TkEarningTime,
			order.AlipayTotalPrice,
			order.PubShareFee,
			order.SellerShopTitle,
			order.IncomeRate,
			order.PubId,
			order.UnId,
			order.SiteId,
			order.AdZoneId,
			order.SiteName,
			order.AdzoneName,
			refundTag,
			order.TerminalType,
			order.ClickTime,
			order.TkCommissionRate,
			order.TkCommissionFee,
			order.TkTotalRate,
			order.TotalCommissionFee,
			order.ItemCategoryName,
			order.SellerNick,
			order.SpecialId,
			order.RelationId,
			syncTime,
		}

		err = writer.Write(row)
		if err != nil {
			log.Log().Error("写入CSV数据行失败", zap.Error(err))
			return err
		}
	}

	log.Log().Info("淘宝订单导出成功", zap.String("文件名", filename), zap.Int("记录数", len(orders)))
	return nil
}

// getOrderStatusMap 获取订单状态映射
func getOrderStatusMap() map[int]string {
	return map[int]string{
		3:  "已结算",
		12: "已付款",
		13: "已失效",
		14: "已成功",
	}
}

// ExportTaobaoOrdersByQuery 根据查询条件导出淘宝订单
func ExportTaobaoOrdersByQuery(req TaobaoOrderListRequest) error {
	// 构建查询
	db := source.DB().Model(&model.TaobaoOrder{})

	// 条件查询
	if req.TradeId != "" {
		db = db.Where("trade_id = ?", req.TradeId)
	}
	if req.Status != nil {
		db = db.Where("tk_status = ?", *req.Status)
	}
	if req.StartTime != "" {
		db = db.Where("tk_create_time >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		db = db.Where("tk_create_time <= ?", req.EndTime)
	}

	// 查询数据
	var list []model.TaobaoOrder
	err := db.Order("sync_time DESC").Find(&list).Error
	if err != nil {
		return fmt.Errorf("查询淘宝订单列表失败: %v", err)
	}

	// 导出数据
	return ExportTaobaoOrders(list)
}

// SyncOrdersToDatabase 同步淘宝订单到数据库
func SyncOrdersToDatabase(orders []model.TaobaoOrder) error {
	if len(orders) == 0 {
		return nil
	}

	log.Log().Info("开始同步淘宝订单到数据库", zap.Int("订单数量", len(orders)))

	// 收集所有订单ID
	var tradeIds []string
	for _, order := range orders {
		tradeIds = append(tradeIds, order.TradeId)
	}

	// 查询数据库中已存在的订单
	var existingOrders []model.TaobaoOrder
	result := source.DB().Where("trade_id IN ?", tradeIds).Find(&existingOrders)
	if result.Error != nil && result.Error != gorm.ErrRecordNotFound {
		log.Log().Error("查询现有订单失败", zap.Error(result.Error))
		return result.Error
	}

	// 将现有订单映射到map中便于快速查找
	existingOrderMap := make(map[string]model.TaobaoOrder)
	for _, order := range existingOrders {
		existingOrderMap[order.TradeId] = order
	}

	// 准备新订单和需要更新的订单
	var newOrders []model.TaobaoOrder
	var updateOrders []model.TaobaoOrder

	for _, order := range orders {
		// 设置同步时间
		order.SyncTime = time.Now()

		if _, exists := existingOrderMap[order.TradeId]; !exists {
			// 新订单
			newOrders = append(newOrders, order)
		} else {
			// 需要更新的订单
			dbOrder := existingOrderMap[order.TradeId]
			dbOrder.TkStatus = order.TkStatus
			dbOrder.RefundTag = order.RefundTag
			dbOrder.TkEarningTime = order.TkEarningTime
			dbOrder.PubShareFee = order.PubShareFee
			dbOrder.TkCommissionFee = order.TkCommissionFee
			dbOrder.TotalCommissionFee = order.TotalCommissionFee
			dbOrder.SyncTime = time.Now()
			updateOrders = append(updateOrders, dbOrder)
		}
	}

	// 批量创建新订单
	if len(newOrders) > 0 {
		// 分批插入以避免一次插入过多数据
		batchSize := 50
		for i := 0; i < len(newOrders); i += batchSize {
			end := i + batchSize
			if end > len(newOrders) {
				end = len(newOrders)
			}

			batch := newOrders[i:end]
			if err := source.DB().Create(&batch).Error; err != nil {
				log.Log().Error("批量创建订单失败", zap.Error(err))
				return err
			}
		}
		log.Log().Info("成功创建新订单", zap.Int("数量", len(newOrders)))
	}

	// 批量更新订单
	if len(updateOrders) > 0 {
		// 分批更新
		batchSize := 50
		for i := 0; i < len(updateOrders); i += batchSize {
			end := i + batchSize
			if end > len(updateOrders) {
				end = len(updateOrders)
			}

			batch := updateOrders[i:end]
			for _, order := range batch {
				// 使用条件更新来避免并发问题
				if err := source.DB().Model(&model.TaobaoOrder{}).Clauses(clause.Returning{}).Where("trade_id = ?", order.TradeId).Updates(map[string]interface{}{
					"tk_status":            order.TkStatus,
					"refund_tag":           order.RefundTag,
					"tk_earning_time":      order.TkEarningTime,
					"pub_share_fee":        order.PubShareFee,
					"tk_commission_fee":    order.TkCommissionFee,
					"total_commission_fee": order.TotalCommissionFee,
					"sync_time":            time.Now(),
				}).Error; err != nil {
					log.Log().Error("更新订单失败", zap.String("订单ID", order.TradeId), zap.Error(err))
					return err
				}
			}
		}
		log.Log().Info("成功更新订单", zap.Int("数量", len(updateOrders)))
	}

	return nil
}

// 淘宝订单查询请求参数
type TaobaoOrderQueryRequest struct {
	ApiKey        string `json:"apikey" form:"apikey"`                            // API密钥
	StartTime     string `json:"start_time" form:"start_time" binding:"required"` // 开始时间
	EndTime       string `json:"end_time" form:"end_time" binding:"required"`     // 结束时间
	PageSize      int    `json:"page_size" form:"page_size" binding:"required"`   // 每页数量
	PageNo        int    `json:"page_no" form:"page_no" binding:"required"`       // 页码
	QueryType     string `json:"query_type" form:"query_type"`                    // 查询类型：1-创建时间，2-付款时间，3-结算时间
	TkStatus      string `json:"tk_status" form:"tk_status"`                      // 订单状态：12-付款，13-失效，14-成功，3-结算
	PositionIndex string `json:"position_index" form:"position_index"`            // 分页游标
	JumpType      int    `json:"jump_type" form:"jump_type"`                      // 跳转类型：-1上一页，1下一页
	OrderScene    int    `json:"order_scene" form:"order_scene"`                  // 订单场景：1-全部，2-活动游戏，3-快捷游戏
}

// 淘宝订单查询响应结构
type TaobaoOrderQueryResponse struct {
	Code          int                 `json:"code"`
	Msg           string              `json:"msg"`
	HasNext       bool                `json:"has_next"`
	HasPre        bool                `json:"has_pre"`
	PageNo        int                 `json:"page_no"`
	PageSize      int                 `json:"page_size"`
	PositionIndex string              `json:"position_index"`
	Data          []model.TaobaoOrder `json:"data"`
}

// QueryTaobaoOrders 直接查询淘宝订单接口
func QueryTaobaoOrders(req TaobaoOrderQueryRequest) (err error, resp TaobaoOrderQueryResponse, syncErr error) {
	// 获取API密钥
	if req.ApiKey == "" {
		err, setting := GetCpsSetting()
		if err != nil {
			return err, resp, nil
		}
		req.ApiKey = setting.Value.ApiKey
	}

	// 设置默认值
	if req.QueryType == "" {
		req.QueryType = "1" // 默认按创建时间查询
	}
	if req.TkStatus == "" {
		req.TkStatus = "12" // 默认查询付款状态的订单
	}

	// 创建HTTP客户端
	client := resty.New().SetRetryCount(3).SetRetryWaitTime(time.Second * 2)

	// 构建查询参数
	queryParams := map[string]string{
		"apikey":     req.ApiKey,
		"start_time": req.StartTime,
		"end_time":   req.EndTime,
		"page_size":  fmt.Sprintf("%d", req.PageSize),
		"page_no":    fmt.Sprintf("%d", req.PageNo),
		"query_type": req.QueryType,
		"tk_status":  req.TkStatus,
	}

	// 添加可选参数
	if req.PositionIndex != "" {
		queryParams["position_index"] = req.PositionIndex
	}
	if req.JumpType != 0 {
		queryParams["jump_type"] = fmt.Sprintf("%d", req.JumpType)
	}
	if req.OrderScene != 0 {
		queryParams["order_scene"] = fmt.Sprintf("%d", req.OrderScene)
	}

	// 发送请求
	response, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetQueryParams(queryParams).
		Post("http://api.tbk.dingdanxia.com/tbk/order_details")

	if err != nil {
		return fmt.Errorf("请求淘宝订单接口失败: %v", err), resp, nil
	}

	// 解析响应
	if err := json.Unmarshal(response.Body(), &resp); err != nil {
		return fmt.Errorf("解析响应数据失败: %v", err), resp, nil
	}

	// 检查响应状态
	if resp.Code != 200 {
		return fmt.Errorf("接口返回错误: %s", resp.Msg), resp, nil
	}

	// 同步订单到数据库（异步处理）
	go func() {
		syncErr = SyncOrdersToDatabase(resp.Data)
	}()

	return nil, resp, syncErr
}
