package service

import (
	"ec-cps-ctrl/model"
	"encoding/json"
	"yz-go/source"
	"yz-go/utils"
)

// 通用请求处理函数
func TbxRequest(apiUrl string, request map[string]interface{}) (err error, response interface{}) {
	var setting model.CpsValue
	err, setting = model.GetCpsSetting()
	if err != nil {
		return
	}
	request["apikey"] = setting.ApiKey
	var requestParams string
	for k, v := range request {
		requestParams += k + "=" + source.Strval(v) + "&"
	}

	var result []byte
	err, result = utils.Post(apiUrl+"?"+requestParams, nil, nil)
	if err != nil {
		return
	}
	err = json.Unmarshal(result, &response)
	return
}
