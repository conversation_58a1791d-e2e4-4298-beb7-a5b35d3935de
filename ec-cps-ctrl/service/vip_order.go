package service

import (
	"ec-cps-ctrl/model"
	"encoding/json"
	model2 "finance/model"
	"fmt"
	"github.com/spf13/cast"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"yz-go/component/log"
	"yz-go/source"
	"yz-go/utils"
)

// 唯品会订单列表请求参数
type VipOrderListRequest struct {
	Page            int    `form:"page" json:"page" binding:"required"`           // 页码
	PageSize        int    `form:"page_size" json:"page_size" binding:"required"` // 每页数量
	OrderSn         string `form:"order_sn" json:"order_sn"`                      // 订单号
	Status          *int16 `form:"status" json:"status"`                          // 订单状态
	StartTime       string `form:"start_time" json:"start_time"`                  // 开始时间
	EndTime         string `form:"end_time" json:"end_time"`                      // 结束时间
	ParentAppID     uint   `form:"parent_app_id" json:"parent_app_id"`            // 中台ID
	ParentAppUserID uint   ` json:"parent_app_user_id"`                           // 中台ID

}

// 唯品会订单列表结果
type VipOrderListResult struct {
	List      []VipOrder        `json:"list"`      // 订单列表
	Total     int64             `json:"total"`     // 总数
	Page      int               `json:"page"`      // 当前页码
	PageSize  int               `json:"page_size"` // 每页数量
	Statistic VipOrderStatistic `json:"statistic"` // 统计数据
	Status    map[int16]string  `json:"status"`    // 状态映射
}

// 唯品会订单统计
type VipOrderStatistic struct {
	Total      int64 `json:"total"`       // 总订单数
	WaitSettle int64 `json:"wait_settle"` // 待结算订单数
	Settled    int64 `json:"settled"`     // 已结算订单数
	Invalid    int64 `json:"invalid"`     // 无效/已失效订单数
}

// 唯品会订单查询请求
type VipOrderQueryRequest struct {
	ApiKey          string `form:"apikey" json:"apikey"`                   // API密钥
	UpdateTimeStart string `form:"updateTimeStart" json:"updateTimeStart"` // 开始时间（毫秒时间戳）
	UpdateTimeEnd   string `form:"updateTimeEnd" json:"updateTimeEnd"`     // 结束时间（毫秒时间戳）
	Page            string `form:"page" json:"page"`                       // 页码
	PageSize        string `form:"pageSize" json:"pageSize"`               // 每页数量
}

// 唯品会订单查询响应
type VipOrderQueryResponse struct {
	Code         int                  `json:"code"`
	Msg          string               `json:"msg"`
	TotalResults int                  `json:"total_results"`
	Data         []model.VipOrderData `json:"data"`
}

// 获取订单状态映射
func getVipOrderStatusMap() map[int16]string {
	return map[int16]string{
		0: "不合格",
		1: "待定",
		2: "已完结",
	}
}

type VipOrder struct {
	source.Model
	OrderSn                   string  `json:"order_sn" gorm:"column:order_sn;index"`                                     // 订单号
	Status                    int16   `json:"status" gorm:"column:status"`                                               // 订单状态:0-不合格，1-待定，2-已完结
	NewCustomer               int16   `json:"new_customer" gorm:"column:new_customer"`                                   // 新老客：0-待定，1-新客，2-老客
	ChannelTag                string  `json:"channel_tag" gorm:"column:channel_tag"`                                     // 渠道标识
	OrderTime                 int64   `json:"order_time" gorm:"column:order_time"`                                       // 下单时间 时间戳 单位毫秒
	SignTime                  int64   `json:"sign_time" gorm:"column:sign_time"`                                         // 签收时间 时间戳 单位毫秒
	SettledTime               int64   `json:"settled_time" gorm:"column:settled_time"`                                   // 结算时间 时间戳 单位毫秒
	LastUpdateTime            int64   `json:"last_update_time" gorm:"column:last_update_time"`                           // 订单上次更新时间 时间戳 单位毫秒
	Settled                   int16   `json:"settled" gorm:"column:settled"`                                             // 订单结算状态 0-未结算,1-已结算
	SelfBuy                   int     `json:"self_buy" gorm:"column:self_buy"`                                           // 是否自推自买 0-否，1-是
	OrderSubStatusName        string  `json:"order_sub_status_name" gorm:"column:order_sub_status_name"`                 // 订单子状态：流转状态
	Commission                string  `json:"commission" gorm:"column:commission"`                                       // 商品总佣金:单位元
	ActualCommission          float64 `json:"actual_commission"`                                                         // 商品总佣金:单位元
	AfterSaleChangeCommission string  `json:"after_sale_change_commission" gorm:"column:after_sale_change_commission"`   // 售后订单佣金变动
	AfterSaleChangeGoodsCount int     `json:"after_sale_change_goods_count" gorm:"column:after_sale_change_goods_count"` // 售后订单总商品数量变动
	CommissionEnterTime       int64   `json:"commission_enter_time" gorm:"column:commission_enter_time"`                 // 入账时间，时间戳，单位毫秒
	OrderSource               string  `json:"order_source" gorm:"column:order_source"`                                   // 订单来源
	Pid                       string  `json:"pid" gorm:"column:pid"`                                                     // 推广PID
	IsPrepay                  int     `json:"is_prepay" gorm:"column:is_prepay"`                                         // 是否预付订单:0-否，1-是
	StatParam                 string  `json:"stat_param" gorm:"column:stat_param"`                                       // 自定义统计参数
	TotalCost                 string  `json:"total_cost" gorm:"column:total_cost"`                                       // 订单支付金额:单位元
	OrderTrackReason          int     `json:"order_track_reason" gorm:"column:order_track_reason"`                       // 订单归因方式：0-常规推广,1-惊喜红包,2-锁粉,3-超级红包
	IsSplit                   int     `json:"is_split" gorm:"column:is_split"`                                           // 订单拆单标识: 0-否，1-是
	AppID                     uint    `json:"app_id" gorm:"column:app_id"`                                               // 商城ID
	ParentAppID               uint    `json:"parent_app_id" gorm:"column:parent_app_id"`                                 // 中台ID
	AppUserID                 uint    `json:"app_user_id" gorm:"column:app_user_id"`                                     // 商城会员ID
	// 额外字段
	SyncTime      time.Time        `json:"sync_time" gorm:"column:sync_time"`                     // 同步时间
	ProcessStatus int              `json:"process_status" gorm:"column:process_status;default:0"` // 处理状态：0-未处理，1-已处理
	DetailList    []VipOrderDetail `json:"detail_list" gorm:"-"`                                  // 订单详情，非数据库字段
}

// TableName 设置表名
func (VipOrder) TableName() string {
	return "ec_cps_vip_orders"
}

// VipOrderDetail 唯品会订单商品明细
type VipOrderDetail struct {
	source.Model
	OrderSn                    string `json:"order_sn" gorm:"column:order_sn;index"`                                       // 订单号
	GoodsId                    string `json:"goods_id" gorm:"column:goods_id"`                                             // 商品id
	GoodsName                  string `json:"goods_name" gorm:"column:goods_name;type:varchar(255)"`                       // 商品名称
	GoodsThumb                 string `json:"goods_thumb" gorm:"column:goods_thumb;type:varchar(255)"`                     // 商品缩略图
	GoodsCount                 int    `json:"goods_count" gorm:"column:goods_count"`                                       // 商品数量
	CommissionTotalCost        string `json:"commission_total_cost" gorm:"column:commission_total_cost"`                   // 商品计佣金额
	CommCode                   string `json:"comm_code" gorm:"column:comm_code"`                                           // 佣金编码：对应商品二级分类
	CommName                   string `json:"comm_name" gorm:"column:comm_name"`                                           // 佣金方案名称
	OrderSource                string `json:"order_source" gorm:"column:order_source"`                                     // 订单来源
	AfterSaleChangedCommission string `json:"after_sale_changed_commission" gorm:"column:after_sale_changed_commission"`   // 商品佣金售后变动
	AfterSaleChangedGoodsCount int    `json:"after_sale_changed_goods_count" gorm:"column:after_sale_changed_goods_count"` // 商品数量售后变动
	AfterSaleSn                string `json:"after_sale_sn" gorm:"column:after_sale_sn"`                                   // 商品售后单号
	AfterSaleStatus            int    `json:"after_sale_status" gorm:"column:after_sale_status"`                           // 商品售后状态：1-售后中，2-售后完成，3-售后取消
	AfterSaleType              int    `json:"after_sale_type" gorm:"column:after_sale_type"`                               // 售后类型：1-退货，2-换货
	AfterSaleFinishTime        int64  `json:"after_sale_finish_time" gorm:"column:after_sale_finish_time"`                 // 售后完成时间，时间戳，单位：毫秒
	SizeId                     string `json:"size_id" gorm:"column:size_id"`                                               // 商品尺码
	Status                     int16  `json:"status" gorm:"column:status"`                                                 // 商品状态：0-不合格，1-待定，2-已完结
	GoodsFinalPrice            string `json:"goods_final_price" gorm:"column:goods_final_price"`                           // 商品成交价
	BrandStoreSn               string `json:"brand_store_sn" gorm:"column:brand_store_sn"`                                 // 品牌编号
	BrandStoreName             string `json:"brand_store_name" gorm:"column:brand_store_name"`                             // 品牌名称
	SpuId                      string `json:"spu_id" gorm:"column:spu_id"`                                                 // 商品spuId
}

// TableName 设置表名
func (VipOrderDetail) TableName() string {
	return "ec_cps_vip_order_details"
}

// GetVipOrderList 获取唯品会订单列表
func GetVipOrderList(req VipOrderListRequest) (error, VipOrderListResult) {
	var result VipOrderListResult

	// 构建查询
	db := source.DB().Model(&model.VipOrder{})

	// 添加中台ID过滤
	if req.ParentAppID > 0 {
		db = db.Where("parent_app_id = ?", req.ParentAppID)
	}

	// 条件查询
	if req.OrderSn != "" {
		db = db.Where("order_sn = ?", req.OrderSn)
	}
	if req.Status != nil {
		db = db.Where("status = ?", *req.Status)
	}
	if req.StartTime != "" {
		// 将时间字符串转换为毫秒时间戳
		startTime, err := time.Parse("2006-01-02 15:04:05", req.StartTime)
		if err == nil {
			db = db.Where("sync_time >= ?", startTime.UnixMilli())
		}
	}
	if req.EndTime != "" {
		// 将时间字符串转换为毫秒时间戳
		endTime, err := time.Parse("2006-01-02 15:04:05", req.EndTime)
		if err == nil {
			db = db.Where("sync_time <= ?", endTime.UnixMilli())
		}
	}

	// 统计数据
	var statistic VipOrderStatistic
	// 总订单数
	db.Count(&statistic.Total)
	// 待结算订单数 (状态为1-待定，且已结算状态为0-未结算)
	source.DB().Model(&model.VipOrder{}).Where("status = ? AND settled = ?", 1, 0).Count(&statistic.WaitSettle)
	// 已结算订单数 (已结算状态为1-已结算)
	source.DB().Model(&model.VipOrder{}).Where("settled = ?", 1).Count(&statistic.Settled)
	// 无效/已失效订单数 (状态为0-不合格)
	source.DB().Model(&model.VipOrder{}).Where("status = ?", 0).Count(&statistic.Invalid)

	// 分页查询
	var list []VipOrder
	err := db.Order("sync_time DESC").Limit(req.PageSize).Offset((req.Page - 1) * req.PageSize).Find(&list).Error
	if err != nil {
		return fmt.Errorf("查询唯品会订单列表失败: %v", err), result
	}
	var userModel model2.User
	err = source.DB().Preload("UserLevelInfo").First(&userModel, req.ParentAppUserID).Error
	if err != nil {
		return fmt.Errorf("查询taobao订单列表失败: %v", err), result
	}
	if req.ParentAppID > 0 {
		for key, item := range list {
			newCommissionAmount := cast.ToFloat64(item.Commission)
			list[key].ActualCommission = newCommissionAmount * float64(userModel.UserLevelInfo.CpsRatio) / 10000
			list[key].Commission = ""
		}
	}

	// 设置结果
	result.List = list
	result.Total = statistic.Total
	result.Page = req.Page
	result.PageSize = req.PageSize
	result.Statistic = statistic
	result.Status = getVipOrderStatusMap()

	return nil, result
}

// GetVipOrderDetail 获取唯品会订单详情
func GetVipOrderDetail(id int) (error, model.VipOrder) {
	var order model.VipOrder
	err := source.DB().First(&order, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("订单不存在"), order
		}
		return fmt.Errorf("查询唯品会订单详情失败: %v", err), order
	}

	// 查询订单详情
	var details []model.VipOrderDetail
	err = source.DB().Where("order_sn = ?", order.OrderSn).Find(&details).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		log.Log().Error("查询订单详情失败", zap.Error(err))
	}

	// 将详情添加到订单中
	order.DetailList = details

	return nil, order
}

// ExportVipOrders 导出唯品会订单
func ExportVipOrders(orders []model.VipOrder) error {
	if len(orders) == 0 {
		return fmt.Errorf("没有可导出的订单数据")
	}

	// 这里可以实现导出功能，或者调用已有的导出功能
	// 为简化示例，这里只记录日志
	log.Log().Info("导出唯品会订单", zap.Int("数量", len(orders)))
	return nil
}

// ExportVipOrdersByQuery 根据查询条件导出唯品会订单
func ExportVipOrdersByQuery(req VipOrderListRequest) error {
	// 构建查询
	db := source.DB().Model(&model.VipOrder{})

	// 添加中台ID过滤
	if req.ParentAppID > 0 {
		db = db.Where("parent_app_id = ?", req.ParentAppID)
	}

	// 条件查询
	if req.OrderSn != "" {
		db = db.Where("order_sn = ?", req.OrderSn)
	}
	if req.Status != nil {
		db = db.Where("status = ?", *req.Status)
	}
	if req.StartTime != "" {
		// 将时间字符串转换为毫秒时间戳
		startTime, err := time.Parse("2006-01-02 15:04:05", req.StartTime)
		if err == nil {
			db = db.Where("order_time >= ?", startTime.UnixMilli())
		}
	}
	if req.EndTime != "" {
		// 将时间字符串转换为毫秒时间戳
		endTime, err := time.Parse("2006-01-02 15:04:05", req.EndTime)
		if err == nil {
			db = db.Where("order_time <= ?", endTime.UnixMilli())
		}
	}

	// 查询数据
	var list []model.VipOrder
	err := db.Order("sync_time DESC").Find(&list).Error
	if err != nil {
		return fmt.Errorf("查询唯品会订单列表失败: %v", err)
	}

	// 导出数据
	return ExportVipOrders(list)
}

// QueryVipOrders 直接查询唯品会订单接口
func QueryVipOrders(req VipOrderQueryRequest) (err error, resp VipOrderQueryResponse, syncErr error) {
	// 获取API密钥
	if req.ApiKey == "" {
		err, setting := GetCpsSetting()
		if err != nil {
			return err, resp, nil
		}
		req.ApiKey = setting.Value.ApiKey
	}

	// 设置默认值
	if req.Page == "" {
		req.Page = "1" // 默认第一页
	}
	if req.PageSize == "" {
		req.PageSize = "20" // 默认每页20条
	}

	// 如果未指定时间范围，默认查询最近24小时
	if req.UpdateTimeStart == "" || req.UpdateTimeEnd == "" {
		now := time.Now()
		req.UpdateTimeEnd = fmt.Sprintf("%d", now.UnixMilli())
		req.UpdateTimeStart = fmt.Sprintf("%d", now.Add(-24*time.Hour).UnixMilli())
	}

	// 构建查询参数
	queryParams := map[string]string{
		"apikey":          req.ApiKey,
		"page":            req.Page,
		"pageSize":        req.PageSize,
		"updateTimeStart": req.UpdateTimeStart,
		"updateTimeEnd":   req.UpdateTimeEnd,
	}

	// 创建HTTP客户端
	client := resty.New().SetRetryCount(3).SetRetryWaitTime(2 * time.Second)

	// 发送请求
	response, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetQueryParams(queryParams).
		Post("http://api.tbk.dingdanxia.com/vip/order_details2")

	if err != nil {
		return fmt.Errorf("请求唯品会订单接口失败: %v", err), resp, nil
	}

	// 解析响应
	if err := json.Unmarshal(response.Body(), &resp); err != nil {
		return fmt.Errorf("解析响应数据失败: %v", err), resp, nil
	}

	// 检查响应状态
	if resp.Code != 200 {
		return fmt.Errorf("接口返回错误: %s", resp.Msg), resp, nil
	}

	// 同步订单到数据库（异步处理）
	go func() {
		syncErr = SyncVipOrdersToDatabase(resp.Data)
	}()

	return nil, resp, syncErr
}

// SyncVipOrdersToDatabase 同步唯品会订单到数据库
func SyncVipOrdersToDatabase(orders []model.VipOrderData) error {
	if len(orders) == 0 {
		return nil
	}

	log.Log().Info("开始同步唯品会订单到数据库", zap.Int("订单数量", len(orders)))

	// 收集所有订单ID
	var orderSns []string
	for _, order := range orders {
		orderSns = append(orderSns, order.OrderSn)
	}

	// 查询数据库中已存在的订单
	var existingOrders []model.VipOrder
	result := source.DB().Where("order_sn IN ?", orderSns).Find(&existingOrders)
	if result.Error != nil && result.Error != gorm.ErrRecordNotFound {
		log.Log().Error("查询现有订单失败", zap.Error(result.Error))
		return result.Error
	}

	// 将现有订单映射到map中便于快速查找
	existingOrderMap := make(map[string]model.VipOrder)
	for _, order := range existingOrders {
		existingOrderMap[order.OrderSn] = order
	}

	// 准备新订单和需要更新的订单
	var newOrders []model.VipOrder
	var updateOrders []model.VipOrder
	var newOrderDetails []model.VipOrderDetail
	var existingOrderDetails []model.VipOrderDetail

	// 查询现有订单详情
	result = source.DB().Where("order_sn IN ?", orderSns).Find(&existingOrderDetails)
	if result.Error != nil && result.Error != gorm.ErrRecordNotFound {
		log.Log().Error("查询现有订单详情失败", zap.Error(result.Error))
	}

	// 将现有订单详情映射到map中便于快速查找
	existingOrderDetailMap := make(map[string]map[string]model.VipOrderDetail)
	for _, detail := range existingOrderDetails {
		if _, exists := existingOrderDetailMap[detail.OrderSn]; !exists {
			existingOrderDetailMap[detail.OrderSn] = make(map[string]model.VipOrderDetail)
		}
		existingOrderDetailMap[detail.OrderSn][detail.GoodsId] = detail
	}

	for _, orderData := range orders {
		// 解析StatParam获取AppID、ParentAppID和AppUserID
		var parentAppID, appID, appUserID uint

		// StatParam格式可能是"parentAppID_appID_appUserID"或其他格式
		if orderData.StatParam != "" {
			parts := strings.Split(orderData.StatParam, "_")
			if len(parts) >= 3 {
				parentAppID = utils.StrToUInt(parts[0])
				appID = utils.StrToUInt(parts[1])
				appUserID = utils.StrToUInt(parts[2])
			} else if len(parts) == 2 {
				// 如果只有两部分，假设是"appID_appUserID"
				appID = utils.StrToUInt(parts[0])
				appUserID = utils.StrToUInt(parts[1])
			} else if len(parts) == 1 {
				// 如果只有一部分，尝试解析为appID
				appID = utils.StrToUInt(parts[0])
			}
		}

		// 转换为模型订单
		order := model.VipOrder{
			OrderSn:                   orderData.OrderSn,
			Status:                    orderData.Status,
			NewCustomer:               orderData.NewCustomer,
			ChannelTag:                orderData.ChannelTag,
			OrderTime:                 orderData.OrderTime,
			SignTime:                  orderData.SignTime,
			SettledTime:               orderData.SettledTime,
			LastUpdateTime:            orderData.LastUpdateTime,
			Settled:                   orderData.Settled,
			SelfBuy:                   orderData.SelfBuy,
			OrderSubStatusName:        orderData.OrderSubStatusName,
			Commission:                orderData.Commission,
			AfterSaleChangeCommission: orderData.AfterSaleChangeCommission,
			AfterSaleChangeGoodsCount: orderData.AfterSaleChangeGoodsCount,
			CommissionEnterTime:       orderData.CommissionEnterTime,
			OrderSource:               orderData.OrderSource,
			Pid:                       orderData.Pid,
			IsPrepay:                  orderData.IsPrepay,
			StatParam:                 orderData.StatParam,
			TotalCost:                 orderData.TotalCost,
			OrderTrackReason:          orderData.OrderTrackReason,
			IsSplit:                   orderData.IsSplit,
			ParentAppID:               parentAppID,
			AppID:                     appID,
			AppUserID:                 appUserID,
			SyncTime:                  time.Now(),
		}

		if _, exists := existingOrderMap[order.OrderSn]; !exists {
			// 新订单
			newOrders = append(newOrders, order)

			// 处理订单详情
			for _, detailData := range orderData.DetailList {
				detail := model.VipOrderDetail{
					OrderSn:                    order.OrderSn,
					GoodsId:                    detailData.GoodsId,
					GoodsName:                  detailData.GoodsName,
					GoodsThumb:                 detailData.GoodsThumb,
					GoodsCount:                 detailData.GoodsCount,
					CommissionTotalCost:        detailData.CommissionTotalCost,
					CommissionRate:             detailData.CommissionRate,
					Commission:                 detailData.Commission,
					CommCode:                   detailData.CommCode,
					CommName:                   detailData.CommName,
					OrderSource:                detailData.OrderSource,
					AfterSaleChangedCommission: detailData.AfterSaleChangedCommission,
					AfterSaleChangedGoodsCount: detailData.AfterSaleChangedGoodsCount,
					AfterSaleSn:                detailData.AfterSaleSn,
					AfterSaleStatus:            detailData.AfterSaleStatus,
					AfterSaleType:              detailData.AfterSaleType,
					AfterSaleFinishTime:        detailData.AfterSaleFinishTime,
					SizeId:                     detailData.SizeId,
					Status:                     detailData.Status,
					GoodsFinalPrice:            detailData.GoodsFinalPrice,
					BrandStoreSn:               detailData.BrandStoreSn,
					BrandStoreName:             detailData.BrandStoreName,
					SpuId:                      detailData.SpuId,
				}
				newOrderDetails = append(newOrderDetails, detail)
			}
		} else {
			// 需要更新的订单
			dbOrder := existingOrderMap[order.OrderSn]
			dbOrder.Status = order.Status
			dbOrder.NewCustomer = order.NewCustomer
			dbOrder.SignTime = order.SignTime
			dbOrder.SettledTime = order.SettledTime
			dbOrder.LastUpdateTime = order.LastUpdateTime
			dbOrder.Settled = order.Settled
			dbOrder.OrderSubStatusName = order.OrderSubStatusName
			dbOrder.Commission = order.Commission
			dbOrder.AfterSaleChangeCommission = order.AfterSaleChangeCommission
			dbOrder.AfterSaleChangeGoodsCount = order.AfterSaleChangeGoodsCount
			dbOrder.CommissionEnterTime = order.CommissionEnterTime
			dbOrder.ParentAppID = order.ParentAppID
			dbOrder.AppID = order.AppID
			dbOrder.AppUserID = order.AppUserID
			dbOrder.SyncTime = time.Now()
			updateOrders = append(updateOrders, dbOrder)
		}
	}

	// 批量创建新订单
	if len(newOrders) > 0 {
		// 分批插入以避免一次插入过多数据
		batchSize := 50
		for i := 0; i < len(newOrders); i += batchSize {
			end := i + batchSize
			if end > len(newOrders) {
				end = len(newOrders)
			}

			batch := newOrders[i:end]
			if err := source.DB().Create(&batch).Error; err != nil {
				log.Log().Error("批量创建订单失败", zap.Error(err))
				return err
			}
		}
		log.Log().Info("成功创建新订单", zap.Int("数量", len(newOrders)))
	}

	// 批量创建新订单详情
	if len(newOrderDetails) > 0 {
		// 分批插入以避免一次插入过多数据
		batchSize := 50
		for i := 0; i < len(newOrderDetails); i += batchSize {
			end := i + batchSize
			if end > len(newOrderDetails) {
				end = len(newOrderDetails)
			}

			batch := newOrderDetails[i:end]
			if err := source.DB().Create(&batch).Error; err != nil {
				log.Log().Error("批量创建订单详情失败", zap.Error(err))
				return err
			}
		}
		log.Log().Info("成功创建新订单详情", zap.Int("数量", len(newOrderDetails)))
	}

	// 批量更新订单
	if len(updateOrders) > 0 {
		// 分批更新
		batchSize := 50
		for i := 0; i < len(updateOrders); i += batchSize {
			end := i + batchSize
			if end > len(updateOrders) {
				end = len(updateOrders)
			}

			batch := updateOrders[i:end]
			for _, order := range batch {
				if err := source.DB().Model(&model.VipOrder{}).Where("order_sn = ?", order.OrderSn).Updates(map[string]interface{}{
					"status":                        order.Status,
					"new_customer":                  order.NewCustomer,
					"sign_time":                     order.SignTime,
					"settled_time":                  order.SettledTime,
					"last_update_time":              order.LastUpdateTime,
					"settled":                       order.Settled,
					"order_sub_status_name":         order.OrderSubStatusName,
					"commission":                    order.Commission,
					"after_sale_change_commission":  order.AfterSaleChangeCommission,
					"after_sale_change_goods_count": order.AfterSaleChangeGoodsCount,
					"commission_enter_time":         order.CommissionEnterTime,
					"app_id":                        order.AppID,
					"parent_app_id":                 order.ParentAppID,
					"app_user_id":                   order.AppUserID,
					"sync_time":                     time.Now(),
				}).Error; err != nil {
					log.Log().Error("更新订单失败", zap.String("订单ID", order.OrderSn), zap.Error(err))
					return err
				}
			}
		}
		log.Log().Info("成功更新订单", zap.Int("数量", len(updateOrders)))
	}

	return nil
}
