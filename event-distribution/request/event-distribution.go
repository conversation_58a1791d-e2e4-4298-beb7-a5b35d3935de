package request

import (
	"event-distribution/model"
	productModel "product/model"
	yzRequest "yz-go/request"
	"yz-go/source"
)

// 添加修改
type EventDistribution struct {
	source.Model
	StartAt                     string                              `json:"start_at" form:"start_at" gorm:"column:start_at;comment:开始时间;"`         //开始时间
	EndAt                       string                              `json:"end_at" form:"end_at" gorm:"column:end_at;comment:结束时间;"`               //结束时间
	Remark                      string                              `json:"remark" form:"remark" gorm:"column:remark;comment:商品备注;"`               // 商品备注
	Title                       string                              `json:"title" form:"title" gorm:"column:title;comment:活动名称;"`                  // 活动名称
	Deliver                     string                              `json:"deliver" form:"deliver" gorm:"column:deliver;comment:发货说明;"`            // 发货说明
	Services                    model.Services                      `json:"services" form:"services" gorm:"column:services;comment:服务;"`           // 服务
	ShareText                   string                              `json:"share_text" form:"share_text" gorm:"column:share_text;comment:活动分享文字;"` // 活动分享文字
	Status                      int                                 `json:"status" form:"status" gorm:"column:status;comment:0未开始1已开始2结束3手动结束;"`   // 0未开始1已开始2结束3手动结束
	ProductId                   uint                                `json:"product_id" form:"product_id" gorm:"column:product_id;comment:商品id;index;"`
	EventDistributionProductSku []model.EventDistributionProductSku `json:"event_distribution_product_sku"`
	Product                     productModel.Product
}

// 列表
type EventDistributionListSearch struct {
	CreateStartAt string `json:"create_start_at" form:"create_start_at" query:"create_start_at" gorm:"column:create_start_at;comment:创建开始时间;"` //开始时间
	CreateEndAt   string `json:"create_end_at" form:"create_end_at" query:"create_end_at" gorm:"column:create_end_at;comment:创建结束时间;"`         //结束时间
	ProductId     uint   `json:"product_id" form:"product_id" query:"product_id"`                                                              //商品id
	ProductTitle  string `json:"product_title" form:"product_title" query:"product_title"`                                                     //商品名称
	Status        *int   `json:"status" form:"status" gorm:"column:status;comment:0未开始1已开始2结束3手动结束;"`                                          // 0未开始1已开始2结束3手动结束,字段不传就是全部
	yzRequest.PageInfo
}

type Spu struct {
	Sku    uint `json:"sku"`
	Number uint `json:"number"`
}

type Ids struct {
	Ids []uint `json:"ids"`
}

// 自营商品条件
type ProductSearch struct {
	yzRequest.PageInfo
	Title string `json:"title" form:"title" query:"title"  gorm:"column:title;comment:商品名称;"` // 商品名称

}
