package v1

import (
	"encoding/json"
	"errors"
	"github.com/gogf/gf/frame/g"
	"gorm.io/gorm"
	"notification/mq"
	pModel "payment/model"
	paymentpay "payment/pay"
	"strconv"
	model2 "yz-go/model"
	service2 "yz-go/service"
	"yz-go/source"
	"yz-go/utils"

	_ "finance/listener"
	"finance/model"
	"finance/request"
	fresponse "finance/response"
	"finance/service"
	"fmt"
	v1 "gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	log2 "log"
	pservice "payment/service"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	response "yz-go/response"
	yzResponse "yz-go/response"
)

// @Tags AccountApply
// @Summary 创建AccountApply
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AccountApply true "创建AccountApply"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /finance/createAccountApply [post]
func CreateAccountApply(c *gin.Context) {
	var Account model.AccountApply
	err := c.ShouldBindJSON(&Account)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if Account.MemberId <= 0 {
		_, supplier := service.GetSupplierId(v1.GetUserID(c))
		Account.MemberId = supplier.ID
	}

	if err, Account := service.CreateAccountApply(Account); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		response.FailWithMessage(err, c)
		return
	} else {
		response.OkWithData(&Account, c)
	}
}

func UserRecharge(c *gin.Context) {
	var balance paymentpay.StationBalance
	err := c.ShouldBindJSON(&balance.StationBalanceRecharge)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, _ = paymentpay.Recharge(&balance); err != nil {
		log.Log().Error("会员充值失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("会员充值失败", c)
		return
	}
	var text = "充值了"
	if balance.StationBalanceRecharge.Action == 2 {
		text = "扣除了"
	}
	var user model.User
	err = source.DB().First(&user, balance.StationBalanceRecharge.Uid).Error
	service2.CreateOperationRecord(v1.GetUserID(c), 2, c.ClientIP(), "会员'"+user.Username+text+utils.Fen2Yuan(balance.StationBalanceRecharge.Amount)+"元 ")
	yzResponse.OkWithData("充值成功", c)

}

func SupplierUserRecharge(c *gin.Context) {
	var balance paymentpay.StationBalance
	err := c.ShouldBindJSON(&balance.StationBalanceRecharge)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := v1.GetUserID(c)
	err, supplier := v1.GetSupplierByUserId(userID)
	if err == nil && userID > 0 {
		if supplier == 0 {
			//总后台
		} else {
			//供应商后台
			balance.StationBalanceRecharge.PetSupplierID = uint(supplier)
		}

	}
	if err, _ = paymentpay.Recharge(&balance); err != nil {
		log.Log().Error("会员充值失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("会员充值失败", c)
		return
	}
	var user model.User
	err = source.DB().First(&user, balance.StationBalanceRecharge.Uid).Error
	service2.CreateOperationRecord(v1.GetUserID(c), 2, c.ClientIP(), "会员'"+user.Username+"'充值了"+strconv.Itoa(int(balance.StationBalanceRecharge.Amount)))
	yzResponse.OkWithData("充值成功", c)

}

// @Tags 退款
// @Summary 退款
// @Security 退款
// @accept application/json
// @Produce application/json
// @Param data body request.Refund true "退款"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /finance/refund [post]
func Refund(c *gin.Context) {
	var refundFace paymentpay.CommonInterface
	var reqParam request.Refund
	if err := c.ShouldBindJSON(&reqParam); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	switch reqParam.PayTypeID {
	case pModel.CONVERGENCEBALANCE:
		var convergenceBalance paymentpay.ConvergenceBalance
		convergenceBalance.ConvergenceBalanceRefund = reqParam
		refundFace = &convergenceBalance
		break
	case pModel.STATIONBALANCE:
		var stationBalance paymentpay.StationBalance
		stationBalance.StationBalanceRefund = reqParam
		refundFace = &stationBalance
		break
	case pModel.BACKSTAGESTATIONBALANCE:
		var stationBalance paymentpay.StationBalance
		stationBalance.StationBalanceRefund = reqParam
		refundFace = &stationBalance
		break
	case pModel.CONVERGENCEWECHAT:
		var convergenceWeChat paymentpay.ConvergenceWeChat
		convergenceWeChat.ConvergenceWeChatRefund = reqParam
		refundFace = &convergenceWeChat
		break
	default:
		yzResponse.FailWithMessage("支付方式异常", c)
		return
	}
	if err, _ := paymentpay.Refund(refundFace); err != nil {
		log.Log().Error("退款失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData("退款成功", c)
}

func PaymentRecord(c *gin.Context) {
	var requestData request.Refund
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var data interface{}
	if err, data = service.PaymentRecord(requestData); err != nil {
		log.Log().Error("查询支付单失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(data, c)
}

func WithdrawStatus(c *gin.Context) {
	var err error
	var withdraw request.WithdrawalExamination
	if err = c.ShouldBindJSON(&withdraw); err != nil {
		yzResponse.FailWithMessage("解析参数失败"+err.Error(), c)
		return
	}

	uid := v1.GetUserID(c)
	ip := c.ClientIP()
	err = service.WithdrawStatus(withdraw, uid, ip)
	if err != nil {
		log.Log().Error("审核失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData("审核成功", c)

}

func WithdrawList(c *gin.Context) {
	var err error
	var into request.WithdrawSearch
	if err = c.ShouldBindJSON(&into); err != nil {
		yzResponse.FailWithMessage("解析参数失败"+err.Error(), c)
		return
	}
	var withdrawList []model.Withdrawal
	var total int64
	err, withdrawList, total = service.WithdrawList(into)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List:     withdrawList,
		Total:    total,
		Page:     into.Page,
		PageSize: into.PageSize,
	}, "获取成功", c)

}
func WithdrawDetail(c *gin.Context) {
	var err error
	var into request.WithdrawSearch
	if err = c.ShouldBindJSON(&into); err != nil {
		yzResponse.FailWithMessage("解析参数失败"+err.Error(), c)
		return
	}
	var withdrawList model.Withdrawal
	err, withdrawList = service.WithdrawDetail(into)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(withdrawList, c)

}
func BillDetail(c *gin.Context) {
	var err error
	var into request.BillDetailSearch
	if err = c.ShouldBindJSON(&into); err != nil {
		yzResponse.FailWithMessage("解析参数失败"+err.Error(), c)
		return
	}
	billErr, billDetail := service.BillDetail(into)
	if billErr != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(billDetail, c)
	}

}

func RemitStatus(c *gin.Context) {
	var err error
	var withdraw model.Withdrawal
	if err = c.ShouldBindJSON(&withdraw); err != nil {
		yzResponse.FailWithMessage("解析参数失败"+err.Error(), c)
		return
	}
	uid := v1.GetUserID(c)
	ip := c.ClientIP()
	err = service.RemitStatus(withdraw, uid, ip)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = mq.PublishMessage(withdraw.UserId, "withdrawPayment", withdraw.ID)
	yzResponse.OkWithData("操作成功", c)

}
func InvoiceStatus(c *gin.Context) {
	var err error
	var withdraw model.Withdrawal
	if err = c.ShouldBindJSON(&withdraw); err != nil {
		yzResponse.FailWithMessage("解析参数失败"+err.Error(), c)
		return
	}
	uid := v1.GetUserID(c)
	ip := c.ClientIP()
	err = service.InvoiceStatus(withdraw, uid, ip)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData("操作成功", c)

}

func SinglePay(c *gin.Context) {
	var requestData model.SinglePay
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err = service.SinglePay(requestData); err != nil {
		log.Log().Error("代付打款失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("代付打款成功", c)
}

func ModifyAccount(c *gin.Context) {
	var Account model.AccountApply
	err := c.ShouldBindJSON(&Account)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, Account := service.ModifyAccount(Account); err != nil {
		log.Log().Error("修改失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		response.FailWithMessage(err, c)
		return
	} else {
		response.OkWithData(&Account, c)
	}
}

// @Tags PostImages
// @Summary 提交开户图像资料
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AccountApply true "提交开户图像资料"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"开户图片上传成功"}"
// @Router /finance/PostImages [post]
func PostImages(c *gin.Context) {
	var Account model.AccountApply
	err := c.ShouldBindJSON(&Account)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err := service.PostImages(Account); err != nil {
		log.Log().Error("开户图片上传失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		response.FailWithMessage(err, c)
		return
	} else {
		response.OkWithMessage("开户图片上传成功", c)
	}
}

func UpdateImages(c *gin.Context) {
	var Account model.AccountApply
	err := c.BindQuery(&Account)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err := service.UpdateImages(Account); err != nil {
		log.Log().Error("图片修改失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		response.FailWithMessage(err, c)
		return
	} else {
		response.OkWithMessage("图片修改成功", c)
	}
}

// @Tags AccountApply
// @Summary 删除AccountApply
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AccountApply true "删除AccountApply"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /finance/deleteAccountApply [delete]
func DeleteAccountApply(c *gin.Context) {
	var Account model.AccountApply
	err := c.ShouldBindJSON(&Account)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err := service.DeleteAccountApply(Account); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		response.FailWithMessage("删除失败", c)
		return
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// @Tags AccountApply
// @Summary 批量删除AccountApply
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.IdsReq true "批量删除AccountApply"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /finance/deleteAccountApplyByIds [delete]
func DeleteAccountApplyByIds(c *gin.Context) {
	var err error
	var IDS yzRequest.IdsReq
	err = c.ShouldBindJSON(&IDS)
	if err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		response.FailWithMessage("批量删除失败", c)
		return
	}
	if err = service.DeleteAccountApplyByIds(IDS); err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		response.FailWithMessage("批量删除失败", c)
		return
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// @Tags AccountApply
// @Summary 更新AccountApply
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AccountApply true "更新AccountApply"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /finance/updateAccountApply [put]
func UpdateAccountApply(c *gin.Context) {
	var Account model.AccountApply
	err := c.ShouldBindJSON(&Account)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err := service.UpdateAccountApply(Account); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		response.FailWithMessage("更新失败", c)
		return
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// @Tags AccountApply
// @Summary 用id查询AccountApply
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AccountApply true "用id查询AccountApply"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /finance/FindAccountApply [get]
func FindAccountApply(c *gin.Context) {
	var Account model.AccountApply
	_ = c.ShouldBindQuery(&Account)
	if err, reAccount := service.GetAccountApply(Account.ID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		response.FailWithMessage("查询失败", c)
		return
	} else {
		response.OkWithData(gin.H{"reAccount": reAccount}, c)
	}
}

// @Tags FindSignContract
// @Summary 用mch查询签约内容
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AccountApply true "用mch查询签约内容"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /finance/FindSignContract [get]
func FindSignContract(c *gin.Context) {
	var Account model.AccountApply
	_ = c.ShouldBindQuery(&Account)
	if err, reAccount := service.FindSignContract(Account.AltMchNo); err != nil {
		log.Log().Error("获取签约内容失败!", zap.Any("err", err))
		response.FailWithMessage("获取签约内容失败", c)
		return
	} else {
		response.OkWithData(gin.H{"reAccount": reAccount}, c)
	}
}

// @Tags SignContract
// @Summary 签约流程
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SignContract true "签约流程"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"签约功"}"
// @Router /finance/SignContract [get]
func SignContract(c *gin.Context) {
	var Account model.AccountApply
	_ = c.ShouldBindQuery(&Account)
	if err, reAccount := service.SignContract(Account.AltMchNo); err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(gin.H{"reAccount": reAccount}, c)
	}
}

// @Tags AccountApply
// @Summary 分页获取AccountApply列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.AccountApplySearch true "分页获取AccountApply列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /finance/GetAccountApplyList [get]
func GetAccountApplyList(c *gin.Context) {
	var pageInfo request.AccountApplySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetAccountApplyInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func GetSupplierSettlementList(c *gin.Context) {
	var pageInfo request.AccountApplySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, list, total := service.GetSupplierSettlementList(pageInfo)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		response.FailWithMessage("明细获取失败", c)
		return
	}
	err, payType := pservice.GetPayType()

	if err != nil {
		response.FailWithMessage("支付方式获取失败", c)
		return
	} else {

		yzResponse.OkWithDetailed(fresponse.PageResult{
			PayType:  payType,
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func GetSupplierSettlementTotal(c *gin.Context) {
	var pageInfo request.AccountApplySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, data := service.GetSupplierSettlementData(pageInfo)
	if err != nil {
		response.FailWithMessage("支付方式获取失败", c)
		return
	} else {

		yzResponse.OkWithData(data, c)
	}
}

// GetMemberList
// @Tags 【后台】会员
// @Summary 获取会员列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Supplier true "分页获取GetMemberList列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /finance/GetMemberList [get]
func GetMemberList(c *gin.Context) {
	var pageInfo model.Supplier
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	log2.Println("name:", pageInfo.Name)
	if err, list := service.GetMemberList(pageInfo.Name); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		response.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List: list,
		}, "获取成功", c)
	}
}

func GetPurchaseBalanceList(c *gin.Context) {
	var pageInfo request.UserBalanceSearch
	if err := c.ShouldBindQuery(&pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetPurchaseBalanceList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		response.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func ExportPurchaseBalanceList(c *gin.Context) {
	var pageInfo request.UserBalanceSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, link := service.ExportPurchaseBalanceList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		response.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}

func GetSettlementBalanceList(c *gin.Context) {
	var pageInfo request.UserBalanceSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list, total := service.GetSettlementBalanceList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		response.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func RetrySeparate(c *gin.Context) {
	var err error
	var pageInfo request.SeparateSearch
	err = c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("解析失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.RetrySeparate(pageInfo); err != nil {
		log.Log().Error("处理失败", zap.Any("err", err))
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("处理中", c)
	}
}

func GetConvergence(c *gin.Context) {
	var pageInfo request.ById
	err := c.ShouldBind(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list := service.GetConvergence(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		response.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List: list,
		}, "获取成功", c)
	}
}
func ExportSettlementBalance(c *gin.Context) {
	var err error
	var pageInfo request.UserBalanceSearch
	err = c.ShouldBind(&pageInfo)
	if err != nil {
		log.Log().Error("解析失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var link string
	if err, link = service.ExportSettlementBalance(pageInfo); err != nil {
		log.Log().Error("处理失败", zap.Any("err", err))
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}
func ExportWithdrawal(c *gin.Context) {
	var err error
	var pageInfo request.WithdrawSearch
	err = c.ShouldBind(&pageInfo)
	if err != nil {
		log.Log().Error("解析失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var link string
	if err, link = service.ExportWithdrawal(pageInfo); err != nil {
		log.Log().Error("处理失败", zap.Any("err", err))
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}
func ExportSettlement(c *gin.Context) {
	var err error
	var pageInfo request.AccountApplySearch
	err = c.ShouldBind(&pageInfo)
	if err != nil {
		log.Log().Error("解析失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var link string
	if err, link = service.ExportSettlement(pageInfo); err != nil {
		log.Log().Error("处理失败", zap.Any("err", err))
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}

func SetSetting(c *gin.Context) {
	var err error
	var param model.WithdrawalSetting
	err = c.ShouldBind(&param)
	if err != nil {
		log.Log().Error("解析失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.SetSetting(param); err != nil {
		log.Log().Error("操作失败", zap.Any("err", err))
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.Ok(c)
	}
}
func SupplierWithdrawalSetting(c *gin.Context) {
	var err error
	var param model.SupplierWithdrawalSetting
	err = c.ShouldBind(&param)
	if err != nil {
		log.Log().Error("解析失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.SupplierWithdrawalSetting(param); err != nil {
		log.Log().Error("操作失败", zap.Any("err", err))
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.Ok(c)
	}
}

// GetInvoiceSetting 获取发票设置
func GetInvoiceSetting(c *gin.Context) {
	err, invoiceSetting := service.GetInvoiceSetting()
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(invoiceSetting, c)
}

// SetInvoiceSetting 设置发票设置
func SetInvoiceSetting(c *gin.Context) {
	var invoiceSetting service.WithdrawalInvoiceSetting
	if err := c.ShouldBindJSON(&invoiceSetting); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err := service.SetInvoiceSetting(invoiceSetting)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.Ok(c)
}

type InvoiceData struct {
	ID uint `json:"id"  form:"id" query:"id"`
}

// GetInvoice 获取发票详情API
func GetInvoice(c *gin.Context) {
	var data InvoiceData
	err := c.ShouldBindQuery(&data)
	if err != nil {
		log.Log().Error("参数错误", zap.Any("err", err))
		yzResponse.FailWithMessage("参数错误", c)
		return
	}

	err, invoice := service.GetInvoice(data.ID)
	if err != nil {
		log.Log().Error("获取发票失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取发票失败", c)
		return
	}

	yzResponse.OkWithData(invoice, c)
}

func IncomeSetSetting(c *gin.Context) {
	var err error
	var param model.IncomeWithdrawalSetting
	err = c.ShouldBind(&param)
	if err != nil {
		log.Log().Error("解析失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.IncomeSetSetting(param); err != nil {
		log.Log().Error("操作失败", zap.Any("err", err))
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.Ok(c)
	}
}

func GetSetting(c *gin.Context) {
	var err error
	var data model.WithdrawalSetting

	err, data = service.GetWithdrawalSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Log().Error("操作失败", zap.Any("err", err))
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}
func GetSupplierWithdrawal(c *gin.Context) {
	var err error
	var data model.SupplierWithdrawalSetting

	err, data = service.GetSupplierWithdrawal()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Log().Error("操作失败", zap.Any("err", err))
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func GetGongMallSetting() (err error, sysSetting model2.SysSetting) {
	err = source.DB().Table("sys_settings").Where("`key` = ?", "gongmall").First(&sysSetting).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("请先保存设置")
	}
	return
}

func GetWithdrawSetting(c *gin.Context) {

	var param model.MqData
	var err error
	if err = c.ShouldBindJSON(&param); err != nil {
		log.Log().Error("解析失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, balanceSetting, incomeSetting, ServiceTax, PoundageAmount := service.GetWithdrawalSettings(param.Amount); err != nil {
		log.Log().Error("操作失败", zap.Any("err", err))
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		type Setting struct {
			Enable string `json:"enable"` //0关闭 1开启
		}
		var setting Setting
		_, gongMall := GetGongMallSetting()
		json.Unmarshal([]byte(gongMall.Value), &setting)

		yzResponse.OkWithData(gin.H{"balanceSetting": balanceSetting, "incomeSetting": incomeSetting, "gongMallSetting": setting, "ServiceTax": ServiceTax, "PoundageAmount": PoundageAmount}, c)
	}
}

func IncomeGetSetting(c *gin.Context) {
	var err error
	var data model.IncomeWithdrawalSetting

	err, data = service.GetIncomeWithdrawalSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Log().Error("操作失败", zap.Any("err", err))
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func GetIncomeDetailList(c *gin.Context) {
	var err error
	var data []model.UserIncomeDetails
	var param request.IncomeSearch
	var total int64

	if err = c.ShouldBindJSON(&param); err != nil {
		log.Log().Error("解析失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data, total = service.GetIncomeDetailList(param); err != nil {
		log.Log().Error("操作失败", zap.Any("err", err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List:     data,
		Total:    total,
		Page:     param.Page,
		PageSize: param.PageSize,
	}, "获取成功", c)

}
func GetUserIncome(c *gin.Context) {
	var err error
	var data []model.UserIncomeSummary
	var param request.IncomeSearch
	var total int64

	if err = c.ShouldBindJSON(&param); err != nil {
		log.Log().Error("解析失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data, total = service.GetUserIncome(param); err != nil {
		log.Log().Error("操作失败", zap.Any("err", err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List:     data,
		Total:    total,
		Page:     param.Page,
		PageSize: param.PageSize,
	}, "获取成功", c)

}

func GetIncomeTypeList(c *gin.Context) {

	data := service.GetIncomeTypeList()
	yzResponse.OkWithData(data, c)

}

func GetSeparateAccountList(c *gin.Context) {
	var pageInfo request.SeparateSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetSeparateAccountList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		response.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func GetWithdrawalList(c *gin.Context) {
	var pageInfo request.GetWithdrawaSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetWithdrawalList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		response.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func GetUserBalance(c *gin.Context) {
	var pageInfo request.UserBalanceSearch
	if err := c.ShouldBindQuery(&pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total, accountBalancesTotals := service.GetUserBalance(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		response.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(g.Map{
			"list":                  list,
			"total":                 total,
			"page":                  pageInfo.Page,
			"pageSize":              pageInfo.PageSize,
			"accountBalancesTotals": accountBalancesTotals,
		}, "获取成功", c)
	}
}

func GetPayType(c *gin.Context) {

	if err, data := pservice.GetPayType(); err != nil {
		yzResponse.FailWithMessage("获取支付方式失败", c)
		log.Log().Debug("获取支付方式失败!", zap.Any("err", err))

	} else {
		yzResponse.OkWithData(data, c)

	}
}
func GetTotalBill(c *gin.Context) {
	var search request.SupplierBillSearch
	if err := c.ShouldBindQuery(&search); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, data := service.GetTotalBill(search); err != nil {
		yzResponse.FailWithMessage("获取统计失败", c)

	} else {
		yzResponse.OkWithData(data, c)

	}
}
func GetSupplierBill(c *gin.Context) {
	var search request.SupplierBillSearch
	if err := c.ShouldBindQuery(&search); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, data, total := service.GetSupplierBill(search); err != nil {
		yzResponse.FailWithMessage("获取统计失败", c)

	} else {
		yzResponse.OkWithDetailed(g.Map{
			"list":     data,
			"total":    total,
			"page":     search.Page,
			"pageSize": search.PageSize,
		}, "获取成功", c)

	}
}

func ExportSupplierBill(c *gin.Context) {
	var search request.SupplierBillSearch
	err := c.ShouldBindQuery(&search)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, link := service.ExportSupplierBill(search)
	if err != nil {
		log.Log().Error("导出失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(link, c)
	return
}
func ExportSupplierSettlementList(c *gin.Context) {
	var search request.AccountApplySearch
	err := c.ShouldBindQuery(&search)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, link := service.ExportSupplierSettlementList(search)
	if err != nil {
		log.Log().Error("导出失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(link, c)
	return
}

func GetWithdrawUnCompleteCount(c *gin.Context) {

	total, err := service.WithdrawUnCompleteCount()
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(total, c)

	return
}
