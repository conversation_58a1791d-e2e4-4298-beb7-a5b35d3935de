package listener

import (
	"encoding/json"
	"errors"
	Pay "payment/pay"

	model2 "finance/model"
	"finance/service"
	"go.uber.org/zap"
	"order/model"
	"order/mq"
	pay_mode "payment/model"
	pmodel "payment/model"
	"strconv"
	"yz-go/component/log"
	model3 "yz-go/model"
	"yz-go/source"
)

type OrderPayMessage struct {
	MessageType int    `json:"message_type"`
	PaySN       string `json:"pay_sn"`
}

func PushPaidCustomerHandles() {

	mq.PushHandles("financeOrderPaid", func(orderMsg mq.OrderMessage) (err error) {
		var SupplierSettlement pmodel.SupplierSettlement
		err = source.DB().Where("order_id=?", orderMsg.OrderID).First(&SupplierSettlement).Error
		if SupplierSettlement.ID > 0 {
			log.Log().Info("已经存在当前订单结算记录", zap.Any("err", orderMsg))
			return nil
		} else {
			err = nil
		}
		var orderPay OrderPayMessage
		var orderData model.Order
		var PayInfo pay_mode.PayInfo
		if orderMsg.MessageType != mq.Paid {
			return
		}
		log.Log().Info("财务监听支付订单信息：", zap.Any("orderMsg", orderMsg))
		source.DB().Where("id = ?", orderMsg.OrderID).First(&orderData)
		if orderData.ID <= 0 {
			log.Log().Error("订单不存在：", zap.Any("orderMsg", orderMsg))

			return
		}
		source.DB().Where("id = ?", orderData.PayInfoID).First(&PayInfo)
		if PayInfo.ID <= 0 {
			log.Log().Error("订单支付信息不存在支付订单id：", zap.Any("orderMsg", orderData))

			return
		}
		orderPay.MessageType = 1
		orderPay.PaySN = strconv.FormatUint(uint64(PayInfo.PaySN), 10)
		//log.Log().Info("财务监听支付订单信息：", zap.Any("orderData", orderData))
		//log.Log().Info("财务监听支付订单信息：", zap.Any("PayInfo", PayInfo))

		err = SupplierSettlementRecord(orderData, PayInfo.PaySN)
		//if orderData.SupplierID > 0 {
		//	err = SupplierSettlementRecord(orderData, PayInfo.PaySN)
		//} else {
		//	err = PlatformSettlementRecord(orderData, PayInfo.PaySN)
		//}

		return nil
	})
}

func GetSupplierSettlementCalculation(order model.Order) (deductionType, settlementType, supplyAmount uint, ratioPercentage, fee, withdrawalRatio, sAmount, tFee uint, err error) {

	var WithdrawalFee uint

	var setting model3.SysSetting
	var settingStr string
	err, settingStr = setting.GetSetting("supplier")
	if err != nil {
		log.Log().Error("获取供应商配置失败", zap.Any("err", err))
		return
	}

	var supplierWithdrawalSetting model2.SupplierWithdrawalSetting

	supplierBillValueErr, supplierBillValue := setting.GetSetting("supplier")

	if supplierBillValueErr != nil {
		log.Log().Error("获取供应商账单提现配置失败", zap.Any("err", err))

	}
	err = json.Unmarshal([]byte(supplierBillValue), &supplierWithdrawalSetting)
	if err != nil {
		log.Log().Error("获取供应商提现配置解析失败", zap.Any("err", err))
		return
	}

	WithdrawalFee = supplierWithdrawalSetting.WithdrawalCharge
	var supplier model2.Supplier
	source.DB().Where("id=?", order.SupplierID).First(&supplier)
	var value model2.SetValue
	err = json.Unmarshal([]byte(settingStr), &value)
	if err != nil {
		log.Log().Error("解析技术服务费失败", zap.Any("err", err))
		return
	}
	if order.Amount <= 0 {
		return
	}
	settAmount := order.Amount - order.TechnicalServicesFee
	settAmountFee := order.CostAmount + order.Freight
	settlementType = 2 //2 订单金额  1 成本+运费
	deductionType = 2
	var ratio uint
	if supplier.DeductionType == 1 { //供应商独立设置
		deductionType = 1
		if supplier.SelltType == 1 { //独立设置结算金额方式为成本价+运费
			settlementType = 1
			settAmount = settAmountFee
		}
		ratio = supplier.DeductionRatio //供应商独立技术服务费比例
	} else {
		deductionType = 2
		if value.SelltType == 1 {
			settlementType = 1
			settAmount = settAmountFee
		}
		ratio = uint(value.SelltRatio) //平台统一技术服务费比例
	}

	supplyAmount = settAmount
	ratioPercentage = ratio
	tFee = (settAmount) * ratio / 10000 // 金额 -  采购服务费用  * 扣点比例=  技术服务费
	//手续费
	sAmount = settAmount - tFee //订单金额-采购技术服务费- 供应商技术服务费
	//在扣除提现手续费
	if WithdrawalFee > 0 {
		fee = sAmount * uint(WithdrawalFee) / 10000
	}
	if fee >= 0 {
		sAmount = sAmount - fee
	}
	withdrawalRatio = uint(WithdrawalFee)

	return
}

func SupplierSettlementRecord(order model.Order, paySn uint) (err error) {
	var withdrawalFee, withdrawalRatio, settlementAmount, technicalServiceCost, ratioPercentage, settAmount, deductionType, settlementType uint
	deductionType, settlementType, settAmount, ratioPercentage, withdrawalFee, withdrawalRatio, settlementAmount, technicalServiceCost, err = GetSupplierSettlementCalculation(order)
	var item = pmodel.SupplierSettlement{

		OrderSn:              order.OrderSN,
		OrderID:              order.ID,
		GoodsPrice:           order.ItemAmount,
		FreightPrice:         order.Freight,
		WithdrawalFee:        withdrawalFee,
		WithdrawalRatio:      int(withdrawalRatio),
		SettlementAmount:     settlementAmount,
		TechnicalServiceCost: technicalServiceCost,
		TechnicalServicesFee: order.TechnicalServicesFee,
		Status:               0,
		TaskStatus:           0,
		PaySn:                paySn,
		SupplierID:           order.SupplierID,
		PayTime:              order.PaidAt,
		PayType:              order.PayTypeID,
		PayInfoID:            order.PayInfoID,
		DeductionType:        deductionType,
		DeductionRatio:       int(ratioPercentage),
		SettlementType:       int(settlementType),
		SupplyAmount:         settAmount,
		RefundAmount:         order.RefundAmount,
	}
	if item.SupplierID == 0 {
		item.SettlementAmount = settAmount
		item.DeductionType = 0
		item.DeductionRatio = 0
		item.SettlementType = 0
		item.WithdrawalRatio = 0
		item.TechnicalServiceCost = 0
		item.WithdrawalFee = 0
	}

	err = service.SupplierSettlements(&item)
	if err != nil {
		log.Log().Info("插入供应商结算流水错误!", zap.Any("err", err))
	} else {
		log.Log().Info("插入供应商结算记录完成!")
	}

	var face Pay.CommonInterface
	switch order.PayTypeID {
	case pmodel.CONVERGENCEBALANCE:
		var convergenceBalance Pay.ConvergenceBalance
		convergenceBalance.ConvergenceBalanceSettlement = item
		face = &convergenceBalance
		break
	case pmodel.STATIONBALANCE:
		var stationBalance Pay.StationBalance
		face = &stationBalance
		break
	case pmodel.BACKSTAGESTATIONBALANCE:
		var stationBalance Pay.StationBalance
		face = &stationBalance
		break
	case pmodel.CONVERGENCEWECHAT:
		var convergenceWeChat Pay.ConvergenceWeChat
		face = &convergenceWeChat
		break
	}
	if face == nil {
		return errors.New("错误的order.PayTypeID：" + "order.PayTypeID")
	}
	err, _ = Pay.Settlement(face)
	if err != nil {
		return err
	}

	return
}
