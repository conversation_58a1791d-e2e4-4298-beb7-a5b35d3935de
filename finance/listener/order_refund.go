package listener

import (
	"errors"
	model2 "finance/model"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"order/model"
	"order/mq"
	pay_mode "payment/model"
	"yz-go/component/log"
	"yz-go/source"
)

//订单退款事件监听
func PushOrderRefundCustomerHandles() {
	mq.PushHandles("financeOrderRefund", func(refund mq.OrderMessage) (err error) {
		if refund.MessageType != mq.Refunded {
			return
		}
		log.Log().Error("financeOrderRefund退款业务开始", zap.Any("err", err))
		var order model.Order
		var PayInfo pay_mode.PayInfo
		err = source.DB().First(&order, "id=?", refund.OrderID).Error
		if err != nil {
			log.Log().Error("financeOrderRefund退款订单异常", zap.Any("err", err))
			return nil
		}
		source.DB().Where("id = ?", order.PayInfoID).First(&PayInfo)
		if PayInfo.ID <= 0 {
			log.Log().Error("订单支付信息不存在支付订单id：", zap.Any("orderMsg", order))

			return
		}
		if order.RefundAmount <= 0 {
			return nil
		}
		if order.PayTypeID == 1 || order.PayTypeID == pay_mode.CONVERGENCEWECHAT {
			err = ConvergenceRefund(order, PayInfo.PaySN)
			if err != nil {
				log.Log().Error("financeOrderRefund ConvergenceRefund退款异常", zap.Any("err", err))
				return nil
			}
		} else if order.PayTypeID == 2 {
			err = StationRefund(order, PayInfo.PaySN)
			if err != nil {
				log.Log().Error("financeOrderRefund StationRefund退款异常", zap.Any("err", err))
				return nil
			}
		}
		return nil
	})
}

func StationRefund(order model.Order, paySn uint) (err error) {

	var supplierSettlement model2.SupplierSettlement
	err = source.DB().Where("order_id=?", order.ID).Delete(&supplierSettlement).Error
	if err != nil {
		return err
	}
	if order.Amount <= 0 {
		err = errors.New("amount 为0 不执行结算记录")
		return
	}
	err = SupplierSettlementRecord(order, paySn)
	if err != nil {
		return err
	}

	return
}

func ConvergenceRefund(order model.Order, paySn uint) (err error) {

	var supplierSettlement model2.SupplierSettlement
	err = source.DB().Preload("SettlementSubAccount", "amount > 0").Where("order_id = ?", order.ID).First(&supplierSettlement).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("未查询到结算订单流水,无法退款")
		return
	}

	err = source.DB().Transaction(func(tx *gorm.DB) error {

		err = tx.Where("id=?", supplierSettlement.ID).Delete(&model2.SupplierSettlement{}).Error
		if err != nil {
			return err
		}
		err = SubAccountSettlement(supplierSettlement)
		if err != nil {
			return err
		} //退回子账户分账金额数据
		err = SupplierSettlementRecord(order, paySn) //从新进行订单结算
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}

	return
}

func SubAccountSettlement(supplierSettlement model2.SupplierSettlement) error {
	err := source.DB().Transaction(func(tx *gorm.DB) error {
		for _, item := range supplierSettlement.SettlementSubAccount {
			err := tx.Model(model2.UserTopUp{}).Where("pay_sn=?", item.UserTopUp).Update("remaining_amount", gorm.Expr("remaining_amount+?", item.Amount)).Error
			if err != nil {
				return err
			}
			err = tx.Delete(&model2.SettlementSubAccount{}, "id=?", item.ID).Error
			if err != nil {
				return err
			}
		}

		return nil
	})
	if err != nil {
		return err
	}

	return nil
}
