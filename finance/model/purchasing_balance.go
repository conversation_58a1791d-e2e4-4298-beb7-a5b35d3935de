package model

import "yz-go/source"

type PurchasingBalance struct {
	source.Model
	OrderSn       uint     `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:订单ID;type:varchar(100);size:100;"`
	OrderID       string   `json:"order_id" form:"order_id" gorm:"column:order_id;comment:支付订单号;type:varchar(255);size:255;"`
	Uid           uint     `json:"uid" form:"uid" gorm:"column:uid;comment:会员ID;type:bigint;size:100;"`
	Amount        uint     `json:"amount" form:"amount" gorm:"column:amount;comment:充值金额;type:int;"`
	BusinessType  int      `json:"business_type" form:"business_type" gorm:"column:business_type;comment:业务类型(1采购2api采购3充值);type:int;"`
	Status        int      `json:"status" form:"status" gorm:"column:status;comment:状态(0消息未通知，1消息已通知);type:int;"`
	Balance       uint     `json:"balance" form:"balance" gorm:"column:balance;comment:余额;type:int;"`
	PayType       int      `json:"pay_type" form:"pay_type" gorm:"column:pay_type;comment:充值方式(1汇聚2平台余额);type:smallint;size:1;"`
	PaySN         uint     `json:"pay_sn" form:"pay_sn" gorm:"column:pay_sn;comment:充值sn;"`
	User          User     `json:"user" gorm:"foreignKey:Uid"`
	Remarks       string   `json:"remarks" form:"remarks"`
	PetSupplierID uint     `json:"pet_supplier_id"`
	Supplier      Supplier `json:"supplier" gorm:"foreignKey:PetSupplierID"`
	IsPlugin      int      `json:"is_plugin" form:"is_plugin"`
	AccountId     int      `json:"account_id" form:"account_id"`
	AccountName   string   `json:"account_name" form:"account_name" `
}

type PurchasingBalanceOrderIds struct {
	source.Model
	OrderID             uint `json:"order_id" form:"order_id" gorm:"unique"`
	PurchasingBalanceId uint `json:"purchasing_balance_id" form:"purchasing_balance_id" gorm:"index"`
	IsLocalOrder        int  `json:"is_local_order" form:"is_local_order" gorm:"default:0"`
}
type PurchasingBalanceModel struct {
	source.Model
	OrderSn       uint   `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:订单ID;type:varchar(100);size:100;"`
	OrderID       string `json:"order_id" form:"order_id" gorm:"column:order_id;comment:支付订单号;type:longtext;"`
	Uid           uint   `json:"uid" form:"uid" gorm:"column:uid;comment:会员ID;type:bigint;size:100;"`
	Amount        uint   `json:"amount" form:"amount" gorm:"column:amount;comment:充值金额;type:int;"`
	BusinessType  int    `json:"business_type" form:"business_type" gorm:"index;column:business_type;comment:业务类型(1采购2api采购3充值);type:int;"`
	Status        int    `json:"status" form:"status" gorm:"index;column:status;comment:状态(0消息未通知，1消息已通知);type:int;"`
	Balance       uint   `json:"balance" form:"balance" gorm:"column:balance;comment:余额;type:int;"`
	PayType       int    `json:"pay_type" form:"pay_type" gorm:"column:pay_type;comment:充值方式(1汇聚2平台余额);type:smallint;size:1;"`
	PaySN         uint   `json:"pay_sn" form:"pay_sn" gorm:"index;column:pay_sn;comment:充值sn;"`
	Remarks       string `json:"remarks" form:"remarks"`
	PetSupplierID uint   `json:"pet_supplier_id"`
	IsPlugin      int    `json:"is_plugin" form:"is_plugin"`
	AccountId     int    `json:"account_id" form:"account_id"`
	AccountName   string `json:"account_name" form:"account_name" `
	IsLocalOrder  int    `json:"is_local_order" form:"is_local_order" gorm:"default:0"`
}

func (PurchasingBalanceModel) TableName() string {
	return "purchasing_balances"
}
