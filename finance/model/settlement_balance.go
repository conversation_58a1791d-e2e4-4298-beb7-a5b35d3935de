package model

import "yz-go/source"

type SettlementBalance struct {
	source.Model
	OrderID      uint     `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单id;"`
	OrderSN      uint     `json:"order_sn" form:"order_sn" gorm:"unique;column:order_sn;comment:订单号;"`
	SupplierID   uint     `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商ID;type:bigint;size:100;"`
	Uid          uint     `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;type:bigint;size:100;"`
	Amount       uint     `json:"amount" form:"amount" gorm:"column:amount;comment:充值金额;type:int;"`
	BusinessType int      `json:"business_type" form:"business_type" gorm:"column:business_type;comment:业务类型(1结算入账2采购手续费3平台手续费);type:int;"`
	Balance      int      `json:"balance" form:"balance" gorm:"column:balance;comment:余额;type:int;"`
	User         User     `json:"user" gorm:"foreignKey:Uid"`
	Supplier     Supplier `json:"Supplier" gorm:"foreignKey:SupplierID"`
	WithdrawalID uint     `json:"withdrawal_id" gorm:"default:0"`
}
type SettlementBalanceModel struct {
	source.Model
	OrderID      uint `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单id;"`
	OrderSN      uint `json:"order_sn" form:"order_sn" gorm:"unique;column:order_sn;comment:订单号;"`
	SupplierID   uint `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商ID;type:bigint;size:100;"`
	Uid          uint `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;type:bigint;size:100;"`
	Amount       uint `json:"amount" form:"amount" gorm:"column:amount;comment:充值金额;type:int;"`
	BusinessType int  `json:"business_type" form:"business_type" gorm:"column:business_type;comment:业务类型(1结算入账2采购手续费3平台手续费);type:int;"`
	Balance      int  `json:"balance" form:"balance" gorm:"column:balance;comment:余额;type:int;"`

	WithdrawalID uint `json:"withdrawal_id" gorm:"default:0"`
}

func (SettlementBalanceModel) TableName() string {
	return "settlement_balances"
}
