package request

type Refund struct {
	UserID    uint   `json:"user_id" form:"user_id" `
	Amount    uint   `json:"amount" form:"amount" ` //分单位
	PayTypeID int    `json:"pay_type_id" form:"pay_type_id" `
	OrderID   uint   `json:"order_id" form:"order_id"`
	RefundSN  string `json:"refund_sn" form:"refund_sn" `
}

type NoSepareteRefund struct {
	RefundSN uint `json:"refund_sn" form:"refund_sn" `
	Uid      uint `json:"uid"`
	Amount   uint `json:"amount"`
}
