package service

import (
	joinModel "convergence/model"
	joinService "convergence/service"
	"encoding/json"
	"errors"
	"finance/model"
	"finance/request"
	"fmt"
	"go.uber.org/zap"
	"gorm.io/gorm"
	orderModel "order/model"
	payModel "payment/model"
	"strconv"
	"yz-go/component/log"
	"yz-go/config"
	model3 "yz-go/model"
	"yz-go/source"
)

func PaymentRecord(request request.Refund) (err error, payInfo payModel.PayInfo) {

	var order orderModel.Order
	err = source.DB().Where("id = ?", request.OrderID).First(&order).Error

	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}

	err = source.DB().Where("id = ?", order.PayInfoID).First(&payInfo).Error

	return

}

//未分账退款
func NoSepareteJoinRefund(requestRefund request.NoSepareteRefund) (err error) {
	var refund joinModel.Refund
	var userTopUp model.UserTopUp
	source.DB().Debug().Where("uid = ? and pay_type =?", requestRefund.Uid, 1).Where("remaining_amount >= ?", requestRefund.Amount).Where("pay_status = ?", 1).First(&userTopUp)

	if userTopUp.ID <= 0 {
		return errors.New("该用户无可用退款金额")
	}
	refund.P2_OrderNo = strconv.Itoa(int(userTopUp.PaySN))
	refund.P3_RefundOrderNo = strconv.FormatUint(uint64(requestRefund.RefundSN), 10)
	refund.P4_RefundAmount = Fen2Yuan(requestRefund.Amount)

	fmt.Println("退款信息", refund)
	var resData interface{}
	err, resData = joinService.NoSepareteRefundAction(refund)

	fmt.Println("退款结果", resData)

	return
}

func GetSupplierSettlementCalculation(SupplierID, amount uint) (supplyAmount, fee, sAmount, tFee uint, err error) {

	var WithdrawalFee int
	WithdrawalFee, err = strconv.Atoi(config.Config().Join.WithdrawalFee)
	var setting model3.SysSetting
	var settingStr string
	err, settingStr = setting.GetSetting("supplier")
	if err != nil {
		log.Log().Error("获取供应商配置失败", zap.Any("err", err))
		return
	}
	var supplier model.Supplier
	source.DB().Where("id=?", SupplierID).First(&supplier)
	var value model.SetValue
	err = json.Unmarshal([]byte(settingStr), &value)
	if err != nil {
		log.Log().Error("解析技术服务费失败", zap.Any("err", err))
		return
	}
	settAmount := amount

	var ratio uint
	if supplier.DeductionType == 1 { //供应商独立设置

		ratio = supplier.DeductionRatio //供应商独立技术服务费比例
	} else {

		ratio = uint(value.SelltRatio) //平台统一技术服务费比例
	}

	supplyAmount = settAmount
	tFee = (settAmount) * ratio / 10000 // 金额 -  采购服务费用  * 扣点比例=  技术服务费
	//手续费
	sAmount = settAmount - tFee //订单金额-采购技术服务费- 供应商技术服务费
	//在扣除提现手续费
	if WithdrawalFee > 0 {
		fee = sAmount * uint(WithdrawalFee) / 10000
	}
	if fee >= 0 {
		sAmount = sAmount - fee
	}

	return
}
