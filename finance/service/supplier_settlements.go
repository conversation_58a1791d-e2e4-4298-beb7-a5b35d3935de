package service

import (
	"finance/model"
	"finance/request"
	"fmt"
	order2 "order/order"
	"os"
	pmodel "payment/model"
	"time"
	"yz-go/config"
	"yz-go/source"
	"yz-go/utils"

	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/shopspring/decimal"
	"github.com/xingliuhua/leaf"
)

//
//func MoreSupplierSettlmentCalculation(SupplierRatio, WithdrawalRatio uint, itemAmount uint) (sAmount uint, err error) {
//
//	var settAmount = itemAmount
//
//	var ratio uint
//	ratio = SupplierRatio                //供应商独立技术服务费比例
//	tFee := (settAmount) * ratio / 10000 // 金额 -  采购服务费用  * 扣点比例=  技术服务费
//	//手续费
//	settAmount = settAmount - tFee //订单金额-采购技术服务费- 供应商技术服务费
//	withdrawalFee := settAmount * WithdrawalRatio / 10000
//
//	sAmount = withdrawalFee + tFee
//
//	return
//}

func SupplierSettlements(param *pmodel.SupplierSettlement) (err error) {
	err = source.DB().Create(&param).Error

	return
}

func GetSetting() (err error, setting model.SupplierSettingModel) {
	err = source.DB().Where("`key` = ?", "supplier").First(&setting).Error

	return
}

func Fen2Yuan(price uint) string {
	d := decimal.New(1, 2) //分除以100得到元

	result := decimal.NewFromInt(int64(price)).DivRound(d, 2).String()
	//result := decimal.NewFromInt(int64(price)).String()
	fmt.Println("Fen2Yuan转换结果", result)

	return result
}

//func Fen2YuanF(price uint) float64 {
//
//	d := decimal.New(1, 2) //分除以100得到元
//
//	result, _ := decimal.NewFromInt(int64(price)).DivRound(d, 2).Float64()
//	//result := decimal.NewFromInt(int64(price)).String()
//	fmt.Println("Fen2Yuan转换结果", result)
//
//	return result
//}

//汇聚结算金额
//func SettlementEnd(order *tmodel.Order, paySn uint) (err error) {
//
//	var supplierAccount model.AccountApply
//	err = source.DB().Where("member_id = ?", order.SupplierID).First(&supplierAccount).Error
//	var joinModel = joinmodel.MoreSeparateData{
//		AltOrderNo: GetOrderNo(),
//		MchOrderNo: strconv.Itoa(int(paySn)),
//	}
//	var res joinpayres.Response
//	err, res = joinservice.EndSeparateAccount(joinModel)
//	if "B100000" == res.Data.BizCode {
//		///err = source.DB().Debug().Model(model.PayRecord{}).Where("pay_info_id = ?", order.PayInfoID).Update("is_end", 1).Error
//
//	}
//	return
//}

//平台余额转移
//func PlatformBalanceTransfer(uid uint, amount uint, operationType uint) (err error) {
//
//	if operationType == 1 {
//		//err = source.DB().Model(model.PlatformBalance{}).Where("id = ?", 1).Update("amount", gorm.Expr("amount + ?", amount)).Error
//		//if err != nil {
//		//
//		//	log.Log().Error("增加平台余额出错", zap.Any("err", err))
//		//	return
//		//}
//	} else if operationType == 2 {
//		//err = source.DB().Model(model.PlatformBalance{}).Where("id = ?", 1).Update("amount", gorm.Expr("amount - ?", amount)).Error
//		//if err != nil {
//		//	log.Log().Error("扣除平台余额出错", zap.Any("err", err))
//		//	return
//		//}
//		err = source.DB().Model(model.AccountBalance{}).Where("uid = ?", uid).Where("type = ?", 2).Update("settlement_balance", gorm.Expr("settlement_balance + ?", amount)).Error
//		if err != nil {
//			log.Log().Error("增加用户站内余额出错", zap.Any("err", err))
//			return
//		}
//	}
//
//	platformBalance := model.PlatformBalanceRecord{
//		Uid:           uid,
//		OperationType: operationType,
//		Amount:        amount,
//	}
//
//	err = source.DB().Create(&platformBalance).Error
//	if err != nil {
//		log.Log().Error("变更平台余额出错", zap.Any("err", err))
//		return
//	}
//	return
//
//}
//
//func PlatformSeparateBalance(supplierOrder model.SupplierSettlement, operationType uint) (err error) {
//	if operationType == 1 {
//		err = PlatformBalanceTransfer(0, supplierOrder.SettlementAmount, operationType)
//		if err != nil {
//			err = errors.New("平台余额转账出错" + err.Error())
//			log.Log().Error("平台余额转账出错", zap.Any("err", err))
//			return
//		}
//
//		supplierOrder.Status = 1
//		err = source.DB().Save(&supplierOrder).Error
//		if err != nil {
//			err = errors.New("保存供应商结算余额出错" + err.Error())
//			return
//		}
//
//		return
//	}
//
//	var supplier model.Supplier
//	err = source.DB().Where("id = ?", supplierOrder.SupplierID).First(&supplier).Error
//	if err != nil {
//		err = errors.New("查询供应商结算记录出错" + err.Error())
//		log.Log().Error("查询供应商结算记录出错", zap.Any("err", err))
//		return
//	}
//
//	err = PlatformBalanceTransfer(uint(supplier.Uid), supplierOrder.SettlementAmount, operationType)
//	if err != nil {
//		err = errors.New("平台余额转账出错" + err.Error())
//		log.Log().Error("平台余额转账出错", zap.Any("err", err))
//		return
//	}
//	supplierOrder.Status = 1
//	err = source.DB().Save(&supplierOrder).Error
//	if err != nil {
//		err = errors.New("保存供应商结算余额出错" + err.Error())
//		return
//	}
//	var accountBalance model.AccountBalance
//	err = source.DB().Where("uid=?", supplier.Uid).Where("type=?", 2).First(&accountBalance).Error
//	if err != nil {
//		err = errors.New("查询会员站内余额出错accountBalance" + err.Error())
//		return
//	}
//
//	var settlementBalance model.SettlementBalance
//	settlementBalance.SupplierID = supplierOrder.SupplierID
//	settlementBalance.Amount = supplierOrder.SettlementAmount
//	settlementBalance.OrderID = supplierOrder.OrderID
//	settlementBalance.OrderSN = supplierOrder.OrderSn
//	settlementBalance.BusinessType = 1
//	settlementBalance.Uid = uint(supplier.Uid)
//	settlementBalance.Balance = int(accountBalance.SettlementBalance)
//	err = source.DB().Where(model.SettlementBalance{OrderSN: supplierOrder.OrderSn, SupplierID: supplierOrder.SupplierID}).FirstOrCreate(&settlementBalance).Error
//	if err != nil {
//		err = errors.New("创建结算余额出错" + err.Error())
//		return
//	}
//	err = source.DB().Create(&joinmodel.SeparateAccountingRecords{
//		Amount:     supplierOrder.SettlementAmount,
//		OrderSN:    supplierOrder.OrderSn,
//		SupplierID: supplierOrder.SupplierID,
//		Info:       "站内余额分账成功",
//		Status:     1,
//	}).Error
//
//	if err != nil {
//		err = errors.New("站内余额更新结算记录状态出错" + err.Error())
//		log.Log().Error("站内余额更新结算记录状态出错", zap.Any("err", err))
//		return
//	}
//
//	return
//
//}

////平台订单分账
//func PlatformSeparateAccount(order *tmodel.Order) (err error) {
//	var transfer model.TransferAccounts
//	transfer.TransferUid = order.UserID
//	transfer.Amount = order.Amount
//	transfer.CollectUid = order.SupplierID
//	err = TransferAccounts(transfer, order.ID)
//	if err != nil {
//		SeparateErrRecord(transfer.Amount, order.SupplierID, order.OrderSN, err.Error())
//		return err
//	}
//
//	return
//
//}
//
////平台手续费分账
//func PlatformFeeSeparateAccount(order *tmodel.Order) (err error) {
//	var supplierOrder model.SupplierSettlement
//	err = source.DB().Where("order_id = ?", order.ID).First(&supplierOrder).Error
//	if err != nil {
//		log.Log().Error("平台分账手续费错误", zap.Any("err", err))
//	}
//	var transfer model.TransferAccounts
//
//	transfer.TransferUid = order.UserID
//	transfer.Amount = supplierOrder.TechnicalServiceCost + supplierOrder.TechnicalServicesFee + supplierOrder.WithdrawalFee
//	transfer.CollectUid = 0
//	err = TransferAccounts(transfer, order.ID)
//	if err != nil {
//		SeparateErrRecord(transfer.Amount, transfer.CollectUid, order.OrderSN, err.Error())
//		return
//	}
//
//	return
//
//}

//采购技术服务费费 分给平台分账方
//func SeparateProcurementFee(order *tmodel.Order, paySn uint, settOrder model.SupplierSettlement) (err error) {
//
//	var res joinpayres.Response
//	altMchNo := config.Config().Join.AltMchNo
//	if altMchNo == "" {
//		err = errors.New("SeparateProcurementFee平台商户号为空")
//		log.Log().Error("SeparateProcurementFee平台商户号为空：", zap.Any("数据：", altMchNo))
//		return
//	}
//
//	altAmount := settOrder.TechnicalServicesFee + settOrder.TechnicalServiceCost + settOrder.WithdrawalFee
//	if altAmount <= 0 {
//		err = errors.New("平台分账服务费+手续费为空")
//		log.Log().Error("平台分账服务费+手续费为空：", zap.Any("数据：", settOrder))
//		return
//	}
//
//	infoData := []joinmodel.AltInfo{
//		{AltMchNo: altMchNo, AltAmount: Fen2Yuan(altAmount)},
//	}
//	var joinModel = joinmodel.MoreSeparateData{
//		AltOrderNo:    GetOrderNo(),
//		MchOrderNo:    strconv.Itoa(int(paySn)),
//		AltThisAmount: Fen2Yuan(altAmount),
//		AltInfo:       infoData,
//	}
//
//	err, res = joinservice.MoreSeparateAccount(joinModel)
//	if err != nil {
//		log.Log().Info("分账结果", zap.Any("info", res))
//
//	}
//	ReqData, err := json.Marshal(joinModel)
//	ResData, err := json.Marshal(res)
//	var SeparateAccountingRecords joinmodel.SeparateAccountingRecords
//	SeparateAccountingRecords.SupplierID = order.SupplierID
//	SeparateAccountingRecords.OrderSN = order.OrderSN
//	SeparateAccountingRecords.Amount = altAmount
//	SeparateAccountingRecords.ReqData = string(ReqData)
//	SeparateAccountingRecords.ResData = string(ResData)
//	SeparateAccountingRecords.Info = res.Data.BizMsg
//	if "B100000" == res.Data.BizCode {
//		SeparateAccountingRecords.Status = 1
//		var sSettle model.SupplierSettlement
//
//		sSettle.Status = 1
//		sSettle.SettlementTime = &source.LocalTime{Time: time.Now()}
//		err = source.DB().Where("order_id = ?", order.ID).Updates(&sSettle).Error
//
//		if err != nil {
//			log.Log().Info("分账成功更新供应商流水记录", zap.Any("info", err))
//
//		}
//
//	}
//	source.DB().Create(&SeparateAccountingRecords)
//
//	return
//}

func GetOrderNo() (id string) {

	var node *leaf.IdNode
	var err error
	err, node = leaf.NewNode(20)
	if err != nil {
		return
	}
	err, id = node.NextId()
	if err != nil {
		return
	}
	return
}

//供应商 微信支付分账
//func SupplierSeparateWechatAccount(order *tmodel.Order, paySn uint, supplierOrder model.SupplierSettlement) (err error) {
//
//	var res joinpayres.Response
//	var supplierAccount model.AccountApply
//
//	err = source.DB().Where("member_id = ?", order.SupplierID).First(&supplierAccount).Error
//	if err != nil {
//		err = errors.New("查询供应商汇聚开户错误" + err.Error())
//		log.Log().Error("supplierAccount_err", zap.Any("查询供应商汇聚开户错误", err))
//		return
//	}
//
//	amount := Fen2Yuan(supplierOrder.SettlementAmount)
//	infoData := []joinmodel.AltInfo{
//		{AltMchNo: supplierAccount.AltMchNo, AltAmount: amount},
//	}
//	var joinModel = joinmodel.MoreSeparateData{
//		AltOrderNo:    GetOrderNo(),
//		MchOrderNo:    strconv.Itoa(int(paySn)),
//		AltThisAmount: amount,
//		AltInfo:       infoData,
//	}
//
//	//err, res = joinservice.MoreSeparateAccount(joinModel)
//	//if err != nil {
//	//	log.Log().Info("分账结果", zap.Any("info", res))
//	//
//	//}
//
//	err = SeparateResponse(order, res, joinModel)
//	return
//}

//计算分账金额需要扣除的手续费
//func CalculationFee(amount uint) (err error, countAmountFee string) {
//
//	var WechatFee float64
//	WechatFee, err = strconv.ParseFloat(config.Config().Join.WechatFee, 64)
//
//	var fee float64 = 0
//	settAmount := Fen2YuanF(amount) //分转成元
//	if WechatFee > 0 {
//		fee = settAmount * (WechatFee / 100)
//	}
//	countAmount := fee + settAmount
//	countAmountFee = decimal.NewFromFloat(countAmount).Round(2).String()
//	return
//}

//供应商 微信支付分账给平台
//func PlatformSeparateWechatAccount(order *tmodel.Order, paySn uint) (err error) {
//
//	var res joinpayres.Response
//	altMchNo := config.Config().Join.AltMchNo
//	if altMchNo == "" {
//		log.Log().Error("平台商户号为空：", zap.Any("数据：", altMchNo))
//		err = errors.New("平台商户号为空")
//		return
//	}
//
//	infoData := []joinmodel.AltInfo{
//		{AltMchNo: altMchNo, AltAmount: Fen2Yuan(order.Amount)},
//	}
//	var joinModel = joinmodel.MoreSeparateData{
//		AltOrderNo:    GetOrderNo(),
//		MchOrderNo:    strconv.Itoa(int(paySn)),
//		AltThisAmount: Fen2Yuan(order.Amount),
//		AltInfo:       infoData,
//	}
//
//	err = SeparateResponse(order, res, joinModel)
//	return
//}
//
//func SeparateResponse(order *tmodel.Order, res joinpay_res.Response, joinModel joinmodel.MoreSeparateData) (err error) {
//	//log.Log().Info("分账请求数据：", zap.Any("数据：", joinModel))
//
//	err, res = joinservice.MoreSeparateAccount(joinModel)
//
//	ReqData, err := json.Marshal(joinModel)
//	ResData, err := json.Marshal(res)
//	var SeparateAccountingRecords joinmodel.SeparateAccountingRecords
//	SeparateAccountingRecords.SupplierID = order.SupplierID
//	SeparateAccountingRecords.OrderSN = order.OrderSN
//	SeparateAccountingRecords.Amount = order.Amount
//	SeparateAccountingRecords.ReqData = string(ReqData)
//	SeparateAccountingRecords.ResData = string(ResData)
//	SeparateAccountingRecords.Info = res.Data.BizMsg
//	if "B100000" == res.Data.BizCode {
//		SeparateAccountingRecords.Status = 1
//		var sSettle model.SupplierSettlement
//		sSettle.Status = 1
//		sSettle.SettlementTime = &source.LocalTime{Time: time.Now()}
//		err = source.DB().Where("order_id = ?", order.ID).Updates(&sSettle).Error
//		if err != nil {
//			log.Log().Info("分账成功更新结算流水记录", zap.Any("info", err))
//		}
//
//	}
//	source.DB().Create(&SeparateAccountingRecords)
//	return
//}

// SupplierSeparateAccount 队列分账业务处理
//func SupplierSeparateAccount(order *tmodel.Order) (err error) {
//
//	var supplierOrder model.SupplierSettlement
//	err = source.DB().Where("order_id = ?", order.ID).First(&supplierOrder).Error
//	if err != nil {
//		log.Log().Error("供应商汇聚余额分账错误SupplierSeparateAccount", zap.Any("err", err))
//		return
//	}
//
//	var transfer model.TransferAccounts
//	transfer.TransferUid = order.UserID
//	transfer.Amount = supplierOrder.SettlementAmount
//	transfer.CollectUid = order.SupplierID
//	err = TransferAccounts(transfer, order.ID)
//	if err != nil {
//		log.Log().Error("供应商汇聚余额分账错误TransferAccounts", zap.Any("err", err))
//		SeparateErrRecord(transfer.Amount, order.SupplierID, order.OrderSN, err.Error())
//		return
//	}
//
//	return
//}

//func SeparateErrRecord(amount, supplierID, OrderSN uint, error string) {
//	var separate joinmodel.SeparateAccountingRecords
//	separate.Amount = amount
//	separate.SupplierID = supplierID
//	separate.OrderSN = OrderSN
//	separate.Info = error
//	source.DB().Create(&separate)
//}
//
//func SettmentAction(order *tmodel.Order, amount uint, bType int) (err error) {
//
//	var settlementData = model.SettlementBalance{
//		OrderID:      order.ID,
//		SupplierID:   order.SupplierID,
//		Amount:       amount,
//		Balance:      0,
//		BusinessType: bType,
//	}
//	err = SettlementBalanceChange(settlementData)
//	return
//}

func GetSupplierSettlementData(info request.AccountApplySearch) (err error, list interface{}) {
	var dataMap map[string]interface{}
	dataMap = make(map[string]interface{})

	var orderAmount, costAmount, freight, technicalServicesFee, supplierBillAmount uint64

	var totalCount int64

	db := source.DB().Model(&model.SupplierSettlement{})
	db.Where("supplier_settlements.supplier_id >0")

	// 如果有条件搜索 下方会自动创建搜索语句

	var joinWhere, orderJoin string
	//joinWhere = "left join suppliers on suppliers.id = supplier_settlements.supplier_id"
	if info.SupplierID > 0 {

		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id "
		db = db.Where("suppliers.id = ?", info.SupplierID)
	}

	if info.NickName != "" {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id "
		db = db.Where("suppliers.name = ?", info.NickName)
	}
	if info.Mobile != "" {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id "
		db = db.Where("suppliers.mobile = ?", info.Mobile)

	}
	if info.RealName != "" {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id "
		db = db.Where("suppliers.realname = ?", info.RealName)

	}

	if info.WithdrawalStatus != "" {
		db = db.Where("supplier_settlements.withdrawal_status = ?", info.WithdrawalStatus)
	}

	if info.RemitStatus != nil {
		db = db.Where("supplier_settlements.remit_status = ?", info.RemitStatus)
	}

	if info.SettID > 0 {
		db = db.Where("supplier_settlements.id = ?", info.SettID)
	}
	if info.OrderSn > 0 {
		db = db.Where("supplier_settlements.order_sn = ?", info.OrderSn)
	}
	if info.Status > 0 {
		db = db.Where("supplier_settlements.status = ?", info.Status-1)
	}
	if info.PayType != nil {
		db = db.Where("supplier_settlements.pay_type = ?", info.PayType)
	}
	orderJoin = "INNER join orders on orders.id = supplier_settlements.order_id"

	if info.TimeS != "" && info.TimeE != "" {
		db = db.Where("orders.created_at BETWEEN ? AND ?", info.TimeS, info.TimeE)

	}

	if joinWhere != "" {
		db.Joins(joinWhere)
	}
	err = db.Joins(orderJoin).Count(&totalCount).Error
	err = db.Select("COALESCE(SUM(settlement_amount), 0) as amount1").First(&supplierBillAmount).Error

	err = db.Select("COALESCE(SUM(orders.amount), 0) as amount1").First(&orderAmount).Error
	err = db.Select("COALESCE(SUM(orders.cost_amount), 0) as amount1").First(&costAmount).Error
	err = db.Select("COALESCE(SUM(orders.freight), 0) as amount1").First(&freight).Error
	err = db.Select("COALESCE(SUM(orders.technical_services_fee), 0) as amount1").First(&technicalServicesFee).Error

	dataMap["orderAmount"] = orderAmount
	dataMap["costAmount"] = costAmount
	dataMap["freight"] = freight
	dataMap["technicalServicesFee"] = technicalServicesFee
	dataMap["supplierBillAmount"] = supplierBillAmount
	dataMap["total_count"] = uint64(totalCount)

	list = dataMap
	return
}

// ... existing code ...

// ExportSupplierSettlementList 导出供应商结算列表
func ExportSupplierSettlementList(info request.AccountApplySearch) (err error, link string) {
	// 不分页获取所有数据
	info.Page = 1
	info.PageSize = 5000

	err, supplierSettlements, _ := GetSupplierSettlementList(info)
	if err != nil {
		return err, ""
	}

	// 创建Excel文件
	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")

	// 设置表头
	f.SetCellValue("Sheet1", "A1", "结算ID")
	f.SetCellValue("Sheet1", "B1", "订单号")
	f.SetCellValue("Sheet1", "C1", "供应商ID")
	f.SetCellValue("Sheet1", "D1", "供应商名称")
	f.SetCellValue("Sheet1", "E1", "联系电话")
	f.SetCellValue("Sheet1", "F1", "真实姓名")
	f.SetCellValue("Sheet1", "G1", "订单金额(元)")
	f.SetCellValue("Sheet1", "H1", "结算金额(元)")
	f.SetCellValue("Sheet1", "I1", "技术服务费(元)")
	f.SetCellValue("Sheet1", "J1", "提现手续费(元)")
	f.SetCellValue("Sheet1", "K1", "结算状态")
	f.SetCellValue("Sheet1", "L1", "提现状态")
	f.SetCellValue("Sheet1", "M1", "打款状态")
	f.SetCellValue("Sheet1", "N1", "创建时间")
	f.SetCellValue("Sheet1", "O1", "结算时间")

	// 填充数据
	for i, v := range supplierSettlements {
		rowIndex := i + 2 // 从第2行开始填充数据

		// 获取状态文本
		statusText := "未结算"
		if v.Status == 1 {
			statusText = "已结算"
		}

		withdrawalStatusText := "未提现"
		if v.WithdrawalStatus == 1 {
			withdrawalStatusText = "已提现"
		} else if v.WithdrawalStatus == 2 {
			withdrawalStatusText = "提现中"
		}

		remitStatusText := "未打款"
		if v.RemitStatus == 1 {
			remitStatusText = "已打款"
		}

		// 供应商信息
		supplierName := ""
		supplierMobile := ""
		supplierRealName := ""

		supplierName = v.Supplier.Name
		supplierMobile = v.Supplier.Mobile
		supplierRealName = v.Supplier.Realname

		// 订单金额
		orderAmount := uint(0)

		orderAmount = v.Order.Amount

		// 设置单元格值
		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", rowIndex), v.ID)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", rowIndex), v.OrderSn)
		f.SetCellValue("Sheet1", fmt.Sprintf("C%d", rowIndex), v.SupplierID)
		f.SetCellValue("Sheet1", fmt.Sprintf("D%d", rowIndex), supplierName)
		f.SetCellValue("Sheet1", fmt.Sprintf("E%d", rowIndex), supplierMobile)
		f.SetCellValue("Sheet1", fmt.Sprintf("F%d", rowIndex), supplierRealName)
		f.SetCellValue("Sheet1", fmt.Sprintf("G%d", rowIndex), Fen2Yuan(orderAmount))
		f.SetCellValue("Sheet1", fmt.Sprintf("H%d", rowIndex), Fen2Yuan(v.SettlementAmount))
		f.SetCellValue("Sheet1", fmt.Sprintf("I%d", rowIndex), Fen2Yuan(v.TechnicalServicesFee))
		f.SetCellValue("Sheet1", fmt.Sprintf("J%d", rowIndex), Fen2Yuan(v.WithdrawalFee))
		f.SetCellValue("Sheet1", fmt.Sprintf("K%d", rowIndex), statusText)
		f.SetCellValue("Sheet1", fmt.Sprintf("L%d", rowIndex), withdrawalStatusText)
		f.SetCellValue("Sheet1", fmt.Sprintf("M%d", rowIndex), remitStatusText)

		// 时间格式化
		createTime := ""
		if v.CreatedAt.Time.Unix() > 0 {
			createTime = v.CreatedAt.Time.Format("2006-01-02 15:04:05")
		}
		f.SetCellValue("Sheet1", fmt.Sprintf("N%d", rowIndex), createTime)

		settlementTime := ""
		if v.SettlementTime != nil && v.SettlementTime.Time.Unix() > 0 {
			settlementTime = v.SettlementTime.Time.Format("2006-01-02 15:04:05")
		}
		f.SetCellValue("Sheet1", fmt.Sprintf("O%d", rowIndex), settlementTime)
	}

	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)

	// 创建导出目录
	timeString := time.Now().Format("20060102150405")
	path := config.Config().Local.Path + "/export_supplier_settlement"
	exist, _ := utils.PathExists(path)

	if !exist {
		// 创建文件夹
		err = os.Mkdir(path, os.ModePerm)
		if err != nil {
			fmt.Printf("mkdir failed![%v]\n", err)
		} else {
			fmt.Printf("mkdir success!\n")
		}
	}

	// 生成文件名
	fileName := timeString + "_供应商账单导出.xlsx"
	link = path + "/" + fileName

	// 保存文件
	if err = f.SaveAs(link); err != nil {
		return
	}

	return err, link
}
func GetSupplierSettlementList(info request.AccountApplySearch) (err error, list []model.SupplierSettlement, total int64) {

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.SupplierSettlement{})
	var supplierSettlement []model.SupplierSettlement
	db.Where("supplier_settlements.supplier_id >0")
	// 如果有条件搜索 下方会自动创建搜索语句

	var joinWhere, orderJoin string
	joinWhere = "left join suppliers on suppliers.id = supplier_settlements.supplier_id"
	if info.SupplierID > 0 {

		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id "
		db = db.Where("suppliers.id = ?", info.SupplierID)
	}

	if info.NickName != "" {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id "
		db = db.Where("suppliers.name = ?", info.NickName)
	}
	if info.Mobile != "" {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id "
		db = db.Where("suppliers.mobile = ?", info.Mobile)

	}
	if info.RealName != "" {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id "
		db = db.Where("suppliers.realname = ?", info.RealName)

	}

	if info.WithdrawalStatus != "" {
		db = db.Where("supplier_settlements.withdrawal_status = ?", info.WithdrawalStatus)
	}

	if info.RemitStatus != nil {
		db = db.Where("supplier_settlements.remit_status = ?", *info.RemitStatus)
	}

	if info.SettID > 0 {
		db = db.Where("supplier_settlements.id = ?", info.SettID)
	}
	if info.OrderSn > 0 {
		db = db.Where("supplier_settlements.order_sn = ?", info.OrderSn)
	}
	if info.Status > 0 {
		db = db.Where("supplier_settlements.status = ?", info.Status-1)
	}
	if info.PayType != nil {
		db = db.Where("supplier_settlements.pay_type = ?", info.PayType)
	}

	if info.TimeS != "" && info.TimeE != "" {
		orderJoin = "INNER join orders on orders.id = supplier_settlements.order_id"
		db = db.Where("orders.created_at BETWEEN ? AND ?", info.TimeS, info.TimeE)

	}

	err = db.Joins(joinWhere).Joins(orderJoin).Count(&total).Error
	err = db.Select("supplier_settlements.*").Limit(limit).Offset(offset).Preload("Order").Preload("Supplier").Order("supplier_settlements.id desc").Find(&supplierSettlement).Error
	return err, supplierSettlement, total

}

// ... existing code ...

// ExportSupplierBill 导出供应商账单
func ExportSupplierBill(info request.SupplierBillSearch) (err error, link string) {
	// 获取供应商账单数据
	err, list, _ := GetSupplierBill(info)
	if err != nil {
		return
	}

	// 创建Excel文件
	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")
	// 设置单元格的值（表头）
	f.SetCellValue("Sheet1", "A1", "供应商ID")
	f.SetCellValue("Sheet1", "B1", "供应商名称")
	f.SetCellValue("Sheet1", "C1", "真实姓名")
	f.SetCellValue("Sheet1", "D1", "联系电话")
	f.SetCellValue("Sheet1", "E1", "分类")
	f.SetCellValue("Sheet1", "F1", "订单总数")
	f.SetCellValue("Sheet1", "G1", "订单总金额")
	f.SetCellValue("Sheet1", "H1", "账单总金额")
	f.SetCellValue("Sheet1", "I1", "技术服务费总额")
	f.SetCellValue("Sheet1", "J1", "已提现金额")
	f.SetCellValue("Sheet1", "K1", "未提现金额")
	f.SetCellValue("Sheet1", "L1", "提现中金额")
	f.SetCellValue("Sheet1", "M1", "提现手续费总额")
	f.SetCellValue("Sheet1", "N1", "无效账单金额")
	f.SetCellValue("Sheet1", "O1", "已支付金额")

	// 填充数据
	for i, v := range list {
		rowIndex := i + 2 // 从第2行开始填充数据

		// 将分类名称获取出来
		categoryName := ""
		if v.CategoryInfo.Name != "" {
			categoryName = v.CategoryInfo.Name
		}

		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", rowIndex), v.ID)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", rowIndex), v.Name)
		f.SetCellValue("Sheet1", fmt.Sprintf("C%d", rowIndex), v.RealName)
		f.SetCellValue("Sheet1", fmt.Sprintf("D%d", rowIndex), v.Mobile)
		f.SetCellValue("Sheet1", fmt.Sprintf("E%d", rowIndex), categoryName)
		f.SetCellValue("Sheet1", fmt.Sprintf("F%d", rowIndex), v.OrderTotalCount)
		f.SetCellValue("Sheet1", fmt.Sprintf("G%d", rowIndex), Fen2Yuan(v.OrderTotalAmount))
		f.SetCellValue("Sheet1", fmt.Sprintf("H%d", rowIndex), Fen2Yuan(v.BillTotalAmount))
		f.SetCellValue("Sheet1", fmt.Sprintf("I%d", rowIndex), Fen2Yuan(v.TechnicalServicesTotalAmount))
		f.SetCellValue("Sheet1", fmt.Sprintf("J%d", rowIndex), Fen2Yuan(v.WithdrawnTotalAmount))
		f.SetCellValue("Sheet1", fmt.Sprintf("K%d", rowIndex), Fen2Yuan(v.NotWithdrawn))
		f.SetCellValue("Sheet1", fmt.Sprintf("L%d", rowIndex), Fen2Yuan(v.WithdrawingInProgress))
		f.SetCellValue("Sheet1", fmt.Sprintf("M%d", rowIndex), Fen2Yuan(v.WithdrawalTotalFee))
		f.SetCellValue("Sheet1", fmt.Sprintf("N%d", rowIndex), Fen2Yuan(v.InvalidBill))
		f.SetCellValue("Sheet1", fmt.Sprintf("O%d", rowIndex), Fen2Yuan(v.PaidAmount))
	}

	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)

	// 创建导出目录
	timeString := time.Now().Format("20060102150405")
	path := config.Config().Local.Path + "/export_supplier_bill"
	exist, _ := utils.PathExists(path)

	if !exist {
		// 创建文件夹
		err = os.Mkdir(path, os.ModePerm)
		if err != nil {
			fmt.Printf("mkdir failed![%v]\n", err)
		} else {
			fmt.Printf("mkdir success!\n")
		}
	}

	// 生成文件名
	fileName := timeString + "_供应商账单导出.xlsx"
	link = path + "/" + fileName

	// 保存文件
	if err = f.SaveAs(link); err != nil {
		return
	}

	return err, link
}

// ... existing code ...
func GetSupplierBill(info request.SupplierBillSearch) (err error, list []model.SupplierBill, total int64) {

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.SupplierBill{})
	var SupplierBill []model.SupplierBill
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.SupplierID > 0 {
		db = db.Where("id=?", info.SupplierID)
	}

	if info.SupplierName != "" {
		db = db.Where("name = ?", info.SupplierName)
	}
	if info.RealName != "" {
		db = db.Where("realname = ?", info.RealName)
	}
	if info.Phone != "" {
		db = db.Where("mobile = ?", info.Phone)
	}
	if info.CategoryId > 0 {
		db = db.Where("category_id = ?", info.CategoryId)
	}

	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Preload("CategoryInfo").Order("id desc").Find(&SupplierBill).Error
	if err != nil {
		return
	}

	// 遍历供应商账单，查询结算统计数据
	for i := range SupplierBill {
		var settlementStats SettlementDataTotal

		// 查询供应商结算统计数据
		settlementDB := source.DB().Model(&model.SupplierSettlement{}).Where("supplier_id = ?", SupplierBill[i].ID)
		orderDB := source.DB().Model(&order2.Order{}).Where("supplier_id = ?", SupplierBill[i].ID)
		orderDB.Select("count(*) as order_total_count").Row().Scan(&settlementStats.OrderTotalCount)
		orderDB.Select("COALESCE(SUM(amount), 0)").Row().Scan(&settlementStats.OrderTotalAmount)

		// 订单总金额和订单数量
		settlementDB.Select("COALESCE(SUM(settlement_amount), 0) as order_total_amount").Row().Scan(&settlementStats.OrderTotalAmount, &settlementStats.OrderTotalCount)

		// 账单总金额（结算金额）
		settlementDB.Select("COALESCE(SUM(settlement_amount), 0) as bill_total_amount").Row().Scan(&settlementStats.BillTotalAmount)

		// 技术服务费总金额
		settlementDB.Select("COALESCE(SUM(technical_service_cost), 0) as technical_services_total_amount").Row().Scan(&settlementStats.TechnicalServicesTotalAmount)

		// 已提现金额（已经申请提现的）
		settlementDB.Where("withdrawal_status=2").Select("COALESCE(SUM(settlement_amount), 0) as withdrawn_total_amount").Row().Scan(&settlementStats.WithdrawnTotalAmount)

		// 未提现金额（状态为未提现的）
		settlementDB.Where("withdrawal_status = 0").Select("COALESCE(SUM(settlement_amount), 0) as not_withdrawn").Row().Scan(&settlementStats.NotWithdrawn)

		// 提现中金额（状态为处理中的）
		settlementDB.Where("withdrawal_status=1 and remit_status=3").Select("COALESCE(SUM(settlement_amount), 0) as withdrawing_in_progress").Row().Scan(&settlementStats.WithdrawingInProgress)

		// 提现手续费总额
		settlementDB.Select("COALESCE(SUM(withdrawal_fee), 0) as withdrawal_total_fee").Row().Scan(&settlementStats.WithdrawalTotalFee)

		// 无效账单金额（状态为无效的）
		settlementDB.Where("withdrawal_status=2").Select("COALESCE(SUM(settlement_amount), 0) as invalid_bill").Row().Scan(&settlementStats.InvalidBill)

		// 已支付金额（所有已完成支付的结算金额）
		settlementDB.Where("(withdrawal_status=1 and remit_status=1").Select("COALESCE(SUM(settlement_amount), 0) as paid_amount").Row().Scan(&settlementStats.PaidAmount)

		// 更新供应商账单统计数据
		SupplierBill[i].OrderTotalAmount = settlementStats.OrderTotalAmount
		SupplierBill[i].OrderTotalCount = int(settlementStats.OrderTotalCount)
		SupplierBill[i].BillTotalAmount = settlementStats.BillTotalAmount
		SupplierBill[i].TechnicalServicesTotalAmount = settlementStats.TechnicalServicesTotalAmount
		SupplierBill[i].WithdrawnTotalAmount = settlementStats.WithdrawnTotalAmount
		SupplierBill[i].NotWithdrawn = settlementStats.NotWithdrawn
		SupplierBill[i].WithdrawingInProgress = settlementStats.WithdrawingInProgress
		SupplierBill[i].WithdrawalTotalFee = settlementStats.WithdrawalTotalFee
		SupplierBill[i].InvalidBill = settlementStats.InvalidBill
		SupplierBill[i].PaidAmount = settlementStats.PaidAmount
	}

	return err, SupplierBill, total

}

type SettlementDataTotal struct {
	OrderTotalAmount             uint
	OrderTotalCount              int64
	BillTotalAmount              uint
	TechnicalServicesTotalAmount uint
	WithdrawnTotalAmount         uint
	NotWithdrawn                 uint
	WithdrawingInProgress        uint
	WithdrawalTotalFee           uint
	InvalidBill                  uint
	PaidAmount                   uint
	SettlementBalance            uint
}

func GetTotalBill(info request.SupplierBillSearch) (err error, data SettlementDataTotal) {

	settlementDB := source.DB().Model(&model.SupplierSettlement{})
	settlementDB.Where("supplier_settlements.supplier_id >0")

	// 如果有条件搜索 下方会自动创建搜索语句

	var joinWhere, orderJoin string
	//joinWhere = "left join suppliers on suppliers.id = supplier_settlements.supplier_id"
	if info.SupplierID > 0 {

		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id "
		settlementDB = settlementDB.Where("suppliers.id = ?", info.SupplierID)
	}

	if info.SupplierName != "" {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id "
		settlementDB = settlementDB.Where("suppliers.name = ?", info.SupplierName)
	}
	if info.Phone != "" {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id "
		settlementDB = settlementDB.Where("suppliers.mobile = ?", info.Phone)

	}
	if info.RealName != "" {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id "
		settlementDB = settlementDB.Where("suppliers.realname = ?", info.RealName)

	}

	if info.CategoryId > 0 {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id "
		settlementDB = settlementDB.Where("suppliers.category_id = ?", info.CategoryId)
	}

	orderJoin = "INNER join orders on orders.id = supplier_settlements.order_id"

	var settlementStats SettlementDataTotal

	if joinWhere != "" {
		settlementDB = settlementDB.Joins(joinWhere)
	}
	// 查询供应商结算统计数据
	settlementDB = settlementDB.Joins(orderJoin).Count(&settlementStats.OrderTotalCount)

	//settlementDB.Select("count(*) as order_total_count").Row().Scan(&settlementStats.OrderTotalCount)
	settlementDB.Select("COALESCE(SUM(orders.amount), 0)").Row().Scan(&settlementStats.OrderTotalAmount)

	// 订单总金额和订单数量
	settlementDB.Select("COALESCE(SUM(settlement_amount), 0) as order_total_amount").Row().Scan(&settlementStats.OrderTotalAmount, &settlementStats.OrderTotalCount)

	// 账单总金额（结算金额）
	settlementDB.Select("COALESCE(SUM(settlement_amount), 0) as bill_total_amount").Row().Scan(&settlementStats.BillTotalAmount)

	// 技术服务费总金额
	settlementDB.Select("COALESCE(SUM(technical_service_cost), 0) as technical_services_total_amount").Row().Scan(&settlementStats.TechnicalServicesTotalAmount)

	// 已提现金额（已经申请提现的）
	settlementDB.Where("withdrawal_status=2").Select("COALESCE(SUM(settlement_amount), 0) as withdrawn_total_amount").Row().Scan(&settlementStats.WithdrawnTotalAmount)

	// 未提现金额（状态为未提现的）
	settlementDB.Where("withdrawal_status = 0").Select("COALESCE(SUM(settlement_amount), 0) as not_withdrawn").Row().Scan(&settlementStats.NotWithdrawn)

	// 提现中金额（状态为处理中的）
	settlementDB.Where("withdrawal_status=1 and remit_status=3").Select("COALESCE(SUM(settlement_amount), 0) as withdrawing_in_progress").Row().Scan(&settlementStats.WithdrawingInProgress)

	// 提现手续费总额
	settlementDB.Select("COALESCE(SUM(withdrawal_fee), 0) as withdrawal_total_fee").Row().Scan(&settlementStats.WithdrawalTotalFee)

	// 无效账单金额（状态为无效的）
	settlementDB.Where("withdrawal_status=2").Select("COALESCE(SUM(settlement_amount), 0) as invalid_bill").Row().Scan(&settlementStats.InvalidBill)

	// 已支付金额（所有已完成支付的结算金额）
	settlementDB.Where("(withdrawal_status=1 and remit_status=1").Select("COALESCE(SUM(settlement_amount), 0) as paid_amount").Row().Scan(&settlementStats.PaidAmount)

	// 更新供应商账单统计数据
	//SupplierBill[i].OrderTotalAmount = settlementStats.OrderTotalAmount
	//SupplierBill[i].OrderTotalCount = settlementStats.OrderTotalCount
	//SupplierBill[i].BillTotalAmount = settlementStats.BillTotalAmount
	//SupplierBill[i].TechnicalServicesTotalAmount = settlementStats.TechnicalServicesTotalAmount
	//SupplierBill[i].WithdrawnTotalAmount = settlementStats.WithdrawnTotalAmount
	//SupplierBill[i].NotWithdrawn = settlementStats.NotWithdrawn
	//SupplierBill[i].WithdrawingInProgress = settlementStats.WithdrawingInProgress
	//SupplierBill[i].WithdrawalTotalFee = settlementStats.WithdrawalTotalFee
	//SupplierBill[i].InvalidBill = settlementStats.InvalidBill
	//SupplierBill[i].PaidAmount = settlementStats.PaidAmount

	return err, settlementStats

}
