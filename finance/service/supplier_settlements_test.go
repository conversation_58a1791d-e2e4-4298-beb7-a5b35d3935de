package service

import (
	"finance/request"
	"reflect"
	"testing"
)

func TestGetSupplierBill(t *testing.T) {
	var info request.SupplierBillSearch
	info.Page = 1
	info.PageSize = 10

	GetSupplierBill(info)

}

func TestGetSupplierBill1(t *testing.T) {
	type args struct {
		info request.SupplierBillSearch
	}
	tests := []struct {
		name      string
		args      args
		wantErr   error
		wantList  interface{}
		wantTotal int64
	}{
		{name: "TestGetSupplierBill1", args: args{info: request.SupplierBillSearch{}}, wantErr: nil, wantList: nil, wantTotal: 0},
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.args.info.Page = 1
			tt.args.info.PageSize = 30
			gotErr, gotList, gotTotal := GetSupplierBill(tt.args.info)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("GetSupplierBill() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if !reflect.DeepEqual(gotList, tt.wantList) {
				t.Errorf("GetSupplierBill() gotList = %v, want %v", gotList, tt.wantList)
			}
			if gotTotal != tt.wantTotal {
				t.Errorf("GetSupplierBill() gotTotal = %v, want %v", gotTotal, tt.wantTotal)
			}
		})
	}
}

func TestGetTotalBill(t *testing.T) {
	type args struct {
		info request.SupplierBillSearch
	}
	tests := []struct {
		name     string
		args     args
		wantErr  error
		wantData SettlementDataTotal
	}{
		// TODO: Add test cases.
		{name: "111", args: args{info: request.SupplierBillSearch{SupplierID: 2}}, wantErr: nil, wantData: SettlementDataTotal{}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErr, gotData := GetTotalBill(tt.args.info)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("GetTotalBill() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if !reflect.DeepEqual(gotData, tt.wantData) {
				t.Errorf("GetTotalBill() gotData = %v, want %v", gotData, tt.wantData)
			}
		})
	}
}

func TestGetSupplierSettlementData(t *testing.T) {

	var info request.AccountApplySearch

	GetSupplierSettlementData(info)

}
