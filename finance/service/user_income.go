package service

import (
	"errors"
	mq "finance/incomeMq"
	"finance/model"
	"finance/request"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"gorm.io/gorm"
	"os"
	"strconv"
	"time"
	"user/service"
	"yz-go/config"
	"yz-go/source"
	"yz-go/utils"
)

func GetIncomeTypeList() map[int]string {
	return model.StatusNameMap
}

func GetIncomeTypeName(typeID int) string {
	return model.StatusNameMap[typeID]
}

func QueueIncome(income model.UserIncomeDetails) (err error) {
	err = source.DB().Transaction(func(tx *gorm.DB) error {
		var userIncome model.UserIncome
		err = tx.First(&userIncome, "user_id = ?", income.UserID).Error
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			userIncome.UserID = income.UserID
			err = tx.Create(&userIncome).Error
			if err != nil {
				return err
			}
		}
		income.Balance = userIncome.IncomeAmount + income.Amount
		if errInfo := tx.Create(&income).Error; err != nil {
			return errInfo
		}
		userIncome.IncomeAmount = userIncome.IncomeAmount + income.Amount
		//userIncome.TotalAmount = userIncome.TotalAmount + income.Amount
		if errInfo := tx.Updates(&userIncome).Error; err != nil {
			return errInfo
		}
		return err
	})
	if err != nil {
		return err
	}

	return
}

func IncreaseIncome(income model.UserIncomeDetails) (err error) {

	err = mq.PublishMessage(income)

	return
}

func GetIncomeDetailList(info request.IncomeSearch) (err error, income []model.UserIncomeDetails, total int64) {

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(model.UserIncomeDetails{})

	if info.UserID > 0 {
		db.Where("user_id=?", info.UserID)
	}
	if info.OrderSN != "" {
		db.Where("order_sn=?", info.OrderSN)
	}
	if info.IncomeType > 0 {
		db.Where("income_type=?", info.IncomeType)
	}

	var joinWhere string
	if info.LevelID > 0 {
		joinWhere = "INNER join users on users.id = user_income_details.user_id and users.level_id=" + strconv.Itoa(int(info.LevelID))

		db.Joins(joinWhere)
	}
	if info.DefaultLevel {
		joinWhere = "INNER join users on users.id = user_income_details.user_id and users.level_id=0"

		db.Joins(joinWhere)
	}

	if info.TimeS != "" && info.TimeE != "" {
		db = db.Where("created_at BETWEEN ? AND ?", info.TimeS, info.TimeE)

	}
	db.Count(&total)
	err = db.Limit(limit).Offset(offset).Preload("User").Preload("Order").Preload("Order.User").Order("created_at desc,id desc").Find(&income).Error
	return
}

func ExportIncomeDetailList(info request.IncomeSearch) (err error, link string) {

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(model.UserIncomeDetails{})

	if info.UserID > 0 {
		db.Where("user_id=?", info.UserID)
	}
	if info.OrderSN != "" {
		db.Where("order_sn=?", info.OrderSN)
	}
	if info.IncomeType > 0 {
		db.Where("income_type=?", info.IncomeType)
	}
	if info.LevelID > 0 {
		//db.Where("income_type=?",info.IncomeType)
	}
	if info.TimeS != "" && info.TimeE != "" {
		db = db.Where("created_at BETWEEN ? AND ?", info.TimeS, info.TimeE)

	}
	var income []model.UserIncomeDetails
	//db.Count(&total)
	err = db.Limit(limit).Offset(offset).Preload("User").Find(&income).Error

	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")
	// 设置单元格的值
	f.SetCellValue("Sheet1", "A1", "时间")
	f.SetCellValue("Sheet1", "B1", "会员ID")
	f.SetCellValue("Sheet1", "C1", "用户名")
	f.SetCellValue("Sheet1", "D1", "订单号")
	f.SetCellValue("Sheet1", "E1", "收入类型")
	f.SetCellValue("Sheet1", "F1", "收入变动金额")
	f.SetCellValue("Sheet1", "G1", "收入变动后余额")

	i := 2
	for _, v := range income {

		//var businessType string
		//var balanceAmount string

		amount := strconv.FormatFloat(Decimal(float64(v.Amount)/float64(100)), 'f', -1, 64)
		balance := strconv.FormatFloat(Decimal(float64(v.Balance)/float64(100)), 'f', -1, 64)
		//businessType += " 订单ID:" + v.OrderID + " 支付单：" + strconv.Itoa(int(v.PaySN))
		f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), v.CreatedAt)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), v.User.ID)
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), v.User.Username)
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), v.OrderSn)
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), GetIncomeTypeName(v.IncomeType))
		f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), amount)
		f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), balance)

		i++

	}
	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)
	// 根据指定路径保存文件
	//year, month, day := time.Now().Format("2006-01-02 15:04:05")
	time := time.Now().Format("20060102150405")
	path := config.Config().Local.Path + "/export_finance"
	exist, _ := utils.PathExists(path)

	if !exist {
		// 创建文件夹
		err = os.Mkdir(path, os.ModePerm)
		if err != nil {
			fmt.Printf("mkdir failed![%v]\n", err)
		} else {
			fmt.Printf("mkdir success!\n")
		}
	}
	link = path + "/" + time + "收入明细导出.xlsx"
	if err = f.SaveAs(link); err != nil {
		return
	}

	return err, link

}

func GetUserIncome(info request.IncomeSearch) (err error, incomeList []model.UserIncomeSummary, total int64) {

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	var user []model.User
	db := source.DB().Model(model.User{})

	if info.Username != "" {
		db = db.Where("`username` LIKE ?", "%"+info.Username+"%")
	}

	if info.LevelID > 0 {
		db.Where("level_id = ? ", info.LevelID)
	}
	if info.DefaultLevel {
		db.Where("level_id = 0")
	}

	if info.IncomeS > 0 && info.IncomeE > 0 {
		db = db.Where("(select  income_amount  from user_incomes where user_id =users.id ) BETWEEN ? AND ?", info.IncomeS, info.IncomeE)

	}
	//"(select sum(invalid_amount) from withdrawal_operation_records where withdrawal_id =users.id and `withdrawal_type`=2) as invalid," +
	db.Count(&total)
	err = db.Limit(limit).Offset(offset).Find(&user).Error

	for _, item := range user {
		incomesResponse := service.GetIncomes(item.ID)
		var userIncomeSummary model.UserIncomeSummary
		userIncomeSummary.ID = item.ID
		userIncomeSummary.Mobile = item.Mobile
		userIncomeSummary.NickName = item.NickName
		userIncomeSummary.InWithdrawal = uint(incomesResponse.WithdrawalOf)
		userIncomeSummary.NotWithdrawal = uint(incomesResponse.NoWithdrawal)
		userIncomeSummary.IsWithdrawal = uint(incomesResponse.HaveWithdrawal)
		userIncomeSummary.TotalWithdrawal = uint(incomesResponse.Total)
		userIncomeSummary.Invalid = uint(incomesResponse.Invalid)
		userIncomeSummary.PoundageAmountTotal = uint(incomesResponse.PoundageAmountTotal)
		userIncomeSummary.ServiceTaxTotal = uint(incomesResponse.ServiceTaxTotal)
		incomeList = append(incomeList, userIncomeSummary)
	}

	return

}
