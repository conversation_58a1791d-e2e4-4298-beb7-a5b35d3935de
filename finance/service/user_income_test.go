package service

import (
	"finance/model"
	"finance/request"
	"fmt"
	"reflect"
	"testing"
	"time"
)

func BenchmarkCombinationParallel(b *testing.B) {
	// 测试⼀个对象或者函数在多线程的场景下⾯是否安全

	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {

			var aa model.UserIncomeDetails
			aa.UserID = 2
			aa.OrderSn = 33333
			aa.IncomeType = 1
			aa.Amount = 3000
			IncreaseIncome(aa)
		}
	})
}

//并⾏测试
func TestBenchmarkSplitParallel(b *testing.T) {
	//b.SetParallelism(4) //设置测试使⽤的CPU数
	//b.RunParallel(func(pb *testing.PB) {
	//	for pb.Next() {

	//for i := 0; i <= 5; i++ {
	//
	//	var aa model.UserIncomeDetails
	//	aa.UserID = 2
	//	aa.OrderSn = 33333
	//	aa.IncomeType = 1
	//	aa.Amount = 3000
	//	IncreaseIncome(aa)
	//
	//}

	c := make(chan int)

	for a := 0; a <= 2; a++ {
		go func(a int) {
			for p := range c {
				fmt.Println("携程", a)
				fmt.Println(p)
			}
			fmt.Println("结束：", a)
			//fmt.Printf("jieshu", a)
			//   }
		}(a)
	}

	time.Sleep(time.Second * 5)

	for i := 0; i < 100; i++ {
		c <- i
	}
	close(c)
	time.Sleep(time.Millisecond)

	//time.Sleep(time.Second * 100000)

	//	}
	//})
}

func TestIncreaseIncome(t *testing.T) {

	var aa model.UserIncomeDetails

	aa.UserID = 2
	aa.OrderSn = 33333
	aa.IncomeType = 1

	aa.Amount = 3000

	t.Run("aaa", func(t *testing.T) {
		err := IncreaseIncome(aa)
		IncreaseIncome(aa)
		if err != nil {
			t.Log("aafggg")
			return
		}

		fmt.Println("names:", t.Name())

	})
	t.Run("bbb", func(t *testing.T) {
		err := IncreaseIncome(aa)
		IncreaseIncome(aa)
		if err != nil {
			t.Log("aafggg")
			return
		}

		fmt.Println("names:", t.Name())

	})

	t.Run("ccc", func(t *testing.T) {
		err := IncreaseIncome(aa)
		IncreaseIncome(aa)
		if err != nil {
			t.Log("aafggg")
			return
		}

		fmt.Println("names:", t.Name())

	})

}

func TestGetIncomeDetailList(t *testing.T) {
	type args struct {
		info request.IncomeSearch
	}
	tests := []struct {
		name       string
		args       args
		wantErr    error
		wantIncome []model.UserIncome
		wantTotal  int64
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErr, gotIncome, gotTotal := GetIncomeDetailList(tt.args.info)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("GetIncomeDetailList() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if !reflect.DeepEqual(gotIncome, tt.wantIncome) {
				t.Errorf("GetIncomeDetailList() gotIncome = %v, want %v", gotIncome, tt.wantIncome)
			}
			if gotTotal != tt.wantTotal {
				t.Errorf("GetIncomeDetailList() gotTotal = %v, want %v", gotTotal, tt.wantTotal)
			}
		})
	}
}

func TestGetUserIncome(t *testing.T) {
	type args struct {
		info request.IncomeSearch
	}
	tests := []struct {
		name       string
		args       args
		wantErr    error
		wantIncome []model.UserIncome
		wantTotal  int64
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErr, gotIncome, gotTotal := GetUserIncome(tt.args.info)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("GetUserIncome() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if !reflect.DeepEqual(gotIncome, tt.wantIncome) {
				t.Errorf("GetUserIncome() gotIncome = %v, want %v", gotIncome, tt.wantIncome)
			}
			if gotTotal != tt.wantTotal {
				t.Errorf("GetUserIncome() gotTotal = %v, want %v", gotTotal, tt.wantTotal)
			}
		})
	}
}

func TestIncreaseIncome1(t *testing.T) {
	type args struct {
		income model.UserIncomeDetails
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := IncreaseIncome(tt.args.income); (err != nil) != tt.wantErr {
				t.Errorf("IncreaseIncome() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
