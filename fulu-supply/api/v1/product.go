package v1

import (
	"fmt"
	millennium "fulu-supply/component/goods"
	"fulu-supply/request"
	"fulu-supply/service"
	v1 "gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
	"strings"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	service2 "yz-go/service"
	"yz-go/utils"
)

type ProductID struct {
	ProductID uint `json:"product_id" form:"product_id" query:"product_id"`
}

func RelieveLockProduct(c *gin.Context) {
	var req ProductID
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.RelieveLockProduct(req.ProductID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("解除锁定成功", c)
	}
}

func LockProduct(c *gin.Context) {
	var req ProductID
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.LockProduct(req.ProductID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("锁定成功", c)
	}
}

func DeleteProduct(c *gin.Context) {
	var req ProductID
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.DeleteProduct(req.ProductID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

func DisplayProductByIds(c *gin.Context) {
	var IDS yzRequest.IdsReqWithValue
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		yzResponse.FailWithMessage("批量修改失败", c)
		return
	}
	if err = utils.Verify(IDS); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.DisplayProductByIds(IDS); err != nil {
		yzResponse.FailWithMessage("批量修改失败!", c)
		return
	} else {
		var idsString []string
		for _, v := range IDS.Ids {
			idsString = append(idsString, strconv.Itoa(int(v)))
		}
		if IDS.Value == 1 {
			service2.CreateOperationRecord(v1.GetUserID(c), 1, c.ClientIP(), "批量上架了商品'"+strings.Join(idsString, ",")+"'")
		} else {
			service2.CreateOperationRecord(v1.GetUserID(c), 1, c.ClientIP(), "批量下架了商品'"+strings.Join(idsString, ",")+"'")
		}
		yzResponse.OkWithMessage("批量修改成功", c)
	}
}

type Type struct {
	Name  string `json:"name" query:"name"`
	Value int    `json:"value" query:"value"`
}

type Types []Type

func GetImportTypes(c *gin.Context) {
	var types Types
	types = append(types, Type{Name: "福禄", Value: 1})
	types = append(types, Type{Name: "会员权益", Value: 2})
	yzResponse.OkWithData(gin.H{"types": types}, c)
}

func SyncProduct(c *gin.Context) {
	var params request.SyncProductType

	if err := c.ShouldBindJSON(&params); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err := syncProduct(params); err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("同步商品成功", c)
}

func syncProduct(params request.SyncProductType) error {
	switch params.Type {
	// 福禄
	case 1:
		return service.ImportFuLuProduct()
	// 会员权益
	case 2:
		return service.ImportProductRun()
	// 权益商品
	case 126:
		return service.ImportLeiJueEquityProductRun(0)
	// 千禧资源
	case 127:
		millenniumSupply := millennium.MillenniumSupply{}

		if err := millenniumSupply.InitSetting(0); err != nil {
			return err
		}

		if err := millenniumSupply.SynchronizeProductsToLocal(); err != nil {
			return err
		}
	default:
		return fmt.Errorf("未知的供应渠道：%d", params.Type)
	}
	return nil
}

func GetProductList(c *gin.Context) {
	var pageInfo request.ProductSearch

	if err := c.ShouldBindJSON(&pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list, total := service.GetProductList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func ExportProductList(c *gin.Context) {
	var pageInfo request.ProductSearch

	if err := c.ShouldBindJSON(&pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, link := service.ExportProductList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}
