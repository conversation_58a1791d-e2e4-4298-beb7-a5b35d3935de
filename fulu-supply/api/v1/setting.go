package v1

import (
	"errors"
	"fmt"
	"fulu-supply/model"
	"fulu-supply/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"public-supply/setting"
	"strconv"
	"yz-go/component/log"
	model2 "yz-go/model"
	yzResponse "yz-go/response"
)

func GetSupplyID(c *gin.Context) {
	err, supplyID := service.GetFuluSupplyID()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取供应链id失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"supply_id": supplyID}, c)
}

func GetEquitySupplyID(c *gin.Context) {
	err, supplyID := service.GetEquitySupplyID()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取供应链id失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"supply_id": supplyID}, c)
}

func FindSetting(c *gin.Context) {
	err, fuluSetting := service.FuLuSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取设置失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"setting": fuluSetting}, c)
}

func UpdateSetting(c *gin.Context) {
	var fuluSetting model.Setting
	err := c.ShouldBindJSON(&fuluSetting)
	if err != nil {
		return
	}
	err = service.SaveFuLuSetting(fuluSetting)
	if err != nil {
		yzResponse.FailWithMessage("保存失败", c)
	}
	yzResponse.OkWithMessage("保存成功", c)
}

func FindUserEquitySetting(c *gin.Context) {
	err, equitySetting := service.GetUserEquitySetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取设置失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"setting": equitySetting}, c)
}

func UpdateUserEquitySetting(c *gin.Context) {
	var equitySetting model.Setting
	err := c.ShouldBindJSON(&equitySetting)
	if err != nil {
		return
	}
	err = service.SaveUserEquitySetting(equitySetting)
	if err != nil {
		yzResponse.FailWithMessage("保存失败", c)
	}
	yzResponse.OkWithMessage("保存成功", c)
}

func GetSupplySetting(c *gin.Context) {
	var supplyID uint
	var err error
	err, supplyID = service.GetFuluSupplyID()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取福禄供应链id失败", c)
		return
	}
	var supplySetting model2.SysSetting
	var key string
	key = "gatherSupply" + strconv.Itoa(int(supplyID))
	err, supplySetting = setting.GetSetting(key)
	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err, c)
		return
	}
	var maps = make(map[string]interface{})
	maps["data"] = supplySetting
	maps["domain"] = c.Request.Host
	maps["proto"] = c.Request.Header.Get("X-Forwarded-Proto")

	maps["url"] = c.Request.Host

	yzResponse.OkWithData(maps, c)
}

func SetSupplySetting(c *gin.Context) {
	var Setting *model2.SysSetting
	err := c.ShouldBindJSON(&Setting)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if Setting.Key == "" {
		var supplyID uint
		err, supplyID = service.GetFuluSupplyID()
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			yzResponse.FailWithMessage("获取福禄供应链id失败", c)
			return
		}
		var key string
		key = "gatherSupply" + strconv.Itoa(int(supplyID))
		Setting.Key = key
	}
	if err := setting.SetSetting(Setting); err != nil {
		log.Log().Error("设置失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err, c)
		return
	} else {
		yzResponse.Ok(c)
	}
}
