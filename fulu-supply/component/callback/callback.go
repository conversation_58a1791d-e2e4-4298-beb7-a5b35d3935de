package callback

import (
	"fulu-supply/model"
	"fulu-supply/service"
	"go.uber.org/zap"
	orderModel "order/model"
	"order/order"
	model2 "product/model"
	productMq "product/mq"
	pubmodel "public-supply/model"
	"strings"
	"yz-go/component/log"
	"yz-go/source"
)

type Fulu struct{}

func (*Fulu) CallBackMessage(request pubmodel.RequestCallBackType) (err error) {

	log.Log().Error("福禄回调CallBackMessage", zap.Any("request", request))

	if find := strings.Contains(request.MessageType, "fuluOrder"); find {
		log.Log().Error("福禄权益商品订单监听:order_sn:" + request.OrderSn + "message_type:" + request.MessageType)
		if request.MessageType == "fuluOrder.delivery" {
			var fuluOrder model.FuluSupplyOrderResult
			err = source.DB().Where("customer_order_no = ? and product_source = 1", request.OrderSn).First(&fuluOrder).Error
			if err != nil {
				log.Log().Error("福禄权益商品订单监听:查询福禄订单失败", zap.Any("info", err.Error()))
				return
			}
			var shopOrder orderModel.Order
			err = source.DB().Where("`order_sn` = ?", fuluOrder.OrderId).First(&shopOrder).Error
			if err != nil {
				log.Log().Error("福禄权益商品订单监听:查询中台订单失败", zap.Any("info", err.Error()))
				return
			}
			if err = order.Send(shopOrder.ID); err != nil {
				log.Log().Error("福禄权益商品订单监听:订单发货操作失败!", zap.Any("err", err))
				return
			}
			if err = order.Received(shopOrder.ID); err != nil {
				log.Log().Error("福禄权益商品订单监听:订单收货操作失败!", zap.Any("err", err))
				return
			}
		}
	}
	// 福禄商品变动
	if find := strings.Contains(request.MessageType, "fuluGoods"); find {

	}
	// 会员权益商品变动
	if find := strings.Contains(request.MessageType, "equityGoods"); find {
		log.Log().Error("福禄权益商品监听", zap.Any("request", request))
		// 修改
		if request.MessageType == "equityGoods.goods.alter" {
			// 再次请求接口更新商品
			err = service.ProductEditSync(request.ProductID)
			if err != nil {
				log.Log().Error("更新福禄权益商品失败!", zap.Any("err", err))
				return
			}
		}
		// 下架
		if request.MessageType == "equityGoods.goods.undercarriage" {
			log.Log().Error("equityGoods.goods.undercarriage")
			var supplyID uint
			err, supplyID = service.GetFuluSupplyID()
			if err != nil {
				log.Log().Error("查询福禄供应链id失败!", zap.Any("err", err))
				return
			}
			var fuluGoods model.FuluSupplyGoods
			err = source.DB().Model(&fuluGoods).Where("product_id = ? and product_source = ?", request.ProductID, 1).First(&fuluGoods).Error
			if err != nil {
				log.Log().Error("查询福禄商品表失败!", zap.Any("err", err))
				return
			}
			err = source.DB().Model(&model2.Product{}).Where("gather_supply_id = ? and is_plugin = ? and is_display = ? and source_goods_id = ?", supplyID, 1, 1, request.ProductID).Update("is_display", 0).Error
			if err != nil {
				log.Log().Error("修改福禄权益商品下架失败!", zap.Any("err", err))
				return
			}
			var goods model2.Product
			err = source.DB().Model(&model2.Product{}).Where("gather_supply_id = ? and is_plugin = ? and source_goods_id = ?", supplyID, 1, request.ProductID).First(&goods).Error
			if err != nil {
				log.Log().Error("查询商品表失败!", zap.Any("err", err))
				return
			}
			err = productMq.PublishMessage(goods.ID, productMq.Undercarriage, 0)
			if err != nil {
				log.Log().Error("福禄权益商品mq失败!", zap.Any("err", err))
				return
			}
		}
		// 上架
		if request.MessageType == "equityGoods.goods.on.sale" {
			log.Log().Error("equityGoods.goods.on.sale")
			var supplyID uint
			err, supplyID = service.GetFuluSupplyID()
			if err != nil {
				log.Log().Error("查询福禄供应链id失败!", zap.Any("err", err))
				return
			}
			var fuluGoods model.FuluSupplyGoods
			err = source.DB().Model(&fuluGoods).Where("product_id = ? and product_source = ?", request.ProductID, 1).First(&fuluGoods).Error
			if err != nil {
				log.Log().Error("查询福禄商品表失败!", zap.Any("err", err))
				return
			}
			err = source.DB().Model(&model2.Product{}).Where("gather_supply_id = ? and is_plugin = ? and is_display = ? and source_goods_id = ?", supplyID, 1, 0, request.ProductID).Update("is_display", 1).Error
			if err != nil {
				log.Log().Error("修改福禄权益商品上架失败!", zap.Any("err", err))
				return
			}
			var goods model2.Product
			err = source.DB().Model(&model2.Product{}).Where("gather_supply_id = ? and is_plugin = ? and source_goods_id = ?", supplyID, 1, request.ProductID).First(&goods).Error
			if err != nil {
				log.Log().Error("查询商品表失败!", zap.Any("err", err))
				return
			}
			err = productMq.PublishMessage(goods.ID, productMq.OnSale, 0)
			if err != nil {
				log.Log().Error("福禄权益商品mq失败!", zap.Any("err", err))
				return
			}
		}
		// 删除
		if request.MessageType == "equityGoods.goods.delete" {
			log.Log().Error("equityGoods.goods.delete")
			var supplyID uint
			err, supplyID = service.GetFuluSupplyID()
			if err != nil {
				log.Log().Error("查询福禄供应链id失败!", zap.Any("err", err))
				return
			}
			var fuluGoods model.FuluSupplyGoods
			err = source.DB().Where("product_id = ? and product_source = ?", request.ProductID, 1).Delete(&fuluGoods).Error
			if err != nil {
				log.Log().Error("删除福禄商品表失败!", zap.Any("err", err))
				return
			}
			var goods model2.Product
			err = source.DB().Model(&model2.Product{}).Where("gather_supply_id = ? and is_plugin = ? and source_goods_id = ?", supplyID, 1, request.ProductID).First(&goods).Error
			if err != nil {
				log.Log().Error("查询商品表失败!", zap.Any("err", err))
				return
			}
			err = source.DB().Delete(&goods).Error
			if err != nil {
				log.Log().Error("删除商品表失败!", zap.Any("err", err))
				return
			}
			err = productMq.PublishMessage(goods.ID, productMq.Delete, 0)
			if err != nil {
				log.Log().Error("福禄权益商品mq失败!", zap.Any("err", err))
				return
			}
		}
	}
	return
}
