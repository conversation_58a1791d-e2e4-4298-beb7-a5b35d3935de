package goods

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"event-distribution/mq"
	"fmt"
	"fulu-supply/model"
	"fulu-supply/service"
	gatherSupplyRequest "gather-supply/request"
	"github.com/li-bao-jia/millennium"
	"github.com/li-bao-jia/millennium/pkg/product"
	"github.com/xingliuhua/leaf"
	"gorm.io/gorm"
	"math"
	productModel "product/model"
	productMq "product/mq"
	productService "product/service"
	callback2 "public-supply/callback"
	"public-supply/common"
	publicModel "public-supply/model"
	publicSupplyModel "public-supply/model"
	publicSupplyRequest "public-supply/request"
	publicService "public-supply/service"
	"strconv"
	"strings"
	"sync"
	"time"
	"yz-go/source"
)

type MillenniumSupply struct{}

func (m *MillenniumSupply) ManuallyProductUpdate(productID uint) (err error) {
	//TODO implement me
	panic("implement me")
}

var Setting model.Setting

func (m *MillenniumSupply) InitSetting(gatherSupplyID uint) (err error) {
	if err, Setting = service.MillenniumSetting(); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("请先配置基础信息")
		return
	}
	if Setting.Values.BaseInfo.AppKey == "" || Setting.Values.BaseInfo.AppSecret == "" {
		err = errors.New("请先配置供应链AppKey、AppSecret")
		return
	}
	return
}

func (m *MillenniumSupply) InitGoods() (err error) {
	// 正式环境
	client := millennium.NewApiClient(
		"https://openapi.qianxiquan.com",
		Setting.Values.BaseInfo.AppKey,
		Setting.Values.BaseInfo.AppSecret,
	)

	// 测试环境
	//client := millennium.NewApiClient(
	//	"https://testopen.qianxiquan.com",
	//	Setting.Values.BaseInfo.AppKey,
	//	Setting.Values.BaseInfo.AppSecret,
	//)

	// 调用接口
	resp, err := client.CallApi(&product.ListProduct{}, product.ListProductParams{})
	if err != nil {
		return
	}

	if resp.Code != 0 {
		err = errors.New(resp.Msg)
		return
	}

	// 转换商品
	var apiProducts []product.Product
	if err = json.Unmarshal([]byte(resp.Data), &apiProducts); err != nil {
		return
	}

	// 本地千禧券选品库 map 数据
	var localProductMap map[uint]model.MillenniumCloudProduct
	if localProductMap, err = service.LocalMillenniumCloudProductMap(); err != nil {
		return
	}

	// 同步千禧券选品库：创建、更新
	var updateProducts []map[string]interface{}
	var createProducts []model.MillenniumCloudProduct
	for _, apiProduct := range apiProducts {
		/*if apiProduct.ID != 160368 {
			continue
		}*/
		apiProductMD5 := productMD5(apiProduct)

		price, _ := strconv.ParseFloat(apiProduct.Price, 64)
		faceValue, _ := strconv.ParseFloat(apiProduct.DisplayPrice, 64)
		// 计算利润率 profitRate = (面值-供货价)/供货价 * 100
		var profitRate float64
		// 供货价为0或面值为0或面值小于供货价，利润率为0
		if price == 0 || faceValue == 0 || faceValue < price {
			profitRate = 0
		} else {
			profitRate = productService.Decimal((faceValue-price)/price) * 100
		}
		// 替换商品详情中的图片地址
		apiProduct.Details = strings.ReplaceAll(apiProduct.Details, "/uploads", "https://openapi.qianxiquan.com/uploads")
		if localProduct, ok := localProductMap[uint(apiProduct.ID)]; ok {
			// 如果 MD5 不相等，更新
			if apiProductMD5 != localProduct.MD5 {
				updateProducts = append(updateProducts, map[string]interface{}{
					"id":             localProduct.ID,
					"product_id":     apiProduct.ID,
					"face_value":     faceValue,
					"purchase_price": price,
					"product_name":   apiProduct.ProductName,
					"product_type":   apiProduct.ProductType,
					"storage":        apiProduct.Storage,
					"sales_status":   apiProduct.SalesStatus,
					"profit_rate":    profitRate,
					"details":        apiProduct.Details,
					"md5":            apiProductMD5,
				})
			}
			delete(localProductMap, uint(apiProduct.ID))
		} else {
			// 创建
			createProducts = append(createProducts, model.MillenniumCloudProduct{
				ProductId:     uint(apiProduct.ID),
				ProductName:   apiProduct.ProductName,
				ProductType:   apiProduct.ProductType,
				FaceValue:     faceValue,
				PurchasePrice: price,
				Storage:       uint(apiProduct.Storage),
				SalesStatus:   apiProduct.SalesStatus,
				ProfitRate:    profitRate,
				Details:       apiProduct.Details,
				MD5:           apiProductMD5,
			})
		}
	}

	// 剩余的 map 组装需要删除的 ids
	var ids []uint
	var productIds []uint
	for _, item := range localProductMap {
		ids = append(ids, item.ID)
		productIds = append(productIds, item.ProductId)
	}

	err = source.DB().Transaction(func(db *gorm.DB) (err error) {
		// 批量更新
		if len(updateProducts) > 0 {
			err = source.BatchUpdate(updateProducts, "millennium_cloud_products", "id")
			if err != nil {
				return
			}
		}

		// 批量创建
		if len(createProducts) > 0 {
			err = source.DB().CreateInBatches(&createProducts, 1000).Error
			if err != nil {
				return
			}
		}

		// 批量删除
		if len(ids) > 0 {
			// 删除选品库数据
			if err = service.DeleteMillenniumCloudProduct(ids); err != nil {
				return
			}

			// 查询需要下架的商品ID
			var takeOffIds []uint
			if err = source.DB().Model(&productModel.Product{}).Where("is_display = 1").Where("source = 127").Where("source_goods_id in ?", productIds).Pluck("id", &takeOffIds).Error; err != nil {
				return
			}

			// 下架商品
			if err = source.DB().Model(&productModel.Product{}).Where("id in ?", takeOffIds).Update("is_display", 0).Error; err != nil {
				return
			}

			// 发送下架消息
			for _, takeOffId := range takeOffIds {
				err = mq.PublishMessage(takeOffId, mq.Undercarriage, 0)
				if err != nil {
					return
				}
			}
		}

		return nil
	})

	return
}

// 千禧券选品库商品转 md5

func productMD5(product product.Product) string {
	var jsonResult []byte
	jsonResult, _ = json.Marshal(product)

	hash := md5.New()
	hash.Write(jsonResult)
	return hex.EncodeToString(hash.Sum(nil))
}

func (m *MillenniumSupply) GetGoods(search publicSupplyRequest.GetGoodsSearch) (err error, data interface{}, total int64, serverRatio int) {
	// 查询已导入的ids
	var importIds []uint
	if importIds, err = service.SearchFuLuProductIds(service.SearchFuLuProduct{ProductSource: common.MILLENNIUM_SOURCE}); err != nil {
		return
	}

	// 列表数据查询
	db := source.DB().Model(&model.MillenniumCloudProduct{})

	if search.Title != "" {
		db.Where("`product_name` like ?", "%"+search.Title+"%")
	}
	if search.TypeName != "" {
		db.Where("`product_type` = ?", search.TypeName)
	}
	if search.PromotionRate.To > 0 {
		db.Where("profit_rate < ?", search.PromotionRate.To)
	}
	if search.PromotionRate.From > 0 {
		db.Where("profit_rate >= ?", search.PromotionRate.From)
	}
	if search.IsImport > 0 {
		switch search.IsImport {
		case 1:
			db.Where("`product_id` in ?", importIds)
		case 2:
			db.Where("`product_id` not in ?", importIds)
		}
	}
	if search.Sort != "" && search.Type != "" {
		db.Order(search.Type + " " + search.Sort)
	}

	if err = db.Count(&total).Error; err != nil {
		return
	}

	var list []model.MillenniumCloudProduct
	limit := search.Limit
	offset := search.Limit * (search.Page - 1)

	if err = db.Limit(limit).Offset(offset).Find(&list).Error; err != nil {
		return
	}

	// 查询千禧券供应链ID
	_, gatherID := service.GetMillenniumSupplyID()

	// 组装已导入ids map
	importIdsMap := make(map[uint]bool)
	for _, id := range importIds {
		importIdsMap[id] = true
	}

	var products []publicModel.Goods
	for _, item := range list {
		products = append(products, convertProduct(item, gatherID, importIdsMap))
	}

	return err, products, total, 0
}

type PriceStrategy struct {
	Price         uint // 供货价
	CostPrice     uint // 成本价
	GuidePrice    uint // 指导价
	OriginPrice   uint // 市场价
	ActivityPrice uint // 营销价
}

func getPriceStrategy(pStrategy model.PricingStrategy, price, mPrice float64) PriceStrategy {
	return PriceStrategy{
		Price:         calculatePriceWithRatio(pStrategy.Price, price, mPrice),
		CostPrice:     calculatePriceWithRatio(pStrategy.CostPrice, price, mPrice),
		GuidePrice:    calculatePriceWithRatio(pStrategy.GuidePrice, price, mPrice),
		OriginPrice:   calculatePriceWithRatio(pStrategy.OriginPrice, price, mPrice),
		ActivityPrice: calculatePriceWithRatio(pStrategy.ActivityPrice, price, mPrice),
	}
}

// 根据定价策略设置、商品价、面值价，计算系统存储价
func calculatePriceWithRatio(pricing model.Pricing, price, dPrice float64) uint {
	if pricing.ComputeType == 1 {
		if pricing.PriceRatio > 0 {
			return uint(math.Floor(price*100)) * uint(pricing.PriceRatio) / 10000
		}

		return uint(price)
	}

	if pricing.FaceRatio > 0 {
		return uint(math.Floor(dPrice*100)) * uint(pricing.FaceRatio) / 10000
	}

	return uint(dPrice)
}

func (m *MillenniumSupply) ImportSelectGoodsRun(info publicSupplyModel.SelectGoods) (err error, list interface{}) {
	if len(info.List) <= 0 {
		err = errors.New("导入的是空数据")
		return
	}

	// 查询供应链ID
	_, info.GatherSupplyID = service.GetFuluSupplyID()

	// 查询已导入的ids
	var importIds []uint
	if importIds, err = service.SearchFuLuProductIds(service.SearchFuLuProduct{ProductSource: common.MILLENNIUM_SOURCE}); err != nil {
		return
	}

	// 已导入的ids map
	var importIdsMap = make(map[uint]bool)
	for _, id := range importIds {
		importIdsMap[id] = true
	}

	// 根据提交数据获取 submitIds
	var submitIds []int
	for _, item := range info.List {
		submitIds = append(submitIds, item.ID)
	}

	// 根据 submitIds 从选品库查询数据 mcProducts
	var mcProducts []model.MillenniumCloudProduct
	if err = source.DB().Where("product_id in ?", submitIds).Find(&mcProducts).Error; err != nil {
		err = errors.New("选品库数据获取失败，请稍后重试")
		return
	}

	// 循环 mcProducts 数据，组装最终操作需要的数据
	var (
		handleIds           []int                   // 待操作的ids
		handleGoodsList     []publicModel.Goods     // 待操作的商品列表
		handleFuLuGoodsList []model.FuluSupplyGoods // 待操作的福禄商品列表
	)

	for _, item := range mcProducts {
		if _, ok := importIdsMap[item.ProductId]; !ok {
			product := convertProduct(item, info.GatherSupplyID, importIdsMap)

			handleIds = append(handleIds, int(item.ProductId))
			handleGoodsList = append(handleGoodsList, product)
			handleFuLuGoodsList = append(handleFuLuGoodsList, convertFuLuGoods(item))
		}
	}

	// 插入商品导入记录
	importRecord := publicModel.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             getOrderNo(),
		EstimatedQuantity: len(submitIds),
		Status:            1,
		Source:            "127",
		IsPlugin:          1,
		RepeatQuantity:    len(submitIds) - len(handleIds),
	}
	// 如果没有需要导入的数据，则直接完成
	if len(handleIds) <= 0 {
		importRecord.Status = 2
		importRecord.CompletionStatus = 1
	}
	if err = source.DB().Create(&importRecord).Error; err != nil || len(handleIds) <= 0 {
		return
	}

	// 插入FuLu商品表
	if err = source.DB().CreateInBatches(handleFuLuGoodsList, 500).Error; err != nil {
		err = errors.New("同步数字权益商品操作失败，请稍后重试")
		return
	}

	// 同步商品表
	_ = m.RunSelectGoodsConcurrent(importRecord.Batch, handleGoodsList, info.Categorys, info.Key, info.GatherSupplyID)

	// 更新商品导入记录状态
	err = source.DB().Model(&importRecord).Update("completion_status", 1).Error

	return
}

func convertProduct(goods model.MillenniumCloudProduct, gatherId uint, importIdsMap map[uint]bool) publicModel.Goods {
	pStrategy := getPriceStrategy(Setting.Values.PricingStrategy, goods.PurchasePrice, goods.FaceValue)

	// 判断是否已导入
	var isImport uint = 0
	if importIdsMap[goods.ProductId] {
		isImport = 1
	}

	return publicModel.Goods{
		GatherSupplyID: gatherId,
		ID:             int(goods.ProductId),
		ProductID:      int(goods.ProductId),
		Cover:          "",
		Status:         1,
		Stock:          goods.Storage,
		TotalStock:     int(goods.Storage),
		Title:          goods.ProductName,
		MarketPrice:    uint(goods.FaceValue * 100),
		AgreementPrice: pStrategy.Price,
		CostPrice:      pStrategy.CostPrice,
		GuidePrice:     pStrategy.GuidePrice,
		SalePrice:      pStrategy.OriginPrice,
		ActivityPrice:  pStrategy.ActivityPrice,
		Rate:           goods.ProfitRate,
		IsImport:       isImport,
	}
}

func convertFuLuGoods(goods model.MillenniumCloudProduct) model.FuluSupplyGoods {
	stockStatus := "断货"
	if goods.Storage > 0 {
		stockStatus = "充足"
	}
	return model.FuluSupplyGoods{
		ProductId:       int(goods.ProductId),
		ProductIdString: "",
		ProductName:     goods.ProductName,
		FaceValue:       goods.FaceValue,
		ProductType:     goods.ProductType,
		PurchasePrice:   goods.PurchasePrice,
		StockStatus:     stockStatus,
		SalesStatus:     goods.SalesStatus,
		Details:         goods.Details,
		MD5:             goods.MD5,
		ProductSource:   common.MILLENNIUM_SOURCE,
		LockStatus:      0,
	}
}

func (m *MillenniumSupply) RunSelectGoodsConcurrent(orderPN string, list []publicModel.Goods, category string, key string, gatherSupplyID uint) (err error) {

	cateList := strings.Split(category, ",")
	var cateId1, cateId2, cateId3 int
	cateId1, err = strconv.Atoi(cateList[0])
	cateId2, err = strconv.Atoi(cateList[1])
	cateId3, err = strconv.Atoi(cateList[2])

	var listGoods []*productModel.Product
	var recordError []publicModel.SupplyGoodsImportRecordErrors
	err, listGoods, recordError = m.CommodityAssembly(list, cateId1, cateId2, cateId3, gatherSupplyID, 0)
	if err != nil {
		return
	}
	if len(listGoods) > 0 {
		err = publicService.FinalProcessing(listGoods, orderPN)
		if err != nil {
			return
		}

	}
	if len(recordError) > 0 {
		err = publicService.FinalProcessingError(recordError, orderPN)
		if err != nil {
			return
		}
	}
	return
}

func (m *MillenniumSupply) CommodityAssembly(list []publicSupplyModel.Goods, cateId1, cateId2, cateId3 int, gatherSupplyID uint, isCreate int) (err error, listGoods []*productModel.Product, recordErrors []publicSupplyModel.SupplyGoodsImportRecordErrors) {
	//var productIds []uint
	//for _, item := range list {
	//	productIds = append(productIds, uint(item.ProductID))
	//}

	for _, item := range list {
		goods := productModel.Product{
			ProductModel: productModel.ProductModel{
				Title:          item.Title,
				DetailImages:   "",
				BrandID:        uint(item.BrandId),
				Category1ID:    uint(cateId1),
				Category2ID:    uint(cateId2),
				Category3ID:    uint(cateId3),
				IsDisplay:      1,
				Stock:          item.Stock,
				Source:         127,
				SourceGoodsID:  uint(item.ProductID),
				GatherSupplyID: gatherSupplyID,
				SingleOption:   1,
				IsPlugin:       1,
				Price:          item.AgreementPrice,
				CostPrice:      item.CostPrice,
				GuidePrice:     item.GuidePrice,
				OriginPrice:    item.MarketPrice,
				ActivityPrice:  item.ActivityPrice,
				Unit:           "件",
			},
			Skus: []productModel.Sku{
				{
					SkuModel: productModel.SkuModel{
						Title:         "默认",
						Stock:         int(item.Stock),
						IsDisplay:     1,
						Price:         item.AgreementPrice,
						CostPrice:     item.CostPrice,
						GuidePrice:    item.GuidePrice,
						OriginPrice:   item.MarketPrice,
						ActivityPrice: item.ActivityPrice,
						Options: productModel.Options{
							productModel.Option{
								SpecName:     "规格",
								SpecItemName: "默认",
							},
						},
					},
				},
			},
		}
		listGoods = append(listGoods, &goods)
	}
	return
}

func (m *MillenniumSupply) ImportGoodsRun(search publicSupplyRequest.GetGoodsSearch) (err error, data interface{}) {
	// 查询已导入的ids
	var importIds []uint
	if importIds, err = service.SearchFuLuProductIds(service.SearchFuLuProduct{ProductSource: common.MILLENNIUM_SOURCE}); err != nil {
		return
	}

	// 已导入的ids map
	var importIdsMap = make(map[uint]bool)
	for _, id := range importIds {
		importIdsMap[id] = true
	}

	// 数据查询
	db := source.DB().Model(&model.MillenniumCloudProduct{})

	if search.Title != "" {
		db.Where("`product_name` like ?", "%"+search.Title+"%")
	}
	if search.TypeName != "" {
		db.Where("`product_type` = ?", search.TypeName)
	}
	if search.PromotionRate.To > 0 {
		db.Where("profit_rate BETWEEN ? AND ?", search.PromotionRate.From, search.PromotionRate.To)
	}
	if search.IsImport > 0 {
		switch search.IsImport {
		case 1:
			db.Where("`product_id` in ?", importIds)
		case 2:
			db.Where("`product_id` not in ?", importIds)
		}
	}
	// 查询需要导入的数据
	var mcProducts []model.MillenniumCloudProduct
	if err = db.Find(&mcProducts).Error; err != nil {
		err = errors.New("选品库数据获取失败，请稍后重试")
		return
	}

	// 循环 mcProducts 数据，组装最终操作需要的数据
	var (
		handleIds           []int                   // 待操作的ids
		handleGoodsList     []publicModel.Goods     // 待操作的商品列表
		handleFuLuGoodsList []model.FuluSupplyGoods // 待操作的福禄商品列表
	)

	// 查询供应链ID
	_, search.GatherSupplyID = service.GetFuluSupplyID()

	for _, item := range mcProducts {
		if _, ok := importIdsMap[item.ProductId]; !ok {
			product := convertProduct(item, search.GatherSupplyID, importIdsMap)

			handleIds = append(handleIds, int(item.ProductId))
			handleGoodsList = append(handleGoodsList, product)
			handleFuLuGoodsList = append(handleFuLuGoodsList, convertFuLuGoods(item))
		}
	}

	searchText, _ := json.Marshal(search)
	// 插入商品导入记录
	importRecord := publicModel.SupplyGoodsImportRecord{
		SysUserId:         search.SysUserID,
		Batch:             getOrderNo(),
		EstimatedQuantity: len(mcProducts),
		Status:            1,
		Source:            "127",
		IsPlugin:          1,
		RepeatQuantity:    len(mcProducts) - len(handleIds),
		SearchCriteria:    string(searchText),
	}
	// 如果没有需要导入的数据，则直接完成
	if len(handleIds) <= 0 {
		importRecord.Status = 2
		importRecord.CompletionStatus = 1
	}
	if err = source.DB().Create(&importRecord).Error; err != nil || len(handleIds) <= 0 {
		return
	}

	// 插入FuLu商品表
	if err = source.DB().CreateInBatches(handleFuLuGoodsList, 500).Error; err != nil {
		err = errors.New("同步数字权益商品操作失败，请稍后重试")
		return
	}

	// 同步商品表
	_ = m.RunSelectGoodsConcurrent(importRecord.Batch, handleGoodsList, search.Categorys, search.Key, search.GatherSupplyID)

	// 更新商品导入记录状态
	err = source.DB().Model(&importRecord).Update("completion_status", 1).Error

	return
}

// todo getOrderNo 方法每个供应链中都有实现，重复代码，可以提出一个公共方法

func getOrderNo() (id string) {
	var node *leaf.IdNode
	var err error
	err, node = leaf.NewNode(20)
	if err != nil {
		return
	}
	err, id = node.NextId()
	if err != nil {
		return
	}
	return
}

func (m *MillenniumSupply) ValidateConfig(params gatherSupplyRequest.ValidateConfigParams) (err error) {

	return
}

func (m *MillenniumSupply) DeleteGoods(id uint) (err error) {
	return
}

func (m *MillenniumSupply) GetCategory(info publicSupplyRequest.GetCategorySearch) (err error, data interface{}) {

	return
}

func (m *MillenniumSupply) GetGroup() (err error, data interface{}) {

	return
}

func (m *MillenniumSupply) GetCategoryChild(pid int, info publicSupplyRequest.GetCategoryChild) (err error, data interface{}) {

	return
}

func (m *MillenniumSupply) RunConcurrent(wg *sync.WaitGroup, info publicSupplyRequest.GetCategorySearch, i int) (err error) {

	return
}

func (m *MillenniumSupply) GoodsStorageAdd(ids []int, supplyId uint) (err error, info interface{}) {

	return
}

func (m *MillenniumSupply) GoodsPriceAlert(GoodsData callback2.GoodsCallBack) (err error) {

	return
}

// 从选品库查询数据更新到商品表

func (m *MillenniumSupply) SynchronizeProductsToLocal() (err error) {
	// 获取供应链ID
	_, gatherId := service.GetFuluSupplyID()

	// 查询已导入商品
	var products []productService.ProductForUpdate
	err = source.DB().Preload("Skus").Where("source = ?", common.MILLENNIUM_SOURCE).Where("gather_supply_id = ?", gatherId).Find(&products).Error
	if err != nil {
		err = errors.New("自动更新千禧券商品操作失败：查询已导入数据失败")
		return
	}

	// 已导入 product_id 集合
	var productIds []uint
	for _, product := range products {
		productIds = append(productIds, product.SourceGoodsID)
	}

	// 删除fulu_supply_goods表中 已删除的商品残留记录
	if err = source.DB().Where("product_source = ?", common.MILLENNIUM_SOURCE).Where("product_id not in ?", productIds).Delete(&model.FuluSupplyGoods{}).Error; err != nil {
		err = errors.New("自动更新千禧券商品操作失败：删除已删除的商品残留记录失败")
		return
	}

	// 查询选品库数据
	var cloudProducts []model.MillenniumCloudProduct
	if err = source.DB().Where("product_id in ?", productIds).Find(&cloudProducts).Error; err != nil {
		err = errors.New("自动更新千禧券商品操作失败：查询选品库数据失败")
		return
	}

	// 构建 product_id -> model.MillenniumCloudProduct 映射
	var cloudProductMap = make(map[uint]model.MillenniumCloudProduct)
	for _, item := range cloudProducts {
		cloudProductMap[item.ProductId] = item
	}

	// 查询已导入千禧券商品
	var fuLuProducts []model.FuluSupplyGoods
	if err = source.DB().Where("product_source = ?", common.MILLENNIUM_SOURCE).Find(&fuLuProducts).Error; err != nil {
		err = errors.New("自动更新千禧券商品操作失败：查询已导入数据失败")
		return
	}

	// 构建 product_id -> model.MillenniumCloudProduct 映射
	var fuLuProductMap = make(map[uint]model.FuluSupplyGoods)
	for _, item := range fuLuProducts {
		fuLuProductMap[uint(item.ProductId)] = item
	}

	var takeOffIds []uint
	for _, product := range products {
		cloudProduct, ok := cloudProductMap[product.SourceGoodsID]
		// 选品库不存在，下架
		if !ok {
			takeOffIds = append(takeOffIds, product.ID)
			continue
		}

		// 关联表不存在、锁定，不更新
		fuLuProduct, isExist := fuLuProductMap[product.SourceGoodsID]
		if !isExist || fuLuProduct.LockStatus == 1 {
			continue
		}

		// md5 相等，不需要更新
		if product.MD5 == cloudProduct.MD5 {
			continue
		}

		// 更新商品
		product.MD5 = cloudProduct.MD5
		product.IsDisplay = 1

		// 更新商品规格
		sku := product.Skus[0]
		sku.IsDisplay = 1

		if Setting.Values.UpdateInfo.AutoProductName == 1 {
			product.Title = cloudProduct.ProductName
		}
		if Setting.Values.UpdateInfo.AutoPurchasePrice == 1 {
			pStrategy := getPriceStrategy(Setting.Values.PricingStrategy, cloudProduct.PurchasePrice, cloudProduct.FaceValue)

			product.Price = pStrategy.Price
			product.CostPrice = pStrategy.CostPrice
			product.GuidePrice = pStrategy.GuidePrice
			product.OriginPrice = pStrategy.OriginPrice
			product.ActivityPrice = pStrategy.ActivityPrice

			sku.Price = pStrategy.Price
			sku.CostPrice = pStrategy.CostPrice
			sku.GuidePrice = pStrategy.GuidePrice
			sku.OriginPrice = pStrategy.OriginPrice
			sku.ActivityPrice = pStrategy.ActivityPrice
		}
		if Setting.Values.UpdateInfo.AutoDetails == 1 {
			product.DetailImages = cloudProduct.Details
		}
		if Setting.Values.UpdateInfo.AutoStockStatus == 1 {
			product.Stock = cloudProduct.Storage

			// 规格库存
			sku.Stock = int(cloudProduct.Storage)
		}
		product.Skus[0] = sku
		// 更新的商品
		if err = productService.UpdateProduct(product); err != nil {
			err = errors.New("自动更新千禧券商品操作失败：商品更新失败，MD5:" + product.MD5)
			return
		}
		// 更新福禄
		fuLuGoods := convertFuLuGoods(cloudProduct)
		if err = source.DB().Where("id = ?", fuLuProduct.ID).Updates(&fuLuGoods).Error; err != nil {
			err = errors.New("自动更新千禧券商品操作失败：福禄商品更新失败，MD5:" + fuLuProduct.MD5)
			return
		}
	}

	// 下架千禧已经不存在的商品
	if err = takeOffProduct(common.MILLENNIUM, takeOffIds); err != nil {
		return
	}

	return
}

// 下架千禧已经不存在的商品
func takeOffProduct(sourceId uint, takeOffIds []uint) (err error) {
	var products []productModel.ProductModel

	if err = source.DB().Where("is_display = 1").Where("id IN ?", takeOffIds).Where("source = ?", sourceId).Find(&products).Error; err != nil {
		return
	}

	for _, product := range products {
		if err = source.DB().Model(&product).Updates(map[string]interface{}{"id": product.ID, "is_display": 0, "updated_at": time.Now().Format("2006-01-02 15:04:05")}).Error; err != nil {
			return
		}

		// 推送ES
		if err = productMq.PublishMessage(product.ID, productMq.Undercarriage, 0); err != nil {
			fmt.Println("mq插入失败pUndercarriage", err)
		}
	}

	return
}

func (m *MillenniumSupply) GetSupplyBalance(GatherSupplyID uint) (err error, balance interface{}) {

	return
}
