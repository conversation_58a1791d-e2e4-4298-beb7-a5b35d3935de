package cron

import (
	"fmt"
	"fulu-supply/model"
	"testing"
	"yz-go/source"
)

func TestUpdate(t *testing.T) {
	source.DB().Model(&model.FuluSupplyOrderResult{}).
		Where("order_id = ?", "24070220530954082111").
		Select("Status", "OrderState", "Error").
		Updates(model.FuluSupplyOrderResult{Status: 1, OrderState: "success", Error: ""})
}

func TestHandle(t *testing.T) {
	var err error
	var NotGatherIds []int
	var fuIDs []int
	var categoryIDs []int
	categoryIDs = append(categoryIDs, 98)
	categoryIDs = append(categoryIDs, 110)
	categoryIDs = append(categoryIDs, 111)

	fuErr := source.DB().Unscoped().Model(&GatherSupply{}).Where("category_id in ?", categoryIDs).Pluck("id", &fuIDs).Error
	if fuErr != nil {
		return
	}
	if len(fuIDs) > 0 {
		NotGatherIds = append(NotGatherIds, fuIDs...)
	}
	fmt.Println(NotGatherIds)
	fmt.Println(err)
}

type GatherSupply struct {
	source.Model
	CategoryID uint `json:"category_id" form:"category_id" gorm:"column:category_id;default:0;"`
}
