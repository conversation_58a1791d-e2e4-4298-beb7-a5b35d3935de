package goods

import (
	categoryModel "category/model"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"fulu-supply/model"
	"github.com/go-resty/resty/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"math"
	productModel "product/model"
	productMq "product/mq"
	"sort"
	"strings"
	"time"
	"yz-go/component/log"
	"yz-go/source"
	"yz-go/utils"
)

var (
	AppSecret       string
	AppKey          string
	PricingStrategy model.PricingStrategy
	updateInfo      model.UpdateInfo
)

// ImportGoodsRun 导入商品
func ImportGoodsRun(supplyID uint) (err error) {
	var setting model.Setting
	err, setting = getSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("请先配置基础信息")
		return
	}
	if setting.Values.BaseInfo.AppKey == "" || setting.Values.BaseInfo.AppSecret == "" {
		err = errors.New("请先配置基础信息")
		return
	}
	AppKey = setting.Values.BaseInfo.AppKey
	AppSecret = setting.Values.BaseInfo.AppSecret
	PricingStrategy = setting.Values.PricingStrategy
	updateInfo = setting.Values.UpdateInfo
	params := getParamsByImportGoods()
	jsonStr, err := json.Marshal(params)
	client1 := resty.New()
	res, err := client1.R().EnableTrace().SetHeader("Content-Type", "application/json").SetBody(jsonStr).Post(model.URL)
	if err != nil {
		return
	}
	var response model.FuluGoodsResponse
	var arrResponse model.ArrFuluSupplyGoods
	err = json.Unmarshal(res.Body(), &response)
	if err != nil {
		err = errors.New("解析请求结果错误")
		return
	}
	if response.Code != 0 {
		err = errors.New(response.Message)
		return
	}
	err = json.Unmarshal([]byte(response.Result), &arrResponse)
	if err != nil {
		err = errors.New("解析商品信息错误")
		return
	}
	// 获取已经保存的fulu商品map
	existedFuluGoodsMap := getExistedFuluGoodsMap()
	var insertFuluGoodsList, updateFuluGoodsList model.ArrFuluSupplyGoods
	// api返回的byn商品 转 map
	var responseBynGoodsMap = make(map[int]model.FuluSupplyGoods)
	for _, goods := range arrResponse {
		// api返回的byn商品 转 map
		responseBynGoodsMap[goods.ProductId] = goods
		var jsonResult []byte
		jsonResult, err = json.Marshal(goods)
		h := md5.New()
		h.Write(jsonResult)
		goods.MD5 = hex.EncodeToString(h.Sum(nil))
		// 验证api返回的fulu商品是否已经保存到fulu_supply_goods表
		if _, existed := existedFuluGoodsMap[goods.ProductId]; existed {
			// 是否锁定 或 验证是否相同:相同, 跳出 else 不相同, 修改
			if existedFuluGoodsMap[goods.ProductId].LockStatus == 1 || goods.MD5 == existedFuluGoodsMap[goods.ProductId].MD5 {
				continue
			}
			// 附加 要修改的fulu商品
			updateFuluGoodsList = append(updateFuluGoodsList, goods)
		} else {
			// 附加 要创建的fulu商品
			insertFuluGoodsList = append(insertFuluGoodsList, goods)
		}
	}

	// 循环 要添加的byn商品, 组装insertProductList
	var insertProductList []productModel.Product
	// 获取默认三级分类
	var category1, category2, category3 categoryModel.Category
	err, category1, category2, category3 = getDefaultCategory(supplyID)
	if err != nil {
		return
	}
	// 默认品牌
	var brand categoryModel.Brand
	err, brand = getDefaultBrand(supplyID)
	// 已经导入中台的商品和规格
	err, importedProductsMap, importedSkusMap := getImportedProductsAndSkusMap(supplyID)
	if err != nil {
		return
	}
	// 要修改的fulu商品 map, 要修改的product map, 要修改的sku map
	var updateFuluGoodsMap, updateProductMap, updateSkuMap []map[string]interface{}
	// 组装商品
	for _, goods := range insertFuluGoodsList {
		if _, existed := importedProductsMap[uint(goods.ProductId)]; existed {
			for _, item := range importedProductsMap[uint(goods.ProductId)] {
				productId := item.ID
				updateProductMap = append(updateProductMap, assembleProduct(goods, productId, updateInfo))

				skuId := importedSkusMap[item.ID].ID
				updateSkuMap = append(updateSkuMap, assembleSku(goods, skuId, updateInfo))
			}

			continue
		}

		insertProductRow := productModel.Product{}
		insertProductRow.Title = goods.ProductName
		insertProductRow.DetailImages = goods.Details
		var stock uint
		if goods.StockStatus != "断货" {
			stock = uint(99999)
		} else {
			stock = uint(0)
		}
		// 2023年10月20日20:51:18 by 韦平 更新设置中，如果开启了自动更新商品库存，则商品上下架跟随福禄，只要有库存、福禄开放平台商品为上架状态的都上架；如果没库存或者福禄开放平台商品下架或者不存在该商品的，我们这边商品下架。
		if updateInfo.AutoStockStatus == 1 {
			if stock == 0 {
				insertProductRow.IsDisplay = 0
			} else {
				insertProductRow.IsDisplay = 1
			}
		}
		insertProductRow.BrandID = brand.ID
		insertProductRow.Category1ID = category1.ID
		insertProductRow.Category2ID = category2.ID
		insertProductRow.Category3ID = category3.ID
		insertProductRow.Stock = stock
		// 2023年10月20日20:51:18 by 韦平 更新设置中，如果开启了自动更新商品库存，则商品上下架跟随福禄，只要有库存、福禄开放平台商品为上架状态的都上架；如果没库存或者福禄开放平台商品下架或者不存在该商品的，我们这边商品下架。
		//// 新导入的商品默认下架
		//insertProductRow.IsDisplay = 0
		insertProductRow.SourceGoodsID = uint(goods.ProductId)
		insertProductRow.GatherSupplyID = supplyID
		insertProductRow.SingleOption = 1
		insertProductRow.IsPlugin = 1

		var price, costPrice, originPrice, guidePrice, activityPrice uint
		//if goods.ProductName == "乐视乐次元影视vip会员年卡-198元-直充-自营" {
		//	price, costPrice, originPrice, guidePrice, activityPrice = getPrices(goods)
		//}
		//continue
		price, costPrice, originPrice, guidePrice, activityPrice = getPrices(goods)

		insertProductRow.Price = price
		insertProductRow.CostPrice = costPrice
		insertProductRow.OriginPrice = originPrice
		insertProductRow.GuidePrice = guidePrice
		insertProductRow.ActivityPrice = activityPrice
		insertProductRow.Unit = "件"
		var sku productModel.Sku
		var options productModel.Options
		var option productModel.Option
		option.SpecName = "规格"
		option.SpecItemName = "默认"
		options = append(options, option)
		sku.Title = "默认"
		sku.Options = options
		sku.Weight = 0
		sku.Stock = int(stock)
		sku.IsDisplay = 1
		sku.Price = price
		sku.OriginPrice = originPrice
		sku.CostPrice = costPrice
		sku.GuidePrice = guidePrice
		sku.ActivityPrice = activityPrice
		sku.OriginalSkuID = int64(goods.ProductId)
		insertProductRow.Skus = append(insertProductRow.Skus, sku)
		insertProductList = append(insertProductList, insertProductRow)
	}

	// 批量创建fulu商品
	if len(insertFuluGoodsList) > 0 {
		err = source.DB().CreateInBatches(insertFuluGoodsList, 500).Error
		if err != nil {
			return
		}
	}
	// 创建中台商品
	if len(insertProductList) > 0 {
		for _, goods := range insertProductList {
			err = source.DB().Create(&goods).Error
			if err != nil {
				fmt.Println("插入失败", err)
			}
			err = productMq.PublishMessage(goods.ID, productMq.Create, 0)
			if err != nil {
				fmt.Println("mq插入失败", err)
			}
		}
	}

	for _, goods := range updateFuluGoodsList {
		updateFuluGoodsMap = append(updateFuluGoodsMap, assembleFuluGoods(goods))

		if _, existed := importedProductsMap[uint(goods.ProductId)]; existed {
			for _, item := range importedProductsMap[uint(goods.ProductId)] {
				productId := item.ID
				updateProductMap = append(updateProductMap, assembleProduct(goods, productId, updateInfo))

				skuId := importedSkusMap[item.ID].ID
				updateSkuMap = append(updateSkuMap, assembleSku(goods, skuId, updateInfo))
			}
		}

		/*productId := importedProductsMap[uint(goods.ProductId)].ID
		updateProductMap = append(updateProductMap, assembleProduct(goods, productId, updateInfo))

		skuId := importedSkusMap[importedProductsMap[uint(goods.ProductId)].ID].ID
		updateSkuMap = append(updateSkuMap, assembleSku(goods, skuId, updateInfo))*/
	}

	// 要删除的byn商品ids, 要删除的中台商品ids
	var deleteFuluGoodsIds, deleteProductIds []uint
	// 循环已存在的fulu商品数据, 对比api返回fulu商品数据, 不存在的删除
	for _, goods := range existedFuluGoodsMap {
		if _, existed := responseBynGoodsMap[goods.ProductId]; !existed {
			deleteFuluGoodsIds = append(deleteFuluGoodsIds, goods.ID)
			if _, existed := importedProductsMap[uint(goods.ProductId)]; existed {
				for _, item := range importedProductsMap[uint(goods.ProductId)] {
					deleteProductIds = append(deleteProductIds, item.ID)
				}
			}
			//deleteProductIds = append(deleteProductIds, importedProductsMap[uint(goods.ProductId)].ID)
		}
	}
	// 删除fulu商品
	if len(deleteFuluGoodsIds) > 0 {
		err = source.DB().Delete(&model.FuluSupplyGoods{}, deleteFuluGoodsIds).Error
		if err != nil {
			return
		}
	}
	// 删除中台商品 修改时间:2023年03月23日18:14:16 修改内容:不删除商品,只做下架处理 by:韦平
	if len(deleteProductIds) > 0 {
		var undercarriageProducts []map[string]interface{}
		for _, productId := range deleteProductIds {
			undercarriageProducts = append(undercarriageProducts, map[string]interface{}{"id": productId, "is_display": 0, "updated_at": time.Now().Format("2006-01-02 15:04:05")})
			err = productMq.PublishMessage(productId, productMq.Undercarriage, 0)
			if err != nil {
				fmt.Println("mq插入失败pUndercarriage", err)
			}
		}
		err = source.BatchUpdate(undercarriageProducts, "products", "")
		if err != nil {
			return
		}
		/*err = source.DB().Delete(&productModel.Product{}, deleteProductIds).Error
		if err != nil {
			return
		}*/
	}
	// 批量修改fulu商品
	/*if len(updateFuluGoodsMap) > 0 {
		err = source.BatchUpdate(updateFuluGoodsMap, "fulu_supply_goods", "product_id")
		if err != nil {
			return
		}
	}*/
	if len(updateFuluGoodsMap) > 0 {
		chunkSize := 500
		for i := 0; i < len(updateFuluGoodsMap); i += chunkSize {
			end := i + chunkSize
			if end > len(updateFuluGoodsMap) {
				end = len(updateFuluGoodsMap)
			}
			err = source.BatchUpdate(updateFuluGoodsMap[i:end], "fulu_supply_goods", "product_id")
			if err != nil {
				return
			}
		}
	}
	// 批量修改中台商品
	if len(updateProductMap) > 0 {
		for _, row := range updateProductMap {
			log.Log().Debug("fulu修改商品", zap.Any("id", row["id"]))
			err = productMq.PublishMessage(row["id"].(uint), productMq.Edit, 0)
			if err != nil {
				fmt.Println("mq插入失败pedit", err)
			}
		}
		// err = source.BatchUpdate(updateProductMap, "products", "")
		chunkSize := 500
		for i := 0; i < len(updateProductMap); i += chunkSize {
			end := i + chunkSize
			if end > len(updateProductMap) {
				end = len(updateProductMap)
			}
			err = source.BatchUpdate(updateProductMap[i:end], "products", "")
			if err != nil {
				return
			}
		}
	}
	// 批量修改中台商品规格
	if len(updateSkuMap) > 0 {
		// err = source.BatchUpdate(updateSkuMap, "skus", "")
		chunkSize := 500
		for i := 0; i < len(updateSkuMap); i += chunkSize {
			end := i + chunkSize
			if end > len(updateSkuMap) {
				end = len(updateSkuMap)
			}
			err = source.BatchUpdate(updateSkuMap[i:end], "skus", "")
			if err != nil {
				return
			}
		}
	}

	return
}

func getDefaultBrand(supplyID uint) (err error, brand categoryModel.Brand) {
	brand.Name = "福禄卡券"
	brand.Source = int(supplyID)
	source.DB().Where("name=?", brand.Name).FirstOrCreate(&brand)
	return
}

func getDefaultCategory(supplyID uint) (err error, category1, category2, category3 categoryModel.Category) {
	var display int
	display = 1
	category1.Name = "福禄卡券"
	category1.Level = 1
	category1.ParentID = 0
	category1.IsDisplay = &display
	category1.Source = int(supplyID)
	source.DB().Where("name=? and level=? and parent_id=?", category1.Name, category1.Level, category1.ParentID).FirstOrCreate(&category1)
	category2.Name = "福禄卡券"
	category2.Level = 2
	category2.ParentID = category1.ID
	category2.IsDisplay = &display
	category2.Source = int(supplyID)
	source.DB().Where("name=? and level=? and parent_id=?", category2.Name, category2.Level, category2.ParentID).FirstOrCreate(&category2)
	category3.Name = "福禄卡券"
	category3.Level = 3
	category3.ParentID = category2.ID
	category3.IsDisplay = &display
	category3.Source = int(supplyID)
	source.DB().Where("name=? and level=? and parent_id=?", category3.Name, category3.Level, category3.ParentID).FirstOrCreate(&category3)
	return
}

func assembleSku(goods model.FuluSupplyGoods, id uint, updateInfo model.UpdateInfo) (row map[string]interface{}) {
	row = make(map[string]interface{})
	row["id"] = id
	row["title"] = "默认"
	if updateInfo.AutoPurchasePrice == 1 {
		var price, costPrice, originPrice, guidePrice, activityPrice uint
		price, costPrice, originPrice, guidePrice, activityPrice = getPrices(goods)
		// 营销价
		row["activity_price"] = activityPrice
		// 指导价
		row["guide_price"] = guidePrice
		// 市场价
		row["origin_price"] = originPrice
		// 成本价
		row["cost_price"] = costPrice
		// 供货价
		row["price"] = price
	}
	return
}

func assembleProduct(goods model.FuluSupplyGoods, id uint, updateInfo model.UpdateInfo) (row map[string]interface{}) {
	row = make(map[string]interface{})
	row["id"] = id
	if updateInfo.AutoProductName == 1 {
		row["title"] = goods.ProductName
	}
	if updateInfo.AutoDetails == 1 {
		row["detail_images"] = goods.Details
	}
	// 2023年10月20日20:51:18 by 韦平 更新设置中，如果开启了自动更新商品库存，则商品上下架跟随福禄，只要有库存、福禄开放平台商品为上架状态的都上架；如果没库存或者福禄开放平台商品下架或者不存在该商品的，我们这边商品下架。
	if updateInfo.AutoStockStatus == 1 {
		if goods.StockStatus != "断货" {
			row["stock"] = uint(99999)
			row["is_display"] = 1
		} else {
			row["stock"] = uint(0)
			row["is_display"] = 0
		}
	}

	row["single_option"] = 1
	row["is_plugin"] = 1
	// 原来商品不修改状态
	// row["is_display"] = 1

	if updateInfo.AutoPurchasePrice == 1 {
		var price, costPrice, originPrice, guidePrice, activityPrice uint
		price, costPrice, originPrice, guidePrice, activityPrice = getPrices(goods)
		row["price"] = price
		row["cost_price"] = costPrice
		row["origin_price"] = originPrice
		row["guide_price"] = guidePrice
		row["activity_price"] = activityPrice
	}
	row["updated_at"] = time.Now().Format("2006-01-02 15:04:05")
	return
}

func getImportedProductsAndSkusMap(supplyID uint) (err error, importedProductsMap map[uint][]productModel.Product, importedSkusMap map[uint]productModel.Sku) {
	importedProductsMap = make(map[uint][]productModel.Product)
	importedSkusMap = make(map[uint]productModel.Sku)
	err, importedProducts := getImportedProducts(supplyID)
	if err != nil {
		return
	}
	for _, goods := range importedProducts {
		if len(goods.Skus) > 0 {
			importedProductsMap[goods.SourceGoodsID] = append(importedProductsMap[goods.SourceGoodsID], goods)
			importedSkusMap[goods.ID] = goods.Skus[0]
		}
	}
	return
}

func getImportedProducts(supplyID uint) (err error, importedProducts []productModel.Product) {
	err = source.DB().Preload("Skus").Where("`gather_supply_id` = ?", supplyID).Find(&importedProducts).Error
	return
}

func assembleFuluGoods(goods model.FuluSupplyGoods) (row map[string]interface{}) {
	row = make(map[string]interface{})
	//row["id"] = goods.ID
	row["product_id"] = goods.ProductId
	row["product_name"] = goods.ProductName
	row["face_value"] = goods.FaceValue
	row["product_type"] = goods.ProductType
	row["purchase_price"] = goods.PurchasePrice
	row["stock_status"] = goods.StockStatus
	row["sales_status"] = goods.SalesStatus
	row["details"] = goods.Details
	row["md5"] = goods.MD5
	return
}

func getExistedFuluGoodsMap() (existedFuluGoodsMap map[int]model.FuluSupplyGoods) {
	existedFuluGoodsMap = make(map[int]model.FuluSupplyGoods)
	err, fuluGoodsList := getExistedFuluGoods()
	if err != nil {
		return
	}
	for _, goods := range fuluGoodsList {
		existedFuluGoodsMap[goods.ProductId] = goods
	}
	return
}

func getExistedFuluGoods() (err error, fuluGoodsList model.ArrFuluSupplyGoods) {
	err = source.DB().Where("product_source = ?", 0).Find(&fuluGoodsList).Error
	return
}

func getSetting() (err error, setting model.Setting) {
	err = source.DB().Where("`key` = ?", "fulu_supply_setting").First(&setting).Error
	return
}

func getPrices(goods model.FuluSupplyGoods) (price, costPrice, originPrice, guidePrice, activityPrice uint) {
	//var price,costPrice,originPrice,guidePrice,activityPrice uint
	if PricingStrategy.Price.ComputeType == 1 {
		price = uint(math.Floor(goods.PurchasePrice * 100))
		if PricingStrategy.Price.PriceRatio > 0 {
			price = uint(math.Floor(goods.PurchasePrice*100)) * uint(PricingStrategy.Price.PriceRatio) / 10000
		}
	} else {
		price = uint(math.Floor(goods.FaceValue * 100))
		if PricingStrategy.Price.FaceRatio > 0 {
			price = uint(math.Floor(goods.FaceValue*100)) * uint(PricingStrategy.Price.FaceRatio) / 10000
		}
	}
	if PricingStrategy.CostPrice.ComputeType == 1 {
		costPrice = uint(math.Floor(goods.PurchasePrice * 100))
		if PricingStrategy.CostPrice.PriceRatio > 0 {
			costPrice = uint(math.Floor(goods.PurchasePrice*100)) * uint(PricingStrategy.CostPrice.PriceRatio) / 10000
		}
	} else {
		costPrice = uint(math.Floor(goods.FaceValue * 100))
		if PricingStrategy.CostPrice.FaceRatio > 0 {
			costPrice = uint(math.Floor(goods.FaceValue*100)) * uint(PricingStrategy.CostPrice.FaceRatio) / 10000
		}
	}
	if PricingStrategy.OriginPrice.ComputeType == 1 {
		originPrice = uint(math.Floor(goods.PurchasePrice * 100))
		if PricingStrategy.OriginPrice.PriceRatio > 0 {
			originPrice = uint(math.Floor(goods.PurchasePrice*100)) * uint(PricingStrategy.OriginPrice.PriceRatio) / 10000
		}
	} else {
		originPrice = uint(math.Floor(goods.FaceValue * 100))
		if PricingStrategy.OriginPrice.FaceRatio > 0 {
			originPrice = uint(math.Floor(goods.FaceValue*100)) * uint(PricingStrategy.OriginPrice.FaceRatio) / 10000
		}
	}
	if PricingStrategy.GuidePrice.ComputeType == 1 {
		guidePrice = uint(math.Floor(goods.PurchasePrice * 100))
		if PricingStrategy.GuidePrice.PriceRatio > 0 {
			guidePrice = uint(math.Floor(goods.PurchasePrice*100)) * uint(PricingStrategy.GuidePrice.PriceRatio) / 10000
		}
	} else {
		guidePrice = uint(math.Floor(goods.FaceValue * 100))
		if PricingStrategy.GuidePrice.FaceRatio > 0 {
			guidePrice = uint(math.Floor(goods.FaceValue*100)) * uint(PricingStrategy.GuidePrice.FaceRatio) / 10000
		}
	}
	if PricingStrategy.ActivityPrice.ComputeType == 1 {
		activityPrice = uint(math.Floor(goods.PurchasePrice * 100))
		if PricingStrategy.ActivityPrice.PriceRatio > 0 {
			activityPrice = uint(math.Floor(goods.PurchasePrice*100)) * uint(PricingStrategy.ActivityPrice.PriceRatio) / 10000
		}
	} else {
		activityPrice = uint(math.Floor(goods.FaceValue * 100))
		if PricingStrategy.ActivityPrice.FaceRatio > 0 {
			activityPrice = uint(math.Floor(goods.FaceValue*100)) * uint(PricingStrategy.ActivityPrice.FaceRatio) / 10000
		}
	}
	return
}

func getParamsByImportGoods() (params map[string]string) {
	//var params map[string]string
	params = make(map[string]string)
	//params["app_key"] = "n78PKDeuKZxs5n6kfrKIZmJu7BBlLISZ2U3y/LaXQwl2HXR5Z/Emi60VWS3gJttj"
	params["app_key"] = AppKey
	params["method"] = "fulu.goods.list.get"
	params["timestamp"] = time.Now().Format("2006-01-02 15:04:05")
	params["version"] = "2.0"
	params["format"] = "json"
	params["charset"] = "utf-8"
	params["sign_type"] = "md5"
	params["app_auth_token"] = ""
	params["biz_content"] = "{}"
	sign := getSign(params)
	params["sign"] = sign
	return
}

func getSign(params map[string]string) (sign string) {
	jsonParams, _ := json.Marshal(params)
	jsonStr := string(jsonParams)
	sortStr := strings.Split(jsonStr, "")
	sort.Strings(sortStr)

	var str string
	for _, s := range sortStr {
		str += s
	}

	//str = str + "cc917764b2b840b88fb3eaea10d79117"
	str = str + AppSecret
	sign = utils.MD5V([]byte(str))
	return
}
