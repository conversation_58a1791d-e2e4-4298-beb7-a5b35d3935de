package service

import (
	categoryModel "category/model"
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"fulu-supply/model"
	"go.uber.org/zap"
	productModel "product/model"
	productMq "product/mq"
	productRequest "product/request"
	"public-supply/common"
	publicSupplySetting "public-supply/setting"
	"strconv"
	"time"
	userEquityModel "user-equity/model"
	userEquityRequest "user-equity/request"
	userEquityService "user-equity/service"
	"yz-go/component/log"
	yzModel "yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

/*type SelfSupplySetting struct {
	SupplySetting
	BaseInfo SelfBaseInfoData `json:"baseInfo"`
}
type SelfBaseInfoData struct {
	BaseInfoData
	Host string `json:"host"`
}*/

type SupplySetting struct {
	BaseInfo   BaseInfoData                    `json:"baseInfo"`
	UpdateInfo userEquityModel.UpdateInfo      `json:"update_info"`
	Pricing    userEquityModel.PricingStrategy `json:"pricing_strategy"`
}

type BaseInfoData struct {
	AppKey    string `json:"appKey"`
	AppSecret string `json:"appSecret"`
	Host      string `json:"host"`
}

var selfData *SupplySetting

func getSettingKey(supplyID uint) (key string) {
	return "gatherSupply" + strconv.Itoa(int(supplyID))
}

func ProductEditSync(productID uint) (err error) {
	var supplyID uint
	err, supplyID = GetFuluSupplyID()
	if err != nil {
		err = errors.New("查询供应链id失败")
		return
	}
	var supplySetting yzModel.SysSetting
	key := getSettingKey(supplyID)
	err, supplySetting = publicSupplySetting.GetSetting(key)
	if err != nil {
		return
	}
	err = json.Unmarshal([]byte(supplySetting.Value), &selfData)
	if err != nil {
		err = errors.New("获取设置失败")
		return
	}
	if selfData.BaseInfo.AppKey == "" || selfData.BaseInfo.AppSecret == "" || selfData.BaseInfo.Host == "" {
		err = errors.New("请先配置供应链key")
		return
	}
	var header map[string]string
	err, header = InitToken(supplyID)
	if err != nil {
		return
	}
	var requestParam productRequest.ProductDetailSearch
	requestParam.Ids = append(requestParam.Ids, int(productID))
	err, result := utils.Post(selfData.BaseInfo.Host+"/app/userEquity/storage/detailList", requestParam, header)
	var response ResponseSync
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}
	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}
	var updateFuluGoodsMap, updateProductMap, updateSkuMap []map[string]interface{}
	// 已经导入中台的商品和规格
	err, importedProductsMap, importedSkusMap := getImportedProductsAndSkusMap(supplyID)
	if err != nil {
		return
	}
	for _, product := range response.Data.List {
		var existedProduct model.FuluSupplyGoods
		err = source.DB().Model(&existedProduct).Where("product_id = ? and product_source = ?", product.ID, 1).First(&existedProduct).Error
		if err != nil {
			return
		}
		var fuluGoods model.FuluSupplyGoods
		fuluGoods.ProductId = int(product.ID)
		fuluGoods.ProductName = product.Title
		fuluGoods.ProductType = "直充"
		fuluGoods.PurchasePrice = float64(product.Price / 100)
		if product.Stock > 0 {
			fuluGoods.StockStatus = "充足"
		} else {
			fuluGoods.StockStatus = "断货"
		}
		if product.IsDisplay == 1 {
			fuluGoods.SalesStatus = "上架"
		} else {
			fuluGoods.SalesStatus = "下架"
		}
		fuluGoods.Details = product.DetailImages
		fuluGoods.ProductSource = 1
		var jsonResult []byte
		jsonResult, err = json.Marshal(product)
		h := md5.New()
		h.Write(jsonResult)
		fuluGoods.MD5 = hex.EncodeToString(h.Sum(nil))
		if fuluGoods.MD5 == existedProduct.MD5 {
			return
		}
		updateFuluGoodsMap = append(updateFuluGoodsMap, assembleFuluGoods(fuluGoods))

		// 获取三级分类
		var category1, category2, category3 categoryModel.Category
		err, category1, category2, category3 = getCategory(supplyID, product.Category1, product.Category2, product.Category3)
		if err != nil {
			return
		}

		if _, existed := importedProductsMap[product.ID]; existed {
			for _, item := range importedProductsMap[uint(product.ID)] {
				productId := item.ID
				updateProductMap = append(updateProductMap, assembleProduct(category1.ID, category2.ID, category3.ID, product, productId, selfData.UpdateInfo, fuluGoods.SalesStatus))

				for _, rsku := range item.Skus {
					skuId := importedSkusMap[item.ID].ID
					updateSkuMap = append(updateSkuMap, assembleSku(product, rsku, skuId, selfData.UpdateInfo))
				}
			}
		}
	}
	// 批量修改fulu商品
	if len(updateFuluGoodsMap) > 0 {
		// err = source.BatchUpdate(updateFuluGoodsMap, "fulu_supply_goods", "product_id")
		chunkSize := 500
		for i := 0; i < len(updateFuluGoodsMap); i += chunkSize {
			end := i + chunkSize
			if end > len(updateFuluGoodsMap) {
				end = len(updateFuluGoodsMap)
			}
			err = source.BatchUpdate(updateFuluGoodsMap[i:end], "fulu_supply_goods", "product_id")
			if err != nil {
				return
			}
		}
	}
	// 批量修改中台商品
	if len(updateProductMap) > 0 {
		for _, row := range updateProductMap {
			log.Log().Debug("fulu修改商品", zap.Any("id", row["id"]))
			err = productMq.PublishMessage(row["id"].(uint), productMq.Edit, 0)
			if err != nil {
				fmt.Println("mq插入失败pedit", err)
			}
		}
		// err = source.BatchUpdate(updateProductMap, "products", "")
		chunkSize := 500
		for i := 0; i < len(updateProductMap); i += chunkSize {
			end := i + chunkSize
			if end > len(updateProductMap) {
				end = len(updateProductMap)
			}
			err = source.BatchUpdate(updateProductMap[i:end], "products", "")
			if err != nil {
				return
			}
		}
	}
	// 批量修改中台商品规格
	if len(updateSkuMap) > 0 {
		// err = source.BatchUpdate(updateSkuMap, "skus", "")
		chunkSize := 500
		for i := 0; i < len(updateSkuMap); i += chunkSize {
			end := i + chunkSize
			if end > len(updateSkuMap) {
				end = len(updateSkuMap)
			}
			err = source.BatchUpdate(updateSkuMap[i:end], "skus", "")
			if err != nil {
				return
			}
		}
	}
	return
}

func GoImportProductRun() (err error) {
	var supplyID uint
	err, supplyID = GetFuluSupplyID()
	if err != nil {
		err = errors.New("GoImportProductRun查询供应链id失败")
		return err
	}
	var supplySetting yzModel.SysSetting
	key := getSettingKey(supplyID)
	err, supplySetting = publicSupplySetting.GetSetting(key)
	if err != nil {
		return errors.New("GoImportProductRun获取设置失败1, err:" + err.Error())
	}
	err = json.Unmarshal([]byte(supplySetting.Value), &selfData)
	if err != nil {
		err = errors.New("GoImportProductRun获取设置失败2, err:" + err.Error())
		return err
	}
	if selfData.BaseInfo.AppKey == "" || selfData.BaseInfo.AppSecret == "" || selfData.BaseInfo.Host == "" {
		err = errors.New("请先配置供应链key")
		return err
	}
	var header map[string]string
	err, header = InitToken(supplyID)
	if err != nil {
		err = errors.New("GoImportProductRun获取token失败, err:" + err.Error())
		return err
	}
	// 已经导入中台的商品和规格
	err, importedProductsMap, importedSkusMap := getImportedProductsAndSkusMap(supplyID)
	if err != nil {
		err = errors.New("GoImportProductRun获取已导入商品失败, err:" + err.Error())
		return err
	}
	// 获取已经保存的fulu商品map
	existedFuluGoodsMap := getExistedFuluGoodsMap()
	var requestParam userEquityRequest.ProductSearch
	requestParam.Page = 0
	requestParam.PageSize = 500
	for {
		requestParam.Page++
		getProductListErr, result := utils.Post(selfData.BaseInfo.Host+"/app/userEquity/storage/getProductList", requestParam, header)
		if getProductListErr != nil {
			err = errors.New("GoImportProductRun获取商品列表失败1, err:" + getProductListErr.Error())
			return err
		}
		var response Response
		err = json.Unmarshal(result, &response)
		if err != nil {
			err = errors.New("GoImportProductRun解析商品列表失败, err:" + err.Error())
			return err
		}
		if response.Code != 0 {
			err = errors.New("GoImportProductRun获取商品列表失败2, err:" + response.Msg)
			return err
		}
		if len(response.Data.List) == 0 {
			break
		}
		// 要修改的fulu商品 map, 要修改的product map, 要修改的sku map
		var updateFuluGoodsMap, updateProductMap, updateSkuMap []map[string]interface{}
		// 批量增加的福禄商品, 批量修改的福禄商品
		var insertFuluGoodsList, updateFuluGoodsList model.ArrFuluSupplyGoods
		// 接口返回的商品 转 map
		var responseGoodsMap = make(map[uint]userEquityService.Product)
		// 组装insertProductList
		var insertProductList []productModel.Product
		// 加入选品库
		var productDetailSearch productRequest.ProductDetailSearch
		// 要删除的byn商品ids, 要删除的中台商品ids
		var deleteFuluGoodsIds, deleteProductIds []uint
		for _, product := range response.Data.List {
			productDetailSearch.Ids = append(productDetailSearch.Ids, int(product.ID))
			// 接口返回的商品 转 map
			responseGoodsMap[product.ID] = product
			var fuluGoods model.FuluSupplyGoods
			fuluGoods.ProductId = int(product.ID)
			fuluGoods.ProductName = product.Title
			fuluGoods.ProductType = "直充"
			fuluGoods.PurchasePrice = float64(product.Price / 100)
			if product.Stock > 0 {
				fuluGoods.StockStatus = "充足"
			} else {
				fuluGoods.StockStatus = "断货"
			}
			if product.IsDisplay == 1 {
				fuluGoods.SalesStatus = "上架"
			} else {
				fuluGoods.SalesStatus = "下架"
			}
			fuluGoods.Details = product.DetailImages
			fuluGoods.ProductSource = 1
			var jsonResult []byte
			jsonResult, err = json.Marshal(product)
			h := md5.New()
			h.Write(jsonResult)
			fuluGoods.MD5 = hex.EncodeToString(h.Sum(nil))
			// 获取三级分类
			var category1, category2, category3 categoryModel.Category
			err, category1, category2, category3 = getCategory(supplyID, product.Category1, product.Category2, product.Category3)
			if err != nil {
				return err
			}
			// 验证api返回的商品是否已经保存到fulu_supply_goods表
			if _, existed := existedFuluGoodsMap[int(product.ID)]; existed {
				// 验证是否相同:相同, 跳出 else 不相同, 修改
				// 先去掉, 因为历史数据遗留问题, 造成无法同步上下架
				//if fuluGoods.MD5 == existedFuluGoodsMap[int(product.ID)].MD5 {
				//	continue
				//}
				// 是否锁定 或 验证是否相同:相同, 跳出 else 不相同, 修改
				if existedFuluGoodsMap[int(product.ID)].LockStatus == 1 || fuluGoods.MD5 == existedFuluGoodsMap[int(product.ID)].MD5 {
					continue
				}
				// 附加 要修改的fulu商品
				updateFuluGoodsList = append(updateFuluGoodsList, fuluGoods)
				// 组装要修改的
				updateFuluGoodsMap = append(updateFuluGoodsMap, assembleFuluGoods(fuluGoods))

				if _, existed := importedProductsMap[product.ID]; existed {
					for _, item := range importedProductsMap[uint(product.ID)] {
						productId := item.ID
						updateProductMap = append(updateProductMap, assembleProduct(category1.ID, category2.ID, category3.ID, product, productId, selfData.UpdateInfo, fuluGoods.SalesStatus))

						for _, rsku := range item.Skus {
							skuId := importedSkusMap[item.ID].ID
							updateSkuMap = append(updateSkuMap, assembleSku(product, rsku, skuId, selfData.UpdateInfo))
						}
					}
				}

			} else {
				// 附加 要创建的fulu商品
				insertFuluGoodsList = append(insertFuluGoodsList, fuluGoods)
				// 组装商品
				insertProductRow := productModel.Product{}
				insertProductRow.Title = product.Title
				insertProductRow.ImageUrl = product.ImageUrl
				insertProductRow.Gallery = product.Gallery
				insertProductRow.DetailImages = product.DetailImages
				insertProductRow.Stock = product.Stock
				// 新导入的商品默认下架
				insertProductRow.IsDisplay = product.IsDisplay
				insertProductRow.SourceGoodsID = product.ID
				insertProductRow.GatherSupplyID = supplyID
				insertProductRow.SingleOption = 1
				insertProductRow.IsPlugin = 1
				insertProductRow.Source = common.EQUITY_SOURCE
				//// 获取三级分类
				//var category1, category2, category3 categoryModel.Category
				//err, category1, category2, category3 = getCategory(supplyID, product.Category1, product.Category2, product.Category3)
				//if err != nil {
				//	return
				//}
				insertProductRow.Category1ID = category1.ID
				insertProductRow.Category2ID = category2.ID
				insertProductRow.Category3ID = category3.ID
				// 获取品牌
				var brand categoryModel.Brand
				err, brand = getBrand(supplyID, product.Brand)
				if err != nil {
					err = errors.New("GoImportProductRun获取品牌失败, err:" + err.Error())
					return err
				}
				insertProductRow.BrandID = brand.ID
				var price, costPrice, originPrice, guidePrice, activityPrice uint
				price, costPrice, originPrice, guidePrice, activityPrice = getPrices(product)
				insertProductRow.Price = price
				insertProductRow.CostPrice = costPrice
				insertProductRow.OriginPrice = originPrice
				insertProductRow.GuidePrice = guidePrice
				insertProductRow.ActivityPrice = activityPrice
				for _, rsku := range product.Skus {
					var sku productModel.Sku
					var options productModel.Options
					var option productModel.Option
					option.SpecName = "规格"
					option.SpecItemName = "默认"
					options = append(options, option)
					sku.Title = rsku.Title
					sku.Options = options
					sku.Weight = rsku.Weight
					sku.Stock = rsku.Stock
					sku.IsDisplay = 1
					sku.Price = price
					sku.OriginPrice = originPrice
					sku.CostPrice = costPrice
					sku.GuidePrice = guidePrice
					sku.ActivityPrice = activityPrice
					sku.OriginalSkuID = int64(product.ID)
					sku.ImageUrl = rsku.ImageUrl
					insertProductRow.Skus = append(insertProductRow.Skus, sku)
				}
				insertProductList = append(insertProductList, insertProductRow)
			}

			// 循环已存在的fulu商品数据, 对比api返回fulu商品数据, 不存在的删除
			if _, fe := existedFuluGoodsMap[int(product.ID)]; !fe {
				deleteFuluGoodsIds = append(deleteFuluGoodsIds, product.ID)
				if _, pe := importedProductsMap[product.ID]; pe {
					for _, item := range importedProductsMap[product.ID] {
						deleteProductIds = append(deleteProductIds, item.ID)
					}
				}
			}
		}

		// 批量创建fulu商品
		if len(insertFuluGoodsList) > 0 {
			err = source.DB().CreateInBatches(insertFuluGoodsList, 500).Error
			if err != nil {
				err = errors.New("GoImportProductRun批量创建fulu商品失败, err:" + err.Error())
				return err
			}
		}
		// 创建中台商品
		if len(insertProductList) > 0 {
			for _, goods := range insertProductList {
				err = source.DB().Create(&goods).Error
				if err != nil {
					fmt.Println("插入失败", err)
				}
				err = productMq.PublishMessage(goods.ID, productMq.Create, 0)
				if err != nil {
					fmt.Println("mq插入失败", err)
				}
			}
		}
		// 批量修改fulu商品
		if len(updateFuluGoodsMap) > 0 {
			// err = source.BatchUpdate(updateFuluGoodsMap, "fulu_supply_goods", "product_id")
			chunkSize := 500
			for i := 0; i < len(updateFuluGoodsMap); i += chunkSize {
				end := i + chunkSize
				if end > len(updateFuluGoodsMap) {
					end = len(updateFuluGoodsMap)
				}
				err = source.BatchUpdate(updateFuluGoodsMap[i:end], "fulu_supply_goods", "product_id")
				if err != nil {
					return
				}
			}
		}
		// 批量修改中台商品
		if len(updateProductMap) > 0 {
			for _, row := range updateProductMap {
				log.Log().Debug("fulu修改商品", zap.Any("id", row["id"]))
				err = productMq.PublishMessage(row["id"].(uint), productMq.Edit, 0)
				if err != nil {
					fmt.Println("mq插入失败pedit", err)
				}
			}
			// err = source.BatchUpdate(updateProductMap, "products", "")
			chunkSize := 500
			for i := 0; i < len(updateProductMap); i += chunkSize {
				end := i + chunkSize
				if end > len(updateProductMap) {
					end = len(updateProductMap)
				}
				err = source.BatchUpdate(updateProductMap[i:end], "products", "")
				if err != nil {
					return
				}
			}
		}
		// 批量修改中台商品规格
		if len(updateSkuMap) > 0 {
			// err = source.BatchUpdate(updateSkuMap, "skus", "")
			chunkSize := 500
			for i := 0; i < len(updateSkuMap); i += chunkSize {
				end := i + chunkSize
				if end > len(updateSkuMap) {
					end = len(updateSkuMap)
				}
				err = source.BatchUpdate(updateSkuMap[i:end], "skus", "")
				if err != nil {
					return
				}
			}
		}
		// 删除fulu商品
		if len(deleteFuluGoodsIds) > 0 {
			err = source.DB().Delete(&model.FuluSupplyGoods{}, deleteFuluGoodsIds).Error
			if err != nil {
				err = errors.New("GoImportProductRun删除fulu商品失败, err:" + err.Error())
				return err
			}
		}
		// 删除中台商品 修改时间:2023年03月23日18:14:16 修改内容:不删除商品,只做下架处理 by:韦平
		if len(deleteProductIds) > 0 {
			var undercarriageProducts []map[string]interface{}
			for _, productId := range deleteProductIds {
				undercarriageProducts = append(undercarriageProducts, map[string]interface{}{"id": productId, "is_display": 0, "updated_at": time.Now().Format("2006-01-02 15:04:05")})
				err = productMq.PublishMessage(productId, productMq.Undercarriage, 0)
				if err != nil {
					fmt.Println("mq插入失败pdel", err)
				}
			}
			err = source.BatchUpdate(undercarriageProducts, "products", "")
			if err != nil {
				err = errors.New("GoImportProductRun删除中台商品失败, err:" + err.Error())
				return err
			}
			/*err = source.DB().Delete(&productModel.Product{}, deleteProductIds).Error
			if err != nil {
				return
			}*/
		}

		addStorageErr, storageResult := utils.Post(selfData.BaseInfo.Host+"/app/product/storage/addStorage", productDetailSearch, header)
		if addStorageErr != nil {
			err = errors.New("GoImportProductRun加入选品库失败1, err:" + addStorageErr.Error())
			return err
		}
		var storageResponse StorageResponse
		err = json.Unmarshal(storageResult, &storageResponse)
		if err != nil {
			err = errors.New("GoImportProductRun解析加入选品库失败, err:" + err.Error())
			return err
		}

		if storageResponse.Code != 0 {
			err = errors.New("GoImportProductRun加入选品库失败2, err:" + storageResponse.Msg)
			return err
		}
	}
	return err

	//err, result := utils.Post(selfData.BaseInfo.Host+"/app/userEquity/storage/getProductList", requestParam, header)
	//var response Response
	//err = json.Unmarshal(result, &response)
	//if err != nil {
	//	return err
	//}
	//
	//if response.Code != 0 {
	//	err = errors.New(response.Msg)
	//	return err
	//}
	//if response.Data.Total == 0 {
	//	return err
	//}
	//// 已经导入中台的商品和规格
	//err, importedProductsMap, importedSkusMap := getImportedProductsAndSkusMap(supplyID)
	//if err != nil {
	//	return err
	//}
	//// 要修改的fulu商品 map, 要修改的product map, 要修改的sku map
	//var updateFuluGoodsMap, updateProductMap, updateSkuMap []map[string]interface{}
	//// 获取已经保存的fulu商品map
	//existedFuluGoodsMap := getExistedFuluGoodsMap()
	//// 批量增加的福禄商品, 批量修改的福禄商品
	//var insertFuluGoodsList, updateFuluGoodsList model.ArrFuluSupplyGoods
	//// 接口返回的商品 转 map
	//var responseGoodsMap = make(map[uint]userEquityService.Product)
	//// 组装insertProductList
	//var insertProductList []productModel.Product
	//// 加入选品库
	//var productDetailSearch productRequest.ProductDetailSearch
	//for _, product := range response.Data.List {
	//	productDetailSearch.Ids = append(productDetailSearch.Ids, int(product.ID))
	//	// 接口返回的商品 转 map
	//	responseGoodsMap[product.ID] = product
	//	var fuluGoods model.FuluSupplyGoods
	//	fuluGoods.ProductId = int(product.ID)
	//	fuluGoods.ProductName = product.Title
	//	fuluGoods.ProductType = "直充"
	//	fuluGoods.PurchasePrice = float64(product.Price / 100)
	//	if product.Stock > 0 {
	//		fuluGoods.StockStatus = "充足"
	//	} else {
	//		fuluGoods.StockStatus = "断货"
	//	}
	//	if product.IsDisplay == 1 {
	//		fuluGoods.SalesStatus = "上架"
	//	} else {
	//		fuluGoods.SalesStatus = "下架"
	//	}
	//	fuluGoods.Details = product.DetailImages
	//	fuluGoods.ProductSource = 1
	//	var jsonResult []byte
	//	jsonResult, err = json.Marshal(product)
	//	h := md5.New()
	//	h.Write(jsonResult)
	//	fuluGoods.MD5 = hex.EncodeToString(h.Sum(nil))
	//	// 获取三级分类
	//	var category1, category2, category3 categoryModel.Category
	//	err, category1, category2, category3 = getCategory(supplyID, product.Category1, product.Category2, product.Category3)
	//	if err != nil {
	//		return err
	//	}
	//	// 验证api返回的商品是否已经保存到fulu_supply_goods表
	//	if _, existed := existedFuluGoodsMap[int(product.ID)]; existed {
	//		// 验证是否相同:相同, 跳出 else 不相同, 修改
	//		if fuluGoods.MD5 == existedFuluGoodsMap[int(product.ID)].MD5 {
	//			continue
	//		}
	//		// 附加 要修改的fulu商品
	//		updateFuluGoodsList = append(updateFuluGoodsList, fuluGoods)
	//		// 组装要修改的
	//		updateFuluGoodsMap = append(updateFuluGoodsMap, assembleFuluGoods(fuluGoods))
	//
	//		if _, existed := importedProductsMap[product.ID]; existed {
	//			for _, item := range importedProductsMap[uint(product.ID)] {
	//				productId := item.ID
	//				updateProductMap = append(updateProductMap, assembleProduct(category1.ID, category2.ID, category3.ID, product, productId, selfData.UpdateInfo, fuluGoods.SalesStatus))
	//
	//				for _, rsku := range item.Skus {
	//					skuId := importedSkusMap[item.ID].ID
	//					updateSkuMap = append(updateSkuMap, assembleSku(product, rsku, skuId, selfData.UpdateInfo))
	//				}
	//			}
	//		}
	//
	//	} else {
	//		// 附加 要创建的fulu商品
	//		insertFuluGoodsList = append(insertFuluGoodsList, fuluGoods)
	//		// 组装商品
	//		insertProductRow := productModel.Product{}
	//		insertProductRow.Title = product.Title
	//		insertProductRow.ImageUrl = product.ImageUrl
	//		insertProductRow.Gallery = product.Gallery
	//		insertProductRow.DetailImages = product.DetailImages
	//		insertProductRow.Stock = product.Stock
	//		// 新导入的商品默认下架
	//		insertProductRow.IsDisplay = product.IsDisplay
	//		insertProductRow.SourceGoodsID = product.ID
	//		insertProductRow.GatherSupplyID = supplyID
	//		insertProductRow.SingleOption = 1
	//		insertProductRow.IsPlugin = 1
	//		insertProductRow.Source = common.EQUITY_SOURCE
	//		//// 获取三级分类
	//		//var category1, category2, category3 categoryModel.Category
	//		//err, category1, category2, category3 = getCategory(supplyID, product.Category1, product.Category2, product.Category3)
	//		//if err != nil {
	//		//	return
	//		//}
	//		insertProductRow.Category1ID = category1.ID
	//		insertProductRow.Category2ID = category2.ID
	//		insertProductRow.Category3ID = category3.ID
	//		// 获取品牌
	//		var brand categoryModel.Brand
	//		err, brand = getBrand(supplyID, product.Brand)
	//		if err != nil {
	//			return err
	//		}
	//		insertProductRow.BrandID = brand.ID
	//		var price, costPrice, originPrice, guidePrice, activityPrice uint
	//		price, costPrice, originPrice, guidePrice, activityPrice = getPrices(product)
	//		insertProductRow.Price = price
	//		insertProductRow.CostPrice = costPrice
	//		insertProductRow.OriginPrice = originPrice
	//		insertProductRow.GuidePrice = guidePrice
	//		insertProductRow.ActivityPrice = activityPrice
	//		for _, rsku := range product.Skus {
	//			var sku productModel.Sku
	//			var options productModel.Options
	//			var option productModel.Option
	//			option.SpecName = "规格"
	//			option.SpecItemName = "默认"
	//			options = append(options, option)
	//			sku.Title = rsku.Title
	//			sku.Options = options
	//			sku.Weight = rsku.Weight
	//			sku.Stock = rsku.Stock
	//			sku.IsDisplay = 1
	//			sku.Price = price
	//			sku.OriginPrice = originPrice
	//			sku.CostPrice = costPrice
	//			sku.GuidePrice = guidePrice
	//			sku.ActivityPrice = activityPrice
	//			sku.OriginalSkuID = int64(product.ID)
	//			sku.ImageUrl = rsku.ImageUrl
	//			insertProductRow.Skus = append(insertProductRow.Skus, sku)
	//		}
	//		insertProductList = append(insertProductList, insertProductRow)
	//	}
	//}
	//
	//// 批量创建fulu商品
	//if len(insertFuluGoodsList) > 0 {
	//	err = source.DB().CreateInBatches(insertFuluGoodsList, 500).Error
	//	if err != nil {
	//		return err
	//	}
	//}
	//// 创建中台商品
	//if len(insertProductList) > 0 {
	//	for _, goods := range insertProductList {
	//		err = source.DB().Create(&goods).Error
	//		if err != nil {
	//			fmt.Println("插入失败", err)
	//		}
	//		err = productMq.PublishMessage(goods.ID, productMq.Create, 0)
	//		if err != nil {
	//			fmt.Println("mq插入失败", err)
	//		}
	//	}
	//}
	//// 批量修改fulu商品
	//if len(updateFuluGoodsMap) > 0 {
	//	err = source.BatchUpdate(updateFuluGoodsMap, "fulu_supply_goods", "product_id")
	//	if err != nil {
	//		return err
	//	}
	//}
	//// 批量修改中台商品
	//if len(updateProductMap) > 0 {
	//	for _, row := range updateProductMap {
	//		log.Log().Debug("fulu修改商品", zap.Any("id", row["id"]))
	//		err = productMq.PublishMessage(row["id"].(uint), productMq.Edit, 0)
	//		if err != nil {
	//			fmt.Println("mq插入失败pedit", err)
	//		}
	//	}
	//	err = source.BatchUpdate(updateProductMap, "products", "")
	//	if err != nil {
	//		return err
	//	}
	//}
	//// 批量修改中台商品规格
	//if len(updateSkuMap) > 0 {
	//	err = source.BatchUpdate(updateSkuMap, "skus", "")
	//	if err != nil {
	//		return err
	//	}
	//}
	//
	//// 要删除的byn商品ids, 要删除的中台商品ids
	//var deleteFuluGoodsIds, deleteProductIds []uint
	//// 循环已存在的fulu商品数据, 对比api返回fulu商品数据, 不存在的删除
	//for _, goods := range existedFuluGoodsMap {
	//	if _, existed := responseGoodsMap[uint(goods.ProductId)]; !existed {
	//		deleteFuluGoodsIds = append(deleteFuluGoodsIds, goods.ID)
	//
	//		if _, existed := importedProductsMap[uint(goods.ProductId)]; existed {
	//			for _, item := range importedProductsMap[uint(goods.ProductId)] {
	//				deleteProductIds = append(deleteProductIds, item.ID)
	//			}
	//		}
	//	}
	//}
	//// 删除fulu商品
	//if len(deleteFuluGoodsIds) > 0 {
	//	err = source.DB().Delete(&model.FuluSupplyGoods{}, deleteFuluGoodsIds).Error
	//	if err != nil {
	//		return err
	//	}
	//}
	//// 删除中台商品 修改时间:2023年03月23日18:14:16 修改内容:不删除商品,只做下架处理 by:韦平
	//if len(deleteProductIds) > 0 {
	//	var undercarriageProducts []map[string]interface{}
	//	for _, productId := range deleteProductIds {
	//		undercarriageProducts = append(undercarriageProducts, map[string]interface{}{"id": productId, "is_display": 0, "updated_at": time.Now().Format("2006-01-02 15:04:05")})
	//		err = productMq.PublishMessage(productId, productMq.Undercarriage, 0)
	//		if err != nil {
	//			fmt.Println("mq插入失败pdel", err)
	//		}
	//	}
	//	err = source.BatchUpdate(undercarriageProducts, "products", "")
	//	if err != nil {
	//		return err
	//	}
	//	/*err = source.DB().Delete(&productModel.Product{}, deleteProductIds).Error
	//	if err != nil {
	//		return
	//	}*/
	//}
	//
	//err, storageResult := utils.Post(selfData.BaseInfo.Host+"/app/product/storage/addStorage", productDetailSearch, header)
	//var storageResponse StorageResponse
	//err = json.Unmarshal(storageResult, &storageResponse)
	//if err != nil {
	//	return err
	//}
	//
	//if storageResponse.Code != 0 {
	//	err = errors.New(response.Msg)
	//	return err
	//}
	//
	//return err
}

func ImportProductRun() (err error) {
	go func() {
		gErr := GoImportProductRun()
		if gErr != nil {
			// 记录错误日志
			log.Log().Error("数字权益导入ImportProductRun" + gErr.Error())
		}
	}()
	return
}

func assembleSku(goods userEquityService.Product, sku productModel.Sku, id uint, updateInfo userEquityModel.UpdateInfo) (row map[string]interface{}) {
	row = make(map[string]interface{})
	row["id"] = id
	if updateInfo.AutoProductName == 1 {
		row["title"] = sku.Title
	}
	if updateInfo.AutoDetails == 1 {
		row["describe"] = sku.Describe
	}
	if updateInfo.AutoStockStatus == 1 {
		row["stock"] = sku.Stock
	}
	if updateInfo.AutoPurchasePrice == 1 {
		var price, costPrice, originPrice, guidePrice, activityPrice uint
		price, costPrice, originPrice, guidePrice, activityPrice = getPrices(goods)
		row["price"] = price
		row["cost_price"] = costPrice
		row["origin_price"] = originPrice
		row["guide_price"] = guidePrice
		row["activity_price"] = activityPrice
	}
	return
}

func assembleProduct(category1ID uint, category2ID uint, category3ID uint, goods userEquityService.Product, id uint, updateInfo userEquityModel.UpdateInfo, salesStatus string) (row map[string]interface{}) {
	row = make(map[string]interface{})
	row["id"] = id
	row["single_option"] = 1
	row["is_plugin"] = 1
	if updateInfo.AutoProductName == 1 {
		row["title"] = goods.Title
	}
	if salesStatus == "上架" {
		row["is_display"] = 1
	} else {
		row["is_display"] = 0
	}
	row["category1_id"] = category1ID
	row["category2_id"] = category2ID
	row["category3_id"] = category3ID
	if updateInfo.AutoDetails == 1 {
		row["detail_images"] = goods.DetailImages
		row["gallery"] = goods.Gallery
		// 会员权益商品没有主图
		// row["image_url"] = goods.ImageUrl

		if goods.ImageUrl != "" {
			row["image_url"] = goods.ImageUrl
		}
	}
	if updateInfo.AutoStockStatus == 1 {
		row["stock"] = goods.Stock
	}
	if updateInfo.AutoPurchasePrice == 1 {
		var price, costPrice, originPrice, guidePrice, activityPrice uint
		price, costPrice, originPrice, guidePrice, activityPrice = getPrices(goods)
		row["price"] = price
		row["cost_price"] = costPrice
		row["origin_price"] = originPrice
		row["guide_price"] = guidePrice
		row["activity_price"] = activityPrice
	}
	row["updated_at"] = time.Now().Format("2006-01-02 15:04:05")
	return
}

func assembleFuluGoods(goods model.FuluSupplyGoods) (row map[string]interface{}) {
	row = make(map[string]interface{})
	//row["id"] = goods.ID
	row["product_id"] = goods.ProductId
	row["product_name"] = goods.ProductName
	row["face_value"] = goods.FaceValue
	row["product_type"] = goods.ProductType
	row["purchase_price"] = goods.PurchasePrice
	row["stock_status"] = goods.StockStatus
	row["sales_status"] = goods.SalesStatus
	row["details"] = goods.Details
	row["md5"] = goods.MD5

	return
}

func getImportedProductsAndSkusMap(supplyID uint) (err error, importedProductsMap map[uint][]productModel.Product, importedSkusMap map[uint]productModel.Sku) {
	importedProductsMap = make(map[uint][]productModel.Product)
	importedSkusMap = make(map[uint]productModel.Sku)
	err, importedProducts := getImportedProducts(supplyID)
	if err != nil {
		return
	}
	for _, goods := range importedProducts {
		if len(goods.Skus) > 0 {
			importedProductsMap[goods.SourceGoodsID] = append(importedProductsMap[goods.SourceGoodsID], goods)
			importedSkusMap[goods.ID] = goods.Skus[0]
		}
		/*importedProductsMap[goods.SourceGoodsID] = append(importedProductsMap[goods.SourceGoodsID], goods)
		importedSkusMap[goods.ID] = goods.Skus[0]*/
	}
	return
}

func getImportedProducts(supplyID uint) (err error, importedProducts []productModel.Product) {
	err = source.DB().Preload("Skus").Where("`gather_supply_id` = ? and source = ?", supplyID, common.EQUITY_SOURCE).Find(&importedProducts).Error
	return
}

func getPrices(goods userEquityService.Product) (price, costPrice, originPrice, guidePrice, activityPrice uint) {
	// price 供货价 origin_price 原价
	if selfData.Pricing.Price.ComputeType == 1 {
		price = goods.Price
		if selfData.Pricing.Price.PriceRatio > 0 {
			price = price * uint(selfData.Pricing.Price.PriceRatio) / 100
		}
	} else {
		price = goods.OriginPrice
		if selfData.Pricing.Price.OriginalPriceRatio > 0 {
			price = price * uint(selfData.Pricing.Price.OriginalPriceRatio) / 100
		}
	}
	if selfData.Pricing.CostPrice.ComputeType == 1 {
		costPrice = goods.Price
		if selfData.Pricing.CostPrice.PriceRatio > 0 {
			costPrice = costPrice * uint(selfData.Pricing.CostPrice.PriceRatio) / 100
		}
	} else {
		costPrice = goods.OriginPrice
		if selfData.Pricing.CostPrice.OriginalPriceRatio > 0 {
			costPrice = costPrice * uint(selfData.Pricing.CostPrice.OriginalPriceRatio) / 100
		}
	}
	if selfData.Pricing.OriginPrice.ComputeType == 1 {
		originPrice = goods.Price
		if selfData.Pricing.OriginPrice.PriceRatio > 0 {
			originPrice = originPrice * uint(selfData.Pricing.OriginPrice.PriceRatio) / 100
		}
	} else {
		originPrice = goods.OriginPrice
		if selfData.Pricing.OriginPrice.OriginalPriceRatio > 0 {
			originPrice = originPrice * uint(selfData.Pricing.OriginPrice.OriginalPriceRatio) / 100
		}
	}
	if selfData.Pricing.GuidePrice.ComputeType == 1 {
		guidePrice = goods.Price
		if selfData.Pricing.GuidePrice.PriceRatio > 0 {
			guidePrice = guidePrice * uint(selfData.Pricing.GuidePrice.PriceRatio) / 100
		}
	} else {
		guidePrice = goods.OriginPrice
		if selfData.Pricing.GuidePrice.OriginalPriceRatio > 0 {
			guidePrice = guidePrice * uint(selfData.Pricing.GuidePrice.OriginalPriceRatio) / 100
		}
	}
	if selfData.Pricing.ActivityPrice.ComputeType == 1 {
		activityPrice = goods.Price
		if selfData.Pricing.ActivityPrice.PriceRatio > 0 {
			activityPrice = activityPrice * uint(selfData.Pricing.ActivityPrice.PriceRatio) / 100
		}
	} else {
		activityPrice = goods.OriginPrice
		if selfData.Pricing.ActivityPrice.OriginalPriceRatio > 0 {
			activityPrice = activityPrice * uint(selfData.Pricing.ActivityPrice.OriginalPriceRatio) / 100
		}
	}
	return
}

func getBrand(supplyID uint, rbrand userEquityService.Brand) (err error, brand categoryModel.Brand) {
	brand.Name = rbrand.Name
	brand.Logo = rbrand.Logo
	brand.Source = int(supplyID)
	source.DB().Where("name = ? and source = ?", brand.Name, brand.Source).FirstOrCreate(&brand)
	return
}

func getCategory(supplyID uint, rcategory1, rcategory2, rcategory3 userEquityService.Category) (err error, category1, category2, category3 categoryModel.Category) {
	display := 1
	category1.Name = rcategory1.Name
	category1.Level = 1
	category1.ParentID = 0
	category1.IsDisplay = &display
	category1.Source = int(supplyID)
	category1.Image = rcategory1.Image
	category1.Desc = rcategory1.Desc
	category1.IsPlugin = 1
	source.DB().Where("name=? and level=? and parent_id=? and is_plugin = ? and source = ?", category1.Name, category1.Level, category1.ParentID, 1, category1.Source).FirstOrCreate(&category1)

	category2.Name = rcategory2.Name
	category2.Level = 2
	category2.ParentID = category1.ID
	category2.IsDisplay = &display
	category2.Source = int(supplyID)
	category2.Image = rcategory2.Image
	category2.Desc = rcategory2.Desc
	category2.IsPlugin = 1
	source.DB().Where("name=? and level=? and parent_id=? and is_plugin = ? and source = ?", category2.Name, category2.Level, category2.ParentID, 1, category2.Source).FirstOrCreate(&category2)

	category3.Name = rcategory3.Name
	category3.Level = 3
	category3.ParentID = category2.ID
	category3.IsDisplay = &display
	category3.Source = int(supplyID)
	category3.Image = rcategory3.Image
	category3.Desc = rcategory3.Desc
	category3.IsPlugin = 1
	source.DB().Where("name=? and level=? and parent_id=? and is_plugin = ? and source = ?", category3.Name, category3.Level, category3.ParentID, 1, category3.Source).FirstOrCreate(&category3)
	return
}

func getExistedFuluGoodsMap() (existedFuluGoodsMap map[int]model.FuluSupplyGoods) {
	existedFuluGoodsMap = make(map[int]model.FuluSupplyGoods)
	err, fuluGoodsList := getExistedFuluGoods()
	if err != nil {
		return
	}
	for _, goods := range fuluGoodsList {
		existedFuluGoodsMap[goods.ProductId] = goods
	}
	return
}

func getExistedFuluGoods() (err error, fuluGoodsList model.ArrFuluSupplyGoods) {
	err = source.DB().Where("product_source = ?", 1).Find(&fuluGoodsList).Error
	return
}

type ResponseSync struct {
	Code int            `json:"code"`
	Data PageResultSync `json:"data"`
	Msg  string         `json:"msg"`
}

type PageResultSync struct {
	List     []userEquityService.Product
	Total    int64  `json:"total"`
	Page     int    `json:"page"`
	PageSize int    `json:"pageSize"`
	NextUrl  string `json:"next_url"`
}

type StorageResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

type Response struct {
	Code int        `json:"code"`
	Data PageResult `json:"data"`
	Msg  string     `json:"msg"`
}

type PageResult struct {
	List     []userEquityService.Product
	Total    int64 `json:"total"`
	Page     int   `json:"page"`
	PageSize int   `json:"pageSize"`
}

type SourceResponse struct {
	Code int         `json:"code"`
	Data interface{} `json:"data"`
	Msg  string      `json:"msg"`
}

func InitToken(gatherSupplyID uint) (err error, header map[string]string) {
	var h = make(map[string]string)
	var ctx = context.Background()
	var token string
	token, err = source.Redis().Get(ctx, "gatherSupply"+strconv.Itoa(int(gatherSupplyID))).Result()
	if token == "" || err != nil {
		err, token = getToken(gatherSupplyID)
		if err != nil {
			return
		}
		h["x-token"] = token
	} else {
		h["x-token"] = token
		var result []byte
		fmt.Println(selfData.BaseInfo.Host + "/app/application/getSupplySource")
		fmt.Println(token)
		err, result = utils.Get(selfData.BaseInfo.Host+"/app/application/getSupplySource", h)
		var response SourceResponse
		err = json.Unmarshal(result, &response)
		if err != nil {
			return
		}
		if response.Code == 6 {
			err, token = getToken(gatherSupplyID)
			if err != nil {
				return
			}
			h["x-token"] = token
		}
	}

	return err, h
}

type GetTokenRequest struct {
	AppKey    string `json:"app_key" validate:"required"`
	AppSecret string `json:"app_secret"  validate:"required"`
}

type TokenResponse struct {
	Code int    `json:"code"`
	Data Token  `json:"data"`
	Msg  string `json:"msg"`
}

type Token struct {
	Token  string `json:"token"`
	Expire int64  `json:"expiresAt"`
}

func getToken(gatherSupplyID uint) (err error, token string) {
	var ctx = context.Background()
	var requestParam GetTokenRequest
	requestParam.AppSecret = selfData.BaseInfo.AppSecret
	requestParam.AppKey = selfData.BaseInfo.AppKey
	var result []byte
	err, result = utils.Post(selfData.BaseInfo.Host+"/app/application/getToken", requestParam, nil)
	var response TokenResponse
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}
	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}
	token = response.Data.Token
	err = source.Redis().Set(ctx, "gatherSupply"+strconv.Itoa(int(gatherSupplyID)), response.Data.Token, time.Second*86400).Err()
	if err != nil {
		return
	}
	return
}
