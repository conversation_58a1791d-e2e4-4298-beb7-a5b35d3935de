package v1

import (
	"encoding/json"
	callback2 "gather-supply/callback"
	funcallback "gather-supply/component/callback"
	"gather-supply/component/order"
	"gather-supply/mq"
	"gather-supply/service"
	gService "gather-supply/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"net/http"
	model2 "order/model"
	ccallback "public-supply/callback"
	callback "public-supply/model"
	"public-supply/request"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/source"
)

// SaleBeforeCheck
// @Tags 可售前置校验
// @Summary 可售前置校验
// @accept application/json
// @Produce application/json
// @Param data body request.RequestSaleBeforeCheck true "可售前置校验"
// @Success 200 {object} []interface{}
// @Router /gatherSupply/saleBeforeCheck [post]
func SaleBeforeCheck(c *gin.Context) {
	var Request request.RequestSaleBeforeCheck
	err := c.ShouldBindJSO<PERSON>(&Request)
	if err != nil {
		log.Log().Error("校验参数错误", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, key := service.GetGatherSupplyCategoryKey(Request.GatherSupplyID)
	var orderClass = order.NewOrder(key)
	err = orderClass.InitSetting(Request.GatherSupplyID)
	if err != nil {
		log.Log().Error("前置校验失败", zap.Any("info", err.Error()))
		return
	}
	if err, info := orderClass.SaleBeforeCheck(Request); err != nil {
		log.Log().Error("校验失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		c.JSON(http.StatusOK, info)
	}
}

// OrderBeforeCheck
// @Tags 下单前置校验
// @Summary 下单前置校验
// @accept application/json
// @Produce application/json
// @Param data body request.RequestSaleBeforeCheck true "下单前置校验"
// @Success 200 {object} []interface{}
// @Router /api/gatherSupply/orderBeforeCheck [post]
func OrderBeforeCheck(c *gin.Context) {
	var Request request.RequestSaleBeforeCheck
	err := c.ShouldBindJSON(&Request)
	//Request.LocalSkus = Request.Skus
	if err != nil {
		log.Log().Error("校验参数错误", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, info := service.OrderBeforeCheck(Request); err != nil {
		log.Log().Error("校验失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		c.JSON(http.StatusOK, info)
	}
}

// ConfirmOrder
// @Tags 下单
// @Summary 下单
// @accept application/json
// @Produce application/json
// @Param data body request.RequestConfirmOrder true "下单"
// @Success 200 {object} []interface{}
// @Router /api/gatherSupply/confirmOrder [post]
func ConfirmOrder(c *gin.Context) {
	var requestConfirmOrder request.RequestConfirmOrder
	err := c.ShouldBindJSON(&requestConfirmOrder)
	if err != nil {
		log.Log().Error("校验参数错误", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, key := service.GetGatherSupplyCategoryKey(requestConfirmOrder.GatherSupplyID)
	var orderClass = order.NewOrder(key)
	err = orderClass.InitSetting(requestConfirmOrder.GatherSupplyID)
	if err != nil {
		log.Log().Error("前置校验失败", zap.Any("info", err.Error()))
		return
	}
	if err, info := orderClass.ConfirmOrder(requestConfirmOrder); err != nil {
		log.Log().Error("下单失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		c.JSON(http.StatusOK, info)
	}
}

// ExpressQuery
// @Tags 供应链快递查询
// @Summary 供应链快递查询
// @accept application/json
// @Produce application/json
// @Param data body request.RequestExpress true "供应链快递查询"
// @Success 200 {object} []interface{}
// @Router /api/gatherSupply/expressQuery [post]
func ExpressQuery(c *gin.Context) {
	var requestExpress request.RequestExpress
	err := c.ShouldBindJSON(&requestExpress)
	if err != nil {
		log.Log().Error("校验参数错误", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var orderModel model2.Order
	err = source.DB().Where("order_sn = ?", requestExpress.OrderSn).First(&orderModel).Error
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, key := service.GetGatherSupplyCategoryKey(orderModel.GatherSupplyID)
	var orderClass = order.NewOrder(key)
	err = orderClass.InitSetting(orderModel.GatherSupplyID)
	if err != nil {
		log.Log().Error("前置校验失败", zap.Any("info", err.Error()))
		return
	}
	if err, info := orderClass.ExpressQuery(requestExpress); err != nil {
		log.Log().Error("物流查询失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		c.JSON(http.StatusOK, info)
	}
}

// AfterSaleBeforeCheck
// @Tags 售后前置校验
// @Summary 售后前置校验
// @accept application/json
// @Produce application/json
// @Param data body request.RequestAfterSale true "售后前置校验"
// @Success 200 {object} []interface{}
// @Router /api/gatherSupply/afterSaleBeforeCheck [post]
func AfterSaleBeforeCheck(c *gin.Context) {
	var requestAfterSale request.RequestAfterSale
	err := c.ShouldBindJSON(&requestAfterSale)
	if err != nil {
		log.Log().Error("校验参数错误", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var orderModel model2.Order
	err = source.DB().Where("order_sn = ?", requestAfterSale.OrderSn.OrderSn).First(&orderModel).Error
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, key := service.GetGatherSupplyCategoryKey(orderModel.GatherSupplyID)
	var orderClass = order.NewOrder(key)
	err = orderClass.InitSetting(orderModel.GatherSupplyID)
	if err != nil {
		log.Log().Error("前置校验失败", zap.Any("info", err.Error()))
		return
	}
	if err, info := orderClass.AfterSalesBeforeCheck(requestAfterSale); err != nil {
		log.Log().Error("售后校验失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		c.JSON(http.StatusOK, info)
	}
}

// AfterSalePicture
// @Tags 售后上传图片
// @Summary 售后上传图片
// @accept application/json
// @Produce application/json
// @Param data body request.RequestAfterSalePicture true "售后上传图片"
// @Success 200 {object} []interface{}
// @Router /api/gatherSupply/afterSalePicture [post]
func AfterSalePicture(c *gin.Context) {
	var requestAfterSalePicture request.RequestAfterSalePicture
	err := c.ShouldBindJSON(&requestAfterSalePicture)
	if err != nil {
		log.Log().Error("校验参数错误", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var orderModel model2.Order
	err = source.DB().Where("order_sn = ?", requestAfterSalePicture.OrderSn.OrderSn).First(&orderModel).Error
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, key := service.GetGatherSupplyCategoryKey(orderModel.GatherSupplyID)
	var orderClass = order.NewOrder(key)
	err = orderClass.InitSetting(orderModel.GatherSupplyID)
	if err != nil {
		log.Log().Error("前置校验失败", zap.Any("info", err.Error()))
		return
	}
	if err, info := orderClass.AfterSalesPicture(requestAfterSalePicture); err != nil {
		log.Log().Error("售后上传图片失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		c.JSON(http.StatusOK, info)
	}
}

func GetSupplySource(c *gin.Context) {
	var req Level
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Log().Error("校验参数错误", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	data := service.GetSupplySource(req.LevelID)
	yzResponse.OkWithData(data, c)

}

func WpsNotify(c *gin.Context) {

	var req callback.WpsNotifyData

	data, _ := c.GetRawData()
	log.Log().Info("WpsNotify data", zap.Any("info", data))
	err := json.Unmarshal(data, req)
	if err != nil {
		log.Log().Error("校验参数错误", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	service.WpsNotify(req)
	yzResponse.OkWithData(data, c)

}

func WpsNotifyProduct(c *gin.Context) {

	var product callback.WpsNotifyProductData
	var shelves callback.WpsNotifyShelvesData
	var afterSale callback.WpsNotifyAfterSale
	data, _ := c.GetRawData()
	log.Log().Info("WpsNotifyProduct ", zap.Any("", string(data)))

	err := json.Unmarshal([]byte(data), &product)
	err = json.Unmarshal([]byte(data), &shelves)
	err = json.Unmarshal([]byte(data), &afterSale)
	if err != nil {
		// log.Log().Error("WpsNotifyProduct校验参数错误", zap.Any("err", err))
	}
	if product.Type == 2 && len(product.CContent.Goods) > 0 { //商品价格修改
		service.WpsNotifyProduct(product)
	}
	if shelves.Type == 4 && len(shelves.CContent) > 0 { //商品上下架
		service.WpsNotifyShelves(shelves)
	}
	if shelves.Type == 16 && len(shelves.CContent) > 0 { //商品详情/规格更新
		service.WpsNotifyShelves(shelves)
	}
	if afterSale.Type == 28 { //上游受理, 中台同意售后
		service.WpsNotifyAfterSale(afterSale)

	}
	if afterSale.Type == 29 { //收到退货地址
		service.WpsNotifyAfterSale(afterSale)

	}

	c.String(http.StatusOK, "success")

}
func WpsNotifyShelves(c *gin.Context) {

	var req callback.WpsNotifyShelvesData
	data, _ := c.GetRawData()
	log.Log().Info("WpsNotifyShelves data", zap.Any("info", data))
	err := json.Unmarshal([]byte(data), &req)
	if err != nil {
		log.Log().Error("校验参数错误", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if req.Type != 4 {
		return
	}
	service.WpsNotifyShelves(req)
	c.String(http.StatusOK, "success")

}

type Level struct {
	LevelID int `json:"level_id" form:"level_id" `
}

// AfterSale
// @Tags 提交售后
// @Summary 提交售后
// @accept application/json
// @Produce application/json
// @Param data body request.AfterSale true "提交售后"
// @Success 200 {object} []interface{}
// @Router /api/gatherSupply/afterSale [post]
func AfterSales(c *gin.Context) {
	var requestAfterSale request.AfterSale
	err := c.ShouldBindJSON(&requestAfterSale)
	if err != nil {
		log.Log().Error("校验参数错误", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var orderModel model2.Order
	err = source.DB().Where("order_sn = ?", requestAfterSale.OrderSn.OrderSn).First(&orderModel).Error
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, key := service.GetGatherSupplyCategoryKey(orderModel.GatherSupplyID)
	var orderClass = order.NewOrder(key)
	err = orderClass.InitSetting(orderModel.GatherSupplyID)
	if err != nil {
		log.Log().Error("前置校验失败", zap.Any("info", err.Error()))
		return
	}
	if err, info := orderClass.AfterSale(requestAfterSale); err != nil {
		log.Log().Error("售后提交失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		c.JSON(http.StatusOK, info)
	}
}

// OrderCallBack
// @Tags 供应链回调接口
// @Summary 供应链回调接口
// @accept application/json
// @Produce application/json
// @Param data body model.CallBackType true "供应链回调接口"
// @Success 200 {object} []interface{}
// @Router /api/gatherSupply/notificationCallBack [post]
func OrderCallBack(c *gin.Context) {

	var requestData = make(map[string]interface{})
	var requestType callback.RequestCallBackType
	RawData, err := c.GetRawData()
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = json.Unmarshal(RawData, &requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//log.Log().Error("供应链回调接口数据OrderCallBack", zap.Any("requestData", requestData))
	if (requestData["id"] != "" && requestData["id"] != nil) && requestData["Event"] == nil {
		//msgId := strconv.FormatFloat(requestData["id"].(float64), 'f', -1, 32)
		requestType.MsgID = requestData["id"].(string)
		requestType.Data = requestData["data"]
		requestType.Type = requestData["type"].(string)
	} else {
		err = json.Unmarshal(RawData, &requestType)
		if err != nil {
			log.Log().Error("供应链解析回调参数错误1", zap.Any("err", err))
		}

	}

	log.Log().Info("中台回调接口接受消息成功", zap.Any("info", string(RawData)))

	var callBackInterfaceType string
	if requestType.MessageCode == 1 {
		callBackInterfaceType = "fulu"
	} else if requestType.MsgID != "" && requestType.HeheEvent == "" {
		callBackInterfaceType = "stbz"
	} else if requestType.MessageID != "" {
		callBackInterfaceType = "self"
	} else if requestType.AppKey != "" {
		callBackInterfaceType = "byn"
	} else if requestType.Appid != "" {
		callBackInterfaceType = "dwd"
	} else if requestType.HeheEvent != "" {
		callBackInterfaceType = "hehe"

	} else {
		yzResponse.FailWithMessage("缺少请求标识", c)
		return
	}
	//log.Log().Error("callBackInterfaceType", zap.Any("callBackInterfaceType", callBackInterfaceType))
	err = mq.PublishMessage(callback2.CallBackType{
		RawData: RawData,
	})
	if err != nil {
		log.Log().Info("供应链回调接口mq插入错误", zap.Any("err", err.Error()))
		yzResponse.FailWithMessage("请重试", c)
		return
	}
	if callBackInterfaceType == "dwd" {
		c.String(http.StatusOK, "SUCCESS")
	} else if callBackInterfaceType == "stbz" {
		c.JSON(http.StatusOK, gin.H{"code": 1})
	} else if callBackInterfaceType == "hehe" {
		c.JSON(http.StatusOK, gin.H{"return_code": "SUCCESS", "return_msg": "OK"})
	} else {
		c.JSON(http.StatusOK, gin.H{"code": 0})
	}
	return
}

func NotificationCallBack(RawData []byte) (err error) {

	var requestData = make(map[string]interface{})
	var requestType callback.RequestCallBackType
	log.Log().Info("NotificationCallBack供应链回调接口数据", zap.Any("data", string(RawData)))
	//log.Log().Error("NotificationCallBack供应链回调接口数据", zap.Any("data", string(RawData)))
	err = json.Unmarshal(RawData, &requestData)
	if err != nil {
		return
	}
	//log.Log().Error("供应链回调接口数据NotificationCallBack", zap.Any("requestData", requestData))
	if (requestData["id"] != "" && requestData["id"] != nil) && requestData["Event"] == nil {
		//msgId := strconv.FormatFloat(requestData["id"].(float64), 'f', -1, 32)
		requestType.MsgID = requestData["id"].(string)
		requestType.Data = requestData["data"]
		requestType.Type = requestData["type"].(string)
		log.Log().Info("记录回调数据，测试一天", zap.Any("info", requestType))
		log.Log().Info("记录回调数据，测试一天1", zap.Any("info", requestData))

	} else {
		err = json.Unmarshal(RawData, &requestType)
		if err != nil {
			log.Log().Error("供应链解析回调参数错误1", zap.Any("err", err))
		}

	}

	//log.Log().Info("中台回调接口接受消息成功", zap.Any("info", requestType))

	var callBackInterfaceType string
	if requestType.MessageCode == 1 {
		log.Log().Error("fulu 收到回调消息, 准备处理数据", zap.Any("info", requestType))
		callBackInterfaceType = "fulu"
	} else if requestType.MsgID != "" && requestType.HeheEvent == "" {
		log.Log().Info("stbz 收到回调消息，准备处理数据", zap.Any("info", requestType))

		callBackInterfaceType = "stbz"
		var data ccallback.CallBackType
		data.Type = requestType.Type
		data.MsgID = requestType.MsgID
		data.Data = requestType.Data
		err = gService.StbzOrderCallBack(data)
		if err != nil {
			log.Log().Error("stbz 中台队列处理错误PushCallBackHandles", zap.Any("err", err))
		}

		return
	} else if requestType.MessageID != "" {
		callBackInterfaceType = "self"
	} else if requestType.AppKey != "" {
		callBackInterfaceType = "byn"
	} else if requestType.Appid != "" {
		callBackInterfaceType = "dwd"
	} else if requestType.HeheEvent != "" {
		callBackInterfaceType = "hehe"
		err = json.Unmarshal(RawData, &requestType.HeheCallBackType)
		if err != nil {
			return
		}
	} else {
		return
	}
	callBackInterface := funcallback.NewCallback(callBackInterfaceType)
	if err = callBackInterface.CallBackMessage(requestType); err != nil {
		log.Log().Info("供应链回调处理出错，", zap.Any("err", err.Error()))
		return
	}
	return
}

func StbzThirdId(c *gin.Context) {

	var requestType callback.Goods
	err := c.ShouldBindJSON(&requestType)
	if err != nil {
		log.Log().Error("供应链解析回调参数错误", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.StbzThird(uint(requestType.ID)); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data.ThirdID, c)
	}

	//c.JSON(http.StatusOK, gin.H{"code": 1})

}
