package v1

import (
	model3 "after-sales/model"
	"encoding/json"
	service2 "gather-supply/service"
	"github.com/gin-gonic/gin"
	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
	"order/model"
	order2 "self-supply/component/order"
	"strconv"
	model2 "supplier/model"
	"supplier/service"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/source"
)

type ShopAddressOrderId struct {
	OrderId      uint   `json:"order_id" form:"order_id"`
	ThirdOrderSN string `json:"third_order_sn" form:"third_order_sn" gorm:"column:third_order_sn;comment:编号;"`     // 采购端单号
	AfterSalesID uint   `json:"after_sales_id" form:"after_sales_id" gorm:"column:after_sales_id;comment:售后申请id;"` // 售后申请id                                         //申请会员id

}

type ShopAddressAfterSalesIDs struct {
	AfterSalesIDs []uint `json:"after_sales_ids" form:"after_sales_ids" gorm:"column:after_sales_ids;comment:售后申请id;"` // 售后申请id                                         //申请会员id

}

// @Tags 商家地址管理
// @Summary 用id查询商家地址管理
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SupplierQualification true "用orderid查询退货地址"
// @Success 200 {object} model.ShopAddress
// @Router /shopAddress/findShopAddressByOrderId [get]
func FindShopAddressByOrderId(c *gin.Context) {
	var shopAddressRequest ShopAddressOrderId
	var err error
	err = c.ShouldBindQuery(&shopAddressRequest)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var afterSales model3.AfterSales
	//新版逻辑  通过售后id 获取指定地址 （审核之后才有指定地址）  -- 如果是中台供应链的订单 当上游同意的时候接收到监听会获取上游地址进行保存
	if shopAddressRequest.AfterSalesID != 0 {
		err = source.DB().Model(&model.AfterSales{}).Where("id = ?", shopAddressRequest.AfterSalesID).First(&afterSales).Error
		if err != nil {
			yzResponse.FailWithMessage("售后不存在", c)
			return
		}
		if afterSales.Status == model3.WaitAuditStatus {
			yzResponse.FailWithMessage("待审核售后暂无退货地址", c)
			return
		}
		if afterSales.NewShippingAddressID != 0 {
			var afterSalesShopAddress model3.AfterSalesShopAddress

			err = source.DB().Model(&model3.AfterSalesShopAddress{}).Preload("ProvinceName").Preload("CityName").Preload("DistrictName").Where("id = ?", afterSales.NewShippingAddressID).First(&afterSalesShopAddress).Error

			yzResponse.OkWithData(afterSalesShopAddress, c)
			return
		}
		shopAddressRequest.OrderId = afterSales.OrderID
		shopAddressRequest.ThirdOrderSN = ""
	}

	if shopAddressRequest.ThirdOrderSN == "" && shopAddressRequest.OrderId == 0 {
		yzResponse.FailWithMessage("请提交采购端订单号或者中台订单id", c)
		return
	}
	var order model.Order
	if shopAddressRequest.ThirdOrderSN != "" {
		err = source.DB().Where("third_order_sn = ?", shopAddressRequest.ThirdOrderSN).First(&order).Error
	} else {
		err = source.DB().Where("id = ?", shopAddressRequest.OrderId).First(&order).Error
	}
	if err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}

	if order.GatherSupplyID != 0 {
		//GetGatherSupplyCategoryKey
		var key string
		err, key = service2.GetGatherSupplyCategoryKey(order.GatherSupplyID)
		if err != nil {
			log.Log().Error("供应链标识获取失败!", zap.Any("err", err))
			yzResponse.FailWithMessage("供应链标识获取失败", c)
			return
		}
		//中台的获取第三方退货地址
		if key == "self" {
			var orderClass = &order2.Self{}
			err = orderClass.InitSetting(order.GatherSupplyID)
			if err != nil {
				log.Log().Error("供应链配置获取失败!", zap.Any("err", err))
				yzResponse.FailWithMessage("供应链配置获取失败", c)
				return
			}
			var res *stbz.APIResult
			err, res = orderClass.GetAfterSalesShopAddress(strconv.Itoa(int(order.OrderSN)), afterSales.SynAfterSalesId)
			if err != nil {
				log.Log().Error("获取第三方退货地址失败!", zap.Any("err", err))
				yzResponse.FailWithMessage("获取第三方退货地址失败"+err.Error(), c)
				return
			}
			if res.Code > 0 {
				log.Log().Error("获取第三方退货地址失败!", zap.Any("res", res))
				yzResponse.FailWithMessage("第三方未设置退货地址，请设置默认退货地址"+res.Msg, c)
				return
			}
			//这里保存是保持原逻辑
			var ShopAddress model2.ShopAddress
			resData, _ := json.Marshal(res.Data)
			_ = json.Unmarshal(resData, &ShopAddress)
			var OldShopAddress model2.ShopAddress
			source.DB().Where("third_shop_address_id = ?", ShopAddress.ID).First(&OldShopAddress)
			ShopAddress.GatherSupplyID = order.GatherSupplyID
			ShopAddress.IsShow = 1
			ShopAddress.ThirdShopAddressId = ShopAddress.ID

			if OldShopAddress.ID != 0 {
				ShopAddress.ID = OldShopAddress.ID
				ShopAddress.CreatedAt = OldShopAddress.CreatedAt
				err = source.DB().Model(&model2.ShopAddress{}).Where("id = ?", OldShopAddress.ID).Save(&ShopAddress).Error
			} else {
				ShopAddress.ID = 0
				err = source.DB().Create(&ShopAddress).Error
			}

			if err != nil {
				log.Log().Error("保存第三方退货地址失败!", zap.Any("err", err))
				yzResponse.FailWithMessage("保存第三方退货地址失败"+err.Error(), c)
				return
			}
			yzResponse.OkWithData(ShopAddress, c)
			return
		}
	}

	if err, res := service.GetShopAddressBySupplierId(order.SupplierID, 0, "0"); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("商家暂时没有配置默认退货地址", c)
		return
	} else {
		yzResponse.OkWithData(res, c)
	}
}

// FindShopAddressByAfterSalesIDs @Tags 商家地址管理
// @Summary 通过售后id查询商家地址
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SupplierQualification true "通过售后id查询商家地址"
// @Success 200 {object} model.ShopAddress
// @Router /shopAddress/findShopAddressAfterSalesID [post]
func FindShopAddressByAfterSalesIDs(c *gin.Context) {
	var shopAddressRequest ShopAddressAfterSalesIDs
	var err error
	err = c.ShouldBindJSON(&shopAddressRequest)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if len(shopAddressRequest.AfterSalesIDs) == 0 {
		yzResponse.FailWithMessage("请提交售后id", c)

		return
	}
	var afterSalesShopAddressIds []uint
	//新版逻辑  通过售后id 获取指定地址 （审核之后才有指定地址）
	err = source.DB().Model(&model.AfterSales{}).Where("id in ?", shopAddressRequest.AfterSalesIDs).Pluck("new_shipping_address_id", &afterSalesShopAddressIds).Error
	if err != nil {
		yzResponse.FailWithMessage("售后记录不存在"+err.Error(), c)

		return
	}
	var afterSalesShopAddress model3.AfterSalesShopAddress

	err = source.DB().Model(&model3.AfterSalesShopAddress{}).Preload("ProvinceName").Preload("CityName").Preload("DistrictName").Where("id in ?", afterSalesShopAddressIds).First(&afterSalesShopAddress).Error
	if err != nil {
		yzResponse.FailWithMessage("获取退货地址失败"+err.Error(), c)
		return
	}
	yzResponse.OkWithData(afterSalesShopAddress, c)
	return

}
