package listener

import (
	"after-sales/model"
	"after-sales/mq"
	"after-sales/request"
	aftersalesService "after-sales/service"
	order5 "dwd-supply/component/order"
	"encoding/json"
	"errors"
	"fmt"
	"gather-supply/service"
	"github.com/shopspring/decimal"
	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
	"gorm.io/gorm"
	hehe "hehe-supply/component/order"
	jdVopSupplyOrder "jd-vop-supply/component/order"
	jdVopPkgAddress "jd-vop-supply/jd-vop-pkg/address"
	jdVopPkgAfterSale "jd-vop-supply/jd-vop-pkg/after-sales"
	order2 "lijing-supply/component/order"
	maiGerSupplyOrder "maiger-supply/component/order"
	maiGerPkgAfter "maiger-supply/maiger-pkg/after"
	url2 "net/url"
	model2 "order/model"
	"order/order"
	productModel "product/model"
	publicSupplyCommon "public-supply/common"
	request2 "public-supply/request"
	publicSupplySetting "public-supply/setting"
	"reflect"
	self "self-supply/component/order"
	order4 "shama-supply/component/order"
	"strconv"
	order3 "tianma-supply/component/order"
	"time"
	order6 "wdt-supply/component/order"
	wps "weipinshang/component/order"
	yjf "yijifen/component/order"
	yyt "yiyatong/component/order"
	youxuan "youxuan-supply/component/order"
	"yz-go/component/log"
	"yz-go/source"
)

type AfterSalesRequest struct {
	OrderItemId  uint   `json:"order_item_id"`
	ThirdOrderSn string `json:"third_order_sn"`
	AfterSalesId uint   `json:"after_sales_id"`
	MessageType  string `json:"message_type"`
	MemberSign   string `json:"member_sign"`
}
type orderAfterSalesStrategyData struct {
	publicSupplySetting.OrderAfterSalesStrategy `json:"order_after_sales_strategy"`
}

// 售后处理
func AfterSalesDisposeHandles() {
	mq.PushHandles("afterSalesDispose", func(afterSales mq.AfterSalesMessage) error {

		var afterSalesModel model.AfterSales
		err := source.DB().Where("id = ?", afterSales.AfterSalesID).Preload("ShopAddress").Preload("ReturnOrderExpress").Preload("OrderItem.Product").Preload("OrderItem").Preload("Order.ShippingAddress").Preload("Order").First(&afterSalesModel).Error
		if err != nil {
			log.Log().Error("售后处理:查询售后失败", zap.Any("afterSales", afterSalesModel))

			return nil
		}
		//以前的 -- 上面默认的关联表都换成新得了 如果没查询到就是旧数据
		if afterSalesModel.ShopAddress.ID == 0 {
			source.DB().Model(&model.ShopAddress{}).Preload("ProvinceName").Preload("CityName").Preload("DistrictName").Where("id = ?", afterSalesModel.ShippingAddressID).First(&afterSalesModel.ShopAddress)
		}
		//不是供应链订单不处理
		if afterSalesModel.Order.GatherSupplyID == 0 {
			return nil
		}
		//如果是修改消息 但是售后并未往上游同步 则直接返回
		if afterSales.MessageType == mq.AfterSalesUpdate && afterSalesModel.SynAfterSalesId == 0 && afterSalesModel.SynAfterSalesIdString == "" && afterSalesModel.GatherSupplyAfterSaleSN == "" {
			log.Log().Error("售后处理:修改消息,售后并未同步到上游", zap.Any("afterSales", afterSales))
			return nil
		}
		//如果是售后创建则判断是否需要推送到第三方
		if afterSales.MessageType == mq.AfterSalesCreate {
			err, gatherSupplySetting := publicSupplySetting.GetSetting("gatherSupply" + strconv.Itoa(int(afterSalesModel.Order.GatherSupplyID)))
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				log.Log().Error("售后处理:查询配置失败", zap.Any("err", err), zap.Any("afterSalesModel", afterSalesModel))
				return nil
			}
			var orderAfterSalesStrategy orderAfterSalesStrategyData
			//转换失败按自动处理
			_ = json.Unmarshal([]byte(gatherSupplySetting.Value), &orderAfterSalesStrategy)
			//审核后提交直接返回
			if orderAfterSalesStrategy.Type == 1 {
				log.Log().Info("售后处理:该供应链设置审核后提交", zap.Any("afterSales", afterSales))
				return nil
			}
		}
		//如果审核通过 是第三方订单 同步第三方售后都是空代表申请时未同步 这里重新同步一下
		if afterSales.MessageType == mq.AfterSalesPassTheAudit && afterSalesModel.SynAfterSalesId == 0 && afterSalesModel.SynAfterSalesIdString == "" && afterSalesModel.GatherSupplyAfterSaleSN == "" {
			afterSales.MessageType = mq.AfterSalesCreate
		}

		err, afterSalesId := SynAfterSale(afterSalesModel, afterSales)
		log.Log().Info("售后消息接收成功2-2", zap.Any("afterSales", err), zap.Any("afterSales", afterSales))
		var mapData = make(map[string]interface{})
		//错误记录
		if err != nil {
			mapData["syn_type"] = -1
			mapData["syn_message"] = err.Error()
			//afterSalesModel.SynType = -1
			//afterSalesModel.SynMessage = err.Error()
		} else {
			if afterSalesId != 0 {
				mapData["syn_after_sales_id"] = afterSalesId
				//afterSalesModel.SynAfterSalesId = afterSalesId
			}
			mapData["syn_type"] = 1
			//afterSalesModel.SynType = 1
		}
		err = source.DB().Model(&model.AfterSales{}).Omit("syn_after_sales_id_string, source, gather_supply_after_sale_sn, gather_supply_status_string").Where("id = ?", afterSalesModel.ID).Updates(&mapData).Error
		if err != nil {
			log.Log().Info("售后保存记录失败", zap.Any("err", err), zap.Any("afterSales", afterSales))
			err = nil
		}
		return nil
	})
}

func IsNil(i interface{}) bool {
	vi := reflect.ValueOf(i)
	if vi.Kind() == reflect.Ptr {
		return vi.IsNil()
	}
	return false
}

func SynAfterSale(afterSalesModel model.AfterSales, afterSalesMessage mq.AfterSalesMessage) (err error, afterSalesId uint) {
	//因为增加不支持时也可以申请到中台这里增加验证 如果不支持则不往下同步
	if afterSalesMessage.MessageType == mq.AfterSalesCreate {
		//判断是否支持该售后方式
		var result interface{}
		var orderData model2.Order
		orderData = afterSalesModel.Order.Order
		orderData.OrderItems = append(orderData.OrderItems, afterSalesModel.OrderItem.OrderItem)
		var gatherSupply aftersalesService.GatherSupply

		err = source.DB().First(&gatherSupply, orderData.GatherSupplyID).Error
		if err != nil {
			err = errors.New("供应链信息错误" + err.Error())
			return
		}

		err, result = aftersalesService.GetAfterSalesTypeNameMap(orderData, gatherSupply, afterSalesModel.OrderItemID)
		if err != nil {
			return
		}
		var ok bool
		var afterSalesTypeNameMap map[string]interface{}
		afterSalesTypeNameMap, ok = result.(map[string]interface{})
		if !ok {
			err = errors.New("售后类型映射格式错误")
			return
		}
		_, ok = afterSalesTypeNameMap[strconv.Itoa(int(afterSalesModel.AfterSaleType))]
		if !ok {
			_, statusName := model.GetAfterSalesTypeName(model.AfterSalesType(afterSalesModel.AfterSaleType))
			errMsg := fmt.Sprintf("该订单不支持%s", statusName)
			err = errors.New(errMsg)
			return
		}
	}

	var product productModel.Product
	err = source.DB().Unscoped().Where("id = ?", afterSalesModel.ProductID).First(&product).Error
	if err != nil {
		log.Log().Info("售后处理:查询售后商品失败", zap.Any("product", product))
		err = errors.New("售后处理:查询售后商品失败" + err.Error())
		return
	}

	// sku软删除数据查询
	var skuModel productModel.Sku
	if err = source.DB().Unscoped().Where("id = ?", afterSalesModel.SkuID).First(&skuModel).Error; err != nil {
		log.Log().Info("售后处理:查询售后商品SKU失败", zap.Any("product", product))
		err = errors.New("售后处理:查询售后商品SKU失败" + err.Error())
		return
	}

	var key string

	err, key = service.GetGatherSupplyCategoryKey(product.GatherSupplyID)
	if err != nil {
		log.Log().Error("获取供应链标识失败"+strconv.Itoa(int(product.GatherSupplyID)), zap.Any("err", err.Error()))
		err = errors.New("获取供应链标识失败" + strconv.Itoa(int(product.GatherSupplyID)) + err.Error())
		return
	}

	//除了中台供应链的其他的不处理
	if key == "self" {
		var orderClass = &self.Self{}
		err = orderClass.InitSetting(product.GatherSupplyID)
		if err != nil {
			return
		}
		//申请售后,修改售后
		if afterSalesMessage.MessageType == mq.AfterSalesCreate || afterSalesMessage.MessageType == mq.AfterSalesUpdate {
			var sku productModel.Sku
			err = source.DB().Unscoped().Where("id = ?", afterSalesModel.SkuID).First(&sku).Error
			if err != nil {
				log.Log().Info("售后处理:查询售后商品规格失败", zap.Any("err", err))
				err = errors.New("售后处理:查询售后商品规格失败" + err.Error())
				return
			}
			var resOrder *stbz.APIResult

			err, resOrder = orderClass.GetOrderByThirdOrderSn(request.AfterSales{ThirdOrderSN: strconv.Itoa(int(afterSalesModel.Order.OrderSN))})
			var resOrderData []order.Order
			if resOrder == nil || resOrder.Data == nil {
				return errors.New("resOrder or resOrder.Data is nil"), 0
			}
			datas, ok := resOrder.Data.(map[string]interface{})
			if !ok {
				return errors.New("resOrder.Data is not of type map[string]interface{}"), 0
			}
			orderDetail1, _ := json.Marshal(datas["read"])

			err = json.Unmarshal(orderDetail1, &resOrderData)
			if err != nil {
				log.Log().Error("获取中台供应链订单详情失败"+strconv.Itoa(int(afterSalesModel.Order.OrderSN)), zap.Any("err", err.Error()))
				err = errors.New("获取中台供应链订单详情失败" + strconv.Itoa(int(afterSalesModel.Order.OrderSN)) + err.Error())
				return
			}
			var ThirdOrderId, ThirdOrderItemId, Amount, TechnicalServicesFee, Freight uint
			for _, orderData := range resOrderData {
				for _, orderDataItem := range orderData.OrderItems {
					if orderDataItem.SkuID == uint(sku.OriginalSkuID) {
						ThirdOrderId = orderDataItem.OrderID
						ThirdOrderItemId = orderDataItem.ID
						Amount = orderDataItem.Amount
						TechnicalServicesFee = orderDataItem.TechnicalServicesFee
						// 根据退款方式计算技术服务费
						if orderDataItem.TechnicalServicesFee > 0 {
							if afterSalesModel.RefundWay == model.RefundWayPrice && afterSalesModel.Amount > 0 {
								// 按退款金额比例计算技术服务费
								refundRatio := float64(afterSalesModel.Amount) / float64(afterSalesModel.OrderItem.Amount)
								TechnicalServicesFee = uint(float64(orderDataItem.TechnicalServicesFee) * refundRatio)
							} else if afterSalesModel.RefundWay == model.RefundWayNum && afterSalesModel.Num > 0 {
								// 按退款个数比例计算技术服务费
								refundRatio := float64(orderDataItem.TechnicalServicesFee) / float64(orderDataItem.Qty)
								TechnicalServicesFee = uint(float64(afterSalesModel.Num) * refundRatio)
							}
						}

						// 根据退款方式计算技术服务费
						if orderDataItem.Amount > 0 {
							if afterSalesModel.RefundWay == model.RefundWayPrice && afterSalesModel.Amount > 0 {
								// 按退款金额比例计算退款金额
								refundRatio := float64(afterSalesModel.Amount) / float64(afterSalesModel.OrderItem.Amount)
								Amount = uint(float64(orderDataItem.Amount) * refundRatio)
							} else if afterSalesModel.RefundWay == model.RefundWayNum && afterSalesModel.Num > 0 {
								// 按退款个数比例计算退款金额
								refundRatio := float64(orderDataItem.Amount) / float64(orderDataItem.Qty)
								Amount = uint(float64(afterSalesModel.Num) * refundRatio)
							}
						}
						Freight = orderData.Freight
						break
					}
				}
			}
			//因为运费使用的是主订单的，所以哪个子订单申请时退运费了，同步的时候也对那个退运费
			if afterSalesModel.Freight == 0 {
				Freight = 0
			}
			var BarterSku productModel.Sku
			//如果是换货,查询换货规格的第三方规格id
			if afterSalesModel.RefundType == model.Barter {
				err = source.DB().Unscoped().Where("id = ?", afterSalesModel.BarterSkuID).First(&BarterSku).Error
				if err != nil {
					log.Log().Info("售后处理:查询售后换货商品规格失败"+strconv.Itoa(int(afterSalesModel.BarterSkuID)), zap.Any("err", err))
					err = errors.New("售后处理:查询售后换货商品规格失败" + strconv.Itoa(int(afterSalesModel.BarterSkuID)) + err.Error())
					return
				}
			}

			var res *stbz.APIResult
			err, res = orderClass.AfterSalesCreate(request.AfterSales{
				OrderID:              ThirdOrderId,
				ThirdOrderSN:         strconv.Itoa(int(afterSalesModel.Order.OrderSN)),
				OrderItemID:          ThirdOrderItemId,
				Amount:               Amount,
				TechnicalServicesFee: TechnicalServicesFee,
				Freight:              Freight,
				ReasonType:           afterSalesModel.ReasonType,
				Reason:               afterSalesModel.Reason,
				Description:          afterSalesModel.Description,
				IsReceived:           afterSalesModel.IsReceived,
				DetailImages:         afterSalesModel.DetailImages,
				RefundType:           afterSalesModel.RefundType,
				BarterSkuID:          uint(BarterSku.OriginalSkuID),
				BarterNum:            afterSalesModel.BarterNum,
				BarterSkuTitle:       BarterSku.Title,
				ImageUrl:             afterSalesModel.ImageUrl,
				RefundWay:            afterSalesModel.RefundWay,
				Num:                  afterSalesModel.Num,
			})
			if err != nil {
				err = errors.New("自动同步申请售后失败" + err.Error())
				return
			}
			if res.Code != 0 {
				err = errors.New("自动同步申请售后失败" + res.Msg)
				return
			}
			if IsNil(res.Data) == false {
				afterSalesId = uint(res.Data.(float64))
			}
			return
		}
		if afterSalesModel.SynAfterSalesId == 0 {
			log.Log().Error("不是申请/修改售后 没有第三方售后id不处理--旧数据" + strconv.Itoa(int(product.GatherSupplyID)))
			return nil, 0
		}
		//售后用户发货
		if afterSalesMessage.MessageType == mq.AfterSalesUserSend {
			if afterSalesModel.RefundType == model.Barter || afterSalesModel.RefundType == model.Return {
				//var shopAddress  model.ShopAddress
				//
				var res *stbz.APIResult
				err, res = orderClass.AfterSalesSend(request.SendRequest{
					AfterSalesID:      afterSalesModel.SynAfterSalesId,
					CompanyName:       afterSalesModel.ReturnOrderExpress.CompanyName,
					CompanyCode:       afterSalesModel.ReturnOrderExpress.CompanyCode,
					ExpressNo:         afterSalesModel.ReturnOrderExpress.ExpressNo,
					ShippingAddressID: afterSalesModel.ShopAddress.ThirdShopAddressId,
				})
				if err != nil {
					err = errors.New("自动同步售后用户发货失败" + err.Error())
					return
				}
				if res.Code > 0 {
					err = errors.New("自动同步售后用户发货失败" + res.Msg)
					return
				}
			}
		}
		//用户收货的完成是由同步方发起，其他的售后完成都是由被同步方发起
		if afterSalesMessage.MessageType == mq.AfterSalesComplete && afterSalesModel.RefundType == model.Barter {
			var res *stbz.APIResult
			err, res = orderClass.AfterSalesUserReceive(request.AfterSales{
				Id: afterSalesModel.SynAfterSalesId,
			})
			if err != nil {
				err = errors.New("自动同步售后用户收货失败" + err.Error())
				return
			}
			if res.Code > 0 {
				err = errors.New("自动同步售后用户收货失败" + res.Msg)
				return
			}
		}
		if afterSalesMessage.MessageType == mq.AfterSalesClose {
			var res *stbz.APIResult
			err, res = orderClass.AfterSalesUserClose(request.AfterSales{
				Id: afterSalesModel.SynAfterSalesId,
			})
			if err != nil {
				err = errors.New("自动同步售后用户关闭失败" + err.Error())
				return
			}
			if res.Code > 0 {
				err = errors.New("自动同步售后用户关闭失败" + res.Msg)
				return
			}
		}

		//switch afterSalesMessage.MessageType {
		//	case mq.AfterSalesCreate: //申请售后
		//	case mq.AfterSalesUpdate: //修改售后
		//
		//		break
		//	//case mq.AfterSalesUpdate: //修改售后
		//	//	break
		//		//以下状态由消息推送给下级
		//	//case mq.AfterSalesRejectAudit: //售后驳回
		//	//	break
		//	//case mq.AfterSalesComplete: //售后完成 确认退款
		//	//	break
		//	//case mq.AfterSalesReceiving: //售后确认收货 (如果是换货 就是 确认收货并发货)
		//	//	break
		//	//case mq.AfterSalesPassTheAudit: //售后审核通过
		//	//	break
		//	case mq.AfterSalesUserSend: //售后用户发货
		//		break
		//	default:
		//		return
		//}
	} else if key == "hehe" {
		var orderClass = &hehe.Hehe{}
		err = orderClass.InitSetting(product.GatherSupplyID)
		if err != nil {
			return
		}
		//申请售后
		if afterSalesMessage.MessageType == mq.AfterSalesCreate {
			err = orderClass.AfterSaleCreate(afterSalesModel)
			if err != nil {
				err = errors.New("和合同步售后申请失败，" + err.Error())
			}
			return
		}
		if afterSalesModel.SynAfterSalesId == 0 {
			log.Log().Error("不是申请/修改售后 没有第三方售后id不处理--旧数据" + strconv.Itoa(int(product.GatherSupplyID)))
			return nil, 0
		}
		//售后用户发货
		if afterSalesMessage.MessageType == mq.AfterSalesUserSend {
			if afterSalesModel.RefundType == model.Barter || afterSalesModel.RefundType == model.Return {
				//var shopAddress  model.ShopAddress
				//

				err = orderClass.AfterSalesSend(request.SendRequest{
					AfterSalesID:      afterSalesModel.SynAfterSalesId,
					CompanyName:       afterSalesModel.ReturnOrderExpress.CompanyName,
					CompanyCode:       afterSalesModel.ReturnOrderExpress.CompanyCode,
					ExpressNo:         afterSalesModel.ReturnOrderExpress.ExpressNo,
					ShippingAddressID: afterSalesModel.ShopAddress.ThirdShopAddressId,
				})
				if err != nil {
					err = errors.New("和合自动同步售后用户发货失败" + err.Error())
					return
				}
			}
		}

		if afterSalesMessage.MessageType == mq.AfterSalesClose {
			err = orderClass.AfterSalesClose(afterSalesModel)
			if err != nil {
				err = errors.New("和合自动同步售后用户关闭失败" + err.Error())
				return
			}
		}

	} else if key == "maiger" {
		m := maiGerSupplyOrder.MaiGerSupply{}

		if err = m.InitSetting(product.GatherSupplyID); err != nil {
			log.Log().Error("迈戈供应链售后处理:初始化设置失败", zap.Any("afterSalesModel", afterSalesModel), zap.Any("err", err.Error()))
			return
		}

		// 如果换货，num 用 barter_num 字段
		if afterSalesModel.RefundType == 2 {
			afterSalesModel.Num = afterSalesModel.BarterNum
		}

		// 售后申请
		if afterSalesMessage.MessageType == mq.AfterSalesCreate || afterSalesMessage.MessageType == mq.AfterSalesUpdate {
			// 售后类型
			var returnType int
			switch afterSalesModel.RefundType {
			case 0:
				returnType = 7 // 退款
			case 1:
				returnType = 0 // 退货退款
			case 2:
				returnType = 2 // 换货
			}

			// 售后原因
			_, reasons := model.GetRefundReasonName(afterSalesModel.ReasonType)

			createParams := maiGerPkgAfter.CreateParams{
				AccessToken: m.AccessToken,
				Domain:      m.Host,
				CreateInfo: []maiGerPkgAfter.CreateRequest{
					{
						OrderSn:      afterSalesModel.Order.GatherSupplySN,
						SkuId:        skuModel.SourceStrId,
						Reasons:      reasons,
						ReturnType:   returnType,
						PicList:      []string{},
						SupplierList: []maiGerPkgAfter.SupplierList{},
					},
				},
			}

			// 创建退款的时候supplierList不用填写，其他申请必须填写
			if returnType != 7 {
				// 根据订单商品ID获取发货包裹信息
				queryParams := maiGerPkgAfter.QueryParams{
					AccessToken: m.AccessToken,
					Domain:      m.Host,
					OrderSn:     afterSalesModel.Order.GatherSupplySN,
					SkuId:       skuModel.SourceStrId,
				}

				var queryResult []maiGerPkgAfter.QueryResult
				if queryResult, err = maiGerPkgAfter.Query(queryParams); err != nil {
					log.Log().Error("售后申请失败", zap.Any("queryResult", queryResult), zap.Any("err", err.Error()))
					return
				}

				var supplierList []maiGerPkgAfter.SupplierList
				for _, item := range queryResult {
					supplierListItem := maiGerPkgAfter.SupplierList{
						SupplierId:   item.SupplierID,
						Quantity:     int(afterSalesModel.Num),
						StoreHouseId: item.StoreHouseID,
						LogisticsNo:  item.LogisticsNo,
						LogisticsId:  item.LogisticsID,
					}
					supplierList = append(supplierList, supplierListItem)
				}
				createParams.CreateInfo[0].SupplierList = supplierList
			}

			var createResult []maiGerPkgAfter.CreateResult
			if createResult, err = maiGerPkgAfter.Create(createParams); err != nil {
				log.Log().Error("售后申请失败", zap.Any("applyParams", createParams), zap.Any("err", err.Error()))
				return
			}

			// 处理售后ID
			var afterSalesStrId string
			for _, item := range createResult {
				for _, strId := range item.AfterSaleIdList {
					afterSalesStrId = afterSalesStrId + strId + "-"
				}
			}
			afterSalesStrId = afterSalesStrId[:len(afterSalesStrId)-1]

			err = source.DB().Model(model.AfterSales{}).Where("id = ?", afterSalesModel.ID).Updates(map[string]interface{}{"source": publicSupplyCommon.SUPPLY_MAIGER, "syn_after_sales_id_string": afterSalesStrId}).Error
			if err != nil {
				log.Log().Error("售后申请更新数据失败", zap.Any("afterSalesModelID", afterSalesModel.ID), zap.Any("createResult", createResult), zap.Any("err", err.Error()))
				return
			}
			return
		}

		// 售后取消
		if afterSalesMessage.MessageType == mq.AfterSalesClose {
			if afterSalesModel.SynAfterSalesIdString == "" {
				return
			}

			cancelParams := maiGerPkgAfter.CancelParams{
				AccessToken: m.AccessToken,
				Domain:      m.Host,
				AfterId:     afterSalesModel.SynAfterSalesIdString,
			}

			if err = maiGerPkgAfter.Cancel(cancelParams); err != nil {
				log.Log().Error("取消售后失败", zap.Any("cancelParams", cancelParams), zap.Any("err", err.Error()))
				return
			}
			return
		}

		// 用户发货(填写运单信息)
		if afterSalesMessage.MessageType == mq.AfterSalesUserSend {
			sendParams := maiGerPkgAfter.SendParams{
				AccessToken: m.AccessToken,
				Domain:      m.Host,
				SendInfo: []maiGerPkgAfter.SendRequest{
					{
						AfterId:      afterSalesModel.SynAfterSalesIdString,
						SkuId:        skuModel.SourceStrId,
						ShippingName: afterSalesModel.ReturnOrderExpress.CompanyName,
						LogisticsNo:  afterSalesModel.ReturnOrderExpress.ExpressNo,
						SendNumber:   int(afterSalesModel.Num),
					},
				},
			}

			if err = maiGerPkgAfter.Send(sendParams); err != nil {
				log.Log().Error("用户发货失败", zap.Any("sendParams", sendParams), zap.Any("err", err.Error()))
				return
			}
			return
		}

		// 售后完成
		if afterSalesMessage.MessageType == mq.AfterSalesComplete {
			signParams := maiGerPkgAfter.SignParams{
				AccessToken: m.AccessToken,
				Domain:      m.Host,
				AfterId:     strconv.Itoa(int(afterSalesModel.SynAfterSalesId)),
			}

			if err = maiGerPkgAfter.Sign(signParams); err != nil {
				log.Log().Error("确认收货失败", zap.Any("signParams", signParams), zap.Any("err", err.Error()))
				return
			}
			return
		}

	} else if key == "jd_vop" {
		var gatherSn uint64
		if gatherSn, err = strconv.ParseUint(afterSalesModel.OrderItem.GatherSupplySN, 10, 0); err != nil {
			log.Log().Error("售后处理:地址转换失败", zap.Any("afterSalesModel", afterSalesModel), zap.Any("err", err.Error()))
			return
		}

		jdOrderId := uint(gatherSn)
		orderIdStr := strconv.FormatUint(uint64(afterSalesModel.OrderID), 10)

		// 实例
		var jd = &jdVopSupplyOrder.JdVop{}
		if err = jd.InitSetting(product.GatherSupplyID); err != nil {
			return
		}

		// 地址转换
		address := afterSalesModel.Order.ShippingAddress.Province + afterSalesModel.Order.ShippingAddress.City + afterSalesModel.Order.ShippingAddress.City + afterSalesModel.Order.ShippingAddress.Town + afterSalesModel.Order.ShippingAddress.Detail

		var transResult jdVopPkgAddress.TransResult
		if transResult, err = jdVopPkgAddress.Trans(jd.AccessToken, address); err != nil {
			return
		}

		// 售后申请
		if afterSalesMessage.MessageType == mq.AfterSalesCreate || afterSalesMessage.MessageType == mq.AfterSalesUpdate {
			customerExpect := 10
			if afterSalesModel.RefundType == model.Barter {
				customerExpect = 20
			}

			applyParam := jdVopPkgAfterSale.ApplyParams{
				AccessToken: jd.AccessToken,
				Param: jdVopPkgAfterSale.ParamInfo{
					CustomerPin:  jd.SysSetting.BaseInfo.Username,
					OrderId:      jdOrderId,
					ThirdApplyId: orderIdStr,
					CustomerInfo: jdVopPkgAfterSale.AfsCustomerInfo{
						CustomerName:        afterSalesModel.Order.ShippingAddress.Realname,
						CustomerMobilePhone: afterSalesModel.Order.ShippingAddress.Mobile,
					},
					PickWareInfo: jdVopPkgAfterSale.AfsPickupWareInfo{
						PickWareType:     40,
						PickWareProvince: transResult.ProvinceId,
						PickWareCity:     transResult.CityId,
						PickWareCounty:   transResult.CountyId,
						PickWareAddress:  afterSalesModel.Order.ShippingAddress.Detail,
					},
					ReturnWareInfo: jdVopPkgAfterSale.AfsReturnWareInfo{
						ReturnWareType:     20,
						ReturnWareProvince: transResult.ProvinceId,
						ReturnWareCity:     transResult.CityId,
						ReturnWareCountry:  transResult.CountyId,
						ReturnWareAddress:  afterSalesModel.Order.ShippingAddress.Detail,
					},
					AfsApplyInfoItemList: []jdVopPkgAfterSale.AfsApplyInfoItem{
						{
							WareDescInfo:   jdVopPkgAfterSale.WareDescInfo{},
							CustomerExpect: customerExpect,
							WareDetailInfo: jdVopPkgAfterSale.WareDetailInfo{
								WareId:     afterSalesModel.OrderItem.Product.SourceGoodsID,
								MainWareId: afterSalesModel.OrderItem.Product.SourceGoodsID,
								WareName:   afterSalesModel.OrderItem.Product.Title,
								WareNum:    afterSalesModel.Num,
								WareType:   10, // 商品类型。10主商品，20赠品。
							},
						},
					},
				},
			}
			if err = jdVopPkgAfterSale.Apply(applyParam); err != nil {
				log.Log().Error("jd_vop 售后申请失败 ", zap.Any("err", err))
				return
			}

			afterSalesId = publicSupplyCommon.SUPPLY_JD_VOP
		}

		// 售后取消
		if afterSalesMessage.MessageType == mq.AfterSalesClose {
			log.Log().Info("jd_vop 售后取消消息接收成功 ", zap.Any("afterSales", afterSalesModel))
			cancelParams := jdVopPkgAfterSale.CancelParams{
				AccessToken: jd.AccessToken,
				Param: jdVopPkgAfterSale.CancelParamInfo{
					CustomerPin:  jd.SysSetting.BaseInfo.Username,
					OrderId:      jdOrderId,
					ThirdApplyId: orderIdStr,
				},
			}
			if err = jdVopPkgAfterSale.Cancel(cancelParams); err != nil {
				log.Log().Error("jd_vop 售后取消失败 ", zap.Any("err", err))
				return
			}
			return
		}

		// 用户发货(填写运单信息)
		if afterSalesMessage.MessageType == mq.AfterSalesUserSend {
			log.Log().Info("jd_vop 用户发货(填写运单信息) ", zap.Any("afterSales", afterSalesModel))
			sendParams := jdVopPkgAfterSale.SendParams{
				AccessToken: jd.AccessToken,
				Param: jdVopPkgAfterSale.SendParamsInfo{
					CustomerPin:  jd.SysSetting.BaseInfo.Username,
					OrderId:      jdOrderId,
					ThirdApplyId: orderIdStr,
					WaybillInfoList: []jdVopPkgAfterSale.WaybillInfo{
						{
							ExpressCompany: afterSalesModel.ReturnOrderExpress.CompanyName,
							ExpressCode:    afterSalesModel.ReturnOrderExpress.ExpressNo,
							DeliverDate:    time.Now().Format("2006-01-02 15:04:05"),
							FreightMoney:   0,
							WareId:         afterSalesModel.OrderItem.Product.SourceGoodsID,
							WareNum:        afterSalesModel.Num,
							WareType:       int(afterSalesModel.Num),
						},
					},
				},
			}
			if err = jdVopPkgAfterSale.Send(sendParams); err != nil {
				log.Log().Error("jd_vop 用户发货(填写运单信息错误) ", zap.Any("err", err))
				return
			}
			return
		}

		// 售后完成（售后申请单在待确认状态超过15天，会自动确认）
		if afterSalesMessage.MessageType == mq.AfterSalesComplete {
			log.Log().Info("jd_vop 售后完成消息接收成功 ", zap.Any("afterSales", afterSalesModel))
			confirmParams := jdVopPkgAfterSale.ConfirmParams{
				AccessToken: jd.AccessToken,
				Param: jdVopPkgAfterSale.ConfirmParamsInfo{
					CustomerPin:   jd.SysSetting.BaseInfo.Username,
					OrderId:       jdOrderId,
					ThirdApplyIds: orderIdStr,
				},
			}
			if err = jdVopPkgAfterSale.Confirm(confirmParams); err != nil {
				log.Log().Error("jd_vop 售后完成失败 ", zap.Any("err", err))
				return
			}
			return
		}

	} else if key == "tianma" {
		var orderClass = &order3.TianMa{}
		err = orderClass.InitSetting(product.GatherSupplyID)
		if err != nil {
			return
		}
		//申请售后,修改售后

		var request request2.AfterSale
		if afterSalesMessage.MessageType == mq.AfterSalesCreate || afterSalesMessage.MessageType == mq.AfterSalesUpdate {
			request.OrderSn.OrderSn = afterSalesModel.Order.GatherSupplySN
			request.ReasonsDescription = afterSalesModel.Description
			if afterSalesModel.RefundType == 0 || afterSalesModel.RefundType == 1 { //退款
				log.Log().Info("tianma AfterSale", zap.Any("", afterSalesModel.Order))

				if afterSalesModel.Order.SentAt == nil {
					log.Log().Info("tianma AfterSale 未发货，取消订单", zap.Any("", afterSalesModel.Order.GatherSupplySN))

					orderSn := strconv.Itoa(int(afterSalesModel.Order.OrderSN))
					err = orderClass.ApplyCancel(orderSn)
					if err != nil {
						return
					}

				} else {
					log.Log().Info("tianma AfterSale 已发货，正常走售后", zap.Any("", afterSalesModel.Order.GatherSupplySN))

					err, _ = orderClass.AfterSale(request)
					if err != nil {
						return
					}
				}

			}

		}

	} else if key == "lijing" {
		var orderClass = &order2.Lijing{}
		err = orderClass.InitSetting(product.GatherSupplyID)
		if err != nil {
			return
		}
		//申请售后,修改售后
		if afterSalesMessage.MessageType == mq.AfterSalesCreate || afterSalesMessage.MessageType == mq.AfterSalesUpdate {
			var sku productModel.Sku
			err = source.DB().Unscoped().Where("id = ?", afterSalesModel.SkuID).First(&sku).Error
			if err != nil {
				log.Log().Info("售后处理:查询售后商品规格失败", zap.Any("err", err))
				err = errors.New("售后处理:查询售后商品规格失败" + err.Error())
				return
			}

			var BarterSku productModel.Sku
			//如果是换货,查询换货规格的第三方规格id
			if afterSalesModel.RefundType == model.Barter {
				err = source.DB().Unscoped().Where("id = ?", afterSalesModel.BarterSkuID).First(&BarterSku).Error
				if err != nil {
					log.Log().Info("售后处理:查询售后换货商品规格失败"+strconv.Itoa(int(afterSalesModel.BarterSkuID)), zap.Any("err", err))
					err = errors.New("售后处理:查询售后换货商品规格失败" + strconv.Itoa(int(afterSalesModel.BarterSkuID)) + err.Error())
					return
				}
			}

			var res []byte
			err, res = orderClass.AfterSalesCreate(request.AfterSales{
				OrderID:        afterSalesModel.OrderID,
				ThirdOrderSN:   strconv.Itoa(int(afterSalesModel.Order.OrderSN)),
				OrderItemID:    afterSalesModel.OrderItemID,
				ReasonType:     afterSalesModel.ReasonType,
				Reason:         afterSalesModel.Reason,
				Description:    afterSalesModel.Description,
				IsReceived:     afterSalesModel.IsReceived,
				DetailImages:   afterSalesModel.DetailImages,
				RefundType:     afterSalesModel.RefundType,
				BarterSkuID:    uint(BarterSku.OriginalSkuID),
				BarterNum:      afterSalesModel.BarterNum,
				BarterSkuTitle: BarterSku.Title,
				ImageUrl:       afterSalesModel.ImageUrl,
				RefundWay:      afterSalesModel.RefundWay,
				Num:            afterSalesModel.Num,
			})
			if err != nil {
				err = errors.New("自动同步申请售后失败" + err.Error())
				return
			}
			var response struct {
				Msg  string `json:"message"`
				Code int    `json:"code"`
			}
			err = json.Unmarshal(res, &response)
			if err != nil {
				err = errors.New("自动同步申请售后失败" + err.Error())

				return
			}
			if response.Code != 0 {
				err = errors.New("自动同步申请售后失败" + response.Msg)
				return
			}
			return
		}
	} else if key == "yjf" {
		var orderClass = &yjf.Yjf{}
		err = orderClass.InitSetting(product.GatherSupplyID)
		var orderItem model2.OrderItem
		source.DB().Where("id=?", afterSalesModel.OrderItemID).First(&orderItem)
		if err != nil {
			return
		}

		if afterSalesMessage.MessageType == mq.AfterSalesCreate {
			log.Log().Info("yjf 售后消息接收成功1", zap.Any("afterSales", afterSalesModel))
			if afterSalesModel.RefundType == 1 || afterSalesModel.RefundType == 0 {
				err = orderClass.AfsApplyRefund(afterSalesModel.Order.GatherSupplySN, orderItem.GatherSupplySN, afterSalesModel.AfterSaleSN, afterSalesModel.Description, afterSalesModel.ID)
				if err != nil {
					err = errors.New("自动同步申请售后失败" + err.Error())
					return
				}
				var afterSales model.AfterSales
				afterSales.SynAfterSalesIdString = "提交成功"
				err = source.DB().Where("id=?", afterSalesModel.ID).Updates(&afterSales).Error
				if err != nil {
					return
				}
			}

			return
		}

		if afterSalesMessage.MessageType == mq.AfterSalesUserSend {
			if afterSalesModel.RefundType == model.Return {
				log.Log().Info("yjf  退货物流信息", zap.Any("info", afterSalesModel))

				var jd bool //京东商品

				err = orderClass.UpdateRefundOrderExpress(afterSalesModel.GatherSupplyAfterSaleSN, afterSalesModel.ReturnOrderExpress.CompanyName, afterSalesModel.ReturnOrderExpress.ExpressNo, jd)
				if err != nil {
					return
				}

			}
		}

	} else if key == "weipinshang" {
		var orderClass = &wps.Wps{}
		err = orderClass.InitSetting(product.GatherSupplyID)
		var orderItem model2.OrderItem
		source.DB().Where("id=?", afterSalesModel.OrderItemID).First(&orderItem)
		if err != nil {
			return
		}

		if afterSalesMessage.MessageType == mq.AfterSalesClose {
			log.Log().Info("weipinshang 售后取消消息接收成功 ", zap.Any("afterSales", afterSalesModel))
			headerData := make(map[string]string)
			reqValue := url2.Values{}
			headerData["mcOrderNo"] = orderItem.GatherSupplySN

			reqValue.Add("mcOrderNo", orderItem.GatherSupplySN)

			err = orderClass.OrderCancel(headerData, reqValue)

			if err != nil {
				log.Log().Info("weipinshang OrderCancel ", zap.Any("info", err))
			}

		}
		if afterSalesMessage.MessageType == mq.AfterSalesCreate || afterSalesMessage.MessageType == mq.AfterSalesUpdate {

			headerData := make(map[string]string)
			reqValue := url2.Values{}
			log.Log().Info("售后消息接收成功2-3", zap.Any("afterSales", afterSalesModel))

			if afterSalesModel.RefundType == 0 { //退款
				headerData["mcOrderNo"] = orderItem.GatherSupplySN
				headerData["customerExpect"] = "40"
				reqValue.Add("mcOrderNo", orderItem.GatherSupplySN)
				reqValue.Add("customerExpect", "40")
				err = orderClass.AfsApply(headerData, reqValue)
				log.Log().Info("售后消息接收成功2-4", zap.Any("afterSales", err))

				if err != nil {
					err = errors.New("自动同步申请售后失败" + err.Error())
					return
				}

				var afterSales model.AfterSales
				afterSales.SynAfterSalesIdString = "提交成功"
				err = source.DB().Where("id=?", afterSalesModel.ID).Updates(&afterSales).Error
				if err != nil {
					return
				}
			}

			if afterSalesModel.RefundType == 1 {

				headerData["mcOrderNo"] = orderItem.GatherSupplySN
				headerData["customerExpect"] = "10"
				headerData["questionDesc"] = afterSalesModel.Description
				reqValue.Add("mcOrderNo", orderItem.GatherSupplySN)
				reqValue.Add("customerExpect", "10")
				reqValue.Add("questionDesc", afterSalesModel.Description)

				err = orderClass.AfsApply(headerData, reqValue)
				if err != nil {
					err = errors.New("自动同步申请售后失败" + err.Error())
					return
				}
				var afterSales model.AfterSales
				afterSales.SynAfterSalesIdString = "提交成功"
				err = source.DB().Where("id=?", afterSalesModel.ID).Updates(&afterSales).Error
				if err != nil {
					return
				}
			}

			return
		}

		if afterSalesMessage.MessageType == mq.AfterSalesUserSend {
			if afterSalesModel.RefundType == model.Barter || afterSalesModel.RefundType == model.Return {

				headerData := make(map[string]string)
				reqValue := url2.Values{}
				headerData["mcOrderNo"] = orderItem.GatherSupplySN
				headerData["deliveryName"] = afterSalesModel.ReturnOrderExpress.CompanyName
				headerData["deliveryNo"] = afterSalesModel.ReturnOrderExpress.ExpressNo
				reqValue.Add("mcOrderNo", orderItem.GatherSupplySN)
				reqValue.Add("deliveryName", afterSalesModel.ReturnOrderExpress.CompanyName)
				reqValue.Add("deliveryNo", afterSalesModel.ReturnOrderExpress.ExpressNo)

				err = orderClass.AfsApplyRefund(headerData, reqValue)

			}
		}

	} else if key == "yiyatong" {
		var orderClass = &yyt.Yyt{}
		err = orderClass.InitSetting(product.GatherSupplyID)
		var orderItem model2.OrderItem
		err = source.DB().Where("id=?", afterSalesModel.OrderItemID).First(&orderItem).Error
		if err != nil {
			return
		}
		if afterSalesMessage.MessageType == mq.AfterSalesComplete {

			log.Log().Info("afterSalesMessage.MessageType == mq.AfterSalesComplete  ")
			if afterSalesModel.RefundType == 2 { //换货用户确认收货
				log.Log().Info("afterSalesMessage.MessageType RefundType  2")
				log.Log().Info(afterSalesModel.GatherSupplyAfterSaleSN)
				err = orderClass.ReceiptRefund(afterSalesModel.GatherSupplyAfterSaleSN)
				log.Log().Info("yyt returnSn ", zap.Any("info", err))

			}

		}

		if afterSalesMessage.MessageType == mq.AfterSalesClose {
			log.Log().Info("yiyatong 售后取消消息接收成功 ", zap.Any("afterSales", afterSalesModel))

			err = orderClass.CancelRefund(afterSalesModel.GatherSupplyAfterSaleSN)

			if err != nil {

				log.Log().Info("yyt CancelRefund ", zap.Any("info", err))
				return
			}

		}

		if afterSalesMessage.MessageType == mq.AfterSalesUpdate { //修改售后

			log.Log().Info("yiyatong AfterSalesUpdate 售后消息接收成功 ", zap.Any("afterSales", afterSalesModel))

			if afterSalesModel.RefundType == 0 { //退款

				var returnType string
				if orderItem.SendStatus == 0 {
					returnType = "1"
				} else {
					returnType = "4"
				}
				var goodsList []yyt.GoodsList
				specId := strconv.Itoa(int(orderItem.OriginalSkuID))
				num := afterSalesModel.Num
				if afterSalesModel.Num == 0 {
					num = afterSalesModel.OrderItem.Qty
				}

				log.Log().Info("yyt after orderitem", zap.Any("info", orderItem))
				if afterSalesModel.RefundWay == 0 { //部分退款
					//refundAmount := float64(afterSalesModel.Amount / 100)
					d := decimal.New(1, 2) //分除以100得到元

					refundAmount, _ := decimal.NewFromInt(int64(afterSalesModel.Amount)).DivRound(d, 2).Float64()

					goodsList = append(goodsList, yyt.GoodsList{GoodsQuantity: num, GoodsSpecId: specId, RefundAmount: refundAmount})
					returnType = "5"
					log.Log().Info("yyt 部分退款", zap.Any("info", returnType))

				} else {
					log.Log().Info("yyt 全部退款", zap.Any("info", returnType))

					goodsList = append(goodsList, yyt.GoodsList{GoodsQuantity: num, GoodsSpecId: specId})
				}

				DetailImages, _ := json.Marshal(&afterSalesModel.DetailImages)
				err = orderClass.AfsApply(goodsList, orderItem.GatherSupplySN, returnType, afterSalesModel.RefundReasonName, afterSalesModel.Reason, string(DetailImages), afterSalesModel.ID)
				log.Log().Info(".RefundType == 0 售后消息接收成功", zap.Any("afterSales", err))

				if err != nil {
					err = errors.New("自动同步申请售后失败" + err.Error())
					return
				}
			}

			if afterSalesModel.RefundType == 1 {

				var returnType string

				returnType = "2"

				var goodsList []yyt.GoodsList
				specId := strconv.Itoa(int(orderItem.OriginalSkuID))
				num := afterSalesModel.Num
				if afterSalesModel.Num == 0 {
					num = afterSalesModel.OrderItem.Qty
				}

				log.Log().Info("yyt after orderitem RefundType 1", zap.Any("info", orderItem))
				if afterSalesModel.RefundWay == 0 { //部分退款
					d := decimal.New(1, 2) //分除以100得到元

					refundAmount, _ := decimal.NewFromInt(int64(afterSalesModel.Amount)).DivRound(d, 2).Float64()
					goodsList = append(goodsList, yyt.GoodsList{GoodsQuantity: num, GoodsSpecId: specId, RefundAmount: refundAmount})
					returnType = "5"
					log.Log().Info("yyt 部分退款1", zap.Any("info", returnType))

				} else {
					log.Log().Info("yyt 全部退款1", zap.Any("info", returnType))

					goodsList = append(goodsList, yyt.GoodsList{GoodsQuantity: num, GoodsSpecId: specId})
				}

				DetailImages, _ := json.Marshal(&afterSalesModel.DetailImages)

				err = orderClass.AfsApply(goodsList, orderItem.GatherSupplySN, returnType, afterSalesModel.RefundReasonName, afterSalesModel.Reason, string(DetailImages), afterSalesModel.ID)
				log.Log().Info("RefundType == 1 售后消息接收成功", zap.Any("afterSales", err))

				if err != nil {
					err = errors.New("自动同步申请售后失败" + err.Error())
					return
				}

			}

			if afterSalesModel.RefundType == 2 {

				var returnType string

				returnType = "3"

				var goodsList []yyt.GoodsList
				specId := strconv.Itoa(int(orderItem.OriginalSkuID))
				num := afterSalesModel.Num
				if afterSalesModel.Num == 0 {
					num = afterSalesModel.OrderItem.Qty
				}
				goodsList = append(goodsList, yyt.GoodsList{GoodsQuantity: num, GoodsSpecId: specId})
				DetailImages, _ := json.Marshal(&afterSalesModel.DetailImages)

				err = orderClass.AfsApply(goodsList, orderItem.GatherSupplySN, returnType, afterSalesModel.RefundReasonName, afterSalesModel.Reason, string(DetailImages), afterSalesModel.ID)
				log.Log().Info("RefundType == 2 换货售后消息接收成功", zap.Any("afterSales", err))

				if err != nil {
					err = errors.New("自动同步申请售后失败" + err.Error())
					return
				}

			}

			return
		}

		if afterSalesMessage.MessageType == mq.AfterSalesCreate { //创建售后

			log.Log().Info("yiyatong 售后消息接收成功 ", zap.Any("afterSales", afterSalesModel))

			if afterSalesModel.RefundType == 0 { //退款

				var returnType string
				if orderItem.SendStatus == 0 {
					returnType = "1"
				} else {
					returnType = "4"
				}
				var goodsList []yyt.GoodsList
				specId := strconv.Itoa(int(orderItem.OriginalSkuID))
				num := afterSalesModel.Num
				if afterSalesModel.Num == 0 {
					num = afterSalesModel.OrderItem.Qty
				}

				log.Log().Info("yyt after orderitem", zap.Any("info", orderItem))
				if afterSalesModel.RefundWay == 0 { //部分退款
					//refundAmount := float64(afterSalesModel.Amount / 100)
					d := decimal.New(1, 2) //分除以100得到元

					refundAmount, _ := decimal.NewFromInt(int64(afterSalesModel.Amount)).DivRound(d, 2).Float64()

					goodsList = append(goodsList, yyt.GoodsList{GoodsQuantity: num, GoodsSpecId: specId, RefundAmount: refundAmount})
					returnType = "5"
					log.Log().Info("yyt 部分退款", zap.Any("info", returnType))

				} else {
					log.Log().Info("yyt 全部退款", zap.Any("info", returnType))

					goodsList = append(goodsList, yyt.GoodsList{GoodsQuantity: num, GoodsSpecId: specId})
				}

				DetailImages, _ := json.Marshal(&afterSalesModel.DetailImages)
				err = orderClass.AfsApply(goodsList, orderItem.GatherSupplySN, returnType, afterSalesModel.RefundReasonName, afterSalesModel.Reason, string(DetailImages), afterSalesModel.ID)
				log.Log().Info(".RefundType == 0 售后消息接收成功", zap.Any("afterSales", err))

				if err != nil {
					err = errors.New("自动同步申请售后失败" + err.Error())
					return
				}
			}

			if afterSalesModel.RefundType == 1 {

				var returnType string

				returnType = "2"

				var goodsList []yyt.GoodsList
				specId := strconv.Itoa(int(orderItem.OriginalSkuID))
				num := afterSalesModel.Num
				if afterSalesModel.Num == 0 {
					num = afterSalesModel.OrderItem.Qty
				}

				log.Log().Info("yyt after orderitem RefundType 1", zap.Any("info", orderItem))
				if afterSalesModel.RefundWay == 0 { //部分退款
					d := decimal.New(1, 2) //分除以100得到元

					refundAmount, _ := decimal.NewFromInt(int64(afterSalesModel.Amount)).DivRound(d, 2).Float64()
					goodsList = append(goodsList, yyt.GoodsList{GoodsQuantity: num, GoodsSpecId: specId, RefundAmount: refundAmount})
					returnType = "5"
					log.Log().Info("yyt 部分退款1", zap.Any("info", returnType))

				} else {
					log.Log().Info("yyt 全部退款1", zap.Any("info", returnType))

					goodsList = append(goodsList, yyt.GoodsList{GoodsQuantity: num, GoodsSpecId: specId})
				}

				DetailImages, _ := json.Marshal(&afterSalesModel.DetailImages)

				err = orderClass.AfsApply(goodsList, orderItem.GatherSupplySN, returnType, afterSalesModel.RefundReasonName, afterSalesModel.Reason, string(DetailImages), afterSalesModel.ID)
				log.Log().Info("RefundType == 1 售后消息接收成功", zap.Any("afterSales", err))

				if err != nil {
					err = errors.New("自动同步申请售后失败" + err.Error())
					return
				}

			}

			if afterSalesModel.RefundType == 2 {

				var returnType string

				returnType = "3"

				var goodsList []yyt.GoodsList
				specId := strconv.Itoa(int(orderItem.OriginalSkuID))
				num := afterSalesModel.Num
				if afterSalesModel.Num == 0 {
					num = afterSalesModel.OrderItem.Qty
				}
				goodsList = append(goodsList, yyt.GoodsList{GoodsQuantity: num, GoodsSpecId: specId})
				DetailImages, _ := json.Marshal(&afterSalesModel.DetailImages)

				err = orderClass.AfsApply(goodsList, orderItem.GatherSupplySN, returnType, afterSalesModel.RefundReasonName, afterSalesModel.Reason, string(DetailImages), afterSalesModel.ID)
				log.Log().Info("RefundType == 2 换货售后消息接收成功", zap.Any("afterSales", err))

				if err != nil {
					err = errors.New("自动同步申请售后失败" + err.Error())
					return
				}

			}

			return
		}

		if afterSalesMessage.MessageType == mq.AfterSalesUserSend {
			if afterSalesModel.RefundType == model.Barter || afterSalesModel.RefundType == model.Return {

				reqData := make(map[string]string)

				reqData["deliverySn"] = afterSalesModel.ReturnOrderExpress.ExpressNo
				reqData["deliveryCorpName"] = afterSalesModel.ReturnOrderExpress.CompanyName
				reqData["returnSn"] = afterSalesModel.GatherSupplyAfterSaleSN

				err = orderClass.AfsApplyRefund(reqData)
				if err != nil {
					return
				}

			}
		}

	} else if key == "youxuan" {
		var orderClass = &youxuan.Youxuan{}
		err = orderClass.InitSetting(product.GatherSupplyID)
		if afterSalesMessage.MessageType == mq.AfterSalesCreate || afterSalesMessage.MessageType == mq.AfterSalesUpdate {

			var res *stbz.APIResult
			err, res = orderClass.AfterSalesCreate(request.AfterSales{
				Id:                   afterSalesModel.ID,
				OrderID:              afterSalesModel.OrderID,
				ThirdOrderSN:         strconv.Itoa(int(afterSalesModel.Order.OrderSN)),
				OrderItemID:          afterSalesModel.OrderItemID,
				Amount:               afterSalesModel.Amount,
				TechnicalServicesFee: afterSalesModel.TechnicalServicesFee,
				Freight:              afterSalesModel.Freight,
				ReasonType:           afterSalesModel.ReasonType,
				Reason:               afterSalesModel.Reason,
				Description:          afterSalesModel.Description,
				IsReceived:           afterSalesModel.IsReceived,
				DetailImages:         afterSalesModel.DetailImages,
				RefundType:           afterSalesModel.RefundType,
				BarterSkuID:          afterSalesModel.BarterSkuID,
				BarterNum:            afterSalesModel.BarterNum,
				BarterSkuTitle:       afterSalesModel.BarterSkuTitle,
				ImageUrl:             afterSalesModel.ImageUrl,
				RefundWay:            afterSalesModel.RefundWay,
				Num:                  afterSalesModel.Num,
			})
			if err != nil {
				err = errors.New("自动同步申请售后失败" + err.Error())
				return
			}
			if res.Code != 0 {
				err = errors.New("自动同步申请售后失败" + res.Msg)
				return
			}
			return
		}
		if afterSalesMessage.MessageType == mq.AfterSalesUserSend {
			if afterSalesModel.RefundType == model.Barter || afterSalesModel.RefundType == model.Return {
				//var shopAddress  model.ShopAddress
				//
				err = orderClass.AfterSalesSend(request.SendRequest{
					AfterSalesID:      afterSalesModel.ID,
					CompanyName:       afterSalesModel.ReturnOrderExpress.CompanyName,
					CompanyCode:       afterSalesModel.ReturnOrderExpress.CompanyCode,
					ExpressNo:         afterSalesModel.ReturnOrderExpress.ExpressNo,
					ShippingAddressID: afterSalesModel.ShopAddress.ThirdShopAddressId,
				})
				if err != nil {
					err = errors.New("自动同步售后用户发货失败" + err.Error())
					return
				}

			}
		}
		if afterSalesMessage.MessageType == mq.AfterSalesClose {
			err = orderClass.AfterSalesUserClose(request.AfterSales{
				Id: afterSalesModel.ID,
			})
			if err != nil {
				err = errors.New("自动同步售后用户关闭失败" + err.Error())
				return
			}
		}

		if afterSalesMessage.MessageType == mq.AfterSalesRejectAudit {
			err = orderClass.AfterSalesUserClose(request.AfterSales{
				Id: afterSalesModel.ID,
			})
			if err != nil {
				err = errors.New("自动同步中台驳回失败" + err.Error())
				return
			}
		}
	} else if key == "wdt" {
		var orderClass = &order6.Wdt{}
		err = orderClass.InitSetting(product.GatherSupplyID)
		if afterSalesMessage.MessageType == mq.AfterSalesCreate || afterSalesMessage.MessageType == mq.AfterSalesUpdate {

			var res *stbz.APIResult
			err, res = orderClass.AfterSalesCreate(request.AfterSales{
				Id:                   afterSalesModel.ID,
				OrderID:              afterSalesModel.OrderID,
				ThirdOrderSN:         strconv.Itoa(int(afterSalesModel.Order.OrderSN)),
				OrderItemID:          afterSalesModel.OrderItemID,
				Amount:               afterSalesModel.Amount,
				TechnicalServicesFee: afterSalesModel.TechnicalServicesFee,
				Freight:              afterSalesModel.Freight,
				ReasonType:           afterSalesModel.ReasonType,
				Reason:               afterSalesModel.Reason,
				Description:          afterSalesModel.Description,
				IsReceived:           afterSalesModel.IsReceived,
				DetailImages:         afterSalesModel.DetailImages,
				RefundType:           afterSalesModel.RefundType,
				BarterSkuID:          afterSalesModel.BarterSkuID,
				BarterNum:            afterSalesModel.BarterNum,
				BarterSkuTitle:       afterSalesModel.BarterSkuTitle,
				ImageUrl:             afterSalesModel.ImageUrl,
				RefundWay:            afterSalesModel.RefundWay,
				Num:                  afterSalesModel.Num,
			})
			if err != nil {
				err = errors.New("自动同步申请售后失败" + err.Error())
				return
			}
			if res.Code != 0 {
				err = errors.New("自动同步申请售后失败" + res.Msg)
				return
			}
			return
		}
		if afterSalesMessage.MessageType == mq.AfterSalesUserSend {
			if afterSalesModel.RefundType == model.Barter || afterSalesModel.RefundType == model.Return {
				//var shopAddress  model.ShopAddress
				//
				err = orderClass.AfterSalesSend(request.SendRequest{
					AfterSalesID:      afterSalesModel.ID,
					CompanyName:       afterSalesModel.ReturnOrderExpress.CompanyName,
					CompanyCode:       afterSalesModel.ReturnOrderExpress.CompanyCode,
					ExpressNo:         afterSalesModel.ReturnOrderExpress.ExpressNo,
					ShippingAddressID: afterSalesModel.ShopAddress.ThirdShopAddressId,
				})
				if err != nil {
					err = errors.New("自动同步售后用户发货失败" + err.Error())
					return
				}

			}
		}
		if afterSalesMessage.MessageType == mq.AfterSalesClose {
			err = orderClass.AfterSalesUserClose(request.AfterSales{
				Id: afterSalesModel.ID,
			})
			if err != nil {
				err = errors.New("自动同步售后用户关闭失败" + err.Error())
				return
			}
		}

		if afterSalesMessage.MessageType == mq.AfterSalesRejectAudit {
			err = orderClass.AfterSalesUserClose(request.AfterSales{
				Id: afterSalesModel.ID,
			})
			if err != nil {
				err = errors.New("自动同步中台驳回失败" + err.Error())
				return
			}
		}
	} else if key == "shama" {
		var orderClass = &order4.Shama{}
		err = orderClass.InitSetting(product.GatherSupplyID)
		if afterSalesMessage.MessageType == mq.AfterSalesCreate || afterSalesMessage.MessageType == mq.AfterSalesUpdate {

			err, _ = orderClass.AfterSalesCreate(request.AfterSales{
				Id:                   afterSalesModel.ID,
				OrderID:              afterSalesModel.OrderID,
				ThirdOrderSN:         strconv.Itoa(int(afterSalesModel.Order.OrderSN)),
				OrderItemID:          afterSalesModel.OrderItemID,
				Amount:               afterSalesModel.Amount,
				TechnicalServicesFee: afterSalesModel.TechnicalServicesFee,
				Freight:              afterSalesModel.Freight,
				ReasonType:           afterSalesModel.ReasonType,
				Reason:               afterSalesModel.Reason,
				Description:          afterSalesModel.Description,
				IsReceived:           afterSalesModel.IsReceived,
				DetailImages:         afterSalesModel.DetailImages,
				RefundType:           afterSalesModel.RefundType,
				BarterSkuID:          afterSalesModel.BarterSkuID,
				BarterNum:            afterSalesModel.BarterNum,
				BarterSkuTitle:       afterSalesModel.BarterSkuTitle,
				ImageUrl:             afterSalesModel.ImageUrl,
				RefundWay:            afterSalesModel.RefundWay,
				Num:                  afterSalesModel.Num,
			})
			if err != nil {
				err = errors.New("自动同步申请售后失败" + err.Error())
				return
			}
			return
		}
		if afterSalesMessage.MessageType == mq.AfterSalesUserSend {
			if afterSalesModel.RefundType == model.Barter || afterSalesModel.RefundType == model.Return {
				//var shopAddress  model.ShopAddress
				//
				err = orderClass.AfterSalesSend(request.SendRequest{
					AfterSalesID:      afterSalesModel.ID,
					CompanyName:       afterSalesModel.ReturnOrderExpress.CompanyName,
					CompanyCode:       afterSalesModel.ReturnOrderExpress.CompanyCode,
					ExpressNo:         afterSalesModel.ReturnOrderExpress.ExpressNo,
					ShippingAddressID: afterSalesModel.ShopAddress.ThirdShopAddressId,
				})
				if err != nil {
					err = errors.New("自动同步售后用户发货失败" + err.Error())
					return
				}

			}
		}
		if afterSalesMessage.MessageType == mq.AfterSalesClose {
			err = orderClass.AfterSalesUserClose(request.AfterSales{
				Id: afterSalesModel.ID,
			})
			if err != nil {
				err = errors.New("自动同步售后用户关闭失败" + err.Error())
				return
			}
		}
	} else if key == "dwd" {
		var orderClass = &order5.Dwd{}
		err = orderClass.InitSetting(product.GatherSupplyID)
		if afterSalesMessage.MessageType == mq.AfterSalesCreate || afterSalesMessage.MessageType == mq.AfterSalesUpdate {

			err, _ = orderClass.AfterSalesCreate(request.AfterSales{
				Id:                   afterSalesModel.ID,
				OrderID:              afterSalesModel.OrderID,
				ThirdOrderSN:         strconv.Itoa(int(afterSalesModel.Order.OrderSN)),
				OrderItemID:          afterSalesModel.OrderItemID,
				Amount:               afterSalesModel.Amount,
				TechnicalServicesFee: afterSalesModel.TechnicalServicesFee,
				Freight:              afterSalesModel.Freight,
				ReasonType:           afterSalesModel.ReasonType,
				Reason:               afterSalesModel.Reason,
				Description:          afterSalesModel.Description,
				IsReceived:           afterSalesModel.IsReceived,
				DetailImages:         afterSalesModel.DetailImages,
				RefundType:           afterSalesModel.RefundType,
				BarterSkuID:          afterSalesModel.BarterSkuID,
				BarterNum:            afterSalesModel.BarterNum,
				BarterSkuTitle:       afterSalesModel.BarterSkuTitle,
				ImageUrl:             afterSalesModel.ImageUrl,
				RefundWay:            afterSalesModel.RefundWay,
				Num:                  afterSalesModel.Num,
			})
			if err != nil {
				err = errors.New("自动同步申请售后失败" + err.Error())
				return
			}
			return
		}
		//if afterSalesMessage.MessageType == mq.AfterSalesUserSend {
		//	if afterSalesModel.RefundType == model.Barter || afterSalesModel.RefundType == model.Return {
		//		//var shopAddress  model.ShopAddress
		//		//
		//		err = orderClass.AfterSalesSend(request.SendRequest{
		//			AfterSalesID:      afterSalesModel.ID,
		//			CompanyName:       afterSalesModel.ReturnOrderExpress.CompanyName,
		//			CompanyCode:       afterSalesModel.ReturnOrderExpress.CompanyCode,
		//			ExpressNo:         afterSalesModel.ReturnOrderExpress.ExpressNo,
		//			ShippingAddressID: afterSalesModel.ShopAddress.ThirdShopAddressId,
		//		})
		//		if err != nil {
		//			err = errors.New("自动同步售后用户发货失败" + err.Error())
		//			return
		//		}
		//
		//	}
		//}
		//if afterSalesMessage.MessageType == mq.AfterSalesClose {
		//	err = orderClass.AfterSalesUserClose(request.AfterSales{
		//		Id: afterSalesModel.ID,
		//	})
		//	if err != nil {
		//		err = errors.New("自动同步售后用户关闭失败" + err.Error())
		//		return
		//	}
		//}

	} else {
		log.Log().Info("售后处理:不是中台供应链不处理", zap.Any("err", err))
	}
	return
}
