package listener

import (
	"encoding/json"
	"gather-supply/service"
	"go.uber.org/zap"
	"order/mq-reconfirm"
	"yz-go/component/log"
)

func PushCustomerReconfirmHandles() {
	mq_reconfirm.PushHandles("supplyOrderPaidReconfirm", func(order mq_reconfirm.OrderMessage) (err error) {
		log.Log().Info("供应链重新下单", zap.Any("zap", order))

		if order.MessageType == mq_reconfirm.Paid {
			var orderJson []byte
			orderJson, err = json.Marshal(order)
			log.Log().Info("监听供应链订单支付：" + string(orderJson))
			err = service.CreateSupplyOrder(order.OrderID)
			if err != nil {
				log.Log().Info("供应链下单失败：")
				log.Log().Info("供应链监听order订单支付：", zap.Any("err", err.<PERSON>rror()))
				return nil

			}
		}

		return nil
	})
}
