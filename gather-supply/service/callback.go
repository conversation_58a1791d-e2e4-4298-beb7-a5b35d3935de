package service

import (
	"encoding/json"
	"fmt"
	goodsClass "gather-supply/component/goods"
	order2 "gather-supply/component/order"
	pmodel "product/model"
	"product/mq"
	"product/service"
	"public-supply/callback"
	"public-supply/common"
	callback2 "public-supply/model"
	SelfSupply "self-supply/component/goods"
	"self-supply/component/order"
	"yz-go/request"
	"yz-go/source"

	"go.uber.org/zap"
	"strings"
	"yz-go/component/log"
)

var CallBackRequest *callback2.SelfCallBackType
var SelfCallBackRequest *callback2.SelfCallBackType

func OrderCallBack(request callback.CallBackType) (err error) {

	log.Log().Info("OrderCallBack request", zap.Any("request", request))

	var GoodsData callback.GoodsCallBack
	var OrderData callback.OrderCallBack
	if err != nil {
		log.Log().Error("解析供应链设置失败", zap.Any("info", err.Error()))
		return
	}
	var data []byte
	if find := strings.Contains(request.Type, "order"); find {
		data, err = json.Marshal(request)
		err = json.Unmarshal(data, &OrderData)
		OrderData.MsgID = request.MsgID
		if err != nil {
			log.Log().Info("订单回调信息解析错误", zap.Any("info", err))
			return
		}

	}
	if find := strings.Contains(request.Type, "goods"); find {

		data, err = json.Marshal(request)
		fmt.Println(string(data))
		if err != nil {
			log.Log().Info("商品数据序列化失败", zap.Any("info", err))
			return
		}
		err = json.Unmarshal(data, &GoodsData)
		if err != nil {
			log.Log().Info("商品数据反序列化失败", zap.Any("info", err))
			return
		}

		var newGoodsData []int
		for _, item := range GoodsData.Data.GoodsIds {
			if item > 0 {
				newGoodsData = append(newGoodsData, item)
			}
		}
		GoodsData.Data.GoodsIds = newGoodsData
		GoodsData.MsgID = request.MsgID
		log.Log().Info("商品回调数据", zap.Any("info", GoodsData.Data.GoodsIds))
	}

	var goodsModel goodsClass.Goods
	var orderModel order2.Order

	if find := strings.Contains(request.MsgID, "self"); find {

		GoodsData.SupplierSource = append(GoodsData.SupplierSource, 101) //中台的
		GoodsData.SupplierType = 2
		//中台的
		orderModel = order2.NewOrder("self")
		goodsModel = goodsClass.NewGoods("self")

		//修改物流 暂时其他供应链没有修改物流，中台接收修改物流消息处理 暂时单独写

		if request.Type == string(callback.ORDERUPDATESEND) {
			var orderSelf order.Self
			_ = orderSelf.UpdateOrderDelivery(OrderData)
		}
	} else {
		GoodsData.SupplierType = 2 //stbz

		GoodsData.SupplierSource = append(GoodsData.SupplierSource, common.STBZ_JD)
		GoodsData.SupplierSource = append(GoodsData.SupplierSource, common.STBZ_AL)
		GoodsData.SupplierSource = append(GoodsData.SupplierSource, common.STBZ_HNYC)
		GoodsData.SupplierSource = append(GoodsData.SupplierSource, common.STBZ_SN)
		GoodsData.SupplierSource = append(GoodsData.SupplierSource, common.STBZ_TM)
		GoodsData.SupplierSource = append(GoodsData.SupplierSource, common.STBZ_YC)
		GoodsData.SupplierSource = append(GoodsData.SupplierSource, common.STBZ_TMYC)
		GoodsData.SupplierSource = append(GoodsData.SupplierSource, common.STBZ_TB)
		GoodsData.SupplierSource = append(GoodsData.SupplierSource, common.STBZ_HDYC)
		GoodsData.SupplierSource = append(GoodsData.SupplierSource, common.STBZ_KJYC)
		GoodsData.SupplierSource = append(GoodsData.SupplierSource, common.STBZ_TMJX)

		orderModel = order2.NewOrder("stbz")
		goodsModel = goodsClass.NewGoods("stbz")

	}
	log.Log().Info("处理消息self 3", zap.Any("Type", request.Type))
	if strings.Contains(request.Type, "CakeGoods") {
		log.Log().Info("CakeGoods alert new goods")

		orderModel = order2.NewOrder("selfCake")
		goodsModel = goodsClass.NewGoods("selfCake")

	}
	switch request.Type {
	case string(callback.ORDER_DELIVERY):

		err = orderModel.OrderDelivery(OrderData) //发货回调逻辑
		break
	case string(callback.ORDER_SENDING):

		err = orderModel.OrderDelivery(OrderData) //发货回调逻辑
		break
	case string(callback.ORDER_DELIVERED):
		err = OrderDelivered()
		break
	case string(callback.ORDER_SUCCESS):
		//err = OrderSuccess()
		break
	case string(callback.ORDER_CANCEL):
		err = OrderCancel()
		break
	case string(callback.GOODS_UNDER):

		err = GoodsUnder(GoodsData)
		break
	case string(callback.GOODS_ONSALE):
		err = GoodsOnSale(GoodsData)
		break
	case string(callback.GOODS_DELETE):
		err = GoodsDelete(GoodsData)
		break
	case string(callback.GOODS_PRICE_ALERT):
		if find := strings.Contains(request.MsgID, "self"); find {
			//var values []string
			//for _, v := range GoodsData.Data.GoodsIds {
			//	values = append(values, strconv.Itoa(v))
			//}
			//
			//err = source.Redis().LPush(context.Background(), "notifyProduct", values).Err()
			err = goodsModel.GoodsPriceAlert(callback.GoodsCallBack{
				Type:  string(callback.GOODS_PRICE_ALERT),
				MsgID: "selfdasdadsa",
				Data: callback.GoodsIds{
					GoodsIds: GoodsData.Data.GoodsIds,
				},
			})
			if err != nil {
				log.Log().Info("商品更新失败", zap.Any("data", GoodsData))
				return nil
			}
		} else {
			err = goodsModel.GoodsPriceAlert(GoodsData)
		}

		break

	case string(callback.GOODS_ALERT):
		if find := strings.Contains(request.MsgID, "self"); find {
			//var values []string
			//for _, v := range GoodsData.Data.GoodsIds {
			//	values = append(values, strconv.Itoa(v))
			//}
			//err = source.Redis().LPush(context.Background(), "notifyProduct", values).Err()
			err = goodsModel.GoodsPriceAlert(callback.GoodsCallBack{
				Type:  string(callback.GOODS_ALERT),
				MsgID: "selfdasdadsa",
				Data: callback.GoodsIds{
					GoodsIds: GoodsData.Data.GoodsIds,
				},
			})
			if err != nil {
				log.Log().Info("商品更新失败", zap.Any("data", GoodsData))
				return nil
			}
		} else {
			err = goodsModel.GoodsPriceAlert(GoodsData)
		}
		break
	case "CakeGoods.goods.alter":

		log.Log().Info("case CakeGoods.goods.alter", zap.Any("info", GoodsData))
		err = goodsModel.GoodsPriceAlert(GoodsData)

		break
	case string(callback.GOODS_STOCK):
		if find := strings.Contains(request.MsgID, "self"); find {
			var selfModel = SelfSupply.Self{}
			err = selfModel.GoodsStockAlert(GoodsData)
			if err != nil {
				log.Log().Info("商品库存回调处理失败", zap.Any("info", GoodsData.Data.GoodsIds), zap.Any("err", err.Error()))
			}
		} else {
			err = goodsModel.GoodsPriceAlert(GoodsData)
		}
		break
	case string(callback.AFTERSALE_AGREE):
		err = AfterSaleAgree()
		break
	case string(callback.AFTERSALE_REFUSE):
		err = AfterSaleRefuse()
		break
	default:
		fmt.Println("未知回调数据类型", request)
	}
	return nil
}

func AfterSaleAgree() (err error) {

	return
}
func AfterSaleRefuse() (err error) {

	return
}

func GoodsAlert(GoodsData callback.GoodsCallBack) (err error) {
	return
}

// 收货
func OrderDelivered() (err error) {

	//Message.MessageType = Receive
	//err = tradeOrder.Received(order.ID)
	//if err != nil {
	//	log.Log().Error("OrderDelivered收货错误", zap.Any("info", err.Error()))
	//
	//}
	//err = listener.PublishExchange(string(callback.OrderStatusDeliveredRouting), Message)

	return
}

// 完成
func OrderSuccess() (err error) {
	//Message.MessageType = Completed
	//err = listener.PublishExchange(string(callback.OrderStatusSuccessRouting), Message)
	//log.Log().Error("OrderSuccess", zap.Any("info", "供应链完成收到消息"))
	//fmt.Println("OrderSuccess", CallBackRequest, CallBackRequest.Type)
	return
}

// 取消
func OrderCancel() (err error) {
	//Message.MessageType = Closed
	//err = tradeOrder.Close(order.ID)
	//if err != nil {
	//	log.Log().Info("OrderCancel取消订单错误", zap.Any("订单id", order.ID), zap.Any("info", err.Error()))
	//}
	//err = listener.PublishExchange(string(callback.OrderStatusCancelRouting), Message)

	return
}

// 商品上架
func GoodsOnSale(GoodsData callback.GoodsCallBack) (err error) {
	log.Log().Info("GoodsOnSale", zap.Any("info", "供应链商品上架收到消息"), zap.Any("info", GoodsData))
	var product *pmodel.Product
	if len(GoodsData.Data.GoodsIds) > 0 {

		var supplyTypeWhere interface{}

		db := source.DB().Model(&product)
		if GoodsData.SupplierType == 1 {
			supplyTypeWhere = source.DB().Where("gather_supply_id =?", 0)
		} else if GoodsData.SupplierType == 2 {
			supplyTypeWhere = source.DB().Where("gather_supply_id >?", 0)
		}

		db.Where(supplyTypeWhere)
		db.Where("status_lock=0")
		db.Where("source in (?)", GoodsData.SupplierSource)

		err = db.Model(&product).Where("source_goods_id in (?)", GoodsData.Data.GoodsIds).Update("is_display", 1).Error
		if err != nil {
			log.Log().Info("GoodsOnSale 上架错误", zap.Any("info", err.Error()))
			return
		}
		var products []pmodel.Product
		dbProduct := source.DB()
		dbProduct.Where(supplyTypeWhere)
		dbProduct.Where("status_lock=0")
		dbProduct.Where("source in (?)", GoodsData.SupplierSource)
		err = dbProduct.Where("source_goods_id in (?)", GoodsData.Data.GoodsIds).Find(&products).Error
		if err != nil {
			return
		}
		for _, v := range products {
			err = mq.PublishMessage(v.ID, mq.OnSale, 0)
		}
		err = callback.DeleteGoodsMsg(GoodsData.MsgID)
		if err != nil {
			log.Log().Info("上架后删除消息错误", zap.Any("info", err.Error()))
			return
		}
	}

	return

}

// 商品下架
func GoodsUnder(GoodsData callback.GoodsCallBack) (err error) {
	log.Log().Info("GoodsUnder", zap.Any("info", "供应链商品下架收到消息"), zap.Any("info", GoodsData))

	var product *pmodel.Product
	if len(GoodsData.Data.GoodsIds) > 0 {
		var supplyTypeWhere interface{}

		db := source.DB().Model(&product)
		if GoodsData.SupplierType == 1 {
			supplyTypeWhere = source.DB().Where("gather_supply_id =?", 0)
		} else if GoodsData.SupplierType == 2 {
			supplyTypeWhere = source.DB().Where("gather_supply_id >?", 0)
		}

		db.Where(supplyTypeWhere)
		db.Where("status_lock=0")
		db.Where("source in (?)", GoodsData.SupplierSource)
		err = db.Where("source_goods_id in (?)", GoodsData.Data.GoodsIds).Update("is_display", 0).Error
		if err != nil {
			log.Log().Info("下架错误", zap.Any("info", err.Error()))
			return
		}
		var products []pmodel.Product
		dbProduct := source.DB().Model(&products)
		dbProduct.Where(supplyTypeWhere)
		dbProduct.Where("status_lock=0")
		dbProduct.Where("source in (?)", GoodsData.SupplierSource)
		err = dbProduct.Where("source_goods_id in (?)", GoodsData.Data.GoodsIds).Find(&products).Error
		if err != nil {
			return
		}
		for _, v := range products {
			err = mq.PublishMessage(v.ID, mq.Undercarriage, 0)
		}
		err = callback.DeleteGoodsMsg(GoodsData.MsgID)
		if err != nil {
			log.Log().Info("下架删除消息错误", zap.Any("info", err.Error()))
			return
		}

	}

	return
}

func GoodsDelete(GoodsData callback.GoodsCallBack) (err error) {
	log.Log().Info("GoodsUnder", zap.Any("info", "供应链商品删除收到消息"), zap.Any("info", GoodsData))
	var ids []uint
	err = source.DB().Model(&pmodel.Product{}).Where("source_goods_id in ?", GoodsData.Data.GoodsIds).Pluck("id", &ids).Error
	if err != nil {
		return
	}

	err = service.DeleteProductByIds(request.IdsReq{
		Ids: ids,
	}, 0, "监听程序")
	return
}
