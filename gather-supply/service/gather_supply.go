package service

import (
	model5 "after-sales/model"
	service2 "after-sales/service"
	appm "application/model"
	request2 "application/request"
	categoryModel "category/model"
	"context"
	"errors"
	"fmt"
	"gather-supply/component/goods"
	gatherSupplyRequest "gather-supply/request"
	"gin-vue-admin/cmd/gva"
	"github.com/chenhg5/collection"
	"go.uber.org/zap"
	"gorm.io/gorm"
	model2 "order/model"
	model3 "product/model"
	"product/service"
	"public-supply/callback"
	"public-supply/common"
	"public-supply/model"
	"public-supply/request"
	"public-supply/setting"
	mq "public-supply/work_mq"
	"strconv"
	"strings"
	"weipinshang/cron"
	"yz-go/cache"
	"yz-go/component/log"
	"yz-go/config"
	model4 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

func ValidateConfig(params gatherSupplyRequest.ValidateConfigParams) error {
	err, key := GetGatherSupplyCategoryKey(params.GatherSupplyID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("供应链不存在")
		}
		return err
	}
	// 验证配置
	gatherClass := goods.NewGoods(key)
	if err = gatherClass.ValidateConfig(params); err != nil {
		return err
	}

	return nil
}

func GetPluginGatherSupplyList(info request.GatherSupplySearch) (err error, gatherSupplyList []model.GatherSupply, total int64) {
	if info.PageSize == 0 {
		info.PageSize = 50
	}
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.GatherSupply{})
	fmt.Println("数据：", info.Name)
	if info.Name != "" {
		db = db.Where("`name` LIKE ?", "%"+info.Name+"%")
	}
	if info.CategoryID > 0 {
		db = db.Where("category_id=?", info.CategoryID)
	}
	//var hiddenIds = []uint{12, 98, 110, 111, 126, 120, 8, 9, 11, 127}
	//db = db.Where("category_id not in ?", hiddenIds)

	// 如果有条件搜索 下方会自动创建搜索语句
	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Order("id desc").Find(&gatherSupplyList).Error
	for k, v := range gatherSupplyList {
		gatherSupplyList[k].Category = GetGatherSupplyCategory(v.CategoryID)
		var goodsCounts, orderCounts, amountCounts int64

		err = source.DB().Model(model3.Product{}).Where("gather_supply_id = ?", v.ID).Count(&goodsCounts).Error
		err = source.DB().Model(model2.Order{}).Where("gather_supply_id = ?", v.ID).Count(&orderCounts).Error
		err = source.DB().Model(model2.Order{}).Where("gather_supply_id = ?", v.ID).Pluck("COALESCE(SUM(amount), 0) as amount1", &amountCounts).Error
		gatherSupplyList[k].GoodsCount = uint(goodsCounts)
		gatherSupplyList[k].OrderAmount = uint(amountCounts)
		gatherSupplyList[k].OrderCount = uint(orderCounts)
		//if v.CategoryID > 0 {
		//	err, gatherSupplyList[k].SupplyBalance = GetSupplyBalance(v.ID)
		//	if err != nil {
		//		err = nil
		//		continue
		//	}
		//}

	}

	return err, gatherSupplyList, total
}

func GetGatherSupplyList(info request.GatherSupplySearch) (err error, gatherSupplyList []model.GatherSupply, total int64) {
	if info.PageSize == 0 {
		info.PageSize = 50
	}
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.GatherSupply{})
	fmt.Println("数据：", info.Name)
	if info.Name != "" {
		db = db.Where("`name` LIKE ?", "%"+info.Name+"%")
	}
	if info.CategoryID > 0 {
		db = db.Where("category_id=?", info.CategoryID)
	}
	var hiddenIds = []uint{12, 98, 110, 111, 126, 120, 8, 9, 11, 127, 132, 133}
	db = db.Where("category_id not in ?", hiddenIds)

	// 如果有条件搜索 下方会自动创建搜索语句
	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Order("id desc").Find(&gatherSupplyList).Error
	for k, v := range gatherSupplyList {
		gatherSupplyList[k].Category = GetGatherSupplyCategory(v.CategoryID)
		var goodsCounts, orderCounts, amountCounts int64

		err = source.DB().Model(model3.Product{}).Where("gather_supply_id = ?", v.ID).Count(&goodsCounts).Error
		err = source.DB().Model(model2.Order{}).Where("gather_supply_id = ?", v.ID).Count(&orderCounts).Error
		err = source.DB().Model(model2.Order{}).Where("gather_supply_id = ?", v.ID).Pluck("COALESCE(SUM(amount), 0) as amount1", &amountCounts).Error
		gatherSupplyList[k].GoodsCount = uint(goodsCounts)
		gatherSupplyList[k].OrderAmount = uint(amountCounts)
		gatherSupplyList[k].OrderCount = uint(orderCounts)
		//if v.CategoryID > 0 {
		//	err, gatherSupplyList[k].SupplyBalance = GetSupplyBalance(v.ID)
		//	if err != nil {
		//		err = nil
		//		continue
		//	}
		//}

	}

	return err, gatherSupplyList, total
}

func GetSupplyBalance(GatherSupplyID uint) (err error, balance interface{}) {

	if GatherSupplyID <= 0 {
		return
	}
	err, key := GetGatherSupplyCategoryKey(GatherSupplyID)
	var orderClass = goods.NewGoods(key)

	err = orderClass.InitSetting(GatherSupplyID)
	//if err != nil {
	//	return
	//}
	err, balance = orderClass.GetSupplyBalance(GatherSupplyID)
	if err != nil {
		return
	}

	return
}

func GetGatherSupplierOptionList() (err error, list interface{}) {

	// 创建db
	db := source.DB().Model(&model.GatherSupply{})
	var suppliers []model.GatherSupply
	// 如果有条件搜索 下方会自动创建搜索语句
	var fields []string
	fields = append(fields, "name")
	fields = append(fields, "id")
	fields = append(fields, "category_id")
	err = db.Select(fields).Find(&suppliers).Error
	//err = db.Limit(limit).Offset(offset).Find(&suppliers).Error
	return err, suppliers
}

func CreateSupply(requestData model.GatherSupply) (err error) {
	err = source.DB().Save(&requestData).Error
	if err != nil {
		return
	}
	cache.ClearGatherSupply(requestData.ID)
	return
}

func UpdateGatherSupplyName(gatherId uint, name string) error {
	return source.DB().Omit("created_at").Model(&model.GatherSupply{}).Where("id = ?", gatherId).Update("name", name).Error
}

func DeleteSupply(requestData model.GatherSupply) (err error) {

	err = source.DB().Where("id = ?", requestData.ID).Delete(&requestData).Error
	err = source.DB().Where("gather_supply_id = ?", requestData.ID).Delete(&model3.Product{}).Error
	err = source.DB().Where("gather_supply_id = ?", requestData.ID).Delete(&setting.GatherSupplyApplicationLevel{}).Error
	err = source.DB().Where("`key` = ?", "gatherSupply"+strconv.Itoa(int(requestData.ID))).Delete(&model4.SysSetting{}).Error

	return
}

func DeleteImportGoodsRecord(requestData model.GatherSupply) (err error) {

	err = source.DB().Where("id = ?", requestData.ID).Delete(&model.SupplyGoodsImportRecord{}).Error

	return
}

func StatisticalGather() (err error, data map[string]interface{}) {

	datas := make(map[string]interface{})

	var goodsCounts, orderCounts, amountCounts int64

	err = source.DB().Model(model.SupplyGoods{}).Count(&goodsCounts).Error
	err = source.DB().Model(model2.Order{}).Where("gather_supply_id > ?", 0).Count(&orderCounts).Error
	err = source.DB().Model(model2.Order{}).Where("gather_supply_id > ?", 0).Pluck("COALESCE(SUM(amount), 0) as amount1", &amountCounts).Error

	datas["goods_count"] = goodsCounts
	datas["order_count"] = orderCounts
	datas["amount_count"] = amountCounts

	data = datas

	return

}

func searchImportRecordGorm(search request.GatherSupplySearch) (db *gorm.DB) {
	db = source.DB().Model(&model.SupplyGoodsImportRecord{})

	// 默认查询 is_plugin = 0
	db.Where("is_plugin = ?", search.IsPlugin)

	if search.Source != "" {
		db.Where("source = ?", search.Source)
	}

	return
}

func GetImportRecordList(info request.GatherSupplySearch) (err error, data []model.SupplyGoodsImportRecord, total int64) {
	db := searchImportRecordGorm(info)

	if err = db.Count(&total).Error; err != nil {
		return
	}

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	err = db.Preload("SysUser").Omit("data").Limit(limit).Offset(offset).Order("id desc").Find(&data).Error

	return
}

func GetImportRecordErrorList(info request.GatherSupplySearch) (err error, data []model.SupplyGoodsImportRecordErrorsResponse, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	err = source.DB().Model(&model.SupplyGoodsImportRecordErrors{}).Where("batch = ?", info.Batch).Count(&total).Error
	err = source.DB().Where("batch = ?", info.Batch).Limit(limit).Offset(offset).Order("id desc").Find(&data).Error

	return err, data, total
}

type SupplyProduct struct {
	model3.Product
	CategoryOne   categoryModel.Category `gorm:"foreignKey:Category1ID"`
	CategoryTwo   categoryModel.Category `gorm:"foreignKey:Category2ID"`
	CategoryThree categoryModel.Category `gorm:"foreignKey:Category3ID"`
}

// TableName  自定义表名
func (this *SupplyProduct) TableName() string {
	return "products"
}

func GetSupplyGoodsList(info request.GatherSupplySearch) (err error, data []SupplyProduct, total int64) {
	var supplyGoods model.SupplyGoodsImportRecord

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	db := source.DB().Model(SupplyProduct{})

	source.DB().Select("goods_arr").Where("id= ?", info.ID).First(&supplyGoods)
	if len(supplyGoods.GoodsArr) > 0 {
		where := "id in ?"
		//if info.Source == strconv.Itoa(common.WEIPINSHANG_SOURCE) {
		//	where = "FIND_IN_SET(source_goods_id_string, ?)"
		//}
		var goodsIdsString []string
		goodsIdsString = strings.Split(supplyGoods.GoodsArr, ",")
		var goodsIdsUint []int
		for _, goodsIdss := range goodsIdsString {
			var id int
			id, err = strconv.Atoi(goodsIdss)
			if err != nil {
				return
			}
			goodsIdsUint = append(goodsIdsUint, id)
		}
		db.Where(where, goodsIdsUint)

		err = db.Count(&total).Error
		err = db.Limit(limit).Offset(offset).Preload("CategoryOne").Preload("CategoryTwo").Preload("CategoryThree").Order("id desc").Find(&data).Error
	}

	return err, data, total

}

func GetGatherSupplyCategoryList() (list []model.GatherSupplyCategory) {
	listSupply := []model.GatherSupplyCategory{
		{Model: source.Model{ID: common.SUPPLY_STBZ}, Name: "STBZ", Key: "stbz"},
		{Model: source.Model{ID: common.SUPPLY_SELF}, Name: "中台供应链", Key: "self"},
		{Model: source.Model{ID: common.SUPPLY_CROSS}, Name: "跨境供应链", Key: "cross"},
		{Model: source.Model{ID: common.SUPPLY_YZH}, Name: "YZH供应链", Key: "yzh"},
		{Model: source.Model{ID: common.SUPPLY_YZHNEW}, Name: "YZH新供应链", Key: "yzh_new"},
		{Model: source.Model{ID: common.SUPPLY_SZBAO}, Name: "永源供应链", Key: "szbao"},
		{Model: source.Model{ID: common.SUPPLY_DWD}, Name: "dwd供应链", Key: "dwd"},
		{Model: source.Model{ID: common.SUPPLY_LIANLIAN}, Name: "联联供应链", Key: "lianlian", IsHide: 1},
		{Model: source.Model{ID: common.SUPPLY_CAKE}, Name: "蛋糕叔叔", Key: "cake", IsHide: 1},
		{Model: source.Model{ID: common.SUPPLY_HEHE}, Name: "和合", Key: "hehe"},
		{Model: source.Model{ID: common.SUPPLY_CURRICULUM}, Name: "课程", Key: "curriculum", IsHide: 1},
		{Model: source.Model{ID: common.SUPPLY_ZYHX}, Name: "智优惠选", Key: "zyhx"},
		{Model: source.Model{ID: common.SUPPLY_LIJING}, Name: "丽晶供应链", Key: "lijing"},
		{Model: source.Model{ID: common.SUPPLY_ALJX}, Name: "阿里精选", Key: "aljx"},
		{Model: source.Model{ID: common.SUPPLY_JUSHUITAN}, Name: "聚水潭", Key: "jushuitan"},
		{Model: source.Model{ID: common.SUPPLY_YOUXUAN}, Name: "优选", Key: "youxuan"},
		{Model: source.Model{ID: common.SUPPLY_WPS}, Name: "唯品尚", Key: "weipinshang"},
		{Model: source.Model{ID: common.SUPPLY_TIANMA}, Name: "天马", Key: "tianma"},
		{Model: source.Model{ID: common.SUPPLY_YIYATONG}, Name: "怡亚通", Key: "yiyatong"},
		{Model: source.Model{ID: common.SUPPLY_YJF}, Name: "易积分", Key: "yjf"},
		{Model: source.Model{ID: common.SELF_CAKE}, Name: "中台蛋糕", Key: "selfCake"},
		{Model: source.Model{ID: common.SUPPLY_SHAMA}, Name: "沙马", Key: "shama"},
		{Model: source.Model{ID: common.SUPPLY_JD_VOP}, Name: "京东VOP", Key: "jd_vop"},
		{Model: source.Model{ID: common.SUPPLY_MAIGER}, Name: "迈戈供应链", Key: "maiger"},
		{Model: source.Model{ID: common.SUPPLY_GAT}, Name: "关爱通", Key: "guanaitong"},
		{Model: source.Model{ID: common.SUPPLY_KUNSHENG}, Name: "坤昇健行", Key: "kunsheng"},
	}

	for _, v := range listSupply {
		if utils.LocalEnv() != true { //本地环境
			list = append(list, v)
		} else { //用户生产环境
			if collection.Collect(gva.GlobalAuth.Supply).Contains(v.ID) == true {
				list = append(list, v)
			}
		}

	}

	return
}

func GetGatherSupplyCategory(id uint) (data model.GatherSupplyCategory) {
	list := GetGatherSupplyCategoryList()
	// 后台接口不显示
	list = append(list, model.GatherSupplyCategory{Model: source.Model{ID: common.FULU_EQUITY}, Name: "福禄", Key: "fulu"})
	list = append(list, model.GatherSupplyCategory{Model: source.Model{ID: 99}, Name: "必应鸟供应链", Key: "byn"})
	list = append(list, model.GatherSupplyCategory{Model: source.Model{ID: 109}, Name: "联联供应链", Key: "lianlian"})
	list = append(list, model.GatherSupplyCategory{Model: source.Model{ID: common.USER_EQUITY}, Name: "会员权益", Key: "userEquity"})
	list = append(list, model.GatherSupplyCategory{Model: source.Model{ID: common.FULU_USER_EQUITY}, Name: "福禄会员权益", Key: "fuluUserEquity"})
	list = append(list, model.GatherSupplyCategory{Model: source.Model{ID: common.LEASE}, Name: "租赁", Key: "lease"})
	list = append(list, model.GatherSupplyCategory{Model: source.Model{ID: common.DACHANGHANGERP}, Name: "大昌行", Key: "da_chang_erp"})
	list = append(list, model.GatherSupplyCategory{Model: source.Model{ID: common.SUPPLY_GD}, Name: "广电售卡", Key: "guangdian"})
	list = append(list, model.GatherSupplyCategory{Model: source.Model{ID: common.SELF_GD}, Name: "广电中台", Key: "gdself"})

	var datas = make(map[uint]model.GatherSupplyCategory)
	for _, v := range list {
		datas[v.ID] = v
	}
	return datas[id]
}

//	type GatherSupplyApplicationLevel struct {
//		source.Model
//		GatherSupplyID     uint `json:"gather_supply_id"`
//		ApplicationLevelID uint `json:"application_level_id"`
//		SourceID           uint `json:"source_id"`
//	}
func GetSupplySource(id int) (data interface{}) {

	//GatherSupplyApplicationLevel{}
	var gsal []setting.GatherSupplyApplicationLevel

	source.DB().Select("source_id").Where("application_level_id=?", id).Find(&gsal)

	data = gsal

	return

}

func WpsNotifyProduct(req model.WpsNotifyProductData) (err error) {

	var gatherSupply model.GatherSupply
	err = source.DB().Where("category_id = ?", common.WEIPINSHANG_SOURCE).Where("deleted_at is null").Order("id desc").First(&gatherSupply).Error
	if err != nil {
		return
	}

	for _, item := range req.CContent.Goods {

		cron.GoodsUpdate(gatherSupply.ID, item.CFatherGoodsId)
	}

	return
}
func WpsNotifyShelves(req model.WpsNotifyShelvesData) (err error) {

	var gatherSupply model.GatherSupply
	err = source.DB().Where("category_id = ?", common.WEIPINSHANG_SOURCE).Where("deleted_at is null").Order("id desc").First(&gatherSupply).Error
	if err != nil {
		return
	}

	for _, item := range req.CContent {

		cron.GoodsUpdate(gatherSupply.ID, item.CFatherGoodsId)
	}

	return
}

func Refund(id uint) (err error) {
	var returnMsg string
	var afterSales model5.AfterSales
	afterSales.ID = id
	err, returnMsg = service2.Refund(afterSales, 0, 0)
	if err != nil {
		log.Log().Error("wps  service.Refund", zap.Any("", err), zap.Any("", returnMsg))
		return
	}
	return
}

func Return(afterSaleId uint) (err error) {
	//var returnMsg string
	var resAfterSaleAudit model5.AfterSalesAudit
	//resAfterSaleAudit.ShippingAddressID
	err, resAfterSaleAudit = service2.GetAfterSalesAuditByAfterSalesId(afterSaleId)

	//resAfterSaleAudit.ID = afterSaleId
	err = service2.PassAudit(resAfterSaleAudit, 0, 0)
	if err != nil {
		log.Log().Error("wps  PassAudit", zap.Any("", err))
		return
	}
	Refund(afterSaleId)

	return
}

func WpsNotifyAfterSale(req model.WpsNotifyAfterSale) (err error) {

	log.Log().Info("wps上游售后同意退款", zap.Any("", req))

	if req.CContent.Type == 29 { // 上游发来退货地址

		log.Log().Info("wps上游售后同意 退货 发送地址 ", zap.Any("", req))

		var orderItem model2.OrderItem

		err = source.DB().Where("gather_supply_sn=?", req.CContent.McOrderNo).Preload("AfterSales").First(&orderItem).Error
		if err != nil {
			log.Log().Error("查询orderitem 错误", zap.Any("err", req), zap.Any("gather_supply_sn", req.CContent.McOrderNo))
			return
		}
		log.Log().Info("wps 退货退款 售后id-1 ", zap.Any(" ", orderItem.AfterSales.ID))

		var shopAddress model5.ShopAddress
		shopAddress.Address = req.CContent.Address
		shopAddress.Tel = req.CContent.Phone
		shopAddress.Contacts = req.CContent.Name
		err = source.DB().Model(&model5.ShopAddress{}).Create(&shopAddress).Error
		if err != nil {
			log.Log().Info("wps 退货退款 售后地址创建失败", zap.Any("err", err))
			return nil
		}

		var resAfterSaleAudit model5.AfterSalesAudit
		err, resAfterSaleAudit = service2.GetAfterSalesAuditByAfterSalesId(orderItem.AfterSales.ID)
		resAfterSaleAudit.ShippingAddressID = shopAddress.ID
		log.Log().Info("wps 退货退款 售后  ", zap.Any(" ", resAfterSaleAudit))

		err = service2.PassAudit(resAfterSaleAudit, 0, 0)
		if err != nil {
			log.Log().Error("wps  PassAudit", zap.Any("", err))
			return
		}

	}

	if req.CContent.RefuseStatus == "SUCCESS" && req.CContent.RefuseType == "RETURN_MONEY" { //仅退款
		log.Log().Info("wps上游售后同意退款,仅退款")

		var orderItem model2.OrderItem

		err = source.DB().Where("gather_supply_sn=?", req.CContent.McOrderNo).Preload("AfterSales").First(&orderItem).Error
		if err != nil {
			log.Log().Error("查询orderitem 错误", zap.Any("err", req), zap.Any("gather_supply_sn", req.CContent.McOrderNo))
			return
		}
		log.Log().Info("wps 售后id ", zap.Any(" ", orderItem.AfterSales.ID))

		Return(orderItem.AfterSales.ID)
	}
	if req.CContent.RefuseStatus == "SUCCESS" && req.CContent.RefuseType == "RETURN_GOODS" { //退货退款
		log.Log().Info("wps上游售后同意退款,退货退款")

		var orderItem model2.OrderItem

		err = source.DB().Where("gather_supply_sn=?", req.CContent.McOrderNo).Preload("AfterSales").First(&orderItem).Error
		if err != nil {
			log.Log().Error("查询orderitem 错误", zap.Any("err", req), zap.Any("gather_supply_sn", req.CContent.McOrderNo))
			return
		}
		log.Log().Info("wps 退货退款 售后id ", zap.Any(" ", orderItem.AfterSales.ID))

		//Return(orderItem.AfterSales.ID)

		Refund(orderItem.AfterSales.ID)

	}

	if req.CContent.RefuseStatus == "ALLOW" && req.CContent.RefuseType == "RETURN_GOODS" { //退货受理，等待买家退货

		log.Log().Info("wps 退货退款 上游受理 ", zap.Any("RETURN_GOODS  ALLOW", req.CContent))

		if req.CContent.Phone != "" {

			var orderItem model2.OrderItem

			err = source.DB().Where("gather_supply_sn=?", req.CContent.McOrderNo).Preload("AfterSales").First(&orderItem).Error
			if err != nil {
				log.Log().Error("查询orderitem 错误", zap.Any("err", req), zap.Any("gather_supply_sn", req.CContent.McOrderNo))
				return
			}
			log.Log().Info("wps 退货退款 售后id ", zap.Any(" ", orderItem.AfterSales.ID))

			var shopAddress model5.ShopAddress
			shopAddress.Address = req.CContent.Address
			shopAddress.Tel = req.CContent.Phone
			shopAddress.Contacts = req.CContent.Name
			err = source.DB().Model(&model5.ShopAddress{}).FirstOrCreate(&shopAddress).Error
			if err != nil {
				log.Log().Info("shama接到售后审核通过消息,执行错误7", zap.Any("err", err))
				return nil
			}

			var resAfterSaleAudit model5.AfterSalesAudit
			err, resAfterSaleAudit = service2.GetAfterSalesAuditByAfterSalesId(orderItem.AfterSales.ID)
			resAfterSaleAudit.ShippingAddressID = shopAddress.ID
			log.Log().Info("wps 退货退款 售后  ", zap.Any(" ", resAfterSaleAudit))

			err = service2.PassAudit(resAfterSaleAudit, 0, 0)
			if err != nil {
				log.Log().Error("wps  PassAudit", zap.Any("", err))
				return
			}

		}

	}

	return
}

func WpsNotify(req model.WpsNotifyData) {
	var resAfterSaleAudit model5.AfterSalesAudit
	var orderItem model2.OrderItem
	err := source.DB().Where("gather_supply_sn=?", req.CContent.McOrderNo).First(&orderItem).Error
	if err != nil {
		log.Log().Error("WpsNotify orderItem err", zap.Any("err", err))
		return
	}

	var order model2.Order
	err = source.DB().Where("id=?", orderItem.OrderID).First(&order).Error
	if err != nil {
		log.Log().Error("WpsNotify order err", zap.Any("err", err))
		return
	}

	var afterSale model5.AfterSales
	err = source.DB().Where("after_sale_sn=?", order.OrderSN).First(&afterSale).Error
	if err != nil {
		log.Log().Error("WpsNotify afterSale err", zap.Any("err", err))
		return
	}
	resAfterSaleAudit.AfterSalesID = afterSale.ID
	if req.CContent.RefuseStatus == "SUCCESS" {
		resAfterSaleAudit.Status = 1

	}
	if req.CContent.RefuseStatus == "NOTALLOW" {
		resAfterSaleAudit.Status = -1

	}
	err = service2.PassAudit(resAfterSaleAudit, 0, 0)
	if err != nil {
		log.Log().Error("WpsNotify WpsNotify", zap.Any("err", err))
	}

	return

}
func GetGatherSupplyCategoryKey(id uint) (err error, key string) {
	var gatherSupply *model4.GatherSupply
	gatherSupply, err = cache.GetGatherSupplyFromCache(id)
	if err != nil {
		return
	}
	var category model.GatherSupplyCategory
	category = GetGatherSupplyCategory(gatherSupply.CategoryID)
	key = category.Key
	return
}

func UpdateImportGoods(gatherSupplyID uint) (err error) {
	var gatherSupply service.GatherSupply

	err = source.DB().Where("id = ?", gatherSupplyID).First(&gatherSupply).Error
	if err != nil {
		return
	}
	if gatherSupply.CategoryID == 2 {
		var productIds []uint
		//var count int64
		//err = source.DB().Model(&model3.Product{}).Where("deleted_at is NULL").Where("gather_supply_id = ?", gatherSupplyID).Count(&count).Error
		//if err != nil {
		//	return
		//}
		err = source.DB().Model(&model3.Product{}).Where("gather_supply_id = ?", gatherSupplyID).Where("deleted_at is NULL").Pluck("source_goods_id", &productIds).Error
		if err != nil {
			return
		}
		var goodsIds []int
		for _, v := range productIds {
			goodsIds = append(goodsIds, int(v))

			if err != nil {
				return
			}
			if len(goodsIds) >= 100 {
				err = mq.PublishMessage(callback.CallBackType{
					Type:  string(callback.GOODS_PRICE_ALERT),
					MsgID: "selfdasdadsa",
					Data: callback.GoodsIds{
						GoodsIds: goodsIds,
					},
				})
				goodsIds = []int{}
			}
		}
		err = source.DB().Unscoped().Delete(&model.GoodsStorage{}, "supply_id=?", gatherSupplyID).Error
		if err != nil {
			return
		}
		err = mq.PublishMessage(callback.CallBackType{
			Type:  string(callback.GOODS_PRICE_ALERT),
			MsgID: "selfdasdadsa",
			Data: callback.GoodsIds{
				GoodsIds: goodsIds,
			},
		})
	}
	if gatherSupply.CategoryID == 134 {
		var productIds []uint
		//var count int64
		//err = source.DB().Model(&model3.Product{}).Where("deleted_at is NULL").Where("gather_supply_id = ?", gatherSupplyID).Count(&count).Error
		//if err != nil {
		//	return
		//}
		err = source.DB().Model(&model3.Product{}).Where("gather_supply_id = ?", gatherSupplyID).Where("deleted_at is NULL").Pluck("source_goods_id", &productIds).Error
		if err != nil {
			return
		}
		var goodsIds []int
		for _, v := range productIds {
			goodsIds = append(goodsIds, int(v))

			if err != nil {
				return
			}
			if len(goodsIds) >= 100 {
				err = mq.PublishMessage(callback.CallBackType{
					Type:  string(callback.GOODS_PRICE_ALERT),
					MsgID: "ztcake",
					Data: callback.GoodsIds{
						GoodsIds: goodsIds,
					},
				})
				goodsIds = []int{}
			}
		}
		err = source.DB().Unscoped().Delete(&model.GoodsStorage{}, "supply_id=?", gatherSupplyID).Error
		if err != nil {
			return
		}
		err = mq.PublishMessage(callback.CallBackType{
			Type:  string(callback.GOODS_PRICE_ALERT),
			MsgID: "ztcake",
			Data: callback.GoodsIds{
				GoodsIds: goodsIds,
			},
		})
	}
	return
}

func UpdateImportGoodsYouxuan(gatherSupplyID uint) (err error) {
	var gatherSupply service.GatherSupply

	err = source.DB().Where("id = ?", gatherSupplyID).First(&gatherSupply).Error
	if err != nil {
		return
	}
	var productIds []uint

	err = source.DB().Model(&model3.Product{}).Where("gather_supply_id = ?", gatherSupplyID).Where("deleted_at is NULL").Pluck("id", &productIds).Error
	if err != nil {
		return
	}
	for _, v := range productIds {
		err = source.Redis().LPush(context.Background(), "notifyProductYouxuan", v).Err()
		if err != nil {
			return
		}
	}

	return
}

/*
*

	初始化来源自定义名称表 （每次有新的来源也执行这个）
*/
func InitApplicationSource() (err error) {
	categoryList := GetGatherSupplyCategoryList()

	var applicationSource []appm.ApplicationSource

	source.DB().Find(&applicationSource)
	var isAdd = 1 //是否添加
	//循环所有有权限的分类 进行把响应来源添加到自定义名称表中
	for _, item := range categoryList {
		for _, itemSource := range config.SupplyMap {
			if item.Key == itemSource.Type {
				isAdd = 1
				for _, itemApplicationSource := range applicationSource {
					if uint(itemSource.ID) == itemApplicationSource.SourceID {
						//如果不一致就更新数据
						if itemSource.Type != itemApplicationSource.Type || itemSource.Name != itemApplicationSource.Name || item.Name != itemApplicationSource.ApplicationTitle {
							var addApplicationSourceUpdate appm.ApplicationSource
							addApplicationSourceUpdate.Type = itemSource.Type
							addApplicationSourceUpdate.Name = itemSource.Name
							addApplicationSourceUpdate.ApplicationTitle = item.Name
							source.DB().Where("id = ?", itemApplicationSource.ID).Updates(&addApplicationSourceUpdate)
						}
						isAdd = 0
						break
					}
				}
				if isAdd == 0 {
					continue
				} else {
					var addApplicationSource appm.ApplicationSource
					addApplicationSource.SourceID = uint(itemSource.ID)
					addApplicationSource.Type = itemSource.Type
					addApplicationSource.Name = itemSource.Name
					addApplicationSource.ApplicationTitle = item.Name
					source.DB().Create(&addApplicationSource)
				}
			}

		}
	}
	return
}

/*
*

	获取来源列表
*/
func ApplicationSourceList(info request2.ApplicationSourceSearch) (err error, applicationSourceList []appm.ApplicationSource, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&appm.ApplicationSource{})

	// 如果有条件搜索 下方会自动创建搜索语句
	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&applicationSourceList).Error

	return
}

/*
*

	修改来源自定义名称
*/
func UpdateApplicationSource(info appm.ApplicationSource) (err error) {
	var applicationSource appm.ApplicationSource
	err = source.DB().Where("id = ?", info.ID).First(&applicationSource).Error
	if err != nil {
		err = errors.New("数据不存在")
	}
	applicationSource.Title = info.Title
	err = source.DB().Save(&applicationSource).Error
	cache.ClearAllApplicationSources()
	return
}

func ListAddressMapping(info request.GetCategorySearch) (err error, data []model.AddressMapping, total int64) {

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	db := source.DB().Model(&model.AddressMapping{}).Where("gather_supply=?", info.GatherSupplyID)

	// 如果有条件搜索 下方会自动创建搜索语句
	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&data).Error

	//err = source.DB().Where("gather_supply=?", id).Find(&data).Error

	return

}

func DeleteAddressMapping(id uint) (err error) {

	var data model.AddressMapping
	err = source.DB().Delete(&data, "id=?", id).Error

	return

}

func SetGoodsMd5EmptyWithGatherSupplyID(gatherSupplyID uint) (err error) {
	err = source.DB().Model(&model3.Product{}).Where("gather_supply_id = ?", gatherSupplyID).Where("deleted_at is null").Update("md5", "").Error
	return
}
