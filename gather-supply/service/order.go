package service

import (
	_ "embed"
	"encoding/json"
	"errors"
	eventDistributionService "event-distribution/service"
	order3 "gather-supply/component/order"
	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"math"
	omodel "order/model"
	model2 "product/model"
	"public-supply/model"
	"public-supply/request"
	"public-supply/response"
	model3 "region/model"
	"shipping/freight"
	service2 "shipping/service"
	shoppingCartModel "shopping-cart/model"
	shoppingCartService "shopping-cart/service"
	"strconv"
	"sync"
	"time"
	"trade/checkout"
	model4 "trade/model"
	request2 "trade/request"
	service4 "trade/service"
	"yz-go/cache"
	"yz-go/component/log"
	"yz-go/source"
)

// 供应链下单
func CreateSupplyOrder(orderId uint) (err error) {

	var supplyOrderData request.RequestConfirmOrder
	var guangDianPreOrder request.GuangDianPreOrder

	var orderSn request.OrderSn
	var spus, localSpus request.GoodsSpus
	var spu, localSpu request.GoodsSpu
	var order omodel.Order
	var supplyOrder model.SupplyOrder
	err = source.DB().Preload("ShippingAddress").Preload("OrderItems", "refund_status < 3").Where("id = ?", orderId).First(&order).Error
	//log.Log().Info("提交未退款的item", zap.Any("info", order.OrderItems))
	if err != nil {
		return
	}
	if order.Status != 1 {
		log.Log().Info("订单不是待发货状态" + strconv.Itoa(int(orderId)))
		return
	}

	if order.GatherSupplyID == 0 { //供应链的订单

		log.Log().Info("不是供应链订单直接跳过" + strconv.Itoa(int(orderId)))

		return
	}
	//修改本地订单为锁定状态
	err = source.DB().Model(&omodel.Order{}).Where("id = ?", orderId).Update("lock", 1).Error
	if err != nil {

		log.Log().Info("锁定订单失败" + strconv.Itoa(int(orderId)))
		return
	}
	//fmt.Print("orderitem", order.OrderItems)
	//
	//fmt.Println("orderaddress", order.ShippingAddress)
	supplyOrder.OrderID = order.ID
	supplyOrder.OrderSn = order.OrderSN
	supplyOrder.SupplyID = order.GatherSupplyID
	supplyOrder.UnionId = order.GatherSupplySN
	err = source.DB().Create(&supplyOrder).Error

	for _, orderItem := range order.OrderItems {
		spu.Sku.Sku = int64(orderItem.OriginalSkuID)
		spu.Number = int(orderItem.Qty)
		spus = append(spus, spu)

		localSpu.Sku.Sku = int64(orderItem.SkuID)
		localSpu.Number = int(orderItem.Qty)
		localSpus = append(localSpus, localSpu)

	}

	var orderDataRecord model4.OrderDataRecord

	err = source.DB().Where("order_sn=?", order.ThirdOrderSN).First(&orderDataRecord).Error
	if err != nil {
		log.Log().Error("CreateSupplyOrder orderDataRecord", zap.Any("err", err))
	}

	if orderDataRecord.Data != "" {
		UnmarshalErr := json.Unmarshal([]byte(orderDataRecord.Data), &guangDianPreOrder)
		if UnmarshalErr != nil {
			log.Log().Error("CreateSupplyOrder orderDataRecord Unmarshal", zap.Any("UnmarshalErr", UnmarshalErr))
		}
	}

	//sku数组

	//收货信息
	var address request.ReceivingInformation
	address.Consignee = order.ShippingAddress.Realname
	address.Phone = order.ShippingAddress.Mobile
	address.ProvinceId = order.ShippingAddress.ProvinceId
	address.CityId = order.ShippingAddress.CityId
	address.CountyId = order.ShippingAddress.CountyId
	address.TownId = order.ShippingAddress.TownId
	address.Province = order.ShippingAddress.Province
	address.City = order.ShippingAddress.City
	address.Area = order.ShippingAddress.County
	address.Street = order.ShippingAddress.Town
	address.Description = order.ShippingAddress.Detail

	orderSn.OrderSn = strconv.Itoa(int(order.OrderSN)) //订单sn
	supplyOrderData.OrderSn = orderSn
	supplyOrderData.Skus = spus       //spus
	supplyOrderData.Address = address //地址
	supplyOrderData.LocalSkus = localSpus
	supplyOrderData.AppID = order.ApplicationID
	supplyOrderData.Remark = order.Remark
	supplyOrderData.GuangDian = guangDianPreOrder

	log.Log().Info("供应链准备提交订单数据", zap.Any("info", supplyOrderData))
	if order.GatherSupplyID > 0 { //供应链的订单
		var resInfo *stbz.APIResult
		var key string
		err, key = GetGatherSupplyCategoryKey(order.GatherSupplyID)
		if err != nil {
			log.Log().Error("前置校验失败"+strconv.Itoa(int(orderId)), zap.Any("info", err.Error()))
			return
		}
		var orderClass = order3.NewOrder(key)
		err = orderClass.InitSetting(order.GatherSupplyID)
		if err != nil {
			log.Log().Error("前置校验失败"+strconv.Itoa(int(orderId)), zap.Any("info", err.Error()))
			return
		}
		err, resInfo = orderClass.ConfirmOrder(supplyOrderData)
		if err == nil {
			if key == "self" {
				if resInfo.Code == 1 {
					//成功
					selfData := source.Strval(resInfo.Data)
					type orderResp struct {
						Orders []omodel.Order `json:"Orders"`
					}
					var orders orderResp
					err = json.Unmarshal([]byte(selfData), &orders)
					if err != nil {
						return
					}
					var orderSnString string
					orderCount := len(orders.Orders)
					for _, v := range orders.Orders {
						orderSnString += strconv.Itoa(int(v.OrderSN))
						if orderCount > 1 {
							orderSnString += "、"
						}
					}
					log.Log().Info("self供应链下单成功"+strconv.Itoa(int(orderId)), zap.Any("data", resInfo))

					err = source.DB().Model(&omodel.OrderModel{}).Where("id = ?", orderId).Updates(&omodel.OrderModel{GatherSupplySN: orderSnString, GatherSupplyMsg: "待发货"}).Error
					if err != nil {
						log.Log().Info("修改供应链订单号失败" + strconv.Itoa(int(orderId)))
						return
					}
				} else {
					//失败
					selfData := source.Strval(resInfo.Data)
					type orderErrorResp struct {
						Messages []string `json:"messages"`
					}
					var orderErrResp orderErrorResp
					err = json.Unmarshal([]byte(selfData), &orderErrResp)
					var orderErrString string
					orderErrString = resInfo.Msg
					for _, v := range orderErrResp.Messages {
						orderErrString += v
					}
					log.Log().Info("中台供应链下单失败"+strconv.Itoa(int(orderId))+"，"+orderErrString, zap.Any("info", resInfo))

					err = source.DB().Model(&omodel.Order{}).Where("id = ?", orderId).Update("gather_supply_msg", "下单失败:"+orderErrString).Error
					if err != nil {
						log.Log().Info("修改供应链订单号失败" + strconv.Itoa(int(orderId)))
						return
					}

				}
			}

			if key == "dwd" {

			}
			err = source.DB().Model(model.SupplyOrder{}).Where("id = ?", supplyOrder.ID).Update("status", 1).Error
			if err != nil {
				log.Log().Info("供应链下单成功，更新商品失败!", zap.Any("err", err.Error()), zap.Any("err", supplyOrder))

			}

		} else {
			log.Log().Info("供应链下单失败!", zap.Any("err", err))
			err = source.DB().Model(&omodel.Order{}).Where("id = ?", orderId).Update("gather_supply_msg", err.Error()).Error

		}
	}

	return
}

type Users struct {
	source.Model
	ThousandsPricesID uint            `json:"thousands_prices_id"`
	ThousandsPrices   ThousandsPrices `json:"thousands_prices"`
	LevelID           uint            `json:"level_id"`
}
type ThousandsPrices struct {
	source.Model
	Name                    string                    `json:"name"`
	Status                  int                       `json:"status"`
	IsFee                   int                       `json:"is_fee"`
	UnifyRatio              uint                      `json:"unify_ratio"`
	GuideRatio              uint                      `json:"guide_ratio" form:"guide_ratio"`
	OriginRatio             uint                      `json:"origin_ratio" form:"origin_ratio"`
	Products                []model2.Product          `json:"products" gorm:"many2many:thousands_prices_products;"`
	ThousandsPricesProducts []ThousandsPricesProducts `json:"thousands_prices_products"`
	FilterImport            int                       `json:"filter_import" gorm:"column:filter_import;default:1"` //是否过滤导入商品，0不过滤 1过滤

}
type ThousandsPricesProducts struct {
	source.Model
	ThousandsPricesID   uint `json:"thousands_prices_id"`
	ProductID           uint `json:"product_id"`
	SkuID               uint `json:"sku_id"`
	Price               uint `json:"price"`
	SkuPrice            uint `json:"sku_price"`
	GuidePrice          uint `json:"guide_price"`
	SkuGuidePrice       uint `json:"sku_guide_price"`
	OriginPrice         uint `json:"origin_price"`
	SkuOriginPrice      uint `json:"sku_origin_price"`
	Strategy            uint `json:"strategy"`              //定价策略方式 1统一定价策略  2供应商定价策略  3分类
	StrategyRatio       uint `json:"strategy_ratio"`        //定价系数
	GuideStrategyRatio  uint `json:"guide_strategy_ratio"`  //定价系数
	OriginStrategyRatio uint `json:"origin_strategy_ratio"` //定价系数
}

func ValidateSkus(checkoutRequest request2.BeforeCheckRequest, userID uint, appID uint) (err error, freightAmount uint) {
	log.Log().Info("ValidateSkus checkoutRequest request", zap.Any("info", checkoutRequest))

	//修复存在客户对接时，参数名称写错导致没有这个参数会返回可售的问题
	if len(checkoutRequest.Spu) == 0 {
		return errors.New("缺少商品规格参数"), freightAmount
	}

	var province model3.Region
	var city model3.Region
	var county model3.Region
	var town model3.Region
	var addressRequest = request2.Address{}
	addressRequest = checkoutRequest.Address
	//原因1 省市区县单独建立方法方便其他地方调用（例如修改收货地址的时） -- 调整为这样的原因是 如果用户传北京-北京市-***-*** 原本的会查询出11-11-110000-1100000
	//造成市的四位1101 等匹配不上导致返回不在配送范围
	//原因2 云中鹤地址绑定绑定中台地址,如果纯使用传过来的参数会导致云中鹤地址绑定匹配不到地址
	err, province, city, county, town = service4.MatchingRegion(addressRequest)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	checkoutRequest.Address.Province = province.Name
	checkoutRequest.Address.City = city.Name
	checkoutRequest.Address.Area = county.Name
	checkoutRequest.Address.Street = town.Name

	//这里改用下单同样的检验 避免出现前置检验通过下单 下单不了的情况
	var id int64
	one := 1
	zero := 0
	id, err = cache.GetID("api_buy")
	if err != nil {
		log.Log().Error("buy_id生成失败", zap.Any("err", err))
		err = errors.New("buy_id生成失败")
		return
	}
	buyID := id*int64(math.Pow(10, 10)) + time.Now().Unix()
	var user Users
	err = source.DB().Preload("ThousandsPrices.Products").Preload("ThousandsPrices.ThousandsPricesProducts").First(&user, userID).Error
	if err != nil {
		return
	}
	var thousandsProductIds = make(map[uint]uint)
	for _, product := range user.ThousandsPrices.Products {
		thousandsProductIds[product.ID] = product.ID
	}
	var gatherMap = make(map[uint][]model2.Sku)
	for _, v := range checkoutRequest.Spu {
		var sku model2.Sku
		err = source.DB().Preload("Product").First(&sku, v.Sku).Error
		if err != nil || sku.Product == nil {
			return errors.New("商品数据出错"), freightAmount
		}
		//千人千价 过滤商品下单
		if len(user.ThousandsPrices.Products) > 0 && user.ThousandsPrices.Status == 1 && user.ThousandsPrices.FilterImport == 1 {
			//千人千价 只查询指定商品
			if _, ok := thousandsProductIds[sku.ProductID]; !ok {
				log.Log().Error("添加失败!", zap.Any("err", err))
				err = errors.New("添加失败,此商品无下单权限：" + sku.Product.Title + "[" + sku.Title + "]")
				return
			}
		}
		sku.Number = v.Number
		shoppingCart := shoppingCartModel.ShoppingCart{
			UserID:        userID,
			SkuID:         sku.ID,
			Qty:           v.Number,
			Status:        &zero,
			Checked:       &one,
			BuyID:         uint(buyID),
			ApplicationID: appID,
			BuyWay:        2,
			AddressID:     0,
		}
		err = shoppingCartService.CreateShoppingCart(shoppingCart)
		if err != nil {
			log.Log().Error("添加失败!", zap.Any("err", err))
			err = errors.New("添加失败,错误：" + err.Error())
			return
		}

		gatherMap[sku.Product.GatherSupplyID] = append(gatherMap[sku.Product.GatherSupplyID], sku)
	}
	var fwg sync.WaitGroup
	var mu sync.Mutex // 用于保护 freightItems 切片
	//var freightItems []uint
	var freightItem uint
	errChan := make(chan error, len(gatherMap)) // 错误通道

	for gatherID, gatherSkus := range gatherMap {
		fwg.Add(1)
		go func(gatherID uint, gatherSkus []model2.Sku) { // 假设 gatherSkus 的类型为 []SkuType
			defer fwg.Done()

			freightItem, err = getGatherSkusFreigh(checkoutRequest, gatherID, gatherSkus, appID, province, city, county, town)
			if err != nil {
				errChan <- err // 将错误发送到错误通道
				return
			}

			mu.Lock()
			//freightItems = append(freightItems, freightItem)
			freightAmount += freightItem //上面那种写法  下面去循环拿不到值 导致运费为0
			mu.Unlock()
		}(gatherID, gatherSkus)
	}

	// 创建另一个WaitGroup用于关闭错误通道
	var closeWg sync.WaitGroup
	closeWg.Add(1)
	go func() {
		fwg.Wait()     // 等待所有请求完成
		close(errChan) // 关闭错误通道
		closeWg.Done()
	}()

	//for _, v := range freightItems {
	//	freightAmount += v
	//}

	// 等待错误通道被关闭
	closeWg.Wait()

	// 处理错误
	for err = range errChan {
		if err != nil {
			return
		}
	}
	//通用校验
	// 读取购物车记录
	var shoppingCarts checkout.ShoppingCarts
	err, shoppingCarts = checkout.GetCheckedShoppingCarts(checkout.ShoppingCart{UserID: userID, BuyID: uint(buyID)})
	if err != nil {
		log.Log().Error("获取失败:读取购物车记录", zap.Any("err", err))
		err = errors.New("获取失败" + err.Error())
		return
	}

	if len(shoppingCarts) == 0 {
		//  没有购物车记录
		log.Log().Error("请选择要结算的商品", zap.Any("err", err))
		err = errors.New("请选择要结算的商品" + err.Error())
		return
	}
	var shoppingCartIDs []uint
	for _, shoppingCartData := range shoppingCarts {
		shoppingCartIDs = append(shoppingCartIDs, shoppingCartData.ID)
	}
	err = shoppingCartService.CheckBeforeSale(shoppingCartIDs)
	if err != nil {
		return
	}
	// 结算信息
	//var checkoutInfo checkout.Checkout
	err, _ = checkout.ShoppingCartCheckout(userID, shoppingCarts)
	if err != nil {
		log.Log().Error("获取失败:结算信息", zap.Any("err", err))
		err = errors.New("获取失败" + err.Error())
		//验证之后清除
		err1 := checkout.ClearCheckedShoppingCarts(checkout.ShoppingCart{UserID: userID, BuyID: uint(buyID), BuyWay: 2})
		if err1 != nil {
			err = errors.New(err.Error() + err1.Error())
		}
		return
	}
	//验证完成之后清除一下避免可能引起其他问题
	err = checkout.ClearCheckedShoppingCarts(checkout.ShoppingCart{UserID: userID, BuyID: uint(buyID), BuyWay: 2})
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		err = errors.New("获取失败" + err.Error())
		return
	}

	return
}

func getGatherSkusFreigh(checkoutRequest request2.BeforeCheckRequest, gatherID uint, gatherSkus []model2.Sku, appID uint, province model3.Region, city model3.Region, county model3.Region, town model3.Region) (freightAmount uint, err error) {
	if gatherID > 0 {

		var key string
		err, key = GetGatherSupplyCategoryKey(gatherID)
		if err != nil {
			return
		}
		var supply = order3.NewOrder(key)
		err = supply.InitSetting(gatherID)
		if err != nil {
			return
		}
		var requests request.RequestSaleBeforeCheck
		var resData response.BeforeCheck
		//requests.GatherSupplyID = v.Product.GatherSupplyID
		requests.Address.City = checkoutRequest.Address.City
		requests.Address.Province = checkoutRequest.Address.Province
		requests.Address.Area = checkoutRequest.Address.Area
		requests.Address.Street = checkoutRequest.Address.Street

		requests.Address.Consignee = checkoutRequest.Address.Consignee
		requests.Address.Phone = checkoutRequest.Address.Phone
		requests.Address.Description = checkoutRequest.Address.GetSimplifyDescription()
		requests.LianLian.IdCard = checkoutRequest.LianLian.IdCard
		requests.LianLian.Memo = checkoutRequest.LianLian.Memo
		requests.LianLian.ThirdOrderSN = checkoutRequest.LianLian.ThirdOrderSN
		requests.LianLian.TravelDate = checkoutRequest.LianLian.TravelDate
		requests.GuangDian.Number = checkoutRequest.Guangdian.Number
		requests.GuangDian.IdenNr = checkoutRequest.Guangdian.IdenNr
		requests.GuangDian.RegionId = checkoutRequest.Guangdian.RegionId
		requests.GuangDian.Area = checkoutRequest.Guangdian.Area
		requests.CakeData.Group = checkoutRequest.CakeData.Group
		requests.CakeData.CityId = checkoutRequest.CakeData.CityId
		requests.CakeData.BuyerMsg = checkoutRequest.CakeData.BuyerMsg
		requests.CakeData.AddressId = checkoutRequest.CakeData.AddressId
		requests.CakeData.BuyerPhone = checkoutRequest.CakeData.BuyerPhone
		requests.CakeData.DistributionRuleId = checkoutRequest.CakeData.DistributionRuleId
		requests.CakeData.ShipType = checkoutRequest.CakeData.ShipType

		requests.AppID = appID
		for _, spu := range gatherSkus {

			requests.Skus = append(requests.Skus, request.GoodsSpu{Sku: request.Sku{Sku: int64(spu.OriginalSkuID)}, Number: int(spu.Number), GatherSupplyID: gatherID})
			requests.LocalSkus = append(requests.LocalSkus, request.GoodsSpu{Sku: request.Sku{Sku: int64(spu.ID)}, Number: int(spu.Number), GatherSupplyID: gatherID})

		}
		log.Log().Info("请求供应链前置校验接口")
		err, resData = supply.OrderBeforeCheck(requests)
		if err != nil {
			return
		} else {
			if resData.Code == 1 {
				if key == "jushuitan" {
					var ShippingItems []freight.Item
					for _, sku := range gatherSkus {
						var storages model2.Storage
						err = source.DB().Model(&model2.Storage{}).Where("app_id = ?", appID).Where("product_id = ?", sku.ProductID).First(&storages).Error
						if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
							err = errors.New("商品:'" + sku.Product.Title + "'未加入选品库")
							return
						}
						shippingItem := freight.Item{
							Amount:            sku.Price * sku.Number,
							Qty:               sku.Number,
							Title:             sku.Product.Title,
							Weight:            uint(sku.Weight),
							Long:              uint(sku.Product.Long),
							Wide:              uint(sku.Product.Wide),
							High:              uint(sku.Product.High),
							Volume:            uint(sku.Product.Volume),
							Freight:           uint(sku.Product.Freight),
							FreightType:       sku.Product.FreightType,
							FreightTemplateID: sku.Product.FreightTemplateID,
							GatherSupplyID:    sku.Product.GatherSupplyID,
							OriginalSkuID:     uint(sku.OriginalSkuID),
							ID:                uint(sku.ID),
						}
						shippingItem.Address.Realname = checkoutRequest.Address.Consignee
						shippingItem.Address.Mobile = checkoutRequest.Address.Phone

						shippingItem.Address.ProvinceId = province.ID
						shippingItem.Address.CityId = city.ID
						shippingItem.Address.CountyId = county.ID
						shippingItem.Address.TownId = town.ID
						shippingItem.Address.Province = province.Name
						shippingItem.Address.City = city.Name
						shippingItem.Address.County = county.Name
						shippingItem.Address.Town = town.Name
						shippingItem.Address.Detail = checkoutRequest.Address.GetSimplifyDescription()

						ShippingItems = append(ShippingItems, shippingItem)

					}
					var amount uint
					err, amount, _ = service2.GetExpressFreight(ShippingItems)
					if err != nil {
						err = errors.New("获取运费失败" + err.Error())
						return
					}
					freightAmount = amount
				} else {
					freightAmount = resData.Freight
				}
			} else {
				err = errors.New(resData.Msg)
				return
			}
		}
	} else {
		//if sku.Stock < int(v.Number) {
		//	return errors.New("商品库存不足"), []model2.Sku{}
		//}
		//if sku.Product.IsDisplay == 0 {
		//	return errors.New("商品已下架"), []model2.Sku{}
		//}
		var ShippingItems []freight.Item
		for _, sku := range gatherSkus {
			var storages model2.Storage
			err = source.DB().Model(&model2.Storage{}).Where("app_id = ?", appID).Where("product_id = ?", sku.ProductID).First(&storages).Error
			if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
				err = errors.New("商品:'" + sku.Product.Title + "'未加入选品库")
				return
			}
			shippingItem := freight.Item{
				Amount:            sku.Price * sku.Number,
				Qty:               sku.Number,
				Title:             sku.Product.Title,
				Weight:            uint(sku.Weight),
				Long:              uint(sku.Product.Long),
				Wide:              uint(sku.Product.Wide),
				High:              uint(sku.Product.High),
				Volume:            uint(sku.Product.Volume),
				Freight:           uint(sku.Product.Freight),
				FreightType:       sku.Product.FreightType,
				FreightTemplateID: sku.Product.FreightTemplateID,
				GatherSupplyID:    sku.Product.GatherSupplyID,
				OriginalSkuID:     uint(sku.OriginalSkuID),
				ID:                uint(sku.ID),
			}
			shippingItem.Address.Realname = checkoutRequest.Address.Consignee
			shippingItem.Address.Mobile = checkoutRequest.Address.Phone

			shippingItem.Address.ProvinceId = province.ID
			shippingItem.Address.CityId = city.ID
			shippingItem.Address.CountyId = county.ID
			shippingItem.Address.TownId = town.ID
			shippingItem.Address.Province = province.Name
			shippingItem.Address.City = city.Name
			shippingItem.Address.County = county.Name
			shippingItem.Address.Town = town.Name
			shippingItem.Address.Detail = checkoutRequest.Address.GetSimplifyDescription()

			ShippingItems = append(ShippingItems, shippingItem)

		}
		var amount uint
		err, amount, _ = service2.GetExpressFreight(ShippingItems)
		if err != nil {
			err = errors.New("获取运费失败" + err.Error())
			return
		}
		freightAmount = amount
	}
	return
}

func AvailableSkus(checkoutRequest request2.BeforeCheckRequest) (err error, availableAndBan map[string][]uint) {

	availableAndBan = make(map[string][]uint)
	//log.Log().Info("taoguangdayin3", zap.Any("data2", checkoutRequest))

	for _, v := range checkoutRequest.Spu {
		var sku model2.Sku
		err = source.DB().Preload("Product").First(&sku, v.Sku).Error
		if err != nil || sku.Product == nil {
			//调整为这样 不然一个商品不存在 所有的商品都不可购买了
			availableAndBan["ban"] = append(availableAndBan["ban"], v.Sku)
			log.Log().Error("商品检验:商品不存在", zap.Any("data", sku))
			continue
			//return errors.New("商品数据出错"), availableAndBan
		}
		//如果是团购商品不可售直接返回
		if v.EventDistributionId != 0 {
			err = eventDistributionService.IsEventDistribution()
			if err != nil {
				return
			}
			err = eventDistributionService.VerifyEventDistribution(v.EventDistributionId)
			if err != nil {
				return
			}
		}
		//log.Log().Info("taoguangdayin", zap.Any("data", sku))
		//log.Log().Info("taoguangdayin2", zap.Any("data2", v))

		if sku.Product.GatherSupplyID > 0 {
			var key string
			err, key = GetGatherSupplyCategoryKey(sku.Product.GatherSupplyID)
			if err != nil {
				return
			}
			var supply = order3.NewOrder(key)
			err = supply.InitSetting(sku.Product.GatherSupplyID)
			if err != nil {
				return
			}
			var requests request.RequestSaleBeforeCheck
			var resData response.ResSaleBeforeCheck
			//requests.GatherSupplyID = sku.Product.GatherSupplyID
			requests.Address.City = checkoutRequest.Address.City
			requests.Address.Consignee = checkoutRequest.Address.Consignee
			requests.Address.Phone = checkoutRequest.Address.Phone
			requests.Address.Province = checkoutRequest.Address.Province
			requests.Address.Area = checkoutRequest.Address.Area
			requests.Address.Street = checkoutRequest.Address.Street
			requests.Address.Description = checkoutRequest.Address.GetSimplifyDescription()
			requests.Skus = append(requests.Skus, request.GoodsSpu{Sku: request.Sku{Sku: int64(sku.OriginalSkuID)}, Number: int(v.Number)})
			requests.LocalSkus = append(requests.LocalSkus, request.GoodsSpu{Sku: request.Sku{Sku: int64(sku.ID)}, Number: int(v.Number)})

			err, resData = supply.SaleBeforeCheck(requests)
			if err != nil {
				availableAndBan["ban"] = append(availableAndBan["ban"], v.Sku)
			} else {
				//返回的是不是第三方sku_id  0是 1不是
				if resData.IsOriginalSkuID == 1 {
					for _, ab := range resData.Data.Ban {
						availableAndBan["ban"] = append(availableAndBan["ban"], ab)
					}
					for _, aa := range resData.Data.Available {
						availableAndBan["available"] = append(availableAndBan["available"], aa)
					}
				} else {
					for _, ab := range resData.Data.Ban {
						if ab == uint(sku.OriginalSkuID) {
							availableAndBan["ban"] = append(availableAndBan["ban"], uint(sku.ID))
						}
					}
					for _, aa := range resData.Data.Available {
						if aa == uint(sku.OriginalSkuID) {
							availableAndBan["available"] = append(availableAndBan["available"], uint(sku.ID))
						}
					}
				}

			}
		} else {

			if sku.Stock < int(v.Number) || sku.Product == nil || sku.Product.IsDisplay == 0 {
				availableAndBan["ban"] = append(availableAndBan["ban"], sku.ID)
			} else {
				availableAndBan["available"] = append(availableAndBan["available"], sku.ID)
			}
		}

	}
	log.Log().Info(" jd availableAndBan", zap.Any("info", availableAndBan))
	return
}

func OrderBeforeCheck(Request request.RequestSaleBeforeCheck) (err error, data response.BeforeCheck) {
	var groupSkus = make(map[uint][]request.GoodsSpu)
	for _, v := range Request.Skus {
		groupSkus[v.GatherSupplyID] = append(groupSkus[v.GatherSupplyID], v)
	}
	var groupLocalSkus = make(map[uint][]request.GoodsSpu)
	for _, v := range Request.LocalSkus {
		groupLocalSkus[v.GatherSupplyID] = append(groupLocalSkus[v.GatherSupplyID], v)
	}
	for k, group := range groupSkus {
		var newRequest request.RequestSaleBeforeCheck
		newRequest.LocalSkus = groupLocalSkus[k]
		newRequest.Skus = group
		newRequest.Address = Request.Address
		var key string
		err, key = GetGatherSupplyCategoryKey(k)
		if err != nil {
			log.Log().Error("前置校验失败123", zap.Any("info", err.Error()))
			return
		}
		// 会员权益商品是供应链商品, 是假的供应链商品, 不需要供应链下单
		if key == "" {
			data.Code = 1
			return
		}
		var orderClass = order3.NewOrder(key)
		err = orderClass.InitSetting(k)
		if err != nil {
			log.Log().Error("前置校验失败234", zap.Any("info", err.Error()))
			return
		}
		log.Log().Info("请求供应链前置校验接口2")
		var info response.BeforeCheck
		err, info = orderClass.OrderBeforeCheck(newRequest)
		if err != nil {
			return
		}
		if info.Code != 1 {
			data.Msg = info.Msg
			data.Code = info.Code
			return
		}
		data.Freight += info.Freight
		data.Code = info.Code
	}
	return

}
