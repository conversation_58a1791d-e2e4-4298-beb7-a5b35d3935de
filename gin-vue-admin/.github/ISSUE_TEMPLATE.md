---
name: issues模板
about: 请严格按照要求进行提交issues! 如果未能按照要求,我们将会close您的issues,敬请谅解!
---
<!-- 在提交问题之前，请先回答这些问题。谢谢! -->

### 1. 您使用的是哪个版本的`Go`和系统类型/架构 ?

<!-- 
请从您的终端粘贴命令 `go version` 的输出。
期望看到像: `go version go1.14.4 darwin/amd64` 
-->

### 2. 您使用的是哪个版本的 `Nodejs` 和 `npm` 或 `cnpm` ?

<!-- 
请从您的终端粘贴命令 `node -v` 的输出。
期望看到像: `v12.16.1` 
-->

<!-- 
请从您的终端粘贴命令 `npm -v` 的输出。
期望看到像: `6.13.4`
-->

<!-- 
请从您的终端粘贴命令 `cnpm -v` 的输出。
期望看到的像: `
cnpm@6.1.1 (/usr/local/lib/node_modules/cnpm/lib/parse_argv.js)
npm@6.14.2 (/usr/local/lib/node_modules/cnpm/node_modules/npm/lib/npm.js)
node@12.16.1 (/usr/local/bin/node)
npminstall@3.27.0 (/usr/local/lib/node_modules/cnpm/node_modules/npminstall/lib/index.js)
prefix=/usr/local 
darwin x64 19.6.0 
registry=https://r.npm.taobao.org
` 
-->

### 3. 您使用的是哪个版本的 `gin-vue-admin` ?

<!-- 
您如果克隆的是master就写master, 
如果是在Releases中下载的zip/tar.gz对应的tag版本,
如:`v2.3.0` 
-->



### 4. 可以在master版本中复现此问题吗 ?



### 5. 您做了什么 ?

<!-- 
如果可能，请提供最短代码的副本以重现该错误。
一个完整的可运行程序是最好的。 
-->



### 6. 您期望看到什么 ?



### 7. 您看到了什么 ?



### 8. 错误堆栈或者SQL打印