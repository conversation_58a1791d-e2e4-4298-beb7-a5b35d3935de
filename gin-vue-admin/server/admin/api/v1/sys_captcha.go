package v1

import (
	"yz-go/captcha"
	"context"
	"fmt"
	"gin-vue-admin/admin/model/response"
	"github.com/gin-gonic/gin"
	"github.com/mojocn/base64Captcha"
	"go.uber.org/zap"
	"time"
	"yz-go/component/log"
	"yz-go/config"
	yzResponse "yz-go/response"
	"yz-go/source"
)

var store = captcha.RedisStore{}

// @Tags Base
// @Summary 生成验证码
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"验证码获取成功"}"
// @Router /base/captcha [post]
func Captcha(c *gin.Context) {
	//字符,公式,验证码配置
	// 生成默认数字的driver
	driver := base64Captcha.NewDriverDigit(config.Config().Captcha.ImgHeight, config.Config().Captcha.ImgWidth, config.Config().Captcha.KeyLong, 0.7, 80)
	cp := base64Captcha.NewCaptcha(driver, store)
	//id, b64s, err := cp.Generate()
	id, content, answer := cp.Driver.GenerateIdQuestionAnswer()
	item, err := cp.Driver.DrawCaptcha(content)
	b64s := item.EncodeB64string()
	fmt.Println(answer)
	var ctx = context.Background()

	source.Redis().Set(ctx, id, answer, time.Second*60)

	if err != nil {
		log.Log().Error("验证码获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("验证码获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(response.SysCaptchaResponse{
			CaptchaId: id,
			PicPath:   b64s,
		}, "验证码获取成功", c)
	}
}
