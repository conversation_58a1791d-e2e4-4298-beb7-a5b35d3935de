package v1

import (
	"fmt"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"

	"gin-vue-admin/admin/model"
	"gin-vue-admin/admin/model/request"
	"gin-vue-admin/admin/model/response"
	"gin-vue-admin/admin/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"yz-go/component/log"
	"yz-go/utils"
)

// @Tags AuthorityMenu
// @Summary 获取用户动态路由
// @Security ApiKeyAuth
// @Produce  application/json
// @Param data body request.Empty true "空"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /menu/getMenu [post]
func GetMenu(c *gin.Context) {
	if claims, exists := c.Get("claims"); !exists {

		fmt.Println("从Gin的Context中获取从jwt解析出来的用户UUID失败, 请检查路由是否使用jwt中间件")
	} else {
		waitUse := claims.(*request.CustomClaims)
		fmt.Println("权限id",waitUse.AuthorityId)
	}
	if err, menus := service.GetMenuTree(getUserAuthorityId(c)); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {

		yzResponse.OkWithDetailed(response.SysMenusResponse{Menus: menus}, "获取成功", c)
	}
}

// @Tags AuthorityMenu
// @Summary 获取用户动态路由
// @Security ApiKeyAuth
// @Produce  application/json
// @Param data body request.Empty true "空"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /menu/getBaseMenuTree [post]
func GetBaseMenuTree(c *gin.Context) {
	if err, menus := service.GetMenuTreeBase(); err != nil {
	//	if err, menus := service.GetBaseMenuTree(); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(response.SysMenusResponse{Menus: menus}, "获取成功", c)
	}
}

// @Tags AuthorityMenu
// @Summary 增加menu和角色关联关系
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.AddMenuAuthorityInfo true "角色ID"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"添加成功"}"
// @Router /menu/addMenuAuthority [post]
func AddMenuAuthority(c *gin.Context) {
	var authorityMenu request.AddMenuAuthorityInfo
	_ = c.ShouldBindJSON(&authorityMenu)
	if err := utils.GVerify(authorityMenu, utils.AuthorityIdVerify); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.AddMenuAuthorityTree(authorityMenu.Menus, authorityMenu.AuthorityId); err != nil {
		log.Log().Error("添加失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("添加失败", c)
		return
	} else {
		yzResponse.OkWithMessage("添加成功", c)
	}
}

// @Tags AuthorityMenu
// @Summary 获取指定角色menu
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetAuthorityId true "角色ID"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /menu/GetMenuAuthority [post]
func GetMenuAuthority(c *gin.Context) {
	var param request.GetAuthorityId
	_ = c.ShouldBindJSON(&param)
	if err := utils.GVerify(param, utils.AuthorityIdVerify); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, menus := service.GetMenuAuthority(&param); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		//yzResponse.FailWithDetailed(response.SysMenusResponse{Menus: menus}, "获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{"menus": menus}, "获取成功", c)
	}
}

// @Tags Menu
// @Summary 新增菜单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysBaseMenu true "路由path, 父菜单ID, 路由name, 对应前端文件路径, 排序标记"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"添加成功"}"
// @Router /menu/addBaseMenu [post]
func AddBaseMenu(c *gin.Context) {
	var menu model.SysBaseMenu
	_ = c.ShouldBindJSON(&menu)
	if err := utils.GVerify(menu, utils.MenuVerify); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := utils.GVerify(menu.Meta, utils.MenuMetaVerify); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.AddBaseMenu(menu); err != nil {
		log.Log().Error("添加失败!", zap.Any("err", err))

		yzResponse.FailWithMessage("添加失败", c)
		return
	} else {
		yzResponse.OkWithMessage("添加成功", c)
	}
}

// @Tags Menu
// @Summary 删除菜单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "菜单id"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /menu/deleteBaseMenu [post]
func DeleteBaseMenu(c *gin.Context) {
	var menu yzRequest.GetById
	_ = c.ShouldBindJSON(&menu)
	if err := utils.GVerify(menu, utils.IdVerify); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteBaseMenu(menu.Id); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// @Tags Menu
// @Summary 更新菜单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysBaseMenu true "路由path, 父菜单ID, 路由name, 对应前端文件路径, 排序标记"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /menu/updateBaseMenu [post]
func UpdateBaseMenu(c *gin.Context) {
	var menu model.SysBaseMenu
	_ = c.ShouldBindJSON(&menu)
	if err := utils.GVerify(menu, utils.MenuVerify); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := utils.GVerify(menu.Meta, utils.MenuMetaVerify); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateBaseMenu(menu); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

// @Tags Menu
// @Summary 根据id获取菜单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "菜单id"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /menu/getBaseMenuById [post]
func GetBaseMenuById(c *gin.Context) {
	var idInfo yzRequest.GetById
	_ = c.ShouldBindJSON(&idInfo)
	if err := utils.GVerify(idInfo, utils.IdVerify); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, menu := service.GetBaseMenuById(idInfo.Id); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(response.SysBaseMenuResponse{Menu: menu}, "获取成功", c)
	}
}

// @Tags Menu
// @Summary 分页获取基础menu列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.PageInfo true "页码, 每页大小"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /menu/getMenuList [post]
func GetMenuList(c *gin.Context) {
	var pageInfo yzRequest.PageInfo
	_ = c.ShouldBindJSON(&pageInfo)
	if err := utils.GVerify(pageInfo, utils.PageInfoVerify); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, menuList, total := service.GetInfoList(); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     menuList,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		},"获取成功", c)
	}
}