package route

import (
	"gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
)

func InitSystemRouter(Router *gin.RouterGroup) {
	SystemRouter := Router.Group("system")
	{
		SystemRouter.POST("getSystemConfig", v1.GetSystemConfig)                                 // 获取配置文件内容
		SystemRouter.POST("downLoadUpdateSupply", v1.DownLoadUpdateSupply)                       // 更新中台
		SystemRouter.POST("downLoadH5UpdateSupply", v1.DownLoadH5UpdateSupply)                   // 更新h5
		SystemRouter.POST("downLoadSmallShopH5UpdateSupply", v1.DownLoadSmallShopH5UpdateSupply) // 更新小商店h5
		SystemRouter.POST("killSupply", v1.KillSupply)                                           // 获取配置文件内容
		SystemRouter.POST("setSystemConfig", v1.SetSystemConfig)                                 // 设置配置文件内容
		SystemRouter.POST("getServerInfo", v1.GetServerInfo)                                     // 获取服务器信息
		SystemRouter.POST("updateHuiJuPaymentMask", v1.UpdateHuiJuPaymentMask)                   // 获取汇聚支付配置文件敏感内容
		SystemRouter.POST("updateWechatPaymentMask", v1.UpdateWechatPaymentMask)                 // 获取微信支付配置文件敏感内容
		SystemRouter.POST("updateLaKaLaPaymentMask", v1.UpdateLaKaLaPaymentMask)                 // 获取拉卡拉支付配置文件敏感内容
	}
}
