package service

import (
	"gin-vue-admin/admin/model"
	"yz-go/source"
	yzRequest "yz-go/request"
)

//@author: [piexlmax](https://github.com/piexlmax)
//@function: CreateExaCustomer
//@description: 创建客户
//@param: e model.ExaCustomer
//@return: err error

func CreateExaCustomer(e model.ExaCustomer) (err error) {
	err = source.DB().Create(&e).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: DeleteFileChunk
//@description: 删除客户
//@param: e model.ExaCustomer
//@return: err error

func DeleteExaCustomer(e model.ExaCustomer) (err error) {
	err = source.DB().Delete(&e).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: UpdateExaCustomer
//@description: 更新客户
//@param: e *model.ExaCustomer
//@return: err error

func UpdateExaCustomer(e *model.ExaCustomer) (err error) {
	err = source.DB().Save(e).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetExaCustomer
//@description: 获取客户信息
//@param: id uint
//@return: err error, customer model.ExaCustomer

func GetExaCustomer(id uint) (err error, customer model.ExaCustomer) {
	err = source.DB().Where("id = ?", id).First(&customer).Error
	return
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetCustomerInfoList
//@description: 分页获取客户列表
//@param: sysUserAuthorityID string, info yzRequest.PageInfo
//@return: err error, list interface{}, total int64

func GetCustomerInfoList(sysUserAuthorityID string, info yzRequest.PageInfo) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(&model.ExaCustomer{})
	var a model.SysAuthority
	a.AuthorityId = sysUserAuthorityID
	err, auth := GetAuthorityInfo(a)
	var dataId []string
	for _, v := range auth.DataAuthorityId {
		dataId = append(dataId, v.AuthorityId)
	}
	var CustomerList []model.ExaCustomer
	err = db.Where("sys_user_authority_id in ?", dataId).Count(&total).Error
	if err != nil {
		return err, CustomerList, total
	} else {
		err = db.Limit(limit).Offset(offset).Preload("SysUser").Where("sys_user_authority_id in ?", dataId).Find(&CustomerList).Error
	}
	return err, CustomerList, total
}
