package information

import (
	"github.com/gookit/color"
	"gorm.io/gorm"
	"yz-go/source"
)

var AuthoritiesMenus = new(authoritiesMenus)

type authoritiesMenus struct{}

type AuthorityMenus struct {
	AuthorityId string `gorm:"column:sys_authority_authority_id"`
	BaseMenuId  uint   `gorm:"column:sys_base_menu_id"`
}

var authorityMenus = []AuthorityMenus{
	{"8881", 1},
	{"8881", 2},
	{"8881", 8},
	{"9528", 1},
	{"9528", 2},
	{"9528", 3},
	{"9528", 4},
	{"9528", 5},
	{"9528", 6},
	{"9528", 7},
	{"9528", 8},
	{"9528", 9},
	{"9528", 10},
	{"9528", 11},
	{"9528", 12},
	{"9528", 14},
	{"9528", 15},
	{"9528", 16},
	{"9528", 17},
	{"110", 1},
}

func SetAdminBaseMenu() {

	for i := 1; i < 2500; i++ {
		if i>305 && i<400{
			continue
		}
		var menus AuthorityMenus
		menus.BaseMenuId = uint(i)
		menus.AuthorityId = "888"

		authorityMenus = append(authorityMenus, menus)
	}

	for i := 305; i < 400; i++ {

		var menus AuthorityMenus
		menus.BaseMenuId = uint(i)
		menus.AuthorityId = "110"

		authorityMenus = append(authorityMenus, menus)
	}

}

//@author: [SliverHorn](https://github.com/SliverHorn)
//@description: sys_authority_menus 表数据初始化
func (a *authoritiesMenus) Init() error {
	SetAdminBaseMenu()
	return source.DB().Table("sys_authority_menus").Transaction(func(tx *gorm.DB) error {
		if tx.Where("sys_authority_authority_id IN ('888', '8881', '9528')").Find(&[]AuthorityMenus{}).RowsAffected == 48 {
			color.Danger.Println("\n[Mysql] --> sys_authority_menus 表的初始数据已存在!")
			return nil
		}
		if err := tx.Create(&authorityMenus).Error; err != nil { // 遇到错误时回滚事务
			return err
		}
		color.Info.Println("\n[Mysql] --> sys_authority_menus 表初始数据成功!")
		return nil
	})
}
