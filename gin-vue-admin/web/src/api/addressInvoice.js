import service from '@/utils/request'
/*
 *@Tags  parsingAddress
 *@Router /address/parsingAddress
 *@Method  post
*/
export const parsingAddress = (data) => {
    return service({
        url: "/address/parsingAddress",
        method: "post",
        data
    })
}
/*
 *@Tags  addressList
 *@Router /address/list
 *@Method  post
*/
export const addressList = (data) => {
    return service({
        url: "/address/list",
        method: "post",
        data
    })
}
/*
 *@Tags  addressUpdate
 *@Router /address/update
 *@Method  post
*/
export const addressUpdate = (data) => {
    return service({
        url: "/api/address/update",
        method: "post",
        data
    })
}
/*
 *@Tags  addressAdd
 *@Router /address/add
 *@Method  post
*/
export const addressAdd = (data) => {
    return service({
        url: "/address/add",
        method: "post",
        data
    })
}
/*
 *@Tags  regionList
 *@Router /region/list
 *@Method  get
*/
export const regionList = (params) => {
    return service({
        url: "/api/region/list",
        method: "get",
        params
    })
}


