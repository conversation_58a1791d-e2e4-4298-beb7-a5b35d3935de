import service from '@/utils/request'

// @Tags supplierUserRecharge
// @Summary 查询supplierUserRecharge
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AccountApply true "查询supplierUserRecharge"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /adminSupplier/supplierUserRecharge [post]
export const supplierUserRecharge = (data) => {
    return service({
        url: "/adminSupplier/supplierUserRecharge",
        method: 'post',
        data
    })
}