/**
 * 获取类目 不带分页
 */
import service from '@/utils/request'

export const getClassify = (level, parent_id, name = "") => {
    return service({
        url: "/category/getCategorysWithLevelAndName",
        method: "get",
        params: {
            level,
            name,
            parent_id
        }
    })
}

/**
 * 复制获取类目 不带分页作用在权益商品选品 
 */
// export const getClassifySelect = (level, parent_id, name = "", is_plugin = 1, source = 127) => {
export const getClassifySelect = (level, parent_id, name = "", is_plugin = 1) => {
    return service({
        url: "/category/getCategorysWithLevelAndName",
        method: "get",
        params: {
            level,
            name,
            parent_id,
            is_plugin
        }
    })
}

/**
 * 复制获取类目 不带分页，非权益商品选品 
 */
// export const getClassifySelectIsPlugin = (level, parent_id, name = "", is_plugin = 0, source = 127) => {
    export const getClassifySelectIsPlugin = (level, parent_id, name = "", is_plugin = 0) => {
        return service({
            url: "/category/getCategorysWithLevelAndName",
            method: "get",
            params: {
                level,
                name,
                parent_id,
                is_plugin
            }
        })
    }


/**
 * 获取类目 (带分页)
 */
export const getCategoryListWithParentId = (params) => {
    return service({
        url: "/category/getCategoryList",
        method: 'get',
        params
    })
}

/**
 * 创建分类By Name
 */
 export const quickCreateCategoryByName = (params) => {
    return service({
        url: "/category/createCategory",
        method: 'post',
        params
    })
}


/**
 * 修改分类状态
 * @param data {"column":"is_hot","id":9527}
 */
 export const upCategoryStatus = (data) => {
    return service({
        url: "/category/displayCategoryByIds",
        method: "post",
        data
    })
}


/**
 * 推荐分类处理
 * @param 
 */
 export const putRecommendCategory = (data) => {
    return service({
        url: "/category/putRecommendCategory",
        method: "post",
        data
    })
}


/**
 * 推荐分类处理
 * @param 
 */
 export const getRecommendCategory = (data) => {
    return service({
        url: "/category/getRecommendCategory",
        method: "get",
        data
    })
}