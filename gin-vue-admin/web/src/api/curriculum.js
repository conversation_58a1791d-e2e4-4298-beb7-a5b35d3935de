import service from '@/utils/request';
/*
 *@Summary  获取基础设置
 *@Router   /curriculum/getBaseSetting
 *@Method   post
 *@Date    2023-03-17
 */
export const getBaseSetting = data => {
    return service({
        url: '/curriculum/getBaseSetting',
        method: 'post',
        data,
    });
};

/*
 *@Summary  保存基础设置
 *@Router   /curriculum/savaBaseSetting
 *@Method   post
 *@Date    2023-03-17
 */
export const savaBaseSetting = data => {
    return service({
        url: '/curriculum/savaBaseSetting',
        method: 'post',
        data,
    });
};

/*
 *@Summary 获取讲师分成列表
 *@Router  /lecturer/findLecturerAward
 *@Method  post
 *@Date    2023-03-21
 */
export const findLecturerAward = data => {
    return service({
        url: '/lecturer/findLecturerAward',
        method: 'post',
        data,
    });
};

/*
 *@Summary  获取供应链列表
 *@Router   /curriculum/supplyList
 *@Method   post
 *@Date    2023-03-22
 */
export const supplyList = data => {
    return service({
        url: '/curriculum/supplyList',
        method: 'post',
        data,
    });
};

/*
 *@Summary: 订单列表
 *@Router:  /curriculum/orderList
 *@Method:  post
 *@Date: 2023-03-27
 */
export const getList = data => {
    return service({
        url: '/curriculum/orderList',
        method: 'post',
        data,
    });
};
