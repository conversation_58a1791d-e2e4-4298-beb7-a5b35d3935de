import service from '@/utils/request'

// @Tags dashboard
// @Summary 获取 今日交易额 等数据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.dashboard true
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /dashboard/getDashboardIndex [get]
export const getDashboardIndex = () => {
    return service({
        url: "/dashboard/getDashboardIndex",
        method: 'get',
    })
}
// @Tags dashboard
// @Summary 获取折线图
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.dashboard true
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /dashboard/getOrderLineChart [get]
export const getOrderLineChart = (params) => {
    return service({
        url: "/dashboard/getOrderLineChart",
        method: 'get',
        params
    })
}

// @Tags dashboard
// @Summary 获取 服务器状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.dashboard true
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /dashboard/getServerStatus [get]
export const getServerStatus = () => {
    return service({
        url: "/dashboard/getServerStatus",
        method: 'get',
    })
}

// 供应商仪表板数据
export const getSupplierDashboardIndex = () => {
    return service({
        url: "/supplier/getDashboardIndex",
        method: "get"
    })
}
// 折线图数据
export const getSupplierOrderLineChart = () => {
    return service({
        url: "/supplier/getOrderLineChart",
        method: "get"
    })
}
// 获取系统运行情况
export const getStatus = ()=>{
    return service({
        url:"/monitor/getStatus",
        method:"get",
    })
}


// 获取获取会员统计，商品统计，供应商统计，商品销量排行，
export const getTotal = ()=>{
    return service({
        url:"/dashboard/getTotal",
        method:"get",
    })
}

// 采购排行
export const userProcurementRanking = (params)=>{
    return service({
        url:"/dashboard/userProcurementRanking",
        method:"get",
        params
    })
}

// 商品销量排行
export const productProcurementRanking = (params)=>{
    return service({
        url:"/dashboard/productProcurementRanking",
        method:"get",
        params
    })
}

// 新增会员 折线图
export const getAddUserLineChart = (params)=>{
    return service({
        url:"/dashboard/getAddUserLineChart",
        method:"get",
        params
    })
}

// 充值站内余额折线图
export const getPurchasingBalanceLineChart = (params)=>{
    return service({
        url:"/dashboard/getPurchasingBalanceLineChart",
        method:"get",
        params
    })
}

// 新增采购端数量折线图
export const getApplicationLineChart = (params)=>{
    return service({
        url:"/dashboard/getApplicationLineChart",
        method:"get",
        params
    })
}

// 新增商品数量折线图
export const getProductLineChart = (params)=>{
    return service({
        url:"/dashboard/getProductLineChart",
        method:"get",
        params
    })
}

// 新增商品统计昨日今日数据
export const productStatisticsData = (params)=>{
    return service({
        url:"/dashboard/productStatisticsData",
        method:"get",
        params
    })
}