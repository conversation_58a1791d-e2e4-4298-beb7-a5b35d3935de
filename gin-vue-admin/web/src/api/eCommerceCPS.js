import service from "@/utils/request";
/*
 *@Summary 保存设置
 *@Router  /ecCpsCtrl/saveSetting
 *@Method  post
 *@Date    2025-06-04
*/
export const saveSetting = (data) => {
    return service({
        url: "/ecCpsCtrl/saveSetting",
        method: "post",
        data
    })
}

/*
 *@Summary 获取设置
 *@Router  /ecCpsCtrl/getSetting
 *@Method  post
 *@Date    2025-06-04
*/
export const getSetting = (data) => {
    return service({
        url: "/ecCpsCtrl/getSetting",
        method: "post",
        data
    })
}

/*
 *@Summary 获取订单列表
 *@Router  /ecCpsCtrl/orderList
 *@Method  post
 *@Date    2025-06-04
*/
export const getOrderList = (data) => {
    return service({
        url: "/ecCpsCtrl/orderList",
        method: "post",
        data
    })
}

/*
 *@Summary 导出订单列表
 *@Router  /ecCpsCtrl/exportOrderList
 *@Method  post
 *@Date    2025-06-04
*/
export const exportOrderList = (data) => {
    return service({
        url: "/ecCpsCtrl/exportOrderList",
        method: "post",
        data
    })
}