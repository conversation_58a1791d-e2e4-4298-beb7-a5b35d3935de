import service from '@/utils/request'

// @Tags ExaFileGroup
// @Summary 创建ExaFileGroup
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ExaFileGroup true "创建ExaFileGroup"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /exaFileGroup/createExaFileGroup [post]
export const createExaFileGroup = (data) => {
     return service({
         url: "/exaFileGroup/createExaFileGroup",
         method: 'post',
         data
     })
 }


// @Tags ExaFileGroup
// @Summary 删除ExaFileGroup
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ExaFileGroup true "删除ExaFileGroup"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /exaFileGroup/deleteExaFileGroup [delete]
 export const deleteExaFileGroup = (data) => {
     return service({
         url: "/exaFileGroup/deleteExaFileGroup",
         method: 'delete',
         data
     })
 }

// @Tags ExaFileGroup
// @Summary 删除ExaFileGroup
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除ExaFileGroup"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /exaFileGroup/deleteExaFileGroup [delete]
 export const deleteExaFileGroupByIds = (data) => {
     return service({
         url: "/exaFileGroup/deleteExaFileGroupByIds",
         method: 'delete',
         data
     })
 }

// @Tags ExaFileGroup
// @Summary 更新ExaFileGroup
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ExaFileGroup true "更新ExaFileGroup"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /exaFileGroup/updateExaFileGroup [put]
 export const updateExaFileGroup = (data) => {
     return service({
         url: "/exaFileGroup/updateExaFileGroup",
         method: 'put',
         data
     })
 }


// @Tags ExaFileGroup
// @Summary 用id查询ExaFileGroup
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ExaFileGroup true "用id查询ExaFileGroup"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /exaFileGroup/findExaFileGroup [get]
 export const findExaFileGroup = (params) => {
     return service({
         url: "/exaFileGroup/findExaFileGroup",
         method: 'get',
         params
     })
 }


// @Tags ExaFileGroup
// @Summary 分页获取ExaFileGroup列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.PageInfo true "分页获取ExaFileGroup列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /exaFileGroup/getExaFileGroupList [get]
 export const getExaFileGroupList = (params) => {
     return service({
         url: "/exaFileGroup/getExaFileGroupList",
         method: 'get',
         params
     })
 }

 // @Tags ExaFileGroup
// @Summary 分页获取ExaFileGroup列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.PageInfo true "分页获取ExaFileGroup列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /exaFileGroup/getExaFileGroupList [get]
 export const getExaFileGroupListWithNum = (params) => {
     return service({
         url: "/exaFileGroup/getExaFileGroupListWithNum",
         method: 'get',
         params
     })
 }