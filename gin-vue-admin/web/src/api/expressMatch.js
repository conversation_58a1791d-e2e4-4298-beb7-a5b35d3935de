/*
 * @Author: jwt <EMAIL>
 * @Date: 2022-06-01 17:23:04
 * @LastEditors: jwt <EMAIL>
 * @LastEditTime: 2022-06-06 09:56:51
 * @FilePath: \demo\gin-vue-admin\web\src\api\expressMatch.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 微信接口
//import service from 'axios'
import service from '@/utils/request'

/** ********************************** 快递匹配列表部分 **************************************/

/** ****************** 快递公司部分 ********************/
/*
 *@Summary  查询
 *@Description  查询云仓快递公司列表
 *@Router  /cloud/getDeliverList?gather_supply_id=7
 *@Method  get
 *@Date  2021-06-01
*/
export const getDeliverList = (params) => {
  return service({
    url: '/cloud/getDeliverList',
    method: 'get',
    params
  })
  // return Promise.resolve({
  //   data: [
  //     {
  //       code: '1',
  //       name: '天天快递'
  //     },
  //     {
  //       code: '2',
  //       name: '中通快递'
  //     }
  //   ]
  // })
}

/*
 *@Summary  查询
 *@Description  查询中台快递公司列表
 *@Router  /shipping/company/list
 *@Method  post
 *@Date  2021-06-01
*/
export const getShippingList = () => {
  return service({
    url: 'shipping/company/list',
    method: 'post'
  })
  // return Promise.resolve({
  //   data: {
  //     list: [
  //       {
  //         code: '1',
  //         name: '京东快递'
  //       },
  //       {
  //         code: '2',
  //         name: '顺丰快递'
  //       }
  //     ]
  //   }

  // })
}

/** ****************** 快递公司结束 ********************/

/*
 *@Summary  查询
 *@Description  查询中台与云仓快递公司关联关系列表
 *@Router  /cloud/getMiddlegroundCloudExpressMatchingList
 *@Method  get
 *@Date  2021-06-01
*/
export const getMatchingList = (params) => {
  return service({
    url: '/cloud/getMiddlegroundCloudExpressMatchingList',
    method: 'get',
    params
  })
  // return Promise.resolve({
  //   data: {
  //     list: [
  //       {
  //         id: '1',
  //         code: '1',
  //         name: '天天快递',
  //         cloud_code: '1',
  //         cloud_name: '京东快递'
  //       },
  //       {
  //         id: '2',
  //         code: '2',
  //         name: '中通快递',
  //         cloud_code: '2',
  //         cloud_name: '顺丰快递'
  //       }
  //     ]
  //   }

  // })
}

/*
 *@Summary  新增
 *@Description  新增中台与云仓快递公司关联关系
 *@Router  /cloud/createMiddlegroundCloudExpressMatching
 *@Method  post
 *@Date  2021-06-01
*/
export const createMatching = (params) => {
  return service({
    url: '/cloud/createMiddlegroundCloudExpressMatching',
    method: 'post',
    data: params
  })
}

/*
 *@Summary  删除
 *@Description  删除中台与云仓快递公司关联关系
 *@Router  /cloud/deleteMiddlegroundCloudExpressMatching
 *@Method  post
 *@Date  2021-06-01
*/
export const deleteMatching = (params) => {
  return service({
    url: '/cloud/deleteMiddlegroundCloudExpressMatching',
    method: 'post',
    data: params
  })
}

/*
 *@Summary  修改
 *@Description  修改中台与云仓快递公司关联关系
 *@Router  /cloud/updateMiddlegroundCloudExpressMatching
 *@Method  post
 *@Date  2021-06-01
*/
export const updateMatching = (params) => {
    return service({
        url: "/cloud/updateMiddlegroundCloudExpressMatching",
        method: "post",
        data: params
    })
}

/*
 *@Summary  重新发货
 *@Description  重新发货
 *@Router  /cloud/getSendErrorOrder
 *@Method  post
 *@Date  2021-06-01
*/
export const getSend = (params) => {
  return service({
    url: '/cloud/getSendErrorOrder',
    method: 'post'
  })
}

/** ********************************** 快递匹配列表结束 **************************************/
