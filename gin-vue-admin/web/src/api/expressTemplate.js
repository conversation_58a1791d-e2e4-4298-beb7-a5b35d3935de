import service from '@/utils/request'
// 列表
export const getExpressTemplateList = (params) => {
    return service({
        url: "/shipping/getExpressTemplateList",
        method: "get",
        params
    })
}
// 删除
export const deleteExpressTemplate = (params) => {
    return service({
        url: "/shipping/deleteExpressTemplate",
        method: "delete",
        params
    })
}
// 创建
export const createExpressTemplate = (data) => {
    return service({
        url: "/shipping/createExpressTemplate",
        method: "post",
        data
    })
}
// 更新
export const updateExpressTemplate = (data) => {
    return service({
        url: "/shipping/updateExpressTemplate",
        method: "put",
        data
    })
}
// 单条查询
export const findExpressTemplate = (params) => {
    return service({
        url: "/shipping/findExpressTemplate",
        method: "get",
        params
    })
}