import service from '@/utils/request'

// @Tags member
// @Summary 获取会员列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.User true "创建Supplier"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /user/getUserList [get]
export const getUserList = (params) => {
    return service({
        url: "/user/getUserList",
        method: 'get',
        params
    })
}
export const getUserOptionList = (params) => {
    return service({
        url: "/user/getUserOptionList",
        method: 'get',
        params
    })
}
export const getUserList2 = (data) => {
    return service({
        url: "/purchase/getUserList",
        method: 'post',
        data
    })
}
/**
 * 删除member
 */
export const deleteUser = (params) => {
    return service({
        url: "/user/deleteUser",
        method: "delete",
        params
    })
}
/*
 *@Tags  member
 *@Summary  会员信息
 *@Description  根据id获取会员信息
 *@Router  /user/findUser
 *@Method  get
 *@Date  2021-05-08
*/
export const findUser = (params) => {
    return service({
        url: "/user/findUser",
        method: "get",
        params
    })
}
/**
 * 会员等级option
 */
export const getUserLevelOptionList = (params) => {
    return service({
        url: "/user/getUserLevelOptionList",
        method: "get",
        params
    })
}
/**
 * 会员等级列表
 */
export const getUserLevelList = (params) => {
    return service({
        url: "/user/getUserLevelList",
        method: "get",
        params
    })
}
/**
 * 新建会员等级
 */
export const createUserLevel = (data) => {
    return service({
        url: "/user/createUserLevel",
        method: "post",
        data
    })
}
/**
 * 删除会员等级
 */
export const deleteUserLevel = (params) => {
    return service({
        url: "/user/deleteUserLevel",
        method: "delete",
        params
    })
}
/**
 * 更新会员等级
 */
export const updateUserLevel = (data) => {
    return service({
        url: "/user/updateUserLevel",
        method: "put",
        data
    })
}
/**
 * 获取会员等级详情
 */
export const findUserLevel = (params) => {
    return service({
        url: "/user/findUserLevel",
        method: "get",
        params
    })
}

/**
 * 获取会员设置
 */
export const findSysTop = (params) => {
    return service({
        url: "/user/findSysTop",
        method: "get",
        params
    })
}
/**
 * 更新会员设置
 */
export const updateSysTop = (data) => {
    return service({
        url: "/user/updateSysTop",
        method: "put",
        data
    })
}
/**
 * 会员充值接口
 */
export const userRecharge = (data) => {
    return service({
        url: "/finance/userRecharge",
        method: "post",
        data
    })
}
/**
 * 新增会员
 */
export const createUser = (data) => {
    return service({
        url: "/user/createUser",
        method: "post",
        data
    })
}
/**
 * 更新会员
 */
export const updateUser = (data) => {
    return service({
        url: "/user/updateUser",
        method: "put",
        data
    })
}
/*
 *@Summary 搜索上级
 *@Router  /user/findUserByID
 *@Method  get
 *@Date    2021-12-08
*/
export const findUserByID = (params) => {
    return service({
        url: "/user/findUserByID",
        method: "get",
        params
    })
}
/*
 *@Summary 搜索上级
 *@Router  /user/findUserByIDOrUsername
 *@Method  get
 *@Date    2025-6-06
*/
export const findUserByIDOrUsername = (params) => {
    return service({
        url: "/user/findUserByIDOrUsername",
        method: "get",
        params
    })
}
/*
 *@Summary 修改上级
 *@Router  /user/updateUserParent
 *@Method  put
 *@Date    2021-12-08
*/
export const updateUserParent = (data) => {
    return service({
        url: "/user/updateUserParent",
        method: "put",
        data
    })
}
/*
 *@Summary 获取修改记录
 *@Router  /user/getUserParentLogList
 *@Method  get
 *@Date    2021-12-08
*/
export const getUserParentLogList = (params) => {
    return service({
        url: "/user/getUserParentLogList",
        method: "get",
        params
    })
}
/*
 *@Summary 搜索会员
 *@Router  /user/getUserListBySearch
 *@Method  get
 *@Date    2021-12-29
*/
export const getUserListBySearch = (params) => {
    return service({
        url:"/user/getUserListBySearch",
        method:"get",
        params
    })
}


export const getDouyinCpsStatus = (params) => {
    return service({
        url:"/user/getDouyinCpsStatus",
        method:"get",
        params
    })
}
export const verifyPayment = (data) => {
    return service({
        url:"/sysUser/verifyPayment",
        method:"post",
        data
    })
}

/*
 *@Summary 获取开通记录
 *@Router  /user/getPurchaseRecordList
 *@Method  get
 *@Date    2023-10-16
*/
export const getPurchaseRecordList = (params) => {
    return service({
        url:"/user/getPurchaseRecordList",
        method:"get",
        params
    })
}

/*
 *@Summary 退款
 *@Router  /user/order/orderRefund
 *@Method  post
 *@Date    2023-10-16
*/
export const orderRefund = (data) => {
    return service({
        url:"/order/orderRefund",
        method:"post",
        data
    })
}

// 获取会员分组列表
export const getUserGroupList = (params) => {
    return service({
        url: "/user/getUserGroupList",
        method: 'get',
        params
    })
}

// 新增会员分组
export const createUserGroup = (data) => {
    return service({
        url: "/user/createUserGroup",
        method: 'post',
        data
    })
}

// 更新会员分组
export const updateUserGroup = (data) => {
    return service({
        url: "/user/updateUserGroup",
        method: 'put',
        data
    })
}

// 删除会员分组
export const deleteUserGroup = (data) => {
    return service({
        url: "/user/deleteUserGroup",
        method: 'delete',
        data
    })
}


// 分组选择会员
export const pickUsers = (data) => {
    return service({
        url: "/user/pickUsers",
        method: 'post',
        data
    })
}

// 分组查会员的回显数据
export const findUserGroup = (params) => {
    return service({
        url: "/user/findUserGroup",
        method: 'get',
        params
    })
}

// 会员查分组的回显数据 
export const pickUserGroups = (data) => {
    return service({
        url: "/user/pickUserGroups",
        method: 'post',
        data
    })
}


// 查询会员全部分组 getUserGroups 
export const getUserGroups = (params) => {
    return service({
        url: "/user/getUserGroups",
        method: 'get',
        params
    })
}


// 筛选会员分组里的会员
export const getUserGroupUserList = (data) => {
    return service({
        url: "/user/getUserGroupUserList",
        method: 'post',
        data
    })
}


// 删除会员分组里的会员
export const pickUsersDelete = (data) => {
    return service({
        url: "/user/pickUsersDelete",
        method: 'post',
        data
    })
}

// 会员导出
export const exportUserList = (params) => {
    return service({
        url: "/user/exportUserList",
        method: 'get',
        params
    })
}