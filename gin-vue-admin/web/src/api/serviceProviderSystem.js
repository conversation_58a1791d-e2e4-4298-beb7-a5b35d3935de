import service from '@/utils/request'
//.获取基础设置
export const getSysServiceProviderSystemSetting = (params) => {
    return service({
        url: "/serviceProviderSystem/getSysServiceProviderSystemSetting",
        method: "get",
        params
    })
}
//保存基础设置
export const saveSysServiceProviderSystemSetting = (data) => {
    return service({
        url: "/serviceProviderSystem/saveSysServiceProviderSystemSetting",
        method: "post",
        data
    })
}
// 推送用户列表
export const serviceProviderSystemGetPushUserList = (data) => {
    return service({
        url: "/serviceProviderSystem/getPushUserList",
        method: "post",
        data
    })
}
//推送用户
export const serviceProviderSystemPushUser = (data) => {
    return service({
        url: "/serviceProviderSystem/pushUser",
        method: "post",
        data
    })
}
//待推送数量
export const serviceProviderSystemAllPushUserTotal = (data) => {
    return service({
        url: "/serviceProviderSystem/allPushUserTotal",
        method: "post",
        data
    })
}
//一键推送用户
export const serviceProviderSystemAllPushUser = (data) => {
    return service({
        url: "/serviceProviderSystem/allPushUser",
        method: "post",
        data
    })
}
// 推送订单列表
export const serviceProviderSystemGetPushOrderList = (data) => {
    return service({
        url: "/serviceProviderSystem/getPushOrderList",
        method: "post",
        data
    })
}
//一键推送订单
export const allPushOrder = (data) => {
    return service({
        url: "/serviceProviderSystem/allPushOrder",
        method: "post",
        data
    })
}
export const serviceProviderSystemAllPushOrder = (data) => {
    return service({
        url: "/serviceProviderSystem/pushOrder",
        method: "post",
        data
    })
}

/*
 *@Summary 获取数据通系统会员等级
 *@Router  memberSync/getLevelMembers
 *@Method  get
 *@Date    2024-10-15
*/
export const getLevelMembers = (params) => {
    return service({
        url: "/memberSync/getLevelMembers",
        method: "get",
        params
    })
}
/*
 *@Summary 获取等级基础设置
 *@Router  memberSync/getMemberLevelRelation
 *@Method  get
 *@Date    2024-10-15
*/
export const getMemberLevelRelation = (params) => {
    return service({
        url: "/memberSync/getMemberLevelRelation",
        method: "get",
        params
    })
}
/*
 *@Summary 保存会员等级设置
 *@Router  memberSync/setMemberLevelRelation
 *@Method  post
 *@Date    2024-10-15
*/
export const setMemberLevelRelation = (data) => {
    return service({
        url: "/memberSync/setMemberLevelRelation",
        method: "post",
        data
    })
}

/*
 *@Summary 获取区分等级同步提示文字
 *@Router  memberSync/getIsThird
 *@Method  get
 *@Date    2024-10-15
*/
export const getIsThird = (params) => {
    return service({
        url: "/memberSync/getIsThird",
        method: "get",
        params
    })
}
/*
 *@Summary 消息日志
 *@Router  memberSync/getMemberOperationRecordList
 *@Method  get
 *@Date    2024-10-26
*/
export const getMemberOperationRecordList = (params) => {
    return service({
        url: "/memberSync/getMemberOperationRecordList",
        method: "get",
        params
    })
}