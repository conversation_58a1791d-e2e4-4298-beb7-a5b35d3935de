import service from '@/utils/request'
// ### 获取基础设置
// GET {{api}}/smallShop/findSetting

export const findSetting = (params) => {
    return service({
        url: "/smallShop/findSetting",
        method: 'get',
        params
    })
}
// ### 获取支付方式
// GET {{api}}/smallShop/getPayTypes

export const getPayTypes = (params) => {
    return service({
        url: "/smallShop/getPayTypes",
        method: 'get',
        params
    })
}
// ### 获取会员等级列表
// GET {{api}}/user/getUserLevelListNotPage
export const getUserLevelListNotPage = (params) => {
    return service({
        url: "/user/getUserLevelListNotPage",
        method: 'get',
        params
    })
}
// ### 更新基础设置
// PUT {{api}}/smallShop/updateSetting

export const updateSetting = (data) => {
    return service({
        url: "/smallShop/updateSetting",
        method: "put",
        data
    })
}
//4
//
// ### 获取小程序设置
// GET {{api}}/smallShop/findWxSetting
export const findWxSetting = (params) => {
    return service({
        url: "/smallShop/findWxSetting",
        method: 'get',
        params
    })
}

// ### 修改小程序设置
// PUT {{api}}/smallShop/updateWxSetting
export const updateWxSetting = (data) => {
    return service({
        url: "/smallShop/updateWxSetting",
        method: "put",
        data
    })
}

// ### 获取小商店管理
// GET {{api}}/smallShop/getSmallShopsList
export const getSmallShopsList = (params) => {
    return service({
        url: "/smallShop/getSmallShopsList",
        method: 'get',
        params
    })
}
// ### 获取收益管理
// GET {{api}}/smallShop/getAwardsList
export const getAwardsList = (params) => {
    return service({
        url: "/smallShop/getAwardsList",
        method: 'get',
        params
    })
}

// ### 获取申请管理
// GET {{api}}/smallShop/getAppliersList

export const getAppliersList = (params) => {
    return service({
        url: "/smallShop/getAppliersList",
        method: 'get',
        params
    })
}
// ### 审核通过
// PUT {{api}}/smallShop/passApply

export const passApply = (data) => {
    return service({
        url: "/smallShop/passApply",
        method: "put",
        data
    })
}
// ### 驳回审核
// PUT {{api}}/smallShop/failApply
export const failApply = (data) => {
    return service({
        url: "/smallShop/failApply",
        method: "put",
        data
    })
}

// ### 获取店主管理
// GET {{api}}/smallShop/getShopkeepersList

export const getShopkeepersList = (params) => {
    return service({
        url: "/smallShop/getShopkeepersList",
        method: 'get',
        params
    })
}
//
// ### 获取小商店会员列表
// GET {{api}}/smallShop/getUserList


export const getUserList = (params) => {
    return service({
        url: "/smallShop/getUserList",
        method: 'get',
        params
    })
}
// ### 加入黑名单
// PUT {{api}}/smallShop/changeBlack

export const changeBlack = (data) => {
    return service({
        url: "/smallShop/changeBlack",
        method: "put",
        data
    })
}

// ### 加入白名单
// PUT {{api}}/smallShop/changeWhite

export const changeWhite = (data) => {
    return service({
        url: "/smallShop/changeWhite",
        method: "put",
        data
    })
}
// ### 获取用户信息
// GET {{api}}/smallShop/getUserInfo?id=1
export const getUserInfo = (params) => {
    return service({
        url: "/smallShop/getUserInfo",
        method: 'get',
        params
    })
}
//
// ### 开启小商店
// PUT {{api}}/smallShop/shopOpen

export const shopOpen = (data) => {
    return service({
        url: "/smallShop/shopOpen",
        method: "put",
        data
    })
}

// ### 关闭小商店
// PUT {{api}}/smallShop/shopClose

export const shopClose = (data) => {
    return service({
        url: "/smallShop/shopClose",
        method: "put",
        data
    })
}

// ### 获取订单列表
// GET {{api}}/smallShop/getOrderList

export const getOrderList = (params) => {
    return service({
        url: "/smallShop/getOrderList",
        method: 'get',
        params
    })
}
//获取供应链
export const getSupplyList = (data) => {
    return service({
        url: "/gatherSupply/optionList",
        method: "post",
        data
    })
}
//获取供应商
export const getSupplierOptionList = (params) => {
    return service({
        url: "/supplier/getSupplierOptionList",
        method: "get",
        params
    })
}
// 获取订单支付类型
export const getPaymentType = (params) => {
    return service({
        url: "/finance/getPayType",
        method: "get",
        params
    })
}
// 导出
export const orderExport = (params) => {
    return service({
        url: "/smallShop/exportOrderList",
        method: "get",
        params
    })
}

// ### 获取全部小商店
// GET {{api}}/smallShop/getAllSmallShop

export const getAllSmallShop = (params) => {
    return service({
        url: "/smallShop/getAllSmallShop",
        method: "get",
        params
    })
}
//
// ### 获取订单详情
// GET {{api}}/smallShop/getOrderDetail?id=2

export const getOrderDetail = (params) => {
    return service({
        url: "/smallShop/getOrderDetail",
        method: "get",
        params
    })
}

// ### 修改订单收货信息
// POST {{api}}/smallShop/updateShippingAddress
export const updateShippingAddress = (data) => {
    return service({
        url: "/smallShop/updateShippingAddress",
        method: "post",
        data
    })
}

// ### 获取订单收货地址修改记录
// GET {{api}}/smallShop/getShippingAddressLog?order_id=2

export const getShippingAddressLog = (params) => {
    return service({
        url: "/smallShop/getShippingAddressLog",
        method: "get",
        params
    })
}

// ### 支付记录
// POST {{api}}/smallShop/finance/getPaymentRecord

export const getPaymentRecord = (data) => {
    return service({
        url: "/smallShop/finance/getPaymentRecord",
        method: "post",
        data
    })
}
//备注
export const addNote = (data) => {
    return service({
        url: "/smallShop/order/note",
        method: 'post',
        data
    })
}

//确认付款
export const orderPay = (data) => {
    return service({
        url: "smallShop/order/pay",
        method: 'post',
        data
    })
}
//关闭订单

export const orderClose = (data) => {
    return service({
        url: "smallShop/order/close",
        method: 'post',
        data
    })
}
/*
 *@Summary 获取所有店主options
 *@Router  /smallShop/getSmallShopListBySetting
 *@Method  get
 *@Date    2023-01-31
*/
export const getSmallShopListBySetting = (params) => {
    return service({
        url: "/smallShop/getSmallShopListBySetting",
        method: "get",
        params
    })
}

/*
 *@Summary 获取售后详情
 *@Router  /smallShop/afterSales/findByOrderItemId
 *@Method  get
 *@Date    2023-03-09
*/
export const findByOrderItemId = (params) => {
    return service({
        url:"/smallShop/afterSales/findByOrderItemId",
        method:"get",
        params
    })
}

/*
 *@Summary 查询协议设置
 *@Router  /smallShop/findProtocolSetting
 *@Method  get
 *@Date    2023-11-09
*/
export const findProtocolSetting = (params) => {
    return service({
        url:"/smallShop/findProtocolSetting",
        method:"get",
        params
    })
}

/*
 *@Summary 更新协议设置
 *@Router  /smallShop/updateProtocolSetting
 *@Method  put
 *@Date    2023-11-09
*/
export const updateProtocolSetting = (data) => {
    return service({
        url:"/smallShop/updateProtocolSetting",
        method:"put",
        data
    })
}

// 获取小商店拉卡拉支付设置
export const getLakalaSetting = () => {
    return service({
        url: "/lakala/getSetting",
        method: "get"
    })
}

// 保存小商店拉卡拉支付设置
export const setLakalaSetting = (data) => {
    return service({
        url: "/lakala/setSetting",
        method: "post",
        data
    })
}


// 获取小商店物流信息
export const getDeliveryListWxMiniSmallShop = (data) => {
    return service({
        url: "/smallShop/getDeliveryListWxMini",
        method: "post",
        data
    })
}

// 确认提交小商店的发货信息推送
export const uploadShippingInfoSmallShop = (data) => {
    return service({
        url: "/smallShop/uploadShippingInfo",
        method: "post",
        data
    })
}

// 创建小商店
export const createSmallShop = (data) => {
    return service({
        url: "/smallShop/createSmallShop",
        method: "post",
        data
    })
}
// 获取小商店底部导航列表
export const findSmallShopNavigationSetting = () => {
    return service({
        url: "/smallShop/findSmallShopNavigationSetting",
        method: "get" 
    })
}

// 修改小商店底部导航
export const updateSmallShopNavigationSetting = (data) => {
    return service({
        url: "/smallShop/updateSmallShopNavigationSetting",
        method: "put",
        data
    })
}

// 获取小商店汇聚支付设置
export const getConvergenceSetting = (params) => {
    return service({
        url: "/smallShop/getConvergenceSetting",
        method: "get",
        params
    })
}

// 保存小商店汇聚支付设置
export const SetConvergenceSetting = (data) => {
    return service({
        url: "/smallShop/setConvergenceSetting",
        method: "post",
        data
    })
}

// 小商店订单退款
export const orderItemRefund = (data) => {
    return service({
        url: "/smallShop/order/itemRefund",
        method: "post",
        data
    })
}

// 商品池-商品池的商品列表
export const getProductListByPond = (params) => {
    return service({
        url: "/smallShop/product/getProductListByPond",
        method: "get",
        params
    })
}

// 商品池-增加商品池的商品列表
export const getProductListByPondAdd = (params) => {
    return service({
        url: "/smallShop/product/getProductListByPondAdd",
        method: "get",
        params
    })
}

// 商品池-增加商品池的商品列表
export const addPond = (data) => {
    return service({
        url: "/smallShop/product/addPond",
        method: "post",
        data
    })
}

// 商品池-添加筛选商品
export const addPondBySearch = (data) => {
    return service({
        url: "/smallShop/product/addPondBySearch",
        method: "post",
        data
    })
}

// 商品池-移除or批量移除
export const productRemovePond = (data) => {
    return service({
        url: "/smallShop/product/removePond",
        method: "post",
        data
    })
}

// 指定专辑-专辑列表
export const getAlbumListByPond = (params) => {
    return service({
        url: "/smallShop/productAlbum/getAlbumListByPond",
        method: "get",
        params
    })
}

// 指定专辑-增加专辑列表
export const getAlbumListByPondAdd = (params) => {
    return service({
        url: "/smallShop/productAlbum/getAlbumListByPondAdd",
        method: "get",
        params
    })
}

// 指定专辑-增加专辑列表
export const productAlbumAddPond = (data) => {
    return service({
        url: "/smallShop/productAlbum/addPond",
        method: "post",
        data
    })
}

// 指定专辑-移除or批量移除
export const removePond = (data) => {
    return service({
        url: "/smallShop/productAlbum/removePond",
        method: "post",
        data
    })
}

// 指定专辑-添加筛选专辑
export const productAlbumAddSearch = (data) => {
    return service({
        url: "/smallShop/productAlbum/addPondBySearch",
        method: "post",
        data
    })
}

// 获取腾讯地图key
export const findTencentMapSetting = (params) => {
    return service({
        url: "/smallShop/findTencentMapSetting",
        method: "get",
        params
    })
}

// 保存腾讯地图key
export const updateTencentMapSetting = (data) => {
    return service({
        url: "/smallShop/updateTencentMapSetting",
        method: "put",
        data
    })
}

// 导出小商店会员
export const exportUserList = (data) => {
    return service({
        url: "/smallShop/exportUserList",
        method: "post",
        data
    })
}

// 获取公众号设置
export const findWechatOfficial = (params) => {
    return service({
        url: "/smallShop/findWechatOfficial",
        method: "get",
        params
    })
}

// 修改公众号设置
export const updateWechatOfficial = (data) => {
    return service({
        url: "/smallShop/updateWechatOfficial",
        method: "put",
        data
    })
}

// 重置AppSecret
export const resetAppSecret = (data) => {
    return service({
        url: "/smallShop/resetAppSecret",
        method: "post",
        data
    })
}

// 获取按钮 
export const getMenuButton = (params) => {
    return service({
        url: "/smallShop/getMenuButton",
        method: "get",
        params
    })
}

// 设置按钮
export const setMenuButton = (data) => {
    return service({
        url: "/smallShop/setMenuButton",
        method: "post",
        data
    })
}

// 上传微信公众号素材
export const uploadFileWechat = (data) => {
    return service({
        url: "/smallShop/uploadFileWechat",
        method: "post",
        data
    })
}

// 获取资源  （微信公众号API）
export const getMaterial = (params) => {
    return service({
        url: "/smallShop/getMaterial",
        method: "get",
        params
    })
}

// 删除资源
export const delMaterial = (data) => {
    return service({
        url: "/smallShop/delMaterial",
        method: "post",
        data
    })
}

// 基础设置 手动同步用户
export const syncUser = (data) => {
    return service({
        url: "/smallShop/syncUser",
        method: "post",
        data
    })
}

// 指定选品-列表
export const getSelectedList = (params) => {
    return service({
        url: "/smallShop/product/getSelectedList",
        method: "get",
        params
    })
}

// 指定选品-增加选品的商品列表
export const getProductListBySelectedAdd = (params) => {
    return service({
        url: "/smallShop/product/getProductListBySelectedAdd",
        method: "get",
        params
    })
}

// 指定选品-增加选品
export const addSelected = (data) => {
    return service({
        url: "/smallShop/product/addSelected",
        method: "post",
        data
    })
}

// 指定选品-移除or批量移除
export const removeSelected = (data) => {
    return service({
        url: "/smallShop/product/removeSelected",
        method: "post",
        data
    })
}

// 指定选品-单个同步
export const syncSelected = (data) => {
    return service({
        url: "/smallShop/product/syncSelected",
        method: "post",
        data
    })
}

// 指定选品-批量同步
export const syncSelectedBatch = (data) => {
    return service({
        url: "/smallShop/product/syncSelectedBatch",
        method: "post",
        data
    })
}

// 获取聚合支付设置
export const findSmallShopAggregatedPaymentSetting = (params) => {
    return service({
        url: "/smallShop/findSmallShopAggregatedPaymentSetting",
        method: "get",
        params
    })
}

// 保存聚合支付设置
export const updateSmallShopAggregatedPaymentSetting = (data) => {
    return service({
        url: "/smallShop/updateSmallShopAggregatedPaymentSetting",
        method: "put",
        data
    })
}

// 获取微信支付方式（h5公众号）设置
export const findWechatPaymentSetting = (params) => {
    return service({
        url: "/smallShop/findWechatPaymentSetting",
        method: "get",
        params
    })
}

// 获取微信支付方式（h5公众号）设置
export const updateWechatPaymentSetting = (data) => {
    return service({
        url: "/smallShop/updateWechatPaymentSetting",
        method: "put",
        data
    })
}

// 微信支付方式（h5公众号）设置 重置
export const updateWechatPaymentMask = (data) => {
    return service({
        url: "/smallShop/updateWechatPaymentMask",
        method: "post",
        data
    })
}

// 微信支付方式（h5公众号）设置 重置
export const getPayTypesByH5 = (params) => {
    return service({
        url: "/smallShop/getPayTypesByH5",
        method: "get",
        params
    })
}

// 微信支付方式（h5公众号）下载平台证书
export const downloaderWechatCert = (data) => {
    return service({
        url: "/smallShop/downloaderWechatCert",
        method: "post",
        data
    })
}
