import service from '@/utils/request'

// @Tags initApplicationSource
// @Summary 同步来源
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AccountApply true "同步来源initApplicationSource"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /adminSupplier/createAccountApply [post]
export const initApplicationSource = (data) => {
    return service({
        url: "/gatherSupply/initApplicationSource",
        method: 'post',
        data
    })
}

// @Tags applicationSourceList
// @Summary 来源列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AccountApply true "来源列表applicationSourceList"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /adminSupplier/createAccountApply [post]
export const applicationSourceList = (data) => {
  return service({
      url: "/gatherSupply/applicationSourceList",
      method: 'post',
      data
  })
}

// @Tags updateApplicationSource
// @Summary 修改来源自定义名称
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AccountApply true "修改来源自定义名称updateApplicationSource"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /adminSupplier/createAccountApply [post]
export const updateApplicationSource = (data) => {
  return service({
      url: "/gatherSupply/updateApplicationSource",
      method: 'post',
      data
  })
}