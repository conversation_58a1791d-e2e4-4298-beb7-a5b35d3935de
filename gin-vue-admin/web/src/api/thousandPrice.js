import service from '@/utils/request'

// 获取千人千价商品池列表
export const getThousandPricesList = (params) => {
  return service({
    url: "/thousandsPrices/list",
    method: "get",
    params
  })
}

// 创建千人千价商品池
export const createThousandPrices = (data) => {
  return service({
    url: "/thousandsPrices/create",
    method: "post",
    data
  })
}

// 保存商品池设置
export const getThousandPrices = (params) => {
  return service({
    url: "/thousandsPrices/detail",
    method: "get",
    params
  })
}

// 创建采购端会员
export const createUser = (data) => {
  return service({
    url: "/thousandsPrices/user/create",
    method: "post",
    data
  })
}

// 删除采购端会员
export const deleteUser = (data) => {
  return service({
    url: "/thousandsPrices/user/delete",
    method: "post",
    data
  })
}

// 添加商品
export const createProduct = (data) => {
  return service({
    url: "/thousandsPrices/product/create",
    method: "post",
    data
  })
}

// 获取商品列表
export const getProductList = (params) => {
  return service({
    url: "/thousandsPrices/product/list",
    method: "get",
    params
  })
}

// 获取商品详情
export const getProductDetail = (params) => {
  return service({
    url: "/thousandsPrices/product/detail",
    method: "get",
    params
  })
}

// 修改商品详情
export const updateProduct = (data) => {
  return service({
    url: "/thousandsPrices/product/update",
    method: "post",
    data
  })
}

// 删除商品
export const delProduct = (data) => {
  return service({
    url: "/thousandsPrices/product/delete",
    method: "post",
    data
  })
}

// 保存修改的千人千价信息
export const updateThousandPrices = (data) => {
  return service({
    url: "/thousandsPrices/update",
    method: "post",
    data
  })
}

// 添加供应商
export const createSupplier = (data) => {
  return service({
    url: "/thousandsPrices/supplier/create",
    method: "post",
    data
  })
}

// 获取供应商列表
export const getSuppliersList = (params) => {
  return service({
    url: "/thousandsPrices/supplier/list",
    method: "get",
    params
  })
}

// 修改定价系数
export const updateSupplier = (data) => {
  return service({
    url: "/thousandsPrices/supplier/update",
    method: "post",
    data
  })
}

// 删除供应商
export const delSupplier = (params) => {
  return service({
    url: "/thousandsPrices/supplier/delete",
    method: "get",
    params
  })
}

// 获取分类定价列表
export const getCategoryList = (params) => {
  return service({
    url: "/thousandsPrices/category/list",
    method: "get",
    params
  })
}

// 创建分类
export const createCategory = (data) => {
  return service({
    url: "/thousandsPrices/category/create",
    method: "post",
    data
  })
}

// 修改分类的定价系数
export const updateCategory = (data) => {
  return service({
    url: "/thousandsPrices/category/update",
    method: "post",
    data
  })
}

// 删除分类
export const delCategory = (params) => {
  return service({
    url: "/thousandsPrices/category/delete",
    method: "get",
    params
  })
}

// 修改商品池状态
export const updateMange = (params) => {
  return service({
    url: "/thousandsPrices/display",
    method: "get",
    params
  })
}

// 删除商品池列表
export const delMange = (params) => {
  return service({
    url: "/thousandsPrices/delete",
    method: "get",
    params
  })
}

// 复制商品池列表
export const copyMange = (params) => {
  return service({
    url: "/thousandsPrices/copy",
    method: "get",
    params
  })
}

// 导出商品池列表
export const exportMange = (params) => {
  return service({
    url: "/thousandsPrices/export",
    method: "get",
    params
  })
}

// 更新商品池价格
export const updatePrice = (data) => {
  return service({
    url: "/thousandsPrices/product/updateAll",
    method: "post",
    data
  })
}

// 点击查看商品池商品数量
export const productCount = (params) => {
  return service({
    url: "/thousandsPrices/productCount",
    method: "get",
    params
  })
}

// 订单金额补正
export const execOrderFix = (data) => {
  return service({
    url: "/order/execOrderFix",
    method: "post",
    data
  })
}

// 商品池导出列表
export const exportThousandsPricesRecordList = (params) => {
  return service({
    url: "/thousandsPrices/exportThousandsPricesRecordList",
    method: "get",
    params
  })
}

// 删除商品池导出列表
export const deleteThousandPriceExportRecord = (data) => {
  return service({
    url: "/thousandsPrices/deleteThousandPriceExportRecord",
    method: "delete",
    data
  })
}

// 商品池 获取商品规格变动列表
export const missSkusList = (data) => {
  return service({
    url: "/thousandsPrices/missSkus/list",
    method: "post",
    data
  })
}

// 商品池 商品规格变动列表 标记已读/批量标记已读
export const missSkusMark = (data) => {
  return service({
    url: "/thousandsPrices/missSkus/mark",
    method: "post",
    data
  })
}