import service from '@/utils/request'
/*
 *@Summary 获取支付方式
 *@Router  /fuluSupply/findSetting
 *@Method  get
 *@Date  2022-01-29
*/
export const getPayType = (params) => {
    return service({
        url: "/userEquity/getPayType",
        method: "get",
        params
    })
}
/*
 *@Summary 获取会员权益设置
 *@Router  /fuluSupply/findUserEquitySetting
 *@Method  get
 *@Date    2023-02-10
*/
export const findUserEquitySetting = (params) => {
    return service({
        url: "/userEquity/findSetting",
        method: "get",
        params
    })
}
/*
 *@Summary 更新会员权益设置
 *@Router  /fuluSupply/updateUserEquitySetting
 *@Method  put
 *@Date    2023-02-10
*/
export const updateUserEquitySetting = (data) => {
    return service({
        url: "/userEquity/updateSetting",
        method: "put",
        data
    })
}

export const getProductList = (params) => {
    return service({
        url: "/userEquity/getProductList",
        method: "get",
        params
    })
}

export const syncProduct = (data) => {
    return service({
        url: "/userEquity/syncProduct",
        method: "post",
        data
    })
}
export const getOrderList = (params) => {
    return service({
        url: "/userEquity/orderList",
        method: "get",
        params
    })
}
export const orderExport = (params) => {
    return service({
        url: "/userEquity/orderExport",
        method: "get",
        params
    })
}
export const getSupplyID = (params) => {
    return service({
        url: "/userEquity/getSupplyID",
        method: "get",
        params
    })
}

//提交订单
export const repairOrder = (data) => {
    return service({
        url: "/userEquity/repairOrder",
        method: "post",
        data
    })
}

// 锁定
export const lockProduct = (data) => {
    return service({
        url: "/userEquity/lockProduct",
        method: "post",
        data
    })
}
// 解锁
export const relieveLockProduct = (data) => {
    return service({
        url: "/userEquity/relieveLockProduct",
        method: "post",
        data
    })
}