import service from '@/utils/request';

/*
 *@Summary 批量更新商品库存
 *@Router  /wdtShop/updateImportGoods
 *@Method  post
 *@Date    2025-06-11
*/
export const updateImportGoods = (data)=>{
    return service({
        url:"/wdtShop/updateImportGoods",
        method:"post",
        data
    })
}

/*
 *@Summary 旺店通店铺配置列表
 *@Router  /wdtShop/list
 *@Method  post
 *@Date    2025-06-11
*/
export const list = (data) => {
    return service({
        url: "/wdtShop/listAll",
        method: "post",
        data
    })
}

/*
 *@Summary 旺店通店铺配置列表
 *@Router  /wdtShop/list
 *@Method  post
 *@Date    2025-06-11
*/
export const lists = (data) => {
    return service({
        url: "/wdtShop/list",
        method: "post",
        data
    })
}

/*
 *@Summary 旺店通店铺新增配置
 *@Router  /wdtShop/create
 *@Method  post
 *@Date    2025-06-11
*/
export const create = (data) => {
    return service({
        url: "/wdtShop/create",
        method: "post",
        data
    })
}

/*
 *@Summary 旺店通店铺删除配置
 *@Router  /wdtShop/delete
 *@Method  post
 *@Date    2025-06-11
*/
export const deleteWdt = (data) => {
    return service({
        url: "/wdtShop/delete",
        method: "post",
        data
    })
}