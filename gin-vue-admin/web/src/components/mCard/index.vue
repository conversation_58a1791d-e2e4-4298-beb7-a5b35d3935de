<template>
  <div class="m-card">
    <div v-if="titIsShow || title" class="f fac title-div">
      <div class="xian"></div>
      <slot name="title">
        <p>{{ title }}</p>
      </slot>
    </div>
    <div class="cont-div">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "mCardIndex",
  props: {
    title: {
      type: String,
      default: () => {
        return "";
      },
    },
    titIsShow: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/style/base.scss";

.m-card {
  background-color: white;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 100);
  // border-radius: 10px;

  .title-div {
    margin-left: 0;
    margin-right: 25px;
    margin-bottom: 30px;

    .xian {
      width: 4px;
      height: 20px;
      background-color: $primary-color;
      margin-right: 5px;
    }
  }
}
</style>
