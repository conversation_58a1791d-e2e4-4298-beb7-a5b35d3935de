<!-- 上传图片dialog -->
<template>
  <el-dialog
    title="上传图片"
    :visible="isShow"
    width="40%"
    :modal-append-to-body="false"
    :append-to-body="true"
    :before-close="handleClose"
  >
    <el-form :model="uploadForm" label-width="90px" ref="form" :rules="rules">
      <el-form-item label="上传方式:" prop="uploadType">
        <el-radio-group v-model="uploadForm.uploadType">
          <el-radio :label="1">本地上传</el-radio>
          <el-radio :label="2">网络图片</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="所在分组:" prop="selectGroup">
        <div class="f fac">
          <el-select v-model="uploadForm.selectGroup" class="select-group">
            <el-option label="未分组" :value="1"></el-option>
            <el-option label="分组一" :value="2"></el-option>
          </el-select>
          <el-button type="text">添加分组</el-button>
          <el-button type="text" :loading="loadIshow" @click="getGroupSelect"
            >刷新</el-button
          >
        </div>
      </el-form-item>
      <el-form-item
        label="本地图片:"
        prop="imgUrl"
        v-if="uploadForm.uploadType === 1"
      >
        <el-upload
          :limit="15"
          class="avatar-uploader"
          list-type="picture-card"
          :action="path + '/fileUploadAndDownload/upload'"
          :headers="{ 'x-token': token }"
          :before-upload="$fn.beforeAvatarUpload"
          :on-success="handleImgsSuccess"
          :on-remove="removeImgsList"
          accept=".jpg,.jpeg,.png,.gif,.bmp,.JPG,.JPEG,.PBG,.GIF,.BMP,"
        >
          <i class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
        <p class="remark-p">
          支持 .jpg, .gif, .png, .bmp 格式，最多15张，单个图片不超过{{$store.state.uploadLimitSize}}MB。
        </p>
      </el-form-item>
      <el-form-item
        label="网络图片:"
        prop="imgUrl"
        v-if="uploadForm.uploadType === 2"
      >
        <el-input
          v-model="uploadForm.imgUrl"
          class="imgUrlInput"
          placeholder="请填写网络图片地址"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <img
          v-if="uploadForm.uploadType === 2 && uploadForm.imgUrl"
          :src="uploadForm.imgUrl"
          class="upload-img"
        />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">返 回</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { mapGetters } from "vuex";
export default {
  name: "ImgsHouseUploadDialog",
  data() {
    return {
      path: this.$path,
      loadIshow: false,
      isShow: false,
      imgsFileList: [],
      uploadForm: {
        uploadType: 1,
        selectGroup: "",
        imgUrl: "",
      },
      rules: {
        uploadType: {
          required: true,
          message: "请选择上传方式",
          trigger: "blur",
        },
        selectGroup: {
          required: true,
          message: "请选择所在分组",
          trigger: "blur",
        },
        imgUrl: [
          {
            required: true,
            trigger: "blur",
            validator: (rule, value, callback) => {
              if (this.uploadForm.uploadType === 1) {
                if (value) {
                  callback();
                } else {
                  return callback(new Error("请上传本地图片"));
                }
              }
              if (this.uploadForm.uploadType === 2) {
                if (value) {
                  callback();
                } else {
                  return callback(new Error("请填写网络图片地址"));
                }
              }
            },
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters("user", ["userInfo", "token"]),
  },
  methods: {
    getGroupSelect() {
      this.loadIshow = true;
      setTimeout(() => {
        this.loadIshow = false;
      }, 1000);
    },
    handleClose() {
      this.isShow = false;
    },
    handleImgsSuccess(res) {
      this.imgsFileList.push({
        src: res.data.file.url,
        url: res.data.file.url,
      });
    },
    // 删除商品相册图片
    removeImgsList(file, fileList) {
      this.imgsFileList = [];
      fileList.forEach((item) => {
        this.imgsFileList.push({
          src: item.response.data.file.url,
          url: item.response.data.file.url,
        });
      });
    },
    beforeAvatarUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error("上传头像图片大小不能超过 10MB!");
      }
      return isLt10M;
    },
    confirm() {},
  },
};
</script>
<style lang="scss" scoped>
.f {
  display: flex;
}
.fac {
  align-items: center;
}
.select-group {
  width: 340px;
  margin-right: 10px;
}
.imgUrlInput {
  width: 340px;
}
.upload-img {
  width: 80px;
  height: 80px;
}
p.remark-p {
  color: var(--theme-stroke-3, #969799);
  line-height: 14px;
  font-size: 12px;
  margin-top: 10px;
  margin-bottom: 0;
}
</style>