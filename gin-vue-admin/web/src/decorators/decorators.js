/**
 * 二次确认框
 * @param title - 标题
 * @param content - 内容
 * @param confirmBtnText - 确认按钮名称
 * @param cancelBtnText - 取消按钮名称
 * @param type - 消息类型，用于显示图标 success / info / warning / error
 */
import {MessageBox} from "element-ui";

export const confirm = (title, content , confirmBtnText = "确认", cancelBtnText = "取消", type = "warning") => (target, name, descriptor) => {
    const method = descriptor.value;
    descriptor.value = async function (...agrs) {
        MessageBox.confirm(content, title, {
            confirmButtonText: confirmBtnText,
            cancelButtonText: cancelBtnText,
            type: type
        }).then(() => {
            return method.call(this, ...agrs)
        }).catch(() => {
        })
    }
}