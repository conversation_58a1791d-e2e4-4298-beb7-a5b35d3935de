@charset "utf-8";


@mixin font_size($num:28px) {
  font-size: $num !important;
}
//外边距的公共样式
@mixin mt_($num:1px) {
  margin-top: $num !important;
}
@mixin mr_($num:1px) {
  margin-right: $num !important;
}
@mixin mb_($num:1px) {
  margin-bottom: $num !important;
}
@mixin ml_($num:1px) {
  margin-left: $num !important;
}

//内边距的公共样式
@mixin pt_($num:1px) {
  padding-top: $num !important;
}
@mixin pr_($num:1px) {
  padding-right: $num !important;
}
@mixin pb_($num:1px) {
  padding-bottom: $num !important;
}
@mixin pl_($num:1px) {
  padding-left: $num !important;
}


@for $i from 1 through 20 {
  .font_size#{($i + 11)}{@include font-size(22px + ($i+$i));}
}

//外边距的公共样式
@for $j from 1 through 100 {
  .mt_#{$j}{@include mt_($j+ px);}
}
@for $j from 1 through 100 {
  .mr_#{$j}{@include mr_($j+ px);}
}
@for $j from 1 through 100 {
  .mb_#{$j}{@include mb_($j+ px);}
}
@for $j from 1 through 100 {
  .ml_#{$j}{@include ml_($j+ px);}
}

//内边距的公共样式
@for $j from 1 through 100 {
  .pt_#{$j}{@include pt_($j+ px);}
}
@for $j from 1 through 100 {
  .pr_#{$j}{@include pr_($j+ px);}
}
@for $j from 1 through 100 {
  .pb_#{$j}{@include pb_($j+ px);}
}
@for $j from 1 through 100 {
  .pl_#{$j}{@include pl_($j+ px);}
}
