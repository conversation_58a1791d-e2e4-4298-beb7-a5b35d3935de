<template>
    <m-card>
        <el-form :model="formData" label-width="100px" inline>
            <el-form-item>
                <el-input placeholder="请输入" v-model="formData.uid" class="line-input" clearable>
                    <span slot="prepend">会员ID</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input placeholder="请输入" v-model="formData.mobile" class="line-input" clearable>
                    <span slot="prepend">手机号</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input placeholder="请输入" v-model="formData.nickname" class="line-input" clearable>
                    <span slot="prepend">昵称</span>
                </el-input>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >分销商等级</span>
                    </div>
                    <el-select
                            v-model="formData.distributor_level_id"
                            clearable
                            class="w100"
                        >
                            <el-option
                                :value="item.id"
                                v-for="item in optionList"
                                :key="item.id"
                                :label="item.name"
                            >
                            </el-option>
                        </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="search">
                    搜索
                </el-button>
            </el-form-item>
            <el-form-item>
                <el-button @click="exportList">导出</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="text" @click="reset">
                    重置搜索条件
                </el-button>
            </el-form-item>

        </el-form>
        <el-table :data="distributorList">
            <el-table-column
                label="ID"
                prop="id"
                align="center"
            ></el-table-column>
            <el-table-column
                label="会员ID"
                prop="uid"
                align="center"
            ></el-table-column>
            <el-table-column label="昵称/手机号" align="center">
                <template slot-scope="scope">
                    <div>{{ scope.row.user_info.nickname }}</div>
                    <div>{{ scope.row.user_info.username }}</div>
                </template>
            </el-table-column>
            <el-table-column
                label="分销商等级"
                prop="distributor_level_info.name"
                align="center"
            ></el-table-column>
            <el-table-column label="直推下小商店数量" align="center">
                <template slot-scope="scope">
                    <div>{{ scope.row.team_small_shop_count }}</div>
                    <el-button
                        type="text"
                        @click.native="
                            toTarget(
                                '/layout/agentIndex/distributorsPushSmallShopIndex',
                                scope.row.uid,
                                1,
                            )
                        "
                        >查看</el-button
                    >
                </template>
            </el-table-column>
            <el-table-column label="直推下分销商数量" align="center">
                <template slot-scope="scope">
                    <div>{{ scope.row.team_distributor_count }}</div>
                    <el-button
                        type="text"
                        @click.native="
                            toTarget(
                                '/layout/agentIndex/distributorsPushDistributorsIndex',
                                scope.row.uid,
                                2,
                            )
                        "
                        >查看</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            class="pagination"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
        >
        </el-pagination>
    </m-card>
</template>

<script>
import { getDistributorAllLevel } from '@/api/distributor';
import { getInsDistributorList, exportInsDistributor } from '@/api/agent';

export default {
    name: 'distributorDataIndex',

    data() {
        return {
            formData: {
                uid: null, // 会员ID
                mobile: '', // 手机号
                nickname: '', // 昵称
                distributor_level_id: '', // 分销商等级
            },
            optionList: [], // 分销商等级列表
            distributorList: [], // 分销商数据列表
            page: 1, // 当前页数
            pageSize: 10, // 每页条数
            total: null, // 总数
        };
    },

    mounted() {
        this.LevelOptionList();
        this.getInsDistributorList();
    },

    methods: {
        // 获取分销商等级
        async LevelOptionList() {
            const res = await getDistributorAllLevel();
            if (res.code === 0) {
                this.optionList = res.data.levels;
            }
        },
        // 获取分销商数据列表
        async getInsDistributorList() {
            const params = {
                page: this.page,
                pageSize: this.pageSize,
                uid: this.formData.uid,
                mobile: this.formData.mobile,
                nickname: this.formData.nickname,
                distributor_level_id: this.formData.distributor_level_id,
            };
            const res = await getInsDistributorList(params);
            if (res.code === 0) {
                this.distributorList = res.data.list;
                this.total = res.data.total;
            }
        },
        // 搜索
        search() {
            this.page = 1;
            this.getInsDistributorList();
        },
        // 导出列表
        async exportList() {
            this.search();
            const params = {
                uid: this.formData.uid,
                mobile: this.formData.mobile,
                nickname: this.formData.nickname,
                distributor_level_id: this.formData.distributor_level_id,
            };
            if (this.total === 0) {
                this.$message.error('请选择需要导出的数据');
                return;
            }
            const res = await exportInsDistributor(params);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.download(res.data.link);
            }
        },
        // 导出方法
        download(link) {
            window.open(this.$path + '/' + link);
        },
        // 重置搜索
        reset() {
            this.formData = {};
            this.search();
        },
        // 页面跳转
        toTarget(name, uid, type) {
            this.$router.push({ path: name, query: { uid: uid, type: type } });
        },
        // 每页条数
        handleSizeChange(val) {
            this.pageSize = val;
            this.page = 1;
            this.getInsDistributorList();
        },
        // 当前页数
        handleCurrentChange(val) {
            this.page = val;
            this.getInsDistributorList();
        },
    },
};
</script>

<style lang="scss" scoped>
.pagination {
    float: right;
}
</style>
