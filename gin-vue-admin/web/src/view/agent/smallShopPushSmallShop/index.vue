<template>
    <m-card>
        <el-form :model="formData" label-width="80px">
            <el-row :gutter="10">
                <el-col :span="6">
                    <el-form-item label="会员ID">
                        <el-input
                            v-model="formData.uid"
                            placeholder="请输入"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="手机号">
                        <el-input
                            v-model="formData.mobile"
                            placeholder="请输入"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="昵称">
                        <el-input
                            v-model="formData.nickname"
                            placeholder="请输入"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="小商店">
                        <el-input
                            v-model="formData.small_shop_name"
                            placeholder="请输入小商店名称"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item label="创建时间">
                        <m-daterange v-model="formData.date"></m-daterange>
                    </el-form-item>
                </el-col>
                <el-col :span="18">
                    <el-form-item>
                        <el-button type="primary" @click="search">
                            搜索
                        </el-button>
                        <el-button @click="exportList">导出</el-button>
                        <el-button type="text" @click="reset">
                            重置搜索条件
                        </el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table :data="optionList">
            <el-table-column
                label="会员ID"
                prop="uid"
                align="center"
            ></el-table-column>
            <el-table-column label="昵称/手机号" align="center">
                <template slot-scope="scope">
                    <div>{{ scope.row.user_info.nickname }}</div>
                    <div>{{ scope.row.user_info.username }}</div>
                </template>
            </el-table-column>
            <el-table-column
                label="小商店名称"
                prop="small_shop.title"
                align="center"
            ></el-table-column>
            <el-table-column label="开通时间" align="center">
                <template slot-scope="scope">
                    {{ scope.row.created_at | formatDate }}
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            class="pagination"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
        >
        </el-pagination>
    </m-card>
</template>

<script>
import MDaterange from '@/components/mDate/daterange';
import {
    getInsSmallShopChildList,
    exportInsSmallShopChild,
} from '@/api/agent';

export default {
    name: 'smallShopPushSmallShopIndex',
    components: { MDaterange },

    data() {
        return {
            formData: {
                uid: null, // 会员ID
                mobile: '', // 手机号
                nickname: '', // 昵称
                small_shop_name: '', // 小商店名称
                date: [], // 创建时间
            },
            optionList: [], // 列表数据
            page: 1, // 当前页数
            pageSize: 10, // 每页条数
            total: null, // 总数
        };
    },

    mounted() {
        this.getInsSmallShopChildList();
    },

    methods: {
        // 获取分销商直推小商店列表
        async getInsSmallShopChildList() {
            const params = {
                page: this.page,
                pageSize: this.pageSize,
                ins_sma_uid: parseInt(this.$route.query.uid),
                uid: this.formData.uid,
                mobile: this.formData.mobile,
                nickname: this.formData.nickname,
                small_shop_name: this.formData.small_shop_name,
                start_time: !this.formData.date ? [] : this.formData.date[0],
                end_time: !this.formData.date ? [] : this.formData.date[1],
            };
            const res = await getInsSmallShopChildList(params);
            if (res.code === 0) {
                this.optionList = res.data.list;
                this.total = res.data.total;
            }
        },
        // 搜索
        search() {
            this.page = 1;
            this.getInsSmallShopChildList();
        },
        // 导出列表
        async exportList() {
            this.search();
            const params = {
                ins_sma_id: parseInt(this.$route.query.id),
                uid: this.formData.uid,
                mobile: this.formData.mobile,
                nickname: this.formData.nickname,
                small_shop_name: this.formData.small_shop_name,
                start_time: !this.formData.date ? [] : this.formData.date[0],
                end_time: !this.formData.date ? [] : this.formData.date[1],
            };
            if (this.total === 0) {
                this.$message.error('请选择需要导出的数据');
                return;
            }
            const res = await exportInsSmallShopChild(params);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.download(res.data.link);
            }
        },
        // 导出方法
        download(link) {
            window.open(this.$path + '/' + link);
        },
        // 重置搜索条件
        reset() {
            this.formData = {};
            this.search();
        },
        // 每页条数
        handleSizeChange(val) {
            this.pageSize = val;
            this.page = 1;
            this.getInsSmallShopChildList();
        },
        // 当前页数
        handleCurrentChange(val) {
            this.page = val;
            this.getInsSmallShopChildList();
        },
    },
};
</script>

<style lang="scss" scoped>
.pagination {
    float: right;
}
</style>
