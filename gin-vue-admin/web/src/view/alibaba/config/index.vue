<template>
  <m-card>
    <el-form :model="formData" label-width="130px">
      <p class="title-p">基础信息</p>
      <el-divider></el-divider>
      <el-row>
        <el-col>
          <el-form-item label="是否开启:">
            <el-radio-group v-model="formData.enable">
              <el-radio :label="1">开启</el-radio>
              <el-radio :label="0">关闭</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="ID:">
            <el-input v-model="formData.id" :disabled="idIsForbid" class="w600"></el-input>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="key:">
            <el-input v-model="formData.key" class="w600"></el-input>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="secret:">
            <el-input v-model="formData.secret" class="w600"></el-input>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="domain:">
            <el-input v-model="formData.domain" class="w600"></el-input>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="token:">
            <div class="f fac">
              <el-input v-model="formData.token" class="w600"></el-input>
              <el-button type="text" class="ml10" @click="getCode">获取code</el-button>
              <el-button type="text" @click="openTokenDialog">生成token</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item>
        <el-button type="primary" @click="save">提交</el-button>
      </el-form-item>
    </el-form>
    <token-dialog ref="tokenDialog" @reload="init"></token-dialog>
  </m-card>
</template>

<script>
import {getSetting, setSetting} from "@/api/aliOpen";
import TokenDialog from "./components/tokenDialog";

export default {
  name: "alibabaConfigIndex",
  components: {TokenDialog},
  data() {
    return {
      idIsForbid: false,
      key: this.$route.query.key || "",
      formData: {
        enable:1,
        id: "",
        key: "",
        secret: "",
        token: "",
        domain: ""
      }
    }
  },
  created() {
    if (this.key) {
      this.init(this.key)
    }
  },
  methods: {
    // 打开生成token弹出层
    openTokenDialog() {
      if (!this.formData.id) {
        this.$message.error("请填写ID")
        return false
      }
      this.$refs.tokenDialog.isShow = true
      this.$nextTick(() => {
        this.$refs.tokenDialog.formData.id = this.formData.id
      })
    },
    getCode() {
      if (!this.formData.key) {
        this.$message.error("请输入key")
        return false
      }
      if (!this.formData.domain) {
        this.$message.error("请输入domain")
        return false
      }
      window.open(`https://auth.1688.com/oauth/authorize?client_id=${this.formData.key}&site=1688&redirect_uri=${this.formData.domain}&state=1`)
    },
    async init(key) {
      this.idIsForbid = true
      let res = await getSetting({id: key})
      if (res.code === 0) {
        let data = JSON.parse(res.data.value)
        this.setFrom(data)
      }
    },
    setFrom(val) {
      const keys = Object.keys(val);
      const that = this;
      keys.forEach((element) => {
        that.formData[element] = val[element];
      });
    },
    async save() {
      let res = await setSetting(this.formData)
      if (res.code === 0) {
        this.$message.success(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/style/base.scss";

p.title-p {
  font-size: 16px;
  font-weight: bold;
}

.el-divider {
  margin: 15px 0;
}

.w600 {
  width: 600px;
}
</style>