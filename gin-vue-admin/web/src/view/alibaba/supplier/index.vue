<template>
    <m-card>
        <el-button type="primary" @click="openAddDialog">新增</el-button>
        <el-table :data="tableData" class="mt25">
            <el-table-column label="ID" prop="memberId" align="center"></el-table-column>
            <el-table-column label="域名" prop="domain" show-overflow-tooltip align="center"></el-table-column>
            <el-table-column label="创建时间" align="center">
                <template slot-scope="scope">
                    {{ scope.row.created_at | formatDate }}
                </template>
            </el-table-column>
            <el-table-column label="登录ID" prop="loginId" align="center"></el-table-column>
            <el-table-column label="名称" prop="companyName" align="center"></el-table-column>
            <el-table-column label="是否自动支付" align="center">
                <template slot-scope="scope">
                    {{ scope.row.auto_pay | formatAutoPay }}
                </template>
            </el-table-column>
            <el-table-column label="下单方式" align="center">
                <template slot-scope="scope">
                    {{ scope.row.order_type | formatOrderType }}
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="350" fixed="right">
                <template slot-scope="scope">
                    <el-button type="text" class="color-red" @click="openSettingDialog(scope.row)">设置</el-button>
                    <el-button type="text" @click="openBindSD(scope.row)">绑定供应商</el-button>
                    <el-button type="text" v-if="scope.row.uid > 0" @click="getSupplierInfo(scope.row)">获取供应商信息
                    </el-button>
                    <el-button type="text" v-if="scope.row.uid > 0" @click="openProductD(scope.row)">绑定商品
                    </el-button>
                    <!--          <el-button type="text" @click="openProductD(scope.row)">绑定商品</el-button>-->
                    <el-button type="text" class="color-red" @click="del(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <add-dialog ref="addDialog" @reload="fetch"></add-dialog>
        <bind-supplier-drawer ref="bindSupplierDrawer" @reload="fetch"></bind-supplier-drawer>
        <product-list-drawer ref="productListDrawer" @reload="fetch"></product-list-drawer>
        <setting-dialog ref="settingDialog" @reload="fetch"></setting-dialog>
    </m-card>
</template>

<script>
import {getSupplierList, deleteDomain, getSupplier} from "@/api/aliOpen"
import AddDialog from "./components/addDialog";
import BindSupplierDrawer from "./components/bindSupplierDrawer";
import ProductListDrawer from "./components/productListDrawer";
import SettingDialog from "./components/settingDialog.vue";

export default {
    name: "alibabaSupplierIndex",
    components: {AddDialog, BindSupplierDrawer, ProductListDrawer, SettingDialog},
    data() {
        return {
            key: this.$route.query.key || "",
            searchInfo: {},
            tableData: []
        }
    },
    filters: {
        formatAutoPay(value) {
            let s = '';
           if (value!="1"){
             s="是"
           }else{
             s="否"
           }
            return s;
        },
        formatOrderType(value){
            let s = '';
            switch (value) {
                case "1":
                    s = "批发下单"
                    break;
                case "0":
                    s = '代发下单'
                    break;
                default:
                    s = '代发下单'
                    break;
            }
            return s;
        }
    },
    mounted() {
        if (this.key) {
            this.fetch()
        } else {
            this.$message.error("访问错误")
            this.$router.push({name: "alibabaList"})
        }
    },
    methods: {
        openSettingDialog(row) {
            this.$refs.settingDialog.init(row)
        },
        // 获取供应商信息
        async getSupplierInfo(row) {
            let res = await getSupplier({id: this.key, domain: row.domain})
            if (res.code === 0) {
                this.$message.success(res.msg)
                this.fetch()
            }
        },
        // 打开供应商列表抽屉
        openBindSD(row) {
            this.$refs.bindSupplierDrawer.isShow = true
            this.$refs.bindSupplierDrawer.init(row.memberId)
        },
        // 打开供应商商品列表
        openProductD(row) {
            this.$refs.productListDrawer.isShow = true
            this.$refs.productListDrawer.init(row.uid)

        },
        // 删除
        del(row) {
            let id = row.id.toString()
            this.$confirm("确定删除吗?", "提示", {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                deleteDomain({id}).then(res => {
                    if (res.code === 0) {
                        this.$message.success(res.msg)
                        this.fetch()
                    }
                })
            }).catch(() => {
            })
        },
        // 打开新增窗口
        openAddDialog() {
            this.$refs.addDialog.isShow = true
            this.$refs.addDialog.formData.id = this.key
        },
        async fetch() {
            this.searchInfo.key = this.key
            let res = await getSupplierList({...this.searchInfo})
            if (res.code === 0) {
                this.tableData = res.data
            }
        },
    }
}
</script>

<style lang="scss" scoped>
@import "@/style/base.scss";
</style>