<template>
  <m-card>
    <el-button @click="openDialog" type="primary">新增</el-button>
    <el-form
        label-width="110px"
        ref="form"
        :model="searchInfo"
        class="search-term mt25"
        inline
    >
      <el-form-item>
          <el-input v-model="searchInfo.appName" class="line-input"  placeholder="请输入" clearable>
              <span slot="prepend">采购端名称</span>
          </el-input>
      </el-form-item>
      <el-form-item>
          <el-input v-model="searchInfo.companyName" class="line-input"  placeholder="请输入" clearable>
              <span slot="prepend">公司名称</span>
          </el-input>
      </el-form-item>
      <el-form-item>
          <el-input v-model="searchInfo.legalPersonName" class="line-input"  placeholder="请输入" clearable>
              <span slot="prepend">法人姓名</span>
          </el-input>
      </el-form-item>
      <el-form-item>
          <el-input v-model="searchInfo.member_name" class="line-input"  placeholder="请输入" clearable>
              <span slot="prepend">会员</span>
          </el-input>
      </el-form-item>
      <el-form-item label="">
            <div class="line-input" >
                <div class="line-box ">
                    <span >省市区</span>
                </div>
                <el-cascader
                    filterable
                    clearable
                    class="w100"
                    :options="options"
                    v-model="searchInfo.selectedOptions"
                    @change="handleChangeSearch"
                >
                </el-cascader>
            </div>
        </el-form-item>
        <el-form-item label="">
            <div class="line-input" >
                <div class="line-box ">
                    <span >采购端等级</span>
                </div>
                <el-select
                    v-model="searchInfo.appLevelId"
                    value-key="value"
                    placeholder="请选择"
                    label="采购端等级:"
                    class="w100"
                    clearable
                    filterable
                >
                  <el-option
                      v-for="item in levelOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                  >
                  </el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="">
            <div class="line-input" >
                <div class="line-box ">
                    <span >绑定的供应商</span>
                </div>
                <el-select v-model="searchInfo.pet_supplier_id" filterable clearable filterable class="w100">
                  <el-option v-for="item in supplierOption" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item>
            <el-input v-model="searchInfo.appid" class="line-input"  placeholder="请输入" clearable>
                <span slot="prepend">AppID</span>
            </el-input>
        </el-form-item>
        <el-form-item label="">
            <div class="line-input" >
                <div class="line-box ">
                    <span >采购端分组</span>
                </div>
                <el-select v-model="searchInfo.group_id" clearable filterable class="w100">
                  <el-option v-for="item in groupOption" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item>
            <el-input v-model="searchInfo.member_user_name" class="line-input"  placeholder="请输入" clearable>
                <span slot="prepend">手机号</span>
            </el-input>
        </el-form-item>
          <!-- <el-col :span="6">
            <el-form-item label="是否有商品池:">
              <el-select v-model="searchInfo.is_import" class="w100">
                <el-option label="全部" value="0" ></el-option>
                <el-option label="是" value="1" ></el-option>
                <el-option label="否" value="2" ></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <br/>
          <el-form-item>
            <el-button @click="onSubmit" type="primary" >查询</el-button>
            <el-button @click="exportApplicationTable" >导出</el-button>

            <el-button type="text" @click="resetSearch">重置搜索条件</el-button>
          </el-form-item>
      <!-- <el-row :gutter="10">
        
      </el-row>
      <el-row :gutter="10">
        
      </el-row> -->
      <!-- <el-row :gutter="10">
        <el-col :xl="5" :lg="9">
          <el-form-item label="省市区:" prop="selectedOptions">
            <el-cascader
              clearable
              class="w100"
              size="large"
              :options="options"
              v-model="searchInfo.selectedOptions"
              @change="handleChangeSearch"
            >
            </el-cascader>
          </el-form-item>
        </el-col>
        <el-col :xl="5" :lg="9">
          <el-form-item label="采购端等级:" prop="appLevelId">
            <el-select
              v-model="searchInfo.appLevelId"
              value-key="value"
              placeholder="请选择"
              label="采购端等级:"
              class="w100"
              clearable
            >
              <el-option
                v-for="item in levelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row> -->
    </el-form>
    <el-table :data="tableData" @selection-change="handleSelectionChange" class="mt25">
      <!-- <el-table-column label="日期" align="center">
        <template slot-scope="scope"
          >{{ scope.row.created_at | formatDate }}
        </template>
</el-table-column>

<el-table-column label="公司名称" prop="companyName" align="center"></el-table-column>

<el-table-column label="信用代码" prop="creditCode" align="center"></el-table-column>

<el-table-column label="法人姓名" prop="legalPersonName" align="center"></el-table-column>

<el-table-column label="联系人姓名" prop="contactsName" align="center"></el-table-column>

<el-table-column label="联系人电话" prop="contactsPhontnumber" align="center"></el-table-column> -->
      <el-table-column label="创建时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.created_at | formatDate }}
        </template>
      </el-table-column>
      <el-table-column label="会员/手机号" align="center">
        <template slot-scope="scope">
          <p style="height: auto !important;line-height: 23px !important">{{ scope.row.user.nickname }}</p>
          <p>{{ scope.row.user.username }}</p>
        </template>
      </el-table-column>
      <el-table-column label="余额" align="center">
        <template slot-scope="scope">
          汇聚：{{ scope.row.goin_balance.purchasing_balance | formatF2Y }}
          <br />
          站内：{{ scope.row.balance.purchasing_balance | formatF2Y }}
        </template>
      </el-table-column>
      <el-table-column label="订单数量" prop="order_count" align="center"></el-table-column>

      <el-table-column label="订单金额" align="center">
        <template slot-scope="scope">{{ scope.row.order_amount | formatF2Y }}
        </template>
      </el-table-column>
      <el-table-column label="公司名称" align="center" prop="companyName"></el-table-column>
      <el-table-column label="采购端名称" prop="appName" align="center"></el-table-column>

      <el-table-column label="采购端等级" prop="appLevelId" align="center">
        <template slot-scope="scope">{{ scope.row.applicationLevel.levelName }}
        </template>
      </el-table-column>
      <el-table-column label="供应商名称" prop="appLevelId" align="center">
        <template slot-scope="scope">{{ scope.row.pet_supplier.shop_name }}
        </template>
      </el-table-column>
      <el-table-column label="禁用状态">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.banlist" :active-value="1" :inactive-value="0"
            @change="handleStatus(scope.row)">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column label="运营状态" prop="status_code" align="center">
        <template slot-scope="scope">{{ scope.row.status_code == 0 ? '测试中' : '运营中' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="360" align="center" fixed="right">
        <template slot-scope="scope">
          <!-- <el-button type="text" @click="keyManagement(scope.row)">密钥管理
          </el-button> -->
          <!-- <el-button type="text" @click="getKey(scope.row)">重新生成密钥
          </el-button> -->
          <el-button v-if="scope.row.is_multi_shop === 1" style="padding: 0 !important;margin-left: 10px !important;" type="text" @click="shopList(scope.row)">商城列表
          </el-button>
          <el-button type="text" @click="checkKey(scope.row)" style="padding: 0 !important;margin-left: 10px !important;">密钥管理
          </el-button>
          <el-button type="text" @click="pushOrderDh(scope.row)" style="padding: 0 !important;margin-left: 10px !important;">推送订单到企业购</el-button>
          <!--          <el-button type="text" @click="syncData(scope.row)">同步</el-button>-->
          <el-button type="text" @click="detail(scope.row)" style="padding: 0 !important;margin-left: 10px !important;">查看</el-button>
          <el-button @click="updateApplication(scope.row)" type="text" style="padding: 0 !important;margin-left: 10px !important;">编辑
          </el-button>
          <el-button @click="openExportDialog(scope.row)" type="text" style="padding: 0 !important;margin-left: 10px !important;">导出
          </el-button>
          <el-button type="text" class="color-red" @click="deleteRow(scope.row)" style="padding: 0 !important;margin-left: 10px !important;">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination background :current-page="page" :page-size="pageSize" :page-sizes="[10, 30, 50, 100]"
      :style="{ float: 'right', padding: '20px' }" :total="total" @current-change="handleCurrentChange"
      @size-change="handleSizeChange" layout="total, sizes, prev, pager, next, jumper"></el-pagination>

    <el-dialog :before-close="closeDialog" :visible.sync="dialogFormVisible" :title="titleDialog">
      <el-form :model="formData" ref="form" :rules="rules" label-position="right" label-width="160px">
        <el-form-item label="公司名称:">
          <el-input v-model="formData.companyName" clearable placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="公司介绍:">
          <el-input v-model="formData.companyIntro" clearable placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="省市区:">
          <el-cascader :options="options" v-model="selectedOptions" @change="handleChange" titleDialog class="w100">
          </el-cascader>
        </el-form-item>

        <el-form-item label="详细地址:">
          <el-input v-model="formData.address" clearable placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="信用代码:">
          <el-input v-model="formData.creditCode" clearable placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="营业执照:">
          <upload-image v-model="formData.businessLicense" :fileSize="1024 * $store.state.uploadLimitSize" :maxWH="1080" />
        </el-form-item>

        <el-form-item label="法人姓名:">
          <el-input v-model="formData.legalPersonName" clearable placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="法人身份证号码:">
          <el-input v-model="formData.idCardNumber" clearable placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="法人身份证正面:">
          <upload-image v-model="formData.idCardFront" :fileSize="1024 * $store.state.uploadLimitSize" :maxWH="1080" />
        </el-form-item>

        <el-form-item label="法人身份证背面:">
          <upload-image v-model="formData.idCardBackend" :fileSize="1024 * $store.state.uploadLimitSize" :maxWH="1080" />
        </el-form-item>

        <el-form-item label="联系人姓名:">
          <el-input v-model="formData.contactsName" clearable placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="联系人电话:">
          <el-input v-model="formData.contactsPhontnumber" clearable placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="联系人邮箱:">
          <el-input v-model="formData.contactsEmail" clearable placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="采购端名称:">
          <el-input v-model="formData.appName" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="采购端分组:">
          <el-select v-model="formData.group_id" clearable filterable class="w100">
            <el-option v-for="item in groupOption" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="采购端等级:" prop="appLevelId">
          <el-select v-model="formData.appLevelId" value-key="value" placeholder="请选择" label="采购端等级:" titleDialog
            class="w100">
            <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="商城:" prop="is_multi_shop">
          <el-radio-group v-model="formData.is_multi_shop">
            <el-radio :label="2">单商城</el-radio>
            <el-radio :label="1">多商城</el-radio>
          </el-radio-group>
          <p class="color-red" >多商城显示商城名称，单商城不显示！单商城修改为多商城后,需要用商城密钥重新获取token进行下单,否则订单无法识别多商城！</p>
        </el-form-item>
        <div v-if="formData.is_multi_shop == 2">
          <el-form-item label="回调地址:" prop="callBackLink" v-if="formData.status_code == 0">
            <div style="display: flex;">
              <el-input v-model="formData.callBackLink" class="xxx" clearable placeholder="请输入"></el-input>
              <el-button class="verify" type="primary" @click="verifyfun">校验</el-button>
            </div>
          </el-form-item>
          <el-form-item label="回调地址:" prop="callBackLink" v-else>
            <div style="display: flex;">
              <el-input v-model="formData.callBackLink" clearable placeholder="请输入"></el-input>
              <el-button class="verify" type="primary" @click="verifyfun">校验</el-button>
            </div>
          </el-form-item>
        </div>
        <el-form-item label="运营状态:" prop="status_code">
          <el-radio-group v-model="formData.status_code" @change="statusCode('callBackLink')">
            <el-radio :label="0">测试中</el-radio>
            <el-radio :label="1">运营中</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="获取下游销售单价:" prop="is_down_price ">
          <el-radio-group v-model="formData.is_down_price">
            <el-radio :label="0">禁用</el-radio>
            <el-radio :label="1">启用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="采购端订单信息地址:" prop="export_link">
          <el-input v-model="formData.export_link" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="抖音cps回调地址:" prop="callBackLinkCps">
          <el-input v-model="formData.callBackLinkCps" clearable placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="聚推联盟回调地址:" prop="callBackLinkJhCps">
          <el-input v-model="formData.callBackLinkJhCps" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="IP白名单:">
          <el-input v-model="formData.ipList" type="textarea" clearable placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="会员:" prop="memberId">
          <!-- <el-input
            v-model.number="formData.memberId"
            clearable
            placeholder="请输入"
          ></el-input> -->
          <el-select v-model="formData.memberId" filterable class="w100">
            <el-option v-for="item in userOption" :key="item.id" :label="item.username" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="签名认证:" prop="is_check_sign ">
          <el-radio-group v-model="formData.is_check_sign">
            <el-radio :label="0">禁用</el-radio>
            <el-radio :label="1">启用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="云仓供应商:" prop="supplier_id">
          <el-select v-model="formData.supplier_id" clearable filterable class="w120">
            <el-option v-for="item in supplierOption" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="multi_pet_supplier === 0" label="供应商:" prop="pet_supplier_id">
          <el-select v-model="formData.pet_supplier_id" clearable filterable class="w120">
            <el-option v-for="item in supplierOption" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="multi_pet_supplier === 1" label="供应商:" prop="pet_suppliers">
          <el-select v-model="formData.pet_supplier_ids" multiple clearable filterable placeholder="请选择">
            <el-option v-for="item in supplierOption" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="消息池开关:" prop="is_message_pool ">
          <el-radio-group v-model="formData.is_message_pool">
            <el-radio :label="1">开启</el-radio>
            <el-radio :label="2">关闭</el-radio>
          </el-radio-group>
          <p class="color-red" >备注：如确定无需走消息池回调方式，需选择关闭。</p>
        </el-form-item>
      </el-form>
      <div class="dialog-footer" slot="footer">
        <el-button @click="enterDialog" type="primary">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </el-dialog>
    <applicationDetail ref="applicationDetail"></applicationDetail>
    <exprotDialog ref="exprotDialog"></exprotDialog>
    <!-- key dialog -->
    <el-dialog title="密钥管理" :visible="keyDialogIsShow" width="40%" :before-close="handleKeyDialogClose">
      <el-form :model="keyData" label-width="90px">
        <el-form-item label="公司名称:">
          <p>
            {{ keyData.companyName }}
          </p>
        </el-form-item>
        <el-form-item label="appKey:">
          <p>
            {{ keyData.appKey }}
            <a href="javascript:;" class="ml10 color-red" @click="$fn.copy(keyData.appKey)">复制</a>
          </p>
        </el-form-item>
        <el-form-item label="appSecret:">
          <p>
            {{ keyData.appSecret | formatSecret }}
            <a href="javascript:;" class="ml10 color-red" @click="$fn.copy(keyData.appSecret)">复制</a>
          </p>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getKey(keyData.row)">重新生成密钥
          </el-button>
          <el-button type="primary" @click="copyAll">复制全部
          </el-button>
        </el-form-item>
      </el-form>
      <template v-if='resData.IsAuthSzbao'>
        <p class="title-p">盛创供应链密钥设置</p>
        <el-form :model="shengchuang_keyData" label-width="90px">
          <el-form-item label="独立配置:">
            <el-switch v-model="shengchuang_keyData.szbao_independence" :active-value="1" :inactive-value="0"></el-switch>
          </el-form-item>
          <el-form-item label="appKey:">
            <el-input v-model="shengchuang_keyData.szbao_independence_app_key" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="appSecret:">
            <el-input v-model="shengchuang_keyData.szbao_independence_app_secret" placeholder="请输入"></el-input>
            <p class='text-p'>
              开启独立配置后，中台永源供应链商品订单将使用独立设置的APPKEY创建订单，不同到盛传供应链的订单将使用独立配置的appkey会员进行下单！<br/>
              只有采购端API同步的订单会使用独立配置下单，直接在中台PC端、移动端、小商店、等场景下单，还是使用供应链-永远供应链配置种的appkey同步下单！
            </p>
          </el-form-item>

        </el-form>
        <div class="dialog_shengchuang f fjsb">
          <el-button type="primary" @click="shengchuangkeyDataFun(keyData.row.id)">保 存</el-button>
          <el-button @click="handleKeyDialogClose">关 闭</el-button>
        </div>
      </template>
      <div v-else slot="footer" class="dialog-footer">
        <el-button @click="handleKeyDialogClose">关 闭</el-button>
      </div>
    </el-dialog>
  </m-card>
</template>

<script>
import {
  createApplication,
  createApplicationKeySecret,
  createApplicationPetSupplier,
  deleteApplication,
  deleteApplicationByIds,
  findApplication,
  findApplicationSetting,
  getApplicationLevelOption,
  getApplicationList,
  updateApplication,
  changeBanlist,
  ApplicationExport,
  updateApplicationSzbao
} from "@/api/application"; //  此处请自行替换地址
import { pushOrderByApplicationId } from "@/api/dahangErp";
import { getSupplierOptionList } from "@/api/goods"
// import { getSupplierOptionList } from "@/api/order";
import { syncPriduct } from "@/api/goods";
import { validateCallback } from "@/api/application";
import { formatTimeToStr } from "@/utils/date";
import infoList from "@/mixins/infoList";
import UploadImage from "@/components/upload/image.vue";
import { regionData } from "element-china-area-data";
import applicationDetail from "./components/applicationDetail";
import { getUserOptionList } from "@/api/member";
import exprotDialog from "./components/exprotDialog";
import { getApplicationGroupList } from "@/api/jushuitan"
import { confirm } from "@/decorators/decorators";
export default {
  name: "Application",
  mixins: [infoList],
  components: {
    UploadImage,
    applicationDetail,
    exprotDialog
  },
  data() {
    return {
      rules: {
        appLevelId: {
          required: true,
          message: "请选择采购端等级",
          trigger: "change",
        },
        callBackLink: {
          required: true,
          message: "请输入回调地址",
          trigger: "blur",
        },
        memberId: { required: true, message: "请选择会员", trigger: "change" },
      },
      groupOption: [],
      userOption: [],
      supplierOption: [],
      titleDialog: "新增",
      listApi: getApplicationList,
      levelApi: getApplicationLevelOption,
      dialogFormVisible: false,
      type: "",
      deleteVisible: false,
      multipleSelection: [],
      //供应商select类型判断
      multi_pet_supplier: '',
      formData: {
        companyName: "",
        companyIntro: "",
        provinceId: 0,
        cityId: 0,
        districtId: 0,
        is_check_sign: 0,
        is_down_price: 0,
        address: "",
        creditCode: "",
        businessLicense: "",
        legalPersonName: "",
        idCardNumber: "",
        idCardFront: "",
        idCardBackend: "",
        contactsName: "",
        contactsPhontnumber: "",
        contactsEmail: "",
        appName: "",
        appLevelId: "",
        callBackLink: "",
        callBackLinkCps: "",
        callBackLinkJhCps: "",
        ipList: "",
        pet_supplier_ids: [],
        pet_suppliers: [],
        memberId: null,
        supplier_id: null,
        pet_supplier_id: null,
        export_link: "",
        group_id: null,
        status_code: 0,
        is_multi_shop: 2, // 2-单商城 1-多商城
        is_message_pool: 0,
      },
      //供应商数组
      gysarr: {
        application_id: 0,
        pet_supplier_ids: [],
      },
      levelOptions: [],
      options: regionData,
      selectedOptions: [],
      keyData: {},
      keyDialogIsShow: false,
      shengchuang_keyData:{
        id:null,
        szbao_independence:0,
        szbao_independence_app_key:'',
        szbao_independence_app_secret:''
      },

    };
  },
  filters: {
    formatSecret: function (str) {
      if (str) {
        let leng = str.length;
        let cent = Math.ceil(leng / 2);
        return str.replace(str.slice(cent - 5, cent + 5), "**********");
      }
    },
    formatDate: function (time) {
      if (time != null && time != "") {
        var date = new Date(time);
        return formatTimeToStr(date, "yyyy-MM-dd hh:mm:ss");
      } else {
        return "";
      }
    },
    formatBoolean: function (bool) {
      if (bool != null) {
        return bool ? "是" : "否";
      } else {
        return "";
      }
    },
  },
  methods: {
      copyAll(){
          let str = `公司名称：${this.keyData.companyName},appKey：${this.keyData.appKey},appSecret：${this.keyData.appSecret}`
        this.$fn.copy(str)
      },
    // 保存盛创供应链密钥设置
    async shengchuangkeyDataFun(id){
      console.log(this.resData.IsAuthSzbao);
      this.shengchuang_keyData.id = id
      let res = await updateApplicationSzbao(this.shengchuang_keyData)
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.handleKeyDialogClose()
        this.getTableData()
      }
    },
    async handleStatus(row) {
      const { code, msg } = await changeBanlist({ id: row.id, banlist: row.banlist })
      if (code === 0) {
        this.$message.success(msg)
        this.getTableData();
      }
    },
    @confirm("提示", "确定推送订单到企业购?")
pushOrderDh(row){
  pushOrderByApplicationId({ 'application_id': parseInt(row.id) }).then((res) => {
    if (res.code === 0) {
      this.$message.success(res.msg);
    }
  })
},
    async getGroupOption(){
  const { code, data } = await getApplicationGroupList({ page: 1, pageSize: 999 })
  if (code === 0) {
    this.groupOption = data.list
  }
},
openExportDialog(row) {
  this.$refs.exprotDialog.init(row)
},
    // 导出
    async exportApplicationTable() {
  for (let k in this.searchInfo) {
    if (typeof this.searchInfo[k] !== "number" && !this.searchInfo[k]) {
      delete this.searchInfo[k]
    }
  }

  const res = await ApplicationExport({ ...this.searchInfo })
  // console.log(res);
  if (res.code === 0) {
    // window.location.href = this.$path + '/' + res.data
    window.open(this.$path + '/' + res.data)
  } else {
    this.$message.error(res.msg)
  }
},
// 获取供应资源
getSupplier() {
  getSupplierOptionList().then((res) => {
    this.supplierOption = res.data.list;
    this.supplierOption.unshift({ id: 0, name: '中台自营' })
  });
},
getUserOption() {
  getUserOptionList({ page: 1, pageSize: 9999 }).then((res) => {
    if (res.code === 0) {
      this.userOption = res.data.list;
    } else {
      this.$message.error(res.msg);
    }
  });
},
syncData(row) {
  syncPriduct({ id: row.id }).then((res) => {
    if (res.code === 0) {
      this.$message.success(res.msg);
    }
  });
},
// 关闭查看密钥dialog
handleKeyDialogClose() {
  this.keyDialogIsShow = false;
  this.keyData = {};
  this.shengchuang_keyData = {
    szbao_independence:0,
    szbao_independence_app_key:'',
    szbao_independence_app_secret:''
  }
},
// 生成密钥
getKey(row) {
  this.$confirm("是否确认重新生成密钥?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    createApplicationKeySecret({ id: row.id }).then((res) => {
      if (res.code === 0) {
        this.$message.success(res.msg);
        this.getTableData();
      }
    });
  });
},
// 商城列表
shopList(row) {
  console.log('点击了商城', row);
  this.$router.push({ path: '/layout/application/shopListIndex', query: {id: row.id} })
},
// 查看密钥
checkKey(row) {
  this.shengchuang_keyData = {
    szbao_independence:row.szbao_independence,
    szbao_independence_app_key:row.szbao_independence_app_key,
    szbao_independence_app_secret:row.szbao_independence_app_secret
  }
  this.keyDialogIsShow = true;
  this.keyData.companyName = row.companyName
  this.keyData.appKey = `application${row.id}`;
  this.keyData.appSecret = row.appSecret;
  this.keyData.row = row // 重新生成密钥
},
detail(row) {
  this.$refs.applicationDetail.isShow = true;
  this.$refs.applicationDetail.getLevelOption(row.id);
  this.$refs.applicationDetail.getApplication(row.id);
},
handleChangeSearch(value) {
  this.searchInfo.provinceId = parseInt(value[0] == null ? "0" : value[0]);
  this.searchInfo.cityId = parseInt(value[1] == null ? "0" : value[1]);
  this.searchInfo.districtId = parseInt(value[2] == null ? "0" : value[2]);
},
handleChange(value) {
  this.formData.provinceId = parseInt(value[0]);
  this.formData.cityId = parseInt(value[1]);
  this.formData.districtId = parseInt(value[2]);
},
resetSearch() {
  this.$refs.form.resetFields();
  this.page = 1;
  this.total = 0;
  this.pageSize = 10;
  this.searchInfo = {};
  this.getTableData();
},
//条件搜索前端看此方法
onSubmit() {
  this.page = 1;
  this.pageSize = 10;
  this.getTableData();
},
handleSelectionChange(val) {
  this.multipleSelection = val;
},
//采购端配置
findSetting() {
  findApplicationSetting().then(res => {
    if (res.code === 0) {
      this.multi_pet_supplier = res.data.setting.value.multi_pet_supplier
    } else {

    }
  })
},
deleteRow(row) {
  this.$confirm("确定要删除吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    this.deleteApplication(row);
  });
},
statusCode(res) {
  if (this.formData.status_code == 0) {
    this.rules.callBackLink = {
      required: false,
      message: "请输入回调地址",
      trigger: "blur",
    }
  } else {
    this.rules.callBackLink = {
      required: true,
      message: "请输入回调地址",
      trigger: "blur",
    }
  }
  this.$nextTick(() => {
    this.$refs.form.clearValidate(res);
  });
},
    async onDelete() {
  const ids = [];
  if (this.multipleSelection.length == 0) {
    this.$message({
      type: "warning",
      message: "请选择要删除的数据",
    });
    return;
  }
  this.multipleSelection &&
    this.multipleSelection.map((item) => {
      ids.push(item.id);
    });
  const res = await deleteApplicationByIds({ ids });
  if (res.code == 0) {
    this.$message({
      type: "success",
      message: "删除成功",
    });
    if (this.tableData.length == ids.length) {
      this.page--;
    }
    this.deleteVisible = false;
    this.getTableData();
  }
},
    async updateApplication(row) {
  this.getUserOption();
  this.titleDialog = "编辑";
  const res = await findApplication({ id: row.id });
  this.type = "update";
  if (res.code == 0) {
    this.formData = res.data.reapplication;
    this.formData.pet_supplier_id = this.formData.pet_supplier_id ? this.formData.pet_supplier_id : null
    this.formData.supplier_id = this.formData.supplier_id ? this.formData.supplier_id : null
    this.gysarr.application_id = res.data.reapplication.id
    this.formData.pet_suppliers = res.data.reapplication.pet_suppliers
    this.selectedOptions = [
      String(this.formData.provinceId),
      String(this.formData.cityId),
      String(this.formData.districtId),
    ];
    this.statusCode('callBackLink') // 点开刷新callBackLink
    this.dialogFormVisible = true;
  }
},
closeDialog() {
  try {
    this.$refs.form.resetFields();
  } catch {
  } finally {
    this.dialogFormVisible = false;
    this.titleDialog = "新增";
    this.formData = {
      companyName: "",
      companyIntro: "",
      provinceId: 0,
      cityId: 0,
      districtId: 0,
      is_check_sign: 0,
      is_down_price: 0,
      address: "",
      creditCode: "",
      businessLicense: "",
      legalPersonName: "",
      idCardNumber: "",
      idCardFront: "",
      idCardBackend: "",
      contactsName: "",
      contactsPhontnumber: "",
      contactsEmail: "",
      appName: "",
      appLevelId: "",
      callBackLink: "",
      ipList: "",
      pet_suppliers: [],
      memberId: null,
      export_link: "",
      group_id: null,
      status_code: 0,
      is_multi_shop: 2,
      is_message_pool: 0,
    };
    this.selectedOptions = [];
  }
},
    async deleteApplication(row) {
  const res = await deleteApplication({ id: row.id });
  if (res.code == 0) {
    this.$message({
      type: "success",
      message: "删除成功",
    });
    if (this.tableData.length == 1) {
      this.page--;
    }
    this.getTableData();
  }
},
    async enterDialog() {
  let valid = await this.$refs.form.validate();
  if (valid) {
    let res;
    this.formData.pet_suppliers = []
    this.formData.appLevelId = parseInt(this.formData.appLevelId);
    if (this.formData.pet_supplier_id === "") {
      this.formData.pet_supplier_id = null
    }
    if (this.formData.supplier_id === "") {
      this.formData.supplier_id = null
    }
    switch (this.type) {
      case "create":
        res = await createApplication(this.formData);
        break;
      case "update":
        res = await updateApplication(this.formData);
        break;
      default:
        res = await createApplication(this.formData);
        break;
    }

    if (res.code == 0) {
      this.$message({
        type: "success",
        message: "创建/更改成功",
      });
      if (this.type == "create") {
        this.gysarr.application_id = res.data.id
      }
      this.gysarr.pet_supplier_ids = this.formData.pet_supplier_ids
      createApplicationPetSupplier(this.gysarr).then((res) => {
      })
      this.closeDialog();
      this.getTableData();
    }
  } else {
    return false;
  }
},
openDialog() {
  this.getUserOption();
  this.type = "create";
  this.statusCode('callBackLink') // 点开刷新callBackLink
  this.dialogFormVisible = true;
},
    async setLevelOption() {
  const options = await this.levelApi();
  if (options.code == 0) {
    options.data.list.map((item) => {
      const option = {
        value: item.id,
        label: item.levelName,
      };
      this.levelOptions.push(option);
    });
  }
},
 async verifyfun(){
  const res = await validateCallback({ link: this.formData.callBackLink })
  if (res.code == 0) {
    this.$message({
      type: "success",
      message: res.msg,
    });
  }

}
  },
  async created() {
  this.getSupplier();
  await this.getTableData();
  await this.setLevelOption();
  this.findSetting();
  this.getGroupOption();
},
};
</script>

<style lang="scss" scoped>
@import "@/style/base.scss";

.verify {
  margin-left: 10px;
}

.title-p {
  font-size: 16px;
  font-weight: bold;
  margin-top:50px;
  margin-bottom:20px;
}

.text-p{
  font-size:12px;
  line-height: 25px;
  margin-top:10px
}

.dialog_shengchuang{
  width: 200px;
  margin: 0 auto;
}
</style>
