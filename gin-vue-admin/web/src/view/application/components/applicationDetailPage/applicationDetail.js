import {
    findApplication,
    getApplicationLevelOption
} from "@/api/application";
import { regionData } from "element-china-area-data";

export default {
    name: "applicationDetail",
    data() {
        return {
            dialogVisible:false,
            dialogImageUrl:"",
            isShow: false,
            appInfo: {},
            options: regionData,
            selectedOptions: [],
            levelOption: [],
            application_pay_sort: []
        }
    },
    methods: {
        openDialogImg(url) {
            if (url) {
                this.dialogVisible = true
                this.dialogImageUrl = url
            }
        },
        handleDialogImgClose() {
            this.dialogVisible = false
            this.dialogImageUrl = ""
        },
        handleClose() {
            this.isShow = false
        },
        getApplication(id) {
            findApplication({ id }).then(res => {
                if (res.code === 0) {
                    this.appInfo = res.data.reapplication
                    this.selectedOptions = [
                        this.appInfo.provinceId.toString(),
                        this.appInfo.cityId.toString(),
                        this.appInfo.districtId.toString(),
                    ];
                    this.application_pay_sort = res.data.reapplication.application_pay_sort
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        // 获取等级option
        getLevelOption() {
            getApplicationLevelOption().then(res => {
                if (res.code === 0) {
                    this.levelOption = res.data.list

                } else {
                    this.$message.error(res.msg)
                }

            })
        }
    }
}