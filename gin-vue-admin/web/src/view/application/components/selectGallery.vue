<template>
    <m-card>
        <el-drawer
            :title="title + ' > 选品库'"
            :visible.sync="drawer"
            direction="rtl"
            size="90%"
        >
            <el-form :model="searchForm" ref="form" class="mt25" inline>
                <el-form-item label="" prop="title">
                    <el-input
                        v-model="searchForm[goodsCommand]"
                        placeholder="请输入"
                        clearable
                        v-if="goodsCommand !== 'isVideoShop'"
                        class="line-input-width"
                    >
                        <template slot="prepend">
                            <el-dropdown
                                class="el-dropdown-row"
                                @command="handleCommand"
                            >
                                <span class="el-dropdown-link">
                                    {{ returnGoodsValue(goodsCommand)
                                    }}<i
                                        class="el-icon-arrow-down el-icon--right"
                                    ></i>
                                </span>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item command="title"
                                        >商品名称</el-dropdown-item
                                    >
                                    <el-dropdown-item command="sn"
                                        >商品条形码</el-dropdown-item
                                    >
                                    <el-dropdown-item command="isVideoShop"
                                        >视频号商品</el-dropdown-item
                                    >
                                    <el-dropdown-item command="id"
                                        >商品ID</el-dropdown-item
                                    >
                                </el-dropdown-menu>
                            </el-dropdown>
                        </template>
                    </el-input>
                    <div
                        class="line-input"
                        v-if="goodsCommand == 'isVideoShop'"
                    >
                        <div class="line-box">
                            <el-dropdown
                                class="el-dropdown-row"
                                @command="handleCommand"
                            >
                                <span class="el-dropdown-link">
                                    {{ returnGoodsValue(goodsCommand)
                                    }}<i
                                        class="el-icon-arrow-down el-icon--right"
                                    ></i>
                                </span>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item command="title"
                                        >商品名称</el-dropdown-item
                                    >
                                    <el-dropdown-item command="sn"
                                        >商品条形码</el-dropdown-item
                                    >
                                    <el-dropdown-item command="isVideoShop"
                                        >视频号商品</el-dropdown-item
                                    >
                                    <el-dropdown-item command="id"
                                        >商品ID</el-dropdown-item
                                    >
                                </el-dropdown-menu>
                            </el-dropdown>
                        </div>
                        <el-select v-model="searchForm.isVideoShop">
                            <el-option label="全部" value="0"></el-option>
                            <el-option label="中台商品" value="-1"></el-option>
                            <el-option label="视频号商品" value="1"></el-option>
                        </el-select>
                    </div>
                </el-form-item>
                <el-form-item label="">
                    <div class="line-input">
                        <div class="line-box">
                            <span>商品状态</span>
                        </div>
                        <el-select v-model="goodsStatus">
                            <el-option
                                v-for="item in goodsStatusList"
                                :key="item.id"
                                :label="item.name"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                </el-form-item>
                <el-form-item label="" prop="shopTitle">
                    <el-input
                        v-model="searchForm[shopCommand]"
                        placeholder="请输入"
                        clearable
                        v-if="shopCommand == 'shopTitle'"
                        class="line-input-width"
                    >
                        <template slot="prepend">
                            <el-dropdown
                                class="el-dropdown-row"
                                @command="handleShopCommand"
                            >
                                <span class="el-dropdown-link">
                                    {{ returnShopValue(shopCommand)
                                    }}<i
                                        class="el-icon-arrow-down el-icon--right"
                                    ></i>
                                </span>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item command="shopTitle"
                                        >店铺名称</el-dropdown-item
                                    >
                                    <el-dropdown-item command="supplier_id"
                                        >供应商</el-dropdown-item
                                    >
                                    <el-dropdown-item command="gather_supply_id"
                                        >供应链</el-dropdown-item
                                    >
                                </el-dropdown-menu>
                            </el-dropdown>
                        </template>
                    </el-input>

                    <div class="line-input" v-if="shopCommand == 'supplier_id'">
                        <div class="line-box">
                            <el-dropdown
                                class="el-dropdown-row"
                                @command="handleShopCommand"
                            >
                                <span class="el-dropdown-link">
                                    {{ returnShopValue(shopCommand)
                                    }}<i
                                        class="el-icon-arrow-down el-icon--right"
                                    ></i>
                                </span>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item command="shopTitle"
                                        >店铺名称</el-dropdown-item
                                    >
                                    <el-dropdown-item command="supplier_id"
                                        >供应商</el-dropdown-item
                                    >
                                    <el-dropdown-item command="gather_supply_id"
                                        >供应链</el-dropdown-item
                                    >
                                </el-dropdown-menu>
                            </el-dropdown>
                        </div>
                        <el-select
                            v-model="searchForm.supplier_id"
                            filterable
                            clearable
                        >
                            <el-option label="全部" value=""></el-option>
                            <el-option label="平台自营" value="0"></el-option>
                            <el-option
                                label="全部供应商"
                                value="999999"
                            ></el-option>
                            <el-option
                                v-for="item in supplierList"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </div>

                    <div
                        class="line-input"
                        v-if="shopCommand == 'gather_supply_id'"
                    >
                        <div class="line-box">
                            <el-dropdown
                                class="el-dropdown-row"
                                @command="handleShopCommand"
                            >
                                <span class="el-dropdown-link">
                                    {{ returnShopValue(shopCommand)
                                    }}<i
                                        class="el-icon-arrow-down el-icon--right"
                                    ></i>
                                </span>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item command="shopTitle"
                                        >店铺名称</el-dropdown-item
                                    >
                                    <el-dropdown-item command="supplier_id"
                                        >供应商</el-dropdown-item
                                    >
                                    <el-dropdown-item command="gather_supply_id"
                                        >供应链</el-dropdown-item
                                    >
                                </el-dropdown-menu>
                            </el-dropdown>
                        </div>
                        <el-select
                            v-model="searchForm.gather_supply_id"
                            @change="supplyFun"
                            filterable
                            clearable
                        >
                            <el-option label="全部" value=""></el-option>
                            <el-option label="平台自营" value="0"></el-option>
                            <el-option
                                :label="item.name"
                                :value="item.id"
                                v-for="item in supplyOptions"
                                :key="item.id"
                            >
                            </el-option>
                        </el-select>
                    </div>
                </el-form-item>
                <el-form-item label="" prop="category1_id">
                    <div class="line-input">
                        <div class="line-box">
                            <span>一级分类 </span>
                        </div>
                        <el-select
                            v-model="searchForm.category1_id"
                            placeholder="请选择一级分类"
                            filterable
                            clearable
                            @change="
                                handleClassiyfChange(2, searchForm.category1_id)
                            "
                        >
                            <el-option
                                v-for="item in categoryList1"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </div>
                </el-form-item>
                <el-form-item label="" prop="category2_id">
                    <div class="line-input">
                        <div class="line-box">
                            <span>二级分类</span>
                        </div>
                        <el-select
                            v-model="searchForm.category2_id"
                            placeholder="请选择二级分类"
                            filterable
                            clearable
                            @change="
                                handleClassiyfChange(3, searchForm.category2_id)
                            "
                        >
                            <el-option
                                v-for="item in categoryList2"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </div>
                </el-form-item>
                <el-form-item label="" prop="category3_id">
                    <div class="line-input">
                        <div class="line-box">
                            <span>三级分类</span>
                        </div>
                        <el-select
                            v-model="searchForm.category3_id"
                            placeholder="请选择三级分类"
                            filterable
                            clearable
                        >
                            <el-option
                                v-for="item in categoryList3"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </div>
                </el-form-item>
                <el-form-item label="">
                    <div class="line-input">
                        <div class="line-box">
                            <span>价格区间</span>
                        </div>
                        <div class="f fac">
                            <el-input
                                v-model="searchForm.minPrice"
                                @input="
                                    (v) =>
                                        (searchForm.minPrice = v
                                            .replace(/[^\d.]/g, '')
                                            .replace(/\.{2,}/g, '.')
                                            .replace(
                                                /^(\d+)\.(\d\d).*$/,
                                                '$1.$2',
                                            ))
                                "
                                clearable
                                placeholder="最低价"
                            ></el-input>
                            <span class="zih-span mr_10">至</span>
                            <el-input
                                v-model="searchForm.maxPrice"
                                @input="
                                    (v) =>
                                        (searchForm.maxPrice = v
                                            .replace(/[^\d.]/g, '')
                                            .replace(/\.{2,}/g, '.')
                                            .replace(
                                                /^(\d+)\.(\d\d).*$/,
                                                '$1.$2',
                                            ))
                                "
                                clearable
                                placeholder="最高价"
                            ></el-input>
                        </div>
                    </div>
                </el-form-item>
                <el-form-item label="" prop="brand_id">
                    <div class="line-input">
                        <div class="line-box">
                            <span>品牌</span>
                        </div>
                        <el-select
                            v-model="searchForm.brand_id"
                            clearable
                            filterable
                            remote
                            :remote-method="remoteMethod"
                            :loading="brandsOptiosData.loading"
                        >
                            <el-option
                                v-for="item in brandsOptiosData.brandsOptios"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                            <div class="text-center">
                                <element
                                    background
                                    small
                                    class="pagination"
                                    style="
                                        padding-top: 10px !important;
                                        padding-bottom: 0 !important;
                                    "
                                    :current-page="brandsOptiosData.page"
                                    :page-size="brandsOptiosData.pageSize"
                                    :total="brandsOptiosData.total"
                                    @current-change="handleBrandPage"
                                    layout="prev,pager, next"
                                />
                            </div>
                        </el-select>
                    </div>
                </el-form-item>
                <el-form-item label="">
                    <div class="line-input">
                        <div class="line-box">
                            <span>营销属性</span>
                        </div>
                        <el-select v-model="arketingChecked" clearable>
                            <el-option
                                v-for="item in arketingOptios"
                                :key="item.id"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>
                    </div>
                </el-form-item>
                <el-form-item label="" prop="status_lock">
                    <div class="line-input">
                        <div class="line-box">
                            <span>锁定状态</span>
                        </div>
                        <el-select v-model="searchForm.status_lock" clearable>
                            <el-option label="全部" value=""></el-option>
                            <el-option label="已锁定" :value="1"></el-option>
                            <el-option label="未锁定" :value="0"></el-option>
                        </el-select>
                    </div>
                </el-form-item>
                <el-form-item label="" prop="sortType">
                    <div class="line-input">
                        <div class="line-box">
                            <span>排序</span>
                        </div>
                        <el-select v-model="searchForm.sortType" clearable>
                            <el-option label="ID" value="0"></el-option>
                            <el-option label="供货价" value="1"></el-option>
                            <el-option label="成本价" value="2"></el-option>
                            <el-option label="零售价" value="3"></el-option>
                            <el-option label="利润率" value="4"></el-option>
                            <el-option label="销量" value="5"></el-option>
                            <el-option label="Sort" value="6"></el-option>
                        </el-select>
                    </div>
                </el-form-item>
                <el-form-item label="" prop="sortMode">
                    <div class="line-input">
                        <div class="line-box">
                            <span>排序方向</span>
                        </div>
                        <el-select v-model="searchForm.sortMode" clearable>
                            <el-option label="从小到大" value="asc"></el-option>
                            <el-option
                                label="从大到小"
                                value="desc"
                            ></el-option>
                        </el-select>
                    </div>
                </el-form-item>
                <el-form-item label="" prop="coding">
                    <div class="line-input">
                        <div class="line-box">
                            <span>赋码状态</span>
                        </div>
                        <el-select v-model="searchForm.coding" clearable>
                            <el-option label="已赋码" value="1"></el-option>
                            <el-option label="未赋码" value="0"></el-option>
                        </el-select>
                    </div>
                </el-form-item>
                <el-form-item label="" prop="member_price">
                    <div class="line-input">
                        <div class="line-box">
                            <span>会员价</span>
                        </div>
                        <el-select v-model="searchForm.member_price" clearable>
                            <el-option
                                label="商品开启独立规则"
                                value="1"
                            ></el-option>
                            <el-option
                                label="商品未开启独立规则"
                                value="0"
                            ></el-option>
                        </el-select>
                    </div>
                </el-form-item>
                <div class="f">
                    <el-button type="primary" @click="search">搜索</el-button>
                    <el-button @click="exportSelectGallery">导出</el-button>
                    <el-button type="text" @click="resetForm">
                        重置搜索条件
                    </el-button>
                    <el-button type="primary" @click="setting">设置</el-button>
                </div>
            </el-form>
            <div class="f fac mt_20">
                <div>商品数量：{{ product_count }}</div>
                <div class="ml_20">累计订单数量：{{ order_count }}单</div>
                <div class="ml_20">
                    累计订单金额：{{ order_amount | formatF2Y }}元
                </div>
                <div class="ml_20">
                    最近选品时间：{{ ShareAt | formatDate }}
                </div>
            </div>
            <el-table class="mt_20" :data="tableData">
                <el-table-column
                    type="selection"
                    width="55"
                    align="center"
                ></el-table-column>
                <el-table-column width="80" label="ID">
                    <template slot-scope="scope">
                        <p>{{ scope.row.id }}</p>
                    </template>
                </el-table-column>
                <el-table-column
                    label="图片"
                    width="140"
                    align="center"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        <el-popover placement="right" title="" trigger="hover">
                            <m-image
                                :src="scope.row.image_url"
                                :style="{
                                    width: '160px',
                                    height: '160px',
                                    borderRadius: '16px 16px 16px 16px',
                                }"
                            ></m-image>
                            <m-image
                                slot="reference"
                                :src="scope.row.image_url"
                                :alt="scope.row.image_url"
                                :style="{
                                    width: '60px',
                                    height: '60px',
                                    borderRadius: '16px 16px 16px 16px',
                                }"
                            ></m-image>
                        </el-popover>
                    </template>
                </el-table-column>
                <el-table-column label="商品" min-width="300">
                    <template slot-scope="scope">
                        <p
                            style="
                                height: auto !important;
                                line-height: 23px !important;
                            "
                        >
                            {{ scope.row.title }}
                        </p>
                        <p v-if="scope.row.sn" class="sn-code">
                            编码：{{ scope.row.sn }}
                        </p>
                        <p
                            v-if="scope.row.gather_supply_id > 0"
                            class="sn-code"
                        >
                            <span
                                v-if="
                                    scope.row.source === 119 ||
                                    scope.row.source === 121 ||
                                    scope.row.source === 122
                                "
                                >第三方商品ID：{{
                                    scope.row.source_goods_id_string
                                }}</span
                            >
                            <span v-else
                                >第三方商品ID：{{
                                    scope.row.source_goods_id
                                }}</span
                            >
                            <span
                                v-if="
                                    scope.row.source === 119 ||
                                    scope.row.source === 121 ||
                                    scope.row.source === 122
                                "
                                @click="
                                    $fn.copy(scope.row.source_goods_id_string)
                                "
                                class="copy"
                                >复制</span
                            >
                            <span
                                v-else
                                @click="$fn.copy(scope.row.source_goods_id)"
                                class="copy"
                                >复制</span
                            >
                        </p>
                    </template>
                </el-table-column>
                <el-table-column
                    align="center"
                    width="150"
                    show-overflow-tooltip
                >
                    <template slot="header">
                        <p>供货价</p>
                    </template>
                    <template slot-scope="scope">
                        <p v-if="scope.row.skus.length === 1">
                            ￥{{ scope.row.skus[0].price | formatF2Y }}
                        </p>
                        <p v-else-if="scope.row.skus.length > 1">
                            ￥{{ scope.row.minPrice | formatF2Y }}-{{
                                scope.row.maxPrice | formatF2Y
                            }}
                        </p>
                        <p v-else>￥{{ scope.row.price | formatF2Y }}</p>
                    </template>
                </el-table-column>
                <el-table-column
                    align="center"
                    width="150"
                    show-overflow-tooltip
                >
                    <template slot="header">
                        <p>成本价</p>
                    </template>
                    <template slot-scope="scope">
                        <p v-if="scope.row.skus.length === 1">
                            ￥{{ scope.row.skus[0].exec_price | formatF2Y }}
                        </p>
                        <p v-else-if="scope.row.skus.length > 1">
                            ￥{{ scope.row.minCostPrice | formatF2Y }}-{{
                                scope.row.maxCostPrice | formatF2Y
                            }}
                        </p>
                        <p v-else>￥{{ scope.row.cost_price | formatF2Y }}</p>
                    </template>
                </el-table-column>
                <el-table-column
                    align="center"
                    width="150"
                    show-overflow-tooltip
                >
                    <template slot="header">
                        <p>零售价 / 指导价</p>
                    </template>
                    <template slot-scope="scope">
                        <p v-if="scope.row.skus.length === 1">
                            ￥{{ scope.row.skus[0].origin_price | formatF2Y }}
                        </p>
                        <p v-else-if="scope.row.skus.length > 1">
                            ￥{{ scope.row.minOriginPrice | formatF2Y }}-{{
                                scope.row.maxOriginPrice | formatF2Y
                            }}
                        </p>
                        <p v-else>￥{{ scope.row.origin_price | formatF2Y }}</p>
                        <p v-if="scope.row.skus.length === 1">
                            ￥{{ scope.row.skus[0].guide_price | formatF2Y }}
                        </p>
                        <p v-else>￥{{ scope.row.guide_price | formatF2Y }}</p>
                    </template>
                </el-table-column>

                <el-table-column
                    label="利润率"
                    width="150"
                    align="center"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        <p v-if="scope.row.skus.length > 1">
                            {{ scope.row.min_profit_rate }}%-{{
                                scope.row.max_profit_rate
                            }}%
                        </p>
                        <p v-else>{{ scope.row.min_profit_rate }}%</p>
                    </template>
                </el-table-column>
                <el-table-column
                    align="center"
                    width="100"
                    show-overflow-tooltip
                >
                    <template slot="header">
                        <p>库存 / 销量</p>
                    </template>
                    <template slot-scope="scope">
                        <p>{{ scope.row.stock }}</p>
                        <p>{{ scope.row.sales }}</p>
                    </template>
                </el-table-column>
                <el-table-column
                    label="品牌"
                    align="center"
                    prop="brand.name"
                    show-overflow-tooltip
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="供应渠道"
                    align="center"
                    width="100"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        <span v-if="scope.row.supplier_id > 0">{{
                            scope.row.supplier.name
                        }}</span>
                        <span v-else-if="scope.row.gather_supply_id > 0">{{
                            scope.row.gather_supply.name
                        }}</span>
                        <span
                            v-else-if="
                                scope.row.supplier_id === 0 &&
                                scope.row.gather_supply_id === 0
                            "
                            >自营</span
                        >
                        <p v-if="scope.row.source_name">
                            {{ scope.row.source_name }}
                        </p>
                    </template>
                </el-table-column>
                <el-table-column width="100" align="center">
                    <template slot="header">
                        <p>上下架状态</p>
                        <p>锁定状态</p>
                    </template>
                    <template slot-scope="scope">
                        <p>
                            <el-switch
                                v-model="scope.row.is_display"
                                :active-value="1"
                                :inactive-value="0"
                                @change="handleStatus(scope.row, 'is_display')"
                            >
                            </el-switch>
                        </p>
                        <p>
                            <el-switch
                                v-model="scope.row.status_lock"
                                :active-value="1"
                                :inactive-value="0"
                                @change="handleStatus(scope.row, 'status_lock')"
                            >
                            </el-switch>
                        </p>
                    </template>
                </el-table-column>
                <el-table-column label="选品时间" align="center" width="100">
                    <template slot-scope="scope">
                        <p>{{ scope.row.updated_at | formatDate }}</p>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                :current-page="page"
                :page-size="pageSize"
                :page-sizes="[10, 30, 50, 100, 200]"
                :style="{
                    display: 'flex',
                    justifyContent: 'flex-end',
                    marginRight: '20px',
                }"
                :total="total"
                @current-change="handleCurrentChange"
                @size-change="handleSizeChange"
                layout="total, sizes,prev, pager, next, jumper"
            >
            </el-pagination>
        </el-drawer>
        <el-dialog title="设置" :visible.sync="dialogVisible" width="30%">
            <el-form :model="settingForm" label-width="160px">
                <el-form-item label="开启选品库共享">
                    <el-switch
                        v-model="settingForm.share"
                        :active-value="1"
                        :inactive-value="0"
                    >
                    </el-switch>
                </el-form-item>
                <el-form-item>
                    <span slot="label">
                        <span class="color-red">*</span>
                        <span>共享选品库名称</span>
                    </span>
                    <el-input
                        v-model="settingForm.name"
                        placeholder="请输入"
                    ></el-input>
                </el-form-item>
                <el-form-item label-width="200px">
                    <el-button type="primary" @click="save">保存</el-button>
                    <el-button @click="dialogVisible = false"> 取消 </el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
    </m-card>
</template>

<script>
import { getSupplierOptionList, upStatus } from '@/api/goods';
import { getClassify } from '@/api/classify';
import { getBrandsList } from '@/api/brands';
import { getSupplyList } from '@/api/order';
import { getGatherSupplyAndSource } from '@/api/gatherSupply';
import { getProductList, updateShare, exportProducts } from '@/api/application';
export default {
    data() {
        return {
            drawer: false,
            app_id: null,
            dialogVisible: false,
            title: '',
            arketingChecked: '',
            arketingOptios: [
                { label: '全部', value: '' },
                { label: '热卖', value: 'is_hot' },
                { label: '促销', value: 'is_promotion' },
                { label: '新品', value: 'is_new' },
            ],
            searchForm: {
                id: null, //商品id
                // filter: 0,
                // 商品
                title: '',
                category1_id: '',
                category2_id: '',
                category3_id: '',
                //店铺名称
                shopTitle: '',
                // 最高价
                maxPrice: '',
                // 最低价
                minPrice: '',
                gather_supply_id: null,
                // 供应商
                supplier_id: null,
                sn: '',
                brand_id: null,
                status_lock: '', // 锁定状态
                sortType: '', //
                sortMode: '', //
                coding: '', // 赋码
                member_price: '', // 会员价
                isVideoShop: '', //
                source_name: null,
            },
            // 类目1
            categoryList1: [],
            categoryList2: [],
            categoryList3: [],
            goodsCommand: 'title',
            shopCommand: 'shopTitle',
            goodsStatus: '0',
            goodsStatusList: [
                { name: '全部', value: '0' },
                { name: '上架', value: '1' },
                { name: '下架', value: '2' },
                { name: '售罄', value: '3' },
            ],
            brandsOptiosData: {
                name: '',
                brandsOptios: [],
                loading: false,
                page: 1,
                pageSize: 10,
                total: 0,
            },
            goodsResources: false,
            page: 1,
            pageSize: 10,
            total: 0,
            tableData: [], // 表格数据
            settingForm: {
                share: 0,
                name: '',
            },
            product_count: 0,
            order_count: 0,
            order_amount: 0,
            ShareAt: 0,
        };
    },
    mounted() {
        // 获取一级类目
        getClassify(1, 0).then((r) => {
            this.categoryList1 = r.data.list;
            // this.categoryListDialog1 = r.data.list;
        });
        this.getBrandsOptios();
        this.getSupply();
        // 获取供应商
        getSupplierOptionList().then((r) => {
            this.supplierList = r.data.list;
        });
    },
    methods: {
        // 获取商品列表
        async getProductList() {
            // let params = this.searchForm;
            let params = {
                "page": this.page,
                "pageSize": this.pageSize,
                "title": this.searchForm.title,
                "category1_id": this.searchForm.category1_id,
                "category2_id": this.searchForm.category2_id,
                "category3_id": this.searchForm.category3_id,
                "maxPrice": this.searchForm.maxPrice,
                "minPrice": this.searchForm.minPrice,
                "brand_id": this.searchForm.brand_id,
                "id": this.searchForm.id,
                "barcode": this.searchForm.sn,
                "shop_name": this.searchForm.shopTitle,
            }

            if (this.searchForm.gather_supply_id !== "") {
                params.gather_supply_id = this.searchForm.gather_supply_id
            }

            if(this.goodsResources){
                if(this.searchForm.source_name !== ""){
                    params.source_name = this.searchForm.source_name
                }
            }
            if (this.searchForm.supplier_id !== "") {
                params.supplier_id = this.searchForm.supplier_id
            }
            if (this.arketingChecked !== "") {
                params[this.arketingChecked] = 1
            }
            if (this.searchForm.status_lock !== "") {
                params.status_lock = this.searchForm.status_lock
            }
            if (this.searchForm.sortType !== "") {
                params.sortType = this.searchForm.sortType
            }
            if (this.searchForm.sortMode !== "") {
                params.sortMode = this.searchForm.sortMode
            }
            if (this.searchForm.coding !== "") {
                params.coding = this.searchForm.coding
            }
            if (this.searchForm.member_price !== "") {
                params.member_price = this.searchForm.member_price
            }
            if (this.searchForm.isVideoShop !== "") {
                params.is_video_shop = this.searchForm.isVideoShop
            }
            if (this.searchForm.member_price !== "") {
                params.member_price = this.searchForm.member_price
            }
            params.app_id = parseInt(this.app_id);
            params.filter = this.goodsStatus;
            params.arketingChecked = this.arketingChecked;
            const res = await getProductList(params);
            if (res.code === 0) {
                this.tableData = res.data.list;
                this.total = res.data.total;
                this.settingForm.name = res.data.stats.name;
                this.settingForm.share = res.data.stats.share;
                this.product_count = res.data.stats.product_count;
                this.order_count = res.data.stats.order_count;
                this.order_amount = res.data.stats.order_amount;
                this.ShareAt = res.data.stats.recent_selection_at;
            }
        },
        // 获取类目
        handleClassiyfChange(level, pid) {
            getClassify(level, pid).then((r) => {
                if (level === 3) {
                    this.categoryList3 = [];
                    this.searchForm.category3_id = '';
                } else {
                    this.categoryList2 = [];
                    this.searchForm.category2_id = '';
                    this.categoryList3 = [];
                    this.searchForm.category3_id = '';
                }
                switch (level) {
                    case 2:
                        this.categoryList2 = r.data.list;
                        break;
                    case 3:
                        this.categoryList3 = r.data.list;
                        break;
                }
            });
        },
        // 品牌搜索
        remoteMethod(query) {
            this.brandsOptiosData.name = query;
            this.brandsOptiosData.page = 1;
            this.getBrandsOptios();
        },
        handleBrandPage(val) {
            this.brandsOptiosData.page = val;
            this.getBrandsOptios();
        },
        // 获取品牌
        async getBrandsOptios() {
            let params = {
                page: this.brandsOptiosData.page,
                pageSize: this.brandsOptiosData.pageSize,
            };
            if (this.brandsOptiosData.name)
                params.name = this.brandsOptiosData.name;
            this.brandsOptiosData.loading = true;
            let res = await getBrandsList(params);
            this.brandsOptiosData.loading = false;
            if (res.code === 0) {
                this.brandsOptiosData.brandsOptios = res.data.list;
                this.brandsOptiosData.total = res.data.total;
            }
        },
        // 获取供应链
        getSupply() {
            getSupplyList().then((res) => {
                this.supplyOptions = res.data.list;
            });
        },
        returnGoodsValue(command) {
            let text = '商品名称';
            switch (command) {
                case 'title':
                    text = '商品名称';
                    break;
                case 'sn':
                    text = '商品条形码';
                    break;
                case 'isVideoShop':
                    text = '视频号商品';
                    break;
                case 'id':
                    text = '商品ID';
                    break;
                default:
                    break;
            }
            return text;
        },
        handleCommand(command) {
            this.goodsCommand = command;
            // 切换选项时清空所有数值
            this.searchForm.id = null;
            this.searchForm.title = '';
            this.searchForm.sn = '';
            this.searchForm.isVideoShop = '';
        },
        handleShopCommand(command) {
            this.shopCommand = command;
            this.searchForm.gather_supply_id = null;
            this.searchForm.supplier_id = null;
            this.searchForm.shopTitle = '';
            this.searchForm.source_name = null;
            if (this.shopCommand !== 'gather_supply_id') {
                this.goodsResources === false;
            }
        },
        returnShopValue(command) {
            let text = '店铺名称';
            switch (command) {
                case 'shopTitle':
                    text = '店铺名称';
                    break;
                case 'supplier_id':
                    text = '供应商';
                    break;
                case 'gather_supply_id':
                    text = '供应链';
                    break;
                default:
                    break;
            }
            return text;
        },
        supplyFun(res) {
            this.searchForm.source_name = '';
            let arr = this.supplyOptions.find((item) => item.id === res);
            if (!arr) {
                this.goodsResources = false;
                return;
            }
            if (arr.category_id === 1) {
                this.goodsResources = true;
                this.goodsResources_id = arr.category_id;
            } else if (arr.category_id === 2) {
                this.getSupplySource();
                this.goodsResources = true;
                this.goodsResources_id = arr.category_id;
            } else {
                this.goodsResources = false;
            }
        },
        async getSupplySource() {
            const { data } = await getGatherSupplyAndSource({
                id: parseInt(this.searchForm.gather_supply_id),
            });
            this.sourceOptions = data;
        },

        // 热卖 促销 新品
        async handleStatus(row, name) {
            let params = {
                column: name,
                id: row.id,
                status: row[name],
            };
            let res = await upStatus(params);
            if (res.code === 0) {
                this.$message.success(res.msg);
            }
        },
        // 搜索
        search() {
            this.page = 1;
            this.getProductList();
        },
        // 重置
        resetForm() {
            this.searchForm = {
                id: null, //商品id
                // filter: 0,
                // 商品
                title: '',
                category1_id: '',
                category2_id: '',
                category3_id: '',
                //店铺名称
                shopTitle: '',
                // 最高价
                maxPrice: '',
                // 最低价
                minPrice: '',
                gather_supply_id: null,
                // 供应商
                supplier_id: null,
                sn: '',
                brand_id: null,
                status_lock: '', // 锁定状态
                sortType: '', //
                sortMode: '', //
                coding: '', // 赋码
                member_price: '', // 会员价
                isVideoShop: '', //
                source_name: null,
            };
            this.goodsStatus = '0';
            this.arketingChecked = '';
            this.goodsCommand = 'title';
            this.shopCommand = 'shopTitle';
            this.brandsOptiosData = {
                name: '',
                brandsOptios: [],
                loading: false,
                page: 1,
                pageSize: 10,
                total: 0,
            };
            this.page = 1;
            this.pageSize = 10;
            this.remoteMethod();
        },
        // 导出
        async exportSelectGallery() {
            let params = {
                "page": this.page,
                "pageSize": this.pageSize,
                "title": this.searchForm.title,
                "category1_id": this.searchForm.category1_id,
                "category2_id": this.searchForm.category2_id,
                "category3_id": this.searchForm.category3_id,
                "maxPrice": this.searchForm.maxPrice,
                "minPrice": this.searchForm.minPrice,
                "brand_id": this.searchForm.brand_id,
                "id": this.searchForm.id,
                "barcode": this.searchForm.sn,
                "shop_name": this.searchForm.shopTitle,
            }

            if (this.searchForm.gather_supply_id !== "") {
                params.gather_supply_id = this.searchForm.gather_supply_id
            }

            if(this.goodsResources){
                if(this.searchForm.source_name !== ""){
                    params.source_name = this.searchForm.source_name
                }
            }
            if (this.searchForm.supplier_id !== "") {
                params.supplier_id = this.searchForm.supplier_id
            }
            if (this.arketingChecked !== "") {
                params[this.arketingChecked] = 1
            }
            if (this.searchForm.status_lock !== "") {
                params.status_lock = this.searchForm.status_lock
            }
            if (this.searchForm.sortType !== "") {
                params.sortType = this.searchForm.sortType
            }
            if (this.searchForm.sortMode !== "") {
                params.sortMode = this.searchForm.sortMode
            }
            if (this.searchForm.coding !== "") {
                params.coding = this.searchForm.coding
            }
            if (this.searchForm.member_price !== "") {
                params.member_price = this.searchForm.member_price
            }
            if (this.searchForm.isVideoShop !== "") {
                params.is_video_shop = this.searchForm.isVideoShop
            }
            if (this.searchForm.member_price !== "") {
                params.member_price = this.searchForm.member_price
            }
            params.app_id = parseInt(this.app_id);
            params.filter = this.goodsStatus;
            params.arketingChecked = this.arketingChecked;
            const res = await exportProducts(params)
            if (res.code === 0) {
                this.$message.success(res.msg)
            }
        },
        // 设置
        setting() {
            this.dialogVisible = true;
        },
        // 保存
        async save() {
            if (!this.settingForm.name) {
                this.$message.error('选品库名称不能为空！');
                return;
            }
            const data = {
                app_id: parseInt(this.app_id),
                name: this.settingForm.name,
                share: this.settingForm.share,
            };
            const res = await updateShare(data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.getProductList();
                this.dialogVisible = false;
            }
        },
        handleCurrentChange(page) {
            this.page = page;
            this.getProductList();
        },

        handleSizeChange(size) {
            this.pageSize = size;
            this.getProductList();
        },
    },
};
</script>

<style lang="scss" scoped></style>
