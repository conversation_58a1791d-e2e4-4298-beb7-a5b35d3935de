<template>
    <m-card>
        <el-form :model="searchForm" class="mt25" inline>
            <el-form-item>
                <div class="line-input line-input-date">
                    <div class="line-box">导出时间</div>
                    <div class="f fac" style="width: 440px">
                        <el-form-item label-width="0px">
                            <el-date-picker
                                class="w100"
                                placeholder="开始日期"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetime"
                                v-model="searchForm.start_at"
                            >
                            </el-date-picker>
                        </el-form-item>
                        <p class="title-3">至</p>
                        <el-form-item label-width="0px">
                            <el-date-picker
                                class="w100"
                                placeholder="结束日期"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetime"
                                v-model="searchForm.end_at"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </div>
                </div>
            </el-form-item>
            <el-form-item label="" prop="error_status">
                <div class="line-input">
                    <div class="line-box">
                        <span>导出状态</span>
                    </div>
                    <el-select v-model="searchForm.error_status" clearable>
                        <el-option label="成功" :value="1"></el-option>
                        <el-option label="失败" :value="2"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="search">搜索</el-button>
                <el-button type="text" @click="reSearch">
                    重置搜索条件
                </el-button>
            </el-form-item>
        </el-form>
        <el-table :data="tableData">
            <el-table-column label="导出时间" align="center">
                <template slot-scope="scope">
                    {{ scope.row.created_at | formatDate }}
                </template>
            </el-table-column>
            <el-table-column
                label="导出状态"
                prop="status_string"
                align="center"
            >
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        v-if="scope.row.link"
                        @click="download(scope.row.link)"
                        style="
                            padding: 0 !important;
                            margin-left: 10px !important;
                        "
                    >
                        下载
                    </el-button>
                    <el-button
                        type="text"
                        class="color-red"
                        @click="del(scope.row)"
                        style="
                            padding: 0 !important;
                            margin-left: 10px !important;
                        "
                    >
                        删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100, 200]"
            :style="{
                display: 'flex',
                justifyContent: 'flex-end',
                marginRight: '20px',
            }"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes,prev, pager, next, jumper"
        >
        </el-pagination>
    </m-card>
</template>

<script>
import { getExportRecordList, deleteExportRecord } from '@/api/application';
export default {
    name: 'shareExportListIndex',
    data() {
        return {
            searchForm: {
                start_at: '',
                end_at: '',
                error_status: null,
            },
            tableData: [],
            page: 1,
            pageSize: 10,
            total: null,
        };
    },
    mounted() {
        this.getExportRecordList();
    },
    methods: {
        // 获取导出列表
        async getExportRecordList() {
            const params = {
                ...this.searchForm,
                page: this.page,
                pageSize: this.pageSize,
            };
            const res = await getExportRecordList(params);
            if (res.code === 0) {
                this.tableData = res.data.list;
                this.total = res.data.total;
            }
        },
        // 搜索
        search() {
            this.page = 1;
            this.getExportRecordList();
        },
        // 重置搜索
        reSearch() {
            this.searchForm = {
                start_at: '',
                end_at: '',
                error_status: null,
            };
        },
        // 下载
        download(link) {
            window.open(this.$path + '/' + link);
        },
        // 删除
        del(row) {
            this.$confirm('确定删除吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                deleteExportRecord({ id: row.id }).then((res) => {
                    if (res.code === 0) {
                        this.$message.success(res.msg);
                        this.getExportRecordList();
                    }
                });
            });
        },
        handleCurrentChange(page) {
            this.page = page;
            this.getExportRecordList();
        },

        handleSizeChange(size) {
            this.pageSize = size;
            this.getExportRecordList();
        },
    },
};
</script>

<style lang="scss" scoped>
.line-input-date {
    width: 510px;
}
.color-red {
    color: red;
}
</style>
