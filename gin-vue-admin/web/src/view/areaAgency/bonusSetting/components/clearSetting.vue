<template>
  <el-row>
    <el-form :model="formData" label-width="130px">
      <el-form-item label="结算方式:">
        <el-radio-group v-model="formData.settle_mode">
          <el-radio :label="0">订单价格:(不包括运费及抵扣金额)</el-radio>
          <el-radio :label="1">利润:(订单最终价格-成本，负数取0)</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否开启平均分红:">
        <el-radio-group v-model="formData.avg_award_switch">
          <el-radio :label="0">否</el-radio>
          <el-radio :label="1">是</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否开启极差分红:">
        <el-radio-group v-model="formData.deduct_award_switch">
          <el-radio :label="0">否</el-radio>
          <el-radio :label="1">是</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="默认分红比例:">
        <el-col :span="13">
          <m-num-input :precision="2" v-model="formData.province_ratio" startText="省" endText="%"></m-num-input>
        </el-col>
      </el-form-item>
      <el-form-item>
        <el-col :span="13">
          <m-num-input :precision="2" v-model="formData.city_ratio" startText="市" endText="%"></m-num-input>
        </el-col>
      </el-form-item>
      <el-form-item>
        <el-col :span="13">
          <m-num-input :precision="2" v-model="formData.county_ratio" startText="区/县" endText="%"></m-num-input>
        </el-col>
      </el-form-item>
      <el-form-item>
        <el-col :span="13">
          <m-num-input v-model="formData.town_ratio" startText="街道/乡镇" endText="%"></m-num-input>
        </el-col>
      </el-form-item>
<!--      <el-form-item label="订单计算方式:">
        <el-radio-group v-model="formData.calculate_mode">
          <el-radio :label="0">实际金额</el-radio>
          <el-radio :label="1">利润</el-radio>
        </el-radio-group>
      </el-form-item>-->
      <!--      <el-form-item label="赠送积分计算方式:">
              <el-radio-group v-model="formData.pjfh">
                <el-radio :label="1">赠送比例</el-radio>
                <el-radio :label="2">固定积分</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item>
              <el-col :span="13">
                <m-num-input v-model="formData.aaaaa" startText="省" endText="%/个"></m-num-input>
              </el-col>
            </el-form-item>
            <el-form-item>
              <el-col :span="13">
                <m-num-input v-model="formData.aaaaa" startText="市" endText="%/个"></m-num-input>
              </el-col>
            </el-form-item>
            <el-form-item>
              <el-col :span="13">
                <m-num-input v-model="formData.aaaaa" startText="区/县" endText="%/个"></m-num-input>
              </el-col>
            </el-form-item>
            <el-form-item>
              <el-col :span="13">
                <m-num-input v-model="formData.aaaaa" startText="街道/乡镇" endText="%/个"></m-num-input>
              </el-col>
            </el-form-item>-->
<!--      <el-form-item label="结算类型:">-->
<!--        <el-radio-group v-model="formData.settle_type">-->
<!--          <el-radio :label="0">自动结算</el-radio>-->
<!--          <el-radio :label="1">手动结算</el-radio>-->
<!--        </el-radio-group>-->
<!--        <p class="color-grap">自动结算：订单完成后，根据结算期时间来加入到提现</p>-->
<!--        <p class="color-grap">手动结算：订单完成后，需要进入推广中心手动领取才可以提现</p>-->
<!--      </el-form-item>-->
      <el-form-item label="结算期:">
        <el-col :span="13">
          <m-num-input v-model="formData.settle_period" endText="天"></m-num-input>
        </el-col>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="save">保 存</el-button>
      </el-form-item>
    </el-form>
  </el-row>
</template>

<script>
export default {
  name: "clearSetting",
  data() {
    return {
      formData: {
        settle_mode: 0,// 结算方式:0订单价格1利润
        avg_award_switch: 0, // 是否开启平均分红
        deduct_award_switch: 0, // 是否开启极差分红
        province_ratio: 0, // 省:默认分红比例
        city_ratio: 0, // 市:默认分红比例
        county_ratio: 0, // 区:默认分红比例
        town_ratio: 0, // 街道:默认分红比例
        // calculate_mode: 0, // 订单计算方式:0实付金额1利润
        settle_type: 0, // 结算类型:0自动结算1手动结算
        settle_period: 0, // 结算期
      }
    }
  },
  methods: {
    save() {
      let data = {
        settle_mode: this.formData.settle_mode,// 结算方式:0订单价格1利润
        avg_award_switch: this.formData.avg_award_switch, // 是否开启平均分红
        deduct_award_switch: this.formData.deduct_award_switch, // 是否开启极差分红
        province_ratio: this.formData.province_ratio * 100, // 省:默认分红比例
        city_ratio: this.formData.city_ratio * 100, // 市:默认分红比例
        county_ratio: this.formData.county_ratio * 100, // 区:默认分红比例
        town_ratio: this.formData.town_ratio * 100, // 街道:默认分红比例
        // calculate_mode: this.formData.calculate_mode, // 订单计算方式:0实付金额1利润
        settle_type: this.formData.settle_type, // 结算类型:0自动结算1手动结算
        settle_period: this.formData.settle_period, // 结算期
      }
      this.$emit("save", data)
    }
  }
}
</script>

<style scoped>

</style>