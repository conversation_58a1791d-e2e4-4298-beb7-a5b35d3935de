<template>
  <el-drawer
    :visible="isShow"
    :close-on-press-escape="false"
    :wrapperClosable="false"
    :before-close="handleClose"
    size="calc(100% - 220px)"
    class="detail-ct"
  >
    <div slot="title">
      <div class="f fac">
        <p>开票订单</p>
<!--        <el-button type="danger" class="ml30">导出订单</el-button>-->
      </div>
    </div>
    <!-- 列表 -->
    <div v-for="item in list" :key="item.id">
      <el-table :data="item.order.order_items" class="table-cont">
        <el-table-column>
          <template slot="header">
            <div class="w100 f fac fjsb">
              <div class="f fac">
                <p>订单ID: {{ item.order ? item.order.id : "-" }}</p>
                <p>订单编号: {{ item.order ? item.order.order_sn : "-" }}</p>
                <p v-if="item.order && item.order.third_order_sn">
                  第三方订单编号:
                  {{ item.order ? item.order.third_order_sn : "-" }}
                </p>
                <p v-if="item.order && item.order.pay_sn">
                  支付单号: {{ item.order ? item.order.pay_sn : "-" }}
                </p>
                <p>下单时间: {{ item.created_at | formatDate }}</p>
                <el-tag class="ml10" type="warning">{{
                  nameData.supplier_name
                }}</el-tag>
              </div>
              <el-button type="text" @click="openDetail(item.order)">查看详情</el-button>
            </div>
          </template>
          <el-table-column width="300">
            <template slot-scope="scope">
              <div class="f fac goods-box">
                <m-image
                  style="width: 60px; height: 60px"
                  :src="scope.row.image_url"
                >
                </m-image>
                <p class="f1">
                  <a
                    href="javascript:;"
                    style="color: #155bd4"
                    @click="
                      $_blank('/layout/goodsIndex/addGoods', {
                        id: scope.row.product_id,
                      })
                    "
                  >
                    {{ scope.row.title }}
                  </a>
                </p>
              </div>
            </template>
          </el-table-column>
          <el-table-column width="210">
            <template slot-scope="scope">
              <div class="comm-box" style="width: 85%">
                <p>供货金额: {{ scope.row.supply_amount | formatF2Y }}</p>
                <p>金额: {{ scope.row.amount | formatF2Y }}</p>
                <p>数量: {{ scope.row.qty }}</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column width="200">
            <template slot-scope="scope">
              <div class="comm-box">
                <p class="title-3">
                  {{ nameData.user_name }}({{ scope.row.user_id }})
                  <!-- <el-button type="text" @click="toUserInfo(item.user.id)"> 
                    {{ item.user.username }}({{ item.user.id }})
                  </el-button> -->
                </p>
                <p class="title-3">
                  {{
                    item.order.shipping_address
                      ? item.order.shipping_address.realname
                      : "-"
                  }}
                </p>
              </div>
            </template>
          </el-table-column>
          <el-table-column width="200">
            <template slot-scope="scope">
              <div class="comm-box">
                <p class="title-3">
                  {{ item.order ? item.order.pay_type : "-" }}
                </p>
                <p class="title-3">快递</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column width="250">
            <template slot-scope="scope">
              <div class="comm-box" style="width: 85%">
                <div
                  v-for="amount_items in item.order.amount_detail.amount_items"
                  :key="amount_items.id"
                >
                  <p>
                    {{ amount_items.title }}: ￥{{
                      amount_items.amount | formatF2Y
                    }}
                  </p>
                </div>
                <p>
                  应付款: ￥{{ item.order.amount_detail.amount | formatF2Y }}
                </p>
              </div>
            </template>
          </el-table-column>
          <el-table-column width="200">
            <template slot-scope="scope">
              <div class="comm-box">
                <p class="title-3">
                  {{ item.order.status | formatStatus }}
                </p>
              </div>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
      <div class="table-foot-box">
        <div class="f fac">
          <p>{{ assemblyAddress(item.order.shipping_address) }}</p>
        </div>
      </div>
    </div>
    <order-detail ref="orderDetail"></order-detail>
  </el-drawer>
</template>
<script>
import OrderDetail from "@/view/order/allOrder/components/orderDetail";
export default {
  name: "billDrawer",
  components: { OrderDetail },
  data() {
    return {
      isShow: false,
      list: [],
      nameData: {
        supplier_name: "",
        user_name: "",
      },
    };
  },
  filters: {
    // 格式化订单状态
    formatStatus: function (status) {
      let name = "";
      switch (status) {
        case 0:
          name = "待付款";
          break;
        case 1:
          name = "待发货";
          break;
        case 2:
          name = "待收货";
          break;
        case 3:
          name = "已完成";
          break;
        case -1:
          name = "已关闭";
          break;
      }
      return name;
    },
  },
  methods: {
    // 打开订单详情
    openDetail(item) {
      this.$refs.orderDetail.isShow = true;
      this.$refs.orderDetail.getOrderDetail(item.id);
    },
    //组装地址
    assemblyAddress(address) {
      let addressStr = "";
      if (!address) {
        return "-";
      }
      addressStr =
        address.realname +
        " " +
        address.mobile +
        " " +
        address.province +
        " " +
        address.city +
        " " +
        address.county +
        " " +
        address.town +
        " " +
        address.detail;
      console.log(addressStr, "????");
      return addressStr ? addressStr : "暂无收货地址";
    },
    handleClose() {
      this.isShow = false;
      this.nameData = {
        supplier_name: "",
        user_name: "",
      };
      this.list = [];
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-drawer .el-drawer__body {
  background-color: #ffffff;
}
.ml30 {
  margin-left: 30px;
}
::v-deep .el-table.table-head {
  margin-bottom: 25px;
  &::before {
    display: none;
  }
  .el-table__header-wrapper {
    tr th {
      background-color: #f7f8fa !important;
      border-bottom: 0;
    }
  }
  .el-table__body-wrapper {
    display: none;
  }
}
::v-deep .el-table.table-cont.el-table--border {
  border: 1px solid #efefef !important;
}
::v-deep .el-table.table-cont {
  margin-bottom: 0 !important;
  thead.has-gutter {
    tr th {
      background-color: #f7f8fa !important;
    }
    tr:last-child {
      display: none;
    }
    tr:first-child {
      th {
        p {
          margin-left: 20px;
          &.supplier-p {
            //background-color: rgb(74, 197, 156);
            padding: 10px;
            color: #0a3cdc;
            //border-radius: 3px;
          }
          &:first-child {
            margin-left: 0;
          }
        }
      }
    }
  }
  .el-table__body-wrapper {
    .goods-box {
      p {
        margin-left: 10px;
      }
    }
    .comm-box {
      width: 50%;
      margin: 0 auto;
      p {
        margin-bottom: 10px;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
.table-foot-box {
  border: 1px solid #ebeef5;
  border-top: 0;
  margin-bottom: 20px;
  padding: 10px;
  p {
    margin-left: 10px;
    &.addr-p {
      span {
        margin-right: 5px;
      }
    }
    &:first-child {
      margin-left: 0;
    }
  }
}
</style>