<template>
  <m-card>
    <el-button type="primary">同步商品</el-button>
    <el-form ref="from" :model="searchInfo" label-width="80px" class="search-term mt25">
      <el-form-item label="活动名称:">
        <el-input v-model="searchInfo.activityName" style="width: 40%"></el-input>
      </el-form-item>
      <el-form-item label="状态:">
        <el-radio-group v-model="searchInfo.condition">
          <el-radio :label="1">全部</el-radio>
          <el-radio :label="2">上架中</el-radio>
          <el-radio :label="3">已下架</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="库存:">
        <el-radio-group v-model="searchInfo.inventory">
          <el-radio :label="1">全部</el-radio>
          <el-radio :label="2">有库存</el-radio>
          <el-radio :label="3">无库存</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="类型:">
        <el-radio-group v-model="searchInfo.type">
          <el-radio :label="1">全部</el-radio>
          <el-radio :label="2">直充</el-radio>
          <el-radio :label="3">卡劵</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="类目:">
        <el-checkbox-group v-model="searchInfo.category ">
          <el-checkbox :label="1" name="category">0元福利</el-checkbox>
          <el-checkbox :label="2" name="category">知识付费</el-checkbox>
          <el-checkbox :label="3" name="category">生活服务</el-checkbox>
          <el-checkbox :label="4" name="category">电影演出</el-checkbox>
          <el-checkbox :label="5" name="category">旅游出行</el-checkbox>
          <el-checkbox :label="6" name="category">商超购物</el-checkbox>
          <el-checkbox :label="7" name="category">外卖送餐</el-checkbox>
          <el-checkbox :label="8" name="category">影音娱乐</el-checkbox>
          <el-checkbox :label="9" name="category">美食饮品</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="品牌:">
        <el-checkbox-group v-model="searchInfo.brand">
          <el-checkbox :label="1" name="brand">看漫画</el-checkbox>
          <el-checkbox :label="2" name="brand">飞卢小说</el-checkbox>
          <el-checkbox :label="3" name="brand">飒漫画</el-checkbox>
          <el-checkbox :label="4" name="brand">电费充值</el-checkbox>
          <el-checkbox :label="5" name="brand">银泰百货</el-checkbox>
          <el-checkbox :label="6" name="brand">世纪联华</el-checkbox>
          <el-checkbox :label="7" name="brand">山姆会员商店</el-checkbox>
          <el-checkbox :label="8" name="brand">埋堆堆</el-checkbox>
          <el-checkbox :label="9" name="brand">乐摩吧</el-checkbox>
          <el-checkbox :label="10" name="brand">多看阅读</el-checkbox>
          <el-checkbox :label="11" name="brand">WPS</el-checkbox>
          <el-checkbox :label="12" name="brand">PP体育</el-checkbox>
          <el-dropdown trigger="click" style="float: right">
            <span style="color: #409EFF;">
                展开<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>看漫画</el-dropdown-item>
              <el-dropdown-item>飞卢小说</el-dropdown-item>
              <el-dropdown-item>飒漫画</el-dropdown-item>
              <el-dropdown-item>电费充值</el-dropdown-item>
              <el-dropdown-item>银泰百货</el-dropdown-item>
              <el-dropdown-item>世纪联华</el-dropdown-item>
              <el-dropdown-item>山姆会员商店</el-dropdown-item>
              <el-dropdown-item>埋堆堆</el-dropdown-item>
              <el-dropdown-item>乐摩吧</el-dropdown-item>
              <el-dropdown-item>多看阅读</el-dropdown-item>
              <el-dropdown-item>WPS</el-dropdown-item>
              <el-dropdown-item>PP体育</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" >查询</el-button>
        <el-button type="text">重置搜索条件</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="tableData" class="mt25">
        <el-table-column label="ID" prop="" align="center"></el-table-column>
        <el-table-column label="商品名称" prop="" align="center"></el-table-column>
        <el-table-column label="有效期" prop="" align="center"></el-table-column>
        <el-table-column label="品牌" prop="" align="center"></el-table-column>
        <el-table-column label="库存" prop="" align="center"></el-table-column>
        <el-table-column label="销量" prop="" align="center"></el-table-column>
        <el-table-column label="原价(元)" prop="" align="center"></el-table-column>
        <el-table-column label="供货价(元)" prop="" align="center"></el-table-column>
        <el-table-column label="必应鸟成本价(元)" prop="" align="center"></el-table-column>
      <el-table-column label="状态" width="260" fixed="right" align="center">
        <template slot-scope="scope">
          <el-radio-group v-model="state">
            <el-radio :label="1">开始</el-radio>
            <el-radio :label="2">关闭</el-radio>
          </el-radio-group>
        </template>
      </el-table-column>
        <el-table-column label="操作" width="260" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text">复制链接</el-button>
            <el-button type="text">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>



  </m-card>
</template>

<script>
export default {
  name: "brandCardGoodsCardManageIndex",
  data(){
    return{
      tableData:[{},{}],
      state:'',
      searchInfo: {
        activityName: '',
        condition: '',
        inventory: '',
        type: '',
        category: [],
        brand: [],
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>