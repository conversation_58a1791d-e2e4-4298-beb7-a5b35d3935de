<template>
  <m-card>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="基础设置" name="baseSetting">
        <base-setting ref="baseSetting"></base-setting>
      </el-tab-pane>
      <el-tab-pane label="导入设置" name="importSettingList">
        <import-setting-list ref="importSettingList"></import-setting-list>
      </el-tab-pane>
    </el-tabs>
  </m-card>
</template>
<script>
import {getSetting, saveSetting, setSupplySetting, getSupplySetting} from "@/api/gatherSupply";
import BaseSetting from "./components/baseSetting"
import ImportSettingList from "./components/importSettingList"

export default ({
  name: "courseBase",
  components: {BaseSetting, ImportSettingList},
  data() {
    return {
      activeName: "baseSetting"
    };
  },
  mounted() {
    this.$refs[this.activeName].init()
  },
  methods: {
    handleClick(tab, event) {
      this.$refs[this.activeName].init()
    }
  }
})
</script>
<style lang="scss" scoped>

</style>
