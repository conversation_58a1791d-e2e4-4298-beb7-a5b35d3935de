<template>
    <div>
        <el-drawer
            :title="`${sectionDialogTitle}课程`"
            :visible="visible"
            :close-on-press-escape="false"
            :wrapperClosable="false"
            @open="onOpen"
            @close="onClose"
            @closed="onClosed"
            size="calc(100% - 220px)"
        >
            <el-button type="primary" @click="onSubmit">保存</el-button>
            <el-tabs v-model="activeName" type="card" class="mt20" @tab-click="handleTabClick">
                <el-tab-pane label="基本信息" name="first">
                    <el-form
                        :model="formData"
                        ref="form"
                        :rules="rules"
                        label-position="right"
                        label-width="120px"
                    >
                        <el-row :gutter="10">
                            <el-col :span="12">
                                <!-- <el-form-item label="分类:">
                  </el-form-item> -->
                                <el-form-item label="一级分类:" prop="category1_id">
                                    <el-select
                                        @change="handleClassifyChange(2, formData.category1_id)"
                                        v-model="formData.category1_id"
                                        filterable
                                        clearable
                                        placeholder="请选择一级分类"
                                        class="w100"
                                    >
                                        <el-option
                                            v-for="item in categoryList1"
                                            :key="item.id"
                                            :label="item.name"
                                            :value="item.id"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="二级分类:" prop="category2_id">
                                    <el-select
                                        @change="handleClassifyChange(3, formData.category2_id)"
                                        v-model="formData.category2_id"
                                        filterable
                                        clearable
                                        placeholder="请选择二级分类"
                                        class="w100"
                                    >
                                        <el-option
                                            v-for="item in categoryList2"
                                            :key="item.id"
                                            :label="item.name"
                                            :value="item.id"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="三级分类:" prop="category3_id">
                                    <el-select
                                        v-model="formData.category3_id"
                                        filterable
                                        clearable
                                        placeholder="请选择三级分类"
                                        class="w100"
                                    >
                                        <el-option
                                            v-for="item in categoryList3"
                                            :key="item.id"
                                            :label="item.name"
                                            :value="item.id"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="讲师:">
                                    <el-select
                                        v-model="formData.lecturer_id"
                                        filterable
                                        class="w100"
                                    >
                                        <el-option
                                            v-for="item in userOption"
                                            :key="item.id"
                                            :label="item.lecturer_name"
                                            :value="item.id"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="课程名称:">
                                    <el-input
                                        v-model="formData.curriculum_name"
                                        clearable
                                        placeholder="请输入"
                                    ></el-input>
                                </el-form-item>
                                <el-form-item label="课程图片:">
                                    <div class="f fw">
                                        <!--                      <div class="cover-list-box" v-for="(item,index) in formData.curriculum_img" :key="item.id">
                        <m-image class="cover-image" :src="item.src"></m-image>
                        <div class="del-box">
                          <el-button type="text" icon="el-icon-delete" @click="delCover(index)"></el-button>
                        </div>
                      </div>-->
                                        <el-upload
                                            :headers="{ 'x-token': token }"
                                            class="covers-uploader"
                                            :action="`${$path}/fileUploadAndDownload/upload`"
                                            :show-file-list="false"
                                            :on-success="handleCoverSuccess"
                                            :before-upload="$fn.beforeAvatarUpload"
                                            accept=".jpg,.jpeg,.png,.gif,.JPG,.JPEG,.PBG,.GIF"
                                        >
                                            <i
                                                v-if="!formData.curriculum_img"
                                                class="el-icon-plus avatar-uploader-icon"
                                            ></i>
                                            <m-image
                                                v-else
                                                :src="formData.curriculum_img"
                                                :style="{ width: '100%', height: '100%' }"
                                            ></m-image>
                                            <!-- <p class="avatar-txt">选择图片</p> -->
                                        </el-upload>
                                    </div>
                                    <p class="color-grap mt25">
                                        建议尺寸：400*400像素,图片需限制在{{ $store.state.uploadLimitSize }}M以内
                                    </p>
                                </el-form-item>
                                <el-form-item label="课程状态:" prop="uploadType">
                                    <el-radio-group v-model="formData.status">
                                        <el-radio :label="1">上架</el-radio>
                                        <el-radio :label="0">下架</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </el-tab-pane>
                <el-tab-pane label="课程介绍" name="second">
                    <!-- label-width="120px" -->
                    <el-form :model="formData" ref="form" :rules="rules" label-position="right">
                        <el-row :gutter="10">
                            <el-col :span="18">
                                <!-- label="讲师介绍:"  -->
                                <el-form-item prop="detailed">
                                    <m-editor v-model="formData.detailed"></m-editor>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </el-tab-pane>
                <el-tab-pane label="课程章节" name="third">
                    <!-- 章节 start -->
                    <div class="search-term">
                        <div v-if="formData.gather_supply_id === 0">
                            <el-popover
                                placement="right"
                                width="250"
                                v-model="chapterVisible"
                                @hide="closeAddPop"
                            >
                                <el-form :model="formData" ref="formOne">
                                    <el-form-item prop="newTitle">
                                        <el-input
                                            placeholder="请填写章节名称"
                                            v-model="newTitle"
                                        ></el-input>
                                    </el-form-item>
                                    <div style="text-align: right; margin: 0">
                                        <el-button type="primary" @click="saveChapter">
                                            确定
                                        </el-button>
                                        <el-button @click="closeAddPop">取消</el-button>
                                    </div>
                                </el-form>
                                <el-button slot="reference" type="primary">新增章节</el-button>
                            </el-popover>
                        </div>
                        <el-radio-group class="mt25" v-model="tabsId" @change="handleTabsChange">
                            <el-radio-button
                                v-for="item in formData.chapter"
                                :key="item.id"
                                :label="item.id"
                            >
                                {{ item.chapter_name }}
                            </el-radio-button>
                        </el-radio-group>
                        <div class="mt25" v-if="formData.gather_supply_id === 0">
                            <el-form :model="formData" inline ref="editForm">
                                <el-form-item label="排序:" class="mr50">
                                    <el-input
                                        placeholder="请填写排序序号"
                                        v-model="currentSort"
                                    ></el-input>
                                    <p class="color-grap">数字小的排在前面，保存后更新章节排序</p>
                                </el-form-item>
                                <el-form-item label="章节名称:">
                                    <el-input
                                        placeholder="请填写章节名称"
                                        v-model="currentTitle"
                                    ></el-input>
                                </el-form-item>
                                <el-form-item>
                                    <el-button @click="onEditChapter(tabsId)">更新</el-button>
                                    <el-button type="danger" @click="onDeleteChapter(tabsId)" v-if="currentTitle">
                                        删除
                                    </el-button>
                                </el-form-item>
                            </el-form>
                        </div>
                    </div>
                    <!-- 章节 end -->
                    <!-- 小节 start -->
                    <div class="mt25">
                        <!-- 添加小节{{currentTitle}} -->
                        <el-button v-if="currentTitle && formData.gather_supply_id === 0" type="primary" @click="onCreateSection">添加小节</el-button>
                        <el-table
                            :data="sectionList"
                            @current-change="handleTableChange"
                            style="margin-top: 30px"
                        >
                            <el-table-column
                                prop="sort"
                                label="排序"
                                align="center"
                            ></el-table-column>
                            <el-table-column
                                prop="subsection_name"
                                label="课程"
                                align="center"
                            ></el-table-column>
                            <el-table-column
                                prop="url"
                                label="url"
                                align="center"
                            ></el-table-column>
                            <el-table-column
                                prop="video_minute"
                                label="时长"
                                align="center"
                                :formatter="formatterTime"
                            ></el-table-column>
                            <el-table-column label="操作" align="center" v-if="formData.gather_supply_id === 0">
                                <template v-slot="scope">
                                    <el-button type="text" @click="onEditSection(scope.row)">
                                        编辑
                                    </el-button>
                                    <el-button
                                        type="text"
                                        slot="reference"
                                        class="btn-del-text"
                                        @click="onDeleteSection(scope.row)"
                                    >
                                        删除
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <!-- <el-pagination
                background
                :current-page="page"
                :page-size="pageSize"
                :page-sizes="[10, 30, 50, 100]"
                :style="{
                  display: 'flex',
                  justifyContent: 'flex-end',
                  marginRight: '20px',
                }"
                :total="total"
                @current-change="handleCurrentChange"
                @size-change="handleSizeChange"
                layout="total, sizes, prev, pager, next, jumper"
              ></el-pagination> -->
                    </div>
                    <!-- 小节 end -->
                </el-tab-pane>
                <el-tab-pane label="销售属性" name="forth">
                    <!-- 销售属性 start -->
                    <el-form
                        :model="formData"
                        ref="form"
                        :rules="rules"
                        label-position="right"
                        label-width="120px"
                    >
                        <el-row :gutter="10">
                            <el-col :span="12">
                                <el-form-item label="课程编码:">
                                    <el-input
                                        :disabled="formData.gather_supply_id !== 0"
                                        v-model="formData.curriculum_code"
                                        clearable
                                        placeholder="请输入"
                                    ></el-input>
                                </el-form-item>
                                <el-form-item>
                                    <span slot="label"><span class="color-red">* </span>销售模式:</span>
                                    <el-radio-group v-model="formData.sales_model" :disabled="formData.gather_supply_id !== 0">
                                        <el-radio :label="1">一次购买无观看限制</el-radio>
                                        <el-radio :label="2">购买后指定期限可看</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                                <el-form-item v-if="formData.sales_model === 2">
                                    <span slot="label"><span class="color-red">* </span>指定期限:</span>
                                    <el-input
                                        v-model="formData.time_limit"
                                        clearable
                                        placeholder="请输入"
                                    >
                                        <template slot="append">天</template>
                                    </el-input>
                                </el-form-item>
                                <el-form-item>
                                    <span slot="label"><span class="color-red">* </span>课程供货价:</span>
                                    <m-num-input
                                        v-model="formData.price"
                                        :precision="2"
                                        endText="元"
                                        clearable
                                        placeholder="请输入"
                                    >
                                        
                                    </m-num-input>
                                </el-form-item>
                                <el-form-item>
                                    <span slot="label"><span class="color-red">* </span>课程成本价:</span>
                                    <m-num-input
                                        v-model="formData.cost_price"
                                        clearable
                                        placeholder="请输入"
                                        :precision="2"
                                        endText="元"
                                    >
                                    </m-num-input>
                                </el-form-item>
                                <el-form-item>
                                    <span slot="label"><span class="color-red">* </span>课程零售价:</span>
                                    <m-num-input
                                        v-model="formData.retail_price"
                                        clearable
                                        placeholder="请输入"
                                        :precision="2"
                                        endText="元"
                                    >
                                    </m-num-input>
                                </el-form-item>
                                <el-form-item v-if="formData.gather_supply_id === 0">
                                    <span slot="label"><span class="color-red">* </span>讲师分成:</span>
                                    <m-num-input
                                        v-model="formData.reward"
                                        clearable
                                        placeholder="请输入"
                                        :precision="2"
                                        endText="元"
                                    >
                                    </m-num-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                    <!-- 销售属性 end -->
                </el-tab-pane>
            </el-tabs>
        </el-drawer>
        <SectionDialog
            :visible.sync="sectionDialog.visible"
            :pageStatus="sectionDialog.pageStatus"
            :propRow="sectionDialog.propRow"
            :currentTitle="currentTitle"
            @submitted="saveSection"
        ></SectionDialog>
    </div>
</template>
<script>
const PAGE_STATUS = {
    CREATE: 0,
    EDIT: 1,
};
import { v4 as uuidv4 } from 'uuid';
import SectionDialog from './sectionDialog.vue';
import { getCategorys } from '@/api/cloud';
import { mapGetters } from 'vuex';
export default {
    name: 'teacherDrawer',
    components: { SectionDialog },
    props: {
        // 弹窗显示/隐藏
        visible: {
            type: Boolean,
            default: false,
        },
        // 页面状态：发布/编辑
        pageStatus: {
            type: Number,
            default: PAGE_STATUS.CREATE,
        },
        //会员信息
        userOption: {
            type: Array,
            default: () => [],
        },
        propRow: {
            type: Object,
            default: () => {
                return {};
            },
        },
    },
    data() {
        return {
            activeName: 'first',
            chapterVisible: false,
            tabsId: '',
            currentTitle: '',
            currentSort: 1,
            newTitle: '', // 章节名称
            newSort: 0, //文章排序
            formData: {
                category1_id: null, //一级分类
                category2_id: null, //二级分类
                category3_id: null, //三级分类
                lecturer_id: null, //讲师Id
                curriculum_name: '', //课程名称
                // curriculum_img: [], //{curriculum_img:""} //课程图片
                curriculum_img: '',
                detailed: '', // 详情
                chapter: [
                    // 文章
                    // {
                    //   "id": "123485345345123123",
                    //   "sort": 1,
                    //   "chapter_name": "测试章节1",
                    //   "subsection": [
                    //       {
                    //         "chapter_id": "123485345345123123",
                    //         "subsection_name": "测试小节111",
                    //         "sort": 1,
                    //         "url": "http://sdfsf324234.com",
                    //         "img": "http://img.com",
                    //         "try": 1,
                    //         "try_time": 50,
                    //         "video_minute": 5,
                    //         "video_second": 60
                    //       },
                    //       {
                    //         "chapter_id": "123485345345123123",
                    //         "subsection_name": "测试小节222",
                    //         "sort": 1,
                    //         "url": "http://sdfsf324234.com",
                    //         "img": "http://img.com",
                    //         "try": 1,
                    //         "try_time": 32423432235,
                    //         "video_minute": 1,
                    //         "video_second": 30
                    //       }
                    //   ]
                    // },
                ],
                curriculum_code: '', // 课程编码
                sales_model: 2, // 销售模式（1无限制2指定期限）
                time_limit: 0, // 限制天数
                price: 0, // 供货价
                cost_price: 0, // 成本价
                retail_price: 0, // 零售价
                reward: 0, // 分成金额
                status: 1, //状态
                gather_supply_id: 0,
            },
            currentSectionIndex: 0, //编辑时选中的section索引
            sectionList: [
                // {
                //   id: '',
                //   chapter_id: '',
                //   subsection_name: '', //小节名称
                //   sort: 1,
                //   url: '',
                //   try: 0,//是否支持试看 1支持
                //   try_time: 0,//试看时间/秒
                //   video_minute: 0,//视频长度/分
                //   video_second: 0,//视频长度/秒
                // }
            ],
            sectionDialog: {
                visible: false,
                pageStatus: 0,
                propRow: {
                    id: '',
                    img: '',
                },
                // searchForm: {},
            },
            // 类目1
            categoryList1: [],
            categoryList2: [],
            categoryList3: [],
            rules: {
                // appLevelId: {
                //   required: true,
                //   message: "请选择采购端等级",
                //   trigger: "change",
                // },
                // callBackLink: {
                //   required: true,
                //   message: "请输入回调地址",
                //   trigger: "blur",
                // },
                // memberId: {required: true, message: "请选择会员", trigger: "change"},
            },
            page: 1,
            pageSize: 10,
            total: 0,
        };
    },
    computed: {
        ...mapGetters('user', ['userInfo', 'token']),
        // 页面标题
        sectionDialogTitle() {
            switch (this.pageStatus) {
                case PAGE_STATUS.CREATE:
                    return '发布';
                case PAGE_STATUS.EDIT:
                default:
                    return '编辑';
            }
        },
    },
    methods: {
        // 格式化视频时长
        formatterTime(row) {
            if (!row.video_minute) {
                if (!row.video_second) {
                    return;
                }
                return row.video_second + '秒';
            }
            if (!row.video_second) {
                return row.video_minute + '分';
            }
            if (!row.video_minute && !row.video_second) {
                return;
            }
            if (row.video_minute && row.video_second) {
                return row.video_minute + '分' + row.video_second + '秒';
            }
        },
        // 新增小节
        onCreateSection() {
            this.sectionDialog.visible = true;
            this.sectionDialog.pageStatus = PAGE_STATUS.CREATE;
        },
        // 编辑小节
        onEditSection(row) {
            this.sectionDialog.visible = true;
            this.sectionDialog.pageStatus = PAGE_STATUS.EDIT;
            this.sectionDialog.propRow = { ...row };
        },
        // 删除小节
        async onDeleteSection() {
            try {
                await this.$confirm('是否确认删除?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                });
                let index = this.getIndex(this.formData.chapter, this.tabsId); //章节索引
                this.formData.chapter[index].subsection.splice(this.currentSectionIndex, 1);
                console.log('删除小节 subsection', this.formData.chapter[index].subsection);
            } catch (error) {
                this.$message({
                    type: 'info',
                    message: '已取消删除',
                });
                return;
            }
        },
        //更新章节
        onEditChapter(id) {
            if (this.currentTitle === '') {
                this.$message.error('请填写章节名称');
            } else {
                let index = this.getIndex(this.formData.chapter, id);
                this.formData.chapter[index].chapter_name = this.currentTitle;
                this.formData.chapter[index].sort = parseInt(this.currentSort);
                this.onSortChapter();
                this.$message.success('更新章节成功');
            }
        },
        // 排序章节
        onSortChapter() {
            this.formData.chapter.sort((a, b) => a.sort - b.sort);
        },
        // 删除章节
        onDeleteChapter(id) {
            this.$confirm('确定要删除吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            })
                .then(() => {
                    let index = this.getIndex(this.formData.chapter, id);
                    this.formData.chapter.splice(index, 1);
                    this.currentTitle = '';
                    this.currentSort = 1;
                })
                .catch(() => {});
        },
        // 通过当前id计算索引
        getIndex(obj, id) {
            let index = obj.findIndex(item => item.id === id);
            return index;
        },
        // 切换tabs
        handleTabsChange(val) {
            let index = this.getIndex(this.formData.chapter, val);
            this.currentTitle = this.formData.chapter[index].chapter_name;
            this.currentSort = this.formData.chapter[index].sort;
            this.sectionList = this.formData.chapter[index].subsection;
        },
        // 保存章节
        saveChapter() {
            if(!this.newTitle){
                this.$message.error('请填写章节名称')
                return
            }
            let uuid = uuidv4();
            let member = {
                id: uuid,
                sort: 1, // 数字小的排在前面
                chapter_name: this.newTitle,
                subsection: [],
            };
            this.formData.chapter.push(member);
        },
        // 编辑时选中的section索引
        handleTableChange(val) {
            this.currentSectionIndex = this.sectionList.indexOf(val);
        },
        // 保存小节
        saveSection(data, status) {
            data.sort = parseInt(data.sort)
            let index = this.getIndex(this.formData.chapter, this.tabsId);
            switch (status) {
                case 0: // CREATE
                    this.formData.chapter[index].subsection.push(data);
                    this.formData.chapter[index].subsection.sort((a, b) => a.sort - b.sort); // 小节排序
                    console.log('新建小节 chapter', this.formData.chapter);
                    this.sectionList = this.formData.chapter[index].subsection; // 回显当前章节所拥有的小节
                    console.log('新建小节 sectionList', this.sectionList);
                    return;
                case 1: // EDIT
                default: // 小节排序
                    // 回显当前章节所拥有的小节
                    this.formData.chapter[index].subsection[this.currentSectionIndex] = data;
                    this.formData.chapter[index].subsection.sort((a, b) => a.sort - b.sort);
                    console.log('编辑小节 chapter', this.formData.chapter);
                    this.sectionList = this.formData.chapter[index].subsection;
                    console.log('编辑小节 sectionList', this.sectionList);
                    return;
            }
        },
        // 关闭一级新增框
        closeAddPop() {
            this.chapterVisible = false;
            this.newTitle = '';
        },
        async handleClassifyInit() {
            // 获取一级类目
            let cateRes = await getCategorys(1, 0);
            if (cateRes.code === 0) {
                this.categoryList1 = cateRes.data.list;
            }
        },
        // 获取类目
        handleClassifyChange(level, pid) {
            getCategorys(level, pid).then(res => {
                if (level === 3) {
                    this.categoryList3 = [];
                    this.formData.category3_id = null;
                } else {
                    this.categoryList2 = [];
                    this.formData.category2_id = null;
                    this.categoryList3 = [];
                    this.formData.category3_id = null;
                }
                switch (level) {
                    case 2:
                        this.categoryList2 = res.data.list;
                        break;
                    case 3:
                        this.categoryList3 = res.data.list;
                        break;
                }
            });
        },
        // 删除封面
        // @confirm("提示", "确定删除?")
        delCover(index) {
            // this.formData.img.splice(index, 1)
        },
        handleCoverSuccess(res) {
            if (res.code === 0) {
                this.formData.curriculum_img = res.data.file.url;
            }
        },
        beforeUpload(file) {
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                this.$message.error('上传图片大小不能超过 10MB!');
            }
            return isLt10M;
        },
        handleTabClick(tab, event) {
            // console.log(tab, event);
        },
        onOpen() {
            this.handleClassifyInit();
            this.$nextTick(() => {
                switch (this.pageStatus) {
                    case PAGE_STATUS.CREATE:
                        break;
                    case PAGE_STATUS.EDIT:
                        /* this.handleClassifyChange(2,this.propRow.category1_id)
                        this.handleClassifyChange(3,this.propRow.category2_id) */
                        this.formData = { ...this.propRow };
                        getCategorys(2, this.propRow.category1_id).then(res => {
                            this.categoryList2 = res.data.list;
                        });
                        getCategorys(3, this.propRow.category2_id).then(res => {
                            this.categoryList3 = res.data.list;
                        });
                        if (this.formData?.price) {
                            this.formData.price = this.formData.price / 100;
                        }
                        if (this.formData?.cost_price) {
                            this.formData.cost_price = this.formData.cost_price / 100;
                        }
                        if (this.formData?.reward) {
                            this.formData.reward = this.formData.reward / 100;
                        }
                        if (this.formData?.retail_price) {
                            this.formData.retail_price = this.formData.retail_price / 100;
                        }
                        if (this.formData.chapter.length > 0) {
                            this.tabsId = this.formData.chapter[0].id; //初始化tabsId
                            this.currentTitle = this.formData.chapter[0].chapter_name; //初始化currentTitle
                            this.sectionList = this.formData.chapter[0].subsection; //初始化subsection
                        }
                        break;
                }
            });
        },
        onSubmit() {
            if(!this.formData.sales_model){
                this.$message.error('请选择销售模式')
                return
            }
            if(this.formData.sales_model === 2 && !this.formData.time_limit){
                this.$message.error('请输入指定期限')
                return
            }
            /* if(this.formData.time_limit === '' || this.formData.time_limit === null){
                this.$message.error('请输入指定期限')
                return
            } */
            if(!this.formData.price === '' || this.formData.price === null){
                this.$message.error('请输入课程供货价')
                return
            }
            if(!this.formData.cost_price === '' || this.formData.cost_price === null){
                this.$message.error('请输入课程成本价')
                return
            }
            if(!this.formData.retail_price === '' || this.formData.retail_price === null){
                this.$message.error('请输入课程零售价')
                return
            }
            if(this.formData.gather_supply_id === 0 && this.formData.reward === ''){
                this.$message.error('请输入讲师分成')
                return
            }
            switch (this.pageStatus) {
                case PAGE_STATUS.CREATE:
                    this.$emit('submitted', this.formData, this.pageStatus);
                    return;
                case PAGE_STATUS.EDIT:
                default:
                    this.$emit('submitted', this.formData, this.pageStatus);
                    return;
            }
        },
        onClose() {
            this.$emit('update:visible', false);
            this.onClosed();
            this.$emit('reload');
        },
        // 可将下述清空提至onOpen方法中
        onClosed() {
            this.activeName = 'first';
            this.chapterVisible = false;
            this.tabsId = '';
            this.currentTitle = '';
            this.currentSort = 1;
            this.newTitle = ''; // 章节名称
            this.newSort = 0; //文章排序
            this.formData = {
                category1_id: null, //一级分类
                category2_id: null, //二级分类
                category3_id: null, //三级分类
                lecturer_id: null, //讲师Id
                curriculum_name: '', //课程名称
                // curriculum_img: [], //{curriculum_img:""} //课程图片
                curriculum_img: '',
                detailed: '', // 详情
                chapter: [
                    // 文章
                    // {
                    //   "id": "123485345345123123",
                    //   "sort": 1,
                    //   "chapter_name": "测试章节1",
                    //   "subsection": [
                    //       {
                    //         "chapter_id": "123485345345123123",
                    //         "subsection_name": "测试小节111",
                    //         "sort": 1,
                    //         "url": "http://sdfsf324234.com",
                    //         "img": "http://img.com",
                    //         "try": 1,
                    //         "try_time": 50,
                    //         "video_minute": 5,
                    //         "video_second": 60
                    //       },
                    //       {
                    //         "chapter_id": "123485345345123123",
                    //         "subsection_name": "测试小节222",
                    //         "sort": 1,
                    //         "url": "http://sdfsf324234.com",
                    //         "img": "http://img.com",
                    //         "try": 1,
                    //         "try_time": 32423432235,
                    //         "video_minute": 1,
                    //         "video_second": 30
                    //       }
                    //   ]
                    // },
                ],
                curriculum_code: '', // 课程编码
                sales_model: 2, // 销售模式（1无限制2指定期限）
                time_limit: 0, // 限制天数
                price: 0, // 供货价
                cost_price: 0, // 成本价
                retail_price: 0, // 零售价
                reward: 0, // 分成金额
                status: null, //状态
                gather_supply_id: 0
            };
            this.currentSectionIndex = 0; //编辑时选中的section索引
            this.sectionList = [
                // {
                //   id: '',
                //   chapter_id: '',
                //   subsection_name: '', //小节名称
                //   sort: 1,
                //   url: '',
                //   try: 0,//是否支持试看 1支持
                //   try_time: 0,//试看时间/秒
                //   video_minute: 0,//视频长度/分
                //   video_second: 0,//视频长度/秒
                // }
            ];
            this.sectionDialog = {
                visible: false,
                pageStatus: 0,
                propRow: {
                    id: '',
                    img: '',
                },
                // searchForm: {},
            };
            // 类目1
            this.categoryList1 = [];
            this.categoryList2 = [];
            this.categoryList3 = [];
            this.page = 1;
            this.pageSize = 10;
            this.total = 0;
        },
        handleClose() {},
    },
};
</script>
<style lang="scss" scoped>
@import '@/style/base.scss';

.mr50 {
    margin-right: 50px;
}

.add-btn-box {
    margin-top: 20px;
}
.children-box {
    margin-left: 10px;
    p.children-p {
        line-height: 40px;
        margin-right: 20px;
    }
}
.mr10 {
    margin-right: 10px;
}
.mt20 {
    margin-top: 20px;
}

/* 相册 */
.covers-uploader,
.cover-image {
    width: 180px;
    height: 180px;
}

.cover-list-box {
    width: 180px;
    height: 180px;
    margin-right: 10px;
    margin-bottom: 10px;
    position: relative;

    &:hover {
        .del-box {
            display: block;
        }
    }

    .del-box {
        display: none;
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        text-align: center;
        background-color: rgba(0, 0, 0, 0.3);

        .el-button.el-button--text {
            font-size: 18px;
            color: #fff;
            margin-top: 10px;
        }
    }
}
.covers-uploader {
    border: 1px dashed #d9dcdf;
    color: #ababab;
    text-align: center;
    i {
        margin-top: 80px;
        font-size: 20px;
    }
}

.avatar-txt {
    margin-top: 12px;
    font-size: 12px;
}
</style>
