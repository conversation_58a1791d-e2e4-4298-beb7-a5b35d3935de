<template>
    <el-dialog
        title="导入课程"
        :visible="visible"
        :close-on-press-escape="false"
        :wrapperClosable="false"
        @open="onOpen"
        @close="onClose"
        @closed="onClosed"
    >
        <el-form :model="formData" label-position="right" label-width="140px">
            <el-row :gutter="10">
                <el-col :span="12">
                    <el-form-item label="供应链:" prop="gather_supply_id">
                        <el-select
                            v-model="formData.gather_supply_id"
                            @change="getCourseCount"
                            class="w100"
                            filterable
                            clearable
                        >
                            <el-option
                                :label="item.name"
                                :value="item.id"
                                v-for="item in supplyOptions"
                                :key="item.id"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="选择导入分类:" prop="category1_id">
                        <el-select
                            @change="handleClassifyChange(2, formData.category1_id)"
                            v-model="formData.category1_id"
                            filterable
                            clearable
                            placeholder="请选择一级分类"
                            class="w100"
                        >
                            <el-option
                                v-for="item in categoryList1"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" prop="category2_id">
                        <el-select
                            @change="handleClassifyChange(3, formData.category2_id)"
                            v-model="formData.category2_id"
                            filterable
                            clearable
                            placeholder="请选择二级分类"
                            class="w100"
                        >
                            <el-option
                                v-for="item in categoryList2"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" prop="category3_id">
                        <el-select
                            v-model="formData.category3_id"
                            filterable
                            clearable
                            placeholder="请选择三级分类"
                            class="w100"
                        >
                            <el-option
                                v-for="item in categoryList3"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            ></el-option>
                        </el-select>
                        <p class="color-grap mt25">如果不选择分类则自动创建分类！</p>
                    </el-form-item>
                    <el-form-item label="未导入课程数量:" prop="">
                        <p>{{ notImportSum }}</p>
                    </el-form-item>
                    <el-form-item label="已导入课程数量:" prop="">
                        <p>{{ isImportSum }}</p>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="10">
                <el-col :span="12" class="con-btn">
                    <el-button type="primary" @click="getCurriculumImport">
                        导入未导入课程
                    </el-button>
                    <el-button type="primary" @click="getCurriculumUpdate">
                        更新已导入课程
                    </el-button>
                    <el-button @click="onClose">取 消</el-button>
                </el-col>
            </el-row>
            <div class="con-p">
                <p>
                    更新已导入课程将会更新课程的全部信息，包括基本信息、详情、章节、价格（根据定价策略）等！
                </p>
            </div>
        </el-form>
    </el-dialog>
</template>
<script>
import { mapGetters } from 'vuex';
import { getCategorys } from '@/api/cloud';
import { selectCurriculumCount, importCurriculum, updateCurriculum } from '@/api/teacher';
import { supplyList } from '@/api/curriculum';

export default {
    name: 'importCourseDialog',
    props: {
        // 弹窗显示/隐藏
        visible: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            formData: {
                category1_id: null, //一级分类
                category2_id: null, //二级分类
                category3_id: null, //三级分类
                gather_supply_id: null, //供应链id
            },
            // 类目1
            categoryList1: [],
            categoryList2: [],
            categoryList3: [],
            supplyOptions: [],
            isImportSum: null,
            notImportSum: null,
        };
    },
    computed: {
        ...mapGetters('user', ['userInfo', 'token']),
    },
    methods: {
        // 获取供应链
        getSupply() {
            supplyList({ page: 1, pageSize: 999 }).then(res => {
                this.supplyOptions = res.data.list;
            });
        },
        // 获取一级类目
        async handleClassifyInit() {
            let cateRes = await getCategorys(1, 0);
            if (cateRes.code === 0) {
                this.categoryList1 = cateRes.data.list;
            }
        },
        // 获取二三级类目
        handleClassifyChange(level, pid) {
            getCategorys(level, pid).then(res => {
                if (level === 3) {
                    this.categoryList3 = [];
                    this.formData.category3_id = null;
                } else {
                    this.categoryList2 = [];
                    this.formData.category2_id = null;
                    this.categoryList3 = [];
                    this.formData.category3_id = null;
                }
                switch (level) {
                    case 2:
                        this.categoryList2 = res.data.list;
                        break;
                    case 3:
                        this.categoryList3 = res.data.list;
                        break;
                }
            });
        },
        // 获取课程数量
        getCourseCount(value) {
            let params = {};
            params.id = parseInt(value);
            selectCurriculumCount(params).then(res => {
                if (res.code === 0) {
                    this.isImportSum = res.data.IsImport;
                    this.notImportSum = res.data.NotImport;
                } else {
                    this.isImportSum = 0;
                    this.notImportSum = 0;
                }
            });
        },
        // 导入未导入课程
        getCurriculumImport() {
            let params = {
                gather_supply_id: this.formData.gather_supply_id,
            };
            if(this.formData.category1_id){
                params.category1_id = this.formData.category1_id
            }
            if(this.formData.category2_id){
                params.category2_id = this.formData.category2_id
            }
            if(this.formData.category3_id){
                params.category3_id = this.formData.category3_id
            }
            importCurriculum(params).then(res => {
                if (res.code === 0) {
                    this.$message.success(res.msg);
                    this.onClose();
                    // this.courseDrawer.userOption = res.data.list
                } else {
                    this.$message.error(res.msg);
                }
            });
        },
        // 导入已导入课程
        getCurriculumUpdate() {
            let params = {
                gather_supply_id: this.formData.gather_supply_id,
            };
            if(this.formData.category1_id){
                params.category1_id = this.formData.category1_id
            }
            if(this.formData.category2_id){
                params.category2_id = this.formData.category2_id
            }
            if(this.formData.category3_id){
                params.category3_id = this.formData.category3_id
            }
            updateCurriculum(params).then(res => {
                if (res.code === 0) {
                    this.$message.success(res.msg);
                    this.onClose();
                    // this.courseDrawer.userOption = res.data.list
                } else {
                    this.$message.error(res.msg);
                }
            });
        },
        onOpen() {
            this.getSupply();
            this.handleClassifyInit();
            this.$nextTick(() => {
                // this.$refs.ruleForm.resetFields()
                // this.formData.img = []
                // this.formData.subsection_name = ''
                // this.formData.sort = 1
                // this.formData.url = ''
                // this.formData.try = 1
                // this.formData.try_time = 0
                // this.formData.video_minute = 0
                // this.formData.video_second = 0
            });
        },
        onClose() {
            this.$emit('update:visible', false);
            this.onClosed();
            this.$emit('reload');
        },
        onClosed() {
            this.formData = {
                category1_id: null, //一级分类
                category2_id: null, //二级分类
                category3_id: null, //三级分类
                gather_supply_id: null, //供应链id
            };
            // 类目1
            this.categoryList1 = [];
            this.categoryList2 = [];
            this.categoryList3 = [];
            this.supplyOptions = [];
            this.isImportSum = null;
            this.notImportSum = null;
        },
        onSave() {
            for (const key in this.formData) {
                if (!this.formData[key]) {
                    delete this.formData[key];
                }
            }
            if (this.formData.try) {
                this.formData.try = Number(this.formData.try);
            }
            if (this.formData.try_time) {
                this.formData.try_time = Number(this.formData.try_time);
            }
            if (this.formData.video_minute) {
                this.formData.video_minute = Number(this.formData.video_minute);
            }
            if (this.formData.video_second) {
                this.formData.video_second = Number(this.formData.video_second);
            }
            if (this.currentTitle) {
                this.$emit('submitted', this.$fn.deepClone(this.formData), this.pageStatus);
                this.$emit('update:visible', false);
                this.$emit('reload');
                this.$message.success('小节内容保存成功');
            } else {
                this.$message.error('请先为该小节选择所属章节');
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.con-btn {
    margin: 0 0 0 80px;

    .el-button {
        margin-right: 20px;
    }
}

.con-p {
    margin: 20px 0 30px 40px;
}
</style>
