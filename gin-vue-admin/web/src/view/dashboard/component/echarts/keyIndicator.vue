<!-- 关键指针曲线echarts -->
<template>
    <div class="line-chart">
        <h3 class="title-h3">关键指标曲线</h3>
        <div class="f fac fjsb buts">
            <div class="f fac">
                <template v-for="item in butlist">
                    <div v-if="item.value !== value" class="btn" @click="butkey(item.value)">{{ item.name }}</div>
                    <div class="but-blue" v-else>
                        <div class="btn-col-blue">{{ item.name }}</div>
                        <div class="but-bottom"></div>
                    </div>
                </template>
            </div>
            <div class="f">
                <el-select class="select" v-model="type" placeholder="请选择" @change="typefun">
                    <el-option v-for="item in date" :key="item.value" :label="item.name" :value="item.value">
                    </el-option>
                </el-select>
                <el-date-picker v-if="type === 0" v-model="dateValue" type="date" placeholder="选择日期" @change="datefun">
                </el-date-picker>
                <el-date-picker v-else-if="type === 1" v-model="dateValue" type="week" placeholder="选择周"
                    format="yyyy 第 WW 周" @change="datefun">
                </el-date-picker>
                <el-date-picker v-else-if="type === 2" v-model="dateValue" type="month" placeholder="选择月"
                    @change="datefun">
                </el-date-picker>
            </div>
        </div>
        <div id="userEcharts" class="user-echarts" ref="keyIndicatoreEcharts"></div>
    </div>
</template>
<script>
import * as echarts from 'echarts';
import {
    getOrderLineChart,
    getAddUserLineChart,
    getPurchasingBalanceLineChart,
    getApplicationLineChart,
    getProductLineChart
} from "@/api/dashboard";

export default {
    data() {
        return {
            userList: [],
            butlist: [
                { name: '订单金额  (元)', value: 1 },
                { name: '订单数量  (单)', value: 2 },
                { name: '新增会员', value: 3 },
                { name: '站内余额充值', value: 4 },
                { name: '新增采购端', value: 5 },
                { name: '新增商品', value: 6 }
            ],
            date: [
                { name: '日', value: 0 },
                { name: '周', value: 1 },
                { name: '月', value: 2 },
            ],// 日期
            type: 0,
            dateValue: '',
            value: 1,
        }
    },
    mounted() {
        this.getOrderLineChart()
    },
    methods: {
        // 切换日周月时清空日期
        typefun(val) {
            this.dateValue = ''
        },
        // 获取日期
        datefun(date) {
            let result = new Date(date).getTime();
            this.keyFun(this.value, { type: this.type, time: result / 1000 })
        },
        // 切换按钮
        butkey(value) {
            this.value = value
            this.type = 0;
            this.dateValue = '';
            this.keyFun(value)
        },
        // 切换折线图
        keyFun(value, data) {
            if (data === undefined) {
                data = {}
            }
            switch (value) {
                case 1:
                    // 订单金额
                    this.getOrderLineChart(0, data)
                    break;
                case 2:
                    // 订单数量
                    this.getOrderLineChart(1, data)
                    break
                case 3:
                    // 新增会员
                    this.getAddUserLineChart(data)
                    break;
                case 4:
                    // 站内余额
                    this.getPurchasingBalanceLineChart(data)
                    break;
                case 5:
                    // 新增采购端
                    this.getApplicationLineChart(data)
                    break;
                case 6:
                    // 新增商品
                    this.getProductLineChart(data)
                    break;
            }
        },
        // 获取订单数量订单金额折线图
        // val 0 订单金额 1 订单数量
        async getOrderLineChart(val, data) {
            let res = await getOrderLineChart(data)
            if (res.code === 0) {
                if (res.data.data.total != undefined) {
                    var completed = res.data.data.completed;
                    var delivered = res.data.data.delivered;
                    var total = res.data.data.total;
                    var amount = res.data.data.amount;
                    var pay = res.data.data.pay;
                    var date = [];
                    var totalData = [],
                        completedData = [],
                        deliveredData = [],
                        payData = [],
                        amountData = [];

                    for (var i = 0; i < total.length; i++) {
                        date[i] = total[i].date;
                        totalData[i] = total[i].count;
                        completedData[i] = completed[i].count;
                        deliveredData[i] = delivered[i].count;
                        payData[i] = pay[i].count;
                        amountData[i] = Math.floor(amount[i].amount) / 100;
                    }
                    if (val) {
                        this.initChars(
                            date,
                            totalData,
                            completedData,
                            deliveredData,
                            payData,
                            ["总订单", "已完成", "已发货", "已支付"]
                        )
                    } else {
                        this.initChar(date, amountData, ['销售额'])
                    }
                }
            } else {
                this.$message.error(res.msg);
            }
        },
        // 新增会员 折线图
        async getAddUserLineChart(data) {
            let res = await getAddUserLineChart(data)
            this.setCharts(res, this, '会员数量')
        },
        // 充值站内余额折线图
        async getPurchasingBalanceLineChart(data) {
            let res = await getPurchasingBalanceLineChart(data)
            this.setCharts(res, this, '充值金额', 4)
        },
        // 新增采购端数量折线图
        async getApplicationLineChart(data) {
            let res = await getApplicationLineChart(data)
            this.setCharts(res, this, '新增采购端数量')
        },
        // 新增商品数量折线图
        async getProductLineChart(data) {
            let res = await getProductLineChart(data)
            this.setCharts(res, this, '新增商品数量')
        },
        setCharts(res, that, str, num) {
            if (!num) {
                num = 0
            }
            if (res.code === 0) {
                if (res.data.data.lineChart != undefined) {
                    var date = []
                    var total = res.data.data.lineChart
                    var amountData = []
                    if (num === 4) {
                        for (var i = 0; i < total.length; i++) {
                            date[i] = total[i].date;
                            amountData[i] = this.$fn.changeMoneyF2Y(total[i].total) 
                        }
                    } else {
                        for (var i = 0; i < total.length; i++) {
                            date[i] = total[i].date;
                            amountData[i] = total[i].total
                        }
                    }
                    that.initChar(date, amountData, [str])
                }
            } else {
                this.$message.error(res.msg);
            }
        },
        initChar(date, amountData, legendData) {

            // 处理时间显示
            let yyrr = []
            date.forEach(element => {
                let s = element.split('-')
                if (this.type === 2) {
                    yyrr.push(parseInt(s[0]) + '年' + parseInt(s[1]) + '月')
                } else {
                    yyrr.push(parseInt(s[1]) + '月' + parseInt(s[2]) + '日')
                }
            });

            let data = [
                {
                    name: legendData[0],
                    type: "line",
                    data: amountData,
                    itemStyle: {
                        color: '#155BD4'
                    },
                },
            ]
            let chartDom = document.getElementById('userEcharts');
            let chartsC = echarts.init(chartDom);
            chartsC.clear(); // 清空之前图标数据
            // 图表配置
            let option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    }
                },
                legend: {
                    data: legendData,
                },
                xAxis: {
                    data: yyrr,
                },
                yAxis: {},
                series: data
            };
            chartsC.setOption(option);
            // window.onresize = charts.resize
            window.addEventListener("resize", function () {
                chartsC.resize();
            })

        },
        initChars(date, totalData, completedData, deliveredData, payData, legendData) {
            // 处理时间显示
            let yyrr = []
            date.forEach(element => {
                let s = element.split('-')
                if (this.type === 2) {
                    yyrr.push(parseInt(s[0]) + '年' + parseInt(s[1]) + '月')
                } else {
                    yyrr.push(parseInt(s[1]) + '月' + parseInt(s[2]) + '日')
                }
            });
            let data = [
                {
                    name: legendData[0],
                    type: "line",
                    data: totalData,
                    itemStyle: {
                        color: '#155BD4'
                    },
                },
                {
                    name: legendData[1],
                    type: "line",
                    data: completedData,
                    itemStyle: {
                        color: '#13C267'
                    },
                },
                {
                    name: legendData[2],
                    type: "line",
                    data: deliveredData,
                    itemStyle: {
                        color: '#88DEA2'
                    },
                },
                {
                    name: legendData[3],
                    type: "line",
                    data: payData,
                    itemStyle: {
                        color: '#DDDFE2'
                    },
                },
            ]
            let chartDom = document.getElementById('userEcharts');
            let chartsC = echarts.init(chartDom);
            chartsC.clear(); // 清空之前图标数据
            // 图标配置
            let option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    }
                },
                legend: {
                    data: legendData,
                },
                xAxis: {
                    data: yyrr,
                },
                yAxis: {},
                series: data,
            };
            chartsC.setOption(option);
            window.addEventListener("resize", function () {
                chartsC.resize();
            })
            // window.onresize = charts.resize

        },
    },
};
</script>
<style lang="scss" scoped>
.line-chart {
    width: 100%;
    height: 517px;
    margin-top: 16px;
    background-color: white;
    border-radius: 12px;
    padding: 20px;
    box-sizing: border-box;
}

.title-h3 {
    font-size: 20px;
    font-weight: bold;
}

.buts {
    margin-top: 20px;

    .btn {

        padding: 11px 0;
        cursor: pointer;
        margin-right: 24px;
        font-size: 14px;
    }


    .but-blue {
        padding-top: 11px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-direction: column;
        margin-right: 24px;

        .btn-col-blue {
            padding-bottom: 8px;
            cursor: pointer;
            font-size: 14px;
            color: #155BD4;
        }

        .but-bottom {
            width: 80%;
            height: 3px;
            border-radius: 1.5px;
            background-color: #155BD4;
        }
    }
}

.user-echarts {
    margin-top: 50px;
    width: 100%;
    height: 350px;
}

.select {
    margin-right: 10px;
}

.echartsbox {

    .date-box {
        width: px;
        right: 250px;
    }
}
</style>