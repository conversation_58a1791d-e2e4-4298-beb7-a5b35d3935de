<template>
    <m-card>
        <el-form :model="searchForm" ref="form" class="search-term mt25" label-width="90px" inline>
            <el-form-item>
                <el-input placeholder="请输入" v-model="searchForm.title" class="line-input" clearable>
                    <span slot="prepend">商品名称</span>
                </el-input>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >商品状态</span>
                    </div>
                    <el-select v-model="goodsStatus" class="w100">
                        <el-option v-for="item in goodsStatusList" :key="item.id" :label="item.name"
                                    :value="item.value">
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <el-input placeholder="请输入" v-model="searchForm.shopTitle" class="line-input" clearable>
                    <span slot="prepend">店铺名称</span>
                </el-input>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >供应商</span>
                    </div>
                    <el-select v-model="searchForm.supplier_id" class="w100" filterable clearable>
                        <el-option label="全部" value=""></el-option>
                        <el-option label="平台自营" value="0"></el-option>
                        <el-option label="全部供应商" value="999999"></el-option>
                        <el-option v-for="item in supplierList" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >供应链</span>
                    </div>
                    <el-select v-model="searchForm.gather_supply_id" class="w100" filterable clearable>
                        <el-option label="全部" value=""></el-option>
                        <el-option label="平台自营" value="0"></el-option>
                        <el-option :label="item.name" :value="item.id" v-for="item in supplyOptions">
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >一级分类</span>
                    </div>
                    <el-select
                            v-model="searchForm.category1_id"
                            placeholder="请选择一级分类"
                            class="w100"
                            filterable
                            clearable
                            @change="handleClassiyfChange(2,searchForm.category1_id)"
                    >
                        <el-option v-for="item in categoryList1" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >二级分类</span>
                    </div>
                    <el-select
                            v-model="searchForm.category2_id"
                            placeholder="请选择二级分类"
                            filterable
                            clearable
                            class="w100"
                            @change="handleClassiyfChange(3,searchForm.category2_id)"
                    >
                        <el-option v-for="item in categoryList2" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >三级分类</span>
                    </div>
                    <el-select
                            v-model="searchForm.category3_id"
                            placeholder="请选择三级分类"
                            filterable
                            clearable
                            class="w100"
                    >
                        <el-option v-for="item in categoryList3" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <el-input placeholder="请输入" v-model="searchForm.sn" class="line-input" clearable>
                    <span slot="prepend">商品条形码</span>
                </el-input>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input">
                    <div class="line-box ">
                        <span>区间价格</span>
                    </div>
                    <div class="f fac">
                        <el-form-item prop="minPrice" class="f1">
                            <el-input
                                v-model="searchForm.minPrice"
                                placeholder="最低价"
                            ></el-input>
                        </el-form-item>
                        <span class="zih-span mr_10">至</span>
                        <el-form-item prop="maxPrice" class="f1">
                            <el-input
                                v-model="searchForm.maxPrice"
                                placeholder="最高价"
                            ></el-input>
                        </el-form-item>
                    </div>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >品牌</span>
                    </div>
                    <el-select v-model="searchForm.brand_id" class="w100" clearable filterable remote
                                :remote-method="remoteMethod" :loading="brandsOptiosData.loading">
                        <el-option v-for="item in brandsOptiosData.brandsOptios" :key="item.id" :label="item.name"
                                    :value="item.id">
                        </el-option>
                        <div class="text-center">
                            <Element background small class="pagination"
                                        style="padding-top:10px !important;padding-bottom: 0 !important;"
                                        :current-page="brandsOptiosData.page"
                                        :page-size="brandsOptiosData.pageSize"
                                        :total="brandsOptiosData.total"
                                        @current-change="handleBrandPage"
                                        layout="prev,pager, next"/>
                        </div>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <el-input placeholder="请输入" v-model="searchForm.id" class="line-input" clearable>
                    <span slot="prepend">商品id</span>
                </el-input>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >营销属性</span>
                    </div>
                    <el-select v-model="arketingChecked" class="w100">
                        <el-option v-for="item in arketingOptios" :key="item.id" :label="item.label"
                                    :value="item.value"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >锁定状态</span>
                    </div>
                    <el-select v-model="searchForm.status_lock" class="w100">
                        <el-option label="全部" value=""></el-option>
                        <el-option label="已锁定" :value="1"></el-option>
                        <el-option label="未锁定" :value="0"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >排序</span>
                    </div>
                    <el-select v-model="searchForm.sortType" class="w100">
                        <el-option label="ID" value="0"></el-option>
                        <el-option label="供货价" value="1"></el-option>
                        <el-option label="成本价" value="2"></el-option>
                        <el-option label="零售价" value="3"></el-option>
                        <el-option label="利润率" value="4"></el-option>
                        <el-option label="销量" value="5"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >排序方向</span>
                    </div>
                    <el-select v-model="searchForm.sortMode" class="w100">
                        <el-option label="从小到大" value="asc"></el-option>
                        <el-option label="从大到小" value="desc"></el-option>
                    </el-select>
                </div>
            </el-form-item>

            <el-form-item>
                <div class="f">
                    <el-button type="primary" @click="search">搜索</el-button>
                    <el-button type="text" @click="resetForm('form')">重置搜索条件</el-button>
                </div>
            </el-form-item>
        </el-form>

        <el-tabs v-model="tabGoodsStatus" @tab-click="handleTabsClick" type="card" class="mt25 goods-tabs">
            <el-tab-pane v-for="item in goodsStatusList" :key="item.id" :label="item.name" :name="item.value">
            </el-tab-pane>
        </el-tabs>

        <el-table class="mt25" :data="tableList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center"></el-table-column>
            <el-table-column width="50" label="ID">
                <template slot-scope="scope">
                    <p>{{ scope.row.id }}</p>
                </template>
            </el-table-column>
            <el-table-column label="图片" width="140" align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                    <el-popover
                            placement="right"
                            title=""
                            trigger="hover">
                        <m-image :src="scope.row.image_url" :style="{width:'160px',height:'160px'}"></m-image>
                        <m-image slot="reference" :src="scope.row.image_url" :alt="scope.row.image_url"
                                 :style="{width:'60px',height:'60px'}"></m-image>
                    </el-popover>
                </template>
            </el-table-column>
            <el-table-column label="商品" width="250">
                <template slot-scope="scope">
                    <p>{{ scope.row.title }}</p>
                    <p v-if="scope.row.sn" class="sn-code">编码：{{scope.row.sn}}</p>
                    <p v-if="scope.row.gather_supply_id > 0" class="sn-code">
                        第三方商品ID：{{scope.row.source_goods_id}}
                        <span @click="$fn.copy(scope.row.source_goods_id)">复制</span>
                    </p>
                </template>
            </el-table-column>
            <el-table-column align="center" width="150" show-overflow-tooltip>
                <template slot="header">
                    <p>供货价</p>
                </template>
                <template slot-scope="scope">
                    <p v-if="scope.row.skus.length>1">￥{{ scope.row.minPrice | formatF2Y }}-{{scope.row.maxPrice | formatF2Y}}</p>
                    <p v-else>￥{{ scope.row.price | formatF2Y }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center" width="150" show-overflow-tooltip>
                <template slot="header">
                    <p>成本价</p>
                </template>
                <template slot-scope="scope">
                    <p v-if="scope.row.skus.length>1">￥{{ scope.row.minCostPrice | formatF2Y }}-{{scope.row.maxCostPrice | formatF2Y}}</p>
                    <p v-else>￥{{ scope.row.cost_price | formatF2Y }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center" width="150"  show-overflow-tooltip>
                <template slot="header">
                    <p>零售价</p>
                    <p>指导价</p>
                </template>
                <template slot-scope="scope">
                    <p v-if="scope.row.skus.length>1">￥{{ scope.row.minOriginPrice | formatF2Y }}-{{scope.row.maxOriginPrice | formatF2Y}}</p>
                    <p v-else>￥{{ scope.row.origin_price | formatF2Y }}</p>
                    <p>￥{{ scope.row.guide_price | formatF2Y }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center" label="分润" width="200">
                <template slot-scope="scope">
                    <p>{{scope.row.first_distributor_string}}</p>
                    <p>{{scope.row.second_distributor_string}}</p>
                </template>
            </el-table-column>
            <el-table-column label="利润率" align="center" width="150"  show-overflow-tooltip>
                <template slot-scope="scope">
                    <p v-if="scope.row.skus.length>1">{{ scope.row.min_profit_rate }}%-{{scope.row.max_profit_rate }}%</p>
                    <p v-if="scope.row.skus.length>1">{{ scope.row.min_profit_rate }}%-{{scope.row.max_profit_rate }}%</p>
                    <p v-else>{{ scope.row.min_profit_rate }}%</p>
                </template>
            </el-table-column>
            <el-table-column align="center" show-overflow-tooltip>
                <template slot="header">
                    <p>库存</p>
                    <p>销量</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.stock}}</p>
                    <p>{{ scope.row.sales}}</p>
                </template>
            </el-table-column>
            <el-table-column label=品牌 align="center" prop="brand.name" show-overflow-tooltip></el-table-column>
            <el-table-column label="供应渠道" width="100" align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                    <span v-if="scope.row.supplier_id > 0">{{ scope.row.supplier.name }}</span>
                    <span v-else-if="scope.row.gather_supply_id > 0">{{ scope.row.gather_supply.name }}</span>
                    <span v-else-if="scope.row.supplier_id === 0 && scope.row.gather_supply_id === 0">自营</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="170" fixed="right" align="center">
                <template slot-scope="scope">
                    <el-button type="text" @click="openSettingDialog(scope.row)">分销设置</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
                background
                :current-page="page"
                :page-size="pageSize"
                :page-sizes="[10, 30, 50, 100,200]"
                :style="{display: 'flex',justifyContent: 'flex-end',marginRight: '20px'}"
                :total="total"
                @current-change="handleCurrentChange"
                @size-change="handleSizeChange"
                layout="total, sizes,prev, pager, next, jumper"
        >
        </el-pagination>
        <!-- 批量分类弹窗 start -->
        <el-dialog title="批量分类" :visible.sync="categoryVisible">
            <el-form :inline="true" :model="batchForm" ref="batchForm" :rules="rules">
                <el-row :gutter="10">
                    <el-col :span="8">
                        <el-form-item label="一级分类:" prop="batch_category1_id">
                            <el-select
                                    v-model="batchForm.batch_category1_id"
                                    placeholder="请选择一级分类"
                                    class="w100"
                                    filterable
                                    clearable
                                    @change="handleClassiyfChangeDialog(2,batchForm.batch_category1_id)"
                            >
                                <el-option v-for="item in categoryListDialog1" :key="item.id" :label="item.name" :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="二级分类:" prop="batch_category2_id">
                            <el-select
                                    v-model="batchForm.batch_category2_id"
                                    placeholder="请选择二级分类"
                                    filterable
                                    clearable
                                    class="w100"
                                    @change="handleClassiyfChangeDialog(3,batchForm.batch_category2_id)"
                            >
                                <el-option v-for="item in categoryListDialog2" :key="item.id" :label="item.name" :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="三级分类:" prop="batch_category3_id">
                            <el-select
                                    v-model="batchForm.batch_category3_id"
                                    placeholder="请选择三级分类"
                                    filterable
                                    clearable
                                    class="w100"
                            >
                                <el-option v-for="item in categoryListDialog3" :key="item.id" :label="item.name" :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="onCloseCategory('batchForm')">取 消</el-button>
                <el-button type="primary" @click="onSubmitCategory('batchForm')">确 定</el-button>
            </div>
        </el-dialog>
        <!-- 批量分类弹窗 end -->
        <!-- 批量品牌 -->
        <el-dialog :visible="brandVisible" title="批量品牌">
            <div class="f fac">
                <span class="mr10"><span class="color-red">* </span>品牌:</span>
                <el-select v-model="batchBrand" clearable filterable remote
                           :remote-method="remoteMethod" :loading="brandsOptiosData.loading">
                    <el-option v-for="item in brandsOptiosData.brandsOptios" :key="item.id" :label="item.name"
                               :value="item.id">
                    </el-option>
                    <div class="text-center">
                        <Element background small class="pagination"
                                 style="padding-top:10px !important;padding-bottom: 0 !important;"
                                 :current-page="brandsOptiosData.page"
                                 :page-size="brandsOptiosData.pageSize"
                                 :total="brandsOptiosData.total"
                                 @current-change="handleBrandPage"
                                 layout="prev,pager, next"/>
                    </div>
                </el-select>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="onCloseBrand">取 消</el-button>
                <el-button type="primary" @click="onSubmitBrand">确 定</el-button>
            </div>
        </el-dialog>
        <SettingDialog ref="settingDialog" />
    </m-card>
</template>
<script>
import {
    getAsyncProductList,
    deleteProduct,
    deleteProductByIds,
    upStatus,
    getSupplierOptionList,
    batchDisplays,
    batchChanges,
    syncPriduct,
    exportProductList,
    deleteProductInfoList,
    copyProduct,
    supplierCopyProduct,
    updateProductByExcel
} from "@/api/goods"
import SettingDialog from "./components/settingDialog.vue";
import {getBrandsList} from "@/api/brands"
import {getSupplyList} from "@/api/order"
import {getClassify} from "@/api/classify"
import zhLang from 'element-ui/lib/locale/lang/zh-CN';
import {confirm} from "@/decorators/decorators";
import {mapGetters} from "vuex";
export default {
    name: "distributionSynchronizationProductManageList",
    components:{SettingDialog},
    computed: {
        ...mapGetters("user", ["token"]),
    },
    data() {
        return {
            batchBrand: null,
            arketingChecked: "",
            arketingOptios: [
                {label: "全部", value: ""},
                {label: "热卖", value: "is_hot"},
                {label: "促销", value: "is_promotion"},
                {label: "新品", value: "is_new"}
            ],
            brandsOptiosData: {
                name: "",
                brandsOptios: [],
                loading: false,
                page: 1,
                pageSize: 10,
                total: 0
            },
            supplyOptions: [],
            goodsStatus: "0",
            tabGoodsStatus: "0",
            is_hot: 0, // 批量热卖
            is_promotion: 0, // 批量促销
            is_new: 0, // 批量新品
            goodsStatusList: [
                {name: "全部", value: "0"},
                {name: "上架", value: "1"},
                {name: "下架", value: "2"},
                {name: "售罄", value: "3"},
            ],
            deleteVisible: false,
            categoryVisible:false,
            brandVisible:false,
            // 供应商
            supplierList: [],
            batchForm:{
                batch_category1_id:null,
                batch_category2_id:null,
                batch_category3_id:null,
            },
            rules: {
                batch_category1_id: [
                    { required: true, message: '请选择活动区域', trigger: 'change' }
                ],
                batch_category2_id: [
                    { required: true, message: '请选择活动区域', trigger: 'change' }
                ],
                batch_category3_id: [
                    { required: true, message: '请选择活动区域', trigger: 'change' }
                ],
            },
            searchForm: {
                id: null,//商品id
                filter: 0,
                // 商品
                title: "",
                category1_id: "",
                category2_id: "",
                category3_id: "",
                //店铺名称
                shopTitle:'',
                // 最高价
                maxPrice: "",
                // 最低价
                minPrice: "",
                gather_supply_id: null,
                // 供应商
                supplier_id: null,
                sn: "",
                brand_id: null,
                status_lock: "",// 锁定状态
                sortType: "",//
                sortMode: "",//
            },
            // 类目1
            categoryList1: [],
            categoryList2: [],
            categoryList3: [],
            //弹窗类目
            categoryListDialog1: [],
            categoryListDialog2: [],
            categoryListDialog3: [],
            tableList: [],
            // 多选选中的数据
            selectTables: [],
            page: 1,
            pageSize: 10,
            total: 0
        }
    },

    mounted() {
        this.resetForm("form");
        // 获取一级类目
        getClassify(1, 0).then(r => {
            this.categoryList1 = r.data.list
            this.categoryListDialog1 = r.data.list
        })
        this.getBrandsOptios()
        this.getSupply()
        // 获取供应商
        getSupplierOptionList().then(r => {
            this.supplierList = r.data.list
        })
    },
    methods: {
        // 打开分销设置
        openSettingDialog(row){
          this.$refs.settingDialog.init(row)
        },
        handleFileSuccess(res) {
            if (res.code === 0) {
                updateProductByExcel({link: res.data.file.key}).then(uploadRes=>{
                    if(uploadRes.code === 0){
                        this.$message.success(uploadRes.msg)
                    }
                })
                // this.formData.link = res.data.file.key
                // this.isShow = true
                // this.getExpress();
            }
        },
        beforeFileUpload(file) {
            const verifySize = file.size / 1024 / 1024 < 10;
            if (!verifySize) {
                this.$message.error("上传文件大小不能超过 10M!");
            }
            return verifySize;
        },
        /**
         * 列表商品状态拼装
         * @param v1 商品状态 1上架 0下架
         * @param v2 锁定该状态 1锁定 0未锁定
         * @returns {string}
         */
        switchStatusStr(v1, v2) {
            let s1, s2 = ""
            switch (v1) {
                case 0:
                    s1 = "下架"
                    break;
                case 1:
                    s1 = "上架"
                    break;
            }
            switch (v2) {
                case 0:
                    s2 = "未锁定"
                    break;
                case 1:
                    s2 = "已锁定"
                    break;
            }
            return s1 + s2;
        },
        handleBrandPage(val) {
            this.brandsOptiosData.page = val
            this.getBrandsOptios()
        },
        // 品牌搜索
        remoteMethod(query) {
            this.brandsOptiosData.name = query
            this.brandsOptiosData.page = 1
            this.getBrandsOptios()
        },
        // 获取品牌
        async getBrandsOptios() {
            let params = {
                page: this.brandsOptiosData.page,
                pageSize: this.brandsOptiosData.pageSize
            }
            if (this.brandsOptiosData.name) params.name = this.brandsOptiosData.name
            this.brandsOptiosData.loading = true
            let res = await getBrandsList(params)
            this.brandsOptiosData.loading = false
            if (res.code === 0) {
                this.brandsOptiosData.brandsOptios = res.data.list
                this.brandsOptiosData.total = res.data.total
            }
        },
        // 获取供应链
        getSupply() {
            getSupplyList().then(res => {
                this.supplyOptions = res.data.list
            })
        },
        // 同步数据
        syncData() {
            this.$confirm("确认同步数据吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                syncPriduct({id: 0}).then(res => {
                    if (res.code === 0) {
                        this.$message.success(res.msg)
                        this.resetForm("form")
                        this.getList()
                    }
                })
            }).catch(() => {
            })
        },
        // 上传excel
        uploadExcel(){

        },
        // 导出
        exTable() {
            this.searchForm.filter = parseInt(this.goodsStatus);//同步数据
            let para = {
                "page": this.page,
                "pageSize": this.pageSize,
                "filter": this.searchForm.filter,
                "title": this.searchForm.title,
                "category1_id": this.searchForm.category1_id,
                "category2_id": this.searchForm.category2_id,
                "category3_id": this.searchForm.category3_id,
                "maxPrice": this.searchForm.maxPrice,
                "minPrice": this.searchForm.minPrice,
                "brand_id": this.searchForm.brand_id,
                "id": this.searchForm.id,
                "barcode": this.searchForm.sn,
                "shop_name": this.searchForm.shopTitle
            }
            if (this.searchForm.gather_supply_id !== "") {
                para.gather_supply_id = this.searchForm.gather_supply_id
            }
            if (this.searchForm.supplier_id !== "") {
                para.supplier_id = this.searchForm.supplier_id
            }
            if (this.arketingChecked !== "") {
                para[this.arketingChecked] = 1
            }
            if (this.searchForm.status_lock !== "") {
                para.status_lock = this.searchForm.status_lock
            }
            if (this.searchForm.sortType !== "") {
                para.sortType = this.searchForm.sortType
            }
            if (this.searchForm.sortMode !== "") {
                para.sortMode = this.searchForm.sortMode
            }
            exportProductList(para).then(res => {
                if (res.code === 0) {
                    this.$message.success(res.msg)
                    // window.open(this.$path + '/' + res.data.link)
                    // window.location.href = this.$path + '/' + res.data.link
                }
            })
        },
        //搜索处理
        search() {
            this.page = 1
            this.tabGoodsStatus = this.goodsStatus;
            this.searchForm.filter = parseInt(this.goodsStatus);//同步数据
            this.getList();
        },
        // 根据筛选条件删除所有商品
        @confirm("提示", "确定删除所有筛选出的商品吗?")
        searchDeleteAll() {
            this.searchForm.filter = parseInt(this.goodsStatus);
            let para = {
                "filter": this.searchForm.filter,
                "title": this.searchForm.title,
                "category1_id": this.searchForm.category1_id,
                "category2_id": this.searchForm.category2_id,
                "category3_id": this.searchForm.category3_id,
                "maxPrice": this.searchForm.maxPrice,
                "minPrice": this.searchForm.minPrice,
                "brand_id": this.searchForm.brand_id,
                "id": this.searchForm.id,
                "barcode": this.searchForm.sn,
                "shop_name": this.searchForm.shopTitle,
            }
            if (this.searchForm.gather_supply_id !== "") {
                para.gather_supply_id = this.searchForm.gather_supply_id
            }
            if (this.searchForm.supplier_id !== "") {
                para.supplier_id = this.searchForm.supplier_id
            }
            if (this.arketingChecked !== "") {
                para[this.arketingChecked] = 1
            }
            if (this.searchForm.status_lock !== "") {
                para.status_lock = this.searchForm.status_lock
            }
            if (this.searchForm.sortType !== "") {
                para.sortType = this.searchForm.sortType
            }
            if (this.searchForm.sortMode !== "") {
                para.sortMode = this.searchForm.sortMode
            }
            deleteProductInfoList(para).then(res => {
                if (res.code === 0) {
                    this.$message.success(res.msg)
                    this.search()
                }
            })
        },
        // 重置
        resetForm(formName) {
            this.goodsStatus = "0";
            this.tabGoodsStatus = "0";
            this.page = 1;
            this.categoryList2 = [];
            this.categoryList3 = [];
            this.$refs[formName].resetFields();
            this.arketingChecked = ""
            this.brandsOptiosData = {
                name: "",
                brandsOptios: [],
                loading: false,
                page: 1,
                pageSize: 10,
                total: 0
            }
            this.searchForm = {
                id: null,//商品id
                filter: 0,
                // 商品
                title: "",
                category1_id: "",
                category2_id: "",
                category3_id: "",
                //店铺名称
                shopTitle:'',
                // 最高价
                maxPrice: "",
                // 最低价
                minPrice: "",
                gather_supply_id: null,
                // 供应商
                supplier_id: null,
                sn: "",
                brand_id: null,
                status_lock: "",// 锁定状态
                sortType: "",//
                sortMode: "",//
            },
            this.remoteMethod()
            this.getList();
        },

        // tabs切换
        handleTabsClick() {
            this.goodsStatus = this.tabGoodsStatus;
            switch (this.tabGoodsStatus) {
                case "0":
                    this.searchForm.filter = parseInt(this.goodsStatus);
                    break;
                case "1":
                    this.searchForm.filter = parseInt(this.goodsStatus);
                    break;
                case "2":
                    this.searchForm.filter = parseInt(this.goodsStatus);
                    break;
                case "3":
                    this.searchForm.filter = parseInt(this.goodsStatus);
                    break;
                default:
                    break;
            }
            this.page = 1;
            this.pageSize = 10;
            this.getList();
        },

        // 获取类目
        handleClassiyfChange(level, pid) {
            getClassify(level, pid).then(r => {
                if (level === 3) {
                    this.categoryList3 = [];
                    this.searchForm.category3_id = ""
                } else {
                    this.categoryList2 = [];
                    this.searchForm.category2_id = ""
                    this.categoryList3 = [];
                    this.searchForm.category3_id = ""
                }
                switch (level) {
                    case 2:
                        this.categoryList2 = r.data.list
                        break;
                    case 3:
                        this.categoryList3 = r.data.list
                        break;
                }
            })
        },
        //弹窗获取类目
        handleClassiyfChangeDialog(level, pid){
            getClassify(level, pid).then(r => {
                // if (level === 3) {
                //     this.categoryListDialog3 = [];
                //     this.searchForm.category3_id = ""
                // } else {
                //     this.categoryListDialog2 = [];
                //     this.searchForm.category2_id = ""
                //     this.categoryListDialog3 = [];
                //     this.searchForm.category3_id = ""
                // }
                switch (level) {
                    case 2:
                        this.categoryListDialog2 = r.data.list
                        break;
                    case 3:
                        this.categoryListDialog3 = r.data.list
                        break;
                }
            })
        },

        // 复制
        copyUrl(url) {
            var input = document.createElement("input"); // 直接构建input
            input.value = url;// 设置内容
            document.body.appendChild(input); // 添加临时实例
            input.select(); // 选择实例内容
            document.execCommand("Copy"); // 执行复制
            document.body.removeChild(input); // 删除临时实例
            this.$message({
                type: "success",
                message: "复制成功"
            });
        },

        // 上下架锁定
        /*handleStateChange(row, name) {
            let data = {
                column: name, id: row.id
            }
            if (row.is_display === 1) data.status = 1
            let params = {
                column: "status_lock",
                id: row.id
            }
            upStatus(data).then(r => {
                if (r.code === 0) {
                    this.$confirm("是否锁定上下架状态?", "提示", {
                        confirmButtonText: "锁定",
                        cancelButtonText: "不锁定",
                        type: "warning",
                    }).then(() => {
                        params.status = 1
                        upStatus(params).then(res => {
                            if (res.code === 0) {
                                this.$message.success(res.msg)
                                this.getList()
                            }
                        })
                    }).catch(() => {
                        params.status = 0
                        upStatus(params).then(res => {
                            if (res.code === 0) {
                                this.$message.success(res.msg)
                                this.getList()
                            }
                        })
                    })
                } else {
                    this.$message.error('更新失败')
                }
            })
        },*/

        // 热卖 促销 新品
        async handleStatus(row, name) {
            console.log(1512151)
            let params = {
                column: name,
                id: row.id,
                status: row[name]
            }
            let res = await upStatus(params)
            if (res.code === 0) {
                this.$message.success(res.msg)
            }
        },

        // 获取列表
        getList() {
            this.tableList = [];
            let para = {
                "page": this.page,
                "pageSize": this.pageSize,
                "filter": this.searchForm.filter,
                "title": this.searchForm.title,
                "category1_id": this.searchForm.category1_id,
                "category2_id": this.searchForm.category2_id,
                "category3_id": this.searchForm.category3_id,
                "maxPrice": this.searchForm.maxPrice,
                "minPrice": this.searchForm.minPrice,
                "brand_id": this.searchForm.brand_id,
                "id": this.searchForm.id,
                "barcode": this.searchForm.sn,
                "shop_name": this.searchForm.shopTitle,
            }
            if (this.searchForm.gather_supply_id !== "") {
                para.gather_supply_id = this.searchForm.gather_supply_id
            }
            if (this.searchForm.supplier_id !== "") {
                para.supplier_id = this.searchForm.supplier_id
            }
            if (this.arketingChecked !== "") {
                para[this.arketingChecked] = 1
            }
            if (this.searchForm.status_lock !== "") {
                para.status_lock = this.searchForm.status_lock
            }
            if (this.searchForm.sortType !== "") {
                para.sortType = this.searchForm.sortType
            }
            if (this.searchForm.sortMode !== "") {
                para.sortMode = this.searchForm.sortMode
            }
            getAsyncProductList(para).then(r => {
                if (r.code === 0) {
                    this.tableList = r.data.list;
                    this.total = r.data.total;
                    zhLang.el.pagination.total = `共 {total} ${this.total / this.pageSize === 100 ? '+' : ''} 条`
                } else {
                    this.tableList = [];
                    this.total = 0;
                }
            });
        },
        // 跳转至前端
        copyLink(id) {
            let link = ""
            if (location.hostname === "localhost") {
                link = location.protocol + "//localhost:9527/goodsDetail?goods_id=" + id
            } else {
                link = location.origin + "/goodsDetail?goods_id=" + id
            }
            this.$fn.copy(link)
        },
        // 编辑
        edit(id) {
            this.$_blank("/layout/goodsIndex/addGoods", {id: id})
        },
        /**
         * 复制商品
         * @param id
         */
        copyProductByid(id){
            this.$confirm('确定要复制吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            }).then(() => {
                copyProduct({id: id}).then(r => {
                    if (r.code === 0) {
                        this.$message.success("复制成功");
                        this.getList();
                    }
                })
            }).catch(() => {
            });
        },

        /**
         * 单个删除
         * @param row
         */
        delGoods(id) {
            this.$confirm('确定要删除吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            }).then(() => {
                deleteProduct({id: id}).then(r => {
                    if (r.code === 0) {
                        this.$message.success("删除成功");
                        this.getList();
                    }
                })
            }).catch(() => {
            });
        },

        /**
         * 多选
         * @param val
         */
        handleSelectionChange(val) {
            this.selectTables = val;
        },

        // 批量删除
        batckDel() {
            this.deleteVisible = false;
            if (this.selectTables.length <= 0) {
                this.$message.warning("请选择要删除的数据");
                return;
            }
            let ids = [];
            this.selectTables.forEach(item => {
                ids.push(item.id);
            })
            deleteProductByIds({ids}).then(r => {
                if (r.code === 0) {
                    this.$message.success("删除成功");
                    this.getList();
                }
            })
        },

        handleCurrentChange(page) {
            this.page = page;
            this.getList();
        },

        handleSizeChange(size) {
            this.pageSize = size;
            this.getList();
        },

        //批量操作验证
        batchHandleVerify() {
            if (this.selectTables.length == 0) {
                this.$message.warning("请选择要处理的数据");
                return false;
            }
            return true;
        },

        //批量上架
        batchPutaway() {
            if (!this.batchHandleVerify()) {
                return;
            }
            let ids = [];
            this.selectTables.forEach(item => {
                ids.push(item.id);
            })
            batchDisplays({value: 1, ids: ids}).then(r => {
                if (r.code === 0) {
                    this.$message.success(r.msg);
                    this.getList();
                }
            });
        },

        //批量下架
        batchSoldOut() {
            if (!this.batchHandleVerify()) {
                return;
            }
            let ids = [];
            this.selectTables.forEach(item => {
                ids.push(item.id);
            })
            batchDisplays({value: 0, ids: ids}).then(r => {
                if (r.code === 0) {
                    this.$message.success(r.msg);
                    this.getList();
                }
            });
        },

        //批量删除
        batchDeleteDialog() {
            if (!this.batchHandleVerify()) {
                return;
            }
            this.$confirm('商品删除请谨慎操作，确定删除？', '删除商品', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            }).then(() => {
                this.batchDelete();
            }).catch(() => {
            });
        },

        batchDelete() {
            if (!this.batchHandleVerify()) {
                return;
            }
            let ids = [];
            this.selectTables.forEach(item => {
                ids.push(item.id);
            })
            deleteProductByIds({ids}).then(r => {
                if (r.code === 0) {
                    this.$message.success(r.msg);
                    this.getList();
                }
            })
        },

        // 批量分类
        onSubmitCategory(formName){
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.categoryVisible = false
                    this.batchCategory()
                    // this.$refs[formName].resetFields();
                } else {
                    return false;
                }
            });
        },
        onCloseCategory(formName){
            this.categoryVisible = false
            this.$refs[formName].resetFields();
        },
        batchCategory(){
            let ids = [];
            this.selectTables.forEach(item => {
                ids.push(item.id);
            })
            let params = {
                ids: ids,
                category1_id: this.batchForm.batch_category1_id,
                category2_id: this.batchForm.batch_category2_id,
                category3_id: this.batchForm.batch_category3_id,
                is_hot: this.is_hot,
                is_promotion: this.is_promotion,
                is_new: this.is_new,
            }
            batchChanges(params).then(res => {
                if (res.code === 0) {
                    this.$message.success(res.msg);
                    // this.getList();
                }
            })

        },

        onOpenBatch(){
            this.categoryVisible = true
            // this.batchForm.batch_category1_id = null
            // this.batchForm.batch_category2_id = null
            // this.batchForm.batch_category3_id = null
        },
        onOpenBrand(){
            this.brandVisible = true
        },
        onCloseBrand(){
            this.brandVisible = false
            this.batchBrand = null
        },
        onSubmitBrand(){
            if(!this.batchBrand){
                this.$message.error("请选择品牌")
                return;
            }
            //brand_id
            let ids = [];
            this.selectTables.forEach(item => {
                ids.push(item.id);
            })
            let params = {
                ids: ids,
                brand_id: this.batchBrand,
            }
            batchChanges(params).then(res => {
                if (res.code === 0) {
                    this.$message.success(res.msg);
                    this.onCloseBrand()
                    // this.getList();
                }
            })
        },
        onCheckedHot(){
            this.is_hot = 1
            this.is_promotion = 0
            this.is_new = 0
            this.batchCategory()
        },
        onCheckedPromotion(){
            this.is_hot = 0
            this.is_promotion = 1
            this.is_new = 0
            this.batchCategory()
        },
        onCheckedNew(){
            this.is_hot = 0
            this.is_promotion = 0
            this.is_new = 1
            this.batchCategory()
        },

    }
}
</script>
<style scoped lang="scss">
@import "@/style/base.scss";

.top-div {
  position: relative;
  width: 100%;

  .add-btn {
    position: absolute;
    right: 0;
    top: 0;
  }
}

.el-row {
  // padding: 0;

  .search-btn {
    background-color: #44d2b9;
    border-color: #44d2b9;
    color: white;
    margin-left: 15px;
    padding-left: 15px;
    padding-right: 15px;
  }

  .zih-span {
    display: inline-block;
    margin: 0 10px;
    color: #606266;
  }
}

.table-top {
  margin-top: 20px;
  margin-bottom: 20px;

  & > .el-checkbox.check-all {
    margin-right: 15px;
  }
}

::v-deep .el-switch {
  .el-switch__label {
    color: #666666;
  }
}

::v-deep .el-checkbox.zdy-checkbox {
  margin-right: 0;
  margin-left: 5px !important;

  &.is-bordered.is-checked {
    border-color: #44d2b9;

    .el-checkbox__input.is-checked {
      .el-checkbox__inner {
        background-color: #44d2b9;
        border-color: #44d2b9;
      }
    }

    .el-checkbox__label {
      color: #44d2b9;
    }
  }
}

/***************************** tabs部分 *******************************/
::v-deep .goods-tabs {
  .el-tabs__header {
    margin-bottom: 0px;

    .el-tabs__item {
      background-color: #f7f8fa;

      &.is-active {
        color: #303133;
        background-color: #ffffff;
      }

      &:hover {
        color: #303133;
      }
    }
  }
}

.checkbox-box {
  .el-checkbox {
    margin-right: 0;
  }
}
::v-deep .el-table .el-table__body { width: 100%; }
/* ::v-deep .el-pager{
  li.number:last-child{
    display: none;
  }
} */

.sn-code {
  margin-top: 5px;
  color: rgb(167, 167, 167);
  span {
    margin-left: 5px;
    font-size: 12px;
    color: #155bd4;
    cursor: pointer;
  }
}
</style>