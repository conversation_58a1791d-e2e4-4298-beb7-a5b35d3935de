<template>
  <m-card>
    <el-button type="primary" @click="openManageDialog">新增/修改</el-button>
    <el-form :model="searchInfo" class="search-term mt25" label-width="120px" inline>
      <el-form-item>
          <el-input placeholder="请输入" v-model="searchInfo.uid" class="line-input" clearable>
              <span slot="prepend">会员ID</span>
          </el-input>
      </el-form-item>
      <el-form-item>
          <el-input placeholder="请输入" v-model="searchInfo.member" class="line-input" clearable>
              <span slot="prepend">会员名称/手机号</span>
          </el-input>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input" >
            <div class="line-box ">
                <span >分销商等级</span>
            </div>
            <el-select v-model="searchInfo.level_id" class="w100" filterable clearable>
              <el-option v-for="item in levelOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
        </div>
      </el-form-item>
      <el-form-item>
        <div class="line-input" style="width: 520px">
            <div class="line-box" style="min-width: auto;padding: 0 10px;">
                <span>成为分销商时间</span>
            </div>
            <div class="f fac">
                <el-date-picker type="datetime" class="w100" value-format="yyyy-MM-dd HH:mm:ss" v-model="searchInfo.start_at" clearable placeholder="开始时间"></el-date-picker>
                <span class="zih-span">至</span>
                <el-date-picker type="datetime" class="w100" value-format="yyyy-MM-dd HH:mm:ss" v-model="searchInfo.end_at" clearable placeholder="截止时间"></el-date-picker>
            </div>
        </div>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="search">搜索</el-button>
        <el-button @click="exportTable">导出</el-button>
        <el-button type="text" @click="reSearch">重置搜索条件</el-button>
      </el-form-item>
    </el-form>
    <div class="search-term f fac mt25">
      <p>总人数: {{ resData.total }}人</p>
      <p class="ml10">累计分成金额: {{ resData.amount_total | formatF2Y }}元</p>
    </div>
    <el-table :data="tableData" class="mt25">
      <el-table-column label="ID" prop="id" align="center"></el-table-column>
      <el-table-column label="成为分销商时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.created_at | formatDate }}
        </template>
      </el-table-column>
      <el-table-column label="会员ID" prop="uid" align="center"></el-table-column>

      <el-table-column align="center" show-overflow-tooltip>
        <template slot="header">
          <p>昵称</p>
          <p>手机号</p>
        </template>
        <template slot-scope="scope">
          <p v-if="scope.row.user_info.nickname">{{scope.row.user_info.nickname}}</p>
          <p v-else>暂无昵称</p>
          <p>{{scope.row.user_info.username}}</p>
        </template>
      </el-table-column>

      <el-table-column label="分销商等级" prop="level_info.name" align="center">
      </el-table-column>
      <el-table-column label="累积推荐订单" align="center">
        <template slot-scope="scope">
          <p>{{ scope.row.recommend_order_count_total }}单</p>
          <p>{{ scope.row.recommend_order_amount_total | formatF2Y }}元</p>
        </template>
      </el-table-column>
      <el-table-column label="累积分成金额" align="center">
        <template slot-scope="scope">
          {{ scope.row.settle_amount_total | formatF2Y }}元
        </template>
      </el-table-column>
      <el-table-column label="已结算金额" align="center">
        <template slot-scope="scope">
          {{ scope.row.finish_settle_amount | formatF2Y }}元
        </template>
      </el-table-column>
      <el-table-column label="未结算金额" align="center">
        <template slot-scope="scope">
          {{ scope.row.wait_settle_amount | formatF2Y }}元
        </template>
      </el-table-column>
      <el-table-column label="黑名单" align="center">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.blacklist" :active-value="1" :inactive-value="0" @change="changeSwitch(scope.row.id)"> </el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="260" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="edit(scope.row)" style="padding: 0 !important;margin-left: 10px !important;">编辑</el-button>
          <el-button type="text" @click="jumpDistributorLog(scope.row)" style="padding: 0 !important;margin-left: 10px !important;">分成明细</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background :current-page="page" :page-size="pageSize" :page-sizes="[10, 30, 50, 100]" :style="{
              display: 'flex',
              justifyContent: 'flex-end',
              marginRight: '20px',
            }" :total="total"  @current-change="handleCurrentChange" @size-change="handleSizeChange"
                   layout="total, sizes, prev, pager, next, jumper"></el-pagination>
    <manage-dialog ref="manageDialog" @reload="getTableData"></manage-dialog>
  </m-card>
</template>

<script>
import manageDialog from "./components/manageDialog"
import {getDistributorList, getDistributorAllLevel, exportDistributorList, getBlacklist} from "@/api/distributor";
import infoList from "../../../mixins/infoList";

export default {
  name: "distributorManage",
  components: {manageDialog},
  mixins: [infoList],
  data() {
    return {
      levelOptions: [],
      listApi: getDistributorList,
      exApi: exportDistributorList,
      optionList: [],
    }
  },
  mounted() {
    this.getTableData()
    this.initOptions()
  },
  methods: {
    // 跳转至分成明细
    jumpDistributorLog(row) {
      this.$router.push({
        name: "distributorLog", query: {
          uid: row.uid
        }
      })
    },
    reSearch() {
      this.page = 1
      this.searchInfo = {}
    },
    search() {
      this.page = 1
      this.getTableData()
    },
    async initOptions() {
      let levelOptionsRes = await getDistributorAllLevel();
      if (levelOptionsRes.code === 0) {
        this.levelOptions = levelOptionsRes.data.levels
      }
    },
    edit(row) {
      this.$refs.manageDialog.isShow = true
      this.$refs.manageDialog.initOptions();
      this.$refs.manageDialog.setData(row);
    },
    openManageDialog() {
      this.$refs.manageDialog.isShow = true
      this.$refs.manageDialog.initOptions();
    },
    // 黑名单的状态
    async changeSwitch(id) {
      const data = {
        id: id,
      }
      const res = await getBlacklist(data)
      if(res.code === 0) {
        this.$message.success(res.msg)
        this.getTableData()
      }
    }
  }
}
</script>

<style scoped>
.w100 {
  width: 100%;
}
</style>