<template>
    <m-card>
        <el-tabs v-model="activeName" type="card">
            <el-tab-pane label="基础设置" name="base">
                <el-form ref="form" label-width="180px">
                    <el-form-item label="订单侠apikey:" prop="apikey">
                        <el-input
                            style="width: 600px"
                            v-model="formData.apikey"
                            placeholder="请输入"
                        ></el-input>
                    </el-form-item>

                    <el-form-item label="淘宝开放平台appKey:" prop="open_taobao_app_key">
                        <el-input
                            style="width: 600px"
                            v-model="formData.open_taobao_app_key"
                            placeholder="请输入"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="淘宝开放平台appSecret:" prop="open_taobao_app_secret">
                        <el-input
                            style="width: 600px"
                            v-model="formData.open_taobao_app_secret"
                            placeholder="请输入"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="淘宝开放平台pid:" prop="open_taobao_pid">
                        <el-input
                            style="width: 600px"
                            v-model="formData.open_taobao_pid"
                            placeholder="请输入"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="拼多多媒体id:" prop="pdd_media_id">
                        <el-input
                            style="width: 600px"
                            v-model="formData.pdd_media_id"
                            placeholder="请输入"
                        ></el-input>
                    </el-form-item>

                    <el-form-item label="淘宝授权回调地址host:" prop="redirect_uri_host">
                        <el-input
                            style="width: 600px"
                            v-model="formData.redirect_uri_host"
                            placeholder="请输入"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="">
                        <el-button type="primary" @click="save">保 存</el-button>
                    </el-form-item>
                </el-form>
            </el-tab-pane>
        </el-tabs>
    </m-card>
</template>
<script>
import { getSetting,saveSetting } from '@/api/eCommerceCPS'
export default {
    name: 'eCommerceCPSBase',
    data() {
        return {
            activeName: 'base',
            formData: {
                apikey: '',
                invite_code: '',
                open_taobao_app_key: '',
                open_taobao_app_secret: '',
                open_taobao_pid: '',
                pdd_media_id: '',
                pdd_pid: '',
                redirect_uri_host: '',
            },
        };
    },
    mounted() {
        this.getSetting()
    },
    methods: {
        async getSetting() {
            let res = await getSetting()
            if (res.code === 0) {
                this.formData = res.data.setting.value
                this.id = res.data.setting.id
            }
        },
        async save() {
            let param = {
                id: this.id,
                value: {
                    ...this.formData
                },
            };
            let res = await saveSetting(param);
            if (res.code === 0) {
                this.$message.success(res.msg)
            }
        },
    },
};
</script>
<style scoped>
</style>
