<template>
    <m-card class="search-box">
        <el-form ref="form" :model="formData" class="search-term" label-width="130px" inline>
            <el-form-item label>
                <div class="line-input">
                    <div class="line-box">
                        <span>订单类型</span>
                    </div>
                    <el-select v-model="formData.type" class="w100" clearable filterable>
                        <el-option label="京东联盟" value="jd"></el-option>
                        <el-option label="淘宝客" value="taobao"></el-option>
                        <el-option label="拼多多客" value="pdd"></el-option>
                        <el-option label="唯品会" value="vip"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label>
                <div class="line-input">
                    <div class="line-box">
                        <span>采购端</span>
                    </div>
                    <el-select v-model="formData.app_id" class="w100" clearable filterable>
                        <el-option
                            :label="item.app_name"
                            :value="item.id"
                            v-for="item in supplyOptions"
                        ></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label>
                <div class="line-input">
                    <div class="line-box">
                        <span>订单状态</span>
                    </div>
                    <el-select v-model="formData.flow_point" class="w100" clearable>
                        <el-option label="全部" value></el-option>
                        <el-option
                            :label="item.name"
                            :value="item.value"
                            v-for="item in orderSupplierConditions"
                        ></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="formData.order_id"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">订单号</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="formData.user_words"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">手机号/昵称</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input placeholder="请输入" v-model="formData.user_id" class="line-input" clearable>
                    <span slot="prepend">采购端平台会员ID</span>
                </el-input>
            </el-form-item>
            <br />
            <el-form-item label>
                <div class="line-input">
                    <div class="line-box">
                        <span>时间类型</span>
                    </div>
                    <el-select v-model="dateType" class="w100">
                        <el-option
                            v-for="item in dateTypeOptios"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <div class="line-input" style="width: 586px;">
                    <div class="f fac">
                        <el-date-picker
                            class="w100"
                            v-model="formData.start_at"
                            type="datetime"
                            placeholder="开始日期"
                        ></el-date-picker>
                        <span class="zih-span">至</span>
                        <el-date-picker
                            class="w100"
                            v-model="formData.end_at"
                            type="datetime"
                            placeholder="结束日期"
                        ></el-date-picker>
                    </div>
                </div>
            </el-form-item>
            <el-form-item label-width="0px">
                <div class="f fac dateBtnBox">
                    <span
                        @click="handleDateTab(item)"
                        v-for="item in dateList"
                        :class="time_type === item.value?'is_active':''"
                        :key="item.id"
                    >{{ item.name }}</span>
                </div>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="searchOrder()">搜索</el-button>
                <el-button @click="exportOrderList">导出</el-button>
                <el-button type="text" @click="clearSearchCondition()">重置搜索条件</el-button>
            </el-form-item>
        </el-form>
        <div class="table-box">
            <el-table :data="[{}]" class="table-head">
                <el-table-column label="商品"></el-table-column>
                <el-table-column label="支付金额/预估结算金额/推广费率" width="200" align="center"></el-table-column>
                <el-table-column label="手机号(ID)/会员昵称" width="200" align="center"></el-table-column>
                <el-table-column label="预估佣金/预估技术服务费" width="250" align="center"></el-table-column>
                <el-table-column label="分成基数/分成比例/分成金额" width="250" align="center"></el-table-column>
                <el-table-column label="订单类型" width="200" align="center"></el-table-column>
                <el-table-column label="订单状态" width="200" align="center"></el-table-column>
            </el-table>
            <div v-for="item in orderList" :key="item.id">
                <el-table :data="[item]" class="table-cont" :span-method="objectSpanMethod">
                    <el-table-column>
                        <template slot="header" slot-scope="scope">
                            <div class="w100 f fac fjsb">
                                <div class="f fac fw">
                                    <p>订单ID: {{item.id }}</p>
                                    <p>CPS订单编号: {{ item.order_id }}</p>
                                    <P>支付时间：{{item.pay_success_time}}</P>
                                </div>
                            </div>
                        </template>
                        <el-table-column>
                            <template slot-scope="scope">
                                <div class="f fac goods-box">
                                    <m-image
                                        style="width: 60px;height:60px"
                                        :src="scope.row.product_img"
                                    ></m-image>
                                    <div class="f1">
                                        <p class="hiddenText2">
                                            <a
                                                href="javascript:;"
                                                style="color: #155bd4;"
                                            >{{ scope.row.product_name }}</a>
                                        </p>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column width="200">
                            <template slot-scope="scope">
                                <div class="comm-box" style="width: 85%;">
                                    <p>支付金额: {{ scope.row.total_pay_amount | formatF2Y }}</p>
                                    <p>预估结算金额: {{scope.row.pay_goods_amount |formatF2Y}}</p>
                                    <p>推广费率: {{ scope.row.split_rate/100 }} %</p>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column width="200">
                            <template slot-scope="scope">
                                <div>
                                    <p class="title-3">
                                        <el-button
                                            type="text"
                                        >{{ item.application.user.username }}({{ item.application.user.id }})</el-button>
                                    </p>
                                    <p class="title-3">{{ item.application.user ? item.application.user.nickname : '' }}</p>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column width="250">
                            <template slot-scope="scope">
                                <div class="comm-box" style="width:85%">
                                    <p>预估佣金: {{ scope.row.estimated_commission | formatF2Y }}</p>
                                    <p>预估技术服务费: {{ scope.row.estimated_tech_service_fee | formatF2Y }}</p>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column width="250">
                            <template slot-scope="scope">
                                <div class="comm-box" style="width:85%">
                                    <p>分成基数:{{scope.row.estimated_commission - scope.row.estimated_tech_service_fee |formatF2Y}}</p>
                                    <p>分成比例: {{ scope.row.award_ratio/100}} %</p>
                                    <p>分成金额: {{ scope.row.award_amount |formatF2Y}}</p>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column width="200">
                            <template slot-scope="scope">
                                <div class="comm-box">
                                    <p class="title-3">{{ scope.row.type | orderType }}</p>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column width="200">
                            <template slot-scope="scope">
                                <div class="comm-box">
                                    <p class="title-3">{{ scope.row.status | formatStatus }}</p>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table-column>
                </el-table>
                <div class="table-foot-box">
                    <div class="f fac fjsb">
                        <p>采购端会员id: {{item.user_id}}</p>
                    </div>
                </div>
            </div>
            <el-pagination
                background
                :current-page="page"
                :page-size="pageSize"
                :page-sizes="[10, 30, 50, 100]"
                :style="{ display: 'flex', justifyContent: 'flex-end', marginRight: '20px' }"
                :total="total"
                @current-change="handleCurrentChange"
                @size-change="handleSizeChange"
                layout="total, sizes, prev, pager, next, jumper"
            ></el-pagination>
        </div>
    </m-card>
</template>
<script>
import { getOrderList,exportOrderList } from '@/api/eCommerceCPS';
import { getApplicationOption } from '@/api/tikTokGps';
export default {
    name: 'eCommerceCPSOrderManage',
    data() {
        return {
            // 分页部分
            page: 1,
            pageSize: 10,
            total: 0,
            // 列表
            orderList: [],
            //时间处理
            formData: {
                app_id: null,
                order_id: '',
                flow_point: '',
                user_id: null,
                user_words: '',
                start_at: '',
                end_at: '',
            },

            supplyOptions: [],
            dateType: 1, // 日期类型 1付款时间 2确认收货时间 3结算时间 4退款时间
            dateTypeOptios: [
                { label: '付款时间', value: 1 },
                { label: '确认收货时间', value: 2 },
                { label: '结算时间', value: 3 },
                { label: '退款时间', value: 4 },
            ],
            orderSupplierConditions: [
                { name: '已支付', value: 'PAY_SUCC' },
                { name: '确认收货', value: 'CONFIRM' },
                { name: '退款', value: 'REFUND' },
                { name: '结算', value: 'SETTLE' },
            ],
            time_type: '',
            dateList: [
                { name: '今', value: 0 },
                { name: '昨', value: 1 },
                { name: '近7天', value: 2 },
                { name: '近30天', value: 3 },
            ],
        };
    },
    filters: {
        // 格式化订单状态
        formatStatus: function(status) {
            let name = '';
            switch (status) {
                case 0:
                    name = '待分成';
                    break;
                case 1:
                    name = '已分成';
                    break;
            }
            return name;
        },
        // 订单类型
        orderType(status) {
            let name = '';
            switch (status) {
                case 'jd':
                    name = '京东联盟';
                    break;
                case 'taobao':
                    name = '淘宝客';
                    break;
                case 'pdd':
                    name = '拼多多客';
                    break;
                case 'vip':
                    name = '唯品会';
                    break;
            }
            return name;
        },
    },
    mounted() {
        this.getOrderList();
        this.getApplicationOption();
    },
    methods: {
        // 获取采购段列表
        getApplicationOption() {
            getApplicationOption().then((res) => {
                if (res.code === 0) {
                    this.supplyOptions = res.data.list;
                }
            });
        },
        // 切换日期
        handleDateTab(item) {
            this.time_type = this.time_type === item.value ? '' : item.value;
            const todayDate = new Date();
            switch (this.time_type) {
                case 0:
                    const dateToday1 = new Date();
                    dateToday1.setHours(0);
                    dateToday1.setMinutes(0);
                    dateToday1.setSeconds(0);
                    this.formData.start_at = dateToday1;
                    todayDate.setHours(23);
                    todayDate.setMinutes(59);
                    todayDate.setSeconds(59);
                    this.formData.end_at = todayDate;
                    break;
                case 1:
                    const dateYesterday1 = new Date();
                    dateYesterday1.setTime(
                        dateYesterday1.getTime() - 3600 * 1000 * 24 * 1,
                    );
                    dateYesterday1.setHours(0);
                    dateYesterday1.setMinutes(0);
                    dateYesterday1.setSeconds(0);
                    this.formData.start_at = dateYesterday1;

                    const dateYesterday2 = new Date();
                    dateYesterday2.setTime(
                        dateYesterday2.getTime() - 3600 * 1000 * 24 * 1,
                    );
                    dateYesterday2.setHours(23);
                    dateYesterday2.setMinutes(59);
                    dateYesterday2.setSeconds(59);
                    this.formData.end_at = dateYesterday2;
                    break;
                case 2:
                    const date7Day1 = new Date();
                    date7Day1.setTime(
                        date7Day1.getTime() - 3600 * 1000 * 24 * 7,
                    );
                    date7Day1.setHours(0);
                    date7Day1.setMinutes(0);
                    date7Day1.setSeconds(0);
                    this.formData.start_at = date7Day1;

                    todayDate.setHours(23);
                    todayDate.setMinutes(59);
                    todayDate.setSeconds(59);
                    this.formData.end_at = todayDate;
                    break;
                case 3:
                    const date30Day1 = new Date();
                    date30Day1.setTime(
                        date30Day1.getTime() - 3600 * 1000 * 24 * 30,
                    );
                    date30Day1.setHours(0);
                    date30Day1.setMinutes(0);
                    date30Day1.setSeconds(0);
                    this.formData.start_at = date30Day1;

                    todayDate.setHours(23);
                    todayDate.setMinutes(59);
                    todayDate.setSeconds(59);
                    this.formData.end_at = todayDate;
                    break;

                default:
                    break;
            }
        },
        paramsFun() {
            let params = {
                page: this.page,
                pageSize: this.pageSize,
                app_id: this.formData.app_id,
                order_id: this.formData.order_id,
                flow_point: this.formData.flow_point,
                user_id: parseInt(this.formData.user_id),
                user_words: this.formData.user_words,
                start_at: this.formData.start_at,
                end_at: this.formData.end_at,
                time_type: this.dateType,
                type: this.formData.type,
            };
            return params;
        },
        // 搜索
        searchOrder() {
            this.page = 1;
            this.getOrderList();
        },
        // 导出
        async exportOrderList() {
            let params = this.paramsFun();
            let res = await exportOrderList(params);
            if (res.code === 0) {
                window.open(this.$path + '/' + res.data.link)
            }
        },
        // 重置搜索条件
        clearSearchCondition() {
            this.page = 1;
            this.pageSize = 10;
            this.total = 0;
            this.$refs.form.resetFields();
            this.formData.app_id = null;
            this.formData.order_id = '';
            this.formData.flow_point = '';
            this.formData.user_id = null;
            this.formData.user_words = '';
            this.formData.start_at = '';
            this.formData.end_at = '';
            this.formData.type = '';
            this.time_type = '';
            this.dateType = 1;
            this.getOrderList();
        },
        // 获取订单
        async getOrderList() {
            let params = this.paramsFun();
            let res = await getOrderList(params);
            if (res.code === 0) {
                this.total = res.data.total;
                this.orderList = res.data.list;
            }
        },
        // 下一页
        handleCurrentChange(page) {
            this.page = page;
            this.getOrderList();
        },
        // 每页显示条数
        handleSizeChange(size) {
            this.pageSize = size;
            this.getOrderList();
        },
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            if (columnIndex >= 2) {
                if (rowIndex % 9999 === 0) {
                    return {
                        rowspan: 9999,
                        colspan: 1,
                    };
                } else {
                    return {
                        rowspan: 0,
                        colspan: 0,
                    };
                }
            }
        },
    },
};
</script>

<style lang="scss" scoped>
@import "@/style/base.scss";
// 搜索部分
.search-term {
  .dateBtnBox {
    height: 36px;
    line-height: 36px;
    span {
      height: 27px;
      line-height: 22px;
      display: inline-block;
      margin-right: 10px;
      padding: 2px 4px;
      border: 1px solid #dcdee0;
      color: #c8c9cc;
      cursor: pointer;
      box-sizing: border-box;
      &:last-child {
        margin-right: 0;
      }
      &:hover {
        color: #155bd4;
        border-color: #155bd4;
        background-color: #fff;
      }
      &.is_active {
        color: #155bd4;
        border-color: #155bd4;
        background-color: #fff;
      }
    }
  }
}

/* 订单列表部分开始 */
.table-title-box {
  p.title-p {
    margin-right: 20px;
  }
  span {
    font-size: 12px;
    color: #c1c1c1;
    margin-right: 10px;
  }
}
.table-box {
  margin-top: 25px;
  // 订单item
  /* .table-item {
      border: 1px solid #e8e8e8;
      border-radius: 5px;
      margin-bottom: 10px;
      // 表头
      .table-head {
          border-bottom: 1px solid #e8e8e8;
          background-color: #fafafa;
          color: #888787;
          font-weight: bold;
          padding: 10px 0;
          a.close-order {
              display: inline-block;
              margin-right: 20px;
          }
          div {
              p {
                  margin-left: 10px;
                  &.supplier-p {
                      background-color: rgb(74, 197, 156);
                      padding: 10px;
                      color: #ffffff;
                      border-radius: 3px;
                  }
              }
          }
      }
      .table-cont {
          .el-row {
              padding: 0;
              .el-col {
                  border-right: 1px solid #e8e8e8;
                  padding: 10px 0;
                  height: 120px;
                  &:last-child {
                      border-right: none;
                  }
                  &.goods-box {
                      img {
                          width: 100px;
                          height: 100px;
                          margin: 0 10px;
                      }
                  }
                  .comm-box {
                      p {
                          margin-bottom: 10px;
                          &:last-child {
                              margin-bottom: 0;
                          }
                      }
                  }
              }
          }
      }
      // 收货人信息
      .table-foot {
          border-top: 1px solid #e8e8e8;
          padding: 10px 0;
          p {
              margin-left: 10px;
              &.addr-p {
                  span {
                      margin-right: 5px;
                  }
              }
          }
      }
  } */
}
/* 订单列表部分结束 */
::v-deep .tabs-box {
  padding-left: 20px;
  padding-right: 20px;
  .el-radio-group {
    .el-radio {
      width: 95px;
      height: 37px;
      line-height: 37px;
      text-align: center;
      .el-radio__input {
        display: none;
      }
      .el-radio__label {
        padding: 0;
      }
      &.is-checked {
        background-color: #13c7a7;
        border-radius: 50px;
        .el-radio__label {
          color: white;
        }
      }
    }
  }
}
.search-box {
  // padding: 20px;
  ::v-deep .el-date-editor.el-range-editor.el-input__inner {
    padding: 0 10px;
  }
}
.green-color {
  color: #4ac59c;
}
.order-card {
  margin-left: 1px;
  border-top: 0;
}
/***************************** tabs部分 *******************************/
::v-deep .order-tabs {
  .el-tabs__header {
    margin-bottom: 0px;
    .el-tabs__item {
      background-color: #f7f8fa;
      &.is-active {
        color: #303133;
        background-color: #ffffff;
      }
      &:hover {
        color: #303133;
      }
    }
  }
}
/***************************** 表格部分 *******************************/
::v-deep .el-table.table-head {
  margin-bottom: 25px;
  &::before {
    display: none;
  }
  .el-table__header-wrapper {
    tr th {
      background-color: #f7f8fa !important;
      border-bottom: 0;
    }
  }
  .el-table__body-wrapper {
    display: none;
  }
}
::v-deep .el-table.table-cont.el-table--border{
  border: 1px solid #efefef !important;
}
::v-deep .el-table.table-cont {
  margin-bottom: 0;
  thead {
    tr th{
      background-color: #f7f8fa !important;
    }
    tr:last-child {
      display: none;
    }
    tr:first-child {
      th {
        p {
          margin-right: 20px;
          &.supplier-p {
            //background-color: rgb(74, 197, 156);
            padding: 10px;
            color: #0a3cdc;
            //border-radius: 3px;
          }
        }
      }
    }
  }
  .el-table__body-wrapper {
    .goods-box {
      p {
        margin-left: 10px;
      }
    }
    .comm-box {
      width: 50%;
      margin: 0 auto;
      p {
        margin-bottom: 10px;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
.table-foot-box {
  border: 1px solid #ebeef5;
  border-top: 0;
  margin-bottom: 20px;
  padding: 10px;
  p {
    margin-left: 10px;
    &.addr-p {
      span {
        margin-right: 5px;
      }
    }
    &:first-child {
      margin-left: 0;
    }
  }
}
</style>