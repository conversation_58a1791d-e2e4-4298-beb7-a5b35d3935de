<template>
    <m-card class="search-box">
        <el-form :model="formData" class="search-term" label-width="80px" inline>
            <el-form-item label>
                <el-input
                    placeholder="请输入"
                    v-model="formData.supplier_id"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">供应商ID</span>
                </el-input>
            </el-form-item>
            <el-form-item label>
                <el-input
                    placeholder="请输入"
                    v-model="formData.supplier_name"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">供应商名称</span>
                </el-input>
            </el-form-item>
            <el-form-item label>
                <el-input
                    placeholder="请输入"
                    v-model="formData.real_name"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">姓名</span>
                </el-input>
            </el-form-item>
            <el-form-item label>
                <el-input
                    placeholder="请输入"
                    v-model="formData.phone"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">手机号</span>
                </el-input>
            </el-form-item>
            <el-form-item label>
                <div class="line-input">
                    <div class="line-box">
                        <span>供应商分类</span>
                    </div>
                    <el-select class="w100" filterable clearable v-model="formData.category_id">
                        <el-option v-for="item in supplierCategoryList" :label="item.name" :value="item.id" :key="item.id">
                            {{ item.name }}
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <br />
            <el-form-item>
                <el-button @click="search()" type="primary">搜索</el-button>
                <el-button @click="mangeExport">导出</el-button>
                <el-button @click="clearSearch()" type="text">重置搜索条件</el-button>
            </el-form-item>
        </el-form>
        <div style="margin-top: 1em; margin-bottom: 1em">
            <el-row>
                <span class="mr_20">合计结果</span>
                <el-button type="text" @click="handleInfo">数据指标说明</el-button>
            </el-row>
            <div class="grid f fac" style="justify-content: space-around;">
                <div class="grid-content" align="center">
                    <p class="numbers-p">{{ dataInfo.OrderTotalAmount | toMoney }}</p>
                    <p>订单总额（元）</p>
                </div>
                <div class="grid-content" align="center">
                    <p class="numbers-p">{{ dataInfo.BillTotalAmount | toMoney }}</p>
                    <p>账单总额（元）</p>
                </div>
                <div class="grid-content" align="center">
                    <p class="numbers-p">{{ dataInfo.TechnicalServicesTotalAmount | toMoney }}</p>
                    <p>技术服务费总额（元）</p>
                </div>
                <div class="grid-content" align="center">
                    <p class="numbers-p">{{ dataInfo.WithdrawnTotalAmount | toMoney }}</p>
                    <p>已提现金额（元）</p>
                </div>
                <div class="grid-content" align="center">
                    <p class="numbers-p">{{ dataInfo.NotWithdrawn | toMoney }}</p>
                    <p>未提现金额（元）</p>
                </div>
                <div class="grid-content" align="center">
                    <p class="numbers-p">{{ dataInfo.WithdrawingInProgress | toMoney }}</p>
                    <p>提现中金额（元）</p>
                </div>
                <div class="grid-content" align="center">
                    <p class="numbers-p">{{ dataInfo.WithdrawalTotalFee | toMoney }}</p>
                    <p>提现手续费（元）</p>
                </div>
                <div class="grid-content" align="center">
                    <p class="numbers-p">{{ dataInfo.InvalidBill | toMoney }}</p>
                    <p>无效账单金额（元）</p>
                </div>
                <div class="grid-content" align="center">
                    <p class="numbers-p">{{ dataInfo.PaidAmount | toMoney }}</p>
                    <p>已打款金额（元）</p>
                </div>
            </div>
        </div>
        <el-table :data="tableData" class="mt25" ref="table">
            <el-table-column label="供应商分类" prop="category_info.name" align="center"></el-table-column>
            <el-table-column label="供应商名称" prop="name" align="center"></el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>姓名</p>
                    <p>手机号</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.realname }}</p>
                    <p>{{ scope.row.mobile }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>订单总数（单）</p>
                    <p>订单总额（元）</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.order_total_count }}</p>
                    <p>{{ scope.row.order_total_amount | toMoney }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>账单总额（元）</p>
                    <p>技术服务费总额（元）</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.bill_total_amount | toMoney }}</p>
                    <p>{{ scope.row.technical_services_total_amount | toMoney }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>已提现金额（元）</p>
                    <p>未提现金额（元）</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.withdrawn_total_amount | toMoney }}</p>
                    <p>{{ scope.row.not_withdrawal_total_amount | toMoney }}</p>
                </template>
            </el-table-column>
            <el-table-column label="提现中的金额（元）" align="center">
                <template slot-scope="scope">
                    <span>{{ scope.row.withdrawing_in_progress | toMoney }}</span>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>提现手续费（元）</p>
                    <p>无效账单金额（元）</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.withdrawal_total_fee | toMoney }}</p>
                    <p>{{ scope.row.invalid_bill | toMoney }}</p>
                </template>
            </el-table-column>
            <el-table-column label="已打款金额（元）" align="center">
                <template slot-scope="scope">
                    <p>{{ scope.row.paid_amount | toMoney }}</p>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100, 200]"
            :style="{
                display: 'flex',
                justifyContent: 'flex-end',
                marginRight: '20px',
            }"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes,prev, pager, next, jumper"
        ></el-pagination>

        <el-dialog
            title="数据指标说明"
            :visible="isShow"
            width="1000px"
            :show-close="false"
            :before-close="handleClose"
        >
            <div class="ml_30 mr_20 m_dialog">
                <p>订单总额（元）:订单商品金额+运费，非实际支付金额，如果改价后，取改价后的金额！</p>
                <p>账单金额（元）:该供应商所有订单给供应商的结算金额！</p>
                <p>技术服务费总额（元）:该供应商所有订单平台扣点总额！</p>
                <p>已提现金额（元）:该供应商结算金额中，已经申请提现的金额！</p>
                <p>未提现金额（元）:该供应商结算金额中，未申请提现的金额！一般等于账单总额-已提现金额！</p>
                <p>提现中金额（元）:该供应商已经申请提现，但是未打款的金额！</p>
                <p>提现手续费（元）:供应商提现结算金额时扣除的提现手续费！包含已打款+提现中金额的账单手续费，审核无效的账单不收取提现手续费！如果提现中的金额已经审核了，审核中无效的账单提现手续费也相应扣除</p>
                <p>无效账单金额（元）:供应商申请提现，审核无效的账单总额！</p>
                <p>已打款金额（元）:供应商申请提现，已经完成打开的金额！</p>
            </div>
            <div class="f fac fjc">
                <el-button type="primary" class="but" @click="handleClose">关闭</el-button>
            </div>
        </el-dialog>
    </m-card>
</template>

<script>
import { getTotalBill, getSupplierBill,exportSupplierBill } from '@/api/finance';
import {
    getSupplierCategoryList
} from "@/api/supplierCategory";
export default {
    name: 'supplierBillManage',
    data() {
        return {
            formData: {
                supplier_id: null, // 供应商id
                supplier_name: '', // 供应商名称
                real_name: '', // 姓名
                phone: '', // 手机号
                category_id: null,
            },
            dataInfo: {}, // 数据统计列表
            tableData: [],
            page: 1,
            pageSize: 10,
            total: 0,
            isShow: false,

            supplierCategoryList: [] // 获取供应商分类
        };
    },
    mounted() {
        this.getTotalBill(); // 获取数据统计列表
        this.initSupplierCategory(); // 获取供应商分类
        this.getSupplierBillList(); // 获取列表
    },
    filters: {
        toMoney(fen) {
            if (fen) {
                if (isNaN(fen)) {
                    this.$message.error('金额中含有不能识别的字符');
                    return;
                }
                var num = fen;
                num = fen * 0.01;
                num += '';
                var reg =
                    num.indexOf('.') > -1
                        ? /(\d{1,3})(?=(?:\d{3})+\.)/g
                        : /(\d{1,3})(?=(?:\d{3})+$)/g;
                num = num.replace(reg, '$1');
                
                var f = parseFloat(num);
                var f = Math.round(num * 100) / 100;
                var s = f.toString();
                var rs = s.indexOf('.');
                if (rs < 0) {
                    rs = s.length;
                    s += '.';
                }
                while (s.length <= rs + 2) {
                    s += '0';
                }
                num = s;
                
                num = typeof num == 'string' ? parseFloat(num) : num; //判断是否是字符串如果是字符串转成数字
                num = num.toFixed(2); //保留两位
                num = parseFloat(num); //转成数字
                num = num.toLocaleString(); //转成金额显示模式
                //判断是否有小数
                if (num.indexOf('.') == -1) {
                    num = num + '.00';
                } else {
                    num = num.split('.')[1].length < 2 ? num + '0' : num;
                }
                return num; //返回的是具有千分位格式并保留2位小数的字符串
            } else {
                return '0.00';
            }
        },
    },
    methods: {
        // 处理搜索条件
        paramsFun() {
            let params = {
                page: this.page,
                pageSize: this.pageSize,
            }
            if (this.formData.supplier_id && this.formData.supplier_id !== '') {
                params.supplier_id = parseInt(this.formData.supplier_id); 
            };
            if (this.formData.supplier_name) {
                params.supplier_name = this.formData.supplier_name;
            };
            if (this.formData.real_name) {
                params.real_name = this.formData.real_name;
            };
            if (this.formData.phone) {
                params.phone = this.formData.phone;
            };
            if (this.formData.category_id && this.formData.category_id !== '') {
                params.category_id = this.formData.category_id;
            };
            return params
        },
        //获取供应商分类
        initSupplierCategory() {
            getSupplierCategoryList().then(res => {
                if (res.code === 0) {
                    this.supplierCategoryList = res.data.list;
                }
            });
        },
        // 搜索
        search() {
            this.page = 1;
            this.getSupplierBillList();
            this.getTotalBill();
        },
        // 获取列表
        async getSupplierBillList() {
            let params = this.paramsFun();
            let res = await getSupplierBill(params);
            if (res.code === 0) {
                this.tableData = res.data.list;
                this.total = res.data.total;
            }
        },
        // 导出
        async mangeExport() {
            let params = this.paramsFun();
            let res = await exportSupplierBill(params);
            if (res.code === 0) {
                console.log(res);
                
                window.open(this.$path + '/' + res.data);
            }
        },
        // 重置搜索条件
        clearSearch() {
            this.page = 1;
            this.pageSize = 10;
            this.total = 0;
            this.formData = {
                supplier_id: null, // 供应商id
                supplier_name: '', // 供应商名称
                real_name: '', // 姓名
                phone: '', // 手机号
                category_id: null,
            };
            this.getSupplierBillList();
            this.getTotalBill();
        },
        // 获取数据统计列表
        async getTotalBill() {
            let params = this.paramsFun();
            let res = await getTotalBill(params);
            if (res.code === 0) {
                this.dataInfo = res.data;
            }
        },
        // 分页
        handleCurrentChange(page) {
            this.page = page;
            this.getSupplierBillList()
        },
        handleSizeChange(pageSize) {
            this.pageSize = pageSize;
            this.getSupplierBillList()
        },
        // 打开数据指标说明
        handleInfo() {
            this.isShow = true;
        },
        // 关闭数据指标说明
        handleClose() {
            this.isShow = false;
        },
    },
};
</script>

<style lang="scss" scoped>
.grid {
    background: rgb(245, 248, 254);
    border-radius: 12px;
    padding: 17px 22px;
}

.grid-content {
    /* border-radius: 4px; */
    min-height: 36px;
}

.grid-content .numbers-p {
    margin-bottom: 15px;
    font-size: 20px;
}

.m_dialog p {
    margin-bottom: 20px;
    line-height: 25px;
}

.but {
    margin: 20px auto 0;
}
</style>