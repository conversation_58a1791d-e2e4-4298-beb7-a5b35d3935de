<!-- 提现记录->审核dialog -->
<template>
  <el-dialog
      title="提现审核"
      :visible="isShow"
      width="40%"
      :modal-append-to-body="false"
      :append-to-body="true"
      :before-close="handleClose">
    <el-form :model="formData" label-width="120px">
      <el-form-item label="提现金额:">
        <p>{{ withdrawal_amount | formatF2Y }}元</p>
      </el-form-item>
      <el-form-item label="无效金额:">
        <div class="f fac">
          <el-input-number v-model="formData.invalid_amount" :controls="false" :precision="2" :min="0"
                           class="input-number-text-left"></el-input-number>
          <span style="margin-left: 10px">元</span>
        </div>
      </el-form-item>
      <el-form-item label="驳回金额:">
        <div class="f fac">
          <el-input-number v-model="formData.rejected_amount" :controls="false" :precision="2" :min="0"
                           class="input-number-text-left"></el-input-number>
          <span style="margin-left: 10px">元</span>
        </div>
      </el-form-item>
      <el-form-item label="通过审核金额:">
        <p>{{
            income_amount - $changeMoneyY2F(formData.invalid_amount) - $changeMoneyY2F(formData.rejected_amount) > 0 ? income_amount - $changeMoneyY2F(formData.invalid_amount) - $changeMoneyY2F(formData.rejected_amount) : 0 | formatF2Y
          }}元</p>
      </el-form-item>
      <el-form-item label="手续费:">
        <p>{{ poundage_amount | formatF2Y }}元</p>
      </el-form-item>
      <el-form-item label="劳务税:">
        <p>{{ service_tax | formatF2Y }}元</p>
      </el-form-item>
      <el-form-item label="备注:">
        <el-input type="textarea" :rows="4" v-model="formData.remarks"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirm(1)">通 过</el-button>
      <el-button type="danger" @click="confirm(2)">无 效</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {withdrawStatus} from "@/api/finance"

export default {
  name: "auditDialog",
  data() {
    return {
      isShow: false,
      // 提现金额
      withdrawal_amount: 0,
      income_amount: 0,
      // 手续费
      poundage_amount: 0,
      // 劳务税
      service_tax: 0,
      formData: {
        id: 0,
        withdrawal_status: 0, // 1审核通过  2 无效
        invalid_amount: 0, // 无效金额
        rejected_amount: 0, // 驳回金额
        remarks: ""
      }
    }
  },
  methods: {
    confirm(status = 1) {
      let params = {
        id: this.formData.id,
        withdrawal_status: status,
        invalid_amount: this.$changeMoneyY2F(this.formData.invalid_amount),
        rejected_amount: this.$changeMoneyY2F(this.formData.rejected_amount),
        remarks: this.formData.remarks
      }
      withdrawStatus(params).then(res => {
        if (res.code === 0) {
          this.$message.success(res.msg)
          this.handleClose()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    handleClose() {
      this.isShow = false
      let id = this.formData.id
      // 提现金额
      this.withdrawal_amount = 0
      // 手续费
      this.poundage_amount = 0
      // 劳务税
      this.service_tax = 0
      this.formData = {
        id: 0,
        withdrawal_status: 0, // 1审核通过  2 无效
        invalid_amount: 0, // 无效金额
        rejected_amount: 0, // 驳回金额
        remarks: ""
      }
      this.$emit("reload", id)
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/style/base.scss";
</style>