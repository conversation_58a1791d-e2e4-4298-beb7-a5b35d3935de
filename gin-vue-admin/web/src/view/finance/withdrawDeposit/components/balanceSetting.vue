<!-- 余额提现设置 -->
<template>
  <el-row>
    <el-form :model="formData" label-width="120px">
      <el-col>
        <el-form-item label="余额提现:">
          <el-radio-group v-model="formData.balance_withdrawal">
            <el-radio :label="1">开启</el-radio>
            <el-radio :label="2">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="汇聚代付:">
          <el-radio-group v-model="formData.convergence_payed">
            <el-radio :label="1">开启</el-radio>
            <el-radio :label="2">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="手动提现:">
          <el-radio-group v-model="formData.manual_withdrawal">
            <el-radio :label="1">开启</el-radio>
            <el-radio :label="2">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-radio-group v-model="formData.withdrawal_type">
            <el-radio :label="1">银行卡</el-radio>
            <el-radio :label="2">支付宝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="提现手续费:">
          <el-input-number
              :controls="false"
              :precision="2"
              class="input-number-text-left"
              v-model="formData.withdrawal_charge"
          ></el-input-number>
          %
        </el-form-item>
        <el-form-item>
          <el-button @click="saveSetting" type="primary">保 存</el-button>
        </el-form-item>
      </el-col>
    </el-form>
  </el-row>
</template>
<script>

import {mapGetters} from "vuex";

const path = process.env.VUE_APP_BASE_API;
import {getSetting, setSetting} from "@/api/finance"; //  此处请自行替换地址

export default {

  name: "BalanceSetting",
  data() {
    return {
      formData: {
        balance_withdrawal: 1,
        convergence_payed: 1,
        manual_withdrawal: 1,
        withdrawal_type: 1,
        withdrawal_charge: 0,
      },
    };

  },

  mounted: function () {     //页面加载完成后执行的方法

    this.getSetting()
  },



  methods: {
    async saveSetting() {

      let that = {
        balance_withdrawal: this.formData.balance_withdrawal,
        convergence_payed: this.formData.convergence_payed,
        manual_withdrawal: this.formData.manual_withdrawal,
        withdrawal_type: this.formData.withdrawal_type,
        withdrawal_charge: this.formData.withdrawal_charge *100,
      }


      let res = await setSetting(that)

      if (res.code === 0) {
        this.$message.success("操作成功")
      }


      console.log("修改返回", res)
    },

    getSetting() {


      getSetting().then((res) => {
        console.log("返回数据1：" + JSON.stringify(res))
        if (res.code === 0) {
          this.formData = res.data

          this.formData.withdrawal_charge = this.formData.withdrawal_charge / 100
        }
      });


    },

  },
};
</script>
<style lang="scss" scoped>
</style>