<!-- 收入提现设置 -->
<template>
  <el-row>
    <el-form :model="incomeformData" label-width="120px">
      <el-col>
        <el-form-item label="收入提现:">
          <el-radio-group v-model="incomeformData.income_withdrawal">
            <el-radio :label="1">开启</el-radio>
            <el-radio :label="2">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="汇聚代付:">
          <el-radio-group v-model="incomeformData.convergence_payed">
            <el-radio :label="1">开启</el-radio>
            <el-radio :label="2">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="提现到站内余额:">
          <el-radio-group v-model="incomeformData.withdrawal_to_balance">
            <el-radio :label="1">开启</el-radio>
            <el-radio :label="2">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="手动提现:">
          <el-radio-group v-model="incomeformData.manual_withdrawal">
            <el-radio :label="1">开启</el-radio>
            <el-radio :label="2">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-radio-group v-model="incomeformData.withdrawal_type">
            <el-radio :label="1">银行卡</el-radio>
            <el-radio :label="2">支付宝</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item label="微信-工猫提现:">
          <el-radio-group v-model="incomeformData.gong_mall_wechat">
            <el-radio :label="1">开启</el-radio>
            <el-radio :label="2">关闭</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="支付宝-工猫提现:">
          <el-radio-group v-model="incomeformData.gong_mall_ali">
            <el-radio :label="1">开启</el-radio>
            <el-radio :label="2">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="银行卡-工猫提现:">
          <el-radio-group v-model="incomeformData.gong_mall_bank">
            <el-radio :label="1">开启</el-radio>
            <el-radio :label="2">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="提现手续费:">
          <el-input-number
            :controls="false"
            :precision="2"
            class="input-number-text-left"
            v-model="incomeformData.withdrawal_charge"
          ></el-input-number>
          %
        </el-form-item>
        <el-form-item label="提现劳务税:">
          <el-input-number
            :controls="false"
            :precision="2"
            class="input-number-text-left"
            v-model="incomeformData.withdrawal_service_tax"
          ></el-input-number>
          %
        </el-form-item>
        <el-form-item v-if="incomeformData.grads_list.length > 0">
          <div
            class="f fac input-number-group"
            v-for="(item, index) in incomeformData.grads_list"
            :key="item.id"
          >
            <div class="input-left-div group-div">范围</div>
            <el-input-number
              :controls="false"
              :precision="2"
              class="input-number-text-left"
              v-model="item.amount"
            ></el-input-number>
            <div class="group-div input-right-div">元</div>
            <el-input-number
              :controls="false"
              :precision="2"
              class="input-number-text-left"
              v-model="item.ratio"
            ></el-input-number>
            <div class="group-div input-right-div">%</div>
            <div class="group-div input-right-div2" @click="delGrads(index)">
              <i class="el-icon-delete-solid"></i>
            </div>
          </div>
          <p class="color-grap">必须按金额从小到大填写</p>
        </el-form-item>
        <el-form-item>
          <el-button @click="addGrads">添加梯度比例</el-button>
          <el-button type="primary" @click="saveSetting">保 存</el-button>
        </el-form-item>
      </el-col>
    </el-form>
  </el-row>
</template>
<script>
import { mapGetters } from "vuex";

const path = process.env.VUE_APP_BASE_API;
import { incomeGetSetting, incomeSetSetting } from "@/api/finance"; //  此处请自行替换地址
export default {
  name: "income_withdrawalSetting",
  data() {
    return {
      incomeformData: {
        income_withdrawal: 1,
        convergence_payed: 1,
        withdrawal_to_balance: 1,
        manual_withdrawal: 1,
        withdrawal_type: 1,
        // gong_mall_wechat: 1,
        gong_mall_ali: 1,
        gong_mall_bank: 1,
        withdrawal_charge: 0,
        withdrawal_service_tax: 0,
        grads_list: [],
      },
    };
  },
  mounted() {
    // this.getSetting()
  },
  methods: {
    //赋值表单
    setFrom(val) {
      const keys = Object.keys(val);
      const that = this;
      keys.forEach((element) => {
        that.incomeformData[element] = val[element];
      });
    },
    async saveSetting() {
      let list = [];
      if (this.incomeformData.grads_list.length > 0) {
        list = this.incomeformData.grads_list.map((item) => ({
          amount: this.$changeMoneyY2F(item.amount),
          ratio: item.ratio * 100,
        }));
      }
      let that = {
        income_withdrawal: this.incomeformData.income_withdrawal,
        convergence_payed: this.incomeformData.convergence_payed,
        withdrawal_to_balance: this.incomeformData.withdrawal_to_balance,
        manual_withdrawal: this.incomeformData.manual_withdrawal,
        withdrawal_type: this.incomeformData.withdrawal_type,
        // gong_mall_wechat: this.incomeformData.gong_mall_wechat,
        gong_mall_ali: this.incomeformData.gong_mall_ali,
        gong_mall_bank: this.incomeformData.gong_mall_bank,
        withdrawal_charge: this.incomeformData.withdrawal_charge * 100,
        withdrawal_service_tax:
          this.incomeformData.withdrawal_service_tax * 100,
        grads_list: list,
      };

      console.log("数据nn：", JSON.stringify(this.incomeformData.grads_list));
      console.log("数据nnaa：", JSON.stringify(that));

      let res = await incomeSetSetting(that);

      if (res.code === 0) {
        this.$message.success("操作成功");
      }

      console.log("修改返回", res);
    },

    async getSetting() {
      incomeGetSetting().then((res) => {
        console.log("返回数据15：" + JSON.stringify(res));
        if (res.code === 0) {
          this.setFrom(res.data);
          // this.incomeformData = res.data;
          // this.incomeformData.grads_list = [];

          this.incomeformData.withdrawal_charge =
            this.incomeformData.withdrawal_charge / 100;
          this.incomeformData.withdrawal_service_tax =
            this.incomeformData.withdrawal_service_tax / 100;
          this.incomeformData.grads_list = this.incomeformData.grads_list.map(
            (item) => ({
              amount: this.$changeMoneyF2Y(item.amount),
              ratio: item.ratio / 100,
            })
          );
        }
      });
    },

    addGrads() {
      this.incomeformData.grads_list.push({
        amount: 0,
        ratio: 0,
      });
    },
    delGrads(index) {
      this.incomeformData.grads_list.splice(index, 1);
    },
  },
};
</script>
<style lang="scss" scoped>
.input-number-group {
  margin-bottom: 10px;
  .group-div {
    background-color: #f5f7fa;
    color: #909399;
    vertical-align: middle;
    display: table-cell;
    position: relative;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 0 20px;
    white-space: nowrap;
  }
  .input-left-div {
    border-right: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .input-right-div {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-left: 0;
    border-right: 0;
  }
  .input-right-div2 {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    cursor: pointer;
  }
  ::v-deep .el-input--medium .el-input__inner {
    height: 38px;
    line-height: 38px;
  }
}

.input-number-text-left {
  width: 200px;
}
</style>