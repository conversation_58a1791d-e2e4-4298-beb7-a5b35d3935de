<template>
    <el-row>
        <el-form
            :model="formData"
            :rules="rules"
            label-width="120px"
            ref="from"
            :validate-on-rule-change="false"
        >
            <el-col :span="6">
                <el-form-item label="发票类型:">
                    <el-radio-group v-model="formData.invoice_type" @input="tabsRadio">
                        <el-radio label="VAT_SPECIAL">增值税专用发票</el-radio>
                        <el-radio label="VAT_GENERAL">增值税普通发票</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="单位名称:" prop="company_name">
                    <el-input v-model="formData.company_name" placeholder="请填写单位名称"></el-input>
                </el-form-item>
                <el-form-item label="纳税人识别号:" prop="tax_id">
                    <el-input v-model="formData.tax_id" placeholder="请填写纳税人识别号"></el-input>
                </el-form-item>
                <el-form-item label="注册地址:" prop="registered_addr">
                    <el-input v-model="formData.registered_addr" placeholder="请填写注册地址"></el-input>
                </el-form-item>
                <el-form-item label="注册电话:" prop="registered_tel">
                    <el-input v-model="formData.registered_tel" placeholder="请填写注册电话"></el-input>
                </el-form-item>
                <el-form-item label="开户银行:" prop="bank_name">
                    <el-input v-model="formData.bank_name" placeholder="请填写开户银行"></el-input>
                </el-form-item>
                <el-form-item label="银行账号:" prop="bank_account">
                    <el-input v-model="formData.bank_account" placeholder="请填写银行账号"></el-input>
                </el-form-item>
            </el-col>
            <el-col>
                <el-form-item label="开票说明:">
                    <m-editor ref="mEditor" v-model="formData.invoice_desc" style="height: 500px"></m-editor>
                </el-form-item>
                <el-form-item>
                    <el-button @click="saveSetting" type="primary">保 存</el-button>
                </el-form-item>
            </el-col>
        </el-form>
    </el-row>
</template>

<script>
import { getInvoiceSetting, setInvoiceSetting } from '@/api/finance'; //  此处请自行替换地址
import verify from '@/utils/verify';

export default {
    name: 'supplierSetting',
    data() {
        return {
            formData: {
                invoice_type: 'VAT_SPECIAL',
                company_name: '',
                tax_id: '',
                registered_addr: '',
                registered_tel: '',
                bank_name: '',
                bank_account: '',
                invoice_desc: '',
            },

            rules: {
                company_name: [
                    {
                        required: true,
                        message: '请填写单位名称',
                        trigger: 'blur',
                    },
                ],
                tax_id: [
                    {
                        required: true,
                        message: '请填写纳税人识别号',
                        trigger: 'blur',
                    },
                ],
                registered_addr: [
                    {
                        required: true,
                        message: '请填写注册地址',
                        trigger: 'blur',
                    },
                ],
                registered_tel: [
                    {
                        required: true,
                        message: '请填写注册电话',
                        trigger: 'blur',
                    },
                    {
                        validator: (rule, value, callback) => {
                            if (verify.checkPhone(value)) {
                                callback();
                            } else {
                                callback(new Error('手机号格式不正确'));
                            }
                        },
                        trigger: 'blur',
                    },
                ],
                bank_name: [
                    {
                        required: true,
                        message: '请填写开户银行',
                        trigger: 'blur',
                    },
                ],
                bank_account: [
                    {
                        required: true,
                        message: '请填写银行账号',
                        trigger: 'blur',
                    },
                ],
            },
        };
    },
    mounted() {},
    methods: {
        // 获取设置
        async getSetting() {
            let res = await getInvoiceSetting();
            if (res.code === 0) {
                this.formData = res.data;
                if (this.formData.invoice_type === 'VAT_GENERAL') {
                    this.rules = {
                        company_name: [
                            {
                                required: true,
                                message: '请填写单位名称',
                                trigger: 'blur',
                            },
                        ],
                        tax_id: [
                            {
                                required: true,
                                message: '请填写纳税人识别号',
                                trigger: 'blur',
                            },
                        ],
                    };
                }
            }
        },
        // 保存
        async saveSetting() {
            this.$refs.from.validate(async (valid) => {
                if (valid) {
                    let params = {
                        invoice_type: this.formData.invoice_type,
                        company_name: this.formData.company_name,
                        tax_id: this.formData.tax_id,
                        registered_addr: this.formData.registered_addr,
                        registered_tel: this.formData.registered_tel,
                        bank_name: this.formData.bank_name,
                        bank_account: this.formData.bank_account,
                        invoice_desc: this.formData.invoice_desc,
                    };
                    let res = await setInvoiceSetting(params);
                    if (res.code === 0) {
                        this.$message.success('操作成功');
                    }
                } else {
                    return false
                }
            })
        },
        tabsRadio(e) {
            if (e === 'VAT_GENERAL') {
                this.rules = {
                    company_name: [
                        {
                            required: true,
                            message: '请填写单位名称',
                            trigger: 'blur',
                        },
                    ],
                    tax_id: [
                        {
                            required: true,
                            message: '请填写纳税人识别号',
                            trigger: 'blur',
                        },
                    ],
                };
            } else {
                this.rules = {
                    company_name: [
                        {
                            required: true,
                            message: '请填写单位名称',
                            trigger: 'blur',
                        },
                    ],
                    tax_id: [
                        {
                            required: true,
                            message: '请填写纳税人识别号',
                            trigger: 'blur',
                        },
                    ],
                    registered_addr: [
                        {
                            required: true,
                            message: '请填写注册地址',
                            trigger: 'blur',
                        },
                    ],
                    registered_tel: [
                        {
                            required: true,
                            message: '请填写注册电话',
                            trigger: 'blur',
                        },
                        {
                            validator: (rule, value, callback) => {
                                if (verify.checkPhone(value)) {
                                    callback();
                                } else {
                                    callback(new Error('手机号格式不正确'));
                                }
                            },
                            trigger: 'blur',
                        },
                    ],
                    bank_name: [
                        {
                            required: true,
                            message: '请填写开户银行',
                            trigger: 'blur',
                        },
                    ],
                    bank_account: [
                        {
                            required: true,
                            message: '请填写银行账号',
                            trigger: 'blur',
                        },
                    ],
                };
            }
            this.$refs.from.clearValidate();
        },
    },
};
</script>

<style lang="scss" scoped>
</style>