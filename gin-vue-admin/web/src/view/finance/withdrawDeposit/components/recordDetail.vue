<template>
  <el-drawer
      title="详情"
      :visible="isShow"
      :close-on-press-escape="false"
      :wrapperClosable="false"
      :before-close="handleClose"
      size="calc(100% - 220px)"
      class="detail-ct"
  >
    <m-card :titIsShow="true">
      <p slot="title">提现会员信息</p>
      <el-row>
        <el-col :span="2" class="title-3">
          <el-avatar :src="detail.user.avatar" :size="60">
            <img
                src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png"
            />
          </el-avatar>
        </el-col>
        <el-col :span="22">
          <p class="mb10">会员名称: {{ detail.user.username }}</p>
          <p class="mb10">会员等级: {{ detail.user.UserLevelInfo.name }}</p>
          <p class="mb10">会员姓名: {{ detail.user.nickname || "" }}</p>
          <p class="mb10">会员手机号: {{ detail.user.mobile || detail.user.username }}</p>
          <p class="mb10">供应商: {{ detail.supplier.name || "" }}</p>
          <p class="mb10">供应商手机号: {{ detail.supplier.mobile || "" }}</p>
        </el-col>
      </el-row>
    </m-card>
    <m-card :titIsShow="true" class="mt25">
      <p slot="title">提现信息</p>
      <div class="f">
        <div class="f1">
          <p class="mb10">提现金额: {{ detail.withdrawal_amount | formatF2Y }}元</p>
          <p class="mb10">提现类型: {{ detail.withdrawal_type | formatWithdrawType }}</p>
          <p class="mb10">提现方式: {{ detail.withdrawal_mode | formatWithdrawWay }}</p>
          <!--      <p class="mb10">手动打款方式: 银行卡</p>-->
          <p class="mb10" v-if="detail.bank.bank_type === 1">开户名: {{ detail.bank.account_name }}</p>
          <p class="mb10" v-if="detail.bank.bank_type === 2">公司名: {{ detail.bank.account_name }}</p>
          <!--      <p class="mb10">银行卡开户行: 中国工商银行</p>-->
          <p class="mb10">银行卡开户省市: {{ detail.bank.province }}{{ detail.bank.city }}</p>
          <p class="mb10">所属银行: {{ detail.bank.bank_name }}</p>
          <p class="mb10">开户支行: {{ detail.bank.branch }}</p>
          <p class="mb10">银行卡: {{ detail.bank.bank_account }}</p>
          <p class="mb10">证件号: {{ detail.bank.card_id }}</p>
          <p class="mb10">审核状态: {{ detail.withdrawal_status|formatWithdrawalStatus }}</p>
          <p class="mb10">打款状态: {{ detail.remit_status|formartRemitStatus }}</p>
          <p class="mb10">申请时间: {{ detail.created_at | formatDate }}</p>
          <p class="mb10">身份证正反面:</p>
          <div class="f">
            <div  @click="openDialogImg(detail.bank.id_card_z)">
              <m-image :src="detail.bank.id_card_z" :style="{width:'250px',height:'180px'}"></m-image>
            </div>
            <div @click="openDialogImg(detail.bank.id_card_f)">
                <m-image :src="detail.bank.id_card_f" :style="{width:'250px',height:'180px',marginLeft:'20px'}">
                </m-image>
            </div>
          </div>
          
        </div>
        <div class="f1" v-if="detail.withdrawal_mode === 2">
          <p class="mb10">受理状态: {{ detail.sing_pay.status }}</p>
          <p class="mb10">汇款状态: {{ detail.sing_pay.pay_status }}</p>
          <p class="mb10">提示: {{ detail.sing_pay.pay_info }}</p>
        </div>
        <div class="f1" v-if="detail.withdrawal_mode === 2">
          <p class="mb10">受理状态: {{ detail.sing_pay.status }}</p>
          <p class="mb10">汇款状态: {{ detail.sing_pay.pay_status }}</p>
          <p class="mb10">提示: {{ detail.sing_pay.pay_info }}</p>
        </div>
      </div>
    </m-card>
    <m-card :titIsShow="true" class="mt25">
      <p slot="title">审核</p>
      <!-- <el-table :data="auditTableData" v-if="detail.withdrawal_type === 1">
        <el-table-column width="260" align="center" v-if="detail.withdrawal_status === 0">
          <template slot="header" slot-scope="scope">
            <el-radio-group v-model="allStatus">
              <el-radio :label="3">全选</el-radio>
              <el-radio :label="4">全选</el-radio>
              <el-radio :label="1">全选</el-radio>
            </el-radio-group>
          </template>
          <template slot-scope="scope">
            <el-radio-group v-model="scope.row.status">
              <el-radio :label="3">通过</el-radio>
              <el-radio :label="4">无效</el-radio>
              <el-radio :label="1">驳回</el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column label="审核状态" align="center" v-else>
          <template slot-scope="scope">
            <span>{{ scope.row.supplier_settlement.withdrawal_status |formatWithdrawalStatusList }}</span>
          </template>
        </el-table-column>
        <el-table-column label="结算ID" align="center">
          <template slot-scope="scope">
            {{ scope.row.supplier_settlement.id }}
          </template>
        </el-table-column>
        <el-table-column label="订单号" align="center">
          <template slot-scope="scope">
            {{ scope.row.supplier_settlement.order_sn }}
          </template>
        </el-table-column>
        <el-table-column label="结算类型" align="center">
          <template slot-scope="scope">
            <span>结算余额</span>
          </template>
        </el-table-column>
        <el-table-column label="结算金额" align="center">
          <template slot-scope="scope">
            {{ scope.row.supplier_settlement.settlement_amount | formatF2Y }}
          </template>
        </el-table-column>
        <el-table-column label="结算状态" align="center">
          <template slot-scope="scope">
            {{ scope.row.supplier_settlement.status === 1 ? '已结算' : '未结算' }}
          </template>
        </el-table-column>
        <el-table-column label="打款状态" align="center">
          <template slot-scope="scope">
            {{ detail.remit_status|formartRemitStatus }}
          </template>
        </el-table-column>
        <el-table-column label="下单时间" align="center">
          <template slot-scope="scope">
            {{ scope.row.supplier_settlement.order.created_at | formatDate }}
          </template>
        </el-table-column>
      </el-table> -->
      <el-table :data="billDetailTableData" v-if="detail.withdrawal_type === 1">
        <el-table-column width="260" align="center" v-if="detail.withdrawal_status === 0">
          <template slot="header" slot-scope="scope">
            <el-radio-group v-model="allStatus">
              <el-radio :label="3">全选</el-radio>
              <el-radio :label="4">全选</el-radio>
              <el-radio :label="1">全选</el-radio>
            </el-radio-group>
          </template>
          <template slot-scope="scope">
            <el-radio-group v-model="scope.row.status">
              <el-radio :label="3">通过</el-radio>
              <el-radio :label="4">无效</el-radio>
              <el-radio :label="1">驳回</el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column label="审核状态" align="center" v-else>
          <template slot-scope="scope">
            <span>{{ scope.row.withdrawal_status | formatWithdrawalStatusList }}</span>
          </template>
        </el-table-column>
        <el-table-column label="时间" align="center">
            <template slot-scope="scope">{{ scope.row.created_at | formatDate }}</template>
        </el-table-column>
        <el-table-column align="center">
            <template slot="header">
                <p>订单号</p>
                <p>订单金额（元）</p>
            </template>
            <template slot-scope="scope">
                <p>{{ scope.row.order_sn }}</p>
                <p>{{ scope.row.order.amount | formatF2Y }}</p>
            </template>
        </el-table-column>
        <el-table-column align="center">
            <template slot="header">
                <p>成本价（元）</p>
                <p>运费（元）</p>
            </template>
            <template slot-scope="scope">
                <p>{{ scope.row.order.cost_amount | formatF2Y }}</p>
                <p>{{ scope.row.freight_price | formatF2Y }}</p>
            </template>
        </el-table-column>
        <el-table-column align="center">
            <template slot="header">
                <p>结算类型</p>
                <p>订单结算金额（元）</p>
            </template>
            <template slot-scope="scope">
                <p>{{ scope.row.settlement_type | settlementType }}</p>
                <p>{{ scope.row.settlement_amount | formatF2Y }}</p>
            </template>
        </el-table-column>
        <el-table-column align="center">
            <template slot="header">
                <p>平台扣点比例</p>
                <p>技术服务费金额（元）</p>
            </template>
            <template slot-scope="scope">
                <p>{{ scope.row.deduction_ratio / 100 }}</p>
                <p>{{ scope.row.technical_service_cost | formatF2Y }}</p>
            </template>
        </el-table-column>
          <el-table-column label="供应商账单金额（元）" align="center">
              <template slot-scope="scope">
                  <p>{{ scope.row.supply_amount | formatF2Y }}</p>
              </template>
          </el-table-column>
          <el-table-column align="center">
              <template slot="header">
                  <p>提现手续费（元）</p>
              </template>
              <template slot-scope="scope">
                  <p>{{ scope.row.withdrawal_fee | formatF2Y }}</p>
              </template>
          </el-table-column>
          <el-table-column align="center">
              <template slot="header">
                  <p>打款状态</p>
                  <p>打款金额（元）</p>
              </template>
              <template slot-scope="scope">
                  <p>{{ scope.row.withdrawal_status === 3 || detail.withdrawal_status === 0  ? scope.row.remit_status : 3 | formartRemitStatus }}</p>
                  <p>{{ scope.row.remit_status === 1 && scope.row.withdrawal_status === 3 ? scope.row.settlement_amount : 0 | formatF2Y }}</p>
              </template>
          </el-table-column>
      </el-table>
      <el-table :data="auditTableData" v-if="detail.withdrawal_type === 2">
        <el-table-column width="260" align="center" v-if="detail.withdrawal_status === 0">
          <template slot="header" slot-scope="scope">
            <el-radio-group v-model="allStatus">
              <el-radio :label="3">全选</el-radio>
              <el-radio :label="4">全选</el-radio>
              <el-radio :label="1">全选</el-radio>
            </el-radio-group>
          </template>
          <template slot-scope="scope">
            <el-radio-group v-model="scope.row.status">
              <el-radio :label="3">通过</el-radio>
              <el-radio :label="4">无效</el-radio>
              <el-radio :label="1">驳回</el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column label="审核状态" align="center" v-else>
          <template slot-scope="scope">
            <span>{{ scope.row.withdrawal_status |formatWithdrawalStatusList }}</span>
          </template>
        </el-table-column>
        <el-table-column label="时间" align="center">
          <template slot-scope="scope">
            {{ scope.row.created_at |formatDate }}
          </template>
        </el-table-column>
        <el-table-column label="收入类型" align="center">
          <template slot-scope="scope">
            {{ scope.row.income_name }}
          </template>
        </el-table-column>
        <el-table-column label="金额" align="center">
          <template slot-scope="scope">
            {{ scope.row.amount | formatF2Y }}
          </template>
        </el-table-column>
        <el-table-column label="订单编号" align="center">
          <template slot-scope="scope">
            {{ scope.row.order_sn }}
          </template>
        </el-table-column>
        <el-table-column label="会员id" align="center">
          <template slot-scope="scope">
            {{ scope.row.user_id }}
          </template>
        </el-table-column>
      </el-table>
      <div class="mt25" v-if="detail.withdrawal_status === 0">
        审核金额: <span class="color-red">{{ auditAmount | formatF2Y }}</span>元 
        手续费: <span class="color-red">{{ detail.poundage_amount | formatF2Y }}</span> 
        劳务税：<span class="color-red">{{ detail.service_tax | formatF2Y }}</span>
        应打款: <span class="color-red">{{ auditAmount > 0 ? auditAmount - detail.poundage_amount - detail.service_tax : 0 | formatF2Y }}</span>
      </div>
      <div class="mt25" v-if="detail.withdrawal_status === 1">
        实际打款金额: <span class="color-red">{{ detail.income_amount |formatF2Y }}</span> 手续费: <span
          class="color-red">{{ detail.poundage_amount|formatF2Y }}</span>
        劳务税：<span class="color-red">{{ detail.service_tax|formatF2Y }}</span>
      </div>
      <div class="mt25">
        <el-button type="primary" v-if="detail.withdrawal_status === 0" @click="submitAudit">提交审核</el-button>
        <div v-else>开票状态：{{ detail.invoice_status | invoiceStatus }} 
          <el-button type="text" v-if='detail.invoice_status == 2' @click="getInvoice">查看发票</el-button>
        </div>
        <!--       <el-button type="primary" v-else>重新审核</el-button>-->
      </div>
    </m-card>
    <m-card :titIsShow="true" class="mt25" v-if="tableData.length>0">
      <p slot="title">操作</p>
      <div> 
        <el-button v-if="detail.withdrawal_type === 1 && detail.invoice_status === 1" type="primary" @click="affirmInvoice">确认开票</el-button> 
        <el-button v-else-if="detail.withdrawal_status === 1 && detail.remit_status === 0" type="primary"
                   @click="confirmRemit">确认打款
        </el-button>
        <el-button v-if="detail.withdrawal_status === 1 && detail.remit_status === 0 && detail.withdrawal_mode === 2"
                   type="primary"
                   @click="takePayment">汇聚支付代付打款
        </el-button>
        <!--        <el-button @click="handleClose">返回列表</el-button>-->
        <el-table v-if="tableData.length>0" :data="tableData" class="mt25">
          <el-table-column label="操作时间" align="center">
            <template slot-scope="scope">
              {{ scope.row.created_at | formatDate }}
            </template>
          </el-table-column>
          <el-table-column label="操作账号" prop="operator_name" align="center"></el-table-column>
          <el-table-column label="操作流程" prop="operator_type" align="center"></el-table-column>
          <el-table-column label="操作IP" prop="operator_ip" align="center"></el-table-column>
          <el-table-column align="center">
            <template slot="header">
              <p>手续费</p>
              <p>劳务税</p>
            </template>
            <template slot-scope="scope">
              <p>{{ detail.poundage_amount | formatF2Y }}</p>
              <p>{{ detail.service_tax | formatF2Y }}</p>
            </template>
          </el-table-column>
          <el-table-column align="center">
            <template slot="header">
              <p>驳回金额</p>
              <p>无效金额</p>
            </template>
            <template slot-scope="scope">
              <p>{{ scope.row.rejected_amount | formatF2Y }}</p>
              <p>{{ scope.row.invalid_amount | formatF2Y }}</p>
            </template>
          </el-table-column>
          <el-table-column label="实际打款金额" align="center">
            <template slot-scope="scope">
              {{ detail.income_amount | formatF2Y }}
            </template>
          </el-table-column>
          <el-table-column label="备注" prop="remarks" align="center"></el-table-column>
        </el-table>
      </div>
    </m-card>
    <!--    <audit-dialog ref="auditDialog" @reload="getDetail"></audit-dialog>-->
    <el-dialog :visible="dialogVisible" @close="handleDialogImgClose" :modal-append-to-body="false"
               :append-to-body="true">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <addInvoice ref="addInvoice"></addInvoice>
  </el-drawer>
</template>
<script>
import {withdrawDetail, remitStatus, singlePay, withdrawStatus,billDetail,invoiceStatus} from "@/api/finance"
import { getInvoice } from "@/api/adminAccountApply";
import AuditDialog from "./auditDialog"
import addInvoice from '../../../supplier/admin/financeManage/components/addInvoice.vue'

export default {
  components: { addInvoice },
  data() {
    return {
      isShow: false,
      detail: {
        user: {UserLevelInfo: {}},
        bank: {},
        sing_pay: {},
        supplier: {},
        withdrawal_operation_record: {},
        withdrawal_detail: []
      },
      tableData: [],
      auditTableData: [],
      allStatus: undefined,
      // 查看身份证
      dialogVisible: false,
      dialogImageUrl: '',

      billDetailTableData: [] // 供应商审核表
    };
  },
  watch: {
    allStatus: function (val) {
      this.auditTableData = this.auditTableData.map((item) => ({
        ...item,
        status: val
      }))
      this.billDetailTableData = this.billDetailTableData.map((item) => ({
        ...item,
        status: val
      }))
    }
  },
  computed: {
    // 审核金额
    auditAmount: function () {
      let amount = 0
      if (this.auditTableData.length > 0) {
        this.auditTableData.forEach(item => {
          if (item.status === 3) {
            amount += item.supplier_settlement ? item.supplier_settlement.settlement_amount : item.amount
          }
        })
      }
      return amount
    }
  },
  filters: {
    // 审核列表 审核状态
    formatWithdrawalStatusList: function (value) {
      let s = "";
      switch (value) {
        case 1:
          s = "驳回"
          break;
        case 3:
          s = "通过"
          break;
        case 4:
          s = "无效"
          break;
      }
      return s;
    },
    // 打款状态
    formartRemitStatus: function (value) {
      let text = "";
      switch (value) {
        case  0:
          text = "待打款"
          break;
        case  1:
          text = "已打款"
          break;
        case  2:
          text = "打款中"
          break;
        default:
          text = "无需打款"
          break;
      }
      return text
    },
    // 提现类型
    formatWithdrawType: function (type) {
      let text = ""
      switch (type) {
        case 1:
          text = "结算余额"
          break;
        case 2:
          text = "收入余额"
          break;
        default:
          text = "-"
          break;
      }
      return text
    },
    // 提现方式
    formatWithdrawWay: function (value) {
      let text = "";
      switch (value) {
        case 1:
          text = "手动提现"
          break;
        case 2:
          text = "汇聚提现"
          break;
        default:
          text = "-"
          break;
      }
      return text;
    },
    // 提现状态
    formatWithdrawalStatus: function (status) {
      let text = "";
      switch (status) {
        case  0:
          text = "待审核"
          break;
        case  1:
          text = "通过"
          break;
        case  2:
          text = "无效"
          break;
        case  3:
          text = "驳回"
          break;
        default:
          text = "-"
          break;
      }
      return text
    },
    // 结算状态
    settlementType(type) {
        let str = ''
        switch (type) {
            case 0:
                str = '订单金额'
                break;
            case 1:
                str = '成本价 + 运费'
                break;
            case 2:
                str = '订单金额'
                break;
            default:
                break;
        }
        return str
    },
    // 开票状态
    invoiceStatus(type) {
        let str = ''
        switch (type) {
            case 0:
                str = '无需开票'
                break;
            case 1:
                str = '待开票'
                break;
            case 2:
                str = '已开票'
                break;
            default:
                break;
        }
        return str
    }
  },
  methods: {
    // 查看发票
    async getInvoice() {
      this.$refs.addInvoice.info({title:'查看发票',id: this.detail.id, income_amount: this.detail.income_amount,type: 1 });
    },
    // 确认开票
    async affirmInvoice() {
      this.$confirm('是否确认开票?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
      }).then(async () => {
        let params = {
          id: this.detail.id
        }
        let res = await invoiceStatus(params);
        if (res.code === 0) {
          this.$message.success(res.msg)
          this.getDetail(this.detail.id)
        }
      })
    },
    openDialogImg(url) {
      console.log(url, '???');
      if (url) {
        this.dialogVisible = true
        this.dialogImageUrl = url
      }
    },
    handleDialogImgClose() {
      this.dialogVisible = false
      this.dialogImageUrl = ""
    },
    // 提交审核
    submitAudit() {
// list_status:[{"id":1,"status":1},{"id":2,"status":2},{"id":3,"status":3}]
      let remarks = ""
      this.$prompt("请输入备注", "备注", {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: "textarea",
        inputPlaceholder: "请输入备注",
        /*inputValidator: (value) => {
          return value ? true : false
        },
        inputErrorMessage: "请输入备注"*/
      }).then(({value}) => {
        remarks = value
        let invalid_amount = 0
        let rejected_amount = 0
        let list_status = []
        if (this.detail.withdrawal_type === 1) {
          // 供应商金额计算 无效金额,  驳回金额
          this.billDetailTableData.forEach(item => {
            if (item.status === 4) {
              invalid_amount += item.settlement_amount
            }
            if (item.status === 1) {
              rejected_amount += item.settlement_amount
            }
            list_status.push({
              id: item.supplier_settlement ? item.supplier_settlement.id : item.id,
              status: item.status
            })
          })
        } else {
            // 计算无效金额,  驳回金额
            this.auditTableData.forEach((item) => {
              if (item.status === 4) {
                invalid_amount += item.supplier_settlement ? item.supplier_settlement.settlement_amount : item.amount
              }
              if (item.status === 1) {
                rejected_amount += item.supplier_settlement ? item.supplier_settlement.settlement_amount : item.amount
              }
              list_status.push({
                id: item.supplier_settlement ? item.supplier_settlement.id : item.id,
                status: item.status
              })
            })
        }
        let params = {
          id: this.detail.id,
          withdrawal_status: 1,
          invalid_amount: invalid_amount, // 无效金额
          rejected_amount: rejected_amount, // 驳回金额
          list_status: list_status,
          remarks
        }
        withdrawStatus(params).then(res => {
          if (res.code === 0) {
            this.$message.success(res.msg)
            this.getDetail(this.detail.id)
          } else {
            this.$message.error(res.msg)
          }
        })
      })
    },
    jumpSettlement() {
      this.$_blank("/layout/Finance/settlement", {
        withdrawal_id: this.detail.id
      })
    },
    // 汇聚代付
    takePayment() {
      let remarks = ""
      this.$prompt("请输入备注", "备注", {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: "textarea",
        inputPlaceholder: "请输入备注",
      }).then(({value}) => {
        remarks = value
        singlePay({id: this.detail.id, remarks}).then(res => {
          if (res.code === 0) {
            this.$message.success(res.msg)
            this.getDetail(this.detail.id)
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      });
    },
    // 确认打款
    confirmRemit() {
      let remarks = ""
      this.$prompt("请输入备注", "备注", {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: "textarea",
        inputPlaceholder: "请输入备注",
      }).then(({value}) => {
        remarks = value
        remitStatus({id: this.detail.id, remit_status: 1, remarks}).then(res => {
          if (res.code === 0) {
            this.$message.success(res.msg)
            this.getDetail(this.detail.id)
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      });
    },
    /*openAuditDialog() {
      this.$refs.auditDialog.isShow = true
      this.$nextTick(() => {
        this.$refs.auditDialog.withdrawal_amount = this.detail.withdrawal_amount
        this.$refs.auditDialog.income_amount = this.detail.income_amount
        this.$refs.auditDialog.poundage_amount = this.detail.poundage_amount
        this.$refs.auditDialog.service_tax = this.detail.service_tax
        this.$refs.auditDialog.formData.id = this.detail.id
      })
    },*/
    handleClose() {
      this.isShow = false;
      this.detail = {user: {UserLevelInfo: {}}, bank: {}, supplier: {}, withdrawal_operation_record: {}}
      this.tableData = []
    },
    getDetail(id) {
      withdrawDetail({id: id}).then(res => {
        if (res.code === 0) {
          this.setFrom(res.data)
          this.tableData = []
          this.tableData = this.detail.withdrawal_operation_record
          switch (this.detail.withdrawal_type) {
            case 1:
              this.auditTableData = this.detail.withdrawal_detail.map((item) => ({
                status: 3,
                ...item
              }))
              
              billDetail({id}).then(res1 => {
                this.billDetailTableData = res1.data.map((item) => ({
                  status: 3,
                  ...item,
                }))
              })
              
              
              break;
            case 2:
              this.auditTableData = this.detail.user_income_details.map((item) => ({
                status: 3,
                ...item
              }))
              break;
          }

        } else
          this.$message.error(res.msg)
      })
    },
    //赋值表单
    setFrom(val) {
      const keys = Object.keys(val);
      const that = this;
      keys.forEach((element) => {
        that.detail[element] = val[element];
      });
      console.log(that.detail);
    },
  },
};
</script>
<style lang="scss" scoped>
.mb10 {
  margin-bottom: 10px;
}
</style>