<template>
    <el-row>
        <el-form :model="formData" label-width="120px">
            <el-col>
                <el-form-item label="余额提现:">
                    <el-radio-group v-model="formData.balance_withdrawal">
                        <el-radio :label="1">开启</el-radio>
                        <el-radio :label="2">关闭</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="汇聚代付:">
                    <el-radio-group v-model="formData.convergence_payed">
                        <el-radio :label="1">开启</el-radio>
                        <el-radio :label="2">关闭</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="手动提现:">
                    <el-radio-group v-model="formData.manual_withdrawal">
                        <el-radio :label="1">开启</el-radio>
                        <el-radio :label="2">关闭</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item>
                    <el-radio-group v-model="formData.withdrawal_type">
                        <el-radio :label="1">银行卡</el-radio>
                        <el-radio :label="2">支付宝</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="提现手续费:">
                    <el-input-number
                        :controls="false"
                        :precision="2"
                        class="input-number-text-left"
                        v-model="formData.withdrawal_charge"
                    ></el-input-number>%
                </el-form-item>
                <el-form-item label="是否需要开票:">
                    <el-radio-group v-model="formData.is_invoice">
                        <el-radio :label="1">开启</el-radio>
                        <el-radio :label="0">关闭</el-radio>
                    </el-radio-group>
                    <p class="hint-p">如果选择时，提现审核通过后，供货商需要先开票上传，才能进入打款环节</p>
                </el-form-item>
                <el-form-item>
                    <el-button @click="saveSetting" type="primary">保 存</el-button>
                </el-form-item>
            </el-col>
        </el-form>
    </el-row>
</template>

<script>
import {
    getSupplierWithdrawal,
    supplierWithdrawalSetting,
} from '@/api/finance'; //  此处请自行替换地址

export default {
    name: 'supplierSetting',
    data() {
        return {
            formData: {
                balance_withdrawal: 1,
                convergence_payed: 1,
                manual_withdrawal: 1,
                withdrawal_type: 1,
                withdrawal_charge: 0,
                is_invoice: 0,
            },
        };
    },
    mounted() {},
    methods: {
        // 获取设置
        async getSetting() {
            let res = await getSupplierWithdrawal();
            if (res.code === 0) {
                this.formData = res.data;
                this.formData.withdrawal_charge =
                    this.formData.withdrawal_charge / 100;
            }
        },
        async saveSetting() {
            let that = {
                balance_withdrawal: this.formData.balance_withdrawal,
                convergence_payed: this.formData.convergence_payed,
                manual_withdrawal: this.formData.manual_withdrawal,
                withdrawal_type: this.formData.withdrawal_type,
                withdrawal_charge: this.formData.withdrawal_charge * 100,
                is_invoice: this.formData.is_invoice
            };
            let res = await supplierWithdrawalSetting(that);
            if (res.code === 0) {
                this.$message.success('操作成功');
            }
        },
    }, 
};
</script>

<style lang="scss" scoped>
p.hint-p {
    font-size: 12px;
    color: #c0c4cc;
    line-height: 20px;
}

.input-number-text-left {
  width: 200px;
}
</style>
