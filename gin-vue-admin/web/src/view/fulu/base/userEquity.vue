<template>
  <m-card>
    <el-form v-model="formData" label-width="130px" class="system">
      <p class="title-p">基础信息</p>
      <el-divider></el-divider>
      <el-row>
        <el-col :span="15">
          <el-form-item label="appKey:">
            <el-input v-model="formData.baseInfo.appKey"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="15">
          <el-form-item label="appSecret:">
            <el-input v-model="formData.baseInfo.appSecret"></el-input>
          </el-form-item>
        </el-col>
        <el-col
            :span="15"
        >
          <el-form-item label="host:">
            <el-input v-model="formData.baseInfo.host"></el-input>
            <div style="font-size: 12px; color: #dc5d5d">
              * {{ domain }}/supplyapi
            </div>
          </el-form-item>
        </el-col>
<!--        <el-col :span="15">
          <el-form-item label="接口地址:">
            <el-input v-model="formData.baseInfo.apiUrl"></el-input>
            <div style="font-size: 12px; color: #dc5d5d">
              * 填写对方接口api域名地址,没有可忽略
            </div>
          </el-form-item>
        </el-col>-->
        <el-col :span="15">
          <el-form-item label="回调地址:">
            <div v-if="rescode == 0">{{ callBackUrl }}</div>
            <div v-else>保存配置后自动生成</div>
            <div style="font-size: 12px; color: #dc5d5d">
              * 上面回调地址复制到对接供应链平台的后台回调地址中
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <p class="title-p">更新设置</p>
      <el-divider></el-divider>
      <el-form-item label="自动更新商品名称:">
        <el-radio-group v-model="formData.update_info.auto_product_name">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="自动更新商品价格:">
        <el-radio-group v-model="formData.update_info.auto_purchase_price">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="自动更新商品详情:">
        <el-radio-group v-model="formData.update_info.auto_details">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="自动更新商品库存:">
        <el-radio-group v-model="formData.update_info.auto_stock_status">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <p class="title-p">定价策略</p>
      <el-divider></el-divider>
      <el-form-item label="成本价:">
        <div class="f fac mt10">
          <el-radio
              v-model="formData.pricing_strategy.cost_price.compute_type"
              :label="1"
          >供货价 X 定价系数
          </el-radio
          >
          <el-input-number
              :controls="false"
              :precision="2"
              class="input-number-text-left"
              :min="0"
              v-model="formData.pricing_strategy.cost_price.price_ratio"
          ></el-input-number>
          <p class="ml10">%</p>
        </div>
        <div class="f fac mt10">
          <el-radio
              v-model="formData.pricing_strategy.cost_price.compute_type"
              :label="2"
          >原价 X 定价系数
          </el-radio
          >
          <el-input-number
              :controls="false"
              :precision="2"
              class="input-number-text-left"
              :min="0"
              v-model="formData.pricing_strategy.cost_price.face_ratio"
          ></el-input-number>
          <p class="ml10">%</p>
        </div>
        <p class="hint-p">
          举例: 福禄供货价80元，原价100元； <br/>
          导入后商品成本价=福禄供货价 X 定价系数 即 80 X 100% = 80 元
          导入后商品成本价=福禄原价 X 定价系数 即 100 X 100% = 100 元
        </p>
      </el-form-item>
      <el-form-item label="供货价:">
        <div class="f fac mt10">
          <el-radio
              v-model="formData.pricing_strategy.price.compute_type"
              :label="1"
          >供货价 X 定价系数
          </el-radio
          >
          <el-input-number
              :controls="false"
              :precision="2"
              class="input-number-text-left"
              :min="0"
              v-model="formData.pricing_strategy.price.price_ratio"
          ></el-input-number>
          <p class="ml10">%</p>
        </div>
        <div class="f fac mt10">
          <el-radio
              v-model="formData.pricing_strategy.price.compute_type"
              :label="2"
          >原价 X 定价系数
          </el-radio
          >
          <el-input-number
              :controls="false"
              :precision="2"
              class="input-number-text-left"
              :min="0"
              v-model="formData.pricing_strategy.price.face_ratio"
          ></el-input-number>
          <p class="ml10">%</p>
        </div>
        <p class="hint-p">商品导入后，中台商品自动设置的商品供货价！</p>
      </el-form-item>
      <el-form-item label="市场价:">
        <div class="f fac mt10">
          <el-radio
              v-model="formData.pricing_strategy.origin_price.compute_type"
              :label="1"
          >供货价 X 定价系数
          </el-radio
          >
          <el-input-number
              :controls="false"
              :precision="2"
              class="input-number-text-left"
              :min="0"
              v-model="formData.pricing_strategy.origin_price.price_ratio"
          ></el-input-number>
          <p class="ml10">%</p>
        </div>
        <div class="f fac mt10">
          <el-radio
              v-model="formData.pricing_strategy.origin_price.compute_type"
              :label="2"
          >原价 X 定价系数
          </el-radio
          >
          <el-input-number
              :controls="false"
              :precision="2"
              class="input-number-text-left"
              :min="0"
              v-model="formData.pricing_strategy.origin_price.face_ratio"
          ></el-input-number>
          <p class="ml10">%</p>
        </div>
        <p class="hint-p">商品导入后，中台商品自动设置的商品市场价！</p>
      </el-form-item>
      <el-form-item label="指导价:">
        <div class="f fac mt10">
          <el-radio
              v-model="formData.pricing_strategy.guide_price.compute_type"
              :label="1"
          >供货价 X 定价系数
          </el-radio
          >
          <el-input-number
              :controls="false"
              :precision="2"
              class="input-number-text-left"
              :min="0"
              v-model="formData.pricing_strategy.guide_price.price_ratio"
          ></el-input-number>
          <p class="ml10">%</p>
        </div>
        <div class="f fac mt10">
          <el-radio
              v-model="formData.pricing_strategy.guide_price.compute_type"
              :label="2"
          >原价 X 定价系数
          </el-radio
          >
          <el-input-number
              :controls="false"
              :precision="2"
              class="input-number-text-left"
              :min="0"
              v-model="formData.pricing_strategy.guide_price.face_ratio"
          ></el-input-number>
          <p class="ml10">%</p>
        </div>
        <p class="hint-p">商品导入后，中台商品自动设置的商品指导价！</p>
      </el-form-item>
      <el-form-item label="营销价:">
        <div class="f fac mt10">
          <el-radio
              v-model="formData.pricing_strategy.activity_price.compute_type"
              :label="1"
          >供货价 X 定价系数
          </el-radio
          >
          <el-input-number
              :controls="false"
              :precision="2"
              class="input-number-text-left"
              :min="0"
              v-model="formData.pricing_strategy.activity_price.price_ratio"
          ></el-input-number>
          <p class="ml10">%</p>
        </div>
        <div class="f fac mt10">
          <el-radio
              v-model="formData.pricing_strategy.activity_price.compute_type"
              :label="2"
          >原价 X 定价系数
          </el-radio
          >
          <el-input-number
              :controls="false"
              :precision="2"
              class="input-number-text-left"
              :min="0"
              v-model="formData.pricing_strategy.activity_price.face_ratio"
          ></el-input-number>
          <p class="ml10">%</p>
        </div>
        <p class="hint-p">商品导入后，中台商品自动设置的商品营销价！</p>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="save">提交</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-col>
        <div class="f fac fjsb">
          <p class="title-p">采购端API同步指定支付</p>
          <el-button type="primary" @click="savePay">保 存</el-button>
        </div>
        <el-divider></el-divider>
      </el-col>
      <el-col :span="12">
        <el-table :data="payFormData">
          <el-table-column label="支付方式" align="center" prop="account_name"></el-table-column>
          <el-table-column label="是否支持" align="center">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.enable" :true-label="1" :false-label="0"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="优先级" align="center">
            <template slot-scope="scope">
              <el-input v-model.number="scope.row.sort_level"></el-input>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
  </m-card>
</template>

<script>
import {getSupplySetting, setSupplySetting, getEquitySupplyID} from "@/api/fulu"
import {createSupplyAccount, getAccount} from "@/api/purchase";
import {confirm} from "@/decorators/decorators";

export default {
  name: "userEquity",
  data() {
    return {
      url: "", // 回调地址
      id: 0,
      domain: "",
      rescode: 9,
      callBackUrl: "",
      formData: {
        baseInfo: {},
        update_info: {
          // brand: 0, // 自动更新品牌
          // auto_product: 0, // 自动更新商品
          auto_product_name: 0, // 自动更新商品名称
          auto_purchase_price: 0, // 自动更新商品价格
          auto_details: 0, // 自动更新商品详情
          auto_stock_status: 0, // 自动更新商品库存
        },
        pricing_strategy: {
          // 成本价
          cost_price: {
            compute_type: null, // 1:零售价2:原价
            price_ratio: null, // 零售价定价系数
            face_ratio: null, // 原价定价系数
          },
          // 供货价
          price: {
            compute_type: null, // 1:零售价2:原价
            price_ratio: null, // 零售价定价系数
            face_ratio: null, // 原价定价系数
          },
          // 市场价
          origin_price: {
            compute_type: null, // 1:零售价2:原价
            price_ratio: null, // 零售价定价系数
            face_ratio: null, // 原价定价系数
          },
          // 指导价
          guide_price: {
            compute_type: null, // 1:零售价2:原价
            price_ratio: null, // 零售价定价系数
            face_ratio: null, // 原价定价系数
          },
          // 营销价
          activity_price: {
            compute_type: null, // 1:零售价2:原价
            price_ratio: null, // 零售价定价系数
            face_ratio: null, // 原价定价系数
          },
        }
      },
      payFormData: [],
      gather_supply_id: null
    }
  },
  methods: {
    async init() {
      const {code, data} = await getSupplySetting()
      if (code === 0) {
        this.formData = JSON.parse(data.data.value);
        this.rescode = code;
        let proto = data.proto ? data.proto : "https";

        this.domain = proto + "://" + data.domain;
        this.callBackUrl =
            proto +
            "://" +
            data.domain +
            "/supplyapi/api/gatherSupply/notificationCallBack";
      }
      this.getGid()
    },
    async getGid() {
      const {code, data} = await getEquitySupplyID()
      if (code === 0) {
        this.gather_supply_id = data.supply_id
        this.getAccountSetting()
      }
    },
    async getAccountSetting() {
      const {code, data} = await getAccount({gather_supply_id: this.gather_supply_id})
      if (code === 0) {
        this.payFormData = data
      }
    },
    async save() {
      let params = {
        value: JSON.stringify(this.formData),
      }
      if (this.id) {
        params.id = this.id
      }
      const {code, msg} = await setSupplySetting(params)
      if (code === 0) {
        this.$message.success(msg)
      }
    },
    @confirm("提示", "是否确认保存?")
    async savePay() {
      this.payFormData = this.payFormData.map(item => ({
        ...item,
        gather_supply_id: this.gather_supply_id
      }))
      const {code, msg} = await createSupplyAccount(this.payFormData)
      if (code === 0) {
        this.$message.success(msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
p.title-p {
  font-size: 16px;
  font-weight: bold;
}

.el-divider {
  margin: 15px 0;
}

::v-deep .el-input-number {
  .el-input__inner {
    width: 100%;
  }
}

p.hint-p {
  margin-top: 10px;
  font-size: 12px;
  color: #c0c4cc;
  line-height: 20px;
}
</style>