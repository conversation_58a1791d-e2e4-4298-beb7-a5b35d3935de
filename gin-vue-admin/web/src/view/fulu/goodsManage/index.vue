<template>
    <m-card>
        <el-button type="primary" @click="openSync">同步商品</el-button>
        <el-form
            :model="searchInfo"
            ref="form"
            class="search-term mt25"
            label-width="80px"
            inline
        >
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.keyword"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">商品标题</span>
                </el-input>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input">
                    <div class="line-box">
                        <span>供应渠道</span>
                    </div>
                    <el-select
                        v-model="searchInfo.sync_product_type"
                        class="w100"
                        clearable
                    >
                        <el-option :value="1" label="福禄"></el-option>
                        <el-option :value="2" label="会员权益"></el-option>
                        <el-option :value="126" label="权益商品"></el-option>
                        <el-option :value="127" label="千禧平台"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input">
                    <div class="line-box">
                        <span>利润率</span>
                    </div>
                    <div v-if="optios === 5" class="f line-input">
                        <m-num-input
                            v-model="optiosCustom.p1"
                            style="width: 25%"
                            :min="0"
                        ></m-num-input>
                        <div class="line">-</div>
                        <m-num-input
                            v-model="optiosCustom.p2"
                            style="width: 46%"
                            :min="0"
                            endText="%"
                        ></m-num-input>
                    </div>
                    <el-select v-model="optios" v-else class="w100" clearable>
                        <template v-for="item in profitRateOptios">
                            <el-option
                                :value="item.value"
                                :label="item.label"
                            ></el-option>
                        </template>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input">
                    <div class="line-box">
                        <span>状态</span>
                    </div>
                    <el-select
                        v-model="searchInfo.is_display"
                        class="w100"
                        clearable
                    >
                        <el-option :value="1" label="全部"></el-option>
                        <el-option :value="2" label="上架中"></el-option>
                        <el-option :value="3" label="已下架"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input">
                    <div class="line-box">
                        <span>库存</span>
                    </div>
                    <el-select
                        v-model="searchInfo.stock_status"
                        class="w100"
                        clearable
                    >
                        <el-option :value="1" label="全部"></el-option>
                        <el-option :value="2" label="有库存"></el-option>
                        <el-option :value="3" label="无库存"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input">
                    <div class="line-box">
                        <span>类型</span>
                    </div>
                    <el-select
                        v-model="searchInfo.product_type"
                        class="w100"
                        clearable
                    >
                        <el-option :value="1" label="全部"></el-option>
                        <el-option :value="2" label="直充"></el-option>
                        <el-option :value="3" label="卡密"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item prop="category1_id">
              <div class="line-input">
                    <div class="line-box">
                        <span>一级分类</span>
                    </div>
                    <el-select
                        v-model="searchInfo.category1_id"
                        placeholder="请选择一级分类"
                        filterable
                        clearable
                        @change="
                            handleClassiyfChange(2, searchInfo.category1_id)
                        "
                    >
                        <el-option
                            v-for="item in categoryList1"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </div>
                </el-form-item>
                <el-form-item prop="category2_id">
                  <div class="line-input">
                    <div class="line-box">
                        <span>二级分类</span>
                    </div>
                    <el-select
                        v-model="searchInfo.category2_id"
                        placeholder="请选择二级分类"
                        filterable
                        clearable
                        @change="
                            handleClassiyfChange(3, searchInfo.category2_id)
                        "
                    >
                        <el-option
                            v-for="item in categoryList2"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </div> 
                </el-form-item>
                <el-form-item prop="category3_id">
                  <div class="line-input">
                    <div class="line-box">
                        <span>三级分类</span>
                    </div>
                    <el-select
                        v-model="searchInfo.category3_id"
                        placeholder="请选择三级分类"
                        filterable
                        clearable
                    >
                        <el-option
                            v-for="item in categoryList3"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </div> 
                </el-form-item>
            <!-- <el-form-item label="状态:">
        <el-radio-group v-model="searchInfo.is_display">
          <el-radio :label="1">全部</el-radio>
          <el-radio :label="2">上架中</el-radio>
          <el-radio :label="3">已下架</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="库存:">
        <el-radio-group v-model="searchInfo.stock_status">
          <el-radio :label="1">全部</el-radio>
          <el-radio :label="2">有库存</el-radio>
          <el-radio :label="3">无库存</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="类型:">
        <el-radio-group v-model="searchInfo.product_type">
          <el-radio :label="1">全部</el-radio>
          <el-radio :label="2">直充</el-radio>
          <el-radio :label="3">卡券</el-radio>
        </el-radio-group>
      </el-form-item> -->
            <el-form-item>
                <el-button type="primary" @click="search">搜索</el-button>
                <el-button @click="exportExcel">导出</el-button>
                <el-button type="text" @click="resetSearch"
                    >重置搜索条件</el-button
                >
            </el-form-item>
        </el-form>
        <div class="mt25">
            <el-button @click="batchOperation(1)">批量上架</el-button>
            <el-button @click="batchOperation(0)">批量下架</el-button>
            <el-button @click="deleteAllProduct">批量删除</el-button>
            <el-button @click="batchCategory">批量分类</el-button>
        </div>
        <el-table
            :data="tableData"
            ref="table"
            class="mt25"
            @selection-change="handleSelectionChange"
        >
            <el-table-column align="center" type="selection"></el-table-column>
            <el-table-column
                label="ID"
                prop="id"
                align="center"
            ></el-table-column>
            <el-table-column
                label="商品名称"
                prop="title"
                width="300"
            ></el-table-column>

            <el-table-column
                label="商品类型"
                prop="fulu_goods_info.product_type"
                width="80"
                align="center"
            >
            </el-table-column>
            <el-table-column label="商品分类" width="200" align="center">
                <template slot-scope="scope">
                    {{ scope.row.category_1.name }}/{{
                        scope.row.category_2.name
                    }}/{{ scope.row.category_3.name }}
                </template>
            </el-table-column>
            <el-table-column
                label="上游ID"
                prop="fulu_goods_info.product_id"
                align="center"
            ></el-table-column>
            <el-table-column label="商品图片" align="center">
                <template slot-scope="scope">
                    <m-image
                        style="width: 60px; height: 60px"
                        :src="scope.row.image_url"
                    >
                    </m-image>
                </template>
            </el-table-column>

            <!--      <el-table-column label="供应商渠道" align="center">
        <template slot-scope="scope">
          {{ scope.row.sync_product_type_name || "-" }}
        </template>
      </el-table-column>-->
            <el-table-column
                label="库存"
                prop="stock"
                align="center"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column label="销量" prop="sales" align="center">
            </el-table-column>
            <el-table-column
                label="渠道"
                prop="sync_product_type_name"
                align="center"
            >
            </el-table-column>
            <el-table-column label="零售价(面值)" width="120" align="center">
                <template slot-scope="scope">
                    ￥{{ scope.row.origin_price | formatF2Y }}
                </template>
            </el-table-column>
            <el-table-column label="供货价(元)" width="100" align="center">
                <template slot-scope="scope">
                    ￥{{ scope.row.price | formatF2Y }}
                </template>
            </el-table-column>
            <el-table-column label="成本价(元)" width="100" align="center">
                <template slot-scope="scope">
                    ￥{{ scope.row.cost_price | formatF2Y }}
                </template>
            </el-table-column>
            <el-table-column
                label="利润率"
                width="80"
                align="center"
                show-overflow-tooltip
            >
                <template slot-scope="scope">
                    {{ scope.row.price_rate }}%
                </template>
            </el-table-column>
            <el-table-column label="状态" width="150" align="center">
                <template slot-scope="scope">
                    <el-switch
                        v-model="scope.row.is_display"
                        :active-value="1"
                        :inactive-value="0"
                        :inactive-text="
                            scope.row.is_display === 0 ? '下架' : '上架'
                        "
                        @change="handleStateChange(scope.row, 'is_display')"
                    >
                    </el-switch>
                    <el-switch
                        v-model="scope.row.fulu_goods_info.lock_status"
                        :active-value="1"
                        :inactive-value="0"
                        :inactive-text="
                            scope.row.fulu_goods_info.lock_status === 0
                                ? '锁定同步'
                                : '解锁同步'
                        "
                        @change="upLockStatus(scope.row)"
                    >
                    </el-switch>
                </template>
            </el-table-column>
            <el-table-column
                label="操作"
                width="150"
                fixed="right"
                align="center"
            >
                <template slot-scope="scope">
                    <!-- <el-button type="text" @click="copyLink(scope.row.id)"
            >复制链接</el-button
          > -->
                    <el-button
                        type="text"
                        @click="edit(scope.row.id)"
                        style="
                            padding: 0 !important;
                            margin-left: 10px !important;
                        "
                        >编辑</el-button
                    >
                    <el-button
                        type="text"
                        class="color-red"
                        @click="del(scope.row.id)"
                        style="
                            padding: 0 !important;
                            margin-left: 10px !important;
                        "
                        >删除</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :style="{
                display: 'flex',
                justifyContent: 'flex-end',
                marginRight: '20px',
            }"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes, prev, pager, next, jumper"
        ></el-pagination>
        <el-dialog
            title="同步商品"
            :visible="syncIsShow"
            width="500px"
            :before-close="handleSyncIsShowClose"
        >
            <el-form label-width="90px">
                <el-form-item label="供应渠道:">
                    <el-select v-model="type" clearable>
                        <el-option :value="1" label="福禄"></el-option>
                        <el-option :value="2" label="会员权益"></el-option>
                        <el-option :value="126" label="权益商品"></el-option>
                        <el-option :value="127" label="千禧平台"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>

            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="syncGoods">同 步</el-button>
                <el-button v-if="type === 127" @click="shareAlbum"
                    >选 品</el-button
                >
                <el-button @click="handleSyncIsShowClose">取 消</el-button>
            </div>
        </el-dialog>
        <el-dialog title="提示" :visible.sync="deleteVisible" width="25%">
            <p>确定要批量删除吗？</p>
            <div class="mt_20" style="text-align: right; margin: 0">
                <el-button @click="deleteVisible = false" size="small"
                    >取消</el-button
                >
                <el-button @click="onDelete" type="primary" size="small"
                    >确定</el-button
                >
            </div>
        </el-dialog>
        <!-- 批量分类 -->
        <el-dialog title="批量分类" :visible.sync="categoryVisible" width="50%">
            <el-form
                :model="searchForm"
                class="search-term mt25"
                inline
                label-width="100px"
            >
                <el-form-item label="选择导入分类:" prop="category1_id">
                    <el-select
                        v-model="searchForm.category1_id"
                        placeholder="请选择一级分类"
                        filterable
                        clearable
                        @change="
                            handleClassiyfChange(2, searchForm.category1_id)
                        "
                    >
                        <el-option
                            v-for="item in categoryList1"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="category2_id">
                    <el-select
                        v-model="searchForm.category2_id"
                        placeholder="请选择二级分类"
                        filterable
                        clearable
                        @change="
                            handleClassiyfChange(3, searchForm.category2_id)
                        "
                    >
                        <el-option
                            v-for="item in categoryList2"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="category3_id">
                    <el-select
                        v-model="searchForm.category3_id"
                        placeholder="请选择三级分类"
                        filterable
                        clearable
                    >
                        <el-option
                            v-for="item in categoryList3"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>

            <div class="mt_20" style="text-align: right; margin: 0">
                <el-button @click="exportCategory" type="primary" size="small"
                    >导入</el-button
                >
                <el-button @click="categoryVisible = false" size="small"
                    >取消</el-button
                >
            </div>
        </el-dialog>
    </m-card>
</template>
<script>
import {
    upStatus,
    batchDisplays,
    deleteProduct,
    deleteProductByIds,
    batchChanges,
} from '@/api/goods';
import infoList from '@/mixins/infoList';
import {
    getProductList,
    syncProduct,
    lockProduct,
    relieveLockProduct,
    exportProductList,
} from '@/api/fulu';
import { confirm } from '@/decorators/decorators';
import { getClassify } from '@/api/classify';
import { getClassifySelect } from '@/api/classify';

export default {
    name: 'fuluGoodsManageIndex',
    mixins: [infoList],
    data() {
        return {
            deleteVisible: false, // 批量删除弹出层
            categoryVisible: false, // 批量分类坦弹出层
            syncIsShow: false,
            listApi: getProductList,
            checkIsd: [],
            type: null,

            // 搜索类目id
            search_from: {
                category1_id: '',
                category2_id: '',
                category3_id: '',
            },

            // 利润率optios
            profitRateOptios: [
                { label: '0-50%', value: 1 },
                { label: '50%-150%', value: 2 },
                { label: '150%-300%', value: 3 },
                { label: '300%及以上', value: 4 },
                { label: '自定义区间', value: 5 },
            ],
            optios: '', // 利润率
            // 自定义利润率
            optiosCustom: {},
            searchForm: {
                category1_id: '',
                category2_id: '',
                category3_id: '',
            },
            // 类目1
            categoryList1: [],
            categoryList2: [],
            categoryList3: [],
        };
    },
    mounted() {
        this.getTableData();
        // 获取一级类目
        getClassifySelect(1, 0).then((r) => {
            this.categoryList1 = r.data.list;
        });
    },
    methods: {
        // 获取类目
        handleClassiyfChange(level, pid) {
            getClassifySelect(level, pid).then((r) => {
                if (level === 3) {
                    this.categoryList3 = [];
                    this.searchForm.category3_id = '';
                } else {
                    this.categoryList2 = [];
                    this.searchForm.category2_id = '';
                    this.categoryList3 = [];
                    this.searchForm.category3_id = '';
                }
                switch (level) {
                    case 2:
                        this.categoryList2 = r.data.list;
                        break;
                    case 3:
                        this.categoryList3 = r.data.list;
                        break;
                }
            });
        },
        // 删除
        @confirm('提示', '确定删除?')
        async del(id) {
            const { code, msg } = await deleteProduct({ id });
            if (code === 0) {
                this.$message.success(msg);
                this.getTableData();
            }
        },
        // 锁定/解锁
        async upLockStatus(row) {
            let res = {};
            switch (row.fulu_goods_info.lock_status) {
                case 1:
                    res = await lockProduct({ product_id: row.id });
                    break;
                case 0:
                    res = await relieveLockProduct({ product_id: row.id });
                    break;
            }
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.getTableData();
            }
        },
        openSync() {
            this.syncIsShow = true;
        },
        handleSyncIsShowClose() {
            this.syncIsShow = false;
            this.type = null;
        },
        // 1 上架 0 下架
        async batchOperation(status) {
            let params = {
                ids: this.checkIsd,
                value: status,
            };
            const { code, msg } = await batchDisplays(params);
            if (code === 0) {
                this.$message.success(msg);
                this.getTableData();
            }
        },
        handleSelectionChange(val) {
            this.checkIsd = [];
            if (val && val.length) {
                val.forEach((item) => {
                    this.checkIsd.push(item.id);
                });
            }
        },
        // 上/下架
        handleStateChange(row, name) {
            let data = {
                column: name,
                id: row.id,
            };
            if (row.is_display === 1) data.status = 1;
            upStatus(data).then((r) => {
                if (r.code === 0) {
                    this.$message.success('更新成功');
                    // this.getList()
                } else {
                    this.$message.error('更新失败');
                }
            });
        },
        // 复制前端商品详情链接
        copyLink(id) {
            let link = '';
            if (location.hostname === 'localhost') {
                link =
                    location.protocol +
                    '//localhost:9527/goodsDetail?goods_id=' +
                    id;
            } else {
                link = location.origin + '/goodsDetail?goods_id=' + id;
            }
            this.$fn.copy(link);
        },
        // 编辑
        edit(id) {
            this.$_blank('/layout/fuluIndex/addGoods', {
                id: id,
                is_plugin: 1,
                source: 127,
            });
        },
        // 同步商品
        syncGoods() {
            if (this.type === null) {
                this.$message.error('请选择供应渠道');
                return false;
            }
            const loading = this.$loading({
                lock: true,
                text: '正在同步...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)',
            });
            let data = {
                type: this.type,
            };
            syncProduct(data).then((res) => {
                if (res.code === 0) {
                    this.$message.success(res.msg);
                    this.handleSyncIsShowClose();
                    loading.close();
                    this.getTableData();
                } else {
                    loading.close();
                }
            });
        },
        // 选品
        shareAlbum() {
            this.$router.push('/layout/fuluIndex/selectionImportIndex');
        },
        search() {
            this.page = 1;
            // 利润率
            if (this.optios) {
                if (this.optios == 5) {
                    this.searchInfo.price_rate = {
                        from: this.optiosCustom.p1,
                        to: this.optiosCustom.p2,
                    };
                } else {
                    const rate = [
                        { from: 0, to: 50 },
                        { from: 50, to: 150 },
                        { from: 150, to: 300 },
                        { from: 300, to: 9999 },
                    ];
                    this.searchInfo.price_rate = rate[this.optios - 1];
                }
            }
            this.getTableData();
        },
        resetSearch() {
            this.page = 1;
            this.searchInfo = {
                dateType: 0, // 日期类型 0开始时间 1结束时间
            };
            this.optios = '';
        },
        // 批量删除
        deleteAllProduct() {
            if (this.checkIsd.length !== 0) {
                this.deleteVisible = true;
            } else {
                this.$message.error('请选择批量删除的数据');
            }
        },
        // 批量分类
        batchCategory() {
            if (this.checkIsd.length !== 0) {
                this.categoryVisible = true;
                this.searchForm.category1_id = '';
                this.searchForm.category2_id = '';
                this.searchForm.category3_id = '';
            } else {
                this.$message.error('请选择批量分类的数据');
            }
        },
        // 确认批量分类导入
        async exportCategory() {
            if (!this.searchForm.category3_id) {
                this.$message.error('请选择分类');
                return;
            }
            const data = {
                ids: this.checkIsd,
                category1_id: parseInt(this.searchForm.category1_id),
                category2_id: parseInt(this.searchForm.category2_id),
                category3_id: parseInt(this.searchForm.category3_id),
            };
            const res = await batchChanges(data);
            if (res.code === 0) {
                this.$message.success(res.msg)
                this.categoryVisible = false;
                this.getTableData();
            }
        },
        // 确认删除
        async onDelete() {
            const data = {
                ids: this.checkIsd,
            };
            const res = await deleteProductByIds(data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.deleteVisible = false;
                this.getTableData();
            }
        },
        // 导出
        async exportExcel() {
            // 利润率
            if (this.optios) {
                if (this.optios == 5) {
                    this.searchInfo.price_rate = {
                        from: this.optiosCustom.p1,
                        to: this.optiosCustom.p2,
                    };
                } else {
                    const rate = [
                        { from: 0, to: 50 },
                        { from: 50, to: 150 },
                        { from: 150, to: 300 },
                        { from: 300, to: 9999 },
                    ];
                    this.searchInfo.price_rate = rate[this.optios - 1];
                }
            }
            const params = {
                ...this.searchInfo,
            };
            const res = await exportProductList(params);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.download(res.data.link);
            }
        },
        // 导出方法
        download(link) {
            window.open(this.$path + '/' + link);
        },
    },
};
</script>
<style lang="scss" scoped>
@import '@/style/base.scss';
::v-deep .el-switch {
    .el-switch__label {
        color: #666666;
        width: 60px;
    }
}
</style>
