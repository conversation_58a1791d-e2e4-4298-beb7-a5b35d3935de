<!-- 商品导入 -->
<template>
    <div class="search-term">
        <div>
            <el-form class="search-term f fac" :model="searchForm">
                <el-form-item label="">
                    <div class="line-input">
                        <div class="line-box">
                            <span>商品类型</span>
                        </div>
                        <el-select v-model="searchForm.type_name" class="w100">
                            <el-option label="直充" value="直充"></el-option>
                            <el-option label="卡密" value="卡密"></el-option>
                        </el-select>
                    </div>
                </el-form-item>
                <el-form-item label="">
                    <div class="line-input">
                        <div class="line-box">
                            <span>商品名称</span>
                        </div>
                        <el-input
                            v-model="searchForm.title"
                            placeholder="请输入商品名称"
                            clearable
                        ></el-input>
                    </div>
                </el-form-item>
                <el-form-item label="">
                    <div class="line-input">
                        <div class="line-box">
                            <span>利润率</span>
                        </div>

                        <el-select
                            v-model="searchForm.promotion_rate"
                            class="w100"
                            @change="changeSelect"
                        >
                            <el-option
                                v-for="item in profitRateOptios"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                </el-form-item>
                <el-form-item label="">
                    <div class="line-input">
                        <div class="line-box">
                            <span>是否已导入</span>
                        </div>
                        <el-select v-model="searchForm.is_import" class="w100">
                            <el-option label="全部" :value="0"></el-option>
                            <el-option label="已导入" :value="1"></el-option>
                            <el-option label="未导入" :value="2"></el-option>
                        </el-select>
                    </div>
                </el-form-item>
                <el-form-item>
                    <el-button
                        class="slightly-rounded"
                        type="primary"
                        @click="searchList"
                        >查询</el-button
                    >
                    <el-button type="text" @click="resetList"
                        >重置搜索条件</el-button
                    >
                </el-form-item>
            </el-form>
        </div>
        <div>
            <div class="mt25">
                <el-button
                    type="primary"
                    class="slightly-rounded"
                    @click="addCarlist"
                    >添加选中</el-button
                >
                <el-badge :value="selectCount" class="ml10">
                    <el-button class="slightly-rounded" @click="openDialog"
                        >已选</el-button
                    >
                </el-badge>
                <span class="ml30" v-if="sever_ratio > 0"
                    >技术服务费 {{ sever_ratio / 100 }}%</span
                >
                <span class="ml30" v-if="$route.query.key === 'aljx'"
                    >线上商品总数: {{ onLineGoodsTotal }}</span
                >
            </div>

            <!-- 表格部分 -->

            <div class="mt25 table-div">
                <!-- 表头部分 -->
                <div class="table-header">
                    <div class="f fac">
                        <el-checkbox
                            class="checkbox-all"
                            v-model="checkAll"
                            @change="handleCheckAllChange"
                            :indeterminate="isIndeterminate"
                            >全选
                        </el-checkbox>

                        <div
                            class="screen-item"
                            v-for="(item, index) in screenList"
                            :key="item.id"
                            @click="handleSortClick(item, index)"
                        >
                            <span
                                class="title-span"
                                :class="sort_id === item.id ? 'active' : ''"
                                >{{ item.name }}</span
                            >
                            <span class="caret-wrapper">
                                <i
                                    :class="icons"
                                    v-for="icons in item.class"
                                    :key="icons.id"
                                ></i>
                            </span>
                        </div>
                    </div>
                    <div>
                        <el-button
                            type="primary"
                            class="slightly-rounded"
                            @click="AllCheck()"
                            >当页全选</el-button
                        >
                        <el-button
                            type="danger"
                            class="slightly-rounded"
                            @click="openSelectionSortDialog"
                            >导入全部筛选商品
                        </el-button>
                    </div>
                </div>
                <!-- table-body部分 -->
                <div class="table-body">
                    <el-row :gutter="20" ref="goodsBox" class="goods-box">
                        <el-col
                            :md="6"
                            :lg="5"
                            v-for="(item, index) in goodsList"
                            :key="item.id"
                        >
                            <div class="goods-item">
                                <div class="top-image-box">
                                    <el-checkbox-group
                                        v-model="checkedCities"
                                        @change="handleCheckedCitiesChange"
                                    >
                                        <el-checkbox
                                            :label="item"
                                        ></el-checkbox>
                                    </el-checkbox-group>
                                    <div class="goods-ig-box">
                                        <!-- <img class="img" :src="item.cover" alt="暂无图片"> -->
                                    </div>
                                    <!-- <m-image :src="item.cover" class="goods-ig-box"></m-image> -->
                                    <div class="operates-box f fac">
                                        <a
                                            v-if="item.third_url"
                                            target="_blank"
                                            :href="item.third_url"
                                            class="f1 text-center"
                                            >查看第三方详情</a
                                        >
                                        <el-divider
                                            v-if="item.third_url"
                                            direction="vertical"
                                        ></el-divider>
                                        <a
                                            href="javascript:;"
                                            @click="pushSelectedGoods(item)"
                                            class="f1 text-center"
                                            >添加</a
                                        >
                                        <el-divider
                                            direction="vertical"
                                            v-if="item.is_import"
                                        ></el-divider>
                                        <p
                                            class="color-red f1 text-center have-import"
                                            v-if="item.is_import"
                                        >
                                            已导入
                                        </p>
                                    </div>
                                </div>
                                <div class="cont-box">
                                    <div class="title">
                                        {{ item.title }}
                                    </div>
                                    <div class="f fjsb">
                                        <div class="title">
                                            利润率:
                                            <span class="color-red"
                                                >{{
                                                    Math.floor(item.rate)
                                                }}%</span
                                            >
                                        </div>
                                        <div class="title">
                                            协议价:
                                            <span class="color-red"
                                                >￥{{
                                                    item.agreement_price
                                                        | formatF2Y
                                                }}</span
                                            >
                                        </div>
                                        <div class="title">
                                            面值:
                                            <span class="color-red"
                                                >￥{{
                                                    item.market_price
                                                        | formatF2Y
                                                }}</span
                                            >
                                        </div>
                                    </div>
                                    <p class="fgx-p"></p>
                                </div>
                            </div>
                        </el-col>
                    </el-row>
                </div>
                <!-- 分页部分 -->
                <el-pagination
                    background
                    :current-page="page"
                    :page-size="pageSize"
                    :page-sizes="[24, 40, 100]"
                    :style="{ float: 'right', padding: '20px' }"
                    :total="total"
                    @current-change="handleCurrentChange"
                    @size-change="handleSizeChange"
                    layout="total, sizes, prev, pager, next, jumper"
                ></el-pagination>
                <GoodsImportDialog
                    ref="goodsImportDialog"
                    @reset="loadCheckData"
                ></GoodsImportDialog>
                <SelectionSortDialog
                    ref="selectionSortDialog"
                ></SelectionSortDialog>
            </div>
        </div>
    </div>
</template>

<script>
import GoodsImportDialog from './components/goodsImportDialog.vue';
import { getCategoryChild, getGoodsList } from '@/api/gatherSupply';
import SelectionSortDialog from './components/selectionSortDialog.vue';
import GoodsImportSearch from './components/goodsImportSearch';
import zhLang from 'element-ui/lib/locale/lang/zh-CN';
// 商品导入
export default {
    name: 'selectionImportIndex',
    components: {
        GoodsImportDialog,
        SelectionSortDialog,
        GoodsImportSearch,
    },
    data() {
        return {
            onLineGoodsTotal: 0,
            sever_ratio: 0,
            selectCount: 0,
            goods_index: null,
            // 商品选中的数据
            checkedCities: [],
            selectGoods: [],
            category: [],
            // category1: {},
            // category2: {},
            // category3: {},
            sort_id: null,
            type: '',
            sort: '',
            // 区分 el-icon-bottom * 接口不用管
            sort_type: 1,
            // 全选样式
            isIndeterminate: false,
            checkAll: false,
            searchForm: {
                type_name: '', // 商品类型
                title: '', // 商品名称
                promotion_rate: '', // 利润率
                is_import: 0, // 0全部, 1已导入, 2未导入
            },
            page: 1,
            pageSize: 24,
            total: 0,
            goodsList: [],
            groupList: [],
            screenList: [
                {
                    id: 1,
                    name: '最新上架',
                    type: 'created_at',
                    class: ['el-icon-bottom'],
                },
                {
                    id: 3,
                    name: '协议价',
                    type: 'purchase_price',
                    class: ['caret-top sort-icon', 'caret-bottom sort-icon'],
                },
            ],
            profitRateId: '', // 选中利润率
            // 利润率选项
            profitRateOptios: [
                {
                    label: '5%以下',
                    value: '1',
                },
                {
                    label: '5%-10%',
                    value: '2',
                },
                {
                    label: '10%-20%',
                    value: '3',
                },
                {
                    label: '20%-50%',
                    value: '4',
                },
                {
                    label: '50%-100%',
                    value: '5',
                },
                {
                    label: '100%-200%',
                    value: '6',
                },
                {
                    label: '200%-500%',
                    value: '7',
                },
                {
                    label: '500%-700%',
                    value: '8',
                },
                {
                    label: '700%-900%',
                    value: '9',
                },
                {
                    label: '900%以上',
                    value: '10',
                },
            ],
        };
    },
    mounted() {
        // this.getCategory(1, 0)
        this.fetch();
        this.getGoodsList();
        localStorage.removeItem('cartlist');
    },
    methods: {
        // 选则利润率
        changeSelect(id) {
            this.profitRateId = id;
        },
        pageReload() {
            this.page = 1;
        },
        // 获取选中的商品
        loadCheckData() {
            localStorage.removeItem('cartlist');
            this.selectCount = 0;
            this.checkedCities = [];
            this.checkAll = false;
            this.isIndeterminate = false;
            this.searchList()
        },
        // 添加
        pushSelectedGoods(item) {
            if (item.is_import !== 1) {
                if (localStorage.getItem('cartlist')) {
                    let cartList = JSON.parse(localStorage.getItem('cartlist'));
                    let obj = {};
                    obj = cartList.find((v) => {
                        return v.id === item.id;
                    });
                    if (!obj) {
                        cartList.push(item);
                        this.checkedCities.push(item);
                        localStorage.setItem(
                            'cartlist',
                            JSON.stringify(cartList),
                        );
                        this.selectCount = cartList.length;
                        this.$message.success('添加成功');
                    } else {
                        this.$message.error('此商品已添加');
                    }
                } else {
                    let cartList = [];
                    cartList.push(item);
                    this.checkedCities.push(item);
                    localStorage.setItem('cartlist', JSON.stringify(cartList));
                    this.selectCount = cartList.length;
                    this.$message.success('添加成功');
                }
            } else {
                this.$message.error('不能添加已导入商品');
            }
        },
        AllCheck() {
            localStorage.setItem('cartlist', JSON.stringify(this.goodsList));

            this.selectCount = this.goodsList.length;
            this.$nextTick(() => {
                this.$refs.goodsImportDialog.updateCard();
            });
            this.openDialog();
        },
        // 打开导入商品选择分类、标签
        openSelectionSortDialog() {
            this.$refs.selectionSortDialog.handleClassiyfChange(1, 0);
            this.$refs.selectionSortDialog.isShow = true;
            this.$refs.selectionSortDialog.goodsTotal = this.total;
        },
        // 添加至选中
        addCarlist() {
            // console.log("当前购物车" + this.selectGoods);
            localStorage.setItem('cartlist', JSON.stringify(this.selectGoods));

            this.selectCount = this.selectGoods.length;
            this.$nextTick(() => {
                this.$refs.goodsImportDialog.updateCard();
            });
        },
        // 全选
        handleCheckAllChange(val) {
            this.checkedCities = val ? this.goodsList : [];
            this.isIndeterminate = false;
            this.selectGoods = this.goodsList;
        },
        // 多选
        handleCheckedCitiesChange(value) {
            let checkedCount = value.length;

            this.selectGoods = value;

            // console.log("当前选择数据：", value);
            this.isIndeterminate =
                checkedCount > 0 && checkedCount < this.goodsList.length;
        },
        async getCategory(level, pid) {
            switch (level) {
                case 1:
                    pid = 0;
                    break;
                case 2:
                    pid = this.searchForm.fl1;
                    break;
                case 3:
                    pid = this.searchForm.fl2;
                    break;
            }
            let searchForm = {
                pid: pid,
                source: parseInt(this.searchForm.terrace),
                gather_supply_id: parseInt(this.$route.query.id),
                key: this.$route.query.key,
            };
            let list = await getCategoryChild(searchForm);

            if (list.code === 0) {
                switch (level) {
                    case 1:
                        this.category1 = list.data;
                        break;
                    case 2:
                        this.category2 = list.data;
                        break;
                    case 3:
                        this.category3 = list.data;
                        break;
                }
            }
        },
        totalNumberOfOnlineGoods(total) {
            this.onLineGoodsTotal = total || 0;
        },
        componentsSearch(searchForm = {}) {
            this.page = 1;
            this.getGoodsList();
        },
        async getGoodsList() {
            let searchForm = {};
            // searchForm = this.$refs[this.$route.query.key].newSearchForm
            searchForm.page = parseInt(this.page);
            searchForm.limit = parseInt(this.pageSize);
            searchForm.type = this.type;
            searchForm.sort = this.sort;
            searchForm.key = 'millennium';
            searchForm.gather_supply_id = parseInt(this.$route.query.id) || 0;
            searchForm.type_name = this.searchForm.type_name;
            searchForm.title = this.searchForm.title;
            searchForm.is_import = this.searchForm.is_import;
            // 利润率
            if (this.searchForm.promotion_rate) {
                searchForm.range_type = this.profitRateId;
                const rate = [
                    { from: 0, to: 5 },
                    { from: 5, to: 10 },
                    { from: 10, to: 20 },
                    { from: 20, to: 50 },
                    { from: 50, to: 100 },
                    { from: 100, to: 200 },
                    { from: 200, to: 500 },
                    { from: 500, to: 700 },
                    { from: 700, to: 900 },
                    { from: 900, to: 9999 },
                ];
                searchForm.promotion_rate =
                    rate[
                        this.profitRateOptios.indexOfJSON(
                            'value',
                            this.searchForm.promotion_rate,
                        )
                    ];
            }

            this.$refs.selectionSortDialog.goodSearchWhere = searchForm;
            this.$refs.selectionSortDialog.notify();
            let is_import = '';
            // is_import = this.$refs[this.$route.query.key].is_import ? this.$refs[this.$route.query.key].is_import : '0'
            let list = await getGoodsList(searchForm);
            if (list.code === 0) {
                /*let resList = list.data.list
        let newList = []
        switch (is_import) {
          case '0': // 全部
            newList = resList
            break
          case '1': // 已导入
            resList.forEach(item => {
              if (item.is_import && item.is_import === 1) {
                newList.push({ ...item })
              }
            })
            break
          case '2': // 未导入
            resList.forEach(item => {
              if (!item.is_import || item.is_import !== 1) {
                newList.push({ ...item })
              }
            })
            break
        } */
                // this.goodsList = newList
                this.goodsList = list.data.list;
                this.sever_ratio = list.data.sever_ratio;
                this.total = list.data.total;
                zhLang.el.pagination.total = `共 {total} ${
                    this.total / this.pageSize === 100 ? '+' : ''
                } 条`;
            }
        },
        // 重置序列条件
        reserSort() {
            this.screenList = [
                {
                    id: 1,
                    name: '最新上架',
                    type: 'created_at',
                    class: ['el-icon-bottom'],
                },
                {
                    id: 3,
                    name: '协议价',
                    type: 'purchase_price',
                    class: ['caret-top sort-icon', 'caret-bottom sort-icon'],
                },
            ];
        },
        // 排序按钮
        handleSortClick(item, index) {
            this.reserSort();
            if (item.class.length === 1 && item.class[0] === 'el-icon-bottom') {
                this.sort_type = 1;
                this.screenList[index].class[0] =
                    this.screenList[index].class[0] + ' active';
                this.sort = 'desc';
            } else if (item.class.length === 2) {
                if (this.sort_type === 1) {
                    this.sort_type = 2;
                    this.sort = '';
                } else {
                    this.sort_type = 1;
                    this.sort = 'desc';
                }
                switch (this.sort) {
                    case 'desc':
                        this.screenList[index].class[0] =
                            this.screenList[index].class[0] + ' active';
                        this.sort = 'asc';
                        break;
                    case 'asc':
                        this.screenList[index].class[1] =
                            this.screenList[index].class[1] + ' active';
                        this.sort = 'desc';
                        break;
                    default:
                        this.screenList[index].class[1] =
                            this.screenList[index].class[1] + ' active';
                        this.sort = 'desc';
                        break;
                }
            }
            this.sort_id = item.id;
            this.type = item.type;

            this.getGoodsList();
        },
        // 打开已选dialog
        openDialog() {
            this.$refs.goodsImportDialog.isShow = true;
            this.$nextTick(() => {
                this.$refs.goodsImportDialog.fetch();
                this.$refs.goodsImportDialog.updateCard();
            });
        },
        // 重置
        resetForm(formName) {
            this.$refs[formName].resetFields();
        },
        // 查询
        searchList() {
            this.page = 1;
            this.getGoodsList();
        },
        // 重置搜索条件
        resetList() {
            this.searchForm.is_import = 0;
            this.searchForm.title = '';
            this.searchForm.type_name = '';
            this.searchForm.promotion_rate = '';
        },
        handleCurrentChange(page) {
            this.page = page;

            this.getGoodsList();
            this.fetch();
        },
        handleSizeChange(size) {
            this.pageSize = size;
            this.getGoodsList();
            this.fetch();
        },
        fetch() {
            /* window.onresize = () => {
        this.$refs.goodsBox.$children.forEach((item) => {
          item.$children[1].$el.style.width = item.$el.offsetWidth - 20 + "px";
          item.$children[1].$el.style.height = item.$el.offsetWidth - 20 + "px";
        });
      };
      this.$refs.goodsBox.$children.forEach((item) => {
        item.$children[1].$el.style.width = item.$el.offsetWidth - 20 + "px";
        item.$children[1].$el.style.height = item.$el.offsetWidth - 20 + "px";
      }); */
        },
    },
};
</script>
<style lang="scss" scoped>
@import './goodsImportStyle/goodsImport.scss';
.slightly-rounded {
    border-radius: 8px !important;
    padding: 13px 30px !important;
}
::v-deep .el-input__inner {
    border-radius: 0 8px 8px 0;
}
::v-deep .el-form-item {
    margin-right: 10px;
}
</style>
