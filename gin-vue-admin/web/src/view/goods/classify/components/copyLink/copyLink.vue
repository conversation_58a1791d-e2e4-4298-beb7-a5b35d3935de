<template>
  <!--复制链接弹窗-->
  <el-dialog :visible.sync="copyVisible" title="复制链接" width="30%">
    <div class="f fac copyBox" >
<!--      <el-button @click="pclinkCopy">pc端链接</el-button>-->
<!--      <el-button  @click="H5linkCopy">H5链接</el-button>-->
<!--      <el-button @click="appletlinkCopy">小程序链接</el-button>-->
    </div>
    <span class="txinfo">点击按钮复制对应的链接！</span>
    <div slot="footer" class="dialog-footer" >
      <el-button @click="copyCloseBrand" type="primary">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "copylink",
  data() {
    return {
      copyVisible:false,
    }
  },
  methods:{
    openshow(){
      this.copyVisible = true
    },
    copyCloseBrand(){
      this.copyVisible = false
    },
  }
}

</script>

<style scoped lang="scss">
.copyBox{
  display: flex;
  align-items: center;
//justify-content: space-around;
}
.offButton{
//border:1px solid #F32321;
  border-radius: 5px;
//color:#FFFFFF ;
//background-color: #F32321;
}
.txinfo{
  display: block;
  margin-left:34px;
  margin-top:13px;
  opacity: 0.75;
  color: rgba(16, 16, 16, 1);
  text-align: left;
  font-family: SourceHanSansSC-regular;
}

</style>