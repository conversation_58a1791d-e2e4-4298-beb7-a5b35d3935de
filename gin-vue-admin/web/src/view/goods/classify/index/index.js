import { getCategoryList, quickCreateCategoryByName, deleteCategory, batchDisplays,moveCategory } from "@/api/category"
import Detail from "../components/detail"
import AddClassify from "../components/add"
import CopyLink from "../components/copyLink/copyLink"
export default {
    name: "classifyIndex",
    components: { Detail, AddClassify ,CopyLink},
    data() {
        return {
            searchInputWidth: "160px",
            rootIsShow: false,
            copyVisible:false,
            category1_id:null,
            category2_id:null,
            category3_id:null,
            searchForm: {
                name: ""
            },
            tableData: [],
            page: 1,
            pageSize: 10,
            total: 0,
            categoryName: "",
            // 多选选中的数据
            selectTables: [],
        }
    },
    filters: {
        formatStatus: function (status) {
            return status ? "启用" : "关闭"
        },
    },
    mounted() {
        this.fetch();
    },
    methods: {
        showTdnex(){
          this.$refs.copyLink.openshow()
        },
        //复制链接
        copyLink(row) {
            console.log(row)
            this.category1_id = row.id
            this.copyVisible = true
            // let link = ""
            // if (location.hostname === "localhost") {
            //     link = location.protocol + "//localhost:9527/goodsDetail?goods_id=" + id
            // } else {
            //     link = location.origin + "/goodsDetail?goods_id=" + id
            // }
            // this.$fn.copy(link)
        },
        //cupy off
        copyCloseBrand(){
            this.copyVisible = false
        },
        //复制链接
        pclinkCopy(){
            let link = ""
            // if (location.hostname === "localhost") {
            //     link = location.protocol + "//localhost:9527/albumGoods?album_key=" + this.copyProductId
            // } else {
            //     link = location.origin + "/albumGoods?album_key=" + this.copyProductId
            // }
            //
            if (location.hostname === "localhost") {
                // link = location.protocol + "//localhost:9527/goodsList?category1_id="+this.category1_id+"&category2_id="+this.category2_id+"&category3_id="+this.category3_id
                link = location.protocol + "//localhost:9527/goodsList?category1_id="+this.category1_id
            } else {
                // link = location.origin + "/goodsList?category1_id="+this.category1_id+"&category2_id="+this.category2_id+"&category3_id="+this.category3_id
                link = location.origin + "/goodsList?category1_id="+this.category1_id

            }
            this.$fn.copy(link)
        },
        H5linkCopy(){
            let link = ""
            if (location.hostname === "localhost") {
                link = location.protocol+ "//localhost:9527"+ "/h5/?menu#/"+"pages/classify/classify"
            } else {
                link = location.origin + "/h5/?menu#/" + "pages/classify/classify"
            }
            this.$fn.copy(link)
        },
        appletlinkCopy(){
            let link = "/pages/classify/classify"
            this.$fn.copy(link)
        },
        // 上移下移
        async updateSort(type, row) {
            let params = {
                id: row.id,
                parent_id: row.parent_id,
                move_operate: type
            }
           let res = await moveCategory(params)
           if(res.code === 0){
            this.$message.success("操作成功")
            this.fetch()
           }
        },
        handleSelectionChange(val) {
            this.selectTables = val;
        },
        // search input 获取焦点
        searchInputFocus() {
            this.searchInputWidth = "300px";
        },
        // search input 失去焦点
        searchInputBlur() {
            this.searchInputWidth = "160px";
        },
        // 打开添加商品分类
        openAddClassify() {
            this.$refs.addClassify.intent(0, 0, 1);
        },
        fetch() {
            getCategoryList({ page: this.page, pageSize: this.pageSize, ...this.searchForm }).then(res => {
                if (res.code === 0) {
                    this.total = res.data.total;
                    this.tableData = res.data.list;
                } else {
                    this.total = 0;
                    this.tableData = [];
                }
            })
        },
        handleCurrentChange(page) {
            this.page = page
            this.fetch()
        },
        handleSizeChange(size) {
            this.pageSize = size
            this.fetch()
        },
        // 打开子类目
        openDetail(row) {
            this.$refs.detail.level2.drawer = true;
            this.$refs.detail.level2.categoryName = row.name;
            this.$nextTick(() => {
                this.$refs.detail.fetchClassifyLevel2(row.id);
            })
        },

        //快速添加分类
        quickCreateCategory() {
            let that = this;
            if (!that.categoryName) {
                return;
            }
            let para = {
                "parent_id": 0,
                "level": 1,
                "name": that.categoryName
            }
            quickCreateCategoryByName(para).then(res => {
                if (res.code === 0) {
                    that.fetch();
                    that.$message.success(res.msg);
                    that.rootIsShow = false;
                    that.categoryName = "";
                } else {
                    that.$message.error(res.msg);
                }
            });
        },

        //删除分类
        deleteCategoryDialog(item) {
            let that = this;
            this.$confirm('确定要删除吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            }).then(() => {
                that.deleteCategoryById(item);
            });
        },
        deleteCategoryById(item) {
            let that = this;
            let para = {
                "id": item.id
            }
            deleteCategory(para).then(res => {
                if (res.code === 0) {
                    that.fetch();
                    that.$message.success(res.msg);
                } else {
                    that.$message.error(res.msg);
                }
            });
        },

        //编辑
        editCategoryById(item) {
            this.$refs.addClassify.intent(item.id, 0, 1);
        },

        //搜索
        search() {
            this.tableData = [];
            this.page = 1;
            this.pageSize = 10;
            this.fetch();
        },

        //状态修改
        upCategoryDisplays(item, value) {
            let para = {
                "ids": [item.id],
                "value": value
            }
            batchDisplays(para).then(r => {
                if (r.code === 0) {
                    this.$message.success(r.msg);
                    this.fetch();
                }
            });
        },

        //批量处理状态
        batchUpCategoryDisplays(value) {
            if (this.selectTables.length == 0) {
                this.$message.warning("请选择要处理的数据");
                return;
            }

            let ids = [];
            this.selectTables.forEach(item => {
                ids.push(item.id);
            })
            let para = {
                "ids": ids,
                "value": value
            }
            batchDisplays(para).then(r => {
                if (r.code === 0) {
                    this.$message.success(r.msg);
                    this.fetch();
                }
            });
        }
    }
}