<template>
    <m-card>
        <el-button type="primary" @click="addUnit">添加</el-button>
        <el-table class="mt_20" :data="tableList">
            <el-table-column
                label="ID"
                prop="id"
                align="center"
            ></el-table-column>
            <el-table-column
                label="名称"
                prop="title"
                align="center"
            ></el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        class="color-red"
                        @click="del(scope.row.id)"
                        style="
                            padding: 0 !important;
                            margin-left: 10px !important;
                        "
                    >
                        删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100, 200]"
            :style="{
                display: 'flex',
                justifyContent: 'flex-end',
                marginRight: '20px',
            }"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes,prev, pager, next, jumper"
        >
        </el-pagination>

        <el-dialog
            title="添加商品单位"
            :visible.sync="dialogVisible"
            width="30%"
        >
            <el-form :model="formData" label-width="80px">
                <el-form-item label="商品单位">
                    <el-input
                        v-model="formData.title"
                        placeholder="请输入商品单位"
                    ></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="creatUnit"> 确 定 </el-button>
            </span>
        </el-dialog>
    </m-card>
</template>

<script>
import { getUnitList, creatUnit, delUnit } from '@/api/goods';
import { confirm } from '@/decorators/decorators';
export default {
    name: 'goodsUnitIndex',
    data() {
        return {
            tableList: [],
            page: 1,
            pageSize: 10,
            total: null,
            dialogVisible: false,
            formData: {
                title: '',
            },
        };
    },
    mounted() {
        this.getUnitList();
    },
    methods: {
        // 商品单位列表
        async getUnitList() {
            const data = {
                page: this.page,
                pageSize: this.pageSize,
            };
            const res = await getUnitList(data);
            if (res.code === 0) {
                this.tableList = res.data.list;
                this.total = res.data.total;
            }
        },
        // 添加商品单位
        addUnit() {
            this.formData.title = '';
            this.dialogVisible = true;
        },
        // 创健商品单位
        async creatUnit() {
            if (!this.formData.title) {
                this.$message.error('请输入商品单位');
                return;
            }
            const data = {
                title: this.formData.title,
            };
            const res = await creatUnit(data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.dialogVisible = false;
                this.page = 1;
                this.getUnitList();
            }
        },
        // 删除商品单位
        @confirm('提示', '确认删除?')
        async del(id) {
            const data = {
                id: parseInt(id),
            };
            const res = await delUnit(data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.page = 1;
                this.getUnitList();
            }
        },
        handleCurrentChange(page) {
            this.page = page;
            this.getUnitList();
        },
        handleSizeChange(size) {
            this.page = 1;
            this.pageSize = size;
            this.getUnitList();
        },
    },
};
</script>

<style lang="scss" soped>
.color-red {
    color: red;
}
</style>
