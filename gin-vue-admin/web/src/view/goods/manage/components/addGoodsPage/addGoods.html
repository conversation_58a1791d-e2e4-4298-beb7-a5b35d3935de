<div class="big">
    <!--    <SelectClassify v-if="step === 1" @handleNext="handleNext"></SelectClassify>-->
    <m-card>
        <el-button type="primary" @click="submitGoods">保存商品</el-button>
        <div class="addGoods-box mt25">
            <el-form label-width="145px">
                <el-tabs
                    type="card"
                    v-model="activeName"
                    @tab-click="handleTabClick"
                >
                    <el-tab-pane label="基本信息" name="1">
                        <!-- 基本信息 -->
                        <el-row>
                            <el-col :span="16">
                                <!--                                <el-form-item>
                                                                    <span slot="label">已选分类/品牌 <span class="color-red">*</span></span>
                                                                    <p>{{ goodsClassifyStr }}</p>
                                                                </el-form-item>-->
                                <el-form-item>
                                    <span slot="label"
                                        ><span class="color-red">*</span
                                        >分类:</span
                                    >
                                    <el-form-item>
                                        <el-select
                                            filterable
                                            clearable
                                            v-model="classifyCheck1"
                                            @change="getClassify2o3(2,classifyCheck1)"
                                        >
                                            <el-option
                                                v-for="item in classifyList1"
                                                :key="item.id"
                                                :label="item.name"
                                                :value="item.id"
                                            >
                                            </el-option>
                                        </el-select>
                                        <el-select
                                            filterable
                                            class="ml10"
                                            clearable
                                            v-model="classifyCheck2"
                                            @change="getClassify2o3(3,classifyCheck2)"
                                        >
                                            <el-option
                                                v-for="item in classifyList2"
                                                :key="item.id"
                                                :label="item.name"
                                                :value="item.id"
                                            >
                                            </el-option>
                                        </el-select>
                                        <el-select
                                            filterable
                                            class="ml10"
                                            clearable
                                            v-model="classifyCheck3"
                                        >
                                            <el-option
                                                v-for="item in classifyList3"
                                                :key="item.id"
                                                :label="item.name"
                                                :value="item.id"
                                            >
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-form-item>
                                <!-- <el-form-item label="品牌:">
                                    <el-select 
                                        v-model="brand_id" 
                                        clearable 
                                        filterable 
                                        remote
                                        :remote-method="remoteMethod" 
                                        :loading="brandsOptiosData.loading"
                                    >
                                        <el-option 
                                            v-for="item in brandsOptiosData.brandOptios" 
                                            :key="item.id" 
                                            :label="item.name"
                                            :value="item.id"
                                        ></el-option>
                                        <div class="text-center">
                                            <el-pagination 
                                                background small class="pagination"
                                                style="padding-top:10px !important;padding-bottom: 0 !important;"
                                                :current-page="brandsOptiosData.page"
                                                :page-size="brandsOptiosData.pageSize"
                                                :total="brandsOptiosData.total"
                                                @current-change="handleBrandPage"
                                                layout="prev,pager, next"
                                            />
                                        </div>
                                    </el-select>
                                </el-form-item> -->
                                <!-- 按字母筛选品牌 start -->
                                <el-form-item label="品牌:">
                                    <el-select
                                        v-model="brand_id"
                                        clearable
                                        filterable
                                        :filter-method="handleFilter"
                                        @visible-change="getBrandsOptios"
                                        @focus="changeSelect"
                                    >
                                        <el-option
                                            v-if="showSelect"
                                            :label="brand_name"
                                            :value="brand_id"
                                        ></el-option>
                                        <el-option
                                            v-for="item in brandsOptiosData.brandsOptios"
                                            :key="item.id"
                                            :label="item.name"
                                            :value="item.id"
                                        ></el-option>
                                        <div class="text-center">
                                            <el-pagination
                                                background
                                                small
                                                class="pagination"
                                                style="
                                                    padding-top: 10px !important;
                                                    padding-bottom: 0 !important;
                                                "
                                                :current-page="brandsOptiosData.page"
                                                :page-size="brandsOptiosData.pageSize"
                                                :total="brandsOptiosData.total"
                                                @current-change="handleBrandPage"
                                                layout="prev,pager, next"
                                            />
                                        </div>
                                    </el-select>
                                </el-form-item>
                                <!-- 按字母筛选品牌 end -->
                                <el-form-item label="排序:">
                                    <el-input-number
                                        :min="0"
                                        :controls="false"
                                        v-model="goodsSort"
                                    ></el-input-number>
                                </el-form-item>
                                <div class="f fac" v-if="supplier_id">
                                    <el-form-item label="商品来源:">
                                        <el-select
                                            v-model="source_id"
                                            clearable
                                            filterable
                                        >
                                            <el-option
                                                v-for="item in source_options"
                                                :key="item.id"
                                                :label="item.name"
                                                :value="item.id"
                                            ></el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="分类:">
                                        <el-select
                                            v-model="source_classify_id"
                                            clearable
                                            filterable
                                        >
                                            <el-option
                                                v-for="item in source_classify_options"
                                                :key="item.id"
                                                :label="item.name"
                                                :value="item.id"
                                            ></el-option>
                                        </el-select>
                                    </el-form-item>
                                </div>
                                <el-form-item>
                                    <span slot="label"
                                        ><span class="color-red">*</span
                                        >商品名称:</span
                                    >
                                    <el-input v-model="goodsTitle"></el-input>
                                </el-form-item>
                                <el-form-item label="商品描述:">
                                    <el-input
                                        v-model="goodsDesc"
                                        :maxlength="60"
                                        show-word-limit
                                    ></el-input>
                                    <p class="color-grap">
                                        商品描述最多可输入60个字符
                                    </p>
                                </el-form-item>
                                <el-form-item>
                                    <span slot="label"
                                        ><span class="color-red">*</span
                                        >商品主图:</span
                                    >
                                    <el-upload
                                        class="avatar-uploader"
                                        :show-file-list="false"
                                        :action="path+'/fileUploadAndDownload/upload'"
                                        :headers="{'x-token':token}"
                                        :on-success="handleMainImgSuccess"
                                        :before-upload="$fn.beforeAvatarUpload"
                                        accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF"
                                    >
                                        <img
                                            v-if="goodsImageUrl"
                                            :src="goodsImageUrl"
                                            class="avatar"
                                        />
                                        <i
                                            v-else
                                            class="el-icon-plus avatar-uploader-icon"
                                        ></i>
                                    </el-upload>
                                    <a
                                        @click="openMaxImgDialog([{url:goodsImageUrl}])"
                                        href="javascript:;"
                                        style="color: #155bd4"
                                        >查看图片</a
                                    >
                                    <p class="color-grap">
                                        建议尺寸：800*800像素,图片需限制在{{$store.state.uploadLimitSize}}M以内
                                    </p>
                                </el-form-item>
                                <el-form-item>
                                    <span slot="label"
                                        ><span class="color-red">*</span
                                        >商品图片:</span
                                    >
                                    <div class="f fac">
                                        <p>({{ goodsGallery.length }}/9)</p>
                                        <el-button
                                            type="text"
                                            class="ml10"
                                            @click="openDialogSort(1)"
                                            >点击进行排序
                                        </el-button>
                                        <el-button
                                            type="text"
                                            @click="openMaxImgDialog(goodsGalleryFileList)"
                                            >查看图片</el-button
                                        >
                                    </div>
                                    <div class="f fw">
                                        <div
                                            class="productImgBox"
                                            v-for="(item,index) in goodsGalleryFileList"
                                            :key="item.id"
                                        >
                                            <m-image
                                                v-if="item.type === 1"
                                                :src="item.url"
                                                style="
                                                    width: 180px;
                                                    height: 180px;
                                                "
                                            />
                                            <div class="del-box">
                                                <el-button
                                                    type="text"
                                                    icon="el-icon-delete"
                                                    @click="removeGoodsGalleryList(index)"
                                                ></el-button>
                                            </div>
                                        </div>
                                        <el-upload
                                            v-if="goodsGalleryFileList.length < 9"
                                            class="avatar-uploader"
                                            multiple
                                            list-type="picture-card"
                                            :action="path+'/fileUploadAndDownload/upload'"
                                            :headers="{'x-token':token}"
                                            :on-success="handleGoodsGallerySuccess"
                                            :before-upload="$fn.beforeAvatarUpload"
                                            :show-file-list="false"
                                            accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF"
                                        >
                                            <i
                                                class="el-icon-plus avatar-uploader-icon"
                                            ></i>
                                        </el-upload>
                                        <!--                                        <el-upload :limit="9" class="avatar-uploader" multiple
                                                   :on-exceed="handle9Exceed"
                                                   list-type="picture-card"
                                                   :action="path+'/fileUploadAndDownload/upload'"
                                                   :headers="{'x-token':token}" :on-success="handleGoodsGallerySuccess"
                                                   :before-upload="beforeAvatarUpload"
                                                   :show-file-list="false"
                                                   accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                                            <i class="el-icon-plus avatar-uploader-icon"></i>
                                        </el-upload>-->
                                    </div>
                                    <p class="color-grap">
                                        建议尺寸：800*800像素,最多上传9个素材,单张图片需限制在{{$store.state.uploadLimitSize}}M以内
                                    </p>
                                </el-form-item>
                                <el-form-item label="首图视频:">
                                    <div class="f fac">
                                        <el-progress
                                            v-if="progressIsShow"
                                            type="circle"
                                            :width="178"
                                            :percentage="progressNum"
                                            style="margin-right: 8px"
                                        ></el-progress>
                                        <div
                                            v-if="videoUrl && !progressIsShow"
                                            class="video-box"
                                        >
                                            <video
                                                controls="controls"
                                                :src="videoUrl"
                                                style="
                                                    width: 100%;
                                                    height: 100%;
                                                "
                                            ></video>
                                        </div>
                                        <el-upload
                                            class="avatar-uploader"
                                            :show-file-list="false"
                                            :action="path+'/fileUploadAndDownload/upload'"
                                            :headers="{'x-token':token}"
                                            :on-success="handleMainVideoSuccess"
                                            :on-progress="videoPrigress"
                                            :on-error="videoError"
                                            :before-upload="$fn.beforeVideoUpload"
                                            accept=".mp4,.3gp,.avi"
                                        >
                                            <i
                                                v-if="videoUrl"
                                                class="el-icon-edit-outline avatar-uploader-icon"
                                            ></i>
                                            <i
                                                v-else
                                                class="el-icon-plus avatar-uploader-icon"
                                            ></i>
                                        </el-upload>
                                    </div>
                                    <el-button
                                        v-if="videoUrl"
                                        type="text"
                                        @click="removeVideo"
                                        >移除视频</el-button
                                    >
                                    <p class="color-grap">
                                        设置后商品详情首图默认显示视频,建议时长9-30秒
                                    </p>
                                </el-form-item>
                                <el-form-item label="商品状态:">
                                    <el-radio-group v-model="is_display">
                                        <el-radio :label="1">上架</el-radio>
                                        <el-radio :label="0">下架</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                                <el-form-item
                                    label="商品自动更新成本价:"
                                    v-if="gather_supply.category_id == 2"
                                >
                                    <el-radio-group
                                        v-model="is_auto_update_cost"
                                    >
                                        <el-radio :label="1">是</el-radio>
                                        <el-radio :label="0">否</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                                <el-form-item
                                    label="商品自动更新供货价:"
                                    v-if="gather_supply.category_id == 2"
                                >
                                    <el-radio-group
                                        v-model="is_auto_update_price"
                                    >
                                        <el-radio :label="1">是</el-radio>
                                        <el-radio :label="0">否</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                                <el-form-item label="锁定上下架:">
                                    <el-radio-group v-model="status_lock">
                                        <el-radio :label="1">是</el-radio>
                                        <el-radio :label="0">否</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                                <el-form-item label="历史销售:">
                                    <el-input-number
                                        :min="0"
                                        style="max-width: 200px"
                                        :controls="false"
                                        v-model="goodsSales"
                                    ></el-input-number>
                                    <p>前端真实销售 = 历史销售 + 系统销售</p>
                                </el-form-item>
                                <el-form-item
                                    label="售后及服务:"
                                    v-show="false"
                                >
                                    <el-input
                                        type="textarea"
                                        :rows="6"
                                        placeholder="填写特殊说明,不超过100字"
                                        v-model="goodsService"
                                    ></el-input>
                                </el-form-item>
                                <el-form-item>
                                    <span slot="label"
                                        ><span class="color-red">*</span
                                        >运费设置:</span
                                    >
                                    <el-radio
                                        :disabled="goodsFreightType === '2'"
                                        v-model="goodsFreightType"
                                        label="0"
                                    >
                                        <span>统一运费</span>
                                        <el-input-number
                                            :min="0"
                                            :controls="false"
                                            class="ml10"
                                            :precision="2"
                                            :disabled="goodsFreightType === '3' || goodsFreightType === '2' || goodsFreightType === '1' ? true : false"
                                            style="max-width: 200px"
                                            v-model="goodsFreightPrice"
                                        >
                                        </el-input-number>
                                        <span class="ml10">元</span>
                                    </el-radio>
                                    <br />
                                    <el-radio
                                        :disabled="goodsFreightType === '2'"
                                        v-model="goodsFreightType"
                                        label="1"
                                        class="mt10"
                                    >
                                        <span>运费模板</span>
                                        <el-select
                                            v-model="freight_template_id"
                                            filterable
                                            :disabled="goodsFreightType === '3' || goodsFreightType === '2' || goodsFreightType === '0' ? true : false"
                                            class="ml10 w200"
                                        >
                                            <el-option
                                                v-for="item in freightTemplateOption"
                                                :key="item.id"
                                                :label="item.name"
                                                :value="item.id"
                                            ></el-option>
                                        </el-select>
                                        <el-button
                                            type="text"
                                            class="ml10"
                                            @click="addTemp"
                                            >新建</el-button
                                        >
                                        <el-button
                                            type="text"
                                            class="ml10"
                                            :loading="btnLoadIsShow"
                                            @click="getFreightTemp(true)"
                                            >刷新
                                        </el-button>
                                    </el-radio>
                                    <br />
                                    <el-radio
                                        v-model="goodsFreightType"
                                        :disabled="goodsFreightType === '2'"
                                        label="3"
                                        class="mt10"
                                    >
                                        <span>包邮</span>
                                    </el-radio>
                                    <br />
                                    <el-radio
                                        v-if="goodsFreightType === '2'"
                                        v-model="goodsFreightType"
                                        label="2"
                                        class="mt10"
                                    >
                                        <span>第三方运费</span>
                                    </el-radio>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-tab-pane>
                    <el-tab-pane label="商品详情" name="2">
                        <!-- 商品详情 -->
                        <!--                        <quill-editor ref="myTextEditor" v-model="goodsDetailImages" :options="editorOption"-->
                        <!--                            style="height:400px;width: 100%;padding-bottom: 100px"></quill-editor> -->
                        <m-editor
                            ref="mEditor"
                            v-model="goodsDetailImages"
                            style="height: 700px"
                        ></m-editor>

                        <!-- <el-form-item>
                            <span slot="label"><span class="color-red"></span>商品详情:</span>
                            <div class="f fac">
                                <p>({{ goodsDetailImages.length }}/9)</p>
                                <el-button type="text" class="ml10" @click="openDialogSort(2)">点击进行排序
                                </el-button>
                            </div>
                            <el-upload :limit="9" class="avatar-uploader" list-type="picture-card"
                                :action="path+'/fileUploadAndDownload/upload'" :headers="{'x-token':token}"
                                :on-success="handleGoodsDetailImagesSuccess" :on-remove="removeGoodsDetailImagesList"
                                :before-upload="beforeAvatarUpload" :file-list="goodsDetailImagesFileList"
                                accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                                <i class="el-icon-plus avatar-uploader-icon"></i>
                            </el-upload>
                            <p class="sug-p">
                                最多上传9个素材,单张图片需限制在10M以内
                            </p>
                        </el-form-item> -->
                    </el-tab-pane>
                    <el-tab-pane label="商品参数" name="3">
                        <!-- 商品参数 -->
                        <el-table :data="goodsParamList" border>
                            <el-table-column label="属性名称">
                                <template slot-scope="scope">
                                    <el-input
                                        v-model="scope.row.name"
                                    ></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="属性值">
                                <template slot-scope="scope">
                                    <el-input
                                        v-model="scope.row.value"
                                    ></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="操作"
                                width="100"
                                align="center"
                            >
                                <template slot-scope="scope">
                                    <!-- <el-popconfirm title="确定删除吗？" class="ml10" @confirm="delGoodsParam(scope.$index)"> -->
                                    <el-button
                                        type="text"
                                        slot="reference"
                                        class="color-red"
                                        @click="delGoodsParam(scope.$index)"
                                        >删除
                                    </el-button>
                                    <!-- </el-popconfirm> -->
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="add-btn-box">
                            <el-button type="primary" @click="addGoodsParam"
                                >添加属性
                            </el-button>
                            <el-button @click="goodsParamList = []"
                                >清空
                            </el-button>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="销售属性" name="4">
                        <!-- 销售属性 -->
                        <div class="sale-box">
                            <div>
                                <el-form-item>
                                    <span slot="label"
                                        ><span class="color-red">*</span
                                        >商品单位:</span
                                    >
                                    <!-- <el-input
                                        class="w287"
                                        v-model="goodsUnit"
                                    ></el-input> -->
                                    <el-select
                                        v-model="goodsUnit"
                                        style="width: 288px"
                                        filterable
                                        clearable
                                        placeholder="请选择"
                                    >
                                        <el-option
                                            v-for="item in goodsUnitList"
                                            :key="item.title"
                                            :label="item.title"
                                            :value="item.title"
                                        >
                                        </el-option>
                                    </el-select>
                                    <el-tooltip style="margin-left: 10px">
                                        <div slot="content">
                                            搜索的商品单位不存在可以手动添加；
                                        </div>
                                        <el-button
                                            type="text"
                                            icon="el-icon-question"
                                            class="tool-btn"
                                        ></el-button>
                                    </el-tooltip>
                                </el-form-item>
                                <el-form-item>
                                    <span slot="label">最小起订量:</span>
                                    <el-input-number
                                        class="w287"
                                        :precision="0"
                                        :min="0"
                                        :controls="false"
                                        v-model="minBuyQty"
                                    >
                                    </el-input-number>
                                    <el-tooltip style="margin-left: 10px">
                                        <div slot="content">
                                            不满足最小起订量的，将无法下单；<br />为空、为零则不限制。
                                        </div>
                                        <el-button
                                            type="text"
                                            icon="el-icon-question"
                                            class="tool-btn"
                                        ></el-button>
                                    </el-tooltip>
                                </el-form-item>

                                <el-form-item>
                                    <span slot="label"
                                        ><span class="color-red">*</span
                                        >属性类型:</span
                                    >
                                    <el-radio-group v-model="typeRadio">
                                        <el-radio :label="1"
                                            >单规格商品</el-radio
                                        >
                                        <el-radio :label="0"
                                            >多规格商品</el-radio
                                        >
                                    </el-radio-group>
                                </el-form-item>
                                <el-form-item
                                    v-if="typeRadio === 1"
                                    label="规格图片:"
                                >
                                    <el-upload
                                        class="avatar-uploader"
                                        :show-file-list="false"
                                        :action="path+'/fileUploadAndDownload/upload'"
                                        :headers="{'x-token':token}"
                                        :on-success="handleSingSkuImgSuccess"
                                        :before-upload="$fn.beforeAvatarUpload"
                                        accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF"
                                    >
                                        <img
                                            v-if="singleSkuImg"
                                            :src="singleSkuImg"
                                            class="avatar"
                                        />
                                        <i
                                            v-else
                                            class="el-icon-plus avatar-uploader-icon"
                                        ></i>
                                    </el-upload>
                                    <p class="color-grap">
                                        建议尺寸：800*800像素,图片需限制在{{$store.state.uploadLimitSize}}M以内
                                    </p>
                                </el-form-item>
                                <el-form-item
                                    v-if="typeRadio === 1"
                                    label="商品编号:"
                                >
                                    <el-input
                                        :controls="false"
                                        class="w287"
                                        v-model="sn"
                                    ></el-input>
                                </el-form-item>
                                <el-form-item
                                    v-if="typeRadio === 1"
                                    label="商品条码:"
                                >
                                    <el-input
                                        :controls="false"
                                        class="w287"
                                        v-model="code"
                                    ></el-input>
                                </el-form-item>
                                <el-form-item v-if="typeRadio === 1">
                                    <span slot="label"
                                        ><span class="color-red">*</span
                                        >商品供货价:</span
                                    >
                                    <el-input-number
                                        class="w287"
                                        :precision="2"
                                        :min="0"
                                        :controls="false"
                                        v-model="goodsPrice"
                                        :disabled="priceIsEdit === 1"
                                    >
                                    </el-input-number>
                                    <span style="margin-left: 10px">元</span>
                                    <el-tooltip>
                                        <div slot="content">
                                            无会员折扣下，卖给中台会员的价格（包括采购端绑定会员和pc端会员）
                                        </div>
                                        <el-button
                                            type="text"
                                            icon="el-icon-question"
                                            class="tool-btn"
                                        ></el-button>
                                    </el-tooltip>
                                </el-form-item>
                                <el-form-item v-if="typeRadio === 1">
                                    <span slot="label"
                                        ><span class="color-red">*</span
                                        >建议零售价:</span
                                    >
                                    <el-input-number
                                        class="w287"
                                        :precision="2"
                                        :min="0"
                                        :controls="false"
                                        v-model="goodsOriginPrice"
                                        :disabled="priceIsEdit === 1"
                                    >
                                    </el-input-number>
                                    <span style="margin-left: 10px">元</span>
                                    <el-tooltip>
                                        <div slot="content">
                                            指该品牌厂家制定的线上线下零售指导价格，此价格作为线上实际销
                                            售价格对比，参考作用。
                                        </div>
                                        <el-button
                                            type="text"
                                            icon="el-icon-question"
                                            class="tool-btn"
                                        ></el-button>
                                    </el-tooltip>
                                </el-form-item>
                                <el-form-item v-if="typeRadio === 1">
                                    <span slot="label"
                                        ><span class="color-red">*</span
                                        >商品成本价:</span
                                    >
                                    <el-input-number
                                        class="w287"
                                        :precision="2"
                                        :min="0"
                                        :controls="false"
                                        v-model="goodsCostPrice"
                                        :disabled="priceIsEdit === 1"
                                    >
                                    </el-input-number>
                                    <span style="margin-left: 10px">元</span>
                                    <el-tooltip>
                                        <div slot="content">
                                            中台进货的价格，统计中台利润时使用
                                        </div>
                                        <el-button
                                            type="text"
                                            icon="el-icon-question"
                                            class="tool-btn"
                                        ></el-button>
                                    </el-tooltip>
                                </el-form-item>
                                <el-form-item v-if="typeRadio === 1">
                                    <span slot="label"
                                        ><span class="color-red">*</span
                                        >商品指导价:</span
                                    >
                                    <el-input-number
                                        class="w287"
                                        :precision="2"
                                        :min="0"
                                        :controls="false"
                                        v-model="goodsGuidePrice"
                                        :disabled="priceIsEdit === 1"
                                    >
                                    </el-input-number>
                                    <span style="margin-left: 10px">元</span>
                                    <el-tooltip>
                                        <div slot="content">
                                            中台建议采购端销售的价格
                                        </div>
                                        <el-button
                                            type="text"
                                            icon="el-icon-question"
                                            class="tool-btn"
                                        ></el-button>
                                    </el-tooltip>
                                </el-form-item>
                                <el-form-item v-if="typeRadio === 1">
                                    <span slot="label"
                                        ><span class="color-red">*</span
                                        >商品营销价:</span
                                    >
                                    <el-input-number
                                        class="w287"
                                        :precision="2"
                                        :min="0"
                                        :controls="false"
                                        v-model="goodsActivityPrice"
                                        :disabled="priceIsEdit === 1"
                                    >
                                    </el-input-number>
                                    <span style="margin-left: 10px">元</span>
                                    <el-tooltip>
                                        <div slot="content">
                                            指同一商品在阿里、京东、淘宝、天猫等对应电商平台促销时段
                                            <br />如 双 11、双
                                            12、618、年货节等营销节日让利价格，或参与秒杀、砍价、
                                            拼团、新品发布等营销活动短时促销价格。（目前只有京东有营销价）
                                        </div>
                                        <el-button
                                            type="text"
                                            icon="el-icon-question"
                                            class="tool-btn"
                                        ></el-button>
                                    </el-tooltip>
                                </el-form-item>
                                <el-form-item v-if="typeRadio === 1">
                                    <span slot="label"
                                        ><span class="color-red">*</span
                                        >库存:</span
                                    >
                                    <el-input-number
                                        class="w287"
                                        :precision="0"
                                        :controls="false"
                                        v-model="goodsStock"
                                    >
                                    </el-input-number>
                                </el-form-item>
                                <el-form-item v-if="typeRadio === 1">
                                    <span slot="label"
                                        ><span class="color-red">*</span
                                        >重量:</span
                                    >
                                    <el-input-number
                                        class="w287"
                                        :precision="0"
                                        :min="0"
                                        :controls="false"
                                        v-model="goodsWeight"
                                    >
                                    </el-input-number>
                                    <span style="margin-left: 10px">g</span>
                                </el-form-item>
                                <!-- 无规格部分 -->
                                <!-- <div v-if="typeRadio === 1">
                                    <el-form-item label="商品编码:">
                                        <el-input class="w287" v-model="goodsSn"></el-input>
                                    </el-form-item>
                                    <el-form-item label="商品条形码:">
                                        <el-input class="w287" v-model="goodsBarcode"></el-input>
                                    </el-form-item>
                                    <el-form-item label="商品重量:">
                                        <el-input-number :min="0" :precision="2" :controls="false" class="w287"
                                            v-model="goodsWeight">
                                        </el-input-number>
                                        <span style="margin-left: 10px">克</span>

                                    </el-form-item>
                                    <el-form-item label="商品体积:">
                                        <el-input-number :min="0" :precision="3" :controls="false" class="w287"
                                            v-model="goodsVolume">
                                        </el-input-number>
                                        <span style="margin-left: 10px">立方米</span>

                                    </el-form-item>
                                </div> -->
                            </div>
                            <!-- 多规格部分 -->
                            <div class="multi-spe-box" v-if="typeRadio === 0">
                                <div class="form-padding">
                                    <!-- 添加规格 循环 -->
                                    <div v-show="speIsShow">
                                        <div
                                            v-for="(item,index) in spec"
                                            :key="item.id"
                                            class="spe-box-item"
                                        >
                                            <i
                                                class="el-icon-error close-i"
                                                @click="deleteSpecItem(index)"
                                            ></i>
                                            <el-form-item
                                                style="padding-right: 24px"
                                            >
                                                <span slot="label"
                                                    ><span class="color-red"
                                                        >*</span
                                                    >规格名:</span
                                                >
                                                <el-input
                                                    v-model="item.name"
                                                    @change="specNameVerify(index,item.name)"
                                                >
                                                </el-input>
                                            </el-form-item>
                                            <el-form-item>
                                                <span slot="label"
                                                    ><span class="color-red"
                                                        >*</span
                                                    >规格项:</span
                                                >
                                                <el-row>
                                                    <el-col
                                                        :span="6"
                                                        class="mr10"
                                                        style="padding-top: 5px"
                                                        v-for="(option,index2) in item.options"
                                                        :key="item.id"
                                                    >
                                                        <el-input
                                                            class="item-spe-input"
                                                            v-model="option.name"
                                                            @change="specOptionNameVerify(index,index2,option.name)"
                                                        >
                                                            <el-button
                                                                slot="append"
                                                                icon="el-icon-close"
                                                                @click="deleteSpecSubdataItem(index,index2,option)"
                                                            >
                                                            </el-button>
                                                        </el-input>
                                                    </el-col>
                                                    <el-col :span="3">
                                                        <el-button
                                                            type="primary"
                                                            @click="addSpecSubdataItem(index,'')"
                                                        >
                                                            添加规格项
                                                        </el-button>
                                                    </el-col>
                                                </el-row>
                                            </el-form-item>
                                        </div>
                                    </div>
                                    <el-form-item label-width="130px">
                                        <el-button
                                            type="primary"
                                            :disabled="spec.length <= 1?false:true"
                                            @click="addSpecItem('')"
                                        >
                                            添加规格
                                        </el-button>
                                        <el-button @click="assemblySpec()">
                                            刷新规格项目表
                                        </el-button>
                                    </el-form-item>
                                    <el-form-item label-width="130px">
                                        <p class="color-red">
                                            注:
                                            新增或删除规格项需重新填写发票信息
                                        </p>
                                    </el-form-item>
                                </div>
                                <table
                                    cellspacing="0"
                                    cellpadding="0"
                                    width="100%"
                                    class="spe-table"
                                >
                                    <thead>
                                        <tr>
                                            <td
                                                :colspan="spec.length+12"
                                                class="text-left"
                                            >
                                                <div
                                                    class="f fac batck-btn-box"
                                                >
                                                    <span class="batch-span"
                                                        >批量操作:
                                                    </span>
                                                    <template
                                                        v-if="batchInputIsShow"
                                                    >
                                                        <el-input-number
                                                            class="w120"
                                                            :min="0"
                                                            :precision="batchPrecision"
                                                            :controls="false"
                                                            size="mini"
                                                            v-model="batchInputValue"
                                                        ></el-input-number>
                                                        <el-button
                                                            type="text"
                                                            class="conf-btn"
                                                            @click="confirmBatch"
                                                            >确定
                                                        </el-button>
                                                        <el-button
                                                            type="text"
                                                            class="conf-btn"
                                                            @click="cancelBatch"
                                                            >取消
                                                        </el-button>
                                                    </template>
                                                    <template v-else>
                                                        <el-button
                                                            type="text"
                                                            @click="openatchInput('batchStock')"
                                                            >库存
                                                        </el-button>
                                                        <el-button
                                                            type="text"
                                                            @click="openatchInput('batchOriginPrice')"
                                                        >
                                                            建议零售价
                                                        </el-button>
                                                        <el-button
                                                            type="text"
                                                            @click="openatchInput('batchPrice')"
                                                            >供货价
                                                        </el-button>
                                                        <el-button
                                                            type="text"
                                                            @click="openatchInput('batchCostPrice')"
                                                        >
                                                            成本价格
                                                        </el-button>
                                                        <el-button
                                                            type="text"
                                                            @click="openatchInput('batchGuidePrice')"
                                                            >指导价
                                                        </el-button>
                                                        <el-button
                                                            type="text"
                                                            @click="openatchInput('batchActivityPrice')"
                                                            >营销价
                                                        </el-button>
                                                        <el-button
                                                            type="text"
                                                            @click="openatchInput('batchWeight')"
                                                            >重量
                                                        </el-button>
                                                        <!-- <el-button type="text" @click="openatchInput('batchVolume')">体积
                                                </el-button> -->
                                                    </template>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td v-for="item in spec">
                                                {{ item.name }}
                                            </td>
                                            <td>
                                                <p>图片</p>
                                            </td>
                                            <td width="10%">
                                                <p>标题</p>
                                            </td>
                                            <td width="10%">
                                                <p>库存</p>
                                            </td>
                                            <td width="10%">预扣库存</td>
                                            <td width="10%">
                                                <p>
                                                    <span class="color-red"
                                                        >*</span
                                                    >
                                                    建议零售价
                                                </p>
                                            </td>
                                            <td width="10%">
                                                <p>
                                                    <span class="color-red"
                                                        >*</span
                                                    >
                                                    供货价
                                                </p>
                                            </td>
                                            <td width="10%">
                                                <p>
                                                    <span class="color-red"
                                                        >*</span
                                                    >
                                                    成本价格
                                                </p>
                                            </td>
                                            <td width="10%">
                                                <p>
                                                    <span class="color-red"
                                                        >*</span
                                                    >
                                                    指导价
                                                </p>
                                            </td>
                                            <td width="10%">
                                                <p>
                                                    营销价
                                                    <span
                                                        class="color-red"
                                                    ></span>
                                                </p>
                                                <p>
                                                    重量(克)
                                                    <span
                                                        class="color-red"
                                                    ></span>
                                                </p>
                                            </td>
                                            <!-- <td width="10%">
                                        <p>商品编码 <span class="color-red"></span></p>
                                    </td> -->
                                            <td width="10%">
                                                <p>
                                                    商品编码
                                                    <span
                                                        class="color-red"
                                                    ></span>
                                                </p>
                                                <p>
                                                    商品条码
                                                    <span
                                                        class="color-red"
                                                    ></span>
                                                </p>
                                            </td>
                                            <!--                                        <td width="10%">
                                                                                    <p>重量(克) <span class="color-red"></span></p>
                                                                                </td>-->
                                            <td width="10%">
                                                <p>
                                                    操作
                                                    <span
                                                        class="color-red"
                                                    ></span>
                                                </p>
                                            </td>
                                            <!-- <td width="10%">
                                        <p>体积(m²) <span class="color-red"></span></p>
                                    </td> -->
                                        </tr>
                                    </thead>

                                    <tbody>
                                        <tr
                                            v-for="(item,index) in skus"
                                            :key="item.id"
                                        >
                                            <td
                                                v-for="(a,index3) in spec"
                                                rowspan="1"
                                            >
                                                {{ getViewName(index3,
                                                item.optionName) }}
                                            </td>
                                            <td rowspan="1">
                                                <el-upload
                                                    class="sku_avatar-uploader"
                                                    :show-file-list="false"
                                                    :action="path + '/fileUploadAndDownload/upload'"
                                                    :headers="{ 'x-token': token }"
                                                    :on-success="function (res,file) {return handleSkusImgSuccess(res,file,index)}"
                                                    :before-upload="$fn.beforeAvatarUpload"
                                                    accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF"
                                                >
                                                    <img
                                                        v-if="item.image_url"
                                                        :src="item.image_url"
                                                        class="avatar"
                                                    />
                                                    <i
                                                        v-else
                                                        class="el-icon-plus avatar-uploader-icon"
                                                    ></i>
                                                </el-upload>
                                                <p
                                                    class="color-grap"
                                                    style="margin-top: 5px"
                                                >
                                                    建议:800*800
                                                </p>
                                            </td>
                                            <td rowspan="1">
                                                <el-input
                                                    :placeholder="item.optionName"
                                                    v-model="item.title"
                                                >
                                                </el-input>
                                            </td>
                                            <td rowspan="1">
                                                <el-input-number
                                                    :min="0"
                                                    :controls="false"
                                                    v-model="item.stock"
                                                >
                                                </el-input-number>
                                            </td>
                                            <td rowspan="1">
                                                <el-input
                                                    disabled
                                                    v-if="item.lock_stock && item.lock_stock > 0"
                                                    v-model="item.lock_stock"
                                                ></el-input>
                                                <el-input
                                                    disabled
                                                    v-else
                                                ></el-input>
                                            </td>
                                            <td rowspan="1">
                                                <el-input-number
                                                    :min="0"
                                                    :precision="2"
                                                    :controls="false"
                                                    v-model="item.origin_price"
                                                    :disabled="priceIsEdit === 1"
                                                >
                                                </el-input-number>
                                            </td>
                                            <td rowspan="1">
                                                <el-input-number
                                                    :min="0"
                                                    :precision="2"
                                                    :controls="false"
                                                    v-model="item.price"
                                                    :disabled="priceIsEdit === 1"
                                                >
                                                </el-input-number>
                                            </td>
                                            <td rowspan="1">
                                                <el-input-number
                                                    :min="0"
                                                    :precision="2"
                                                    :controls="false"
                                                    v-model="item.cost_price"
                                                    :disabled="priceIsEdit === 1"
                                                >
                                                </el-input-number>
                                            </td>
                                            <td rowspan="1">
                                                <el-input-number
                                                    :min="0"
                                                    :precision="2"
                                                    :controls="false"
                                                    v-model="item.guide_price"
                                                    :disabled="priceIsEdit === 1"
                                                >
                                                </el-input-number>
                                            </td>
                                            <td rowspan="1">
                                                <el-input-number
                                                    :min="0"
                                                    :precision="2"
                                                    :controls="false"
                                                    v-model="item.activity_price"
                                                    placeholder="营销价"
                                                    :disabled="priceIsEdit === 1"
                                                >
                                                </el-input-number>
                                                <el-input-number
                                                    :precision="0"
                                                    :min="0"
                                                    :controls="false"
                                                    placeholder="重量"
                                                    v-model="item.weight"
                                                >
                                                </el-input-number>
                                            </td>
                                            <!-- <td rowspan="1">
                                        <el-input v-model="item.commodity"></el-input>
                                    </td> -->
                                            <td rowspan="1">
                                                <el-input
                                                    v-model="item.sn"
                                                    style="margin-bottom: 5px"
                                                    placeholder="商品编码"
                                                ></el-input>
                                                <el-input
                                                    v-model="item.code"
                                                    placeholder="商品条码"
                                                ></el-input>
                                            </td>
                                            <!--                                        <td rowspan="1">
                                                                                    <el-input-number :precision="0" :min="0" :controls="false"
                                                                                                     v-model="item.weight">
                                                                                    </el-input-number>
                                                                                </td>-->
                                            <td rowspan="1">
                                                <el-button
                                                    type="text"
                                                    @click="editDescribe(item,index)"
                                                    >规格介绍
                                                </el-button>
                                            </td>
                                            <!-- <td rowspan="1">
                                        <el-input-number :min="0" :precision="3" :controls="false"
                                            v-model="item.volume">
                                        </el-input-number>
                                    </td> -->
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="商品特殊资质" name="5">
                        <!-- 商品特殊资质 -->
                        <!-- <quill-editor ref="myTextEditor" v-model="goodsAffiche" :options="editorOption"
                          style="height:400px;width: 100%;padding-bottom: 100px"></quill-editor> -->
                        <span style="display: none">{{ goodsAffiche }}</span>
                        <div
                            class="qualifications-row"
                            v-for="(item,index) in goodsAffiche"
                            :key="item.id"
                        >
                            <el-form-item>
                                <span slot="label"
                                    ><span class="color-red">*</span
                                    >特殊资质名称:</span
                                >
                                <el-row :gutter="10" style="padding: 0">
                                    <el-col :span="6">
                                        <el-input
                                            v-model="item.title"
                                        ></el-input>
                                    </el-col>
                                    <el-col :span="6" class="f fac">
                                        <el-upload
                                            :show-file-list="false"
                                            :action="path+'/fileUploadAndDownload/upload'"
                                            :headers="{'x-token':token}"
                                            :data="{index:index}"
                                            :on-success="handleGoodsAfficheSuccess"
                                            :before-upload="$fn.beforeAvatarUpload"
                                            accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF"
                                        >
                                            <el-button>上传图片</el-button>
                                        </el-upload>
                                        <el-button
                                            class="ml10"
                                            @click="removeItem(index)"
                                            >移除</el-button
                                        >
                                    </el-col>
                                </el-row>
                                <p class="color-grap">
                                    建议尺寸宽度：900像素，图片需限制在{{$store.state.uploadLimitSize}}M以内
                                </p>
                                <div class="qualifications-img-box">
                                    <img v-if="item.url" :src="item.url" />
                                </div>
                            </el-form-item>
                        </div>

                        <div class="add-btn-box">
                            <!-- <el-button icon="el-icon-plus" class="btn2" @click="addGoodsAffiche">添加特殊资质
                            </el-button> -->
                            <el-button type="primary" @click="addGoodsAffiche"
                                >添加特殊资质
                            </el-button>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="发票管理" name="6">
                        <el-row>
                            <!-- 新增部分 -->
                            <el-col :span="24">
                                <el-form-item
                                    label="赋码方式:"
                                    label-width="80px"
                                >
                                    <el-radio-group v-model="bill_position">
                                        <el-radio
                                            :label="1"
                                            :disabled="typeRadio === 0"
                                            >按商品赋码</el-radio
                                        >
                                        <el-radio
                                            :label="2"
                                            :disabled="typeRadio === 1"
                                            >按规格赋码</el-radio
                                        >
                                    </el-radio-group>
                                </el-form-item>
                            </el-col>
                            <!-- 修改部分 -->
                            <template v-if="bill_position === 1">
                                <el-col :span="8" class="tree-box">
                                    <div class="f fac">
                                        <el-input
                                            size="small"
                                            v-model="treeInput"
                                        ></el-input>
                                        <el-button
                                            size="small"
                                            type="primary"
                                            @click="searchTree"
                                            >搜索</el-button
                                        >
                                    </div>
                                    <el-tree
                                        :filter-node-method="filterNode"
                                        highlight-current
                                        ref="tree"
                                        class="mt10"
                                        :data="treeData"
                                        :props="defaultProps"
                                        @node-click="handleNodeClick"
                                    >
                                    </el-tree>
                                </el-col>
                                <el-col :span="16" class="right-form-box">
                                    <el-row>
                                        <el-col :span="12">
                                            <el-form-item label="税收分类编码:">
                                                <el-input
                                                    v-model="tax_code"
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="发票商品名称:">
                                                <el-input
                                                    v-model="tax_product_name"
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="税收分类简称:">
                                                <el-input
                                                    v-model="tax_short_name"
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="商品简码:">
                                                <el-input
                                                    v-model="short_code"
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <!--                                    <el-col :span="12">
                                                                                <el-form-item label="规格型号:">
                                                                                    <el-input v-model="tax_option"></el-input>
                                                                                </el-form-item>
                                                                            </el-col>-->
                                        <!--                                    <el-col :span="12">
                                                                                <el-form-item label="计量单位(元):">
                                                                                    <el-input-number v-model="goodsPrice" :controls="false" :min="0"
                                                                                                     :precision="2"
                                                                                                     class="w100 input-number-text-left"></el-input-number>
                                                                                </el-form-item>
                                                                            </el-col>-->
                                        <!--                                    <el-col :span="12">
                                                                                <el-form-item label="单位:">
                                                                                    <el-input v-model="tax_unit"></el-input>
                                                                                </el-form-item>
                                                                            </el-col>-->
                                        <el-col :span="12">
                                            <el-form-item label="使用优惠政策:">
                                                <el-radio-group
                                                    v-model="is_favorable_policy"
                                                >
                                                    <el-radio :label="1"
                                                        >是</el-radio
                                                    >
                                                    <el-radio :label="0"
                                                        >否</el-radio
                                                    >
                                                </el-radio-group>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item>
                                                <span slot="label"
                                                    ><span
                                                        class="color-red"
                                                        v-if="is_favorable_policy === 1"
                                                        >* </span
                                                    >优惠政策类型:</span
                                                >
                                                <el-select
                                                    v-model="favorable_policy"
                                                    clearable
                                                    class="w100"
                                                >
                                                    <el-option
                                                        v-for="item in favorablePolicyOptios"
                                                        :key="item.id"
                                                        :label="item"
                                                        :value="item"
                                                    ></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item>
                                                <span slot="label"
                                                    ><span class="color-red"
                                                        >*</span
                                                    >税率:</span
                                                >
                                                <div class="f fac">
                                                    <!--                                                tax_rate-->
                                                    <el-select
                                                        v-model="tax_rate"
                                                    >
                                                        <el-option
                                                            v-for="item in taxRateOptios"
                                                            :key="item.id"
                                                            :label="item.label"
                                                            :value="item.value"
                                                        ></el-option>
                                                    </el-select>
                                                </div>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item>
                                                <span slot="label"
                                                    ><span class="color-red"
                                                        >*</span
                                                    >免税类型:</span
                                                >
                                                <el-select
                                                    v-model="free_of_tax"
                                                    class="w100"
                                                >
                                                    <el-option
                                                        v-for="item in freeOfTaxOptions"
                                                        :key="item.id"
                                                        :label="item.label"
                                                        :value="item.value"
                                                    ></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="含税标志:">
                                                <el-radio-group
                                                    v-model="is_tax_logo"
                                                >
                                                    <el-radio :label="1"
                                                        >含税</el-radio
                                                    >
                                                    <el-radio :label="0"
                                                        >不含税</el-radio
                                                    >
                                                </el-radio-group>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-col>
                            </template>
                            <!-- 新增部分 -->
                            <template v-if="bill_position === 2">
                                <el-col>
                                    <el-form-item
                                        label="批量操作"
                                        label-width="80px"
                                    >
                                        <el-button
                                            type="text"
                                            @click="openCodeTypeDialog('all')"
                                            >赋码</el-button
                                        >
                                    </el-form-item>
                                </el-col>
                                <table
                                    cellspacing="0"
                                    cellpadding="0"
                                    class="bill-table"
                                >
                                    <thead>
                                        <tr>
                                            <td v-for="item in spec">
                                                {{ item.name }}
                                            </td>
                                            <td>
                                                <p>标题</p>
                                            </td>
                                            <td>
                                                <p>赋码状态</p>
                                            </td>
                                            <td>
                                                <p>发票赋码</p>
                                            </td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr
                                            v-for="(item,index) in skus"
                                            :key="item.id"
                                        >
                                            <td
                                                v-for="(a,index3) in spec"
                                                rowspan="1"
                                            >
                                                {{ getRowName(index3,
                                                item.optionName) }}
                                            </td>
                                            <td rowspan="1">
                                                <el-input
                                                    :placeholder="item.optionName"
                                                    v-model="item.title"
                                                >
                                                </el-input>
                                            </td>
                                            <td>
                                                <span
                                                    >{{ item.tax_code != '' ?
                                                    '已赋码' : '未赋码' }}</span
                                                >
                                            </td>
                                            <td>
                                                <el-button
                                                    type="text"
                                                    @click="openCodeTypeDialog(index)"
                                                    >赋码</el-button
                                                >
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </template>
                        </el-row>
                    </el-tab-pane>
                    <el-tab-pane
                        label="视频号"
                        name="7"
                        v-if="goodsId && plugin_widget"
                    >
                        <el-row>
                            <el-col :span="16">
                                <!--                                <el-form-item>
                                                                    <span slot="label">已选分类/品牌 <span class="color-red">*</span></span>
                                                                    <p>{{ goodsClassifyStr }}</p>
                                                                </el-form-item>-->
                                <el-form-item label="视频号:">
                                    <el-switch
                                        v-model="is_open"
                                        :active-value="1"
                                        :inactive-value="0"
                                    >
                                    </el-switch>
                                </el-form-item>
                                <el-form-item>
                                    <span slot="label"
                                        ><span class="color-red">*</span
                                        >商品类目:</span
                                    >
                                    <el-form-item>
                                        <el-select
                                            filterable
                                            v-model="smallClassifyCheck1"
                                            @change="smallGetClassify2o3(2,smallClassifyCheck1)"
                                        >
                                            <el-option
                                                v-for="item in smallClassifyList1"
                                                :key="item.id"
                                                :label="item.name"
                                                :value="item.cat_id"
                                            >
                                            </el-option>
                                        </el-select>
                                        <el-select
                                            filterable
                                            class="ml10"
                                            clearable
                                            v-model="smallClassifyCheck2"
                                            @change="smallGetClassify2o3(3,smallClassifyCheck2)"
                                        >
                                            <el-option
                                                v-for="item in smallClassifyList2"
                                                :key="item.id"
                                                :label="item.name"
                                                :value="item.cat_id"
                                            >
                                            </el-option>
                                        </el-select>
                                        <el-select
                                            filterable
                                            class="ml10"
                                            clearable
                                            v-model="smallClassifyCheck3"
                                            @change="getproductargments(smallClassifyCheck3)"
                                        >
                                            <el-option
                                                v-for="item in smallClassifyList3"
                                                :key="item.id"
                                                :label="item.name"
                                                :value="item.cat_id"
                                            >
                                            </el-option>
                                        </el-select>
                                        <el-button
                                            type="primary"
                                            style="margin-left: 10px"
                                            @click="synchronizeClick()"
                                            >同步分类
                                        </el-button>
                                    </el-form-item>

                                    <p class="color-grap">
                                        选择视频号类目，小商店选品后，如果绑定的视频号没有这个类目就不能推送商品给视频号！
                                    </p>
                                </el-form-item>
                                <el-form-item label="是否需要发货:">
                                    <el-radio-group
                                        v-model="is_videodisplay"
                                        :disabled="shop_no_shipment === true"
                                    >
                                        <el-radio :label="0">需要</el-radio>
                                        <el-radio :label="1">不需要</el-radio>
                                    </el-radio-group>
                                    <div v-if="is_videodisplay == 0">
                                        <span>运费模板</span>
                                        <el-select
                                            v-model="videofreight_template_id"
                                            class="ml10 w200"
                                        >
                                            <el-option
                                                v-for="item in smallShopVideofreightgetData"
                                                :key="item.id"
                                                :label="item.name"
                                                :value="item.template_id"
                                            ></el-option>
                                        </el-select>
                                        <el-button
                                            style="margin-left: 10px"
                                            type="primary"
                                            @click="synctemplateClick()"
                                            >同步运费模板
                                        </el-button>
                                        <!--                                            <el-button type="text" class="ml10" @click="addTemp">新建</el-button>-->
                                        <!--                                            <el-button type="text" class="ml10" :loading="btnLoadIsShow"-->
                                        <!--                                                       @click="getFreightTemp(true)">刷新-->
                                        <!--                                            </el-button>-->
                                        <br />
                                        <!--                                        <el-radio v-model="goodsFreightType" :disabled="goodsFreightType === '2'" label="3"-->
                                        <!--                                                  class="mt10">-->
                                        <!--                                            <span>包邮</span>-->
                                        <!--                                        </el-radio>-->
                                        <!--                                        <br>-->
                                        <!--                                        <el-radio v-if="goodsFreightType === '2'" v-model="goodsFreightType" label="2"-->
                                        <!--                                                  class="mt10">-->
                                        <!--                                            <span>第三方运费</span>-->
                                        <!--                                        </el-radio>-->
                                    </div>
                                    <p class="color-grap">
                                        如果类目要求必须要发货的，不可选！
                                    </p>
                                </el-form-item>
                                <el-form-item label="商品品牌:">
                                    <el-select
                                        v-model="smallVideobrand_id"
                                        clearable
                                        filterable
                                        :filter-method="smallhandleFilter"
                                        @visible-change="smallgetBrandsOptiospp"
                                    >
                                        <el-option
                                            v-for="item in smallbrandsOptiosData.brandsOptios"
                                            :key="item.id"
                                            :label="item.ch_name"
                                            :value="item.brand_id"
                                        ></el-option>
                                        <div class="text-center">
                                            <el-pagination
                                                background
                                                small
                                                class="pagination"
                                                style="
                                                    padding-top: 10px !important;
                                                    padding-bottom: 0 !important;
                                                "
                                                :current-page="smallbrandsOptiosData.page"
                                                :page-size="smallbrandsOptiosData.pageSize"
                                                :total="smallbrandsOptiosData.total"
                                                @current-change="smallhandleBrandPage"
                                                layout="prev,pager, next"
                                            />
                                        </div>
                                    </el-select>
                                    <el-button
                                        style="margin-left: 10px"
                                        type="primary"
                                        @click="synchbrandClick()"
                                        >同步品牌
                                    </el-button>
                                </el-form-item>
                                <el-form-item
                                    label="商品规格规范:"
                                    v-if="saleAttrList && saleAttrList.length"
                                >
                                    <el-table :data="saleAttrList">
                                        <el-table-column
                                            label="规格名"
                                            align="center"
                                            prop="name"
                                        ></el-table-column>
                                        <el-table-column
                                            label="可选参数"
                                            align="center"
                                        >
                                            <template slot-scope="scope">
                                                <p
                                                    v-if="scope.row.type === 'string'"
                                                >
                                                    <span v-if="scope.row.value"
                                                        >{{scope.row.value}}</span
                                                    >
                                                    <span v-else
                                                        >自行填写,不做限制</span
                                                    >
                                                </p>
                                                <p v-else>
                                                    {{scope.row.value}}
                                                </p>
                                            </template>
                                        </el-table-column>
                                        <el-table-column
                                            label="规格是否必须"
                                            align="center"
                                        >
                                            <template slot-scope="scope">
                                                <p v-if="scope.row.is_required">
                                                    是
                                                </p>
                                                <p v-else>否</p>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                    <p class="color-grap mt_10">
                                        商品规格必须符合上述规范，上传至视频号小店才可以通过审核
                                    </p>
                                </el-form-item>
                                <el-form-item label="商品参数:">
                                    <el-input
                                        style="margin-bottom: 10px"
                                        placeholder="请输入内容"
                                        v-if="item.type == 'string'"
                                        v-model="item.value"
                                        v-for="(item,index) in productArgamentsData "
                                    >
                                        <template slot="prepend"
                                            >{{item.name}}</template
                                        >
                                    </el-input>
                                    <div
                                        class="selectbox"
                                        style="display: flex"
                                        v-if="item.type === 'select_one'"
                                        v-for="(item,index) in productArgamentsArr"
                                    >
                                        <div
                                            class="tabsaa"
                                            style="
                                                margin-bottom: 10px;
                                                display: inline-block;
                                                background-color: #f5f7fa;
                                                color: #909399;
                                                position: relative;
                                                vertical-align: middle;
                                                display: table-cell;
                                                border: 1px solid #dcdfe6;
                                                border-radius: 0;
                                                padding: 0 20px;
                                                height: 34px;
                                                white-space: nowrap;
                                            "
                                        >
                                            {{item.name}}
                                        </div>
                                        <el-select
                                            style="display: block"
                                            v-model="editData[index]"
                                            clearable
                                            filterable
                                            :placeholder="'请选择'"
                                            :filter-method="smallhandleFilter"
                                            @visible-change="smallgetBrandsOptios"
                                        >
                                            <el-option
                                                v-for="(item2,index2) in item.value"
                                                :key="index2"
                                                :label="item2"
                                                :value="item2"
                                            ></el-option>
                                        </el-select>
                                    </div>
                                </el-form-item>
                                <el-form-item>
                                    <span slot="label"
                                        ><span class="color-red">*</span
                                        >资质主图::</span
                                    >
                                    <div class="f fac">
                                        <p>
                                            ({{ videogoodsImageUrlList.length
                                            }}/5)
                                        </p>
                                    </div>
                                    <div class="f fw">
                                        <div
                                            class="productImgBox"
                                            v-for="(item,index) in videogoodsImageUrlList"
                                            :key="item.id"
                                        >
                                            <m-image
                                                :src="item.url"
                                                style="
                                                    width: 180px;
                                                    height: 180px;
                                                "
                                            />
                                            <div class="del-box">
                                                <el-button
                                                    type="text"
                                                    icon="el-icon-delete"
                                                    @click="zizhiremoveGoodsGalleryList(index)"
                                                ></el-button>
                                            </div>
                                        </div>
                                        <el-upload
                                            v-if="videogoodsImageUrlList.length < 5"
                                            class="avatar-uploader"
                                            multiple
                                            list-type="picture-card"
                                            :action="path+'/fileUploadAndDownload/upload'"
                                            :headers="{'x-token':token}"
                                            :on-success="zizhihandleGoodsGallerySuccess"
                                            :before-upload="$fn.beforeAvatarUpload"
                                            :show-file-list="false"
                                            accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF"
                                        >
                                            <i
                                                class="el-icon-plus avatar-uploader-icon"
                                            ></i>
                                        </el-upload>
                                    </div>
                                    <p class="color-grap">最多选择5张</p>
                                </el-form-item>
                                <el-form-item>
                                    <span slot="label"
                                        ><span class="color-red">*</span
                                        >商品详情图片:</span
                                    >
                                    <div class="f fac">
                                        <p>
                                            ({{ detialproductphotoList.length
                                            }}/20)
                                        </p>
                                        <el-button
                                            type="text"
                                            class="ml10"
                                            @click="detailedimages()"
                                            >提取详情图片
                                        </el-button>
                                    </div>
                                    <div class="f fw">
                                        <div
                                            class="productImgBox"
                                            v-for="(item,index) in detialproductphotoList"
                                            :key="item.id"
                                        >
                                            <m-image
                                                :src="item.url"
                                                style="
                                                    width: 180px;
                                                    height: 180px;
                                                "
                                            />
                                            <div class="del-box">
                                                <el-button
                                                    type="text"
                                                    icon="el-icon-delete"
                                                    @click="xiangqingremoveGoodsGalleryList(index)"
                                                ></el-button>
                                            </div>
                                        </div>
                                        <el-upload
                                            v-if="detialproductphotoList.length < 20"
                                            class="avatar-uploader"
                                            multiple
                                            list-type="picture-card"
                                            :action="path+'/fileUploadAndDownload/upload'"
                                            :headers="{'x-token':token}"
                                            :on-success="detialhandleGoodsGallerySuccess"
                                            :before-upload="$fn.beforeAvatarUpload"
                                            :show-file-list="false"
                                            accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF"
                                        >
                                            <i
                                                class="el-icon-plus avatar-uploader-icon"
                                            ></i>
                                        </el-upload>
                                    </div>
                                    <p class="color-grap">最多选择20张</p>
                                </el-form-item>
                                <el-form-item label="商品详情文本:">
                                    <el-input
                                        type="textarea"
                                        v-model="videoproductDetial"
                                        :rows="5"
                                    ></el-input>
                                </el-form-item>
                                <!--                                <el-form-item>-->
                                <!--                                    <el-button style="margin-left:10px;" type="primary"  @click="synchronizesubmit()">提交-->
                                <!--                                    </el-button>-->
                                <!--                                </el-form-item>-->
                            </el-col>
                        </el-row>
                    </el-tab-pane>
                    <el-tab-pane label="会员价" name="8">
                        <el-row>
                            <el-col :span="16">
                                <el-form-item label="独立规则">
                                    <el-switch
                                        v-model="user_price_switch"
                                        :active-value="1"
                                        :inactive-value="0"
                                    ></el-switch>
                                    <p>
                                        开启后，商品的会员价按独立设置的规则计算，不走会员等级统一设置。
                                    </p>
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="计算方式">
                                    <el-radio-group
                                        v-model="user_price.method"
                                        @input="changeMethod"
                                    >
                                        <el-radio :label="1">折扣</el-radio>
                                        <el-radio
                                            :label="2"
                                            :disabled="typeRadio == 0"
                                            >固定金额</el-radio
                                        >
                                    </el-radio-group>
                                    <p>
                                        折扣计算规则：对应会员等级商品价格=供货价×折扣率；
                                    </p>
                                    <p>
                                        固定金额：对应会员等级商品价格为设置的金额。
                                    </p>
                                    <p>
                                        注意：如果是多规格商品，不支持固定金额方式设置，选择不可选！如果设置后，修改单规格商品为多规格商品的，会提示会员价设置有误，无法保存商品。
                                    </p>
                                    <p style="color: red">
                                        举例：如果选择折扣，建议折扣区间0.1~9.9；当设置为8折时，商品原价100元，最终价格等于100*0.8=80元。
                                    </p>
                                    <div
                                        class="f fac mb_10"
                                        v-for="(item,index) in user_price.levels"
                                        :key="index"
                                    >
                                        <div class="mr_10">{{ item.name }}</div>
                                        <el-input-number
                                            v-model="item.value"
                                            placeholder="请输入"
                                            :precision="2"
                                            :controls="false"
                                        ></el-input-number>
                                        <div
                                            class="ml_10"
                                            v-if="user_price.method === 1"
                                        >
                                            折
                                        </div>
                                        <div
                                            class="ml_10"
                                            v-if="user_price.method === 2"
                                        >
                                            元
                                        </div>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-tab-pane>
                </el-tabs>
            </el-form>
        </div>
    </m-card>
    <el-dialog
        title="商品详情图片"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :visible="dialogSortIsShow"
        width="50%"
        :before-close="dialogSortClose"
    >
        <draggable
            v-model="goodsGallery"
            chosenClass="chosen"
            forceFallback="true"
            group="people"
            animation="500"
            @start="onStart"
            @end="onEnd(1)"
            class="draggable-box"
        >
            <transition-group>
                <div
                    class="item"
                    v-for="element in goodsGallery"
                    :key="element.src"
                >
                    <img :src="element.url" />
                </div>
            </transition-group>
        </draggable>
    </el-dialog>
    <el-dialog
        title="提取商品详情图片"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :visible="detialdialogSortIsShow"
        width="50%"
        :before-close="detialdialogSortClose"
    >
        <div
            style="display: inline-block"
            v-for="(element,index) in detialimgsArr"
            :key="element.src"
        >
            <el-checkbox-group v-model="checkList">
                <el-checkbox :label="element"
                    ><img
                        :src="element"
                        style="
                            width: 200px;
                            height: 200px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                        "
                /></el-checkbox>
            </el-checkbox-group>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="confirmPass">确 定</el-button>
            <el-button @click="handlePassClose()">取 消</el-button>
        </span>
    </el-dialog>
    <!-- <el-dialog title="商品详情排序" :close-on-click-modal="false" :close-on-press-escape="false" :visible="dialogSortIsShow2"
        width="50%" :before-close="dialogSortClose2">
        <draggable v-model="goodsDetailImages" chosenClass="chosen" forceFallback="true" group="people" animation="500"
            @start="onStart" @end="onEnd(2)" class="draggable-box">
            <transition-group>
                <div class="item" v-for="element in goodsDetailImages" :key="element.url">
                    <img :src="element.url">
                </div>
            </transition-group>
        </draggable>
    </el-dialog> -->
    <TempDialog ref="tempDialog" @load="getFreightTemp"></TempDialog>
    <!-- <SkusDescribeDialog ref="skusDescribeDialog" @getDescribe="getDescribe"></SkusDescribeDialog> -->
    <CodeTypeDialog ref="codeTypeDialog" @resSkus="resSkus"></CodeTypeDialog>
    <el-dialog
        title="提示"
        :visible="isShow"
        width="400px"
        :before-close="tipClose"
    >
        <span>存在商品价格为0的属性,是否进行保存?</span>
        <span slot="footer" class="dialog-footer">
            <el-button @click="tipClose">取 消</el-button>
            <el-button type="primary" @click="tipConfirm">确 定</el-button>
        </span>
    </el-dialog>
    <SkusIntroduce
        ref="SkusIntroduce"
        @getDescribe="getDescribe"
    ></SkusIntroduce>

    <!-- 查看大图 -->
    <el-dialog
        :visible.sync="maxImgDialogIsShow"
        :close-on-click-modal="false"
        width="800px"
        top="5vh"
    >
        <el-carousel
            v-if="maxImgList.length > 0"
            :interval="5000"
            arrow="always"
            height="800px"
        >
            <el-carousel-item v-for="(image, index) in maxImgList" :key="index">
                <img :src="image.url" style="width: 800px; height: 800px" />
            </el-carousel-item>
        </el-carousel>
        <span slot="footer" class="dialog-footer">
            <el-button
                type="primary"
                @click="maxImgDialogIsShow = false;maxImgList=[]"
                >关闭</el-button
            >
        </span>
    </el-dialog>
</div>
