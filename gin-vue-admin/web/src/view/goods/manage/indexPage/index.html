<m-card>
    <el-button
        type="primary"
        @click="$router.push('/layout/goodsIndex/addGoods')"
    >
        发布商品
    </el-button>
    <el-button @click="syncData">同步数据</el-button>
    <el-form :model="searchForm" ref="form" class="mt25" inline>
        <el-form-item label="" prop="title">
            <el-input
                v-model="searchForm[goodsCommand]"
                placeholder="请输入"
                clearable
                v-if="goodsCommand !== 'isVideoShop'"
                class="line-input-width"
            >
                <template slot="prepend">
                    <el-dropdown
                        class="el-dropdown-row"
                        @command="handleCommand"
                    >
                        <span class="el-dropdown-link">
                            {{returnGoodsValue(goodsCommand)}}<i
                                class="el-icon-arrow-down el-icon--right"
                            ></i>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item command="title"
                                >商品名称</el-dropdown-item
                            >
                            <el-dropdown-item command="sn"
                                >商品条形码</el-dropdown-item
                            >
                            <el-dropdown-item command="isVideoShop"
                                >视频号商品</el-dropdown-item
                            >
                            <el-dropdown-item command="id"
                                >商品ID</el-dropdown-item
                            >
                        </el-dropdown-menu>
                    </el-dropdown>
                </template>
            </el-input>
            <div class="line-input" v-if="goodsCommand == 'isVideoShop'">
                <div class="line-box">
                    <el-dropdown
                        class="el-dropdown-row"
                        @command="handleCommand"
                    >
                        <span class="el-dropdown-link">
                            {{returnGoodsValue(goodsCommand)}}<i
                                class="el-icon-arrow-down el-icon--right"
                            ></i>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item command="title"
                                >商品名称</el-dropdown-item
                            >
                            <el-dropdown-item command="sn"
                                >商品条形码</el-dropdown-item
                            >
                            <el-dropdown-item command="isVideoShop"
                                >视频号商品</el-dropdown-item
                            >
                            <el-dropdown-item command="id"
                                >商品ID</el-dropdown-item
                            >
                        </el-dropdown-menu>
                    </el-dropdown>
                </div>
                <el-select v-model="searchForm.isVideoShop">
                    <el-option label="全部" value="0"></el-option>
                    <el-option label="中台商品" value="-1"></el-option>
                    <el-option label="视频号商品" value="1"></el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="">
            <div class="line-input">
                <div class="line-box">
                    <span>商品状态</span>
                </div>
                <el-select v-model="goodsStatus">
                    <el-option
                        v-for="item in goodsStatusList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="" prop="shopTitle">
            <el-input
                v-model="searchForm[shopCommand]"
                placeholder="请输入"
                clearable
                v-if="shopCommand == 'shopTitle'"
                class="line-input-width"
            >
                <template slot="prepend">
                    <el-dropdown
                        class="el-dropdown-row"
                        @command="handleShopCommand"
                    >
                        <span class="el-dropdown-link">
                            {{returnShopValue(shopCommand)}}<i
                                class="el-icon-arrow-down el-icon--right"
                            ></i>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item command="shopTitle"
                                >店铺名称</el-dropdown-item
                            >
                            <el-dropdown-item command="supplier_id"
                                >供应商</el-dropdown-item
                            >
                            <el-dropdown-item command="gather_supply_id"
                                >供应链</el-dropdown-item
                            >
                        </el-dropdown-menu>
                    </el-dropdown>
                </template>
            </el-input>

            <div class="line-input" v-if="shopCommand == 'supplier_id'">
                <div class="line-box">
                    <el-dropdown
                        class="el-dropdown-row"
                        @command="handleShopCommand"
                    >
                        <span class="el-dropdown-link">
                            {{returnShopValue(shopCommand)}}<i
                                class="el-icon-arrow-down el-icon--right"
                            ></i>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item command="shopTitle"
                                >店铺名称</el-dropdown-item
                            >
                            <el-dropdown-item command="supplier_id"
                                >供应商</el-dropdown-item
                            >
                            <el-dropdown-item command="gather_supply_id"
                                >供应链</el-dropdown-item
                            >
                        </el-dropdown-menu>
                    </el-dropdown>
                </div>
                <el-select
                    v-model="searchForm.supplier_id"
                    filterable
                    clearable
                >
                    <el-option label="全部" value=""></el-option>
                    <el-option label="平台自营" value="0"></el-option>
                    <el-option label="全部供应商" value="999999"></el-option>
                    <el-option
                        v-for="item in supplierList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
            </div>

            <div class="line-input" v-if="shopCommand == 'gather_supply_id'">
                <div class="line-box">
                    <el-dropdown
                        class="el-dropdown-row"
                        @command="handleShopCommand"
                    >
                        <span class="el-dropdown-link">
                            {{returnShopValue(shopCommand)}}<i
                                class="el-icon-arrow-down el-icon--right"
                            ></i>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item command="shopTitle"
                                >店铺名称</el-dropdown-item
                            >
                            <el-dropdown-item command="supplier_id"
                                >供应商</el-dropdown-item
                            >
                            <el-dropdown-item command="gather_supply_id"
                                >供应链</el-dropdown-item
                            >
                        </el-dropdown-menu>
                    </el-dropdown>
                </div>
                <el-select
                    v-model="searchForm.gather_supply_id"
                    @change="supplyFun"
                    filterable
                    clearable
                >
                    <el-option label="全部" value=""></el-option>
                    <el-option label="平台自营" value="0"></el-option>
                    <el-option
                        :label="item.name"
                        :value="item.id"
                        v-for="item in supplyOptions"
                    >
                    </el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item
            v-if="goodsResources && shopCommand === 'gather_supply_id'"
            prop="source_name"
        >
            <span>
                <el-select
                    v-if="goodsResources_id === 2"
                    v-model="searchForm.source_name"
                    placeholder="请输入"
                    filterable
                    clearable
                >
                    <el-option label="全部" value="全部"></el-option>
                    <el-option
                        :label="item.source_name"
                        :value="item.source_name"
                        v-for="item in sourceOptions"
                    >
                    </el-option>
                </el-select>
                <el-select
                    v-if="goodsResources_id === 1"
                    v-model="searchForm.source_name"
                    placeholder="请输入"
                >
                    <!-- <el-option v-for="item in stbzSource" :value="item.id" :label="item.name"></el-option> -->
                    <el-option value="京东" label="京东"></el-option>
                    <el-option value="阿里" label="阿里"></el-option>
                    <el-option value="天猫" label="天猫"></el-option>
                    <el-option value="苏宁" label="苏宁"></el-option>
                    <el-option value="华南一仓" label="华南一仓"></el-option>
                    <el-option value="特卖一仓" label="特卖一仓"></el-option>
                    <el-option value="华东一仓" label="华东一仓"></el-option>
                    <el-option value="淘宝" label="淘宝"></el-option>
                    <el-option value="跨境一仓" label="跨境一仓"></el-option>
                    <el-option value="天猫精选" label="天猫精选"></el-option>
                    <el-option value="厂家直销" label="厂家直销"></el-option>
                </el-select>
            </span>
        </el-form-item>
        <el-form-item label="" prop="category1_id">
            <div class="line-input">
                <div class="line-box">
                    <span>一级分类 </span>
                </div>
                <el-select
                    v-model="searchForm.category1_id"
                    placeholder="请选择一级分类"
                    filterable
                    clearable
                    @change="handleClassiyfChange(2,searchForm.category1_id)"
                >
                    <el-option
                        v-for="item in categoryList1"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="" prop="category2_id">
            <div class="line-input">
                <div class="line-box">
                    <span>二级分类</span>
                </div>
                <el-select
                    v-model="searchForm.category2_id"
                    placeholder="请选择二级分类"
                    filterable
                    clearable
                    @change="handleClassiyfChange(3,searchForm.category2_id)"
                >
                    <el-option
                        v-for="item in categoryList2"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="" prop="category3_id">
            <div class="line-input">
                <div class="line-box">
                    <span>三级分类</span>
                </div>
                <el-select
                    v-model="searchForm.category3_id"
                    placeholder="请选择三级分类"
                    filterable
                    clearable
                >
                    <el-option
                        v-for="item in categoryList3"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="">
            <div class="line-input">
                <div class="line-box">
                    <span>价格区间</span>
                </div>
                <div class="f fac">
                    <el-input
                        v-model="searchForm.minPrice"
                        @input="(v) => (searchForm.minPrice = v.replace(/[^\d.]/g,'').replace(/\.{2,}/g, '.').replace(/^(\d+)\.(\d\d).*$/, '$1.$2'))"
                        clearable
                        placeholder="最低价"
                    ></el-input>
                    <span class="zih-span mr_10">至</span>
                    <el-input
                        v-model="searchForm.maxPrice"
                        @input="(v) => (searchForm.maxPrice = v.replace(/[^\d.]/g,'').replace(/\.{2,}/g, '.').replace(/^(\d+)\.(\d\d).*$/, '$1.$2'))"
                        clearable
                        placeholder="最高价"
                    ></el-input>
                </div>
            </div>
        </el-form-item>
        <el-form-item label="" prop="brand_id">
            <div class="line-input">
                <div class="line-box">
                    <span>品牌</span>
                </div>
                <el-select
                    v-model="searchForm.brand_id"
                    clearable
                    filterable
                    remote
                    :remote-method="remoteMethod"
                    :loading="brandsOptiosData.loading"
                >
                    <el-option
                        v-for="item in brandsOptiosData.brandsOptios"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                    <div class="text-center">
                        <element
                            background
                            small
                            class="pagination"
                            style="
                                padding-top: 10px !important;
                                padding-bottom: 0 !important;
                            "
                            :current-page="brandsOptiosData.page"
                            :page-size="brandsOptiosData.pageSize"
                            :total="brandsOptiosData.total"
                            @current-change="handleBrandPage"
                            layout="prev,pager, next"
                        />
                    </div>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="">
            <div class="line-input">
                <div class="line-box">
                    <span>营销属性</span>
                </div>
                <el-select v-model="arketingChecked" clearable>
                    <el-option
                        v-for="item in arketingOptios"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                    ></el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="" prop="status_lock">
            <div class="line-input">
                <div class="line-box">
                    <span>锁定状态</span>
                </div>
                <el-select v-model="searchForm.status_lock" clearable>
                    <el-option label="全部" value=""></el-option>
                    <el-option label="已锁定" :value="1"></el-option>
                    <el-option label="未锁定" :value="0"></el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="" prop="sortType">
            <div class="line-input">
                <div class="line-box">
                    <span>排序</span>
                </div>
                <el-select v-model="searchForm.sortType" clearable>
                    <el-option label="ID" value="0"></el-option>
                    <el-option label="供货价" value="1"></el-option>
                    <el-option label="成本价" value="2"></el-option>
                    <el-option label="零售价" value="3"></el-option>
                    <el-option label="利润率" value="4"></el-option>
                    <el-option label="销量" value="5"></el-option>
                    <el-option label="Sort" value="6"></el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="" prop="sortMode">
            <div class="line-input">
                <div class="line-box">
                    <span>排序方向</span>
                </div>
                <el-select v-model="searchForm.sortMode" clearable>
                    <el-option label="从小到大" value="asc"></el-option>
                    <el-option label="从大到小" value="desc"></el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="" prop="coding">
            <div class="line-input">
                <div class="line-box">
                    <span>赋码状态</span>
                </div>
                <el-select v-model="searchForm.coding" clearable>
                    <el-option label="已赋码" value="1"></el-option>
                    <el-option label="未赋码" value="0"></el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="" prop="member_price">
            <div class="line-input">
                <div class="line-box">
                    <span>会员价</span>
                </div>
                <el-select v-model="searchForm.member_price" clearable>
                    <el-option label="商品开启独立规则" value="1"></el-option>
                    <el-option label="商品未开启独立规则" value="0"></el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="" prop="member_price">
            <div class="line-input">
                <div class="line-box">
                    <span>商品资料</span>
                </div>
                <el-select v-model="searchForm.product_materials" clearable>
                    <el-option label="主图" value="image_url"></el-option>
                    <el-option label="商品图片" value="gallery_images"></el-option>
                    <el-option label="详情" value="detail_images"></el-option>
                </el-select>
            </div>
        </el-form-item>
        <!-- <el-form-item label="" prop="isVideoShop">
            <div class="line-input">
                <div class="line-box ">
                    <span >视频号商品</span>
                </div>
                <el-select v-model="searchForm.isVideoShop">
                    <el-option label="全部" value="0"></el-option>
                    <el-option label="中台商品" value="-1"></el-option>
                    <el-option label="视频号商品" value="1"></el-option>
                </el-select>
            </div>
        </el-form-item> -->
        <!--        <el-row :gutter="10">
                  <el-col :span="6">
                      <el-form-item label="商品名称:" prop="title">
                          <el-input v-model="searchForm.title" placeholder="请输入" clearable></el-input>
                      </el-form-item>
                  </el-col>
                  <el-col :span="6">
                      <el-form-item label="商品状态:">
                          <el-select v-model="goodsStatus" class="w100">
                              <el-option v-for="item in goodsStatusList" :key="item.id" :label="item.name"
                                         :value="item.value">
                              </el-option>
                          </el-select>
                      </el-form-item>
                  </el-col>
                  <el-col :span="6">
                      <el-form-item label="店铺名称:" prop="shopTitle">
                          <el-input v-model="searchForm.shopTitle" placeholder="请输入" clearable></el-input>
                      </el-form-item>
                  </el-col>
                  <el-col :span="6">
                      <el-form-item label="供应商:" prop="supplier_id">
                          <el-select v-model="searchForm.supplier_id" class="w100" filterable clearable>
                              <el-option label="全部" value=""></el-option>
                              <el-option label="平台自营" value="0"></el-option>
                              <el-option label="全部供应商" value="999999"></el-option>
                              <el-option v-for="item in supplierList" :key="item.id" :label="item.name" :value="item.id">
                              </el-option>
                          </el-select>
                      </el-form-item>
                  </el-col>
                  <el-col :span="6">
                      <el-form-item label="供应链:" prop="gather_supply_id">
                          <el-select v-model="searchForm.gather_supply_id" class="w100" filterable clearable>
                              <el-option label="全部" value=""></el-option>
                              <el-option label="平台自营" value="0"></el-option>
                              <el-option :label="item.name" :value="item.id" v-for="item in supplyOptions">
                              </el-option>
                          </el-select>
                      </el-form-item>
                  </el-col>
                  <el-col :span="6">
                      <el-form-item label="一级分类:" prop="category1_id">
                          <el-select
                              v-model="searchForm.category1_id"
                              placeholder="请选择一级分类"
                              class="w100"
                              filterable
                              clearable
                              @change="handleClassiyfChange(2,searchForm.category1_id)"
                          >
                              <el-option v-for="item in categoryList1" :key="item.id" :label="item.name" :value="item.id">
                              </el-option>
                          </el-select>
                      </el-form-item>
                  </el-col>
                  <el-col :span="6">
                      <el-form-item label="二级分类:" prop="category2_id">
                          <el-select
                              v-model="searchForm.category2_id"
                              placeholder="请选择二级分类"
                              filterable
                              clearable
                              class="w100"
                              @change="handleClassiyfChange(3,searchForm.category2_id)"
                          >
                              <el-option v-for="item in categoryList2" :key="item.id" :label="item.name" :value="item.id">
                              </el-option>
                          </el-select>
                      </el-form-item>
                  </el-col>
                  <el-col :span="6">
                      <el-form-item label="三级分类:" prop="category3_id">
                          <el-select
                              v-model="searchForm.category3_id"
                              placeholder="请选择三级分类"
                              filterable
                              clearable
                              class="w100"
                          >
                              <el-option v-for="item in categoryList3" :key="item.id" :label="item.name" :value="item.id">
                              </el-option>
                          </el-select>
                      </el-form-item>
                  </el-col>
                  <el-col :span="6">
                      <el-form-item label="商品条形码:" prop="sn">
                          <el-input v-model="searchForm.sn" placeholder="请输入" clearable></el-input>
                      </el-form-item>
                  </el-col>
                  <el-col :span="12">
                      <el-form-item label="区间价格:">
                          <div class="f fac">
                              <el-form-item prop="minPrice" class="f1">
                                  <el-input v-model="searchForm.minPrice" clearable placeholder="最低价"></el-input>
                              </el-form-item>
                              <span class="zih-span">至</span>
                              <el-form-item prop="maxPrice" class="f1">
                                  <el-input v-model="searchForm.maxPrice" clearable placeholder="最高价"></el-input>
                              </el-form-item>
                          </div>
                      </el-form-item>
                  </el-col>
                  <el-col :span="6">
                      <el-form-item label="品牌:" prop="brand_id">
                          <el-select v-model="searchForm.brand_id" class="w100" clearable filterable remote
                                     :remote-method="remoteMethod" :loading="brandsOptiosData.loading">
                              <el-option v-for="item in brandsOptiosData.brandsOptios" :key="item.id" :label="item.name"
                                         :value="item.id">
                              </el-option>
                              <div class="text-center">
                                  <Element background small class="pagination"
                                                 style="padding-top:10px !important;padding-bottom: 0 !important;"
                                                 :current-page="brandsOptiosData.page"
                                                 :page-size="brandsOptiosData.pageSize"
                                                 :total="brandsOptiosData.total"
                                                 @current-change="handleBrandPage"
                                                 layout="prev,pager, next"/>
                              </div>
                          </el-select>
                      </el-form-item>
                  </el-col>
                  <el-col :span="6">
                      <el-form-item label="商品id:" prop="id">
                          <el-input v-model="searchForm.id" placeholder="请输入" clearable></el-input>
                      </el-form-item>
                  </el-col>
                  <el-col :span="6">
                      <el-form-item label="营销属性:">
                          <el-select v-model="arketingChecked" class="w100">
                              <el-option v-for="item in arketingOptios" :key="item.id" :label="item.label"
                                         :value="item.value"></el-option>
                          </el-select>
                      </el-form-item>
                  </el-col>
                  <el-col :span="6">
                      <el-form-item label="锁定状态:" prop="status_lock">
                          <el-select v-model="searchForm.status_lock" class="w100">
                              <el-option label="全部" value=""></el-option>
                              <el-option label="已锁定" :value="1"></el-option>
                              <el-option label="未锁定" :value="0"></el-option>
                          </el-select>
                      </el-form-item>
                  </el-col>
                  <el-col :span="6">
                      <el-form-item label="排序:" prop="sortType">
                          <el-select v-model="searchForm.sortType" class="w100">
                              <el-option label="ID" value="0"></el-option>
                              <el-option label="供货价" value="1"></el-option>
                              <el-option label="成本价" value="2"></el-option>
                              <el-option label="零售价" value="3"></el-option>
                              <el-option label="利润率" value="4"></el-option>
                              <el-option label="销量" value="5"></el-option>
                          </el-select>
                      </el-form-item>
                  </el-col>
                  <el-col :span="6">
                      <el-form-item label="排序方向:" prop="sortMode">
                          <el-select v-model="searchForm.sortMode" class="w100">
                              <el-option label="从小到大" value="asc"></el-option>
                              <el-option label="从大到小" value="desc"></el-option>
                          </el-select>
                      </el-form-item>
                  </el-col>
                  <el-col :span="6">
                      <el-form-item label="视频号商品:" prop="isVideoShop">
                          <el-select v-model="searchForm.isVideoShop" class="w100">
                              <el-option label="全部" value="0"></el-option>
                              <el-option label="中台商品" value="-1"></el-option>
                              <el-option label="视频号商品" value="1"></el-option>
                          </el-select>
                      </el-form-item>
                  </el-col>
              </el-row>-->
        <div class="f">
            <el-button type="primary" @click="search">搜索</el-button>
            <el-button @click="exTable">导出</el-button>
            <el-upload
                class="ml_10 mr_10"
                :action="$path + '/adminSupplier/upload'"
                :headers="{ 'x-token': token }"
                :show-file-list="false"
                accept=".xlsx"
                :before-upload="beforeFileUpload"
                :on-success="handleFileSuccess"
            >
                <el-button>上传excel（批量修改商品）</el-button>
            </el-upload>
            <el-button @click="searchDeleteAll">删除所有筛选商品</el-button>
            <el-button type="text" @click="resetForm('form')"
                >重置搜索条件</el-button
            >
        </div>
    </el-form>

    <el-tabs
        v-model="tabGoodsStatus"
        @tab-click="handleTabsClick"
        type="card"
        class="mt25 goods-tabs"
    >
        <el-tab-pane
            v-for="item in goodsStatusList"
            :key="item.id"
            :label="item.name"
            :name="item.value"
        >
        </el-tab-pane>
    </el-tabs>

    <div class="table-top">
        <el-button
            @click="batchPutaway()"
            v-if="[0,2].includes(searchForm.filter)"
            >批量上架</el-button
        >
        <el-button
            @click="batchSoldOut()"
            v-if="[0,1,3].includes(searchForm.filter)"
            >批量下架</el-button
        >
        <el-button slot="reference" @click="batchDeleteDialog()"
            >批量删除</el-button
        >
        <el-button @click="onOpenBatch()">批量分类</el-button>
        <el-button @click="onOpenBrand()">批量品牌</el-button>
        <el-button @click="onCheckedHot()">批量热卖</el-button>
        <el-button @click="onCheckedPromotion()">批量促销</el-button>
        <el-button @click="onCheckedNew()">批量新品</el-button>
        <el-button @click="onBatchSetting()">批量设置</el-button>
    </div>

    <el-table :data="tableList" @selection-change="handleSelectionChange">
        <el-table-column
            type="selection"
            width="55"
            align="center"
        ></el-table-column>
        <el-table-column width="60" label="排序">
            <template slot-scope="scope">
                <p>{{ scope.row.sort }}</p>
            </template>
        </el-table-column>
        <el-table-column width="80" label="ID">
            <template slot-scope="scope">
                <p>{{ scope.row.id }}</p>
            </template>
        </el-table-column>
        <el-table-column
            label="图片"
            width="140"
            align="center"
            show-overflow-tooltip
        >
            <template slot-scope="scope">
                <el-popover placement="right" title="" trigger="hover">
                    <m-image
                        :src="scope.row.image_url"
                        :style="{width:'160px',height:'160px',borderRadius: '16px 16px 16px 16px'}"
                    ></m-image>
                    <m-image
                        slot="reference"
                        :src="scope.row.image_url"
                        :alt="scope.row.image_url"
                        :style="{width:'60px',height:'60px',borderRadius: '16px 16px 16px 16px'}"
                    ></m-image>
                </el-popover>
            </template>
        </el-table-column>
        <el-table-column label="商品" min-width="300">
            <template slot-scope="scope">
                <p style="height: auto !important;line-height: 23px !important">{{ scope.row.title }}</p>
                <p v-if="scope.row.sn" class="sn-code">
                    编码：{{scope.row.sn}}
                </p>
                <p v-if="scope.row.gather_supply_id > 0" class="sn-code">
                    <span
                        v-if="scope.row.source === 119 || scope.row.source === 121 || scope.row.source === 122"
                        >第三方商品ID：{{scope.row.source_goods_id_string}}</span
                    >
                    <span v-else
                        >第三方商品ID：{{scope.row.source_goods_id}}</span
                    >
                    <span
                        v-if="scope.row.source === 119 || scope.row.source === 121 || scope.row.source === 122"
                        @click="$fn.copy(scope.row.source_goods_id_string)"
                        class="copy"
                        >复制</span
                    >
                    <span
                        v-else
                        @click="$fn.copy(scope.row.source_goods_id)"
                        class="copy"
                        >复制</span
                    >
                </p>
            </template>
        </el-table-column>
        <el-table-column align="center" width="150" show-overflow-tooltip>
            <template slot="header">
                <p>供货价 / 成本价</p>
            </template>
            <template slot-scope="scope">
                <p v-if="scope.row.skus.length === 1">
                    ￥{{ scope.row.skus[0].price | formatF2Y }}
                </p>
                <p v-else-if="scope.row.skus.length>1">
                    ￥{{ scope.row.minPrice | formatF2Y }}-{{scope.row.maxPrice
                    | formatF2Y}}
                </p>
                <p v-else>￥{{ scope.row.price | formatF2Y }}</p>
                <p v-if="scope.row.skus.length === 1">
                    ￥{{ scope.row.skus[0].exec_price | formatF2Y }}
                </p>
                <p v-else-if="scope.row.skus.length>1">
                    ￥{{ scope.row.minCostPrice | formatF2Y
                    }}-{{scope.row.maxCostPrice | formatF2Y}}
                </p>
                <p v-else>￥{{ scope.row.cost_price | formatF2Y }}</p>
            </template>
        </el-table-column>
        <!-- <el-table-column align="center" width="150" show-overflow-tooltip>
            <template slot="header">
                <p>成本价</p>
            </template>
            <template slot-scope="scope">
                <p v-if="scope.row.skus.length === 1">
                    ￥{{ scope.row.skus[0].exec_price | formatF2Y }}
                </p>
                <p v-else-if="scope.row.skus.length>1">
                    ￥{{ scope.row.minCostPrice | formatF2Y
                    }}-{{scope.row.maxCostPrice | formatF2Y}}
                </p>
                <p v-else>￥{{ scope.row.cost_price | formatF2Y }}</p>
            </template>
        </el-table-column> -->
        <el-table-column align="center" width="150" show-overflow-tooltip>
            <template slot="header">
                <p>零售价 / 指导价</p>
            </template>
            <template slot-scope="scope">
                <p v-if="scope.row.skus.length === 1">
                    ￥{{ scope.row.skus[0].origin_price | formatF2Y }}
                </p>
                <p v-else-if="scope.row.skus.length>1">
                    ￥{{ scope.row.minOriginPrice | formatF2Y
                    }}-{{scope.row.maxOriginPrice | formatF2Y}}
                </p>
                <p v-else>￥{{ scope.row.origin_price | formatF2Y }}</p>
                <p v-if="scope.row.skus.length === 1">
                    ￥{{ scope.row.skus[0].guide_price | formatF2Y }}
                </p>
                <p v-else>￥{{ scope.row.guide_price | formatF2Y }}</p>
            </template>
        </el-table-column>

        <el-table-column label="利润率" width="150" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
                <p v-if="scope.row.skus.length>1">
                    {{ scope.row.min_profit_rate }}%-{{scope.row.max_profit_rate
                    }}%
                </p>
                <p v-else>{{ scope.row.min_profit_rate }}%</p>
            </template>
        </el-table-column>
        <el-table-column align="center" width="100" show-overflow-tooltip>
            <template slot="header">
                <p>库存 / 销量</p>
            </template>
            <template slot-scope="scope">
                <p>{{ scope.row.stock}}</p>
                <p>{{ scope.row.sales}}</p>
            </template>
        </el-table-column>
        <el-table-column
            label="品牌"
            align="center"
            prop="brand.name"
            show-overflow-tooltip
            width="100"
        ></el-table-column>
        <el-table-column label="供应渠道" align="center" width="100" show-overflow-tooltip>
            <template slot-scope="scope">
                <span v-if="scope.row.supplier_id > 0"
                    >{{ scope.row.supplier.name }}</span
                >
                <span v-else-if="scope.row.gather_supply_id > 0"
                    >{{ scope.row.gather_supply.name }}</span
                >
                <span
                    v-else-if="scope.row.supplier_id === 0 && scope.row.gather_supply_id === 0"
                    >自营</span
                >
                <p v-if="scope.row.source_name">{{ scope.row.source_name }}</p>
            </template>
        </el-table-column>
        <el-table-column width="100" align="center">
            <!-- <template slot="header">
                <p>上下架状态 / 锁定状态</p>
            </template> -->
            <template slot="header">
                <p>上下架状态</p>
                <p>锁定状态</p>
            </template>
            <template slot-scope="scope">
                <p>
                    <el-switch
                        v-model="scope.row.is_display"
                        :active-value="1"
                        :inactive-value="0"
                        @change="handleStatus(scope.row,'is_display')"
                    >
                    </el-switch>
                </p>
                <p>
                    <el-switch
                        v-model="scope.row.status_lock"
                        :active-value="1"
                        :inactive-value="0"
                        @change="handleStatus(scope.row,'status_lock')"
                    >
                    </el-switch>
                </p>
                <!--                <el-switch v-model="scope.row.is_display" :active-value="1" :inactive-value="0"
                                    :inactive-text="switchStatusStr(scope.row.is_display,scope.row.status_lock)"
                                    @change="handleStateChange(scope.row,'is_display')">
                                </el-switch>-->
            </template>
        </el-table-column>
        <el-table-column label="操作" width="240" align="center">
            <template slot-scope="scope">
                <div class="f fac fjc">
                    <span
                        v-if="scope.row.source == 108"
                        @click="updateProduct(scope.row)"
                        class="copy"
                        >更新商品</span
                    >
                    <span
                        v-if="scope.row.source == 116"
                        @click="aliUpdateProduct(scope.row)"
                        class="copy"
                        >更新商品</span
                    >
                    <span
                        v-if="scope.row.gather_supply.category_id == 2"
                        @click="selfUpdateProduct(scope.row)"
                        class="copy"
                        >更新商品</span
                    >
                    <span @click="copyLink(scope.row)" class="copy"
                        >复制链接</span
                    >
                    <span @click="edit(scope.row.id)" class="copy">编辑</span>  
                </div>
                <div class="f fac fjc">
                    <span @click="copyProductByid(scope.row.id)" class="copy"
                        >复制</span
                    >
                    <span @click="delGoods(scope.row.id)" class="copy"
                        >删除</span
                    >
                </div>
                <div class="checkbox-box">
                    <el-checkbox
                        v-model="scope.row.is_hot"
                        size="mini"
                        @change="handleStatus(scope.row,'is_hot')"
                        :true-label="1"
                        :false-label="0"
                        label="热卖"
                        border
                    ></el-checkbox>
                    <el-checkbox
                        v-model="scope.row.is_promotion"
                        size="mini"
                        @change="handleStatus(scope.row,'is_promotion')"
                        :true-label="1"
                        :false-label="0"
                        label="促销"
                        border
                    ></el-checkbox>
                    <el-checkbox
                        v-model="scope.row.is_new"
                        size="mini"
                        @change="handleStatus(scope.row,'is_new')"
                        :true-label="1"
                        :false-label="0"
                        label="新品"
                        border
                    ></el-checkbox>
                </div>
            </template>
        </el-table-column>
    </el-table>
    <el-pagination
        background
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="[10, 30, 50, 100,200]"
        :style="{display: 'flex',justifyContent: 'flex-end',marginRight: '20px'}"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        layout="total, sizes,prev, pager, next, jumper"
    >
    </el-pagination>
    <!-- 批量分类弹窗 start -->
    <el-dialog title="批量分类" :visible.sync="categoryVisible">
        <el-form
            :inline="true"
            :model="batchForm"
            ref="batchForm"
            :rules="rules"
        >
            <el-row :gutter="10">
                <el-col :span="8">
                    <el-form-item label="一级分类:" prop="batch_category1_id">
                        <el-select
                            v-model="batchForm.batch_category1_id"
                            placeholder="请选择一级分类"
                            class="w100"
                            filterable
                            clearable
                            @change="handleClassiyfChangeDialog(2,batchForm.batch_category1_id)"
                        >
                            <el-option
                                v-for="item in categoryListDialog1"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="二级分类:" prop="batch_category2_id">
                        <el-select
                            v-model="batchForm.batch_category2_id"
                            placeholder="请选择二级分类"
                            filterable
                            clearable
                            class="w100"
                            @change="handleClassiyfChangeDialog(3,batchForm.batch_category2_id)"
                        >
                            <el-option
                                v-for="item in categoryListDialog2"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="三级分类:" prop="batch_category3_id">
                        <el-select
                            v-model="batchForm.batch_category3_id"
                            placeholder="请选择三级分类"
                            filterable
                            clearable
                            class="w100"
                        >
                            <el-option
                                v-for="item in categoryListDialog3"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="onCloseCategory('batchForm')">取 消</el-button>
            <el-button type="primary" @click="onSubmitCategory('batchForm')"
                >确 定</el-button
            >
        </div>
    </el-dialog>
    <!-- 批量分类弹窗 end -->
    <!-- 批量品牌 -->
    <el-dialog
        :visible="brandVisible"
        :before-close="onCloseBrand"
        title="批量品牌"
    >
        <div class="f fac">
            <span class="mr10"><span class="color-red">* </span>品牌:</span>
            <el-select
                v-model="batchBrand"
                clearable
                filterable
                remote
                :remote-method="remoteMethod"
                :loading="brandsOptiosData.loading"
            >
                <el-option
                    v-for="item in brandsOptiosData.brandsOptios"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                >
                </el-option>
                <div class="text-center">
                    <element
                        background
                        small
                        class="pagination"
                        style="
                            padding-top: 10px !important;
                            padding-bottom: 0 !important;
                        "
                        :current-page="brandsOptiosData.page"
                        :page-size="brandsOptiosData.pageSize"
                        :total="brandsOptiosData.total"
                        @current-change="handleBrandPage"
                        layout="prev,pager, next"
                    />
                </div>
            </el-select>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button @click="onCloseBrand">取 消</el-button>
            <el-button type="primary" @click="onSubmitBrand">确 定</el-button>
        </div>
    </el-dialog>
    <!--复制链接弹窗-->
    <el-dialog :visible.sync="copyVisible" title="复制链接" width="30%">
        <div class="f fac copyBox">
            <el-button @click="pclinkCopy">pc端链接</el-button>
            <el-button @click="H5linkCopy">H5链接</el-button>
            <el-button @click="appletlinkCopy">小程序链接</el-button>
        </div>
        <span class="txinfo">点击按钮复制对应的链接！</span>
        <div slot="footer" class="dialog-footer">
            <el-button @click="copyCloseBrand" type="primary">关 闭</el-button>
        </div>
    </el-dialog>
    <batch-setting ref="batchSetting"></batch-setting>
</m-card>
