<template>
  <el-drawer :title="formData.id ? '编辑':'新增'"  :visible="isShow" :close-on-press-escape="false" :wrapperClosable="false"
             :before-close="handleClose" size="calc(100% - 220px)" class="detail-ct">
    <el-form :model="formData" :rules="rules"  status-icon label-width="120px" ref="form">
      <el-row>
        <!--        <el-col :span="13">-->
        <!--            <el-form-item label="专题名称:" prop="title">-->
        <!--              <el-input v-model="formData.title"></el-input>-->
        <!--            </el-form-item>-->
        <!--        </el-col>-->
        <!--        <el-col :span="13">-->
        <!--          <el-form-item label="专题分类:" prop="category">-->
        <!--            <el-select v-model="formData.category" clearable filterable class="w100">-->
        <!--              <el-option v-for="item in categotyList" :key="item.id" :label="item" :value="item"></el-option>-->
        <!--            </el-select>-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
        <!--        <el-col class="mt25" :span="13">-->
        <!--          <el-form-item label="banner图:" prop="banner">-->
        <!--            <el-upload class="uploader-box uploader-banner-box" :show-file-list="false"-->
        <!--                       :action="`${$path}/fileUploadAndDownload/upload`"-->
        <!--                       :headers="{ 'x-token': token }" :on-success="handleBannerSuccess"-->
        <!--                       :before-upload="beforeUpload"-->
        <!--                       accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">-->
        <!--              <m-image v-if="formData.banner" style="width: 100%;height: 100%" :src="formData.banner">-->
        <!--              </m-image>-->
        <!--              <i v-else class="el-icon-plus avatar-uploader-icon"></i>-->
        <!--            </el-upload>-->
        <!--            <p class="color-grap">建议尺寸：640*320px</p>-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
        <el-col :span="16">
          <el-form-item label="商品:" prop="products">
            <div class="f fw">
              <template v-if="formData.products.length > 0">
                <div class="goods-box goods-check-item" v-for="(item,index) in formData.products" :key="item.id">
                  <m-image :src="item.image_url" style="width: 100%;height: 100%"></m-image>
                  <div>
                    <el-tooltip
                        effect="light"
                        :content="item.title"
                        v-if="item.title.length > 18"
                        placement="bottom-start">
                      <p class="thumbtitle black">{{ item.title.slice(0, 18) + '...' }}</p>
                    </el-tooltip>
                    <p v-else class="thumbtitle">{{ item.title }}</p>
                  </div>
                  <p class="thumb-remark">商品id: {{ item.id }}</p>
<!--                  <div class="del-box">-->
<!--                    <el-button type="text" icon="el-icon-delete" @click="delGoodsItem(index)"></el-button>-->
<!--                  </div>-->
                </div>
              </template>
              <div class="goods-box select-goods-box" @click="openProductDrawer" v-if="formData.products.length <= 0  ">
                <i class="el-icon-plus avatar-uploader-icon"></i>
                <p>选择商品</p>
              </div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="13">
          <el-form-item label="活动时间:" prop="start_at">
            <el-row style="padding: 0;display: flex">
              <el-form-item label-width='0px'>
                <el-date-picker
                    class="w100"
                    v-model="formData.start_at"
                    type="datetime"
                    placeholder="开始日期">
                </el-date-picker>
              </el-form-item>
              <p style="padding-left:10px;padding-right: 10px">至</p>
              <el-form-item label-width='0px' prop="end_at">
                <el-date-picker
                    class="w100"
                    v-model="formData.end_at"
                    type="datetime"
                    placeholder="结束日期">
                </el-date-picker>
              </el-form-item>
            </el-row>
          </el-form-item>
        </el-col>
        <el-col :span="13">
          <el-form-item label="价格设置:" prop="category">
            <!-- 商品参数 -->
            <el-table :data="skus" border>
              <el-table-column label="规格">
                <template slot-scope="scope">
<!--                  <el-input v-model="scope.row.title" disabled></el-input>-->
                  <div>{{scope.row.title}}</div>
                </template>
              </el-table-column>
              <el-table-column label="供货价">
                <template slot-scope="scope">
<!--                  <el-input v-model="scope.row.price" disabled></el-input>-->
                  <div>{{scope.row.price | formatF2Y}}</div>
                </template>
              </el-table-column>
              <el-table-column label="活动价格">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.new_guide_price"></el-input>
                </template>
              </el-table-column>
              <!--                        <el-table-column label="操作" width="100" align="center">-->
              <!--                          <template slot-scope="scope">-->
              <!--                            &lt;!&ndash; <el-popconfirm title="确定删除吗？" class="ml10" @confirm="delGoodsParam(scope.$index)"> &ndash;&gt;-->
              <!--                            <el-button type="text" slot="reference" class="color-red"-->
              <!--                                       @click="delGoodsParam(scope.$index)">删除-->
              <!--                            </el-button>-->
              <!--                            &lt;!&ndash; </el-popconfirm> &ndash;&gt;-->
              <!--                          </template>-->
              <!--                        </el-table-column>-->
            </el-table>
            <!--                      <div class="add-btn-box">-->
            <!--                        <el-button type="primary" @click="addGoodsParam">添加属性-->
            <!--                        </el-button>-->
            <!--                        <el-button @click="goodsParamList = []">清空-->
            <!--                        </el-button>-->
            <!--                      </div>-->
          </el-form-item>
        </el-col>
        <el-col :span="13">
          <el-form-item label="商品备注:" prop="remark">
            <el-input v-model="formData.remark"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="13">
          <el-form-item label="活动:" prop="title">
            <el-input v-model="formData.title"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="13">
          <el-form-item label="发货:" prop="deliver">
            <el-input v-model="formData.deliver"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="13">
          <el-form-item label="服务:">
            <div v-for="(item,index) in formData.services" :key="index">
              <div style="display: flex;"><el-input style="margin-bottom: 15px;" v-model="item.services"></el-input>
                <div class="change-icon"   @click="delserviceParam(index)"><i class="el-icon-delete" v-if="formData.id == undefined"></i></div></div>
            </div>
            <div class="add-btn-box">
              <el-button type="primary" v-if="formData.id == undefined" @click="addGoodsServices">新增服务
              </el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="活动文字:" prop="share_text">
<!--            <m-editor v-model="formData.share_text"></m-editor>-->
            <el-input v-model="formData.share_text"></el-input>
          </el-form-item>
        </el-col>
        <el-col style="margin-top: 40px">
          <el-form-item>
            <el-button type="primary" v-if="formData.id == undefined" @click="save('form')">保 存</el-button>
            <el-button type="primary" v-if="formData.id" @click="reback()">返 回</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <ActivityProduct-drawer ref="ActivityProductDrawer"
                            @getSelectedProduct="getSelectedProduct"></ActivityProduct-drawer>
  </el-drawer>
</template>

<script>
import {
  getEventDistributionList,
  createEventDistribution,
  getEventDistributionById,
  saveEventDistribution
} from "@/api/eventDistribution";
import {mapGetters} from "vuex";
import ActivityProductDrawer from "@/components/activityProductDrawer";
import {confirm} from "@/decorators/decorators";

export default {
  name: "subjectDrawer",
  components: {ActivityProductDrawer},
  data() {
    return {
      isShow: false,
      categotyList: [],
      servicesarr: [],
      openoff:'',
      goodsParamList: [],
      skus:[],
      skusgoods:[],
      event_distribution_product_sku: [],
      event_distribution_product_skuArr: [],
      rules:{
        products: [
          { required: true, message: '请选择商品', trigger: 'blur' }
        ],
        title: [
          {required: true, message: '请输入活动名称', trigger: 'blur'  }
        ],
        start_at: [
          { type: 'date', required: true, message: '请选择开始日期', trigger: 'change' }
        ],
        end_at: [
          { type: 'date', required: true, message: '请选择结束日期', trigger: 'change' }
        ],
      },
      formData: {
        id: null,
        title: "",
        products: [], // 商品
        start_at: "",
        end_at: "",
        remark: "",
        deliver: "",
        share_text: "",
        services: [
          {services: ''}
        ],

      },
    }
  },
  computed: {
    ...mapGetters("user", ["token"])
  },
  methods: {
    reback(){
      this.handleClose()
    },
    // 添加属性
    addGoodsParam() {
      this.goodsParamList.push({name: "", value: ""});
    },
    // 添加服务
    addGoodsServices() {
      this.formData.services.push({services: ''});
    },
    // 删除属性
    delGoodsParam(index) {
      this.goodsParamList.splice(index, 1);
    },
    delserviceParam(index){
      this.formData.services.splice(index, 1);
    },
    // 删除选中商品
    @confirm("提示", "确定删除?")
    delGoodsItem(index) {
      this.formData.products.splice(index, 1)
      this.handleClose()
    },
    // 获取选中的商品
    getSelectedProduct(products) {
      this.formData.products = this.$fn.merjeArr(this.formData.products, products)
      this.skus = this.formData.products[0].skus
        this.skus.forEach((item,index)=>{
            this.$set(this.skus[index],"new_guide_price",this.$fn.changeMoneyF2Y(item.guide_price))
        })
      // this.formData.products[0].skus.forEach((item) => {
      //   this.skus.push({})
      // })
    },
    openProductDrawer() {
      this.$refs.ActivityProductDrawer.isShow = true
      this.$refs.ActivityProductDrawer.getTableData()
    },
    save() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.formData.id) {
            this.formData.services.forEach((item) => {
              this.servicesarr.push(item.services)
            })
            let date = new Date(this.formData.start_at)
            let date2 = new Date(this.formData.end_at)
            let time = this.formatDate(date, 'yyyy-MM-dd hh:mm')
            let time2 = this.formatDate(date2, 'yyyy-MM-dd hh:mm')
            this.event_distribution_product_sku.forEach((item) => {
              this.event_distribution_product_skuArr.push({'sku_id': item.sku_id, "price":item.guide_price * 100})
            })
            let params2 = {
              event_distribution_product_sku: this.event_distribution_product_skuArr,
              'start_at': time,
              'end_at': time2,
              remark: this.formData.remark,
              deliver: this.formData.deliver,
              title: this.formData.title,
              share_text: this.formData.share_text,
              services: this.servicesarr,
              product_id: this.formData.products[0].id,
            }
            params2.id = this.formData.id
            saveEventDistribution(params2).then(res => {
              if (res.code === 0) {
                this.$message.success(res.msg)
                this.handleClose()
                this.$emit("reload", 2)
              }
            })
          } else {
            console.log(this.formData.services)
            this.formData.services.forEach((item) => {
              this.servicesarr.push(item.services)
            })
            let date = new Date(this.formData.start_at)
            let date2 = new Date(this.formData.end_at)
            let time = this.formatDate(date, 'yyyy-MM-dd hh:mm')
            let time2 = this.formatDate(date2, 'yyyy-MM-dd hh:mm')
            /*this.formData.products[0].skus.forEach((item) => {
              this.event_distribution_product_skuArr.push({'sku_id': item.id, "price": item.guide_price * 100})
            })*/
              this.skus.forEach((item) => {
                  this.event_distribution_product_skuArr.push({'sku_id': item.id, "price": this.$fn.changeMoneyY2F(item.new_guide_price)})
              })

            let params = {
              event_distribution_product_sku: this.event_distribution_product_skuArr,
              'start_at': time,
              'end_at': time2,
              remark: this.formData.remark,
              deliver: this.formData.deliver,
              title: this.formData.title,
              share_text: this.formData.share_text,
              services: this.servicesarr,
              product_id: this.formData.products[0].id,
            }
            createEventDistribution(params).then(res => {
              if (res.code === 0) {
                console.log(res)
                this.$message.success(res.msg)
                this.handleClose()
                this.$emit("reload", 1)
              }
            })
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    formatDate(date, fmt) {
      if (!date) {
        return '';
      }
      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
      }
      let o = {
        'M+': date.getMonth() + 1,
        'd+': date.getDate(),
        'h+': date.getHours(),
        'm+': date.getMinutes(),
        's+': date.getSeconds()
      };
      for (let k in o) {
        if (new RegExp(`(${k})`).test(fmt)) {
          let str = o[k] + '';
          fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? str : this.padLeftZero(str));
        }
      }
      return fmt;
    },
    padLeftZero(str) {
      return ('00' + str).substr(str.length);
    },
    async init(id) {
      console.log(id,'weqewqeqweqweqw')
      this.isShow = true
      this.formData.id = id
      console.log(this.formData.id,'weqewqeqweqweqw')
      if (id) {
        const {code, data} = await getEventDistributionById({id})
        if (code === 0) {
          console.log(data, '111111111w111')
          this.formData.products = [data.product]
          let skusarr = []
          data.event_distribution_product_sku.forEach((item) => {
            skusarr.push({'sku_id': item.id, "new_guide_price": this.$fn.changeMoneyF2Y(item.price),'title':item.sku.title,'price':item.sku.price })
          })
          this.skus = skusarr
          this.formData.title = data.title
          this.formData.start_at  = this.timestampToTime(data.start_at)
          let d = new Date(this.formData.start_at);
          d = d.toISOString()
          this.formData.end_at  = this.timestampToTime(data.end_at)
          let c = new Date(this.formData.end_at);
          // console.log("dddd1111",d)
          c = c.toISOString()
          // console.log("转为UTC格式",d)
          this.formData.start_at = d
          this.formData.end_at = c
          this.formData.remark = data.remark
          this.formData.deliver = data.deliver
          this.formData.share_text = data.share_text
          let arr = []
          data.services.forEach((item) => {
            arr.push({'services': item})
          })
          console.log(arr)
          this.formData.services = arr
        }
      }
    },
    // 时间戳：1637244864707
    /* 时间戳转换为时间 */
    timestampToTime(timestamp) {
      timestamp = timestamp ? timestamp : null;
      let date = new Date(timestamp * 1000);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
      let Y = date.getFullYear() + '-';
      let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
      let D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';
      let h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
      let m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
      let s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
      return Y + M + D + h + m + s;
    },
    handleClose() {
      try {
        this.$refs.form.resetFields()
      } catch {
      } finally {
        this.isShow = false
        this.categotyList = []
            this.event_distribution_product_sku = [],
            this.event_distribution_product_skuArr = [],
                this.skus = [],
            this.servicesarr = [],
            this.formData = {
              id: null,
              title: "",
              products: [], // 商品
              start_at: "",
              end_at: "",
              remark: "",
              deliver: "",
              share_text: "",
              services: [
                {services: ''}
              ],
            }
      }
    },

    // banner图
    handleBannerSuccess(res) {
      if (res.code === 0) {
        this.formData.banner = res.data.file.url
      }
    },
    // 海报背景
    handlePostImgSuccess(res) {
      if (res.code === 0) {
        this.formData.post_img = res.data.file.url
      }
    },
    // 广告图片
    handleAdImgSuccess(res) {
      if (res.code === 0) {
        this.formData.ad_img = res.data.file.url
      }
    },
    beforeUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error("上传图片大小不能超过 10MB!");
      }
      return isLt10M;
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .uploader-box {
  width: 134px;
  height: 134px;
  line-height: 134px;
  border: 1px dashed rgba(226, 226, 226, 100);
  text-align: center;

  .el-upload {
    width: 100%;
    height: 100%;
  }

  i {
    font-size: 20px;
    color: #8F949E;
  }

  &.uploader-banner-box {
    width: 253px;
    height: 126px;
    line-height: 126px;
  }
}

::v-deep .el-drawer {
  .el-drawer__body {
    background-color: #fff !important;
  }
}

.goods-box {
  width: 134px;
  height: 134px;
}

.select-goods-box {
  cursor: pointer;
  border: 1px dashed rgba(226, 226, 226, 100);
  text-align: center;
  color: #BEBEBE;

  i {
    margin-top: 40px;
    font-size: 20px;
  }
}

.goods-check-item {
  margin-right: 20px;
  margin-bottom: 100px;
  position: relative;

  &:hover {
    .del-box {
      display: inline-block;
    }
  }

  .del-box {
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-color: rgba(0, 0, 0, 0.4);
    text-align: center;
    line-height: 134px;

    .el-button {
      color: #fff;
      font-size: 20px;
    }
  }
}
.change-icon{
  margin-left:10px;
  font-size:30px;
}
.mb50 {
  margin-bottom: 50px;
}

.thumb-remark {
  margin-top: 5px;
  line-height: 22px;
  font-size: 12px;
  color: #606266c2;
}

.thumbtitle {
  line-height: 22px;
  color: #606266;
}
</style>