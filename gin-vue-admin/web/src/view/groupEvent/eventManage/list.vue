<template>
  <m-card>
    <el-button type="primary" @click="openSubjectDrawer()">新增活动</el-button>
    <el-form :model="searchInfo" label-width="90px" class="search-term mt25" inline>
      <el-form-item>
          <el-input placeholder="请输入" v-model="searchInfo.product_id" class="line-input" clearable>
              <span slot="prepend">商品ID</span>
          </el-input>
      </el-form-item>
      <el-form-item>
          <el-input placeholder="请输入" v-model="searchInfo.product_title" class="line-input" clearable>
              <span slot="prepend">商品名称</span>
          </el-input>
      </el-form-item>
      <el-form-item>
          <div class="line-input" style="width: 586px;">
              <div class="line-box ">
                  <span >创建时间</span>
              </div>
              <div class="f fac ">
                  <el-date-picker
                      class="w100"
                      v-model="searchInfo.create_start_at"
                      type="datetime"
                      placeholder="开始日期">
                  </el-date-picker>
                  <span class="zih-span">至</span>
                  <el-date-picker
                      class="w100"
                      v-model="searchInfo.create_end_at"
                      type="datetime"
                      placeholder="结束日期">
                  </el-date-picker>
              </div>
          </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search">搜索</el-button>
        <el-button type="text" @click="reSearch">重置搜索条件</el-button>
      </el-form-item>
    </el-form>
    <el-tabs @tab-click="handleTabsClick" class="mt25 order-tabs" type="card" v-model="orderStatus">
      <el-tab-pane :key="item.id" :label="item.name"
                   :name="item.value" v-for="item in orderStatusConditions">
      </el-tab-pane>
    </el-tabs>
    <el-table :data="tableData" class="mt25">
      <el-table-column label="ID" prop="id" align="center"></el-table-column>
      <el-table-column label="创建时间" prop="title" align="center">
        <template slot-scope="scope">
          {{ scope.row.created_at | formatDate}}
        </template>
      </el-table-column>
      <el-table-column label="活动商品" align="center">
          <template slot-scope="scope">
            <div class="ml10 table-goods-imgName">
              <m-image style="width: 60px;height: 60px;" :src="scope.row.product.image_url"></m-image>
              <p>
                <el-button type="text">{{scope.row.product.title}}</el-button>
              </p>
            </div>
          </template>
      </el-table-column>
      <el-table-column label="活动价格" prop="title" align="center">
        <template slot-scope="scope">
          ¥{{ scope.row.min_price  | formatF2Y}} - ¥{{ scope.row.max_price  | formatF2Y}}
        </template>
      </el-table-column>
      <el-table-column label="活动时间" prop="category" align="center">
        <template slot-scope="scope">
          {{ scope.row.start_at | formatDate }}至{{ scope.row.end_at | formatDate }}
        </template>
      </el-table-column>
<!--      <el-table-column label="商品数量" align="center">-->
<!--        <template slot-scope="scope">-->
<!--          <p>{{ scope.row.products.length ? scope.row.products.length : 0 }}</p>-->
<!--          <el-button type="text" @click="openProductsDialog(scope.row.products)">查看</el-button>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="发布者" align="center">-->
<!--        <template slot-scope="scope">-->
<!--          <span v-if="scope.row.supplier_id === 0">总平台</span>-->
<!--          <span v-else>供应商</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" v-if="scope.row.status === 0 || scope.row.status === 1" @click="manualEnd(scope.row)" style="padding: 0 !important;margin-left: 10px !important;">手动结束</el-button>
          <el-button type="text"  @click="openSubjectDrawer(scope.row.id)" style="padding: 0 !important;margin-left: 10px !important;">详情</el-button>
          <el-button type="text" class="color-red" @click="del(scope.row)" style="padding: 0 !important;margin-left: 10px !important;">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background :current-page="page" :page-size="pageSize" :page-sizes="[10, 30, 50, 100,200]"
                   :style="{display: 'flex',justifyContent: 'flex-end',marginRight: '20px'}" :total="total"
                   @current-change="handleCurrentChange" @size-change="handleSizeChange"
                   layout="total, sizes,prev, pager, next, jumper"></el-pagination>
    <subject-drawer ref="subjectDrawer" @reload="reload"></subject-drawer>
    <products-dialog ref="productsDialog"></products-dialog>
    <preview-dialog ref="previewDialog"></preview-dialog>
  </m-card>
</template>

<script>

import SubjectDrawer from "./components/subjectDrawer";
import ProductsDialog from "./components/productsDialog";
import PreviewDialog from "./components/previewDialog";
import infoList from "@/mixins/infoList";
import {getTopicList, getTopicCategory, deleteTopic} from "@/api/topic";
import {formatTimeToStr} from "@/utils/date";
import {getSupplierOptionList} from "@/api/goods";
import {confirm} from "@/decorators/decorators";
import {getEventDistributionList,createEventDistribution,saveEventDistributionEnd,deleteEventDistribution} from "@/api/eventDistribution";
export default {
  name: "supplySubjectManageIndex",
  mixins: [infoList],
  components: {SubjectDrawer, ProductsDialog, PreviewDialog},
  data() {
    return {
      listApi: getTopicList,
      categoryOptions: [],
      supplierOptios: [],
      //------价格设置-------//
      goodsParamList: [],
      tableData:[],
      searchInfo:{
        product_id:'',
        product_title:'',
        create_start_at:'',
        create_end_at:'',
      },
      page:1,
      pageSize:10,
      total:0,
      orderStatus:"9",
      orderStatusConditionsTag:"9",
      //订单状态
      orderStatusConditions: [
        {
          name: "全部",
          value: '9',
        },
        {
          name: "未开始",
          value: '0',
        },
        {
          name: "活动中",
          value: '1',
        },
        {
          name: "已结束",
          value: '2',
        },
      ],
    }
  },
  mounted() {
    // this.init()
    this.getTableData()
  },
  methods: {
    manualEnd(row){
      let params2 = {
       'status':2
      }
      params2.id = row.id
      saveEventDistributionEnd(params2).then(res => {
        if (res.code === 0) {
          this.$message.success(res.msg)
          this.getTableData()
          this.handleClose()
        }
      })
    },
    // tabs切换
    handleTabsClick() {
      this.orderStatusConditionsTag = this.orderStatus;
      if( this.orderStatusConditionsTag == 9 ){
        this.orderStatusConditionsTag = null
      }
      this.page = 1;
      this.pageSize = 10;
      this.getTableData()
    },
    // 预览
    openPreviewDialog(row) {
      this.$refs.previewDialog.init(row)
    },
    openProductsDialog(products) {
      this.$refs.productsDialog.init(products)
    },
    @confirm("提示", "确定删除?")
    async del(row) {
      const {code, msg} = await deleteEventDistribution({id: row.id})
      if (code === 0) {
        this.$message.success(msg)
        this.getTableData()
      }
    },

    handleCurrentChange(page) {
      console.log(page)
      this.page = page;
      this.getTableData();
    },

    handleSizeChange(size) {
      console.log(size)
      this.pageSize = size;
      this.getTableData();
    },
    async init() {
      // 获取分类
      let cateRes = await getTopicCategory()
      if (cateRes.code === 0) {
        this.categoryOptions = cateRes.data.list
      }
      // 获取供应商
      let supplierRes = await getSupplierOptionList()
      if (supplierRes.code === 0) {
        this.supplierOptios = supplierRes.data.list
      }
    },
    search() {
      this.page = 1
      this.getTableData()
    },
    reSearch() {
      this.page = 1
      this.searchInfo = {}
    },
    reload(type) {
      if (type === 1) {
        this.page = 1
      }
      this.getTableData()
    },
    getTableData(){
      if(this.orderStatusConditionsTag == '9'){
        this.orderStatusConditionsTag = null
      }
      let params = {
        'product_id':this.searchInfo.product_id,
        'product_title':this.searchInfo.product_title,
        'status':this.orderStatusConditionsTag,
        page: this.page,
        pageSize: this.pageSize,
      }
      if(this.orderStatusConditionsTag == '9'){

      }
          if(this.searchInfo.create_start_at){
            params.create_start_at = this.dealWithTime(this.searchInfo.create_start_at)
          }
      if(this.searchInfo.create_end_at){
        params.create_end_at = this.dealWithTime(this.searchInfo.create_end_at)
      }
      getEventDistributionList(params).then((res) => {
        if(res.code == 0){
          console.log(res)
          this.tableData = res.data.list
          this.total = res.data.total
        }
      })
    },
    dealWithTime(date) {
      let Y = date.getFullYear()
      let M = date.getMonth() + 1 - 0 >= 10 ? Number(date.getMonth()) + 1 : '0' + (Number(date.getMonth()) + 1)
      let D = date.getDate()
      let h = date.getHours() >= 10 ? date.getHours() : '0' + date.getHours()
      let m = date.getMinutes() >= 10 ? date.getMinutes() : '0' + date.getMinutes()
      let s = date.getSeconds() >= 10 ? date.getSeconds() : '0' + date.getSeconds()
      return Y + '-' + M + '-' + D + ' ' + h + ':' + m + ':' + s
    },　
    openSubjectDrawer(id) {
      console.log(id)
      this.$refs.subjectDrawer.init(id)
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/style/base.scss";
.imageshow{
  display: flex;
  align-items: center;
}
//.ml10 {
//  display: flex;
//  align-items: center;
//  justify-content: center;
//  /* margin-left: 10px; */
//}
</style>