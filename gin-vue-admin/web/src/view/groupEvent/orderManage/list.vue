<template>
  <!-- <m-card>订单管理列表</m-card> -->
  <m-card class="search-box">
    <el-form :model="formData" class="search-term" label-width="80px" inline>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >订单搜索</span>
              </div>
              <el-select class="w100" v-model="orderSearchConditionTag">
                <el-option :label="item.name" :value="item.value" v-for="item in orderSearchConditions"> </el-option>
              </el-select>
          </div>
      </el-form-item>
      <el-form-item label-width="0px">
        <el-input clearable placeholder="请输入" v-model="orderSearchCondition"></el-input>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input" >
            <div class="line-box ">
                <span >供应商</span>
            </div>
            <el-select class="w100" clearable filterable v-model="orderSupplierConditionTag">
              <el-option label="全部" value=""></el-option>
              <el-option label="平台自营" value="0"></el-option>
              <el-option label="全部供应商" value="999999"></el-option>
              <el-option :label="item.name" :value="item.id" v-for="item in orderSupplierConditions"> </el-option>
            </el-select>
        </div>
      </el-form-item>
      <el-form-item>
          <el-input placeholder="请输入" v-model="orderGoodsNameCondition" class="line-input" clearable>
              <span slot="prepend">商品名称</span>
          </el-input>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input" >
            <div class="line-box ">
                <span >支付方式</span>
            </div>
            <el-select class="w100" v-model="orderPaymentTypeConditionTag">
              <el-option
                :key="item.id"
                :label="item.name"
                :value="item.code"
                v-for="item in orderPaymentTypeConditions"
              >
              </el-option>
            </el-select>
        </div>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input" >
            <div class="line-box ">
                <span >订单状态</span>
            </div>
            <el-select class="w100" v-model="orderStatusConditionsTag">
              <el-option :key="item.id" :label="item.name" :value="item.value" v-for="item in orderStatusConditions">
              </el-option>
            </el-select>
        </div>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input" >
            <div class="line-box ">
                <span >采购端</span>
            </div>
            <el-select class="w100" filterable clearable v-model="orderApplicationConditionsTag">
              <el-option
                v-for="item in orderApplicationConditions"
                :key="item.id"
                :label="item.app_name"
                :value="item.id"
              >
              </el-option>
            </el-select>
        </div>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input" >
            <div class="line-box ">
                <span >售后状态</span>
            </div>
            <el-select class="w100" clearable v-model="afterSaleStatus">
              <el-option :value="-1" label="售后关闭"></el-option>
              <el-option :value="2" label="售后中"></el-option>
              <el-option :value="3" label="售后完成"></el-option>
            </el-select>
        </div>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input" >
            <div class="line-box ">
                <span >异常订单</span>
            </div>
            <el-radio-group v-model="gather_supplier_status" class="radio-sty">
              <el-radio :key="item.id" :label="item.value" v-for="item in gather_supplier_status_options">
                {{ item.label }}
              </el-radio>
            </el-radio-group>
        </div>
      </el-form-item>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >时间类型</span>
              </div>
              <el-select class="w100" v-model="dateType">
                <el-option
                  :key="item.id"
                  :label="item.label"
                  :value="item.value"
                  v-for="item in dateTypeOptios"
                ></el-option>
              </el-select>
          </div>
      </el-form-item>
      <el-form-item>
        <div class="line-input" style="width: 586px;">
          <div class="f fac ">
            <el-date-picker class="w100" placeholder="开始日期" type="datetime" v-model="formData.d1"></el-date-picker>
            <span class="zih-span">至</span>
            <el-date-picker class="w100" placeholder="结束日期" type="datetime" v-model="formData.d2"></el-date-picker>
          </div>
        </div>
      </el-form-item>
      <el-form-item label-width="0px">
        <div class="f fac dateBtnBox">
          <span
              :class="dateActive === item.value ? 'is_active' : ''"
              :key="item.id"
              @click="handleDateTab(item)"
              v-for="item in dateList"
              >{{ item.name }}</span>
        </div>
      </el-form-item>

      <el-form-item>
        <el-button @click="searchOrder()" type="primary">搜索</el-button>
        <el-button @click="orderExport">导出</el-button>
        <el-button @click="clearSearchCondition()" type="text">重置搜索条件</el-button>
      </el-form-item>
    </el-form>
    <el-tabs @tab-click="handleTabsClick" class="mt25 order-tabs" type="card" v-model="orderStatus">
      <el-tab-pane
        :key="item.id"
        :label="`${item.name} ${item.total !== null ? item.total : ''}`"
        :name="item.value"
        v-for="item in orderStatusConditions"
      >
      </el-tab-pane>
    </el-tabs>

    <div class="table-box">
      <el-table :data="[{}]" class="table-head">
        <el-table-column label="商品" width="300"></el-table-column>
        <el-table-column align="center" label="单件(元)/数量" width="200"></el-table-column>
        <el-table-column align="center" label="手机号(ID)/会员昵称" width="200"></el-table-column>
        <el-table-column align="center" label="付款方式/配送方式" width="200"></el-table-column>
        <el-table-column align="center" label="小计/运费/应付款" width="250"></el-table-column>
        <el-table-column align="center" label="订单状态" width="200"></el-table-column>
        <el-table-column align="center" label="操作"></el-table-column>
      </el-table>
      <div :key="item.id" v-for="item in orderList">
        <el-table :data="item.order_items" :span-method="objectSpanMethod" class="table-cont">
          <el-table-column>
            <template slot="header">
              <div class="w100 f fac fjsb">
                <div class="f fac fw">
                  <p>订单ID: {{ item.id }}</p>
                  <p>订单编号: {{ item.order_sn }}</p>
                  <p v-if="item.third_order_sn">第三方订单编号: {{ item.third_order_sn }}</p>
                  <p v-if="item.pay_info.pay_sn">支付单号: {{ item.pay_info.pay_sn }}</p>
                  <p v-if="item.gather_supply_id > 0">
                    供应链单号:
                    <span v-if="item.gather_supply_sn">
                      {{ item.gather_supply_sn }}
                      <span class="color-red" v-if="!item.gather_supply_sn && item.gather_supply.category_id === 2">
                        {{ item.gather_supply_msg }}
                      </span>
                      <span class="color-red" v-if="item.gather_supply.category_id === 6">
                        {{ item.gather_supply_msg }}
                      </span>
                    </span>
                    <span class="color-red" v-else-if="!item.gather_supply_sn && item.gather_supply.category_id > 0">
                      订单异常<span v-if="item.gather_supply_msg"> {{ item.gather_supply_msg }}</span>
                    </span>
                    <!--                                    <span class="color-red"
                                          v-else-if="item.gather_supply_type === 7 && !item.gather_supply_sn">
                                        <el-tooltip effect="dark" :content="item.gather_supply_msg" placement="top">
                                        <span><i class="el-icon-warning"></i> 订单异常</span>
                                    </el-tooltip>
                                    </span>-->
                    <!--                                    <span class="color-red"
                                          v-else-if="!item.gather_supply_sn && (item.gather_supply.category_id === 4 || item.gather_supply.category_id === 1 || item.gather_supply.category_id === 2 || item.gather_supply.category_id === 6 || item.gather_supply.category_id === 7)">
                                        订单异常<span v-if="item.gather_supply_msg"> {{ item.gather_supply_msg }}</span>
                                    </span>-->
                  </p>
                  <p v-if="item.cloud_order.cloud_order_id">云仓订单id: {{ item.cloud_order.cloud_order_id }}</p>
                  <p v-if="orderStatus === '2' || orderStatus === '3'">发货时间: {{ item.sent_at | formatDate }}</p>
                  <p v-else>下单时间: {{ item.created_at | formatDate }}</p>
                  <el-tag class="mr10" type="warning" v-if="item.cloud_order.id !== 0">云仓订单</el-tag>
                  <el-tag type="warning">{{ item.shop_name }}</el-tag>
                  <el-tag type="warning" class="ml10" v-if="item.share_live_room_id">{{
                    item.share_live_room.title
                  }}</el-tag>
                </div>
              </div>
              <div>
                <p v-if="item.gather_supply_type === 7 && !item.gather_supply_sn" class="color-red">
                  {{ item.gather_supply_msg }}
                </p>
              </div>
            </template>
            <el-table-column width="300">
              <template slot-scope="scope">
                <div class="f fac goods-box">
                  <m-image :src="scope.row.image_url" style="width: 60px; height: 60px"> </m-image>
                  <div class="f1">
                    <!--题目两行缩略class： <p class="hiddenText2"> -->
                    <p>
                      <a
                        @click="$_blank('/layout/goodsIndex/addGoods', { id: scope.row.product_id })"
                        href="javascript:;"
                        style="color: #155bd4"
                      >
                        {{ scope.row.title }}
                      </a>
                    </p>
                    <p style="color: #a7a7a7">规格: {{ scope.row.sku_title }}</p>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column width="210">
              <template slot-scope="scope">
                <div class="comm-box" style="width: 85%">
                  <p>供货单价: {{ (scope.row.amount / scope.row.qty) | formatF2Y }}</p>
                  <p>数量: {{ scope.row.qty }}</p>
                  <p>金额小计: {{ scope.row.amount | formatF2Y }}</p>
                  <p>子订单号: {{ scope.row.id }}</p>
                  <el-button
                    @click="openAfterDetail(scope.row)"
                    class="color-red"
                    style="padding: 0; margin: 0"
                    type="text"
                    v-if="scope.row.after_sales.id"
                    >查看售后
                  </el-button>
                </div>
              </template>
            </el-table-column>
            <el-table-column width="200">
              <template slot-scope="scope">
                <div class="comm-box">
                  <p class="title-3">
                    <el-button @click="toUserInfo(item.user.id)" type="text">
                      {{ item.user.username }}({{ item.user.id }})
                    </el-button>
                  </p>
                  <p class="title-3">{{ item.user ? item.user.nickname : '' }}</p>
                </div>
              </template>
            </el-table-column>
            <el-table-column width="200">
              <template slot-scope="scope">
                <div class="comm-box">
                  <p class="title-3">{{ item.pay_type }}</p>
                  <p class="title-3">快递</p>
                </div>
              </template>
            </el-table-column>
            <el-table-column width="250">
              <template slot-scope="scope">
                <div class="comm-box" style="width: 85%">
                  <div v-for="amount_items in item.amount_detail.amount_items">
                    <p>{{ amount_items.title }}: ￥{{ amount_items.amount | formatF2Y }}</p>
                  </div>
                  <p>应付款: ￥{{ item.amount_detail.amount | formatF2Y }}</p>
                </div>
              </template>
            </el-table-column>
            <el-table-column width="200">
              <template slot-scope="scope">
                <div class="comm-box text-center">
                  <p class="title-3">
                    {{ item.status | formatStatus }}
                  </p>
                  <template
                    v-for="(ctm, index) in item.order_expresss.length === 0
                      ? item.order_expresss.length + 1
                      : item.order_expresss.length"
                  >
                    <el-button
                      @click="openPackageLogistics(item)"
                      type="text"
                      v-if="item.status === -1 || item.status >= 2"
                      style="margin-left: 0"
                    >
                      <span>包裹{{ index + 1 }}:</span>查看物流
                    </el-button>
                  </template>
                </div>
              </template>
            </el-table-column>
            <el-table-column>
              <template slot-scope="scope">
                <div class="title-3">
                  <template v-if="item.lock === 0">
                    <el-button @click="orderOperationDialog(item.id, btn)" type="text" v-for="btn in item.button"
                      >{{ btn.title }}
                    </el-button>
                  </template>
                  <template v-else-if="item.lock === 1 && item.status === 1">
                    <el-button @click="reLock(item.id)" class="color-red" type="text">解除锁定 </el-button>
                    <el-button @click="submitOrder(item.id)" class="color-red" type="text">提交订单 </el-button>
                  </template>
                  <template v-if="item.da_hang_erp_order.id && item.da_hang_erp_order.status !== -2 && item.status !== -1">
                    <el-button @click="pushDachanghangOrder(item)" class="color-red" type="text"
                      >推送至大昌行
                    </el-button>
                  </template>
                  <el-button type="text" v-if="item.status >= 2" @click="adviseReceiptOfGoods(item.id)">
                    通知发货
                  </el-button>
                </div>
                <!--                            <div class="title-3" v-else-if="item.lock === 1 && item.status === 1 ">
                                <el-button @click="reLock(item.id)" class="color-red"
                                           type="text">解除锁定
                                </el-button>
                                <el-button @click="submitOrder(item.id)" class="color-red"
                                           type="text">提交订单
                                </el-button>
                            </div>-->
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
        <div class="table-foot-box">
          <div class="f fac fjsb">
            <!--                    <p> {{ assemblyAddress(item.shipping_address) }}</p>-->
            <!--                    <p>{{ item}}</p>-->
            <p>
              {{ assemblyAddress(item.shipping_address) }}
              <a
                @click="openEditAddressDialog(item)"
                class="color-red ml10"
                href="javascript:;"
                v-if="item.status <= 1 && item.is_update_shipping_address === 1"
                >修改地址</a
              >
              <el-button
                type="text"
                class="ml10 color-red"
                v-if="assemblyAddress(item.shipping_address)"
                @click="$fn.copy(assemblyAddress(item.shipping_address))"
                >复制
              </el-button>
            </p>
            <p v-if="item.gather_supply_type == '116'">阿里店铺:{{ item.source_shop_name }}</p>
            <p v-if="item.gather_supply_type == '14'">聚水潭店铺:{{ item.source_shop_name }}</p>
            <el-tag type="danger" class="ml10" v-if="item.da_hang_erp_order.status === -1">{{
              item.da_hang_erp_order.push_error_msg
            }}</el-tag>
            <div class="f fac">
              <template v-if="aliBtnIsShow">
                <a
                  v-if="item.supplier.id && item.status === 1 && item.gather_supply_sn === ''"
                  @click="pushAlibaba(item)"
                  class="close-order color-red"
                  href="javascript:;"
                  >推送阿里巴巴</a
                >
              </template>
              <p @click="dialogIsShow(item)" class="color-red shou mr10">
                <span
                  style="
                    display: inline-block;
                    width: 150px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                  "
                  v-if="item.note"
                  >备注 : {{ item.note }}</span
                >
                <span v-else>备注</span>
              </p>
              <!--                        <p v-if="item.note !==''" @click="dialogIsShow(item)"
                                                   style="color: red ;margin-right: 20px;width: 150px; overflow:hidden; white-space:nowrap;text-overflow:ellipsis;cursor: pointer">
                                                    备注 : {{ item.note }}</p>-->
              <template v-if="item.print_button">
                <span class="cgray mr10">{{ item.print_button[0].title }}</span>
                <el-dropdown trigger="click" class="hauto">
                  <a href="javascript:;" class="primary close-order">打印电子面单</a>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item v-for="(btn, bindex) in item.print_button">
                      <p v-if="bindex !== 0" @click="handlePrintClick(btn, item.id)">{{ btn.title }}</p>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
              <a
                @click="openDetail(item)"
                class="primary close-order"
                :class="item.print_button ? 'ml10' : ''"
                href="javascript:;"
                >查看详情</a
              >
            </div>
            <!-- <p>{{item.shipping_address.mobile}}</p>
                    <p class="addr-p">
                        {{assemblyAddress(item.shipping_address)}}
                        {{item.province}} {{item.city}} {{item.county}} {{item.detail}}
                    </p> -->
          </div>
          <p v-if="item.remark" style="margin-left: 0; color: red">买家留言: {{ item.remark }}</p>
        </div>
      </div>
      <el-pagination
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="[10, 30, 50, 100]"
        :style="{ display: 'flex', justifyContent: 'flex-end', marginRight: '20px' }"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        background
        layout="total, sizes, prev, pager, next, jumper"
      ></el-pagination>
      <order-detail ref="orderDetail"></order-detail>
      <user-detail ref="userDetail"></user-detail>
      <order-shipments-dialog @reload="initOrder" ref="orderShipmentsDialog"></order-shipments-dialog>
      <edit-logistics-dialog ref="editLogisticsDialog"></edit-logistics-dialog>
      <detail-after-sale @reload="initOrder" ref="detailAfterSale"></detail-after-sale>
      <print-surface-single-dialog ref="PrintSurfaceSingleDialog" @reload="initOrder"></print-surface-single-dialog>
      <multiple-packages-dialog ref="MultiplePackagesDialog" @getData="printPackages"></multiple-packages-dialog>
      <address-dialog
        :addressDialogIsShow.sync="addressDialogIsShow"
        @reload="initOrder"
        ref="addressDialog"
        v-if="addressDialogIsShow"
      ></address-dialog>
      <el-dialog :before-close="handleDialogClose" :visible="isShow" title="备注" top="40vh" width="600px">
        <el-input :rows="6" placeholder="请输入内容" type="textarea" v-model="dialogForm.note"> </el-input>
        <div class="dialog-footer" slot="footer">
          <el-button @click="confirmNote" type="primary">确 定</el-button>
          <el-button @click="handleDialogClose">取 消</el-button>
        </div>
      </el-dialog>
    </div>
    <!-- 查看物流详情 -->
    <package-logistics-dialog ref="packageLogisticsDialog"></package-logistics-dialog>
  </m-card>
</template>
<script>
import {
  getList,
  orderUpdateState,
  getSupplierOptionList,
  getSupplyList,
  getPaymentType,
  getApplicationOption,
  orderExport,
  cancelLock,
  orderRefund,
  reConfirm,
  addNote,
  manualAlibbOrder,
  handleNotifyApplicationOrderSend,
  categoryList,
} from '@/api/order'
import { getOrderList, exportOrderList } from '@/api/eventDistribution'
import { getShareLiveRooms } from '@/api/shareLive'
import fn from '@/utils/fun'
import { findUser } from '@/api/member'
import { formatTimeToStr } from '@/utils/date'
import OrderDetail from './components/orderDetail.vue'
import UserDetail from '../../member/components/userDetail'
import OrderShipmentsDialog from './components/orderShipmentsDialog'
import EditLogisticsDialog from './components/editLogisticsDialog'
import DetailAfterSale from '@/view/order/afterSale/components/detailAfterSale'
import AddressDialog from './components/addressDialog'
import { request_post } from '@/api/req'
import PrintSurfaceSingleDialog from '@/view/order/allOrder/components/printSurfaceSingleDialog'
import MultiplePackagesDialog from '@/view/order/allOrder/components/multiplePackagesDialog'
import order from '@/mixins/order'
import { getResourcePluginList } from '@/api/plugin'
import PackageLogisticsDialog from './components/packageLogisticsDialog'
import { confirm } from '@/decorators/decorators'
import { daHangErppushOrder } from '@/api/dahangErp'
export default {
  name: 'groupEventOrderManageList',
  mixins: [order],
  components: {
    OrderDetail,
    UserDetail,
    OrderShipmentsDialog,
    EditLogisticsDialog,
    DetailAfterSale,
    AddressDialog,
    PrintSurfaceSingleDialog,
    MultiplePackagesDialog,
    PackageLogisticsDialog,
  },
  data() {
    return {
      shareLiveOption: [],
      aliBtnIsShow: false,
      addressDialogIsShow: false,
      isShow: false, //点击显示全部备注
      dialogForm: {
        //点击显示全部备注字段
        id: null,
        note: '',
      },
      dateType: 0, // 日期类型 0下单时间 1付款时间 2发货时间 3完成时间
      dateTypeOptios: [
        { label: '下单时间', value: 0 },
        { label: '付款时间', value: 1 },
        { label: '发货时间', value: 2 },
        { label: '完成时间', value: 3 },
      ],
      path: this.$path,
      //订单搜索条件
      orderSearchCondition: '',
      orderSearchConditionTag: 0,
      orderSearchConditions: [
        { name: '订单编号', value: 0 },
        { name: '支付单号', value: 1 },
        { name: '快递单号', value: 2 },
        { name: '会员ID', value: 3 },
        { name: '会员昵称', value: 4 },
        { name: '收货人姓名', value: 5 },
        { name: '收货人手机号', value: 6 },
        { name: '三方订单号', value: 7 },
        { name: '云仓ID', value: 8 },
        { name: '供应链单号', value: 9 },
      ],
      //供应商资源
      orderSupplierConditions: [{ name: '全部', code: 0 }],
      supplyOptions: null,
      gather_supplier_id: null,
      orderSupplierConditionTag: null,
      gather_supplier_status: 2,
      source_shop_name: '',
      gather_supplier_status_options: [
        { label: '是', value: 1 },
        { label: '否', value: 2 },
      ],
      //商品名称
      orderGoodsNameCondition: '',
      //支付方式
      orderPaymentTypeConditions: [],
      orderPaymentTypeConditionTag: 0,
      //订单状态
      orderStatusConditions: [
        {
          name: '全部',
          value: '-100',
          total: null,
        },
        {
          name: '待支付',
          value: '0',
          total: 0,
        },
        {
          name: '待发货',
          value: '1',
          total: 0,
        },
        {
          name: '待收货',
          value: '2',
          total: 0,
        },
        {
          name: '已完成',
          value: '3',
          total: 0,
        },
        {
          name: '已关闭',
          value: '-1',
          total: 0,
        },
        /*{
                    name: "退换货",
                    value: '5',
                    total:0
                },
                {
                    name: "已退款",
                    value: '6',
                    total:0
                }*/
      ],
      afterSaleStatus: '',
      orderStatusConditionsTag: '-100',
      orderStatus: '-100',

      //应用
      orderApplicationConditions: [{ app_name: '全部', id: 0 }],
      orderApplicationConditionsTag: 0,

      //时间处理
      dateActive: '',
      dateList: [
        { name: '今', value: 0 },
        { name: '昨', value: 1 },
        { name: '近7天', value: 2 },
        { name: '近30天', value: 3 },
      ],
      formData: {
        d1: '',
        d2: '',
      },
      loading: false,
      // 列表
      orderList: [],
      totalNum: {
        BackNum: 0,
        ClosedNum: 0,
        CompletedNum: 0,
        RefundNum: 0,
        WaitPayNum: 0,
        WaitReceiveNum: 0,
        WaitSendNum: 0,
      },
      //显示0r隐藏
      dianpushow: 0,
      // 直播间id
      share_live_id: null,
      // 分页部分
      page: 1,
      pageSize: 10,
      total: 0,
    }
  },
  filters: {
    // 订单金额
    orderAmountTotal: function (arr) {
      if (!arr) return 0
      let num = 0
      arr.forEach(item => {
        num += item.amount
      })
      return fn.changeMoneyF2Y(num)
    },
    // 格式化订单状态
    formatStatus: function (status) {
      let name = ''
      switch (status) {
        case 0:
          name = '待付款'
          break
        case 1:
          name = '待发货'
          break
        case 2:
          name = '待收货'
          break
        case 3:
          name = '已完成'
          break
        case -1:
          name = '已关闭'
          break
      }
      return name
    },
  },
  mounted() {
    if (this.$route.query.share_live_room_id) {
      this.share_live_id = parseInt(this.$route.query.share_live_room_id)
    }
    if (this.$route.query.application_id) {
      this.orderApplicationConditionsTag = parseInt(this.$route.query.application_id)
    }
    if (this.$route.query.order_sn) {
      this.orderSearchConditionTag = 0
      this.orderSearchCondition = this.$route.query.order_sn
    }
    this.initOrder()
    this.getSupplier()
    this.getSupply()
    this.getPaymentType()
    this.getApplication()
    this.getAliBtnShow()
    this.getShareLiveOption()
    this.categoryList()
  },
  methods: {
    //推送订单到大昌行
    @confirm('提示', '确定推送至推送至大昌行?')
    pushDachanghangOrder(row) {
      console.log(row)
      daHangErppushOrder({ order_id: row.id }).then(res => {
        if (res.code == 0) {
          this.$message.success(res.msg)
        }
      })
    },
    //获取所有供应链
    categoryList() {
      categoryList().then(res => {
        if (res.code == '0') {
          res.data.list.forEach(item => {
            if (item.id == '116') {
              this.dianpushow = 1
            }
          })
        }
      })
    },
    // 获取所有直播间
    getShareLiveOption() {
      getShareLiveRooms().then(({ code, data }) => {
        if (code === 0) {
          this.shareLiveOption = data
        }
      })
    },
    // 通知收货
    async adviseReceiptOfGoods(id) {
      const { code, msg } = await handleNotifyApplicationOrderSend({ id })
      if (code === 0) {
        this.$message.success(msg)
        this.initOrder()
      }
    },
    async getAliBtnShow() {
      const { code, data } = await getResourcePluginList({ page: 1, pageSize: 300 })
      const list = data.list || []
      if (code === 0 && list.length) {
        let aliObj = list.find(item => item.path === 'alibabaList')
        this.aliBtnIsShow = aliObj ? true : false
      }
    },
    // 多包裹打印
    printPackages(arr) {
      this.$refs.PrintSurfaceSingleDialog.init(arr, 2)
    },
    // 打印包裹
    async handlePrintClick(row, order_id) {
      let printObj = this.formatPrintType(row.title)
      // 单包裹 原单号 新单号打印
      if (printObj && printObj.type !== 2) {
        let res = await request_post(row.url, { order_id })
        if (res.code === 0) {
          this.$refs.PrintSurfaceSingleDialog.init(res.data, printObj.type)
        }
      } else if (printObj && printObj.type === 2) {
        // 多包裹
        this.$refs.MultiplePackagesDialog.init(order_id)
      }
    },

    // 推送阿里巴巴
    @confirm('提示', '是否推送阿里巴巴?')
    async pushAlibaba(item) {
      const { code, msg } = await manualAlibbOrder({ id: item.id })
      if (code === 0) {
        this.$message.success(msg)
      }
    },
    // 打开包裹物流详情dialog
    openPackageLogistics(item) {
      this.$refs.packageLogisticsDialog.init(item.id)
    },
    // 打开修改地址
    openEditAddressDialog(row) {
      this.addressDialogIsShow = true
      this.$nextTick(() => {
        this.$refs.addressDialog.isShow = true
        this.$refs.addressDialog.handleFormData(row)
      })
    },
    confirmNote() {
      addNote(this.dialogForm).then(res => {
        if (res.code === 0) {
          this.$message.success(res.msg)
          this.handleDialogClose()
          this.initOrder()
        }
      })
    },
    handleDialogClose() {
      this.isShow = false
      this.dialogForm = {
        id: null,
        note: '',
      }
    },
    //点击弹出一个展示全部备注
    dialogIsShow(item) {
      this.isShow = true
      this.dialogForm.id = item.id
      this.dialogForm.note = item.note
    },
    // 解除锁定
    reLock(id) {
      this.$confirm('是否解除锁定?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          cancelLock({ id }).then(res => {
            if (res.code === 0) {
              this.$message.success(res.msg)
              this.initOrder()
            } else {
              this.$message.error(res.msg)
            }
          })
        })
        .catch(() => {})
    },
    // 提交订单
    submitOrder(id) {
      this.$confirm('是否提交订单?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          reConfirm({ id }).then(res => {
            if (res.code === 0) {
              this.$message.success(res.msg)
              this.initOrder()
            } else {
              this.$message.error(res.msg)
            }
          })
        })
        .catch(() => {})
    },
    // 导出
    orderExport() {
      let params = this.getSearchCondition()
      exportOrderList(params).then(res => {
        if (res.code === 0) {
          this.$message.success(res.msg)
          // window.open(this.path + '/' + res.data.link)
          // window.location.href = this.path + '/' + res.data.link
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 获取供应链
    getSupply() {
      getSupplyList().then(res => {
        this.supplyOptions = res.data.list
      })
    },
    // 获取供应资源
    getSupplier() {
      getSupplierOptionList().then(res => {
        this.orderSupplierConditions = res.data.list
      })
    },
    //获取支付方式
    getPaymentType() {
      getPaymentType().then(res => {
        this.orderPaymentTypeConditions = res.data || []
        this.orderPaymentTypeConditions.unshift({ name: '全部', code: 0 })
      })
    },
    //应用
    getApplication() {
      getApplicationOption().then(res => {
        this.orderApplicationConditions = res.data.list
        this.orderApplicationConditions.unshift({ app_name: '全部', id: 0 })
      })
    },
    //备注

    // 打开订单详情
    openDetail(item) {
      this.$refs.orderDetail.isShow = true
      this.$refs.orderDetail.getOrderDetail(item.id)
    },
    //初始化搜索条件
    initSearchCondition() {
      this.orderSearchCondition = ''
      this.orderSearchConditionTag = this.orderSearchConditions[0].value
    },

    //获取搜索条件
    getSearchCondition() {
      let para = {}
      if (this.share_live_id) {
        para.share_live_room_id = this.share_live_id
      }
      switch (this.orderSearchConditionTag) {
        case 0:
          para.order_sn = this.orderSearchCondition
          break
        case 1:
          para.pay_sn = this.orderSearchCondition
          break
        case 2:
          para.shipping_sn = this.orderSearchCondition
          break
        case 3:
          para.user_id = this.orderSearchCondition
          break
        case 4:
          para.nick_name = this.orderSearchCondition
          break
        case 5:
          para.user_name = this.orderSearchCondition
          break
        case 6:
          para.user_mobile = this.orderSearchCondition
          break
        case 7:
          para.third_order_sn = this.orderSearchCondition
          break
        case 8:
          para.cloud_order_id = this.orderSearchCondition
          break
        case 9:
          para.supply_sn = this.orderSearchCondition
          break
        default:
          break
      }

      if (this.orderSupplierConditionTag) {
        para.supplier_id = this.orderSupplierConditionTag
      }

      if (this.orderGoodsNameCondition != '') {
        para.product_title = this.orderGoodsNameCondition
      }

      if (this.orderPaymentTypeConditionTag != 0) {
        para.pay_type_id = this.orderPaymentTypeConditionTag
      }
      if (this.afterSaleStatus) {
        para.refund_status = this.afterSaleStatus
      }
      if (this.orderStatusConditionsTag != '-100') {
        para.status = this.orderStatusConditionsTag
        this.orderStatus = this.orderStatusConditionsTag
      }

      if (this.orderApplicationConditionsTag != 0) {
        para.application_id = this.orderApplicationConditionsTag
      }

      if (this.formData.d1 != '' && this.formData.d1 != undefined) {
        para.start_at = formatTimeToStr(this.formData.d1.getTime() / 1000, 'yyyy-MM-dd hh:mm:ss')
      }

      if (this.formData.d2 != '' && this.formData.d2 != undefined) {
        para.end_at = formatTimeToStr(this.formData.d2.getTime() / 1000, 'yyyy-MM-dd hh:mm:ss')
      }
      if (this.gather_supplier_id) {
        para.gather_supplier_id = this.gather_supplier_id
      }
      if (this.gather_supplier_status) {
        para.gather_supplier_status = this.gather_supplier_status === 1 ? 1 : ''
      }
      if (this.source_shop_name) {
        para.source_shop_name = this.source_shop_name
      }
      para.time_type = this.dateType
      para.page = this.page
      para.pageSize = this.pageSize
    //   para.plugin_id = 39
      return para
    },

    //根据条件获取userId数据
    getUserInfo() {
      if (!this.orderSearchCondition) {
        return -1
      }
      let para = {}

      switch (this.orderSearchConditionTag) {
        case 4:
          para.nickname = this.orderSearchCondition
          break
        default:
          break
      }

      findUser(para).then(res => {
        if (res.code === 0) {
        } else {
        }
      })
    },

    // 切换日期
    handleDateTab(item) {
      this.dateActive = this.dateActive === item.value ? '' : item.value
      const todayDate = new Date()
      switch (this.dateActive) {
        case 0:
          const dateToday1 = new Date()
          dateToday1.setHours(0)
          dateToday1.setMinutes(0)
          dateToday1.setSeconds(0)
          this.formData.d1 = dateToday1
          todayDate.setHours(23)
          todayDate.setMinutes(59)
          todayDate.setSeconds(59)
          this.formData.d2 = todayDate
          break
        case 1:
          const dateYesterday1 = new Date()
          dateYesterday1.setTime(dateYesterday1.getTime() - 3600 * 1000 * 24 * 1)
          dateYesterday1.setHours(0)
          dateYesterday1.setMinutes(0)
          dateYesterday1.setSeconds(0)
          this.formData.d1 = dateYesterday1

          const dateYesterday2 = new Date()
          dateYesterday2.setTime(dateYesterday2.getTime() - 3600 * 1000 * 24 * 1)
          dateYesterday2.setHours(23)
          dateYesterday2.setMinutes(59)
          dateYesterday2.setSeconds(59)
          this.formData.d2 = dateYesterday2
          break
        case 2:
          const date7Day1 = new Date()
          date7Day1.setTime(date7Day1.getTime() - 3600 * 1000 * 24 * 7)
          date7Day1.setHours(0)
          date7Day1.setMinutes(0)
          date7Day1.setSeconds(0)
          this.formData.d1 = date7Day1

          todayDate.setHours(23)
          todayDate.setMinutes(59)
          todayDate.setSeconds(59)
          this.formData.d2 = todayDate
          break
        case 3:
          const date30Day1 = new Date()
          date30Day1.setTime(date30Day1.getTime() - 3600 * 1000 * 24 * 30)
          date30Day1.setHours(0)
          date30Day1.setMinutes(0)
          date30Day1.setSeconds(0)
          this.formData.d1 = date30Day1

          todayDate.setHours(23)
          todayDate.setMinutes(59)
          todayDate.setSeconds(59)
          this.formData.d2 = todayDate
          break

        default:
          break
      }
    },

    //清除搜索条件
    clearSearchCondition() {
      this.orderSearchCondition = ''
      this.orderSearchConditionTag = 0
      this.orderSupplierConditionTag = null
      this.gather_supplier_id = null
      this.orderGoodsNameCondition = ''
      this.orderPaymentTypeConditionTag = 0
      this.afterSaleStatus = ''
      this.orderStatusConditionsTag = '-100'
      this.orderApplicationConditionsTag = 0
      this.dateActive = ''
      this.dateType = 0
      this.formData.d1 = ''
      this.formData.d2 = ''
      this.share_live_id = null
      this.source_shop_name = ''
    },

    // tabs切换
    handleTabsClick() {
      this.orderStatusConditionsTag = this.orderStatus
      this.page = 1
      this.pageSize = 10
      this.initOrder()
    },
    openAfterDetail(row) {
      this.$refs.detailAfterSale.isShow = true
      this.$refs.detailAfterSale.getByOrderItemId(row.after_sales.order_item_id)
    },
    // 获取列表
    initOrder() {
      this.orderList = []
      this.loading = true
      let para = this.getSearchCondition()
      getOrderList(para).then(res => {
        this.loading = false
        if (res.code === 0) {
          this.orderList = res.data.list
          this.setTotalNum(res.data)
          this.total = res.data.total
        } else {
          this.orderList = []
          this.total = 0
        }
      })
    },
    setTotalNum(data) {
      this.orderStatusConditions[1].total = data.WaitPayNum //待支付
      this.orderStatusConditions[2].total = data.WaitSendNum //待发货
      this.orderStatusConditions[3].total = data.WaitReceiveNum //待收货
      this.orderStatusConditions[4].total = data.CompletedNum //已完成
      this.orderStatusConditions[5].total = data.ClosedNum //已关闭
      //this.orderStatusConditions[6].total = data.BackNum //退换货
      //this.orderStatusConditions[7].total = data.RefundNum //已退款

      /*this.totalNum.BackNum = data.BackNum //退换货
            this.totalNum.ClosedNum = data.ClosedNum // 已关闭
            this.totalNum.CompletedNum = data.CompletedNum //已完成
            this.totalNum.RefundNum=data.RefundNum  //已退款
            this.totalNum.WaitPayNum=data.WaitPayNum  //待支付
            this.totalNum.WaitReceiveNum=data.WaitReceiveNum //待收货
            this.totalNum.WaitSendNum=data.WaitSendNum //待发货*/
    },
    // 下一页
    handleCurrentChange(page) {
      this.page = page
      this.initOrder()
    },

    // 每页显示条数
    handleSizeChange(size) {
      this.pageSize = size
      this.initOrder()
    },

    //搜索
    searchOrder() {
      this.page = 1
      this.pageSize = 10
      this.initOrder()
    },

    //组装地址
    assemblyAddress(address) {
      let addressStr = ''
      if (!address) {
        return '-'
      }
      addressStr =
        address.realname +
        ' ' +
        address.mobile +
        ' ' +
        address.province +
        ' ' +
        address.city +
        ' ' +
        address.county +
        ' ' +
        address.town +
        ' ' +
        address.detail
      return addressStr
    },

    //订单操作
    orderOperationDialog(id, operationItem) {
      if (!operationItem || operationItem.url == '' || operationItem.url == undefined) {
        return
      }

      if (operationItem.url == 'order/send') {
        this.orderSendOperation(id, operationItem)
        return
      }
      // 修改物流
      if (operationItem.url == 'order/updateOrderExpress') {
        this.openEditLogistice(id, operationItem)
        return
      }

      this.$confirm('是否【' + operationItem.title + '】?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      })
        .then(() => {
          // 强制退款
          if (operationItem.url === 'order/orderRefund') {
            orderRefund({ order_id: id }).then(res => {
              if (res.code === 0) {
                this.$message.success(res.msg)
                this.initOrder()
              } else {
                this.$message.error(res.msg)
              }
            })
          } else {
            this.orderOperation(id, operationItem)
            this.initOrder()
          }
        })
        .catch(() => {})
    },

    orderOperation(id, operationItem) {
      let para = {
        order_id: id,
      }
      this.loading = true
      orderUpdateState(operationItem.url, para).then(res => {
        this.loading = false
        if (res.code === 0) {
          this.$message.success(res.msg)
          this.initOrder()
        }
      })
    },
    // 打开修改物流
    openEditLogistice(id, operationItem) {
      this.$refs.editLogisticsDialog.isShow = true
      this.$refs.editLogisticsDialog.initOrderInfo(id, true)
    },
    //订单发货处理
    orderSendOperation(id, operationItem) {
      this.$refs.orderShipmentsDialog.isShow = true
      this.$refs.orderShipmentsDialog.initOrderInfo(id)
    },

    //查看会员信息
    toUserInfo(id) {
      this.$refs.userDetail.isShow = true
      this.$nextTick(() => {
        this.$refs.userDetail.getUserDetail(id)
      })
    },

    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex >= 2) {
        if (rowIndex % 9999 === 0) {
          return {
            rowspan: 9999,
            colspan: 1,
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          }
        }
      }
    },
  },
}
</script>
<style scoped lang="scss">
@import '@/style/base.scss';
// 搜索部分
.search-term {
  .dateBtnBox {
    height: 36px;
    line-height: 36px;
    span {
      height: 27px;
      line-height: 22px;
      display: inline-block;
      margin-right: 10px;
      padding: 2px 4px;
      border: 1px solid #dcdee0;
      color: #c8c9cc;
      cursor: pointer;
      box-sizing: border-box;
      &:last-child {
        margin-right: 0;
      }
      &:hover {
        color: #155bd4;
        border-color: #155bd4;
        background-color: #fff;
      }
      &.is_active {
        color: #155bd4;
        border-color: #155bd4;
        background-color: #fff;
      }
    }
  }
}

/* 订单列表部分开始 */
.table-title-box {
  p.title-p {
    margin-right: 20px;
  }
  span {
    font-size: 12px;
    color: #c1c1c1;
    margin-right: 10px;
  }
}
//.comm-box2{
//    padding-bottom: 10px;
//    border-bottom: 2px solid red;
//}
.table-box {
  margin-top: 25px;
  // 订单item
  /* .table-item {
        border: 1px solid #e8e8e8;
        border-radius: 5px;
        margin-bottom: 10px;
        // 表头
        .table-head {
            border-bottom: 1px solid #e8e8e8;
            background-color: #fafafa;
            color: #888787;
            font-weight: bold;
            padding: 10px 0;
            a.close-order {
                display: inline-block;
                margin-right: 20px;
            }
            div {
                p {
                    margin-left: 10px;
                    &.supplier-p {
                        background-color: rgb(74, 197, 156);
                        padding: 10px;
                        color: #ffffff;
                        border-radius: 3px;
                    }
                }
            }
        }
        .table-cont {
            .el-row {
                padding: 0;
                .el-col {
                    border-right: 1px solid #e8e8e8;
                    padding: 10px 0;
                    height: 120px;
                    &:last-child {
                        border-right: none;
                    }
                    &.goods-box {
                        img {
                            width: 100px;
                            height: 100px;
                            margin: 0 10px;
                        }
                    }
                    .comm-box {
                        p {
                            margin-bottom: 10px;
                            &:last-child {
                                margin-bottom: 0;
                            }
                        }
                    }
                }
            }
        }
        // 收货人信息
        .table-foot {
            border-top: 1px solid #e8e8e8;
            padding: 10px 0;
            p {
                margin-left: 10px;
                &.addr-p {
                    span {
                        margin-right: 5px;
                    }
                }
            }
        }
    } */
}
/* 订单列表部分结束 */
::v-deep .tabs-box {
  padding-left: 20px;
  padding-right: 20px;
  .el-radio-group {
    .el-radio {
      width: 95px;
      height: 37px;
      line-height: 37px;
      text-align: center;
      .el-radio__input {
        display: none;
      }
      .el-radio__label {
        padding: 0;
      }
      &.is-checked {
        background-color: #13c7a7;
        border-radius: 50px;
        .el-radio__label {
          color: white;
        }
      }
    }
  }
}
.search-box {
  // padding: 20px;
  ::v-deep .el-date-editor.el-range-editor.el-input__inner {
    padding: 0 10px;
  }
}
.green-color {
  color: #4ac59c;
}
.order-card {
  margin-left: 1px;
  border-top: 0;
}
/***************************** tabs部分 *******************************/
::v-deep .order-tabs {
  .el-tabs__header {
    margin-bottom: 0px;
    .el-tabs__item {
      background-color: #f7f8fa;
      &.is-active {
        color: #303133;
        background-color: #ffffff;
      }
      &:hover {
        color: #303133;
      }
    }
  }
}
/***************************** 表格部分 *******************************/
::v-deep .el-table.table-head {
  margin-bottom: 25px;
  &::before {
    display: none;
  }
  .el-table__header-wrapper {
    tr th {
      background-color: #f7f8fa !important;
      border-bottom: 0;
    }
  }
  .el-table__body-wrapper {
    display: none;
  }
}
::v-deep .el-table.table-cont.el-table--border {
  border: 1px solid #efefef !important;
}
::v-deep .el-table.table-cont {
  margin-bottom: 0;
  thead {
    tr th {
      background-color: #f7f8fa !important;
    }
    tr:last-child {
      display: none;
    }
    tr:first-child {
      th {
        p {
          margin-right: 20px;
          &.supplier-p {
            //background-color: rgb(74, 197, 156);
            padding: 10px;
            color: #0a3cdc;
            //border-radius: 3px;
          }
        }
      }
    }
  }
  .el-table__body-wrapper {
    .goods-box {
      p {
        margin-left: 10px;
      }
    }
    .comm-box {
      width: 50%;
      margin: 0 auto;
      p {
        margin-bottom: 10px;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
.table-foot-box {
  border: 1px solid #ebeef5;
  border-top: 0;
  margin-bottom: 20px;
  padding: 10px;
  p {
    margin-left: 10px;
    &.addr-p {
      span {
        margin-right: 5px;
      }
    }
    &:first-child {
      margin-left: 0;
    }
  }
}
.hauto {
  height: auto;
}

.radio-sty {
    display: flex;
    align-items: center;
    width: 100%;
    text-align: center;
    justify-content: center;
    border: 1px solid #dcdfe6;
    border-radius: 0 8px 8px 0;
}
</style>
