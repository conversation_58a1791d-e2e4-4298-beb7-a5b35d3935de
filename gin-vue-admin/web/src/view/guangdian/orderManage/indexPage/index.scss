@import "@/style/base.scss";
// 搜索部分
.search-term {
    .dateBtnBox {
        height: 36px;
        line-height: 36px;
        span {
            height: 38px;
            line-height: 38px;
            display: inline-block;
            margin-right: 10px;
            padding: 0 7px;
            border: 1px solid #dcdee0;
            color: #c8c9cc;
            cursor: pointer;
            box-sizing: border-box;
            &:last-child {
                margin-right: 0;
            }
            &:hover {
                color: #155bd4;
                border-color: #155bd4;
                background-color: #fff;
            }
            &.is_active {
                color: #155bd4;
                border-color: #155bd4;
                background-color: #fff;
            }
        }
    }
}

/* 订单列表部分开始 */
.table-title-box {
    p.title-p {
        margin-right: 20px;
    }
    span {
        font-size: 12px;
        color: #c1c1c1;
        margin-right: 10px;
    }
}
//.comm-box2{
//    padding-bottom: 10px;
//    border-bottom: 2px solid red;
//}
.table-box {
    margin-top: 25px;
    // 订单item
    /* .table-item {
        border: 1px solid #e8e8e8;
        border-radius: 5px;
        margin-bottom: 10px;
        // 表头
        .table-head {
            border-bottom: 1px solid #e8e8e8;
            background-color: #fafafa;
            color: #888787;
            font-weight: bold;
            padding: 10px 0;
            a.close-order {
                display: inline-block;
                margin-right: 20px;
            }
            div {
                p {
                    margin-left: 10px;
                    &.supplier-p {
                        background-color: rgb(74, 197, 156);
                        padding: 10px;
                        color: #ffffff;
                        border-radius: 3px;
                    }
                }
            }
        }
        .table-cont {
            .el-row {
                padding: 0;
                .el-col {
                    border-right: 1px solid #e8e8e8;
                    padding: 10px 0;
                    height: 120px;
                    &:last-child {
                        border-right: none;
                    }
                    &.goods-box {
                        img {
                            width: 100px;
                            height: 100px;
                            margin: 0 10px;
                        }
                    }
                    .comm-box {
                        p {
                            margin-bottom: 10px;
                            &:last-child {
                                margin-bottom: 0;
                            }
                        }
                    }
                }
            }
        }
        // 收货人信息
        .table-foot {
            border-top: 1px solid #e8e8e8;
            padding: 10px 0;
            p {
                margin-left: 10px;
                &.addr-p {
                    span {
                        margin-right: 5px;
                    }
                }
            }
        }
    } */
}
/* 订单列表部分结束 */
::v-deep .tabs-box {
    padding-left: 20px;
    padding-right: 20px;
    .el-radio-group {
        .el-radio {
            width: 95px;
            height: 37px;
            line-height: 37px;
            text-align: center;
            .el-radio__input {
                display: none;
            }
            .el-radio__label {
                padding: 0;
            }
            &.is-checked {
                background-color: #13c7a7;
                border-radius: 50px;
                .el-radio__label {
                    color: white;
                }
            }
        }
    }
}
.search-box {
    // padding: 20px;
    ::v-deep .el-date-editor.el-range-editor.el-input__inner {
        padding: 0 10px;
    }
}
.green-color {
    color: #4ac59c;
}
.order-card {
    margin-left: 1px;
    border-top: 0;
}
/***************************** tabs部分 *******************************/
::v-deep .order-tabs {
    .el-tabs__header {
        margin-bottom: 0px;
        .el-tabs__item {
            background-color: #f7f8fa;
            &.is-active {
                color: #303133;
                background-color: #ffffff;
            }
            &:hover {
                color: #303133;
            }
        }
    }
}
/***************************** 表格部分 *******************************/
::v-deep .el-table.table-head {
    margin-bottom: 10px;
    &::before {
        display: none;
    }
    .el-table__header-wrapper {
        tr th {
            background-color: #f7f8fa !important;
            border-bottom: 0;
        }
    }
    .el-table__body-wrapper {
        display: none;
    }
}
::v-deep .el-table.table-cont.el-table--border{
    border: 1px solid #efefef !important;
    border-radius: 4px 4px 0 0;
    border-bottom-style: none !important;
    border-radius: 12px 12px 0px 0px;
}
::v-deep .el-table.table-cont {
    margin-bottom: 0;
    thead {
        tr th{
            background-color: #f7f8fa !important;
        }
        tr:last-child {
            display: none;
        }
        tr:first-child {
            th {
                p {
                    margin-right: 20px;
                    &.supplier-p {
                        //background-color: rgb(74, 197, 156);
                        padding: 10px;
                        color: #0a3cdc;
                        //border-radius: 3px;
                    }
                }
            }
        }
    }
    .el-table__body-wrapper {
        .goods-box {
            p {
                margin-left: 10px;
            }
        }
        .comm-box {
            width: 50%;
            margin: 0 auto;
            p {
                margin-bottom: 10px;
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
}
.table-foot-box {
    border: 1px solid #ebeef5;
    border-top: 0;
    margin-bottom: 20px;
    padding: 0 10px;
    border-radius: 0 0 12px 12px;
    p {
        margin-left: 10px;
        &.addr-p {
            span {
                margin-right: 5px;
            }
        }
        &:first-child {
            margin-left: 0;
        }
    }
}
.hauto{
    height: auto;
}

.search-term .line-input-date .line-box {
    min-width: 0;
    width: 175px;
    padding: 0;
}

//.title-3 , .comm-box {
//    display: flex;
//    flex-direction: column;
//    align-items: flex-start;
//    white-space: nowrap;
//}

.title-3 .el-button {
    margin-left: 0;
}

::v-deep  .el-table--border td {
    border-right: none !important;
}