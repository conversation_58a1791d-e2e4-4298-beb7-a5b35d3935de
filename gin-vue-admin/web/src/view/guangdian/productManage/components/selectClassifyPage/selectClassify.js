import { getClassify } from "@/api/classify";
export default {
  name: "selectClassify",
  data() {
    return {
      oneIndex: null,
      twoIndex: null,
      threeIndex: null,
      // brandIndex: null,
      searchData: {
        name1: "",
        name2: "",
        name3: "",
      },
      classifyData: {
        category1_id: null,
        category2_id: null,
        category3_id: null,
        category1_name: "",
        category2_name: "",
        category3_name: "",
        // brandId: null
      },
      list: [],
      list2: [],
      list3: [],
      // list4: [],
    };
  },
  mounted() {
    getClassify(1, 0).then((r) => {
      this.list = r.data.list;
    });
  },
  methods: {
    /**
     * 选择则
     * @param level 第*级分类
     * @param index
     * @param row  点中的数据
     */
    handleCheck(level, index, row) {
      switch (level) {
        case 1:
          //二次点击取消点中效果
          if (this.oneIndex === index) {
            this.oneIndex = null;
            this.twoIndex = null;
            this.threeIndex = null;
            this.list2 = [];
            this.list3 = [];

            this.classifyData.category1_id = null;
            this.classifyData.category2_id = null;
            this.classifyData.category3_id = null;
          } else {
            this.oneIndex = index;
            this.twoIndex = null;
            this.threeIndex = null;
            this.list2 = [];
            this.list3 = [];
            this.classifyData.category1_id = row.id;
            this.classifyData.category1_name = row.name;
            this.classifyData.category2_id = null;
            this.classifyData.category3_id = null;
            // 请求接口
            getClassify(2, row.id).then((r) => {
              this.list2 = r.data.list;
            });
          }
          break;
        case 2:
          //二次点击取消点中效果
          if (this.twoIndex === index) {
            this.twoIndex = null;
            this.list3 = [];
            this.classifyData.category2_id = null;
          } else {
            this.twoIndex = index;
            this.list3 = [];
            this.classifyData.category2_id = row.id;
            this.classifyData.category2_name = row.name;
            // 请求接口
            getClassify(3, row.id).then((r) => {
              this.list3 = r.data.list;
            });
          }
          break;
        case 3:
          //二次点击取消点中效果
          if (this.threeIndex === index) {
            this.threeIndex = null;
            // this.list4 = []
            this.classifyData.category3_id = null;
          } else {
            this.threeIndex = index;
            // this.list4 = []
            this.classifyData.category3_id = row.id;
            this.classifyData.category3_name = row.name;
            // 请求接口
            /* for (let i = 0; i < 6; i++) {
                                this.list4.push({name: '品牌' + i, id: i + 10})
                            } */
          }
          break;
        /* case 4:
                        //二次点击取消点中效果
                        if (this.brandIndex === index) {
                            this.brandIndex = null
                            this.classifyData.brandId = row.id
                        } else {
                            this.brandIndex = index
                            this.classifyData.brandId = row.id
                        }
                        break; */
      }
    },
    // 搜索
    search(level, value) {
      let pid = 0;
      switch (level) {
        case 2:
          if (this.classifyData.category1_id) {
            pid = this.classifyData.category1_id;
          } else {
            this.$message.error("请选择一级类目后再搜索!");
          }
          break;
        case 3:
          if (this.classifyData.category2_id) {
            pid = this.classifyData.category2_id;
          } else {
            this.$message.error("请选择二级类目后再搜索!");
          }
          break;
        default:
          pid = 0;
          break;
      }
      getClassify(level, pid, value).then((r) => {
        switch (level) {
          case 1:
            this.list = r.data.list;
            break;
          case 2:
            this.list2 = r.data.list;
            break;
          case 3:
            this.list3 = r.data.list;
            break;
        }
      });
    },
    // 下一步
    handleNext() {
      if(!this.classifyData.category1_id ||!this.classifyData.category2_id|| !this.classifyData.category3_id){
        this.$message.error("请选择类目!");
        return;
      }
      this.$emit("handleNext", this.classifyData);
    },
  },
};