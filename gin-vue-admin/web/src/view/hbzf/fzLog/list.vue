<template>
    <m-card>
        <el-form :model="searchInfo" class="search-term" label-width="100px" inline>
            <el-form-item>
                <el-input placeholder="请输入" v-model="searchInfo.order_sn" class="line-input" clearable>
                    <span slot="prepend">订单编号</span>
                </el-input>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >供应商</span>
                    </div>
                    <el-select v-model="searchInfo.supplier_id" class="w100" clearable>
                        <el-option v-for="item in supplierOption" :key="item.id" :label="item.name" :value="item.id"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >采购商</span>
                    </div>
                    <el-select v-model="searchInfo.application_id" class="w100" clearable>
                        <el-option v-for="item in applierOption" :key="item.id" :label="item.appName"
                                    :value="item.id"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" style="width: 500px">
                    <div class="line-box ">
                        <span >分账时间</span>
                    </div>
                    <m-daterange v-model="date"></m-daterange>
                </div>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="search">搜索</el-button>
                <el-button type="text" @click="reSearch">重置搜索条件</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="tableData" class="mt25">
            <el-table-column label="分账时间" align="center">
                <template slot-scope="scope">
                    <p>{{ scope.row.created_at|formatDate }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header" slot-scope="scope">
                    <p>分账订单号</p>
                    <p>订单编号</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.share_apply_sn ? scope.row.share_apply_sn : '暂无分账订单号' }}</p>
                    <p>{{ scope.row.order_sn ? scope.row.order_sn : '暂无订单编号' }}</p>
                </template>
            </el-table-column>
            <el-table-column label="供应商名称" prop="supplier.name" align="center"></el-table-column>
            <el-table-column label="采购端名称" prop="application.appName" align="center"></el-table-column>
            <el-table-column label="分账金额" align="center">
                <template slot-scope="scope">
                    <p>{{ scope.row.pay_total_amount|formatF2Y }}</p>
                </template>
            </el-table-column>
            <el-table-column label="供应商到账金额" align="center">
                <template slot-scope="scope">
                    <p>{{ scope.row.supplier_amount|formatF2Y }}</p>
                </template>
            </el-table-column>
            <el-table-column label="中台到账金额" align="center">
                <template slot-scope="scope">
                    <p>{{ scope.row.platform_amount|formatF2Y }}</p>
                </template>
            </el-table-column>
            <el-table-column label="采购商到账金额" align="center">
                <template slot-scope="scope">
                    <p>{{ scope.row.app_amount|formatF2Y }}</p>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button type="text" @click="jumpOrder(scope.row)">查看订单</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
                background
                :current-page="page"
                :page-size="pageSize"
                :page-sizes="[10, 30, 50, 100,200]"
                :style="{display: 'flex',justifyContent: 'flex-end',marginRight: '20px'}"
                :total="total"
                @current-change="handleCurrentChange"
                @size-change="handleSizeChange"
                layout="total, sizes,prev, pager, next, jumper"
        >
        </el-pagination>
    </m-card>
</template>
<script>
import infoList from "@/mixins/infoList";
import {getDivideAccounts} from "@/api/hbzf";
import MDaterange from "@/components/mDate/daterange.vue";
import {getApplicationList} from "@/api/application";
import {getSupplierOptionList} from "@/api/goods";

export default {
    name: "bhzfFzLogList",
    components: {MDaterange},
    mixins: [infoList],
    data() {
        return {
            listApi: getDivideAccounts,
            date: [],
            applierOption:[],
            supplierOption:[]
        }
    },
    mounted() {
        if(this.$route.query.order_sn){
            this.searchInfo.order_sn = this.$route.query.order_sn
        }
        this.getApplierOption()
        this.getSupplierOption()
        this.getTableData()
    },
    methods:{
        search(){
            if(this.date && this.date.length){
                this.searchInfo.begin_time = this.date[0]
                this.searchInfo.end_time = this.date[1]
            }
            this.getTableData(1)
        },
        reSearch(){
          this.searchInfo = {}
          this.date = []
        },
        // 获取采购端
        async getApplierOption() {
            const {code, data} = await getApplicationList({page: 1, pageSize: 999})
            if (code === 0) {
                this.applierOption = data.list
            }
        },
        // 获取供应商
        async getSupplierOption() {
            const {code, data} = await getSupplierOptionList()
            if (code === 0) {
                this.supplierOption = data.list
            }
        },
        // 新页面跳转订单页面
        jumpOrder(row){
            this.$_blank('/layout/orderIndex/allOrderIndex',{
                order_sn: row.order_sn
            })
        }
    }
}
</script>
<style scoped lang="scss"></style>