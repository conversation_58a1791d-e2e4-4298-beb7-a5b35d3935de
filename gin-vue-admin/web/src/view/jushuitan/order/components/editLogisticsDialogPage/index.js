import {
    getOrderSendInfo,
    getShippingCompany,
    getDetail,
    updateOrderExpress,
    supperUpdateOrderExpress
} from "@/api/order"

export default {
    name: "editLogisticsDialog",
    data() {
        return {
            queryID: null,
            isSupplier: false,
            isShow: false,
            order_express: [],
            activeName: "",
            dialogIsShow: false,
            logisticsFormData: {
                id: "",
                express_no: "", // 物流单号
                company_code: "", // 物流code
            },
            expressList: [],
            rules: {
                company_code: {required: true, message: "请选择物流公司"},
                express_no: {required: true, message: "请输入物流单号"},
            }
        }
    },
    methods: {
        confirm() {
            this.$refs.form.validate((valid) => {
                if (!valid) return;
                this.$confirm("是否确定修改?", "提示", {
                    type: "warning"
                }).then(() => {
                    if (this.isSupplier) {
                        supperUpdateOrderExpress(this.logisticsFormData).then(res => {
                            if (res.code === 0) {
                                this.$message.success(res.msg)
                                this.handleDialogClose()
                                this.initOrderInfo(this.queryID, this.isSupplier)
                            } else {
                                this.$message.error(res.msg)
                            }
                        })
                    } else {
                        updateOrderExpress(this.logisticsFormData).then(res => {
                            if (res.code === 0) {
                                this.$message.success(res.msg)
                                this.handleDialogClose()
                                this.initOrderInfo(this.queryID, this.isSupplier)
                            } else {
                                this.$message.error(res.msg)
                            }
                        })
                    }
                })
            })
        },
        //获取快递公司信息
        async getExpress() {
            this.expressList = [];
            let res = await getShippingCompany()
            if (res.code === 0) {
                this.expressList = res.data.list;
            }
        },
        //赋值
        /* setFrom(val) {
             const keys = Object.keys(val);
             const that = this;
             keys.forEach((element) => {
                 that.logisticsFormData[element] = val[element];
             });
         },*/
        handleClose() {
            this.isShow = false
            this.isSupplier = false
            this.queryID = null
        },
        initOrderInfo(id, isSupplier = false) {
            this.isSupplier = isSupplier
            this.queryID = id
            getDetail({id}).then(res => {
                if (res.code === 0) {
                    this.order_express = res.data.read.order_express
                    if (this.order_express.length > 0) {
                        this.activeName = this.order_express[0].id
                        this.activeName += ""
                    }
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        // 修改物流dialog关闭
        handleDialogClose() {
            try {
                this.$refs.form.resetFields();
            } catch {
            } finally {
                this.dialogIsShow = false
                this.logisticsFormData = {
                    id: "",
                    express_no: "", // 物流单号
                    company_code: "", // 物流code
                }
            }
        },
        openDialog(row) {
            this.dialogIsShow = true
            this.logisticsFormData.id = row.id;
            this.logisticsFormData.express_no = row.express_no;
            this.logisticsFormData.company_code = row.company_code;
            this.getExpress()
        }
    }
}