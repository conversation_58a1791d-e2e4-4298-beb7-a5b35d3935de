import {getShippingCompany, getOrderSendInfo, orderSend} from "@/api/order"
import fn from "@/utils/fun"
export default {
    data() {
        return {
            shipmentsType: "1",
            selectionOrder: [],
            isShow: false,
            id: 0,
            formData: {
                logisticsCompany: "",
                logisticsNumber: ""
            },
            expressList: [],
            order_items: [],
            cloudOrder: [],//云仓订单
            sent_count: 0,
            not_send_count: 0,
            shipping_address: {},
        };

    },
    mounted() {
        this.getExpress();
    },
    methods: {
        handleClose() {
            this.isShow = false;
            this.selectionOrder = [];
            this.shipmentsType = "1";
            this.id = 0;
            this.formData.logisticsCompany = "";
            this.formData.logisticsNumber = "";
        },

        //获取快递公司信息
        getExpress() {
            this.expressList = [];
            getShippingCompany().then(res => {
                if (res.code === 0) {
                    this.expressList = res.data.list;
                }
            });
        },

        //获取订单数据
        initOrderInfo(id) {
            this.getExpress();
            this.id = id;
            let para = {
                "id": id
            }
            getOrderSendInfo(para).then(res => {
                if (res.code === 0) {
                    this.order_items = res.data.send_info.order_items.map((item) => ({
                        num: item.can_send_num,
                        ...item
                    }));
                    this.sent_count = res.data.send_info.sent_count;
                    this.not_send_count = res.data.send_info.not_send_count;
                    this.shipping_address = res.data.send_info.shipping_address;
                    this.cloudOrder = res.data.send_info.cloud_order;
                }
            });
        },

        //发货
        orderSend: fn.throttle(function () {
            if (this.selectionOrder.length == 0) {
              this.$message.warning("请先选择要发货的商品");
              return;
            }

            let order_item_ids = [];
            this.selectionOrder.forEach(item => {
              order_item_ids.push({
                id: item.id,
                num: item.num
              });
            });

            if (this.shipmentsType == "1") {
              if (!this.formData.logisticsCompany) {
                this.$message.warning("请先选择物流公司");
                return;
              }
              if (!this.formData.logisticsNumber) {
                this.$message.warning("请填写物流单号");
                return;
              }
            }

            let para = {
              "order_id": this.id,
              "order_item_ids": order_item_ids,
              "company_code": this.formData.logisticsCompany,
              "express_no": this.formData.logisticsNumber
            }
            orderSend(para).then(res => {
              if (res.code === 0) {
                this.$message.success(res.msg);
                this.handleClose();
                this.$emit('reload')
              }
            });
        }),
        selectionChange(val) {
            this.selectionOrder = val;
        },
        //验证是否可选
        itemIsCheck(row, index) {
            if (row.send_status === 1) {
                return false;
            }
            return true;

        },
        numItemIsCheck(row, index) {
            if (this.cloudOrder.id !== 0) {
                return true;
            }
            return false
        }
    },
};