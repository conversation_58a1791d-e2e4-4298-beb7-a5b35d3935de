<!-- 主动退款dialog -->
<template>
  <el-dialog
      title="商家主动退款"
      :visible="isShow"
      width="30%"
      :before-close="handleClose">
    <div class="warning-box">
      <div class="f">
        <i class="el-icon-warning"></i>
        <p>商家主动退款功能仅作为退款维权业务的补充功能，请勿过度依赖和使用，退款将返还实际入账金额，免充值的支付优惠金额将从退款金额中扣除。</p>
      </div>
    </div>
    <el-table :data="tableData" class="mt25">
      <el-table-column label="商品" prop="title"></el-table-column>
      <el-table-column label="可退款金额(元)" align="center">
        <template slot-scope="scope">
          {{ scope.row.amount | formatF2Y }}
        </template>
      </el-table-column>
      <el-table-column label="退款金额(元)" align="center">
        <template slot-scope="scope">
          <el-input-number v-model="scope.row.refund_amount" :precision="2" :min="0" class="input-number-text-left w100"
                           :controls="false" @change="handleAmountChange(scope.row)"></el-input-number>
        </template>
      </el-table-column>
    </el-table>
    <div class="mt25">
      <p>退款给买家/付款人: <span class="color-red">{{ amountShow | formatF2Y }}</span></p>
      <p class="mt10">提示: 订单全额退款后,积分抵现将会退给买家账户</p>
    </div>
    <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="confirm">确 定</el-button>
  </span>
  </el-dialog>
</template>

<script>
import {refund} from "@/api/order";

export default {
  name: "refundDialog",
  data() {
    return {
      isShow: false,
      tableData: [],
      amountShow: 0
    }
  },
  methods: {
    handleAmountChange(row) {
      if (this.$fn.changeMoneyY2F(row.refund_amount) > row.amount) {
        row.refund_amount = this.$fn.changeMoneyF2Y(row.amount)
      }
      this.amountShow = this.$fn.changeMoneyY2F(row.refund_amount)
    },
    handleClose(reload = false) {
      this.isShow = false
      this.tableData = []
      this.amountShow = 0
      if (reload) this.$emit("reload");
    },
    confirm() {
      if (this.tableData[0].refund_amount <= 0) {
        this.$message.error("请填写退款金额")
        return;
      }
      let data = {
        order_item_id: this.tableData[0].id,
        refund_amount: this.$fn.changeMoneyY2F(this.tableData[0].refund_amount)
      }
      console.log(data, '???');
      refund(data).then(res => {
        if (res.code === 0) {
          this.$message.success(res.msg)
          this.handleClose(true)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/style/base.scss";

.warning-box {
  border: 1px solid #e6a23c;
  background-color: #fdf6ec;
  padding: 10px;

  .el-icon-warning {
    margin-right: 10px;
    color: #e6a23c;
  }
}
</style>