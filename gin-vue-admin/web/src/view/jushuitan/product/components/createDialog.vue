<template>
    <div>
        <el-dialog
            title="新增"
            :visible="isShow"
            width="800px"
            @close="handleClose"
            @open="handleOpen"
        >
            <el-form :model="formData" label-width="90px" ref="form" :rules="rules">
                <el-form-item label="采购端:" prop="application_id">
                    <el-select v-model="formData.application_id" clearable class="w100">
                        <el-option
                            v-for="item in applicationOption"
                            :key="item.id"
                            :label="item.app_name"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="库存" prop="stock">
                    <m-num-input v-model="formData.stock"></m-num-input>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button type="primary" @click="confirm">确定</el-button>
                <el-button @click="handleClose">取消</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { createApplicationVirtualStock } from '@/api/jushuitan';
import { getApplicationOption } from '@/api/order';
export default {
    inject: ['product_id'],
    computed: {
        checkProductID() {
            return this.product_id;
        },
    },
    data() {
        return {
            isShow: false,
            formData: {
                application_id: null,
                stock: null,
            },
            applicationOption: [],
            rules: {
                application_id: {
                    required: true,
                    message: '请选择采购端',
                    trigger: 'change',
                },
                stock: {
                    required: true,
                    message: '请输入库存',
                    trigger: 'blur',
                },
            },
        };
    },
    methods: {
        confirm() {
            this.$refs.form.validate(valid => {
                if (!valid) return;
                let params = {
                    application_id: this.formData.application_id,
                    stock: this.formData.stock,
                    product_id: this.checkProductID(),
                };
                createApplicationVirtualStock(params).then(({code, msg}) => {
                    if (code === 0) {
                        this.$message.success(msg);
                        this.handleClose()
                        this.$emit('reload')
                    }
                });
            });
        },
        async handleOpen() {
            const { code, data } = await getApplicationOption();
            if (code === 0) {
                this.applicationOption = data.list;
            }
        },
        handleClose() {
            try {
                this.$refs.form.resetFields();
            } catch {
            } finally {
                this.isShow = false;
                this.formData = {
                    application_id: null,
                    stock: null,
                };
            }
        },
    },
};
</script>
<style lang="scss" scoped></style>
