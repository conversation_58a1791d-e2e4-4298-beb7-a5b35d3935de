import {
    createApplicationGroup,
    deleteApplicationGroup,
    findApplicationGroup,
    getApplicationGroupList,
    updateApplicationGroup,
  } from "@/api/jushuitan"; //  此处请自行替换地址
  import { formatTimeToStr } from "@/utils/date";
  import infoList from "@/mixins/infoList";
  
  export default {
    name: "jushuitanPurchaseGroup",
    mixins: [infoList],
    data() {
      return {
        dialogTitle: "新增",
        searchInputWidth: "160px",
        listApi: getApplicationGroupList,
        dialogFormVisible: false,
        type: "",
        deleteVisible: false,
        multipleSelection: [],
        formData: {
          name: "",
          sort: 0,
          is_show: 0,
        },
      };
    },
    filters: {
      formatState: function (state) {
        return state===1?"启用":"关闭";
      },
    },
    methods: {
      // search input 获取焦点
      searchInputFocus() {
        this.searchInputWidth = "300px";
      },
      // search input 失去焦点
      searchInputBlur() {
        this.searchInputWidth = "160px";
      },
      //条件搜索前端看此方法
      onSubmit() {
        this.page = 1;
        this.pageSize = 10;
        this.getTableData();
      },
      /* handleSelectionChange(val) {
        this.multipleSelection = val;
      }, */
      deleteRowDialog(row) {
        this.$confirm("确定要删除吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
      })
          .then(() => {
            this.deleteSupplierCategory(row);
          }).catch(() => { });

      },
      async updateSupplierCategory(row) {
        const res = await findApplicationGroup({ id: row.id });
        this.type = "update";
        if (res.code == 0) {
          this.formData = res.data.resupplierGroup;
          this.dialogFormVisible = true;
          this.$nextTick(() => {
            this.dialogTitle = "编辑";
          });
        }
      },
      closeDialog() {
        this.dialogFormVisible = false;
        this.formData = {
          name: "",
          sort: 0,
          is_show: 0,
        };
      },
      async deleteSupplierCategory(row) {
        const res = await deleteApplicationGroup({ id: row.id });
        if (res.code == 0) {
          this.$message({
            type: "success",
            message: "删除成功",
          });
          if (this.tableData.length == 1) {
            this.page--;
          }
          this.getTableData();
        }
      },
      async enterDialog() {
        let res;
        switch (this.type) {
          case "create":
            res = await createApplicationGroup(this.formData);
            break;
          case "update":
            res = await updateApplicationGroup(this.formData);
            break;
          default:
            res = await createApplicationGroup(this.formData);
            break;
        }
        if (res.code == 0) {
          this.$message({
            type: "success",
            message: "创建/更改成功",
          });
          this.closeDialog();
          this.getTableData();
        }
      },
      openDialog() {
        this.type = "create";
        this.dialogFormVisible = true;
        this.$nextTick(() => {
          this.dialogTitle = "新增";
        });
      },
    },
    async created() {
      await this.getTableData();
    },
  };