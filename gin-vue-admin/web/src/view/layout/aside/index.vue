<template>
  <div>
    <el-scrollbar style="height: calc(100vh - 64px)">
      <transition
        :duration="{ enter: 800, leave: 100 }"
        mode="out-in"
        name="el-fade-in-linear"
      >
        <!-- active-text-color="#44D2B9" text-color="rgb(191, 203, 217)" -->
        <el-menu
          :collapse="isCollapse"
          :collapse-transition="true"
          :default-active="active"
          @select="selectMenuItem"
          class="el-menu-vertical"
          unique-opened
        >
          <template v-for="item in asyncRouters[0].children">
            <aside-component
              :key="item.name"
              :routerInfo="item"
              v-if="!item.hidden"
            />
          </template>
        </el-menu>
      </transition>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from "vuex";
import AsideComponent from "@/view/layout/aside/asideComponent";

export default {
  name: "Aside",
  data() {
    return {
      active: "",
      isCollapse: false,
    };
  },
  methods: {
    ...mapMutations("history", ["addHistory"]),
    selectMenuItem(index, _, ele) {
      const query = {};
      const params = {};
      ele.route.parameters &&
        ele.route.parameters.map((item) => {
          if (item.type == "query") {
            query[item.key] = item.value;
          } else {
            params[item.key] = item.value;
          }
        });
      if (index === this.$route.name) return;
      if (index.indexOf("http://") > -1 || index.indexOf("https://") > -1) {
        window.open(index);
      } else {
        this.$router.push({ name: index, query, params });
      }
    },
  },
  computed: {
    ...mapGetters("router", ["asyncRouters"]),
  },
  components: {
    AsideComponent,
  },
  created() {
    this.active = this.$route.name;
    let screenWidth = document.body.clientWidth;
    if (screenWidth < 1000) {
      this.isCollapse = !this.isCollapse;
    }

    this.$bus.on("collapse", (item) => {
      this.isCollapse = item;
    });
  },
  watch: {
    $route(to, form) {
      console.log("🚀 ~ file: index.vue ~ line 83 ~ $route ~ form", form);
      console.log("🚀 ~ file: index.vue ~ line 83 ~ $route ~ to", to);
      this.active = this.$route.name;
    },
  },
  beforeDestroy() {
    this.$bus.off("collapse");
  },
};
</script>

<style lang="scss">
.el-scrollbar {
  .el-scrollbar__view {
    height: 100%;
  }
}

.el-menu-vertical {
  // background-color: rgba(48, 65, 86, 100) !important;

  .el-submenu {
    // background-color: rgba(48, 65, 86, 100) !important;

    .el-submenu__title:hover {
      // background-color: rgba(31, 45, 61, 100) !important;
    }

    &.is-opened .el-menu {
      border-left: 0 !important;

      .el-menu-item {
        // background-color: rgba(31, 45, 61, 100) !important;
      }
    }
  }
}

.menu-info {
  .menu-contorl {
    line-height: 52px;
    font-size: 20px;
    display: table-cell;
    vertical-align: middle;
  }
}
</style>
