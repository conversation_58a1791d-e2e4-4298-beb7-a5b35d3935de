<!-- 系统消息 -->
<template>
  <div class="message-box" v-if="isShow">
    <div class="notice-center-m_panel_2c6hC">
      <i class="el-icon-circle-close close-i" @click="handleClose"></i>
      <div class="notice-center-m_panel-tabs_1T-g9">
        <div
            class="notice-center-m_tab_fnO5b"
            :class="activeValue === item.value ? 'active' : ''"
            v-for="item in tabs"
            :key="item.id"
            @click="handleTabsClick(item)"
        >
          {{ item.label }}
        </div>
      </div>
      <div class="notice-center-m_list-wrap_3TBJp">
        <template v-if="messageList.length > 0">
          <div v-for="item in messageList" :key="item.id" class="message-div">
            <p class="n1Date">{{ item.created_at | formatDate }}</p>

            <div class="item">
              <div class="title f fac fjsb">
                <div>
                  <i class="el-icon-bell"></i>
                  店铺消息
                </div>
                {{ item.created_at | mydate }}
              </div>
              <div class="body">
                <p class="title-p">{{ item.title }}</p>
                <p class="body-p">{{ item.body }}</p>
                <p></p>
              </div>
            </div>
          </div>
          <el-pagination
              v-if="messageList.length > 10"
              :current-page="page" :page-size="pageSize" :total="total"
              @current-change="handleCurrentChange"
              style="text-align: center;"
              small
              layout="prev, pager, next">
          </el-pagination>
        </template>
        <div v-else class="notice-center-m_list-wrap-empty_21Z1r">
          暂无系统消息
        </div>
      </div>
      <div class="notice-center-m_panel-bottom_hHm-i">
        <!--        <a href="javascript:;">查看更多</a>-->
      </div>
      <div class="notice-center-m_panel-arrow_3RY0G"></div>
      <div class="notice-center-m_panel-arrow-mask_20SKg"></div>
    </div>
  </div>
</template>
<script>
import {getNotifyList} from "@/api/notify"
import {formatTimeToStr} from "@/utils/date";

export default {
  data() {
    return {
      isShow: false,
      activeValue: 2,
      page: 1,
      pageSize: 10,
      total: 0,
      tabs: [
        // { label: "客户消息", value: 1 },
        {label: "系统消息", value: 2},
      ],
      messageList: [],
    };
  },
  filters: {
    mydate: function (value) {
      return formatTimeToStr(value, "MM月dd日")
    }
  },
  methods: {
    handleCurrentChange(page) {
      this.page = page;
      this.handleNews();
    },
    handleTabsClick(row) {
      this.activeValue = row.value;
    },
    handleClose() {
      this.isShow = false;
    },
    handleNews() {
      if (this.isShow) {
        getNotifyList({page:this.page,pageSize:this.pageSize}).then(res => {
          if (res.code === 0) {
            this.messageList = res.data.list
            this.total = res.data.total
          } else {
            this.$message.error(res.msg)
          }
        })
      }

    }
  },
};
</script>
<style src="./messagePage/message.scss" lang="scss" scoped></style>
