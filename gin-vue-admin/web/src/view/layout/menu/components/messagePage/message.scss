.message-box {
    position: fixed;
    left: 100px;
    bottom: 0px;
    z-index: 1001;
    .notice-center-m_panel_2c6hC {
        background: #fff;
        border-radius: 3px;
        box-shadow: 0 0 8px 0 #bebebe;
        margin: 10px;
        width: 280px;
        height: 393px;
        display: -webkit-box;
        display: -webkit-flex;
        display: -moz-box;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -moz-box-orient: vertical;
        -moz-box-direction: normal;
        flex-direction: column;
        position: relative;
        font-size: 14px;
        .close-i {
            position: absolute;
            right: 12px;
            top: 12px;
            font-size: 14px;
            cursor: pointer;
            color: #666;
        }
        .notice-center-m_panel-tabs_1T-g9 {
            -webkit-box-flex: 0;
            -webkit-flex: 0 0 40px;
            -moz-box-flex: 0;
            flex: 0 0 40px;
            padding: 0 34px;
            -webkit-border-radius: 3px 3px 0 0;
            border-radius: 3px 3px 0 0;
            background-color: #fff;
            box-shadow: 0 2px 4px 0 #f3f3f3;
            display: -webkit-box;
            display: -webkit-flex;
            display: -moz-box;
            display: flex;
            -webkit-justify-content: space-around;
            justify-content: space-around;
            .notice-center-m_tab_fnO5b {
                -webkit-box-flex: 0;
                -webkit-flex: 0 0 auto;
                -moz-box-flex: 0;
                flex: 0 0 auto;
                color: #333;
                height: 100%;
                -webkit-box-sizing: border-box;
                -moz-box-sizing: border-box;
                box-sizing: border-box;
                cursor: pointer;
                line-height: 40px;
                position: relative;
                &.active {
                    border-bottom: 2px solid #38f;
                }
            }
        }
        .notice-center-m_list-wrap_3TBJp {
            -webkit-box-flex: 1;
            -webkit-flex: 1 1 100%;
            -moz-box-flex: 1;
            flex: 1 1 100%;
            overflow-x: hidden;
            overflow-y: auto;
            overscroll-behavior: contain;
            position: relative;
            background: #f3f3f3;
            .message-div {
                margin: 10px 15px 0 15px;
                &:last-child{
                    margin-bottom: 10px;
                }
                .n1Date{
                    padding: 0 10px;
                }
                .item {
                    border: 1px solid #d3d3d3;
                    border-radius: 5px;
                    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
                    margin-top: 10px;
                    .title {
                        background-color: #d4d4d4;
                        height: 30px;
                        padding: 0 10px;
                        color: #6d6d6d;
                        i{
                            font-weight: bold;
                        }
                    }
                    .body {
                        padding: 5px 20px;
                        .title-p{
                            margin-top: 5px;
                            color: #575757;
                        }
                        .body-p{
                            margin-top: 10px;
                            font-size: 13px;
                            color: #666666;
                            line-height: 18px;
                        }
                    }
                }
            }
            .notice-center-m_list-wrap-empty_21Z1r {
                font-size: 14px;
                color: #969799;
                display: -webkit-box;
                display: -webkit-flex;
                display: -moz-box;
                display: flex;
                -webkit-box-align: center;
                -webkit-align-items: center;
                -moz-box-align: center;
                align-items: center;
                -webkit-box-pack: center;
                -webkit-justify-content: center;
                -moz-box-pack: center;
                justify-content: center;
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
            }
        }
        .notice-center-m_panel-bottom_hHm-i {
            -webkit-box-flex: 0;
            -webkit-flex: 0 0 36px;
            -moz-box-flex: 0;
            flex: 0 0 36px;
            box-shadow: 0 -2px 3px 0 #f3f3f3;
            color: #333;
            text-align: center;
            line-height: 36px;
            a {
                color: #333;
            }
        }
        .notice-center-m_panel-arrow_3RY0G {
            position: absolute;
            background: #fff;
            -webkit-transform: translate(-50%, 50%) rotate(45deg);
            -moz-transform: translate(-50%, 50%) rotate(45deg);
            transform: translate(-50%, 50%) rotate(45deg);
            box-shadow: 0 1px 4px #bebebe;
            left: 0;
            bottom: 20px;
            width: 6px;
            height: 6px;
            z-index: 1;
        }
        .notice-center-m_panel-arrow-mask_20SKg {
            position: absolute;
            width: 10px;
            height: 10px;
            background: #fff;
            bottom: 15px;
            z-index: 2;
            left: 0;
        }
    }
}
