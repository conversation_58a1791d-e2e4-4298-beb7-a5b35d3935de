<template>
  <m-card>
    <el-button @click="openDialog" type="primary">新增租期</el-button>
    <el-form
        label-width="120px"
        ref="form"
        :model="searchInfoData"
        class="search-term mt25"
        inline
    >
      <el-form-item label="">
        <div class="line-input" >
            <div class="line-box ">
                <span >供应商</span>
            </div>
            <el-select  v-model="searchInfoData.suppliers_id"   clearable placeholder="请选择">
              <el-option
                  v-for="item in supplierOption"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
              ></el-option>
            </el-select>
        </div>
      </el-form-item>
      <el-form-item>
          <el-input placeholder="请输入" v-model="searchInfoData.title" class="line-input">
              <span slot="prepend">搜索名称</span>
          </el-input>
      </el-form-item>

      <el-form-item>
        <el-button @click="onSubmit" type="primary">查询</el-button>
        <el-button type="text" @click="resetSearch">重置搜索条件</el-button>
      </el-form-item>
    </el-form>
    <el-table
        :data="tableData"
        class="mt25"
    >
      <el-table-column label="排序" align="center">
        <template slot-scope="scope">
          {{ scope.row.sort}}
        </template>
      </el-table-column>
      <el-table-column label="名称" align="center">
        <template slot-scope="scope">
          {{ scope.row.title}}
        </template>
      </el-table-column>
      <el-table-column label="天数" align="center">
        <template slot-scope="scope">
          {{ scope.row.num_days}}
        </template>
      </el-table-column>
      <el-table-column label="优惠比例" align="center">
        <template slot-scope="scope">
          {{ scope.row.preferential_ratio / 100}} %
        </template>
      </el-table-column>
      <el-table-column label="供应商名称"align="center">
        <template slot-scope="scope">
          {{ scope.row.supplier.name}}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="360" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button @click="updateApplication(scope.row)" type="text"
          style="padding: 0 !important"
          >编辑
          </el-button>
          <el-button type="text" class="color-red" @click="deleteRow(scope.row)"
          style="padding: 0 !important"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
        background
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="[10, 30, 50, 100]"
        :style="{ float: 'right', padding: '20px' }"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>

    <el-dialog
        :before-close="closeDialog"
        :visible.sync="dialogFormVisible"
        :title="titleDialog"
    >
      <el-form
          :model="formData"
          ref="form"
          :rules="rules"
          label-position="right"
          label-width="160px"
          class="form-box"
      >
        <el-form-item label="排序:" >
          <el-input-number  :min="0" :controls="false" v-model="formData.sort" @change="toInteger"></el-input-number>
          <p class="paixutext">数字越大越靠前</p>
        </el-form-item>

        <el-form-item label="名称:" prop="title">
          <el-input
              v-model="formData.title"
              clearable
              placeholder="请输入"
              style="width:95%"
          ></el-input>
        </el-form-item>
        <el-form-item label="天数:" prop="num_days">
          <el-input-number  :min="0"    @change="toIntegernum" :controls="false" v-model="formData.num_days"></el-input-number><span class="tian">天</span>
        </el-form-item>
        <el-form-item label="优惠比例:" prop="preferential_ratio">
          <el-input-number  :min="0" :max="99" :controls="false" :precision="2" v-model="formData.preferential_ratio"></el-input-number> <span class="tian">%</span>
        </el-form-item>
      </el-form>
      <div class="dialog-footer" slot="footer">
        <el-button @click="enterDialog" type="primary">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </el-dialog>
  </m-card>
</template>

<script>
import {
  deleteApplication,
  getApplicationLevelOption,
} from "@/api/application"; //  此处请自行替换地址
import {getLeaseTenancyTermList,saveLeaseTenancyTerm,leaseDeleteLeaseTenancyTerm,createLeaseTenancyTerm,getLeaseTenancyTermById} from "@/api/rent";
import {getSupplierOptionList} from "@/api/order";
import infoList from "@/mixins/infoList";
import UploadImage from "@/components/upload/image.vue";
import {regionData} from "element-china-area-data";
import {getUserOptionList} from "@/api/member";
export default {
  name: "Application",
  mixins: [infoList],
  components: {
    UploadImage,
  },
  data() {
    return {
      rules: {
        title: { required: true, message: "请填写名称", trigger: "blur" },
        num_days: { required: true, message: "请填写天数", trigger: "blur" },
        preferential_ratio: { required: true, message: "请填写优惠比例", trigger: "blur" },
      },
      //搜索
      searchInfoData:{
        suppliers_id:null,
        title:''
      },
      //表格数据
      tableData:[],
      // 分页部分
      page: 1,
      pageSize: 10,
      total: 0,
      userOption: [],
      supplierOption: [],
      titleDialog: "新增",
      listApi: getLeaseTenancyTermList,
      levelApi: getApplicationLevelOption,
      dialogFormVisible: false,
      type: "",
      deleteVisible: false,
      multipleSelection: [],
      formData: {
        sort:null,
        title:'',
        num_days:null,
        preferential_ratio:null,
      },
      levelOptions: [],
      options: regionData,
      selectedOptions: [],
    };
  },
  methods: {
    // 获取归还地址
    getLeaseReturnAddress(){
      let prams = {
        "page": this.page,
        "pageSize": this.pageSize,
        "suppliers_id":this.searchInfoData.suppliers_id,
        "title":this.searchInfoData.title,
      }
      getLeaseTenancyTermList(prams).then((res) => {
        if(res.code === 0){
          this.tableData = res.data.list
          this.total = res.data.total
        }
      })
    },
    // 获取供应资源
    getSupplier() {
      getSupplierOptionList().then((res) => {
        this.supplierOption = res.data.list;
      });
    },
    getUserOption() {
      getUserOptionList({page: 1, pageSize: 9999}).then((res) => {
        if (res.code === 0) {
          this.userOption = res.data.list;
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    detail(row) {
      this.$refs.applicationDetail.isShow = true;
      this.$refs.applicationDetail.getLevelOption(row.id);
      this.$refs.applicationDetail.getApplication(row.id);
    },
    handleChangeSearch(value) {
      this.searchInfo.provinceId = parseInt(value[0] == null ? "0" : value[0]);
      this.searchInfo.cityId = parseInt(value[1] == null ? "0" : value[1]);
      this.searchInfo.districtId = parseInt(value[2] == null ? "0" : value[2]);
    },
    handleChange(value) {
      this.formData.provinceId = parseInt(value[0]);
      this.formData.cityId = parseInt(value[1]);
      this.formData.districtId = parseInt(value[2]);
    },
    resetSearch() {
      this.$refs.form.resetFields();
      this.page = 1;
      this.total = 0;
      this.pageSize = 10;
      this.searchInfoData = {};
      this.getLeaseReturnAddress();
    },
    //条件搜索前端看此方法
    onSubmit() {
      this.page = 1;
      this.pageSize = 10;
      this.getLeaseReturnAddress();
    },
    deleteRow(row) {
      this.$confirm("确定要删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        leaseDeleteLeaseTenancyTerm({id:row.id}).then((res) => {
          if(res.code == 0){
            this.$message({
              type: "success",
              message: "删除成功",
            });
            this.getLeaseReturnAddress();
          }
        })
      });
    },
    async updateApplication(row) {
      // this.getUserOption();
      this.titleDialog = "编辑";
      const res = await getLeaseTenancyTermById({id: row.id});
      this.type = "update";
      if (res.code == 0) {
        this.formData = res.data;
        this.formData.preferential_ratio = this.formData.preferential_ratio / 100
        // this.formData.pet_supplier_id = this.formData.pet_supplier_id ? this.formData.pet_supplier_id : null
        // this.formData.supplier_id = this.formData.supplier_id ? this.formData.supplier_id : null
        // this.gysarr.application_id = res.data.reapplication.id
        // this.formData.pet_suppliers =res.data.reapplication.pet_suppliers
        this.selectedOptions = [
          String(this.formData.province_id),
          String(this.formData.city_id),
          String(this.formData.county_id),
        ];
        this.dialogFormVisible = true;
      }
    },
    closeDialog() {
      try {
        this.$refs.form.resetFields();
      } catch {
      } finally {
        this.dialogFormVisible = false;
        this.titleDialog = "新增";
        // this.formData.cityId = this.formData.city_id
        // this.formData.provinceId =this.formData.province_id
        // this.formData.countyId = this.formData.county_id
        this.formData = {
          sort:null,
          title:'',
          num_days:null,
          preferential_ratio:null,
        };
        this.selectedOptions = [];
      }
    },
    async deleteApplication(row) {
      const res = await deleteApplication({id: row.id});
      if (res.code == 0) {
        this.$message({
          type: "success",
          message: "删除成功",
        });
        if (this.tableData.length == 1) {
          this.page--;
        }
        this.getTableData();
      }
    },
    // 下一页
    handleCurrentChange(page) {
      this.page = page
      this.getLeaseReturnAddress()
    },
    // 每页显示条数
    handleSizeChange(size) {
      this.pageSize = size
      this.getLeaseReturnAddress()
    },
    async enterDialog() {
      console.log(this.formData)
      this.formData.sort = this.formData.sort * 1
      this.formData.num_days = this.formData.num_days * 1
      let data = {
        "num_days": this.formData.num_days,
        "preferential_ratio":    this.formData.preferential_ratio * 100,
        "sort":  this.formData.sort,
        "title":    this.formData.title,
      }
      let dataupdata = {
        "id": this.formData.id,
        "num_days": this.formData.num_days,
        "preferential_ratio":    this.formData.preferential_ratio * 100,
        "sort":  this.formData.sort,
        "title":    this.formData.title,
      }
      let valid = await this.$refs.form.validate();
      if (valid) {
        let res;
        switch (this.type) {
          case "create":
            res = await createLeaseTenancyTerm(data);
            break;
          case "update":
            res = await saveLeaseTenancyTerm(dataupdata);
            break;
          default:
            res = await createLeaseTenancyTerm(data);
            break;
        }
        if (res.code == 0) {
          this.$message({
            type: "success",
            message: "创建/更改成功",
          });
          this.closeDialog();
          this.getLeaseReturnAddress();
        }
      } else {
        return false;
      }
    },
    toInteger() {
      let reg = /^[0-9]+$/
      if (!reg.test(this.formData.sort)) {
        this.$message.warning("只能输入整数排序")
        // 用以在dom渲染挂载后重新触发dom渲染挂载
        this.$nextTick(() => {
          this.formData.sort = parseInt(this.formData.sort)
        })
      }
    },
    toIntegernum() {
      let reg = /^[0-9]+$/
      if (!reg.test(this.formData.num_days)) {
        this.$message.warning("只能输入整数排序")
        // 用以在dom渲染挂载后重新触发dom渲染挂载
        this.$nextTick(() => {
          this.formData.num_days = parseInt(this.formData.num_days)
        })
      }
    },
    openDialog() {
      this.getUserOption();
      this.type = "create";
      this.dialogFormVisible = true;
    },
    async setLevelOption() {
      const options = await this.levelApi();
      if (options.code == 0) {
        options.data.list.map((item) => {
          const option = {
            value: item.id,
            label: item.levelName,
          };
          this.levelOptions.push(option);
        });
      }
    },
  },
  async created() {
    await this.setLevelOption();
    this.getSupplier();
    await this.getLeaseReturnAddress()
  },
};
</script>

<style lang="scss" scoped>
@import "@/style/base.scss";
.paixutext{
  color:#AFAFAF
}
::v-deep .form-box {
  padding: 0 40px;
  .el-input-number {
    width: 95%;
    .el-input__inner {
      text-align: left;
    }
  }
}
.tian{
  margin-left:10px;
}
</style>
