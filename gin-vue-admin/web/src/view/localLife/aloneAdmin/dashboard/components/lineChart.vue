<template>
    <div class="box mt-15 echarts">
        <div class="f fac fjsb">
            <p>
                <span class="title-h3" style="margin-right: 20px;">数据概括</span>
                <span style="font-size: 12px;color: rgba(136, 136, 136, 1);">数据更新时间{{ date }}</span>
            </p>
            <div class="f fac">
                <template v-for="item in datelist">
                    <div class='btn f fac ' @click="datetypefun(item.value)" v-if="datetype !== item.value">{{ item.name
                        }}</div>
                    <div class='btn red-btn f fac ' v-else>{{ item.name }}</div>
                </template>
            </div>
        </div>
        <div class="f fac fjsb">
            <div class="f mt-15 box-echarts">
                <template v-for="item in echartsTypeList">
                    <div :class="['box-echartsList', 'mr-10', item.value === echartsType ? 'echartsList-color' : '']"
                        v-if="item.type === 2" @click="typefun(item.value)">
                        <div class="echartsList-title">{{ item.name }}</div>
                        <div class="echartsList-num">{{ toMoney(item.num / 100) }}<span class="yuan">元</span></div>
                    </div>
                    <div :class="['box-echartsList', 'mr-10', item.value === echartsType ? 'echartsList-color' : '']"
                        v-else @click="typefun(item.value)">
                        <div class="echartsList-title">{{ item.name }}</div>
                        <div class="echartsList-num">{{ toCount(item.num) }}</div>
                    </div>
                </template>
            </div>
            <div id="userEcharts" class="user-echarts" ref="keyIndicatoreEcharts"></div>
        </div>
    </div>
</template>
<script>
import * as echarts from 'echarts';
import { dataOverview } from "@/api/localLife"
import { formatTimeToStr } from "@/utils/date";

export default {
    data() {
        return {
            date: '',
            datelist: [
                { name: '今日', value: 0 },
                { name: '近7日', value: 1 },
                { name: '近30日', value: 2 },
            ],
            datetype: 0,
            echartsTypeList: [
                { name: '核销券数', value: 0, num: 0, type: 1 },
                { name: '核销金额', value: 1, num: 0, type: 2 },
                { name: '退款券数', value: 2, num: 0, type: 1 },
                { name: '退款金额', value: 3, num: 0, type: 2 },
                { name: '成交券数', value: 4, num: 0, type: 1 },
                { name: '成交金额', value: 5, num: 0, type: 2 },
            ],
            echartsType: 0,
            echartslist: [], // 图标数据
        }
    },
    mounted() {
        this.getdataOverview()
    },
    methods: {
        typefun(value) {
            this.echartsType = value;
            this.getdataOverview()
        },
        datetypefun(value) {
            this.datetype = value;
            this.getdataOverview()
        },
        // 元转换
        toMoney(num) {
            if (num) {
                if (isNaN(num)) {
                    this.$message.error('金额中含有不能识别的字符')
                    return
                }
                num = typeof num == 'string' ? parseFloat(num) : num //判断是否是字符串如果是字符串转成数字
                num = num.toFixed(2) //保留两位
                num = parseFloat(num) //转成数字
                num = num.toLocaleString() //转成金额显示模式
                //判断是否有小数
                if (num.indexOf('.') == -1) {
                    num = num + '.00'
                } else {
                    num = num.split('.')[1].length < 2 ? num + '0' : num
                }
                return num //返回的是具有千分位格式并保留2位小数的字符串
            } else {
                return (num = '0.00')
            }
        },
        // 个转换
        toCount(num) {
            if (num) {
                if (isNaN(num)) {
                    this.$message.error('金额中含有不能识别的字符')
                    return
                }
                num = typeof num == 'string' ? parseFloat(num) : num //判断是否是字符串如果是字符串转成数字
                num = num.toFixed(2) //保留两位
                num = parseFloat(num) //转成数字
                num = num.toLocaleString() //转成金额显示模式
                //判断是否有小数
                if (num.indexOf('.') == -1) {
                    num = num
                } else {
                    num = num.split('.')[1].length < 2 ? num + '0' : num
                }
                return num //返回的是具有千分位格式并保留2位小数的字符串
            } else {
                return (num = '0')
            }
        },
        // 获取折线图数据
        async getdataOverview() {
            let params = {
                type: this.echartsType,
                date_type: this.datetype
            }
            let res = await dataOverview(params)
            if (res.code === 0) {
                let newDate = new Date();
                this.date = formatTimeToStr(newDate / 1000, "yyyy-MM-dd hh:mm:ss");
                this.echartsTypeList[0].num = res.data.data.vft_count;// 核销券数
                this.echartsTypeList[1].num = res.data.data.vft_amount;//核销金额
                this.echartsTypeList[2].num = res.data.data.refund_vft_count;//退款券数
                this.echartsTypeList[3].num = res.data.data.refund_amount;//退款金额
                this.echartsTypeList[4].num = res.data.data.deal_vft_count;// 成交券数
                this.echartsTypeList[5].num = res.data.data.deal_amount;// 成交金额
                let date = [];
                let amountData = []
                switch (this.echartsType) {
                    case 0:
                        this.echartslist = [...res.data.data.vft_count_details]
                        break;
                    case 1:
                        this.echartslist = [...res.data.data.vft_amount_details]
                        break;
                    case 2:
                        this.echartslist = [...res.data.data.refund_vft_details]
                        break;
                    case 3:
                        this.echartslist = [...res.data.data.refund_amount_details]
                        break;
                    case 4:
                        this.echartslist = [...res.data.data.deal_vft_details]
                        break;
                    case 5:
                        this.echartslist = [...res.data.data.deal_amount_details]
                        break;
                }
                this.echartslist.forEach(item => {
                    date.push(item.date);
                    amountData.push(item.value)
                })
                this.initChar(date, amountData, [this.echartsTypeList[this.echartsType].name])
            }
        },
        initChar(date, amountData, legendData) {
            // 处理时间显示
            let yyrr = []
            switch (this.datetype) {
                case 0:
                    date.forEach(element => {
                        yyrr.push(element.split(' ')[1] + ':00')
                    })
                    break;
                case 1:
                    yyrr = [...date]
                    break;
                case 2:
                    yyrr = [...date]
                    break;
            }
            let chartDom = document.getElementById('userEcharts');
            let chartsC = echarts.init(chartDom);
            
            chartsC.clear(); // 清空之前图标数据
            let option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985',
                            formatter: (params) => {
                                let s = ''
                                if (params.axisDimension === 'y') {
                                    if (this.echartsTypeList[this.echartsType].type === 2) {
                                        s = this.toMoney(params.value / 100) + '元'
                                    } else {
                                        s = this.toCount(params.value)
                                    }
                                }
                                return s
                            }
                        },
                    },
                    formatter: (data) => {
                        let s = ''
                        if (this.echartsTypeList[this.echartsType].type === 2) {
                            s = this.toMoney(data[0].value / 100) + '元'
                        } else {
                            s = this.toCount(data[0].value)
                        }
                        let str = `
                          <div style="height: 25px; line-height: 25px;">
                            <span style="display: inline-block; margin-right: 10px; border-radius: 50%; width: 8px; height: 8px;background-color: #155BD4;"></span>
                            <span style="color: #155BD4;">${legendData[0]}，</span>
                            <span style="color: #155BD4;">${data[0].name}，</span>
                            <span style="color: #424864;">${s}</span>
                          </div>`
                        return str
                    }
                },
                grid: {
                    left: '6%',   // 左侧内边距
                    right: '8%',  // 右侧内边距
                    bottom: '20%', // 底部内边距
                    top: '10%',    // 顶部内边距
                },
                legend: {
                    data: legendData,
                },
                xAxis: {
                    data: yyrr,
                },
                yAxis: [{
                    type: "value",
                    axisLabel: {
                        show: true,
                        interval: "auto",
                        formatter: (data) => {
                            let s = ''
                            if (this.echartsTypeList[this.echartsType].type === 2) {
                                s = this.toMoney(data / 100)
                            } else {
                                s = this.toCount(data)
                            }
                            return s
                        }
                    },
                }],
                series: [
                    {
                        type: "line",
                        data: amountData,
                        itemStyle: {
                            color: '#155BD4'
                        },
                        smooth: true,
                    },
                ]
            };
            chartsC.setOption(option);
            window.addEventListener("resize", function () {
                chartsC.resize();
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.box {
    background-color: white;
    border-radius: 12px;
    padding: 20px;
    box-sizing: border-box;
}

.title-h3 {
    font-size: 20px;
    font-weight: bold;
}

.yuan {
    font-size: 12px;
    margin-left: 5px;
    color: rgb(158, 163, 167);
}

.btn {
    height: 28px;
    margin-left: 10px;
    background-color: #F5F6F8;
    justify-content: center;
    font-size: 12px;
    border-radius: 4px;
    padding: 0 14px;
    cursor: pointer
}

.echarts {
    height: 345px;
}

.red-btn {
    background-color: rgba(21, 91, 212, 0.2);
    color: #155BD4;
}

.mt-15 {
    margin-top: 15px;
}

.box-echarts {
    width: 260px;
    flex-wrap: wrap;
    // flex-direction: column;
}

.user-echarts {
    width: calc(100% - 270px);
    height: 290px;
}

.echartsList-color {
    background: linear-gradient(180deg, #ddf1fe 0%, rgba(255, 255, 255, 0) 100%);
}

.box-echartsList {
    width: 120px;
    height: 80px;
    margin-bottom: 10px;
    padding-top: 15px;
    padding-left: 15px;
    box-sizing: border-box;
    border-radius: 12px;
    cursor: pointer;

    .echartsList-title {
        font-size: 12px;
    }

    .echartsList-num {
        width: 90px;
        font-size: 20px;
        font-weight: bold;
        margin-top: 5px;
        overflow-wrap: break-word;
    }
}

.mr-10 {
    margin-right: 10px;
}
</style>