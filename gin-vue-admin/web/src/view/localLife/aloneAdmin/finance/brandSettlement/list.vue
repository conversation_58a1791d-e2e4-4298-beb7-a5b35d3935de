<template>
    <m-card>
        <el-form :model="formData" class="search-term mt25" ref="form" inline>
            <el-form-item>
                <el-input placeholder="请输入" v-model="formData.order_sn" class="line-input" clearable>
                    <span slot="prepend">订单号</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <div class="line-input-date line-input">
                    <div class="line-box">
                        <span>结算时间</span>
                    </div>
                    <div class="f fac">
                        <el-date-picker class="w100" placeholder="开始日期" type="datetime" v-model="formData.d1">
                        </el-date-picker>
                        <p class="title-3">至</p>
                        <el-date-picker class="w100" placeholder="结束日期" type="datetime" v-model="formData.d2">
                        </el-date-picker>
                    </div>
                </div>
            </el-form-item>
            <el-form-item label-width="0px">
                <div class="f fac dateBtnBox">
                    <span :class="dateActive === item.value ? 'is_active' : ''" :key="item.id"
                        @click="handleDateTab(item)" v-for="item in dateList">{{ item.name }}</span>
                </div>
            </el-form-item>
            <br />
            <el-form-item>
                <el-button @click="tabSearch()" type="primary">搜索</el-button>
                <el-button @click="orderExport">导出</el-button>
                <el-button @click="clearSearchCondition()" type="text">重置搜索条件</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="tableData" class="mt_20">
            <el-table-column align="center" label="结算时间">
                <template slot-scope="scope">
                    {{ scope.row.settle_at | formatDate }}
                </template>
            </el-table-column>
            <el-table-column align="center" prop="order_sn" label="订单号">
            </el-table-column>
            <el-table-column align="center" prop="" label="收入">
                <template slot-scope="scope">
                    {{ scope.row.brand_settle_amount | formatF2Y }}
                </template>
            </el-table-column>
        </el-table>
        <el-pagination background :current-page="page" :page-size="pageSize" :page-sizes="[10, 30, 50, 100]" :style="{
            display: 'flex',
            justifyContent: 'flex-end',
            marginRight: '20px',
        }" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange"
            layout="total, sizes, prev, pager, next, jumper"></el-pagination>
    </m-card>
</template>
<script>
import { brandSettlement,brandSettlementExport } from "@/api/localLife";
import { formatTimeToStr } from "@/utils/date";

export default {
    name: "localLifeAFBL",
    data() {
        return {
            page: 1,
            pageSize: 10,
            total: null,
            formData: {
                order_sn: '',
                d1: '',
                d2: '',
            },
            tableData: [],
            // 时间
            dateActive: "",
            dateList: [
                { name: "今", value: 0 },
                { name: "昨", value: 1 },
                { name: "近7天", value: 2 },
                { name: "近30天", value: 3 },
            ],
        }
    },
    async mounted() {
        let params = {
            page: 1,
            pageSize: 10,
        }
        let res = await brandSettlement(params);
        if (res.code === 0) {
            this.tableData = res.data.list;
            this.total = res.data.total;
        }
    },
    methods: {
        tabSearch() {
            this.page = 1;
            this.pageSize = 10;
            this.search()
        },
        paramsfun() {
            let params = {
                page: this.page,
                pageSize: this.pageSize
            }
            if (this.formData.order_sn && this.formData.order_sn !== '') {
                params.order_sn = this.formData.order_sn
            }
            if (this.formData.d1 && this.formData.d1 !== '') {
                params.time_start = formatTimeToStr(this.formData.d1.getTime() / 1000, "yyyy-MM-dd hh:mm:ss");
            }
            if (this.formData.d2 && this.formData.d2 !== '') {
                params.time_end = formatTimeToStr(this.formData.d2.getTime() / 1000, "yyyy-MM-dd hh:mm:ss");
            }
            return params
        },
        // 搜索
        async search() {
            let params = this.paramsfun()
            let res = await brandSettlement(params);
            if (res.code === 0) {
                this.tableData = res.data.list;
                this.total = res.data.total;
            }
        },
        // 导出
        async orderExport() {
            let params = this.paramsfun()
            let res = await brandSettlementExport(params)
            if (res.code === 0) {
                this.$message.success(res.msg)
            }
        },
        clearSearchCondition() {
            this.dateActive = ""
            this.formData = {
                order_sn: '',
                d1: '',
                d2: '',
            };
            this.page = 1;
            this.pageSize = 10;
            this.search()
        },
        // 切换日期
        handleDateTab(item) {
            this.dateActive = this.dateActive === item.value ? "" : item.value;
            const todayDate = new Date();
            switch (this.dateActive) {
                case 0:
                    const dateToday1 = new Date();
                    dateToday1.setHours(0);
                    dateToday1.setMinutes(0);
                    dateToday1.setSeconds(0);
                    this.formData.d1 = dateToday1;
                    todayDate.setHours(23);
                    todayDate.setMinutes(59);
                    todayDate.setSeconds(59);
                    this.formData.d2 = todayDate;
                    break;
                case 1:
                    const dateYesterday1 = new Date();
                    dateYesterday1.setTime(dateYesterday1.getTime() - 3600 * 1000 * 24 * 1);
                    dateYesterday1.setHours(0);
                    dateYesterday1.setMinutes(0);
                    dateYesterday1.setSeconds(0);
                    this.formData.d1 = dateYesterday1;

                    const dateYesterday2 = new Date();
                    dateYesterday2.setTime(dateYesterday2.getTime() - 3600 * 1000 * 24 * 1);
                    dateYesterday2.setHours(23);
                    dateYesterday2.setMinutes(59);
                    dateYesterday2.setSeconds(59);
                    this.formData.d2 = dateYesterday2;
                    break;
                case 2:
                    const date7Day1 = new Date();
                    date7Day1.setTime(date7Day1.getTime() - 3600 * 1000 * 24 * 7);
                    date7Day1.setHours(0);
                    date7Day1.setMinutes(0);
                    date7Day1.setSeconds(0);
                    this.formData.d1 = date7Day1;

                    todayDate.setHours(23);
                    todayDate.setMinutes(59);
                    todayDate.setSeconds(59);
                    this.formData.d2 = todayDate;
                    break;
                case 3:
                    const date30Day1 = new Date();
                    date30Day1.setTime(date30Day1.getTime() - 3600 * 1000 * 24 * 30);
                    date30Day1.setHours(0);
                    date30Day1.setMinutes(0);
                    date30Day1.setSeconds(0);
                    this.formData.d1 = date30Day1;

                    todayDate.setHours(23);
                    todayDate.setMinutes(59);
                    todayDate.setSeconds(59);
                    this.formData.d2 = todayDate;
                    break;

                default:
                    break;
            }
        },
        // 分页
        handleSizeChange(page) {
            this.pageSize = page;
            this.search();
        },
        handleCurrentChange(size) {
            this.page = size;
            this.search();
        },
    }
}
</script>
<style scoped lang="scss">
@import "@/style/base.scss";
.search-term {
    .dateBtnBox {
        height: 36px;
        line-height: 36px;

        span {
            height: 38px;
            line-height: 38px;
            display: inline-block;
            margin-right: 10px;
            padding: 0 7px;
            border: 1px solid #dcdee0;
            color: #c8c9cc;
            cursor: pointer;
            box-sizing: border-box;

            &:last-child {
                margin-right: 0;
            }

            &:hover {
                color: #155bd4;
                border-color: #155bd4;
                background-color: #fff;
            }

            &.is_active {
                color: #155bd4;
                border-color: #155bd4;
                background-color: #fff;
            }
        }
    }
}

.line-input-date .line-box {
    color: #1E1E1E;
    min-width: 70px
}
</style>