<template>
    <m-card>
        <el-dialog
            :title="storeTitle"
            :visible.sync="showDialog"
            @close="cancelStore"
            width="50%"
        >
            <el-form :model="formData" label-width="160px">
                <el-row :gutter="10">
                    <el-col :span="21">
                        <el-form-item label="门店名称:">
                            <el-input
                                v-model="formData.name"
                                placeholder="请输入"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="21">
                        <el-form-item label="会员:">
                            <el-select
                                v-model="formData.user_id"
                                filterable
                                class="w100"
                            >
                                <el-option
                                    v-for="item in userOption"
                                    :key="item.id"
                                    :label="item.username"
                                    :value="item.id"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="21">
                        <el-form-item label="省市区:">
                            <!-- <el-cascader
                                filterable
                                clearable
                                class="w100"
                                :options="options"
                                v-model="formData.selectedOptions"
                                @change="handleChangeSearch"
                            >
                            </el-cascader> -->
                            <el-cascader
                                v-model="formData.selectedOptions"
                                style="width: 100%"
                                :options="regionOptions"
                                @change="handleChangeSearch"
                                clearable
                            ></el-cascader>
                        </el-form-item>
                    </el-col>
                    <el-col :span="21">
                        <el-form-item label="详细地址:">
                            <el-input
                                v-model="formData.address_detail"
                                placeholder="请输入"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="21">
                        <el-form-item label="定位:">
                            <el-col :span="10">
                                <el-input
                                    v-model="formData.longitude"
                                    placeholder="请输入经度"
                                ></el-input>
                            </el-col>
                            <el-col :span="10">
                                <el-input
                                    v-model="formData.latitude"
                                    placeholder="请输入纬度"
                                ></el-input>
                            </el-col>
                            <el-col class="pb_10" :span="2">
                                <el-button size="mini" @click="chooseCoordinate"
                                    >选择坐标</el-button
                                >
                            </el-col>
                        </el-form-item>
                    </el-col>
                    <el-col :span="21">
                        <el-form-item label="店铺电话:">
                            <el-input
                                v-model="formData.tel"
                                placeholder="请输入"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="营业时间:">
                            <el-button @click="chooseWeeks" type="primary">
                                选择星期
                            </el-button>
                            <div
                                class="mt_10"
                                v-for="(item, index) in formData.open_time"
                                :key="index"
                            >
                                <div class="f fac">
                                    <el-checkbox-group
                                        class="mt_5"
                                        v-model="
                                            formData.open_time[index].weeks
                                        "
                                    >
                                        <el-checkbox
                                            v-for="(
                                                order, weekIndex
                                            ) in item.weekList"
                                            :label="order"
                                            :key="weekIndex"
                                        ></el-checkbox>
                                    </el-checkbox-group>
                                    <el-button
                                        type="text"
                                        class="pd_0 ml_50"
                                        @click="addTime(index)"
                                    >
                                        添加时间段
                                    </el-button>
                                    <el-button
                                        type="text"
                                        class="color-red pd_0"
                                        @click="delWeeks(index)"
                                    >
                                        删除星期
                                    </el-button>
                                </div>
                                <div
                                    class="mt_10"
                                    v-for="(time, timeIndex) in item.times"
                                    :key="timeIndex"
                                >
                                    <el-time-picker
                                        v-model="
                                            formData.open_time[index].times[
                                                timeIndex
                                            ].start_time
                                        "
                                        format="HH:mm"
                                        clearable
                                        size="medium"
                                        placeholder="请选择时间"
                                    >
                                    </el-time-picker>
                                    <el-time-picker
                                        v-model="
                                            formData.open_time[index].times[
                                                timeIndex
                                            ].end_time
                                        "
                                        class="ml_10"
                                        format="HH:mm"
                                        clearable
                                        size="medium"
                                        placeholder="请选择时间"
                                    >
                                    </el-time-picker>
                                    <el-button
                                        class="color-red ml_20 pd_0"
                                        @click="delTime(index, timeIndex)"
                                        type="text"
                                    >
                                        删除
                                    </el-button>
                                </div>
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="21">
                        <el-form-item label="营业执照:">
                            <el-upload
                                class="avatar-uploader"
                                :show-file-list="false"
                                :action="path + '/fileUploadAndDownload/upload'"
                                :headers="{ 'x-token': token }"
                                :on-success="handleBusinessLicenseSuccess"
                                :before-upload="$fn.beforeAvatarUpload"
                                accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF"
                            >
                                <img
                                    v-if="formData.business_license"
                                    :src="formData.business_license"
                                    class="avatar wh100"
                                />
                                <i
                                    v-else
                                    class="el-icon-plus avatar-uploader-icon"
                                ></i>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="21">
                        <el-form-item label="身份证人像面:">
                            <el-upload
                                class="avatar-uploader"
                                :show-file-list="false"
                                :action="path + '/fileUploadAndDownload/upload'"
                                :headers="{ 'x-token': token }"
                                :on-success="handleFrontOfIdCardSuccess"
                                :before-upload="$fn.beforeAvatarUpload"
                                accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF"
                            >
                                <img
                                    v-if="formData.front_of_id_card"
                                    :src="formData.front_of_id_card"
                                    class="avatar wh100"
                                />
                                <i
                                    v-else
                                    class="el-icon-plus avatar-uploader-icon"
                                ></i>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="21">
                        <el-form-item label="身份证国徽面:">
                            <el-upload
                                class="avatar-uploader"
                                :show-file-list="false"
                                :action="path + '/fileUploadAndDownload/upload'"
                                :headers="{ 'x-token': token }"
                                :on-success="handleBackOfIdCardSuccess"
                                :before-upload="$fn.beforeAvatarUpload"
                                accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF"
                            >
                                <img
                                    v-if="formData.back_of_id_card"
                                    :src="formData.back_of_id_card"
                                    class="avatar wh100"
                                />
                                <i
                                    v-else
                                    class="el-icon-plus avatar-uploader-icon"
                                ></i>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="21">
                        <el-form-item label="设施及服务:">
                            <el-input
                                v-model="formData.facilities_and_service"
                                placeholder="请输入"
                                type="textarea"
                                rows="6"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col class="mt_20" :offset="5" :span="12">
                        <el-form-item>
                            <el-button size="mini" @click="cancelStore">
                                取消
                            </el-button>
                            <el-button
                                type="primary"
                                size="mini"
                                @click="createStoreAdmin"
                            >
                                确定
                            </el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </el-dialog>
    </m-card>
</template>

<script>
import { mapGetters } from 'vuex';
import { getUserOptionList } from '@/api/member';
import { createStoreAdmin, findStoreAdmin, updateStoreAdmin } from '@/api/localLife';
import { regiongetAreas } from '@/api/region';

export default {
    name: 'addStoreIndex',

    data() {
        return {
            storeTitle: '新增', // 弹层显示
            showDialog: false, // 是否显示弹出层
            path: this.$path,
            regionOptions: [],
            userOption: [], // 会员列表
            editId: null, // 编辑ID
            formData: {
                name: '', // 门店名称
                user_id: null, // 会员
                selectedOptions: [], // 选中的省市区
                province_id: null, // 省
                city_id: null, // 市
                area_id: null, // 区
                address_detail: '', // 详细地址
                longitude: '', // 经度
                latitude: '', // 纬度
                tel: '', // 电话
                open_time: [], // 营业时间
                business_license: '', // 营业执照
                front_of_id_card: '', // 身份证正面
                back_of_id_card: '', // 身份证背面
                facilities_and_service: '', // 设施及服务
            },
        };
    },
    computed: {
        ...mapGetters('user', ['userInfo', 'token']),
    },
    mounted() {
        this.getUserOption();
        this.getArea();
    },
    methods: {
        // 获取地区
        async getArea(parent_id = 0, checked = false) {
            let res = await regiongetAreas();
            let list = [];
            if (res.code === 0) {
                let sheng = [];
                let shi = [];
                let qu = [];
                res.data.forEach((item) => {
                    if (item.level === 1) {
                        sheng.push(item);
                    }
                    if (item.level === 2) {
                        shi.push(item);
                    }
                    if (item.level === 3) {
                        qu.push(item);
                    }
                });
                //处理省市区数组字段
                let sheng2 = [];
                let shi2 = [];
                let qu2 = [];
                sheng.forEach((item) => {
                    sheng2.push({
                        value: item.id,
                        label: item.name,
                        id: item.id,
                    });
                });
                shi.forEach((item) => {
                    shi2.push({
                        value: item.id,
                        label: item.name,
                        parent_id: item.parent_id,
                    });
                });
                qu.forEach((item) => {
                    qu2.push({
                        value: item.id,
                        label: item.name,
                        parent_id: item.parent_id,
                    });
                });
                sheng2.forEach((shengItem, shengIndex) => {
                    let cc = shi2.filter(function (value) {
                        return value.parent_id === shengItem.id;
                    });
                    sheng2[shengIndex].children = cc;
                });
                sheng2.forEach((sheng2Item) => {
                    sheng2Item.children.forEach((sheng2children, index) => {
                        let bb = qu2.filter(function (value) {
                            return value.parent_id === sheng2children.value;
                        });
                        sheng2Item.children[index].children = bb;
                    });
                });
                this.regionOptions = sheng2;
            } else {
                list = [];
            }
        },
        // 获取会员信息
        getUserOption() {
            getUserOptionList({ page: 1, pageSize: 9999 }).then((res) => {
                if (res.code === 0) {
                    this.userOption = res.data.list;
                } else {
                    this.$message.error(res.msg);
                }
            });
        },
        // 选择省市区
        handleChangeSearch(value) {
            this.formData.province_id = parseInt(
                value[0] == null ? '0' : value[0],
            );
            this.formData.city_id = parseInt(value[1] == null ? '0' : value[1]);
            this.formData.area_id = parseInt(value[2] == null ? '0' : value[2]);
        },
        // 选择坐标
        chooseCoordinate() {
            window.open('https://api.map.baidu.com/lbsapi/getpoint/index.html');
        },
        // 选择星期
        chooseWeeks() {
            if (this.formData.open_time.length > 6) {
                this.$message.error('星期最大添加七条数据');
            } else {
                this.formData.open_time.push({
                    weekList: [
                        '周日',
                        '周一',
                        '周二',
                        '周三',
                        '周四',
                        '周五',
                        '周六',
                    ], // 星期列表
                    weeks: [], // 选中的星期
                    times: [],
                });
            }
        },
        // 添加时间段
        addTime(index) {
            this.formData.open_time[index].times.push({
                start_time: '', // 营业时间开始
                end_time: '', // 营业时间结束
            });
        },
        // 删除星期
        delWeeks(index) {
            this.formData.open_time.splice(index, 1);
        },
        // 删除时间段
        delTime(index, timeIndex) {
            this.formData.open_time[index].times.splice(timeIndex, 1);
        },
        // 上传营业执照
        handleBusinessLicenseSuccess(res) {
            if (res.code === 0) {
                this.formData.business_license = res.data.file.url;
            } else {
                this.$message.error(res.msg);
            }
        },
        // 上传身份证正面
        handleFrontOfIdCardSuccess(res) {
            if (res.code === 0) {
                this.formData.front_of_id_card = res.data.file.url;
            } else {
                this.$message.error(res.msg);
            }
        },
        // 上传身份证背面
        handleBackOfIdCardSuccess(res) {
            if (res.code === 0) {
                this.formData.back_of_id_card = res.data.file.url;
            } else {
                this.$message.error(res.msg);
            }
        },
        // 编辑门店
        async editStore(id) {
            const params = {
                id: parseInt(id),
            };
            const res = await findStoreAdmin(params);
            if (res.code === 0) {
                this.showDialog = true;
                if (res.data.open_time !== null) {
                    res.data.open_time.forEach((item) => {
                        item.weekList = [
                            '周日',
                            '周一',
                            '周二',
                            '周三',
                            '周四',
                            '周五',
                            '周六',
                        ];
                        item.times.forEach((items) => {
                            items.start_time = parseInt(items.start_time);
                            items.end_time = parseInt(items.end_time);
                        });
                    });
                } else {
                    res.data.open_time = []
                }
                this.formData = res.data;
                let options = [];
                options.push(res.data.province_id);
                options.push(res.data.city_id);
                options.push(res.data.area_id);
                this.formData.selectedOptions = options;
            }
        },
        // 创建门店
        async createStoreAdmin() {
            // 时间戳转换
            this.formData.open_time.forEach((item) => {
                item.times.forEach((items) => {
                    items.start_time = new Date(items.start_time)
                        .getTime()
                        .toString();
                    items.end_time = new Date(items.end_time)
                        .getTime()
                        .toString();
                });
            });
            // 星期选项重复
            let weeks = [];
            let chooseWeeks = false;
            this.formData.open_time.forEach((item) => {
                item.weeks.forEach((items) => {
                    let newWeeks = weeks.find((i) => i === items);
                    if (!newWeeks) {
                        weeks.push(items);
                    } else {
                        chooseWeeks = true;
                        return;
                    }
                });
            });
            if (chooseWeeks) {
                this.$message.error('星期不能重复选择');
                return;
            }
            if (this.editId) {
                this.formData.id = parseInt(this.editId);
                const data = this.formData;
                const res = await updateStoreAdmin(data);
                if (res.code === 0) {
                    this.$message.success(res.msg);
                }
            } else {
                const data = this.formData;
                const res = await createStoreAdmin(data);
                if (res.code === 0) {
                    this.$message.success(res.msg);
                }
            }
            this.showDialog = false;
            this.cancelStore();
            this.$emit('get-list');
        },
        // 取消创建
        cancelStore() {
            this.showDialog = false;
            this.formData = {
                name: '',
                user_id: null,
                province_id: null,
                city_id: null,
                area_id: null,
                address_detail: '',
                longitude: '',
                latitude: '',
                tel: '',
                open_time: [],
                business_license: '',
                front_of_id_card: '',
                back_of_id_card: '',
                facilities_and_service: '',
            };
        },
    },
};
</script>

<style lang="scss" scoped>
::v-deep .el-input--medium .el-input__inner {
    border-radius: 5px 5px 5px 5px !important;
}
::v-deep .avatar-uploader {
    .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        width: 140px;
        height: 140px;
        .avatar-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            line-height: 140px;
            text-align: center;
        }
    }
}
.color-red {
    color: red;
}
.pd_0 {
    padding: 0px !important;
}
</style>
