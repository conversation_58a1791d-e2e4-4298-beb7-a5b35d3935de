<template>
    <m-card>
        <el-button @click="addStore" type="primary">新增</el-button>
        <el-form :model="searchInfo" ref="form" class="mt25" inline>
            <!-- <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.brand_id"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">品牌</span>
                </el-input>
            </el-form-item> -->
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    clearable
                    v-model="searchInfo[shopCommand]"
                    class="line-input-width"
                >
                    <template slot="prepend">
                        <el-dropdown
                            class="el-dropdown-row"
                            @command="handleShopCommand"
                        >
                            <span class="el-dropdown-link">
                                {{ returnShopValue(shopCommand)
                                }}<i
                                    class="el-icon-arrow-down el-icon--right"
                                ></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item command="user_id">
                                    店长会员ID
                                </el-dropdown-item>
                                <el-dropdown-item command="tel">
                                    手机号
                                </el-dropdown-item>
                                <el-dropdown-item command="store_member_name">
                                    店长昵称
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </template>
                </el-input>
            </el-form-item>
            <el-form-item prop="brand_name">
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.id"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">门店ID</span>
                </el-input>
            </el-form-item>
            <el-form-item prop="brand_name">
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.name"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">门店名称</span>
                </el-input>
            </el-form-item>
            <el-form-item prop="brand_name">
                <div class="line-input">
                    <div class="line-box">
                        <span>省市区</span>
                    </div>
                    <!-- <el-cascader
                        filterable
                        clearable
                        class="w100"
                        :options="options"
                        v-model="searchInfo.selectedOptions"
                        @change="handleChangeSearch"
                    >
                    </el-cascader> -->
                    <el-cascader
                        v-model="searchInfo.selectedOptions"
                        style="width: 100%"
                        :options="regionOptions"
                        @change="handleChangeSearch"
                        clearable
                    ></el-cascader>
                </div>
            </el-form-item>
            <el-form-item prop="brand_name">
                <div class="line-input">
                    <div class="line-box">
                        <span>状态</span>
                    </div>
                    <el-select
                        class="w100"
                        v-model="searchInfo.status"
                        clearable
                    >
                        <el-option label="开启" :value="1"></el-option>
                        <el-option label="禁用" :value="0"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <el-button @click="searchForm" type="primary">搜索</el-button>
                <el-button @click="exportForm">导出</el-button>
                <el-button type="text" @click="resetForm"
                    >重置搜索条件</el-button
                >
            </el-form-item>
        </el-form>
        <el-table class="mt_20" :data="tableList">
            <el-table-column align="center" width="80" prop="id" label="ID">
            </el-table-column>
            <el-table-column align="center" width="150" label="入驻时间">
                <template slot-scope="scope">
                    {{ scope.row.created_at | formatDate }}
                </template>
            </el-table-column>
            <el-table-column align="center" prop="name" label="门店名称">
            </el-table-column>
            <el-table-column align="center" label="会员ID/手机号">
                <template slot-scope="scope">
                    <p>{{ scope.row.user_id }}</p>
                    <p>{{ scope.row.tel }}</p>
                </template>
            </el-table-column>
            <el-table-column width="200" align="center">
                <template slot="header">
                    <p>所在省市</p>
                    <p>详细地址</p>
                </template>
                <template slot-scope="scope">
                    <p>
                        {{ scope.row.province }}{{ scope.row.city
                        }}{{ scope.row.area }}
                    </p>
                    <p>{{ scope.row.address_detail }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="brand.name" label="品牌">
            </el-table-column>
            <el-table-column align="center" prop="brand.source" label="渠道">
            </el-table-column>
            <el-table-column
                align="center"
                prop="verification_num"
                label="核销数量"
            >
            </el-table-column>
            <el-table-column align="center" label="收入">
                <template slot-scope="scope">
                    ￥{{ scope.row.total_amount | formatF2Y }}
                </template>
            </el-table-column>
            <el-table-column align="center" label="状态">
                <template slot-scope="scope">
                    <el-switch
                        v-model="scope.row.status"
                        @change="changeStatus(scope.row)"
                        :active-value="1"
                        :inactive-value="0"
                    >
                    </el-switch>
                </template>
            </el-table-column>
            <el-table-column align="center" width="120" label="操作">
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        style="padding: 0 !important"
                        @click="editStore(scope.row.id)"
                    >
                        编辑
                    </el-button>
                    <el-button
                        class="color-red"
                        type="text"
                        style="padding: 0 !important"
                        @click="deleteStoreAdmin(scope.row.id)"
                    >
                        删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :style="{
                display: 'flex',
                justifyContent: 'flex-end',
                marginRight: '20px',
            }"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes,prev, pager, next, jumper"
        >
        </el-pagination>
        <add-store @get-list="storelistadmin()" ref="addStore"></add-store>
    </m-card>
</template>
<script>
import addStore from './components/addStore.vue';
import { confirm } from '@/decorators/decorators';
import { regiongetAreas } from '@/api/region';
import {
    storelistadmin,
    deleteStoreAdmin,
    displayStoreByIdsStoreAdmin,
    exportStoreAdmin,
} from '@/api/localLife';

export default {
    name: 'localLifeASFML',
    components: { addStore },
    data() {
        return {
            regionOptions: [],
            searchInfo: {
                brand_id: null, // 品牌ID
                user_id: null, // 会员ID
                tel: null, // 手机号
                store_member_name: null, // 昵称
                name: '', // 门店名称
                id: null, // 门店ID
                selectedOptions: [], // 选中的省市区
                province_id: null, // 省
                city_id: null, // 市
                area_id: null, // 区
                status: null, // 状态
            },
            shopCommand: 'user_id', // 选中的搜索方式
            tableList: [], // 列表数据
            page: 1, // 当前页数
            pageSize: 10, // 每页条数
            total: 0, // 总数
        };
    },
    mounted() {
        this.storelistadmin();
        this.getArea();
    },
    methods: {
        // 获取地区
        async getArea(parent_id = 0, checked = false) {
            let res = await regiongetAreas();
            let list = [];
            if (res.code === 0) {
                let sheng = [];
                let shi = [];
                let qu = [];
                res.data.forEach((item) => {
                    if (item.level === 1) {
                        sheng.push(item);
                    }
                    if (item.level === 2) {
                        shi.push(item);
                    }
                    if (item.level === 3) {
                        qu.push(item);
                    }
                });
                //处理省市区数组字段
                let sheng2 = [];
                let shi2 = [];
                let qu2 = [];
                sheng.forEach((item) => {
                    sheng2.push({
                        value: item.id,
                        label: item.name,
                        id: item.id,
                    });
                });
                shi.forEach((item) => {
                    shi2.push({
                        value: item.id,
                        label: item.name,
                        parent_id: item.parent_id,
                    });
                });
                qu.forEach((item) => {
                    qu2.push({
                        value: item.id,
                        label: item.name,
                        parent_id: item.parent_id,
                    });
                });
                sheng2.forEach((shengItem, shengIndex) => {
                    let cc = shi2.filter(function (value) {
                        return value.parent_id === shengItem.id;
                    });
                    sheng2[shengIndex].children = cc;
                });
                sheng2.forEach((sheng2Item) => {
                    sheng2Item.children.forEach((sheng2children, index) => {
                        let bb = qu2.filter(function (value) {
                            return value.parent_id === sheng2children.value;
                        });
                        sheng2Item.children[index].children = bb;
                    });
                });
                this.regionOptions = sheng2;
            } else {
                list = [];
            }
        },
        addStore() {
            this.$refs.addStore.storeTitle = '新增';
            this.$refs.addStore.editId = null;
            this.$refs.addStore.showDialog = true;
        },
        // 获取门店列表
        async storelistadmin() {
            const params = {
                brand_id: this.searchInfo.brand_id
                    ? parseInt(this.searchInfo.brand_id)
                    : null,
                user_id: this.searchInfo.user_id
                    ? parseInt(this.searchInfo.user_id)
                    : null,
                tel: this.searchInfo.tel ? parseInt(this.searchInfo.tel) : null,
                store_member_name: this.searchInfo.store_member_name
                    ? this.searchInfo.store_member_name
                    : '',
                name: this.searchInfo.name ? this.searchInfo.name : '',
                id: this.searchInfo.id ? parseInt(this.searchInfo.id) : null,
                province_id: this.searchInfo.province_id
                    ? parseInt(this.searchInfo.province_id)
                    : null,
                city_id: this.searchInfo.city_id
                    ? parseInt(this.searchInfo.city_id)
                    : null,
                area_id: this.searchInfo.area_id
                    ? parseInt(this.searchInfo.area_id)
                    : null,
                status:
                    this.searchInfo.status !== null
                        ? parseInt(this.searchInfo.status)
                        : null,
                page: this.page,
                pageSize: this.pageSize,
            };
            const res = await storelistadmin(params);
            if (res.code === 0) {
                this.tableList = res.data.list;
                this.total = res.data.total;
            }
        },
        // 选择省市区
        handleChangeSearch(value) {
            this.searchInfo.province_id = parseInt(
                value[0] == null ? '0' : value[0],
            );
            this.searchInfo.city_id = parseInt(
                value[1] == null ? '0' : value[1],
            );
            this.searchInfo.area_id = parseInt(
                value[2] == null ? '0' : value[2],
            );
        },
        // 选择搜索条件
        handleShopCommand(command) {
            this.shopCommand = command;
            this.searchInfo.user_id = null;
            this.searchInfo.tel = null;
            this.searchInfo.store_member_name = null;
        },
        // 搜索条件列表
        returnShopValue(command) {
            let text = '店长会员ID';
            switch (command) {
                case 'user_id':
                    text = '店长会员ID';
                    break;
                case 'tel':
                    text = '手机号';
                    break;
                case 'store_member_name':
                    text = '店长名称';
                    break;
                default:
                    break;
            }
            return text;
        },
        // 修改门店状态
        async changeStatus(row) {
            const data = {
                ids: [row.id],
                value: parseInt(row.status),
            };
            const res = await displayStoreByIdsStoreAdmin(data);
            if (res.code === 0) {
                this.$message.success('操作成功');
                this.storelistadmin();
            }
        },
        // 编辑门店
        async editStore(id) {
            this.$refs.addStore.storeTitle = '编辑';
            this.$refs.addStore.editId = id;
            this.$refs.addStore.editStore(id);
        },
        // 删除门店
        @confirm('提示', '确定删除?')
        async deleteStoreAdmin(id) {
            const data = {
                id: parseInt(id),
            };
            const res = await deleteStoreAdmin(data);
            if (res.code === 0) {
                this.$message.error(res.msg);
                this.storelistadmin();
            }
        },
        // 搜索列表
        searchForm() {
            this.page = 1;
            this.storelistadmin();
        },
        // 导出表格
        async exportForm() {
            const data = {
                brand_id: this.searchInfo.brand_id
                    ? parseInt(this.searchInfo.brand_id)
                    : null,
                user_id: this.searchInfo.user_id
                    ? parseInt(this.searchInfo.user_id)
                    : null,
                tel: this.searchInfo.tel ? parseInt(this.searchInfo.tel) : null,
                store_member_name: this.searchInfo.store_member_name
                    ? this.searchInfo.store_member_name
                    : '',
                name: this.searchInfo.name ? this.searchInfo.name : '',
                id: this.searchInfo.id ? parseInt(this.searchInfo.id) : null,
                province_id: this.searchInfo.province_id
                    ? parseInt(this.searchInfo.province_id)
                    : null,
                city_id: this.searchInfo.city_id
                    ? parseInt(this.searchInfo.city_id)
                    : null,
                area_id: this.searchInfo.area_id
                    ? parseInt(this.searchInfo.area_id)
                    : null,
                status:
                    this.searchInfo.status !== null
                        ? parseInt(this.searchInfo.status)
                        : null,
            };
            const res = await exportStoreAdmin(data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.download(res.data.link);
            }
        },
        // 导出方法
        download(link) {
            window.open(this.$path + '/' + link);
        },
        // 重置搜索条件
        resetForm() {
            this.searchInfo = {
                brand_id: null, // 品牌ID
                user_id: null, // 会员ID
                tel: null, // 手机号
                store_member_name: null, // 昵称
                name: '', // 门店名称
                id: null, // 门店ID
                selectedOptions: [], // 选中的省市区
                province_id: null, // 省
                city_id: null, // 市
                area_id: null, // 区
                status: null, // 状态
            };
            this.shopCommand = 'user_id';
        },
        // 当前页数
        handleCurrentChange(page) {
            this.page = page;
            this.storelistadmin();
        },
        // 每页条数
        handleSizeChange(size) {
            this.pageSize = size;
            this.storelistadmin();
        },
    },
};
</script>
<style scoped lang="scss">
.color-red {
    color: red;
}
</style>
