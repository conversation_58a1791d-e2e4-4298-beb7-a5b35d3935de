<template>
    <el-drawer title="详情" :visible="isShow" :close-on-press-escape="false" :wrapperClosable="false"
        :before-close="handleClose" size="calc(100% - 220px)" class="detail-ct">
        <m-card style="margin-top: 20px">
            <div class="detail">
                <div class="w100 f fac fjsb ">
                    <div class="f fac " style="margin-left: 20px">
                        <p style="color: orange;font-size: 22px">仅退款<span v-if="DetailsTable.status === 2">成功</span></p>
                        <p style="margin-left: 10px;font-size: 16px">售后编号 :{{ DetailsTable.after_sale_sn }} </p>
                        <p v-if="DetailsTable.order ? DetailsTable.order.third_order_sn !== '' : '_'"
                            style="margin-left: 10px;font-size: 16px">第三方订单编号 :{{ DetailsTable.order ?
                                DetailsTable.order.third_order_sn : "_" }} </p>
                        <p v-if="DetailsTable.application ? DetailsTable.application.app_name !== '' : '_'"
                            style="margin-left: 10px;font-size: 16px">采购端名称 :{{ DetailsTable.application ?
                                DetailsTable.application.app_name : "_" }} </p>
                    </div>
                    <div class="f fac">
                        <p>申请人 : <span>{{ DetailsTable.user ? DetailsTable.user.username : "_" }}</span></p>
                        <p style="margin-left: 20px">申请时间 :
                            {{ DetailsTable.created_at ? DetailsTable.created_at : "" |
                                formatDate }}</p>
                    </div>
                </div>
                <div style="margin-left: 20px ; margin-top: 10px">
                    <span>商品退款金额 : <span style="color: red; font-size: 16px">￥:{{ DetailsTable.amount | formatF2Y
                            }}</span></span>
                </div>
                <div v-if="DetailsTable.status === 0">
                    <el-button type="text" style="margin-left: 20px" @click="pass">审核通过</el-button>
                    <el-button type="text" style="margin-left: 20px" @click="reject">驳回申请</el-button>
                    <el-button type="text" style="margin-left: 20px" @click="close">关闭售后</el-button>
                </div>
                <div v-if="DetailsTable.status === 1">
                    <el-button type="text" style="margin-left: 20px" @click="refund">售后退款</el-button>
                    <el-button type="text" style="margin-left: 20px" @click="close">关闭售后</el-button>
                </div>
                <div v-if="DetailsTable.status === 2">
                    <el-tag style="margin: 20px;">已完成</el-tag>
                </div>
                <div v-if="DetailsTable.status === -2">
                    <el-tag style="margin: 20px;">已关闭</el-tag>
                </div>
                <div v-if="DetailsTable.status === -1">
                    <el-tag type="danger" style="margin: 20px;">商家已驳回</el-tag>
                    <el-button type="text" style="margin-left: 20px" @click="close">关闭售后</el-button>
                </div>
            </div>
            <div class="detail_two">
                <p style="color: orange">退款提醒 : </p>
                <p class="color-grap">如通过 "微信支付" 付款订单,退款3-7个工作日到账</p>
            </div>
        </m-card>
        <m-card style="margin-top: 20px">
            <div class="detail_goods">
                <div>
                    <h3>售后商品</h3>
                    <img :src="DetailsTable.order_item ? DetailsTable.order_item.image_url : ''"
                        style="width: 60px;height: 60px;margin-top: 10px" />
                    <p>{{ DetailsTable.order_item ? DetailsTable.order_item.title : '' }}</p>
                </div>
                <div>
                    <h3>售后信息</h3>
                    <p>售后方式 : <span style="color: red">仅退款</span></p>
                    <p>退款方式 : <span style="color: red">按商品个数</span></p>
                    <p>退款商品个数 : <span style="color: red"> {{ DetailsTable.num }}</span></p>
                    <p>退款金额 : <span style="color: red">￥ {{ DetailsTable.amount | formatF2Y }}</span></p>
                    <p>技术服务费 : <span style="color: red">￥ {{ DetailsTable.technical_services_fee | formatF2Y }}</span>
                    </p>
                    <p>联系方式 : <span>{{ DetailsTable.user ? DetailsTable.user.username : "" }}</span></p>
                    <p>退款原因 : {{ DetailsTable.refund_reason_name }}</p>
                    <p>退款说明 : {{ DetailsTable.description }} </p>
                    <p>上传凭证:
                        <span v-for="item in DetailsTable.detail_images">
                            <el-image style="width: 100px; height: 100px" :src="item"
                                :preview-src-list="DetailsTable.detail_images">
                            </el-image>
                        </span>
                    </p>
                </div>
                <div>
                    <h3>购买信息</h3>
                    <p>商品单价 : <span style="color: red" v-if="DetailsTable.order_item">￥: {{
                        DetailsTable.order_item.amount | formatF2Y }}</span></p>
                    <p>实付金额 : <span style="color: red">￥:{{
                        DetailsTable.order_item ? $options.filters.formatF2Y(DetailsTable.order_item.payment_amount)
                            : 0.00
                            }}</span>
                        <span v-if="DetailsTable.status === 2">(已退 ￥:{{
                            DetailsTable.order_item ? $options.filters.formatF2Y(DetailsTable.order_item.refund_amount)
                                : 0.00
                        }}) </span>
                    </p>
                    <p>使用状态 : {{ DetailsTable.order ? DetailsTable.order.status : "" | formatStatus }}</p>
                    <p>订单编号 : <el-button type="text" @click="jumpOrder(DetailsTable.order.order_sn)">{{
                        DetailsTable.order ? DetailsTable.order.order_sn : ""
                            }}</el-button></p>
                </div>
            </div>
        </m-card>
        <m-card style="margin-top: 20px">
            <div class="detail_xs">
                <div class="detail_xs_left">
                    <h3>协商记录</h3>
                </div>
            </div>
            <el-steps direction="vertical" :active="1" style="margin-top: 30px;margin-left: 10px">
                <el-step class="xc" v-for="item in DetailsTable.logs">
                    <template slot="description">
                        <div class="detail_xs_row" style="width: 100%">
                            <div class="detail_xs_count">
                                <div class="detail_xs_row_left">
                                    <p> {{ item.content }}</p>
                                </div>
                                <div class="f fac">
                                    <p style="color: gray">{{ item.created_at }}</p>
                                </div>
                            </div>
                            <div class="detail_xs_reason">
                                <p style="color: gray">退款金额 :{{ item.price | formatF2Y }} </p>
                                <p style="color: gray">技术服务费 :{{ item.technical_services_fee | formatF2Y }} </p>
                            </div>
                        </div>
                    </template>
                </el-step>
            </el-steps>
        </m-card>
        <!-- 申请驳回 -->
        <el-dialog title="申请驳回" :visible="rejectShow" width="30%" :modal-append-to-body="false" :append-to-body="true"
            :before-close="dialogRejectShow">
            <el-input type="textarea" :rows="4" placeholder="请输入内容" v-model="cause">
            </el-input>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogRejectShow">取 消</el-button>
                <el-button type="primary" @click="dialogReject">确 定</el-button>
            </span>
        </el-dialog>
    </el-drawer>
</template>
<script>
import {
    getAfterSales,
    afterSalesPass,
    afterSalesRefund,
    afterSalesReject,
    afterSalesClose
} from '@/api/localLife';
export default {
    data() {
        return {
            isShow: false,
            afterSaleId: null,
            DetailsTable: [],

            // 申请驳回
            rejectShow: false,
            cause: ''
        }
    },
    filters: {
        // 格式化订单状态
        formatStatus: function (status) {
            let name = ""
            switch (status) {
                case 0:
                    name = "待支付"
                    break;
                case 1:
                    name = "待使用"
                    break;
                case 2:
                    name = "部分使用"
                    break;
                case 3:
                    name = "已完成"
                    break;
                case -1:
                    name = "已关闭"
                    break;
            }
            return name;
        }
    },
    methods: {
        info(id) {
            this.isShow = true;
            this.afterSaleId = id
            this.getAfterSalesfun()
        },
        async getAfterSalesfun() {
            let res = await getAfterSales({ id: this.afterSaleId })
            if (res.code === 0) {
                this.DetailsTable = res.data
            }
        },
        handleClose() {
            this.isShow = false;
            this.afterSaleId = null
            this.DetailsTable = {}
            this.$emit('handleClose')
        },
        // 审核通过
        pass() {
            this.$confirm("确定要通过审核?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                let res = await afterSalesPass({ id: this.afterSaleId });
                if (res.code === 0) {
                    this.$message.success(res.msg)
                    this.getAfterSalesfun()
                }
            })
        },
        // 售后退款
        refund() {
            this.$confirm("确定要退款?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                let res = await afterSalesRefund({ id: this.afterSaleId });
                if (res.code === 0) {
                    this.$message.success(res.msg)
                    this.getAfterSalesfun()
                }
            })
        },
        // 驳回请求
        reject() {
            this.rejectShow = true
        },
        // 驳回请求 确认
        async dialogReject() {
            let res = await afterSalesReject({ id: this.afterSaleId, cause: this.cause })
            if (res.code === 0) {
                this.$message.success(res.msg)
                this.getAfterSalesfun()
                this.dialogRejectShow()
            }
        },
        // 驳回请求 取消
        dialogRejectShow() {
            this.rejectShow = false
            this.cause = ""
        },
        // 关闭请求
        close() {
            this.$confirm("确定要关闭?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                let res = await afterSalesClose({ id: this.afterSaleId })
                if (res.code === 0) {
                    this.$message.success(res.msg)
                    this.getAfterSalesfun()
                }
            })
        },
        jumpOrder(order_sn) {
            let routeUrl = this.$router.resolve({
                name: "localLifeMOL",
                query: { order_sn }
            })
            window.open(routeUrl.href, '_blank');
        },
    }
}
</script>
<style lang="scss" scoped>
@import "@/style/base.scss";

.wl {
    ::v-deep .el-step__icon.is-text {
        width: 18px;
        height: 18px;
        margin-left: 3px;

    }

    ::v-deep .el-step__line {
        background: #f0f2f5;

    }
}

.detail {
    width: 100%;
    border-bottom: 3px solid #f0f2f5;
}

.detail_two {
    margin-left: 20px;
    margin-top: 10px;

    p {
        margin-top: 10px;
    }
}

.detail_goods {
    width: 100%;
    display: flex;

    div {
        flex: 1px;
        margin-left: 20px;

        p {
            margin-top: 10px;
            font-size: 14px;
            color: gray;
        }
    }
}

.detail_wl {
    width: 100%;

    .detail_wl_right {
        overflow: auto;

        .detail_wlxq {
            display: flex;

            .detail_wlxq_left {
                flex: 1;
            }

            .detail_wlxq_right {
                flex: 4;
            }
        }
    }
}

.detail_xs {
    width: 100%;
    display: flex;
    border-bottom: 3px solid #f0f2f5;

    .detail_xs_left {
        padding-left: 20px;
        margin-bottom: 10px;
        flex: 1;
    }

    .detail_xs_right {
        p {
            margin-right: 10px;
        }
    }
}

.xc {
    ::v-deep .el-step__description {
        padding-right: 0px;
    }
}

.detail_xs_row {
    margin-top: -30px;
    margin-bottom: 20px;
    background-color: #f0f2f5;
    width: 100%;

    //border: 1px solid red;
    .detail_xs_count {
        margin-top: 10px;
        padding: 10px;
        display: flex;
        border-bottom: 1px solid #e1e3e4;

        .detail_xs_row_left {
            flex: 1;
            padding: 10px;

            p {
                color: black;
                font-weight: 900;

                span {
                    color: gray;
                }
            }
        }
    }

    .detail_xs_reason {
        padding: 20px;
        margin-top: -18px;

        p {
            margin-top: 10px;
            color: gray;
            color: black;
        }
    }
}

::v-deep .address-radio .el-radio__label {
    display: none;
}
</style>