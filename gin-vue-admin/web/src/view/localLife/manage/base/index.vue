<template>
    <m-card>
        <el-tabs v-model="activeName" type="card">
            <el-tab-pane label="基础设置" name="baseSetting">
                <el-form :model="formData" label-width="120px">
                    <el-form-item label="插件开关:">
                        <el-radio-group v-model="formData.switch">
                            <el-radio :label="1">开启</el-radio>
                            <el-radio :label="0">关闭</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="结算期:">
                        <div style="width:300px">
                            <m-num-input v-model="formData.settlementPeriod" endText="天"></m-num-input>
                        </div>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="save">保存</el-button>
                    </el-form-item>
                </el-form>
            </el-tab-pane>
        </el-tabs>
    </m-card>
</template>
<script>
import { find, update } from '@/api/localLife';

export default {
    name: 'localLifeMBIndex',
    data() {
        return {
            activeName: 'baseSetting',
            formData: {
                id: 0,
                switch: 0, // 0关闭 1开启
                settlementPeriod: null // 结算期
            },
        };
    },
    mounted() {
        this.init();
    },
    methods: {
        async init() {
            const { code, data } = await find();
            if (code === 0) {
                this.formData.id = data.setting.id || 0;
                this.formData.switch = data.setting.value.switch;
                this.formData.settlementPeriod = data.setting.value.settlementPeriod
            }
        },
        async save() {
            let params = {
                id: this.formData.id,
                value: {
                    switch: this.formData.switch,
                    settlementPeriod: this.formData.settlementPeriod
                },
            };
            const { code, msg } = await update(params);
            if (code === 0) {
                this.$message.success(msg);
            }
        },
    },
};
</script>
<style scoped lang="scss"></style>
