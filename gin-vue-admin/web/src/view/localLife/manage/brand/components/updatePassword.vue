<template>
    <el-dialog :before-close="handleDialogClose" :visible="isShow" title="修改密码" width="700px">
        <el-form :model="searchform" :rules="rules" label-width="90px" ref="searchform">
            <el-form-item label="品牌名称:" prop="brand_name">
                <el-input class="w100" v-model="searchform.brand_name" disabled clearable>
                </el-input>
            </el-form-item>
            <el-form-item label="账号:" prop="username">
                <el-input class="w100" v-model="searchform.username" disabled clearable>
                </el-input>
            </el-form-item>
            <el-form-item label="密码:" prop="pwd">
                <el-input class="w100" v-model="searchform.pwd" clearable>
                </el-input>
            </el-form-item>
            <el-form-item label="确认密码:" prop="confirm_pwd">
                <el-input class="w100" v-model="searchform.confirm_pwd" clearable>
                </el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="notarize" type="primary">确认</el-button>
            <el-button @click="handleDialogClose">取消</el-button>
        </div>
    </el-dialog>
</template>
<script>
import { updatePassword } from '@/api/localLife';

export default {
    data() {
        return {
            isShow: false,
            id: null,
            searchform: {
                username: '',
                brand_name: '',
                pwd: '', // 密码
                confirm_pwd: ''// 再次确认密码
            },
            rules: {
                pwd: [
                    { required: true, message: '请填写密码', trigger: 'blur' }
                ],
                confirm_pwd: [
                    { required: true, message: '请再次填写密码', trigger: 'blur' }
                ]
            }
        }
    },
    methods: {
        // 页面赋值
        info(id, brand_name, username) {
            this.id = id;
            this.searchform.username = username
            this.searchform.brand_name = brand_name
            this.isShow = true
        },
        // 关闭按钮
        handleDialogClose() {
            this.isShow = false;
            this.id = null;
            this.$refs.searchform.resetFields();
            this.searchform = {
                username: '',
                brand_name: '',
                pwd: '', // 密码
                confirm_pwd: ''// 再次确认密码
            }
        },
        // 确认修改密码
        notarize() {
            this.$refs.searchform.validate(async (v) => {
                if (v) {
                    let params = {
                        brand_id: parseInt(this.id),
                        pwd: this.searchform.pwd,
                        confirm_pwd: this.searchform.confirm_pwd
                    }
                    let res = await updatePassword(params)
                    if (res.code === 0) {
                        this.$message.success(res.msg);
                        this.handleDialogClos()
                        this.$emit('reset')
                    }
                }
            })
        }
    }
}
</script>
<style lang="scss" scoped></style>