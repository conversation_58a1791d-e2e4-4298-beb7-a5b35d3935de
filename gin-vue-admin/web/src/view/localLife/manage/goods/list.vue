<template>
    <m-card>
        <el-form :model="searchInfo" ref="form" class="mt25" inline>
            <el-form-item>
                <div class="line-input">
                    <div class="line-box">
                        <span>品牌</span>
                    </div>
                    <el-input
                        v-model="searchInfo.brand_name"
                        placeholder="请输入"
                    ></el-input>
                </div>
            </el-form-item>
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    clearable
                    v-model="searchInfo[shopCommand]"
                    class="line-input-width"
                >
                    <template slot="prepend">
                        <el-dropdown
                            class="el-dropdown-row"
                            style="height: 30px"
                            @command="handleShopCommand"
                        >
                            <span class="el-dropdown-link">
                                {{ returnShopValue(shopCommand)
                                }}<i
                                    class="el-icon-arrow-down el-icon--right"
                                ></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item command="product_title">
                                    商品名称
                                </el-dropdown-item>
                                <el-dropdown-item command="product_id">
                                    商品ID
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </template>
                </el-input>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input">
                    <div class="line-box">
                        <span>商品类型</span>
                    </div>
                    <el-select v-model="searchInfo.product_type">
                        <el-option label="全部类型" :value="0"></el-option>
                        <el-option label="团购券" :value="1"></el-option>
                        <el-option label="代金券" :value="2"></el-option>
                        <el-option label="次卡" :value="3"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="" prop="category1_id">
                <div class="line-input">
                    <div class="line-box">
                        <span>一级分类</span>
                    </div>
                    <el-select
                        v-model="searchInfo.category1_id"
                        placeholder="请选择一级分类"
                        filterable
                        clearable
                        @change="
                            handleClassiyfChange(2, searchInfo.category1_id)
                        "
                    >
                        <el-option
                            v-for="item in categoryList1"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item prop="category2_id">
                <div class="line-input">
                    <div class="line-box">
                        <span>二级分类</span>
                    </div>
                    <el-select
                        v-model="searchInfo.category2_id"
                        placeholder="请选择二级分类"
                        filterable
                        clearable
                        @change="
                            handleClassiyfChange(3, searchInfo.category2_id)
                        "
                    >
                        <el-option
                            v-for="item in categoryList2"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item prop="category3_id">
                <div class="line-input">
                    <div class="line-box">
                        <span>三级分类</span>
                    </div>
                    <el-select
                        v-model="searchInfo.category3_id"
                        placeholder="请选择三级分类"
                        filterable
                        clearable
                    >
                        <el-option
                            v-for="item in categoryList3"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="search">搜索</el-button>
                <el-button @click="deleteBySearchByAdmin"
                    >删除所有筛选商品</el-button
                >
                <el-button type="text" @click="reset">重置搜索条件</el-button>
                <el-button @click="openSupply"
                    >同步商品管理</el-button
                >
            </el-form-item>
        </el-form>
        <!-- 选项切换卡 -->
        <el-tabs type="card" class="mt25 goods-tabs" @tab-click="handleClick">
            <el-tab-pane
                v-for="item in goodsStatusList"
                :key="item.id"
                :label="`${item.name} ${item.total !== null ? item.total : ''}`"
                :name="item.value"
            >
            </el-tab-pane>
        </el-tabs>
        <el-button @click="batchPlay">批量上架</el-button>
        <el-button @click="batchDisplay">批量下架</el-button>
        <el-button @click="batchDelete">批量删除</el-button>
        <!-- 表格 -->
        <el-table
            class="mt_20"
            :data="productList"
            @selection-change="handleSelectionChange"
        >
            <el-table-column
                type="selection"
                width="55"
                align="center"
            ></el-table-column>
            <el-table-column align="center" prop="id" width="80" label="ID">
            </el-table-column>
            <el-table-column align="center" prop="title" label="商品名称">
            </el-table-column>
            <el-table-column align="center" label="供货价">
                <template slot-scope="scope">
                    ￥{{ scope.row.price | formatF2Y }}
                </template>
            </el-table-column>
            <el-table-column align="center" label="成本价">
                <template slot-scope="scope">
                    ￥{{ scope.row.cost_price | formatF2Y }}
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>库存</p>
                    <p>销量</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.stock_name }}</p>
                    <p>{{ scope.row.sales }}</p>
                </template>
            </el-table-column>
            <el-table-column
                align="center"
                prop="brand_info.name"
                label="品牌"
            ></el-table-column>
            <el-table-column
                align="center"
                prop="brand_source.source_name"
                label="渠道"
            >
              <template slot-scope="scope">
                <p>{{ scope.row.source_name }}</p>
              </template>
            </el-table-column>
            <el-table-column align="center" label="状态">
                <template slot-scope="scope">
                    <el-switch
                        v-model="scope.row.is_display"
                        :active-value="1"
                        :inactive-value="0"
                        @change="changeSwitch(scope.row)"
                    >
                    </el-switch>
                </template>
            </el-table-column>
            <el-table-column align="center" width="220" label="操作">
                <template slot-scope="scope">
                    <!-- <el-button
                        type="text"
                        style="
                            padding: 0 !important;
                            margin-left: 10px !important;
                        "
                    >
                        复制链接
                    </el-button> -->
                    <el-button
                        @click="copyProductAdmin(scope.row.id)"
                        type="text"
                        style="
                            padding: 0 !important;
                            margin-left: 10px !important;
                        "
                    >
                        复制
                    </el-button>
                    <el-button
                        @click="edit(scope.row.id, scope.row.brand_id)"
                        type="text"
                        style="
                            padding: 0 !important;
                            margin-left: 10px !important;
                        "
                    >
                        编辑
                    </el-button>
                    <el-button
                        @click="deleteProductAdmin(scope.row.id)"
                        class="color-red"
                        type="text"
                        style="
                            padding: 0 !important;
                            margin-left: 10px !important;
                        "
                    >
                        删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :style="{
                display: 'flex',
                justifyContent: 'flex-end',
                marginRight: '20px',
            }"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes,prev, pager, next, jumper"
        >
        </el-pagination>
        <product-drawer
            ref="productDrawer"
            @get-list="getProductListByAdmin"
        ></product-drawer>

        <el-dialog 
            title="同步商品管理" 
            :visible="syncIsShow" 
            width="500px" 
            :before-close="handleSyncIsShowClose"
        >
            <el-form label-width="90px">
                <el-form-item label="供应渠道:">
                    <el-select 
                        v-model="supply_id" 
                        clearable
                    >
                        <el-option 
                            v-for="item in supply.list" 
                            :value="item.id" 
                            :label="item.name"
                        ></el-option>
                        <div class="text-center">
                            <el-pagination
                                background
                                small
                                class="pagination"
                                :current-page="supply.page"
                                :page-size="supply.pageSize"
                                style="padding-top:10px !important;padding-bottom: 0 !important;"
                                :total="supply.total"
                                layout="prev,pager, next"
                                @current-change="handleSupplyPage"
                            ></el-pagination>
                        </div>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="syncSupplyChainProduct">同 步</el-button>
                <el-button @click="handleSyncIsShowClose">取 消</el-button>
            </div>
        </el-dialog>
    </m-card>
</template>
<script>
import {
    getAllCategoryChildList,
    getProductListByAdmin,
    displayProductByIds,
    copyProductAdmin,
    deleteProductAdmin,
    deleteBySearchByAdmin,
    getSupplyChainList,
    syncSupplyChainProduct
} from '@/api/localLife';
import productDrawer from './components/productDrawer.vue';
import { confirm } from '@/decorators/decorators';

export default {
    name: 'localLifeMGL',
    components: { productDrawer },
    data() {
        return {
            goodsStatusList: [
                { name: '全部', value: '0', total: null },
                { name: '上架', value: '1', total: 0 },
                { name: '下架', value: '2', total: 0 },
                { name: '售罄', value: '3', total: 0 },
            ],
            shopCommand: 'product_title', // 选中的搜索方式
            searchInfo: {
                brand_name: null, // 品牌
                product_title: '', // 商品名称
                product_id: null, // 商品ID
                product_type: 0, // 商品类型
                category1_id: null, // 一级分类
                category2_id: null, // 二级分类
                category3_id: null, // 三级分类
            },
            categoryList1: [], // 一级分类表
            categoryList2: [], // 二级分类表
            categoryList3: [], // 三级分类表
            productList: [], // 商品列表
            selectIds: [], // 选中的ID数组
            tag_type: 0, // 0-全部 1-上架 2-下架 3-售罄
            page: 1, // 当前页数
            pageSize: 10, // 每页条数
            total: 0, // 总数

            // 同步供应链
            syncIsShow: false,
            supply_id: null, // 供应链id
            supply: { // 分页
                page: 1,
                pageSize: 10,
                total: null,
                list: []
            }
        };
    },
    mounted() {
        getAllCategoryChildList(0).then((r) => {
            this.categoryList1 = r.data;
        });
        // 品牌管理跳转传参
        if (this.$route.query.brand_id) {
            this.searchInfo.brand_id = parseInt(this.$route.query.brand_id);
        }
        this.getProductListByAdmin();
    },
    methods: {
        // 打开同步商品
        openSupply() {
            this.getSupply();
            this.syncIsShow = true;
        },
        // 获取供应链
        async getSupply() {
            let data = {
                page: this.supply.page,
                pageSize: this.supply.pageSize
            }
            let res = await getSupplyChainList(data);
            if (res.code === 0) {
                this.supply.list = res.data.list;
                this.supply.total = res.data.total;
            }
        },
        // 供应链分页
        handleSupplyPage(val) {
            this.supply.page = val;
            this.getSupply()
        },
        // 同步商品
        async syncSupplyChainProduct() {
            let data = {
                supply_chain_id: parseInt(this.supply_id)
            }
            let res = await syncSupplyChainProduct(data);
            if (res.code ===  0) {
                this.$message.success(res.msg);
                this.handleSyncIsShowClose()
            }
        },
        // 关闭同步商品
        handleSyncIsShowClose() {
            this.supply = {
                page: 1,
                pageSize: 10,
                total: null,
                list: []
            }
            this.supply_id = null;
            this.syncIsShow = false
        },
        // 获取类目
        handleClassiyfChange(level, parent_id) {
            let params = {
                level,
                parent_id,
            };
            getAllCategoryChildList(params).then((r) => {
                if (level === 3) {
                    this.categoryList3 = [];
                    this.searchInfo.category3_id = '';
                } else {
                    this.categoryList2 = [];
                    this.searchInfo.category2_id = '';
                    this.categoryList3 = [];
                    this.searchInfo.category3_id = '';
                }
                switch (level) {
                    case 2:
                        this.categoryList2 = r.data;
                        break;
                    case 3:
                        this.categoryList3 = r.data;
                        break;
                }
            });
        },
        // 获取商品列表
        async getProductListByAdmin() {
            const params = {
                brand_name: this.searchInfo.brand_name,
                product_title: this.searchInfo.product_title,
                product_id: this.searchInfo.product_id
                    ? parseInt(this.searchInfo.product_id)
                    : null,
                tag_type: parseInt(this.tag_type),
                category1_id: this.searchInfo.category1_id
                    ? this.searchInfo.category1_id
                    : null,
                category2_id: this.searchInfo.category2_id
                    ? this.searchInfo.category2_id
                    : null,
                category3_id: this.searchInfo.category3_id
                    ? this.searchInfo.category3_id
                    : null,
                product_type: this.searchInfo.product_type,
                page: this.page,
                pageSize: this.pageSize,
            };
            if (this.searchInfo.brand_id) {
                params.brand_id = this.searchInfo.brand_id
            }            
            const res = await getProductListByAdmin(params);
            if (res.code === 0) {
                this.total = res.data.total;
                this.productList = res.data.list;
                this.goodsStatusList[1].total = res.data.statistic.stock_count; // 上架
                this.goodsStatusList[2].total =
                    res.data.statistic.un_stock_count; // 下架
                this.goodsStatusList[3].total =
                    res.data.statistic.sold_out_count; // 售罄
            }
        },
        // 切换tabs
        handleClick(tab) {
            switch (tab.index) {
                case '0':
                    this.tag_type = 0;
                    break;
                case '1':
                    this.tag_type = 1;
                    break;
                case '2':
                    this.tag_type = 2;
                    break;
                case '3':
                    this.tag_type = 3;
                    break;
                default:
                    break;
            }
            this.search();
        },
        // 更新状态
        async changeSwitch(row) {
            const data = {
                ids: [row.id],
                value: parseInt(row.is_display),
            };
            const res = await displayProductByIds(data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.getProductListByAdmin();
            }
        },
        // 搜索
        search() {
            this.page = 1;
            this.searchInfo.brand_id = null
            this.getProductListByAdmin();
        },
        // 删除所有筛选商品
        async deleteBySearchByAdmin() {
            const data = {
                brand_name: this.searchInfo.brand_name,
                product_title: this.searchInfo.product_title,
                product_id: this.searchInfo.product_id
                    ? parseInt(this.searchInfo.product_id)
                    : null,
                tag_type: parseInt(this.tag_type),
                category1_id: this.searchInfo.category1_id
                    ? this.searchInfo.category1_id
                    : null,
                category2_id: this.searchInfo.category2_id
                    ? this.searchInfo.category2_id
                    : null,
                category3_id: this.searchInfo.category3_id
                    ? this.searchInfo.category3_id
                    : null,
                product_type: this.searchInfo.product_type,
                page: this.page,
                pageSize: this.pageSize,
            };
            const res = await deleteBySearchByAdmin(data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.getProductListByAdmin();
            }
        },
        // 重置搜索
        reset() {
            this.searchInfo = {
                brand_name: null,
                product_title: '',
                product_id: null,
                product_type: 0,
                category1_id: null,
                category2_id: null,
                category3_id: null,
            };
        },
        // 复制商品
        async copyProductAdmin(id) {
            const data = {
                id: parseInt(id),
            };
            const res = await copyProductAdmin(data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.getProductListByAdmin();
            }
        },
        // 编辑
        edit(id, brand_id) {
            this.$refs.productDrawer.drawerShow = true;
            this.$refs.productDrawer.editId = id;
            this.$refs.productDrawer.resetProductForm();
            this.$refs.productDrawer.getBrandCategoriesFirst(1, 0, brand_id);
            this.$refs.productDrawer.getProductInfo(id);
        },
        // 删除商品
        @confirm('提示', '确定删除?')
        async deleteProductAdmin(id) {
            const data = {
                ids: [parseInt(id)],
            };
            const res = await deleteProductAdmin(data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.getProductListByAdmin();
            }
        },
        // 选择搜索条件
        handleShopCommand(command) {
            this.shopCommand = command;
            this.searchInfo.product_title = null;
            this.searchInfo.product_id = null;
        },
        // 搜索条件列表
        returnShopValue(command) {
            let text = '商品名称';
            switch (command) {
                case 'product_title':
                    text = '商品名称';
                    break;
                case 'product_id':
                    text = '商品ID';
                    break;
                default:
                    break;
            }
            return text;
        },
        // 批量删除选中
        handleSelectionChange(val) {
            this.selectIds = val.map((item) => item.id);
        },
        // 批量上架
        async batchPlay() {
            if (this.selectIds.length === 0) {
                this.$message.error('请选择操作的数据');
                return;
            }
            const data = {
                ids: this.selectIds,
                value: 1,
            };
            const res = await displayProductByIds(data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.getProductListByAdmin();
            }
        },
        // 批量下架
        async batchDisplay() {
            if (this.selectIds.length === 0) {
                this.$message.error('请选择操作的数据');
                return;
            }
            const data = {
                ids: this.selectIds,
                value: 0,
            };
            const res = await displayProductByIds(data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.getProductListByAdmin();
            }
        },
        // 批量删除
        async batchDelete() {
            if (this.selectIds.length === 0) {
                this.$message.error('请选择操作的数据');
                return;
            }
            const data = {
                ids: this.selectIds,
            };
            const res = await deleteProductAdmin(data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.getProductListByAdmin();
            }
        },
        // 当前页数
        handleCurrentChange(page) {
            this.page = page;
            this.getProductListByAdmin();
        },
        // 每页条数
        handleSizeChange(size) {
            this.pageSize = size;
            this.search()
            // this.getProductListByAdmin();
        },
    },
};
</script>
<style scoped lang="scss">
.color-red {
    color: red;
}
</style>
