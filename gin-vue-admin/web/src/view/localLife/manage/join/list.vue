<template>
    <m-card>
        <el-form :model="searchInfo" ref="form" inline>
            <el-form-item prop="brand_name">
                <el-input placeholder="请输入" v-model="searchInfo.brand_name" class="line-input" clearable>
                    <span slot="prepend">品牌</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input placeholder="请输入" clearable v-model="searchInfo.user_keyword" class="line-input-width">
                    <template slot="prepend">
                        <el-dropdown class="el-dropdown-row" @command="handleShopCommand">
                            <span class="el-dropdown-link">
                                {{ returnShopValue(searchInfo.user_type)
                                }}<i class="el-icon-arrow-down el-icon--right"></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item :command="1">
                                    会员ID
                                </el-dropdown-item>
                                <el-dropdown-item :command="2">
                                    手机号
                                </el-dropdown-item>
                                <el-dropdown-item :command="3">
                                    申请人姓名
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </template>
                </el-input>
            </el-form-item>
            <el-form-item label="" prop="category1_id">
                <div class="line-input">
                    <div class="line-box ">
                        <span>一级经营类目</span>
                    </div>
                    <el-select v-model="searchInfo.category1_id" placeholder="请选择一级分类" filterable clearable
                        @change="handleClassiyfChange(2, searchInfo.category1_id)">
                        <el-option v-for="item in categoryList1" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item prop="category2_id">
                <div class="line-input">
                    <div class="line-box ">
                        <span>二级经营类目</span>
                    </div>
                    <el-select v-model="searchInfo.category2_id" placeholder="请选择二级分类" filterable clearable
                        @change="handleClassiyfChange(3, searchInfo.category2_id)">
                        <el-option v-for="item in categoryList2" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item prop="category3_id">
                <div class="line-input">
                    <div class="line-box ">
                        <span>三级经营类目</span>
                    </div>
                    <el-select v-model="searchInfo.category3_id" placeholder="请选择三级分类" filterable clearable>
                        <el-option v-for="item in categoryList3" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item prop="status">
                <div class="line-input">
                    <div class="line-box ">
                        <span>状态</span>
                    </div>
                    <el-select v-model="searchInfo.audit_status" clearable>
                        <el-option label="待审核" :value="1"></el-option>
                        <el-option label="已审核" :value="2"></el-option>
                        <el-option label="驳回" :value="3"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <el-button @click="search" type="primary">搜索</el-button>
                <el-button type="text" @click="resetForm">重置搜索条件</el-button>
            </el-form-item>
        </el-form>
        <el-button @click="batch(2)">批量通过</el-button>
        <el-button @click="batchNoPassDialog">批量驳回</el-button>
        <el-table :data="tableList" class="mt25" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" :selectable="selectable"></el-table-column>
            <el-table-column align="center" label="申请时间">
                <template slot-scope="scope">
                    {{ scope.row.created_at | formatDate }}
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>品牌名称</p>
                    <p>主体名称</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.brand_info.name }}</p>
                    <p>{{ scope.row.name }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="source_name" label="渠道">
            </el-table-column>
            <el-table-column align="center" label="经营类目">
                <template slot-scope="scope">
                    <div>{{ scope.row.categories[0].category1.name }}</div>
                    <div>{{ scope.row.categories[0].category2.name }}</div>
                    <div>{{ scope.row.categories[0].category3.name }}</div>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>会员</p>
                    <p>登录账号</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.user.username }}</p>
                    <p>{{ scope.row.username }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>姓名</p>
                    <p>电话</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.real_name }}</p>
                    <p>{{ scope.row.mobile }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center" label="状态">
                <template slot-scope="scope">
                    <p>{{ scope.row.audit_status_name }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center" width="150" label="操作">
                <template slot-scope="scope">
                    <el-button type="text" style="padding: 0 !important"
                        @click="find(scope.row.id, scope.row.audit_status)">
                        详情
                    </el-button>
                    <el-button v-if="scope.row.audit_status === 1 || scope.row.audit_status === 4" type="text" style="padding: 0 !important"
                        @click="pass(scope.row.id)">
                        通过
                    </el-button>
                    <el-button v-if="scope.row.audit_status === 1 || scope.row.audit_status === 4" type="text" class="red" style="padding: 0 !important"
                        @click="nopassDialog(scope.row.id)">
                        驳回
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination background :current-page="page" :page-size="pageSize" :page-sizes="[10, 30, 50, 100]" :style="{
            display: 'flex',
            justifyContent: 'flex-end',
            marginRight: '20px',
        }" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange"
            layout="total, sizes,prev, pager, next, jumper">
        </el-pagination>
        <addJoinDialog ref="addJoinDialog" @reset="search"></addJoinDialog>
        <!-- 驳回 -->
        <el-dialog append-to-body :before-close="passDialogClose" :visible="isShowPass" title="驳回理由" width="1000px">
            <el-input type="textarea" :rows="11" placeholder="请输入内容" v-model="reject"></el-input>
            <span slot="footer" class="dialog-footer">
                <el-button @click="passDialogClose">取 消</el-button>
                <el-button type="primary" v-if="status" @click="batch(3)">确 定</el-button>
                <el-button type="primary" v-else @click="nopass">确 定</el-button>
            </span>
        </el-dialog>
    </m-card>
</template>
<script>
import { getAllCategoryChildList, brandApplyList, brandApplyAudit } from '@/api/localLife';
import addJoinDialog from './components/addJoinDialog.vue'
export default {
    name: "localLifeMJL",
    components: { addJoinDialog },
    data() {
        return {
            id: null,
            status: false,
            searchInfo: {
                brand_name: '',
                user_type: 1,
                user_keyword: '',
                category1_id: null,
                category2_id: null,
                category3_id: null,
                audit_status: null,
            },
            tableList: [], // 入住数据
            selectTables: [], // 多选
            categoryList1: [], // 一级分类表
            categoryList2: [], // 二级分类表
            categoryList3: [], // 三级分类表
            page: 1,
            pageSize: 10,
            total: null,

            // 驳回 
            isShowPass: false,
            reject: '',
        }
    },
    // filters: {
    //     status: function (status) {
    //         let name = ""
    //         switch (status) {
    //             case 1:
    //                 name = "待审核"
    //                 break;
    //             case 2:
    //                 name = "审核通过"
    //                 break;
    //             case 3:
    //                 name = "审核驳回"
    //                 break;
    //         }
    //         return name;
    //     }
    // },
    mounted() {
        getAllCategoryChildList(0).then((r) => {
            this.categoryList1 = r.data;
        })
        brandApplyList({ page: 1, pageSize: 10 }).then(res => {
            if (res.code === 0) {
                this.tableList = res.data.list
                this.total = res.data.total
            }
        })
    },
    methods: {
        // 详情
        find(id, status) {
            this.$refs.addJoinDialog.edit(id, status)
        },
        // 打开驳回弹窗
        nopassDialog(brand_id) {
            this.id = brand_id
            this.isShowPass = true
        },
        // 关闭驳回弹窗
        passDialogClose() {
            this.isShowPass = false
            this.reject = ''
        },
        // 审核通过
        pass(brand_id) {
            this.$confirm('是否确认通过?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                let params = {
                    brand_ids: [brand_id],
                    status: 2,
                }
                let res = await brandApplyAudit(params);
                if (res.code === 0) {
                    this.$message.success(res.msg)
                    this.search()
                }
            }).catch(() => { });
        },
        // 审核驳回
        nopass() {
            this.$confirm('是否确认驳回?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                let params = {
                    brand_ids: [this.id],
                    status: 3,
                    remark: this.reject
                }
                let res = await brandApplyAudit(params);
                if (res.code === 0) {
                    this.$message.success('驳回成功')
                    this.search()
                    this.passDialogClose()
                }
            }).catch(() => { });
        },
        // 批量打开打开驳回弹窗
        batchNoPassDialog() {
            if (this.selectTables.length === 0) {
                this.$message.error('请勾选入驻信息')
                return
            };
            this.status = true
            this.isShowPass = true
        },
        // 批量审核通过/驳回
        batch(status) {
            if (this.selectTables.length === 0) {
                console.log(111);
                this.$message.error('请勾选入驻信息')
                return
            };
            let brand_ids = []
            let str = '是否确认批量通过?'
            this.selectTables.forEach(element => {
                brand_ids.push(element.id)
            });
            if (status === 3) {
                str = '是否确认批量驳回?'
            }
            this.$confirm(str, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                let params = {
                    brand_ids,
                    status: status
                }
                if (status === 3) {
                    params.remark = this.reject
                }
                let res = await brandApplyAudit(params);
                if (res.code === 0) {
                    this.$message.success(res.msg)
                    this.search()
                    if (status === 3) {
                        this.passDialogClose()
                    }
                }
            }).catch(() => {});
        },
        // 获取数据
        async search() {
            let params = {
                page: this.page,
                pageSize: this.pageSize,
            }
            if (this.searchInfo.brand_name && this.searchInfo.brand_name !== '') {
                params.brand_name = this.searchInfo.brand_name
            }
            if (this.searchInfo.user_keyword && this.searchInfo.user_keyword !== '') {
                params.user_keyword = this.searchInfo.user_keyword
                params.user_type = this.searchInfo.user_type
            }
            if (this.searchInfo.category1_id && this.searchInfo.category1_id !== '') {
                params.category1_id = this.searchInfo.category1_id
            }
            if (this.searchInfo.category2_id && this.searchInfo.category2_id !== '') {
                params.category2_id = this.searchInfo.category2_id
            }
            if (this.searchInfo.category3_id && this.searchInfo.category3_id !== '') {
                params.category3_id = this.searchInfo.category3_id
            }
            if (this.searchInfo.audit_status && this.searchInfo.audit_status !== '') {
                params.audit_status = this.searchInfo.audit_status
            }
            let res = await brandApplyList(params)
            if (res.code === 0) {
                this.tableList = res.data.list
                this.total = res.data.total
            }
        },
        // 重置搜索条件
        resetForm() {
            this.searchInfo = {
                brand_name: '',
                user_type: 1,
                user_keyword: '',
                category1_id: null,
                category2_id: null,
                category3_id: null,
                audit_status: null,
            }
            this.page = 1,
            this.pageSize = 10
            this.search()
        },
        // 获取类目
        handleClassiyfChange(level, parent_id) {
            let params = {
                level,
                parent_id
            }
            getAllCategoryChildList(params).then(r => {
                if (level === 3) {
                    this.categoryList3 = [];
                    this.searchInfo.category3_id = ""
                } else {
                    this.categoryList2 = [];
                    this.searchInfo.category2_id = ""
                    this.categoryList3 = [];
                    this.searchInfo.category3_id = ""
                }
                switch (level) {
                    case 2:
                        this.categoryList2 = r.data
                        break;
                    case 3:
                        this.categoryList3 = r.data
                        break;
                }
            })
        },
        // 多选
        handleSelectionChange(val) {
            this.selectTables = val;
        },
        // 分页
        handleSizeChange(val) {
            this.pageSize = val
            this.search()
        },
        handleCurrentChange(val) {
            this.page = val
            this.search()
        },
        // 选择搜索条件
        handleShopCommand(command) {
            this.searchInfo.user_type = command;
            this.searchInfo.user_keyword = '';
        },
        // 搜索条件列表
        returnShopValue(command) {
            let text = '会员ID';
            switch (command) {
                case 1:
                    text = '会员ID';
                    break;
                case 2:
                    text = '手机号';
                    break;
                case 3:
                    text = '申请人姓名';
                    break;
                default:
                    break;
            }
            return text;
        },
        // 多选框禁用
        selectable(row) {
            return row.audit_status === 1 || row.audit_status === 4
        }
    }
}
</script>
<style scoped lang="scss">
.red {
    color: red;
}
</style>