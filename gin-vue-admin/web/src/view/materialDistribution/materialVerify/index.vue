<template>
    <m-card>
        <el-form class="search-term" label-width="120px" inline>
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.user_id"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">会员ID</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.user_mobile"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">手机号</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.user_nickname"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">会员昵称</span>
                </el-input>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input">
                    <div class="line-box">
                        <span>会员等级</span>
                    </div>
                    <el-select
                        v-model="searchInfo.user_level_id"
                        clearable
                        class="w100"
                    >
                        <el-option
                            v-for="item in memberLevelOptions"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <div class="line-input">
                    <div class="line-box">
                        <span>审核状态</span>
                    </div>
                    <el-select
                        v-model="searchInfo.status"
                        clearable
                        class="w100"
                    >
                        <el-option label="待审核" :value="1"></el-option>
                        <el-option label="审核通过" :value="2"></el-option>
                        <el-option label="审核不通过" :value="3"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.title"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">素材标题</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.id"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">素材ID</span>
                </el-input>
            </el-form-item>
            <!-- <el-form-item>
                <div class="line-input">
                    <div class="line-box">
                        <span>素材状态</span>
                    </div>
                    <el-select
                        v-model="searchInfo.is_display"
                        clearable
                        class="w100"
                    >
                        <el-option label="启用" :value="1"></el-option>
                        <el-option label="禁用" :value="0"></el-option>
                    </el-select>
                </div>
            </el-form-item> -->
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.product_id"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">商品ID</span>
                </el-input>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input">
                    <div class="line-box">
                        <span>一级分类</span>
                    </div>
                    <el-select
                        v-model="category1_id"
                        clearable
                        class="w100"
                        @change="getCategory(2, category1_id)"
                    >
                        <el-option
                            v-for="item in categoryList1"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input">
                    <div class="line-box">
                        <span>二级分类</span>
                    </div>
                    <el-select
                        v-model="category2_id"
                        clearable
                        class="w100"
                        @change="getCategory(3, category2_id)"
                    >
                        <el-option
                            v-for="item in categoryList2"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input">
                    <div class="line-box">
                        <span>三级分类</span>
                    </div>
                    <el-select v-model="category3_id" clearable class="w100">
                        <el-option
                            v-for="item in categoryList3"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.product_title"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">商品名称</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <div class="line-input">
                    <el-input
                        placeholder="请输入"
                        v-model="searchInfo.shop_name"
                        class="line-input"
                        clearable
                    >
                        <span slot="prepend">店铺</span>
                    </el-input>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input">
                    <div class="line-box">
                        <span>品牌</span>
                    </div>
                    <el-select
                        v-model="searchInfo.brand_id"
                        clearable
                        class="w100"
                    >
                        <el-option
                            v-for="item in brandData.options"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        ></el-option>
                        <div class="text-center">
                            <el-pagination
                                style="
                                    padding-top: 10px !important;
                                    padding-bottom: 0 !important;
                                "
                                background
                                small
                                layout="prev,pager, next"
                                :current-page="brandData.page"
                                :page-size="brandData.pageSize"
                                :total="brandData.total"
                                @current-change="handleBrandCurrentChange"
                            ></el-pagination>
                        </div>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="search">搜索</el-button>
                <el-button type="text" @click="reset" class="ml10">
                    重置搜索条件
                </el-button>
                <el-button @click="byPassAll">批量通过</el-button>
                <el-button @click="nopassAll">批量驳回</el-button>
            </el-form-item>
        </el-form>
        <el-table
            class="mt_20"
            :data="tableData"
            @selection-change="handleSelectionChange"
        >
            <el-table-column
                type="selection"
                width="55"
                :selectable="selectable"
            ></el-table-column>
            <el-table-column
                label="ID"
                prop="id"
                align="center"
            ></el-table-column>
            <el-table-column label="素材标题" align="center" prop="title">
            </el-table-column>
            <el-table-column label="关联商品" align="center" width="200">
                <template slot-scope="scope">
                    {{ scope.row.product.title }}
                </template>
            </el-table-column>
            <el-table-column label="店铺" align="center">
                <template slot-scope="scope">
                    {{ scope.row.product.supplier.shop_name }}
                </template>
            </el-table-column>
            <el-table-column label="品牌" align="center">
                <template slot-scope="scope">
                    {{ scope.row.product.brand.name }}
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>审核状态</p>
                    <p>审核时间</p>
                </template>
                <template slot-scope="scope">
                    <p v-if="scope.row.status == 1" style="color: #c2cd00">
                        待审核
                    </p>
                    <p v-if="scope.row.status == 2" style="color: #29ba9c">
                        审核通过
                    </p>
                    <p v-if="scope.row.status == 3" class="color-red">
                        审核不通过
                    </p>
                    <p v-if="scope.row.status !== 1">
                        {{ scope.row.updated_at | formatDate }}
                    </p>
                </template>
            </el-table-column>
            <el-table-column label="发布人" align="center">
                <template slot-scope="scope">
                    <p>{{ scope.row.user.username }}</p>
                    <p>{{ scope.row.user.nickname }}</p>
                </template>
            </el-table-column>
            <el-table-column label="素材分组" align="center">
                <template slot-scope="scope">
                    <p>{{ scope.row.group.title }}</p>
                </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center">
                <template slot-scope="scope">
                    {{ scope.row.created_at | formatDate }}
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        style="
                            padding: 0 !important;
                            margin-left: 10px !important;
                        "
                        @click="detail(scope.row)"
                    >
                        详情
                    </el-button>
                    <el-button
                        v-if="scope.row.status == 1"
                        type="text"
                        style="
                            padding: 0 !important;
                            margin-left: 10px !important;
                        "
                        @click="byPass(scope.row.id)"
                    >
                        通过
                    </el-button>
                    <el-button
                        v-if="scope.row.status == 1"
                        type="text"
                        class="color-red"
                        style="
                            padding: 0 !important;
                            margin-left: 10px !important;
                        "
                        @click="noPass(scope.row.id)"
                    >
                        驳回
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :style="{
                display: 'flex',
                justifyContent: 'flex-end',
                marginRight: '20px',
            }"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
        ></el-pagination>
        <!-- 驳回 -->
        <el-dialog title="驳回理由" :visible.sync="dialogVisible" width="50%">
            <m-editor v-model="remark"></m-editor>
            <span slot="footer" class="mt_20">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="rejectProductAudit">
                    确 定
                </el-button>
            </span>
        </el-dialog>
        <!-- 抽屉 -->
        <materialDrawerVue
            ref="materialDrawerVue"
            @reSearch="search"
        ></materialDrawerVue>
    </m-card>
</template>

<script>
import { getAuditList, getAuditOperate } from '@/api/material';
import { getBrandsList } from '@/api/brands';
import { getUserLevelOptionList } from '@/api/member';
import { confirm } from '@/decorators/decorators';
import category from '@/mixins/category';
import materialDrawerVue from './components/materialVerifyDrawer.vue';
export default {
    name: 'materialVerify',
    components: { confirm, materialDrawerVue },
    mixins: [category],
    data() {
        return {
            page: 1,
            pageSize: 10,
            total: null,
            searchInfo: {
                user_id: null,
                mobile: null,
                nickname: null,
                user_level_id: null,
                status: null,
                title: null,
                id: null,
                // is_display: null,
                product_id: null,
                product_title: null,
                shop_name: null,
                brand_id: null,
            },
            tableData: [],
            memberLevelOptions: [],
            brandData: {
                page: 1,
                pageSize: 10,
                options: [],
                total: 0,
            },
            category1_id: null,
            category2_id: null,
            category3_id: null,
            chooseSelectId: [],
            dialogVisible: false,
            remark: '',
        };
    },
    mounted() {
        this.getCategory();
        this.getAuditList();
        this.getBrandOption();
        this.getUserLevelOptionList();
    },
    methods: {
        // 获取品牌列表
        async getBrandOption() {
            let params = {
                page: this.brandData.page,
                pageSize: this.brandData.pageSize,
            };
            const { code, data } = await getBrandsList(params);
            if (code === 0) {
                this.brandData.options = data.list;
                this.brandData.total = data.total;
            }
        },
        // 会员等级
        async getUserLevelOptionList() {
            const res = await getUserLevelOptionList();
            if (res.code === 0) {
                this.memberLevelOptions = res.data.list;
            }
        },
        // 获取素材审核列表
        async getAuditList() {
            const data = {
                page: this.page,
                pageSize: this.pageSize,
                ...this.searchInfo,
            };
            const res = await getAuditList(data);
            if (res.code === 0) {
                this.tableData = res.data.list;
                this.total = res.data.total;
            }
        },
        // 搜索
        search() {
            this.page = 1;
            this.searchInfo.status = this.searchInfo.status
                ? this.searchInfo.status
                : null;
            this.searchInfo.category1_id = this.category1_id
                ? this.category1_id
                : null;
            this.searchInfo.category2_id = this.category2_id
                ? this.category2_id
                : null;
            this.searchInfo.category3_id = this.category3_id
                ? this.category3_id
                : null;
            this.searchInfo.brand_id = this.searchInfo.brand_id
                ? this.searchInfo.brand_id
                : null;
            this.searchInfo.user_id = this.searchInfo.user_id
                ? parseInt(this.searchInfo.user_id)
                : null;
            this.searchInfo.id = this.searchInfo.id
                ? parseInt(this.searchInfo.id)
                : null;
            this.searchInfo.product_id = this.searchInfo.product_id
                ? parseInt(this.searchInfo.product_id)
                : null;
            this.getAuditList();
        },
        // 重置搜索条件
        reset() {
            this.searchInfo = {
                user_id: null,
                mobile: null,
                nickname: null,
                user_level_id: null,
                status: null,
                title: null,
                id: null,
                // is_display: null,
                product_id: null,
                product_title: null,
                shop_name: null,
                brand_id: null,
            };
            this.category1_id = null;
            this.category2_id = null;
            this.category3_id = null;
        },
        // 查看详情
        detail(item) {
            this.$refs.materialDrawerVue.info(item);
        },
        // 通过
        @confirm('提示', '确定通过?')
        async byPass(id) {
            const data = {
                ids: [id],
                status: 2,
            };
            const res = await getAuditOperate(data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.getAuditList();
            }
        },
        // 驳回
        noPass(id) {
            this.chooseSelectId = [];
            this.chooseSelectId.push(id);
            this.remark = '';
            this.dialogVisible = true;
        },
        // 确认驳回
        async rejectProductAudit() {
            const data = {
                ids: this.chooseSelectId,
                status: 3,
                rejected_reason: this.remark,
            };
            const res = await getAuditOperate(data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.dialogVisible = false;
                this.chooseSelectId = [];
                this.getAuditList();
            }
        },
        // 多选事件
        handleSelectionChange(item) {
            this.chooseSelectId = [];
            item.forEach((element) => {
                this.chooseSelectId.push(element.id);
            });
        },
        // 禁用
        selectable(row) {
            if (row.status !== 1) {
                return false;
            } else {
                return true;
            }
        },
        // 批量通过
        async byPassAll() {
            if (this.chooseSelectId.length <= 0) {
                this.$message.error('请选择要操作的数据');
                return;
            }
            const data = {
                ids: this.chooseSelectId,
                status: 2,
            };
            const res = await getAuditOperate(data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.getAuditList();
            }
        },
        // 批量驳回
        nopassAll() {
            if (this.chooseSelectId.length <= 0) {
                this.$message.error('请选择要操作的数据');
                return;
            }
            this.remark = '';
            this.dialogVisible = true;
        },
        // 分页
        handleCurrentChange(page) {
            this.page = page;
            this.getAuditList();
        },
        handleSizeChange(size) {
            this.pageSize = size;
            this.search();
        },
        // 品牌分页
        handleBrandCurrentChange(val) {
            this.brandData.page = val;
            this.getBrandOption();
        },
    },
};
</script>

<style>
::v-deep .search-term .line-input .fac .el-input--medium .el-input__inner {
    text-align: left;
}
.color-red {
    color: red;
}
</style>
