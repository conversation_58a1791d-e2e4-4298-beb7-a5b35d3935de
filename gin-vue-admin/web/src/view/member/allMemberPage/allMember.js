import {getUserList, deleteUser, getUserLevelOptionList,exportUserList} from "@/api/member";
import { getApplicationLevelOption } from "@/api/application";
import { getSystemConfig } from "@/api/system";
import {formatTimeToStr} from "@/utils/date";
import UserDetail from "../components/userDetail"
import RechargeDialog from "../components/rechargeDialog"
import AddMemberDialog from "../components/addMemberDialog"
import {Base64} from "js-base64";

export default {
    name: "allMember",
    components: {UserDetail, RechargeDialog, AddMemberDialog},
    data() {
        return {
            join_balance: '', // 是否显示汇聚余额
            // 审核按钮是否显示
            // auditBtnIsShow: false,
            page: 1,
            total: 0,
            pageSize: 10,
            tableData: [],
            searchInfo: {
                id: null,
                username: "",
                nickName: "",
                level_id: null,
                start_at: null,
                end_at: null,
                status: null,
                parent_mobile:"",
                appLevelId: null,
                is_application: 0,
                type: null
            },
            optionList: [],
            //时间处理
            dateActive: "",
            dateList: [
                {name: "今", value: 0},
                {name: "昨", value: 1},
                {name: "近7天", value: 2},
                {name: "近30天", value: 3},
            ],
            statusName: [
                {name: "全部", value: null},
                {name: "待审核", value: 0},
                {name: "审核通过", value: 1},
                {name: "拉黑", value: -1},
            ],
            formData: {
                d1: '',
                d2: ''
            },

            levelOptions: []
        };
    },
    filters: {
        formatAudit: function (v) {
            let s = "";
            switch (v) {
                case 0:
                    s = "待审核"
                    break;
                case 1:
                    s = "审核通过"
                    break;
                case -1:
                    s = "黑名单"
                    break;
            }
            return s
        }
    },
    mounted() {
        this.getTableData();
        getUserLevelOptionList().then((res) => {
            if (res.code === 0) {
                this.optionList = res.data.list;
                this.optionList.unshift({
                    id: 0,
                    name:"默认等级"
                })
            }
        });
        this.getSystemConfig()
        this.setLevelOption()
    },
    methods: {
        // 导出
        async exportUser() {
            let para = {
                page: this.page,
                pageSize: this.pageSize,
                ...this.searchInfo,
            };
            if(this.searchInfo.level_id === 0){
                delete para.level_id
                para.def_level = true
            }
            if (this.formData.d1 != "" && this.formData.d1 != undefined && this.formData.d2 != "" && this.formData.d2 != undefined) {
                para.start_at = formatTimeToStr(this.formData.d1.getTime() / 1000, "yyyy-MM-dd hh:mm:ss");
                para.end_at = formatTimeToStr(this.formData.d2.getTime() / 1000, "yyyy-MM-dd hh:mm:ss");
            }
            let res = await exportUserList(para);
            if (res.code === 0) {
                window.open(this.$path + '/' + res.data.link)
            }
        },
        // 获取采购端等级
        async setLevelOption() {
          const options = await getApplicationLevelOption();
          if (options.code == 0) {
            options.data.list.map((item) => {
              const option = {
                value: item.id,
                label: item.levelName,
              };
              this.levelOptions.push(option);
            });
          }
        },
        // 获取汇聚显示判断
        async getSystemConfig() {
            const res = await getSystemConfig()
            if(res.code === 0) {
                const data = JSON.parse(Base64.decode(res?.data?.config))
                this.join_balance = data.join.balance_settings.join_balance
            }
        },
        openAddMemberDialog() {
            this.$refs.addMemberDialog.isShow = true
            this.$refs.addMemberDialog.getLevel(null)
            this.$nextTick(() => {
                this.$refs.addMemberDialog.title = "新增"
            })
        },
        edit(row) {
            this.$refs.addMemberDialog.isShow = true
            this.$refs.addMemberDialog.getLevel(row.level_id)
            this.$refs.addMemberDialog.getUser(row.id)
            this.$nextTick(() => {
                this.$refs.addMemberDialog.title = "编辑"
            })
        },
        // 重置
        resetSearch() {
            // this.$refs.form.resetFields();
            this.page = 1;
            this.total = 0;
            this.pageSize = 10;
            this.formData.d1 = null
            this.formData.d2 = null
            this.searchInfo.start_at = null
            this.searchInfo.end_at = null
            this.searchInfo = {
                id: null,
                username: "",
                nickName: "",
                level_id: null,
                start_at: null,
                end_at: null,
                status: null,
                parent_mobile:"",
            }
            this.getTableData();
        },
        // 充值
        topUp(row) {
            this.$refs.rechargeDialog.isShow = true
            this.$nextTick(() => {
                this.$refs.rechargeDialog.formData.uid = row.id
            })
        },
        // 打开会员详情
        openUserDetail(row) {
            this.$refs.userDetail.join_balance = this.join_balance
            this.$refs.userDetail.isShow = true;
            this.$nextTick(() => {
                this.$refs.userDetail.getUserDetail(row.id);
            });
        },
        handleSizeChange(val) {
            this.pageSize = val;
            this.getTableData();
        },
        handleCurrentChange(val) {
            this.page = val;
            this.getTableData();
        },
        getTableData() {
            /*findSysTop().then((res) => {
                if (res.code === 0) {
                    // 1 需要审核 2不需要审核
                    if (res.data.setting.value.need_check === 1) {
                        this.auditBtnIsShow = true
                    } else {
                        this.auditBtnIsShow = false
                    }
                } else {
                    this.$message.error(res.msg)
                }
            });*/
            let para = {
                page: this.page,
                pageSize: this.pageSize,
                ...this.searchInfo,
            };
            if(this.searchInfo.level_id === 0){
                delete para.level_id
                para.def_level = true
            }
            if (this.formData.d1 != "" && this.formData.d1 != undefined && this.formData.d2 != "" && this.formData.d2 != undefined) {
                para.start_at = formatTimeToStr(this.formData.d1.getTime() / 1000, "yyyy-MM-dd hh:mm:ss");
                para.end_at = formatTimeToStr(this.formData.d2.getTime() / 1000, "yyyy-MM-dd hh:mm:ss");
            }

            getUserList(para).then((res) => {
                if (res.code == 0) {
                    this.tableData = res.data.list;
                    this.total = res.data.total;
                    this.page = res.data.page;
                }
            });
        },
        del(row) {
            this.$confirm("确定删除吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            })
                .then(() => {
                    deleteUser({id:row.id}).then((res) => {
                        if (res.code === 0) {
                            this.$message.success("删除成功");
                            this.getTableData();
                        } else {
                            this.$message.error(res.msg);
                        }
                    });
                })
                .catch(() => {
                });
        },
    },
};