import { getUserLevelList, createUser, updateUser, findUser, findSysTop } from "@/api/member";
import { mapGetters } from "vuex";
import verify from "@/utils/verify";
export default {
    name: "addMemberDialog",
    data() {
        return {
            isShow: false,
            title: "新增",
            path: this.$path,
            statusIsShow: true,
            formData: {
                avatar: "", // 头像
                username: "", // 用户登录名
                nickname: "", // 用户昵称
                level_id: 0, // 等级
                password: "", // 用户密码//不填写为不修改
                status: 0, // -1拉黑0待审核1正常
                remark: "", // 备注信息
            },
            // 等级列表
            levelList: [],
            rules: {
                level_id: { required: true, message: "请选择会员等级", trigger: "change" },
                status: { required: true, message: "请选择审核状态", trigger: "change" },
                username: { required:false }
            }
        }
    },
    computed: {
        ...mapGetters("user", ["userInfo", "token"]),
    },
    methods: {
        getUser(id) {
            findUser({ id }).then(res => {
                if (res.code === 0) {
                    this.setFrom(res.data.reuser)
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        //赋值表单
        setFrom(val) {
            const keys = Object.keys(val);
            const that = this;
            keys.forEach((element) => {
                that.formData[element] = val[element];
            });
        },
        confirm() {
            this.$refs.form.validate((valid) => {
                if (valid) {
                    if (this.formData.id) {
                        updateUser(this.formData).then(res => {
                            if (res.code === 0) {
                                this.$message.success(res.msg)
                                this.handleClose()
                                this.$emit("reload")
                            } else {
                                this.$message.error(res.msg)
                            }
                        })
                    } else {
                        createUser(this.formData).then(res => {
                            if (res.code === 0) {
                                this.$message.success(res.msg)
                                this.handleClose()
                                this.$emit("reload")
                            } else {
                                this.$message.error(res.msg)
                            }
                        })
                    }
                } else {
                    return false
                }
            })
        },
        // 上传图片
        handleMainImgSuccess(res) {
            this.formData.avatar = res.data.file.url;
        },
        beforeAvatarUpload(file) {
            this.uploadLoading = true;
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                this.$message.error("上传头像图片大小不能超过 10MB!");
            }
            return isLt10M;
        },
        handleClose() {
            try { this.$refs.form.resetFields() } catch { } finally {
                this.isShow = false
                if (this.formData.id) {
                    delete this.formData.id
                }
            }
        },
        getLevel(leve_id) {
            findSysTop().then((res) => {
                if (res.code === 0) {
                    // 1 需要审核 2不需要审核
                    if (res.data.setting.value.need_check === 1) {
                        this.statusIsShow = true
                    } else {
                        this.statusIsShow = false
                        this.formData.status = 1
                    }
                    //  1不需要验证 其他需要验证
                    if (res.data.setting.value.need_check_mobile !== 1) {
                        this.rules.username = {
                            required: true, trigger: "blur", validator: (rule, value, callback) => {
                                if (verify.checkPhone(value)) {
                                    callback()
                                } else {
                                    callback(new Error('手机号格式不正确'))
                                }
                            }
                        }
                    }
                } else {
                    this.$message.error(res.msg)
                }
            });
            getUserLevelList({ page: 1, pageSize: 999 }).then(res => {
                if (res.code === 0) {
                    this.levelList = res.data.list;
                } else {
                    this.tableList = [];
                }
            })
        },
    }
};