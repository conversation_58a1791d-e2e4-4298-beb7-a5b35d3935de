<template>
  <el-dialog
      title="修改上级"
      :visible="isShow"
      width="40%"
      :modal-append-to-body="false"
      append-to-body
      :before-close="handleClose">
    <div class="f fac">
      <el-select v-model="type" clearable class="selectStyle" @change="selectChange">
          <el-option value="uid" label="会员ID"></el-option>
          <el-option value="username" label="会员手机号"></el-option>
      </el-select>
      <el-input v-model="value" class="f1 ml10" @input="inputValueFun">
        <el-button slot="append" icon="el-icon-search" @click="search"></el-button>
      </el-input>
    </div>
    <div v-if="detailIsShow" class="mt25">
      <p>上级信息</p>
      <el-divider></el-divider>
      <div class="f">
        <div class="f1">
          <p>用户名: {{ detailData.username }}</p>
        </div>
        <div class="f1">
          <p>邀请码: {{ detailData.invite_code }}</p>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirm">确 定</el-button>
      <el-button @click="handleClose">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {findUserByIDOrUsername, updateUserParent} from "@/api/member";

export default {
  name: "editParentDialog",
  data() {
    return {
      isShow: false,
      type: 'uid',
      value: null,
      uid:null,
      detailIsShow: false,
      detailData: {}
    }
  },
  methods: {
    confirm() {
      if (this.detailData.id === undefined) {
        this.$message.error('请点击搜索,查看会员信息后在确认！');
        return
      }
      let param = {
        uid: this.uid,
        parent_id: parseInt(this.detailData.id),
        before_parent_id: this.detailData.parent_id
      }
      updateUserParent(param).then(res => {
        if (res.code === 0) {
          this.$message.success(res.msg)
          this.handleClose(true);
        }
      })
    },
    handleClose(onload = false) {
      this.isShow = false
      this.type = 'uid';
      this.value = null
      this.detailIsShow = false
      this.detailData = {}
      this.uid = null
      if(onload) this.$emit("onload")
    },
    selectChange(e) {
      this.value = ''
      this.detailIsShow = false
      this.detailData = {}
    },
    inputValueFun(v) {
      this.detailIsShow = false
      this.detailData = {}
    },
    search() {
      let params = {};
      if (this.type === 'uid') {
          params.uid = parseInt(this.value);
      } else {
          params.username = this.value
      }
      findUserByIDOrUsername(params).then(res => {
        if (res.code === 0) {
          this.detailIsShow = true
          this.detailData = res.data.reuser
        }
        console.log(res, '????');
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-divider {
  margin: 10px 0;
}

.selectStyle {
  width: 200px;
}
</style>