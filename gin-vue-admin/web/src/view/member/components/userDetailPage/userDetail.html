<el-drawer title="详情" :visible="isShow" :close-on-press-escape="false" :wrapperClosable="false"
    :before-close="handleClose" size="calc(100% - 220px)" class="detail-ct">
    <m-card :titIsShow="true">
        <p class="h4l4" slot="title">基础信息</p>
        <el-row>
            <el-col :span="2" class="title-3">
                <el-avatar :size="60" :src="user.avatar">
                    <img src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png" />
                </el-avatar>
            </el-col>
            <el-col :span="22">
                <p>用户ID: {{ user.id }}</p>
                <p>手机号: {{ user.username }}</p>
            </el-col>
        </el-row>
    </m-card>
    <m-card :titIsShow="true" class="mt25">
        <p class="h4l4" slot="title">账户信息</p>
        <p class="h4l4">姓名: {{ user.nickname }}</p>
        <p class="h4l4" v-for="item in account_info" :key="item.id">{{item.name}}: ￥<span>{{
                item.value | formatF2Y
                }}</span> <el-button v-if="is_back === 1" type="text" class="ml30" @click="jump(item)">查看明细</el-button></p>
        <!--        <p>汇聚: ￥<span
                v-if="user.goin_balance.purchasing_balance">{{
                user.goin_balance.purchasing_balance | formatF2Y
            }}</span><span
                v-else>0.00</span></p>
        <p>站内: ￥<span
                v-if="user.balance.purchasing_balance">{{ user.balance.purchasing_balance |formatF2Y }}</span><span
                v-else>0.00</span></p>-->
        <p class="h4l4">联系电话: {{ user.mobile }}</p>
        <p class="h4l4">等级: {{ user.level.name }}</p>
        <p class="h4l4">公众号 openId：{{ user.wx_openid }}</p>
        <p class="h4l4">小程序 openId：{{ user.wx_mini_openid }}</p>
        <p class="h4l4">微信开放平台 unionId：{{ user.wx_unionid }}</p>
        <p class="h4l4">注册时间: {{ user.created_at|formatDate }}</p>
        <!-- <p>备注: </p> -->
    </m-card>
    <m-card :titIsShow="true" class="mt25">
        <p class="h4l4" slot="title">推广信息</p>
        <p class="h4l4">推辞人: {{user.parent_user ? user.parent_user.nickname : ''}}
            <el-button class="ml10" @click="openEditDialog">修改</el-button>
            <el-button @click="openEditLog">修改记录</el-button>
        </p>
        <p class="h4l4">我的邀请码: {{user.invite_code}}</p>
    </m-card>
    <m-card :titIsShow="true" class="mt25">
        <p class="h4l4" slot="title">会员分组</p>
        <el-button class="ml10" @click="openUserGroupin">添加</el-button>
        <el-table ref="table" class="mt_20" :data="tableList">
            <el-table-column label="会员分组ID" prop="id" align="center"></el-table-column>
            <el-table-column label="会员昵称" prop="name" align="center">
                <template slot-scope="scope">
                    {{ scope.row.name }}
                </template>
            </el-table-column>
            <el-table-column label="排序权重" prop="weight" align="center">
                <template slot-scope="scope">
                    {{ scope.row.weight }}
                </template>
            </el-table-column>
            <el-table-column label="创建时间" prop="created_at" align="center">
                <template slot-scope="scope">
                    {{ scope.row.created_at | formatDate }}
                </template>
            </el-table-column>
            <el-table-column label="更新时间" prop="updated_at" align="center">
                <template slot-scope="scope">
                    {{ scope.row.updated_at | formatDate }}
                </template>
            </el-table-column>
        </el-table>
    </m-card>
    <userGroupingDialog ref="userGroupingDialog" @getIds="getIds"></userGroupingDialog>
    <edit-parent-dialog ref="editParentDialog" @onload="getUserDetail(user.id)"></edit-parent-dialog>
    <edit-parent-log ref="editParentLog"></edit-parent-log>
</el-drawer>