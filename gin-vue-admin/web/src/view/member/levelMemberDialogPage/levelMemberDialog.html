<el-dialog :title="title" :visible="dialogVisible" width="40%" :before-close="handleClose">
    <el-form :model="formData" ref="form" :rules="rules" label-width="140px">
        <el-form-item label="等级权重:" prop="level">
            <el-input-number class="winput" :min="1" :controls="false" :precision="0" v-model="formData.level">
            </el-input-number>
        </el-form-item>
        <el-form-item label="等级名称:" prop="name">
            <el-input v-model="formData.name" class="winput"></el-input>
        </el-form-item>
        <template v-if="upgrade_type === 4">
            <el-form-item label="是否付费升级:" prop="pay_upgrade">
                <el-radio-group v-model="formData.pay_upgrade">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="0">否</el-radio>
                </el-radio-group>
                <p>如果选择否，则不显示付费金额、有效期、付费通道设置，该等级不升级、不降级，长期有效！是改成否，原该等级会员全部变成长期有效；否该成是，原该等级会员按照设置有效期赋值！</p>
            </el-form-item>
            <template v-if="formData.pay_upgrade === 1">
                <el-form-item label="付费金额:" prop="price">
                    <div class="f fac">
                        <el-input v-model="pay_product_price"></el-input>
                        <span class="ml10">元</span>
                    </div>
                </el-form-item>
                <el-form-item label="有效期:" prop="expire_days">
                    <div class="f fac">
                        <el-input v-model="formData.expire_days"></el-input>
                        <span class="ml10">天</span>
                    </div>
                    <p>默认3650天，如果中途修改有效期天数，只针对修改后付费生效！</p>
                </el-form-item>
                <el-form-item label="付费通道:" prop="pay_switch">
                    <el-switch active-color="#155bd4" v-model="formData.pay_switch" :active-value="1" :inactive-value="0" clearable></el-switch>
                    <p>关闭后前端不显示该等级的付费购买、续费操作！</p>
                </el-form-item>
            </template>
        </template>
        <el-form-item label="采购权限:">
            <el-checkbox-group v-model="checkBox">
                <el-checkbox :label="1" name="checkBox">前端采购</el-checkbox>
                <el-checkbox :label="2" name="checkBox">api采购</el-checkbox>
            </el-checkbox-group>
            <p>
                默认全部选中，前端采购包括中台PC端、移动端，无权限不能下单；
                API采购包括所有通过API提交的订单，如果无权限，则无法提交订单！
            </p>
        </el-form-item>
        <el-form-item v-if="upgrade_type !== 4" label="是否自动升级:" prop="is_update">
            <el-radio-group v-model="formData.is_update">
                <el-radio :label="0">是</el-radio>
                <el-radio :label="1">否</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item v-if="upgrade_type !== 4">
            <span slot="label"><span style="color: #f56c6c">*</span> 升级条件:</span>
            <template v-if="upgrade_type === 1">
                <div class="select-goods-box f fw">
                    <div class="select-goods-list-item" v-for="(item, index) in selectGoodsList" :key="item.id">
                        <!--                        <img :src="item.image_url" class="w100" />-->
                        <m-image :src="item.image_url"></m-image>
                        <p style="line-height: 16px">[ID:{{item.id}}]</p>
                        <div class="bg-del-box">
                            <i class="el-icon-delete" @click="delGoods(index)"></i>
                        </div>
                    </div>
                    <div class="select-goods-list-item select-goods-item" @click="openSelectGoodsDalog">
                        <i class="el-icon-plus"></i>
                        <p>选择商品</p>
                    </div>
                </div>
                <p class="comments-p">可指定多件商品,只需购买其中一件就可以升级</p>
            </template>
            <template v-if="upgrade_type === 2">
                <el-form-item prop="order_count">
                    <el-input v-model="formData.order_count" @input="checkNumber" class="winput">
                        <template slot="prepend">订单数量满</template>
                    </el-input>
                </el-form-item>
            </template>
            <template v-if="upgrade_type === 3">
                <el-form-item prop="order_amount">
                    <el-input v-model="formData.order_amount" @input="checkNumber" class="winput">
                        <template slot="prepend">订单金额满</template>
                    </el-input>
                    <span class="ml10">元</span>
                </el-form-item>
            </template>
            <!-- <template v-if="upgrade_type === 4">
                <el-form-item prop="order_amount">
                    <el-input v-model="formData.order_amount" @input="checkNumber" class="winput">
                        <template slot="prepend">付费金额满</template>
                    </el-input>
                    <span class="ml10">元</span>
                </el-form-item>
            </template> -->
        </el-form-item>
        <el-form-item label="折扣:" prop="discount" v-if="discount_type === 0">
            <div class="f fac">
                <el-input-number :min="0" :controls="false" class="winput"
                                 v-model="formData.discount"></el-input-number>
                <span class="ml10">%</span>
            </div>
            <p>折扣后价格=商品供货价*折扣率</p>
        </el-form-item>
        <el-form-item label="会员价:" prop="discount" v-if="discount_type === 1">
            <div class="f fac">
                <m-num-input startText="成本价 * " class="winput" :min="0" :precision="2"
                             v-model="formData.discount"></m-num-input>
                <span class="ml10">%</span>
            </div>
            <p>折扣后价格=商品成本价*折扣率</p>
        </el-form-item>
<!--        <el-form-item label="独立规则:" prop="independent_ratio">
            <div class="f fac">
                <el-radio-group v-model="formData.independent_ratio">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                </el-radio-group>
            </div>
            <p>选择"是"即按照当前会员等级的技术服务费比例结算，反之按照全局技术服务费结算</p>
        </el-form-item>-->
        <el-form-item label="技术服务费:" prop="server_ratio">
            <div class="f fac">
                <el-input-number :min="0" :controls="false" class="winput"
                                 v-model="formData.server_ratio"></el-input-number>
                <span class="ml10">%</span>
            </div>
            <p></p>
        </el-form-item>
        <el-form-item label="电商cps分成比例:" prop="cps_ratio" v-if="formData.cps_show">
            <div class="f fac">
                <el-input-number :min="0" :controls="false" class="winput"
                                 v-model="formData.cps_ratio"></el-input-number>
                <span class="ml10">%</span>
            </div>
            <p></p>
        </el-form-item>
        <el-form-item label="聚推联盟分成比例:" prop="jh_cps_ratio" v-if="formData.jh_cps_show">
            <div class="f fac">
                <el-input-number :min="0" :controls="false" class="winput"
                                 v-model="formData.jh_cps_ratio"></el-input-number>
                <span class="ml10">%</span>
            </div>
            <p></p>
        </el-form-item>
        <el-form-item label="美团联盟分成比例:" prop="cps_ratio">
            <div class="f fac">
                <el-input-number :min="0" :precision="2" :controls="false" class="winput"
                                 v-model="formData.meituan_distributor_ratio"></el-input-number>
                <span class="ml10">%</span>
            </div>
            <p></p>
        </el-form-item>
        <!-- <el-form-item label="联联周边游比例:" prop="lian_ratio" v-if="formData.lian_show">
            <div class="f fac">
                <el-input-number :min="0" :controls="false" class="winput"
                                 v-model="formData.lian_ratio"></el-input-number>
                <span class="ml10">%</span>
            </div>
            <p></p>
        </el-form-item> -->
        <el-form-item label="抖音团购分成比例:" prop="douyin_group_ratio">
            <div class="f fac">
                <el-input-number :min="0" :controls="false" class="winput"
                                 v-model="formData.douyin_group_ratio"></el-input-number>
                <span class="ml10">%</span>
            </div>
            <p></p>
        </el-form-item>
        <!-- <el-form-item label="运费减免:" prop="freight_deduction">
            <div class="f fac">
                <el-input-number class="winput" :controls="false" :min="0" v-model="formData.freight_deduction">
                </el-input-number>
                <span class="ml10">%</span>
            </div>
            <p>例如快递费10元,减免10%,运费: 10*(1-10%) = 8元</p>
        </el-form-item> -->
        <el-form-item label="权益说明:" prop="description">
            <m-editor ref="mEditor" v-model="formData.description" style="height: 300px; width: 526px;"></m-editor>
            <!-- <el-input type="textarea" class="winput" v-model="formData.description" :rows="6" resize="none"></el-input> -->
            <p class="color-grap mt_70">此处显示于h5/手机端</p>
        </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm">确 定</el-button>
        <el-button @click="handleClose">取 消</el-button>
    </span>
    <!-- 选择商品组件 -->
    <select-goods-dialog ref="selectGoodsDialog" @addGoodsList="addGoodsList"></select-goods-dialog>
</el-dialog>