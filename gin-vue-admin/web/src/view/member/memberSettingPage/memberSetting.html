<m-card>
    <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="会员设置" name="1">
            <el-form :model="formData" label-width="140px" class="mt25">
                <el-form-item label="会员申请审核:">
                    <el-radio-group v-model="formData.value.need_check">
                        <el-radio :label="1">是</el-radio>
                        <el-radio :label="2">否</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="后台会员手机号验证:">
                    <el-radio-group v-model="formData.value.need_check_mobile">
                        <el-radio :label="0">是</el-radio>
                        <el-radio :label="1">否</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="注册密码:">
                    <el-radio-group v-model="formData.value.need_password">
                        <el-radio :label="1">隐藏</el-radio>
                        <el-radio :label="0">显示</el-radio>
                    </el-radio-group>
                    <p class="text-color">
                        默认显示,隐藏后pc端,移动端用户注册不显示密码填写框,使用短信验证码注册即可！
                    </p>
                </el-form-item>
                <el-form-item label="会员升级条件:">
                    <el-radio-group v-model="formData.value.upgrade_type">
                        <div class="radio-box">
                            <el-radio :label="3">订单金额</el-radio>
                        </div>
                        <div class="radio-box">
                            <el-radio :label="2">订单数量</el-radio>
                        </div>
                        <div class="radio-box">
                            <el-radio :label="1">指定购买商品</el-radio>
                        </div>
                        <div class="radio-box">
                            <el-radio :label="4">付费购买</el-radio>
                        </div>
                        <p class="text-color">
                            选择付费购买，支持设置会员等级有效期！
                        </p>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="会员等级定价方式:">
                    <el-radio-group v-model="formData.value.discount_type">
                        <div class="radio-box">
                            <el-radio :label="0">折扣</el-radio>
                        </div>
                        <div class="radio-box">
                            <el-radio :label="1">成本价*n</el-radio>
                        </div>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="获得等级条件:">
                    <el-radio-group v-model="formData.value.upgrade_timing">
                        <div class="radio-box">
                            <el-radio :label="1">付款后</el-radio>
                        </div>
                        <div class="radio-box">
                            <el-radio :label="2">完成后</el-radio>
                        </div>
                    </el-radio-group>
                    <p class="text-color">
                        如果选择付款后,只要用户下单付款满足升级依据,即可升级,如果选择完成后,则表示需要订单完成状态才能升级
                    </p>
                </el-form-item>
                <el-form-item label="权益说明:">
                    <m-editor v-model="formData.value.equity_statement"></m-editor>
                    <p class="color-grap">此处显示于PC端</p>
                </el-form-item>
            </el-form>   
        </el-tab-pane>
        <el-tab-pane label="默认等级设置" name="2">
            <el-form :model="formData" label-width="140px" class="mt25">
                <el-form-item label="默认会员等级:">
                    <el-select v-model="formData.value.default_level_id" clearable>
                        <el-option v-for="item in levelList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                    </el-select>
                    <p class="text-color">
                        选择后，用户注册默认成为所选等级的会员，会员等级权益以应对的等级设置为准；
                        如未选择，用户注册默认为默认等级，权益按下方设置！
                    </p>
                </el-form-item>
                <el-form-item label="采购权限:">
                    <!-- <el-checkbox-group v-model="formData.value.purchase_perm">
                        <el-checkbox :label="1" name="purchase_perm">前端采购</el-checkbox>
                        <el-checkbox :label="2" name="purchase_perm">api采购</el-checkbox>
                    </el-checkbox-group> -->
                    <el-checkbox-group v-model="checkBox">
                        <el-checkbox :label="1" name="checkBox">前端采购</el-checkbox>
                        <el-checkbox :label="2" name="checkBox">api采购</el-checkbox>
                    </el-checkbox-group>
                    <p class="text-color">
                        默认全部选中，前端采购包括中台PC端、移动端，无权限不能下单；
                        API采购包括所有通过API提交的订单，如果无权限，则无法提交订单！
                    </p>
                </el-form-item>

                <el-form-item label="技术服务费:">
                    <div class="f fac">
                        <el-input-number :min="0" :precision="2" :controls="false" class="winput" v-model="formData.value.server_radio"></el-input-number>
                        <span class="ml10">%</span>
                    </div>
                    <p></p>
                </el-form-item>
                <el-form-item label="电商cps分成比例:">
                    <div class="f fac">
                        <el-input-number :min="0" :precision="2" :controls="false" class="winput" v-model="formData.value.cps_ratio"></el-input-number>
                        <span class="ml10">%</span>
                    </div>
                    <p></p>
                </el-form-item>
                <el-form-item label="聚推联盟分成比例:">
                    <div class="f fac">
                        <el-input-number :min="0" :precision="2" :controls="false" class="winput" v-model="formData.value.jh_cps_ratio"></el-input-number>
                        <span class="ml10">%</span>
                    </div>
                    <p></p>
                </el-form-item>
                <el-form-item label="联联周边游比例:">
                    <div class="f fac">
                        <el-input-number :min="0" :precision="2" :controls="false" class="winput" v-model="formData.value.lian_ratio"></el-input-number>
                        <span class="ml10">%</span>
                    </div>
                    <p></p>
                </el-form-item>
            </el-form>            
        </el-tab-pane>
        <el-tab-pane label="协议设置" name="3">
            <el-form :model="formData" label-width="140px" class="mt25">
                <el-form-item label="注册协议:">
                    <m-editor v-model="formData.value.agreement"></m-editor>
                </el-form-item>
                <el-form-item label="用户服务协议:">
                    <m-editor v-model="formData.value.service_agreement"></m-editor>
                </el-form-item>
                <el-form-item label="隐私协议:">
                    <m-editor v-model="formData.value.privacy_policy"></m-editor>
                </el-form-item>
                <!-- <el-form-item label="等级时间限制:">
                    <el-switch v-model="formData.value.level_limit" active-color="#44D2B9" :active-value="1" :inactive-value="2"></el-switch> 
                    <el-radio-group v-model="formData.value.level_limit">
                        <el-radio :label="1">是</el-radio>
                        <el-radio :label="2">否</el-radio>
                    </el-radio-group>
                </el-form-item> -->
            </el-form>       
        </el-tab-pane>
        <el-tab-pane label="会员注册审核提示" name="4">
            <el-form :model="formData" label-width="140px" class="mt25">
                <el-form-item label="审核提示:">
                    <m-editor v-model="formData.value.audit_agreement"></m-editor>
                </el-form-item>
            </el-form>
        </el-tab-pane>
    </el-tabs>
    <el-button type="primary" @click="save" class="ml140">提交</el-button>
</m-card>