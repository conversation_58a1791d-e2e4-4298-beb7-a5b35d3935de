<template>
  <el-dialog
      :title="title"
      :visible="isShow"
      width="50%"
      :before-close="handleClose">
    <el-form label-width="90px" :model="formData" ref="form" :rules="rules">
      <el-form-item label="等级权重:" prop="weight">
        <el-input-number v-model="formData.weight" :min="1" class="number-text-left w100" :precision="0"
                         :controls="false"></el-input-number>
      </el-form-item>
      <el-form-item label="等级名称:" prop="name">
        <el-input v-model="formData.name"></el-input>
      </el-form-item>
      <el-form-item label="升级条件:" prop="upgrade_code">
        <el-radio-group v-model="formData.upgrade_code">
          <el-radio :label="1">与</el-radio>
          <el-radio :label="2">或</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <div class="f fac">
          <el-checkbox v-model="formData.order_amount_switch" :true-label="1" :false-label="0">累计订单金额</el-checkbox>
          <el-input-number class="number-text-left ml10" v-model="formData.order_amount_total" size="small" :min="0"
                           :precision="2" :controls="false"></el-input-number>
          <span class="ml10">元</span>
        </div>
      </el-form-item>
      <el-form-item>
        <div class="f fac">
          <el-checkbox v-model="formData.order_count_switch" :true-label="1" :false-label="0">累计订单数量</el-checkbox>
          <el-input-number class="number-text-left ml10" v-model="formData.order_count_total" size="small" :min="0"
                           :precision="0" :controls="false"></el-input-number>
          <span class="ml10">单</span>
        </div>
      </el-form-item>
      <el-form-item>
        <div class="f fac">
          <el-checkbox v-model="formData.recommend_supplier_switch" :true-label="1" :false-label="0">推荐供应商数量
          </el-checkbox>
          <el-input-number class="number-text-left ml10" v-model="formData.recommend_supplier_total" size="small"
                           :min="0" :precision="0" :controls="false"></el-input-number>
          <span class="ml10">个</span>
        </div>
      </el-form-item>
      <el-form-item>
        <div class="f fac">
          <el-checkbox v-model="formData.recommend_order_amount_switch" :true-label="1" :false-label="0">推荐供应商累计销售订单金额
          </el-checkbox>
          <el-input-number class="number-text-left ml10" v-model="formData.recommend_order_amount_total" size="small"
                           :min="0" :precision="2" :controls="false"></el-input-number>
          <span class="ml10">元</span>
        </div>
      </el-form-item>
      <el-form-item>
        <div class="f fac">
          <el-checkbox v-model="formData.recommend_order_count_switch" :true-label="1" :false-label="0">推荐供应商累计销售订单数量
          </el-checkbox>
          <el-input-number class="number-text-left ml10" v-model="formData.recommend_order_count_total" size="small"
                           :min="0" :precision="0" :controls="false"></el-input-number>
          <span class="ml10">个</span>
        </div>
      </el-form-item>
      <el-form-item>
        <el-checkbox v-model="formData.buy_product_switch" :true-label="1" :false-label="0">购买指定商品之一</el-checkbox>
        <div style="margin-left: 24px">
          <div class="select-goods-box f fac fw">
            <div class="select-goods-list-item" v-for="(item, index) in formData.upgrade_products" :key="item.id">
              <!--                        <img :src="item.image_url" class="w100" />-->
              <m-image :src="item.product_image"></m-image>
              <div class="bg-del-box">
                <i class="el-icon-delete" @click="delGoods(index)"></i>
              </div>
            </div>
            <div class="select-goods-list-item select-goods-item" @click="openSelectGoodsDalog">
              <i class="el-icon-plus"></i>
              <p>选择商品</p>
            </div>
          </div>
          <p class="color-grap">可指定多件商品,只需购买其中一件就可以升级</p>
        </div>
      </el-form-item>
      <el-form-item label-width="0">
        <el-checkbox v-model="formData.supplier_settle_info.amount_switch" :true-label="1" :false-label="0">订单实付金额
        </el-checkbox>
        <el-checkbox v-model="formData.supplier_settle_info.cost_switch" :true-label="1" :false-label="0">减成本
        </el-checkbox>
        <el-checkbox v-model="formData.supplier_settle_info.freight_switch" :true-label="1" :false-label="0">减运费
        </el-checkbox>
        <el-input-number v-model="formData.supplier_settle_info.formula_ratio" :controls="false" size="small"
                         :precision="2" class="number-text-left ml10"></el-input-number>
        <span class="ml10">%</span>
      </el-form-item>
      <el-form-item label-width="0">
        <el-checkbox v-model="formData.supplier_settle_info.supplier_rebate_switch" :true-label="1" :false-label="0">
          供应商扣点
        </el-checkbox>
        <el-input-number v-model="formData.supplier_settle_info.supplier_rebate_ratio" :controls="false" size="small"
                         :precision="2" class="number-text-left ml10"></el-input-number>
        <span class="ml10">%</span>
      </el-form-item>
      <el-form-item label-width="0">
        <el-checkbox v-model="formData.supplier_settle_info.buy_service_switch" :true-label="1" :false-label="0">
          采购技术服务费
        </el-checkbox>
        <el-input-number v-model="formData.supplier_settle_info.buy_service_ratio" :controls="false" size="small"
                         :precision="2" class="number-text-left ml10"></el-input-number>
        <span class="ml10">%</span>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirm">确 定</el-button>
      <el-button @click="handleClose">取 消</el-button>
    </div>
    <select-goods-dialog ref="selectGoodsDialog" @addGoodsList="addGoodsList"></select-goods-dialog>
  </el-dialog>
</template>

<script>
import SelectGoodsDialog from "@/view/member/components/selectGoodsDialog";
import {createLevel, updateLevel} from "@/api/merchant";

export default {
  name: "operateDialog",
  components: {SelectGoodsDialog},
  data() {
    return {
      title: "新增",
      isShow: false,
      formData: {
        weight: null, // 等级权重
        name: "", // 等级名称
        upgrade_code: 1, // 升级条件 1或 2与
        order_amount_switch: 0, // 累计订单金额是否勾选
        order_amount_total: null, // 累计订单金额
        order_count_switch: 0, // 累计订单数量是否勾选
        order_count_total: null, // 累计订单数量
        recommend_supplier_switch: 0, // 推荐供应商数量是否勾选
        recommend_supplier_total: null, // 推荐供应商数量
        recommend_order_amount_switch: 0, // 推荐供应商订单金额是否勾选
        recommend_order_amount_total: null, // 推荐供应商订单金额
        recommend_order_count_switch: 0, // 推荐供应商订单数量是否勾选
        recommend_order_count_total: null, // 推荐供应商订单数量
        buy_product_switch: 0, // 购买指定商品是否勾选
        upgrade_products: [], // 指定购买商品  {product_id,product_image}
        supplier_settle_info: { // 供应商订单结算详情
          amount_switch: 0, // 订单实付金额
          cost_switch: 0, // 减成本
          freight_switch: 0, // 减运费
          formula_ratio: null, // 比例
          supplier_rebate_switch: 0, // 供应商扣点
          supplier_rebate_ratio: null, // 比例
          buy_service_switch: 0, // 采购技术服务费
          buy_service_ratio: null,// 比例
        }
      },
      rules: {
        weight: {required: true, message: "请输入等级权重", trigger: "blur"},
        name: {required: true, message: "请输入等级名称", trigger: "blur"},
        upgrade_code: {required: true, message: "请选择升级条件", trigger: "blur"},
      }
    }
  },
  methods: {
    handleClose(onload = false) {
      try {
        this.$refs.form.resetFields()
      } catch {
      } finally {
        this.isShow = false
        this.title = "新增"
        this.formData = {
          weight: null, // 等级权重
          name: "", // 等级名称
          upgrade_code: 1, // 升级条件 1或 2与
          order_amount_switch: 0, // 累计订单金额是否勾选
          order_amount_total: null, // 累计订单金额
          order_count_switch: 0, // 累计订单数量是否勾选
          order_count_total: null, // 累计订单数量
          recommend_supplier_switch: 0, // 推荐供应商数量是否勾选
          recommend_supplier_total: null, // 推荐供应商数量
          recommend_order_amount_switch: 0, // 推荐供应商订单金额是否勾选
          recommend_order_amount_total: null, // 推荐供应商订单金额
          recommend_order_count_switch: 0, // 推荐供应商订单数量是否勾选
          recommend_order_count_total: null, // 推荐供应商订单数量
          buy_product_switch: 0, // 购买指定商品是否勾选
          upgrade_products: [], // 指定购买商品  {product_id,product_image}
          supplier_settle_info: { // 供应商订单结算详情
            amount_switch: 0, // 订单实付金额
            cost_switch: 0, // 减成本
            freight_switch: 0, // 减运费
            formula_ratio: null, // 比例
            supplier_rebate_switch: 0, // 供应商扣点
            supplier_rebate_ratio: null, // 比例
            buy_service_switch: 0, // 采购技术服务费
            buy_service_ratio: null,// 比例
          }
        }
        if (onload) this.$emit("onload")
      }
    },
    //赋值表单
    setFrom(val) {
      const keys = Object.keys(val);
      const that = this;
      keys.forEach((element) => {
        that.formData[element] = val[element];
      });
      that.formData.order_amount_total = that.$fn.changeMoneyF2Y(that.formData.order_amount_total)
      that.formData.recommend_order_amount_total = that.$fn.changeMoneyF2Y(that.formData.recommend_order_amount_total)
      that.formData.supplier_settle_info.formula_ratio = that.formData.supplier_settle_info.formula_ratio / 100
      that.formData.supplier_settle_info.supplier_rebate_ratio = that.formData.supplier_settle_info.supplier_rebate_ratio / 100
      that.formData.supplier_settle_info.buy_service_ratio = that.formData.supplier_settle_info.buy_service_ratio / 100
    },
    confirm() {
      this.$refs.form.validate((valid) => {
        if (!valid) return false
        let params = JSON.parse(JSON.stringify(this.formData))
        params.order_amount_total = this.$fn.changeMoneyY2F(this.formData.order_amount_total)
        params.recommend_order_amount_total = this.$fn.changeMoneyY2F(this.formData.recommend_order_amount_total)
        params.supplier_settle_info.formula_ratio = this.formData.supplier_settle_info.formula_ratio * 100
        params.supplier_settle_info.supplier_rebate_ratio = this.formData.supplier_settle_info.supplier_rebate_ratio * 100
        params.supplier_settle_info.buy_service_ratio = this.formData.supplier_settle_info.buy_service_ratio * 100

        if (this.formData.id) {
          updateLevel(params).then(res => {
            if (res.code === 0) {
              this.$message.success(res.msg)
              this.handleClose(true)
            }
          })
        } else {
          createLevel(params).then(res => {
            if (res.code === 0) {
              this.$message.success(res.msg)
              this.handleClose(true)
            }
          })
        }
      })
    },
    // 打开选择商品
    openSelectGoodsDalog() {
      this.$refs.selectGoodsDialog.isShow = true;
      this.$nextTick(() => {
        this.$refs.selectGoodsDialog.fetch();
        this.$refs.selectGoodsDialog.selectGoodsList = this.formData.upgrade_products;
      });
    },
    // 累加选中商品
    addGoodsList(goods) {
      this.formData.upgrade_products.push({
        product_id: goods.id,
        product_image: goods.image_url
      });
    },
    // 删除商品
    delGoods(index) {
      this.formData.upgrade_products.splice(index, 1);
    },
  }
}
</script>

<style lang="scss" scoped>
@import "@/style/base.scss";

.select-goods-box {
  .select-goods-list-item {
    width: 100px;
    height: 100px;
    text-align: center;
    margin-right: 10px;
    margin-top: 10px;
    position: relative;

    &:hover {
      .bg-del-box {
        display: inline-block;
      }
    }

    .bg-del-box {
      display: none;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.7);

      i.el-icon-delete {
        line-height: 100px;
        color: #fff;
        font-size: 20px;
        cursor: pointer;
      }
    }

    &:last-child {
      margin-right: 0;
    }

    &.select-goods-item {
      cursor: pointer;
      border: 1px dashed rgba(187, 187, 187, 100);

      i {
        font-size: 24px;
        margin-top: 25px;
      }
    }
  }
}
</style>