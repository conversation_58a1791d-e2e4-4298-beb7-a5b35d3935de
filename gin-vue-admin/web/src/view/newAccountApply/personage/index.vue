<!-- 开户申请个人 -->
<template>
  <m-card>
    <m-steps
        v-if="formData.signing === 1"
        :titles="['编辑资料', '资料上传']"
        :active="stepActive"
    ></m-steps>
    <m-steps
        v-else
        :titles="['填写资料', '资料上传', '协议签约']"
        :active="stepActive"
    ></m-steps>
    <!-- <el-steps :active="stepActive" simple v-if="formData.id">
      <el-step title="编辑资料"></el-step>
    </el-steps>
    <el-steps :active="stepActive" simple v-else class="mt25">
      <el-step title="填写资料"></el-step>
      <el-step title="资料上传"></el-step>
      <el-step title="协议签约"></el-step>
    </el-steps> -->

    <el-form
        :model="formData"
        label-width="110px"
        class="mt25"
        ref="form"
        v-if="stepActive === 1"
        :rules="rules"
    >
      <p class="title-p">商户信息</p>
      <el-divider></el-divider>
      <el-form-item label="供应商:" prop="member_id">
        <el-select v-model="formData.member_id" filterable class="winput">
          <el-option
              v-for="item in memberList"
              :key="item.id"
              :value="item.id"
              :label="item.value"
          ></el-option>
        </el-select>
        <el-button
            type="text"
            class="ml10"
            @click="memberLoad"
            :loading="memberLoading"
        >刷新
        </el-button
        >
      </el-form-item>
      <el-form-item label="登录名:" prop="login_name">
        <el-input
            v-model="formData.login_name"
            class="winput"
            placeholder="请输入"
        ></el-input>
        <div style="font-size: 12px">字母+数字组成，至少6位以上</div>
      </el-form-item>
      <el-form-item label="商户全称:" prop="alt_mch_name">
        <el-input
            v-model="formData.alt_mch_name"
            class="winput"
            placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="商户简称:" prop="alt_mch_short_name">
        <el-input
            v-model="formData.alt_mch_short_name"
            class="winput"
            placeholder="请输入"
        ></el-input>
      </el-form-item>
      <p class="title-p">个人信息</p>
      <el-divider></el-divider>
      <el-form-item label="主体类型:" prop="alt_merchant_type">
        <el-radio-group v-model="formData.alt_merchant_type" disabled>
          <el-radio label="10">个人</el-radio>
          <el-radio label="11">个体户</el-radio>
          <el-radio label="12">企业</el-radio>
        </el-radio-group>
        <p class="hint-p">如需修改主体类型,请在新增开户申请进行操作</p>
      </el-form-item>
      <el-form-item label="个人姓名:" prop="legal_person">
        <el-input
            v-model="formData.legal_person"
            class="winput"
            placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="联系方式:" prop="phone_no">
        <el-input
            class="winput"
            v-model="formData.phone_no"
            placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="身份证号:" prop="id_card_no">
        <el-input
            v-model="formData.id_card_no"
            class="winput"
            placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="身份证有效期:">
        <el-date-picker
            value-format="yyyy-MM-dd"
            type="date"
            placeholder="截止日期"
            v-model="formData.id_card_expiry"
        ></el-date-picker>
        <!-- <el-radio-group v-model="id_card_type">
          <div class="f fac" style="margin-bottom: 20px">
            <el-radio :label="1"> 区间有效</el-radio>
            <div class="f fac fjsb">
              <el-date-picker
                :disabled="id_card_type === 2 ? true : false"
                type="date"
                placeholder="开始日期"
                v-model="formData.start_id_card_expiry"
              ></el-date-picker>
              <span style="margin: 0 10px">至</span>
              <el-date-picker
                :disabled="id_card_type === 2 ? true : false"
                type="date"
                placeholder="截止日期"
                v-model="formData.id_card_expiry"
              ></el-date-picker>
            </div>
          </div>
          <div class="f fac">
            <el-radio :label="2"> 长期有效</el-radio>
            <el-date-picker
              :disabled="id_card_type === 1 ? true : false"
              type="date"
              placeholder="开始日期"
              v-model="formData.lang_start_id_card_expiry"
            ></el-date-picker>
          </div>
        </el-radio-group> -->
      </el-form-item>
      <p class="title-p">联系人信息</p>
      <el-divider></el-divider>
      <el-form-item label="联系人姓名:" prop="busi_contact_name">
        <el-input
            v-model="formData.busi_contact_name"
            class="winput"
            placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="手机号:" prop="busi_contact_mobile_no">
        <el-input
            v-model="formData.busi_contact_mobile_no"
            class="winput"
            placeholder="请输入"
        ></el-input>
      </el-form-item>
      <p class="title-p">结算信息</p>
      <el-divider></el-divider>
      <el-form-item label="结算方式:" prop="sett_mode">
        <el-radio-group v-model="formData.sett_mode">
          <el-radio label="1">自动结算</el-radio>
          <!--          <el-radio label="2">手工结算</el-radio>-->
          <!--          <el-radio label="3">手工+D0 结算</el-radio>-->
          <!--          <el-radio label="4">自动+D0 结算</el-radio>-->
        </el-radio-group>
      </el-form-item>
      <el-form-item label="结算周期类型:" prop="sett_date_type">
        <el-radio-group v-model="formData.sett_date_type">
          <!--          <el-radio label="1">工作日</el-radio>-->
          <el-radio label="2">自然日(D)</el-radio>
          <!--          <el-radio label="3">月结日</el-radio>-->
        </el-radio-group>
        <p class="hint-p" v-if="formData.sett_date_type === '1'">
          法定的工作时间
        </p>
        <p class="hint-p" v-if="formData.sett_date_type === '2'">每天</p>
        <p class="hint-p" v-if="formData.sett_date_type === '3'">
          每月中的某一天
        </p>
      </el-form-item>
      <el-form-item label="结算周期:" prop="risk_day">
        <template v-if="formData.sett_date_type === '3'">
          <el-input-number
              :controls="false"
              :min="1"
              :max="28"
              :precision="0"
              v-model="formData.risk_day"
              class="number-text-left winput"
              placeholder="请输入"
          ></el-input-number>
          <p class="hint-p">最大值为28</p>
        </template>
        <el-input-number
            v-else
            :controls="false"
            :min="1"
            :precision="0"
            v-model="formData.risk_day"
            class="number-text-left winput"
            placeholder="请输入"
        ></el-input-number>
      </el-form-item>
      <p class="title-p">账户信息</p>
      <el-divider></el-divider>
      <el-form-item label="银行账户名称:" prop="bank_account_name">
        <el-input
            v-model="formData.bank_account_name"
            class="winput"
            placeholder="请输入"
        ></el-input>
        <p class="hint-p">名称需与个人姓名保持一致</p>
      </el-form-item>
      <el-form-item label="银行账户:" prop="bank_account_no">
        <el-input
            v-model="formData.bank_account_no"
            class="winput"
            placeholder="请输入"
        ></el-input>
        <p class="hint-p">银行账号为个人的借记(储蓄卡)银行卡号</p>
      </el-form-item>
      <el-form-item label-width="0" class="title-3">
        <el-button type="primary" @click="createApply_valid">下一步</el-button>
        <el-button type="primary" v-if="formData.id" @click="stepActive=2">跳 过</el-button>
      </el-form-item>
    </el-form>

    <el-form
        :model="uploadFormData"
        label-width="110px"
        class="mt25"
        ref="uploadForm"
        v-if="stepActive === 2"
        :rules="uploadRules"
    >
      <p class="title-p">个人证件照</p>
      <el-divider></el-divider>
      <el-form-item label="个人信息面:" prop="Legal_person_imgz">
        <el-upload
            class="avatar-uploader"
            :show-file-list="false"
            :action="path + '/fileUploadAndDownload/upload'"
            :headers="{ 'x-token': token }"
            :on-progress="uploadPersonImgzProcess"
            :on-success="handlePersonImgzSuccess"
            :before-upload="$fn.beforeAvatarUpload"
            accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF"
        >
          <el-progress
              type="circle"
              :percentage="personImgzPercentage"
              v-if="personImgzPercentageIsShow"
              :width="178"
          ></el-progress>
          <img
              v-if="uploadFormData.Legal_person_imgz"
              :src="uploadFormData.Legal_person_imgz"
              class="avatar"
          />
          <i
              v-if="
              personImgzPercentageIsShow === false &&
              !uploadFormData.Legal_person_imgz
            "
              class="el-icon-plus avatar-uploader-icon"
          ></i>
        </el-upload>
      </el-form-item>
      <el-form-item label="国徽面:" prop="Legal_person_imgf">
        <el-upload
            class="avatar-uploader"
            :show-file-list="false"
            :action="path + '/fileUploadAndDownload/upload'"
            :headers="{ 'x-token': token }"
            :on-progress="uploadPersonImgfProcess"
            :on-success="handlePersonImgfSuccess"
            :before-upload="$fn.beforeAvatarUpload"
            accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF"
        >
          <el-progress
              type="circle"
              :percentage="personImgfPercentage"
              v-if="personImgfPercentageIsShow"
              :width="178"
          ></el-progress>
          <img
              v-if="uploadFormData.Legal_person_imgf"
              :src="uploadFormData.Legal_person_imgf"
              class="avatar"
          />
          <i
              v-if="
              personImgfPercentageIsShow === false &&
              !uploadFormData.Legal_person_imgf
            "
              class="el-icon-plus avatar-uploader-icon"
          ></i>
        </el-upload>
      </el-form-item>
      <el-form-item label-width="0" class="title-3">
        <el-button type="primary" @click="stepActive = 1">上一步</el-button>
        <el-button
            type="primary"
            v-if="formData.signing === 0"
            @click="createImages_valid('uploadForm')"
        >下一步
        </el-button
        >
        <el-button
            type="primary"
            v-else-if="formData.signing === 1"
            @click="createImages_valid('uploadForm')"
        >保存编辑
        </el-button
        >
        <el-button type="primary" v-if="signIsShow" @click="handleSignClick">签 约</el-button>
      </el-form-item>
    </el-form>
    <div v-if="stepActive === 3" class="mt25">
      <div v-html="sign_html"></div>
      <div class="title-3 mt25">
        <el-button type="primary" @click="confirmSign">确认签约</el-button>
      </div>
    </div>
  </m-card>
</template>
<script>
import {regionData} from "element-china-area-data";
import {mapGetters} from "vuex";

const path = process.env.VUE_APP_BASE_API;
import {
  createAccountApply,
  deleteAccountApply,
  deleteAccountApplyByIds,
  findAccountApply,
  findMemberList,
  findSignContract,
  signContract,
  getAccountApplyList,
  updateAccountApply,
  postImages,
  updateImages,
  saveAccountApply,
} from "../accountApply";
import verify from "@/utils/verify";

export default {
  name: "accountApplyPerSonage",
  computed: {
    ...mapGetters("user", ["userInfo", "token"]),
  },
  watch: {
    id_card_type: function (val) {
      switch (val) {
          // 区间有效
        case 1:
          this.formData.lang_start_id_card_expiry = "";
          break;
          // 长期有效
        case 2:
          this.formData.start_id_card_expiry = "";
          this.formData.id_card_expiry = "";
          break;
      }
    },
  },
  data() {
    return {
      signIsShow: false,
      isEdit: false,
      // 刷新按钮加载
      memberLoading: false,
      path: path,
      // 协议
      sign_html: "",
      // 步骤
      stepActive: 1,
      // 填写资料表单
      formData: {
        // 分账方编号
        alt_mch_no: "",
        // 会员id
        member_id: null,
        // 登录名
        login_name: "",
        // 商户全称
        alt_mch_name: "",
        // 商户简称
        alt_mch_short_name: "",
        // 主体类型
        alt_merchant_type: "10",
        // 个人姓名
        legal_person: "",
        // 身份证号
        id_card_no: "",
        // 联系方式
        phone_no: "",
        // 银行账户名称
        bank_account_name: "",
        // 银行账户
        bank_account_no: "",
        // 开始日期
        start_id_card_expiry: "",
        // 截止日期
        id_card_expiry: "",
        // 长期开始日期
        lang_start_id_card_expiry: "",
        // 联系人姓名
        busi_contact_name: "",
        // 联系人手机号
        busi_contact_mobile_no: "",
        // 结算账户类型 1 个人借记卡
        bank_account_type: "1",
        // 结算方式
        sett_mode: "1",
        // 结算周期类型
        sett_date_type: "2",
        // 结算周期
        risk_day: "",
        signing:0
      },
      memberList: [],
      // 身份证期限选择 1= 区间有效 2 =长期有效
      id_card_type: 1,
      // 资料上传表单
      uploadFormData: {
        // 个人信息面
        Legal_person_imgz: "",
        // 国徽面
        Legal_person_imgf: "",
      },
      // 身份证正面进度是否显示
      personImgzPercentageIsShow: false,
      // 身份证正面进度
      personImgzPercentage: 0,
      // 身份证反面进度是否显示
      personImgfPercentageIsShow: false,
      // 填写资料表单验证
      rules: {
        // 会员选择验证
        member_id: {
          required: true,
          message: "请选择会员",
          trigger: "blur",
        },
        // 登录名
        login_name: {
          required: true,
          message: "请输入登录名",
          trigger: "blur",
        },
        // 商户全称
        alt_mch_name: {
          required: true,
          message: "请输入商户全称",
          trigger: "blur",
        },
        // 主体类型
        alt_merchant_type: {
          required: true,
          message: "请选择主体类型",
          trigger: "change",
        },
        // 个人姓名
        legal_person: {
          required: true,
          message: "请输入个人姓名",
          trigger: "blur",
        },
        // 身份证号
        id_card_no: [
          {
            required: true,
            message: "请输入身份证号",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => {
              let res = verify.checkIDCard(value);
              return res
                  ? callback()
                  : callback(new Error("身份证号格式不正确"));
            },
          },
        ],
        // 联系方式
        phone_no: [
          {
            required: true,
            message: "请输入联系方式",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => {
              let res = verify.checkPhone(value);
              return res ? callback() : callback(new Error("手机号格式不正确"));
            },
          },
        ],
        // 联系人姓名
        busi_contact_name: {
          required: true,
          message: "请输入联系人姓名",
          trigger: "blur",
        },
        // 联系人手机号
        busi_contact_mobile_no: [
          {
            required: true,
            message: "请输入手机号",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => {
              let res = verify.checkPhone(value);
              return res ? callback() : callback(new Error("手机号格式不正确"));
            },
          },
        ],
        // 银行账户名称
        bank_account_name: [
          {
            required: true,
            message: "请输入银行账户名称",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => {
              return value === this.formData.legal_person
                  ? callback()
                  : callback(new Error("银行账户名称需与个人姓名一致"));
            },
          },
        ],
        // 银行账户
        bank_account_no: [
          {
            required: true,
            message: "请输入银行账户",
            trigger: "blur",
          },
        ],
        // 结算方式
        sett_mode: {
          required: true,
          message: "请选择结算方式",
          trigger: "change",
        },
        // 结算周期类型
        sett_date_type: {
          required: true,
          message: "请选择结算周期类型",
          trigger: "change",
        },
        // 结算周期
        risk_day: {
          required: true,
          message: "请输入结算周期",
          trigger: "blur",
        },
      },

      // 资料上传表单验证
      uploadRules: {
        Legal_person_imgz: {
          required: true,
          message: "请上传个人信息面",
        },
        Legal_person_imgf: {
          required: true,
          message: "请上传国徽面",
        },
      },
    };
  },
  mounted() {
    this.getMemberList();
    if (this.$route.query.member_id) {
      findAccountApply({id: this.$route.query.member_id}).then((res) => {
        if (res.code === 0) {
          if (
              res.data.reAccount.Legal_person_imgz &&
              res.data.reAccount.Legal_person_imgf
          ) {
            this.isEdit = true;
          }
          this.uploadFormData.Legal_person_imgz =
              res.data.reAccount.Legal_person_imgz;
          this.uploadFormData.Legal_person_imgf =
              res.data.reAccount.Legal_person_imgf;
          if (res.data.reAccount.signing === 0 && res.data.reAccount.Legal_person_imgz && res.data.reAccount.Legal_person_imgf) {
            this.signIsShow = true
          }
          delete res.data.reAccount.Legal_person_imgz;
          delete res.data.reAccount.Legal_person_imgf;
          this.setFrom(res.data.reAccount);
        } else {
          this.$message.error(res.msg);
        }
      });
    }
  },
  methods: {
    // 去签约
    handleSignClick() {
      let form = this.$fn.extend(this.formData, this.uploadFormData);
      this.findSign(form);
    },
    // 刷新会员
    memberLoad() {
      this.memberLoading = true;
      findMemberList({}).then((res) => {
        if (res.code === 0) {
          this.memberList = res.data.list;
        }
        this.memberLoading = false;
      });
    },
    // 获取会员
    getMemberList() {
      findMemberList({}).then((res) => {
        if (res.code === 0) {
          this.memberList = res.data.list;
        }
      });
    },
    //赋值表单
    setFrom(val) {
      const keys = Object.keys(val);
      const that = this;
      keys.forEach((element) => {
        that.formData[element] = val[element];
      });
    },
    // 清空form表单
    emptyForm() {
      try {
        this.$refs.form.resetFields();
      } catch {
      }
      // this.$refs.uploadForm.resetFields();
      (this.uploadFormData = {
        // 个人信息面
        Legal_person_imgz: "",
        // 国徽面
        Legal_person_imgf: "",
      }),
          (this.sign_html = "");
      this.stepActive = 1;
      this.id_card_type = 1;
    },
    // 身份证正面success
    handlePersonImgzSuccess(res) {
      this.uploadFormData.Legal_person_imgz = res.data.file.url;
    },
    // 身份证正面进度条
    uploadPersonImgzProcess(event, file, fileList) {
      this.personImgzPercentageIsShow = true;
      this.personImgzPercentage = parseInt(event.percent);
      if (this.personImgzPercentage >= 100) {
        this.personImgzPercentag = 100;
        setTimeout(() => {
          this.personImgzPercentageIsShow = false;
        }, 1000); //一秒后关闭进度条
      }
    },
    // 身份证反面success
    handlePersonImgfSuccess(res) {
      this.uploadFormData.Legal_person_imgf = res.data.file.url;
    },
    // 身份证反面进度条
    uploadPersonImgfProcess(event, file, fileList) {
      this.personImgfPercentageIsShow = true;
      this.personImgfPercentage = parseInt(event.percent);
      if (this.personImgfPercentage >= 100) {
        this.personImgfPercentage = 100;
        setTimeout(() => {
          this.personImgfPercentageIsShow = false;
        }, 1000); //一秒后关闭进度条
      }
    },
    beforeAvatarUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.$message.error("图片大小不能超过 2MB!");
      }
      return isLt2M;
    },
    /**
     * 步骤
     * formName 表单ref
     * step     步骤
     * is_verif 是否表单验证  y 验证  n 不验证
     */
    /*handleNextClick(formName, step, is_verif) {
      // 下一步  验证表单
      if (is_verif === "y") {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            this.stepActive = step;
            // 获取签约协议
            if (step === 3) {
              findSignContract(this.formData).then((res) => {
                if (res.code === 0) {
                  this.sign_html = res.data.reAccount.data.sign_content;
                }
              });
            }
          } else {
            return false;
          }
        });
      } else {
        // 上一步  不验证表单
        this.stepActive = step;
      }
    },*/
    // 下一步 资料信息表单提交
    createApply_valid() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 编辑
          if (this.formData.id) {
            saveAccountApply(this.formData).then((res) => {
              if (res.code === 0) {
                this.formData.alt_mch_no = res.data.data.alt_mch_no;
                this.$message.success("修改成功");
                this.stepActive = 2;
              }
            });
          } else {
            // 添加
            createAccountApply(this.formData).then((res) => {
              if (res.code == 0) {
                this.formData.alt_mch_no = res.data.data.alt_mch_no;
                this.$message({
                  type: "success",
                  message: "开户成功，请继续上传图像资料",
                });
                this.stepActive = 2;
              }
            });
          }
        } else {
          return false;
        }
      });
    },
    // 下一步 提交资料图片表单
    createImages_valid(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let form = this.$fn.extend(this.formData, this.uploadFormData);
          this.uploadFormData.alt_mch_no = this.formData.alt_mch_no;
          // 编辑
          if (this.isEdit) {
            let para = {
              ...this.uploadFormData,
            };
            if (this.formData.id) {
              para.id = this.formData.id;
            }
            updateImages(para).then((res) => {
              if (res.code === 0) {
                this.$message({
                  type: "success",
                  message: res.msg,
                });
                if (this.formData.signing === 1) {
                  this.emptyForm();
                  this.$router.push("/layout/Finance/accountApply");
                } else if (this.formData.signing === 0) {
                  this.findSign(form);
                }
              }
            });
          } else {
            let para = {
              ...this.uploadFormData,
            };
            if (this.formData.id) {
              para.id = this.formData.id;
            }
            postImages(para).then((res) => {
              if (res.code === 0) {
                this.$message({
                  type: "success",
                  message: res.msg,
                });
                this.findSign(form);
                /* this.stepActive = 3;
                findSignContract(form).then((res) => {
                  if (res.code === 0) {
                    this.sign_html = res.data.reAccount.data.sign_content;
                  }
                }); */
              }
            });
          }
        } else {
          return false;
        }
      });
    },
    // 获取签约
    findSign(form) {
      this.stepActive = 3;
      findSignContract(form).then((res) => {
        if (res.code === 0) {
          this.sign_html = res.data.reAccount.data.sign_content;
        }
      });
    },
    // 确认签约
    confirmSign() {
      let form = this.$fn.extend(this.formData, this.uploadFormData);
      signContract(form).then((res) => {
        if (res.code === 0) {
          this.$message.success("操作成功");
          this.emptyForm();
          this.$router.push("/layout/Finance/accountApply");
        } else {
          this.$message.error(res.msg);
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/style/base.scss";

p.title-p {
  font-size: 16px;
  font-weight: bold;
}

.el-divider {
  margin: 15px 0;
}

.winput {
  width: 300px;
}

.hint-p {
  font-size: 12px;
  color: #c0c4cc;
  line-height: 20px;
}

::v-deep .el-steps {
  .el-step__main {
    .el-step__arrow {
      &::before,
      &::after {
        width: 3px;
        height: 30px;
        background-color: #ffffff;
      }

      &::before {
        transform: rotate(-40deg) translateY(-10px);
      }

      &::after {
        transform: rotate(40deg) translateY(10px);
      }
    }
  }
}

::v-deep .avatar-uploader {
  .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    width: 178px;
    height: 178px;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;

  line-height: 178px;
  text-align: center;
}
</style>