<template>
  <m-card>
    <div class="f fac">
      <el-upload
          :action="path + '/adminSupplier/upload'"
          :headers="{ 'x-token': token }"
          :show-file-list="false"
          accept=".xlsx"
          :before-upload="beforeFileUpload"
          :on-success="handleFileSuccess">
        <el-button type="primary">上传模板</el-button>
      </el-upload>
      <el-button class="ml10" @click="download">下载模板</el-button>
    </div>
    <div class="search-term mt25 f fac">
      <div class="step-box f fac">
        <span class="sum">1</span>
        <p class="text ml10">下载发货单模板</p>
      </div>
      <div class="divider"></div>
      <div class="step-box f fac">
        <span class="sum">2</span>
        <p class="text ml10">按模板填写发货单</p>
      </div>
      <div class="divider"></div>
      <div class="step-box f fac">
        <span class="sum">3</span>
        <p class="text ml10">上传发货单</p>
      </div>
      <div class="divider"></div>
      <div class="step-box f fac">
        <span class="sum">4</span>
        <p class="text ml10">确认发货</p>
      </div>
      <div class="step-box f fac"  style="color: red">
        <span class="sum">注意：</span>
        <p class="text ml10">批量发货为异步，请勿短时间上传同一发货模板</p>
      </div>
    </div>
    <el-table class="mt25" :data="list">
      <el-table-column label="发货时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.created_at | formatDate }}
        </template>
      </el-table-column>
      <!--      <el-table-column label="头像" align="center">
              <template slot-scope="scope">
                <m-image style="width:60px;height:60px"></m-image>
              </template>
            </el-table-column>-->
      <!--      <el-table-column label="会员名称" align="center"></el-table-column>-->
      <el-table-column label="发货数量" prop="send_num" align="center"></el-table-column>
      <el-table-column label="成功数量" prop="success_num" align="center"></el-table-column>
      <el-table-column label="状态"  align="center">
        <template slot-scope="scope">
          {{ scope.row.success_num + scope.row.fail_num  ===  scope.row.send_num ? '已完成':'发货中' }}
        </template>
      </el-table-column>
      <el-table-column label="失败数量" prop="fail_num" align="center"></el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
<!--          <el-button type="text" v-if="scope.row.fail_excel_link" @click="dow(scope.row)">下载错误模板</el-button>-->
          <el-button type="text" @click="find(scope.row)">查看记录</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background :current-page="page" :page-size="pageSize" :page-sizes="[10, 30, 50, 100]"
                   :style="{display: 'flex',justifyContent: 'flex-end',marginRight: '20px'}" :total="total"
                   @current-change="handleCurrentChange" @size-change="handleSizeChange"
                   layout="total, sizes, prev, pager, next, jumper"></el-pagination>
    <!-- <el-dialog
        title="选择快递"
        :visible="isShow"
        width="30%"
        :before-close="handleClose">
      <el-form :model="formData" label-width="80px">
        <el-form-item label="选择快递:">
          <el-select v-model="formData.express_code" filterable clearable class="w100">
            <el-option v-for="item in expressList" :key="item.id" :value="item.code" :label="item.name"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="confirmBtnLoading" @click="$fn.clicks(confirm,3000)">确 定</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </el-dialog> -->
    <batchDeliverDetailDialog ref="batchDeliverDetailDialog"></batchDeliverDetailDialog>
  </m-card>
</template>

<script>
import {mapGetters} from "vuex";
import {getSendOrderRecordList,getSendOrderList,getShippingCompany,batchSendOrder} from "@/api/order"
import batchDeliverDetailDialog from "./components/batchDeliverDetailDialog";
export default {
  name: "batchDeliverList",
  components:{batchDeliverDetailDialog},
  data() {
    return {
      confirmBtnLoading:false,
      path: this.$path,
      formData: {
        // express_code: "",
        link: "",
      },
      page: 1,
      pageSize: 10,
      total: 0,
      list: [],
      // isShow: false,
      // expressList: []
    }
  },

  computed: {
    ...mapGetters("user", ["userInfo", "token"]),
  },
  mounted() {
    this.fetch()
  },
  methods: {
    find(row) {
      this.$refs.batchDeliverDetailDialog.isShow = true
      this.$refs.batchDeliverDetailDialog.getExpress()
      this.$refs.batchDeliverDetailDialog.fetch(row)
    },
    // 下载错误模板
    dow(row) {
      window.location.href = this.path + '/' + row.fail_excel_link
      // window.location.href = 'template.xlsx'
    },
    confirm() {
      // if (!this.formData.express_code) {
      //   this.$message.error("请选择快递")
      //   return false
      // }
      if (!this.formData.link) {
        this.$message.error("请上传模板")
        return false
      }
      // this.confirmBtnLoading = true
      batchSendOrder(this.formData).then(res => {
        if (res.code === 0) {
          this.$message.success(res.msg)
          this.handleClose()
          this.fetch()
        } else {
          this.$message.error(res.msg)
        }
        // this.confirmBtnLoading = false
      })
    },
    handleClose() {
      // this.isShow = false
      this.formData = {
        // express_code: "",
        link: "",
      }
      // this.expressList = []
    },
    handleCurrentChange(page) {
      this.page = page;
      this.fetch();
    },
    handleSizeChange(size) {
      this.pageSize = size;
      this.fetch();
    },
    fetch() {
      getSendOrderRecordList({page: this.page, pageSize: this.pageSize}).then(res => {
        if (res.code === 0) {
          this.list = res.data.list
          this.total = res.data.total
        }
      })
    },
    download() {
      window.location.href = 'template.xlsx'
    },
    handleFileSuccess(res) {
      if (res.code === 0) {
        console.log(111);
        this.formData.link = res.data.file.key
        this.confirm()
        // this.isShow = true
        // this.getExpress();
      }
    },
    //获取快递公司信息
    // getExpress() {
    //   this.expressList = [];
    //   getShippingCompany().then(res => {
    //     if (res.code === 0) {
    //       this.expressList = res.data.list;
    //     }
    //   });
    // },
    beforeFileUpload(file) {
      const verifySize = file.size / 1024 < 100;
      if (!verifySize) {
        this.$message.error("上传文件大小不能超过 100k!");
      }
      return verifySize;
    },
  }
}
</script>

<style scoped>

</style>