<!-- 空状态 -->
<template>
  <div class="empty">{{ text }}</div>
</template>
<script>
export default {
  props: {
    text: {
      type: String,
      default: () => {
        return "暂无数据";
      },
    },
  },
};
</script>
<style lang="scss" scoped>
.empty {
  width: 100%;
  height: 100%;
  color: #acacac;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}
</style>