<el-drawer title="选择地区" :append-to-body="true" :modal-append-to-body="false" :visible="isShow"
           :close-on-press-escape="false" :wrapperClosable="false" :before-close="handleClose" size="calc(100% - 220px)"
           class="detail-ct">
    <m-card class="drawer_content">
        <div class="f1">
            <div class="main">
                <div class="item">
                    <div class="title">省</div>
                    <div class="list">
                        <template v-if="checkedArea.length > 0">
                            <div class="li" :class="check_province_id === province.id ? 'true' : 'false'"
                                 @click="getCityOptions(province)" v-for="province in checkedArea" :key="province.id">
                                <template v-if="province.disabled">
                                    <div class="select disabled">

                                    </div>
                                    <div class="name">{{ province.name }}</div>
                                    <div class="code">{{ province.id }}</div>
                                    <div class="go">
                                        <div class="el-icon-arrow-right"></div>
                                    </div>
                                </template>
                                <template v-else>
                                    <div class="select" :class="
                      province.checked || province.check_children_num > 0
                        ? 'true'
                        : 'false'
                    " @click.stop="checkedProvince(province)">
                                        <i v-if="province.checked" class="el-icon-check"></i>
                                        <span v-else-if="province.check_children_num">
                                        {{ province.check_children_num }}
                                    </span>
                                    </div>
                                    <div class="name">{{ province.name }}</div>
                                    <div class="code">{{ province.id }}</div>
                                    <div class="go">
                                        <div class="el-icon-arrow-right"></div>
                                    </div>
                                </template>

                            </div>
                        </template>
                        <Empty v-else></Empty>
                    </div>
                </div>
                <div class="item">
                    <div class="title">市</div>
                    <div class="list">
                        <template v-if="check_province_id !== null">
                            <div class="li" :class="check_city_id === city.id ? 'true' : 'false'"
                                 v-for="city in getAreaFind(1).children" :key="city.id" @click="getCountyOption(city)">
                                <template v-if="city.disabled">
                                    <div class="select disabled">

                                    </div>
                                    <div class="name">{{ city.name }}</div>
                                    <div class="code">{{ city.id }}</div>
                                    <div class="go">
                                        <div class="el-icon-arrow-right"></div>
                                    </div>
                                </template>
                                <template v-else>
                                    <div class="select" :class="
                      city.checked || city.check_children_num > 0
                        ? 'true'
                        : 'false'
                    " @click.stop="checkedCity(city)">
                                        <i v-if="city.checked" class="el-icon-check"></i>
                                        <span v-else-if="city.check_children_num > 0">{{
                                                city.check_children_num
                                            }}</span>
                                    </div>
                                    <div class="name">{{ city.name }}</div>
                                    <div class="code">{{ city.id }}</div>
                                    <div class="go">
                                        <div class="el-icon-arrow-right"></div>
                                    </div>
                                </template>
                            </div>
                        </template>
                        <Empty v-else></Empty>
                    </div>
                </div>
                <div class="item">
                    <div class="title">区县</div>
                    <div class="list">
                        <template v-if="check_city_id !== null">
                            <div class="li" :class="check_county_id === county.id ? 'true' : 'false'"
                                 v-for="county in getAreaFind(2).children" :key="county.id"
                                 @click="getVillages(county)">
                                <template v-if="county.disabled">
                                    <div class="select disabled">

                                    </div>
                                    <div class="name">{{ county.name }}</div>
                                    <div class="code">{{ county.id }}</div>
                                    <div class="go">
                                        <div class="el-icon-arrow-right"></div>
                                    </div>
                                </template>
                                <template v-else>
                                    <div class="select" :class="
                      county.checked || county.check_children_num > 0
                        ? 'true'
                        : 'false'
                    " @click.stop="checkedCounty(county)">
                                        <i v-if="county.checked" class="el-icon-check"></i>
                                        <span v-else-if="county.check_children_num > 0">{{
                                                county.check_children_num
                                            }}</span>
                                    </div>
                                    <div class="name">{{ county.name }}</div>
                                    <div class="code">{{ county.id }}</div>
                                    <div class="go">
                                        <div class="el-icon-arrow-right"></div>
                                    </div>
                                </template>
                            </div>
                        </template>
                        <Empty v-else></Empty>
                    </div>
                </div>
                <div class="item">
                    <div class="title">乡镇街道</div>
                    <div class="list">
                        <template v-if="check_county_id !== null">
                            <div class="li" v-for="villages in getAreaFind(3).children" :key="villages.id">
                                <template v-if="villages.disabled">
                                    <div class="select disabled">
                                    </div>
                                    <div class="name">{{ villages.name }}</div>
                                    <div class="code">{{ villages.id }}</div>
                                    <div class="go">
                                        <div class="el-icon-arrow-right"></div>
                                    </div>
                                </template>
                                <template v-else>
                                    <div class="select" :class="villages.checked ? 'true' : 'false'"
                                         @click="checkedVillages(villages)">
                                        <i class="el-icon-check"></i>
                                    </div>
                                    <div class="name">{{ villages.name }}</div>
                                    <div class="code">{{ villages.id }}</div>
                                    <div class="go">
                                        <div class="el-icon-arrow-right"></div>
                                    </div>
                                </template>
                            </div>
                        </template>
                        <Empty v-else></Empty>
                    </div>
                </div>
                <div class="item">
                    <div class="title">已选省、市、区县、乡镇街道</div>
                    <div class="selectList">
                        <template v-if="isChecked">
                            <div class="ul" v-for="province in checkedArea" :key="province.id">
                                <div class="li" v-if="province.checked || province.check_children_num > 0">
                                    <div class="border">
                                        <div class="left"></div>
                                        <div class="name">{{ province.name }}</div>
                                        <div class="del" @click="delProvince(province)">
                                            <i class="el-icon-delete"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="ul" v-for="city in province.children" :key="city.id">
                                    <div class="li" v-if="city.checked || city.check_children_num > 0">
                                        <div class="border">
                                            <div class="left"></div>
                                            <div class="name">{{ city.name }}</div>
                                            <div class="del" @click="delCity(city)">
                                                <i class="el-icon-delete"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="ul" v-for="county in city.children" :key="county.id">
                                        <div class="li" v-if="county.checked || county.check_children_num > 0">
                                            <div class="border">
                                                <div class="left"></div>
                                                <div class="name">{{ county.name }}</div>
                                                <div class="del" @click="delCounty(county)">
                                                    <i class="el-icon-delete"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="ul" v-for="villages in county.children" :key="villages.id">
                                            <div class="li" v-if="
                            villages.checked || villages.check_children_num > 0
                          ">
                                                <div class="border">
                                                    <div class="left"></div>
                                                    <div class="name">{{ villages.name }}</div>
                                                    <div class="del" @click="delVillages(villages)">
                                                        <i class="el-icon-delete"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                        <Empty v-else text="暂无已选数据"></Empty>
                    </div>
                </div>
            </div>
        </div>
        <div class="drawer__footer">
            <el-button type="primary" @click="save">保 存</el-button>
            <el-button @click="handleClose">取 消</el-button>
        </div>
    </m-card>
</el-drawer>