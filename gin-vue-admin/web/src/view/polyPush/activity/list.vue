<template>
  <m-card>
<!--    <el-button type="primary" @click="openBrandDialog()">新增</el-button>-->
    <el-form class="search-term mt25" :model="searchInfo" label-width="90px" inline>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >联盟平台</span>
              </div>
              <el-select v-model="searchInfo.type" class="w100" clearable >
                <el-option :label="item.name" :value="item.value" v-for="item in allianceConditions"></el-option>
              </el-select>
          </div>
      </el-form-item>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >状态</span>
              </div>
              <el-select v-model="searchInfo.status" class="w100" clearable >
                <el-option :label="item.name" :value="item.value" v-for="item in statusConditions"></el-option>
              </el-select>
          </div>
      </el-form-item>
      <el-form-item>
          <el-input placeholder="请输入" v-model="searchInfo.title" class="line-input" clearable>
              <span slot="prepend">活动名称</span>
          </el-input>
      </el-form-item>
      <el-form-item>
          <el-input placeholder="请输入" v-model="searchInfo.activity_id" class="line-input" clearable>
              <span slot="prepend">第三方ID</span>
          </el-input>
      </el-form-item>
      <el-row>
        <el-col :span="6">
          <el-button type="primary" @click="search">搜索</el-button>
<!--          <el-button @click="orderExport">导出</el-button>-->
          <el-button type="text" @click="reSearch">重置搜索条件</el-button>
        </el-col>
        <el-col :span="6" :push="16">
          <el-button type="primary" @click="openActivityDialog">添加活动</el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-table class="mt25" :data="activityList">
      <el-table-column label="ID" align="center" prop="id"></el-table-column>
      <el-table-column label="联盟平台" align="center" prop="type">
        <template slot-scope="scope">
          {{scope.row.type === 'meituan' ? '美团':scope.row.type === 'didi'?'滴滴':scope.row.type === 'eleme'? '饿了么':''}}
        </template>
      </el-table-column>
      <el-table-column label="第三方ID" align="center" prop="activity_id"></el-table-column>
      <el-table-column label="到期时间" align="center">
          <template slot-scope="scope">
              {{scope.row.expire_time | formatDate}}
          </template>
      </el-table-column>
<!--      <el-table-column label="活动分类" align="center" prop="category_id"></el-table-column>-->
      <el-table-column label="活动名称" align="center" prop="title"></el-table-column>
      <el-table-column label="活动主图" align="center">
        <template slot-scope="scope">
          <m-image style="width: 60px;height:60px" :src="scope.row.image"></m-image>
        </template>
      </el-table-column>
      <el-table-column label="佣金比率" align="center" prop="commission_rate">
        <template slot-scope="scope">
          {{scope.row.commission_rate}}
        </template>
      </el-table-column>
      <el-table-column label="结算节点" align="center" prop="settle_time"></el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
        <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0"
                   @change="handleStatus(scope.row,'is_display')">
        </el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click="openActivityDialog(scope.row)" style="padding: 0 !important;margin-left: 10px !important;">编辑</el-button>
          <el-button type="text" class="color-red" @click="del(scope.row)" style="padding: 0 !important;margin-left: 10px !important;">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background :current-page="page" :page-size="pageSize" :page-sizes="[10, 30, 50, 100,200]"
                   :style="{display: 'flex',justifyContent: 'flex-end',marginRight: '20px'}" :total="total"
                   @current-change="handleCurrentChange" @size-change="handleSizeChange"
                   layout="total, sizes,prev, pager, next, jumper"></el-pagination>
    <activity-dialog ref="activityDialog" @reload="activityManagementList"></activity-dialog>
  </m-card>
</template>
<script>
import infoList from "../../../mixins/infoList";
import {deleteActivity,activityManagementList,orderExport,setAttributeStatus} from "@/api/pushAlliance";
import activityDialog from "./components/activityDialog";

export default {
  name: "brandIndex",
  components: {activityDialog},
  mixins: [infoList],
  data() {
    return {
      // listApi: getBrandsList,
      searchInfo:{
        type:'',
        title:'',
      },
      // 分页部分
      page: 1,
      pageSize: 10,
      total: 0,
      // 列表
      activityList: [],
      //活动
      supplyOptions:[
        {name:"美团",value:"meituan"},
      ],
      //联盟平台
      allianceConditions:[
        {name:"全部",value:""},
        {name:"美团联盟",value:"meituan"},
        {name:"滴滴",value:"didi"},
        {name:"饿了么",value:"eleme"},
      ],
      //状态
      statusConditions:[
        {name:"关闭",value:0},
        {name:"启用",value:1},
      ],
    }
  },
  created() {
    // this.activityManagementList()
  },
  mounted() {
    this.activityManagementList()
  },
  methods: {
    activityManagementList(){
      let data={
        "page":this.page,
        "pageSize":this.pageSize,
        "type":this.searchInfo.type,
        "title":this.searchInfo.title,
        "status":this.searchInfo.status,
        "activity_id":this.searchInfo.activity_id
      }
      activityManagementList(data).then(res => {
        if (res.code === 0) {
          console.log(res)
           this.activityList = res.data.list
           this.page = res.data.page
           this.pageSize = res.data.pageSize
           this.total = res.data.total
        } else {
          this.$message.error(res.msg)
        }
      })

    },
    openActivityDialog(row = {}) {
      this.$refs.activityDialog.isShow = true
      if (row && row.type != 'click') {
        this.$nextTick(() => {
          this.$refs.activityDialog.formData.id = row.id
          this.$refs.activityDialog.formData.title = row.title
          this.$refs.activityDialog.formData.type = row.type
          this.$refs.activityDialog.formData.image = row.image
          this.$refs.activityDialog.formData.activity_id = row.activity_id
          this.$refs.activityDialog.formData.settle_type = row.settle_type
          this.$refs.activityDialog.formData.settle_time = row.settle_time
          this.$refs.activityDialog.formData.commission_rate = row.commission_rate
          this.$refs.activityDialog.formData.description = row.description
          if(row.expire_time){
              this.$refs.activityDialog.formData.expire_time = parseInt(row.expire_time * 1000)
          }
        })
      }else{
          this.$nextTick(()=>{
              let d = new Date()
              this.$refs.activityDialog.formData.expire_time = d.getTime()
          })
      }
    },
    //状态
    async handleStatus(row, name) {
      console.log(row)
      let params = {
        column: name,
        id: row.id,
        status: row[name]
      }
      let res = await setAttributeStatus(params)
      if (res.code === 0) {
        this.$message.success(res.msg)
      }
    },
    //导出
    orderExport() {
      console.log(111)
      let params = this.getSearchCondition()
      orderExport(params).then(res => {
        if (res.code === 0) {
          window.open(this.path + '/' + res.data.link)
          // window.location.href = this.path + '/' + res.data.link
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    del(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let res = await deleteActivity({id: row.id})
        if(res.code === 0){
          this.$message.success(res.msg)
          this.activityManagementList()
        }
      }).catch(() => {
      })
    },
    reSearch() {
      this.page = 1
      this.searchInfo = {}
    },
    search() {
      this.page = 1
      this.activityManagementList()
    },
    // 下一页
    handleCurrentChange(page) {
      this.page = page
      this.activityManagementList()
    },
    // 每页显示条数
    handleSizeChange(size) {
      this.pageSize = size
      this.activityManagementList()
    },
  }
}
</script>
<style lang="scss" scoped>
@import "@/style/base.scss";
</style>