<template>
  <m-card class="search-box">
    <el-form ref="ref" :model="formData" class="search-term" label-width="130px" inline>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >订单状态</span>
              </div>
              <el-select v-model="formData.status" class="w100" clearable>
                <el-option label="全部" value=""></el-option>
                <el-option :label="item.name" :value="item.value" v-for="item in orderSupplierConditions"></el-option>
              </el-select>
          </div>
      </el-form-item>
      <el-form-item>
          <el-input placeholder="请输入" v-model="formData.order_sn" class="line-input" clearable>
              <span slot="prepend">订单号</span>
          </el-input>
      </el-form-item>
      <el-form-item>
          <el-input placeholder="请输入" v-model="formData.username" class="line-input" clearable>
              <span slot="prepend">手机号/昵称</span>
          </el-input>
      </el-form-item>
      <el-form-item>
          <el-input placeholder="请输入" v-model="formData.application_id" class="line-input" clearable>
              <span slot="prepend">采购端平台会员ID</span>
          </el-input>
      </el-form-item>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >联盟平台</span>
              </div>
              <el-select v-model="formData.alliance" class="w100" clearable>
                <el-option label="全部" value=""/>
                <el-option :label="item.name" :value="item.value" v-for="item in allianceConditions"></el-option>
              </el-select>
          </div>
      </el-form-item>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >时间类型</span>
              </div>
              <el-select v-model="dateType" class="w100">
                <el-option v-for="item in dateTypeOptios" :key="item.id" :label="item.label"
                            :value="item.value"></el-option>
              </el-select>
          </div>
      </el-form-item>
      <el-form-item>
        <div class="line-input" style="width: 586px;">
          <div class="f fac ">
            <el-date-picker class="w100" v-model="formData.start_at" type="datetime"
                            placeholder="开始日期">
            </el-date-picker>
            <span class="zih-span">至</span>
            <el-date-picker class="w100" v-model="formData.end_at" type="datetime"
                            placeholder="结束日期">
            </el-date-picker>
          </div>
        </div>
      </el-form-item>
      <el-form-item label-width="0px">
        <div class="f fac dateBtnBox">
          <span @click="handleDateTab(item)" v-for="item in dateList"
              :class="time_type === item.value?'is_active':''"
              :key="item.id">{{ item.name }}</span>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="searchOrder()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button @click="xportActivity">导出</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="text" @click="clearSearchCondition()">重置搜索条件</el-button>
      </el-form-item>
    </el-form>
    <div class="table-box">
      <el-table :data="[{}]" class="table-head">
        <el-table-column label="商品" width="300"></el-table-column>
        <el-table-column label="联盟平台/支付金额" width="200" align="center"></el-table-column>
        <el-table-column label="手机号(ID)/会员昵称" width="200" align="center"></el-table-column>
        <el-table-column label="预估佣金/佣金比率" width="200" align="center"></el-table-column>
        <el-table-column label="分成基数/分成比例/分成金额" width="250" align="center"></el-table-column>
        <el-table-column label="订单状态" width="200" align="center"></el-table-column>
        <el-table-column label="操作" align="center"></el-table-column>
      </el-table>
      <div v-for="item in orderList" :key="item.id">
        <el-table :data="[item]" class="table-cont" :span-method="objectSpanMethod">
          <el-table-column>
            <template slot="header" slot-scope="scope">
              <div class="w100 f fac fjsb">
                <div class="f fac fw">
                  <p>订单ID: {{ item.id }}</p>
                  <p>订单编号: {{ item.order_sn }}</p>
                  <p>联盟订单号: {{ item.order_id }}</p>
                  <!--&lt;!&ndash;                  <p>支付单号: {{ item.pay_info.pay_sn }}</p>&ndash;&gt;-->
                  <P>支付时间：{{ item.pay_at | formatDate }}</P>
                </div>
              </div>
            </template>
            <el-table-column width="300">
              <template slot-scope="scope">
                <div class="f fac goods-box">
                  <!--                  <m-image style="width: 60px;height:60px" :src="scope.row.product_img">-->
                  <!--                  </m-image>-->
                  <div class="f1">
                    <p class="hiddenText2">
                      <a href="javascript:;" style="color: #155bd4;"
                         @click="$_blank('/layout/goodsIndex/addGoods',{id:scope.row.product_id})">
                        {{ scope.row.title }}
                      </a>
                    </p>
                    <!--                    <p style="color: #a7a7a7;">-->
                    <!--                      规格: {{ scope.row.sku_title }}-->
                    <!--                    </p>-->
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column width="210">
              <template slot-scope="scope">
                <div class="comm-box" style="width: 85%;">
                  <p>{{ scope.row.type === 'meituan' ? '美团联盟' : scope.row.type === 'didi' ? '滴滴' : scope.row.type === 'eleme' ? '饿了么' : '美团分销联盟'  }}</p>
                  <p>支付金额: {{ scope.row.price | formatF2Y }}</p>
                </div>
              </template>
            </el-table-column>
            <el-table-column width="200">
              <template slot-scope="scope">
                <div >
                  <p class="title-3">
                    <el-button type="text" @click="toUserInfo(item.user.id)">
                      {{ scope.row.user.username }}({{ scope.row.user.id }})
                    </el-button>
                  </p>
                  <p class="title-3">{{ scope.row.user ? scope.row.user.nickname : '' }}</p>
                </div>
              </template>
            </el-table-column>
            <el-table-column width="230">
              <template slot-scope="scope">
                <div class="comm-box">
                  <p>预估佣金: {{ scope.row.commission_price  | formatF2Y }}</p>
                  <p>佣金比率: {{ scope.row.ratio }}%</p>
                </div>
              </template>
            </el-table-column>
            <el-table-column width="250">
              <template slot-scope="scope">
                <div class="comm-box" style="width:85%">
                  <p>分成基数:{{ scope.row.commission_price / 100}} </p>
                  <p>分成比例: {{ scope.row.user.level.jh_cps_ratio / 100 }} % </p>
                  <p>分成金额: {{ scope.row.commission_price/ 100 * scope.row.user.level.jh_cps_ratio / 10000}}</p>
                </div>
              </template>
            </el-table-column>
            <el-table-column width="200">
              <template slot-scope="scope">
                <div class="comm-box">
                  <p class="title-3">
                    {{
                      scope.row.status === 'completed' ?
                          '已完成' : scope.row.status === 'payed' ?
                              '付款成功' : scope.row.status === 'settled' ?
                                  '结算' : scope.row.status === 'refunded' ?
                                      '退款' : ''
                    }}
                  </p>
                </div>
              </template>
            </el-table-column>
            <el-table-column>
              <template slot-scope="scope">
                <div class="title-3">
                  <el-button type="primary"
                             @click="orderOperationDialog(item.id)">同步订单
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
        <div class="table-foot-box">
          <div class="f fac fjsb">
            <p>采购端会员id: {{ item.user_id }}</p>
            <!--            <div class="f fac ">-->
            <!--                            <a href="javascript:;" class="primary close-order"-->
            <!--                               @click="openDetail(item)">查看详情</a>-->
            <!--            </div>-->
          </div>
        </div>
      </div>
      <el-pagination background :current-page="page" :page-size="pageSize" :page-sizes="[10, 30, 50, 100]"
                     :style="{display: 'flex',justifyContent: 'flex-end',marginRight: '20px'}" :total="total"
                     @current-change="handleCurrentChange" @size-change="handleSizeChange"
                     layout="total, sizes, prev, pager, next, jumper"></el-pagination>
    </div>
  </m-card>
</template>
<script>
import {exportOrderList, getOrderList, setOrderDisplay,} from "@/api/pushAlliance";
const path = process.env.VUE_APP_BASE_API;
export default {
  name: "tikTokCPSOrderManage",
  data() {
    return {
      // 分页部分
      page: 1,
      pageSize: 10,
      total: 0,
      path: path,
      // 列表
      orderList: [],
      //时间处理
      formData: {
        activity_id: null,
        order_id: "",
        order_sn: "",
        status: '',
        third_user_id: '',
        application_id: '',
        user_id: null,
        username: "",
        start_at: "",
        end_at: "",
        alliance: '',
      },
      dateType: null, // 日期类型 1付款时间 2确认收货时间 3结算时间 4退款时间
      dateTypeOptios: [
        {label: "付款时间", value: 'pay_at'},
        {label: "完成时间", value: 'complete_at '},
        {label: "退款时间", value: 'refund_at'},
        {label: "结算时间", value: 'local_settle_at'},
      ],
      orderSupplierConditions: [
        {name: "支付完成", value: "payed"},
        {name: "订单完成", value: "completed"},
        {name: "退款", value: "refunded"},
        {name: "结算", value: "settled"},
      ],
      //联盟平台
      allianceConditions: [
        {name: "美团", value: "meituan"},
        {name: "滴滴", value: "didi"},
        {name: "饿了么", value: "eleme"},
      ],
      supplyOptions: [
        {name: "全部", value: ""},
        {name: "美团", value: "meituan"},
      ],
      time_type: "",
      dateList: [
        {name: "今", value: 0},
        {name: "昨", value: 1},
        {name: "近7天", value: 2},
        {name: "近30天", value: 3},
      ],
    }
  },
  mounted() {
    this.getOrderList()
  },
  methods: {
//获取列表
    getOrderList() {
      let data = {
        "page": this.page,
        "pageSize": this.pageSize,
        "activity_id": this.formData.activity_id,
         "alliance":this.formData.alliance,
        "order_sn": this.formData.order_sn,
        "status": this.formData.status,
        "username": this.formData.username,
        "application_id": this.formData.application_id,
        // "user_id":parseInt(this.formData.user_id),
        "third_user_id": this.formData.third_user_id,
        "start_at": this.formData.start_at,
        "end_at": this.formData.end_at,
        "time_type": this.dateType,
      }
      getOrderList(data).then(res => {
        if (res.code === 0) {
          this.orderList = res.data.list
          this.total = res.data.total;
        } else {
          this.orderList = []
          this.total = 0;
        }
      })
    },
    //查看会员信息
    toUserInfo(id) {
      this.$refs.UserDetail.isShow = true;
      this.$nextTick(() => {
        this.$refs.UserDetail.getUserDetail(id);
      });
    },
    //操作按钮
    orderOperationDialog(id) {
      this.$confirm('确认要同步订单?, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        setOrderDisplay({id: id}).then(res => {
          if (res.code == 0) {
            this.$message.success(res.msg);
          }
        })
      }).catch(() => {

      })
    },
    // //查看详情
    // openDetail(row){
    //  console.log(row)
    // },
    //搜索
    searchOrder() {
      this.page = 1
      this.getOrderList()
    },
    // 导出
    xportActivity() {
      console.log(this.path)
      let params = {
        "page": this.page,
        "pageSize": this.pageSize,
        "activity_id": this.formData.activity_id,
        "alliance":this.formData.alliance,
        "order_sn": this.formData.order_sn,
        "status": this.formData.status,
        "username": this.formData.username,
        "application_id": this.formData.application_id,
        // "user_id":parseInt(this.formData.user_id),
        "third_user_id": this.formData.third_user_id,
        "start_at": this.formData.start_at,
        "end_at": this.formData.end_at,
        "time_type": this.dateType,
      }
      exportOrderList(params).then(res => {
        if (res.code === 0) {
          console.log(res)
          window.open(this.path + '/' + res.data.link)
          // window.location.href = this.path + '/' + res.data.link
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    //重置
    clearSearchCondition() {
      this.$refs.ref.resetFields()
      this.formData.activity_id = null
      this.formData.order_id = ""
      this.formData.order_sn = ""
      this.formData.status = ""
      this.formData.third_user_id = ""
      this.formData.application_id = ""
      this.formData.user_id = null
      this.formData.username = ""
      this.formData.start_at = ""
      this.formData.end_at = ""
      this.formData.alliance = ""
      this.time_type = "";
      this.dateType = null;
      this.getOrderList()
    },
    // 切换日期
    handleDateTab(item) {
      this.time_type = this.time_type === item.value ? "" : item.value;
      const todayDate = new Date();
      switch (this.time_type) {
        case 0:
          const dateToday1 = new Date();
          dateToday1.setHours(0);
          dateToday1.setMinutes(0);
          dateToday1.setSeconds(0);
          this.formData.start_at = dateToday1;
          todayDate.setHours(23);
          todayDate.setMinutes(59);
          todayDate.setSeconds(59);
          this.formData.end_at = todayDate;
          break;
        case 1:
          const dateYesterday1 = new Date();
          dateYesterday1.setTime(dateYesterday1.getTime() - 3600 * 1000 * 24 * 1);
          dateYesterday1.setHours(0);
          dateYesterday1.setMinutes(0);
          dateYesterday1.setSeconds(0);
          this.formData.start_at = dateYesterday1;

          const dateYesterday2 = new Date();
          dateYesterday2.setTime(dateYesterday2.getTime() - 3600 * 1000 * 24 * 1);
          dateYesterday2.setHours(23);
          dateYesterday2.setMinutes(59);
          dateYesterday2.setSeconds(59);
          this.formData.end_at = dateYesterday2;
          break;
        case 2:
          const date7Day1 = new Date();
          date7Day1.setTime(date7Day1.getTime() - 3600 * 1000 * 24 * 7);
          date7Day1.setHours(0);
          date7Day1.setMinutes(0);
          date7Day1.setSeconds(0);
          this.formData.start_at = date7Day1;

          todayDate.setHours(23);
          todayDate.setMinutes(59);
          todayDate.setSeconds(59);
          this.formData.end_at = todayDate;
          break;
        case 3:
          const date30Day1 = new Date();
          date30Day1.setTime(date30Day1.getTime() - 3600 * 1000 * 24 * 30);
          date30Day1.setHours(0);
          date30Day1.setMinutes(0);
          date30Day1.setSeconds(0);
          this.formData.start_at = date30Day1;

          todayDate.setHours(23);
          todayDate.setMinutes(59);
          todayDate.setSeconds(59);
          this.formData.end_at = todayDate;
          break;

        default:
          break;
      }
    },
    // 下一页
    handleCurrentChange(page) {
      this.page = page
      this.getOrderList()
    },

    // 每页显示条数
    handleSizeChange(size) {
      this.pageSize = size
      this.getOrderList()
    },
    // 获取搜索条件
    getSearchCondition() {
      let data = {};
      switch (this.orderSearchConditionTag) {
        case 0:
          data.order_sn = this.orderSearchCondition;
          break;
        case 1:
          data.pay_sn = this.orderSearchCondition;
          break;
        case 2:
          data.shipping_sn = this.orderSearchCondition;
          break;
        case 3:
          data.user_id = this.orderSearchCondition;
          break;
        case 4:
          data.nick_name = this.orderSearchCondition;
          break;
        case 5:
          data.user_name = this.orderSearchCondition;
          break;
        case 6:
          data.user_mobile = this.orderSearchCondition;
          break;
        case 7:
          data.third_order_sn = this.orderSearchCondition;
          break;
        case 8:
          data.cloud_order_id = this.orderSearchCondition;
          break;
        case 9:
          data.supply_sn = this.orderSearchCondition;
          break;
        default:
          break;
      }

      if (this.orderSupplierConditionTag) {
        data.supplier_id = this.orderSupplierConditionTag;
      }
      if (this.sendCodeStatus !== '') {
        data.sendCodeStatus = this.sendCodeStatus;
      }
      if (this.writeStatus !== '') {
        data.writeStatus = this.writeStatus;
      }
      if (this.orderGoodsNameCondition !== '') {
        data.product_title = this.orderGoodsNameCondition;
      }

      if (this.orderPaymentTypeConditionTag !== 0) {
        data.pay_type_id = this.orderPaymentTypeConditionTag;
      }
      if (this.afterSaleStatus) {
        data.refund_status = this.afterSaleStatus;
      }
      if (this.orderStatusConditionsTag !== "-100") {
        data.status = this.orderStatusConditionsTag;
        this.orderStatus = this.orderStatusConditionsTag;
      }

      if (this.orderApplicationConditionsTag !== 0) {
        data.application_id = this.orderApplicationConditionsTag;
      }

      if (this.formData.d1 !== "" && this.formData.d1 !== undefined) {
        data.start_at = formatTimeToStr(this.formData.d1.getTime() / 1000, "yyyy-MM-dd hh:mm:ss");
      }

      if (this.formData.d2 !== "" && this.formData.d2 !== undefined) {
        data.end_at = formatTimeToStr(this.formData.d2.getTime() / 1000, "yyyy-MM-dd hh:mm:ss");
      }
      // if (this.gather_supplier_id) {
      //   data.gather_supplier_id = this.gather_supplier_id
      // }
      if (this.gather_supplier_status) {
        data.gather_supplier_status = this.gather_supplier_status === 1 ? 1 : "";
      }
      data.time_type = this.dateType
      data.page = this.page;
      data.pageSize = this.pageSize;

      return data;
    },
    objectSpanMethod({row, column, rowIndex, columnIndex}) {
      if (columnIndex >= 2) {
        if (rowIndex % 9999 === 0) {
          return {
            rowspan: 9999,
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    },
  }
}
</script>

<style lang="scss" scoped>
@import "@/style/base.scss";
// 搜索部分
.search-term {
  .dateBtnBox {
    height: 36px;
    line-height: 36px;

    span {
      height: 27px;
      line-height: 22px;
      display: inline-block;
      margin-right: 10px;
      padding: 2px 4px;
      border: 1px solid #dcdee0;
      color: #c8c9cc;
      cursor: pointer;
      box-sizing: border-box;

      &:last-child {
        margin-right: 0;
      }

      &:hover {
        color: #155bd4;
        border-color: #155bd4;
        background-color: #fff;
      }

      &.is_active {
        color: #155bd4;
        border-color: #155bd4;
        background-color: #fff;
      }
    }
  }
}

/* 订单列表部分开始 */
.table-title-box {
  p.title-p {
    margin-right: 20px;
  }

  span {
    font-size: 12px;
    color: #c1c1c1;
    margin-right: 10px;
  }
}

.table-box {
  margin-top: 25px;
  // 订单item
  /* .table-item {
      border: 1px solid #e8e8e8;
      border-radius: 5px;
      margin-bottom: 10px;
      // 表头
      .table-head {
          border-bottom: 1px solid #e8e8e8;
          background-color: #fafafa;
          color: #888787;
          font-weight: bold;
          padding: 10px 0;
          a.close-order {
              display: inline-block;
              margin-right: 20px;
          }
          div {
              p {
                  margin-left: 10px;
                  &.supplier-p {
                      background-color: rgb(74, 197, 156);
                      padding: 10px;
                      color: #ffffff;
                      border-radius: 3px;
                  }
              }
          }
      }
      .table-cont {
          .el-row {
              padding: 0;
              .el-col {
                  border-right: 1px solid #e8e8e8;
                  padding: 10px 0;
                  height: 120px;
                  &:last-child {
                      border-right: none;
                  }
                  &.goods-box {
                      img {
                          width: 100px;
                          height: 100px;
                          margin: 0 10px;
                      }
                  }
                  .comm-box {
                      p {
                          margin-bottom: 10px;
                          &:last-child {
                              margin-bottom: 0;
                          }
                      }
                  }
              }
          }
      }
      // 收货人信息
      .table-foot {
          border-top: 1px solid #e8e8e8;
          padding: 10px 0;
          p {
              margin-left: 10px;
              &.addr-p {
                  span {
                      margin-right: 5px;
                  }
              }
          }
      }
  } */
}

/* 订单列表部分结束 */
::v-deep .tabs-box {
  padding-left: 20px;
  padding-right: 20px;

  .el-radio-group {
    .el-radio {
      width: 95px;
      height: 37px;
      line-height: 37px;
      text-align: center;

      .el-radio__input {
        display: none;
      }

      .el-radio__label {
        padding: 0;
      }

      &.is-checked {
        background-color: #13c7a7;
        border-radius: 50px;

        .el-radio__label {
          color: white;
        }
      }
    }
  }
}

.search-box {
  // padding: 20px;
  ::v-deep .el-date-editor.el-range-editor.el-input__inner {
    padding: 0 10px;
  }
}

.green-color {
  color: #4ac59c;
}

.order-card {
  margin-left: 1px;
  border-top: 0;
}

/***************************** tabs部分 *******************************/
::v-deep .order-tabs {
  .el-tabs__header {
    margin-bottom: 0px;

    .el-tabs__item {
      background-color: #f7f8fa;

      &.is-active {
        color: #303133;
        background-color: #ffffff;
      }

      &:hover {
        color: #303133;
      }
    }
  }
}

/***************************** 表格部分 *******************************/
::v-deep .el-table.table-head {
  margin-bottom: 25px;

  &::before {
    display: none;
  }

  .el-table__header-wrapper {
    tr th {
      background-color: #f7f8fa !important;
      border-bottom: 0;
    }
  }

  .el-table__body-wrapper {
    display: none;
  }
}

::v-deep .el-table.table-cont.el-table--border {
  border: 1px solid #efefef !important;
}

::v-deep .el-table.table-cont {
  margin-bottom: 0;

  thead {
    tr th {
      background-color: #f7f8fa !important;
    }

    tr:last-child {
      display: none;
    }

    tr:first-child {
      th {
        p {
          margin-right: 20px;

          &.supplier-p {
            //background-color: rgb(74, 197, 156);
            padding: 10px;
            color: #0a3cdc;
            //border-radius: 3px;
          }
        }
      }
    }
  }

  .el-table__body-wrapper {
    .goods-box {
      p {
        margin-left: 10px;
      }
    }

    .comm-box {
      width: 50%;
      margin: 0 auto;

      p {
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.table-foot-box {
  border: 1px solid #ebeef5;
  border-top: 0;
  margin-bottom: 20px;
  padding: 10px;

  p {
    margin-left: 10px;

    &.addr-p {
      span {
        margin-right: 5px;
      }
    }

    &:first-child {
      margin-left: 0;
    }
  }
}

</style>