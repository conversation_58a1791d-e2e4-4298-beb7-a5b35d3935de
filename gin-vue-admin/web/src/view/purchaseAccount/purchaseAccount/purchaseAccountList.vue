<template>
  <m-card>
    <el-button type="primary" @click="openAdd">新增</el-button>
    <el-table :data="tableData" class="mt25">
      <el-table-column label="ID" align="center" prop="id"></el-table-column>
      <el-table-column label="账户名称" align="center" prop="account_name"></el-table-column>
      <el-table-column label="累计充值金额" align="center">
        <template slot-scope="scope">￥{{ scope.row.recharge_money | formatF2Y }}</template>
      </el-table-column>
      <el-table-column label="剩余金额" align="center">
        <template slot-scope="scope">￥{{ scope.row.remaining_amount | formatF2Y }}</template>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.account_status" :active-value="1" :inactive-value="2"
                     @change="handleChange(scope.row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="edit(scope.row)" style="padding: 0 !important;margin-left: 10px !important;">编辑</el-button>
          <el-button type="text" @click="jump('memberAccountList',scope.row.id)" style="padding: 0 !important;margin-left: 10px !important;">会员账户</el-button>
          <el-button type="text" @click="jump('detailRecordList',scope.row.id)" style="padding: 0 !important;margin-left: 10px !important;">明细记录</el-button>
        </template>
      </el-table-column>
    </el-table>
    <addPurchaseAccountDialog ref="addPurchaseAccountDialog" @reload="getTableData"></addPurchaseAccountDialog>
  </m-card>
</template>

<script>
import addPurchaseAccountDialog from "./components/addPurchaseAccountDialog";
import {getAccountList, updateAccount} from "@/api/purchase";
import infoList from "@/mixins/infoList";

export default {
  name: "purchaseAccountList",
  mixins: [infoList],
  components: {addPurchaseAccountDialog},
  data() {
    return {
      listApi: getAccountList
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    async handleChange(row) {
      const {code, msg} = await updateAccount(row)
      if (code === 0) {
        this.$message.success(msg)
      }
    },
    jump(name, id) {
      this.$router.push({
        name,
        query: {
          id
        }
      })
    },
    openAdd() {
      this.$refs.addPurchaseAccountDialog.init()
    },
    edit(row) {
      this.$refs.addPurchaseAccountDialog.init(row)
    }
  }
}
</script>

<style scoped>

</style>