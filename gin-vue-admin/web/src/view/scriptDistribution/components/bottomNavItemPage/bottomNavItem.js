import { getLinkList, createLink, deleteLink, findLink, updateLink, getLinkGroupOption } from "@/api/shopSetting"
// import {createCategory,scriptList,getCategory,scriptgetCategory,scriptUpdate,scriptDelete,scriptDeleteCategory,updateCategory,scriptCreate} from "@/api/scriptDistribution"
import {createCategory,scriptList,getCategory,scriptgetCategory,scriptUpdate,scriptDelete,getApplicationLevelList,getApplicationGroupList,scriptDeleteCategory,updateCategory,scriptCreate,getApplicationApplyList} from "@/api/scriptDistribution"
import BottomNavChildDialog from "../bttomNavChildDialog.vue"
import { Popconfirm } from 'element-ui';


export default {
    components: { BottomNavChildDialog },
    data() {
        return {
            tabsId: "",
            // 编辑一级导航dialog
            isShow: false,
            visible: false,
            group_id: 1,
            primaryType: "",
            formDatas: {
                id:null,
                title: "",
                type:'',
                status: 1,
                auth:[],
            },
            formDatas2: {
                id:null,
                title: "",
                type:'',
                status: 1,
                auth:[],
            },
            formData: {
                id: '',
                title: "",
                is_display: null,
                // material: "{'3232111'}"
            },
            authority:[
                {
                    name:'指定采购端会员',
                    value:1,
                },
                {
                    name:'指定采购端等级',
                    value:2,
                },
                {
                    name:'指定采购端分组',
                    value:3,
                },
                {
                    name:'无限制',
                    value:4,
                },
            ],
            optionList: [],
            //采购端会员列表
            segmentList:[],
            //采购端等级列表
            gradeList:[],
            //采购端分组列表
            groupingList:[],
            tableList: [],
            page: 1,
            pageSize: 10,
            total: 0,
            page1:1,
            pageSize1: 10,
            total1: 0,
            // 子导航抽屉显示
            childTableIsShow: false,
            // pid
            childPid: "",
            // 子导航table数据
            childTableList: [],
            childDrawerTitle: "",
            dataList: [],
            groupList: []
        };
    },
    filters: {
        formatStatus: function (status) {
            return status === 1 ? '显示' : '隐藏'
        },
    },
    mounted() {
        this.getApplicationApplyListdata()
        this.gradeListListdata()
        this.getApplicationGroupListData()
    },
    methods: {
        addFirsttype(){
            this.isShow = true
            this.formDatas2.auth = []
            this.formDatas2.type = ''
        },
        authorityChange(val){
            this.formDatas2.auth = []
        },
        //获取采购段会员列表
        getApplicationApplyListdata(){
            getApplicationApplyList().then((res) => {
                if(res.code == 0){
                    console.log(res)
                    this.segmentList = res.data.list
                }
            })
        },
        //获取采购端等级
        gradeListListdata(){
            getApplicationLevelList().then((res) => {
                if(res.code == 0){
                    console.log(res)
                    this.gradeList = res.data.list
                }
            })
        },
        //获取采购端分组
        getApplicationGroupListData(){
            getApplicationGroupList().then((res) => {
                if(res.code == 0){
                    console.log(res)
                    this.groupingList = res.data.list
                }
            })
        },
        // 关闭一级导航编辑dialog
        handleClose() {
            this.$refs.editForm.resetFields();
            this.isShow = false
            this.primaryType = ""
            this.formDatas2 =  {
                id:null,
                title: "",
                type:'',
                status: 1,
                auth:[],
            }
        },
        // 打开一级导航编辑dialog
        openDialog(id) {
            console.log(id)
            console.log(this.tableList)
            this.tableList.forEach((item) => {
              if(item.id === id){
                  // this.$set(this.formDatas,'title',item.title)
                  // this.$set(this.formDatas,'type',item.type)
                  // this.$set(this.formDatas,'auth',item.auth)
                  this.formDatas.pid = item.pid;
                  this.formDatas.title = item.title;
                  this.formDatas.type = item.type;
                  this.formDatas.auth = item.auth;
                  let cc =  this.formDatas.auth.slice(1,-1)
                  let bb = cc.split(',')
                  let dd = []
                  bb.forEach(item => {
                      dd.push(Number(item))
                  })
                  this.formDatas.auth = dd
                  this.formDatas.id = item.id
              }
            })
        },
        // 保存修改一级导航
        saveForm() {
            console.log(this.formDatas.id)
            if (!this.formDatas.title) {
                this.$message.error("请填写一级分类名称!");
                return;
            }
            this.formDatas.auth = '[' + this.formDatas.auth.toString() + ']'
            updateCategory(this.formDatas).then(res => {
                if (res.code === 0) {
                    this.$message.success("修改成功");
                    // this.openDialog(this.formDatas.id)
                    // this.tabsId = 0
                    // this.handleClose();
                    this.fetch();
                    this.handleTabsChange(this.tabsId)
                } else {
                    this.$message.error("修改失败");
                }
            })
        },
        // 打开二级导航列表
        openChildDrawer(row) {
            this.childTableIsShow = true;
            this.childDrawerTitle = row.title;
            this.childPid = row.id;
            this.fetch(row.id);
        },
        // 删除
        deleteGroupDialog(id) {
            this.$confirm('确定要删除吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            }).then(() => {
                this.del(id, true);
            }).catch(() => { });
        },

        del(id, flg = false) {
            scriptDeleteCategory({ id: id }).then(res => {
                if (res.code === 0) {
                    this.$message.success("删除成功")
                    if (flg) {
                        let page = {
                            page: this.page, pageSize: this.pageSize
                        }
                        scriptgetCategory({...page }).then((res) => {
                            if (res.code === 0) {
                                this.total = res.data.total
                                this.tableList = res.data.list
                                if (this.tableList.length > 0) {
                                    this.tabsId = this.tableList[0].id
                                    this.openDialog(this.tabsId)
                                    this.fetch(this.tableList[0].id)
                                }
                            }
                        })
                    } else {
                        this.fetch(this.group_id)
                    }
                } else {
                    this.$message.error("删除失败")
                }
            })
        },

        //删除
        deleteDialog(row) {
            this.$confirm('确定要删除吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            }).then(() => {
                scriptDeleteCategory({ id: row.id }).then(res => {
                    if (res.code === 0) {
                        this.$message.success("删除成功")
                        this.fetch(row.pid)
                    } else {
                        this.$message.error("删除失败")
                    }
                })
            }).catch(() => { });
        },

        handleCurrentChange(page) {
            this.page  = page;
            this.fetch();
        },
        handleCurrentChange1(page) {
            this.page1 = page;
            this.fetch();
        },
        handleSizeChange(size) {
            this.pageSize = size;
            this.fetch();
        },
        handleSizeChange1(size) {
            this.pageSize1 = size;
            this.fetch();
        },
        // 确定新增
        saveOneNav() {
            if (!this.primaryType) {
                this.$message.error("请填写一级分类名称")
                return
            }
            let params = JSON.parse(JSON.stringify(this.formDatas2))
            this.formDatas2.auth.toString()
            this.formDatas2.auth = '[' + this.formDatas2.auth.toString() + ']'
            let para = {
                pid: 0,
                title: this.primaryType,
                type:this.formDatas2.type,
                auth: this.formDatas2.auth
            }
            createCategory(para).then(res => {
                if (res.code === 0) {
                    this.$message.success("新增成功");
                    this.closeAddPop();
                    this.fetch()
                    console.log(this.tableList[0])
                    console.log(this.tabsId)
                } else {
                    this.$message.error("新增失败");
                }
            })
        },
        // 打开添加子导航
        openNavChild(type, row = {}) {
            this.$refs.bottomNavChildDialog.dialogVisible = true
            this.$nextTick(() => {
                if (type === "add") {
                    if (this.$refs.bottomNavChildDialog.formData.id) {
                        delete this.$refs.bottomNavChildDialog.formData.id
                    }
                    this.$refs.bottomNavChildDialog.title = "添加"
                    this.$refs.bottomNavChildDialog.formData.pid = this.tabsId
                    this.$refs.bottomNavChildDialog.formData.group_id = 1
                } else if (type === 'edit') {
                    this.$refs.bottomNavChildDialog.title = "编辑"
                    this.$refs.bottomNavChildDialog.getInfo(row)
                }
                this.$refs.bottomNavChildDialog.getOption()
            })

        },
        // 关闭一级新增框
        closeAddPop() {
            this.isShow = false
            this.primaryType = ""
            this.formDatas2 =  {
                id:null,
                title: "",
                type:'',
                status: 1,
                auth:[],
            }
        },
        handleTabsChange(val) {
            console.log(val)
            console.log(this.tabsId)
            // this.page = 1
            this.openDialog(val)
            this.fetch(val)
        },
        // 获取列表
        fetch(pid = 0) {
            let page = {}
            // if (pid === 0) {
                page = {
                    page: this.page, pageSize: this.pageSize
                }
            // }
            scriptgetCategory({ pid: pid, ...page }).then(res => {
                if (res.code === 0) {
                    if (pid === 0) {
                        this.total = res.data.total
                        this.tableList = res.data.list
                        if (!this.tabsId) {
                            this.tabsId = this.tableList[0].id
                            this.openDialog(this.tabsId)
                        }else {
                            this.openDialog(this.tabsId)
                        }
                        // this.tabsId = this.tableList[0].id

                        // this.fetch(this.tableList[0].id)
                    } else {
                        this.total1 = res.data.total
                        this.childTableList = res.data.list
                        console.log( this.childTableList)
                    }
                } else {
                    if (pid === 0) {
                        this.total = 0
                        this.tableList = []
                    } else {
                        this.total1 = 0
                        this.childTableList = []
                    }
                }
            })
        }
    },
};