<div>
    <el-dialog :title="title" :visible="dialogVisible" width="30%" :before-close="handleClose">
        <el-form :model="formData" ref="form" label-width="130px">
            <el-form-item label="二级级分类名称:"  prop="title">
                <el-input v-model="formData.title" placeholder="请输入二级分类名称"></el-input>
            </el-form-item>
<!--            <el-form-item label="权限设置:"  prop="title">-->
<!--                <el-select  v-model="formData.type"  @change="authorityChange"  clearable placeholder="请选择采购端分组">-->
<!--                    <el-option-->
<!--                            v-for="item in authority"-->
<!--                            :key="item.id"-->
<!--                            :label="item.name"-->
<!--                            :value="item.value"-->
<!--                    ></el-option>-->
<!--                </el-select>-->
<!--            </el-form-item>-->
<!--            <el-form-item label="选择采购端会员:"  prop="title" v-if="formData.type === 1">-->
<!--                <el-select  v-model="formData.auth"  multiple filterable clearable placeholder="请选择">-->
<!--                    <el-option-->
<!--                            v-for="item in segmentList"-->
<!--                            :key="item.user.id"-->
<!--                            :label="item.user.nickname  + '/' + item.user.username"-->
<!--                            :value="item.user.id"-->
<!--                    ></el-option>-->
<!--                </el-select>-->

<!--            </el-form-item>-->
<!--            <el-form-item label="选择采购端等级:"  prop="title" v-if="formData.type === 2">-->
<!--                <el-select  v-model="formData.auth"  multiple filterable clearable placeholder="请选择">-->
<!--                    <el-option-->
<!--                            v-for="item in gradeList"-->
<!--                            :key="item.id"-->
<!--                            :label="item.levelName"-->
<!--                            :value="item.id"-->
<!--                    ></el-option>-->
<!--                </el-select>-->
<!--            </el-form-item>-->
<!--            <el-form-item label="选择采购端分组:"  prop="title" v-if="formData.type === 3">-->
<!--                <el-select  v-model="formData.auth"  multiple filterable clearable placeholder="请选择">-->
<!--                    <el-option-->
<!--                            v-for="item in groupingList"-->
<!--                            :key="item.id"-->
<!--                            :label="item.name"-->
<!--                            :value="item.id"-->
<!--                    ></el-option>-->
<!--                </el-select>-->
<!--            </el-form-item>-->
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="save">确定</el-button>
            <el-button @click="handleClose">取消</el-button>
        </span>
    </el-dialog>
</div>