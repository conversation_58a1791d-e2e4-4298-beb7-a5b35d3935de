import {
    getLinkList,
    createLink,
    deleteLink,
    findLink,
    updateLink,
    getLinkGroupOption,
} from "@/api/shopSetting";
import BottomNavChildDialog from "../bttomNavChildDialog";

export default {
    components: { BottomNavChildDialog },
    data() {
        return {
            visible: false,
            newTitle: "",
            formData: {
                group_id: 4,
                pid: 0,
                title: "",
            },
            tabsId: 0,
            tabsList: [],
            tableList: [],
            page: 1,
            pageSize: 10,
            total: 0,
        };
    },
    filters: {
        formatStatus: function (status) {
            return status === 1 ? "显示" : "隐藏";
        },
    },
    mounted() {
        /* updateLink({pid:0,group_id:4,id:23,title:"最新公告1"}).then(res=>{

        }) */
    },
    methods: {
        closeAddPop() {
            this.visible = false
            this.newTitle = ""
        },
        // 确定新增
        saveOneNav() {
            if (!this.newTitle) {
                this.$message.error("请填写公告名称")
                return
            }
            let para = {
                group_id: 4,
                pid: 0,
                title: this.newTitle
            }
            createLink(para).then(res => {
                if (res.code === 0) {
                    this.$message.success("新增成功");
                    this.closeAddPop();
                    this.fetch();
                } else {
                    this.$message.error("新增失败");
                }
            })
        },
        saveForm() {
            if (!this.formData.title) {
                this.$message.error("请填写公告名称!");
                return;
            }
            updateLink(this.formData).then(res => {
                if (res.code === 0) {
                    this.$message.success("修改成功");
                    this.handleClose();
                    // this.fetch(this.tabsId);
                    this.fetch();
                } else {
                    this.$message.error("修改失败");
                }
            })
        },
        handleClose() {
            this.$refs.editForm.resetFields();
        },
        // 删除
        deleteGroupDialog(id) {
            this.$confirm('确定要删除吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            }).then(() => {
                this.del(id, true);
            }).catch(() => { });
        },
        // 打开添加子导航
        openNavChild(type, row = {}) {
            this.$refs.bottomNavChildDialog.dialogVisible = true;
            this.$nextTick(() => {
                if (type === "add") {
                    if (this.$refs.bottomNavChildDialog.formData.id) {
                        delete this.$refs.bottomNavChildDialog.formData.id;
                    }
                    this.$refs.bottomNavChildDialog.title = "添加";
                    this.$refs.bottomNavChildDialog.formData.pid = this.tabsId;
                    this.$refs.bottomNavChildDialog.formData.group_id = 4;
                } else if (type === "edit") {
                    this.$refs.bottomNavChildDialog.title = "编辑";
                    this.$refs.bottomNavChildDialog.getInfo(row.id);
                }
                this.$refs.bottomNavChildDialog.getOption();
            });
        },
        // 删除
        deleteLinkDialog(id) {
            this.$confirm('确定要删除吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            }).then(() => {
                this.del(id);
            }).catch(() => { });
        },

        del(id, flg = false) {
            deleteLink({ id: id }).then((res) => {
                if (res.code === 0) {
                    this.$message.success("删除成功");
                    // this.fetch(this.tabsId);
                    if (flg) {
                        let page = {
                            page: this.page, pageSize: this.pageSize
                        }

                        getLinkList({ pid: 0, group_id: 4, ...page }).then(res => {
                            if (res.code === 0) {
                                this.total = res.data.total
                                this.tabsList = res.data.list
                                if (this.tabsList.length > 0) {
                                    this.tabsId = this.tabsList[0].id
                                    this.openDialog(this.tabsId)
                                    this.fetch(this.tabsList[0].id)
                                }
                            }
                        })
                    } else {
                        this.fetch(this.group_id)
                    }
                } else {
                    this.$message.error("删除失败");
                }
            });
        },
        handleTabsChange(val) {
            this.page = 1;
            this.openDialog(val);
            this.fetch(val);
        },
        // 打开一级导航编辑dialog
        openDialog(id) {
            findLink({ id: id }).then(res => {
                if (res.code === 0) {
                    let data = res.data.rehotSearch;
                    this.formData.id = data.id;
                    this.formData.pid = data.pid;
                    this.formData.title = data.title;
                    // this.formData.group_id = data.group_id
                }
            })
        },
        fetch(pid = 0) {
            let page = {};
            if (pid === 0) {
                page = {
                    page: this.page,
                    pageSize: this.pageSize,
                };
            }
            getLinkList({ pid: pid, group_id: 4, ...page }).then((res) => {
                if (res.code === 0) {
                    if (pid === 0) {
                        this.total = res.data.total;
                        this.tabsList = res.data.list;
                        if (!this.tabsId) {
                            this.tabsId = this.tabsList[0].id
                            this.openDialog(this.tabsId)
                        }
                        this.fetch(this.tabsList[0].id);
                    } else {
                        this.total = res.data.total;
                        this.tableList = res.data.list;
                    }
                } else {
                    if (pid === 0) {
                        this.total = 0;
                        this.tabsList = [];
                    } else {
                        this.tableList = [];
                    }
                }
            });
        },
        handleCurrentChange(page) {
            this.page = page;
            this.fetch(this.tabsId);
        },
        handleSizeChange(size) {
            this.pageSize = size;
            this.fetch(this.tabsId);
        },
    },
};