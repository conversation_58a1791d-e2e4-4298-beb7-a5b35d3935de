<template>
  <el-drawer :title="`${formData.id ? '编辑' : '新增'}移动端首页列表`" :visible="isShow" :close-on-press-escape="false"
             :wrapperClosable="false"
             :before-close="handleClose" size="calc(100% - 220px)" class="detail-ct">
    <el-row>
      <el-form :model="formData" label-width="90px" :rules="rules" ref="form">
        <el-col :span="13">
          <el-form-item label="排序:" prop="sort">
            <m-num-input v-model="formData.sort" placeholder="请输入排序"></m-num-input>
          </el-form-item>
        </el-col>
        <el-col :span="13">
          <el-form-item label="显示名称:" prop="title">
            <el-input v-model="formData.title" placeholder="请输入显示名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="13">
          <el-form-item label="商品数据:" prop="type">
            <el-radio-group v-model="formData.type">
              <el-radio :label="0">全部商品</el-radio>
              <el-radio :label="1">指定分类</el-radio>
              <el-radio :label="2">营销属性</el-radio>
              <el-radio :label="3">指定专辑</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="13" v-if="formData.type === 1">
          <div class="f fac" style="padding-left: 90px;">
            <el-form-item label-width="0" prop="category1_id" class="f1">
              <el-select v-model="formData.category1_id" class="w100" placeholder="请选择一级分类"
                         @change="getCategory(2,formData.category1_id)" clearable>
                <el-option v-for="item in category1List" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <div style="width: 10px;"></div>
            <el-form-item label-width="0" prop="category2_id" class="f1">
              <el-select v-model="formData.category2_id" class="w100" placeholder="请选择二级分类"
                         @change="getCategory(3,formData.category2_id)" clearable>
                <el-option v-for="item in category2List" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <div style="width: 10px;"></div>
            <el-form-item label-width="0" prop="category2_id" class="f1">
              <el-select v-model="formData.category3_id" class="w100" placeholder="请选择三级分类" clearable
                         @change="getCategory(4,formData.category3_id)">
                <el-option v-for="item in category3List" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </div>
        </el-col>
        <el-col :span="13" v-if="formData.type === 2">
          <el-form-item>
            <el-radio-group v-model="formData.marketing">
              <el-radio :label="0">热卖</el-radio>
              <el-radio :label="1">促销</el-radio>
              <el-radio :label="2">新品</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="13" v-if="formData.type === 3">
          <el-form-item prop="collection_id">
            <el-select v-model="formData.collection_id" class="f1" placeholder="请选择专辑" clearable>
              <el-option v-for="item in collectionList" :key="item.id" :label="item.title" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="13">
          <el-form-item>
            <el-button type="primary" @click="confirm">确 定</el-button>
            <el-button @click="handleClose">取 消</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </el-drawer>
</template>

<script>
import {getCategoryListWithParentId} from "@/api/category";
import {getCollectionList} from "@/api/collection";
import {createWapHomeSetting, updateWapHomeSetting} from "@/api/shopSetting";
import {isNumber} from "@antv/util";

export default {
  name: "mobileHomeListDrawer",
  data() {
    return {
      isShow: false,
      formData: {
        sort: 0,
        title: "",
        status: 0,//开启状态0开启 1关闭
        type: 0,//0全部商品，1指定分类，2营销属性，3指定专辑
        category1_id: null,
        category2_id: null,
        category3_id: null,
        marketing: 0,//营销属性0热卖1促销2新品 默认热卖
        collection_id: null, // 专辑id
      },
      category1List: [],
      category2List: [],
      category3List: [],
      collectionList: [],
      rules: {
        sort: {required: true, message: "请输入排序", trigger: "blur"},
        title: {required: true, message: "请输入显示名称", trigger: "blur"},
        type: {required: true, message: "请选择商品数据", trigger: "blur"},
      }
    }
  },
  methods: {
    init(row) {
      row.category1_id = row.category1_id ? row.category1_id : null;
      row.category2_id = row.category2_id ? row.category2_id : null;
      row.category3_id = row.category3_id ? row.category3_id : null;
      row.collection_id = row.collection_id ? row.collection_id : null;
      this.formData = this.$fn.deepClone(row)
      this.initCategory(row.category1_id, row.category2_id, row.category3_id)
    },
    async initCategory(c1, c2, c3) {
      let {data} = await getCategoryListWithParentId()
      this.category1List = data.list
      if (c2) {
        let {data} = await getCategoryListWithParentId({parent_id: c1})
        this.category2List = data.list
      }
      if (c3) {
        let {data} = await getCategoryListWithParentId({parent_id: c2})
        this.category3List = data.list
      }
    },
    // 提交
    confirm() {
      this.$refs.form.validate((valid) => {
        if (!valid) return false
        switch (this.formData.type) {
          case 1: // 指定分类
            if (!this.formData.category1_id && !this.formData.category2_id && !this.formData.category3_id) {
              this.$message.error("分类至少选择一级")
              return false;
            }
            break;
          case 3: // 指定专辑
            if (!this.formData.collection_id) {
              this.$message.error("请选择专辑")
              return false;
            }
            break;
        }
        if (this.formData.id) {
          updateWapHomeSetting(this.formData).then(({code, msg}) => {
            if (code === 0) {
              this.$message.success(msg)
              this.handleClose()
              this.$emit("reload")
            }
          })
        } else {
          createWapHomeSetting(this.formData).then(({code, msg}) => {
            if (code === 0) {
              this.$message.success(msg)
              this.handleClose()
              this.$emit("reload")
            }
          })
        }
      })
    },
    // 关闭抽屉
    handleClose() {
      try {
        this.$refs.form.resetFields()
      } catch {
      } finally {
        this.isShow = false
        this.formData = {
          sort: 0,
          title: "",
          status: 0,//开启状态0开启 1关闭
          type: 0,//0全部商品，1指定分类，2营销属性，3指定专辑
          category1_id: null,
          category2_id: null,
          category3_id: null,
          marketing: 0,//营销属性0热卖1促销2新品 默认热卖
          collection_id: null, // 专辑id
        }
        this.category1List = []
        this.category2List = []
        this.category3List = []
        this.collectionList = []
      }
    },
    // 获取分类
    async getCategory(level = 1, pid = 0) {
      if (isNumber(pid)) {
        const {data} = await getCategoryListWithParentId({parent_id: pid})
        switch (level) {
          case 1:
            this.category1List = []
            this.formData.category1_id = null
            this.category2List = []
            this.formData.category2_id = null
            this.category3List = []
            this.formData.category3_id = null
            this.category1List = data.list
            break;
          case 2:
            this.category2List = []
            this.formData.category2_id = null
            this.category3List = []
            this.formData.category3_id = null
            this.category2List = data.list
            break;
          case 3:
            this.category3List = []
            this.formData.category3_id = null
            this.category3List = data.list
            break;
        }
      } else {
        level = level - 1
        switch (level) {
          case 1:
            this.formData.category1_id = null
            this.category2List = []
            this.formData.category2_id = null
            this.category3List = []
            this.formData.category3_id = null
            break;
          case 2:
            this.formData.category2_id = null
            this.category3List = []
            this.formData.category3_id = null
            break;
          case 3:
            this.formData.category3_id = null
            break;
        }
      }
    },
    // 获取专辑
    async getAlbum() {
      let {code, data} = await getCollectionList()
      if (code === 0) {
        this.collectionList = data.list
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-drawer {
  .el-drawer__body {
    background-color: #fff !important;
  }
}
</style>