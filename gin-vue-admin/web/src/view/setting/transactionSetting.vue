<template>
  <m-card>
    <el-tabs v-model="setName" type="card" @tab-click="handleClick">
      <el-tab-pane label="基础设置" name="1">
        <el-row>
          <el-form :model="formData" label-width="160px">
            <el-col :span="13">
              <el-form-item label="自动关闭未支付订单:">
                <el-input-number :controls="false" :precision="0" :min="0" class="number-text-left"
                  v-model="formData.order_close_time" placeholder="请输入">
                </el-input-number>
                天
              </el-form-item>
            </el-col>
            <el-col :span="13">
              <el-form-item label="订单自动收货时间:">
                <el-input-number :controls="false" :precision="0" :min="0" class="number-text-left"
                  v-model="formData.order_receive_time" placeholder="请输入">
                </el-input-number>
                天
              </el-form-item>
            </el-col>
            <!--        <el-col :span="13">
                <el-form-item label="发票设置:">
                  <el-checkbox-group v-model="invoiceTypes">
                    <el-checkbox label="1">电子普通发票</el-checkbox>
                    <el-checkbox label="2">增值税专用发票</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>-->
            <el-col :span="13">
              <el-form-item>
                <el-button type="primary" @click="save">保存</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="支付设置" name="2">
        <SimultaneousAssignmentPayment />
      </el-tab-pane>
    </el-tabs>

  </m-card>
</template>
<script>
import SimultaneousAssignmentPayment from "@/view/supply/page/components/simultaneousAssignmentPayment.vue";
// *100
import { updateTradeSetting, findSetting } from "@/api/transactionSetting.js";
export default {
  name: "transactionSetting",
  components: { SimultaneousAssignmentPayment },
  data() {
    return {
      formData: {
        server_radio: 0,
        order_close_time: 0,
        order_receive_time: 0,
        // order_bill_type: '',
      },
      // invoiceTypes: [],
      submit: {
        value: {},
        key: "trade_setting",
      },
      setName: '1',

    };
  },
  mounted() {
    this.find();
  },
  methods: {
    handleClick(){

    },
    find() {
      findSetting().then((res) => {
        if (res.code === 0) {
          //this.formData.id = res.data.setting.id;
          //this.server_radio = res.data.setting.value.server_radio;

          this.formData = res.data.setting.value;
          this.formData.server_radio = this.formData.server_radio / 100;
          this.id = res.data.setting.id;
          // this.invoiceTypes = this.formData.order_bill_type.split(",");
        }
      });
    },
    save() {
      //this.formData.server_radio = this.server_radio;
      //this.formData.server_radio = this.formData.server_radio * 100;

      console.log(this.formData);

      //this.formData.server_radio = this.formData.server_radio * 100;
      let n = 0;
      n = this.formData.server_radio;
      n = n * 100;
      console.log(n, '???');

      this.submit.id = this.id;
      this.submit.value.server_radio = n;
      this.submit.value.order_close_time = this.formData.order_close_time;
      this.submit.value.order_receive_time = this.formData.order_receive_time;
      // this.submit.value.order_bill_type = this.invoiceTypes.join(',');
      updateTradeSetting(this.submit).then((res) => {
        if (res.code === 0) {
          this.$message.success(res.msg);
        } else {
          this.$message.error(res.msg);
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
p.title-p {
  font-size: 16px;
  font-weight: bold;
}
</style>