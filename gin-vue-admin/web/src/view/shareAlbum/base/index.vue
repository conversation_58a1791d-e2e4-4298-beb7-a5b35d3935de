<template>
  <el-tabs v-model="activeName" type="card">
    <el-tab-pane label="基础设置" name="baseSetting">
      <base-setting ref="baseSetting"></base-setting>
    </el-tab-pane>
  </el-tabs>
</template>
<script>
import BaseSetting from "./components/baseSetting";

export default {
  name: "shareAlbumBaseIndex",
  components: {BaseSetting},
  data() {
    return {
      activeName: "baseSetting"
    }
  },
  mounted() {
    this.$refs.baseSetting.init()
  },
  methods: {}
}
</script>
<style scoped lang="scss">

</style>
