<el-dialog :title="title" :visible="isShow" width="60%" :before-close="handleClose">
    <el-form :model="formData" ref="form" :rules="rules" label-width="90px">
        <el-form-item label="标题:" prop="title">
            <el-input v-model="formData.title" class="w300" placeholder="请输入文章标题"></el-input>
        </el-form-item>
        <el-form-item label="作者:" prop="author">
            <el-input v-model="formData.author" class="w300" placeholder="请输入作者"></el-input>
        </el-form-item>
        <el-form-item label="文章分类:">
            <el-select class="w180" @change="handleClassifyChange(2,formData.article_category1_id)" v-model="formData.article_category1_id" clearable placeholder="请选择一级分类">
                <el-option
                    :label="item.title"
                    :value="item.id"
                    v-for="item in categoryOptions1"
                    :key="item.id"
                ></el-option>
            </el-select>
            <el-select class="ml10 w180" v-model="formData.article_category2_id" clearable placeholder="请选择二级分类">
                <el-option
                    :label="item.title"
                    :value="item.id"
                    v-for="item in categoryOptions2"
                    :key="item.id"
                ></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="状态:" prop="status">
            <!-- <el-switch v-model="formData.status" active-color="#44D2B9"></el-switch> -->
            <el-radio-group v-model="formData.status">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="0">关闭</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="内容:" prop="content">
            <quill-editor v-if="isShow" ref="myTextEditor" v-model="formData.content" :options="editorOption"
                style="height: 400px; width: 100%; padding-bottom: 100px"></quill-editor>
            <p class="color-grap">图片最大支持10M</p>
        </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm">确 定</el-button>
        <el-button @click="handleClose">取 消</el-button>
    </span>
</el-dialog>