<template>
  <div>
    <el-dialog
        :title="title"
        :visible="dialogVisible"
        width="800px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="handleClose"
    >
      <el-form :model="formData" ref="form" label-width="130px">
        <el-form-item prop="sort">
          <span slot="label"><span class="color-red">*</span> 排序:</span>
          <el-input
              v-model="formData.sort"
              placeholder="请输入导航名称"
          ></el-input>
        </el-form-item>
        <el-form-item prop="title">
          <span slot="label"><span class="color-red">*</span> 名称:</span>
          <el-input
              v-model="formData.title"
              placeholder="请输入导航名称"
          ></el-input>
        </el-form-item>
        <el-form-item prop="url">
          <span slot="label">PC跳转链接:</span>
          <el-input
              v-model="formData.url"
              placeholder="请输入PC跳转链接"
          ></el-input>
          <p class="color-grap">请填写完整链接,如: http://www.xxxx.com</p>
        </el-form-item>
        <!--产品说先都是使用一个-->
        <el-form-item prop="h5_url">
          <span slot="label"> H5跳转链接:</span>
          <el-input
              v-model="formData.h5_url"
              placeholder="请输入H5跳转链接"
          ></el-input>
          <p class="color-grap">请填写完整链接,如: http://www.xxxx.com</p>
        </el-form-item>
        <el-form-item prop="mini_url">
          <span slot="label">小程序跳转链接:</span>
            <el-radio-group v-model="formData.mini_url_type" class="mr10"
                            @change="handleJumpTypeChange('mini',formData.mini_url_type)">
              <el-radio :label="0">文章</el-radio>
              <el-radio :label="1">商品专辑</el-radio>
              <el-radio :label="2">内部链接</el-radio>
              <el-radio :label="3">第三方小程序链接</el-radio>
            </el-radio-group>
            <el-select v-model="formData.mini_collection_id" v-if="formData.mini_url_type === 0">
              <el-option v-for="item in miniOptions" :key="item.id" :label="item.title" :value="item.id"></el-option>
            </el-select>
          <el-select v-model="formData.mini_collection_id" v-if="formData.mini_url_type === 1">
            <el-option v-for="item in appOptions" :key="item.id" :label="item.title" :value="item.id"></el-option>
          </el-select>
          <el-input v-if="formData.mini_url_type === 2" v-model="formData.mini_url" placeholder="请输入" clearable></el-input>
          <span class="nblinlStyle"  v-if="formData.mini_url_type=== 2">内部链接为跳转当前小程序的路径，请先配置好小程序，以/开头！</span>
          <el-input v-if="formData.mini_url_type === 3" v-model="formData.mini_id" placeholder="请填写跳转小程序的 APPID" clearable></el-input>
          <el-input style="margin-top: 10px;" v-if="formData.mini_url_type === 3" v-model="formData.mini_url" placeholder="请输入跳转路径" clearable></el-input>
          <span class="applinlStyle"  v-if="formData.mini_url_type === 3">跳转第三方小程序，填写第三方小程序的 appid 和路径！</span>
<!--            <el-input-->
<!--                class="f1"-->
<!--                v-model="formData.mini_url"-->
<!--                disabled-->
<!--                placeholder="小程序跳转链接"-->
<!--            ></el-input>-->
        </el-form-item>
        <el-form-item prop="app_url">
          <span slot="label">APP跳转链接:</span>
<!--          <div class="f fac">-->
<!--            <el-radio-group v-model="formData.app_url_type" class="mr10"-->
<!--                            @change="handleJumpTypeChange('app',formData.app_url_type)">-->
<!--              <el-radio :label="0">文章</el-radio>-->
<!--              <el-radio :label="1">专辑</el-radio>-->
<!--            </el-radio-group>-->
<!--            <el-select v-model="formData.app_collection_id" class="mr10">-->
<!--              <el-option v-for="item in appOptions" :key="item.id" :label="item.title" :value="item.id"></el-option>-->
<!--            </el-select>-->
<!--            <el-input-->
<!--                class="f1"-->
<!--                v-model="formData.app_url"-->
<!--                disabled-->
<!--                placeholder="APP跳转链接"-->
<!--            ></el-input>-->
<!--          </div>-->
          <el-radio-group v-model="formData.app_url_type" class="mr10"
                          @change="handleJumpTypeChange('app',formData.app_url_type)">
            <el-radio :label="0">文章</el-radio>
            <el-radio :label="1">商品专辑</el-radio>
            <el-radio :label="2">内部链接</el-radio>
            <el-radio :label="3">微信小程序链接</el-radio>
          </el-radio-group>
          <el-select v-model="formData.app_collection_id" v-if="formData.app_url_type === 0">
            <el-option v-for="item in miniOptions" :key="item.id" :label="item.title" :value="item.id"></el-option>
          </el-select>
          <el-select v-model="formData.app_collection_id" v-if="formData.app_url_type === 1">
            <el-option v-for="item in appOptions" :key="item.id" :label="item.title" :value="item.id"></el-option>
          </el-select>
          <el-input v-if="formData.app_url_type === 2" v-model="formData.app_url" placeholder="请输入" clearable></el-input>
          <span class="nblinlStyle"  v-if="formData.app_url_type=== 2">APP内部路径！</span>
          <el-input v-if="formData.app_url_type === 3" v-model="formData.app_id" placeholder="请输入小程序 gh_开头的原始 id" clearable></el-input>
          <el-input style="margin-top: 10px;" v-if="formData.app_url_type === 3" v-model="formData.app_url" placeholder="请输入跳转路径" clearable></el-input>
          <span class="applinlStyle"  v-if="formData.app_url_type === 3">APP跳转小程序使用的是原始 ID！</span>
        </el-form-item>
        <el-form-item prop="pc_show">
          <span slot="label">PC端显示:</span>
          <el-switch v-model="formData.pc_show" :active-value="1" :inactive-value="0">
          </el-switch>
        </el-form-item>
        <el-form-item prop="app_show">
          <span slot="label">移动端显示:</span>
          <el-switch v-model="formData.app_show" :active-value="1" :inactive-value="0">
          </el-switch>
        </el-form-item>
       <!-- <p class="color-red">注: 链接会在对应端显示,如果链接全部为空则所有端都不会显示</p> -->
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="save">保存</el-button>
        <el-button @click="handleClose">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {createLink, findLink, updateLink} from "@/api/shopSetting";
import {getArticleList} from "@/api/sales";
import {getCollectionList} from "@/api/collection";

export default {
  data() {
    return {
      dialogVisible: false,
      miniOptions: [],
      appOptions: [],
      formData: {
        title: "",
        url: "",
        sort: 0,
        group_id: 3,
        pid: 0,
        mini_id:null,
        mini_url: "",//小程序访问链接
        h5_url: "",//H5访问链接
        app_id:null,
        app_url: "",//APP访问链接
        mini_url_type: 0, // 小程序跳转链接类型 0文章 1专辑
        app_url_type: 0, // APP跳转链接 0文章 1专辑
        mini_collection_id: null, // 小程序跳转专辑id
        app_collection_id: null, // APP跳转专辑id
        pc_show: 1, // PC端显示
        app_show: 1, // 移动端显示
      },
      title: "新增",
    };
  },
  mounted() {
  },
  methods: {
    init() {
      this.getArticleList()
      this.getCollectionList()
    },
    handleJumpTypeChange(){
    },
    getArticleList(){
      getArticleList({page: 1, pageSize: 9999}).then((res) => {
        if(res.code == 0){
          this.miniOptions = res.data.list;
        }
      })
    },
    getCollectionList(){
      getCollectionList({page: 1, pageSize: 9999}).then((res) => {
        if(res.code == 0){
          this.appOptions = res.data.list;
        }
      })
    },
    // 关闭dialog
    handleClose() {
      this.dialogVisible = false;
      this.$refs.form.resetFields();
      this.miniOptions = []
      this.appOptions = []
      this.formData = {
        title: "",
        url: "",
        sort: 0,
        group_id: 3,
        pid: 0,
        mini_id:null,
        mini_url: "",//小程序访问链接
        h5_url: "",//H5访问链接
        app_id:null,
        app_url: "",//APP访问链接
        mini_url_type: 0, // 小程序跳转链接类型 0文章 1专辑
        app_url_type: 0, // APP跳转链接 0文章 1专辑
        mini_collection_id: null, // 小程序跳转专辑id
        app_collection_id: null, // APP跳转专辑id
        pc_show: 1, // PC端显示
        app_show: 1, // 移动端显示
      }
      this.title = "新增"
      if (this.formData.id) {
        delete this.formData.id;
      }
    },
    // 保存
    save() {
      if (!this.formData.title) {
        this.$message.error("请填写导航名称");
        return;
      }
      this.formData.sort = parseInt(this.formData.sort);
      if (this.formData.id) {
        updateLink(this.formData).then((res) => {
          if (res.code === 0) {
            this.$message.success("编辑成功");
            this.handleClose();
            this.$emit("onload", this.formData.pid);
          } else {
            this.$message.error(res.msg);
          }
        });
      } else {
        createLink(this.formData).then((res) => {
          if (res.code === 0) {
            this.$message.success("新增成功");
            this.handleClose();
            this.$emit("onload", this.formData.pid);
          } else {
            this.$message.error("新增失败");
          }
        });
      }
    },
    getInfo(row) {
      findLink({id: row.id}).then((res) => {
        if (res.code === 0) {
          let data = res.data.rehotSearch;
          this.formData.id = data.id;
          this.formData.mini_id = data.mini_id;
          this.formData.app_id = data.app_id;
          this.formData.title = data.title;
          this.formData.url = data.url;
          this.formData.mini_url = data.mini_url;
          this.formData.h5_url = data.h5_url;
          this.formData.app_url = data.app_url;
          this.formData.sort = data.sort;
          this.formData.mini_url_type = data.mini_url_type;
          this.formData.app_url_type = data.app_url_type;
          this.formData.mini_collection_id = data.mini_collection_id;
          this.formData.app_collection_id = data.app_collection_id;
          this.formData.pc_show = data.pc_show
          this.formData.app_show = data.app_show

        } else {
          this.$message.error(res.msg);
        }
      });
    },
  },
};
</script>
<style lang="scss">
.nblinlStyle{
  color: rgba(243, 35, 33, 1);
  //font-size: 12px;
  text-align: left;
  font-family: SourceHanSansSC-regular;
}
.applinlStyle{
  color: rgba(243, 35, 33, 1);
  //font-size: 12px;
  text-align: left;
  font-family: SourceHanSansSC-regular;
}
</style>