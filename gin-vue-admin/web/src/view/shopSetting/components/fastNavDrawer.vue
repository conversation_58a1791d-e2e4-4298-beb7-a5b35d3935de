<template>
    <div>
        <el-drawer
            :title="drawerTitle"
            :visible.sync="drawer"
            direction="rtl"
            size="85%"
        >
            <el-button type="primary" @click="openDialog('add')">
                新增子导航
            </el-button>
            <p class="hint-p mt10">当子导航存在时一级导航失效！</p>
            <el-table class="mt10" :data="tableData">
                <el-table-column
                    label="名称"
                    align="center"
                    prop="title"
                ></el-table-column>
                <!-- <el-table-column label="跳转链接" prop="url" align="center"></el-table-column> -->
                <el-table-column label="PC端显示" align="center">
                    <template slot-scope="scope">
                        <el-switch
                            v-model="scope.row.pc_show"
                            :active-value="1"
                            :inactive-value="0"
                            @change="changeSwitch('pc_show', scope.row)"
                        >
                        </el-switch>
                    </template>
                </el-table-column>
                <el-table-column label="移动端显示" align="center">
                    <template slot-scope="scope">
                        <el-switch
                            v-model="scope.row.app_show"
                            :active-value="1"
                            :inactive-value="0"
                            @change="changeSwitch('app_show', scope.row)"
                        >
                        </el-switch>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            @click="moveLink('up', scope.row)"
                            style="
                                padding: 0 !important;
                                margin-left: 10px !important;
                            "
                            >上移</el-button
                        >
                        <el-button
                            type="text"
                            @click="moveLink('down', scope.row)"
                            style="
                                padding: 0 !important;
                                margin-left: 10px !important;
                            "
                            >下移</el-button
                        >
                        <el-button
                            type="text"
                            @click="openDialog('edit', scope.row)"
                            style="
                                padding: 0 !important;
                                margin-left: 10px !important;
                            "
                            >编辑</el-button
                        >
                        <el-button
                            slot="reference"
                            type="text"
                            class="btn-del-text"
                            @click="deleteNavItemDialog(scope.row)"
                            style="
                                padding: 0 !important;
                                margin-left: 10px !important;
                            "
                            >删除</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                :current-page="page"
                :page-size="pageSize"
                :page-sizes="[10, 30, 50, 100]"
                :style="{
                    display: 'flex',
                    justifyContent: 'flex-end',
                    marginRight: '20px',
                }"
                :total="total"
                @current-change="handleCurrentChange"
                @size-change="handleSizeChange"
                layout="total, sizes, prev, pager, next, jumper"
            ></el-pagination>
        </el-drawer>
        <!-- 新增,编辑快速导航dialog -->
        <fastNavChildDialog
            ref="fastNavChildDialog"
            @onload="getTableData"
        ></fastNavChildDialog>
    </div>
</template>

<script>
import {
    getLinkList,
    deleteLink,
    moveLink,
    updateLink,
} from '@/api/shopSetting';
import infoList from '@/mixins/infoList';
import fastNavChildDialog from './fastNavChildDialog.vue';

export default {
    components: { fastNavChildDialog },
    mixins: [infoList],
    data() {
        return {
            drawerTitle: '',
            pid: null, // 父ID
            drawer: false,
            listApi: getLinkList,
            searchInfo: { group_id: 3, pid: null },
        };
    },
    methods: {
        // 修改PC或移动端显示
        async changeSwitch(type, row) {
            const res = await updateLink(row);
            if (res.code === 0) {
                this.$message.success('操作成功');
                this.getTableData();
            }
        },
        // 上移或下移
        async moveLink(type, row) {
            const data = {
                id: row.id,
                pid: parseInt(this.pid),
                move_operate: type,
            };
            const res = await moveLink(data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.getTableData();
            }
        },
        openDialog(type, row) {
            this.$refs.fastNavChildDialog.dialogVisible = true;
            this.$refs.fastNavChildDialog.formData.pid = this.pid;
            this.$nextTick(() => {
                if (type === 'add') {
                    this.$refs.fastNavChildDialog.title = '新增';
                    this.$refs.fastNavChildDialog.init();
                } else if (type === 'edit') {
                    this.$refs.fastNavChildDialog.title = '编辑';
                    this.$refs.fastNavChildDialog.init();
                    this.$refs.fastNavChildDialog.getInfo(row);
                }
            });
        },

        //删除导航
        deleteNavItemDialog(item) {
            this.$confirm('确定要删除吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            })
                .then(() => {
                    this.deleteNavItem(item);
                })
                .catch(() => {});
        },

        deleteNavItem(row) {
            let para = {
                id: row.id,
                pid: parseInt(this.pid),
            };
            deleteLink(para).then((res) => {
                if (res.code === 0) {
                    this.$message.success('删除成功');
                    this.getTableData();
                }
            });
        },
    },
};
</script>

<style></style>
