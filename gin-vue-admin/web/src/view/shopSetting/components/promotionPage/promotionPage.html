<m-card>
    <el-row>
        <el-col :span="12">
            <el-table class="mb25" :data="tableData">
                <el-table-column label="类型" align="center" prop="name"></el-table-column>
                <el-table-column label="是否显示" align="center">
                    <template slot-scope="scope">
                        <el-checkbox v-model="scope.row.isShow" :true-label="0" :false-label="1"></el-checkbox>
                    </template>
                </el-table-column>
                <el-table-column label="排序" align="center">
                    <template slot-scope="scope">
                        <el-input v-model.number="scope.row.sort"></el-input>
                    </template>
                </el-table-column>
            </el-table>
            <el-button type="primary" @click="save">保存</el-button>
        </el-col>
    </el-row>
</m-card>