<div>
    <el-form class="search-term" v-model="settingSearchForm" inline>
        <el-form-item label="搜索框默认搜索词:" prop="title">
            <el-input v-model="settingSearchForm.title" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="统计数据显示:" prop="title">
            <el-checkbox true-label="1" false-label="0" v-model="settingSearchForm.is_show_product_count">商品总量(sku)</el-checkbox>
            <el-checkbox true-label="1" false-label="0" v-model="settingSearchForm.is_show_sale_count   ">销售总量(件)</el-checkbox>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" @click="saveSearchKeyWord()">
                保存
            </el-button>
        </el-form-item>
    </el-form>
    <el-button type="primary" @click="createSearchKeyword()" class="mt25">新增</el-button>
    <el-table :data="tableList" class="mt25">
        <el-table-column type="selection" align="center" width="55"></el-table-column>
        <el-table-column label="热搜词" prop="title" align="center"></el-table-column>
        <el-table-column label="排序" prop="sort" align="center"></el-table-column>
        <el-table-column label="状态" align="center">
            <template slot-scope="scope">
                {{ scope.row.is_display | formatState }}
            </template>
        </el-table-column>
        <el-table-column label="日期" align="center">
            <template slot-scope="scope">
                {{ scope.row.created_at | formatDate }}
            </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200" fixed="right">
            <template slot-scope="scope">
                <el-button type="text" v-if="false">上移</el-button>
                <el-button type="text" v-if="false">下移</el-button>
                <el-button type="text" @click="editSearchKeyword(scope.row)" style="padding: 0 !important;margin-left: 10px !important;">编辑</el-button>
                <el-button type="text" slot="reference" class="color-red" @click="deleteSearchKeywordDialog(scope.row)" style="padding: 0 !important;margin-left: 10px !important;">删除</el-button> 
            </template>
        </el-table-column>
    </el-table>
    <el-pagination background :current-page="page" :page-size="pageSize" :page-sizes="[10, 30, 50, 100]" :style="{
        display: 'flex',
        justifyContent: 'flex-end',
        marginRight: '20px',
      }" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange"
        layout="total, sizes, prev, pager, next, jumper"></el-pagination>
    <el-dialog :title="dialogTitle" :visible="isShow" width="30%" :before-close="handleClose">
        <el-form :model="formData" label-width="80px" ref="form" :rules="rules">
            <el-form-item label="热搜词:" prop="title">
                <el-input v-model="formData.title" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="排序:" prop="sort">
                <el-input-number v-model="formData.sort" :controls="false" class="w100" :precision="0" :min="0">
                </el-input-number>
            </el-form-item>
            <el-form-item label="状态:">
                <el-radio-group v-model="formData.is_display">
                    <el-radio :label="1">显示</el-radio>
                    <el-radio :label="0">隐藏</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="confirm">确 定</el-button>
            <el-button @click="handleClose">取 消</el-button>
        </span>
    </el-dialog>
</div>