<template>
    <el-form ref="formData" :model="formData" label-width="170px">
        <el-row :gutter="10">
            <el-col :span="13">
                <el-form-item size="normal">
                    <span slot="label">
                        <span class="color-red">*</span>AppID:
                    </span>
                    <el-input v-model="formData.appid" size="normal" clearable></el-input>
                </el-form-item>
                <el-form-item size="normal">
                    <span slot="label">
                        <span class="color-red">*</span>AppSecret:
                    </span>
                    <div class="verify">
                        <el-input
                            v-model="formData.appsecret"
                            size="normal"
                            clearable
                            :disabled="true"
                        ></el-input>
                        <!-- 重置 -->
                        <el-popover placement="top" width="500" v-model="visible[0].popover">
                            <p>重置-AppSecret</p>
                            <el-input
                                type="textarea"
                                v-model="visible[0].value"
                                class="popover_val"
                                placeholder="请输入"
                                rows="1"
                            ></el-input>

                            <div style="text-align: right; margin: 0">
                                <el-button
                                    size="mini"
                                    type="text"
                                    @click="visible[0].popover = false"
                                >取消</el-button>
                                <el-button
                                    type="primary"
                                    size="mini"
                                    @click="resetToken('AppSecret')"
                                >确定</el-button>
                            </div>
                            <el-button slot="reference" type="primary" class="verify_but">重置</el-button>
                        </el-popover>
                    </div>
                </el-form-item>
                <el-form-item size="normal">
                    <span slot="label">
                        <span class="color-red">*</span>支付商户号:
                    </span>
                    <el-input v-model="formData.mch_id" size="normal" clearable></el-input>
                </el-form-item>
                <el-form-item size="normal">
                    <span slot="label">
                        <span class="color-red">*</span>支付密钥:
                    </span>
                    <div class="verify">
                        <el-input
                            v-model="formData.pay_key"
                            size="normal"
                            :disabled="true"
                            clearable
                        ></el-input>
                        <!-- 重置 -->
                        <el-popover placement="top" width="500" v-model="visible[1].popover">
                            <p>重置-支付密钥</p>
                            <el-input
                                type="textarea"
                                v-model="visible[1].value"
                                class="popover_val"
                                placeholder="请输入"
                                rows="1"
                            ></el-input>
                            <div style="text-align: right; margin: 0">
                                <el-button
                                    size="mini"
                                    type="text"
                                    @click="visible[1].popover = false"
                                >取消</el-button>
                                <el-button
                                    type="primary"
                                    size="mini"
                                    @click="resetToken('PayKey')"
                                >确定</el-button>
                            </div>
                            <el-button slot="reference" type="primary" class="verify_but">重置</el-button>
                        </el-popover>
                    </div>
                    <p class="color-grap" style="color: red; height: 10px">V3密钥</p>
                </el-form-item>
                <el-form-item size="normal">
                    <span slot="label">
                        <span class="color-red">*</span>CERT证书文件:
                    </span>
                    <el-upload
                        :action="`${$path}/smallShop/uploadWechatCertFile`"
                        ref="upload"
                        accept=".pem"
                        :headers="{ 'x-token': token }"
                        :show-file-list="false"
                        :before-upload="beforeUpload"
                        :on-success="handleCERTSuccess"
                    >
                        <el-button>上传文件</el-button>
                    </el-upload>
                    <el-tag
                        :type="formData.cert_path ? '' : 'danger'"
                        size="normal"
                    >{{formData.cert_path? '已上传' : '未上传'}}</el-tag>
                </el-form-item>
                <el-form-item size="normal">
                    <span slot="label">
                        <span class="color-red">*</span>KEY密钥文件:
                    </span>
                    <el-upload
                        :action="`${$path}/smallShop/uploadWechatCertFile`"
                        ref="upload"
                        accept=".pem"
                        :headers="{ 'x-token': token }"
                        :show-file-list="false"
                        :before-upload="beforeUpload"
                        :on-success="handleKeyuccess"
                    >
                        <el-button>上传文件</el-button>
                    </el-upload>
                    <el-tag
                        :type="formData.key_path ? '' : 'danger'"
                        size="normal"
                    >{{ formData.key_path ? '已上传' : '未上传' }}</el-tag>
                </el-form-item>
                <el-form-item size="normal">
                    <span slot="label">
                        <span class="color-red">*</span>证书编号:
                    </span>
                    <el-input
                        v-model="formData.mch_certificate_serial_number"
                        size="normal"
                        clearable
                    ></el-input>
                    <p class="color-grap" style="color: red">
                        *请设置系统 - 附件配置 - OSS类型（local）-
                        本地host
                        设置为当前域名（https://xxx.xxx.com/），否则微信支付回调，退款回调无法通知到中台，设置完成之后把OSS类型切换为您使用的即可。
                    </p>
                </el-form-item>
                <el-form-item label>
                    <el-button type="primary" @click="save">立即更新</el-button>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>
</template>

<script>
import {
    findWechatPaymentSetting,
    updateWechatPaymentSetting,
    updateWechatPaymentMask,
    downloaderWechatCert
} from '@/api/smallShop';
import { mapGetters } from 'vuex';

const path = process.env.VUE_APP_BASE_API;
export default {
    data() {
        return {
            path: path,

            id: null,
            key: '',
            formData: {
                appid: '',
                appsecret: '',
                mch_id: '',
                pay_key: '',
                cert_path: '',
                key_path: '',
                mch_certificate_serial_number: '',

                serial: '',
                cert: '',
            },

            visible: [
                { name: 'AppSecret', popover: false, value: '' },
                { name: 'pay_key', popover: false, value: '' },
            ],
        };
    },
    computed: {
        ...mapGetters('user', ['userInfo', 'token']),
    },
    methods() {},
    methods: {
        info() {
            this.getFindWechatPaymentSetting();
        },
        // 获取微信支付方式（h5公众号）设置
        async getFindWechatPaymentSetting() {
            let res = await findWechatPaymentSetting();
            if (res.code === 0) {
                this.formData = res.data.setting.value;
                this.id = res.data.setting.id;
                this.key = res.data.setting.key;
            }
        },
        // 保存
        async save() {
            const wechatPay = this.formData;
            // appid
            if (!wechatPay.appid) {
                this.$message({
                    message: '请输入AppID',
                    type: 'error',
                });
                return
            }
            // AppSecret
            if (!wechatPay.appsecret) {
                this.$message({
                    message: '请输入AppSecret',
                    type: 'error',
                });
                return false;
            }
            // 支付商户号
            if (!wechatPay.mch_id) {
                this.$message({
                    message: '请输入支付商户号',
                    type: 'error',
                });
                return false;
            }
            // 支付密钥
            if (!wechatPay.pay_key) {
                this.$message({
                    message: '请输入支付密钥',
                    type: 'error',
                });
                return false;
            }
            // CERT证书文件
            if (!wechatPay.cert_path) {
                this.$message({
                    message: '请上传CERT证书文件',
                    type: 'error',
                });
                return false;
            }
            // KEY密钥文件
            if (!wechatPay.key_path) {
                this.$message({
                    message: '请上传KEY密钥文件',
                    type: 'error',
                });
                return false;
            }
            // 证书编号
            if (!wechatPay.mch_certificate_serial_number) {
                this.$message({
                    message: '请输入证书编号',
                    type: 'error',
                });
                return false;
            }
            let form = {
                mch_id: wechatPay.mch_id,
                pay_key: wechatPay.pay_key,
                cert_path: wechatPay.cert_path,
                key_path: wechatPay.key_path,
                mch_certificate_serial_number: wechatPay.mch_certificate_serial_number,
            }
            const { code, data } = await downloaderWechatCert(form);
            if (code === 0) {
                this.formData.cert = data.cert;
                this.formData.serial = data.serial;
            } else {
                return
            }
            let params = {
                id: this.id,
                key: this.key,
                value: this.formData,
            }
            let res = await updateWechatPaymentSetting(params);
            if (res.code === 0) {
                this.$message.success(res.msg);
            }
        },
        // 重置
        async resetToken(str) {
            switch (str) {
                case 'AppSecret':
                    this.formData.appsecret = this.visible[0].value;
                    this.visible[0].popover = false;
                    await updateWechatPaymentMask({
                        field: str,
                        value: this.visible[0].value,
                    });
                    this.visible[0].value = '';
                    break;
                case 'PayKey':
                    this.formData.pay_key = this.visible[1].value;
                    this.visible[1].popover = false;
                    await updateWechatPaymentMask({
                        field: str,
                        value: this.visible[1].value,
                    });
                    this.visible[1].value = '';
                    break;
            }
        },
        // 上传 CERT证书文件
        handleCERTSuccess(res) {
            if (res.code === 0) {
                this.formData.cert_path = res.data;
            } else {
                this.$message.error(res.msg);
            }
        },
        handleKeyuccess(res) {
            if (res.code === 0) {
                this.formData.key_path = res.data;
            } else {
                this.$message.error(res.msg);
            }
        },
        beforeUpload(file) {
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                this.$message.error('上传文件大小不能超过 10MB!');
            }
            return isLt10M;
        },
    },
};
</script>

<style lang="scss" scoped>
.el-divider {
    margin: 15px 0;
}
::v-deep .el-input .el-input__inner {
    width: 100%;
}

.verify {
    display: flex;

    .verify_but {
        margin-left: 10px;
    }
}

.popover_val {
    width: 500px;
    margin: 10px auto;
}
</style>