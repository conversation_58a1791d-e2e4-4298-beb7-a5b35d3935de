<template>
    <el-drawer
        title="添加商品"
        :visible="isShow"
        :close-on-press-escape="false"
        :wrapperClosable="false"
        size="calc(100% - 220px)"
        :before-close="handleClose"
    >
        <el-form
            :model="searchInfo"
            ref="form"
            class="search-term mt25"
            inline
            label-width="0px"
        >
            <!-- <el-form-item label="商品名称:" prop="title">
                <el-input v-model="searchInfo.title" placeholder="请输入" clearable></el-input>
            </el-form-item> -->
            <el-form-item prop="title">
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.title"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">商品名称</span>
                </el-input>
            </el-form-item>
            <el-form-item label="" prop="goodsStatus">
                <div class="line-input">
                    <div class="line-box">
                        <span>商品状态</span>
                    </div>
                    <el-select v-model="goodsStatus">
                        <el-option
                            v-for="item in goodsStatusList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item prop="shopTitle">
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.shopTitle"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">店铺名称</span>
                </el-input>
            </el-form-item>
            <el-form-item label="" prop="supplier_id">
                <div class="line-input">
                    <div class="line-box">
                        <span>供应商</span>
                    </div>
                    <el-select v-model="searchInfo.supplier_id">
                        <el-option label="全部" value=""></el-option>
                        <el-option label="平台自营" value="0"></el-option>
                        <el-option
                            label="全部供应商"
                            value="999999"
                        ></el-option>
                        <el-option
                            v-for="item in supplierList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="" prop="gather_supply_id">
                <div class="line-input">
                    <div class="line-box">
                        <span>供应链</span>
                    </div>
                    <el-select v-model="searchInfo.gather_supply_id">
                        <el-option label="全部" value=""></el-option>
                        <el-option label="平台自营" value="1"></el-option>
                        <el-option
                            :label="item.name"
                            :value="item.id"
                            :key="item.id"
                            v-for="item in supplyOptions"
                        >
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="" prop="category1_id">
                <div class="line-input">
                    <div class="line-box">
                        <span>一级分类</span>
                    </div>
                    <el-select
                        v-model="searchInfo.category1_id"
                        placeholder="请选择一级分类"
                        @change="
                            handleClassiyfChange(2, searchInfo.category1_id)
                        "
                    >
                        <el-option
                            v-for="item in categoryList1"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="" prop="category2_id">
                <div class="line-input">
                    <div class="line-box">
                        <span>二级分类</span>
                    </div>
                    <el-select
                        v-model="searchInfo.category2_id"
                        placeholder="请选择二级分类"
                        @change="
                            handleClassiyfChange(3, searchInfo.category2_id)
                        "
                    >
                        <el-option
                            v-for="item in categoryList2"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="" prop="category3_id">
                <div class="line-input">
                    <div class="line-box">
                        <span>三级分类</span>
                    </div>
                    <el-select
                        v-model="searchInfo.category3_id"
                        placeholder="请选择三级分类"
                    >
                        <el-option
                            v-for="item in categoryList3"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item prop="sn">
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.sn"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">商品条形码</span>
                </el-input>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input">
                    <div class="line-box">
                        <span>价格区间</span>
                    </div>
                    <div class="f fac">
                        <el-input
                            v-model="searchInfo.minPrice"
                            clearable
                            placeholder="最低价"
                        ></el-input>
                        <span class="zih-span mr_10">至</span>
                        <el-input
                            v-model="searchInfo.maxPrice"
                            clearable
                            placeholder="最高价"
                        ></el-input>
                    </div>
                </div>
            </el-form-item>
            <el-form-item label="" prop="brand_id">
                <div class="line-input">
                    <div class="line-box">
                        <span>品牌</span>
                    </div>
                    <el-select
                        v-model="searchInfo.brand_id"
                        clearable
                        filterable
                        remote
                        :remote-method="remoteMethod"
                        :loading="brandsOptiosData.loading"
                    >
                        <el-option
                            v-for="item in brandsOptiosData.brandsOptios"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                        <div class="text-center">
                            <el-pagination
                                background
                                small
                                class="pagination"
                                :current-page="brandsOptiosData.page"
                                :page-size="brandsOptiosData.pageSize"
                                style="
                                    padding-top: 10px !important;
                                    padding-bottom: 0 !important;
                                "
                                :total="brandsOptiosData.total"
                                layout="prev,pager, next"
                                @current-change="handleBrandPage"
                            ></el-pagination>
                        </div>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item prop="id">
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.id"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">商品id</span>
                </el-input>
            </el-form-item>
            <el-form-item label="" prop="arketingChecked">
                <div class="line-input">
                    <div class="line-box">
                        <span>营销属性</span>
                    </div>
                    <el-select v-model="arketingChecked" placeholder="请输入">
                        <el-option
                            :label="item.label"
                            :value="item.value"
                            :key="item.value"
                            v-for="item in arketingOptios"
                        >
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="" prop="status_lock">
                <div class="line-input">
                    <div class="line-box">
                        <span>锁定状态</span>
                    </div>
                    <el-select
                        v-model="searchInfo.status_lock"
                        placeholder="请输入"
                    >
                        <el-option label="全部" value=""></el-option>
                        <el-option label="已锁定" :value="1"></el-option>
                        <el-option label="未锁定" :value="0"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="" prop="sortType">
                <div class="line-input">
                    <div class="line-box">
                        <span>排序</span>
                    </div>
                    <el-select
                        v-model="searchInfo.sortType"
                        placeholder="请输入"
                    >
                        <el-option label="ID" value="0"></el-option>
                        <el-option label="供货价" value="1"></el-option>
                        <el-option label="成本价" value="2"></el-option>
                        <el-option label="零售价" value="3"></el-option>
                        <el-option label="利润率" value="4"></el-option>
                        <el-option label="销量" value="5"></el-option>
                        <el-option label="Sort" value="6"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="" prop="sortMode">
                <div class="line-input">
                    <div class="line-box">
                        <span>排序方向</span>
                    </div>
                    <el-select
                        v-model="searchInfo.sortMode"
                        placeholder="请输入"
                    >
                        <el-option label="从小到大" value="asc"></el-option>
                        <el-option label="从大到小" value="desc"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="" prop="isVideoShop">
                <div class="line-input">
                    <div class="line-box">
                        <span>视频号商品</span>
                    </div>
                    <el-select
                        v-model="searchInfo.isVideoShop"
                        placeholder="请输入"
                    >
                        <el-option label="全部" value="0"></el-option>
                        <el-option label="中台商品" value="-1"></el-option>
                        <el-option label="视频号商品" value="1"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <div class="f">
                <el-button type="primary" @click="getTableData">搜索</el-button>
                <el-button type="text" @click="resetForm('form')"
                    >重置搜索条件</el-button
                >
            </div>
        </el-form>
        <el-table
            class="mt25"
            :data="tableData"
            @selection-change="handleSelectionChange"
        >
            <el-table-column
                type="selection"
                width="55"
                align="center"
            ></el-table-column>
            <el-table-column label="ID" width="100" align="center">
                <template slot-scope="scope">
                    <p>{{ scope.row.id }}</p>
                </template>
            </el-table-column>
            <el-table-column label="图片" align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                    <el-popover placement="right" title="" trigger="hover">
                        <m-image
                            :src="scope.row.image_url"
                            :style="{ width: '160px', height: '160px' }"
                        ></m-image>
                        <m-image
                            slot="reference"
                            :src="scope.row.image_url"
                            :alt="scope.row.image_url"
                            :style="{ width: '60px', height: '60px' }"
                        ></m-image>
                    </el-popover>
                </template>
            </el-table-column>
            <el-table-column label="商品">
                <template slot-scope="scope">
                    <p>{{ scope.row.title }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center" show-overflow-tooltip>
                <template slot="header">
                    <p>供货价/成本价</p>
                </template>
                <template slot-scope="scope">
                    <p v-if="scope.row.skus.length > 1">
                        {{ scope.row.minPrice | formatF2Y }}-{{
                            scope.row.maxPrice | formatF2Y
                        }}
                        / {{ scope.row.minCostPrice | formatF2Y }}-{{
                            scope.row.maxCostPrice | formatF2Y
                        }}
                    </p>
                    <p v-else>
                        {{ scope.row.price | formatF2Y }} /
                        {{ scope.row.cost_price | formatF2Y }}
                    </p>
                </template>
            </el-table-column>
            <el-table-column
                label="商品状态"
                align="center"
                show-overflow-tooltip
            >
                <template slot-scope="scope">
                    <p>{{ scope.row.is_display === 1 ? '上架' : '下架' }}</p>
                </template>
            </el-table-column>
            <el-table-column label="库存" align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                    <p>{{ scope.row.stock }}</p>
                </template>
            </el-table-column>
            <el-table-column label="销量" align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                    <p>{{ scope.row.sales }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center" show-overflow-tooltip>
                <template slot="header">
                    <p>供应渠道</p>
                </template>
                <template slot-scope="scope">
                    <span v-if="scope.row.supplier_id > 0">{{
                        scope.row.supplier.name
                    }}</span>
                    <span v-else-if="scope.row.gather_supply_id > 0">{{
                        scope.row.gather_supply.name
                    }}</span>
                    <span
                        v-else-if="
                            scope.row.supplier_id === 0 &&
                            scope.row.gather_supply_id === 0
                        "
                        >自营</span
                    >
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :style="{
                display: 'flex',
                justifyContent: 'flex-end',
                marginRight: '20px',
            }"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes, prev, pager, next, jumper"
        ></el-pagination>
        <el-button type="primary" @click="addShop">加入选中商品</el-button>
        <!-- <el-button type="primary" @click="addSeachShop">
            加入全部筛选商品 {{ total }}
        </el-button> -->
    </el-drawer>
</template>

<script>
import { getClassify } from '@/api/classify';
import { getSupplyList } from '@/api/order';
import { getBrandsList } from '@/api/brands';
import { getSupplierOptionList } from '@/api/goods';
import {
    getProductListBySelectedAdd,
    addSelected,
    addPondBySearch,
} from '@/api/smallShop';

export default {
    data() {
        return {
            isShow: false,
            // 分页
            page: 1,
            pageSize: 10,
            total: 10,

            // 类目1
            categoryList1: [],
            categoryList2: [],
            categoryList3: [],
            //弹窗类目
            categoryListDialog1: [],
            categoryListDialog2: [],
            categoryListDialog3: [],
            // 供应商
            supplierList: [],
            // 供应链
            supplyOptions: [],
            // 品牌
            brandsOptiosData: {
                name: '',
                brandsOptios: [],
                loading: false,
                page: 1,
                pageSize: 10,
                total: 0,
            },
            searchInfo: {
                id: null, //商品id
                filter: 0,
                // 商品
                title: '',
                category1_id: '',
                category2_id: '',
                category3_id: '',
                //店铺名称
                shopTitle: '',
                // 最高价
                maxPrice: '',
                // 最低价
                minPrice: '',
                gather_supply_id: null,
                // 供应商
                supplier_id: null,
                sn: '',
                brand_id: null,
                status_lock: '', // 锁定状态
                sortType: '', //
                sortMode: '', //
                isVideoShop: '', //
                source_name: '',
            }, // 搜索
            goodsStatus: '0', // 商品状态
            arketingChecked: '', // 营销属性字段
            tableData: [], // 表格数据
            goodsStatusList: [
                { name: '全部', value: '0' },
                { name: '上架', value: '1' },
                { name: '下架', value: '2' },
                { name: '售罄', value: '3' },
            ],
            arketingOptios: [
                { label: '全部', value: '' },
                { label: '热卖', value: 'is_hot' },
                { label: '促销', value: 'is_promotion' },
                { label: '新品', value: 'is_new' },
            ],
            selectTables: [], // 多选
        };
    },
    methods: {
        init() {
            this.isShow = true;
            // 获取表格数据
            this.getProductListByPond();
            // 获取一级分类
            getClassify(1, 0).then((r) => {
                this.categoryList1 = r.data.list;
                this.categoryListDialog1 = r.data.list;
            });
            // 获取供应商
            getSupplierOptionList().then((r) => {
                this.supplierList = r.data.list;
            });
            // 获取品牌
            this.getBrandsOptios();
            // 获取供应链数据
            this.getSupply();
        },
        handleClose() {
            this.isShow = false;
            this.$emit('showfun');
        },
        // 获取表格
        async getProductListByPond() {
            let data = {
                page: 1,
                pageSize: 10,
            };
            let res = await getProductListBySelectedAdd(data);
            if (res.code === 0) {
                this.tableData = res.data.list;
                this.total = res.data.total;
            }
        },
        // 获取供应链
        getSupply() {
            getSupplyList().then((res) => {
                this.supplyOptions = res.data.list;
            });
        },
        // 添加商品
        async addShop() {
            if (this.selectTables.length === 0) {
                this.$message.warning('请勾选商品');
                return;
            }
            let res = await addSelected({ product_ids: this.selectTables });
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.page = 1;
                this.getProductListByPond(); // 重置页面
            }
        },
        // 添加全部筛选的商品
        async addSeachShop() {
            let para = {};

            if (this.searchInfo.filter !== '' && this.searchInfo.filter) {
                para.filter = this.searchInfo.filter;
            }

            if (this.searchInfo.title !== '' && this.searchInfo.title) {
                para.title = this.searchInfo.title;
            }

            if (
                this.searchInfo.category1_id !== '' &&
                this.searchInfo.category1_id
            ) {
                para.category1_id = this.searchInfo.category1_id;
            }

            if (
                this.searchInfo.category2_id !== '' &&
                this.searchInfo.category2_id
            ) {
                para.category2_id = this.searchInfo.category2_id;
            }

            if (
                this.searchInfo.category3_id !== '' &&
                this.searchInfo.category3_id
            ) {
                para.category3_id = this.searchInfo.category3_id;
            }

            if (this.searchInfo.maxPrice !== '' && this.searchInfo.maxPrice) {
                para.maxPrice = this.searchInfo.maxPrice;
            }

            if (this.searchInfo.minPrice !== '' && this.searchInfo.minPrice) {
                para.minPrice = this.searchInfo.minPrice;
            }

            if (this.searchInfo.brand_id !== '' && this.searchInfo.brand_id) {
                para.brand_id = this.searchInfo.brand_id;
            }

            if (this.searchInfo.id !== '' && this.searchInfo.id) {
                para.id = parseInt(this.searchInfo.id);
            }

            if (this.searchInfo.sn !== '' && this.searchInfo.sn) {
                para.barcode = this.searchInfo.sn;
            }

            if (this.searchInfo.shopTitle !== '' && this.searchInfo.shopTitle) {
                para.shop_name = this.searchInfo.shopTitle;
            }

            if (
                this.searchInfo.source_name !== '' &&
                this.searchInfo.source_name
            ) {
                para.source_name = this.searchInfo.source_name;
            }

            if (
                this.searchInfo.gather_supply_id !== '' &&
                this.searchInfo.gather_supply_id
            ) {
                para.gather_supply_id = this.searchInfo.gather_supply_id;
            }

            if (this.goodsResources) {
                if (
                    this.searchInfo.source_name !== '' &&
                    this.searchInfo.source_name
                ) {
                    para.source_name = this.searchInfo.source_name;
                }
            }

            if (
                this.searchInfo.supplier_id !== '' &&
                this.searchInfo.supplier_id
            ) {
                para.supplier_id = this.searchInfo.supplier_id;
            }
            if (this.arketingChecked !== '') {
                para[this.arketingChecked] = 1;
            }
            if (this.searchInfo.status_lock !== '') {
                para.status_lock = this.searchInfo.status_lock;
            }
            if (this.searchInfo.sortType !== '') {
                para.sortType = this.searchInfo.sortType;
            }
            if (this.searchInfo.sortMode !== '') {
                para.sortMode = this.searchInfo.sortMode;
            }
            if (this.searchInfo.isVideoShop !== '') {
                para.is_video_shop = this.searchInfo.isVideoShop;
            }

            let res = await addPondBySearch(para);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.resetForm('form'); // 重置页面
            }
        },
        // 下一页
        handleCurrentChange(page) {
            this.page = page;
            this.selectTables = [];
            this.getList();
        },
        // 每页显示条数
        handleSizeChange(size) {
            this.pageSize = size;
            this.selectTables = [];
            this.getList();
        },
        async getList() {
            this.tableData = [];
            let para = {
                page: this.page,
                pageSize: this.pageSize,
                filter: this.searchInfo.filter,
                title: this.searchInfo.title,
                category1_id: this.searchInfo.category1_id,
                category2_id: this.searchInfo.category2_id,
                category3_id: this.searchInfo.category3_id,
                maxPrice: this.searchInfo.maxPrice,
                minPrice: this.searchInfo.minPrice,
                brand_id: this.searchInfo.brand_id,
                id: this.searchInfo.id,
                barcode: this.searchInfo.sn,
                shop_name: this.searchInfo.shopTitle,
                source_name: this.searchInfo.source_name,
            };
            if (this.searchInfo.gather_supply_id !== '') {
                para.gather_supply_id = this.searchInfo.gather_supply_id;
            }

            if (this.goodsResources) {
                if (this.searchInfo.source_name !== '') {
                    para.source_name = this.searchInfo.source_name;
                }
            }

            if (this.searchInfo.supplier_id !== '') {
                para.supplier_id = this.searchInfo.supplier_id;
            }
            if (this.arketingChecked !== '') {
                para[this.arketingChecked] = 1;
            }
            if (this.searchInfo.status_lock !== '') {
                para.status_lock = this.searchInfo.status_lock;
            }
            if (this.searchInfo.sortType !== '') {
                para.sortType = this.searchInfo.sortType;
            }
            if (this.searchInfo.sortMode !== '') {
                para.sortMode = this.searchInfo.sortMode;
            }
            if (this.searchInfo.isVideoShop !== '') {
                para.is_video_shop = this.searchInfo.isVideoShop;
            }
            let res = await getProductListBySelectedAdd(para);
            if (res.code === 0) {
                this.tableData = res.data.list;
                this.total = res.data.total;
            } else {
                this.tableData = [];
                this.total = 0;
            }
        },
        // 品牌搜索
        remoteMethod(query) {
            this.brandsOptiosData.name = query;
            this.brandsOptiosData.page = 1;
            this.getBrandsOptios();
        },
        // 获取品牌
        async getBrandsOptios() {
            let params = {
                page: this.brandsOptiosData.page,
                pageSize: this.brandsOptiosData.pageSize,
            };
            if (this.brandsOptiosData.name)
                params.name = this.brandsOptiosData.name;
            this.brandsOptiosData.loading = true;
            let res = await getBrandsList(params);
            this.brandsOptiosData.loading = false;
            if (res.code === 0) {
                this.brandsOptiosData.brandsOptios = res.data.list;
                this.brandsOptiosData.total = res.data.total;
            }
        },
        // 品牌分页
        handleBrandPage(val) {
            this.brandsOptiosData.page = val;
            this.getBrandsOptios();
        },
        // 获取类目
        handleClassiyfChange(level, pid) {
            getClassify(level, pid).then((r) => {
                if (level === 3) {
                    this.categoryList3 = [];
                    this.searchInfo.category3_id = '';
                } else {
                    this.categoryList2 = [];
                    this.searchInfo.category2_id = '';
                    this.categoryList3 = [];
                    this.searchInfo.category3_id = '';
                }
                switch (level) {
                    case 2:
                        this.categoryList2 = r.data.list;
                        break;
                    case 3:
                        this.categoryList3 = r.data.list;
                        break;
                }
            });
        },
        // 搜索
        getTableData() {
            this.page = 1;
            this.searchInfo.filter = parseInt(this.goodsStatus); //同步数据
            this.getList();
        },
        // 重置筛选条件
        resetForm(formName) {
            this.goodsStatus = '0';
            this.page = 1;
            this.categoryList2 = [];
            this.categoryList3 = [];
            this.$refs[formName].resetFields();
            this.searchInfo.minPrice = '';
            this.searchInfo.maxPrice = '';
            this.arketingChecked = '';
            this.brandsOptiosData = {
                name: '',
                brandsOptios: [],
                loading: false,
                page: 1,
                pageSize: 10,
                total: 0,
            };
            this.remoteMethod();
        },
        // 多选
        handleSelectionChange(val) {
            let arr = [];
            val.forEach((element) => arr.push(element.id));
            this.selectTables = arr;
        },
    },
};
</script>

<style scoped lang="scss">
.el-row {
    .search-btn {
        background-color: #44d2b9;
        border-color: #44d2b9;
        color: white;
        margin-left: 15px;
        padding-left: 15px;
        padding-right: 15px;
    }

    .zih-span {
        display: inline-block;
        margin: 0 10px;
        color: #606266;
    }
}
</style>
