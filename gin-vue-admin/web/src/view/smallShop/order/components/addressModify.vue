<template>
  <el-dialog
      title="修改地址"
      :visible="isShow"
      width="800px"
      :before-close="handleClose">
    <el-form :model="formData" ref="form" :rules="rules" label-width="120px">
      <el-form-item label="收货人姓名:" prop="realname">
        <el-input v-model="formData.realname" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="收货人电话:" prop="mobile">
        <el-input v-model="formData.mobile" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="收货地址:" :required="true" :error="errorStr">
        <el-cascader
            ref="cascader"
            class="w100"
            v-model="areaArr"
            :options="options"
            :props="areaProps"
            @change="handleChange"></el-cascader>
      </el-form-item>
      <el-form-item label="详细地址:" prop="detail">
        <el-input v-model="formData.detail" placeholder="请输入"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirm">确 定</el-button>
      <el-button @click="handleClose">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {getAreaDataApi} from "@/api/addressManage"
import {updateShippingAddress} from "@/api/smallShop"
import verify from "../../../../utils/verify";

export default {
  name: "addressModify",
  props:{
    addressDialogIsShow:{
      type:Boolean,
      detail:()=>{
        return false
      }
    }
  },
  data() {
    return {
      errorStr: "",
      isShow: false,
      formData: {
        id: null,
        country_id: null,// 国家id
        province_id: null, // 省id
        city_id: null, // 市id
        county_id: null, // 区id
        town_id: null, // 街道id
        province: "",
        city: "",
        county: "",
        town: "",
        realname: "",
        mobile: "",
        detail: "",
        is_default: false,
        order_id: null
      },
      // 选中的地区id
      areaArr: [],
      // 地区数据数组
      options: [],
      // 配置级联选择器
      areaProps: {
        lazy: true,
        label: "name",
        value: "id",
        lazyLoad: (node, resolve) => {
          this.getArea(node.value).then(res => {
            resolve(res)
          })
        }
      },
      rules: {
        realname: {required: true, message: "请输入收货人姓名", trigger: "blur"},
        mobile: [
          {required: true, message: "请输入收货人电话", trigger: "blur"},
          {
            validator: (rule, value, callback) => {
              if (verify.checkPhone(value)) {
                callback()
              } else {
                callback(new Error("手机号格式不正确"))
              }
            }
          }
        ],
        detail: {required: true, message: "请输入详细地址", trigger: "blur"}
      }
    }
  },
  methods: {
    handleChange(val) {
      this.errorStr = this.areaArr.length === 4 ? "" : "请输入收货地址";
    },
    // 数据赋值
    handleFormData(data) {
      console.log(data);
      // this.getArea().then(res => {
      //   this.options = res
      this.areaArr = [data.shipping_address.province_id, data.shipping_address.city_id, data.shipping_address.county_id, data.shipping_address.town_id]
      this.formData.id = data.shipping_address.id
      this.formData.city = data.shipping_address.city
      this.formData.city_id = data.shipping_address.city_id
      this.formData.country_id = data.shipping_address.country_id
      this.formData.county = data.shipping_address.county
      this.formData.county_id = data.shipping_address.county_id
      this.formData.detail = data.shipping_address.detail
      this.formData.mobile = data.shipping_address.mobile
      this.formData.province = data.shipping_address.province
      this.formData.province_id = data.shipping_address.province_id
      this.formData.realname = data.shipping_address.realname
      this.formData.town = data.shipping_address.town
      this.formData.town_id = data.shipping_address.town_id
      this.formData.order_id = data.id
      // })
    },
    // 关闭
    handleClose() {
      try {
        this.$refs.form.resetFields()
      } catch {
      } finally {
        this.isShow = false
        this.formData = {
          id: null,
          country_id: null,// 国家id
          province_id: null, // 省id
          city_id: null, // 市id
          county_id: null, // 区id
          town_id: null, // 街道id
          province: "",
          city: "",
          county: "",
          town: "",
          realname: "",
          mobile: "",
          detail: "",
          is_default: false,
          order_id: null,
        }
        this.areaProps={}
        this.areaArr = []
        this.options = []
        this.errorStr = ""
        this.$emit("update:addressDialogIsShow",false)
      }
    },
    // 获取地市
    async getArea(parent_id = 0) {
      let res = await getAreaDataApi({parent_id})
      if (res.code === 0) {
        return res.data.list.map(item => ({
          ...item,
          leaf: item.level >= 4
        }))
      } else {
        return []
      }
    },
    // 确认
    confirm() {
      this.$refs.form.validate(valid => {
        if (!valid) return false;
        if (this.areaArr.length !== 4) {
          this.errorStr = "请输入收货地址"
          return false
        }
        let areas = this.$refs.cascader.getCheckedNodes()[0].pathNodes
        this.formData.province_id = areas[0].value
        this.formData.city_id = areas[1].value
        this.formData.county_id = areas[2].value
        this.formData.town_id = areas[3].value
        this.formData.province = areas[0].label
        this.formData.city = areas[1].label
        this.formData.county = areas[2].label
        this.formData.town = areas[3].label
        updateShippingAddress(this.formData).then(res=>{
          if(res.code === 0){
            this.$message.success(res.msg)
            this.handleClose()
            this.$emit("reload")
          }
        })
      })
    }
  }
}
</script>

<style scoped>

</style>