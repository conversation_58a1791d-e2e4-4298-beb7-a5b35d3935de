<template>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="指定商品" name="1">
        <assignProduct ref="assignProduct"></assignProduct>
        </el-tab-pane>
        <el-tab-pane label="指定商品专辑" name="2">
            <assingProductAlbum ref="assingProductAlbum"></assingProductAlbum>
        </el-tab-pane>
    </el-tabs>
</template>

<script>

import assignProduct from './components/assignProduct.vue';
import assingProductAlbum from '../appointAlbum/index.vue';
export default {
    name: "shopPondIndex",
    components:{assignProduct,assingProductAlbum},
    data() {
        return {
            activeName: '1'
        }
    },
    mounted() {
        this.handleClick()
    },
    methods: {
        handleClick() {
            switch(this.activeName){
                case '1':
                    this.$refs.assignProduct.init();
                    break;
                case '2':
                    this.$refs.assingProductAlbum.init();
                    break;
            }
        }
    }
}
</script>

<style scoped>
</style>