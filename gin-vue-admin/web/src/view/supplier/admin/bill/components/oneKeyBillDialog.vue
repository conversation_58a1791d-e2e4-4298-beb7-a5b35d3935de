<template>
  <el-dialog
      title="发票明细"
      :visible="isShow"
      width="800px"
      :before-close="handleClose"
  >
    <p class="title">购货单位</p>

    <el-row :gutter="10">
      <el-col :span="12">
        <p>
          名称:
          <span v-if="detial.info.account_type === 1">{{ detial.info.person_name }}</span>
          <span v-if="detial.info.account_type === 2">{{ detial.info.company_name }}</span>
        </p>
        <p class="mt10">地址: {{ detial.info.sign_address }}</p>
        <p class="mt10">电话: {{ detial.info.sign_mobile }}</p>
        <p class="mt10">手机: {{ detial.info.mobile }}</p>
      </el-col>
      <el-col :span="12">
        <p>纳税人识别号: {{ detial.info.company_code }}</p>
        <p class="mt10">开户行: {{ detial.info.opening_bank }}</p>
        <p class="mt10">账号: {{ detial.info.bank_account }}</p>
        <p class="mt10">邮箱: {{ detial.info.email }}</p>
      </el-col>
    </el-row>
    <p class="title mt25">销货单位</p>
    <el-row :gutter="10">
      <el-col :span="12">
        <p>名称: {{ detial.sale.bill_company_name }}</p>
        <p class="mt10">地址: {{ detial.sale.bill_address }}</p>
        <p class="mt10">电话: {{ detial.sale.bill_mobile }}</p>
      </el-col>
      <el-col :span="12">
        <p>纳税人识别号: {{ detial.sale.taxpayer_number }}</p>
        <p class="mt10">开户行: {{ detial.sale.register_bunk }}</p>
        <p class="mt10">账号: {{ detial.sale.bunk_number }}</p>
      </el-col>
    </el-row>


    <el-row :gutter="10" class="mt25">
      <el-col :span="8">
        <p>收款人: {{ detial.sale.bill_receiver }}</p>
      </el-col>
      <el-col :span="8">
        <p>复核人: {{ detial.sale.bill_recheck_name }}</p>
      </el-col>
      <el-col :span="8">
        <p>开票人: {{ detial.sale.bill_check_name }}</p>
      </el-col>
    </el-row>
    <div v-for="item in detial.info.order_bill" :key="item.id" class="mt25">
      <el-row>
        <el-col :span="14">
          <div class="f fac fjsb">
            <p>价税合计: {{ totalAmount(1, item.order) }}</p>
            <p>金额: {{ totalAmount(2, item.order) }}</p>
            <p>税额: {{ totalAmount(3, item.order) }}</p>
          </div>
        </el-col>
      </el-row>
      <el-table :data="item.order.order_items">
        <el-table-column label="商品">
          <template slot-scope="scope">
            <p class="hiddenText2">{{ scope.row.title }}</p>
          </template>
        </el-table-column>
        <el-table-column label="规格" align="center">
          <template slot-scope="scope">
            {{ scope.row.sku_title }}
          </template>
        </el-table-column>
        <el-table-column label="数量" align="center">
          <template slot-scope="scope">
            {{ scope.row.qty }}
          </template>
        </el-table-column>
        <el-table-column label="单价(含税)" align="center">
          <template slot-scope="scope">
            {{ scope.row.sku.price | formatF2Y }}
          </template>
        </el-table-column>
        <el-table-column label="金额(含税)" align="center">
          <template slot-scope="scope">
            {{ scope.row.amount | formatF2Y }}
          </template>
        </el-table-column>
        <el-table-column label="税率" align="center">
          <template slot-scope="scope">
            {{ scope.row.product.tax_rate / 100 }}
          </template>
        </el-table-column>
        <el-table-column label="税额" align="center">
          <template slot-scope="scope"> {{ taxItemAmount(scope.row.amount, scope.row.product.tax_rate) }}</template>
        </el-table-column>
      </el-table>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirm">确认开票</el-button>
      <el-button @click="handleClose">取 消</el-button>
    </span>
  </el-dialog>
</template>
<script>
import {confirmCheckout, findTradeSetting,billCheckout} from "@/api/supplier/bill";
import operation from "@/utils/operation";

export default {
  name: "oneKeyBillDialog",
  data() {
    return {
      isShow: false,
      detial: {
        sale: {}, // 销货单位
        info: {},
      },
    };
  },
  methods: {
    confirm(){
      billCheckout({id:this.detial.info.id}).then(res=>{
        if(res.code === 0){
          this.$message.success(res.msg)
          this.handleClose(true)
        }
      })
    },
    // 合计
    totalAmount(type, item) {
      let total = 0;
      switch (type) {
        case 1: // 价税合计
          let amount = 0;
          let tax = 0;
          item.order_items.forEach(el => {
            amount += el.amount
            tax += this.taxItemAmount(el.amount, el.product.tax_rate)
          })
          total = operation.accAdd(amount, tax)
          break;
        case 2: // 金额合计
          item.order_items.forEach(el => {
            total += el.amount
          })
          break;
        case 3: // 税额合计
          item.order_items.forEach(el => {
            total += this.taxItemAmount(el.amount, el.product.tax_rate)
          })
          break;
      }
      return this.$fn.changeMoneyF2Y(total)
    },
    // 单条税额
    taxItemAmount(amount, tax_rate) {
      let new_tax_rate = tax_rate / 100
      let newAmount = operation.accMul(amount, new_tax_rate)
      return this.$fn.changeMoneyF2Y(newAmount)
    },
    handleClose(onLoad = false) {
      this.isShow = false;
      this.detial = {
        sale: {}, // 销货单位
        info: {},
      };
      if(onLoad) this.$emit("onload")
    },
    // 获取发票明细数据
    async getInfo(row) {
      let res = await confirmCheckout({id: row.id});
      if (res.code === 0) {
        this.detial.sale = {
          ...res.data.info,
        };
        this.detial.info = {
          ...row,
        };
      }
      console.log(this.detial, '????');
    },
  },
};
</script>
<style lang="scss" scoped>
p.title {
  font-weight: bold;
}
</style>