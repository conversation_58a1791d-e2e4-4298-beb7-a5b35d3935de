<template>
    <m-card>
        <el-form label-width="80px" :model="formData" class="search-term" inline>
            <el-form-item label>
                <div class="line-input">
                    <div class="line-box">
                        <span>提现状态</span>
                    </div>
                    <el-select
                        class="w100"
                        filterable
                        clearable
                        v-model="formData.withdrawal_status"
                    >
                        <el-option :value="0" label="未提现"></el-option>
                        <el-option :value="1" label="驳回"></el-option>
                        <el-option :value="2" label="已申请"></el-option>
                        <el-option :value="3" label="通过"></el-option>
                        <el-option :value="4" label="无效"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label>
                <div class="line-input">
                    <div class="line-box">
                        <span>打款状态</span>
                    </div>
                    <el-select class="w100" filterable clearable v-model="formData.remit_status">
                        <el-option :value="0" label="待打款"></el-option>
                        <el-option :value="1" label="已打款"></el-option>
                        <el-option :value="2" label="打款中"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <div class="line-input line-input-date">
                    <div
                        class="line-box"
                        style="display: flex;justify-content: center;min-width: 80px;"
                    >
                        <span>时间</span>
                    </div>
                    <div class="f fac">
                        <el-date-picker
                            v-model="formData.times"
                            type="date"
                            placeholder="选择日期"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            class="f1"
                        ></el-date-picker>
                        <p class="title-3">至</p>
                        <el-date-picker
                            v-model="formData.timee"
                            type="date"
                            placeholder="选择日期"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            class="f1"
                        ></el-date-picker>
                    </div>
                </div>
            </el-form-item>
            <el-form-item>
                <el-button @click="onSubmit" type="primary">查询</el-button>
                <el-button @click="exportSupplierBill">导出</el-button>
                <el-button @click="ClearSearch" type="text">重置搜索条件</el-button>
            </el-form-item>
        </el-form>
        <div style="margin-top: 1em; margin-bottom: 1em">
            <el-row>
                <span class="mr_20">合计结果</span>
            </el-row>
            <div class="grid">
                <el-row>
                    <el-col :span="4">
                        <div class="grid-content" align="center">
                            <p class="numbers-p">{{ dataInfo.orderAmount | toMoney }}</p>
                            <p>订单总额（元）</p>
                        </div>
                    </el-col>
                    <el-col :span="4">
                        <div class="grid-content" align="center">
                            <p class="numbers-p">{{ dataInfo.costAmount | toMoney }}</p>
                            <p>成本价总额（元）</p>
                        </div>
                    </el-col>
                    <el-col :span="4">
                        <div class="grid-content" align="center">
                            <p class="numbers-p">{{ dataInfo.freight | toMoney }}</p>
                            <p>运费总额（元）</p>
                        </div>
                    </el-col>
                    <el-col :span="4">
                        <div class="grid-content" align="center">
                            <p class="numbers-p">{{ dataInfo.technicalServicesFee | toMoney }}</p>
                            <p>技术服务费总额（元）</p>
                        </div>
                    </el-col>
                    <el-col :span="4">
                        <div class="grid-content" align="center">
                            <p class="numbers-p">{{ dataInfo.supplierBillAmount | toMoney }}</p>
                            <p>供应商账单总额（元）</p>
                        </div>
                    </el-col>
                    <el-col :span="4">
                        <div class="grid-content" align="center">
                            <p class="numbers-p">{{ dataInfo.total_count }}</p>
                            <p>总数</p>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>
        <el-table :data="tableData" class="mt25" ref="table">
            <el-table-column label="时间" align="center">
                <template slot-scope="scope">{{ scope.row.created_at | formatDate }}</template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>订单号</p>
                    <p>订单金额（元）</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.order_sn }}</p>
                    <p>{{ scope.row.order.amount | formatF2Y }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>成本价（元）</p>
                    <p>运费（元）</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.order.cost_amount | toMoney }}</p>
                    <p>{{ scope.row.freight_price | toMoney }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>结算类型</p>
                    <p>订单结算金额（元）</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.settlement_type | settlementType }}</p>
                    <p>{{ scope.row.settlement_amount | toMoney }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>平台扣点比例</p>
                    <p>技术服务费金额（元）</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.deduction_ratio / 100 }}</p>
                    <p>{{ scope.row.technical_service_cost | toMoney }}</p>
                </template>
            </el-table-column>
            <el-table-column label="账单金额（元）" align="center">
                <template slot-scope="scope">
                    <p>{{ scope.row.supply_amount | toMoney }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>提现状态</p>
                    <p>提现手续费（元）</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.withdrawal_status | withdrawalStatus }}</p>
                    <p>{{ scope.row.withdrawal_fee | toMoney }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>打款状态</p>
                    <p>打款金额（元）</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.remit_status | remitStatus }}</p>
                    <p>{{ scope.row.settlement_amount | formatF2Y }}</p>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100, 200]"
            :style="{
                display: 'flex',
                justifyContent: 'flex-end',
                marginRight: '20px',
            }"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes,prev, pager, next, jumper"
        ></el-pagination>
    </m-card>
</template>

<script>
import {
    getSupplierBillDetail,
    exportSupplierBillDetail,
} from '@/api/adminAccountApply';
export default {
    data() {
        return {
            formData: {
                withdrawal_status: '', // 提现状态
                remit_status: '', // 打款状态
                times: '',
                timee: '',
            },
            dataInfo: {},
            tableData: [],
            withdrawal_id: null,
            page: 1,
            pageSize: 10,
            total: 0,

            withdrawal_detail: null,
        };
    },
    filters: {
        // 处理金额展示格式，千位 0,000:00
        toMoney(fen) {
            if (fen) {
                if (isNaN(fen)) {
                    this.$message.error('金额中含有不能识别的字符');
                    return;
                }
                var num = fen;
                num = fen * 0.01;
                num += '';
                var reg =
                    num.indexOf('.') > -1
                        ? /(\d{1,3})(?=(?:\d{3})+\.)/g
                        : /(\d{1,3})(?=(?:\d{3})+$)/g;
                num = num.replace(reg, '$1');

                var f = parseFloat(num);
                var f = Math.round(num * 100) / 100;
                var s = f.toString();
                var rs = s.indexOf('.');
                if (rs < 0) {
                    rs = s.length;
                    s += '.';
                }
                while (s.length <= rs + 2) {
                    s += '0';
                }
                num = s;

                num = typeof num == 'string' ? parseFloat(num) : num; //判断是否是字符串如果是字符串转成数字
                num = num.toFixed(2); //保留两位
                num = parseFloat(num); //转成数字
                num = num.toLocaleString(); //转成金额显示模式
                //判断是否有小数
                if (num.indexOf('.') == -1) {
                    num = num + '.00';
                } else {
                    num = num.split('.')[1].length < 2 ? num + '0' : num;
                }
                return num; //返回的是具有千分位格式并保留2位小数的字符串
            } else {
                return '0.00';
            }
        },
        // 结算状态
        settlementType(type) {
            let str = '';
            switch (type) {
                case 0:
                    str = '订单金额';
                    break;
                case 1:
                    str = '成本价 + 运费';
                    break;
                case 2:
                    str = '订单金额';
                    break;
                case 3:
                    str = '-';
                    break;
                default:
                    break;
            }
            return str;
        },
        withdrawalStatus(status) {
            let str = '';
            switch (status) {
                case 0:
                    str = '未提现';
                    break;
                case 1:
                    str = '驳回';
                    break;
                case 2:
                    str = '已申请';
                    break;
                case 3:
                    str = '通过';
                    break;
                case 4:
                    str = '无效';
                    break;
                default:
                    break;
            }
            return str;
        },
        remitStatus(status) {
            let str = '';
            switch (status) {
                case 0:
                    str = '待打款';
                    break;
                case 1:
                    str = '已打款';
                    break;
                case 2:
                    str = '打款中';
                    break;
                default:
                    break;
            }
            return str;
        },
    },
    mounted() {
        if (this.$route.query.type) {
            this.withdrawal_detail = this.$route.query.type;
        }
        if (this.$route.query.id) {
            this.withdrawal_id = this.$route.query.id;
        }
        // 获取 账单明细
        this.getSupplierBillDetail();
    },
    methods: {
        // 整理数据
        paramsFun() {
            let params = {
                page: this.page,
                pageSize: this.pageSize,
            };
            if (this.withdrawal_id) {
                params.withdrawal_id = this.withdrawal_id;
            }
            // 提现状态
            if (this.formData.withdrawal_status !== '') {
                params.withdrawal_status = parseInt(
                    this.formData.withdrawal_status,
                );
            }
            // 打款状态
            if (this.formData.remit_status !== '') {
                params.remit_status = parseInt(this.formData.remit_status);
            }
            // 时间
            if (this.formData.times) {
                params.times = this.formData.times;
            }
            if (this.formData.timee) {
                params.timee = this.formData.timee;
            }

            // 搜索
            if (this.withdrawal_detail) {
                params.withdrawal_detail = this.withdrawal_detail;
            }
            return params;
        },
        // 导出
        async exportSupplierBill() {
            let params = this.paramsFun();
            let res = await exportSupplierBillDetail(params);
            if (res.code === 0) {
                window.open(this.$path + '/' + res.data.link);
            }
        },
        // 获取 账单明细
        async getSupplierBillDetail() {
            let params = this.paramsFun();
            let res = await getSupplierBillDetail(params);
            if (res.code === 0) {
                this.total = res.data.total;
                this.tableData = res.data.list;
                this.dataInfo = res.data.data;
            }
        },
        // 查询
        onSubmit() {
            this.page = 1;
            this.getSupplierBillDetail();
        },
        // 重制搜索
        ClearSearch() {
            this.formData = {
                withdrawal_status: '', // 提现状态
                remit_status: '', // 打款状态
                times: '',
                timee: '',
            };
            this.withdrawal_id = null;
            this.page = 1;
            this.pageSize = 10;

            this.withdrawal_detail = null;
            this.getSupplierBillDetail();
        },
        // 分页
        handleCurrentChange(page) {
            this.page = page;
            this.getSupplierBillDetail();
        },
        handleSizeChange(pageSize) {
            this.pageSize = pageSize;
            this.getSupplierBillDetail();
        },
    },
};
</script>

<style lang="scss" scoped>
.grid {
    background: rgb(245, 248, 254);
    border-radius: 12px;
    padding: 17px 22px;

    .grid-content .numbers-p {
        margin-bottom: 10px;
        font-size: 20px;
    }
}

.grid-content {
    /* border-radius: 4px; */
    min-height: 36px;
}
</style>
