<template>
    <el-dialog
        title="新增银行卡"
        :visible="isShow"
        width="1200px"
        
        :show-close="false"
        :before-close="handleClose"
        append-to-body
    >
        <div class="ml_50">
            <el-form label-width="123px" ref="form" :rules="rules" label-position="left" :model="formData">
                <el-form-item label="账户类型" prop="bank_type">
                    <el-radio-group class="m-radio-group" v-model="formData.bank_type">
                        <el-radio :label="1">对私</el-radio>
                        <el-radio :label="2">对公</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item prop="account_name">
                    <span slot="label">{{formData.bank_type === 1 ? '开户名' : '公司名'}}</span>
                    <el-input class="w365" v-model="formData.account_name" placeholder="请输入姓名"></el-input>
                </el-form-item>
                <el-form-item>
                    <span slot="label">
                        <span class="color-red">*</span>证件号
                    </span>
                    <div class="f w365">
                        <el-form-item class="zj-item n-mb">
                            <el-select v-model="formData.bank_type" disabled>
                                <el-option label="身份证" :value="1"></el-option>
                                <el-option label="营业执照" :value="2"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item class="f1 n-mb" prop="card_id">
                            <el-input v-model="formData.card_id"></el-input>
                        </el-form-item>
                    </div>
                    <p class="hint-p" v-if="formData.bank_type === 1">请输入在银行办理该卡时使用的证件号</p>
                    <p class="hint-p" v-if="formData.bank_type === 2">请输入营业执照号</p>
                </el-form-item>
                <el-form-item label="身份证正面" prop="id_card_z" v-if="formData.bank_type === 1">
                    <el-upload
                        class="avatar-uploader"
                        :show-file-list="false"
                        :action="path+'/fileUploadAndDownload/upload'"
                        :headers="{'x-token':token}"
                        :on-success="handleWeChatIDCardZImgSuccess"
                        :before-upload="$fn.beforeAvatarUpload"
                        accept=".jpg, .jpeg, .png, .gif, .bmp, .pdf, .JPG, .JPEG, .PBG, .GIF, .BMP, .PDF"
                    >
                        <img v-if="formData.id_card_z" :src="formData.id_card_z" class="avatar" />
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                </el-form-item>
                <el-form-item label="身份证反面" prop="id_card_f" v-if="formData.bank_type === 1">
                    <el-upload
                        class="avatar-uploader"
                        :show-file-list="false"
                        :action="path+'/fileUploadAndDownload/upload'"
                        :headers="{'x-token':token}"
                        :on-success="handleWeChatIDCardFImgSuccess"
                        :before-upload="$fn.beforeAvatarUpload"
                        accept=".jpg, .jpeg, .png, .gif, .bmp, .pdf, .JPG, .JPEG, .PBG, .GIF, .BMP, .PDF"
                    >
                        <img v-if="formData.id_card_f" :src="formData.id_card_f" class="avatar" />
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                </el-form-item>
                <el-form-item label="营业执照" prop="business_license" v-if="formData.bank_type === 2">
                    <el-upload
                        class="avatar-uploader"
                        :show-file-list="false"
                        :action="path+'/fileUploadAndDownload/upload'"
                        :headers="{'x-token':token}"
                        :on-success="handleWeChatBusinessImgSuccess"
                        :before-upload="$fn.beforeAvatarUpload"
                        accept=".jpg, .jpeg, .png, .gif, .bmp, .pdf, .JPG, .JPEG, .PBG, .GIF, .BMP, .PDF"
                    >
                        <img
                            v-if="formData.business_license"
                            :src="formData.business_license"
                            class="avatar"
                        />
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                </el-form-item>
                <el-form-item label="银行卡号" prop="bank_account" class="bank-box">
                    <el-input v-model="formData.bank_account" class="w365" placeholder="请输入银行卡号"></el-input>
                </el-form-item>
                <el-form-item label="所属银行" prop="bank_name">
                    <el-select
                        filterable
                        clearable
                        v-model="formData.bank_name"
                        class="w365"
                        placeholder="请选择所属银行"
                    >
                        <el-option
                            v-for="item in bank_options"
                            :key="item.id"
                            :label="item.text"
                            :value="item.text"
                        ></el-option>
                    </el-select>
                    <p class="hint-p">输入银行名即可查询</p>
                </el-form-item>
                <el-form-item :error="areaError" class="pull-box">
                    <span slot="label">
                        <span class="color-red">*</span>所在地区
                    </span>
                    <div>
                        <el-input
                            @focus="openPullArea"
                            readonly
                            class="w365"
                            placeholder="请选择地区"
                            suffix-icon="el-icon-arrow-down"
                            v-model="addressStr"
                        ></el-input>
                        <el-collapse-transition>
                            <div class="pull-area-box" v-show="pullAreaIsShow">
                                <div class="closePullArea" @click="closePullArea"></div>
                                <el-tabs stretch v-model="pullAreaActiveName">
                                    <el-tab-pane label="省份" name="province">
                                        <p v-for="item in provinceList" :key="item.id">
                                            <a @click="selectProvince(item)" href="javascript:;">
                                                {{
                                                item.name
                                                }}
                                            </a>
                                        </p>
                                    </el-tab-pane>
                                    <el-tab-pane label="城市" name="city">
                                        <p v-for="item in cityList" :key="item.id">
                                            <a @click="selectCity(item)" href="javascript:;">
                                                {{
                                                item.name
                                                }}
                                            </a>
                                        </p>
                                    </el-tab-pane>
                                    <el-tab-pane label="区县" name="county">
                                        <p v-for="item in countyList" :key="item.id">
                                            <a @click="selectCounty(item)" href="javascript:;">
                                                {{
                                                item.name
                                                }}
                                            </a>
                                        </p>
                                    </el-tab-pane>
                                </el-tabs>
                            </div>
                        </el-collapse-transition>
                    </div>
                </el-form-item>
                <el-form-item label="银行分行" prop="branch" v-if="formData.bank_type === 1">
                    <el-input v-model="formData.branch" placeholder="请输入银行分行" class="w365"></el-input>
                </el-form-item>
                <el-form-item class="mt20" label="设置默认" prop="is_default">
                    <el-radio-group class="m-radio-group" v-model="formData.is_default">
                        <el-radio :label="1">是</el-radio>
                        <el-radio :label="0">否</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item
                    v-if="formData.bank_type===2"
                    label="联行号"
                    prop="bank_channel_no"
                    :rules="[{ required: true, message: '请输入联行号'}]"
                >
                    <el-input v-model="formData.bank_channel_no" placeholder="请输入联行号" class="w365"></el-input>
                </el-form-item>
            </el-form>
        </div>
        <div class="f fac fjc">
            <el-button type="primary" @click="save">保存</el-button>
            <el-button @click="handleClose">关闭</el-button>
        </div>
    </el-dialog>
</template>

<script>
import verify from "@/utils/verify";
import bankList from '@/json/bank';
import { mapGetters } from 'vuex';
import { getRegion } from '@/api/region';
import { createBank,saveUserBank } from "@/api/adminAccountApply"
export default {
    data() {
        return {
            pullAreaIsShow: false,
            pullAreaActiveName: 'province',
            provinceList: [],
            cityList: [],
            countyList: [],
            addressStr: '',
            selectData: {
                province_id: '', // 省
                province: '',
                city_id: '', // 市
                city: '',
                county_id: '', // 区
                county: '',
            },

            isShow: false,
            formData: {
                bank_type: 1, // 对公，对私
                account_name: '', // 开户名
                card_id: '', // 身份证
                id_card_z: '', // 身份证正面
                id_card_f: '', // 身份证反面
                business_license: '', // 营业执照
                bank_account: '', // 银行卡号
                branch: '', // 分行
                is_default: 0, // 默认

                province_id: '',
                province: '',
                city_id: '',
                city: '',
                county_id: '',
                county: ''
            },
            bank_options: bankList,
            areaError: '',
            path: this.$path,
            rules: {
                account_name: {
                    required: true,
                    validator: (rule, value, callback) => {
                        switch (this.formData.bank_type) {
                            case 1:
                                !value
                                    ? callback(new Error('请输入开户名'))
                                    : callback();
                                break;
                            case 2:
                                !value
                                    ? callback(new Error('请输入公司名'))
                                    : callback();
                                break;
                        }
                    },
                    trigger: 'blur',
                },
                card_id: {
                    required: true,
                    validator: (rule, value, callback) => {
                        switch (this.formData.bank_type) {
                            case 1: // 身份证
                                if (verify.checkIDCard(value)) {
                                    callback();
                                } else {
                                    callback(new Error('身份证格式不正确'));
                                }
                                break;
                            case 2: // 营业执照
                                if (verify.checkBusiness(value)) {
                                    callback();
                                } else {
                                    callback(new Error('营业执照格式不正确'));
                                }
                                break;
                        }
                    },
                    trigger: 'blur',
                },
                bank_account: {
                    required: true,
                    // message:"请输入银行卡号",
                    validator: (rule, value, callback) => {
                        if(verify.checkNumber(value)){
                            callback()
                        }else{
                            callback('银行卡号格式不正确')
                        }
                    },
                    trigger: 'blur',
                },
                bank_name: {
                    required: true,
                    message: '请选择所属银行',
                    trigger: 'change',
                },
                branch: {
                    required: true,
                    message: '请输入银行分行',
                    trigger: 'blur',
                },
                bank_type: {
                    required: true,
                    message: '请选择账户类型',
                    trigger: 'change',
                },
                id_card_f: {
                    required: true,
                    validator: (rule, value, callback) => {
                        if (this.formData.bank_type === 1) {
                            !value
                                ? callback(new Error('请上传身份证反面'))
                                : callback();
                        } else {
                            callback();
                        }
                    },
                    trigger: 'blur',
                },
                id_card_z: {
                    required: true,
                    validator: (rule, value, callback) => {
                        if (this.formData.bank_type === 1) {
                            !value
                                ? callback(new Error('请上传身份证正面'))
                                : callback();
                        } else {
                            callback();
                        }
                    },
                    trigger: 'blur',
                },
                business_license: {
                    required: true,
                    validator: (rule, value, callback) => {
                        if (this.formData.bank_type === 2) {
                            !value
                                ? callback(new Error('请上传营业执照'))
                                : callback();
                        } else {
                            callback();
                        }
                    },
                },
            },

            type: '',
        };
    },
    computed: {
        ...mapGetters('user', ['userInfo', 'token']),
    },
    mounted() {},
    methods: {
        info(item) {
            this.isShow = true;
            if (item.id) {
                this.selectData = {
                    province_id: item.province_id,
                    province: item.province,
                    city_id: item.city_id,
                    city: item.city,
                    county_id: item.county_id,
                    county: item.county
                }
                this.addressStr = item.province + ' ' + item.city + ' ' + item.county;
                this.formData = item;
                this.type = '编辑'
            }
        },
        checkArea() {
            this.areaError = this.formData.province_id ? "" : "请选择省份";
            this.areaError = this.formData.city_id ? "" : "请选择城市";
            this.areaError = this.formData.county_id ? "" : "请选择区/县";
        },
        // 保存
        async save() {
            this.formData.province_id = this.selectData.province_id
            this.formData.province = this.selectData.province
            this.formData.city_id = this.selectData.city_id
            this.formData.city = this.selectData.city
            this.formData.county_id = this.selectData.county_id
            this.formData.county = this.selectData.county
            this.checkArea();
            this.$refs.form.validate((valid) => {
                if (valid) {
                    if(this.formData.province_id  !== "" && this.formData.city_id !== "" && this.formData.county_id !== "") {
                        if (this.type === '编辑') {
                            saveUserBank(this.formData).then(res => {
                                if (res.code === 0) {
                                    this.$message.success(res.msg);
                                    this.handleClose();
                                    this.$emit('createBank')
                                }
                            })
                        } else {
                            createBank(this.formData).then(res => {
                                if (res.code === 0) {
                                    this.$message.success(res.msg);
                                    this.handleClose();
                                    this.$emit('createBank')
                                }
                            })
                        }
                    }else {
                        this.$message.error("请填写完整的地区信息")
                    }
                } else {
                    return false
                }
            })
        },
        // 关闭新增银行卡
        handleClose() {
            this.pullAreaIsShow = false;
            this.isShow = false;
            this.pullAreaActiveName = 'province';
            this.provinceList = [];
            this.cityList = [];
            this.countyList = [];
            this.addressStr = '';
            this.selectData = {
                province_id: '', // 省
                province: '',
                city_id: '', // 市
                city: '',
                county_id: '', // 区
                county: '',
            }
            this.formData = {
                bank_type: 1, // 对公，对私
                account_name: '', // 开户名
                card_id: '', // 身份证
                id_card_z: '', // 身份证正面
                id_card_f: '', // 身份证反面
                business_license: '', // 营业执照
                bank_account: '', // 银行卡号
                branch: '', // 分行
                is_default: 0, // 

                province_id: '',
                province: '',
                city_id: '',
                city: '',
                county_id: '',
                county: ''
            }
            this.areaError = '';

            this.type = '';
            this.$refs.form.clearValidate();
        },
        handleImgsSuccess(res) {
            this.imgsFileList.push({
                src: res.data.file.url,
                url: res.data.file.url,
            });
        },
        //身份证正面
        handleWeChatIDCardZImgSuccess(res) {
            this.formData.id_card_z = res.data.file.url;
        },
        //身份证反面
        handleWeChatIDCardFImgSuccess(res) {
            this.formData.id_card_f = res.data.file.url;
        },
        // 营业执照
        handleWeChatBusinessImgSuccess(res) {
            this.formData.business_license = res.data.file.url;
        },

        openPullArea() {
            this.pullAreaIsShow = true;
            this.getProvince();
        },
        closePullArea() {
            this.pullAreaIsShow = false;
        },
        // 获取所有省
        async getProvince() {
            let res = await getRegion({ parent_id: 0 });
            if (res.code === 0) {
                this.provinceList = res.data.list;
            }
        },
        // 获取市
        async getCity(pid) {
            let res = await getRegion({ parent_id: pid });
            if (res.code === 0) {
                this.cityList = res.data.list;
            }
        },
        // 获取区
        async getCounty(pid) {
            let res = await getRegion({ parent_id: pid });
            if (res.code === 0) {
                this.countyList = res.data.list;
            }
        },
        //省份选择处理
        selectProvince(provinceItem) {
            this.selectData.province_id = provinceItem.id;
            this.selectData.province = provinceItem.name;

            this.selectData.city_id = '';
            this.selectData.city = '';
            this.selectData.county_id = '';
            this.selectData.county = '';
            this.cityList = [];
            this.countyList = [];
            this.pullAreaActiveName = 'city';
            this.getCity(provinceItem.id);
            this.assembleAddress();
        },
        // 市选中处理
        selectCity(item) {
            this.selectData.city_id = item.id;
            this.selectData.city = item.name;
            this.selectData.county_id = '';
            this.selectData.county = '';
            this.countyList = [];
            this.pullAreaActiveName = 'county';
            this.getCounty(item.id);
            this.assembleAddress();
        },
        // 区县选中处理
        selectCounty(item) {
            this.selectData.county_id = item.id;
            this.selectData.county = item.name;
            this.assembleAddress();
            this.closePullArea();
        },
        // 组装省市区
        assembleAddress() {
            if (this.selectData.province) {
                this.addressStr = this.selectData.province;
            }
            if (this.selectData.city) {
                this.addressStr =
                    this.selectData.province + ' ' + this.selectData.city ||
                    '' + ' ';
            }
            if (this.selectData.county) {
                this.addressStr =
                    this.selectData.province +
                    ' ' +
                    this.selectData.city +
                    ' ' +
                    this.selectData.county;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
@import '@/style/base.scss';

::v-deep .avatar-uploader {
    .el-upload-list {
        li {
            width: 178px;
            height: 178px;
        }
    }

    .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        width: 178px;
        height: 178px;

        img {
            width: 178px;
            height: 178px;
        }
    }
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;

    line-height: 178px;
    text-align: center;
}

.w365 {
    width: 365px;
}

.zj-item {
    width: 116px;
    margin-right: 19px;
}

.n-mb {
    margin-bottom: 0;
}

.pull-box {
    position: relative;

    .pull-area-box {
        width: 600px;
        position: absolute;
        margin-top: 9px;
        box-shadow: 0px 2px 7px 3px rgba(1, 2, 16, 0.03);
        border-radius: 10px;
        z-index: 999;
        background-color: white;
        padding: 13px 15px;

        .closePullArea {
            position: fixed;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
        }

        ::v-deep .el-tabs {
            .el-tabs__header {
                .el-tabs__nav-wrap {
                    &::after {
                        display: none;
                    }

                    .el-tabs__nav {
                        width: 70%;

                        .el-tabs__item {
                            &:hover {
                                color: #303133;
                            }

                            &.is-active {
                                color: #303133;
                            }
                        }

                        .el-tabs__active-bar {
                            background-color: #175bd4;
                        }
                    }
                }
            }

            .el-tabs__content {
                max-height: 300px;
                overflow-y: scroll;

                .el-tab-pane {
                    display: grid;
                    grid-template-columns: repeat(5, 110px);
                    grid-row-gap: 13px;
                    grid-column-gap: 13px;
                    line-height: normal;
                    p {
                        white-space: nowrap;
                        overflow: hidden;
                        &:hover {
                            a {
                                color: #175bd4;
                            }
                        }
                    }
                }
            }

            /*滚动条样式*/
            &::-webkit-scrollbar {
                /*滚动条整体样式*/
                width: 4px; /*高宽分别对应横竖滚动条的尺寸*/
                height: 4px;
            }

            &::-webkit-scrollbar-thumb {
                /*滚动条里面小方块*/
                border-radius: 5px;
                -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
                background: rgba(242, 242, 242, 0.2);
            }

            &::-webkit-scrollbar-track {
                /*滚动条里面轨道*/
                -webkit-box-shadow: inset 0 0 5px rgba(242, 242, 242, 0.2);
                border-radius: 0;
                background: white;
            }
        }
    }
}

.hint-p {
  margin-top: 14px;
  color: #AAAAAA;
  line-height: normal;
}
</style>