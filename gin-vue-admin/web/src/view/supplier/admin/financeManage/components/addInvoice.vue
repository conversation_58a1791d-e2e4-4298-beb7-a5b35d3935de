<template>
    <div>
        <el-dialog
            :title="title"
            :visible="isShow"
            width="900px"
            :before-close="handleClose"
            append-to-body
        >
            <div class="ml_50">
                <p>
                    开票金额:
                    <span style="font-size: 22px">{{ income_amount | toMoney }}元</span>
                </p>
                <p>发票类型: {{ formData.invoice_type | invoiceType }}</p>
                <p>单位名称: {{ formData.company_name }}</p>
                <p>纳税人识别号: {{ formData.tax_id }}</p>
                <p>注册地址: {{ formData.registered_addr }}</p>
                <p>注册电话: {{ formData.registered_tel }}</p>
                <p>开户银行: {{ formData.bank_name }}</p>
                <p>上传发票:</p>
                <div class="f fw mb_10">
                    <div v-for="(item,index) in billImg" :key="item.id">
                        <div class="billImgBox">
                            <m-image
                                v-if="item.type === 1"
                                :src="item.url"
                                style="width: 148px;height: 148px;"
                            />
                        </div>
                        <div class="f fac fjc">
                            <el-button type="text" @click="openMaxImgDialog(item)">查看</el-button>
                            <el-button type="text" v-if="title === '上传发票'" @click="removeGoodsGalleryList(index)" class="ml_20">删除</el-button>
                        </div>
                    </div>

                    <el-upload
                        class="avatar-uploader"
                        multiple
                        list-type="picture-card"
                        :action="path+'/fileUploadAndDownload/upload'"
                        :headers="{'x-token':token}"
                        :on-success="handleGoodsGallerySuccess"
                        :before-upload="$fn.beforeAvatarUpload"
                        :show-file-list="false"
                        accept=".jpg, .jpeg, .png, .gif, .bmp, .pdf, .JPG, .JPEG, .PBG, .GIF, .BMP, .PDF"
                        v-if="title === '上传发票'"
                    >
                        <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                </div>
                <p class="color-grap">支持上传图片、PDF格式，支持上传多张！</p>
                <p class="f">开票说明: <span class="ml_10" v-html="formData.invoice_desc"></span></p>
            </div>
            <div class="f fac fjc"> 
                <el-button type="primary" v-if="title === '上传发票'" @click="save">保 存</el-button>
                <el-button @click="handleClose">关 闭</el-button>
            </div>
        </el-dialog>

        <!-- 查看大图 -->
        <el-dialog
            :visible.sync="maxImgDialogIsShow"
            :close-on-click-modal="false"
            :append-to-body="true"
            width="850px"
            top="5vh"
        >
            <img :src="maxImgList.url" style="width: 800px; height: 800px" />
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="maxImgDialogIsShow = false;maxImgList=[]">关闭</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { createInvoice,getInvoice,getWithdrawalInvoice } from "@/api/adminAccountApply";
import { getInvoiceSetting } from '@/api/finance'
export default {
    data() {
        return {
            formData: {

            },
            title: '',
            id: '',
            income_amount: '',
            isShow: false,
            billImg: [], // 发票
            path: this.$path,

            maxImgDialogIsShow: false,
            maxImgList: {},
        };
    },
    computed: {
        ...mapGetters('user', ['userInfo', 'token']),
    },
    filters: {
        invoiceType(type) {
            let text = "";
            switch (type) {
                case  'VAT_SPECIAL':
                    text = "增值税专用发票"
                    break;
                case  'VAT_GENERAL':
                    text = "增值税普通发票"
                    break;
                default:
                    text = "-"
                    break;
            }
            return text
        },
        // 处理金额展示格式，千位 0,000:00 
        toMoney(fen) {
            if (fen) {
                if (isNaN(fen)) {
                    this.$message.error('金额中含有不能识别的字符');
                    return;
                }
                var num = fen;
                num = fen * 0.01;
                num += '';
                var reg =
                    num.indexOf('.') > -1
                        ? /(\d{1,3})(?=(?:\d{3})+\.)/g
                        : /(\d{1,3})(?=(?:\d{3})+$)/g;
                num = num.replace(reg, '$1');

                var f = parseFloat(num);
                var f = Math.round(num * 100) / 100;
                var s = f.toString();
                var rs = s.indexOf('.');
                if (rs < 0) {
                    rs = s.length;
                    s += '.';
                }
                while (s.length <= rs + 2) {
                    s += '0';
                }
                num = s;

                num = typeof num == 'string' ? parseFloat(num) : num; //判断是否是字符串如果是字符串转成数字
                num = num.toFixed(2); //保留两位
                num = parseFloat(num); //转成数字
                num = num.toLocaleString(); //转成金额显示模式
                //判断是否有小数
                if (num.indexOf('.') == -1) {
                    num = num + '.00';
                } else {
                    num = num.split('.')[1].length < 2 ? num + '0' : num;
                }
                return num; //返回的是具有千分位格式并保留2位小数的字符串
            } else {
                return '0.00';
            }
        },
    },
    mounted() {},
    methods: {
        info(res) {
            this.title = res.title;
            this.id = res.id;
            this.income_amount = res.income_amount;
            this.isShow = true;
            this.getInvoiceSetting();
            if (this.title === '查看发票') {
                if (res.type === 1) {
                    this.getWithdrawalInvoice() // 查看发票 总后台
                } else {
                    this.getInvoice(); // 查看发票
                }
                
            }
        },
        // 获取发票详情
        async getInvoiceSetting() {
            let res = await getInvoiceSetting();
            if (res.code === 0) {
                this.formData = res.data;
            }
        },
        // 获取发票 总后台
        async getWithdrawalInvoice() {
            let res = await getWithdrawalInvoice({id: this.id});
            if (res.code === 0) {
                this.billImg = [
                    {
                        type: 1,
                        src: res.data.invoice_url,
                        url: res.data.invoice_url,
                    }
                ]
            }
        },
        // 获取发票
        async getInvoice() {
            let res = await getInvoice({id: this.id});
            if (res.code === 0) {
                this.billImg = [
                    {
                        type: 1,
                        src: res.data.invoice_url,
                        url: res.data.invoice_url,
                    }
                ]
            }
        },
        // 保存
        async save() {
            if (this.title === '上传发票') {
                let params = {
                    withdrawal_id: this.id,
                    invoice_url: this.billImg[0].url,
                };
                let res = await createInvoice(params);
                if (res.code === 0) {
                    this.$message.success(res.msg);
                    this.handleClose();
                    this.$emit('save')
                }
            }
        },
        // 关闭
        handleClose() {
            this.isShow = false;
            this.formData = {};
            this.id = '';
            this.title = '';
        },
        // 图片
        handleGoodsGallerySuccess(res) {
            if (res.code === 0) {
                this.billImg = [
                    {
                        type: 1,
                        src: res.data.file.url,
                        url: res.data.file.url,
                    }
                ]
            } else {
                this.$message.error(res.msg);
            }
        },
        // 删除商品相册图片
        removeGoodsGalleryList(index) {
            this.$confirm('是否确认删除?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.billImg.splice(index, 1);
            })
            
        },
        // 查看图片
        openMaxImgDialog(imgs) {
            this.maxImgDialogIsShow = true;
            this.maxImgList = imgs;
        },
    },
};
</script>

<style lang="scss" scoped>
p {
    margin-bottom: 15px;
}

.billImgBox {
    width: 148px;
    height: 148px;
    position: relative;
    margin-right: 10px;
    margin-bottom: 10px;

    .selected {
        border: 1px solid red;
    }
    selsectbox {
        display: flex;
    }
    .tabsaa {
        width: 1000px;
        height: 70px;
        background-color: red;
    }
    .pl-10px {
        width: 200px;
        height: 100px;
    }
}
</style>
