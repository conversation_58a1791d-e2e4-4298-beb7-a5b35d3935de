<template>
    <div>
        <el-dialog
            title="我的银行卡"
            :visible="isShow"
            width="1200px"
            :show-close="false"
            :before-close="handleClose"
        >
            <div class="f fw" style="padding:0 20px 20px;">
                <template v-for="item in bankList">
                    <div class="mr_20 mb_20" style="width: 350px;">
                        <div class="bankCard-box">
                            <div class="bankCard-title f fac fjc">{{ item.bank_name }}</div>
                            <div class="bankCard-con">
                                <p>对公账户名称：{{ item.account_name }}</p>
                                <p>银行名称：{{ item.bank_name }}</p>
                                <p>对公银行账户：{{ item.bank_account }}</p>
                                <p>开户行（支行）：{{ item.branch }}</p>
                            </div>
                        </div>
                        <div class="f fac fjc mt_20">
                            <el-button @click="edit(item)">编辑</el-button>
                            <el-button class="ml_10" @click="del(item)">删除</el-button>
                        </div>
                    </div>
                </template>
                <div class="bankCard-box f fac fjc color_box" @click="addBankCard">+</div>
            </div>
            <div class="f fac fjc">
                <el-button type="primary" @click="handleClose">关闭</el-button>
            </div>
        </el-dialog>
        <addBankCardDialog ref="addBankCardDialog" @createBank='bankListFun'></addBankCardDialog>
    </div>
</template>

<script>
import addBankCardDialog from './addBankCardDialog.vue'
import { bankList,deleteUserBank} from '@/api/adminAccountApply'
export default {
    components: {addBankCardDialog},
    data() {
        return {
            isShow: false,
            bankList: [],
        };
    },
    mounted() {
        
    },
    methods: {
        info() {
            this.isShow = true;
            this.bankListFun();
        },
        // 获取银行卡
        async bankListFun() {
            let res = await bankList();
            if (res.code === 0) {
                this.bankList = res.data;
            }
        },
        edit(item) {
            this.$refs.addBankCardDialog.info(item);
        },
        // 删除
        del(item) {
            this.$confirm("确定删除吗?", "提示", {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                let res = await deleteUserBank({id: item.id})
                if (res.code === 0) {
                    this.$message.success(res.msg);
                    this.bankListFun();
                }
            })
        },
        // 关闭
        handleClose() {
            this.isShow = false;
        },
        // 添加银行卡
        addBankCard() {
            this.$refs.addBankCardDialog.info({id: 0})
        },
    },
};
</script>

<style lang="scss" scoped>
.bankCard-box {
    width: 350px;
    height: 200px;
    border-radius: 8px;
    overflow: hidden;
    // box-shadow: 5px 8px 8px 5px red;
    box-shadow: 0px -3px 5px 0px rgba(0, 52, 107, 0.04),
        -2px 0px 3px 0px rgba(0, 52, 107, 0.04),
        1px 0px 3px 0px rgba(0, 52, 107, 0.04),
        0px 0px 5px 0px rgba(0, 52, 107, 0.04);

    .bankCard-title {
        width: 100%;
        height: 50px;
        background-color: #5ea3f7;
        color: #fff;
    }

    .bankCard-con {
        padding: 20px;

        p {
            margin-bottom: 17px;
            font-size: 12px;
        }
    }
}

.color_box {
    background-color: #f9fbfd;
    cursor: pointer;
}
</style>