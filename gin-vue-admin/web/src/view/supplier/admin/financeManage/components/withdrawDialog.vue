<template>
    <div>
        <el-dialog
            title="提现"
            :visible="isShow"
            width="800px"
            :before-close="handleClose"
            append-to-body
        >
            <el-form :model="formData" label-width="180px">
                <el-form-item label="当前可提现账单金额:">{{ dataInfo.SettlementBalance | toMoney }}</el-form-item>
                <el-form-item label="提现至:">
                    <div class="f fac">
                        <el-select
                            class="w350"
                            filterable
                            clearable
                            v-model="formData.bankID"
                        >
                            <el-option v-for="item in bankList" :key='item.id' :value="item.id" :label="item.bank_name + '(' + item.bank_account.slice(-4) + ')'"></el-option>
                        </el-select>
                        <el-button type="text" class="ml_10" @click="addBankCard()">新增银行卡</el-button>
                    </div>
                </el-form-item>
                <el-form-item label="提现金额:">
                    <div class="f fac">
                        {{ dataInfo.SettlementBalance | toMoney }}
                        <el-button v-if='dataInfo.SettlementBalance !== 0' type="text" class="ml_10" @click="lookDillingDetails(item)">查看账单明细</el-button>
                    </div>
                </el-form-item>
                <el-form-item label="提现手续费:">{{ dataInfo.WithdrawalTotalFee | toMoney }}</el-form-item>
                <p class="hint-p ml_40 red" v-if="is_invoice">发起提现后，后台审核，通过审核后需要上传发票才能打款；开票金额以最终审核为准，请留意审核结果！</p>
            </el-form>
            <div class="f fac fjc mt_20">
                <el-button type="primary" @click="withdraw">提现</el-button>
                <el-button @click="handleClose">关闭</el-button>
            </div>
        </el-dialog>
        <addBankCardDialog ref="addBankCardDialog" @createBank='getBankList'></addBankCardDialog>
    </div>
</template>

<script>
import addBankCardDialog from './addBankCardDialog.vue';
import { bankList,withdraw } from '@/api/adminAccountApply'
import {
    getSupplierWithdrawal
} from '@/api/finance'; //  此处请自行替换地址
export default {
    components: { addBankCardDialog },
    data() {
        return {
            isShow: false,
            formData: {
                bankID: null,
            },
            bankList: [],
            dataInfo: {},
            is_invoice: ''
        };
    },
    filters: {
        // 处理金额展示格式，千位 0,000:00 
        toMoney(fen) {
            if (fen) {
                if (isNaN(fen)) {
                    this.$message.error('金额中含有不能识别的字符');
                    return;
                }
                var num = fen;
                num = fen * 0.01;
                num += '';
                var reg =
                    num.indexOf('.') > -1
                        ? /(\d{1,3})(?=(?:\d{3})+\.)/g
                        : /(\d{1,3})(?=(?:\d{3})+$)/g;
                num = num.replace(reg, '$1');

                var f = parseFloat(num);
                var f = Math.round(num * 100) / 100;
                var s = f.toString();
                var rs = s.indexOf('.');
                if (rs < 0) {
                    rs = s.length;
                    s += '.';
                }
                while (s.length <= rs + 2) {
                    s += '0';
                }
                num = s;

                num = typeof num == 'string' ? parseFloat(num) : num; //判断是否是字符串如果是字符串转成数字
                num = num.toFixed(2); //保留两位
                num = parseFloat(num); //转成数字
                num = num.toLocaleString(); //转成金额显示模式
                //判断是否有小数
                if (num.indexOf('.') == -1) {
                    num = num + '.00';
                } else {
                    num = num.split('.')[1].length < 2 ? num + '0' : num;
                }
                return num; //返回的是具有千分位格式并保留2位小数的字符串
            } else {
                return '0.00';
            }
        },
    },
    mounted() {},
    methods: {
        // 提现
        withdraw() {
            this.$confirm('是否确认提现?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async ()=> {
                if (!this.formData.bankID) {
                    this.$message.error('请选择银行卡')
                    return
                }
                let params = {
                    user_bank_id: this.formData.bankID,
                    withdrawal_mode: 1,
                    withdrawal_type: 1,
                    withdrawal_amount: this.dataInfo.SettlementBalance
                };
                let res = await withdraw(params)
                if (res.code === 0) {
                    this.$message.success(res.msg);
                    this.handleClose();
                    this.$emit('withdrawSuccess')
                }
            })
        },
        // 关闭
        handleClose() {
            this.bankList = [];
            this.dataInfo = {};
            this.is_invoice = '';
            this.formData = {
                bankID: null,
            };
            this.isShow = false;
        },
        // 获取设置
        async getSetting() {
            let res = await getSupplierWithdrawal();
            if (res.code === 0) {
                this.is_invoice = res.data.is_invoice;
            }
        },
        info(dataInfo) {
            this.isShow = true;
            this.dataInfo = dataInfo;
            console.log(this.dataInfo);
            
            this.getBankList();
            this.getSetting();
        },
        async getBankList() {
            let res = await bankList();
            if (res.code === 0) {
                this.bankList = res.data;
                this.formData.bankID =  this.bankList.find(item => item.is_default === 1).id
            }
        },
        lookDillingDetails() {
            this.$_blank('/layout/financeManage/billingDetails',{type:1});
        },
        // 添加银行卡
        addBankCard() {
            this.$refs.addBankCardDialog.info({id: 0});
        },
    },
};
</script>

<style lang="scss" scoped>
.w350 {
    width: 350px;
}
.red {
    color: red;
}
</style>