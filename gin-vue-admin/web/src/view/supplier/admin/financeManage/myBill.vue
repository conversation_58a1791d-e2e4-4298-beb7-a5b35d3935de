<template>
    <div class="big" style="padding: 0">
        <m-card style="border-radius: 8px">
            <div class="f fjsb fac">
                <h1>我的账单</h1>
                <div class="f">
                    <el-button type="text" @click="withdraw">提现</el-button>
                    <el-button class="ml_20" type="text" @click="handleInfo">数据指标说明</el-button>
                    <el-button class="ml_20" type="text" @click="openBankCard">我的银行卡</el-button>
                </div>
            </div>
            <div class="grid mt_10 f fac" style="justify-content: space-around;">
                <div class="grid-content" align="center">
                    <p class="numbers-p">{{ dataInfo.OrderTotalAmount | toMoney }}</p>
                    <p>订单总额（元）</p>
                </div>
                <div class="grid-content" align="center">
                    <p class="numbers-p">{{ dataInfo.BillTotalAmount | toMoney }}</p>
                    <p>账单总额（元）</p>
                </div>
                <div class="grid-content" align="center">
                    <p class="numbers-p">{{ dataInfo.TechnicalServicesTotalAmount | toMoney }}</p>
                    <p>技术服务费总额（元）</p>
                </div>
                <div class="grid-content" align="center">
                    <p class="numbers-p">{{ dataInfo.WithdrawnTotalAmount | toMoney }}</p>
                    <p>已提现金额（元）</p>
                </div>
                <div class="grid-content" align="center">
                    <p class="numbers-p">{{ dataInfo.NotWithdrawn | toMoney }}</p>
                    <p>未提现金额（元）</p>
                </div>
                <div class="grid-content" align="center">
                    <p class="numbers-p">{{ dataInfo.WithdrawingInProgress | toMoney }}</p>
                    <p>提现中金额（元）</p>
                </div>
                <div class="grid-content" align="center">
                    <p class="numbers-p">{{ dataInfo.WithdrawalTotalFee | toMoney }}</p>
                    <p>提现手续费（元）</p>
                </div>
                <div class="grid-content" align="center">
                    <p class="numbers-p">{{ dataInfo.InvalidBill | toMoney }}</p>
                    <p>无效账单金额（元）</p>
                </div>
                <div class="grid-content" align="center">
                    <p class="numbers-p">{{ dataInfo.PaidAmount | toMoney }}</p>
                    <p>已打款金额（元）</p>
                </div>
            </div>
        </m-card>
        <m-card class="mt25" style="border-radius: 8px">
            <h1>提现记录</h1>
            <el-form :model="formData" class="search-term mt_20" label-width="80px" inline>
                <el-form-item label>
                    <el-input
                        placeholder="请输入"
                        v-model="formData.order_sn"
                        class="line-input"
                        clearable
                    >
                        <span slot="prepend">提现编号</span>
                    </el-input>
                </el-form-item>
                <el-form-item label>
                    <div class="line-input">
                        <div class="line-box">
                            <span>提现方式</span>
                        </div>
                        <el-select
                            class="w100"
                            filterable
                            clearable
                            v-model="formData.withdrawal_mode"
                        >
                            <el-option :value="1" label="手动打款"></el-option>
                            <el-option :value="2" label="汇聚"></el-option>
                        </el-select>
                    </div>
                </el-form-item>
                <el-form-item label>
                    <div class="line-input">
                        <div class="line-box">
                            <span>审核状态</span>
                        </div>
                        <el-select class="w100" filterable clearable v-model="formData.withdrawal_status">
                            <el-option :value="0" label="待审核"></el-option>
                            <el-option :value="1" label="通过"></el-option>
                            <el-option :value="2" label="无效"></el-option>
                            <el-option :value="3" label="驳回"></el-option>
                        </el-select>
                    </div>
                </el-form-item>
                <el-form-item label>
                    <div class="line-input">
                        <div class="line-box">
                            <span>开票状态</span>
                        </div>
                        <el-select
                            class="w100"
                            filterable
                            clearable
                            v-model="formData.invoice_status"
                        >
                            <el-option :value="0" label="无需开票"></el-option>
                            <el-option :value="1" label="待开票"></el-option>
                            <el-option :value="2" label="已开票"></el-option>
                        </el-select>
                    </div>
                </el-form-item>
                <el-form-item label>
                    <div class="line-input">
                        <div class="line-box">
                            <span>打款状态</span>
                        </div>
                        <el-select
                            class="w100"
                            filterable
                            clearable
                            v-model="formData.remit_status"
                        >
                            <el-option :value="0" label="待打款"></el-option>
                            <el-option :value="1" label="已打款"></el-option>
                            <el-option :value="2" label="打款中"></el-option>
                            <el-option :value="4" label="无需打款"></el-option>
                        </el-select>
                    </div>
                </el-form-item>
                <el-form-item>
                    <div class="line-input line-input-date">
                        <div
                            class="line-box"
                            style="display: flex;justify-content: center;min-width: 80px;"
                        >
                            <span>提现时间</span>
                        </div>
                        <div class="f fac">
                            <el-date-picker
                                v-model="formData.start_time"
                                type="date"
                                placeholder="选择日期"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                class="f1"
                            ></el-date-picker>
                            <p class="title-3">至</p>
                            <el-date-picker
                                v-model="formData.end_time"
                                type="date"
                                placeholder="选择日期"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                class="f1"
                            ></el-date-picker>
                        </div>
                    </div>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search">查询</el-button>
                    <el-button @click="exportSupplierBill">导出</el-button>
                    <el-button type="text" @click="clearSearch">重置搜索条件</el-button>
                </el-form-item>
            </el-form>
        </m-card>
        <m-card class="mt25" style="border-radius: 8px">
            <h1>提现列表</h1>
            <el-table :data="tableData" class="mt25" ref="table">
                <el-table-column label="提现时间" align="center">
                    <template slot-scope="scope">{{ scope.row.created_at | formatDate }}</template>
                </el-table-column>
                <el-table-column label="提现编号" prop="order_sn" align="center"></el-table-column>
                <el-table-column label="提现方式" align="center">
                    <template slot-scope="scope">
                        <span>{{ scope.row.withdrawal_mode | withdrawalMode }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="提现金额（元）" align="center">
                    <template slot-scope="scope">
                        <span>{{ scope.row.withdrawal_amount | toMoney }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="提现手续费（元）" align="center">
                    <template slot-scope="scope">
                        <span>{{ scope.row.poundage_amount | toMoney }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center">
                    <template slot="header">
                        <p>驳回金额（元）</p>
                        <p>无效金额（元）</p>
                    </template>
                    <template slot-scope="scope">
                        <p>{{ scope.row.settlement_amount | formatF2Y }}</p>
                        <p>{{ scope.row.withdrawal_operation_record.length > 0 ? scope.row.withdrawal_operation_record[0].invalid_amount : 0 | toMoney }}</p>
                    </template>
                </el-table-column>
                <el-table-column label="实际打款金额（元）" align="center">
                    <template slot-scope="scope">
                        <span>{{ scope.row.income_amount | toMoney }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="审核状态" align="center">
                    <template slot-scope="scope">
                        <p>{{ scope.row.withdrawal_status | formatWithdrawalStatus }}</p>
                    </template>
                </el-table-column>
                <el-table-column label="开票状态" align="center">
                    <template slot-scope="scope">
                        <p>{{ scope.row.invoice_status | invoiceStatus }}</p>
                    </template>
                </el-table-column>
                <el-table-column label="打款状态" align="center">
                    <template slot-scope="scope">
                        <p>{{ scope.row.remit_status | remitStatus }}</p>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center">
                    <template slot-scope="scope">
                        <el-button
                            v-if="scope.row.invoice_status === 2"
                            @click="examineInvoice(scope.row.id,scope.row.income_amount)"
                            type="text"
                        >查看发票</el-button>
                        <el-button
                            v-else-if="scope.row.invoice_status === 1"
                            @click="uploadInvoice(scope.row.id,scope.row.income_amount)"
                            type="text"
                        >上传发票</el-button>
                        <el-button @click="billingDetails(scope.row.id)" type="text">账单明细</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                :current-page="page"
                :page-size="pageSize"
                :page-sizes="[10, 30, 50, 100, 200]"
                :style="{
                display: 'flex',
                justifyContent: 'flex-end',
                marginRight: '20px',
            }"
                :total="total"
                @current-change="handleCurrentChange"
                @size-change="handleSizeChange"
                layout="total, sizes,prev, pager, next, jumper"
            ></el-pagination>
        </m-card>
        <el-dialog
            title="数据指标说明"
            :visible="isShow"
            width="1000px"
            :show-close="false"
            :before-close="handleClose"
        >
            <div class="ml_30 mr_20 m_dialog">
                <p>订单总额（元）:订单商品金额+运费，非实际支付金额，如果改价后，取改价后的金额！</p>
                <p>账单金额（元）:该供应商所有订单给供应商的结算金额！</p>
                <p>技术服务费总额（元）:该供应商所有订单平台扣点总额！</p>
                <p>已提现金额（元）:该供应商结算金额中，已经申请提现的金额！</p>
                <p>未提现金额（元）:该供应商结算金额中，未申请提现的金额！一般等于账单总额-已提现金额！</p>
                <p>提现中金额（元）:该供应商已经申请提现，但是未打款的金额！</p>
                <p>提现手续费（元）:供应商提现结算金额时扣除的提现手续费！包含已打款+提现中金额的账单手续费，审核无效的账单不收取提现手续费！如果提现中的金额已经审核了，审核中无效的账单提现手续费也相应扣除</p>
                <p>无效账单金额（元）:供应商申请提现，审核无效的账单总额！</p>
                <p>已打款金额（元）:供应商申请提现，已经完成打开的金额！</p>
            </div>
            <div class="f fac fjc">
                <el-button type="primary" class="but" @click="handleClose">关闭</el-button>
            </div>
        </el-dialog>

        <myBankCardDialog ref="myBankCardDialog"></myBankCardDialog>
        <addInvoice ref="addInvoice" @save="getSupplierBill"></addInvoice>
        <withdrawDialog ref="withdrawDialog" @withdrawSuccess='withdrawSuccess'></withdrawDialog>
    </div>
</template>

<script>
import {
    getSupplierBill,
    getTotalBill,
    exportSupplierBill,
} from '@/api/adminAccountApply';
import myBankCardDialog from './components/myBankCardDialog.vue';
import addInvoice from './components/addInvoice.vue';
import withdrawDialog from './components/withdrawDialog.vue';
export default {
    name: 'myBill',
    components: { myBankCardDialog, addInvoice, withdrawDialog },
    data() {
        return {
            dataInfo: {},
            formData: {
                order_sn: '', // 提现编号
                withdrawal_mode: '', // 提现方式
                withdrawal_status: '', // 审核状态
                invoice_status: '', // 开票状态
                remit_status: '', // 打款状态
                start_time: '', // 开始时间
                end_time: '', // 结束时间
            },
            tableData: [],

            page: 1,
            pageSize: 10,
            total: 0,
            isShow: false,
        };
    },
    filters: {
        // 处理金额展示格式，千位 0,000:00
        toMoney(fen) {
            if (fen) {
                if (isNaN(fen)) {
                    this.$message.error('金额中含有不能识别的字符');
                    return;
                }
                var num = fen;
                num = fen * 0.01;
                num += '';
                var reg =
                    num.indexOf('.') > -1
                        ? /(\d{1,3})(?=(?:\d{3})+\.)/g
                        : /(\d{1,3})(?=(?:\d{3})+$)/g;
                num = num.replace(reg, '$1');

                var f = parseFloat(num);
                var f = Math.round(num * 100) / 100;
                var s = f.toString();
                var rs = s.indexOf('.');
                if (rs < 0) {
                    rs = s.length;
                    s += '.';
                }
                while (s.length <= rs + 2) {
                    s += '0';
                }
                num = s;

                num = typeof num == 'string' ? parseFloat(num) : num; //判断是否是字符串如果是字符串转成数字
                num = num.toFixed(2); //保留两位
                num = parseFloat(num); //转成数字
                num = num.toLocaleString(); //转成金额显示模式
                //判断是否有小数
                if (num.indexOf('.') == -1) {
                    num = num + '.00';
                } else {
                    num = num.split('.')[1].length < 2 ? num + '0' : num;
                }
                return num; //返回的是具有千分位格式并保留2位小数的字符串
            } else {
                return '0.00';
            }
        },
        withdrawalMode(type) {
            let str = '';
            switch (type) {
                case 1:
                    str = '手动打款';
                    break;
                case 2:
                    str = '汇聚';
                    break;
                default:
                    str = '-';
                    break;
            }
            return str;
        },
        // 提现状态
        formatWithdrawalStatus: function(status) {
            let text = '';
            switch (status) {
                case 0:
                    text = '待审核';
                    break;
                case 1:
                    text = '通过';
                    break;
                case 2:
                    text = '无效';
                    break;
                case 3:
                    text = '驳回';
                    break;
                default:
                    text = '-';
                    break;
            }
            return text;
        },
        invoiceStatus(type) {
            let str = '';
            switch (type) {
                case 0:
                    str = '无需开票';
                    break;
                case 1:
                    str = '待开票';
                    break;
                case 2:
                    str = '已开票';
                    break;
                default:
                    str = '-';
                    break;
            }
            return str;
        },
        remitStatus(type) {
            let str = '';
            switch (type) {
                case 0:
                    str = '待打款';
                    break;
                case 1:
                    str = '已打款';
                    break;
                case 2:
                    str = '打款中';
                    break;
                case 4:
                    str = '无需打款';
                    break;
                default:
                    str = '-';
                    break;
            }
            return str;
        },
    },
    mounted() {
        // 获取统计数据
        this.getTotalBill();
        // 获取列表
        this.getSupplierBill();
    },
    methods: {
        // 导出
        async exportSupplierBill() {
            let params = this.paramsFun();
            let res = await exportSupplierBill(params);
            if (res.code === 0) {
                window.open(this.$path + '/' + res.data.link);
            }
        },
        // 查看发票
        examineInvoice(id, income_amount) {
            this.$refs.addInvoice.info({
                title: '查看发票',
                id,
                income_amount,
                type: 0,
            });
        },
        // 上传发票
        uploadInvoice(id, income_amount) {
            this.$refs.addInvoice.info({
                title: '上传发票',
                id,
                income_amount,
                type: 0,
            });
        },
        // 账单明细
        billingDetails(id) {
            this.$_blank('/layout/financeManage/billingDetails', { id });
        },
        // 整理数据
        paramsFun() {
            let params = {
                page: this.page,
                pageSize: this.pageSize,
            };
            // 提现编号
            if (this.formData.order_sn) {
                params.order_sn = this.formData.order_sn;
            }
            // 提现方式
            if (this.formData.withdrawal_mode) {
                params.withdrawal_mode = parseInt(
                    this.formData.withdrawal_mode,
                );
            }
            // 审核状态
            if (this.formData.withdrawal_status !== '') {
                params.withdrawal_status = parseInt(this.formData.withdrawal_status);
            }
            // 开票状态
            if (this.formData.invoice_status !== '') {
                params.invoice_status = parseInt(this.formData.invoice_status);
            }
            // 打款状态
            if (this.formData.remit_status !== '') {
                params.remit_status = parseInt(this.formData.remit_status);
            }
            // 时间
            if (this.formData.start_time && this.formData.start_time !== '') {
                params.start_time = this.formData.start_time;
            }
            if (this.formData.end_time && this.formData.end_time !== '') {
                params.end_time = this.formData.end_time;
            }

            return params;
        },
        // 搜索
        search() {
            this.page = 1;
            this.getSupplierBill();
        },
        clearSearch() {
            this.page = 1;
            this.pageSize = 10;
            this.total = 0;
            this.formData = {
                order_sn: '', // 提现编号
                withdrawal_mode: '', // 提现方式
                withdrawal_status: '', // 审核状态
                invoice_status: '', // 开票状态
                remit_status: '', // 打款状态
                start_time: '', // 开始时间
                end_time: '', // 结束时间
            };
            this.tableData = [];
            this.getSupplierBill();
        },
        // 获取列表
        async getSupplierBill() {
            let params = this.paramsFun();
            let res = await getSupplierBill(params);
            if (res.code === 0) {
                this.total = res.data.total;
                this.tableData = res.data.list;
            }
        },
        // 分页
        handleCurrentChange(page) {
            this.page = page;
            this.getSupplierBill();
        },
        handleSizeChange(pageSize) {
            this.pageSize = pageSize;
            this.getSupplierBill();
        },
        // 获取统计数据
        async getTotalBill() {
            let res = await getTotalBill();
            if (res.code === 0) {
                this.dataInfo = res.data;
            }
        },
        // 提现
        withdraw() {
            this.$refs.withdrawDialog.info(this.dataInfo);
        },
        // 提现成功
        withdrawSuccess() {
            // 获取统计数据
            this.getTotalBill();
            // 获取列表
            this.getSupplierBill();
        },
        // 打开数据指标说明
        handleInfo() {
            this.isShow = true;
        },
        // 关闭数据指标说明
        handleClose() {
            this.isShow = false;
        },
        // 打开银行卡
        openBankCard() {
            this.$refs.myBankCardDialog.info();
        },
    },
};
</script>

<style lang="scss" scoped>
// @import '@/style/base.scss';
h1 {
    font-size: 18px;
}
.mb20 {
    margin-bottom: 20px;
}
.fixed-bottom {
    height: 33px;
    bottom: 0;
}

.grid {
    border-radius: 12px;
    padding: 17px 22px;
}

.grid-content {
    /* border-radius: 4px; */
    min-height: 36px;
}

.grid-content .numbers-p {
    margin-bottom: 25px;
    font-size: 20px;
}

::v-deep .has-gutter {
    tr {
        th {
            background-color: #ffffff !important;
        }
    }
}
::v-deep .el-table--fit {
    border: 0px;
}

::v-deep .admin-box .el-table th.is-leaf {
    border-bottom: 0px;
}
::v-deep .el-table th.is-leaf {
    border-bottom: 0px;
}

.m_dialog p {
    margin-bottom: 20px;
    line-height: 25px;
}

.but {
    margin: 20px auto 0;
}
</style>
