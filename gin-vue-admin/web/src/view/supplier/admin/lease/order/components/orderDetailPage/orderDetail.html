<div>
    <el-drawer
        title="订单详情"
        :visible="isShow"
        :close-on-press-escape="false"
        :append-to-body="true"
        :wrapperClosable="false"
        :before-close="handleClose"
        size="calc(100% - 220px)"
        class="detail-ct"
    >
        <m-card>
            <div class="f fac">
                <p>订单编号: {{ orderData.order_sn }}</p>
                <p class="ml10">
                    第三方订单编号: {{ orderData.third_order_sn }}
                </p>
                <p class="ml10">
                    下单时间: {{ orderData.created_at | formatDate }}
                </p>
            </div>
            <!-- 订单状态部分 -->
            <el-row class="order-status-box mt25">
                <el-col :span="9" class="left">
                    <h1 class="order-status-title">
                        {{ orderData.status | formatStatus }}
                    </h1>
                    <!-- <p class="order-status-marked-p">
                        如买家未在时间内付款, 订单将按照设置逾期自动关闭
                    </p> -->
                    <p class="remind-p">
                        <span class="remind-span">提醒:</span>
                        请务必等待订单状态变更为"买家已付款,
                        等待卖家发货"后再进行发货.
                    </p>
                    <div>
                        <!-- <el-button class="mt25" v-if="orderData.status === 0" @click="openOrderShipmentsDialog">确认付款
                        </el-button> -->
                        <el-button
                            type="text"
                            v-for="btn in orderData.button"
                            :key="btn.id"
                            @click="orderOperation(orderData.id,btn)"
                            >{{ btn.title }}
                        </el-button>
                    </div>
                    <el-button
                        type="text"
                        class="remark-btn"
                        @click="openDialog"
                        >备注</el-button
                    >
                </el-col>
                <el-col :span="15" class="right f fac">
                    <el-steps
                        :active="orderData.status+1"
                        align-center
                        finish-status="success"
                        class="f1"
                    >
                        <el-step
                            title="买家下单"
                            :description="orderData.created_at | formatDate"
                        ></el-step>
                        <el-step
                            title="买家付款"
                            :description="orderData.paid_at | formatDate"
                        ></el-step>
                        <el-step
                            title="商家发货"
                            :description="orderData.sent_at | formatDate"
                        ></el-step>
                        <el-step
                            title="交易成功"
                            :description="orderData.received_at | formatDate"
                        ></el-step>
                    </el-steps>
                </el-col>
            </el-row>
            <div class="order-status-remind">
                <p class="remind-p">
                    <span class="remind-span">备注:</span>
                    {{ orderData.note ? orderData.note : "-" }}
                </p>
            </div>
            <!--            &lt;!&ndash; 包裹部分 &ndash;&gt;-->
            <!--            <el-tabs v-model="activeName" type="card" class="mt25 parcel-tabs">-->
            <!--                <el-tab-pane :label="'包裹'+(index+1)" :name="index+''" v-for="(item,index) in orderData.order_express">-->
            <!--                    <div class="tab-item">-->
            <!--                        <el-row>-->
            <!--                            <el-col :span="8">-->
            <!--                                <ul>-->
            <!--                                    <li>-->
            <!--                                        <span class="title-span">发货方式:</span>-->
            <!--                                        <span class="cont-span">快递</span>-->
            <!--                                    </li>-->
            <!--                                    <li>-->
            <!--                                        <span class="title-span">发货人:</span>-->
            <!--                                        <span class="cont-span">-</span>-->
            <!--                                    </li>-->
            <!--                                    <li>-->
            <!--                                        <span class="title-span">发货时间:</span>-->
            <!--&lt;!&ndash;                                        <span class="cont-span">{{ item.created_at |formatDate }}</span>&ndash;&gt;-->
            <!--                                    </li>-->
            <!--                                    <li class="f">-->
            <!--                                        <div class="goods-info ml10" v-for="goods in item.order_items">-->
            <!--                                            <m-image :src="goods.image_url" style="width: 70px; height: 70px"></m-image>-->
            <!--                                            <p>{{ goods.title }} {{ goods.sku_title }}</p>-->
            <!--                                            <p>数量: {{ goods.send_num }}</p>-->
            <!--                                        </div>-->
            <!--                                    </li>-->
            <!--                                </ul>-->
            <!--                            </el-col>-->
            <!--                            <el-col :span="16">-->
            <!--                                <ul>-->
            <!--                                    <li>-->
            <!--                                        <span class="title-span">物流状态:</span>-->
            <!--                                        <span class="cont-span">{{ expressInfo(item) }}</span>-->
            <!--                                        <el-button type="text" v-if="expressInfo(item)"-->
            <!--                                                   @click="openLogisticsDialog(index)">查看物流-->
            <!--                                        </el-button>-->
            <!--                                        <el-button class="ml10" type="text" v-if="expressInfo(item)"-->
            <!--                                                   @click="openEditLogistice(orderData.id,{})">修改物流-->
            <!--                                        </el-button>-->
            <!--                                    </li>-->
            <!--                                    &lt;!&ndash; <li class="f" v-if="item.List">-->
            <!--                                        <span class="title-span">物流明细:</span>-->
            <!--                                        <div>-->
            <!--                                            <p v-for="list in item.List" :key="list.id" class="cont-span">-->
            <!--                                                {{ list.time }}&nbsp;&nbsp;&nbsp;&nbsp;{{ list.status }}-->
            <!--                                            </p>-->
            <!--                                        </div>-->
            <!--                                    </li> &ndash;&gt;-->
            <!--                                </ul>-->
            <!--                            </el-col>-->
            <!--                        </el-row>-->
            <!--                    </div>-->
            <!--                </el-tab-pane>-->
            <!--            </el-tabs>-->

            <!-- 收货人信息,配送信息,付款信息,买家信息 -->
            <div class="synthesize-box mt25 f">
                <div class="f1">
                    <p class="f fac head-p">
                        <span style="margin-right: 20px">收货人信息</span>
                        <!-- <el-button class="ml10" type="text" @click="openEditConsigneeDialog">修改</el-button> -->
                        <el-button
                            type="text"
                            v-if="orderData.status <= 1 && orderData.is_update_shipping_address === 1"
                            class="color-red"
                            @click="openEditAddressDialog"
                            >修改地址
                        </el-button>
                        <el-button
                            type="text"
                            v-if="orderData.status <= 1 && orderData.is_update_shipping_address === 1"
                            @click="modifyRecord"
                            >修改记录
                        </el-button>
                    </p>
                    <ul>
                        <li>
                            收货人: {{ orderData.shipping_address.realname }}
                        </li>
                        <li>
                            联系电话: {{ orderData.shipping_address.mobile }}
                        </li>
                        <li>
                            收货地址: {{
                            assemblyAddress(orderData.shipping_address) }}
                        </li>
                    </ul>
                </div>
                <div class="f1">
                    <p class="head-p">配送信息</p>
                    <ul>
                        <li>配送方式:配送上门</li>
                    </ul>
                </div>
                <div class="f1">
                    <p class="head-p">发货信息</p>
                    <ul>
                        <li>
                            发货状态: {{ orderData.send_status | formatStatus2
                            }}
                        </li>
                    </ul>
                </div>
                <div class="f1">
                    <p class="head-p">买家信息</p>
                    <ul>
                        <li>
                            买家:
                            <el-button
                                type="text"
                                @click="toUserInfo(orderData.user.id)"
                            >
                                {{ orderData.user.nickname }}
                            </el-button>
                            <el-button
                                type="text"
                                v-if="orderData.status !== 0"
                                @click="openPaymentLog"
                                >查看支付记录
                            </el-button>
                        </li>

                        <li>
                            买家留言: {{ orderData.remark ? orderData.remark :
                            "-" }}
                        </li>
                    </ul>
                </div>
            </div>
            <div class="synthesize-box mt25 f">
                <div class="f1">
                    <p class="f fac head-p">
                        <span style="margin-right: 20px">租赁信息</span>
                        <!-- <el-button class="ml10" type="text" @click="openEditConsigneeDialog">修改</el-button> -->
                    </p>
                    <ul>
                        <li>租赁天数: {{ orderData.order_leases.num_days }}</li>
                        <li>
                            租赁到期时间: {{ orderData.order_leases.end_at |
                            formatDate}}
                        </li>
                        <li>
                            租赁状态: {{orderData.order_leases.status_name}}
                        </li>
                        <li>
                            归还信息: {{
                            orderData.order_leases.order_lease_return_address.username}}
                            地址: {{
                            assemblyAddress(orderData.order_leases.order_lease_return_address)
                            }}
                        </li>
                        <li>
                            快递名称: {{ orderData.order_leases.company_name }}
                        </li>
                        <li>
                            快递单号:
                            {{orderData.order_leases.express_no}}<el-button
                                style="margin-left: 10px"
                                type="text"
                                @click="openLogisticsDialog(orderData.order_leases)"
                                >查看物流</el-button
                            >
                        </li>
                        <li>
                            填写快递单号时间:
                            {{orderData.order_leases.order_lease_return_address.created_at
                            | formatDate}}
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 发票内容 -->
            <!--            <div class="invoice-box" v-if="orderBill">-->
            <!--                <div class="left-invoice-box">-->
            <!--                    <div v-if="billIsEmpty">-->
            <!--                        <p v-if="orderBill.type">发票类型: {{ orderBill.type == 1 ? '电子普通发票' : '增值税发票' }}</p>-->
            <!--                        &lt;!&ndash; 电子普通发票 &ndash;&gt;-->
            <!--                        <template v-if="orderBill.type == 1 ">-->
            <!--                            <p v-if="orderBill.company_code">发票内容:-->
            <!--                                {{ orderBill.detail_type == 1 ? '商品明细' : '商品类别' }}</p>-->
            <!--                            <p v-if="orderBill.account_type">发票抬头: {{ orderBill.account_type == 1 ? '个人' : '公司' }}</p>-->
            <!--                            <p v-if="orderBill.person_name">个人名称: {{ orderBill.person_name }}</p>-->
            <!--                        </template>-->
            <!--                        &lt;!&ndash; 增值税发票 &ndash;&gt;-->
            <!--                        <template>-->
            <!--                            <p v-if="orderBill.sign_address">注册地址: {{ orderBill.sign_address }}</p>-->
            <!--                            <p v-if="orderBill.sign_mobile">注册电话: {{ orderBill.sign_mobile }}</p>-->
            <!--                            <p v-if="orderBill.opening_bank">开户银行: {{ orderBill.opening_bank }}</p>-->
            <!--                            <p v-if="orderBill.bank_account">银行账号: {{ orderBill.bank_account }}</p>-->
            <!--                        </template>-->
            <!--                        <p v-if="orderBill.company_name">单位名称: {{ orderBill.company_name }}</p>-->
            <!--                        <p v-if="orderBill.company_code">纳税人识别号: {{ orderBill.company_code }}</p>-->
            <!--                        <p v-if="orderBill.mobile">收票人手机: {{ orderBill.mobile }}</p>-->
            <!--                        <p v-if="orderBill.email">收票人邮箱: {{ orderBill.email }}</p>-->
            <!--                    </div>-->
            <!--                    <el-empty v-else description="没有发票信息" :image-size="200"></el-empty>-->
            <!--                </div>-->
            <!--                <div class="right-upload">-->
            <!--                    <h3 class="title">上传发票</h3>-->
            <!--                    <el-upload class="img-uploader" :show-file-list="false"-->
            <!--                        :action="path + '/fileUploadAndDownload/upload'" :headers="{ 'x-token': token }"-->
            <!--                        :on-success="handleImgSuccess" :before-upload="beforeAvatarUpload"-->
            <!--                        accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">-->
            <!--                        <m-image v-if="orderBill.image" style="width:100%;height:100%" :src="orderBill.image">-->
            <!--                        </m-image>-->
            <!--                        <i v-else class="el-icon-plus img-uploader-icon"></i>-->
            <!--                    </el-upload>-->
            <!--                    <el-button class="save-invoice" type="primary" @click="handleSaveImg">保存发票</el-button>-->
            <!--                </div>-->
            <!--            </div>-->
            <template v-if="afterSalesList && afterSalesList.length>0">
                <!--售后商品-->
                <p class="mt25">售后商品</p>
                <el-table :data="afterSalesList" style="margin-top: 10px">
                    <el-table-column label="商品">
                        <template slot-scope="scope">
                            <div class="f fa">
                                <m-image
                                    :src="scope.row.image_url"
                                    style="
                                        width: 69px;
                                        min-width: 69px;
                                        height: 69px;
                                        min-height: 69px;
                                    "
                                ></m-image>
                                <div class="ml10 table-goods-imgName">
                                    <p class="hiddenText1">
                                        <el-button
                                            type="text"
                                            @click="$_blank('/layout/supplierLeaseIndex/supplierLeaseReleaseGoods',{id:scope.row.product_id})"
                                        >
                                            {{ scope.row.title }}
                                        </el-button>
                                    </p>
                                    <p class="hiddenText2">
                                        规格: {{ scope.row.sku_title }}
                                    </p>
                                    <p>
                                        数量: {{ scope.row.qty }}{{
                                        scope.row.unit }}
                                    </p>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="发货状态" align="center">
                        <template slot-scope="scope">
                            <span
                                >{{ scope.row.send_status | formatShipments
                                }}</span
                            >
                        </template>
                    </el-table-column>
                    <el-table-column label="订单金额(元)" align="center">
                        <template slot-scope="scope">
                            {{ scope.row.order_amount | formatF2Y }}
                        </template>
                    </el-table-column>
                    <el-table-column label="退款金额(元)" align="center">
                        <template slot-scope="scope">
                            {{ scope.row.amount | formatF2Y }}
                        </template>
                    </el-table-column>
                    <el-table-column label="申请时间" align="center">
                        <template slot-scope="scope">
                            <!--                            {{ scope.row.after_sales_audit.created_at | formatDate }}-->
                        </template>
                    </el-table-column>
                    <el-table-column label="售后状态" align="center">
                        <template slot-scope="scope">
                            <span v-if="scope.row.status === 0"
                                >{{ scope.row.after_sales_audit.status_name
                                }}</span
                            >
                            <span v-else>{{ scope.row.status_name }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="right">
                        <template slot-scope="scope">
                            <el-button
                                type="text"
                                @click="openAfterDetail(scope.row)"
                                >查看详情</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
            </template>
            <!-- 商品部分 -->
            <p class="mt25">订单商品</p>
            <el-table :data="orderData.order_items" style="margin-top: 10px">
                <el-table-column label="商品">
                    <template slot-scope="scope">
                        <div class="f fa">
                            <m-image
                                :src="scope.row.image_url"
                                style="
                                    width: 69px;
                                    min-width: 69px;
                                    height: 69px;
                                    min-height: 69px;
                                "
                            ></m-image>
                            <div class="ml10 table-goods-imgName">
                                <p class="hiddenText1">
                                    <el-button
                                        type="text"
                                        @click="$_blank('/layout/supplierLeaseIndex/supplierLeaseReleaseGoods',{id:scope.row.product_id})"
                                    >
                                        {{ scope.row.title }}
                                    </el-button>
                                </p>
                                <p class="hiddenText2">
                                    规格: {{ scope.row.sku_title }}
                                </p>
                                <p>
                                    数量: {{ scope.row.qty }}{{ scope.row.unit
                                    }}
                                </p>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="单价(元)" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.price | formatF2Y }}
                    </template>
                </el-table-column>
                <el-table-column label="优惠(元)" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.discount_amount ? scope.row.discount_amount
                        : 0 | formatF2Y }}
                    </template>
                </el-table-column>
                <el-table-column label="供货金额(元)" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.supply_amount ? scope.row.supply_amount : 0
                        | formatF2Y }}
                    </template>
                </el-table-column>
                <el-table-column label="小计(元)" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.price * scope.row.qty | formatF2Y }}
                    </template>
                </el-table-column>
                <el-table-column label="售后状态" align="center">
                    <template slot-scope="scope">
                        <el-button
                            v-if="scope.row.can_refund"
                            size="small"
                            @click="openRefundDialog(scope.row)"
                            >主动退款
                        </el-button>
                    </template>
                </el-table-column>
                <!--                <el-table-column label="发货状态" align="right">-->
                <!--                    <template slot-scope="scope">-->
                <!--                        <span>{{ scope.row.send_status | formatShipments }}</span>-->
                <!--                    </template>-->
                <!--                </el-table-column>-->
            </el-table>
            <!-- 统计部分 -->
            <div class="statistics-box f fjend mt25">
                <div class="statistics">
                    <ul
                        v-for="amount_items in orderData.amount_detail.amount_items"
                    >
                        <li>
                            <span class="title-span text-right"
                                >{{ amount_items.title }}:</span
                            ><span class="sum-span text-right"
                                >￥{{ amount_items.amount | formatF2Y }}</span
                            >
                        </li>

                        <!-- <li>
                            <span class="title-span text-right">礼品卡/储蓄卡:</span><span
                                class="sum-span text-right">￥0.00</span>
                        </li> -->
                    </ul>
                    <p class="sum-total">
                        <span class="title-span text-right">实收金额:</span>
                        <span class="sum-span text-right color-red"
                            >￥{{ orderData.amount | formatF2Y }}</span
                        >
                    </p>
                </div>
            </div>
        </m-card>
    </el-drawer>
    <el-dialog
        title="备注"
        :visible="dialogIsShow"
        top="40vh"
        width="30%"
        :before-close="handleDialogClose"
    >
        <el-form :model="dialogForm" label-width="60px" ref="dialogForm">
            <el-form-item label="备注:" prop="note">
                <el-input
                    v-model="dialogForm.note"
                    type="textarea"
                    :rows="4"
                ></el-input>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="confirm">确 定</el-button>
            <el-button @click="handleDialogClose">取 消</el-button>
        </span>
    </el-dialog>
    <el-dialog
        title="物流详情"
        :visible="logisticsIsShow"
        top="40vh"
        width="800px"
        :before-close="handlelogisticsClose"
    >
        <template v-if="logisticsList.length > 0">
            <p
                v-for="list in logisticsList"
                :key="list.id"
                class="cont-span mt10"
            >
                {{ list.time }}&nbsp;&nbsp;&nbsp;&nbsp;{{ list.status }}
            </p>
        </template>
        <div v-else class="f fac fjc color-grap">暂无物流数据~</div>
        <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="handlelogisticsClose"
                >关 闭</el-button
            >
        </span>
    </el-dialog>
    <edit-consignee-dialog ref="editConsigneeDialog"></edit-consignee-dialog>
    <order-shipments-dialog ref="orderShipmentsDialog"></order-shipments-dialog>
    <user-detail ref="userDetail"></user-detail>
    <!-- 支付记录dialog -->
    <PaymentLog ref="paymentLog"></PaymentLog>

    <!-- 主动退款dialog -->
    <refund-dialog
        ref="refundDialog"
        @reload="getOrderDetail(orderData.id)"
    ></refund-dialog>
    <detail-after-sale
        ref="detailAfterSale"
        @reload="getOrderDetail(orderData.id)"
    ></detail-after-sale>

    <edit-logistics-dialog ref="editLogisticsDialog"></edit-logistics-dialog>
    <address-dialog
        v-if="addressDialogIsShow"
        :addressDialogIsShow.sync="addressDialogIsShow"
        ref="addressDialog"
        @reload="getOrderDetail(orderData.id)"
    ></address-dialog>
    <modify-record-dialog ref="modifyRecordDialog"></modify-record-dialog>
</div>
