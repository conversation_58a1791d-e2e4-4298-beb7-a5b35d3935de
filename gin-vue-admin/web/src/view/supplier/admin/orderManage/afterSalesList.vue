<template>
  <m-card>
    <el-form :model="searchInfo" class="search-term" inline>
      <el-form-item>
        <div class="line-input line-input-date">
            <div class="line-box" style="min-width: auto;padding: 0 10px;">
                <span>申请时间</span>
            </div>
            <div>
              <mDaterange v-model="payDate"></mDaterange>
            </div>
        </div>
      </el-form-item>
      <br/>
      <el-form-item>
        <el-input placeholder="请输入" v-model="orderSearchCondition" class="line-input-width" clearable>
            <el-select v-model="orderSearchConditionTag"  slot="prepend">
              <el-option
                  :label="item.name"
                  :value="item.value"
                  v-for="item in orderSearchConditions"
                  :key="item.id"
              >
              </el-option>
            </el-select>
        </el-input>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input" >
            <div class="line-box ">
                <span >供应链</span>
            </div>
            <el-select
                v-model="searchInfo.gather_supplier_id"
                class="w100"
                filterable
                clearable
            >
              <el-option
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                  v-for="item in supplyChainOption"
              ></el-option>
            </el-select>
        </div>
      </el-form-item>
      <el-form-item>
        <el-input placeholder="请输入" v-model="searchInfo.product_title" class="line-input" clearable>
            <span slot="prepend">商品名称</span>
        </el-input>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input" >
            <div class="line-box ">
                <span >退款类型</span>
            </div>
            <el-select v-model="searchInfo.type" class="w100" clearable>
              <el-option label="退款" :value="0"></el-option>
              <el-option label="退货" :value="1"></el-option>
              <el-option label="换货" :value="2"></el-option>
            </el-select>
        </div>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input" >
            <div class="line-box ">
                <span >发货状态</span>
            </div>
            <el-select v-model="searchInfo.send_status" class="w100" clearable>
              <el-option label="待发货" :value="0"></el-option>
              <el-option label="部分发货" :value="1"></el-option>
              <el-option label="已发货" :value="2"></el-option>
            </el-select>
        </div>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input" >
            <div class="line-box ">
                <span >退货物流</span>
            </div>
            <el-select
                v-model="searchInfo.company_code"
                class="w100"
                filterable
                clearable
            >
              <el-option
                  v-for="item in returnsLogisticsOption"
                  :key="item.id"
                  :label="item.name"
                  :value="item.code"
              ></el-option>
            </el-select>
        </div>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input" >
            <div class="line-box ">
                <span >售后状态</span>
            </div>
            <el-select v-model="orderStatus" class="w100">
              <el-option v-for="item in orderStatusConditions" :key="item.id" :label="item.name"
                        :value="item.value"></el-option>
            </el-select>
        </div>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input" >
            <div class="line-box ">
                <span >采购端</span>
            </div>
            <el-select
                class="w100"
                filterable
                v-model="searchInfo.application_id" 
                clearable
            >
              <el-option
                  v-for="item in orderApplicationConditions"
                  :key="item.id"
                  :value="item.id"
                  :label="item.app_name"
              >
              </el-option>
            </el-select>
        </div>
      </el-form-item>
      <br/>
      <el-form-item>
        <el-button type="primary" @click="search">搜索</el-button>
        <el-button type="text" @click="reSearch">重置搜索条件</el-button>
       <el-button @click="afterSaleExport">导出</el-button>
      </el-form-item>
    </el-form>
    <el-tabs
        v-model="orderStatus"
        type="card"
        class="mt25 order-tabs"
        @tab-click="handleTabsClick"
    >
      <el-tab-pane
          v-for="item in orderStatusConditions"
          :key="item.id"
          :label="`${item.name} ${item.total ? item.total : ''}`"
          :name="item.value"
      >
      </el-tab-pane>
    </el-tabs>
    <div class="table-box mt25">
      <el-table :data="[{}]" class="table-head">
        <el-table-column label="商品" style="width: 20%"></el-table-column>
        <el-table-column
            label="供应链单号"
            style="width: 10%"
            align="center"
        ></el-table-column>
        <el-table-column
            label="供应链名称"
            style="width: 10%"
            align="center"
        ></el-table-column>
        <el-table-column
            label="订单状态"
            width="200"
            align="center"
        ></el-table-column>
        <el-table-column
            label="订单金额(元)"
            style="width: 10%"
            align="center"
        ></el-table-column>
        <el-table-column
            label="退款金额(元)"
            style="width: 10%"
            align="center"
        ></el-table-column>
        <el-table-column
            label="运费(元)"
            style="width: 10%"
            align="center"
        ></el-table-column>
        <el-table-column
            label="技术服务费(元)"
            style="width: 10%"
            align="center"
        ></el-table-column>
        <el-table-column
            label="申请时间"
            style="width: 10%"
            align="center"
        ></el-table-column>
        <el-table-column
            label="售后状态"
            style="width: 10%"
            align="center"
        ></el-table-column>
        <el-table-column label="操作" align="center"></el-table-column>
      </el-table>
      <div v-for="item in tableData" :key="item.id" class="mt25">
        <el-table :data="[item]" class="table-cont">
          <el-table-column>
            <template slot="header">
              <div class="w100 f fac fjsb">
                <div class="f fac">
                  <p>售后编号: {{ item.after_sale_sn }}</p>
                  <p>订单编号: {{ item.order.order_sn }}</p>
                  <p v-if="item.order.third_order_sn !==''">第三方订单号: {{ item.order.third_order_sn}}</p>
                  <p v-if="item.order.application.appName !==''">采购端名称:{{item.order.application ? item.order.application.appName:"_"}}</p>
                  <el-tag class="ml10" v-if="item.type === 0">仅退款</el-tag>
                  <el-tag class="ml10" v-if="item.type === 1" type="danger"
                  >退货退款
                  </el-tag
                  >
                  <el-tag class="ml10" v-if="item.type === 2" type="warning"
                  >换货
                  </el-tag>
                  <!-- <el-tag class="ml10" v-if="item.order.gather_supply.id" type="warning"
                  >供应链: {{ item.order.gather_supply.name }}
                  </el-tag> -->
                </div>
              </div>
            </template>
            <el-table-column style="width: 20%">
              <template slot-scope="scope">
                {{ scope.row.order_item.title }}
              </template>
            </el-table-column>
            <!-- 供应链单号 -->
            <el-table-column style="width: 10%">
              <template slot-scope="scope">
                {{scope.row.order.gather_supply_sn ? '' : '暂无供应链单号'}}
              </template>
            </el-table-column>
            <!-- 供应链名称 -->
            <el-table-column style="width: 10%">
              <template slot-scope="scope">
               {{ scope.row.order.gather_supply.name ? '' : '暂无供应链名称' }}
              </template>
            </el-table-column>
            <el-table-column style="width: 10%">
              <template slot-scope="scope">
                <span>
                  {{ scope.row.order.status_name }}
                </span>
              </template>
            </el-table-column>
            <el-table-column style="width: 10%">
              <template slot-scope="scope">
                {{ scope.row.order_item.amount | formatF2Y }}
              </template>
            </el-table-column>
            <el-table-column style="width: 10%">
              <template slot-scope="scope">
                {{ scope.row.order_item.freight | formatF2Y }}
              </template>
            </el-table-column>
            <el-table-column style="width: 10%">
              <template slot-scope="scope">
                {{ scope.row.order_item.technical_services_fee | formatF2Y }}
              </template>
            </el-table-column>
            <el-table-column style="width: 10%">
              <template slot-scope="scope">
                {{ scope.row.amount | formatF2Y }}
              </template>
            </el-table-column>
            <el-table-column style="width: 10%">
              <template slot-scope="scope">
                {{ scope.row.after_sales_audit.created_at | formatDate }}
              </template>
            </el-table-column>
            <el-table-column style="width: 10%">
              <template slot-scope="scope">
                <span v-if="scope.row.status === 0">{{ scope.row.after_sales_audit.status_name }}</span>
                <span v-else>{{ scope.row.status_name }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center">
              <template slot-scope="scope">
                <el-button type="text" @click="viewDetails(scope.row)">查看详情</el-button>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-pagination
        background
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="[10, 30, 50, 100]"
        :style="{
        display: 'flex',
        justifyContent: 'flex-end',
        marginRight: '20px',
      }"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>
    <detail-after-sale ref="detailAfterSale" @reload="detailTable"></detail-after-sale>
  </m-card>
</template>

<script>
import infoList from "@/mixins/infoList";
import {afterSalesList, getShippingCompany, getSupplyList, getApplicationOption, exportAfterSalesList} from "@/api/supplierRouterAfterSales";
import mDaterange from "@/components/mDate/daterange";
import detailAfterSale from "../components/detailAfterSale";

export default {
  name: "supplierAfterSalesList",
  mixins: [infoList],
  watch:{
    resData(data){
      let afterSalesStatusCounts = data.afterSalesStatusCounts
      // 全部
      // this.orderStatusConditions[0].total
      // 待审核 waiting_review
      this.$set(this.orderStatusConditions[1],'total',afterSalesStatusCounts.waiting_review)
      // 待买家发货 waiting_user_send
      this.$set(this.orderStatusConditions[2],'total',afterSalesStatusCounts.waiting_user_send)
      // 待商家收货 waiting_shop_receiving
      this.$set(this.orderStatusConditions[3],'total',afterSalesStatusCounts.waiting_shop_receiving)
      // 待商家发货 waiting_shop_send
      this.$set(this.orderStatusConditions[4],'total',afterSalesStatusCounts.waiting_shop_send)
      // 待买家收货 waiting_user_receiving
      this.$set(this.orderStatusConditions[5],'total',afterSalesStatusCounts.waiting_user_receiving)
      // 待退款 waiting_refund
      this.$set(this.orderStatusConditions[6],'total',afterSalesStatusCounts.waiting_refund)
      // 已完成 completed
      this.$set(this.orderStatusConditions[7],'total',afterSalesStatusCounts.completed)
      // 已关闭 close
      this.$set(this.orderStatusConditions[8],'total',afterSalesStatusCounts.close)
      // 已驳回 reject
      this.$set(this.orderStatusConditions[9],'total',afterSalesStatusCounts.reject)
    }
  },
  components: {mDaterange, detailAfterSale},
  data() {
    return {
      listApi: afterSalesList,
      // 搜索下拉
      orderSearchConditionTag: 0,
      // 搜索输入框
      orderSearchCondition: "",
      // 下单时间
      payDate: [],
      orderSearchConditions: [{name: "订单编号", value: 0}],
      returnsLogisticsOption: [], //退货物流
      supplyChainOption: [], //供应链
      orderApplicationConditions: [], // 采购端
      dateActive: "",
      dateList: [
        {name: "今", value: 0},
        {name: "昨", value: 1},
        {name: "近7天", value: 2},
        {name: "近30天", value: 3},
      ],
      //订单状态
      orderStatusConditions: [
        {name: "全部", value: "-100"},
        {name: "待审核", value: "0"},
        {name: "待买家发货", value: "1"},
        {name: "待商家收货", value: "2"},
        {name: "待商家发货", value: "5"},
        {name: "待买家收货", value: "6"},
        /*{name: "待发货", value: "1"},
        {name: "待收货", value: "2"},*/
        {name: "待退款", value: "3"},
        {name: "已完成", value: "4"},
        {name: "已关闭", value: "-1"},
        {name: "已驳回", value: "-10"},
      ],
      orderStatus: "-100",
    };
  },
  mounted() {
    this.getTableData();
    this.initOptions();
  },
  methods: {
    //重新加载
    detailTable() {
      this.page = 1
      this.getTableData();
    },
    //查看详情
    viewDetails(row) {
      this.$refs.detailAfterSale.isShow = true
      this.$refs.detailAfterSale.getSetting()
      this.$refs.detailAfterSale.afterSalesDetails(row)
    },
    //搜索
    search() {
      this.page = 1;
      this.getSearchCondition();
      this.getTableData();
    },
    // 获取搜索条件
    getSearchCondition() {
      switch (this.orderSearchConditionTag) {
        case 0: // 订单编号
          this.searchInfo.order_sn = this.orderSearchCondition;
          break;
      }
      if (this.orderStatus !== '-100') {
        this.searchInfo.status = parseInt(this.orderStatus)
      } else if (this.searchInfo.status) {
        delete this.searchInfo.status
      }
      if (this.payDate.length > 0) {
        this.searchInfo.start_at = this.payDate[0];
        this.searchInfo.end_at = this.payDate[1];
      }
    },
    //重置
    reSearch() {
      this.page = 1;
      this.orderSearchConditionTag = 0;
      // 搜索输入框
      this.orderSearchCondition = "";
      // 下单时间
      this.payDate = [];
      this.searchInfo = {};
      this.orderStatus = "-100"
    },
    // 导出
    async afterSaleExport() {
      if (this.orderSearchCondition) {
        switch (this.orderSearchConditionTag) {
          case 0:
            this.searchInfo.order_sn = this.orderSearchCondition;
            break;
          default:
            break;
        }
      }
      let params = this.searchInfo
      params.page = this.page,
      params.pageSize = this.pageSize
      const res = await exportAfterSalesList(params)
      if (res.code === 0) {
        this.$message.success(res.msg);
        this.download(res.data.link);
      }
    },
    // 导出方法
    download(link) {
        window.open(this.$path + '/' + link);
    },
    async initOptions() {
      let returnsLogisticsOptionRes = await getShippingCompany();
      if (returnsLogisticsOptionRes.code === 0) {
        this.returnsLogisticsOption = returnsLogisticsOptionRes.data.list;
      }
      let supplyChainOptionRes = await getSupplyList();
      if (supplyChainOptionRes.code === 0) {
        this.supplyChainOption = supplyChainOptionRes.data.list;
      }
      let applicationRes = await getApplicationOption();
      if (applicationRes.code === 0) {
        this.orderApplicationConditions = applicationRes.data.list;
      }
    },
    // 切换日期
    handleDateTab(item) {
      this.dateActive = this.dateActive === item.value ? "" : item.value;
      const todayDate = new Date();
      switch (this.dateActive) {
        case 0:
          const dateToday1 = new Date();
          dateToday1.setHours(0);
          dateToday1.setMinutes(0);
          dateToday1.setSeconds(0);
          this.formData.d1 = dateToday1;
          todayDate.setHours(23);
          todayDate.setMinutes(59);
          todayDate.setSeconds(59);
          this.formData.d2 = todayDate;
          break;
        case 1:
          const dateYesterday1 = new Date();
          dateYesterday1.setTime(
              dateYesterday1.getTime() - 3600 * 1000 * 24 * 1
          );
          dateYesterday1.setHours(0);
          dateYesterday1.setMinutes(0);
          dateYesterday1.setSeconds(0);
          this.formData.d1 = dateYesterday1;

          const dateYesterday2 = new Date();
          dateYesterday2.setTime(
              dateYesterday2.getTime() - 3600 * 1000 * 24 * 1
          );
          dateYesterday2.setHours(23);
          dateYesterday2.setMinutes(59);
          dateYesterday2.setSeconds(59);
          this.formData.d2 = dateYesterday2;
          break;
        case 2:
          const date7Day1 = new Date();
          date7Day1.setTime(date7Day1.getTime() - 3600 * 1000 * 24 * 7);
          date7Day1.setHours(0);
          date7Day1.setMinutes(0);
          date7Day1.setSeconds(0);
          this.formData.d1 = date7Day1;

          todayDate.setHours(23);
          todayDate.setMinutes(59);
          todayDate.setSeconds(59);
          this.formData.d2 = todayDate;
          break;
        case 3:
          const date30Day1 = new Date();
          date30Day1.setTime(date30Day1.getTime() - 3600 * 1000 * 24 * 30);
          date30Day1.setHours(0);
          date30Day1.setMinutes(0);
          date30Day1.setSeconds(0);
          this.formData.d1 = date30Day1;

          todayDate.setHours(23);
          todayDate.setMinutes(59);
          todayDate.setSeconds(59);
          this.formData.d2 = todayDate;
          break;

        default:
          break;
      }
    },
    // tabs切换
    handleTabsClick() {
      if (this.orderStatus === "-100") {
        if ('status' in this.searchInfo) delete this.searchInfo.status
      } else {
        this.searchInfo.status = parseInt(this.orderStatus);
      }
      this.page = 1;
      this.pageSize = 10;
      this.getTableData();
    },
  }
}
</script>

<style lang="scss" scoped>
/** 搜索**/
.search-term {
  .dateBtnBox {
    height: 36px;
    line-height: 36px;
    margin-left: 40px;

    span {
      height: 27px;
      line-height: 22px;
      display: inline-block;
      margin-right: 10px;
      padding: 2px 4px;
      border: 1px solid #dcdee0;
      color: #c8c9cc;
      cursor: pointer;
      box-sizing: border-box;

      &:last-child {
        margin-right: 0;
      }

      &:hover {
        color: #155bd4;
        border-color: #155bd4;
        background-color: #fff;
      }

      &.is_active {
        color: #155bd4;
        border-color: #155bd4;
        background-color: #fff;
      }
    }
  }
}

/**tab**/
::v-deep .order-tabs {
  .el-tabs__header {
    margin-bottom: 0px;

    .el-tabs__item {
      background-color: #f7f8fa;

      &.is-active {
        color: #303133;
        background-color: #ffffff;
      }

      &:hover {
        color: #303133;
      }
    }
  }
}

/***************************** 表格部分 *******************************/
::v-deep .el-table.table-head {
  margin-bottom: 25px;

  &::before {
    display: none;
  }

  .el-table__header-wrapper {
    tr th {
      background-color: #f7f8fa !important;
      border-bottom: 0;
    }
  }

  .el-table__body-wrapper {
    display: none;
  }
}

::v-deep .el-table.table-cont.el-table--border {
  border: 1px solid #efefef !important;
}

::v-deep .el-table.table-cont {
  margin-bottom: 0;

  thead {
    tr th {
      background-color: #f7f8fa !important;
    }

    tr:last-child {
      display: none;
    }

    tr:first-child {
      th {
        p {
          margin-left: 20px;

          &.supplier-p {
            //background-color: rgb(74, 197, 156);
            padding: 10px;
            color: #0a3cdc;
            //border-radius: 3px;
          }

          &:first-child {
            margin-left: 0;
          }
        }
      }
    }
  }

  .el-table__body-wrapper {
    .goods-box {
      p {
        margin-left: 10px;
      }
    }

    .comm-box {
      width: 50%;
      margin: 0 auto;

      p {
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.table-foot-box {
  border: 1px solid #ebeef5;
  border-top: 0;
  margin-bottom: 20px;
  padding: 10px;

  p {
    margin-left: 10px;

    &.addr-p {
      span {
        margin-right: 5px;
      }
    }

    &:first-child {
      margin-left: 0;
    }
  }
}
</style>