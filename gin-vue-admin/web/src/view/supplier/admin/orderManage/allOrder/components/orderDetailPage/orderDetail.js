import {getDetail, orderUpdateState, addNote} from "@/api/supplier/order"
import {getLogisticsList} from "@/api/order"

import EditConsigneeDialog from "../editConsigneeDialog"
import OrderShipmentsDialog from "../orderShipmentsDialog"
import UserDetail from "@/view/member/components/userDetail"
import EditLogisticsDialog from "@/view/order/allOrder/components/editLogisticsDialog"
import PaymentLog from "../paymentLog"
import detailAfterSale from "../../../../components/detailAfterSale"
export default {
    components: {
        EditConsigneeDialog,
        OrderShipmentsDialog,
        UserDetail,
        PaymentLog,
        detailAfterSale,
        EditLogisticsDialog
    },
    data() {
        return {
            afterSalesList: [],
            logisticsIsShow: false, // 物流详情dialog
            logisticsList: [],
            dialogIsShow: false,
            isShow: false,
            activeName: "0",
            dialogForm: {
                id: null,
                // 备注
                note: "",
            },
            orderData: {
                user: {
                    nickname: ""
                },
                shipping_address: {
                    realname: ""
                }
            }
        };
    },
    filters: {
        formatStatus: function (status) {
            let name = ""
            switch (status) {
                case 0:
                    name = "商品已拍下,等待买家付款"
                    break;
                case 1:
                    name = "买家已付款,等待卖家发货"
                    break;
                case 2:
                    name = "卖家已发货,等待买家收货"
                    break;
                case 3:
                    name = "卖家已收货,订单已完成"
                    break;
            }
            return name;
        },
        formatStatus2: function (status) {
            let name = ""
            switch (status) {
                case 0:
                    name = "未发货"
                    break;
                case 1:
                    name = "发货中"
                    break;
                case 2:
                    name = "已发货"
                    break;
            }
            return name;
        },
        // 发货状态
        formatShipments: function (status) {
            let name = ""
            switch (status) {
                case 0:
                    name = "未发货"
                    break;
                case 1:
                    name = "已发货"
                    break;
                case 2:
                    name = "部分发货"
                    break;
            }
            return name;
        }
    },
    methods: {
        openAfterDetail(row){
            this.$refs.detailAfterSale.isShow = true
            this.$refs.detailAfterSale.getByOrderItemId(row.order_item_id)
        },
        // 打开物流详情dialog 并查询数据赋值
        openLogisticsDialog(index) {
            this.logisticsIsShow = true
            this.getLogistics(index)
        },
        // 关闭物流详情dialog
        handlelogisticsClose() {
            this.logisticsIsShow = false
            this.logisticsList = []
        },
        // 获取物流详情
        getLogistics(index) {
            getLogisticsList({ id: this.orderData.id }).then(res => {
                if (res.code === 0) {
                    this.logisticsList = res.data.reshipping[index].data.List
                    /* let data = res.data.reshipping || [];
                    if (data) {
                        for (let i = 0; i < data.length; i++) {
                            for (let j = 0; j < this.orderData.order_express.length; j++) {
                                if (this.orderData.order_express[j].express_no === data[i].express_code) {
                                    this.$set(this.orderData.order_express[j], 'List', data[i].data.List)
                                }
                            }
                        }
                    } */
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        // 打开支付记录
        openPaymentLog() {
            this.$refs.paymentLog.isShow = true
        },
        // 订单操作
        orderOperationDialog(id, operationItem) {
            if (!operationItem || operationItem.url == "" || operationItem.url == undefined) {
                return;
            }
            if (operationItem.url == "order/send") {
                this.orderSendOperation(id, operationItem);
                return;
            }
            // 修改物流                               
            if (operationItem.url == "order/updateOrderExpress") {
                this.openEditLogistice(id, operationItem)
                return;
            }
            if (operationItem.url == "adminSupplier/updateOrderExpress") {
                this.openEditLogistice(id, operationItem)
                return;
            }
            // 确认发货
            /* if (operationItem.url === "order/send") {
                this.orderSendOperation(id, operationItem);
            } else {
                this.$confirm("是否【" + operationItem.title + "】?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                }).then(() => {
                    orderUpdateState(operationItem.url, {order_id: id}).then((res) => {
                        if (res.code === 0) {
                            this.$message.success(res.msg)
                            this.getOrderDetail(id)
                        }
                    })
                }).catch(() => {
                });
            } */
        },
        // 打开修改物流
        openEditLogistice(id, operationItem) {
            this.$refs.editLogisticsDialog.isShow = true;
            this.$refs.editLogisticsDialog.initOrderInfo(id,true)
        },

        //订单发货处理
        orderSendOperation(id, operationItem) {
            this.$refs.orderShipmentsDialog.isShow = true;
            this.$refs.orderShipmentsDialog.initOrderInfo(id);
        },

        // 打开修改收货人dialog
        openEditConsigneeDialog() {
            this.$refs.editConsigneeDialog.isShow = true
        },
        // 计算总优惠金额
        computeDiscounts(arr) {
            let sum = 0
            if (arr && arr.length > 0) {
                arr.forEach(item => {
                    sum += item.discount_amount ? item.discount_amount : 0
                })
            } else {
                sum = 0
            }
            return sum
        },
        //组装地址
        assemblyAddress(address) {
            let addressStr = "";
            if (!address) {
                return "-";
            }
            addressStr = address.province + " " + address.city + " " + address.county + " " + address.town + " " + address.detail;
            return addressStr;
        },
        // 获取订单详情
        getOrderDetail(id) {
            //this.$refs.orderData.resetFields();
            this.activeName = "0";
            this.dialogForm.id = id
            getDetail({id}).then(res => {
                if (res.code === 0) {
                    let data = res.data.read;
                    this.orderData = data;
                    // this.getLogistics(id);
                    this.setAfterSalesList(data)
                }
            })
        },
        // 售后数据
        setAfterSalesList(data) {
            const {order_items} = data
            this.afterSalesList = []
            if (order_items && order_items.length > 0) {
                order_items.forEach(item => {
                    if (item.after_sales && item.after_sales.id) {
                        this.afterSalesList.push({
                            ...item.after_sales,
                            title: item.title,
                            image_url: item.image_url,
                            sku_title: item.sku_title,
                            qty:item.qty,
                            unit:item.unit,
                            send_status:item.send_status,
                            order_amount: item.amount
                        })
                    }
                })
            }
        },
        openDialog() {
            this.dialogIsShow = true;
            this.dialogForm.note = this.orderData.note || ""
        },
        confirm() {
            addNote(this.dialogForm).then(res => {
                if (res.code === 0) {
                    this.$message.success(res.msg)
                    this.getOrderDetail(this.dialogForm.id)
                    this.handleDialogClose()
                }
            })
        },
        handleDialogClose() {
            this.dialogIsShow = false;
            this.$refs.dialogForm.resetFields();
        },
        handleClose() {
            this.isShow = false;
        },

        //查看会员信息
        toUserInfo(id) {
            this.$refs.userDetail.isShow = true;
            this.$refs.userDetail.is_back = 0;
            this.$nextTick(() => {
                this.$refs.userDetail.getUserDetail(id);
            });
        },

        //物流信息
        expressInfo(express) {
            if (!express.express_no && !express.company_name) {
                return "无物流信息"
            }
            let s = "";
            s = express.company_name || "";
            s += express.express_no ? "【" + express.express_no + "】" : "";
            return s;
        }
    },
};