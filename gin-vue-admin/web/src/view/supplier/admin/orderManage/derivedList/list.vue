<template>
    <m-card>
        <el-form :model="searchInfo" label-width="90px" class="search-term" inline>
          <el-form-item label="" >
            <el-input placeholder="请输入导出人昵称" v-model="searchInfo.key_word" class="line-input" clearable>
                <span slot="prepend">导出人</span>
            </el-input>
          </el-form-item>
          <el-form-item>
              <div class="line-input" >
                  <div class="line-box ">
                      <span >导出状态</span>
                  </div>
                  <el-select v-model="searchInfo.error_status" class="w100" clearable>
                      <el-option label="成功" :value="1"></el-option>
                      <el-option label="失败" :value="2"></el-option>
                  </el-select>
              </div>
          </el-form-item>
          <el-form-item>
            <div class="line-input line-input-date">
                <div class="line-box">
                    <el-select class="w100" v-model="searchInfo.dateType">
                    <el-option :key="item.id" :label="item.label" :value="item.value"
                                v-for="item in dateTypeOptios"></el-option>
                    </el-select>
                </div>
                <div class="f fac">
                    <el-form-item label-width='0px'>
                        <el-date-picker class="w100" placeholder="开始日期" value-format="yyyy-MM-dd HH:mm:ss" type="datetime"
                                        v-model="searchInfo.start_at">
                        </el-date-picker>
                    </el-form-item>
                    <p class="title-3">至</p>
                    <el-form-item label-width='0px'>
                        <el-date-picker class="w100" placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss" type="datetime"
                                        v-model="searchInfo.end_at">
                        </el-date-picker>
                    </el-form-item>
                </div>
            </div>
          </el-form-item>

          <br>
          <el-form-item>
              <el-button type="primary" @click="search" >搜索</el-button>
              <el-button type="text" @click="reSearch">重置搜索条件</el-button>
          </el-form-item>
            <!-- <el-row>

              <el-col :span="6">
                <el-form-item label="导出人:">
                  <el-input clearable placeholder="请输入导出人昵称" v-model="searchInfo.key_word"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="18">
                <el-row :gutter="10">
                  <el-col :span="16">
                    <el-form-item>
                      <span slot="label">时间类型:</span>
                      <el-row style="padding: 0;">
                        <el-col :span="6" style="padding-right: 10px">
                          <el-select class="w100" v-model="searchInfo.dateType">
                            <el-option :key="item.id" :label="item.label" :value="item.value"
                                       v-for="item in dateTypeOptios"></el-option>
                          </el-select>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label-width='0px'>
                            <el-date-picker class="w100" placeholder="开始日期" value-format="yyyy-MM-dd HH:mm:ss" type="datetime"
                                            v-model="searchInfo.start_at">
                            </el-date-picker>
                          </el-form-item>
                        </el-col>
                        <el-col :span="2">
                          <p class="title-3">至</p>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label-width='0px'>
                            <el-date-picker class="w100" placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss" type="datetime"
                                            v-model="searchInfo.end_at">
                            </el-date-picker>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
                <el-col :span="6">
                    <el-form-item label="导出状态:">
                        <el-select v-model="searchInfo.error_status" class="w100" clearable>
                            <el-option label="成功" :value="1"></el-option>
                            <el-option label="失败" :value="2"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col>
                    <el-form-item>
                        <el-button type="primary" @click="search">搜索</el-button>
                        <el-button type="text" @click="reSearch">重置搜索条件</el-button>
                    </el-form-item>
                </el-col>
            </el-row> -->
        </el-form>
        <el-table :data="tableData" class="mt25">
          <el-table-column label="订单最早下单时间" align="center">
            <template slot-scope="scope">
              {{ scope.row.start_order_at | formatDate }}
            </template>
          </el-table-column>
          <el-table-column label="订单最晚下单时间" align="center">
            <template slot-scope="scope">
              {{ scope.row.end_order_at | formatDate }}
            </template>
          </el-table-column>
            <el-table-column label="订单数量" prop="order_count" align="center"></el-table-column>
            <el-table-column label="导出人"  align="center">
              <template slot-scope="scope">

                <span v-if="scope.row.sys_user.id == 0">{{ scope.row.user.nick_name }}</span>
                <span v-else>{{ scope.row.sys_user.nick_name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="导出时间" align="center">
              <template slot-scope="scope">
                {{ scope.row.created_at | formatDate }}
              </template>
            </el-table-column>
            <el-table-column label="导出状态" prop="status_string" align="center"></el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button type="text" v-if="scope.row.link" @click="download(scope.row.link)">
                        下载
                    </el-button>
                    <el-button type="text" class="color-red" @click="del(scope.row)">
                        删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100, 200]"
            :style="{ display: 'flex', justifyContent: 'flex-end', marginRight: '20px' }"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes,prev, pager, next, jumper"
        ></el-pagination>
    </m-card>
</template>
<script>
import { exportOrderRecordList } from '@/api/supplier/order';
import infoList from '@/mixins/infoList';
import Daterange from '@/components/mDate/daterange';

export default {
    name: 'SupplierOrderDerivedList',
    mixins: [infoList],
    components: { Daterange },
    data() {
        return {
            isShow: false,
            listApi: exportOrderRecordList,
            searchDate: [],
        };
    },
    mounted() {
        this.getTableData();
    },
    methods: {
        search() {
            this.page = 1;
            if (this.searchDate.length) {
                this.searchInfo.start_at = this.searchDate[0];
                this.searchInfo.end_at = this.searchDate[1];
            }
            this.getTableData();
        },
        reSearch() {
            this.page = 1;
            this.searchDate = [];
            this.searchInfo = {};
        },
        download(link) {
            window.open(this.$path + '/' + link);
        },
        del(row) {
            this.$fn.confirm(function () {
                deleteOrderExportRecord({ id: row.id }).then(res => {
                    if (res.code === 0) {
                        this.$message.success(res.msg);
                        this.getTableData();
                    }
                });
            }, '确定删除吗?');
        },
    },
};
</script>
<style lang="scss" scoped></style>
