<template>
  <el-dialog
      :title="formData.id ? '编辑' : '新增'"
      :visible="isShow"
      width="600px"
      :before-close="handleClose">
    <el-form :model="formData" label-width="90px">
      <el-form-item label="商品来源:">
        <el-input v-model="formData.name" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="分类:">
        <el-select v-model="formData.supplier_source_category_id" clearable filterable class="w100">
          <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirm">确 定</el-button>
      <el-button @click="handleClose">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {createSupplierSource, updateSupplierSource, getSupplierSourceCategoryOptionList} from "@/api/supplier/source";

export default {
  name: "sourceDialog",
  data() {
    return {
      formData: {
        name: "",
        supplier_source_category_id: null
      },
      isShow: false,
      options: []
    }
  },
  methods: {
    init(data = {}) {
      this.isShow = true
      this.getOption()
      if (data) {
        this.formData.name = data.name
        this.formData.supplier_source_category_id = data.supplier_source_category_id
        this.formData.id = data.id
      }
    },
    async getOption() {
      const {code, data} = await getSupplierSourceCategoryOptionList()
      if (code === 0) {
        this.options = data.list
      }
    },
    async confirm() {
      if (!this.formData.name) {
        this.$message.error("请输入商品来源")
        return false;
      }
      if (!this.formData.supplier_source_category_id) {
        this.$message.error("请选择分类")
        return false;
      }
      if (this.formData.id) {
        const {code, msg} = await updateSupplierSource(this.formData)
        if (code === 0) {
          this.$message.success(msg)
          this.$emit("reload")
          this.handleClose()
        }
      } else {
        const {code, msg} = await createSupplierSource(this.formData)
        if (code === 0) {
          this.$message.success(msg)
          this.$emit("reload")
          this.handleClose()
        }
      }
    },
    handleClose() {
      this.isShow = false
      this.formData = {
        name: "",
        supplier_source_category_id: null
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>