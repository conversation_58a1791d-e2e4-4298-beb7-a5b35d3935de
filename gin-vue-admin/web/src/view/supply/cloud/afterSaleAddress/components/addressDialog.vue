<template>
  <el-dialog :title="title" :visible="isShow" width="50%" :before-close="handleClose">
    <el-form :model="formData" ref="form" label-width="145px">
      <el-form-item label="姓名:" prop="real_name">
        <el-input v-model="formData.real_name" placeholder="请输入" clearable></el-input>
      </el-form-item>
      <el-form-item label="手机号:" prop="mobile">
        <el-input v-model="formData.mobile" placeholder="请输入" clearable></el-input>
      </el-form-item>
      <el-form-item label="省:" prop="province_id">
        <el-select  @change="handleClassiyfChange(2,formData.province_id)"
            v-model="formData.province_id" filterable clearable placeholder="请选择" class="w100">
          <el-option  v-for="item in provinceOption" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="市:" prop="city_id">
        <el-select  @change="handleClassiyfChange(3,formData.city_id)"
            v-model="formData.city_id" filterable clearable placeholder="请选择" class="w100">
          <el-option v-for="item in cityOption" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="区:" prop="district_id">
        <el-select v-model="formData.district_id" filterable clearable placeholder="请选择" class="w100">
          <el-option v-for="item in districtOption" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="详细地址:" prop="address">
        <el-input v-model="formData.address" placeholder="请输入" clearable></el-input>
      </el-form-item>
      <el-form-item prop="is_default" label="是否为默认:">
        <el-radio-group v-model="formData.is_default">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button type="primary" @click="confirm">提 交</el-button>
      <el-button @click="handleClose">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {getRegionList , addRefundAddress , updateRefundAddress} from "@/api/cloud";

export default {
  name: "addressDialog",
  data() {
    return {
      gather_supply_id:this.$ls.getGatherID(),
      isShow: false,
      title: "添加售后地址",
      provinceOption:[],
      cityOption:[],
      districtOption:[],
      formData:{
        id:null,
        is_default:1 ,// // 1是 0否
        real_name:"",//姓名
        mobile:"",//手机号
        province_id:"",//省
        city_id:"",//市
        district_id:"",//区
        address:"",//地址
        }
    }
  },
  mounted() {
   this.getRegion()
  },
  methods: {
    //修改
   async setAddress(row){
      //获取市
      await this.handleClassiyfInit(2, row.province_id)
      await this.handleClassiyfInit(3, row.city_id)

      this.isShow = true
      this.title = "修改售后地址"
      this.formData = {...row}
      this.formData.id=row.id
      this.formData.is_default=row.is_default
      this.formData.real_name=row.real_name
      this.formData.mobile=row.mobile
      this.formData.address=row.address
      this.formData.province_id=row.province_id
      this.formData.city_id=row.city_id
      this.formData.district_id=row.district_id
    },
    getRegion(){
      getRegionList(1,0 ,this.gather_supply_id).then(res=>{
        this.provinceOption=res.data
      })
    },
    //获取类目
    handleClassiyfChange(level, pid) {
      getRegionList(level, pid, this.gather_supply_id).then(res => {
        if (level === 3) {
          this.districtOption = [];
          this.formData.district_id = ""
        } else {
          this.cityOption = [];
          this.formData.city_id = ""
          this.districtOption = [];
          this.formData.district_id = ""
        }
        switch (level) {
          case 2:
            this.cityOption = res.data
            break;
          case 3:
            this.districtOption = res.data

            break;
        }
      })
    },
    //初始化类目
    async handleClassiyfInit(level, pid) {
      const res=await getRegionList(level, pid, this.gather_supply_id)

      if (level === 3) {
        this.districtOption = [];
        // this.formData.district_id = ""
      } else {
        this.cityOption = [];
        // this.formData.city_id = ""
        this.districtOption = [];
        // this.formData.district_id = ""
      }
      switch (level) {
        case 2:
          this.cityOption = res.data
          break;
        case 3:
          this.districtOption = res.data

          break;
      }
    },
    //提交
    confirm() {
      let data = {}
      data.is_default = this.formData.is_default
      data.real_name = this.formData.real_name
      data.mobile = this.formData.mobile
      data.address = this.formData.address
      data.province_id = this.formData.province_id
      data.city_id = this.formData.city_id
      data.district_id = this.formData.district_id
      data.gather_supply_id = this.gather_supply_id
      data.id=this.formData.id
      if (this.formData.id) {
        updateRefundAddress(data).then(res => {
          if (res.code === 0) {
            this.$message.success(res.msg)
            this.handleClose( true)
          }
        })
      } else {
        addRefundAddress(data).then(res => {
          if (res.code === 0) {
            this.$message.success(res.msg)
            this.handleClose(true)
          }
        })
      }
    },
    handleClose(reload=false) {
      try {
        this.$refs.form.resetFields();
      } catch {
      } finally {
        this.isShow = false
        this.title = "添加售后地址"
        this.formData = {
          id:null,
          is_default:1 ,// // 1是 0否
          real_name:"",//姓名
          mobile:"",//手机号
          address:"",//地址
          province_id:"",//省
          city_id:"",//市
          district_id:""//区
        }
        if (reload) this.$emit("reload")
      }
    }
  }
}
</script>

