<template>
  <el-dialog :title="`${title}运费模板`" :visible="isShow" width="60%" :before-close="handleClose">
    <el-form :model="formData" :rules="rules" ref="form" label-width="145px">
      <el-form-item label="排序:" prop="sort">
        <el-input-number :min="0" :precision="0" :controls="false" class="number-text-left w100" v-model="formData.sort" placeholder="请输入" clearable></el-input-number>
      </el-form-item>
      <el-form-item label="配送方式名称:" prop="name">
        <el-input v-model="formData.name" placeholder="请输入" clearable></el-input>
      </el-form-item>
      <el-form-item prop="is_default" label="是否为默认快递模板:">
        <el-radio-group v-model="formData.is_default">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="charge_type" label="计费方式:">
        <el-radio-group v-model="formData.charge_type">
          <el-radio v-for="item in typeOptions" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
        <el-form-item label="配送区域及运费:" prop="dispatching">
          <el-table :data="formData.dispatching" class="area-table">
            <el-table-column label="可配送区域">
              <template slot-scope="scope">
                <div class="mt20">
                </div>
                <div class="f fac fjsb">
                 <span class="f1">
                    <span class="f1" v-for="item in scope.row.a" :key="item.id" >
                      {{item}}
                    </span>
                 </span>
                  <div v-if="scope.row.a!=='全国'">
                    <el-button type="text" @click="aredEdit(scope.row, scope.$index)">修改</el-button>
                    <el-button type="text" @click="areaDel(scope.row, scope.$index)" >删除</el-button>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column :label="formData.charge_type === 1 ? '首重 (g)' : '首件 (个)' " align="center" width="130">
              <template slot-scope="scope">
                <el-form-item :prop="`dispatching.`+scope.$index+`.f`" :rules="freight.f">
                  <el-input-number v-model="scope.row.f" class="input-number-text-left" :min="0" :precision="0" :controls="false"></el-input-number>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="运费 (元) " align="center" width="130">
              <template slot-scope="scope">
                <el-form-item :prop="`dispatching.`+scope.$index+`.fp`" :rules="freight.fp">
                  <el-input-number v-model="scope.row.fp" class="input-number-text-left" :controls="false" :min="0" :precision="2"></el-input-number>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column :label="formData.charge_type === 1 ? '续重 (g)' : '续件 (个)' " align="center" width="130">
              <template slot-scope="scope">
                <el-form-item :prop="`dispatching.`+scope.$index+`.n`" :rules="freight.n">
                  <el-input-number v-model="scope.row.n" class="input-number-text-left" :min="0" :precision="0" :controls="false"></el-input-number>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="续费 (元) " align="center" width="130">
              <template slot-scope="scope">
                <el-form-item :prop="`dispatching.`+scope.$index+`.np`" :rules="freight.np">
                  <el-input-number v-model="scope.row.np" class="input-number-text-left" :controls="false" :min="0" :precision="2"></el-input-number>
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
          <el-button icon="el-icon-map-location" @click="openRegion">点击添加可配送区域和运费</el-button>
        </el-form-item>
      <el-form-item prop="publish" label="是否启用:">
        <el-radio-group v-model="formData.publish">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div slot="footer">
<!--      <el-button type="primary" @click="confirm">提 交</el-button>-->
      <el-button @click="handleClose">取 消</el-button>
    </div>
    <select-region ref="SelectRegion" @getCheckedRegion="getCheckedRegion" ></select-region>
  </el-dialog>
</template>

<script>
 import {regionData} from "element-china-area-data";
import {updateFreight , addFreight ,getCloudFreight} from "@/api/cloud"
import SelectRegion from "../selectRegionPage/selectRegion";

 /**
  * 获取数组对象指定k的下标
  */
 Array.prototype.indexOfJSON = function (kName, value) {
   for (var i = 0; i < this.length; i++) {
     if (this[i][kName] == value) return i;
   }
   return -1;
 };
 export default {
  name: "freightTempDialog",
   components:{
     SelectRegion
   },
  data() {
    return {
      options: regionData,
      isShow: false,
      title: "新增",
      gather_supply_id:this.$ls.getGatherID(),
      formData:{
        id:null,
        freight_id:null,
        sort:"",//排序
        name:"",//名称
        is_default: 0,//是否默认快递模板1是0否
        dispatching:[
          {a:"全国"}
        ],//配送区域
        charge_type: 1, //计费方式 // 1重量 2件
        publish: 0,//是否启用 1是0否
        created:null,//创建时间
        modified:null,//修改时间
      },
      typeOptions: [
        {
          label: "按重量",
          value: 1,
        },
        {
          label: "按件数",
          value: 2,
        },
      ],
      rules: {
        name:[
          {required: true, message: "请输入配送方式名称", trigger: "blur"},
        ],

      },
      freight: {
        f: {
          required: true, message: "请填写", trigger: "blur"
        },
        fp: {
          required: true, message: "请填写", trigger: "blur"
        },
        n: {
          required: true, message: "请填写", trigger: "blur"
        },
        np: {
          required: true, message: "请填写", trigger: "blur"
        }
      },
      regions:[{}],//修改的数组
    }
  },
   methods:{
     // 获取选中的地市
     getCheckedRegion(checkedArea,index) {
       let region = [];
       checkedArea.forEach(item => {
         if (item.is_checked === true) {
            region.push(item.name)
         }else if(item.is_checked_num > 0) {
         item.children.forEach(city=>{
           region.push(city.name)
         })
         }
       })
       if(index >= 0 && index !== null){
          this.formData.dispatching[index].a=region.join(";")
          this.regions[index]=checkedArea
        }else {
          this.formData.dispatching.push({a:region.join(";")})
          this.regions.push(checkedArea)
        }
     },
     //编辑
     findDate(row){
       getCloudFreight({gather_supply_id:this.gather_supply_id,freight_id:row.id}).then(res=>{
         if (res.code===0){
           let data=res.data
           this.formData.id=data.id
           this.formData.sort=data.sort
           this.formData.name=data.name
           this.formData.is_default=data.is_default
           this.formData.publish=data.publish
           this.formData.charge_type=data.charge_type
           this.formData.created=data.created
           this.formData.modified=data.modified
           data.dispatching.forEach((item) => {
             item.fp = this.$fn.changeMoneyF2Y(item.fp)
             item.np = this.$fn.changeMoneyF2Y(item.np)
           })
           this.formData.dispatching=data.dispatching
            data.dispatching.forEach((item)=>{
              if(item.area!==null){
                item.area.forEach((res)=>{
                  if(res.children===null){
                    res.is_checked=true
                  }else {
                    res.is_checked=false
                    res.is_checked_num=res.children.length
                    res.children.forEach((para)=>{
                      para.is_checked=true
                    })
                  }
                })
                this.regions.push(item.area)
              }
            })
         }
       })

     },
     //修改配送区域
     aredEdit(row ,index) {
       this.$refs.SelectRegion.isShow = true
       // this.$refs.SelectRegion.getArea()
       this.$refs.SelectRegion.disposeData(this.regions[index],index)
     },
     //删除配送区域
     areaDel(row, index) {
       this.$confirm("你确定要删除该配送区域么?", "提示", {
         confirmButtonText: "确定",
         cancelButtonText: "取消",
       }).then(() => {
             this.formData.dispatching.splice(index, 1);
           })
           .catch(() => { });
     },
     openRegion(){
      this.$refs.SelectRegion.isShow=true
      this.$refs.SelectRegion.getArea()
     },

     confirm(){
       this.$refs.form.validate((valid) => {
         if (!valid) return false;
         let dispatching = JSON.parse(JSON.stringify(this.formData.dispatching))
         dispatching.forEach((item) => {
            item.fp = this.$changeMoneyY2F(item.fp);
            item.np = this.$changeMoneyY2F(item.np);
         });
         let data = {}
         data.sort = this.formData.sort
         data.name = this.formData.name
         data.is_default = this.formData.is_default
         data.dispatching = dispatching
         data.publish = this.formData.publish
         data.gather_supply_id = this.gather_supply_id
         data.id=this.formData.id
         data.charge_type = this.formData.charge_type
         data.created=this.formData.created
         data.modified=this.formData.modified
         if (this.formData.id) {
           updateFreight(data).then(res => {
             if (res.code === 0) {
               this.$message.success(res.msg)
               this.handleClose(true)
             }
           })
           return
         }
         addFreight(data).then(res => {
           if (res.code === 0) {
             this.$message.success(res.msg)
             this.handleClose(true)
           }
         })
       })
     },

     handleClose(reload = false){
       this.isShow = false;
       try {
         this.$refs.form.resetFields();
       } catch { } finally {
         this.title = "新增",
         this.formData={
           id:null,
           freight_id:null,
           sort:"",//排序
           name:"",//名称
           is_default: 0,//是否默认快递模板1是0否
           dispatching:[  {a:"全国"}],//配送区域
           charge_type: 1, //计费方式 // 1重量 2件
           publish: 0,//是否启用 1是0否
         }
         this.regions = [{}]
       }
         if (reload) this.$emit("reload")
       }


}}
</script>

<style lang="scss" scoped>
@import "@/style/base.scss";
.area-table {
  margin-bottom: 20px;
}
.el-input-number {
  width: 100%;
}


</style>