<template>
  <div>
    <el-form :model="searchInfo" ref="searchInfoRef" class="search-term mt25" label-width="135px">
      <el-row :gutter="10">
        <el-col :xl="6" :lg="12">
          <el-form-item label="商品状态:" prop="goodsStatus">
            <el-select v-model="goodsStatus" clearable filterable class="w100">
              <el-option :value="item.value" v-for="item in goodsStatusList" :key="item.id" :label="item.name">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="6" :lg="12">
          <el-form-item label="一级分类:" prop="category1_id">
            <el-select @change="handleClassiyfChange(2,searchInfo.category1_id)"
                       v-model="searchInfo.category1_id" filterable clearable placeholder="请选择一级分类" class="w100">
              <el-option v-for="item in categoryList1" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="6" :lg="12">
          <el-form-item label="二级分类:" prop="category2_id">
            <el-select @change="handleClassiyfChange(3,searchInfo.category2_id)"
                       v-model="searchInfo.category2_id" filterable clearable placeholder="请选择二级分类" class="w100">
              <el-option v-for="item in categoryList2" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="6" :lg="12">
          <el-form-item label="三级分类:" prop="category3_id">
            <el-select v-model="searchInfo.category3_id" filterable clearable placeholder="请选择三级分类"
                       class="w100">
              <el-option v-for="item in categoryList3" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="6" :lg="12">
          <el-form-item label="供应商:" prop="supplier_id ">
            <el-select v-model="searchInfo.supplier_id" clearable filterable class="w100">
              <el-option label="全部" value=""></el-option>
              <el-option label="平台自营" value="0"></el-option>
              <el-option :value="item.id" v-for="item in supplierOptions" :key="item.id" :label="item.name">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="6" :lg="12">
          <el-form-item label="供应链:" prop="res_gather_supply_id">
            <el-select v-model="searchInfo.res_gather_supply_id" clearable filterable class="w100">
              <el-option label="全部" value=""></el-option>
              <el-option label="平台自营" value="0"></el-option>
              <el-option :value="item.id" v-for="item in supplyOptions" :key="item.id" :label="item.name">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="6" :lg="12">
          <el-form-item label="商品名称或关键字:" prop="title">
            <el-input v-model="searchInfo.title" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="6" :lg="12">
          <el-form-item label="聚水潭店铺名称:" prop="title">
            <el-input v-model="searchInfo.jushuitan_distributor_supplier_name" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="6" :lg="12">
          <el-form-item label="商品id:" prop="title">
            <el-input v-model="searchInfo.id" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="6" :lg="12">
          <el-form-item label="商品条形码:" prop="barcode">
            <el-input v-model="searchInfo.barcode" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="6" :lg="12">
          <el-form-item label="状态筛选:" prop="cloud_status">
            <el-select v-model="searchInfo.cloud_status" filterable clearable placeholder="请选择状态"
                       class="w100">
              <el-option v-for="item in stateOption" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="6" :lg="12">
          <el-form-item label="价格区间:">
            <div class="f fac">
              <el-form-item prop="minPrice">
                <el-input-number v-model="searchInfo.minPrice" placeholder="最低价" :min="0" :precision="0" :controls="false"></el-input-number>
              </el-form-item>
              <span class="zih-span">至</span>
              <el-form-item prop="maxPrice">
                <el-input-number v-model="searchInfo.maxPrice" placeholder="最高价" :min="0" :precision="0" :controls="false"></el-input-number>
              </el-form-item>
            </div>
          </el-form-item>
        </el-col>

      </el-row>
      <el-form-item>
        <el-button type="primary" @click="search">搜索</el-button>
        <el-button type="text" @click="research">重置搜索条件</el-button>
      </el-form-item>
    </el-form>
    <el-tabs v-model="tabGoodsStatus" @tab-click="handleTabsClick" type="card" class="mt25 goods-tabs">
      <el-tab-pane v-for="item in goodsTapStatusList" :key="item.id" :label="item.name" :name="item.value">
      </el-tab-pane>
    </el-tabs>

    <el-table ref="multipleTable" height="900" :data="tableData" class="mt25"  @selection-change="handleSelectionChange">
      <el-table-column type="selection" width=55></el-table-column>
      <el-table-column label="ID" prop="id" align="center"></el-table-column>
      <el-table-column label="商品图片" align="center">
        <template slot-scope="scope">
          <m-image :src="scope.row.image_url" style="width: 60px;height: 60px;"></m-image>
        </template>
      </el-table-column>
      <el-table-column label="商品名称" align="center" width="200">
        <template slot-scope="scope">
        <div @click="viewDetail(scope.row)" style="cursor: pointer;">
          <p type="text" style="word-break: break-all; color: #1a61dc"> {{scope.row.title}}</p>
        </div>
        </template>
      </el-table-column>
      <el-table-column label="市场价格" align="center">
        <template slot-scope="scope">
          {{ scope.row.origin_price | formatF2Y }}元
        </template>
      </el-table-column>
      <el-table-column label="销售价格" align="center">
        <template slot-scope="scope">
          {{ scope.row.price | formatF2Y }}元
        </template>
      </el-table-column>
      <el-table-column label="成本价格" align="center">
        <template slot-scope="scope">
          {{ scope.row.cost_price | formatF2Y }}元
        </template>
      </el-table-column>
      <el-table-column label="预计云仓市场价格" align="center">
        <template slot-scope="scope">
          {{ scope.row.cloud_price.cloud_origin_price | formatF2Y }}元
        </template>
      </el-table-column>
      <el-table-column label="预计云仓指导价格" align="center">
        <template slot-scope="scope">
          {{ scope.row.cloud_price.cloud_guide_price | formatF2Y }}元
        </template>
      </el-table-column>
      <el-table-column label="预计云仓结算价格" align="center">
        <template slot-scope="scope">
          {{ scope.row.cloud_price.cloud_price | formatF2Y }}元
        </template>
      </el-table-column>
      <el-table-column label="供应商" align="center" width="150">
        <template slot-scope="scope" >
          {{ scope.row.supplier.name }}
        </template>
      </el-table-column>
      <el-table-column label="供应链" align="center" width="150">
        <template slot-scope="scope" >
          {{ scope.row.gather_supply.name }}
        </template>
      </el-table-column>
      <el-table-column label="库存" prop="stock" align="center"></el-table-column>
      <el-table-column label="销量" prop="sales" align="center"></el-table-column>
      <el-table-column label="状态" align="center" width="200">
        <template slot-scope="scope" >
          <span v-if="scope.row.cloud_goods.id && scope.row.cloud_goods.is_update===0 && scope.row.cloud_goods.is_delete===0">已添加</span>
          <span v-if="scope.row.cloud_goods.id===0">未添加</span>
          <span v-if="scope.row.cloud_goods.is_update===1">待更新</span>
          <span v-if="scope.row.cloud_goods.is_delete===1">待删除 </span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background :current-page="page" :page-size="pageSize" :page-sizes="[10, 30, 50, 100]" :style="{
              display: 'flex',
              justifyContent: 'flex-end',
              marginRight: '20px',
            }" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange"
                   layout="total, sizes, prev, pager, next, jumper">

    </el-pagination>
    <el-form :model="pushInfo" :rules="pushInfoRules" ref="pushInfoForm" class="search-term mt25" label-width="135px">
      <el-row :gutter="10">
        <el-col :xl="6" :lg="12">
          <el-form-item label="一级分类:" prop="cloud_category1_id">
            <el-select @change="getCateBusinessChange(1,$event)"
                       v-model="pushInfo.cloud_category1_id" filterable clearable placeholder="请选择一级分类" class="w100">
              <el-option v-for="item in categoryListOne" :key="item.id" :label="item.label" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="6" :lg="12">
          <el-form-item label="二级分类:" prop="cloud_category2_id">
            <el-select @change="getCateBusinessChange(2,$event)"
                       v-model="pushInfo.cloud_category2_id" filterable clearable placeholder="请选择二级分类" class="w100">
              <el-option v-for="item in categoryListTwo" :key="item.id" :label="item.label" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="6" :lg="12">
          <el-form-item label="三级分类:" prop="cloud_category3_id">
            <el-select v-model="pushInfo.cloud_category3_id" filterable clearable placeholder="请选择三级分类"
                       class="w100" >
              <el-option v-for="item in categoryListThree" :key="item.id" :label="item.label" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="6" :lg="12">
          <el-form-item label="售后时长:" prop="aftersale_time">
            <el-select v-model="pushInfo.aftersale_time" clearable filterable class="w100">
              <el-option :value="item.value" v-for="item in afterTime" :key="item.value" :label="item.name">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="6" :lg="12">
          <el-form-item label="运费模板:" prop="freight_id">
            <el-select v-model="pushInfo.freight_id" clearable filterable class="w100">
              <el-option :value="item.id" v-for="item in freightOptions" :key="item.id" :label="item.name">
              </el-option>
              <div class="text-center">
                  <el-pagination background small class="pagination"
                                  style="padding-top:10px !important;padding-bottom: 0 !important;"
                                  :current-page="freightPage"
                                  :page-size="freightPageSize"
                                  :total="freightTotal"
                                  @current-change="handleFreightPage"
                                  layout="prev,pager, next"/>
              </div>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="6" :lg="12">
          <el-form-item label="服务标签:" prop="currentSelLable">
            <el-select v-model="pushInfo.currentSelLable" clearable  class="w100" multiple >
              <el-option :value="item.id" v-for="item in serveOptions" :key="item.id" :label="item.tags">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="6" :lg="12">
          <el-form-item label="发货延期:" prop="delay_compensate">
            <el-select v-model="pushInfo.delay_compensate" clearable filterable class="w100">
              <el-option :value="item.value" v-for="item in postponeTime" :key="item.value" :label="item.name">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="6" :lg="12">
          <el-form-item label="关键字:" prop="goods_des">
            <el-input v-model="pushInfo.goods_des" placeholder="请输入关键字,英文逗号连接" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="6" :lg="12">
          <el-form-item label="产地:" prop="producing_area">
            <el-input v-model="pushInfo.producing_area" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="6" :lg="12">
          <el-form-item label="发货地:" prop="deliver_area">
            <el-input v-model="pushInfo.deliver_area" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item>
        <el-button @click="selectAll(tableData)">全选</el-button>
        <el-button type="success" @click="goodsPush" >推送商品</el-button>
        <el-button type="danger" @click="allGoodsPush" >推送全部商品</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>

import {getProductsList, getCategorys, getCateBusinessList, getStagsList, getFreightList ,cloudPushGoods ,cloudAllPushGoods ,getGatherSupplies ,getSupplierOptionList} from "@/api/cloud"

export default {
  name: "cloudManageIndex",
  data() {
    return {
      freightPage: 1,
      freightPageSize: 50,
      freightTotal: 0,
      gather_supply_id:this.$ls.getGatherID(),
      page: 1,
      pageSize: 50,
      total: 0,
      tableData: [],
      supplierOptions:[],//供应商
      supplyOptions:[],//供应链
      //筛选状态
      stateOption:[
        {name: "状态不限", id:null},
        {name: "未添加", id: 0},
        {name: "已添加", id: 1},
        {name: "待更新", id: 2},
        {name: "待删除", id: 3},
      ],
      // 类目1
      categoryList1: [],
      categoryList2: [],
      categoryList3: [],
      //推送部分类目
      categoryListOne: [],
      categoryListTwo: [],
      categoryListThree: [],
      levelOptions: [],
      freightOptions: [],//运费模板
      serveOptions: [],//服务标签
      //售后时长
      afterTime: [
        {name: "7天", value: 7},
        {name: "15天", value: 15},
        {name: "30天", value: 30},
      ],
      //发货延期
      postponeTime: [
        {name: "24小时", value: 24},
        {name: "48小时", value: 48},
        {name: "72小时", value: 72},
        {name: "0小时", value: 0},
      ],
      goodsStatus: 0,
      tabGoodsStatus:0,
      goodsStatusList: [
        {name: "全部", value: 0},
        {name: "上架", value: 1},
        {name: "下架", value: 2},
        {name: "售罄", value: 3},
      ],
      goodsTapStatusList: [
        {name: "全部", value: "0"},
        {name: "上架", value: "1"},
        {name: "下架", value: "2"},
        {name: "售罄", value: "3"},
      ],
      searchInfo: {
        id:0,
        filter: 0,//商品状态
        title: "",//商品名称
        category1_id: null,//一级分类
        category2_id: null,//二级分类
        category3_id: null,//三级分类
        barcode: "",//条形码
        maxPrice: "",//最高价
        minPrice: "",//最低价
        supplier_id:null,//供应商
        res_gather_supply_id:null,//供应链
        cloud_status:null,//筛选状态
        jushuitan_distributor_supplier_name:"",//聚水潭店铺名称
      },
      pushInfo: {
        product_ids:"",//商品id
        cloud_category1_id: "",//一级分类
        cloud_category2_id: "",//二级分类
        cloud_category3_id: "",//三级分类
        freight_id: "",//运费模板
        aftersale_time: "",//售后时长
        delay_compensate: "",//发货延期
        stags: "",//服务标签
        producing_area:"",//产地
        deliver_area:"",//发货地
        goods_des:"",//关键字
        is_have_default: 0,
        currentSelLable:[]//服务标签
      },
      pushInfoRules:{
        cloud_category1_id: {required: true, message: '请输入一级分类', trigger: 'change'},
        cloud_category2_id: {required: true, message: '请输入二级分类', trigger: 'change'},
        cloud_category3_id: {required: true, message: '请输入三级分类', trigger: 'change'},
        freight_id: {required: true, message: '请输入运费模板', trigger: 'change'},
        aftersale_time: {required: true, message: '请输入售后时长', trigger: 'change'},
        delay_compensate: {required: true, message: '请输入发货延期', trigger: 'change'},
        currentSelLable: { type: 'array', required: true, trigger: ['blur','change'], message: '请输入服务标签' },
        producing_area: {required: true, message: '请输入产地', trigger: 'blur'},
        deliver_area: {required: true, message: '请输入发货地', trigger: 'blur'},
        goods_des: {required: true, message: '请输入关键字', trigger: 'blur'},
      },
      multipleSelection:[],//选中的数据

    }
  },
  mounted() {
    this.getGatherSupplies()
    this.getSupplierOptionList()
    if (this.gather_supply_id) {
      this.initOptions()
      this.getList()
      this.getFreightList()
    } else {
      this.$message.error("操作失败")
      this.$router.back();
    }
  },

  methods: {


    //供应链
    getGatherSupplies(){
      getGatherSupplies().then(res=>{
        if(res.code===0){
          this.supplyOptions=res.data
        }
      })
    },
    //供应商
    getSupplierOptionList(){
      getSupplierOptionList().then(res=>{
        if(res.code===0){
          this.supplierOptions=res.data.list
        }
      })
    },
    //点击商品名跳转详情
    viewDetail(row){
      if(row.id !==0){
        this.$_blank("/layout/goodsIndex/addGoods", { id:row.id})
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    //全选
    selectAll(rows){
      if (rows) {
        rows.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      } else {
        this.$refs.multipleTable.clearSelection();
      }
    },
    //推送全部商品
    allGoodsPush(){

        this.$refs.pushInfoForm.validate((valid) => {
          if (!valid) return false
          this.$confirm('确定要推送全部商品吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(()=>{
          let arr=[];
          this.pushInfo.currentSelLable.forEach(item=>{
            arr.push(item)
          })
          let data={
            "cloud_category1_id":this.pushInfo.cloud_category1_id,
            "cloud_category2_id":this.pushInfo.cloud_category2_id,
            "cloud_category3_id":this.pushInfo.cloud_category3_id,
            "freight_id":this.pushInfo.freight_id,
            "aftersale_time":this.pushInfo.aftersale_time,
            "delay_compensate":this.pushInfo.delay_compensate,
            "stags":arr.join(","),
            "producing_area":this.pushInfo.producing_area,
            "deliver_area":this.pushInfo.deliver_area,
            "goods_des":this.pushInfo.goods_des,
            "gather_supply_id":this.gather_supply_id,
            "filter":this.searchInfo.filter,
            "title":this.searchInfo.title ,
            "category1_id": this.searchInfo.category1_id,
            "category2_id": this.searchInfo.category2_id,
            "category3_id": this.searchInfo.category3_id,
            "barcode": this.searchInfo.barcode,
            "id": this.searchInfo.id,
            "maxPrice": this.searchInfo.maxPrice,
            "minPrice": this.searchInfo.minPrice,
            "cloud_status":this.searchInfo.cloud_status,
            "supplier_id":this.searchInfo.supplier_id,
            "res_gather_supply_id":this.searchInfo.res_gather_supply_id,
            "jushuitan_distributor_supplier_name":this.searchInfo.jushuitan_distributor_supplier_name
          }
          cloudAllPushGoods(data).then(res=>{
            if(res.code===0){
              this.$message.success(res.msg)
            }else {
              this.$message.error(res.msg)
            }

          })
        })
      })

    },
    //推送商品
    goodsPush(){
      this.$refs.pushInfoForm.validate((valid) => {
        if (!valid) return false
            this.$confirm('确定要推送商品吗?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(()=>{
        let arr=[];
        this.pushInfo.currentSelLable.forEach(item=>{
          arr.push(item)
        })
       // this.currentSelLable=arr.join(",");

        let product_ids = [];
        this.multipleSelection.forEach(item => {
          product_ids.push(item.id);
        })
        let data={
          "cloud_category1_id":this.pushInfo.cloud_category1_id,
          "cloud_category2_id":this.pushInfo.cloud_category2_id,
          "cloud_category3_id":this.pushInfo.cloud_category3_id,
          "freight_id":this.pushInfo.freight_id,
          "aftersale_time":this.pushInfo.aftersale_time,
          "delay_compensate":this.pushInfo.delay_compensate,
          "stags":arr.join(","),
          "producing_area":this.pushInfo.producing_area,
          "deliver_area":this.pushInfo.deliver_area,
          "goods_des":this.pushInfo.goods_des,
          "gather_supply_id":this.gather_supply_id,
          "product_ids":product_ids
        }
        cloudPushGoods(data).then(res=>{
            if(res.code===0){
              this.getList() //刷新列表数据
              this.$message.success(res.msg)
            }else {
              this.$message.error(res.msg)
            }
        })
      })
      })
    },
    async initOptions() {
      let gather_supply_id = this.gather_supply_id;
      // 获取一级类目
      let cateRes = await getCategorys(1, 0)
      if (cateRes.code === 0) {
        this.categoryList1 = cateRes.data.list
      }
      //获取推送一级类目
      let businessRes = await getCateBusinessList({gather_supply_id})
      if (businessRes.code === 0) {
        this.categoryListOne = businessRes.data.list
      }

      //获取推送服务标签
      let stagesRes = await getStagsList({gather_supply_id})
      if (stagesRes.code === 0) {
        this.serveOptions = stagesRes.data.list
      }
    },
    handleFreightPage(page){
      this.freightPage = page
      this.getFreightList()
    },
    // 获取推送运费模板
    getFreightList() {
      let data = {
        "page": this.freightPage,
        "pageSize": this.freightPageSize,
        "gather_supply_id": this.gather_supply_id,
        "is_have_default": this.pushInfo.is_have_default
      }
      getFreightList(data).then(res => {
        this.freightOptions = res.data.list.list
        this.freightTotal = res.data.list.count
      })
    },
    //获取推送类目
    getCateBusinessChange(level, id) {
      switch (level) {
        case 1:
          this.pushInfo.category2_id = null
          this.categoryListTwo = []
          this.pushInfo.category3_id = null
          this.categoryListThree = []
          if (id) {
            this.categoryListOne.forEach(item => {
              if (item.id === id) {
                this.categoryListTwo = item.children;
              }
            })
          }
          break;
        case 2:
          this.pushInfo.category3_id = null
          this.categoryListThree = []
          if (id) {
            this.categoryListTwo.forEach(item => {
              if (item.id === id) {
                this.categoryListThree = item.children
              }
            })
          }
          break;
      }

    },
    // tabs切换
    handleTabsClick() {
      this.goodsStatus = parseInt(this.tabGoodsStatus);
      switch (this.tabGoodsStatus) {
        case "0":
          this.searchInfo.filter = parseInt(this.goodsStatus);
          break;
        case "1":
          this.searchInfo.filter = parseInt(this.goodsStatus);
          break;
        case "2":
          this.searchInfo.filter = parseInt(this.goodsStatus);
          break;
        case "3":
          this.searchInfo.filter = parseInt(this.goodsStatus);
          break;
        default:
          break;
      }
      this.page = 1;
      this.pageSize = 10;
      this.getList();
    },

    // 获取类目
    handleClassiyfChange(level, pid) {
      getCategorys(level, pid).then(res => {
        if (level === 3) {
          this.categoryList3 = [];
          this.searchInfo.category3_id = null
        } else {
          this.categoryList2 = [];
          this.searchInfo.category2_id = null
          this.categoryList3 = [];
          this.searchInfo.category3_id = null
        }
        switch (level) {
          case 2:
            this.categoryList2 = res.data.list
            break;
          case 3:
            this.categoryList3 = res.data.list
            break;
        }
      })
    },
    // 获取列表
    getList() {
      this.tableData = [];
      let para = {
        "page": this.page,
        "pageSize": this.pageSize,
        "filter": this.searchInfo.filter,
        "title": this.searchInfo.title,
        "category1_id": this.searchInfo.category1_id,
        "category2_id": this.searchInfo.category2_id,
        "category3_id": this.searchInfo.category3_id,
        "maxPrice": this.searchInfo.maxPrice,
        "minPrice": this.searchInfo.minPrice,
        "barcode": this.searchInfo.barcode,
        "id": this.searchInfo.id,
        "cloud_status":this.searchInfo.cloud_status,
        "supplier_id":this.searchInfo.supplier_id,
        "res_gather_supply_id":this.searchInfo.res_gather_supply_id,
        "jushuitan_distributor_supplier_name":this.searchInfo.jushuitan_distributor_supplier_name
      }
      if (this.gather_supply_id !== "") {
        para.gather_supply_id = this.gather_supply_id
      }
      getProductsList(para).then(res => {
        if (res.code === 0) {
          this.tableData = res.data.list;
          this.total = res.data.total;
        } else {
          this.tableData = [];
          this.total = 0;
        }
      });
    },
    handleCurrentChange(page) {
      this.page = page;
      this.getList();
    },

    handleSizeChange(size) {
      this.pageSize = size;
      this.getList();
    },

    //搜索
    search() {
      this.page = 1;
      this.tabGoodsStatus = this.goodsStatus.toString();
      this.searchInfo.filter = parseInt(this.goodsStatus);//同步数据
      this.getList();
    },
    //重置搜索条件
    research() {
      this.goodsStatus = 0;
      this.tabGoodsStatus = "0";
      this.page = 1;
      this.$refs.searchInfoRef.resetFields();
      this.getList();
    }


  }
}

</script>
<style lang="scss">
.zih-span {
  margin: 0 5px;
}
.cell{
  span{
    padding: 5px;
  }
}

</style>