<!-- 商品导入dialog -->
<template>
  <el-dialog
      :visible="isShow"
      width="50%"
      :before-close="handleClose"
  >
    <div slot="title" class="titletop">
      <span >已选中商品</span>
      <span v-if="$route.query.key === 'yiyatong'">导入商品数量（{{total}}）</span>
    </div>
    <div class="f fjsb">
      <el-button type="danger" @click="clearGoods">取消选择</el-button>
      <div>
        <el-button type="primary" @click="$fn.clicks(importSelectGoods,5000)">导 入</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </div>
    <el-form :model="importForm" label-width="110px" class="search-term mt25">
      <div style="margin-top: 1em; margin-bottom: 1em; color: #dc5d5d">
        1.因条件限制，该操作只能导入前2500个商品
      </div>
      <div v-if="$route.query.key !== 'youxuan'" style="margin-top: 1em; margin-bottom: 1em; color: #dc5d5d">
        2.如不选择分类，则自动匹配当前系统存在的分类，如果分类不存在则自动创建(部分供应链类型不支持,不选择分类可能会无法导入)
      </div>
      <el-form-item label="选择导入分类:">
        <el-row :gutter="10">
          <el-col :xl="7" :lg="7">
            <el-form-item label-width="0px">
              <el-select
                  @change="handleClassiyfChange(2, importForm.fl1)"
                  v-model="importForm.fl1"
                  filterable
                  clearable
                  placeholder="请选择一级分类"
                  class="w100"
              >
                <el-option
                    v-for="item in categoryList1"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                >
                </el-option>
              </el-select>

              <!--              <el-select v-model="importForm.fl1" class="w100">-->
              <!--                <el-option label="分类1" value="1"></el-option>-->
              <!--              </el-select>-->
            </el-form-item>
          </el-col>
          <el-col :xl="7" :lg="7">
            <el-form-item label-width="0px">
              <el-select
                  @change="handleClassiyfChange(3, importForm.fl2)"
                  v-model="importForm.fl2"
                  filterable
                  clearable
                  placeholder="请选择二级分类"
                  class="w100"
              >
                <el-option
                    v-for="item in categoryList2"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xl="7" :lg="7">
            <el-form-item label-width="0px">
              <el-select
                  v-model="importForm.fl3"
                  filterable
                  clearable
                  placeholder="请选择三级分类"
                  class="w100"
              >
                <el-option
                    v-for="item in categoryList3"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="品牌:" prop="brand_id" v-if="$route.query.key === 'szbao'">
        <el-select v-model="importForm.brand_id" clearable filterable remote :remote-method="remoteMethod" :loading="brandsOptiosData.loading">
          <el-option v-for="item in brandsOptiosData.brandsOptios" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
          <div class="text-center">
            <el-pagination
              background
              small
              class="pagination"
              :current-page="brandsOptiosData.page"
              :page-size="brandsOptiosData.pageSize"
              style="padding-top:10px !important;padding-bottom: 0 !important;"
              :total="brandsOptiosData.total"
              layout="prev,pager, next"
              @current-change="handleBrandPage"
            ></el-pagination>
          </div>
        </el-select>
      </el-form-item>
      <!--      <el-form-item label="选择导入标签">-->
      <!--        <el-row :gutter="10">-->
      <!--          <el-col :xl="7" :lg="7">-->
      <!--            <el-form-item label-width="0px">-->
      <!--              <el-select v-model="importForm.bq1" class="w100">-->
      <!--                <el-option label="标签1" value="1"></el-option>-->
      <!--              </el-select>-->
      <!--            </el-form-item>-->
      <!--          </el-col>-->
      <!--          <el-col :xl="7" :lg="7">-->
      <!--            <el-form-item label-width="0px">-->
      <!--              <el-select v-model="importForm.bq2" class="w100">-->
      <!--                <el-option label="标签2" value="1"></el-option>-->
      <!--              </el-select>-->
      <!--            </el-form-item>-->
      <!--          </el-col>-->
      <!--        </el-row>-->
      <!--      </el-form-item>-->
    </el-form>
    <el-row :gutter="20" ref="goodsBox" class="goods-box">
      <el-col :xl="6" :lg="8" v-for="(item, index) in dataList" :key="item.id">
        <div class="goods-item">
          <div class="top-image-box">
            <m-image :src="item.cover" class="goods-ig-box"></m-image>
            <div class="operates-box">
              <a href="javascript:;" @click="shiftOut(item, index)">移出</a>
            </div>
          </div>
          <!-- 怡亚通的商品信息展示 -->
          <div class="cont-box" v-if="$route.query.key === 'yiyatong'">
            <div class="title">
              {{ item.title }}
            </div>
            <p class="fgx-p"></p>
            <el-row :gutter="10">
              <el-col :span="8" class="title-3">
                <p>市场价</p>
                <p class="color-red mt5">￥{{ item.official_distri_price }}</p>
              </el-col>
              <el-col :span="8" class="title-3">
                <p>分销价</p>
                <p class="color-red mt5">￥{{ item.base_price }}</p>
              </el-col>
              <el-col :span="8" class="title-3">
                <p>建议零售价</p>
                <p class="color-red mt5">￥{{ item.suggest_price }}</p>
              </el-col>
            </el-row>
            
            <el-row :gutter="10">
              <el-col :span="8" class="title-3">
                <p>利润率</p>
                <p class="color-red mt5">{{ item.rate }}%</p>
              </el-col>
              <el-col :span="8" class="title-3">
                <p>推荐品牌</p>
                <p class="color-red mt5">
                  {{ item.third_brand_name ? item.third_brand_name : '-' }}
                </p>
              </el-col>
              <el-col :span="8" class="title-3">
                <p>推荐分类</p>
                <p class="color-red mt5 third-category-p">
                  {{ item.third_category_name ? item.third_category_name : '-' }}
                </p>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="8" class="title-3">
                <p>库存</p>
                <p class="color-red mt5">{{ item.stock }}</p>
              </el-col>
            </el-row>
          </div>
          <!-- 迈戈的商品信息展示 -->
          <div class="cont-box" v-else-if="$route.query.key === 'maiger'">
            <div class="title">
              {{ item.title }}
            </div>
            <p class="fgx-p"></p>
            <el-row :gutter="10">
              <el-col :span="8" class="title-3">
                <p>协议价</p>
                <p class="color-red mt5">￥{{ item.agreement_price | formatF2Y }}</p>
              </el-col>
              <el-col :span="8" class="title-3">
                <p>市场价</p>
                <p class="color-red mt5">￥{{ item.market_price | formatF2Y }}</p>
              </el-col>
              <el-col :span="8" class="title-3">
                <p>推荐品牌</p>
                <p class="color-red mt5">
                  {{ item.third_brand_name ? item.third_brand_name : '-' }}
                </p>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="8" class="title-3">
                <p>推荐分类</p>
                <p class="color-red mt5 third-category-p">
                  {{ item.third_category_name ? item.third_category_name : '-' }}
                </p>
              </el-col>
            </el-row>
          </div>
          <!-- 其他供应链的商品信息展示 -->
          <div class="cont-box" v-else>
            <div class="title">
              {{ item.title }}
            </div>
            <p class="fgx-p"></p>
            <el-row :gutter="10">
              <el-col :span="8" class="title-3">
                <p v-if="$route.query.key === 'yjf'">供货价</p>
                <p v-else>协议价</p>
                <p class="color-red mt5" v-if="$route.query.key === 'yjf'">
                  ￥{{ item.cost_price | formatF2Y }}
                </p>
                <p class="color-red mt5" v-else>
                  ￥{{ item.agreement_price | formatF2Y }}
                </p>
              </el-col>
              <el-col :span="8" class="title-3">
                <p>利润率</p>
                <p class="color-red mt5">{{ item.rate }}%</p>
              </el-col>
              <el-col :span="8" class="title-3">
                <p v-if="$route.query.key === 'yjf'">市场价</p>
                <p v-else>销售价</p>
                <p v-if="$route.query.key === 'yjf'" class="color-red mt5">￥{{ item.market_price | formatF2Y }}</p>
                <p v-else class="color-red mt5">￥{{ item.sale_price | formatF2Y }}</p>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="8" class="title-3">
                <p v-if="$route.query.key === 'yjf'">第三方平台销售价格</p>
                <p v-else>营销价</p>
                <p v-if="$route.query.key === 'yjf'" class="color-red mt5">
                  ￥{{ item.sale_price | formatF2Y }}
                </p>
                <p v-else class="color-red mt5">
                  ￥{{ item.activity_price | formatF2Y }}
                </p>
              </el-col>
              <el-col :span="8" class="title-3">
                <p v-if="$route.query.key === 'yjf'">库存</p>
                <p v-else>营销利润率</p>
                <p v-if="$route.query.key === 'yjf'" class="color-red mt5">{{ item.stock }}</p>
                <p v-else class="color-red mt5">{{ item.activity_rate }}%</p>
              </el-col>
              <el-col :span="8" class="title-3">
                <p v-if="$route.query.key === 'yjf'">中付平台销售价格</p>
                <p v-else>指导价</p>
                <p class="color-red mt5">
                  ￥{{ item.guide_price | formatF2Y }}
                </p>
              </el-col>
            </el-row>
            <el-row :gutter="10" v-if="$route.query.key !== 'yjf'">
              <el-col :span="8" class="title-3">
                <p>市场价</p>
                <p class="color-red mt5">
                  ￥{{ item.market_price | formatF2Y }}
                </p>
              </el-col>
              <el-col :span="8" class="title-3">
                <p>推荐品牌</p>
                <p class="color-red mt5">
                  {{ item.third_brand_name ? item.third_brand_name : "-" }}
                </p>
              </el-col>
              <el-col :span="8" class="title-3">
                <p>推荐分类</p>
                <p class="color-red mt5 third-category-p">
                  {{
                    item.third_category_name ? item.third_category_name : "-"
                  }}
                </p>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- 分页部分 -->
    <!--    <div class="f fjend">
      <el-pagination
          background
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          :style="{ float: 'right', padding: '20px' }"
          @current-change="handleCurrentChange"
          layout="total, prev, pager, next, jumper"
      ></el-pagination>
    </div>-->
    <!--    <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="importSelectGoods">导 入</el-button>
          <el-button @click="handleClose">取 消</el-button>
        </span>-->
  </el-dialog>
</template>
<script>
import {importSelectGoods} from "@/api/gatherSupply";
import {getClassifySelectIsPlugin} from "@/api/classify";
import {getBrandsList} from "@/api/brands"
import fastNavDialogVue from "../../../shopSetting/components/fastNavDialog.vue";

export default {
  data() {
    return {
      categoryList1: [],
      categoryList2: [],
      categoryList3: [],
      isShow: false,
      page: 1,
      pageSize: 8,
      total: 0,
      dataList: [],
      tableData: [],
      importForm: {
        fl1: null,
        fl2: null,
        fl3: null,
        brand_id:null
      },
      brandsOptiosData: {
          name: "",
          brandsOptios: [],
          loading: false,
          page: 1,
          pageSize: 10,
          total: 0
      },
    };
  },

  oncreate() {
    console.log("333");
  },
  mounted() {
    this.handleClassiyfChange(1, 0);
    this.getBrandsOptios()
    // console.log("总数据量11：", this.dataList.length);
  },
  methods: {
     // 品牌搜索
     remoteMethod(query) {
        this.brandsOptiosData.name = query
        this.brandsOptiosData.page = 1
        this.getBrandsOptios()
    },
    handleBrandPage(val) {
      this.brandsOptiosData.page = val
      this.getBrandsOptios()
    },
    // 获取品牌
    async getBrandsOptios() {
      let params = {
          page: this.brandsOptiosData.page,
          pageSize: this.brandsOptiosData.pageSize
      }
      if (this.brandsOptiosData.name) params.name = this.brandsOptiosData.name
      this.brandsOptiosData.loading = true
      let res = await getBrandsList(params)
      this.brandsOptiosData.loading = false
      if (res.code === 0) {
          this.brandsOptiosData.brandsOptios = res.data.list
          this.brandsOptiosData.total = res.data.total
      }
    },
    // 取消选择
    clearGoods() {
      this.$confirm("是否确定取消选择? 确定后弹出框将自动关闭", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
          .then(() => {
            this.dataList = [];
            localStorage.removeItem("cartlist");
            this.updateCard();
            this.handleClose(true);
          })
          .catch();
    },
    // 移出
    shiftOut(item, index) {
      this.dataList.splice(index, 1);
      localStorage.setItem("cartlist", JSON.stringify(this.dataList));
      this.updateCard();
    },
    // 获取类目
    handleClassiyfChange(level, pid) {
      getClassifySelectIsPlugin(level, pid).then((r) => {
        if (level === 3) {
          this.categoryList3 = [];
        } else {
          this.categoryList2 = [];
          //this.searchForm.category2_id = ""
          this.categoryList3 = [];
          //this.searchForm.category3_id = ""
        }
        switch (level) {
          case 1:
            this.categoryList1 = r.data.list;
            break;
          case 2:
            this.categoryList2 = r.data.list;
            break;
          case 3:
            this.categoryList3 = r.data.list;
            break;
        }
      });
    },

    async importSelectGoods() {
      /* console.log("分类1", this.importForm.fl1);
      console.log("分类2", this.importForm.fl2);
      console.log("分类3", this.importForm.fl3);
      console.log("分类4", this.importForm); */
      if(this.$route.query.key === 'youxuan') {
        if (this.importForm.fl3 === "" || this.importForm.fl3 == null) {
          this.$message.error("请选择分类");
          return;
        }
      }
      if (!this.dataList || this.dataList.length <= 0) {
        this.$message.error("暂无商品,请勾选要导入的商品");
        return false;
      }
      var categorys = "";
      categorys =
          this.importForm.fl1 +
          "," +
          this.importForm.fl2 +
          "," +
          this.importForm.fl3;
      this.dataList = this.dataList.map((item) => ({
        gather_supply_id: parseInt(this.$route.query.id),
        ...item,
      }));
      if (this.$route.query.key == "self") {
        this.dataList.forEach((item, index) => {
          this.dataList[index].source = 0;
        });
      }
      let jsonData = {
        list: this.dataList,
        categorys: categorys,
        key: this.$route.query.key,
        gather_supply_id: parseInt(this.$route.query.id),
        
      };
      if (this.importForm.brand_id != '' && this.importForm.brand_id != undefined) {
        jsonData.brand_id = this.importForm.brand_id
      }
      let res = await importSelectGoods(jsonData);

      /* console.log("准备导入");
      console.log("返回结果", res); */

      if (res.code === 0) {
        this.$message.success("操作成功,等待后台操作");
        this.handleClose(true);
      } else {
        this.$messag.error(res.msg);
      }
    },

    updateCard() {
      this.dataList = JSON.parse(localStorage.getItem("cartlist"));
      console.log("购物车选中数据", this.dataList);
      this.total = this.dataList ? this.dataList.length : 0;
      // console.log("总数据量：", this.total);
    },
    // 获取商品宽度
    fetch() {
      this.total = this.tableData.length;
      /* window.onresize = () => {
        this.$refs.goodsBox.$children.forEach((item) => {
          item.$children[0].$el.style.width = item.$el.offsetWidth - 20 + "px";
          item.$children[0].$el.style.height = item.$el.offsetWidth - 20 + "px";
        });
      };
      this.$refs.goodsBox.$children.forEach((item) => {
        item.$children[0].$el.style.width = item.$el.offsetWidth - 20 + "px";
        item.$children[0].$el.style.height = item.$el.offsetWidth - 20 + "px";
      }); */
    },
    handleClose(reload = false) {
      this.isShow = false;
      if (reload === true) {
        this.$emit("reset");
      }
    },
    handleCurrentChange(page) {
      this.page = page;
    },
  },
};
</script>
<style lang="scss" scoped>
.goods-box {
  .goods-item {
    width: 100%;
    border: 1px solid #f1f1f1;
    transition: all 0.3s ease;
    margin-top: 20px;

    .fontS18 {
      font-size: 16px;
    }

    .mt10 {
      margin-top: 10px;
    }

    .top-image-box {
      position: relative;

      ::v-deep .goods-ig-box {
        position: relative;
        width: 100%;
        height: 0;
        padding-top: 100%;

        img {
          position: absolute;
          top: 0;
          left: 0;
        }
      }

      .operates-box {
        opacity: 0;
        width: 100%;
        height: 40px;
        line-height: 40px;
        text-align: center;
        position: absolute;
        bottom: -10px;
        transition: all 0.3s ease;

        a {
          color: #ffffff;
        }
      }

      &:hover {
        .operates-box {
          opacity: 1;
          background-color: rgba(0, 0, 0, 0.5);
          bottom: 3px;
        }
      }
    }

    .cont-box {
      height: 280px;
      padding: 10px;

      .title {
        height: 40px;
        color: #333;
        font-weight: 500;
        font-size: 12px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 20px;
        -webkit-box-orient: vertical;
      }

      .el-row {
        p {
          font-size: 12px;
          line-height: 1.5em;
        }

        p.third-category-p {
          height: 36px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-box-orient: vertical;
        }
      }

      .mt5 {
        margin-top: 5px;
      }

      .fgx-p {
        border-top: 1px dashed rgb(204, 204, 204);
        margin: 10px 0;
      }
    }

    &:hover {
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
  }
}

.titletop{
  width: 95%;
  display: flex;
  justify-content: space-between;

  span{
    line-height: 24px;
    font-size: 18px;
    color: #303133;
  }
}
</style>