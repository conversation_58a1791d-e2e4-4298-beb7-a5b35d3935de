<!-- 选择分类dialog -->
<template>
  <el-dialog
      :visible="isShow"
      width="40%"
      :before-close="handleClose"
  >
  <div slot="title" class="titletop">
    <span v-if="$route.query.key === 'szbao'">选择导入分类、品牌</span>
    <span v-else>选择导入分类、标签</span>
    <span v-if="$route.query.key === 'yiyatong'">导入商品数量（{{exportTotal}}）</span>
  </div>
    <el-form :model="importForm" label-width="100px" ref="form" :rules="rules">
      <div style="margin-top: 1em; margin-bottom: 1em; color: #dc5d5d">1.因条件限制，该操作只能导入前2500个商品</div>
      <div style="margin-top: 1em; margin-bottom: 1em; color: #dc5d5d">2.如不选择分类，则自动匹配当前系统存在的分类，如果分类不存在则自动创建(部分供应链类型不支持,不选择分类可能会无法导入)</div>
      <el-form-item label="选择导入分类:">
        <div class="f fac">
          <el-form-item>
            <el-select @change="handleClassiyfChange(2,importForm.fl1)"
                       v-model="importForm.fl1" filterable clearable placeholder="请选择一级分类" class="w100">
              <el-option v-for="item in categoryList1" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>

          </el-form-item>
          <el-form-item class="ml10">
            <el-select @change="handleClassiyfChange(3,importForm.fl2)"
                       v-model="importForm.fl2" filterable clearable placeholder="请选择二级分类" class="w100">
              <el-option v-for="item in categoryList2" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="ml10">
            <el-select
                v-model="importForm.fl3" filterable clearable placeholder="请选择三级分类" class="w100">
              <el-option v-for="item in categoryList3" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
      </el-form-item>
      <el-form-item label="导入品牌:" v-if="$route.query.key === 'szbao'">
        
          <el-select v-model="importForm.brand_id" clearable filterable remote :remote-method="remoteMethod" :loading="brandsOptiosData.loading">
            <el-option v-for="item in brandsOptiosData.brandsOptios" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
            <div class="text-center">
              <el-pagination
                background
                small
                class="pagination"
                :current-page="brandsOptiosData.page"
                :page-size="brandsOptiosData.pageSize"
                style="padding-top:10px !important;padding-bottom: 0 !important;"
                :total="brandsOptiosData.total"
                layout="prev,pager, next"
                @current-change="handleBrandPage"
              ></el-pagination>
            </div>
          </el-select>
      </el-form-item>
      <!--      <el-form-item label="选择导入标签:">-->
      <!--        <div class="f fac">-->
      <!--          <el-form-item>-->
      <!--            <el-select-->
      <!--              v-model="importForm.labelGroup"-->
      <!--              placeholder="请选择标签组"-->
      <!--              class="f1"-->
      <!--            >-->
      <!--              <el-option value="1" label="标签组1"></el-option>-->
      <!--            </el-select>-->
      <!--          </el-form-item>-->
      <!--          <el-form-item>-->
      <!--            <el-select-->
      <!--              v-model="importForm.labelValue"-->
      <!--              placeholder="请选择标签值"-->
      <!--              class="f1"-->
      <!--            >-->
      <!--              <el-option value="1" label="标签值1"></el-option>-->
      <!--            </el-select>-->
      <!--          </el-form-item>-->
      <!--        </div>-->
      <!--      </el-form-item>-->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirm">导 入</el-button>
      <el-button @click="handleClose">取 消</el-button>
    </span>
  </el-dialog>
</template>
<script>
import {getClassifySelectIsPlugin} from "@/api/classify";
import {importAllSearchGoods} from "@/api/gatherSupply";
import {getBrandsList} from "@/api/brands"

export default {
  data() {
    return {
      categoryList1: [],
      categoryList2: [],
      categoryList3: [],
      isShow: false,
      page: 1,
      pageSize: 8,
      total: 0,
      dataList: [],
      goodSearchWhere: [],
      tableData: [],
      importForm: {
        fl1: null,
        fl2: null,
        fl3: null
      },
      rules: {},
      exportTotal:0,
      brandsOptiosData: {
          name: "",
          brandsOptios: [],
          loading: false,
          page: 1,
          pageSize: 10,
          total: 0
      },
    };
  },

  mounted() {
  },
  methods: {
    // 品牌搜索
    remoteMethod(query) {
        this.brandsOptiosData.name = query
        this.brandsOptiosData.page = 1
        this.getBrandsOptios()
    },
    handleBrandPage(val) {
      this.brandsOptiosData.page = val
      this.getBrandsOptios()
    },
    // 获取品牌
    async getBrandsOptios() {
      let params = {
          page: this.brandsOptiosData.page,
          pageSize: this.brandsOptiosData.pageSize
      }
      if (this.brandsOptiosData.name) params.name = this.brandsOptiosData.name
      this.brandsOptiosData.loading = true
      let res = await getBrandsList(params)
      this.brandsOptiosData.loading = false
      if (res.code === 0) {
          this.brandsOptiosData.brandsOptios = res.data.list
          this.brandsOptiosData.total = res.data.total
      }
    },

    notify() {
      // console.log("通知测试",this.goodSearchWhere)
    },

    // 获取类目
    handleClassiyfChange(level, pid) {
      getClassifySelectIsPlugin(level, pid).then(r => {
        if (level === 3) {
          this.categoryList3 = [];

        } else {
          this.categoryList2 = [];
          //this.searchForm.category2_id = ""
          this.categoryList3 = [];
          //this.searchForm.category3_id = ""
        }
        switch (level) {
          case 1:
            this.categoryList1 = r.data.list
            break;
          case 2:
            this.categoryList2 = r.data.list
            break;
          case 3:
            this.categoryList3 = r.data.list
            break;
        }
      })
    },


    async importAllSearchGoods() {

      let jsonData = this.goodSearchWhere

      jsonData.gather_supply_id = parseInt(this.$route.query.id)
      jsonData.key = this.$route.query.key
      if ((this.importForm.fl1 != "" && this.importForm.fl1 != undefined) && (this.importForm.fl2 != "" && this.importForm.fl2 != undefined) && (this.importForm.fl3 != "" && this.importForm.fl3 != undefined)) {
        jsonData.categorys = this.importForm.fl1 + "," + this.importForm.fl2 + "," + this.importForm.fl3
      }
      if (this.importForm.brand_id != '' && this.importForm.brand_id != undefined) {
        jsonData.brand_id = this.importForm.brand_id
      }
      let res = await importAllSearchGoods(jsonData);
      if (res.code === 0) {
        this.$message.success("操作成功，等待后台处理")
          this.handleClose()
      } else {
        this.$message.error(res.msg)
      }
    },


    handleClose() {
      this.isShow = false;
      this.categoryList1= []
      this.categoryList2= []
      this.categoryList3= []
      this.page= 1
      this.pageSize= 8
      this.total= 0
      this.dataList= []
      this.goodSearchWhere= []
      this.tableData= []
      this.importForm= {
            fl1: null,
                fl2: null,
                fl3: null
        }
    },
    confirm() {
      // if(this.importForm.fl3==="" || this.importForm.fl3==null){
      //   this.$message.error("请选择分类")
      //   return
      //
      // }
      if(this.$route.query.key === "youxuan") {
        if(this.importForm.fl3==="" || this.importForm.fl3==null){
        this.$message.error("请选择分类")
        return
        } else {
          this.importAllSearchGoods();
        }
      } else {
        this.importAllSearchGoods();
      }


      // this.$refs.form.validate((valid) => {
      //   if (valid) {
      //
      //
      //   } else {
      //     return false;
      //   }
      // });
    },
  },
};
</script>
<style lang="scss" scoped>

.titletop{
  width: 95%;
  display: flex;
  justify-content: space-between;

  span{
    line-height: 24px;
    font-size: 18px;
    color: #303133;
  }
}

</style>