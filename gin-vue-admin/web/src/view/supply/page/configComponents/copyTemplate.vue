<template>
  <el-form :model="formData" label-width="130px">
    <p class="title-p">定价策略</p>
    <el-divider></el-divider>
    <el-form-item label="上游定价策略:">
      <el-radio-group v-model="formData.strategy">
        <el-radio :label="1">开启</el-radio>
        <el-radio :label="2">关闭</el-radio>
      </el-radio-group>
      <p class="hint-p">
        开启后销售价、成本价将使用上游定价策略计算后的价格，本地定价策略失效;如上游无定价策略设置的，则为上游的销售价、成本价。
        <br>
        并非所有供应链都会对接上游定价策略，如未对接的开启后使用上游销售价、成本价，使用前请务必测试后使用!
      </p>
    </el-form-item>
    <!-- 京东部分 -->
    <el-form-item label="京东销售价:">
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.JDSales" :label="1"></el-radio>
        <el-form-item label="指导价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.JDSalesGuide"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.JDSales" :label="2"></el-radio>
        <el-form-item label="协议价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.JDSalesAgreement"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.JDSales" :label="3"></el-radio>
        <el-form-item label="营销价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.JDSalesMarketing"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <p class="hint-p">举例: 协议价50元, 指导价100元</p>
      <p class="hint-p">协议价 X 定价系数 即 50 X 130% = 65 元</p>
      <p class="hint-p">指导价 X 定价系数 即 100 X 80% = 80 元</p>
      <p class="hint-p">
        默认为指导价 X 100% (指导价不变, 协议价上涨可能导致亏损!)
      </p>
      <p class="hint-p">没有营销价的商品按指导价计算</p>
    </el-form-item>
    <el-form-item label="京东成本价:">
      <div class="f fac blockRadio-box">
        <el-radio
            v-model="formData.JDCostPrice"
            :label="1"
        ></el-radio>
        <el-form-item label="协议价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.JDCostAgreement"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio
            v-model="formData.JDCostPrice"
            :label="2"
        ></el-radio>
        <el-form-item label="营销价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.JDCostMarketing"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <p class="hint-p">没有营销价的商品按指导价计算</p>
    </el-form-item>
    <!-- 阿里部分 -->
    <el-form-item label="阿里销售价:">
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.ALSales" :label="1"></el-radio>
        <el-form-item label="指导价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.ALSalesGuide"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.ALSales" :label="2"></el-radio>
        <el-form-item label="协议价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.ALSalesAgreement"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.ALSales" :label="3"></el-radio>
        <el-form-item label="营销价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.ALSalesMarketing"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
    </el-form-item>
    <el-form-item label="阿里成本价:">
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.ALCost" :label="1"></el-radio>
        <el-form-item label="协议价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.ALCostAgreement"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.ALCost" :label="2"></el-radio>
        <el-form-item label="营销价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.ALCostMarketing"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
    </el-form-item>
    <!-- 天猫部分 -->
    <el-form-item label="天猫销售价:">
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.TMSales" :label="1"></el-radio>
        <el-form-item label="指导价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.TMSalesGuide"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.TMSales" :label="2"></el-radio>
        <el-form-item label="协议价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.TMSalesAgreement"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.TMSales" :label="3"></el-radio>
        <el-form-item label="营销价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.TMSalesMarketing"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
    </el-form-item>
    <el-form-item label="天猫成本价:">
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.TMCost" :label="1"></el-radio>
        <el-form-item label="协议价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.TMCostAgreement"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.TMCost" :label="2"></el-radio>
        <el-form-item label="营销价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.TMCostMarketing"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
    </el-form-item>
    <!-- 云仓部分 -->
    <el-form-item label="云仓销售价:">
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.YCSales" :label="1"></el-radio>
        <el-form-item label="指导价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.YCSalesGuide"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.YCSales" :label="2"></el-radio>
        <el-form-item label="协议价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.YCSalesAgreement"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.YCSales" :label="3"></el-radio>
        <el-form-item label="营销价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.YCSalesMarketing"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
    </el-form-item>
    <el-form-item label="云仓成本价:">
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.YCCost" :label="1"></el-radio>
        <el-form-item label="协议价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.YCCostAgreement"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.YCCost" :label="2"></el-radio>
        <el-form-item label="营销价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.YCCostMarketing"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
    </el-form-item>


    <!-- 中台本地部分 -->
    <el-form-item label="自主销售价:">
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.SupplySales" :label="1"></el-radio>
        <el-form-item label="指导价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.SupplySalesGuide"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.SupplySales" :label="2"></el-radio>
        <el-form-item label="协议价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.SupplySalesAgreement"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.SupplySales" :label="3"></el-radio>
        <el-form-item label="营销价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.SupplySalesMarketing"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
    </el-form-item>
    <el-form-item label="自主成本价:">
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.SupplyCost" :label="1"></el-radio>
        <el-form-item label="协议价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.SupplyCostAgreement"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.SupplyCost" :label="2"></el-radio>
        <el-form-item label="营销价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.SupplyCostMarketing"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  name: "copyTemplate",
  data() {
    return {
      formData: {
        strategy: 1,

        JDSales: 1,
        JDSalesGuide: "100",
        JDSalesAgreement: "100",
        JDSalesMarketing: "100",

        JDCostPrice: 1,
        JDCostAgreement: "100",
        JDCostMarketing: "100",

        ALSales: 1,
        ALSalesGuide: "100",
        ALSalesAgreement: "100",
        ALSalesMarketing: "100",

        ALCost: 1,
        ALCostAgreement: "100",
        ALCostMarketing: "100",

        TMSales: 1,
        TMSalesGuide: "100",
        TMSalesAgreement: "100",
        TMSalesMarketing: "100",

        TMCost: 1,
        TMCostAgreement: "100",
        TMCostMarketing: "100",

        YCSales: 1,
        YCSalesGuide: "100",
        YCSalesAgreement: "100",
        YCSalesMarketing: "100",

        YCCost: 1,
        YCCostAgreement: "100",
        YCCostMarketing: "100",

        SupplySales: 1,
        SupplySalesGuide: "100",
        SupplySalesAgreement: "100",
        SupplySalesMarketing: "100",

        SupplyCost: 1,
        SupplyCostAgreement: "100",
        SupplyCostMarketing: "100",
      }
    }
  },
  methods:{
    //赋值表单
    setFrom(val) {
      const keys = Object.keys(val);
      const that = this;
      keys.forEach((element) => {
        that.formData[element] = val[element];
      });
    },
  }
}
</script>

<style lang="scss" scoped>
p.title-p {
  font-size: 16px;
  font-weight: bold;
}

.el-divider {
  margin: 15px 0;
}
.blockRadio-box {
  margin-top: 15px;

  & :first-child {
    margin-top: 0;
  }

  ::v-deep .el-radio {
    margin: 0;

    .el-radio__label {
      display: none;
    }
  }
}
p.hint-p {
  font-size: 12px;
  color: #c0c4cc;
  line-height: 20px;
}
</style>