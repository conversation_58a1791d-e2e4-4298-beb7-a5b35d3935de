<template>
  <el-form :model="formData" label-width="130px">
    <p class="title-p">定价策略</p>
    <el-divider></el-divider>
    <el-form-item label="上游定价策略:">
      <el-radio-group v-model="formData.strategy">
        <el-radio :label="1">开启</el-radio>
        <el-radio :label="2">关闭</el-radio>
      </el-radio-group>
      <p class="hint-p">
        开启后销售价、成本价将使用上游定价策略计算后的价格，本地定价策略失效;如上游无定价策略设置的，则为上游的销售价、成本价。
        <br>
        并非所有供应链都会对接上游定价策略，如未对接的开启后使用上游销售价、成本价，使用前请务必测试后使用!
      </p>
    </el-form-item>



    <!-- 中台本地部分 -->
    <el-form-item label="供货价:">
<!--  -->
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.YzhNewSupplySales" :label="1"></el-radio>
        <el-form-item label="结算价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.YzhNewSupplySalesAgreement"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.YzhNewSupplySales" :label="2"></el-radio>
        <el-form-item label="售价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.YzhNewSupplySalesMarketing"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.YzhNewSupplySales" :label="3"></el-radio>
        <el-form-item label="原价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.YzhNewSupplySalesOriginalPrice"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
    </el-form-item>
    <el-form-item label="成本价:">
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.YzhNewSupplyCost" :label="1"></el-radio>
        <el-form-item label="结算价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.YzhNewSupplyCostAgreement"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.YzhNewSupplyCost" :label="2"></el-radio>
        <el-form-item label="售价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.YzhNewSupplyCostMarketing"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.YzhNewSupplyCost" :label="3"></el-radio>
        <el-form-item label="原价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.YzhNewSupplyCostOriginalPrice"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
    </el-form-item>




    <el-form-item label="建议零售价:">
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.YzhNewSupplyOrigin" :label="1"></el-radio>
        <el-form-item label="结算价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.YzhNewSupplyOriginAgreement"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.YzhNewSupplyOrigin" :label="2"></el-radio>
        <el-form-item label="售价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.YzhNewSupplyOriginMarketing"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.YzhNewSupplyOrigin" :label="3"></el-radio>
        <el-form-item label="原价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.YzhNewSupplyOriginOriginalPrice"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>

    </el-form-item>


    <el-form-item label="商品指导价:">
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.YzhNewSupplyGuide" :label="1"></el-radio>
        <el-form-item label="结算价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.YzhNewSupplyGuideAgreement"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.YzhNewSupplyGuide" :label="2"></el-radio>
        <el-form-item label="售价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.YzhNewSupplyGuideMarketing"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.YzhNewSupplyGuide" :label="3"></el-radio>
        <el-form-item label="原价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.YzhNewSupplyGuideOriginalPrice"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
    </el-form-item>



    <el-form-item label="商品营销价:">
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.YzhNewSupplyActivity" :label="1"></el-radio>
        <el-form-item label="结算价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.YzhNewSupplyActivityAgreement"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.YzhNewSupplyActivity" :label="2"></el-radio>
        <el-form-item label="售价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.YzhNewSupplyActivityMarketing"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.YzhNewSupplyActivity" :label="3"></el-radio>
        <el-form-item label="原价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.YzhNewSupplyActivityOriginalPrice"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
    </el-form-item>
    <p class="title-p">等级限制</p>
    <el-divider></el-divider>
    <el-form-item
        v-for="(platformItem, index) in platforms"
        :key="platformItem.id"
        :label="platformItem.name"
    >
      <el-checkbox
          v-model="platformItem.isCheckAll"
          @change="checkAll(platformItem.isCheckAll, index)"
      >全选</el-checkbox
      >
      <el-checkbox-group
          v-model="platformItem.application_level_ids"
          @change="checkItem(platformItem.application_level_ids, index)"
      >
        <el-checkbox
            v-for="item in sserLevelOptionList"
            :key="item.id"
            :label="item.id"
        >{{ item.levelName }}</el-checkbox
        >
      </el-checkbox-group>
    </el-form-item>
  </el-form>
</template>

<script>
import { getApplicationLevelOption } from "@/api/application";
export default {
  name: "copyTemplate",
  data() {
    return {
      platforms: [
        { name: "采购端等级:", id: 108, application_level_ids: [], isCheckAll: false },
        //{ name: "采购端等级:", id: 10, application_level_ids: [], isCheckAll: false },
      ],
      sserLevelOptionList: [],

      formData: {
        strategy: 2,



        YzhNewSupplySales: 3,
        YzhNewSupplySalesGuide: "100",
        YzhNewSupplySalesAgreement: "100",
        YzhNewSupplySalesMarketing: "100",
        YzhNewSupplySalesOriginalPrice: "100",

        YzhNewSupplyCost: 1,
        YzhNewSupplyCostAgreement: "100",
        YzhNewSupplyCostMarketing: "100",
        YzhNewSupplyCostOriginalPrice: "100",


        //建议零售价
        YzhNewSupplyOrigin: 1,
        YzhNewSupplyOriginAgreement: "100",
        YzhNewSupplyOriginMarketing: "100",
        YzhNewSupplyOriginOriginalPrice: "100",


        //商品营销价
        YzhNewSupplyActivity: 1,
        YzhNewSupplyActivityAgreement: "100",
        YzhNewSupplyActivityMarketing: "100",
        YzhNewSupplyActivityOriginalPrice: "100",


        //商品指导价
        YzhNewSupplyGuide: 1,
        YzhNewSupplyGuideAgreement: "100",
        YzhNewSupplyGuideMarketing: "100",
        YzhNewSupplyGuideOriginalPrice: "100",
      }
    }
  },

  mounted() {
    this.getUserLevel();
  },
  methods:{

    // 处理等级限制回显
    disposeLevelData(data) {
      data.forEach((item) => {
        this.platforms.forEach((item2) => {
          if (item.source_id === item2.id) {
            item2.application_level_ids = item.application_level_ids;
            item2.isCheckAll =
                item.application_level_ids.length ===
                this.sserLevelOptionList.length;
          }
        });
      });
    },
    // return等级限制数据
    getLevelData() {
      let returnData = [];
      this.platforms.forEach((item) => {
        if (item.application_level_ids.length > 0) {
          returnData.push({
            source_id: item.id,
            application_level_ids: item.application_level_ids,
          });
        }
      });
      return { application_level_ids: returnData };
    },
    async getUserLevel() {
      let res = await getApplicationLevelOption();
      if (res.code === 0) {
        this.sserLevelOptionList = res.data.list;
      }
    },
    checkAll(isCheckAll, index) {
      if (isCheckAll) {
        this.sserLevelOptionList.forEach((item) => {
          this.platforms[index].application_level_ids.push(item.id);
        });
      } else {
        this.platforms[index].application_level_ids = [];
      }
    },
    checkItem(value, index) {
      this.platforms[index].isCheckAll =
          value.length === this.sserLevelOptionList.length;
    },

    //赋值表单
    setFrom(val) {
      const keys = Object.keys(val);
      const that = this;
      keys.forEach((element) => {
        that.formData[element] = val[element];
      });
    },
  }
}
</script>

<style lang="scss" scoped>
p.title-p {
  font-size: 16px;
  font-weight: bold;
}

.el-divider {
  margin: 15px 0;
}
.blockRadio-box {
  margin-top: 15px;

  & :first-child {
    margin-top: 0;
  }

  ::v-deep .el-radio {
    margin: 0;

    .el-radio__label {
      display: none;
    }
  }
}
p.hint-p {
  font-size: 12px;
  color: #c0c4cc;
  line-height: 20px;
}
</style>