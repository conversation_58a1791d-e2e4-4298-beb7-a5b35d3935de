<!-- 配置信息 -->
<template>
    <m-card>
        <el-tabs v-model="activeName" type="card">
            <el-tab-pane label="基础设置" name="baseSetting">
                <el-form :model="formData" label-width="230px">
                    <p class="title-p">基础信息</p>
                    <el-divider></el-divider>
                    <el-row>
                        <el-col :span="15" v-if="$route.query.key === 'hehe'">
                            <el-form-item label="host:">
                                <el-input
                                    v-model="formData.baseInfo.apiUrl"
                                ></el-input>
                                <div style="font-size: 12px; color: #dc5d5d">
                                    填写请求供应链中台系统的地址例如：https://xxx.xxx.com/supplyapi
                                    ,如果没有请联系客服.
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="15">
                            <el-form-item
                                v-if="
                                    $route.query.key === 'weipinshang' ||
                                    $route.query.key === 'yjf'
                                "
                                label="渠道号:"
                            >
                                <el-input
                                    v-model="formData.baseInfo.channel"
                                ></el-input>
                            </el-form-item>
                            <!--              <el-form-item v-if="$route.query.key === 'yiyatong'" label="渠道号:">-->
                            <!--                <el-input v-model="formData.baseInfo.channel"></el-input>-->
                            <!--              </el-form-item>-->
                            <el-form-item
                                v-if="
                                    $route.query.key !== 'weipinshang' &&
                                    $route.query.key !== 'tianma' &&
                                    $route.query.key !== 'yjf' &&
                                    $route.query.key !== 'jd_vop' &&
                                    $route.query.key !== 'guanaitong' &&
                                    $route.query.key !== 'maiger' && 
                                    $route.query.key !== 'wdt'
                                "
                                label="appKey:"
                            >
                                <el-input
                                    v-model="formData.baseInfo.appKey"
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                v-if="$route.query.key === 'wdt'"
                                label="appkey:"
                            >
                                <el-input
                                    v-model="formData.baseInfo.appkey"
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                v-if="$route.query.key === 'wdt'"
                                label="appsecret:"
                            >
                                <div class="verify" v-if="visibleWhether">
                                    <el-input
                                        v-model="formData.baseInfo.appsecret"
                                        :disabled="true"
                                    ></el-input>
                                    <el-popover
                                        placement="top"
                                        width="500"
                                        v-model="visible[10].popover"
                                    >
                                        <p>重置-appsecret</p>
                                        <el-input
                                            type="textarea"
                                            v-model="visible[10].value"
                                            class="popover_val"
                                            rows="1"
                                        ></el-input>
                                        <div
                                            style="text-align: right; margin: 0"
                                        >
                                            <el-button
                                                size="mini"
                                                type="text"
                                                @click="
                                                    visible[10].popover = false
                                                "
                                                >取消</el-button
                                            >
                                            <el-button
                                                type="primary"
                                                size="mini"
                                                @click="
                                                    reset(visible[10].name, 10)
                                                "
                                                >确定</el-button
                                            >
                                        </div>
                                        <el-button
                                            slot="reference"
                                            type="primary"
                                            class="verify_but"
                                            >重置</el-button
                                        >
                                    </el-popover>
                                </div>
                                <el-input
                                    v-model="formData.baseInfo.appsecret"
                                    v-else
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="sid:"
                                v-if="$route.query.key === 'wdt'"
                            >
                                <el-input
                                    v-model="formData.baseInfo.sid"
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="shop_id:"
                                v-if="$route.query.key === 'wdt'"
                            >
                                <el-input
                                    v-model="formData.baseInfo.shop_id"
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="distributor_appkey:"
                                v-if="$route.query.key === 'wdt'"
                            >
                                <el-input
                                    v-model="formData.baseInfo.distributor_appkey"
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                v-if="$route.query.key === 'wdt'"
                                label="distributor_appsecret:"
                            >
                                <div class="verify" v-if="visibleWhether">
                                    <el-input
                                        v-model="formData.baseInfo.distributor_appsecret"
                                        :disabled="true"
                                    ></el-input>
                                    <el-popover
                                        placement="top"
                                        width="500"
                                        v-model="visible[11].popover"
                                    >
                                        <p>重置-distributor_appsecret</p>
                                        <el-input
                                            type="textarea"
                                            v-model="visible[11].value"
                                            class="popover_val"
                                            rows="1"
                                        ></el-input>
                                        <div
                                            style="text-align: right; margin: 0"
                                        >
                                            <el-button
                                                size="mini"
                                                type="text"
                                                @click="
                                                    visible[11].popover = false
                                                "
                                                >取消</el-button
                                            >
                                            <el-button
                                                type="primary"
                                                size="mini"
                                                @click="
                                                    reset(visible[11].name, 11)
                                                "
                                                >确定</el-button
                                            >
                                        </div>
                                        <el-button
                                            slot="reference"
                                            type="primary"
                                            class="verify_but"
                                            >重置</el-button
                                        >
                                    </el-popover>
                                </div>
                                <el-input
                                    v-model="formData.baseInfo.distributor_appsecret"
                                    v-else
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                v-if="$route.query.key === 'guanaitong'"
                                label="appid:"
                            >
                                <el-input
                                    v-model="formData.baseInfo.appid"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <!-- <el-col :span="15" v-if="$route.query.key === 'self' || $route.query.key === 'szbao' || $route.query.key === 'selfCake'"> -->
                        <el-col
                            :span="15"
                            v-if="
                                $route.query.key === 'self' ||
                                $route.query.key === 'szbao'
                            "
                        >
                            <el-form-item label="host:">
                                <el-input
                                    v-model="formData.baseInfo.host"
                                ></el-input>
                                <!-- <div style="font-size: 12px; color: #dc5d5d" v-if="$route.query.key === 'self' || $route.query.key === 'selfCake'"> -->
                                <div
                                    style="font-size: 12px; color: #dc5d5d"
                                    v-if="$route.query.key === 'self'"
                                >
                                    填写请求供应链中台系统的地址例如：https://xxx.xxx.com/supplyapi
                                    ,如果没有请联系客服.
                                </div>
                                <div
                                    style="font-size: 12px; color: #dc5d5d"
                                    v-else
                                >
                                    * {{ domain }}/supplyapi
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="15" v-if="$route.query.key === 'szbao'">
                            <el-form-item label="ossHost:">
                                <el-input
                                    v-model="formData.baseInfo.ossHost"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="15" v-if="$route.query.key === 'tianma'">
                            <el-form-item label="用户名:">
                                <el-input
                                    v-model="formData.baseInfo.userName"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="15" v-if="$route.query.key === 'tianma'">
                            <el-form-item label="密码:">
                                <div class="verify" v-if="visibleWhether">
                                    <el-input
                                        v-model="formData.baseInfo.passWord"
                                        :disabled="true"
                                    ></el-input>
                                    <el-popover
                                        placement="top"
                                        width="500"
                                        v-model="visible[8].popover"
                                    >
                                        <p>重置-密码</p>
                                        <el-input
                                            type="textarea"
                                            v-model="visible[8].value"
                                            class="popover_val"
                                            rows="1"
                                        ></el-input>
                                        <div
                                            style="text-align: right; margin: 0"
                                        >
                                            <el-button
                                                size="mini"
                                                type="text"
                                                @click="
                                                    visible[8].popover = false
                                                "
                                                >取消</el-button
                                            >
                                            <el-button
                                                type="primary"
                                                size="mini"
                                                @click="
                                                    reset(visible[8].name, 8)
                                                "
                                                >确定</el-button
                                            >
                                        </div>
                                        <el-button
                                            slot="reference"
                                            type="primary"
                                            class="verify_but"
                                            >重置</el-button
                                        >
                                    </el-popover>
                                </div>
                                <el-input
                                    v-else
                                    v-model="formData.baseInfo.passWord"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="15" v-if="$route.query.key === 'tianma' || $route.query.key === 'kunsheng'">
                            <el-form-item label="接口域名:">
                                <el-input
                                    v-model="formData.baseInfo.apiUrl"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="15">
                            <!-- <el-form-item label="appSecret:" v-if="$route.query.key !== 'zyhx' && $route.query.key !== 'weipinshang' && $route.query.key !== 'yjf' && $route.query.key !== 'jd_vop'">
                                <div class="verify" v-if="visibleWhether">
                                <el-input v-model="formData.baseInfo.appSecret" :disabled="true"></el-input>
                                <el-popover placement="top" width="500" v-model="visible[0].popover">
                                    <p>重置-appSecret</p>
                                    <el-input type="textarea" v-model="visible[0].value" class="popover_val"  rows="1"></el-input>
                                    <div style="text-align: right; margin: 0">
                                    <el-button size="mini" type="text" @click="visible[0].popover = false">取消</el-button>
                                    <el-button type="primary" size="mini" @click="reset(visible[0].name,0)">确定</el-button>
                                    </div>
                                    <el-button slot="reference" type="primary" class="verify_but">重置</el-button>
                                </el-popover>
                                </div>
                                <el-input v-model="formData.baseInfo.appSecret" v-else></el-input>
                            </el-form-item> -->
                                            <!-- <el-form-item label="md5秘钥:" v-if="$route.query.key !== 'zyhx' && $route.query.key === 'weipinshang' && $route.query.key !== 'jd_vop'">
                                <div class="verify" v-if="visibleWhether">
                                <el-input v-model="formData.baseInfo.appSecret" :disabled="true"></el-input>
                                <el-popover placement="top" width="500" v-model="visible[5].popover">
                                    <p>重置-md5秘钥</p>
                                    <el-input type="textarea" v-model="visible[5].value" class="popover_val"  rows="1"></el-input>
                                    <div style="text-align: right; margin: 0">
                                    <el-button size="mini" type="text" @click="visible[5].popover = false">取消</el-button>
                                    <el-button type="primary" size="mini" @click="reset(visible[5].name,5)">确定</el-button>
                                    </div>
                                    <el-button slot="reference" type="primary" class="verify_but">重置</el-button>
                                </el-popover>
                                </div>
                                <el-input v-model="formData.baseInfo.appSecret" v-else></el-input>
                            </el-form-item> -->
                            <el-form-item
                                label="密钥:"
                                v-if="$route.query.key === 'yjf'"
                            >
                                <div class="verify" v-if="visibleWhether">
                                    <el-input
                                        v-model="formData.baseInfo.appSecret"
                                        :disabled="true"
                                    ></el-input>
                                    <el-popover
                                        placement="top"
                                        width="500"
                                        v-model="visible[7].popover"
                                    >
                                        <p>重置-密钥</p>
                                        <el-input
                                            type="textarea"
                                            v-model="visible[7].value"
                                            class="popover_val"
                                            rows="1"
                                        ></el-input>
                                        <div
                                            style="text-align: right; margin: 0"
                                        >
                                            <el-button
                                                size="mini"
                                                type="text"
                                                @click="
                                                    visible[7].popover = false
                                                "
                                                >取消</el-button
                                            >
                                            <el-button
                                                type="primary"
                                                size="mini"
                                                @click="
                                                    reset(visible[7].name, 7)
                                                "
                                                >确定</el-button
                                            >
                                        </div>
                                        <el-button
                                            slot="reference"
                                            type="primary"
                                            class="verify_but"
                                            >重置</el-button
                                        >
                                    </el-popover>
                                </div>
                                <el-input
                                    v-model="formData.baseInfo.appSecret"
                                    v-else
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="channelID:"
                                v-else-if="$route.query.key === 'selfCake'"
                            >
                                <el-input
                                    v-model="formData.baseInfo.appSecret"
                                ></el-input>
                            </el-form-item>
                            <!-- appSecret之前的判断 v-if="$route.query.key !== 'zyhx' && $route.query.key !== 'weipinshang' && $route.query.key !== 'yjf' && $route.query.key !== 'jd_vop'" -->
                            <el-form-item
                                label="appSecret:"
                                v-else-if="
                                    $route.query.key !== 'weipinshang' &&
                                    $route.query.key !== 'jd_vop' &&
                                    $route.query.key !== 'maiger' &&
                                    $route.query.key !== 'wdt'
                                "
                            >
                                <div class="verify" v-if="visibleWhether">
                                    <el-input
                                        v-model="formData.baseInfo.appSecret"
                                        :disabled="true"
                                    ></el-input>
                                    <el-popover
                                        placement="top"
                                        width="500"
                                        v-model="visible[0].popover"
                                    >
                                        <p>重置-appSecret</p>
                                        <el-input
                                            type="textarea"
                                            v-model="visible[0].value"
                                            class="popover_val"
                                            rows="1"
                                        ></el-input>
                                        <div
                                            style="text-align: right; margin: 0"
                                        >
                                            <el-button
                                                size="mini"
                                                type="text"
                                                @click="
                                                    visible[0].popover = false
                                                "
                                                >取消</el-button
                                            >
                                            <el-button
                                                type="primary"
                                                size="mini"
                                                @click="
                                                    reset(visible[0].name, 0)
                                                "
                                                >确定</el-button
                                            >
                                        </div>
                                        <el-button
                                            slot="reference"
                                            type="primary"
                                            class="verify_but"
                                            >重置</el-button
                                        >
                                    </el-popover>
                                </div>
                                <el-input
                                    v-model="formData.baseInfo.appSecret"
                                    v-else
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="md5秘钥:"
                                v-else-if="
                                    $route.query.key !== 'zyhx' &&
                                    $route.query.key !== 'yjf' &&
                                    $route.query.key !== 'jd_vop' &&
                                    $route.query.key !== 'maiger' &&
                                    $route.query.key !== 'wdt'
                                "
                            >
                                <div class="verify" v-if="visibleWhether">
                                    <el-input
                                        v-model="formData.baseInfo.appSecret"
                                        :disabled="true"
                                    ></el-input>
                                    <el-popover
                                        placement="top"
                                        width="500"
                                        v-model="visible[6].popover"
                                    >
                                        <p>重置-md5秘钥</p>
                                        <el-input
                                            type="textarea"
                                            v-model="visible[6].value"
                                            class="popover_val"
                                            rows="1"
                                        ></el-input>
                                        <div
                                            style="text-align: right; margin: 0"
                                        >
                                            <el-button
                                                size="mini"
                                                type="text"
                                                @click="
                                                    visible[6].popover = false
                                                "
                                                >取消</el-button
                                            >
                                            <el-button
                                                type="primary"
                                                size="mini"
                                                @click="
                                                    reset(visible[6].name, 6)
                                                "
                                                >确定</el-button
                                            >
                                        </div>
                                        <el-button
                                            slot="reference"
                                            type="primary"
                                            class="verify_but"
                                            >重置</el-button
                                        >
                                    </el-popover>
                                </div>
                                <el-input
                                    v-model="formData.baseInfo.appSecret"
                                    v-else
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col
                            :span="15"
                            v-if="$route.query.key === 'selfCake'"
                        >
                            <el-form-item label="接口地址:">
                                <el-input
                                    v-model="formData.baseInfo.apiUrl"
                                ></el-input>
                                <div style="font-size: 12px; color: #dc5d5d">
                                    * 填写对方接口api域名地址,没有可忽略
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="15" v-if="$route.query.key === 'dwd'">
                            <el-form-item label="接口域名:">
                                <el-input
                                    v-model="formData.baseInfo.apiUrl"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="15" v-if="$route.query.key === 'cross'">
                            <el-form-item label="接口域名:">
                                <el-input
                                    v-model="formData.baseInfo.apiUrl"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col
                            :span="15"
                            v-if="$route.query.key === 'yzh_new'"
                        >
                            <el-form-item label="接口域名:">
                                <el-input
                                    v-model="formData.baseInfo.apiUrl"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col
                            :span="15"
                            v-if="$route.query.key === 'selfCake'"
                        >
                            <el-form-item label="用户ID:">
                                <el-input
                                    v-model="formData.baseInfo.user_id"
                                ></el-input>
                            </el-form-item>
                            <el-form-item label="订单回调地址:">
                                <div v-if="rescode == 0">{{ callBackUrl }}</div>
                            </el-form-item>
                            <el-form-item label="价格变更回调地址:">
                                <div v-if="rescode == 0">
                                    {{ priceCallBackUrl }}
                                </div>
                            </el-form-item>
                            <el-form-item label="上下架回调地址:">
                                <div v-if="rescode == 0">
                                    {{ saleCallBackUrl }}
                                </div>
                                <div v-else>保存配置后自动生成</div>
                                <div style="font-size: 12px; color: #dc5d5d">
                                    *
                                    上面回调地址复制到对接供应链平台的后台回调地址中
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col
                            :span="15"
                            v-if="
                                $route.query.key === 'youxuan' ||
                                $route.query.key === 'shama' ||
                                $route.query.key === 'guanaitong' ||
                                $route.query.key === 'maiger'
                            "
                        >
                            <el-form-item label="host:">
                                <el-input
                                    v-model="formData.baseInfo.host"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="15" v-if="$route.query.key === 'shama'">
                            <el-form-item label="ip:">
                                <el-input
                                    v-model="formData.baseInfo.ip"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="15">
                            <el-form-item
                                label="接口地址:"
                                v-if="
                                    $route.query.key === 'weipinshang' ||
                                    $route.query.key === 'yjf' ||
                                    $route.query.key === 'yiyatong'
                                "
                            >
                                <el-input
                                    v-model="formData.baseInfo.apiUrl"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="15">
                            <el-form-item
                                label="weeks:"
                                v-if="$route.query.key === 'jushuitan'"
                            >
                                <m-num-input
                                    v-model="formData.baseInfo.weeks"
                                ></m-num-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="15">
                            <el-form-item
                                label="用户名:"
                                v-if="$route.query.key === 'zyhx'"
                            >
                                <el-input
                                    v-model="formData.baseInfo.userName"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="15">
                            <el-form-item
                                label="密码:"
                                v-if="$route.query.key === 'zyhx'"
                            >
                                <div class="verify" v-if="visibleWhether">
                                    <el-input
                                        v-model="formData.baseInfo.passWord"
                                        :disabled="true"
                                    ></el-input>
                                    <el-popover
                                        placement="top"
                                        width="500"
                                        v-model="visible[9].popover"
                                    >
                                        <p>重置-密码</p>
                                        <el-input
                                            type="textarea"
                                            v-model="visible[9].value"
                                            class="popover_val"
                                            rows="1"
                                        ></el-input>
                                        <div
                                            style="text-align: right; margin: 0"
                                        >
                                            <el-button
                                                size="mini"
                                                type="text"
                                                @click="
                                                    visible[9].popover = false
                                                "
                                                >取消</el-button
                                            >
                                            <el-button
                                                type="primary"
                                                size="mini"
                                                @click="
                                                    reset(visible[9].name, 9)
                                                "
                                                >确定</el-button
                                            >
                                        </div>
                                        <el-button
                                            slot="reference"
                                            type="primary"
                                            class="verify_but"
                                            >重置</el-button
                                        >
                                    </el-popover>
                                </div>
                                <el-input
                                    v-model="formData.baseInfo.passWord"
                                    v-else
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="15" v-if="$route.query.key === 'zyhx'">
                            <el-form-item label="接口地址:">
                                <el-input
                                    v-model="formData.baseInfo.apiUrl"
                                ></el-input>
                                <div style="font-size: 12px; color: #dc5d5d">
                                    * 填写对方接口api域名地址,没有可忽略
                                </div>
                            </el-form-item>
                        </el-col>

                        <el-col
                            :span="15"
                            v-if="
                                routeKey != 'zyhx' &&
                                $route.query.key !== 'tianma' &&
                                $route.query.key !== 'yjf' &&
                                $route.query.key !== 'jd_vop' &&
                                $route.query.key !== 'guanaitong' &&
                                $route.query.key !== 'maiger' &&
                                $route.query.key !== 'selfCake' && 
                                $route.query.key !== 'kunsheng'
                            "
                        >
                            <el-form-item label="回调地址:">
                                <div v-if="rescode == 0">{{ callBackUrl }}</div>
                                <div v-else>保存配置后自动生成</div>
                                <div style="font-size: 12px; color: #dc5d5d">
                                    *
                                    上面回调地址复制到对接供应链平台的后台回调地址中
                                </div>
                            </el-form-item>
                        </el-col>

                        <el-col :span="15" v-if="routeKey === 'aljx'">
                            <el-form-item label="token:">
                                <div class="f fac">
                                    <el-input
                                        v-model="formData.baseInfo.token"
                                        class="w600"
                                    ></el-input>
                                    <el-button
                                        type="text"
                                        class="ml_10"
                                        @click="getCode"
                                        >获取code</el-button
                                    >
                                    <el-button
                                        type="text"
                                        @click="openTokenDialog"
                                        >生成token
                                    </el-button>
                                </div>
                            </el-form-item>
                        </el-col>

                        <!-- 京东vop start-->
                        <el-col :span="15">
                            <el-form-item
                                label="username:"
                                v-if="$route.query.key === 'jd_vop'"
                            >
                                <el-input
                                    v-model="formData.baseInfo.username"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="15">
                            <el-form-item
                                label="password:"
                                v-if="$route.query.key === 'jd_vop'"
                            >
                                <div class="verify" v-if="visibleWhether">
                                    <el-input
                                        v-model="formData.baseInfo.password"
                                        :disabled="true"
                                    ></el-input>
                                    <el-popover
                                        placement="top"
                                        width="500"
                                        v-model="visible[1].popover"
                                    >
                                        <p>重置-password</p>
                                        <el-input
                                            type="textarea"
                                            v-model="visible[1].value"
                                            class="popover_val"
                                            rows="1"
                                        ></el-input>
                                        <div
                                            style="text-align: right; margin: 0"
                                        >
                                            <el-button
                                                size="mini"
                                                type="text"
                                                @click="
                                                    visible[1].popover = false
                                                "
                                                >取消</el-button
                                            >
                                            <el-button
                                                type="primary"
                                                size="mini"
                                                @click="
                                                    reset(visible[1].name, 1)
                                                "
                                                >确定</el-button
                                            >
                                        </div>
                                        <el-button
                                            slot="reference"
                                            type="primary"
                                            class="verify_but"
                                            >重置</el-button
                                        >
                                    </el-popover>
                                </div>
                                <el-input
                                    v-model="formData.baseInfo.password"
                                    v-else
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="15">
                            <el-form-item
                                label="client_id:"
                                v-if="
                                    $route.query.key === 'jd_vop' ||
                                    $route.query.key === 'maiger'
                                "
                            >
                                <div class="verify" v-if="visibleWhether">
                                    <el-input
                                        v-model="formData.baseInfo.client_id"
                                        :disabled="true"
                                    ></el-input>
                                    <el-popover
                                        placement="top"
                                        width="500"
                                        v-model="visible[2].popover"
                                    >
                                        <p>重置-client_id</p>
                                        <el-input
                                            type="textarea"
                                            v-model="visible[2].value"
                                            class="popover_val"
                                            rows="1"
                                        ></el-input>
                                        <div
                                            style="text-align: right; margin: 0"
                                        >
                                            <el-button
                                                size="mini"
                                                type="text"
                                                @click="
                                                    visible[2].popover = false
                                                "
                                                >取消</el-button
                                            >
                                            <el-button
                                                type="primary"
                                                size="mini"
                                                @click="
                                                    reset(visible[2].name, 2)
                                                "
                                                >确定</el-button
                                            >
                                        </div>
                                        <el-button
                                            slot="reference"
                                            type="primary"
                                            class="verify_but"
                                            >重置</el-button
                                        >
                                    </el-popover>
                                </div>
                                <el-input
                                    v-model="formData.baseInfo.client_id"
                                    v-else
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="15">
                            <el-form-item
                                label="client_secret:"
                                v-if="
                                    $route.query.key === 'jd_vop' ||
                                    $route.query.key === 'maiger'
                                "
                            >
                                <div class="verify" v-if="visibleWhether">
                                    <el-input
                                        v-model="
                                            formData.baseInfo.client_secret
                                        "
                                        :disabled="true"
                                    ></el-input>
                                    <el-popover
                                        placement="top"
                                        width="500"
                                        v-model="visible[3].popover"
                                    >
                                        <p>重置-client_secret</p>
                                        <el-input
                                            type="textarea"
                                            v-model="visible[3].value"
                                            class="popover_val"
                                            rows="1"
                                        ></el-input>
                                        <div
                                            style="text-align: right; margin: 0"
                                        >
                                            <el-button
                                                size="mini"
                                                type="text"
                                                @click="
                                                    visible[3].popover = false
                                                "
                                                >取消</el-button
                                            >
                                            <el-button
                                                type="primary"
                                                size="mini"
                                                @click="
                                                    reset(visible[3].name, 3)
                                                "
                                                >确定</el-button
                                            >
                                        </div>
                                        <el-button
                                            slot="reference"
                                            type="primary"
                                            class="verify_but"
                                            >重置</el-button
                                        >
                                    </el-popover>
                                </div>
                                <el-input
                                    v-model="formData.baseInfo.client_secret"
                                    v-else
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="15">
                            <el-form-item
                                label="收票人电话:"
                                v-if="$route.query.key === 'jd_vop'"
                            >
                                <el-input
                                    v-model="formData.baseInfo.ticket_tel"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="15">
                            <el-form-item
                                label="专票资质公司名称:"
                                v-if="$route.query.key === 'jd_vop'"
                            >
                                <el-input
                                    v-model="formData.baseInfo.ticket_title"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="15">
                            <el-form-item
                                label="专票资质纳税人识别号:"
                                v-if="$route.query.key === 'jd_vop'"
                            >
                                <div class="verify" v-if="visibleWhether">
                                    <el-input
                                        v-model="
                                            formData.baseInfo.ticket_number
                                        "
                                        :disabled="true"
                                    ></el-input>
                                    <el-popover
                                        placement="top"
                                        width="500"
                                        v-model="visible[4].popover"
                                    >
                                        <p>重置-专票资质纳税人识别号</p>
                                        <el-input
                                            type="textarea"
                                            v-model="visible[4].value"
                                            class="popover_val"
                                            rows="1"
                                        ></el-input>
                                        <div
                                            style="text-align: right; margin: 0"
                                        >
                                            <el-button
                                                size="mini"
                                                type="text"
                                                @click="
                                                    visible[4].popover = false
                                                "
                                                >取消</el-button
                                            >
                                            <el-button
                                                type="primary"
                                                size="mini"
                                                @click="
                                                    reset(visible[4].name, 4)
                                                "
                                                >确定</el-button
                                            >
                                        </div>
                                        <el-button
                                            slot="reference"
                                            type="primary"
                                            class="verify_but"
                                            >重置</el-button
                                        >
                                    </el-popover>
                                </div>
                                <el-input
                                    v-model="formData.baseInfo.ticket_number"
                                    v-else
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <!-- 京东vop end -->
                        <el-col :span="15">
                            <el-form-item label="供应链名称:">
                                <div class="verify">
                                    <el-input
                                        v-model="formData.baseInfo.storeName"
                                    ></el-input>
                                    <el-button
                                        v-if="
                                            $route.query.key === 'shama' ||
                                            $route.query.key === 'guanaitong'
                                        "
                                        class="verify_but"
                                        type="primary"
                                        @click="initGoods"
                                        >初始化商品</el-button
                                    >
                                    <el-button
                                        v-else
                                        class="verify_but"
                                        type="primary"
                                        @click="verify"
                                        >效验配置</el-button
                                    >
                                </div>
                                <div
                                    v-if="$route.query.key === 'shama'"
                                    style="font-size: 12px; color: #dc5d5d"
                                >
                                    *
                                    点击初始化商品以后程序要在后台执行一段时间,时长取决于商品数量,执行中请勿重复点击!
                                </div>
                            </el-form-item>
                        </el-col>
                        <!-- <el-col :span="15" v-if="$route.query.key === 'stbz' || $route.query.key === 'self' || $route.query.key === 'selfCake'"> -->
                        <el-col
                            :span="15"
                            v-if="
                                $route.query.key === 'stbz' ||
                                $route.query.key === 'self'
                            "
                        >
                            <el-form-item label="销量开关:">
                                <el-radio-group v-model="formData.update.sales">
                                    <el-radio :label="1">开启</el-radio>
                                    <el-radio :label="2">关闭</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>

                        <el-col :span="15" v-if="$route.query.key === 'stbz'">
                            <el-form-item label="京东商品拆分规格:">
                                <el-radio-group
                                    v-model="formData.update.splitSku"
                                >
                                    <el-radio :label="1">开启</el-radio>
                                    <el-radio :label="2">关闭</el-radio>
                                </el-radio-group>
                                <br />开启后，新导入的京东商品将按规格拆分成多个商品，每个规格一个商品！原导入的商品不变！
                            </el-form-item>
                        </el-col>
                        <el-col :span="15">
                            <el-form-item label="pc端选品中心显示:">
                                <el-radio-group
                                    v-model="
                                        formData.baseInfo
                                            .show_source_in_my_storage
                                    "
                                >
                                    <el-radio :label="0">开启</el-radio>
                                    <el-radio :label="1">关闭</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <template v-if="routeKey === 'selfCake'">
                        <p class="title-p">更新设置</p>
                        <el-divider></el-divider>
                        <el-form-item label="定时任务规则:">
                            <div class="f fac">
                                <el-input
                                    v-model="formData.update.cron"
                                    style="width: 30%"
                                ></el-input>
                            </div>
                            <p class="hint-p">
                                示例：0 0 03,19 * * ?
                                表示每天凌晨3点跟19点执行两次，具体规则自行查询
                            </p>
                        </el-form-item>
                        <el-form-item label="自动导入商品:">
                            <el-radio-group
                                v-model="formData.update.autoImportProduct"
                            >
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <!--            <p class="hint-p">关闭则不更新商品供货价</p>-->
                        </el-form-item>
                        <el-form-item label="自动导入规则:">
                            <div class="f fac">
                                <el-input
                                    v-model="
                                        formData.update.autoImportProductCron
                                    "
                                    style="width: 30%"
                                ></el-input>
                            </div>
                            <!--            <p class="hint-p">-->
                            <!--                示例：0 0 03,19 * * ? 表示每天凌晨3点跟19点执行两次，具体规则自行查询-->
                            <!--            </p>-->
                        </el-form-item>
                        <el-form-item label="自动更新供货价:">
                            <el-radio-group
                                v-model="formData.update.currentPrice"
                            >
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <p class="hint-p">关闭则不更新商品供货价</p>
                        </el-form-item>
                        <el-form-item label="自动更新分类:">
                            <el-radio-group v-model="formData.update.category">
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <p class="hint-p">关闭则不更新商品分类</p>
                        </el-form-item>

                        <!--      <el-form-item label="自动更新市场价:">-->
                        <!--        <el-radio-group v-model="formData.update.originalPrice">-->
                        <!--          <el-radio :label="1">开启</el-radio>-->
                        <!--          <el-radio :label="2">关闭</el-radio>-->
                        <!--        </el-radio-group>-->
                        <!--        <p class="hint-p">关闭则不更新商品市场价</p>-->
                        <!--      </el-form-item>-->
                        <el-form-item label="自动更新成本价:">
                            <el-radio-group v-model="formData.update.costPrice">
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <p class="hint-p">关闭则不更新商品成本价</p>
                        </el-form-item>
                        <el-form-item label="自动更新基本信息:">
                            <el-radio-group v-model="formData.update.baseInfo">
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <p class="hint-p">关闭则不更新商品名称和商品详情</p>
                        </el-form-item>
                        <!--      <el-form-item label="去除图片水印:" v-if="$route.query.key !== 'szbao'">-->
                        <!--        <el-radio-group v-model="formData.update.imageWatermark">-->
                        <!--          <el-radio :label="1">开启</el-radio>-->
                        <!--          <el-radio :label="2">关闭</el-radio>-->
                        <!--        </el-radio-group>-->
                        <!--      </el-form-item>-->
                        <el-form-item
                            label="导入时创建品牌:"
                            v-if="$route.query.key !== 'szbao'"
                        >
                            <el-radio-group
                                v-model="formData.update.createBrand"
                            >
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <p class="hint-p">
                                开启后导入商品时将创建对应商品品牌并将商品绑入相应品牌
                            </p>
                        </el-form-item>
                    </template>
                    <template v-else-if="routeKey === 'aljx'">
                        <p class="title-p">更新设置</p>
                        <el-divider></el-divider>
                        <el-form-item label="定时任务规则:">
                            <div class="f fac">
                                <el-input
                                    v-model="formData.update.cron"
                                    style="width: 30%"
                                ></el-input>
                            </div>
                            <p class="hint-p">
                                示例：0 0 03,19 * * ?
                                表示每天凌晨3点跟19点执行两次，具体规则自行查询
                            </p>
                        </el-form-item>
                        <el-form-item label="自动更新供货价:">
                            <el-radio-group
                                v-model="formData.update.currentPrice"
                            >
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <p class="hint-p">关闭则不更新商品供货价</p>
                        </el-form-item>
                        <el-form-item label="自动更新分类:">
                            <el-radio-group v-model="formData.update.category">
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <p class="hint-p">关闭则不更新商品分类</p>
                        </el-form-item>
                        <el-form-item label="自动更新成本价:">
                            <el-radio-group v-model="formData.update.costPrice">
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <p class="hint-p">关闭则不更新商品成本价</p>
                        </el-form-item>
                        <el-form-item label="自动更新基本信息:">
                            <el-radio-group v-model="formData.update.baseInfo">
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <p class="hint-p">关闭则不更新商品名称、商品主图、商品图片、商品详情！</p>
                        </el-form-item>
                        <el-form-item label="自动更新指导价:">
                            <el-radio-group
                                v-model="formData.update.guide_price"
                            >
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <p class="hint-p">关闭则不更新商品指导价</p>
                        </el-form-item>
                        <el-form-item label="自动更新建议零售价:">
                            <el-radio-group
                                v-model="formData.update.originalPrice"
                            >
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <p class="hint-p">关闭则不更新商品建议零售价</p>
                        </el-form-item>
                        <el-form-item label="自动更新营销价:">
                            <el-radio-group
                                v-model="formData.update.activity_price"
                            >
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <p class="hint-p">关闭则不更新商品营销价</p>
                        </el-form-item>
                        <el-form-item label="手动更新设置:">
                            <el-checkbox-group v-model="formData.update.manually_update_settings">
                                <el-checkbox v-for="(item,index) in manuallyUpdateSettingList" :key="index" :label="item.key">
                                    {{ item.name }}
                                </el-checkbox>
                            </el-checkbox-group>
                            <p class="hint-p">默认不勾选，勾选后，在商品列表点击更新商品，则更新对应的项目;价格按照定价策略执行，如果店铺有独立设置的，优先走独立设置!</p>
                        </el-form-item>
                    </template>
                    <template v-else-if="routeKey !== 'jushuitan' && routeKey !== 'wdt'">
                        <p class="title-p">更新设置</p>
                        <el-divider></el-divider>
                        <el-form-item label="定时任务规则:">
                            <div class="f fac">
                                <el-input
                                    v-model="formData.update.cron"
                                    style="width: 30%"
                                ></el-input>
                            </div>
                            <p class="hint-p">
                                示例：0 0 03,19 * * ?
                                表示每天凌晨3点跟19点执行两次，具体规则自行查询
                            </p>
                        </el-form-item>
                        <el-form-item label="自动更新供货价:">
                            <el-radio-group
                                v-model="formData.update.currentPrice"
                            >
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <p class="hint-p">关闭则不更新商品供货价</p>
                        </el-form-item>
                        <el-form-item
                            label="建议零售价:"
                            v-if="$route.query.key === 'yzh_new'"
                        >
                            <el-radio-group
                                v-model="formData.update.originalPrice"
                            >
                                <el-radio :label="0">开启</el-radio>
                                <el-radio :label="1">关闭</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item
                            label="指导价:"
                            v-if="$route.query.key === 'yzh_new'"
                        >
                            <el-radio-group
                                v-model="formData.update.guide_price"
                            >
                                <el-radio :label="0">开启</el-radio>
                                <el-radio :label="1">关闭</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item
                            label="营销价:"
                            v-if="$route.query.key === 'yzh_new'"
                        >
                            <el-radio-group
                                v-model="formData.update.activity_price"
                            >
                                <el-radio :label="0">开启</el-radio>
                                <el-radio :label="1">关闭</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="自动更新分类:">
                            <el-radio-group v-model="formData.update.category">
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <p class="hint-p">关闭则不更新商品分类</p>
                        </el-form-item>
                        <!--      <el-form-item label="自动更新市场价:">-->
                        <!--        <el-radio-group v-model="formData.update.originalPrice">-->
                        <!--          <el-radio :label="1">开启</el-radio>-->
                        <!--          <el-radio :label="2">关闭</el-radio>-->
                        <!--        </el-radio-group>-->
                        <!--        <p class="hint-p">关闭则不更新商品市场价</p>-->
                        <!--      </el-form-item>-->
                        <el-form-item
                            label="自动更新成本价:"
                            v-if="
                                routeKey !== 'jd_vop' && routeKey !== 'maiger'
                            "
                        >
                            <el-radio-group v-model="formData.update.costPrice">
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <p class="hint-p">关闭则不更新商品成本价</p>
                        </el-form-item>

                        <el-form-item
                            label="自动更新建议零售价:"
                            v-if="$route.query.key === 'szbao'"
                        >
                            <el-radio-group
                                v-model="formData.update.originalPrice"
                            >
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <p class="hint-p">关闭则不更新商品建议零售价</p>
                        </el-form-item>
                        <el-form-item
                            label="自动更新指导价:"
                            v-if="$route.query.key === 'szbao'"
                        >
                            <el-radio-group
                                v-model="formData.update.guide_price"
                            >
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <p class="hint-p">关闭则不更新商品指导价</p>
                        </el-form-item>
                        <el-form-item
                            label="自动更新营销价:"
                            v-if="$route.query.key === 'szbao'"
                        >
                            <el-radio-group
                                v-model="formData.update.activity_price"
                            >
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <p class="hint-p">关闭则不更新商品营销价</p>
                        </el-form-item>

                        <el-form-item label="自动更新基本信息:">
                            <el-radio-group v-model="formData.update.baseInfo">
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <p class="hint-p">关闭则不更新商品名称和商品详情</p>
                        </el-form-item>
                        <el-form-item
                            v-if="$route.query.key === 'stbz'"
                            label="自动更新零售价，协议价，营销价:"
                        >
                            <el-radio-group
                                v-model="formData.update.originalPrice"
                            >
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <p class="hint-p">
                                关闭则不更新零售价，协议价，营销价
                            </p>
                        </el-form-item>

                        <el-form-item
                            v-if="
                                $route.query.key === 'shama' ||
                                $route.query.key === 'tianma' ||
                                $route.query.key === 'kunsheng'
                            "
                            label="自动更新商品指导价:"
                        >
                            <el-radio-group
                                v-model="formData.update.guide_price"
                            >
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <p class="hint-p">关闭则不更新指导价</p>
                        </el-form-item>

                        <el-form-item
                            v-if="
                                $route.query.key === 'shama' ||
                                $route.query.key === 'tianma' ||
                                $route.query.key === 'kunsheng'
                            "
                            label="自动更新商品营销价:"
                        >
                            <el-radio-group
                                v-model="formData.update.activity_price"
                            >
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <p class="hint-p">关闭则不更新营销价</p>
                        </el-form-item>

                        <!-- <el-form-item v-if="$route.query.key === 'self' || $route.query.key === 'selfCake'" label="自动更新商品发票信息:"> -->
                        <el-form-item
                            v-if="$route.query.key === 'self'"
                            label="自动更新商品发票信息:"
                        >
                            <el-radio-group v-model="formData.update.bill">
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                            <p class="hint-p">关闭则不更新商品发票信息</p>
                        </el-form-item>
                    </template>

                    <!-- <el-form-item label="去除图片水印:" v-if="$route.query.key !== 'szbao'">
                           <el-radio-group v-model="formData.update.imageWatermark">
                             <el-radio :label="1">开启</el-radio>
                             <el-radio :label="2">关闭</el-radio>
                           </el-radio-group>
                         </el-form-item>
                   <el-form-item label="导入时创建品牌:" v-if="$route.query.key !== 'szbao'">
                       <el-radio-group v-model="formData.update.createBrand">
                           <el-radio :label="1">开启</el-radio>
                           <el-radio :label="2">关闭</el-radio>
                       </el-radio-group>
                       <p class="hint-p">
                           开启后导入商品时将创建对应商品品牌并将商品绑入相应品牌
                       </p>
                   </el-form-item> -->

                    <!-- 测试模板展示部分 -->
                    <!--      <copyTemplate ref="copyTemplate" v-if="routeKey === 'copyTemplate'"></copyTemplate>-->
                    <yzh ref="yzh" v-if="routeKey === 'yzh'"></yzh>
                    <stbz ref="stbz" v-if="routeKey === 'stbz'"></stbz>
                    <self ref="self" v-if="routeKey === 'self'"></self>
                    <selfCake
                        ref="selfCake"
                        v-if="routeKey === 'selfCake'"
                    ></selfCake>
                    <szbao ref="szbao" v-if="routeKey === 'szbao'"></szbao>
                    <stbzCloud
                        ref="stbzCloud"
                        v-if="routeKey === 'stbz'"
                    ></stbzCloud>
                    <cross ref="cross" v-if="routeKey === 'cross'"></cross>
                    <dwd ref="dwd" v-if="routeKey === 'dwd'"></dwd>
                    <yzhNew
                        ref="yzh_new"
                        v-if="routeKey === 'yzh_new'"
                    ></yzhNew>
                    <zyhx ref="zyhx" v-if="routeKey === 'zyhx'"></zyhx>
                    <hehe ref="hehe" v-if="routeKey === 'hehe'"></hehe>
                    <aljx ref="aljx" v-if="routeKey === 'aljx'"></aljx>
                    <jushuitan
                        ref="jushuitan"
                        v-if="routeKey === 'jushuitan'"
                    ></jushuitan>
                    <weipinshang
                        ref="weipinshang"
                        v-if="routeKey === 'weipinshang'"
                    ></weipinshang>
                    <tianma ref="tianma" v-if="routeKey === 'tianma'"></tianma>
                    <youxuan
                        ref="youxuan"
                        v-if="routeKey === 'youxuan'"
                    ></youxuan>
                    <yiyatong
                        ref="yiyatong"
                        v-if="routeKey === 'yiyatong'"
                    ></yiyatong>
                    <shama ref="shama" v-if="routeKey === 'shama'"></shama>
                    <yjf ref="yjf" v-if="routeKey === 'yjf'"></yjf>
                    <jdvop ref="jd_vop" v-if="routeKey === 'jd_vop'"></jdvop>
                    <guanaitong
                        ref="guanaitong"
                        v-if="routeKey === 'guanaitong'"
                    ></guanaitong>
                    <maiger ref="maiger" v-if="routeKey === 'maiger'"></maiger>
                    <kunsheng ref="kunsheng" v-if="routeKey === 'kunsheng'"></kunsheng>
                    <wdt ref="wdt" v-if="routeKey === 'wdt'"></wdt>
                    <!-- 测试模板展示部分 -->

                    <p
                        class="title-p"
                        v-if="
                            $route.query.key !== 'szbao' &&
                            routeKey !== 'jd_vop' &&
                            routeKey !== 'maiger'
                        "
                    >
                        风控策略
                    </p>
                    <el-divider
                        v-if="
                            $route.query.key !== 'szbao' &&
                            routeKey !== 'jd_vop' &&
                            routeKey !== 'maiger'
                        "
                    ></el-divider>
                    <el-form-item
                        label="风控策略:"
                        v-if="
                            $route.query.key !== 'szbao' &&
                            routeKey !== 'jd_vop' &&
                            routeKey !== 'maiger'
                        "
                    >
                        <div class="f fac blockRadio-box">
                            <el-radio
                                v-model="formData.management.productPriceStatus"
                                :label="1"
                            ></el-radio>
                            <el-form-item
                                label="产品售价 < 成本价"
                                label-width="135px"
                            >
                                <div class="f fac">
                                    <el-input
                                        v-model="formData.management.products"
                                    ></el-input>
                                    <p class="f1 ml10">%</p>
                                </div>
                            </el-form-item>
                        </div>
                        <div class="f fac blockRadio-box">
                            <el-radio
                                v-model="formData.management.productPriceStatus"
                                :label="2"
                            ></el-radio>
                            <el-form-item
                                label="利润率 < 设定利润"
                                label-width="135px"
                            >
                                <div class="f fac">
                                    <el-input
                                        v-model="formData.management.profit"
                                    ></el-input>
                                    <p class="f1 ml10">%</p>
                                </div>
                            </el-form-item>
                        </div>
                        <p class="hint-p">利润率 = (售价-成本价)/成本价</p>
                        <p class="hint-p">满足条件的商品会执行下架</p>
                    </el-form-item>
                    <template v-if="routeKey !== 'aljx'">
                    <p class="title-p">订单策略</p>
                    <el-divider></el-divider>
                    <el-form-item label="售后策略:">
                        <el-radio-group
                            v-model="formData.order_after_sales_strategy.type"
                        >
                            <el-radio :label="0">自动提交</el-radio>
                            <el-radio :label="1">审核后提交</el-radio>
                        </el-radio-group>
                        <p class="text-p">
                            选择自动提交：<br />
                            1、中台用户申请售后，售后订单自动同步到供应链中台；<br />
                            2、中台可以单独对售后状态进行操作，如退款等，不影响上游供应链中台订单售后状态，<span
                                style="font-weight: bold"
                                >如中台自行处理上游供应链订单售后的，请务必确认上游供应链售后订单状态；</span
                            ><br />
                            3、上游供应链中台操作订单售后状态同步给中台系统，如果中台系统已经单独变更了对应的售后状态，同步时将不在执行任何操作。<br />
                            <span v-if="routeKey !== 'aljx' && routeKey !== 'stbz' && routeKey !== 'tianma' && routeKey !== 'yiyatong'">4、自动提交的，中台端如果驳回售后申请，上游供应链中台同步驳回；中台端取消，上游供应链中台同步取消。<br /></span> 
                            选择审核后提交：<br />
                            1、中台用户申请售后，售后不会直接同步到上游供应链中台，需中台审核通过后才会同步申请上游供应链中台订单售后；同步后上游供应链中台售后订单依旧为待审核状态；如果中台审核取消或驳回，将不会同步创建上游供应链中台售后订单。<br />
                            2、中台可以单独对售后状态进行操作，如退款等，不影响上游供应链中台订单售后状态，<span
                                style="font-weight: bold"
                                >如中台自行处理上游供应链订单售后的，请务必确认上游供应链售后订单状态；</span
                            ><br />
                            3、上游供应链中台操作订单售后状态同步给中台系统，如果中台系统已经单独变更了对应的售后状态，同步时将不在执行任何操作。
                        </p>
                    </el-form-item>
                </template>
                    <el-form-item>
                        <el-button type="primary" @click="saveSetting"
                            >提交</el-button
                        >
                    </el-form-item>
                </el-form>
            </el-tab-pane>
            <el-tab-pane label="店铺设置" name="storeSetting">
                <el-form class="mt25" :model="shopData" label-width="130px">
                    <el-form-item
                        label="供应链logo:"
                        prop="value.shop_logo_squar"
                    >
                        <el-upload
                            class="avatar-uploader"
                            :show-file-list="false"
                            :action="path + '/fileUploadAndDownload/upload'"
                            :headers="{ 'x-token': token }"
                            :on-success="handleSupplier"
                            :before-upload="$fn.beforeAvatarUpload"
                            accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF"
                        >
                            <m-image
                                v-if="shopData.value.shop_logo_square"
                                :src="shopData.value.shop_logo_square"
                                style="width: 100%; height: 100%"
                            >
                            </m-image>
                            <i
                                v-else
                                class="el-icon-plus avatar-uploader-icon"
                            ></i>
                        </el-upload>
                    </el-form-item>
                    <el-form-item
                        label="店铺公告:"
                        prop="value.shop_announcement"
                    >
                        <m-editor
                            v-model="shopData.value.shop_announcement"
                        ></m-editor>
                    </el-form-item>
                    <el-form-item
                        label="退货说明:"
                        prop="value.shop_store_returns_instructions"
                    >
                        <m-editor
                            v-model="
                                shopData.value.shop_store_returns_instructions
                            "
                        ></m-editor>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="saveShop"
                            >提交</el-button
                        >
                    </el-form-item>
                </el-form>
            </el-tab-pane>
            <el-tab-pane label="店铺执照" name="storeLicense">
                <el-form class="mt25" :model="shopData" label-width="130px">
                    <el-form-item
                        label="营业执照:"
                        prop="value.ShopStoreLicenses"
                    >
                        <div class="f fw">
                            <div
                                class="cover-list-box"
                                v-for="(item, index) in shopData.value
                                    .ShopStoreLicenses"
                                :key="item.id"
                            >
                                <m-image
                                    class="cover-image"
                                    :src="item"
                                ></m-image>
                                <div class="del-box">
                                    <el-button
                                        type="text"
                                        icon="el-icon-delete"
                                        @click="delCover(index)"
                                    ></el-button>
                                </div>
                            </div>
                            <el-upload
                                class="avatar-uploader"
                                :show-file-list="false"
                                :action="path + '/fileUploadAndDownload/upload'"
                                :headers="{ 'x-token': token }"
                                :on-success="handleLicenses"
                                :before-upload="$fn.beforeAvatarUpload"
                                accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF"
                            >
                                <!-- <m-image
                                  v-if="shopData.value.ShopStoreLicenses"
                                  :src="shopData.value.ShopStoreLicenses"
                                  style="width:100%;height:100%"
                                >
                                </m-image> -->
                                <!-- <i v-else class="el-icon-plus avatar-uploader-icon"></i> -->
                                <i
                                    class="el-icon-plus avatar-uploader-icon"
                                ></i>
                            </el-upload>
                        </div>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="saveShop"
                            >提交</el-button
                        >
                    </el-form-item>
                </el-form>
            </el-tab-pane>
            <el-tab-pane label="支付设置" name="paymentSetup">
                <SimultaneousAssignmentPayment />
            </el-tab-pane>
        </el-tabs>
        <aljxTokenDialog ref="aljxTokenDialog" @getToken="getToken" />
    </m-card>
</template>

<script>
// 配置信息
import { mapGetters } from 'vuex';
import {
    getSetting,
    saveSetting,
    setSupplySetting,
    getSupplySetting,
    updateName,
    validateConfig,
    shamaInitGoods,
    guanaitongInitGoods,
    updateSettingMask,
} from '@/api/gatherSupply';
import aljxTokenDialog from './components/aljxTokenDialog.vue';
import yzh from './configComponents/yzh';
import stbz from './configComponents/stbz';
import self from './configComponents/self';
import selfCake from './configComponents/selfCake';
import szbao from './configComponents/szbao';
import stbzCloud from './configComponents/stbzCloud';
import cross from './configComponents/cross';
import dwd from './configComponents/dwd';
import yzhNew from './configComponents/yzh_new';
import hehe from './configComponents/hehe';
import zyhx from './configComponents/zyhx';
import aljx from './configComponents/aljx';
import jushuitan from './configComponents/jushuitan';
import weipinshang from './configComponents/weipinshang.vue';
import tianma from './configComponents/tianma.vue';
import youxuan from './configComponents/youxuan.vue';
import shama from './configComponents/shama.vue';
import yiyatong from './configComponents/yiyatong.vue';
import yjf from './configComponents/yjf.vue';
import SimultaneousAssignmentPayment from '@/view/supply/page/components/simultaneousAssignmentPayment.vue';
import jdvop from './configComponents/jd_vop.vue';
import guanaitong from './configComponents/guanaitong.vue';
import maiger from './configComponents/maiger.vue';
import kunsheng from './configComponents/kunsheng.vue';
import wdt from './configComponents/wdt';
export default {
    name: 'SupplyConfigMessage',
    components: {
        aljxTokenDialog,
        yzh,
        stbz,
        self,
        selfCake,
        szbao,
        stbzCloud,
        cross,
        dwd,
        yzhNew,
        hehe,
        zyhx,
        aljx,
        jushuitan,
        weipinshang,
        tianma,
        youxuan,
        yiyatong,
        shama,
        yjf,
        SimultaneousAssignmentPayment,
        jdvop,
        guanaitong,
        maiger,
        kunsheng,
        wdt,
    },
    // components: {aljxTokenDialog, yzh, stbz, self, szbao, stbzCloud, cross, dwd, yzhNew, hehe, zyhx, aljx, jushuitan, youxuan, SimultaneousAssignmentPayment},
    data() {
        return {
            activeName: 'baseSetting',
            callBackUrl: '',
            priceCallBackUrl: '',
            saleCallBackUrl: '',
            domain: '',
            routeKey: this.$route.query.key || '',
            rescode: 9,
            formData: {
                baseInfo: {},
                update: {
                    show_source_in_my_storage: 2, // pc端选品中心显示
                    sales: 2,
                    splitSku: 0,
                    currentPrice: 2, // 自动更新供货价
                    category: 2, // 自动更新分类
                    baseInfo: 2, // 自动更新基本信息
                    guide_price: 2, // 自动更新指导价
                    activity_price: 2, // 自动更新营销价
                    originalPrice: 2, // 自动更新建议零售价
                    manually_update_settings: [], // 手动更新设置
                    /* originalPrice: 2,
                    costPrice: 2,
                    imageWatermark: 2,
                    createBrand: 2, */
                },
                pricing: {
                    /*strategy: 1,

                    JDSales: 1,
                    JDSalesGuide: "100",
                    JDSalesAgreement: "100",
                    JDSalesMarketing: "100",

                    JDCostPrice: 1,
                    JDCostAgreement: "100",
                    JDCostMarketing: "100",

                    ALSales: 1,
                    ALSalesGuide: "100",
                    ALSalesAgreement: "100",
                    ALSalesMarketing: "100",

                    ALCost: 1,
                    ALCostAgreement: "100",
                    ALCostMarketing: "100",

                    TMSales: 1,
                    TMSalesGuide: "100",
                    TMSalesAgreement: "100",
                    TMSalesMarketing: "100",

                    TMCost: 1,
                    TMCostAgreement: "100",
                    TMCostMarketing: "100",

                    YCSales: 1,
                    YCSalesGuide: "100",
                    YCSalesAgreement: "100",
                    YCSalesMarketing: "100",

                    YCCost: 1,
                    YCCostAgreement: "100",
                    YCCostMarketing: "100",

                    SupplySales: 1,
                    SupplySalesGuide: "100",
                    SupplySalesAgreement: "100",
                    SupplySalesMarketing: "100",

                    SupplyCost: 1,
                    SupplyCostAgreement: "100",
                    SupplyCostMarketing: "100",*/
                },
                cloud: {},
                management: {
                    productPriceStatus: 1,
                    products: 0,
                },
                storage_auth: {},
                order_after_sales_strategy: {
                    type: 0,
                },
            },
            // 店铺设置、店铺执照
            shopData: {
                value: {
                    shop_announcement: '', // 店铺公告
                    ShopStoreLicenses: [], // 营业执照
                    shop_store_returns_instructions: '', // 退换货说明
                    shop_logo_square: '', //供应链logo
                },
            },
            path: this.$path,
            tempId: 0,
            tempKey: '',

            // 提示弹框
            visible: [
                { name: 'appSecret', popover: false, value: '' },
                { name: 'password', popover: false, value: '' },
                { name: 'client_id', popover: false, value: '' },
                { name: 'client_secret', popover: false, value: '' },
                { name: 'ticket_number', popover: false, value: '' },
                { name: 'md5秘钥', popover: false, value: '' },
                { name: 'md5秘钥1', popover: false, value: '' },
                { name: '密钥', popover: false, value: '' },
                { name: 'passWord', popover: false, value: '' },
                { name: 'passWord1', popover: false, value: '' },
                { name: 'appsecret', popover: false, value: '' },
                { name: 'distributor_appsecret', popover: false, value: '' },
            ],
            visibleWhether: false,
            manuallyUpdateSettingList: [
                { name: '供货价', key: 'currentPrice' },
                { name: '分类', key: 'cateGory' },
                { name: '成本价', key: 'costPrice' },
                { name: '基础信息', key: 'baseInfo' },
                { name: '指导价', key: 'guide_price' },
                { name: '建议零售价', key: 'originalPrice' },
                { name: '营销价', key: 'activity_price' },
            ]
        };
    },
    computed: {
        ...mapGetters('user', ['userInfo', 'token']),
    },
    created() {
        getSetting({
            key: 'gatherSupply' + parseInt(this.$route.query.id),
        }).then((res) => {
            try {
                let value = JSON.parse(res.data.data.value);
                // console.log(value,111);
                // if ((this.$route.query.key === 'stbz' || this.$route.query.key === 'self' || this.$route.query.key === 'selfCake') && !('sales' in value.update)) {
                if (
                    (this.$route.query.key === 'stbz' ||
                        this.$route.query.key === 'self') &&
                    !('sales' in value.update)
                ) {
                    value.update.sales = 2;
                }
                if (value.order_after_sales_strategy === undefined) {
                    value.order_after_sales_strategy =
                        this.formData.order_after_sales_strategy;
                }
                this.formData = this.$fn.deepClone(value);
                this.rescode = res.code;
                // console.log("rescode", res.code);
                if (this.routeKey) {
                    this.$refs[this.routeKey].setFrom(this.formData.pricing);
                    if (this.routeKey === 'stbz') {
                        this.$refs.stbzCloud.setFrom(this.formData.cloud);
                    }
                    if (this.routeKey === 'yjf') {
                        this.$refs.yjf.setFrom(
                            this.formData.pricing,
                            this.formData.mysql_info,
                        );
                    }
                    this.$refs[this.routeKey].disposeLevelData(
                        this.formData.storage_auth.application_level_ids,
                    );
                }
                /* if (this.routeKey === "stbz") {
                      this.$refs.stbz.disposeLevelData(
                        this.formData.storage_auth.application_level_ids
                      );
                    }
                    if (this.routeKey === "yzh") {
                      this.$refs.yzh.disposeLevelData(
                          this.formData.storage_auth.application_level_ids
                      );

                    } */

                this.formData.update.currentPrice =
                    this.formData.update.currentPrice || 2;

                this.formData.update.costPrice =
                    this.formData.update.costPrice || 2;
                this.formData.update.category =
                    this.formData.update.category || 2;
                this.formData.update.show_source_in_my_storage =
                    this.formData.update.show_source_in_my_storage || 2;

                this.formData.update.baseInfo =
                    this.formData.update.baseInfo || 2;
                this.formData.update.imageWatermark =
                    this.formData.update.imageWatermark || 2;
                this.formData.update.createBrand =
                    this.formData.update.createBrand || 2;
                this.formData.update.activity_price =
                    this.formData.update.activity_price || 2;
                this.formData.update.manually_update_settings =
                    this.formData.update.manually_update_settings ? this.formData.update.manually_update_settings : [];
                this.formData.update.guide_price =
                    this.formData.update.guide_price || 2;
                if (this.$route.query.key === 'yzh_new') {
                    let originalPrice =
                        !value.update.originalPrice ||
                        value.update.originalPrice === 2
                            ? 0
                            : value.update.originalPrice;
                    this.$set(
                        this.formData.update,
                        'originalPrice',
                        originalPrice,
                    );
                    let guide_price = value.update.guide_price || 0;
                    this.$set(this.formData.update, 'guide_price', guide_price);
                    let activity_price = value.update.activity_price || 0;
                    this.$set(
                        this.formData.update,
                        'activity_price',
                        activity_price,
                    );
                } else {
                    this.formData.update.originalPrice =
                        this.formData.update.originalPrice || 2;
                }

                // 判断显示是否显示保密字段的验证按钮
                if (res.data.data.id > 0) {
                    this.visibleWhether = true;
                }

                // 判断阿里精选供应链手动更新设置
                if (this.$route.query.key === 'aljx' && !value.update.manually_update_settings) {
                    setTimeout(() => {
                        this.saveSetting()
                        this.$router.go(0);
                    },0);
                }
            } catch {
                if (res.code !== 0) {
                    this.visibleWhether = false;
                }
            } finally {
                let ishttps =
                    'https:' == document.location.protocol ? 'https' : 'http';
                let domain = document.domain;

                //res.data.url

                let proto = res.data.proto ? res.data.proto : 'https';

                this.domain = proto + '://' + res.data.domain;
                if (this.$route.query.key === 'yiyatong') {
                    this.callBackUrl =
                        proto +
                        '://' +
                        res.data.domain +
                        '/supplyapi/api/yyt/callBack';
                } else if (this.$route.query.key === 'aljx') {
                    this.callBackUrl =
                        proto +
                        '://' +
                        res.data.domain +
                        '/supplyapi/api/ali/callBackMessage';
                } else if (this.$route.query.key === 'selfCake') {
                    this.callBackUrl =
                        proto +
                        '://' +
                        res.data.domain +
                        '/supplyapi/api/cake/orderStatus';
                    this.priceCallBackUrl =
                        proto +
                        '://' +
                        res.data.domain +
                        '/supplyapi/api/cake/alertPrice';
                    this.saleCallBackUrl =
                        proto +
                        '://' +
                        res.data.domain +
                        '/supplyapi/api/cake/alertSale';
                } else {
                    this.callBackUrl =
                        proto +
                        '://' +
                        res.data.domain +
                        '/supplyapi/api/gatherSupply/notificationCallBack';
                }
            }

            // console.log("数据：", res.data.url)
        });
        this.getShop();
    },
    methods: {
        getCode() {
            // this.$refs.aljxCodeDialog.init()
            let domainName = location.origin;
            window.open(
                `https://auth.1688.com/oauth/authorize?client_id=${this.formData.baseInfo.appKey}&site=1688&redirect_uri=${domainName}&state=1`,
            );
        },
        openTokenDialog() {
            this.$refs.aljxTokenDialog.init(this.$route.query.id);
        },
        getToken(token) {
            console.log('token:' + token);
            this.formData.baseInfo.token = token;
        },
        handleLicenses(res) {
            // this.shopData.value.ShopStoreLicenses = res.data.file.url;
            this.shopData.value.ShopStoreLicenses.push(res.data.file.url);
        },
        handleSupplier(res) {
            this.shopData.value.shop_logo_square = res.data.file.url;
        },
        beforeAvatarUpload(file) {
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                this.$message.error('上传头像图片大小不能超过 10MB!');
            }
            return isLt10M;
        },
        // 删除封面
        delCover(index) {
            this.shopData.value.ShopStoreLicenses.splice(index, 1);
        },
        async saveShop() {
            const params = {
                id: this.tempId,
                key: this.tempKey,
                ...this.shopData,
            };
            console.log('params', params, 2);
            const res = await setSupplySetting(params);

            if (res.code === 0) {
                this.$message.success(res.msg);
            } else {
                this.$message.error(res.msg);
            }
        },
        async getShop() {
            const params = {
                gather_supply_id: parseInt(this.$route.query.id),
            };
            const res = await getSupplySetting(params);
            this.tempKey = res.data.key;
            this.tempId = res.data.id;
            if (res.code === 0) {
                this.shopData.value = res.data.value;
                if (this.shopData.value.ShopStoreLicenses === null)
                    this.shopData.value.ShopStoreLicenses = [];
            } else {
                this.$message.error(res.msg);
            }
        },
        async saveSetting() {
            if (this.routeKey) {
                this.formData.pricing = {
                    ...this.$refs[this.routeKey].formData,
                };
                if (this.routeKey === 'stbz') {
                    this.formData.cloud = { ...this.$refs.stbzCloud.formData };
                }
                if (this.routeKey === 'yjf') {
                    this.formData.mysql_info = { ...this.$refs.yjf.mysql_info };
                }
                if (this.routeKey === 'selfCake') {
                    this.formData.router_key = 'selfCake';
                }
                let storage_auth = this.$refs[this.routeKey].getLevelData();
                this.formData.storage_auth = storage_auth;
            }

            this.formData.management.products = parseInt(
                this.formData.management.products,
            );
            this.formData.management.profit = this.formData.management.profit
                ? parseInt(this.formData.management.profit)
                : 0;
            // console.log(this.formData.order_after_sales_strategy.type);
            // this.formData.order_after_sales_strategy.type = parseInt(this.formData.order_after_sales_strategy.type)
            /* if (this.routeKey === "stbz") {
              let storage_auth = this.$refs.stbz.getLevelData();
              this.formData.storage_auth = storage_auth;
            }
            if (this.routeKey === "yzh") {
              let storage_auth = this.$refs.yzh.getLevelData();
              this.formData.storage_auth = storage_auth;
            } */
            if (
                this.$route.query.key !== 'stbz' &&
                this.$route.query.key !== 'self' &&
                'sales' in this.formData.update
            ) {
                delete this.formData.update.sales;
            }
            if(this.$route.query.key === 'aljx' && 'type' in this.formData.order_after_sales_strategy){
                delete this.formData.order_after_sales_strategy.type
            }
            let data = {
                key: 'gatherSupply' + parseInt(this.$route.query.id),
                value: JSON.stringify(this.formData),
            };

            // console.log("请求数据：" + JSON.stringify(data))
            let res = await saveSetting(data);
            // console.log();
            let res1 = await updateName({
                id: Number(this.$route.query.id),
                name: this.formData.baseInfo.storeName,
            });
            // console.log("返回数据：" + JSON.stringify(res))
            if (res.code === 0 && res1.code === 0) {
                this.$message({
                    type: 'success',
                    message: '保存成功',
                });
            } else if (res.code === 0) {
                this.visibleWhether = true;
            }
        },
        // 效验
        async verify() {
            let data = {};
            data.gather_supply_id = parseInt(this.$route.query.id);

            // 判断appkey 是否存在
            if (this.formData.baseInfo.appKey) {
                data.app_key = this.formData.baseInfo.appKey;
            }

            // 判断 接口域名 是否存在
            if (this.formData.baseInfo.apiUrl) {
                data.apiUrl = this.formData.baseInfo.apiUrl;
            }

            // 判断 appSecret 是否存在
            if (this.formData.baseInfo.appSecret) {
                data.app_secret = this.formData.baseInfo.appSecret;
            }

            // 判断 host 是否存在
            if (this.formData.baseInfo.host) {
                data.host = this.formData.baseInfo.host;
            }
            let res = await validateConfig(data);

            if (res.code == 0) {
                this.$message.success(res.msg);
            }
        },
        // 初始化商品
        async initGoods() {
            let res;
            if (this.$route.query.key === 'shama') {
                res = await shamaInitGoods({
                    id: parseInt(this.$route.query.id),
                });
            } else {
                res = await guanaitongInitGoods({
                    id: parseInt(this.$route.query.id),
                });
            }
            if (res.code === 0) {
                this.$message({
                    type: 'success',
                    message: res.msg,
                });
            }
        },
        // 重置密钥
        async reset(str, index) {
            let value = '';
            if (
                !this.visible[index].value &&
                this.visible[index].value === ''
            ) {
                this.$message.error('请输入内容');
                return;
            }
            switch (str) {
                case 'appSecret':
                    this.formData.baseInfo.appSecret = this.visible[0].value;
                    value = this.visible[0].value;
                    this.visible[0].popover = false;
                    this.visible[0].value = '';
                    break;
                case 'password':
                    this.formData.baseInfo.password = this.visible[1].value;
                    value = this.visible[1].value;
                    this.visible[1].popover = false;
                    this.visible[1].value = '';
                    break;
                case 'client_id':
                    this.formData.baseInfo.client_id = this.visible[2].value;
                    value = this.visible[2].value;
                    this.visible[2].popover = false;
                    this.visible[2].value = '';
                    break;
                case 'client_secret':
                    this.formData.baseInfo.client_secret =
                        this.visible[3].value;
                    value = this.visible[3].value;
                    this.visible[3].popover = false;
                    this.visible[3].value = '';
                    break;
                case 'ticket_number':
                    this.formData.baseInfo.ticket_number =
                        this.visible[4].value;
                    value = this.visible[4].value;
                    this.visible[4].popover = false;
                    this.visible[4].value = '';
                    break;
                case 'md5秘钥':
                    this.formData.baseInfo.appSecret = this.visible[5].value;
                    value = this.visible[5].value;
                    this.visible[5].popover = false;
                    this.visible[5].value = '';
                    str = 'appSecret';
                    break;
                case 'md5秘钥1':
                    this.formData.baseInfo.appSecret = this.visible[6].value;
                    value = this.visible[6].value;
                    this.visible[6].popover = false;
                    this.visible[6].value = '';
                    str = 'appSecret';
                    break;
                case '密钥':
                    this.formData.baseInfo.appSecret = this.visible[7].value;
                    value = this.visible[7].value;
                    this.visible[7].popover = false;
                    this.visible[7].value = '';
                    str = 'appSecret';
                    break;
                case 'passWord':
                    this.formData.baseInfo.passWord = this.visible[8].value;
                    value = this.visible[8].value;
                    this.visible[8].popover = false;
                    this.visible[8].value = '';
                    break;
                case 'passWord1':
                    this.formData.baseInfo.passWord = this.visible[9].value;
                    value = this.visible[9].value;
                    this.visible[9].popover = false;
                    this.visible[9].value = '';
                    str = 'passWord';
                    break;
                case 'appsecret':
                    this.formData.baseInfo.appsecret = this.visible[10].value;
                    value = this.visible[10].value;
                    this.visible[10].popover = false;
                    this.visible[10].value = '';
                    break;
                case 'distributor_appsecret':
                    this.formData.baseInfo.distributor_appsecret = this.visible[11].value;
                    value = this.visible[11].value;
                    this.visible[11].popover = false;
                    this.visible[11].value = '';
                    break;
            }

            await updateSettingMask({
                key: 'gatherSupply' + parseInt(this.$route.query.id),
                field: str,
                value,
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.el-form {
    .el-row {
        padding: 0;
    }

    .blockRadio-box {
        margin-top: 15px;

        & :first-child {
            margin-top: 0;
        }

        ::v-deep .el-radio {
            margin: 0;

            .el-radio__label {
                display: none;
            }
        }
    }

    p.hint-p {
        font-size: 12px;
        color: #c0c4cc;
        line-height: 20px;
    }
}

p.title-p {
    font-size: 16px;
    font-weight: bold;
}

.el-divider {
    margin: 15px 0;
}

.w270 {
    width: 270px;
}

::v-deep .avatar-uploader {
    .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        width: 178px;
        height: 178px;

        .avatar-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            line-height: 178px;
            text-align: center;
        }
    }
}

.covers-uploader,
.cover-image {
    width: 178px;
    height: 178px;
}

.cover-list-box {
    width: 178px;
    height: 178px;
    margin-right: 10px;
    margin-bottom: 10px;
    position: relative;

    &:hover {
        .del-box {
            display: block;
            line-height: 178px;
        }
    }

    .del-box {
        display: none;
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        text-align: center;
        background-color: rgba(0, 0, 0, 0.3);

        .el-button.el-button--text {
            font-size: 18px;
            color: #fff;
            margin-top: 10px;
        }
    }
}

.covers-uploader {
    border: 1px dashed #d9dcdf;
    color: #ababab;
    text-align: center;

    i {
        // margin-top: 24px;
        // font-size: 40px;
        margin-top: 20px;
        font-size: 20px;
    }
}

.avatar-txt {
    margin-top: 12px;
    font-size: 12px;
}

.verify {
    display: flex;

    .verify_but {
        margin-left: 10px;
    }
}

.text-p {
    width: 70%;
    color: #f32321;
    font-size: 12px;
    line-height: 25px;
}

.verify {
    display: flex;

    .verify_but {
        margin-left: 10px;
    }
}

.popover_val {
    width: 500px;
    margin: 10px auto;
}
</style>
