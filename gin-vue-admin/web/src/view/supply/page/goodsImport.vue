<!-- 商品导入 -->
<template>
  <m-card>
    <!--    <goods-import-search ref="goodsImportSearch" @handleSearch="getGoodsList"
                             @pageReload="pageReload"></goods-import-search>-->
    <yzh ref="yzh" @handleSearch="componentsSearch" @pageReload="pageReload" v-if="$route.query.key === 'yzh'"></yzh>
    <yzhNew
      ref="yzh_new"
      @handleSearch="componentsSearch"
      @pageReload="pageReload"
      v-if="$route.query.key === 'yzh_new'"
    ></yzhNew>
    <zyhx
      ref="zyhx"
      @handleSearch="componentsSearch"
      @pageReload="pageReload"
      v-if="$route.query.key === 'zyhx'"
    ></zyhx>
    <cross
      ref="cross"
      @handleSearch="componentsSearch"
      @pageReload="pageReload"
      v-if="$route.query.key === 'cross'"
    ></cross>

    <stbz
      ref="stbz"
      @handleSearch="componentsSearch"
      @pageReload="pageReload"
      v-if="$route.query.key === 'stbz'"
    ></stbz>
    <self
      ref="self"
      @handleSearch="componentsSearch"
      @pageReload="pageReload"
      v-if="$route.query.key === 'self'"
    ></self>
    <gdself
      ref="gdself"
      @handleSearch="componentsSearch"
      @pageReload="pageReload"
      v-if="$route.query.key === 'gdself'"
    ></gdself>
    <szbao
      ref="szbao"
      @handleSearch="componentsSearch"
      @pageReload="pageReload"
      v-if="$route.query.key === 'szbao'"
    ></szbao>
    <dwd ref="dwd" @handleSearch="componentsSearch" @pageReload="pageReload" v-if="$route.query.key === 'dwd'"></dwd>
    <lianlian
      ref="lianlian"
      @handleSearch="componentsSearch"
      @pageReload="pageReload"
      v-if="$route.query.key === 'lianlian'"
    ></lianlian>
    <hehe
      ref="hehe"
      @handleSearch="componentsSearch"
      @pageReload="pageReload"
      v-if="$route.query.key === 'hehe'"
    ></hehe>
    <aljx
      ref="aljx"
      @handleSearch="componentsSearch"
      @pageReload="pageReload"
      @totalNumberOfOnlineGoods="totalNumberOfOnlineGoods"
      v-if="$route.query.key === 'aljx'"
    ></aljx>
    <jushuitan
      ref="jushuitan"
      @handleSearch="componentsSearch"
      @reLoad="getGoodsList"
      @pageReload="pageReload"
      v-if="$route.query.key === 'jushuitan'"
    ></jushuitan>
    <youxuan
      ref="youxuan"
      @handleSearch="componentsSearch"
      @pageReload="pageReload"
      v-if="$route.query.key === 'youxuan'"
    ></youxuan>
    <tianma
      ref="tianma"
      @handleSearch="componentsSearch"
      @pageReload="pageReload"
      v-if="$route.query.key === 'tianma'"
    ></tianma>
    <yiyatong 
      ref="yiyatong"
      @handleSearch="componentsSearch"
      @pageReload="pageReload"
      @syncGoodsList="getGoodsList"
      v-if="$route.query.key === 'yiyatong'"
    ></yiyatong>
    <shama
      ref="shama"
      @handleSearch="componentsSearch"
      @pageReload="pageReload"
      v-if="$route.query.key === 'shama'"
    ></shama>
    <yjf
      ref="yjf"
      @handleSearch="componentsSearch"
      @pageReload="pageReload"
      v-if="$route.query.key === 'yjf'"
    ></yjf>
    <jdvop
      ref="jd_vop"
      @handleSearch="componentsSearch"
      @reLoad="getGoodsList"
      @pageReload="pageReload"
      v-if="$route.query.key === 'jd_vop'"
    ></jdvop>
    <guanaitong
      ref="guanaitong"
      @handleSearch="componentsSearch"
      @reLoad="getGoodsList"
      @pageReload="pageReload"
      v-if="$route.query.key === 'guanaitong'"
    ></guanaitong>
    <maiger
      ref="maiger"
      @handleSearch="componentsSearch"
      @reLoad="getGoodsList"
      @pageReload="pageReload"
      v-if="$route.query.key === 'maiger'"
    ></maiger>
    <selfCake
      ref="selfCake"
      @handleSearch="componentsSearch"
      @reLoad="getGoodsList"
      @pageReload="pageReload"
      v-if="$route.query.key === 'selfCake'"
    ></selfCake>
    <kunsheng
      ref="kunsheng"
      @handleSearch="componentsSearch"
      @reLoad="getGoodsList"
      @pageReload="pageReload"
      v-if="$route.query.key === 'kunsheng'"
    ></kunsheng>
    <wdt
      ref="wdt"
      @handleSearch="componentsSearch"
      @reLoad="getGoodsList"
      @pageReload="pageReload"
      v-if="$route.query.key === 'wdt'"
    ></wdt>
    <!-- <wps
        ref="weipinshang"
        @handleSearch="componentsSearch"
        @pageReload="pageReload"
        v-if="$route.query.key === 'weipinshang'"
    ></wps> -->
    <wpsGoodsImport v-if="$route.query.key === 'weipinshang'"></wpsGoodsImport>
    <div v-else>
      <div class="mt25" v-if="$route.query.key !== 'tianma'">
        <el-button type="primary" @click="addCarlist">添加选中</el-button>
        <el-badge :value="selectCount" class="ml10">
          <el-button @click="openDialog">已选</el-button>
        </el-badge>
        <span class="ml30" v-if="sever_ratio > 0">技术服务费 {{ sever_ratio / 100 }}%</span>
        <span class="ml30" v-if="$route.query.key === 'aljx'">线上商品总数: {{ onLineGoodsTotal }}</span>
      </div>

      <!-- 表格部分 -->
      <div class="mt25 table-div">
        <!-- 表头部分 -->
        <div class="table-header">
          <div class="f fac">
            <el-checkbox
              v-if="$route.query.key !== 'tianma'"
              class="checkbox-all"
              v-model="checkAll"
              @change="handleCheckAllChange"
              :indeterminate="isIndeterminate"
              >全选
            </el-checkbox>
            <div
              class="screen-item"
              v-if="$route.query.key !== 'tianma'"
              v-for="(item, index) in screenList"
              :key="item.id"
              @click="handleSortClick(item, index)"
            >
              <span class="title-span" :class="sort_id === item.id ? 'active' : ''">{{ item.name }}</span>
              <span class="caret-wrapper">
                <i :class="icons" v-for="icons in item.class" :key="icons.id"></i>
              </span>
            </div>

            <!-- 天马的表头 -->
            <div class='tianmaHead' v-if="$route.query.key === 'tianma'">
              <el-checkbox
                class="checkbox-all"
                v-model="checkAll"
                @change="tianmaHandleCheckAllChange"
                :indeterminate="isIndeterminate"
              >全选
            </el-checkbox>
              <!-- <el-button type="primary" @click="chooseDelivery">选择快递</el-button> -->
              <el-button type="primary" @click="exportIn">导入商品</el-button>
              <el-button @click="delCheck">清除勾选</el-button>
              <div class="checkedCitiesLIN">已选商品({{checkedCities.length}})</div>
            </div>

          </div>
          <div v-if="$route.query.key !== 'tianma'">
            <el-button type="primary" @click="AllCheck()">当页全选</el-button>
            <el-button type="danger" @click="$fn.clicks(openSelectionSortDialog,5000)">导入全部筛选商品 </el-button>
          </div>
        </div>
        <!-- table-body部分 -->
        <div class="table-body">
          <el-row :gutter="20" ref="goodsBox" class="goods-box">
            <!-- goodsList -->
            <el-col :md="6" :lg="5" v-for="(item, index) in goodsList" >
              <div class="goods-item" :key="item.id">
                <div class="top-image-box">


                  <template  v-if="$route.query.key === 'tianma' || $route.query.key === 'yiyatong'">
                    <el-checkbox-group
                      v-if="item.is_import == 0"
                      v-model="checkedCities"
                      @change="handleCheckedCitiesChange"
                    >
                      <el-checkbox :label="item"></el-checkbox>
                    </el-checkbox-group>
                  </template>

                  <el-checkbox-group
                    v-else
                    v-model="checkedCities"
                    @change="handleCheckedCitiesChange"
                  >
                    <el-checkbox :label="item"></el-checkbox>
                  </el-checkbox-group>

                  <m-image :src="item.cover" class="goods-ig-box"></m-image>
                  <div class="operates-box f fac" v-if="$route.query.key === 'tianma'">
                    <div class="operates-box-is_import" v-if="item.is_import == 1">
                      <div>已导入</div>
                      <a href="javascript:;" @click="jumpHouse(item)" class="f1 text-center">查看货仓</a>
                    </div>
                    <a v-else href="javascript:;" @click="jumpHouse(item)" class="f1 text-center">查看货仓</a>
                  </div>
                  <div class="operates-box f fac" v-else>
                    <a v-if="item.third_url" target="_blank" :href="item.third_url" class="f1 text-center"
                      >查看第三方详情</a
                    >
                    <el-divider v-if="item.third_url" direction="vertical"></el-divider>
                    <a href="javascript:;" @click="pushSelectedGoods(item)" class="f1 text-center">添加</a>
                    <el-divider direction="vertical" v-if="item.is_import"></el-divider>
                    <p class="color-red f1 text-center have-import" v-if="item.is_import">已导入</p>
                  </div>
                </div>
                <!-- 怡亚通的商品信息展示 -->
                <div class="cont-box" v-if="$route.query.key === 'yiyatong'">
                  <div class="title">
                    {{ item.title }}
                  </div>
                  <p class="fgx-p"></p>

                  <el-row :gutter="10">
                    <el-col :span="8" class="title-3">
                      <p>市场价</p>
                      <p class="color-red mt5">￥{{ item.official_distri_price }}</p>
                    </el-col>
                    <el-col :span="8" class="title-3">
                      <p>分销价</p>
                      <p class="color-red mt5">￥{{ item.base_price }}</p>
                    </el-col>
                    <el-col :span="8" class="title-3">
                      <p>建议零售价</p>
                      <p class="color-red mt5">￥{{ item.suggest_price }}</p>
                    </el-col>
                  </el-row>
                 
                  <el-row :gutter="10">
                    <el-col :span="8" class="title-3">
                      <p>利润率</p>
                      <p class="color-red mt5">{{ item.rate }}%</p>
                    </el-col>
                    <el-col :span="8" class="title-3">
                      <p>推荐品牌</p>
                      <p class="color-red mt5">
                        {{ item.third_brand_name ? item.third_brand_name : '-' }}
                      </p>
                    </el-col>
                    <el-col :span="8" class="title-3">
                      <p>推荐分类</p>
                      <p class="color-red mt5 third-category-p">
                        {{ item.third_category_name ? item.third_category_name : '-' }}
                      </p>
                    </el-col>
                  </el-row>

                  <el-row :gutter="10">
                    <el-col :span="8" class="title-3">
                      <p>库存</p>
                      <p class="color-red mt5">{{ item.stock }}</p>
                    </el-col>
                  </el-row>
                </div>

                <!-- 关爱通商品信息展示 -->
                <div class="cont-box" v-else-if="$route.query.key === 'guanaitong'">
                  <div class="title">
                    {{ item.title }}
                  </div>
                  <p class="fgx-p"></p>
                  <el-row :gutter="10">
                    <el-col :span="8" class="title-3">
                      <p>协议价</p>
                      <p class="color-red mt5">￥{{ item.agreement_price | formatF2Y }}</p>
                    </el-col>
                    <el-col :span="8" class="title-3">
                      <p>销售价</p>
                      <p class="color-red mt5">￥{{ item.sale_price | formatF2Y }}</p>
                    </el-col>
                    <el-col :span="8" class="title-3">
                      <p>市场价</p>
                      <p class="color-red mt5">￥{{ item.market_price | formatF2Y }}</p>
                    </el-col>
                  </el-row>
                  <el-row :gutter="10">
                    <el-col :span="8" class="title-3">
                      <p>利润率</p>
                      <p class="color-red mt5">{{ accMulRate(item.rate) }}%</p>
                    </el-col>
                    <el-col :span="8" class="title-3">
                      <p>库存</p>
                      <p class="color-red mt5">{{ item.stock }}</p>
                    </el-col>
                    <el-col :span="8" class="title-3">
                      <p>推荐品牌</p>
                      <p class="color-red mt5">
                        {{ item.third_brand_name ? item.third_brand_name : '-' }}
                      </p>
                    </el-col>
                  </el-row>
                  <el-row :gutter="10">
                    <el-col :span="8" class="title-3">
                      <p>推荐分类</p>
                      <p class="color-red mt5 third-category-p">
                        {{ item.third_category_name ? item.third_category_name : '-' }}
                      </p>
                    </el-col>
                  </el-row>
                </div>

                <!-- 迈戈商品信息展示 -->
                <div class="cont-box" v-else-if="$route.query.key === 'maiger'">
                  <div class="title">
                    {{ item.title }}
                  </div>
                  <p class="fgx-p"></p>
                  <el-row :gutter="10">
                    <el-col :span="8" class="title-3">
                      <p>协议价</p>
                      <p class="color-red mt5">￥{{ item.agreement_price | formatF2Y }}</p>
                    </el-col>
                    <el-col :span="8" class="title-3">
                      <p>市场价</p>
                      <p class="color-red mt5">￥{{ item.market_price | formatF2Y }}</p>
                    </el-col>
                    <el-col :span="8" class="title-3">
                      <p>推荐品牌</p>
                      <p class="color-red mt5">
                        {{ item.third_brand_name ? item.third_brand_name : '-' }}
                      </p>
                    </el-col>
                  </el-row>
                  <el-row :gutter="10">
                    <el-col :span="8" class="title-3">
                      <p>推荐分类</p>
                      <p class="color-red mt5 third-category-p">
                        {{ item.third_category_name ? item.third_category_name : '-' }}
                      </p>
                    </el-col>
                  </el-row>
                </div>
                
                <!-- 蛋糕叔叔信息展示 -->
                <div class="cont-box" v-else-if="$route.query.key === 'selfCake'">
                  <div class="title">
                    {{ item.title }}
                  </div>
                  <p class="fgx-p"></p>
                  <el-row :gutter="10">
                    <el-col :span="8" class="title-3">
                      <p>售价</p>
                      <p class="color-red mt5">￥{{ item.agreement_price | formatF2Y }}</p>
                    </el-col>
                    <el-col :span="8" class="title-3">
                      <p>利润率</p>
                      <p class="color-red mt5">{{ item.rate }}%</p>
                    </el-col>
                    <el-col :span="8" class="title-3">
                      <p>市场价</p>
                      <p class="color-red mt5">￥{{ item.sale_price | formatF2Y }}</p>
                    </el-col>
                  </el-row>
                  <el-row :gutter="10">
                    <el-col :span="8" class="title-3">
                      <p>推荐品牌</p>
                      <p class="color-red mt5">
                        {{ item.third_brand_name ? item.third_brand_name : '-' }}
                      </p>
                    </el-col>
                  </el-row>
                </div>

                <!-- 坤昇健行信心展示 -->
                <div class="cont-box" v-else-if="$route.query.key === 'kunsheng'">
                  <div class="title">
                    {{ item.title }}
                  </div>
                  <p class="fgx-p"></p>
                  <el-row :gutter="10">
                    <el-col :span="8" class="title-3">
                      <p>吊牌价</p>
                      <p class="color-red mt5">￥{{ item.agreement_price | formatF2Y }}</p>
                    </el-col>
                    <el-col :span="8" class="title-3">
                      <p>利润率</p>
                      <p class="color-red mt5">{{ item.rate }}%</p>
                    </el-col>
                    <el-col :span="8" class="title-3">
                      <p>尺码</p>
                      <p class="color-red mt5">
                        {{ item.unit }}
                      </p>
                    </el-col>
                  </el-row>
                  <el-row :gutter="10">
                    <el-col :span="8" class="title-3">
                      <p>推荐品牌</p>
                      <p class="color-red mt5">
                        {{ item.third_brand_name ? item.third_brand_name : '-' }}
                      </p>
                    </el-col>
                    <el-col :span="8" class="title-3">
                      <p>推荐分类</p>
                      <p class="color-red mt5 third-category-p">
                        {{ item.third_category_name ? item.third_category_name : '-' }}
                      </p>
                    </el-col>
                    <el-col :span="8" class="title-3">
                      <p>颜色</p>
                      <p class="color-red mt5">{{ item.color }}</p>
                    </el-col>
                  </el-row>
                  <el-row :gutter="10">
                    <el-col :span="8" class="title-3">
                      <p>商品条码</p>
                      <p class="color-red mt5">{{ item.sn }}</p>
                    </el-col>
                  </el-row>
                </div>

                <!-- 其他供应链的商品信息展示 -->
                <div class="cont-box" v-else>
                  <div class="title">
                    {{ item.title }}
                  </div>
                  <p class="fgx-p"></p>
                  
                  <el-row :gutter="10">
                    <el-col :span="8" class="title-3">
                      <p v-if="$route.query.key === 'yjf'">供货价</p>
                      <p v-else>协议价</p>
                      <p class="color-red mt5" v-if="$route.query.key === 'yjf'">￥{{ item.cost_price | formatF2Y }}</p>
                      <p class="color-red mt5" v-else>￥{{ item.agreement_price | formatF2Y }}</p>
                    </el-col>
                    <el-col :span="8" class="title-3">
                      <p v-if="$route.query.key === 'tianma'">折扣</p>
                      <p v-else>利润率</p>
                      <p v-if="$route.query.key === 'tianma'" class="color-red mt5">{{ item.rate }}折</p>
                      <p v-else class="color-red mt5">{{ item.rate }}%</p>
                    </el-col>
                    <el-col :span="8" class="title-3">
                      <p v-if="$route.query.key === 'weipinshang'">吊牌价</p>
                      <p v-else-if="$route.query.key === 'tianma'">颜色</p>
                      <p v-else-if="$route.query.key === 'yjf' || $route.query.key === 'hehe'">市场价</p>
                      <p v-else>销售价</p>
                      <p v-if="$route.query.key === 'tianma'" class="color-red mt5">
                        {{ item.color }}
                      </p>
                      <p v-else-if="$route.query.key === 'yjf' || $route.query.key === 'hehe'" class="color-red mt5">
                        ￥{{ item.market_price | formatF2Y }}
                      </p>
                      <p v-else class="color-red mt5">￥{{ item.sale_price | formatF2Y }}</p>
                    </el-col>
                  </el-row>
                  <el-row :gutter="10" v-if="$route.query.key !== 'hehe'">
                    <el-col :span="8" class="title-3">
                      <template v-if="$route.query.key === 'tianma'">
                        <p>重量</p>
                        <p class="color-red mt5">
                          {{ item.weight }}
                        </p>
                      </template>
                      <template v-else-if="$route.query.key === 'yjf'">
                        <p>第三方平台销售价格</p>
                        <p class="color-red mt5">
                          ￥{{ item.sale_price | formatF2Y }}
                        </p>
                      </template>
                      <template v-else>
                        <p>营销价</p>
                        <p class="color-red mt5">￥{{ item.activity_price | formatF2Y }}</p>
                      </template>
                    </el-col>
                    <el-col :span="8" class="title-3" v-if="$route.query.key === 'weipinshang' || $route.query.key === 'yjf'">
                      <p>库存</p>
                      <p class="color-red mt5">{{ item.stock }}</p>
                    </el-col>
                    <el-col :span="8" class="title-3" v-else-if="$route.query.key === 'shama'">
                      <p>市场价</p>
                      <p class="color-red mt5">￥{{ item.market_price | formatF2Y }}</p>
                    </el-col>
                    <el-col :span="8" class="title-3" v-else>
                      <p>营销利润率</p>
                      <p class="color-red mt5">{{ item.activity_rate }}%</p>
                    </el-col>
                    <el-col :span="8" class="title-3">
                      <p v-if="$route.query.key === 'weipinshang'">建议零售价</p>
                      <p v-else-if="$route.query.key === 'tianma'">尺码</p>
                      <p v-else-if="$route.query.key === 'yjf'">中付平台销售价格</p>
                      <p v-else>指导价</p>
                      <p v-if="$route.query.key === 'tianma'" class="color-red mt5">
                        {{ item.unit }}
                      </p>
                      <p v-else-if="$route.query.key === 'yjf'" class="color-red mt5">
                        ￥{{ item.guide_price | formatF2Y}}
                      </p>
                      <p v-else class="color-red mt5">￥{{ item.guide_price | formatF2Y }}</p>
                    </el-col>
                  </el-row>
                  <el-row :gutter="10">
                    <el-col :span="8" class="title-3" v-if="$route.query.key !== 'shama' && $route.query.key !== 'yjf' && $route.query.key !== 'hehe'">
                      <p>市场价</p>
                      <p class="color-red mt5">￥{{ item.market_price | formatF2Y }}</p>
                    </el-col>
                    <el-col :span="8" class="title-3" v-if="$route.query.key !== 'jushuitan' && $route.query.key !== 'yjf' && $route.query.key !== 'wdt'">
                      <p>推荐品牌</p>
                      <p class="color-red mt5">
                        {{ item.third_brand_name ? item.third_brand_name : '-' }}
                      </p>
                    </el-col>
                    <el-col :span="8" class="title-3" v-if="$route.query.key !== 'jushuitan' && $route.query.key !== 'yjf' && $route.query.key !== 'wdt'">
                      <p>推荐分类</p>
                      <p class="color-red mt5 third-category-p">
                        {{ item.third_category_name ? item.third_category_name : '-' }}
                      </p>
                    </el-col>
                    <el-col :span="8" class="title-3" v-if="$route.query.key === 'jushuitan' || $route.query.key === 'wdt'">
                      <p>店铺</p>
                      <p class="color-red mt5">{{ item.shop_name }}</p>
                    </el-col>
                  </el-row>
                  <el-row :gutter="10">
                    <el-col
                      :span="8"
                      class="title-3"
                      v-if="
                        $route.query.key !== 'weipinshang' &&
                        $route.query.key !== 'jushuitan' &&
                        $route.query.key !== 'tianma' &&
                        $route.query.key !== 'shama' &&
                        $route.query.key !== 'yjf' &&
                        $route.query.key !== 'jd_vop' && 
                        $route.query.key !== 'hehe' && 
                        $route.query.key !== 'wdt'
                      "
                    >
                      <p>销量</p>
                      <p class="color-red mt5">
                        {{ item.sale ? item.sale : 0 }}
                      </p>
                    </el-col>
                    <el-col v-if="$route.query.key === 'aljx'" :span="8" class="title-3">
                      <!-- <p>销量</p> -->
                      <p class="color-red mt5">
                        {{ item.WangWangAccount ? item.WangWangAccount : '' }}
                      </p>
                    </el-col>
                    <el-col
                      :span="8"
                      class="title-3"
                      v-if="$route.query.key === 'youxuan' || $route.query.key === 'tianma' || $route.query.key === 'guanaitong'"
                    >
                      <p>库存</p>
                      <p class="color-red mt5">{{ item.stock }}</p>
                    </el-col>
                      <el-col
                              :span="8"
                              class="title-3"
                              v-if="$route.query.key === 'szbao'"
                      >
                          <p>含运价</p>
                          <p class="color-red mt5">￥{{ item.freight_price | formatF2Y }}</p>
                      </el-col>
                  </el-row>
                </div>

              </div>
            </el-col>
          </el-row>
        </div>
        <!-- 分页部分 -->
        <div
          v-if="
            $route.query.key === 'stbz' ||
            $route.query.key === 'yzh' ||
            $route.query.key === 'yzh_new' ||
            $route.query.key === 'self' || 
            $route.query.key === 'gdself' || 
            $route.query.key === 'guanaitong' ||
            this.$route.query.key === 'selfCake'
          "
          :style="{
            display: 'flex',
            justifyContent: 'flex-end',
            alignItems: 'center',
          }"
        >
          <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :style="{ float: 'right', padding: '20px' }"
            :total="total > 1200 ? 1200 : total"
            layout="sizes,prev, pager, next, jumper"
            :page-sizes="[20, 50, 100]"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
          ></el-pagination>
          <span style="display: inline-block; margin-left: 5px; font-size: 13px" class="el-pagination__total"
            >共 {{ total }} 条</span
          >
        </div>
        <el-pagination
          v-else
          background
          :current-page="page"
          :page-size="pageSize"
          :style="{ float: 'right', padding: '20px' }"
          :total="total"
          :page-sizes="[20, 50, 100]"
          layout="sizes,prev, pager, next, jumper"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        ></el-pagination>
        <GoodsImportDialog ref="goodsImportDialog" @reset="loadCheckData"></GoodsImportDialog>
        <SelectionSortDialog ref="selectionSortDialog"></SelectionSortDialog>
      </div>
    </div>

    <!-- 天马的导入框 -->
    <el-dialog title="导入" :visible.sync="showDialog" width="50%">
      <el-form :model="tianma_searchForm" label-width="120px">
        <el-row :gutter="10">
          <el-col :span="24">
            <p class="tip">如不选择分类，则自动匹配当前系统存在的分类，如果分类不存在则自动创建</p>
          </el-col>
          <el-col :span="24">
            <el-form-item label="选择导入分类:">
              <el-col :span="7">
                <el-select
                  @change="handleClassiyfChange(2, tianma_searchForm.category1_id)"
                  v-model="tianma_searchForm.category1_id"
                  filterable
                  clearable
                  placeholder="请选择一级分类"
                  class="w100"
                >
                  <el-option v-for="item in categoryList1" :key="item.id" :label="item.name" :value="item.id">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="7">
                <el-select
                  @change="handleClassiyfChange(3, tianma_searchForm.category2_id)"
                  v-model="tianma_searchForm.category2_id"
                  filterable
                  clearable
                  placeholder="请选择二级分类"
                  class="w100"
                >
                  <el-option v-for="item in categoryList2" :key="item.id" :label="item.name" :value="item.id">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="7">
                <el-select
                  v-model="tianma_searchForm.category3_id"
                  filterable
                  clearable
                  placeholder="请选择三级分类"
                  class="w100"
                >
                  <el-option v-for="item in categoryList3" :key="item.id" :label="item.name" :value="item.id">
                  </el-option>
                </el-select>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="已选货仓:">
              {{ wareHouseName }}
              <!-- <span v-for="item in chooseRows" :key="item.wareHouseName"> {{ item.wareHouseName }} </span> -->
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="快递:">
              <el-button type="text" @click="chooseDelivery">选择快递</el-button>
              <br />
              <el-tag v-for="item in tags" :key="item.dilivery_name" closable type="info" @close="delTag">
                {{ item.dilivery_name }}
              </el-tag>
            </el-form-item>
          </el-col>
          <el-col :offset="10" :span="12">
            <el-button type="primary" @click="confirmExport">导入</el-button>
            <el-button @click="cancelExpotr">取消</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
    <!-- 选择快递 -->
    <el-drawer title="选择快递" :visible.sync="drawer" :direction="direction" size="70%">
      <el-button class="btn" type="primary" @click="chooseDeliveryOk">确定</el-button>
      <el-table :data="deliveryData" class="table" ref="deliveryTable" @selection-change="chooseDeliveryTable">
        <el-table-column type="selection" align="center" width="80px"></el-table-column>
        <el-table-column label="快递名称" prop="dilivery_name" align="center" width="220px"></el-table-column>
        <el-table-column label="首重/首件" prop="first_postage" align="center" width="220px"></el-table-column>
        <el-table-column label="续重/续件" prop="additional_postage" align="center" width="220px"></el-table-column>
      </el-table>
    </el-drawer>
  </m-card>
</template>

<script>
import GoodsImportDialog from './components/goodsImportDialog.vue'
import { getCategoryChild, getGoodsList, getGroup } from '@/api/gatherSupply'
import { getDeliveryInfo } from '@/api/tianma'
import { getClassifySelectIsPlugin } from '@/api/classify'
import { importSelectGoods } from '@/api/gatherSupply'
import SelectionSortDialog from './components/selectionSortDialog.vue'
import GoodsImportSearch from './components/goodsImportSearch'
import copyTemplate from './searchComponents/copyTemplate'
import yzh from './searchComponents/yzh'
import yzhNew from './searchComponents/yzh_new'
import zyhx from './searchComponents/zyhx'
import cross from './searchComponents/cross'
import stbz from './searchComponents/stbz'
import self from './searchComponents/self'
import gdself from './searchComponents/gdself'
import szbao from './searchComponents/szbao'
import dwd from './searchComponents/dwd'
import lianlian from './searchComponents/lianlian'
import zhLang from 'element-ui/lib/locale/lang/zh-CN'
import hehe from './searchComponents/hehe'
import aljx from './searchComponents/aljx'
import jushuitan from './searchComponents/jushuitan'
// import wps from "./searchComponents/weipinshang.vue"
import youxuan from './searchComponents/youxuan.vue'
import wpsGoodsImport from './weipinshangGoodsImport.vue'
import tianma from './searchComponents/tianma.vue'
import yiyatong from './searchComponents/yiyatong.vue'
import shama from './searchComponents/shama.vue'
import yjf from './searchComponents/yjf.vue'
import jdvop from './searchComponents/jd_vop.vue'
import guanaitong from './searchComponents/guanaitong.vue'
import maiger from './searchComponents/maiger.vue'
import selfCake from './searchComponents/selfCake.vue'
import kunsheng from './searchComponents/kunsheng.vue'
import wdt from './searchComponents/wdt'
// 商品导入
export default {
  name: 'SupplyGoodsImport',
  components: {
    GoodsImportDialog,
    SelectionSortDialog,
    GoodsImportSearch,
    copyTemplate,
    yzh,
    yzhNew,
    stbz,
    self,
    gdself,
    szbao,
    cross,
    dwd,
    lianlian,
    hehe,
    zyhx,
    aljx,
    jushuitan,
    youxuan,
    tianma,
    yiyatong,
    // wps,
    wpsGoodsImport,
    shama,
    yjf,
    jdvop,
    guanaitong,
    maiger,
    selfCake,
    kunsheng,
    wdt
  },
  data() {
    return {
      onLineGoodsTotal: 0,
      sever_ratio: 0,
      selectCount: 0,
      goods_index: null,
      // 商品选中的数据
      checkedCities: [],
      selectGoods: [],
      category: [],
      // category1: {},
      // category2: {},
      // category3: {},
      sort_id: null,
      type: '',
      sort: '',
      // 区分 el-icon-bottom * 接口不用管
      sort_type: 1,
      // 全选样式
      isIndeterminate: false,
      checkAll: false,
      searchForm: {},
      page: 1,
      pageSize: 20,
      total: 0,
      goodsList: [],
      sectionType: [
        {
          name: '利润区间',
          type: 'profits',
        },
        {
          name: '协议价',
          type: 'agreement_price',
        },
        {
          name: '指导价',
          type: 'guide_price',
        },
        {
          name: '营销价',
          type: 'activity_price',
        },
        {
          name: '常规利润率',
          type: 'promotion_rate',
        },
        {
          name: '营销利润率',
          type: 'activity_rate',
        },
      ],
      groupList: [],
      screenList: [
        {
          id: 1,
          name: '最新上架',
          type: 'created_at',
          class: ['el-icon-bottom'],
        },
        {
          id: 3,
          name: '协议价',
          type: 'agreement_price',
          class: ['caret-top sort-icon', 'caret-bottom sort-icon'],
        },
        {
          id: 4,
          name: '指导价',
          type: 'guide_price',
          class: ['caret-top sort-icon', 'caret-bottom sort-icon'],
        },
        {
          id: 5,
          name: '营销价',
          type: 'activity_price',
          class: ['caret-top sort-icon', 'caret-bottom sort-icon'],
        },
        {
          id: 6,
          name: '常规利润率',
          type: 'origin_rate',
          class: ['caret-top sort-icon', 'caret-bottom sort-icon'],
        },
        {
          id: 7,
          name: '营销利润率',
          type: 'activity_rate',
          class: ['caret-top sort-icon', 'caret-bottom sort-icon'],
        },
        {
          id: 8,
          name: '销量',
          type: 'sales',
          class: ['caret-top sort-icon', 'caret-bottom sort-icon'],
        },
      ],

      // 天马的弹出框属性
      drawer:false,
      direction:"rtl",
      deliveryData: [], // 快递列表
      showDialog:false,
      categoryList1: [],
      categoryList2: [],
      categoryList3: [],
      wareHouseName: '', // 货仓名称
      tags: [], // 标签
      tianma_searchForm: {
        category1_id: '',
        category2_id: '',
        category3_id: '',
      }

    }
  },
  mounted() {
    // this.getCategory(1, 0)
    this.fetch()
    // 怡亚通排序
    if (this.$route.query.key === 'yiyatong') {
      this.yiyatongScreenListFun()
    }
    // 迈戈供应链 
    if (this.$route.query.key === 'maiger') {
      this.maigerScreenListFun()
    }
    if (this.$route.query.key !== 'weipinshang') {
      this.getGoodsList()
    }

    if (this.$route.query.key === 'stbz') {
      this.getGroup()
    }
    localStorage.removeItem('cartlist')

    if (this.$route.query.key === 'stbz') {
      // 获取类目
      this.handleClassiyfChange()
    }
  },
  methods: {
    // 怡亚通排序内容
    yiyatongScreenListFun(){
      console.log(111);
      this.screenList = [
        {
          id: 1,
          name: '最新上架',
          type: 'created_at',
          class: ['el-icon-bottom'],
        },
        {
          id: 3,
          name: '市场价',
          type: 'agreement_price',
          class: ['caret-top sort-icon', 'caret-bottom sort-icon'],
        },
        {
          id: 4,
          name: '分销价',
          type: 'guide_price',
          class: ['caret-top sort-icon', 'caret-bottom sort-icon'],
        },
        {
          id: 5,
          name: '建议零售价',
          type: 'activity_price',
          class: ['caret-top sort-icon', 'caret-bottom sort-icon'],
        },
        {
          id: 6,
          name: '常规利润率',
          type: 'promotion_rate',
          class: ['caret-top sort-icon', 'caret-bottom sort-icon'],
        },
        {
          id: 7,
          name: '库存',
          type: 'sales',
          class: ['caret-top sort-icon', 'caret-bottom sort-icon'],
        },
      ]
    },
    // 迈戈排序内容
    maigerScreenListFun() {
      this.screenList = [
        {
          id: 1,
          name: '最新上架',
          type: 'created_at',
          class: ['el-icon-bottom'],
        },
        {
          id: 3,
          name: '协议价',
          type: 'agreement_price',
          class: ['caret-top sort-icon', 'caret-bottom sort-icon'],
        },
        {
          id: 4,
          name: '市场价',
          type: 'market_price',
          class: ['caret-top sort-icon', 'caret-bottom sort-icon'],
        },
      ]
    },
    // 跳转货仓
    jumpHouse(item) {
      if(this.$route.query.key === 'tianma' ){
        if(this.$refs[this.$route.query.key].searchForm.ware_house_name != ''){
          this.$_blank("/layout/SupplyIndex/tianmaFreightHouse",{
          gather_supply_id: this.$route.query.id, 
          sn: item.sn,
          key: this.$route.query.key,
          cargo: this.$refs[this.$route.query.key].searchForm.ware_house_name
          })
        }else{
          this.$message('请选择货仓')
        }
        
      }else{
        this.$_blank("/layout/SupplyIndex/tianmaFreightHouse",{
          gather_supply_id: this.$route.query.id, sn: item.sn, key: this.$route.query.key
        })
      }
      
      
      /*this.$router.push({
        name: 'tianmaFreightHouse',
        query: { gather_supply_id: this.$route.query.id, sn: item.sn, key: this.$route.query.key },
      })*/
      localStorage.setItem('cargoShop', JSON.stringify(item))
      // localStorage.setItem('diliveryKey', JSON.stringify(this.$route.query.key))
    },
    pageReload() {
      this.page = 1;
    },
    // 获取选中的商品
    loadCheckData() {
      /* if (localStorage.getItem("cartlist")) {
        let cartList = JSON.parse(localStorage.getItem("cartlist"));
        this.selectCount = cartList.length;
        this.checkedCities = cartList;
      } else {
        this.selectCount = 0;
        this.checkedCities = [];
      } */
      localStorage.removeItem('cartlist')
      this.selectCount = 0
      this.checkedCities = []
      this.checkAll = false
      this.isIndeterminate = false
    },
    // 添加
    pushSelectedGoods(item) {
      if (item.is_import !== 1) {
        if (localStorage.getItem('cartlist')) {
          let cartList = JSON.parse(localStorage.getItem('cartlist'))
          let obj = {}
          obj = cartList.find(v => {
            return v.id === item.id
          })
          if (!obj) {
            cartList.push(item)
            this.checkedCities.push(item)
            localStorage.setItem('cartlist', JSON.stringify(cartList))
            this.selectCount = cartList.length
            this.$message.success('添加成功')
            /* this.$nextTick(() => {
              this.$refs.goodsImportDialog.updateCard();
            }); */
          } else {
            this.$message.error('此商品已添加')
          }
        } else {
          let cartList = []
          cartList.push(item)
          this.checkedCities.push(item)
          localStorage.setItem('cartlist', JSON.stringify(cartList))
          this.selectCount = cartList.length
          this.$message.success('添加成功')
        }
        // this.checkedCities.push(item)
      } else {
        this.$message.error('不能添加已导入商品')
      }
    },
    AllCheck() {
      localStorage.setItem('cartlist', JSON.stringify(this.goodsList))

      this.selectCount = this.goodsList.length
      this.$nextTick(() => {
        this.$refs.goodsImportDialog.updateCard()
      })
      this.openDialog()
    },
    // 打开导入商品选择分类、标签
    openSelectionSortDialog() {
      this.$refs[this.$route.query.key].getGoodsList()
      let searchForm = {}
      searchForm = this.$refs[this.$route.query.key].newSearchForm
      if(this.$route.query.key === "tianma" && !searchForm.ware_house_name){
          searchForm.ware_house_name = this.$refs[this.$route.query.key].searchForm.ware_house_name
      }
      searchForm.page = parseInt(this.page)
      /*if(this.$route.query.key === "tianma") {
          this.pageSize = 50
      }*/
      searchForm.limit = parseInt(this.pageSize)
      searchForm.type = this.type
      searchForm.sort = this.sort
      searchForm.key = this.$route.query.key || ''
      searchForm.gather_supply_id = parseInt(this.$route.query.id) || 0
      this.$refs.selectionSortDialog.goodSearchWhere = searchForm
      this.$refs.selectionSortDialog.handleClassiyfChange(1, 0);
      this.$refs.selectionSortDialog.isShow = true
      this.$refs.selectionSortDialog.exportTotal = this.total
      this.$refs.selectionSortDialog.getBrandsOptios()
    },
    // 添加至选中
    addCarlist() {
      // console.log("当前购物车" + this.selectGoods);
      localStorage.setItem('cartlist', JSON.stringify(this.selectGoods))

      this.selectCount = this.selectGoods.length
      this.$nextTick(() => {
        this.$refs.goodsImportDialog.updateCard()
      })
      // this.$refs.goodsImportDialog.updateCard();
    },
    // 全选
    handleCheckAllChange(val) {
      this.checkedCities = val ? this.goodsList : []
      this.isIndeterminate = false
      
      if (this.$route.query.key === "yiyatong") {
        this.selectGoods = this.goodsList.filter(item => !item.is_import)
      }else{
        this.selectGoods = this.goodsList
      }
      console.log(this.selectGoods,1111);
    },
    tianmaCheckAllChange(val){
      this.checkedCities = val ? this.goodsList : []
      this.isIndeterminate = false
      this.selectGoods = this.goodsList
    },
    // 多选
    handleCheckedCitiesChange(value) {
      console.log(value);
      let checkedCount = value.length

      this.selectGoods = value

      // console.log("当前选择数据：", value);
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.goodsList.length
    },
    async getGroup() {
      let params = {
        gather_supply_id: parseInt(this.$route.query.id),
        key: this.$route.query.key,
      }
      let res = await getGroup(params)
      if (res.code === 0) {
        this.groupList = res.data
      }
    },

    async getCategory(level, pid) {
      switch (level) {
        case 1:
          pid = 0
          break
        case 2:
          pid = this.searchForm.fl1
          break
        case 3:
          pid = this.searchForm.fl2
          break
      }
      let searchForm = {
        pid: pid,
        source: parseInt(this.searchForm.terrace),
        gather_supply_id: parseInt(this.$route.query.id),
        key: this.$route.query.key,
      }
      let list = await getCategoryChild(searchForm)

      if (list.code === 0) {
        switch (level) {
          case 1:
            this.category1 = list.data
            break
          case 2:
            this.category2 = list.data
            break
          case 3:
            this.category3 = list.data
            break
        }
      }
    },
    totalNumberOfOnlineGoods(total) {
      this.onLineGoodsTotal = total || 0
    },
    componentsSearch(searchForm = {}) {
      this.page = 1
      if(this.$route.query.key == 'tianma'){
        this.checkAll = false
        this.isIndeterminate = false
      }
      this.getGoodsList()
    },
    async getGoodsList() {
      console.log('33334', this.$route.query.key)
      let searchForm = {}
      searchForm = this.$refs[this.$route.query.key].newSearchForm
        if(this.$route.query.key === "tianma" && !searchForm.ware_house_name){
            searchForm.ware_house_name = this.$refs[this.$route.query.key].searchForm.ware_house_name
        }
        searchForm.page = parseInt(this.page)
        /*if(this.$route.query.key === "tianma") {
          this.pageSize = 50
        }*/
      searchForm.limit = parseInt(this.pageSize)
      searchForm.type = this.type
      searchForm.sort = this.sort
      // let   category1 = this.category1.find((item)=>{
      //    return item.id === this.searchForm.fl1;
      //    //筛选出匹配数据，是对应数据的整个对象
      //  });
      // let   category2 = this.category2.find((item)=>{
      //    return item.id === this.searchForm.fl2;
      //    //筛选出匹配数据，是对应数据的整个对象
      //  });
      // let   category3 = this.category3.find((item)=>{
      //    return item.id === this.searchForm.fl3;
      //    //筛选出匹配数据，是对应数据的整个对象
      //  });
      //
      //  searchForm.categorys = category1.title+","+category2.title+","+category3.title;
        searchForm.key = this.$route.query.key || ''
      searchForm.gather_supply_id = parseInt(this.$route.query.id) || 0
        let is_import = ''
      if (this.$route.query.key === 'yzh_new' || this.$route.query.key === 'zyhx' || this.$route.query.key === 'aljx' || this.$route.query.key === 'yiyatong') {
        if (parseInt(this.$refs[this.$route.query.key].is_import) === 1) {
          searchForm.is_import = 2
        } else if (parseInt(this.$refs[this.$route.query.key].is_import) === 2) {
          searchForm.is_import = 1
        } else {
          delete searchForm.is_import
        }
      } else {
        is_import = this.$refs[this.$route.query.key].is_import ? this.$refs[this.$route.query.key].is_import : '0'
      }
      console.log('aoteman');
      let list = await getGoodsList(searchForm)
      if (list.code === 0) {
        let resList = list.data.list
        let newList = []
        if (
          this.$route.query.key === 'yzh_new' ||
          this.$route.query.key === 'aljx' ||
          this.$route.query.key === 'dwd' ||
          this.$route.query.key === 'self' ||
          this.$route.query.key === 'gdself' ||
          this.$route.query.key === 'zyhx' ||
          this.$route.query.key === 'jushuitan' ||
          this.$route.query.key === 'youxuan' ||
          this.$route.query.key === 'yiyatong' ||
          this.$route.query.key === 'jd_vop' ||
          this.$route.query.key === 'guanaitong' ||
          this.$route.query.key === 'selfCake' ||
          this.$route.query.key === 'wdt'
        ) {
          newList = resList
        } else {
          switch (is_import) {
            case '0': // 全部
              newList = resList
              break
            case '1': // 已导入
              resList.forEach(item => {
                if (item.is_import && item.is_import === 1) {
                  newList.push({ ...item })
                }
              })
              break
            case '2': // 未导入
              resList.forEach(item => {
                if (!item.is_import || item.is_import !== 1) {
                  newList.push({ ...item })
                }
              })
              break
          }
        }
        this.goodsList = newList
        this.sever_ratio = list.data.sever_ratio
        this.total = list.data.total
        zhLang.el.pagination.total = `共 {total} ${this.total / this.pageSize === 100 ? '+' : ''} 条`
        if(this.$route.query.key === 'tianma' && this.goodsList.length > 0){
          let s = false
          let newGoodsList = this.goodsList.filter(item => item.is_import != 1)
          if(newGoodsList.length > 0){
            for (let i of newGoodsList) {
              let arr = this.checkedCities.find(item => item.sn + item.unit == i.sn + i.unit )
              if (arr == undefined) {
                s = true
                break
              }
            }
          }else{
            s = true
          }
          if (s) {
            this.checkAll = false
            this.isIndeterminate = false
          }else{
            this.checkAll = true
            this.isIndeterminate = false
          }
          
          let arr = []
          // 处理回显选择的数据
          for(let i of this.goodsList){
            let obj = this.checkedCities.find(item => item.unit + item.sn == i.unit + i.sn)
            if(obj != undefined){
              arr.push(obj)
            }else{
              arr.push(i)
            }
          }
          this.goodsList = arr
        }
      }
    },
    // 重置序列条件
    reserSort() {
      if(this.$route.query.key === 'yiyatong'){
        this.yiyatongScreenListFun()
        return
      }
      if (this.$route.query.key === 'maiger') {
        this.maigerScreenListFun()
        return
      }
      this.screenList = [
        {
          id: 1,
          name: '最新上架',
          type: 'created_time',
          class: ['el-icon-bottom'],
        },
        {
          id: 3,
          name: '协议价',
          type: 'agreement_price',
          class: ['caret-top sort-icon', 'caret-bottom sort-icon'],
        },
        {
          id: 4,
          name: '指导价',
          type: 'guide_price',
          class: ['caret-top sort-icon', 'caret-bottom sort-icon'],
        },
        {
          id: 5,
          name: '营销价',
          type: 'activity_price',
          class: ['caret-top sort-icon', 'caret-bottom sort-icon'],
        },
        {
          id: 6,
          name: '常规利润率',
          type: 'promotion_rate',
          class: ['caret-top sort-icon', 'caret-bottom sort-icon'],
        },
        {
          id: 7,
          name: '营销利润率',
          type: 'activity_rate',
          class: ['caret-top sort-icon', 'caret-bottom sort-icon'],
        },
        {
          id: 8,
          name: '销量',
          type: 'sales',
          class: ['caret-top sort-icon', 'caret-bottom sort-icon'],
        },
      ]
    },
    // 排序按钮
    handleSortClick(item, index) {
      this.reserSort()
      if (item.class.length === 1 && item.class[0] === 'el-icon-bottom') {
        this.sort_type = 1
        this.screenList[index].class[0] = this.screenList[index].class[0] + ' active'
        this.sort = 'desc' 
      } else if (item.class.length === 2) {
        if (this.sort_type === 1) {
          this.sort_type = 2
          this.sort = ''
        } else {
          this.sort_type = 1
          this.sort = 'desc'
        }
        switch (this.sort) {
          case 'desc':
            this.screenList[index].class[0] = this.screenList[index].class[0] + ' active'
            this.sort = 'asc'
            break
          case 'asc':
            this.screenList[index].class[1] = this.screenList[index].class[1] + ' active'
            this.sort = 'desc'
            break
          default:
            this.screenList[index].class[1] = this.screenList[index].class[1] + ' active'
            this.sort = 'desc'
            break
        }
      }
      this.sort_id = item.id
      this.type = item.type

      this.getGoodsList()
    },
    // 打开已选dialog
    openDialog() {
      this.$refs.goodsImportDialog.isShow = true
      this.$nextTick(() => {
        this.$refs.goodsImportDialog.fetch()
        this.$refs.goodsImportDialog.updateCard()
      })
    },
    // 重置
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    handleCurrentChange(page) {
      this.page = page
      this.getGoodsList()
      this.fetch()
    },
    handleSizeChange(size) {
      this.pageSize = size
      this.getGoodsList()
      this.fetch()
    },
    fetch() {
      /* window.onresize = () => {
        this.$refs.goodsBox.$children.forEach((item) => {
          item.$children[1].$el.style.width = item.$el.offsetWidth - 20 + "px";
          item.$children[1].$el.style.height = item.$el.offsetWidth - 20 + "px";
        });
      };
      this.$refs.goodsBox.$children.forEach((item) => {
        item.$children[1].$el.style.width = item.$el.offsetWidth - 20 + "px";
        item.$children[1].$el.style.height = item.$el.offsetWidth - 20 + "px";
      }); */
    },
    async chooseDelivery() {
      this.drawer = true
      const data = {
        ware_house_name: this.$refs[this.$route.query.key].searchForm.ware_house_name,
        gather_supply_id: parseInt(this.$route.query.id),
      }
      const res = await getDeliveryInfo(data)
      if (res.code === 0) {
        this.deliveryData = res.data.rows
      }
      this.chooseDeliveryList = []
    },
    chooseDeliveryOk() {
      this.drawer = false
      this.tags = this.chooseDeliveryList
    },
    // 选中的快递
    chooseDeliveryTable(val) {
      // 选中大于一就清楚所有选项
      if (val.length > 1) {
        this.$refs.deliveryTable.clearSelection()
        this.$refs.deliveryTable.toggleRowSelection(val.pop())
      }
      this.chooseDeliveryList = val
    },
    // 导入按钮
    exportIn() {
      this.wareHouseName = this.$refs[this.$route.query.key].searchForm.ware_house_name
      if(this.wareHouseName){
        if(this.checkedCities.length > 0){
          this.showDialog = true
        }else{
          this.$message('至少选择一个商品')
        }
        
      }else{
        this.$message('请选择货仓')
      }
      
    },
    // 取消导入
    cancelExpotr() {
      this.showDialog = false
      this.checkedCities = []
      this.tags = []
    },
    // 获取类目
    handleClassiyfChange(level = 1, pid = 0) {
      getClassifySelectIsPlugin(level, pid).then(r => {
        if (level === 3) {
          this.categoryList3 = []
          this.tianma_searchForm.category3_id = ''
        } else {
          this.categoryList2 = []
          this.tianma_searchForm.category2_id = ''
          this.categoryList3 = []
          this.tianma_searchForm.category3_id = ''
        }
        switch (level) {
          case 1:
            this.categoryList1 = r.data.list
            break
          case 2:
            this.categoryList2 = r.data.list
            break
          case 3:
            this.categoryList3 = r.data.list
            break
        }
      })
    },
    // 删除选中的标签
    delTag() {
      this.tags = []
      this.$message.success('删除成功')
    },
    tianmaHandleCheckAllChange(val){
      let newGoodsList = this.goodsList.filter(item => item.is_import != 1)
      if(val){
        for (let i of newGoodsList) {
          let arr = this.checkedCities.find(item => item.sn + item.unit == i.sn + i.unit )
          if(arr == undefined){
            this.checkedCities.push(i)
          }
        }
      }else{
        for (let i of newGoodsList) {
          let arr = this.checkedCities.find(item => item.sn + item.unit == i.sn + i.unit )
          if(arr != undefined){
            this.checkedCities = this.checkedCities.filter(item => item.sn + item.unit != i.sn + i.unit)
          }
        }
      }
    },
    delCheck(){
      this.checkAll = false
      this.isIndeterminate = false
      this.checkedCities = []
    },
     // 确认导入
    async confirmExport() {
      if (this.tags.length < 1) {
        this.$message.error('请选择快递')
      } else {
        var categorys = ''

        categorys = 
        this.tianma_searchForm.category1_id + ',' 
        + this.tianma_searchForm.category2_id + ',' 
        + this.tianma_searchForm.category3_id
        for (let i of this.checkedCities) {
          i.express_delivery = this.tags[0].dilivery_name
        }
        let jsonData = {
          shop_name: this.wareHouseName,
          list: this.checkedCities,
          categorys: categorys,
          key: this.$route.query.key,
          gather_supply_id: parseInt(this.$route.query.id),
        }
        const res = await importSelectGoods(jsonData)
        if (res.code === 0) {
          this.$message.success('操作成功,等待后台操作')
          this.cancelExpotr()
          this.getGoodsList()
          
        } else {
          this.$messag.error(res.msg)
        }
      }
    },
    // 关爱通*100取证
    accMulRate(arr) {
      return parseInt(arr * 100)
    },
  },
}
</script>
<style lang="scss" scoped>
@import './goodsImportStyle/goodsImport.scss';

::v-deep .el-table__row {
  height: 80px;
}
::v-deep .el-table__header-wrapper .el-checkbox {
  display: none;
}
.price {
  color: rgba(0, 0, 0, 0.4);
  text-decoration: line-through;
}
.tip {
  color: #dc5d5d;
  margin-left: 40px;
  margin-bottom: 20px;
}
.table {
  margin-left: 100px;
  margin-top: 60px;
}
.btn {
  position: fixed;
  top: 80px;
}

.tianmaHead{
  display:flex;
  align-items: center;

  .checkedCitiesLIN{
    margin-left: 12px;
  }
}


.operates-box-is_import{
  width: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;

  div{
    width: 50%;
    height: 28px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-right: 1px solid #fff;
    color: #ff0000;
    box-sizing: border-box;
  }
}

</style>
