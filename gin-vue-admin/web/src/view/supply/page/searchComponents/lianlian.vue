<template>

  <el-form
      :model="searchForm"
      ref="searchForm"
      label-width="90px"
      class="search-term"
      inline
  >
      <!--      <el-col :xl="8" :lg="12">-->
      <!--        <el-form-item label="平台:" prop="terrace">-->
      <!--          <el-select-->
      <!--              v-model="searchForm.terrace"-->
      <!--              class="w100"-->
      <!--              @change="getCategory(1, 0)"-->
      <!--          >-->
      <!--            &lt;!&ndash;              <el-option value="" label="全部"></el-option>&ndash;&gt;-->
      <!--            <el-option value="1" label="云仓"></el-option>-->
      <!--            <el-option value="2" label="京东"></el-option>-->
      <!--            <el-option value="6" label="阿里"></el-option>-->
      <!--            <el-option value="7" label="天猫"></el-option>-->
      <!--            <el-option value="8" label="苏宁"></el-option>-->
      <!--          </el-select>-->
      <!--        </el-form-item>-->
      <!--      </el-col>-->
      <!--      <el-col :xl="8" :lg="12">-->
      <!--        <el-form-item label="是否包邮:" prop="isPostage">-->
      <!--          <el-select v-model="searchForm.isPostage" class="w100">-->
      <!--            <el-option value="" label="不限"></el-option>-->
      <!--            <el-option value="1" label="包邮"></el-option>-->
      <!--            <el-option value="0" label="不包邮"></el-option>-->
      <!--          </el-select>-->
      <!--        </el-form-item>-->
      <!--      </el-col>-->
      <!--      <el-col :xl="8" :lg="12" v-if="$route.query.key !== 'self'">-->
      <!--        <el-form-item label="营销活动:" prop="marketingCampaign">-->
      <!--          <el-select v-model="searchForm.marketingCampaign" class="w100">-->
      <!--            <el-option-->
      <!--                v-for="item in groupList"-->
      <!--                :key="item.id"-->
      <!--                :label="item.name"-->
      <!--                :value="item.id"-->
      <!--            >-->
      <!--            </el-option>-->
      <!--          </el-select>-->
      <!--        </el-form-item>-->
      <!--      </el-col>-->
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >一级分类</span>
              </div>
              <el-select
                  v-model="searchForm.fl1"
                  filterable
                  class="w100"
                  @change="operationCategory(2,searchForm.fl1)"
              >
                <el-option
                    v-for="item in category1"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                >
                </el-option>
              </el-select>
          </div>
      </el-form-item>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >二级分类</span>
              </div>
              <el-select
                  v-model="searchForm.fl2"
                  filterable
                  class="w100"
                  @change="operationCategory(3,searchForm.fl2)"
              >
                <el-option
                    v-for="item in category2"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                >
                </el-option>
              </el-select>
          </div>
      </el-form-item>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >三级分类</span>
              </div>
              <el-select v-model="searchForm.fl3" filterable class="w100">
                <el-option
                    v-for="item in category3"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                  >
                  </el-option>
                </el-select>
          </div>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input">
            <div class="line-box ">
                <span v-if="searchForm.sectionType === 'promotion_rate'">利润百分比:</span>
                <span v-else-if="searchForm.sectionType === 'gross_profit_rate'">利率百分比:</span>
                <span v-else>区间价格:</span>
            </div>
            <div class="f fac">
                <el-form-item prop="minPrice" class="f1">
                    <el-input
                      v-model="searchForm.range_from"
                      placeholder="请输入"
                  ></el-input>
                </el-form-item>
                <span class="zih-span mr_10">至</span>
                <el-form-item prop="maxPrice" class="f1">
                    <el-input
                      v-model="searchForm.range_to"
                      placeholder="请输入"
                  ></el-input>
                </el-form-item>
            </div>
        </div>
      </el-form-item>
      <el-form-item>
          <el-input placeholder="商品名称" v-model="searchForm.goodsName" class="line-input" clearable>
              <span slot="prepend">商品</span>
          </el-input>
      </el-form-item>

<!--      <el-col :xl="8" :lg="12">-->
<!--        <el-form-item label="区间类型:" prop="sectionType">-->
<!--          <el-select v-model="searchForm.sectionType" class="w100">-->
<!--            <el-option-->
<!--                v-for="item in sectionType"-->
<!--                :key="item.id"-->
<!--                :label="item.name"-->
<!--                :value="item.type"-->
<!--            >-->
<!--            </el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
<!--      </el-col>-->

<!--      <el-col :xl="8" :lg="12">-->
<!--        <el-form-item label="是否已导入:">-->
<!--          <el-select v-model="is_import" class="w100">-->
<!--            <el-option label="全部" value="0"></el-option>-->
<!--            <el-option label="已导入" value="1"></el-option>-->
<!--            <el-option label="未导入" value="2"></el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
<!--      </el-col>-->
    <br>
    <el-form-item>
      <el-button type="primary" @click="getGoodsList()">查询</el-button>
      <el-button type="text" @click="resetForm('searchForm')"
      >重置搜索条件
      </el-button>
      <el-button type="primary" @click="synchronizationGoodsList()"
      >同步拉取供应链商品
      </el-button>
      <el-button type="primary" @click="upDetail()"
      >批量更新所有详情
      </el-button>
      <el-button type="primary" @click="upNullDetail()"
      >批量更新空详情
      </el-button>
    </el-form-item>
  </el-form>


</template>

<script>
import {getGoodsList, getGroup, syncGoodsList} from "@/api/gatherSupply";
import {getCategory} from "@/api/lianlian"
export default {
  name: "goodsImportSearch",
  data() {
    return {

      categoryList: [],
      // 是否已导入
      is_import: "0",
      newSearchForm: {},
      keyName: "",
      searchForm: {
        fl1: "",
        fl2: "",
        fl3: "",
      },
      category1: [],
      category2: [],
      category3: [],
      groupList: [],
      sectionType: [

        {
          name: "协议价",
          type: "agreement_price",
        },
        {
          name: "指导价",
          type: "guide_price",
        },
        {
          name: "利润率",
          type: "promotion_rate",
        },
        {
          name: "折扣",
          type: "discount",
        },
        {
          name: "毛利率",
          type: "gross_profit_rate",
        },
      ],
    }
  },
  mounted() {
    this.keyName = this.$route.query.key
    this.getCategory()
    if (this.$route.query.key === 'stbz') {
      this.getGroup();
    }
  },
  methods: {
    upNullDetail(){
      this.$emit('upNullload')
    },
    upDetail(){
      this.$emit('upload')
    },
    async getGroup() {
      let params = {
        gather_supply_id: parseInt(this.$route.query.id),
        key: this.$route.query.key
      }
      let res = await getGroup(params);
      if (res.code === 0) {
        this.groupList = res.data;
      }
    },


    async synchronizationGoodsList() {

      let data = {
        gather_supply_id: parseInt(this.$route.query.id),
        key: "lianlian",
      }

      let info = await syncGoodsList(data);

      if (info.code === 0) {
        this.$message.success("后台正在同步,预计5-10分钟完成,请稍后刷新页面查看");
        console.log(info)
      }


    },

    async getGoodsList() {


      let searchForm = {}
      searchForm.source = parseInt(this.searchForm.terrace);
      searchForm.category_str_id = this.searchForm.fl3.toString();
      searchForm.is_free_shipping = parseInt(this.searchForm.isPostage);
      searchForm.shop_words = this.searchForm.store;
      searchForm.category_str_id = this.searchForm.fl3.toString();
      searchForm.search_words = this.searchForm.goodsName;
      searchForm.range_from = parseInt(this.searchForm.range_from);
      searchForm.range_to = parseInt(this.searchForm.range_to);
      searchForm.range_type = this.searchForm.sectionType;
      searchForm.group_id = this.searchForm.marketingCampaign;
      searchForm.search_words = this.searchForm.goodsName;
      if (this.searchForm.fl1) {
        searchForm.category_str_id = this.searchForm.fl1.toString();
        let category1 = this.category1.find((item) => {
          return item.id === this.searchForm.fl1;
          //筛选出匹配数据，是对应数据的整个对象
        });
        searchForm.categorys = category1.name
      }
      if (this.searchForm.fl2) {
        searchForm.category_str_id = this.searchForm.fl2.toString();
        let category2 = this.category2.find((item) => {
          return item.id === this.searchForm.fl2;
          //筛选出匹配数据，是对应数据的整个对象
        });
        searchForm.categorys += "," + category2.name
      }
      if (this.searchForm.fl3) {
        searchForm.category_str_id = this.searchForm.fl3.toString();
        let category3 = this.category3.find((item) => {
          return item.id === this.searchForm.fl3;
          //筛选出匹配数据，是对应数据的整个对象
        });
        searchForm.categorys += "," + category3.name;
      }
      this.newSearchForm = searchForm;
      this.$emit("handleSearch", searchForm)
    },

    // 重置
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.$emit("pageReload")
      this.getGoodsList()
    },
   operationCategory(level = 1, pid = 0) {
      switch (level) {
        case 1:
          this.category1 = []
          break;
        case 2:
          this.category2 = []
          this.searchForm.fl2 = ""
          this.category3 = []
          this.searchForm.fl3 = ""
          break;
        case 3:
          this.category3 = []
          this.searchForm.fl3 = ""
          break;
      }
      this.categoryList.forEach(item => {
        if (item.level === level && item.parentId === pid) {
          this[`category${level}`].push({
            ...item
          })
        }
      })
    },
    async getCategory() {
      let searchForm = {
        gather_supply_id: parseInt(this.$route.query.id),
        key: this.$route.query.key
      };
      let {data} = await getCategory(searchForm);
      this.categoryList =JSON.parse(data)
      this.operationCategory()
      /*switch (level) {
        case 1:
          pid = 0;
          break;
        case 2:
          pid = this.searchForm.fl1;
          this.category2 = [];
          this.category3 = [];
          this.searchForm.fl2 = "";
          this.searchForm.fl3 = "";
          break;
        case 3:
          pid = this.searchForm.fl2;
          this.category3 = [];
          this.searchForm.fl3 = "";
          break;
      }

      let searchForm = {
        pid: pid,
        source: parseInt(this.searchForm.terrace),
        gather_supply_id: parseInt(this.$route.query.id),
        key: this.$route.query.key
      };
      let list = await getCategoryChild(searchForm);

      if (list.code === 0) {
        switch (level) {
          case 1:
            this.category1 = list.data;
            break;
          case 2:
            this.category2 = list.data;
            break;
          case 3:
            this.category3 = list.data;
            break;
        }
      }*/
    },
  }
}
</script>

<style scoped>

</style>