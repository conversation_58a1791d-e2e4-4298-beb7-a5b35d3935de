<template>
  <el-form
           :model="searchForm"
           ref="searchForm"
           label-width="80px"
           class="search-term"
           inline
  >
    <el-form-item prop="goodsName">
      <el-input placeholder="商品名称" v-model="searchForm.goodsName" class="line-input" clearable>
          <span slot="prepend">商品</span>
      </el-input>
    </el-form-item>
    <el-form-item prop="fl1">
        <div class="line-input" >
            <div class="line-box ">
                <span >一级分类</span>
            </div>
            <el-select
              v-model="searchForm.fl1"
              filterable
              class="w100"
              @change="getCategory(2, 0)"
          >
            <el-option
                v-for="item in category1"
                :key="item.id"
                :label="item.name"
                :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
    </el-form-item>
    <el-form-item prop="fl2">
        <div class="line-input" >
            <div class="line-box ">
                <span >二级分类</span>
            </div>
            <el-select
              v-model="searchForm.fl2"
              filterable
              class="w100"
              @change="getCategory(3, 0)"
          >
            <el-option
                v-for="item in category2"
                :key="item.id"
                :label="item.name"
                :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
    </el-form-item>
    <el-form-item prop="fl3">
        <div class="line-input" >
            <div class="line-box ">
                <span >三级分类</span>
            </div>
            <el-select
              v-model="searchForm.fl3"
              filterable
              class="w100"
          >
            <el-option
                v-for="item in category3"
                :key="item.id"
                :label="item.name"
                :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
    </el-form-item>
    <el-form-item prop="isPostage">
        <div class="line-input" >
            <div class="line-box ">
                <span >是否包邮</span>
            </div>
            <el-select v-model="searchForm.isPostage" class="w100">
              <el-option value="" label="不限"></el-option>
              <el-option value="1" label="包邮"></el-option>
              <el-option value="2" label="不包邮"></el-option>
            </el-select>
        </div>
    </el-form-item>
    <el-form-item prop="isDisplay">
        <div class="line-input" >
            <div class="line-box ">
                <span >状态</span>
            </div>
            <el-select v-model="searchForm.isDisplay" class="w100">
              <el-option value="" label="不限"></el-option>
              <el-option value="1" label="上架"></el-option>
              <el-option value="0" label="下架"></el-option>
            </el-select>
        </div>
    </el-form-item>
    <el-form-item prop="is_import">
        <div class="line-input" >
            <div class="line-box ">
                <span >是否已导入</span>
            </div>
            <el-select v-model="searchForm.is_import" class="w100">
              <el-option label="全部" value="0"></el-option>
              <el-option label="已导入" value="1"></el-option>
              <el-option label="未导入" value="2"></el-option>
            </el-select>
        </div>
    </el-form-item>
    <el-form-item prop="sectionType">
        <div class="line-input" >
            <div class="line-box ">
                <span >区间类型</span>
            </div>
            <el-select v-model="searchForm.sectionType" class="w100">
              <el-option
                  v-for="item in sectionType"
                  :key="item.id"
                  :label="item.name"
                  :value="item.type"
              >
              </el-option>
            </el-select>
        </div>
    </el-form-item>
    <el-form-item label="">
      <div class="line-input">
          <div class="line-box ">
              <span >区间价格:</span>
          </div>
          <div class="f fac">
              <el-form-item prop="minPrice" class="f1">
                  <el-input
                      v-model="searchForm.minPrice"
                      placeholder="请输入"
                  ></el-input>
              </el-form-item>
              <span class="zih-span mr_10">至</span>
              <el-form-item prop="maxPrice" class="f1">
                  <el-input
                      v-model="searchForm.maxPrice"
                      placeholder="请输入"
                  ></el-input>
              </el-form-item>
          </div>
      </div>
    </el-form-item>
    <el-form-item prop="is_free_tax" >
      <div class="line-input" >
          <div class="line-box ">
              <span >是否含税</span>
          </div>
          <el-select v-model="searchForm.is_free_tax" class="w100">
            <el-option label="含税" value="1"></el-option>
            <el-option label="不含税" value="2"></el-option>
          </el-select>
      </div>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="getGoodsList()">查询</el-button>
      <el-button type="text" @click="resetForm('searchForm')"
      >重置搜索条件
      </el-button>
        <el-button type="primary" @click="updatesGoodsPrice()">批量更新商品价格</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import {getCategoryChild, updateGoodsMd5Empty} from "@/api/gatherSupply";
import {confirm} from "@/decorators/decorators";

export default {
  name: "goodsImportSearch",
  data() {
    return {
      newSearchForm:{},
      keyName :"",
      searchForm: {
        fl1: "",
        fl2: "",
        fl3: "",
      },
      category1: {},
      category2: {},
      category3: {},
      groupList: [],
      sectionType: [

        {
          name: "协议价",
          type: "agreement_price",
        },
        {
          name: "指导价",
          type: "guide_price",
        }
      ],
    }
  },
  mounted() {
    this.keyName=this.$route.query.key
    this.getCategory(1, 0)

  },
  methods: {
      @confirm("提示","确定批量更新商品价格?")
      updatesGoodsPrice(){
          updateGoodsMd5Empty({id:parseInt(this.$route.query.id)}).then(res=>{
              if(res.code === 0){
                  this.$message.success(res.msg)
              }
          })
      },

    async getGoodsList() {

      let searchForm = {}
      // searchForm.category_id = parseInt(this.searchForm.fl3);
      searchForm.search_words = this.searchForm.goodsName;
      searchForm.range_from = parseInt(this.searchForm.minPrice);
      searchForm.range_to = parseInt(this.searchForm.maxPrice);
      searchForm.range_type = this.searchForm.sectionType;
      searchForm.is_free_shipping = parseInt(this.searchForm.isPostage);
      searchForm.is_display = parseInt(this.searchForm.isDisplay);
      searchForm.is_import = parseInt(this.searchForm.is_import);
      searchForm.is_free_tax = parseInt(this.searchForm.is_free_tax);
      searchForm.category1_id = parseInt(this.searchForm.fl1)
      searchForm.category2_id = parseInt(this.searchForm.fl2)
      searchForm.category3_id = parseInt(this.searchForm.fl3)
      // if (this.searchForm.fl1) {
      //   searchForm.category_id = parseInt(this.searchForm.fl1);
      //   let category1 = this.category1.find((item) => {
      //     return item.id === this.searchForm.fl1;
      //     //筛选出匹配数据，是对应数据的整个对象
      //   });
      //   searchForm.categorys = category1.name
      //   searchForm.category1_id = category1.id
      // }
      // if (this.searchForm.fl2) {
      //   searchForm.category_id = parseInt(this.searchForm.fl2);
      //   let category2 = this.category2.find((item) => {
      //     return item.id === this.searchForm.fl2;
      //     //筛选出匹配数据，是对应数据的整个对象
      //   });
      //   searchForm.categorys += "," + category2.name
      //   searchForm.category2_id = category2.id
      // }
      // if (this.searchForm.fl3) {
      //   searchForm.category_id = parseInt(this.searchForm.fl3);
      //   let category3 = this.category3.find((item) => {
      //     return item.id === this.searchForm.fl3;
      //     //筛选出匹配数据，是对应数据的整个对象
      //   });
      //   searchForm.categorys += "," + category3.name
      //   searchForm.category3_id = category3.id
      // }
      
      this.newSearchForm = searchForm;
      this.$emit("handleSearch", searchForm)
    },

    // 重置
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.searchForm = {
        fl1: "",
        fl2: "",
        fl3: ""
      }
      this.$emit("pageReload")
    },
    async getCategory(level, pid) {
      switch (level) {
        case 1:
          pid = 0;
          break;
        case 2:
          pid = this.searchForm.fl1;
          this.category2 = [];
          this.searchForm.fl2 = "";
          break;
        case 3:
          pid = this.searchForm.fl2;
          this.category3 = [];
          this.searchForm.fl3 = "";
          break;
      }

      let searchForm = {
        pid: pid,
        source: parseInt(this.searchForm.terrace),
        gather_supply_id: parseInt(this.$route.query.id),
        key: this.$route.query.key
      };
      let list = await getCategoryChild(searchForm);

      if (list.code === 0) {
        switch (level) {
          case 1:
            this.category1 = list.data;
            break;
          case 2:
            this.category2 = list.data;
            break;
          case 3:
            this.category3 = list.data;
            break;
        }
      }
    },
  }
}
</script>

<style scoped>

</style>