<template>
    <div class="search-term">
        <el-form :model="searchForm" ref="searchForm" inline>
            <el-form-item label="品牌:">
                <el-select v-model="searchForm.brand_names" clearable>
                    <el-option v-for="item in brandOptions" :key="item.id" :label="item" :value="item"></el-option>
                </el-select>
                <!--                <el-input v-model="searchForm.brand_names" placeholder="请输入"></el-input>-->
            </el-form-item>
            <el-form-item label="分类:">
                <el-select v-model="searchForm.divisions" clearable>
                    <el-option label="鞋" value="鞋"></el-option>
                    <el-option label="服" value="服"></el-option>
                    <el-option label="配" value="配"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="性别:">
                <el-select v-model="searchForm.sexs" clearable>
                    <el-option label="男" value="男"></el-option>
                    <el-option label="女" value="女"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="季节:">
                <el-input v-model="searchForm.quarters"></el-input>
            </el-form-item>
            <el-form-item label="价格:">
                <el-select v-model="searchForm.marketV" clearable>
                    <el-option
                        v-for="item in marketPriceOptions"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="折扣:">
                <el-select
                    v-model="searchForm.discountV"
                    clearable
                    v-if="searchForm.discountV !== 5"
                >
                    <el-option
                        v-for="item in discountOptions"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                    ></el-option>
                </el-select>
                <div v-if="searchForm.discountV === 5" class="f">
                    <m-num-input
                        v-model="searchForm.minDiscount"
                        style="width: 150px"
                        :min="0"
                    ></m-num-input>
                    <div class="line">-</div>
                    <m-num-input
                        v-model="searchForm.maxDiscount"
                        style="width: 170px"
                        :min="0"
                        endText="%"
                    ></m-num-input>
                </div>
            </el-form-item>
          <el-form-item label="货号:">
            <el-input v-model="searchForm.articleno"></el-input>
          </el-form-item>
            <el-form-item label="货仓:">
                <el-select v-model="searchForm.ware_house_name" clearable>
                    <!--                    <el-option v-for="item in wareHouseNameList" :key="item.wareHouseName" :label="item.wareHouseName" :value="item.wareHouseName" ></el-option>-->
                    <el-option
                        label="天马连云港仓"
                        value="天马连云港仓"
                    ></el-option>
                    <el-option
                        label="天马淮安仓"
                        value="天马淮安仓"
                    ></el-option>
                    <el-option
                        label="广东李宁大仓"
                        value="广东李宁大仓"
                    ></el-option>
                    <el-option
                        label="李宁WWE仓"
                        value="李宁WWE仓"
                    ></el-option>
                </el-select>
                <p class="color-red">需要先选货仓，再加入其他条件进行筛选</p>
                <!-- <el-input v-model="searchForm.ware_house_name"></el-input> -->
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="search">搜索</el-button>
                <el-button type="text" @click="reSearch"
                    >重置搜索条件</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
import { getWarehouseDetail, getWareHouseNameInfo } from '@/api/tianma';

export default {
    data() {
        return {
            brandOptions:[],
            marketPriceOptions: [
                { label: '0-200', value: 1 },
                { label: '200-500', value: 2 },
                { label: '500-1000', value: 3 },
                { label: '1000元以上', value: 4 },
            ],
            discountOptions: [
                { label: '0-3折', value: 1 },
                { label: '3-5折', value: 2 },
                { label: '5-8折', value: 3 },
                { label: '8折及以上', value: 4 },
                { label: '自定义区间', value: 5 },
            ],
            searchForm: {
                brand_names: '', // 品牌
                divisions: '', // 分类
                quarters: '', // 季节
                sexs: '', // 性别
                articleno: '', // 货号
                marketV: '',
                discountV: '',
                maxMarketPrice: '', // 最高价
                minMarketPrice: '', // 最低价
                maxDiscount: '', // 最高折扣
                minDiscount: '', // 最低折扣
                ware_house_name: '天马连云港仓', // 货仓名称
            },
            newSearchForm: {},
            wareHouseNameList: [], // 货仓名称列表
        };
    },
    mounted() {
        this.getWareHouseNameInfo();
        this.getBrand()
    },
    methods: {
        // 获取品牌
        async getBrand() {
            const {code,data} = await getWarehouseDetail({gather_supply_id: parseInt(this.$route.query.id),ware_house_name:this.searchForm.ware_house_name});
            if(code === 0){
                this.brandOptions = data.rows.length ? data.rows[0].brands.split(",") : []
            }
        },
        // 获取发货仓
        async getWareHouseNameInfo() {
            //   获取供应链ID
            const url = window.location.href;
            const id = url.split('&')[0].split('=')[1];
            const data = {
                gather_supply_id: parseInt(id),
            };
            const res = await getWareHouseNameInfo(data);
            this.wareHouseNameList = res.data.rows;
        },
        search() {
            this.getBrand()
            let searchForm = {
                brand_names: this.searchForm.brand_names,
                divisions: this.searchForm.divisions,
                quarters: this.searchForm.quarters,
                sexs: this.searchForm.sexs,
                ware_house_name: this.searchForm.ware_house_name,
                articleno: this.searchForm.articleno,

            };
            if (this.searchForm.marketV) {
                const rate = [
                    { min: 0, max: 200 },
                    { min: 200, max: 500 },
                    { min: 500, max: 1000 },
                    { min: 1000, max: 99999 },
                ];
                let marketObj =
                    rate[
                        this.marketPriceOptions.indexOfJSON(
                            'value',
                            this.searchForm.marketV,
                        )
                    ];
                searchForm.maxMarketPrice = marketObj.max.toString();
                searchForm.minMarketPrice = marketObj.min.toString();
            }
            if (this.searchForm.discountV) {
                if (this.searchForm.discountV === 5) {
                    searchForm.maxDiscount =
                        this.searchForm.maxDiscount.toString();
                    searchForm.minDiscount =
                        this.searchForm.minDiscount.toString();
                } else {
                    const rate = [
                        { min: 0, max: 3 },
                        { min: 3, max: 5 },
                        { min: 5, max: 8 },
                        { min: 8, max: 99 },
                    ];
                    let discountVObj =
                        rate[
                            this.discountOptions.indexOfJSON(
                                'value',
                                this.searchForm.discountV,
                            )
                        ];
                    searchForm.maxDiscount = discountVObj.max.toString();
                    searchForm.minDiscount = discountVObj.min.toString();
                }
            }
            this.newSearchForm = searchForm;
            this.$emit('handleSearch', searchForm);
        },
        reSearch() {
            this.searchForm = {
                brand_names: '', // 品牌
                divisions: '', // 分类
                quarters: '', // 季节
                sexs: '', // 性别
                marketV: '',
                discountV: '',
                maxMarketPrice: '', // 最高价
                minMarketPrice: '', // 最低价
                maxDiscount: '', // 最高折扣
                minDiscount: '', // 最低折扣
                ware_house_name: '', // 货仓名称\
            };
            this.$emit('pageReload');
        },
    },
};
</script>
<style scoped lang="scss">
.line {
    font-size: 25px;
    padding: 8px 12px;
    color: #555;
    line-height: 16px;
}
</style>