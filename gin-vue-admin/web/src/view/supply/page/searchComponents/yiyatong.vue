<template>
    <el-form :model="searchForm" ref="searchForm" class="search-term" inline>
        <el-form-item label="" prop="fl1">
            <div class="line-input">
                <div class="line-box">
                    <span>一级分类</span>
                </div>
                <el-select
                    v-model="searchForm.fl1"
                    filterable
                    class="w100"
                    @change="operationCategory(2, searchForm.fl1)"
                >
                    <el-option
                        v-for="item in category1"
                        v-if="item.name !== ''"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="" prop="fl2">
            <div class="line-input">
                <div class="line-box">
                    <span>二级分类</span>
                </div>
                <el-select
                    v-model="searchForm.fl2"
                    filterable
                    class="w100"
                    @change="operationCategory(3, searchForm.fl2)"
                >
                    <el-option
                        v-for="item in category2"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="" prop="fl3">
            <div class="line-input">
                <div class="line-box">
                    <span>三级分类</span>
                </div>
                <el-select v-model="searchForm.fl3" filterable class="w100">
                    <el-option
                        v-for="item in category3"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="" prop="sectionType">
            <div class="line-input">
                <div class="line-box">
                    <span>利润率</span>
                </div>
                <div class="f fac">
                    <el-input v-model="searchForm.promotionRate.from" placeholder="最低价"></el-input>
                    <span class="zih-span mr_10">至</span>
                    <el-input v-model="searchForm.promotionRate.to" placeholder="最高价"></el-input>
                </div>
            </div>
        </el-form-item>
        <el-form-item label="" prop="sectionType">
            <el-form-item label="">
            <div class="line-input">
                <div class="line-box ">
                    <span >市场价</span>
                </div>
                <div class="f fac">
                    <el-input v-model="searchForm.officialDistriPrice.from" placeholder="最低价"></el-input>
                    <span class="zih-span mr_10">至</span>
                    <el-input v-model="searchForm.officialDistriPrice.to" placeholder="最高价"></el-input>
                </div>
            </div>
        </el-form-item>
        </el-form-item>
        <el-form-item label="" prop="sectionType">
            <div class="line-input">
                <div class="line-box">
                    <span>分销价</span>
                </div>
                <div class="f fac">
                    <el-input v-model="searchForm.basePrice.from" placeholder="最低价"></el-input>
                    <span class="zih-span mr_10">至</span>
                    <el-input v-model="searchForm.basePrice.to" placeholder="最高价"></el-input>
                </div>
            </div>
        </el-form-item>
        <el-form-item label="" prop="sectionType">
            <div class="line-input">
                <div class="line-box">
                    <span>建议零售价</span>
                </div>
                <div class="f fac">
                    <el-input v-model="searchForm.suggestPrice.from" placeholder="最低价"></el-input>
                    <span class="zih-span mr_10">至</span>
                    <el-input v-model="searchForm.suggestPrice.to" placeholder="最高价"></el-input>
                </div>
            </div>
        </el-form-item>
        <el-form-item label="" prop="goodsName">
            <div class="line-input">
                <div class="line-box">
                    <span>商品</span>
                </div>
                <el-input
                    placeholder="商品名称"
                    v-model="searchForm.goodsName"
                ></el-input>
            </div>
        </el-form-item>
        <el-form-item label="">
            <div class="line-input">
                <div class="line-box">
                    <span>是否已导入</span>
                </div>
                <el-select v-model="is_import" class="w100">
                    <el-option label="全部" value="0"></el-option>
                    <el-option label="已导入" value="1"></el-option>
                    <el-option label="未导入" value="2"></el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="" prop="is_sort">
            <div class="line-input">
                <div class="line-box">
                    <span>排序</span>
                </div>
                <el-select v-model="searchForm.is_sort" class="w100">
                    <el-option
                        v-for="item in is_sort"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" @click="getGoodsList()">查询</el-button>
            <el-button type="primary" @click="updateAllProduct">
                批量更新价格
            </el-button>
            <el-button @click="syncGoodsListFun()"> 同步拉取商品 </el-button>
            <el-button type="text" @click="resetForm('searchForm')">
                重置搜索条件
            </el-button>
        </el-form-item>
    </el-form>
</template>

<script>
import {
    getCategoryChild,
    getGoodsList,
    getGroup,
    syncGoodsList,
    updateAllYytProduct,
} from '@/api/gatherSupply';

export default {
    name: 'goodsImportSearch',
    data() {
        return {
            categoryList: [],
            // 是否已导入
            is_import: '0',
            newSearchForm: {},
            keyName: '',
            searchForm: {
                fl1: '',
                fl2: '',
                fl3: '',
                promotionRate: {
                    from: null,
                    to: null,
                },
                officialDistriPrice: {
                    from: null,
                    to: null,
                },
                basePrice: {
                    from: null,
                    to: null,
                },
                suggestPrice: {
                    from: null,
                    to: null,
                }
            },
            category1: [],
            category2: [],
            category3: [],
            groupList: [],
            // 利润率
            profitRateId: '',
            sectionType: [],
            is_sort: [
                {
                    label: '正序',
                    value: 'desc',
                },
                {
                    label: '倒序',
                    value: 'asc',
                },
            ], // 排序
            // 价格区间
            marketPriceOptions: [
                { label: '0-200', value: 1 },
                { label: '200-500', value: 2 },
                { label: '500-1000', value: 3 },
                { label: '1000元以上', value: 4 },
            ],
        };
    },
    mounted() {
        this.keyName = this.$route.query.key;
        this.operationCategory();
    },
    methods: {
        changeSelect(id) {
            this.profitRateId = id;
        },

        async getGoodsList() {
            let searchForm = {};
            searchForm.source = parseInt(this.searchForm.terrace);
            searchForm.category_str_id = this.searchForm.fl3.toString();
            searchForm.is_free_shipping = parseInt(this.searchForm.isPostage);
            searchForm.shop_words = this.searchForm.store;
            searchForm.category_str_id = this.searchForm.fl3.toString();
            searchForm.search_words = this.searchForm.goodsName;
            searchForm.range_from = parseInt(this.searchForm.range_from);
            searchForm.range_to = parseInt(this.searchForm.range_to);
            searchForm.range_type = this.searchForm.sectionType;
            searchForm.group_id = this.searchForm.marketingCampaign;
            searchForm.search_words = this.searchForm.goodsName;
            searchForm.is_sort = this.searchForm.is_sort;
            if (this.searchForm.fl1) {
                searchForm.category_str_id = this.searchForm.fl1.toString();
                let category1 = this.category1.find((item) => {
                    return item.id === this.searchForm.fl1;
                    //筛选出匹配数据，是对应数据的整个对象
                });
                console.log(category1);
                searchForm.category_level = '1';
                searchForm.category_str_id = category1.id;
            }
            if (this.searchForm.fl2) {
                searchForm.category_str_id = this.searchForm.fl2.toString();
                let category2 = this.category2.find((item) => {
                    return item.id === this.searchForm.fl2;
                    //筛选出匹配数据，是对应数据的整个对象
                });
                searchForm.category_level = '2';
                searchForm.category_str_id = category2.id;
            }
            if (this.searchForm.fl3) {
                searchForm.category_str_id = this.searchForm.fl3.toString();
                let category3 = this.category3.find((item) => {
                    return item.id === this.searchForm.fl3;
                    //筛选出匹配数据，是对应数据的整个对象
                });
                // searchForm.category3_id = category3.id
                searchForm.category_level = '3';
                searchForm.category_str_id = category3.id;
            }

            const rate = [
                { from: 0, to: 200 },
                { from: 200, to: 500 },
                { from: 500, to: 1000 },
                { from: 1000, to: 99999 },
            ];
            // 利润率
            if (this.searchForm.promotionRate) {
                searchForm.promotion_rate = {
                    from: parseInt(this.searchForm.promotionRate.from),
                    to: parseInt(this.searchForm.promotionRate.to)
                }
            }
            // 市场价
            if (this.searchForm.officialDistriPrice) {
                // searchForm.official_distri_price =
                //     rate[this.searchForm.officialDistriPrice - 1];
                searchForm.official_distri_price = {
                    from: parseInt(this.searchForm.officialDistriPrice.from),
                    to: parseInt(this.searchForm.officialDistriPrice.to)
                }
                
            }
            // 分销价
            if (this.searchForm.basePrice) {
                // searchForm.base_price = rate[this.searchForm.basePrice - 1];
                searchForm.base_price = {
                    from: parseInt(this.searchForm.basePrice.from),
                    to: parseInt(this.searchForm.basePrice.to),
                }
            }
            // 建议零售价
            if (this.searchForm.suggestPrice) {
                // searchForm.suggest_price =
                //     rate[this.searchForm.suggestPrice - 1];
                searchForm.suggest_price = {
                    from: parseInt(this.searchForm.suggestPrice.from),
                    to: parseInt(this.searchForm.suggestPrice.to),
                }
            }

            this.newSearchForm = searchForm;
            console.log(searchForm.is_sort, 'px', searchForm);
            this.$emit('handleSearch', searchForm);
        },

        // 重置
        resetForm(formName) {
            this.$refs[formName].resetFields();
            this.$emit('pageReload');
            this.is_import = '0';
            this.searchForm.promotionRate.from = null;
            this.searchForm.promotionRate.to = null;
            this.searchForm.officialDistriPrice.from = null;
            this.searchForm.officialDistriPrice.to = null;
            this.searchForm.basePrice.from = null;
            this.searchForm.basePrice.to = null;
            this.searchForm.suggestPrice.from = null;
            this.searchForm.suggestPrice.to = null;
        },
        async operationCategory(level = 1, pid = '') {
            switch (level) {
                case 1:
                    this.category1 = [];
                    break;
                case 2:
                    this.category2 = [];
                    this.searchForm.fl2 = '';
                    this.category3 = [];
                    this.searchForm.fl3 = '';
                    break;
                case 3:
                    this.category3 = [];
                    this.searchForm.fl3 = '';
                    break;
            }
            let params = {
                gather_supply_id: parseInt(this.$route.query.id),
                key: this.$route.query.key,
            };
            if (pid) {
                // params.pid = parseInt(pid);
                params.str_id = pid;
                // console.log(params.pid);
            }
            let { data } = await getCategoryChild(params);
            console.log(data);
            data.data.forEach((item) => {
                if (item.level === level) {
                    this[`category${level}`].push({
                        ...item,
                    });
                }
            });
        },
        async getCategory() {
            let searchForm = {
                gather_supply_id: parseInt(this.$route.query.id),
                key: this.$route.query.key,
            };
            let { data } = await getCategoryChild(searchForm);
            this.categoryList = data;
            this.operationCategory();
        },
        // 同步拉取商品
        async syncGoodsListFun() {
            let params = {
                gather_supply_id: parseInt(this.$route.query.id),
                key: this.$route.query.key,
            };
            let res = await syncGoodsList(params);
            console.log(res);
            if (res.code == 0) {
                this.$message.success(res.msg);
                this.$emit('syncGoodsList');
            }
        },
        // 批量更新价格
        async updateAllProduct() {
            const data = {
                gather_supply_id: parseInt(this.$route.query.id),
            };
            const res = await updateAllYytProduct(data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.$emit('syncGoodsList');
            }
        },
    },
};
</script>

<style scoped></style>
