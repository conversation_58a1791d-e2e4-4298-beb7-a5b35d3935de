<template>
  <el-drawer
    :append-to-body="true"
    :modal-append-to-body="false"
    title="店铺"
    :visible="isShow"
    :close-on-press-escape="false"
    :wrapperClosable="false"
    :before-close="handleClose"
    size="calc(100% - 220px)"
  >
    <el-form inline :model="searchInfo"  ref="searchInfo" class="search-term f fjsb bgw">
      <div>
        <el-form-item prop="goodsName">
          <el-input placeholder="搜索名称" v-model="searchInfo.name" class="line-input" clearable>
              <span slot="prepend">名称</span>
          </el-input>
        </el-form-item>
        <el-form-item prop="is_virtual_stock">
          <div class="line-input" >
            <div class="line-box ">
                <span>虚拟库存</span>
            </div>
            <el-select v-model="searchInfo.is_virtual_stock" class="w100">
              <el-option :value="0" label="关闭"></el-option>
              <el-option :value="1" label="开启"></el-option>
            </el-select>
        </div>
       </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">搜索</el-button>
          <el-button type="text" @click="resetForm('searchInfo')">重置搜索条件</el-button>
        </el-form-item>
      </div>
      <el-form-item>
        <el-button type="primary" @click="openDialog(null)">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" class="mt25">
      <el-table-column label="名称" align="center" prop="name"></el-table-column>
      <el-table-column label="供货价定价系数" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.strategy">{{ transitionFn(scope.row.strategy, 'price') }}%</span>
        </template>
      </el-table-column>
      <el-table-column label="成本价定价系数" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.strategy">{{ transitionFn(scope.row.strategy, 'cost') }}%</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="openDialog(scope.row)">编辑</el-button>
          <el-button type="text" class="color-red" @click="del(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      :current-page="page"
      :page-size="pageSize"
      :page-sizes="[10, 30, 50, 100]"
      :style="{ float: 'right', padding: '20px' }"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
      layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>
    <el-dialog
      :title="title"
      :visible="createIsShow"
      width="900px"
      :modal-append-to-body="false"
      append-to-body
      :before-close="handleCreateClose"
    >
      <el-form :model="formData" label-width="120px">
        <h3>基础设置</h3>
        <el-divider></el-divider>
        <el-form-item label="名称:" prop="name">
          <el-input v-model="formData.name"></el-input>
        </el-form-item>
        <el-form-item label="店铺ID:" prop="shop_id">
          <el-input v-model="formData.shop_id"></el-input>
        </el-form-item>
        <el-form-item label="运费设置:" prop="freight_template_id">
          <!-- <span slot="label"><span class="color-red">*</span>运费设置:</span> -->
          <!-- <el-radio v-model="formData.strategy.goodsFreightType" label="1"> -->
          <!-- <span>运费模板</span> -->
          <div class="w600">
            <el-select v-model="formData.freight_template_id" filterable class="w400">
              <el-option
                  v-for="item in freightTemplateOption"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
              ></el-option>
            </el-select>
            <el-button type="text" class="ml10" @click="addTemp">新建</el-button>
            <el-button type="text" class="ml10" :loading="btnLoadIsShow" @click="getFreightTemp(true)"
            >刷新
            </el-button>
          </div>
          <!-- </el-radio> -->
        </el-form-item>
        <el-form-item label="是否开启虚拟库存:">
          <el-radio-group v-model="formData.is_virtual_stock">
            <el-radio :label="0">关闭</el-radio>
            <el-radio :label="1">开启</el-radio>
          </el-radio-group>
        </el-form-item>
        <h3>定价策略</h3>
        <el-divider></el-divider>
        <el-row>
          <el-col :span="18">
            <el-form-item label="定价策略:">
              <el-radio-group v-model="formData.is_open">
                <el-radio :label="0">关闭</el-radio>
                <el-radio :label="1">开启</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="供货价:">
              <div class="f fac">
                <el-input v-model="formData.strategy.price" @blur="syncInputValue"></el-input>
                <span class="ml_20">%</span>
              </div>
            </el-form-item>
            <el-form-item label="成本价:">
              <div class="f fac">
                <el-input v-model="formData.strategy.cost"></el-input>
                <span class="ml_20">%</span>
              </div>
            </el-form-item>
            <el-form-item label="营销价:">
              <div class="f fac">
                <el-input v-model="formData.strategy.marketing"></el-input>
                <span class="ml_20">%</span>
              </div>
            </el-form-item>
            <el-form-item label="指导价:">
              <div class="f fac">
                <el-input v-model="formData.strategy.guide"></el-input>
                <span class="ml_20">%</span>
              </div>
            </el-form-item>
            <el-form-item label="建议零售价:">
              <div class="f fac">
                <el-input v-model="formData.strategy.origin"></el-input>
                <span class="ml_20">%</span>
              </div>
            </el-form-item>

          </el-col>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button type="primary" @click="confirm">确 定</el-button>
        <el-button @click="handleCreateClose">取 消</el-button>
      </div>
      <TempDialog ref="tempDialog" @load="getFreightTemp" append-to-body></TempDialog>
    </el-dialog>
  </el-drawer>
</template>
<script>
import { lists, deleteWdt, create } from '@/api/wangdiantong'
import infoList from '@/mixins/infoList'
import { confirm } from '@/decorators/decorators'
import { getExpressTemplateList } from '@/api/expressTemplate'
import TempDialog from '@/view/order/distributionTemp/components/templateDialog'

export default {
  mixins: [infoList],
  components: { TempDialog },
  computed: {
    title() {
      return this.formData.id ? '编辑' : '新增'
    },
  },
  data() {
    return {
      isShow: false,
      listApi: lists,
      createIsShow: false,
      btnLoadIsShow: false, // 刷新按钮状态
      freightTemplateOption: [], // 运费模板option
      formData: {
        is_open: 0, // 0 关闭  1  开启
        is_virtual_stock:0, // 虚拟库存
        name: '',
        freight_template_id: null, // 运费模板id
        shop_id: '', // 商品ID
        strategy: {
          price: '', // 供货
          cost: '', // 成本
          marketing: '', // 营销
          guide: '', // 指导
          origin: '', // 建议零售
          // goodsFreightType: '1', // 运费模版
        },
      },
    }
  },
  mounted() {
    // 获取运费模板
    this.getFreightTemp()
  },
  methods: {
    @confirm('提示', '确认删除?')
    async del(id) {
      const { code, msg } = await deleteWdt({ id })
      if (code === 0) {
        this.$message.success(msg)
        this.getTableData()
      }
    },
    resetForm(from){
      this.$refs[from].resetFields();
      this.searchInfo = {}
    },
    async search() {
      this.page = 1
      this.getTableData()
    },
    // 初始化
    init() {
      this.isShow = true
      this.getTableData()
    },
    handleClose() {
      this.isShow = false
      this.searchInfo = {}
      this.page = 1
    },
    openDialog(row = null) {
      this.createIsShow = true
      if (row) {
        let strategy = JSON.parse(row.strategy)
        this.formData = { ...row }
        this.formData.strategy = { ...strategy }
      }
    },
    /**
     * 新增弹窗部分开始
     */
    handleCreateClose() {
      this.createIsShow = false
      this.formData = {
        is_open: 0, // 0 关闭  1  开启
        is_virtual_stock:0, // 开启虚拟库存
        name: '',
        freight_template_id: null, // 运费模板id
        shop_id: '', // 商品ID
        strategy: {
          price: '', // 供货
          cost: '', // 成本
          marketing: '', // 营销
          guide: '', // 指导
          origin: '', // 建议零售
        },
      }
    },
    async confirm() {
      let params = {
        is_open: parseInt(this.formData.is_open),
        name: this.formData.name,
        strategy: JSON.stringify(this.formData.strategy),
        freight_template_id: this.formData.freight_template_id,
        shop_id: this.formData.shop_id,
        is_virtual_stock: this.formData.is_virtual_stock
      }
      if (this.formData.id) {
        params.id = this.formData.id
      }
      const { code, msg } = await create(params)
      if (code === 0) {
        this.$message.success(msg)
        this.handleCreateClose()
        this.getTableData()
      }
    },
    // 新增情况下 同步输入框
    syncInputValue() {
      if (!this.formData.id) {
        this.formData.strategy.cost = this.formData.strategy.price
        this.formData.strategy.marketing = this.formData.strategy.price
        this.formData.strategy.guide = this.formData.strategy.price
        this.formData.strategy.origin = this.formData.strategy.price
      }
    },
    transitionFn(row, key) {
      let data = JSON.parse(row)
      return data[key]
    },
    // 新建运费模版
    addTemp() {
      this.$refs.tempDialog.isShow = true
      this.isShow = true
      this.$nextTick(() => {
        this.$refs.tempDialog.title = '新增'
      })
    },
    // 获取运费模板
    getFreightTemp(flg = false) {
      this.btnLoadIsShow = flg
      getExpressTemplateList({ page: 1, pageSize: 999 }).then(res => {
        if (res.code === 0) {
          this.freightTemplateOption = res.data.list
          this.btnLoadIsShow = false
        }
      })
    },
  },
}
</script>
<style scoped lang="scss">
@import '@/style/base.scss';
.w400 {
  width: 492px;
}
.w600 {
  width: 600px;
}

.bgw{
  background-color: white;
}
</style>
