<template>
  <m-card>
    <el-form :model="formData" label-width="90px">
      <el-row>
        <el-col :span="13">
          <el-form-item label="分类设置:" prop="category_string">
            <el-input v-model="formData.category_string" type="textarea" :rows="6"></el-input>
            <p class="color-grap">每行一个分类!</p>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item>
            <el-button type="primary" @click="save">保 存</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </m-card>
</template>

<script>
import {findApplicationSetting, updateApplicationSetting} from "@/api/topic";

export default {
  name: "supplySubjectBase",
  data() {
    return {
      formData: {
        id: null,
        category_string: ""
      }
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async save() {
      let params = {
        id: this.formData.id,
        value: {
          category_string: this.formData.category_string
        }
      }
      const {code, msg} = await updateApplicationSetting(params)
      if (code === 0) {
        this.$message.success(msg)
      }
    },
    async init() {
      const {data} = await findApplicationSetting()
      const setting = data.setting
      this.formData.id = setting.id
      this.formData.category_string = setting.value.category_string
    }
  }
}
</script>

<style scoped>

</style>