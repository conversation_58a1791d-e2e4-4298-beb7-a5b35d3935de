<template>
  <el-drawer :title="formData.id ? '编辑':'新增'" :visible="isShow" :close-on-press-escape="false" :wrapperClosable="false"
             :before-close="handleClose" size="calc(100% - 220px)" class="detail-ct">
    <el-form :model="formData" label-width="120px" ref="form" :rules="rules">
      <el-row>
        <el-col :span="13">
          <el-form-item label="专题名称:" prop="title">
            <el-input v-model="formData.title"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="13">
          <el-form-item label="专题分类:" prop="category">
            <el-select v-model="formData.category" clearable filterable class="w100">
              <el-option v-for="item in categotyList" :key="item.id" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col class="mt25">
          <p>专题内容</p>
        </el-col>
        <el-col class="mt25" :span="13">
          <el-form-item label="banner图:" prop="banner">
            <el-upload class="uploader-box uploader-banner-box" :show-file-list="false"
                       :action="`${$path}/fileUploadAndDownload/upload`"
                       :headers="{ 'x-token': token }" :on-success="handleBannerSuccess"
                       :before-upload="$fn.beforeAvatarUpload "
                       accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
              <m-image v-if="formData.banner" style="width: 100%;height: 100%" :src="formData.banner">
              </m-image>
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
            <p class="color-grap">建议尺寸：640*320px</p>
          </el-form-item>
        </el-col>
        <el-col :span="13">
          <el-form-item label="主题色:" prop="main_color">
            <el-color-picker v-model="formData.main_color"></el-color-picker>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="商品:" prop="products">
            <div class="f fw">
              <template v-if="formData.products.length > 0">
                <div class="goods-box goods-check-item" v-for="(item,index) in formData.products" :key="item.id">
                  <m-image :src="item.image_url" style="width: 100%;height: 100%"></m-image>
                  <div>
                    <el-tooltip 
                      effect="light" 
                      :content="item.title" 
                      v-if="item.title.length > 18"
                      placement="bottom-start">
                      <p class="thumbtitle black">{{item.title.slice(0, 18) + '...'}}</p>
                    </el-tooltip>
                    <p v-else class="thumbtitle">{{item.title}}</p>
                  </div>
                  <p class="thumb-remark">商品id: {{item.id}}</p>
                  <div class="del-box">
                    <el-button type="text" icon="el-icon-delete" @click="delGoodsItem(index)"></el-button>
                  </div>
                </div>
              </template>
              <div class="goods-box select-goods-box" @click="openProductDrawer">
                <i class="el-icon-plus avatar-uploader-icon"></i>
                <p>选择商品</p>
              </div>
            </div>
          </el-form-item>
        </el-col>
        <el-col class="mt25">
          <p>专题素材</p>
        </el-col>
        <el-col class="mt25" :span="13">
          <el-form-item label="海报背景:" prop="post_img">
            <el-upload class="uploader-box" :show-file-list="false"
                       :action="`${$path}/fileUploadAndDownload/upload`"
                       :headers="{ 'x-token': token }" :on-success="handlePostImgSuccess"
                       :before-upload="$fn.beforeAvatarUpload "
                       accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
              <m-image v-if="formData.post_img" style="width: 100%;height: 100%" :src="formData.post_img">
              </m-image>
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="13">
          <el-form-item label="广告图片:" prop="ad_img">
            <el-upload class="uploader-box" :show-file-list="false"
                       :action="`${$path}/fileUploadAndDownload/upload`"
                       :headers="{ 'x-token': token }" :on-success="handleAdImgSuccess"
                       :before-upload="$fn.beforeAvatarUpload "
                       accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
              <m-image v-if="formData.ad_img" style="width: 100%;height: 100%" :src="formData.ad_img">
              </m-image>
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="推广文案:" prop="text">
            <m-editor v-model="formData.text"></m-editor>
          </el-form-item>
        </el-col>
        <el-col style="margin-top: 40px">
          <el-form-item>
            <el-button type="primary" @click="save('form')">保 存</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <product-drawer ref="productDrawer" @getSelectedProduct="getSelectedProduct"></product-drawer>
  </el-drawer>
</template>

<script>
import {getTopicCategory, updateTopic, createTopic, findTopic} from "@/api/topic"
import {mapGetters} from "vuex";
import ProductDrawer from "@/components/productDrawer";
import {confirm} from "@/decorators/decorators";

export default {
  name: "subjectDrawer",
  components: {ProductDrawer},
  data() {
    return {
      isShow: false,
      categotyList: [],
      formData: {
        id: null,
        title: "",
        category: "", // 分类 string
        banner: "",
        main_color: "", // 主题颜色
        products: [], // 商品
        post_img: "", // 海报背景
        ad_img: "", // 广告图片
        text: "", // 推广文案
      },
      rules: {
        title: {required: true, message: "请输入专题名称", trigger: "blur"},
        banner: {required: true, message: "请选择banner图", trigger: "blur"},
        products: {required: true, message: "请选择商品", trigger: "blur"},
      }
    }
  },
  computed: {
    ...mapGetters("user", ["token"])
  },
  methods: {
    // 删除选中商品
    @confirm("提示", "确定删除?")
    delGoodsItem(index) {
      this.formData.products.splice(index, 1)
    },
    // 获取选中的商品
    getSelectedProduct(products) {
      this.formData.products = this.$fn.merjeArr(this.formData.products, products)
    },
    openProductDrawer() {
      this.$refs.productDrawer.isShow = true
      this.$refs.productDrawer.init()
    },
    save(refName) {
      this.$refs[refName].validate((valid) => {
        if (!valid) return false;
        let params = {
          title: this.formData.title,
          category: this.formData.category,
          banner: this.formData.banner,
          main_color: this.formData.main_color,
          post_img: this.formData.post_img,
          ad_img: this.formData.ad_img,
          text: this.formData.text,
        }
        if (this.formData.products && this.formData.products.length) {
          /*params.products = []
          this.formData.products.forEach(item=>{
            params.products.push(item.id)
          })*/
          params.products = this.formData.products.map(item => ({id: item.id}))
        }
        if (this.formData.id) {
          params.id = this.formData.id
          updateTopic(params).then(res => {
            if (res.code === 0) {
              this.$message.success(res.msg)
              this.handleClose()
              this.$emit("reload", 2)
            }
          })
        } else {
          createTopic(params).then(res => {
            if (res.code === 0) {
              this.$message.success(res.msg)
              this.handleClose()
              this.$emit("reload", 1)
            }
          })
        }
      })
    },
    async init(id) {
      this.isShow = true
      // 获取专题分类
      let categoryRes = await getTopicCategory()
      if (categoryRes.code === 0) {
        this.categotyList = categoryRes.data.list
      }
      if (id) {
        const {code, data} = await findTopic({id})
        if (code === 0) {
          if (data.retopic.products && data.retopic.products.length) {
            data.retopic.products = data.retopic.products.map(item => ({
              ...item,
              image_url: item.thumb
            }))
          }
          this.formData = this.$fn.deepClone(data.retopic)
        }
      }
    },
    handleClose() {
      try {
        this.$refs.form.resetFields()
      } catch {
      } finally {
        this.isShow = false
        this.categotyList = []
        this.formData = {
          id: null,
          title: "",
          category: "", // 分类 string
          banner: "",
          main_color: "", // 主题颜色
          products: [], // 商品
          post_img: "", // 海报背景
          ad_img: "", // 广告图片
          text: "", // 推广文案
        }
      }
    },
    // banner图
    handleBannerSuccess(res) {
      if (res.code === 0) {
        this.formData.banner = res.data.file.url
      }
    },
    // 海报背景
    handlePostImgSuccess(res) {
      if (res.code === 0) {
        this.formData.post_img = res.data.file.url
      }
    },
    // 广告图片
    handleAdImgSuccess(res) {
      if (res.code === 0) {
        this.formData.ad_img = res.data.file.url
      }
    },
    beforeUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error("上传图片大小不能超过 10MB!");
      }
      return isLt10M;
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .uploader-box {
  width: 134px;
  height: 134px;
  line-height: 134px;
  border: 1px dashed rgba(226, 226, 226, 100);
  text-align: center;

  .el-upload {
    width: 100%;
    height: 100%;
  }

  i {
    font-size: 20px;
    color: #8F949E;
  }

  &.uploader-banner-box {
    width: 253px;
    height: 126px;
    line-height: 126px;
  }
}

::v-deep .el-drawer {
  .el-drawer__body {
    background-color: #fff !important;
  }
}

.goods-box {
  width: 134px;
  height: 134px;
}

.select-goods-box {
  cursor: pointer;
  border: 1px dashed rgba(226, 226, 226, 100);
  text-align: center;
  color: #BEBEBE;

  i {
    margin-top: 40px;
    font-size: 20px;
  }
}

.goods-check-item {
  margin-right: 20px;
  margin-bottom: 100px;
  position: relative;

  &:hover {
    .del-box {
      display: inline-block;
    }
  }

  .del-box {
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-color: rgba(0, 0, 0, 0.4);
    text-align: center;
    line-height: 134px;

    .el-button {
      color: #fff;
      font-size: 20px;
    }
  }
}
.mb50 {
  margin-bottom: 50px;
}
.thumb-remark {
  margin-top: 5px;
  line-height: 22px;
  font-size: 12px;
  color: #606266c2;
}
.thumbtitle {
  line-height: 22px;
  color: #606266;
}
</style>