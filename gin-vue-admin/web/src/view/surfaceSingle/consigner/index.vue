<template>
  <m-card>
    <el-button type="primary" @click="openAdd">新增</el-button>
    <el-table :data="tableData" class="mt25">
      <el-table-column label="ID" align="center" prop="id"></el-table-column>
      <el-table-column label="发件人" align="center" prop="name"></el-table-column>
      <el-table-column label="联系电话" align="center" prop="mobile"></el-table-column>
      <el-table-column label="发件地址" align="center" prop="shipperAddress"></el-table-column>
      <el-table-column label="发件地邮编" align="center" prop="post_code"></el-table-column>
      <el-table-column label="发件签名" align="center" prop="remark"></el-table-column>
      <el-table-column label="是否默认(只能设置一个)" align="center">
        <template slot-scope="scope">
          <el-switch 
            v-model="scope.row.is_default" 
            @change="onChangeDefault(scope.row)" 
            :active-value="1" 
            :inactive-value="0">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="openEdit(scope.row)" style="padding: 0 !important;margin-left: 10px !important;">编辑</el-button>
          <el-button type="text" class="color-red" @click="onDelete(scope.row.id)" style="padding: 0 !important;margin-left: 10px !important;">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination 
      background 
      layout="total, sizes, prev, pager, next, jumper"
      :style="{ display: 'flex', justifyContent: 'flex-end', marginRight: '20px', }" 
      :current-page="page" 
      :page-size="pageSize" 
      :page-sizes="[10, 30, 50, 100]" 
      :total="total" 
      @current-change="handleCurrentChange" 
      @size-change="handleSizeChange">
    </el-pagination>
    <AddEdit ref="addEdit" @reload="init" @switch="onChangeSub"></AddEdit>
  </m-card>
</template>

<script>
import infoList from "@/mixins/infoList";
import AddEdit from "./components/add";
import { getAddressersList, deleteAddresser, changeAddresserDefault } from "@/api/surfaceSingle";
import {confirm} from "@/decorators/decorators"
export default {
  name: "consignerMessageIndex",
  components: {AddEdit},
  mixins: [infoList],
  data() {
    return {
      tableData:[],
      page: 1,
      pageSize: 10,
      total: 0,
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    openAdd() {
      this.$refs.addEdit.isShow = true
      this.$refs.addEdit.formData = {}
    },
    openEdit(row) {
      this.$refs.addEdit.isShow = true
      this.$refs.addEdit.init(row)
    },
    onChangeDefault(row){
      const params = {
        id: row.id,
        is_default: row.is_default
      }
      changeAddresserDefault(params).then((res) => {
        if (res.code === 0) {
          this.$message.success(res.msg)
        } else {
          this.$message.error(res.msg)
        }
      });
    },
    onChangeSub(val){
      const params = {
        id: val.id,
        is_default: val.is_default
      }
      changeAddresserDefault(params).then((res) => {
        if (res.code === 0) {
          this.$message.success(res.msg)
          
        } else {
          this.$message.error(res.msg)
        }
      });
    },
    init() {
      const params = {
        page: this.page,
        pageSize: this.pageSize
      }
      getAddressersList(params).then((res) => {
        if (res.code === 0 && res.data.list.length) {
          this.tableData = res.data.list.map(item => {
            return {
              ...item, 
              shipperAddress: item.province + item.city + item.county + item.town + item.address
            }
          })
          this.total = res.data.total
        }
      });
    },
    @confirm("提示","确定取消绑定?")
    onDelete(id){
      const params = {
        id: id,
      }
      deleteAddresser(params).then((res) => {
        if (res.code === 0) {
          this.$message.success(res.msg)
          this.init()
        } else {
          this.$message.error(res.msg)
        }
      });
    },
    handleCurrentChange(page) {
      this.page = page;
      this.init();
    },
    handleSizeChange(size) {
      this.pageSize = size;
      this.init();
    },

  }
}
</script>

<style lang="scss" scoped>

</style>