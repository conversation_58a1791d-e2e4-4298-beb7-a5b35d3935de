<template>
  <el-drawer :title="formData.id ? '编辑' : '新增'" :visible="isShow" :close-on-press-escape="false"
             :wrapperClosable="false"
             :before-close="handleClose" size="calc(100% - 220px)" class="detail-ct">
    <m-card>
      <div class="search-term top-box">
        <p>
          <b>快递公司支持情况:</b>
          顺丰速运、宅急送、圆通速递、百世快递、中通快递、韵达速递、申通快递、德邦快递、优速快递、京东快递、信丰物流、安能快递、国通快递、天天快递、跨越速运、邮政快递包裹、中铁快运、邮政国内标快、远成快运、全一快递、速尔快递、品骏快递。
        </p>
        <p>
          <b>快运公司支持情况:</b>
          德邦快运、安能快运、京东快运、龙邦快运。
        </p>
        <p>
          <b>无需申请直接打单:</b>
          顺丰（SF）、宅急送（ZJS）、中铁快运（ZTKY）、全一快递（UAPEX）
        </p>
        <p>
          <b>月结账号直接打单:</b>
          德邦（DBL）
        </p>
        <p>
          <b>快递鸟后台申请账号:</b>
          优速（UC）、韵达（YD）、圆通（YTO）、远成（YCWL）、安能（ANE）、百世快递（HTKY）
        </p>
        <p>
          <b>线下（网点）申请账号:</b>
          EMS（广东省内发件不需要,
          广东省外EMS发货，需联系当地EMS网点，在85系统里面申请大客户和APP_SECRET）、中通（ZTO）、申通（STO）、德邦（DBL）、京东（JD）、信丰（XFEX）、国通（GTO）、天天快递（HHTT）、速尔快递（SURE）、品骏快递（PJ）
        </p>
        <p>
          <b>快运电子面单:</b>
          京东快运（JDKY）,安能快运（ANEKY）,德邦快运（DBLKY），龙邦快运（LB）
        </p>
        <p>
          <b>
            顺丰、宅急送等直营型的可以直接使用快递鸟的账户请求电子面单接口。中通，圆通，申通，百世快递，韵达，优速等加盟型的需要客户去当地物流快递网点申请电子面单账户，将相应的参数传入快递鸟电子面单接口进行电子面单请求。
          </b>
        </p>
        <p>
          <b>
            将订单号、收寄件地址等信息通过电子面单API传递给快递公司，快递公司会通过接口返回物流单号给到用户端，打印在面单上，就是面单上的运单号。加盟快递公司需要预先充值单号，请联系当地合作网点办理。直营类快递公司，如顺丰、宅急送等，审核后无需预充值，随用随取。
          </b>
        </p>
      </div>
      <el-form class="mt25" :model="formData" :rules="rules" ref="form" label-width="180px">
        <el-form-item label="电子面单名称:" prop="name">
          <el-input v-model="formData.name" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="电子面单客户账号:" prop="user_name">
          <el-input v-model="formData.user_name" placeholder="请输入"></el-input>
          <p class="color-grap">可输入商家ID、客户简称、商家编码、客户平台ID、客户帐号、客户号、操作编码、商家代码、客户编号、商家cp、大客户号中的一种</p>
        </el-form-item>
        <el-form-item label="电子面单密码:" prop="password">
          <el-input v-model="formData.password" placeholder="请输入"></el-input>
          <p class="color-grap">可输入客户平台验证码、客户密码、接口联调密码、ERP秘钥、客户平台验证码、APP_SECRET中的一种</p>
        </el-form-item>
        <el-form-item label="快递类型:" prop="shipper_code">
          <el-select v-model="formData.shipper_code" class="w100" filterable
                     @change="handleShipperCodeChange">
            <el-option v-for="item in courierOptions" :key="item.id" :label="item.name" :value="item.code"></el-option>
          </el-select>
          <p class="color-grap">
            顺丰、宅急送可以直接使用快递鸟的账户请求电子面单接口。 <br>
            京东，德邦，中通，圆通，申通，百世快递，韵达，安能，优速，快捷等需要客户去当地物流快递网点申请电子面单账户，将相应的参数传入快递鸟电子面单接口进行电子面单请求。
          </p>
        </el-form-item>
        <el-form-item label="快递/快运业务类型:" prop="exp_type">
          <el-select v-model="formData.exp_type" class="w100" filterable>
            <el-option v-for="item in expTypeOptios" :key="item.id" :label="item.name" :value="item.key"></el-option>
          </el-select>
          <p class="color-grap">不选或没有选项则使用默认类型</p>
        </el-form-item>
        <el-form-item label="模板样式:" prop="template_size">
          <el-select v-model="formData.template_size" class="w100" filterable>
            <el-option v-for="item in templateOptions" :key="item.id" :label="item.name"
                       :value="item.value"></el-option>
          </el-select>
          <p class="color-grap">
            除品骏快递(PJ)为一联宽80mm、佳吉快运(CNEX)为一联宽90mm、德邦快运(DBLKY)为三联宽100mm,其余快递公司模板样式均为二联,宽100mm,展示的为模板高度</p>
        </el-form-item>
        <el-form-item label="月结编码:" prop="sign">
          <el-input v-model="formData.sign" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="收件网点标识:" prop="station_sign">
          <el-input v-model="formData.station_sign" placeholder="请输入"></el-input>
          <p class="color-grap">可输入仓库ID、网点名称、网点编号(仓库号)中的一种</p>
        </el-form-item>
        <el-form-item label="是否为默认模板:" prop="is_default">
          <el-radio-group v-model="formData.is_default">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否通知快递员上门揽件:" prop="is_notice">
          <el-radio-group v-model="formData.is_notice">
            <el-radio :label="0">是</el-radio>
            <el-radio :label="1">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="通知快递员上门揽件时间:" v-if="formData.is_notice === 0">
          <div class="f fac">
            <el-select v-model="dateObj.day" placeholder="请选择时间" clearable>
              <el-option v-for="item in dateObj.dayOptions" :key="item.id" :label="item.label"
                         :value="item.type"></el-option>
            </el-select>
            <el-select v-model="dateObj.startTime" placeholder="起始时间" clearable class="ml10"
                       @change="handleStartTimeChange">
              <el-option v-for="item in dateObj.startTimeOptions" :key="item.id" :label="item._time"
                         :value="item._time"></el-option>
            </el-select>
            <span class="ml10 mr10">至</span>
            <el-select v-model="dateObj.endTime" placeholder="结束时间" clearable>
              <el-option v-for="item in dateObj.endTimeOptions" :key="item.id" :label="item._time" :value="item._time"
                         :disabled="item.disabled"></el-option>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="save">保 存</el-button>
        </el-form-item>
      </el-form>
    </m-card>
  </el-drawer>
</template>

<script>
import {createSurfaceSingle, getCourierList, updateSurfaceSingle} from "@/api/surfaceSingle";
import {formatTimeToStr} from "@/utils/date";

export default {
  data() {
    return {
      isShow: false,
      // 时间部分
      dateObj: {
        day: "",
        dayOptions: [
          {label: "今天", type: 1},
          {label: "明天", type: 2},
          {label: "后天", type: 3},
        ],
        startTime: "",
        startTimeOptions: [
          {_time: "08:00"},
          {_time: "09:00"},
          {_time: "10:00"},
          {_time: "11:00"},
          {_time: "12:00"},
          {_time: "13:00"},
          {_time: "14:00"},
          {_time: "15:00"},
          {_time: "16:00"},
          {_time: "17:00"},
        ],
        endTime: "",
        endTimeOptions: [
          {_time: "08:00", disabled: false},
          {_time: "09:00", disabled: false},
          {_time: "10:00", disabled: false},
          {_time: "11:00", disabled: false},
          {_time: "12:00", disabled: false},
          {_time: "13:00", disabled: false},
          {_time: "14:00", disabled: false},
          {_time: "15:00", disabled: false},
          {_time: "16:00", disabled: false},
          {_time: "17:00", disabled: false},
        ],
      },
      courierOptions: [],
      expTypeOptios: [],
      templateOptions: [],
      formData: {
        name: "",
        user_name: "", // 客户账号
        password: "", // 密码
        sign: "", // 月结编码
        shipper_code: "", // 快递公司编码
        shipper_name: "", // 快递公司名称
        exp_type: "", // 快递业务类型
        template_size: "", // 模板样式
        station_sign: "", // 网点标识
        is_default: null, // 默认模板
        is_notice: 1, // 是否通知快递员上门揽件 0- 通知 1- 不通知 不填则默认为1
        start_date: "", // 上门取件时间
        end_date: "", // 上门取件时间
      },
      rules: {
        name: {required: true, message: "请输入电子面单名称", trigger: "blur"},
        shipper_code: {required: true, message: "请选择快递类型", trigger: "change"},
        exp_type: {required: true, message: "请选择快递/快运业务类型", trigger: "change"}
      }
    }
  },
  methods: {
    // 切换开始时间 处理结束时间
    handleStartTimeChange(value) {
      let i = this.dateObj.endTimeOptions.indexOfJSON("_time", value) || 0
      this.dateObj.endTimeOptions.forEach((item, index) => {
        if (index <= i) {
          item.disabled = true
        } else {
          item.disabled = false
        }
      })
    },
    // 选中快递
    handleShipperCodeChange(value) {
      this.expTypeOptios = []
      this.templateOptions = []
      this.formData.shipper_name = ""
      this.formData.exp_type = ""
      this.formData.template_size = ""
      let obj = this.courierOptions.find((item) => item.code === value)
      this.formData.shipper_name = obj.name
      this.expTypeOptios = obj.types
      this.templateOptions = obj.template_style
    },
    // 打开弹出层
    open(row) {
      this.isShow = true
      this.getCourierOptions().then(() => {
        if (row) {
          this.handleShipperCodeChange(row.shipper_code)
          this.formData = {...row}
          if (row.is_notice === 0) {
            let startDate = formatTimeToStr(row.start_date)
            let endDate = formatTimeToStr(row.end_date)
            this.dateObj.day = startDate.split(" ")[0]
            this.dateObj.startTime = startDate.split(" ")[1].substring(0, 5)
            this.dateObj.endTime = endDate.split(" ")[1].substring(0, 5)
            this.handleStartTimeChange(this.dateObj.startTime)
          }

        }
      })
    },
    // 获取快递列表
    async getCourierOptions() {
      const {code, data} = await getCourierList()
      if (code === 0) {
        this.courierOptions = data.list
      }
    },
    // 关闭弹出层
    handleClose() {
      try {
        this.$refs.form.resetFields()
      } catch {
      } finally {
        this.isShow = false
        this.dateObj = {
          day: "",
          dayOptions: [
            {label: "今天", type: 1},
            {label: "明天", type: 2},
            {label: "后天", type: 3},
          ],
          startTime: "",
          startTimeOptions: [
            {_time: "08:00"},
            {_time: "09:00"},
            {_time: "10:00"},
            {_time: "11:00"},
            {_time: "12:00"},
            {_time: "13:00"},
            {_time: "14:00"},
            {_time: "15:00"},
            {_time: "16:00"},
            {_time: "17:00"},
          ],
          endTime: "",
          endTimeOptions: [
            {_time: "08:00", disabled: false},
            {_time: "09:00", disabled: false},
            {_time: "10:00", disabled: false},
            {_time: "11:00", disabled: false},
            {_time: "12:00", disabled: false},
            {_time: "13:00", disabled: false},
            {_time: "14:00", disabled: false},
            {_time: "15:00", disabled: false},
            {_time: "16:00", disabled: false},
            {_time: "17:00", disabled: false},
          ],
        }
        this.courierOptions = []
        this.expTypeOptios = []
        this.templateOptions = []
        this.formData = {
          name: "",
          user_name: "", // 客户账号
          password: "", // 密码
          sign: "", // 月结编码
          shipper_code: "", // 快递公司编码
          shipper_name: "", // 快递公司名称
          exp_type: "", // 快递业务类型
          template_size: "", // 模板样式
          station_sign: "", // 网点标识
          is_default: null, // 默认模板
          is_notice: 1, // 是否通知快递员上门揽件 0- 通知 1- 不通知 不填则默认为1
          start_date: "", // 上门取件时间
          end_date: "", // 上门取件时间
        }
      }
    },
    // 保存
    save() {
      this.$refs.form.validate((valid) => {
        if (!valid) return false
        let startTime, endTime = ""
        if (this.formData.is_notice === 0) {
          // 日期处理
          if (this.dateObj.day) {
            startTime = this.getDate(this.dateObj.day)
            endTime = this.getDate(this.dateObj.day)
          } else {
            this.$message.error("请选择时间")
            return false
          }
          // 开始时间处理
          if (this.dateObj.startTime) {
            startTime += ` ${this.dateObj.startTime}:00`
          } else {
            this.$message.error("请选择开始时间")
            return false
          }
          // 结束时间处理
          if (this.dateObj.endTime) {
            endTime += ` ${this.dateObj.endTime}:00`
          } else {
            this.$message.error("请选择开始时间")
            return false
          }
          this.formData.start_date = this.dateStrToTime(startTime)
          this.formData.end_date = this.dateStrToTime(endTime)
        }
        // 编辑
        if (this.formData.id) {
          updateSurfaceSingle(this.formData).then(res => {
            if (res.code === 0) {
              this.$message.success(res.msg)
              this.handleClose()
              this.$emit("reload")
            }
          })
        } else { // 新增
          createSurfaceSingle(this.formData).then(res => {
            if (res.code === 0) {
              this.$message.success(res.msg)
              this.handleClose()
              this.$emit("reload")
            }
          })
        }
      })
    },
    // 获取日期
    getDate(type) {
      let day = new Date();
      switch (type) {
        case 1: // 今天
          day.setTime(day.getTime());
          break;
        case 2: // 明天
          day.setTime(day.getTime() + 24 * 60 * 60 * 1000);
          break;
        case 3: // 后天
          day.setTime(day.getTime() + 48 * 60 * 60 * 1000);
          break;
      }
      return day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
    },
    //日期字符串转成时间戳
    //例如var date = '2015-03-05 17:59:00';
    dateStrToTime(dateStr) {
      dateStr = dateStr.substring(0, 19);
      dateStr = dateStr.replace(/-/g, '/');
      let _time = new Date(dateStr).getTime() / 1000;
      return _time
    }
  }
}
</script>

<style lang="scss" scoped>
.top-box {
  p {
    margin-bottom: 10px;
    line-height: 23px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>