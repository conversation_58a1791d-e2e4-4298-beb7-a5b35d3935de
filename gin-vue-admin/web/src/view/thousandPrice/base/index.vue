<template>
  <m-card>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="商品池商品" name="1">
        <Base ref="base"></Base>
      </el-tab-pane>
      <el-tab-pane label="商品池设置" name="2">
        <shop-ratio ref="shopRatio"></shop-ratio>
      </el-tab-pane>
      <el-tab-pane label="统一定价策略" name="3">
        <unify-ratio ref="unifyRatio"></unify-ratio>
      </el-tab-pane>
      <el-tab-pane label="供应商定价策略" name="4">
        <supplier-ratio ref="supplierRatio"></supplier-ratio>
      </el-tab-pane>
      <el-tab-pane label="分类定价策略" name="5">
        <category-ratio ref="categoryRatio"></category-ratio>
      </el-tab-pane>
      <el-tab-pane label="商品规格变动列表" name="6">
        <miss-skus-list ref="missSkusList"></miss-skus-list>
      </el-tab-pane>
    </el-tabs>
  </m-card>
</template>

<script>
import Base from './base'
import unifyRatio from './unifyRatio.vue'
import supplierRatio from './supplierRatio.vue'
import categoryRatio from './categoryRatio.vue'
import shopRatio from './shopRatio.vue'
import missSkusList from './missSkusList.vue'
export default {
  name: 'thousandPriceBaseIndex',
  components: {
    Base,
    unifyRatio,
    supplierRatio,
    categoryRatio,
    shopRatio,
    missSkusList
  },
  data() {
    return {
      activeName: '1',
    }
  },
  methods: {
    handleClick(tab, event) {
      let name = ''
      switch (this.activeName) {
        case '1':
          name = 'base'
          break
        case '2':
          name = 'shopRatio'
          break
        case '3':
          name = 'unifyRatio'
          break
        case '4':
          name = 'supplierRatio'
          break
        case '5':
          name = 'categoryRatio'
          break
        case '6':
          name = 'missSkusList'
      }
      if (this.activeName !== '3') {
        this.$refs[name].page = 1
      }
      if (this.activeName === '1') {
        this.$refs[name].getProductList()
      }
      if (this.activeName !== '1') {
        this.$refs[name].init()
      }
    },
  },
}
</script>

<style scoped></style>
