<template>
    <m-card>
        <el-form ref="form" label-width="120px" inline>
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="formData.ProductID"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">商品ID</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="formData.ProductName"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">商品名称</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input placeholder="请输入" v-model="formData.SkuID" class="line-input" clearable>
                    <span slot="prepend">规格ID</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input placeholder="请输入" v-model="formData.SkuName" class="line-input" clearable>
                    <span slot="prepend">规格名称</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="search">搜索</el-button>
                <el-button type="text" @click="reset">重置搜索条件</el-button>
                <el-button @click="missSkusMarkALL">批量标记已读</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="tableList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column label="商品ID" prop="product_id" align="center"></el-table-column>
            <el-table-column label="商品名称" prop="product_title" align="center"></el-table-column>
            <el-table-column label="规格ID" prop="sku_id" align="center"></el-table-column>
            <el-table-column label="规格名称" prop="sku_title" align="center"></el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button
                        v-if="scope.row.status !== 1"
                        type="text"
                        @click="missSkusMark(scope.row.id)"
                        style="padding: 0 !important;margin-left: 10px !important;"
                    >标记已读</el-button>
                    <el-button
                        v-else
                        type="text"
                        style="padding: 0 !important;margin-left: 10px !important;"
                    >已读</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            class="pagination"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
        ></el-pagination>
    </m-card>
</template>

<script>
import { missSkusList, missSkusMark } from '@/api/thousandPrice';

export default {
    name: 'missSkusList',
    data() {
        return {
            page: 1,
            pageSize: 10,
            total: 0,
            formData: {
                ProductID: null,
                ProductName: '',
                SkuName: '',
                SkuID: '',
            },
            tableList: [],
            select_ids: [],
        };
    },
    mounted() {},
    methods: {
        init() {
            this.getMissSkusList();
        },
        // 搜索
        search() {
            this.page = 1;
            this.getMissSkusList();
        },
        // 重置搜索
        reset() {
            this.page = 1;
            this.pageSize = 10;
            this.total = 0;
            this.formData = {
                ProductID: null,
                ProductName: '',
                SkuName: '',
                SkuID: '',
            };
            this.getMissSkusList();
        },
        // 选中的标记已读数据
        handleSelectionChange(val) {
            this.select_ids = [];
            val.forEach((element) => {
                this.select_ids.push(element.id);
            });
        },
        // 获取sku列表
        async getMissSkusList() {
            let params = {
                Page: this.page,
                PageSize: this.pageSize,
                ID: parseInt(this.$route.query.id)
            };
            
            if (this.formData.ProductID) {
                if (parseInt(this.formData.ProductID) || parseInt(this.formData.ProductID) === 0) {
                    params.ProductID = parseInt(this.formData.ProductID);
                } else {
                    this.$message.error('商品ID有误');
                    return
                }
            }
            if (this.formData.ProductName) {
                params.ProductName = this.formData.ProductName;
            }
            if (this.formData.SkuName) {
                params.SkuName = this.formData.SkuName;
            }
            if (this.formData.SkuID) {
                if (parseInt(this.formData.SkuID) || parseInt(this.formData.SkuID) === 0) {
                    params.SkuID = parseInt(this.formData.SkuID);
                } else {
                    this.$message.error('规格ID有误');
                    return
                }
            }
            let res = await missSkusList(params);
            if (res.code === 0) {
                this.total = res.data.total;
                this.tableList = res.data.data;
            }
        },
        // 标记已读
        missSkusMark(id) {
            this.$confirm('确定要标记已读吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                let res = await missSkusMark({ ids: [id] });
                if (res.code === 0) {
                    this.$message.success(res.msg);
                    this.getMissSkusList();
                }
            });
        },
        // 批量标记已读
        missSkusMarkALL() {
            if (this.select_ids.length === 0) {
                this.$message.error('请选择标记已读的数据');
                return;
            }
            this.$confirm('确定要标记已读吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                let params = {
                    ids: this.select_ids,
                };
                let res = await missSkusMark(params);
                if (res.code === 0) {
                    this.$message.success(res.msg);
                    this.getMissSkusList();
                }
            })
        },
        // 分页 每页条数
        handleSizeChange(pageSize) {
            this.pageSize = pageSize;
            this.getMissSkusList();
        },
        // 分页 当前页数
        handleCurrentChange(page) {
            this.page = page;
            this.getMissSkusList();
        },
    },
};
</script>

<style lang="scss" scoped>
.pagination {
    float: right;
}
</style>