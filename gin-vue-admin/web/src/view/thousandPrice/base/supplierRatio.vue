<template>
  <m-card>
    <el-button type="primary" @click="addSupplier" style="margin-bottom: 20px;">添加供应商定价策略</el-button>
    <el-form :model="searchForm" label-width="80px" inline>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >供应商</span>
              </div>
              <el-select v-model="searchForm.supplier_id" filterable clearable placeholder="请选择">
                <el-option label="全部" value=""></el-option>
                <el-option label="平台自营" value="0"></el-option>
                <el-option label="全部供应商" value="999999"></el-option>
                <el-option v-for="item in supplierOptionList" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
          </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="supplierTable">
      <el-table-column label="ID" prop="id" align="center"></el-table-column>
      <el-table-column label="供应商名称" align="center">
        <template slot-scope="scope">
          {{ scope.row.supplier.name }}
        </template>
      </el-table-column>
      <el-table-column label="登录账号" align="center">
        <template slot-scope="scope">
          {{ scope.row.supplier.user.username }}
        </template>
      </el-table-column>
      <el-table-column label="定价系数" align="center">
        <template slot-scope="scope">
          {{ scope.row.ratio }} %
          <el-popover trigger="click" placement="top" width="150">
            <el-input class="input" v-model="ratio"></el-input>
            <el-button type="text" slot="reference" icon="el-icon-edit" style="padding: 0 !important;"></el-button>
            <el-button size="mini" @click="changeRatio(scope.row.id)">确定</el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="指导价系数" align="center">
        <template slot-scope="scope">
          {{ scope.row.guide_ratio }} %
          <el-popover trigger="click" placement="top" width="150">
            <el-input class="input" v-model="guide_ratio"></el-input>
            <el-button type="text" slot="reference" icon="el-icon-edit" style="padding: 0 !important;"></el-button>
            <el-button size="mini" @click="changeRatio(scope.row.id)">确定</el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="销售价系数" align="center">
        <template slot-scope="scope">
          {{ scope.row.origin_ratio }} %
          <el-popover trigger="click" placement="top" width="150">
            <el-input class="input" v-model="origin_ratio"></el-input>
            <el-button type="text" slot="reference" icon="el-icon-edit" style="padding: 0 !important;"></el-button>
            <el-button size="mini" @click="changeRatio(scope.row.id)">确定</el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" class="color-red" @click="del(scope.row.supplier_id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="pagination"
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="page"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>
    <add-supplier ref="addSupplier" @getSupplier="init()"></add-supplier>
  </m-card>
</template>

<script>
import { getSuppliersList, updateSupplier, delSupplier } from '@/api/thousandPrice'
import { getSupplierOptionList } from '@/api/goods'

import addSupplier from '../components/addSupplier.vue'
export default {
  name: 'supplierRatio',
  components: { addSupplier },

  data() {
    return {
      searchForm: {
        supplier_id: null, // 商品名称
      },
      page: 1, // 当前页数
      pageSize: 10, // 每页条数
      total: null, // 总数
      supplierTable: [], // 供应商列表数据
      supplierOptionList: [], // 供应商列表
      ratio: null, // 定价系数
      guide_ratio: null, // 指导价系数
      origin_ratio: null, // 销售价系数
    }
  },
  mounted() {
    this.getSupplierOptionList()
  },
  methods: {
    // 添加供应商
    addSupplier() {
      this.$refs.addSupplier.drawer = true
      this.$refs.addSupplier.getSupplierCategoryList()
      this.$refs.addSupplier.getSupplierList()
    },
    // 获取供应商
    async getSupplierOptionList() {
      const res = await getSupplierOptionList()
      this.supplierOptionList = res.data.list
    },
    // 获取供应商列表
    async init() {
      const id = window.location.href.split('=')[1]
      const params = {
        id: parseInt(id),
        page: this.page,
        pageSize: this.pageSize,
        supplier_id: this.searchForm.supplier_id,
      }
      const res = await getSuppliersList(params)
      if (res.code === 0) {
        this.supplierTable = res.data.list
        this.total = res.data.total
      }
    },
    // 搜索
    search() {
      this.page = 1
      this.init()
    },
    // 确定修改定价系数
    async changeRatio(val) {
      const data = {
        id: parseInt(val),
        ratio: parseInt(this.ratio),
        guide_ratio: parseInt(this.guide_ratio),
        origin_ratio: parseInt(this.origin_ratio),
      }
      if (this.ratio || this.guide_ratio || this.origin_ratio) {
        const res = await updateSupplier(data)
        if (res.code === 0) {
          this.$message.success(res.msg)
          this.ratio = null
          this.guide_ratio = null
          this.origin_ratio = null
          this.init()
        }
      } else {
        this.$message.error('定价系数不能为空')
      }
    },
    // 删除
    async del(val) {
      const id = window.location.href.split('=')[1]
      const params = {
        id: parseInt(id),
        supplier_id: val,
      }
      const res = await delSupplier(params)
      if (res.code === 0) {
        this.$message.success('删除成功')
        this.init()
      }
    },
    // 每页条数
    handleSizeChange(val) {
      this.page = 1
      this.pageSize = val
      this.init()
    },
    // 当前页数
    handleCurrentChange(val) {
      this.page = val
      this.init()
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-table__row {
  height: 60px;
}
.input {
  width: 55%;
  margin-right: 5px;
  ::v-deep .el-input__inner {
    height: 30px;
  }
}
.pagination {
  float: right;
}
.color-red {
  color: red;
}
</style>
