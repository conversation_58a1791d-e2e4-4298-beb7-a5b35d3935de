<template>
  <m-card>
    <el-button type="primary" @click="addCategory">添加分类定价策略</el-button>
    <el-form :model="searchForm" label-width="100px">
      <el-row :gutter="10">
        <el-col :span="5">
          <el-form-item label="一级分类:" prop="category1_id">
            <el-select
              v-model="searchForm.category1_id"
              placeholder="请选择一级分类"
              filterable
              clearable
              @change="handleClassiyfChange(2, searchForm.category1_id)"
            >
              <el-option v-for="item in categoryList1" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="二级分类:" prop="category2_id">
            <el-select
              v-model="searchForm.category2_id"
              placeholder="请选择二级分类"
              filterable
              clearable
              @change="handleClassiyfChange(3, searchForm.category2_id)"
            >
              <el-option v-for="item in categoryList2" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="三级分类:" prop="category3_id">
            <el-select v-model="searchForm.category3_id" placeholder="请选择三级分类" filterable clearable>
              <el-option v-for="item in categoryList3" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-button type="primary" @click="search">搜索</el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-table :data="tableData">
      <el-table-column label="一级分类" align="center">
        <template slot-scope="scope">
          {{ scope.row.category_1.name }}
        </template>
      </el-table-column>
      <el-table-column label="二级分类" align="center">
        <template slot-scope="scope">
          {{ scope.row.category_2.name }}
        </template>
      </el-table-column>
      <el-table-column label="三级分类" align="center">
        <template slot-scope="scope">
          {{ scope.row.category_3.name }}
        </template>
      </el-table-column>
      <el-table-column label="定价系数" align="center">
        <template slot-scope="scope">
          {{ scope.row.ratio }} %
          <el-popover trigger="click" placement="top" width="150">
            <el-input class="input" v-model="ratio"></el-input>
            <el-button type="text" slot="reference" icon="el-icon-edit"></el-button>
            <el-button size="mini" @click="changeRatio(scope.row.id)">确定</el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" class="color-red" @click="del(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="pagination"
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="page"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>
    <add-category ref="addCategory" @getCategory="init()"></add-category>
  </m-card>
</template>

<script>
import { getClassify } from '@/api/classify'
import { getCategoryList, updateCategory, delCategory } from '@/api/thousandPrice'
import addCategory from '../components/addCategory.vue'
export default {
  name: 'categoryRatio',
  components: { addCategory },
  data() {
    return {
      searchForm: {
        category1_id: '', // 一级分类
        category2_id: '', // 二级分类
        category3_id: '', // 三级分类
      },
      // 类目1
      categoryList1: [],
      categoryList2: [],
      categoryList3: [],
      page: 1, // 当前页数
      pageSize: 10, // 每页条数
      total: null, // 总数
      tableData: [], // 表格分类列表
      ratio: null, // 定价系数
    }
  },

  mounted() {
    this.getClassify()
  },

  methods: {
    // 获取分类定价列表
    // async init() {
    //   const id = window.location.href.split('=')[1]
    //   const params = {
    //     id: parseInt(id),
    //     page: this.page,
    //     pageSize: this.pageSize,
    //     category_1_id: this.searchForm.category1_id,
    //     category_2_id: this.searchForm.category2_id,
    //     category_3_id: this.searchForm.category3_id,
    //   }
    //   const res = await getCategoryList(params)
    //   if (res.code === 0) {
    //     this.tableData = res.data.list
    //     this.total = res.data.total
    //   }
    // },
    // 添加分类定价弹层
    addCategory() {
      this.$refs.addCategory.drawer = true
      // this.$refs.addCategory.getClassify()
      this.$refs.addCategory.getCategory()
    },
    // 获取分类
    async getClassify() {
      const res = await getClassify(1, 0)
      this.categoryList1 = res.data.list
    },
    // 获取类目
    handleClassiyfChange(level, pid) {
      getClassify(level, pid).then(r => {
        if (level === 3) {
          this.categoryList3 = []
          this.searchForm.category3_id = ''
        } else {
          this.categoryList2 = []
          this.searchForm.category2_id = ''
          this.categoryList3 = []
          this.searchForm.category3_id = ''
        }
        switch (level) {
          case 2:
            this.categoryList2 = r.data.list
            break
          case 3:
            this.categoryList3 = r.data.list
            break
        }
      })
    },
    // 搜索
    search() {
      this.page = 1
      this.init()
    },
    // 修改定价系数
    async changeRatio(id) {
      const data = {
        id,
        ratio: parseInt(this.ratio),
      }
      if (this.ratio) {
        const res = await updateCategory(data)
        if (res.code === 0) {
          this.$message.success(res.msg)
          this.init()
          this.ratio = null
        }
      } else {
        this.$message.error('定价系数不能为空')
      }
    },
    // 删除分类
    async del(id) {
      const params = {
        id,
      }
      const res = await delCategory(params)
      if (res.code === 0) {
        this.$message.success('删除成功')
        this.init()
      }
    },
    // 每页条数
    handleSizeChange(val) {
      this.page = 1
      this.pageSize = val
      this.init()
    },
    // 当前页数
    handleCurrentChange(val) {
      this.page = val
      this.init()
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-table__row {
  height: 60px;
}
.input {
  width: 55%;
  margin-right: 5px;
  ::v-deep .el-input__inner {
    height: 30px;
  }
}
.pagination {
  float: right;
}
.color-red {
  color: red;
}
</style>
