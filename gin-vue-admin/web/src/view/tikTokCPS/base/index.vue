<template>
    <m-card>
        <el-tabs v-model="activeName" type="card">
            <el-tab-pane label="抖音cps" name="douyin">
                <el-form ref="form" label-width="140px">
                    <el-form-item label="抖音cps订单同步">
                        <el-radio-group v-model="formData.sync_order">
                            <el-radio :label="0">否</el-radio>
                            <el-radio :label="1">是</el-radio>
                        </el-radio-group>
                        <p class="color-grap">关闭后,将不再同步订单给采购端上级</p>
                    </el-form-item>
                    <el-form-item label="secunitKey:" prop="security_key">
                        <el-input
                            style="width: 200px"
                            v-model="formData.security_key"
                            placeholder="请输入"
                            show-password
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="应用Id:" prop="role_id">
                        <el-input-number
                            class="number-text-left"
                            :controls="false"
                            :precision="0"
                            :min="0"
                            v-model="formData.role_id"
                            placeholder="请输入"
                            clearable
                        ></el-input-number>
                    </el-form-item>
                </el-form>
            </el-tab-pane>
            <el-tab-pane label="电商cps" name="dianshang">
                <el-form :model="formData" label-width="180px">
                    <p class="title-p mt_15">基础信息</p>
                    <el-divider></el-divider>
                    <el-row>
                        <el-col :span="13">
                            <el-form-item label="appkey:">
                                <el-input v-model="formData.ec_cps_app_key"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="13">
                            <el-form-item label="appSecret:">
                                <el-input v-model="formData.ec_cps_app_secret"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="13">
                            <el-form-item label="host:">
                                <el-input v-model="formData.ec_cps_host"></el-input>
                                <div style="font-size: 12px; color: #dc5d5d">/supplyapi</div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </el-tab-pane>
        </el-tabs>
        <el-button style="margin-left: 10px" type="primary" @click="save">保存</el-button>
    </m-card>
</template>

<script>
import { getSetting, saveSetting } from '@/api/tikTokGps';
export default {
    name: 'tikTokCPSBase',
    data() {
        return {
            activeName: 'douyin',
            formData: {
                sync_order: null,
                security_key: '',
                role_id: '',

                ec_cps_app_key: '',
                ec_cps_app_secret: '',
                ec_cps_host: ''
            },
        };
    },
    mounted() {
        this.getSetting()
    },
    methods: {
        async getSetting() {
            let res = await getSetting();
            if (res.code === 0) {
                this.formData = res.data.setting.value;
                this.id = res.data.setting.id;
            }
        },
        async save() {
            let params = {
                id: this.id,
                value: {
                    ...this.formData
                }
            }
            let res = await saveSetting(params)
            if (res.code === 0) {
                this.$message.success(res.msg);
            }
        },
    },
};
</script>

<style scoped>

p.title-p {
    font-size: 16px;
    font-weight: bold;
}
</style>