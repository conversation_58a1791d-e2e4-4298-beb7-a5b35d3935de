<template>
    <m-card>
        <el-tabs v-model="activeName" type="card">
            <el-tab-pane label="抖音cps" name="douyin">
              <douyinBase ref="douyinBase"></douyinBase>
            </el-tab-pane>
            <el-tab-pane label="电商cps" name="dianshang">
              <dianshangBase ref="douyinBase"></dianshangBase>
            </el-tab-pane>
        </el-tabs>
    </m-card>
</template>

<script>
import douyinBase from './components/douyinBase.vue';
import dianshangBase from './components/dianshangBase.vue'
export default {
    name: 'tikTokCPSBase',
    components: {
        douyinBase,dianshangBase
    },
    data() {
        return {
            activeName: 'douyin',
        };
    },
    mounted() {
      this.$refs.douyinBase.getSetting()
    },
    methods: {},
};
</script>

<style scoped>

</style>