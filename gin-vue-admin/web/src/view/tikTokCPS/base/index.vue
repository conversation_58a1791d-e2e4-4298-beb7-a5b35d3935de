<template>
  <m-card>
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane label="基础设置" name="first">
        <el-form ref="form" label-width="140px">
          <el-form-item label="抖音cps订单同步">
            <el-radio-group v-model="formData.sync_order">
              <el-radio :label="0">否</el-radio>
              <el-radio :label="1">是</el-radio>
            </el-radio-group>
            <p class="color-grap">关闭后,将不再同步订单给采购端上次</p>
          </el-form-item>
          <el-form-item label="secunitKey:" prop="security_key">
            <el-input style="width: 200px" v-model="formData.security_key" placeholder="请输入" show-password></el-input>
          </el-form-item>
          <el-form-item label="应用Id:" prop="role_id">
                        <el-input-number class="number-text-left" :controls="false" :precision="0" :min="0"
                                         v-model="formData.role_id"
                                         placeholder="请输入" clearable>
                        </el-input-number>
          </el-form-item>
<!--          <el-form-item label="成分比例">-->
<!--          <el-form-item label="普通等级:" prop="settle_days">-->
<!--            <el-input-number class="number-text-left" :controls="false" :precision="0" :min="0"-->
<!--                             v-model="formData.settle_days"-->
<!--                             placeholder="请输入" clearable>-->
<!--            </el-input-number>-->
<!--            <span class="ml10">%</span>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="会员等级1:" prop="settle_days" style="margin-top: 10px">-->
<!--            <el-input-number class="number-text-left" :controls="false" :precision="0" :min="0"-->
<!--                             v-model="formData.settle_days"-->
<!--                             placeholder="请输入" clearable>-->
<!--            </el-input-number>-->
<!--            <span class="ml10">%</span>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="会员等级2:" prop="settle_days" style="margin-top: 10px">-->
<!--            <el-input-number class="number-text-left" :controls="false" :precision="0" :min="0"-->
<!--                             v-model="formData.settle_days"-->
<!--                             placeholder="请输入" clearable>-->
<!--            </el-input-number>-->
<!--            <span class="ml10">%</span>-->
<!--          </el-form-item>-->
<!--          </el-form-item>-->
        </el-form>
      </el-tab-pane>
      <el-button style="margin-left: 10px" type="primary" @click="save">保存</el-button>
    </el-tabs>
  </m-card>
</template>

<script>
import {getSetting,saveSetting} from "@/api/tikTokGps";

export default {
  name: "tikTokCPSBase",
      data() {
        return {
          activeName: 'first',
          formData: {
            id: null,
            role_id:null,
            security_key:"",
            sync_order:null,
          }
        }
      },
  mounted() {
    this.getSetting()
  },
  methods:{
     getSetting(){
       getSetting().then(res=>{
         if(res.code===0){
           if (res.data.setting.id) {
             this.formData.id=res.data.setting.id
           }
           this.formData.role_id=res.data.setting.value.role_id
           this.formData.security_key=res.data.setting.value.security_key
           this.formData.sync_order=res.data.setting.value.sync_order
         }
       })
     },

     //保存
     save(){
       let param = {
         value: {
           role_id: this.formData.role_id,
           security_key: this.formData.security_key,
           sync_order: this.formData.sync_order
         }
       }
       if (this.formData.id) {
         param.id = this.formData.id
       }
       saveSetting(param).then(res => {
         if (res.code === 0) {
           this.$message.success(res.msg)
         } else {
           this.$message.error(res.msg)
         }
       })
     }
   }


}
</script>

<style scoped>

</style>