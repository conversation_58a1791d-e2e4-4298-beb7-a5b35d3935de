<template>
    <m-card class="search-box">
        <el-form ref="ref" :model="formData" class="search-term" label-width="130px" inline>
            <el-form-item prop="application_id">
                <div class="line-input">
                    <div class="line-box ">
                        <span>采购端</span>
                    </div>
                    <el-select v-model="formData.application_id" class="w100" clearable filterable>
                        <el-option :label="item.app_name" :value="item.id" v-for="item in supplyOptions">
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item prop="status">
                <div class="line-input">
                    <div class="line-box ">
                        <span>订单状态</span>
                    </div>
                    <el-select v-model="formData.status" class="w100" clearable>
                        <el-option label="全部" value=""></el-option>
                        <el-option :label="item.name" :value="item.value"
                            v-for="item in orderSupplierConditions"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <el-input placeholder="请输入" v-model="formData[goodsCommand]" class="line-input" clearable>
                    <template slot="prepend">
                        <el-dropdown class="el-dropdown-row" @command="handleCommand">
                            <span class="el-dropdown-link">
                                {{ returnGoodsValue(goodsCommand) }}<i class="el-icon-arrow-down el-icon--right"></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item command="order_sn">订单编号</el-dropdown-item>
                                <el-dropdown-item command="root_order_id">抖音订单号</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </template>
                </el-input>
            </el-form-item>
            <el-form-item prop="key_word">
                <el-input placeholder="请输入" v-model="formData.key_word" class="line-input" clearable>
                    <span slot="prepend">手机号/昵称</span>
                </el-input>
            </el-form-item>
            <el-form-item prop="user_id">
                <el-input placeholder="请输入" v-model="formData.user_id" class="line-input" clearable>
                    <span slot="prepend">采购端平台会员ID</span>
                </el-input>
            </el-form-item>
            <el-form-item prop="dateType">
                <div class="line-input">
                    <div class="line-box ">
                        <span>时间类型</span>
                    </div>
                    <el-select v-model="dateType" class="w100">
                        <el-option v-for="item in dateTypeOptios" :key="item.id" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <div class="line-input" style="width: 586px;">
                    <div class="f fac ">
                        <el-date-picker class="w100" v-model="formData.start_at" type="datetime" placeholder="开始日期">
                        </el-date-picker>
                        <span class="zih-span">至</span>
                        <el-date-picker class="w100" v-model="formData.end_at" type="datetime" placeholder="结束日期">
                        </el-date-picker>
                    </div>
                </div>
            </el-form-item>
            <el-form-item label-width="0px">
                <div class="f fac dateBtnBox">
                    <span @click="handleDateTab(item)" v-for="item in dateList"
                        :class="time_type === item.value ? 'is_active' : ''" :key="item.id">{{ item.name }}</span>
                </div>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="searchOrder()">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <el-button @click="exportOrderList">导出</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="text" @click="clearSearchCondition()">重置搜索条件</el-button>
            </el-form-item>
        </el-form>
        <div class="table-box">
            <el-table :data="[{}]" class="table-head">
                <el-table-column label="商品" width="300"></el-table-column>
                <el-table-column label="支付金额/预估结算金额/推广费率" width="200" align="center"></el-table-column>
                <el-table-column label="手机号(ID)/会员昵称" width="200" align="center"></el-table-column>
                <el-table-column label="预估佣金/预估技术服务费" width="200" align="center"></el-table-column>
                <el-table-column label="分成基数/分成比例/分成金额" width="250" align="center"></el-table-column>
                <el-table-column label="订单状态" width="200" align="center"></el-table-column>
                <el-table-column label="操作" align="center"></el-table-column>
            </el-table>
            <div v-for="item in orderList" :key="item.id">
                <el-table :data="[item]" class="table-cont" :span-method="objectSpanMethod">
                    <el-table-column>
                        <template slot="header" slot-scope="scope">
                            <div class="w100 f fac fjsb">
                                <div class="f fac fw">
                                    <p>订单ID: {{ item.id }}</p>
                                    <p>订单编号: {{ item.order_sn }}</p>
                                    <!-- <p>第三方订单编号: {{ item.order_sn }}</p> -->
                                    <p>抖音订单号: {{ item.root_order_id }}</p>
                                    <p>抖音订单状态: {{ item.status | formatTiktokStatus }}</p>
                                    <P>支付时间：{{ timestampToTime(item.pay_time) }}</P>
                                </div>
                            </div>
                        </template>
                        <el-table-column width="300">
                            <template slot-scope="scope">
                                <div class="f fac goods-box">
                                    <m-image style="width: 60px;height:60px" :src="scope.row.product_list[0].img">
                                    </m-image>
                                    <div class="f1">
                                        <p class="hiddenText2">
                                            <a href="javascript:;" style="color: #155bd4;">
                                                {{ scope.row.product_list[0].name }}
                                            </a>
                                        </p>
                                        <p style="color: #a7a7a7;">
                                            规格: {{ scope.row.sku_title }}
                                        </p>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column width="210">
                            <template slot-scope="scope">
                                <div class="comm-box" style="width: 85%;">
                                    <p>支付金额: {{ scope.row.pay_amount | formatF2Y }}</p>
                                    <p>预估结算金额: {{ scope.row.pay_amount | formatF2Y }}</p>
                                    <p>推广费率: {{ scope.row.commission_rate / 100 }} %</p>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column width="200">
                            <template slot-scope="scope">
                                <div class="comm-box">
                                    <p class="title-3">
                                        <span style="color: #155bd4; cursor:pointer">
                                            {{ item.user.username }}({{ item.user.id }})
                                        </span>
                                    </p>
                                    <p class="title-3">{{ item.user ? item.user.nickname : '' }}</p>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column width="230">
                            <template slot-scope="scope">
                                <div class="comm-box">
                                    <p>预估佣金: {{ scope.row.commission_amount | formatF2Y }}</p>
                                    <p>结算金额: {{ scope.row.settle_amount | formatF2Y }}</p>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column width="250">
                            <template slot-scope="scope">
                                <div class="comm-box" style="width:85%">
                                    <p>分成基数:{{ scope.row.award_base | formatF2Y }} </p>
                                    <p>分成比例: {{ scope.row.award_ratio / 100 }} % </p>
                                    <p>分成金额: {{ scope.row.award_amount | formatF2Y }}</p>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column width="200">
                            <template slot-scope="scope">
                                <div class="comm-box">
                                    <p class="title-3">
                                        {{ scope.row.supply_status | formatStatus }}
                                    </p>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column>
                            <template slot-scope="scope">
                                <div class="title-3">
                                    <el-button type="text" @click="orderOperationDialog(item.order_id)">订单同步
                                    </el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table-column>
                </el-table>
                <div class="table-foot-box">
                    <div class="f fac">
                        <p>采购端会员id: {{ item.user_id }}</p>
                        <p>pid: {{ item.pid }}</p>
                        <p>external_info: {{ item.command_external_info }}</p>
                    </div>
                </div>
            </div>
            <el-pagination background :current-page="page" :page-size="pageSize" :page-sizes="[10, 30, 50, 100]"
                :style="{ display: 'flex', justifyContent: 'flex-end', marginRight: '20px' }" :total="total"
                @current-change="handleCurrentChange" @size-change="handleSizeChange"
                layout="total, sizes, prev, pager, next, jumper"></el-pagination>
        </div>
    </m-card>
</template>
<script>
import { getOrderList, syncDouYinGroupOrderByOrderId, exportOrderList } from "@/api/tiktokGroupon";
import { getApplicationOption } from "@/api/tikTokGps";
export default {
    name: "tiktokGrouponOrderManage",
    data() {
        return {
            // 分页部分
            page: 1,
            pageSize: 10,
            total: 0,
            // 列表
            orderList: [],
            //时间处理
            formData: {
                application_id: null,
                status: "",
                user_id: null,
                key_word: "",
                start_at: "",
                end_at: "",
                order_sn: '',
                root_order_id: ''
            },
            dateType: 1, // 日期类型 1付款时间 2结算时间 3退款时间
            dateTypeOptios: [
                { label: "付款时间", value: 1 },
                { label: "结算时间", value: 2 },
                { label: "退款时间", value: 3 },
            ],
            orderSupplierConditions: [
                { name: "已支付", value: 1 },
                { name: "已退款", value: 2 },
                { name: "已核销", value: 3 },
                { name: "部分核销", value: 4 },
                { name: "部分退款", value: 5 },
            ],
            supplyOptions: [],
            time_type: "",
            dateList: [
                { name: "今", value: 0 },
                { name: "昨", value: 1 },
                { name: "近7天", value: 2 },
                { name: "近30天", value: 3 },
            ],
            goodsCommand: 'order_sn'
        }
    },
    filters: {
        // 格式化订单状态
        formatStatus: function (status) {
            let name = ""
            switch (status) {
                case 0:
                    name = "待分成"
                    break;
                case 1:
                    name = "已分成"
                    break;
            }
            return name;
        },
        // 格式化抖音订单状态
        formatTiktokStatus: function (status) {
            let name = ""
            switch (status) {
                case 1:
                    name = "已支付"
                    break;
                case 2:
                    name = "已退款"
                    break;
                case 3:
                    name = "已核销"
                    break;
                case 4:
                    name = '部分核销'
                case 5:
                    name = '部分推广'
            }
            return name;
        }
    },
    mounted() {
        this.getOrderList()
        this.getApplicationOption()

    },
    methods: {
        // 时间戳：1637244864707
        /* 时间戳转换为时间 */
        timestampToTime(timestamp) {
            timestamp = timestamp ? timestamp : null;
            let date = new Date(timestamp * 1000);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
            let Y = date.getFullYear() + '-';
            let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
            let D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';
            let h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
            let m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
            let s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
            return Y + M + D + h + m + s;
        },
        // 获取采购段列表
        getApplicationOption() {
            getApplicationOption().then(res => {
                if (res.code === 0) {
                    this.supplyOptions = res.data.list
                }
            })
        },
        //获取列表
        getOrderList() {
            let data = {
                "page": this.page,
                "pageSize": this.pageSize,
                "time_type": this.dateType,
            }

            if (this.formData.application_id !== '' && this.formData.application_id) {
                data.application_id = this.formData.application_id
            }

            if (this.formData.status !== '' && this.formData.status) {
                data.status = this.formData.status
            }

            if (this.formData.user_id !== '' && this.formData.user_id) {
                data.user_id = parseInt(this.formData.user_id) 
            }

            if (this.formData.key_word !== '' && this.formData.key_word) {
                data.key_word = this.formData.key_word
            }

            if (this.formData.order_sn !== '' && this.formData.order_sn) {
                data.order_sn = parseInt(this.formData.order_sn) 
            }

            if (this.formData.root_order_id !== '' && this.formData.root_order_id) {
                data.root_order_id = this.formData.root_order_id
            }

            if (this.formData.start_at !== '' && this.formData.start_at) {
                data.start_at = String(new Date(this.formData.start_at).getTime() / 1000)
            }

            if (this.formData.end_at !== '' && this.formData.end_at) {
                data.end_at = String(new Date(this.formData.end_at).getTime() / 1000)
            }

            getOrderList(data).then(res => {
                if (res.code === 0) {
                    this.orderList = res.data.list
                    this.total = res.data.total;
                } else {
                    this.orderList = []
                    this.total = 0;
                }
            })
        },
        // 筛选名字
        returnGoodsValue(command) {
            let text = ""
            switch (command) {
                case "root_order_id":
                    text = "抖音订单号"
                    break;
                case "order_sn":
                    text = "订单编号"
                    break;
                default:
                    break;
            }
            return text
        },
        // 切换订单
        handleCommand(command) {
            this.goodsCommand = command;
            this.formData.order_sn = '';
            this.formData.root_order_id = '';
        },
        //操作按钮
        orderOperationDialog(order_id) {
            this.$confirm('确认要同步订单?, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                syncDouYinGroupOrderByOrderId({ order_id }).then(res => {
                    if (res.code == 0) {
                        this.$message.success(res.msg);
                    }
                })
            }).catch(() => { })
        },
        //搜索
        searchOrder() {
            this.page = 1
            this.getOrderList()
        },
        // 订单导出
        async exportOrderList() {
            let data = {}

            if (this.formData.application_id !== '' && this.formData.application_id) {
                data.application_id = this.formData.application_id
            }

            if (this.formData.status !== '' && this.formData.status) {
                data.status = this.formData.status
            }

            if (this.formData.user_id !== '' && this.formData.user_id) {
                data.user_id = parseInt(this.formData.user_id) 
            }

            if (this.formData.key_word !== '' && this.formData.key_word) {
                data.key_word = this.formData.key_word
            }

            if (this.formData.order_sn !== '' && this.formData.order_sn) {
                data.order_sn = parseInt(this.formData.order_sn)
            }

            if (this.formData.root_order_id !== '' && this.formData.root_order_id) {
                data.root_order_id = this.formData.root_order_id
            }

            if (this.formData.start_at !== '' && this.formData.start_at) {
                data.start_at = String(new Date(this.formData.start_at).getTime() / 1000)
            }

            if (this.formData.end_at !== '' && this.formData.end_at) {
                data.end_at = String(new Date(this.formData.end_at).getTime() / 1000)
            }
            if (data.end_at && data.start_at) {
                data.time_type = this.dateType
            }
            if (data.end_at && !data.start_at) {
                this.$message.error('请输入开始时间')
                return
            }
            if (data.start_at && !data.end_at) {
                this.$message.error('请输入结束时间')
                return
            }

            let res = await exportOrderList(data)
            if (res.code === 0) {
                window.open(this.$path + '/' + res.data.link)
            } else {
                this.$message.error(res.msg)
            }
        },
        //重置
        clearSearchCondition() {
            this.$refs.ref.resetFields()
            this.formData.order_sn = '';
            this.formData.root_order_id = '';
            this.formData.start_at = '';
            this.formData.end_at = '';
            this.time_type = "";
            this.goodsCommand = 'order_sn'
            this.dateType = 1;
            this.getOrderList()

        },
        // 切换日期
        handleDateTab(item) {
            this.time_type = this.time_type === item.value ? "" : item.value;
            const todayDate = new Date();
            switch (this.time_type) {
                case 0:
                    const dateToday1 = new Date();
                    dateToday1.setHours(0);
                    dateToday1.setMinutes(0);
                    dateToday1.setSeconds(0);
                    this.formData.start_at = dateToday1;
                    todayDate.setHours(23);
                    todayDate.setMinutes(59);
                    todayDate.setSeconds(59);
                    this.formData.end_at = todayDate;
                    break;
                case 1:
                    const dateYesterday1 = new Date();
                    dateYesterday1.setTime(dateYesterday1.getTime() - 3600 * 1000 * 24 * 1);
                    dateYesterday1.setHours(0);
                    dateYesterday1.setMinutes(0);
                    dateYesterday1.setSeconds(0);
                    this.formData.start_at = dateYesterday1;

                    const dateYesterday2 = new Date();
                    dateYesterday2.setTime(dateYesterday2.getTime() - 3600 * 1000 * 24 * 1);
                    dateYesterday2.setHours(23);
                    dateYesterday2.setMinutes(59);
                    dateYesterday2.setSeconds(59);
                    this.formData.end_at = dateYesterday2;
                    break;
                case 2:
                    const date7Day1 = new Date();
                    date7Day1.setTime(date7Day1.getTime() - 3600 * 1000 * 24 * 7);
                    date7Day1.setHours(0);
                    date7Day1.setMinutes(0);
                    date7Day1.setSeconds(0);
                    this.formData.start_at = date7Day1;

                    todayDate.setHours(23);
                    todayDate.setMinutes(59);
                    todayDate.setSeconds(59);
                    this.formData.end_at = todayDate;
                    break;
                case 3:
                    const date30Day1 = new Date();
                    date30Day1.setTime(date30Day1.getTime() - 3600 * 1000 * 24 * 30);
                    date30Day1.setHours(0);
                    date30Day1.setMinutes(0);
                    date30Day1.setSeconds(0);
                    this.formData.start_at = date30Day1;

                    todayDate.setHours(23);
                    todayDate.setMinutes(59);
                    todayDate.setSeconds(59);
                    this.formData.end_at = todayDate;
                    break;

                default:
                    break;
            }
        },
        // 下一页
        handleCurrentChange(page) {
            this.page = page
            this.getOrderList()
        },

        // 每页显示条数
        handleSizeChange(size) {
            this.pageSize = size
            this.getOrderList()
        },
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            if (columnIndex >= 2) {
                if (rowIndex % 9999 === 0) {
                    return {
                        rowspan: 9999,
                        colspan: 1
                    };
                } else {
                    return {
                        rowspan: 0,
                        colspan: 0
                    };
                }
            }
        },
    }
}
</script>

<style lang="scss" scoped>
@import "@/style/base.scss";

// 搜索部分
.search-term {
    .dateBtnBox {
        height: 36px;
        line-height: 36px;

        span {
            height: 27px;
            line-height: 22px;
            display: inline-block;
            margin-right: 10px;
            padding: 2px 4px;
            border: 1px solid #dcdee0;
            color: #c8c9cc;
            cursor: pointer;
            box-sizing: border-box;

            &:last-child {
                margin-right: 0;
            }

            &:hover {
                color: #155bd4;
                border-color: #155bd4;
                background-color: #fff;
            }

            &.is_active {
                color: #155bd4;
                border-color: #155bd4;
                background-color: #fff;
            }
        }
    }
}

/* 订单列表部分开始 */
.table-title-box {
    p.title-p {
        margin-right: 20px;
    }

    span {
        font-size: 12px;
        color: #c1c1c1;
        margin-right: 10px;
    }
}

.table-box {
    margin-top: 25px;
    // 订单item
    /* .table-item {
        border: 1px solid #e8e8e8;
        border-radius: 5px;
        margin-bottom: 10px;
        // 表头
        .table-head {
            border-bottom: 1px solid #e8e8e8;
            background-color: #fafafa;
            color: #888787;
            font-weight: bold;
            padding: 10px 0;
            a.close-order {
                display: inline-block;
                margin-right: 20px;
            }
            div {
                p {
                    margin-left: 10px;
                    &.supplier-p {
                        background-color: rgb(74, 197, 156);
                        padding: 10px;
                        color: #ffffff;
                        border-radius: 3px;
                    }
                }
            }
        }
        .table-cont {
            .el-row {
                padding: 0;
                .el-col {
                    border-right: 1px solid #e8e8e8;
                    padding: 10px 0;
                    height: 120px;
                    &:last-child {
                        border-right: none;
                    }
                    &.goods-box {
                        img {
                            width: 100px;
                            height: 100px;
                            margin: 0 10px;
                        }
                    }
                    .comm-box {
                        p {
                            margin-bottom: 10px;
                            &:last-child {
                                margin-bottom: 0;
                            }
                        }
                    }
                }
            }
        }
        // 收货人信息
        .table-foot {
            border-top: 1px solid #e8e8e8;
            padding: 10px 0;
            p {
                margin-left: 10px;
                &.addr-p {
                    span {
                        margin-right: 5px;
                    }
                }
            }
        }
    } */
}

/* 订单列表部分结束 */
::v-deep .tabs-box {
    padding-left: 20px;
    padding-right: 20px;

    .el-radio-group {
        .el-radio {
            width: 95px;
            height: 37px;
            line-height: 37px;
            text-align: center;

            .el-radio__input {
                display: none;
            }

            .el-radio__label {
                padding: 0;
            }

            &.is-checked {
                background-color: #13c7a7;
                border-radius: 50px;

                .el-radio__label {
                    color: white;
                }
            }
        }
    }
}

.search-box {

    // padding: 20px;
    ::v-deep .el-date-editor.el-range-editor.el-input__inner {
        padding: 0 10px;
    }
}

.green-color {
    color: #4ac59c;
}

.order-card {
    margin-left: 1px;
    border-top: 0;
}

/***************************** tabs部分 *******************************/
::v-deep .order-tabs {
    .el-tabs__header {
        margin-bottom: 0px;

        .el-tabs__item {
            background-color: #f7f8fa;

            &.is-active {
                color: #303133;
                background-color: #ffffff;
            }

            &:hover {
                color: #303133;
            }
        }
    }
}

/***************************** 表格部分 *******************************/
::v-deep .el-table.table-head {
    margin-bottom: 25px;

    &::before {
        display: none;
    }

    .el-table__header-wrapper {
        tr th {
            background-color: #f7f8fa !important;
            border-bottom: 0;
        }
    }

    .el-table__body-wrapper {
        display: none;
    }
}

::v-deep .el-table.table-cont.el-table--border {
    border: 1px solid #efefef !important;
}

::v-deep .el-table.table-cont {
    margin-bottom: 0;

    thead {
        tr th {
            background-color: #f7f8fa !important;
        }

        tr:last-child {
            display: none;
        }

        tr:first-child {
            th {
                p {
                    margin-right: 20px;

                    &.supplier-p {
                        //background-color: rgb(74, 197, 156);
                        padding: 10px;
                        color: #0a3cdc;
                        //border-radius: 3px;
                    }
                }
            }
        }
    }

    .el-table__body-wrapper {
        .goods-box {
            p {
                margin-left: 10px;
            }
        }

        .comm-box {
            width: 50%;
            margin: 0 auto;

            p {
                margin-bottom: 10px;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
}

.table-foot-box {
    border: 1px solid #ebeef5;
    border-top: 0;
    margin-bottom: 20px;
    padding: 10px;

    p {
        margin-left: 10px;

        &.addr-p {
            span {
                margin-right: 5px;
            }
        }

        &:first-child {
            margin-left: 0;
        }
    }
}

.vip {
    width: 200px;
}
</style>