<template>
  <m-card>
    <el-form :model="formData" label-width="130px">
      <p class="title-p">基础信息</p>
      <el-divider></el-divider>
      <el-row>
        <el-col :span="15">
          <el-form-item label="appKey:">
            <el-input v-model="formData.baseInfo.appKey"></el-input>
          </el-form-item>
        </el-col>
        <el-col
            :span="15"
            v-if="$route.query.key === 'self' || $route.query.key === 'szbao'"
        >
          <el-form-item label="host:">
            <el-input v-model="formData.baseInfo.host"></el-input>
            <div style="font-size: 12px; color: #dc5d5d">
              * {{ domain }}/supplyapi
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="15" v-if="$route.query.key === 'szbao'">
          <el-form-item label="ossHost:">
            <el-input v-model="formData.baseInfo.ossHost"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="15">
          <el-form-item label="channelID:">
            <el-input v-model="formData.baseInfo.appSecret"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="15">
          <el-form-item label="接口地址:">
            <el-input v-model="formData.baseInfo.apiUrl"></el-input>
            <div style="font-size: 12px; color: #dc5d5d">
              * 填写对方接口api域名地址,没有可忽略
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="15">
          <el-form-item label="用户ID:">
            <el-input v-model="formData.baseInfo.user_id"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="15">
          <el-form-item label="订单回调地址:">
            <div v-if="rescode == 0">{{ callBackUrl }}</div>

          </el-form-item>
          <el-form-item label="价格变更回调地址:">
            <div v-if="rescode == 0">{{ priceCallBackUrl }}</div>

          </el-form-item>
          <el-form-item label="上下架回调地址:">
            <div v-if="rescode == 0">{{ saleCallBackUrl }}</div>
            <div v-else>保存配置后自动生成</div>
            <div style="font-size: 12px; color: #dc5d5d">
              * 上面回调地址复制到对接供应链平台的后台回调地址中
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="15">
          <el-form-item label="供应链名称:">
            <el-input v-model="formData.baseInfo.storeName"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <p class="title-p">更新设置</p>
      <el-divider></el-divider>
      <el-form-item label="定时任务规则:">
        <div class="f fac">
          <el-input
              v-model="formData.update.cron"
              style="width: 30%"
          ></el-input>
        </div>
        <p class="hint-p">
          示例：0 0 03,19 * * ? 表示每天凌晨3点跟19点执行两次，具体规则自行查询
        </p>
      </el-form-item>
        <el-form-item label="自动导入商品:">
            <el-radio-group v-model="formData.update.autoImportProduct">
                <el-radio :label="1">开启</el-radio>
                <el-radio :label="2">关闭</el-radio>
            </el-radio-group>
<!--            <p class="hint-p">关闭则不更新商品供货价</p>-->
        </el-form-item>
        <el-form-item label="自动导入规则:">
            <div class="f fac">
                <el-input
                        v-model="formData.update.autoImportProductCron"
                        style="width: 30%"
                ></el-input>
            </div>
<!--            <p class="hint-p">-->
<!--                示例：0 0 03,19 * * ? 表示每天凌晨3点跟19点执行两次，具体规则自行查询-->
<!--            </p>-->
        </el-form-item>
      <el-form-item label="自动更新供货价:">
        <el-radio-group v-model="formData.update.currentPrice">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="2">关闭</el-radio>
        </el-radio-group>
        <p class="hint-p">关闭则不更新商品供货价</p>
      </el-form-item>
      <el-form-item label="自动更新分类:">
        <el-radio-group v-model="formData.update.category">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="2">关闭</el-radio>
        </el-radio-group>
        <p class="hint-p">关闭则不更新商品分类</p>
      </el-form-item>

      <!--      <el-form-item label="自动更新市场价:">-->
      <!--        <el-radio-group v-model="formData.update.originalPrice">-->
      <!--          <el-radio :label="1">开启</el-radio>-->
      <!--          <el-radio :label="2">关闭</el-radio>-->
      <!--        </el-radio-group>-->
      <!--        <p class="hint-p">关闭则不更新商品市场价</p>-->
      <!--      </el-form-item>-->
      <el-form-item label="自动更新成本价:">
        <el-radio-group v-model="formData.update.costPrice">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="2">关闭</el-radio>
        </el-radio-group>
        <p class="hint-p">关闭则不更新商品成本价</p>
      </el-form-item>
      <el-form-item label="自动更新基本信息:">
        <el-radio-group v-model="formData.update.baseInfo">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="2">关闭</el-radio>
        </el-radio-group>
        <p class="hint-p">关闭则不更新商品名称和商品详情</p>
      </el-form-item>
      <!--      <el-form-item label="去除图片水印:" v-if="$route.query.key !== 'szbao'">-->
      <!--        <el-radio-group v-model="formData.update.imageWatermark">-->
      <!--          <el-radio :label="1">开启</el-radio>-->
      <!--          <el-radio :label="2">关闭</el-radio>-->
      <!--        </el-radio-group>-->
      <!--      </el-form-item>-->
      <el-form-item label="导入时创建品牌:" v-if="$route.query.key !== 'szbao'">
        <el-radio-group v-model="formData.update.createBrand">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="2">关闭</el-radio>
        </el-radio-group>
        <p class="hint-p">
          开启后导入商品时将创建对应商品品牌并将商品绑入相应品牌
        </p>
      </el-form-item>
      <!--      定价策略-->
      <cake ref="cake" v-if="routeKey === 'cake'"></cake>
      
      <p class="title-p" v-if="$route.query.key !== 'szbao'">同步设置</p>
      <el-divider></el-divider>
      <el-form-item label="同步类目:">
        <el-checkbox-group v-model="formData.cake">
          <el-checkbox :label="1">蛋糕</el-checkbox>
          <el-checkbox :label="5">零食</el-checkbox>
          <el-checkbox :label="8">鲜花</el-checkbox>
        </el-checkbox-group>
        <p style="font-size: 12px; line-height: 20px;">默认全选，商品、品牌同步只同步勾选类目的品牌、商品；</p>
        <p style="font-size: 12px; line-height: 20px;">门店同步根据所导入的品牌来同步，如果未导入的品牌门店不导入！</p>
        <p style="font-size: 12px; color: #dc5d5d;margin-top: 20px;line-height: 20px;">保存设置讲对已导入的品牌、门店、商品数据进行处理，删除未勾选类目的品牌、门店、商品，请谨慎选择！</p>
        <p style="font-size: 12px; color: #dc5d5d;line-height: 20px;">保存设置讲对已导入的品牌、门店、商品数据进行处理，删除未勾选类目的品牌、门店、商品，请谨慎选择！</p>
      </el-form-item>

      <p class="title-p" v-if="$route.query.key !== 'szbao'">风控策略</p>
      <el-divider v-if="$route.query.key !== 'szbao'"></el-divider>
      <el-form-item label="风控策略:" v-if="$route.query.key !== 'szbao'">
        <div class="f fac blockRadio-box">
          <el-radio
              v-model="formData.management.productPriceStatus"
              :label="1"
          ></el-radio>
          <el-form-item label="产品售价 < 成本价" label-width="135px">
            <div class="f fac">
              <el-input v-model="formData.management.products"></el-input>
              <p class="f1 ml10">%</p>
            </div>
          </el-form-item>
        </div>
        <div class="f fac blockRadio-box">
          <el-radio
              v-model="formData.management.productPriceStatus"
              :label="2"
          ></el-radio>
          <el-form-item label="利润率 < 设定利润" label-width="135px">
            <div class="f fac">
              <el-input v-model="formData.management.profit"></el-input>
              <p class="f1 ml10">%</p>
            </div>
          </el-form-item>
        </div>
        <p class="hint-p">利润率 = (售价-成本价)/成本价</p>
        <p class="hint-p">满足条件的商品会执行下架</p>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="saveSetting">提交</el-button>
      </el-form-item>
    </el-form>
  </m-card>
</template>

<script>
import { getSetting, saveSetting } from "@/api/lianlian";
import cake from "../../supply/page/configComponents/cake";
export default {
  name: "cakeConfigMessage",
  components:{
    cake
  },
  data() {
    return {
      callBackUrl: "",
      priceCallBackUrl: "",
      saleCallBackUrl: "",
      domain: "",
      routeKey: this.$route.query.key || "",
      rescode: 9,
      formData: {
        baseInfo: {},
        update: {
          /* currentPrice: 2,
          originalPrice: 2,
          costPrice: 2,
          baseInfo: 2,
          imageWatermark: 2,
          createBrand: 2, */
        },
        pricing: {
          /*strategy: 1,

          JDSales: 1,
          JDSalesGuide: "100",
          JDSalesAgreement: "100",
          JDSalesMarketing: "100",

          JDCostPrice: 1,
          JDCostAgreement: "100",
          JDCostMarketing: "100",

          ALSales: 1,
          ALSalesGuide: "100",
          ALSalesAgreement: "100",
          ALSalesMarketing: "100",

          ALCost: 1,
          ALCostAgreement: "100",
          ALCostMarketing: "100",

          TMSales: 1,
          TMSalesGuide: "100",
          TMSalesAgreement: "100",
          TMSalesMarketing: "100",

          TMCost: 1,
          TMCostAgreement: "100",
          TMCostMarketing: "100",

          YCSales: 1,
          YCSalesGuide: "100",
          YCSalesAgreement: "100",
          YCSalesMarketing: "100",

          YCCost: 1,
          YCCostAgreement: "100",
          YCCostMarketing: "100",

          SupplySales: 1,
          SupplySalesGuide: "100",
          SupplySalesAgreement: "100",
          SupplySalesMarketing: "100",

          SupplyCost: 1,
          SupplyCostAgreement: "100",
          SupplyCostMarketing: "100",*/
        },
        cloud: {},
        management: {
          productPriceStatus: 1,
          products: 0,
        },
        storage_auth: {},
        cake: [1,5,8], // 同步设置
      },
    };
  },
  created() {
    // console.log("数据11：")
    getSetting({ key: "gatherSupply" + parseInt(this.$route.query.id) }).then(
        (res) => {
          try {
            this.formData = JSON.parse(res.data.data.value);
            // console.log(this.formData, "????");
            this.rescode = res.code;
            //  console.log("rescode", res.code);
            if (this.routeKey) {
              this.$refs[this.routeKey].setFrom(this.formData.pricing);
              if (this.routeKey === "stbz") {
                this.$refs.stbzCloud.setFrom(this.formData.cloud);
              }
              this.$refs[this.routeKey].disposeLevelData(
                  this.formData.storage_auth.application_level_ids
              );
            }
            /* if (this.routeKey === "stbz") {
              this.$refs.stbz.disposeLevelData(
                this.formData.storage_auth.application_level_ids
              );
            }
            if (this.routeKey === "yzh") {
              this.$refs.yzh.disposeLevelData(
                  this.formData.storage_auth.application_level_ids
              );

            } */

            this.formData.update.currentPrice =
                this.formData.update.currentPrice || 2;
            this.formData.update.originalPrice =
                this.formData.update.originalPrice || 2;
            this.formData.update.costPrice = this.formData.update.costPrice || 2;

            this.formData.update.baseInfo = this.formData.update.baseInfo || 2;
            this.formData.update.category = this.formData.update.category || 2;
            this.formData.update.imageWatermark =
                this.formData.update.imageWatermark || 2;
            this.formData.update.createBrand =
                this.formData.update.createBrand || 2;
          } catch {
          } finally {
            let ishttps =
                "https:" == document.location.protocol ? "https" : "http";
            let domain = document.domain;

            //res.data.url

            let proto = res.data.proto ? res.data.proto : "https";

            this.domain = proto + "://" + res.data.domain;
            this.callBackUrl =
                proto +
                "://" +
                res.data.domain +
                "/supplyapi/api/cake/orderStatus";
            this.priceCallBackUrl =
                proto +
                "://" +
                res.data.domain +
                "/supplyapi/api/cake/alertPrice";
            this.saleCallBackUrl =
                proto +
                "://" +
                res.data.domain +
                "/supplyapi/api/cake/alertSale";
          }

          // console.log("数据：", res.data.url)
        }
    );
  },

  methods: {
    async saveSetting() {
      if (this.routeKey) {
        //console.log(this.$refs[this.routeKey], "??????");
        this.formData.pricing = { ...this.$refs[this.routeKey].formData };
        if (this.routeKey === "stbz") {
          this.formData.cloud = { ...this.$refs.stbzCloud.formData };
        }
        let storage_auth = this.$refs[this.routeKey].getLevelData();
        this.formData.storage_auth = storage_auth;
      }

      this.formData.management.products = parseInt(
          this.formData.management.products
      );
      this.formData.management.profit = parseInt(
          this.formData.management.profit
      );

      /* if (this.routeKey === "stbz") {
        let storage_auth = this.$refs.stbz.getLevelData();
        this.formData.storage_auth = storage_auth;
      }
      if (this.routeKey === "yzh") {
        let storage_auth = this.$refs.yzh.getLevelData();
        this.formData.storage_auth = storage_auth;
      } */
      this.formData.cake = this.formData.cake? this.formData.cake: [1,5,8]
      this.formData.router_key = 'cake'
      let data = {
        key: "gatherSupply" + parseInt(this.$route.query.id),

        value: JSON.stringify(this.formData),
      };

      // console.log("请求数据：" + JSON.stringify(data))
      let res = await saveSetting(data);

      // console.log("返回数据：" + JSON.stringify(res))
      if (res.code === 0) {
        this.$message({
          type: "success",
          message: "保存成功",
        });
        console.log("成功");
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.el-form {
  .el-row {
    padding: 0;
  }

  .blockRadio-box {
    margin-top: 15px;

    & :first-child {
      margin-top: 0;
    }

    ::v-deep .el-radio {
      margin: 0;

      .el-radio__label {
        display: none;
      }
    }
  }

  p.hint-p {
    font-size: 12px;
    color: #c0c4cc;
    line-height: 20px;
  }
}

p.title-p {
  font-size: 16px;
  font-weight: bold;
}

.el-divider {
  margin: 15px 0;
}
</style>