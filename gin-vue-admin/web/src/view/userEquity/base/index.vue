<template>
  <m-card>
    <el-form v-model="formData" label-width="130px" class="system">
      <p class="title-p">基础设置</p>
      <el-divider></el-divider>
      <el-form-item label="app_id:">
        <el-input
            v-model="formData.base_info.app_id"
            placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="app_secret:">
        <el-input
            v-model="formData.base_info.app_secret"
            placeholder="请输入"
        ></el-input>
      </el-form-item>
      <!--      <el-form-item label="回调地址:">
              <span>{{ url ? url : "-" }}</span>
              <div style="font-size: 12px; color: #dc5d5d">
                * 上面回调地址复制到对接供应链平台的后台回调地址中
              </div>
            </el-form-item>-->
      <p class="title-p">更新设置</p>
      <el-divider></el-divider>
      <!-- <el-form-item label="自动更新品牌:">
        <el-radio-group v-model="formData.update.brand">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item> -->
      <!-- <el-form-item label="自动更新商品:">
        <el-radio-group v-model="formData.update_info.auto_product">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item> -->
      <el-form-item label="自动更新商品名称:">
        <el-radio-group v-model="formData.update_info.auto_product_name">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="自动更新商品价格:">
        <el-radio-group v-model="formData.update_info.auto_purchase_price">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="自动更新商品详情:">
        <el-radio-group v-model="formData.update_info.auto_details">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="自动更新商品库存:">
        <el-radio-group v-model="formData.update_info.auto_stock_status">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <p class="title-p">定价策略</p>
      <el-divider></el-divider>
      <el-form-item label="成本价:">
        <div class="f fac mt10">
          <el-radio
              v-model="formData.pricing_strategy.cost_price.compute_type"
              :label="1"
          >折扣价 X 定价系数
          </el-radio
          >
          <el-input-number
              :controls="false"
              :precision="2"
              class="input-number-text-left"
              :min="0"
              v-model="formData.pricing_strategy.cost_price.price_ratio"
          ></el-input-number>
          <p class="ml10">%</p>
        </div>
        <div class="f fac mt10">
          <el-radio
              v-model="formData.pricing_strategy.cost_price.compute_type"
              :label="2"
          >官方原价 X 定价系数
          </el-radio
          >
          <el-input-number
              :controls="false"
              :precision="2"
              class="input-number-text-left"
              :min="0"
              v-model="formData.pricing_strategy.cost_price.face_ratio"
          ></el-input-number>
          <p class="ml10">%</p>
        </div>
        <p class="hint-p">
          商品导入后，中台商品自动设置的商品成本价！ <br/>
          举例: 权益折扣价80元，官方原价100元； <br/>
          导入后商品成本价=权益折扣价 X 定价系数 即 80 X 100% = 80 元
          <br/>
          导入后商品成本价=权益官方原价 X 定价系数即 100 X 100% = 100
          元，如果无官方原价，则取折扣价
        </p>
      </el-form-item>
      <el-form-item label="供货价:">
        <div class="f fac mt10">
          <el-radio
              v-model="formData.pricing_strategy.price.compute_type"
              :label="1"
          >折扣价 X 定价系数
          </el-radio
          >
          <el-input-number
              :controls="false"
              :precision="2"
              class="input-number-text-left"
              :min="0"
              v-model="formData.pricing_strategy.price.price_ratio"
          ></el-input-number>
          <p class="ml10">%</p>
        </div>
        <div class="f fac mt10">
          <el-radio
              v-model="formData.pricing_strategy.price.compute_type"
              :label="2"
          >官方原价 X 定价系数
          </el-radio
          >
          <el-input-number
              :controls="false"
              :precision="2"
              class="input-number-text-left"
              :min="0"
              v-model="formData.pricing_strategy.price.face_ratio"
          ></el-input-number>
          <p class="ml10">%</p>
        </div>
        <p class="hint-p">商品导入后，中台商品自动设置的商品供货价！</p>
      </el-form-item>
      <el-form-item label="市场价:">
        <div class="f fac mt10">
          <el-radio
              v-model="formData.pricing_strategy.origin_price.compute_type"
              :label="1"
          >折扣价 X 定价系数
          </el-radio
          >
          <el-input-number
              :controls="false"
              :precision="2"
              class="input-number-text-left"
              :min="0"
              v-model="formData.pricing_strategy.origin_price.price_ratio"
          ></el-input-number>
          <p class="ml10">%</p>
        </div>
        <div class="f fac mt10">
          <el-radio
              v-model="formData.pricing_strategy.origin_price.compute_type"
              :label="2"
          >官方原价 X 定价系数
          </el-radio
          >
          <el-input-number
              :controls="false"
              :precision="2"
              class="input-number-text-left"
              :min="0"
              v-model="formData.pricing_strategy.origin_price.face_ratio"
          ></el-input-number>
          <p class="ml10">%</p>
        </div>
        <p class="hint-p">商品导入后，中台商品自动设置的商品市场价！</p>
      </el-form-item>
      <el-form-item label="指导价:">
        <div class="f fac mt10">
          <el-radio
              v-model="formData.pricing_strategy.guide_price.compute_type"
              :label="1"
          >折扣价 X 定价系数
          </el-radio
          >
          <el-input-number
              :controls="false"
              :precision="2"
              class="input-number-text-left"
              :min="0"
              v-model="formData.pricing_strategy.guide_price.price_ratio"
          ></el-input-number>
          <p class="ml10">%</p>
        </div>
        <div class="f fac mt10">
          <el-radio
              v-model="formData.pricing_strategy.guide_price.compute_type"
              :label="2"
          >官方原价 X 定价系数
          </el-radio
          >
          <el-input-number
              :controls="false"
              :precision="2"
              class="input-number-text-left"
              :min="0"
              v-model="formData.pricing_strategy.guide_price.face_ratio"
          ></el-input-number>
          <p class="ml10">%</p>
        </div>
        <p class="hint-p">商品导入后，中台商品自动设置的商品指导价！</p>
      </el-form-item>
      <el-form-item label="营销价:">
        <div class="f fac mt10">
          <el-radio
              v-model="formData.pricing_strategy.activity_price.compute_type"
              :label="1"
          >折扣价 X 定价系数
          </el-radio
          >
          <el-input-number
              :controls="false"
              :precision="2"
              class="input-number-text-left"
              :min="0"
              v-model="formData.pricing_strategy.activity_price.price_ratio"
          ></el-input-number>
          <p class="ml10">%</p>
        </div>
        <div class="f fac mt10">
          <el-radio
              v-model="formData.pricing_strategy.activity_price.compute_type"
              :label="2"
          >官方原价 X 定价系数
          </el-radio
          >
          <el-input-number
              :controls="false"
              :precision="2"
              class="input-number-text-left"
              :min="0"
              v-model="formData.pricing_strategy.activity_price.face_ratio"
          ></el-input-number>
          <p class="ml10">%</p>
        </div>
        <p class="hint-p">商品导入后，中台商品自动设置的商品营销价！</p>
      </el-form-item>
      <p class="title-p">发票设置</p>
      <el-divider></el-divider>
      <el-form-item label="支持开票:">
        <el-switch
            v-model="formData.open_bill"
            :active-value="1"
            :inactive-value="0">
        </el-switch>
        <p>
          关闭后，数字权益商品订单不支持开票，在我的发票--订单发票中不显示数字权益商品订单；
          但是技术服务费发票中显示，可以正常申请技术服务费发票！
        </p>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="save">保 存</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-col>
        <div class="f fac fjsb">
          <p class="title-p">采购端API同步指定支付</p>
          <el-button type="primary" @click="savePay">保 存</el-button>
        </div>
        <el-divider></el-divider>
      </el-col>
      <el-col :span="12">
        <el-table :data="payFormData">
          <el-table-column label="支付方式" align="center" prop="account_name"></el-table-column>
          <el-table-column label="是否支持" align="center">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.enable" :true-label="1" :false-label="0"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="优先级" align="center">
            <template slot-scope="scope">
              <el-input v-model.number="scope.row.sort_level"></el-input>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
  </m-card>
</template>

<script>
import {findUserEquitySetting, updateUserEquitySetting, getSupplyID} from "@/api/userEquity"
import {createSupplyAccount, getAccount} from "@/api/purchase";
import {confirm} from "@/decorators/decorators";

export default {
  name: "userEquityBaseIndex",
  data() {
    return {
      gather_supply_id: null,
      payFormData: [],
      url: "", // 回调地址
      id: 0,
      // gatherSupply: "",
      formData: {
        open_bill: 0,
        base_info: {
          app_id: "",
          app_secret: "",
        },
        update_info: {
          // brand: 0, // 自动更新品牌
          // auto_product: 0, // 自动更新商品
          auto_product_name: 0, // 自动更新商品名称
          auto_purchase_price: 0, // 自动更新商品价格
          auto_details: 0, // 自动更新商品详情
          auto_stock_status: 0, // 自动更新商品库存
        },
        pricing_strategy: {
          // 成本价
          cost_price: {
            compute_type: null, // 1:零售价2:官方原价
            price_ratio: null, // 零售价定价系数
            face_ratio: null, // 官方原价定价系数
          },
          // 供货价
          price: {
            compute_type: null, // 1:零售价2:官方原价
            price_ratio: null, // 零售价定价系数
            face_ratio: null, // 官方原价定价系数
          },
          // 市场价
          origin_price: {
            compute_type: null, // 1:零售价2:官方原价
            price_ratio: null, // 零售价定价系数
            face_ratio: null, // 官方原价定价系数
          },
          // 指导价
          guide_price: {
            compute_type: null, // 1:零售价2:官方原价
            price_ratio: null, // 零售价定价系数
            face_ratio: null, // 官方原价定价系数
          },
          // 营销价
          activity_price: {
            compute_type: null, // 1:零售价2:官方原价
            price_ratio: null, // 零售价定价系数
            face_ratio: null, // 官方原价定价系数
          },
        },
      },
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    @confirm("提示", "是否确认保存?")
    async savePay() {
      this.payFormData = this.payFormData.map(item => ({
        ...item,
        gather_supply_id: this.gather_supply_id
      }))
      const {code, msg} = await createSupplyAccount(this.payFormData)
      if (code === 0) {
        this.$message.success(msg)
      }
    },
    async getAccountSetting() {
      const {code, data} = await getAccount({gather_supply_id: this.gather_supply_id})
      if (code === 0) {
        this.payFormData = data
      }
    },
    async getGid() {
      const {code, data} = await getSupplyID()
      if (code === 0) {
        this.gather_supply_id = data.supply_id
        this.getAccountSetting()
      }
    },
    init() {
      findUserEquitySetting().then((res) => {
        if (res.code === 0) {
          let ishttps =
              "https:" == document.location.protocol ? "https" : "http";
          let domain = document.domain;

          //res.data.url

          let proto = res.data.proto ? res.data.proto : "https";

          this.domain = proto + "://" + res.data.domain;
          this.url =
              proto +
              "://" +
              res.data.domain +
              "/supplyapi/api/gatherSupply/notificationCallBack";
          this.id = res.data.setting.id;
          let data = res.data.setting.value;
          data.pricing_strategy.price.face_ratio =
              data.pricing_strategy.price.face_ratio / 100;
          data.pricing_strategy.price.price_ratio =
              data.pricing_strategy.price.price_ratio / 100;

          data.pricing_strategy.origin_price.face_ratio =
              data.pricing_strategy.origin_price.face_ratio / 100;
          data.pricing_strategy.origin_price.price_ratio =
              data.pricing_strategy.origin_price.price_ratio / 100;

          data.pricing_strategy.guide_price.face_ratio =
              data.pricing_strategy.guide_price.face_ratio / 100;
          data.pricing_strategy.guide_price.price_ratio =
              data.pricing_strategy.guide_price.price_ratio / 100;

          data.pricing_strategy.cost_price.face_ratio =
              data.pricing_strategy.cost_price.face_ratio / 100;
          data.pricing_strategy.cost_price.price_ratio =
              data.pricing_strategy.cost_price.price_ratio / 100;

          data.pricing_strategy.activity_price.face_ratio =
              data.pricing_strategy.activity_price.face_ratio / 100;
          data.pricing_strategy.activity_price.price_ratio =
              data.pricing_strategy.activity_price.price_ratio / 100;
          this.formData.open_bill = data.open_bill
          this.formData = this.$fn.deepClone(data)
          this.getGid()
        }
      });
    },
    save() {
      let data = {
        id: this.id,
        value: JSON.parse(JSON.stringify(this.formData)),
      };
      data.value.pricing_strategy.price.face_ratio =
          data.value.pricing_strategy.price.face_ratio * 100;
      data.value.pricing_strategy.price.price_ratio =
          data.value.pricing_strategy.price.price_ratio * 100;

      data.value.pricing_strategy.origin_price.face_ratio =
          data.value.pricing_strategy.origin_price.face_ratio * 100;
      data.value.pricing_strategy.origin_price.price_ratio =
          data.value.pricing_strategy.origin_price.price_ratio * 100;

      data.value.pricing_strategy.guide_price.face_ratio =
          data.value.pricing_strategy.guide_price.face_ratio * 100;
      data.value.pricing_strategy.guide_price.price_ratio =
          data.value.pricing_strategy.guide_price.price_ratio * 100;

      data.value.pricing_strategy.cost_price.face_ratio =
          data.value.pricing_strategy.cost_price.face_ratio * 100;
      data.value.pricing_strategy.cost_price.price_ratio =
          data.value.pricing_strategy.cost_price.price_ratio * 100;

      data.value.pricing_strategy.activity_price.face_ratio =
          data.value.pricing_strategy.activity_price.face_ratio * 100;
      data.value.pricing_strategy.activity_price.price_ratio =
          data.value.pricing_strategy.activity_price.price_ratio * 100;
      updateUserEquitySetting(data).then((res) => {
        if (res.code === 0) {
          this.$message.success(res.msg);
        }
      });
    }
  }
}
</script>

<style scoped>

</style>