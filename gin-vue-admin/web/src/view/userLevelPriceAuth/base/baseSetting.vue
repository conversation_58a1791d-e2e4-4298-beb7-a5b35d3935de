<template>
  <m-card>
      <el-row>
          <el-col :span="16">
              <el-form :model="formData" label-width="220px">
                  <el-form-item label="开启权限: " prop="is_open">
                      <el-radio-group v-model="formData.is_open">
                          <el-radio label="1">开启</el-radio>
                          <el-radio label="0">关闭</el-radio>
                      </el-radio-group>
                      <p class="color-grap">
                          开启该功能后，用户进入商城浏览首页、商品详情页、商品列表页、足迹、收藏页面、商品分类页如果没有查看商品价格权限的将不再显示商品价格
                      </p>
                      <p class="color-grap">
                          显示格式为”¥：自定义显示文字“
                      </p>
                  </el-form-item>
                  <el-form-item label="商品价格会员等级设置查看购买权限: " prop="user_level">
                      <el-checkbox-group v-model="formData.user_level">
                          <el-checkbox v-for="item in userLevelList" :key="item.id" :label="item.id">{{ item.name}}</el-checkbox>
                      </el-checkbox-group>
                  </el-form-item>
                  <el-form-item label="商品价格查看无权限显示文字: " prop="message">
                      <el-input v-model="formData.message"></el-input>
                      <p class="color-grap">默认显示无权限</p>
                  </el-form-item>
                  <el-form-item>
                      <el-button type="primary" @click="save">保 存</el-button>
                  </el-form-item>
              </el-form>
          </el-col>
      </el-row>
  </m-card>
</template>
<script>
import {update,getAuth} from "@/api/userPriceAuth";
import {getUserLevelListNotPage} from "@/api/listNotPage";
export default {
    name: "userLevelPriceAuthBaseSetting",
    data(){
        return {
            userLevelList:[],
            formData:{
                is_open: "0", // 0关闭 1开启
                user_level: [],
                message: ""
            }
        }
    },
    mounted() {
        this.getUserLevelList()
        this.init()
    },
    methods:{
        async save(){
            if(this.formData.is_open === "1" && !this.formData.message){
                this.$message.error("请输入商品价格查看无权限显示文字")
                return;
            }
          const {code,msg} = await update(this.formData)
          if(code === 0){
              this.$message.success(msg)
          }
        },
        async init(){
            const {code,data} = await getAuth()
            if(code === 0){
                const resData = JSON.parse(data)
                this.formData.is_open = resData.is_open
                this.formData.user_level = resData.user_level
                this.formData.message = resData.message
            }
        },
        async getUserLevelList(){
            const {code, data} = await getUserLevelListNotPage()
            if(code === 0){
                this.userLevelList = data.list
            }
        }
    }
}
</script>
<style scoped lang="scss"></style>