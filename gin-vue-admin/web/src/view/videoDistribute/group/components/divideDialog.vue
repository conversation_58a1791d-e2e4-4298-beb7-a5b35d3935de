<template>
  <el-dialog :title="title" :visible="dialogVisible" width="30%" :before-close="handleClose">
      <el-form :model="formData" ref="form" label-width="130px">
          <el-form-item label=" 分组名称:"  prop="title">
              <el-input v-model="formData.title" placeholder="请输入分组名称"></el-input>
          </el-form-item>
          <el-form-item label="权限设置:"  prop="title">
              <el-select  v-model="formData.type"  @change="authorityChange"  clearable placeholder="请选择采购端分组">
                  <el-option
                          v-for="item in authority"
                          :key="item.id"
                          :label="item.name"
                          :value="item.value"
                  ></el-option>
              </el-select>
          </el-form-item>
          <el-form-item label="选择采购端会员:"  prop="title" v-if="formData.type === 1">
              <el-select  v-model="formData.auth"  multiple filterable clearable placeholder="请选择">
                  <el-option
                          v-for="item in segmentList"
                          :key="item.id"
                          :label="item.user.nickname  + '/' + item.user.username"
                          :value="item.id"
                  ></el-option>
              </el-select>

          </el-form-item>
          <el-form-item label="选择采购端等级:"  prop="title" v-if="formData.type === 2">
              <el-select  v-model="formData.auth"  multiple filterable clearable placeholder="请选择">
                  <el-option
                          v-for="item in gradeList"
                          :key="item.id"
                          :label="item.levelName"
                          :value="item.id"
                  ></el-option>
              </el-select>
          </el-form-item>
          <el-form-item label="选择采购端分组:"  prop="title" v-if="formData.type === 3">
              <el-select  v-model="formData.auth"  multiple filterable clearable placeholder="请选择">
                  <el-option
                          v-for="item in groupingList"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id"
                  ></el-option>
              </el-select>
          </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="save">确定</el-button>
          <el-button @click="handleClose">取消</el-button>
      </span>
  </el-dialog>
</template>

<script>
import {createGroup, updateGroup} from '@/api/video'
// import {getApplicationOption} from "@/api/order"
// import {getApplicationLevelList} from "@/api/applicationLevel"
// import {getApplicationGroupList} from "@/api/jushuitan"

import { createLink, findLink, updateLink } from "@/api/shopSetting";
import { createCategory, getApplicationLevelList,getApplicationGroupList,getApplicationApplyList} from "@/api/scriptDistribution"
import { getArticleList } from "@/api/sales";

export default {
  name: 'DivideDialogVideo',
    data() {
        return {
            dialogVisible: false,
            // 商城端文章跳转链接id
            articleUrlId: "",
            formData: {
                // id:null,
                title: "",
                type:'',
                // status: 1,
                auth:[],
            },
            authority:[
                {
                    name:'指定采购端会员',
                    value:1,
                },
                {
                    name:'指定采购端等级',
                    value:2,
                },
                {
                    name:'指定采购端分组',
                    value:3,
                },
                {
                    name:'无限制',
                    value:4,
                },
            ],
            optionList: [],
            //采购端会员列表
            segmentList:[],
            //采购端等级列表
            gradeList:[],
            //采购端分组列表
            groupingList:[],
            title: "新增",
        };
    },
    mounted() {
        this.getApplicationApplyListdata()
        this.gradeListListdata()
        this.getApplicationGroupListData()
    },
    methods: {
        //
        authorityChange(val){
            this.formData.auth = []
        },
        handleChange(id) {
            // 底部导航
            if (this.formData.group_id === 1) {
                this.formData.url = "/article?id=" + id
            } else if (this.formData.group_id === 4) {
                // 文章
                this.formData.url = "/about?id=" + id
            }
        },
        // 获取文章列表
        getOption() {
            getArticleList({ page: 1, pageSize: 9999 }).then(res => {
                if (res.code === 0) {
                    this.optionList = res.data.list
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        handleClose() {
            this.dialogVisible = false;
            this.articleUrlId = ""
            this.optionList = [];
            this.$refs.form.resetFields();
        },
        //获取采购段会员列表
        getApplicationApplyListdata(){
            getApplicationApplyList().then((res) => {
                if(res.code == 0){
                    console.log(11, res)
                    this.segmentList = res.data.list
                }
            })
        },
        //获取采购端等级
        gradeListListdata(){
            getApplicationLevelList().then((res) => {
                if(res.code == 0){
                    console.log(22, res)
                    this.gradeList = res.data.list
                }
            })
        },
        //获取采购端分组
        getApplicationGroupListData(){
            getApplicationGroupList().then((res) => {
                if(res.code == 0){
                    console.log(33, res)
                    this.groupingList = res.data.list
                }
            })
        },
        // 获取字符串id值
        getStrId(str) {
            let wz = str.indexOf("?id=") + 4
            let res = 0
            if (wz > 0) {
                res = str.substring(wz, str.length)
            }
            return parseInt(res)
        },
        save() {
            if (!this.formData.title) {
                this.$message.error("请输入分组名称");
                return;
            }           
            delete this.formData.pid;
            delete this.formData.group_id;
            let params = JSON.parse(JSON.stringify(this.formData))
            // console.log(params)
            // console.log(this.formData.id)
            this.formData.auth.toString()
            // console.log( this.formData.auth.toString())
            
            this.formData.auth = '[' + this.formData.auth.toString() + ']'
            // console.log(this.formData.userId)
            // JSON.parse(this.formData.gradeId)
            // JSON.parse(this.formData.groupingId)
            if (this.formData.id) {
                console.log('这里是编辑');            
                console.log('this.formData 1111', this.formData)
                updateGroup(this.formData).then(res => {
                    if (res.code === 0) {
                        this.$message.success("编辑成功")
                        this.handleClose();
                        // this.$emit("onload", this.formData.pid);
                        this.$emit("onload");
                    } else {
                        this.$message.error(res.msg)
                    }
                })
            } else {
                console.log('这里是添加');
                // 添加
                createGroup(this.formData).then((res) => {
                    if (res.code === 0) {
                        this.$message.success("新增成功");
                        this.handleClose();
                        this.$emit("onload", this.formData.pid);
                    } else {
                        this.$message.error("新增失败");
                    }
                });
            }
        },
        getInfo(row) {
          console.log(row)
            this.formData.title = row.title
            this.formData.type = row.type
            let cc = row.auth.slice(1,-1)
            console.log(cc)
             let bb = cc.split(',')
            console.log(bb)
            let dd = []
            bb.forEach(item => {
                dd.push(Number(item))
            })
            this.formData.auth = dd
            this.formData.id = row.id * 1
        },
    },
};
</script>
<style lang="scss" scoped>
.w400 {width: 400px;}
.ml20 {margin-left: 20px;}
.level-tag {
  margin-left: 5px;
}
.level-tag:not(:last-child)::after {    
  content: ",";
}
</style>
