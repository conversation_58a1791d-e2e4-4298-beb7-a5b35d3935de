<template>
  <el-dialog 
    title="商品库存信息" 
    :visible="visible" 
    @open="onOpen"
    @close="onClose"
  >
    <el-table :data="tableData" class="mt25">
      <el-table-column prop="order.order_sn" label="订单号" align="center"></el-table-column>
      <el-table-column prop="application_id" label="采购端ID" align="center"></el-table-column>
      <el-table-column prop="application.app_name" label="采购端名称" align="center"></el-table-column>
      <el-table-column prop="product_id" label="商品ID" align="center"></el-table-column>
      <el-table-column label="商品图片" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
              <el-popover placement="right" title="" trigger="hover">
                  <m-image
                      :src="scope.row.product.image_url"
                      :style="{ width: '160px', height: '160px' }"
                  ></m-image>
                  <m-image
                      slot="reference"
                      :src="scope.row.product.image_url"
                      :alt="scope.row.product.image_url"
                      :style="{ width: '60px', height: '60px' }"
                  ></m-image>
              </el-popover>
          </template>
      </el-table-column>
      <el-table-column prop="product.title" label="商品标题" align="center"></el-table-column>
      <el-table-column label="库存" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.type === 1">
          +{{ scope.row.stock}}
          </span>
          <span v-if="scope.row.type === 2">
          -{{ scope.row.stock}}
          </span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        background
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="[10, 30, 50, 100, 200]"
        :style="{ display: 'flex', justifyContent: 'flex-end', marginRight: '20px' }"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        layout="total, sizes,prev, pager, next, jumper"
    ></el-pagination>
  </el-dialog>
</template>
<script>
import { getStockChangeRecordList } from '@/api/stock'
export default {
  name: "StockDetails",
  props: {
    // 弹窗显示/隐藏
    visible: {
      type: Boolean,
      default: false
    },
    propRow: {
      type: Object,
      default: () => { return {} }
    }
  },
  data() {
    return {
      application_id : null,
      product_id : null,
      tableData: [],
      page: 1,
      pageSize: 10,
      total: 0,
      // stockDetails: {
      //   application: {
      //     app_name: ''
      //   }
      // }
    }
  },
  methods: {
    onOpen () {
      // this.stockDetails = this.propRow
      this.application_id = this.propRow.application_id
      this.product_id = this.propRow.product_id
      this.getStockDetailList()
    },
    onClose () {
      this.$emit('update:visible', false)
    },
    // showDetails(row){
    //   this.stockDetails = row
    // }
    async getStockDetailList(){
        const param = {
          application_id : this.application_id,
          product_id : this.product_id,
          page: this.page,
          pageSize: this.pageSize,
        }
        try {
            let res = await getStockChangeRecordList(param)
            if (res.code === 0) {
              this.tableData = res.data.list
              this.total = res.data.total
            } else {
              this.tableData = [];
              this.total = 0;
            }
        } catch (error) {
            this.$message.error('获取失败')
        }
    },
    handleCurrentChange(page) {
      this.page = page;
      this.getStockDetailList()
    },
    handleSizeChange(size) {
      this.pageSize = size;
      this.getStockDetailList()
    },
  }
};
</script>
<style lang="scss" scoped>
@import "@/style/base.scss";
p span.pay-sort-span{
  display: inline-block;
  margin-right: 5px;
  span{
    display: inline-block;
    margin-left: 5px;
  }
  &:last-child{
    margin-right: 0;
    span{
      display: none;
    }
  }
}
</style>
