package model

import (
	umodel "user/model"
	yzRequest "yz-go/request"
	"yz-go/source"
)

type SinglePaymentCallBack struct {
	RequestId          string  `json:"requestId"`
	InnerTradeNo       string  `json:"innerTradeNo"`
	Status             int64   `json:"status"`
	FailReason         string  `json:"failReason"`
	Name               string  `json:"name"`
	Mobile             string  `json:"mobile"`
	Amount             float64 `json:"amount"`
	CurrentRealWage    float64 `json:"currentRealWage"`
	CurrentTax         float64 `json:"currentTax"`
	CurrentManageFee   float64 `json:"currentManageFee"`
	CurrentAddTax      float64 `json:"currentAddTax"`
	CurrentAddValueTax float64 `json:"currentAddValueTax"`
	Identity           string  `json:"identity"`
	BankName           string  `json:"bankName"`
	BankAccount        string  `json:"bankAccount"`
	DateTime           string  `json:"dateTime"`
	CreateTime         string  `json:"createTime"`
	PayTime            string  `json:"payTime"`
	Remark             string  `json:"remark"`
	ExtRemark          string  `json:"extRemark"`
}

type RequestContract struct {
	Identity string `json:"identity"`
}

type Setting struct {
	Enable      string `json:"enable"`  //0关闭 1开启
	ApiUrl      string `json:"api_url"` //接口地址
	Key         string `json:"key"`
	Secret      string `json:"secret"`
	ContractUrl string `json:"contract_url"`
	ServiceId   string `json:"service_id"`
	GzhAppId    string `json:"gzh_app_id"`
	XcxAppId    string `json:"xcx_app_id"`
}

type MqData struct {
	Uid        uint   `json:"uid"`
	OrderSN    string `json:"order_sn"`
	Amount     uint   `json:"amount"`
	Type       int    `json:"type"`
	UserBankId int    `json:"user_bank_id"`
}

type SearchGongMallPayment struct {
	GongMallPayment
	Nickname string `json:"nickname"`
	yzRequest.PageInfo
	PayTimeStart string `json:"pay_time_start"`
	PayTimeEnd   string `json:"pay_time_end"`
}
type GongMallPaymentModel struct {
	GongMallPayment

	User umodel.User `json:"user" gorm:"foreignKey:Uid"`
}

func (i GongMallPaymentModel) TableName() string {
	return "gong_mall_payments"
}

type Status int

var (
	InProcess Status = 3
	Complete  Status = 2
	Fail      Status = 1
)

type GongMallPayment struct {
	source.Model
	Uid     uint   `json:"uid"`
	OrderSN string `json:"order_sn"`
	Amount  uint   `json:"amount"`
	Status  Status `json:"status" gorm:"default:0"` // 3 处理中     2  成功      1 失败
	PayTime string `json:"pay_time"`
	ErrMsg  string `json:"err_msg"`
}

type Balance struct {
	Sign string `json:"sign"`
	Data struct {
		TotalAvailableBalance float64 `json:"totalAvailableBalance"`
		TotalFrozenAmount     float64 `json:"totalFrozenAmount"`
		AccountInfoList       []struct {
			AccountNo                   string  `json:"accountNo,omitempty"`
			AccountChannelGroupType     string  `json:"accountChannelGroupType"`
			AccountChannelGroupTypeName string  `json:"accountChannelGroupTypeName"`
			AvailableBalance            float64 `json:"availableBalance"`
			FrozenAmount                float64 `json:"frozenAmount"`
			Opened                      bool    `json:"opened"`
			Status                      int     `json:"status,omitempty"`
		} `json:"accountInfoList"`
	} `json:"data"`
	Success bool `json:"success"`
}

type ContractStatus struct {
	ErrorMsg string `json:"errorMsg"`
	Sign     string `json:"sign"`
	Data     struct {
		ProcessStatus int    `json:"processStatus"`
		FileUrl       string `json:"fileUrl"`
	} `json:"data"`
	Success bool `json:"success"`
}

type Bank struct {
	Sign string `json:"sign"`
	Data struct {
		BankAccountNo string `json:"bankAccountNo"`
		BankName      string `json:"bankName"`
	} `json:"data"`
	Success bool `json:"success"`
}

type Payment struct {
	Sign     string `json:"sign"`
	ErrorMsg string `json:"errorMsg"`

	ErrorCode string `json:"errorCode"`
	Data      struct {
		RequestId   string `json:"requestId"`
		AppmentTime string `json:"appmentTime"`
	} `json:"data"`
	Success bool `json:"success"`
}

type PayQueryData struct {
	Sign string `json:"sign"`
	Data struct {
		RequestId          string  `json:"requestId"`
		Status             int     `json:"status"`
		FailReason         string  `json:"failReason"`
		PendingStatus      int     `json:"pendingStatus"`
		Name               string  `json:"name"`
		Mobile             string  `json:"mobile"`
		Amount             float64 `json:"amount"`
		Identity           string  `json:"identity"`
		BankName           string  `json:"bankName"`
		BankAccount        string  `json:"bankAccount"`
		CreateTime         string  `json:"createTime"`
		DateTime           string  `json:"dateTime"`
		PayTime            string  `json:"payTime"`
		Remark             string  `json:"remark"`
		InnerTradeNo       string  `json:"innerTradeNo"`
		CurrentRealWage    float64 `json:"currentRealWage"`
		CurrentTax         float64 `json:"currentTax"`
		CurrentManageFee   float64 `json:"currentManageFee"`
		CurrentAddTax      float64 `json:"currentAddTax"`
		CurrentAddValueTax float64 `json:"currentAddValueTax"`
	} `json:"data"`
	Success bool `json:"success"`
}
