package goods

import (
	catemodel "category/model"
	"encoding/json"
	"errors"
	"fmt"
	gatherSupplyRequest "gather-supply/request"
	"github.com/chenhg5/collection"
	"github.com/xingliuhua/leaf"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"guanaitong-supply/common"
	"guanaitong-supply/model"
	log2 "log"
	"math"
	pmodel "product/model"
	pMq "product/mq"
	"product/service"
	callback2 "public-supply/callback"
	common2 "public-supply/common"
	publicModel "public-supply/model"
	"public-supply/request"
	service2 "public-supply/service"
	setting2 "public-supply/setting"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

type Guanaitong struct {
	GatData *model.GuanaitongSupplySetting
}

func (gat *Guanaitong) ManuallyProductUpdate(productID uint) (err error) {
	//TODO implement me
	panic("implement me")
}

func (gat *Guanaitong) SynchronizeProductsToLocal() (err error) {
	//TODO implement me
	panic("implement me")
}

var wg sync.WaitGroup

func splitArray(arr []publicModel.Goods, num int64) [][]publicModel.Goods {
	max := int64(len(arr))
	//判断数组大小是否小于等于指定分割大小的值，是则把原数组放入二维数组返回
	if max <= num {
		return [][]publicModel.Goods{arr}
	}
	//获取应该数组分割为多少份
	var quantity int64
	if max%num == 0 {
		quantity = max / num
	} else {
		quantity = (max / num) + 1
	}

	fmt.Println("quantity数量：", quantity)
	//声明分割好的二维数组
	var segments = make([][]publicModel.Goods, 0)
	//声明分割数组的截止下标
	var start, end, i int64
	for i = 1; i <= quantity; i++ {
		end = i * num
		if i != quantity {
			segments = append(segments, arr[start:end])
		} else {
			segments = append(segments, arr[start:])
		}
		start = i * num
	}
	return segments
}
func (gat *Guanaitong) ProductEdit(skuId string) (err error) {

	var gatProduct model.GuanaitongSku
	err = source.DB().Where("sku_id = ?", skuId).First(&gatProduct).Error
	if err != nil {
		log.Log().Info("获取本地商品失败", zap.Any("err", err))
		return
	}
	err, result := gat.GetGoodsDetails([]string{skuId})
	if err != nil {
		log.Log().Info("获取gat商品失败", zap.Any("err", err))
		return
	}
	if len(result) > 0 {
		detail := result[0]
		detail.ID = gatProduct.ID
		err = source.DB().Updates(&detail).Error
		if err != nil {
			return
		}
		var localProduct service.ProductForUpdate
		err = source.DB().Preload("Skus").Where("sn = ?", gatProduct.SpuId).Where("gather_supply_id = ?", GatherSupplyID).First(&localProduct).Error
		if err != nil {
			return
		}
		if gatData.UpdateInfo.BaseInfo == 1 {
			localProduct.Title = detail.ProductTitle
			var images []model.Pic
			var detailImages []model.Pic
			if len(detail.Pics) > 0 {
				for _, pic := range detail.Pics {
					if pic.PicType == 1 {
						images = append(images, pic)
					}
					if pic.PicType == 2 {
						detailImages = append(detailImages, pic)
					}

				}
				if len(images) > 0 {
					sort.Slice(images, func(i, j int) bool {
						return images[i].PicSort > images[j].PicSort // 倒序排序，Age 大的排在前面
					})
					localProduct.ImageUrl = images[0].PicUrl
				}
				if len(detailImages) > 0 {
					sort.Slice(detailImages, func(i, j int) bool {
						return detailImages[i].PicSort > detailImages[j].PicSort // 倒序排序，Age 大的排在前面
					})
				}
			}

			var gallery pmodel.Gallery
			for _, image := range images {
				gallery = append(gallery, pmodel.GalleryItem{
					Type: 1,
					Src:  image.PicUrl,
				})
			}
			localProduct.Gallery = gallery
			localProduct.DetailImages = "<p>"
			for _, dimage := range detailImages {
				localProduct.DetailImages += "<img src=\"" + dimage.PicUrl + "\">"
			}
			localProduct.DetailImages += "</p>"

		}
		if gatData.UpdateInfo.CateGory == 1 {
			if len(detail.Categories) == 3 {
				var resCategory1 catemodel.Category
				var resCategory2 catemodel.Category
				var resCategory3 catemodel.Category

				for _, category := range detail.Categories {
					if category.CategoryLevel == 1 {
						resCategory1.Name = category.CategoryName
					}
					if category.CategoryLevel == 2 {
						resCategory2.Name = category.CategoryName
					}
					if category.CategoryLevel == 3 {
						resCategory3.Name = category.CategoryName
					}
				}
				display := 1
				resCategory1.Level = 1
				resCategory1.ParentID = 0
				resCategory1.IsDisplay = &display
				err = source.DB().Where("name = ? and level = ? and parent_id = ?", resCategory1.Name, 1, 0).FirstOrCreate(&resCategory1).Error
				if err != nil {
					return
				}
				resCategory2.Level = 2
				resCategory2.ParentID = resCategory1.ID
				resCategory2.IsDisplay = &display
				err = source.DB().Where("name = ? and level = ? and parent_id = ?", resCategory2.Name, 2, resCategory2.ParentID).FirstOrCreate(&resCategory2).Error
				if err != nil {
					return
				}
				resCategory3.Level = 3
				resCategory3.ParentID = resCategory2.ID
				resCategory3.IsDisplay = &display
				err = source.DB().Where("name = ? and level = ? and parent_id = ?", resCategory3.Name, 3, resCategory3.ParentID).FirstOrCreate(&resCategory3).Error
				if err != nil {
					return
				}
				localProduct.Category1ID = resCategory1.ID
				localProduct.Category2ID = resCategory2.ID
				localProduct.Category3ID = resCategory3.ID
			}
		}
		var isRisk int
		var costPrice, salePrice, activityPrice, guidePrice, originPrice uint

		err, costPrice, salePrice, activityPrice, guidePrice, originPrice = GetPricingPrice(uint(math.Round(detail.Price*100)), uint(math.Round(detail.SalePrice*100)), uint(math.Round(detail.MarketPrice*100)))
		if err != nil {
			return
		}
		var minPrice, minCostPrice, minActivityPrice, minGuidePrice, minOriginPrice uint
		var status, stock int
		for k, sku := range localProduct.Skus {
			if sku.Sn == detail.SkuId {
				localProduct.Skus[k].Price = salePrice
				localProduct.Skus[k].CostPrice = costPrice
				localProduct.Skus[k].OriginPrice = originPrice
				localProduct.Skus[k].GuidePrice = guidePrice
				localProduct.Skus[k].ActivityPrice = activityPrice
				if localProduct.Skus[k].GuidePrice > 0 {
					localProduct.Skus[k].ProfitRate = utils.ExecProfitRate(localProduct.Skus[k].GuidePrice, localProduct.Skus[k].Price)
				} else {
					localProduct.Skus[k].ProfitRate = 0
				}

				localProduct.Skus[k].Title = detail.SkuName
				localProduct.Skus[k].Stock = detail.Stock
				if detail.SkuState == 0 {
					//sku下架,库存设置为0
					localProduct.Skus[k].Stock = 0
				}
				if gatData.Management.ProductPriceStatus == 1 {
					if float64(localProduct.Skus[k].Price) < float64(localProduct.Skus[k].CostPrice)*float64(gatData.Management.Products)/100 {
						localProduct.IsDisplay = 0
						isRisk++
					}
				} else if gatData.Management.ProductPriceStatus == 2 {
					if (float64(localProduct.Skus[k].Price)-float64(localProduct.Skus[k].CostPrice))/float64(localProduct.Skus[k].CostPrice) < float64(gatData.Management.Profit)/100 {
						localProduct.IsDisplay = 0
						isRisk++
					}
				}

			}

			if minPrice == 0 || localProduct.Skus[k].Price <= minPrice {
				minPrice = localProduct.Skus[k].Price
			}
			if minCostPrice == 0 || localProduct.Skus[k].CostPrice <= minCostPrice {
				minCostPrice = localProduct.Skus[k].CostPrice
			}
			if minActivityPrice == 0 || localProduct.Skus[k].ActivityPrice <= minActivityPrice {
				minActivityPrice = localProduct.Skus[k].ActivityPrice
			}
			if minGuidePrice == 0 || localProduct.Skus[k].GuidePrice <= minGuidePrice {
				minGuidePrice = localProduct.Skus[k].GuidePrice
			}
			if minOriginPrice == 0 || localProduct.Skus[k].OriginPrice <= minOriginPrice {
				minOriginPrice = localProduct.Skus[k].OriginPrice
			}

			if len(localProduct.Skus) > 0 {
				localProduct.ProfitRate = localProduct.Skus[k].ProfitRate
			}
			stock += localProduct.Skus[k].Stock
			//借用商品库存判断sku是否上架,如果有sku库存不为0，则商品上架
			status += localProduct.Skus[k].Stock
		}
		localProduct.Stock = uint(stock)
		localProduct.GuidePrice = minGuidePrice
		localProduct.OriginPrice = minOriginPrice
		localProduct.ActivityPrice = minActivityPrice
		if gatData.UpdateInfo.CurrentPrice == 1 {
			localProduct.Price = minPrice
		}
		if gatData.UpdateInfo.CostPrice == 1 {
			localProduct.CostPrice = minCostPrice
		}
		if status > 1 {
			status = 1
		}
		localProduct.IsDisplay = status
		err = service.UpdateProduct(localProduct)
		if err != nil {
			return
		}
	}

	return
}

func (gat *Guanaitong) ProductPriceEdit(skuId string) (err error) {
	var gatProduct model.GuanaitongSku
	err = source.DB().Where("sku_id = ?", skuId).First(&gatProduct).Error
	if err != nil {
		log.Log().Info("获取本地商品失败", zap.Any("err", err))
		return
	}

	var priceData []PriceData
	err, priceData = gat.GetGoodsPrice([]string{skuId})
	if err != nil {
		return
	}
	for _, price := range priceData {
		if price.SkuId == gatProduct.SkuId {
			gatProduct.Price = price.Price
			gatProduct.SalePrice = price.SalePrice
			gatProduct.MarketPrice = price.MarketPrice
		}
	}
	err = source.DB().Updates(&gatProduct).Error
	if err != nil {
		return
	}
	var localProduct service.ProductForUpdate
	err = source.DB().Preload("Skus").Where("sn = ?", gatProduct.SpuId).Where("gather_supply_id = ?", GatherSupplyID).First(&localProduct).Error
	if err != nil {
		return
	}
	var costPrice, salePrice, activityPrice, guidePrice, originPrice uint
	err, costPrice, salePrice, activityPrice, guidePrice, originPrice = GetPricingPrice(uint(math.Round(gatProduct.Price*100)), uint(math.Round(gatProduct.SalePrice*100)), uint(math.Round(gatProduct.MarketPrice*100)))
	if err != nil {
		return
	}
	var minPrice, minCostPrice, minActivityPrice, minGuidePrice, minOriginPrice uint
	for k, sku := range localProduct.Skus {
		if sku.Sn == gatProduct.SkuId {
			localProduct.Skus[k].Price = salePrice
			localProduct.Skus[k].CostPrice = costPrice
			localProduct.Skus[k].OriginPrice = originPrice
			localProduct.Skus[k].GuidePrice = guidePrice
			localProduct.Skus[k].ActivityPrice = activityPrice
			if localProduct.Skus[k].GuidePrice > 0 {
				localProduct.Skus[k].ProfitRate = utils.ExecProfitRate(localProduct.Skus[k].GuidePrice, localProduct.Skus[k].Price)
			} else {
				localProduct.Skus[k].ProfitRate = 0
			}
		}

		if minPrice == 0 || localProduct.Skus[k].Price <= minPrice {
			minPrice = localProduct.Skus[k].Price
			minCostPrice = localProduct.Skus[k].CostPrice
			minActivityPrice = localProduct.Skus[k].ActivityPrice
			minGuidePrice = localProduct.Skus[k].GuidePrice
			minOriginPrice = localProduct.Skus[k].OriginPrice
		}

	}
	localProduct.GuidePrice = minGuidePrice
	localProduct.OriginPrice = minOriginPrice
	localProduct.ActivityPrice = minActivityPrice
	if gatData.UpdateInfo.CurrentPrice == 1 {
		localProduct.Price = minPrice
	}
	if gatData.UpdateInfo.CostPrice == 1 {
		localProduct.CostPrice = minCostPrice
	}
	err = service.UpdateProduct(localProduct)
	if err != nil {
		return
	}
	return
}

func (gat *Guanaitong) ProductAddOrDelete(skuId string) (err error) {
	err, result := gat.GetGoodsDetails([]string{skuId})
	if err != nil {
		//删除
		var gatSku model.GuanaitongSku
		err = source.DB().Where("sku_id = ?", skuId).First(&gatSku).Error
		if err != nil {
			return
		}
		//删除guanaitong商品
		err = source.DB().Unscoped().Where("sku_id = ?", skuId).Delete(&model.GuanaitongSku{}).Error
		if err != nil {
			return
		}
		//删除本地商品
		err = source.DB().Where("sn = ?", gatSku.SkuId).Delete(&pmodel.Sku{}).Error
		if err != nil {
			return
		}
		//判断是否还有其他sku
		var skuCount int64
		err = source.DB().Model(model.GuanaitongSku{}).Where("spu_id = ?", gatSku.SpuId).Count(&skuCount).Error
		if err != nil {
			return
		}
		if skuCount <= 0 {
			//删除guanaitong商品
			err = source.DB().Unscoped().Where("spu_id = ?", gatSku.SpuId).Delete(&model.GuanaitongProduct{}).Error
			if err != nil {
				return
			}
			//下架本地商品
			var localProduct service.ProductForUpdate
			err = source.DB().Model(&pmodel.Product{}).Where("sn = ?", gatSku.SpuId).First(&localProduct).Error
			if err != nil {
				return
			}
			localProduct.IsDisplay = 0
			localProduct.StatusLock = 1
			localProduct.MD5 = "上游商品已删除"
			err = source.DB().Model(&pmodel.Product{}).Where("id = ?", localProduct.ID).Updates(&localProduct).Error
			if err != nil {
				return
			}
			err = pMq.PublishMessage(localProduct.ID, pMq.Undercarriage, 0)
		}
		return
	} else {
		//新增
		var priceData []PriceData
		err, priceData = gat.GetGoodsPrice([]string{skuId})
		if err != nil {
			return
		}
		for _, detail := range result {
			for _, cate := range detail.Categories {
				err = source.DB().Where("category_id = ?", cate.CategoryId).FirstOrCreate(&cate).Error
				if err != nil {
					return
				}
				if cate.CategoryLevel == 1 {
					detail.Category1Id = cate.CategoryId
				}
				if cate.CategoryLevel == 2 {
					detail.Category2Id = cate.CategoryId
				}
				if cate.CategoryLevel == 3 {
					detail.Category3Id = cate.CategoryId
				}
			}
			for _, price := range priceData {
				if price.SkuId == detail.SkuId {
					detail.Price = price.Price
					detail.SalePrice = price.SalePrice
					detail.MarketPrice = price.MarketPrice
				}
			}
			err = source.DB().Where("sku_id = ?", detail.SkuId).FirstOrCreate(&detail).Error
			if err != nil {
				return
			}
			var gatProduct model.GuanaitongProduct
			gatProduct.SpuId = detail.SpuId
			err = source.DB().Where("spu_id = ?", detail.SpuId).FirstOrCreate(&gatProduct).Error
		}
	}
	return
}
func (gat *Guanaitong) ImportSelectGoodsRun(info publicModel.SelectGoods) (err error, list interface{}) {
	orderPN := GetOrderNo()
	common2.GlobalOrderSN = orderPN
	goodsRecord := publicModel.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: len(info.List),
		Status:            1,
	}
	var resultInt []string
	var field string
	field = "sn"

	err = source.DB().Model(pmodel.Product{}).Where("gather_supply_id=?", info.GatherSupplyID).Pluck(field, &resultInt).Error
	if err != nil {
		log2.Println("查询供应链商品id错误", err)
		return
	}

	if len(info.List) <= 0 {
		log2.Println("导入空数据")
		err = errors.New("导入的是空数据")
		return
	}

	var idsArr []string
	idsArr = GetIdArr(info.List)

	difference := collection.Collect(idsArr).Diff(resultInt).ToStringArray()
	repeat := len(idsArr) - len(difference)
	goodsRecord.RepeatQuantity = repeat //重复数据
	if len(difference) <= 0 {
		goodsRecord.Status = 2
		goodsRecord.CompletionStatus = 1
		source.DB().Omit("goods_arr").Create(&goodsRecord) //当前没有商品可以导入
		return
	}
	source.DB().Omit("goods_arr").Create(&goodsRecord) //创建导入记录
	var goodsList []publicModel.Goods
	for _, v := range difference {
		for _, item := range info.List {
			if item.SN == v {
				goodsList = append(goodsList, item)
			}
		}

	}
	info.List = goodsList
	arrList := splitArray(info.List, 20)
	for index, item := range arrList {
		wg.Add(1)
		fmt.Println("循环", index)
		gat.RunSelectGoodsConcurrent(orderPN, item, info.Categorys, info.Key, info.GatherSupplyID)
	}
	wg.Wait()
	err = SetImportRecordCompletion(orderPN)
	fmt.Println("全部完成：")

	return
}
func (gat *Guanaitong) RunSelectGoodsConcurrent(orderPN string, list []publicModel.Goods, category string, key string, gatherSupplyID uint) (err error) {

	defer wg.Done()

	cateList := strings.Split(category, ",")
	var cateId1, cateId2, cateId3 int
	cateId1, err = strconv.Atoi(cateList[0])
	cateId2, err = strconv.Atoi(cateList[1])
	cateId3, err = strconv.Atoi(cateList[2])

	var listGoods []*pmodel.Product
	var recordError []publicModel.SupplyGoodsImportRecordErrors
	err, listGoods, recordError = gat.CommodityAssembly(list, cateId1, cateId2, cateId3, GatherSupplyID, 1)
	if err != nil {
		return
	}
	if len(listGoods) > 0 {
		err = service2.FinalProcessing(listGoods, orderPN)
		if err != nil {
			return
		}
		err = SetImported(listGoods)
		if err != nil {
			err = errors.New("修改商品导入状态失败:" + err.Error())
			return
		}
	}
	if len(recordError) > 0 {
		err = service2.FinalProcessingError(recordError, orderPN)
		if err != nil {
			return
		}
	}
	return

}
func (gat *Guanaitong) ValidateConfig(params gatherSupplyRequest.ValidateConfigParams) (err error) {

	return
}

var gatData *model.GuanaitongSupplySetting

var GatherSupplyID uint

func (gat *Guanaitong) InitSetting(gatherSupplyID uint) (err error) {
	var setting model2.SysSetting
	err, setting = setting2.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		return
	}
	GatherSupplyID = gatherSupplyID
	err = json.Unmarshal([]byte(setting.Value), &gatData)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	if gatData.BaseInfo.AppId == "" || gatData.BaseInfo.AppSecret == "" || gatData.BaseInfo.Host == "" {
		err = errors.New("请先配置供应链key")
		return
	}
	gat.GatData = gatData
	return
}

func (gat *Guanaitong) GetGoods(info request.GetGoodsSearch) (err error, data interface{}, total int64, serverRatio int) {
	limit := info.Limit
	offset := info.Limit * (info.Page - 1)
	var youxuanGoods []model.GuanaitongProduct
	var spuIds []string
	db := source.DB().Model(&model.GuanaitongSku{}).Distinct("spu_id")
	db.Where("sku_state = 1")
	var searchCount int64
	if info.SearchWords != "" {
		db.Where("`product_title` like ?", "%"+info.SearchWords+"%")
		searchCount++
	}

	if info.Category1ID > 0 {
		db.Where("`category1_id` = ?", info.Category1ID)
		searchCount++
	}
	if info.Category2ID > 0 {
		db.Where("`category2_id` = ?", info.Category2ID)
		searchCount++
	}
	if info.Category3ID > 0 {
		db.Where("`category3_id` = ?", info.Category3ID)
		searchCount++
	}
	if info.BrandNames != "" {
		db.Where("`brand_name` = ?", info.BrandID)
		searchCount++
	}
	if info.PromotionRate.To > 0 {
		db.Where("profit_rate < ?", info.PromotionRate.To)
		searchCount++
	}
	if info.PromotionRate.From > 0 {
		db.Where("profit_rate >= ?", info.PromotionRate.From)
		searchCount++
	}
	if info.RangeType != "" {
		if info.RangeType == "guide_price" {
			db.Where("`market_price` >= ?", info.RangeForm)
			db.Where("`market_price` <= ?", info.RangeTo)
			searchCount++
		}
		if info.RangeType == "agreement_price" {
			db.Where("`price` >= ?", info.RangeForm)
			db.Where("`price` <= ?", info.RangeTo)
			searchCount++
		}

	}
	if searchCount > 0 {
		err = db.Pluck("spu_id", &spuIds).Error
	}
	if err != nil {
		return
	}
	dbProduct := source.DB().Model(&model.GuanaitongProduct{}).Preload("Skus")
	if len(spuIds) > 0 {
		dbProduct.Where("`spu_id` in (?)", spuIds)
	}
	if info.IsImport > 0 {

		if info.IsImport == 1 {
			dbProduct.Where("is_import = 1")

		} else if info.IsImport == 2 {
			dbProduct.Where("is_import = 0")

		}

	}
	err = dbProduct.Count(&total).Error
	err = dbProduct.Limit(limit).Offset(offset).Find(&youxuanGoods).Error

	data = gat.ProductToGoods(youxuanGoods)
	return
}

func (gat *Guanaitong) ProductToGoods(data []model.GuanaitongProduct) (list []publicModel.Goods) {

	for _, v := range data {
		gsm := v.Skus
		var minPrice, minMarketPrice, minSalePrice float64
		var productStock int
		var status int
		for _, sku := range gsm {
			productStock += sku.Stock
			if minPrice == 0 || sku.Price <= minPrice {
				minPrice = sku.Price
				minMarketPrice = sku.MarketPrice
				minSalePrice = sku.SalePrice

			}

			status += sku.SkuState
		}
		var rate float64
		if minMarketPrice > minPrice {
			rate = utils.Decimal((minSalePrice - minPrice) / minSalePrice)
		}
		if status > 0 {
			status = 1
		}
		var cateName string
		if len(gsm[0].Categories) == 3 {
			cateName = gsm[0].Categories[0].CategoryName + "," + gsm[0].Categories[1].CategoryName + "," + gsm[0].Categories[2].CategoryName
		}
		var images []model.Pic
		var imageUrl string
		if len(gsm[0].Pics) > 0 {
			for _, pic := range gsm[0].Pics {
				if pic.PicType == 1 {
					images = append(images, pic)
				}
			}
			if len(images) > 0 {
				sort.Slice(images, func(i, j int) bool {
					return images[i].PicSort > images[j].PicSort // 倒序排序，Age 大的排在前面
				})
				imageUrl = images[0].PicUrl
			}

		}
		var item publicModel.Goods
		item = publicModel.Goods{
			GatherSupplyID:    GatherSupplyID,
			ThirdCategoryName: cateName,
			ThirdBrandName:    gsm[0].BrandName,
			MarketPrice:       uint(minMarketPrice * 100),
			ID:                int(v.ID),
			IsImport:          uint(v.IsImport),
			ProductID:         int(v.ID),
			TotalStock:        0,
			Cover:             imageUrl,
			Status:            status,
			Stock:             uint(productStock),
			Title:             gsm[0].ProductTitle,
			CategoryIds:       []string{},
			CostPrice:         uint(minPrice * 100),
			AgreementPrice:    uint(minPrice * 100),
			ActivityPrice:     uint(minMarketPrice * 100),
			GuidePrice:        uint(minMarketPrice * 100),
			Rate:              rate,
			SalePrice:         uint(minSalePrice * 100),
			SN:                v.SpuId,
		}
		if item.MarketPrice < item.AgreementPrice {
			item.Rate = 0
		}
		list = append(list, item)
	}
	return

}

func (gat *Guanaitong) DeleteGoods(id uint) (err error) {
	return
}

func (gat *Guanaitong) ImportGoodsRun(info request.GetGoodsSearch) (err error, data interface{}) {
	db := source.DB().Model(&model.GuanaitongProduct{})
	//db.Where("gather_supply_id = ?", info.GatherSupplyID)
	db.Where("status = 1")

	if info.SearchWords != "" {
		db.Where("`product_title` like ?", "%"+info.SearchWords+"%")
	}

	if info.Category1ID > 0 {
		db.Where("`category1_id` = ?", info.Category1ID)
	}
	if info.Category2ID > 0 {
		db.Where("`category2_id` = ?", info.Category2ID)
	}
	if info.Category3ID > 0 {
		db.Where("`category3_id` = ?", info.Category3ID)
	}
	if info.BrandNames != "" {
		db.Where("`brand_name` = ?", info.BrandID)
	}
	if info.PromotionRate.To > 0 {
		db.Where("profit_rate < ?", info.PromotionRate.To)
	}
	if info.PromotionRate.From > 0 {
		db.Where("profit_rate >= ?", info.PromotionRate.From)
	}
	if info.RangeType != "" {
		if info.RangeType == "guide_price" {
			db.Where("`market_price` >= ?", info.RangeForm)
			db.Where("`market_price` <= ?", info.RangeTo)
		}
		if info.RangeType == "agreement_price" {
			db.Where("`price` >= ?", info.RangeForm)
			db.Where("`price` <= ?", info.RangeTo)
		}

	}
	db.Where("is_import = 0")

	var total int64
	err = db.Count(&total).Error
	if err != nil {
		log.Log().Info("查询导入数量失败", zap.Any("err", err))
		return
	}

	orderPN := GetOrderNo()
	goodsRecord := publicModel.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: int(total),
		Status:            1,
	}

	//获取搜索条件
	var searchText []byte
	searchText, err = json.Marshal(info)
	if err != nil {
		goodsRecord.CompletionStatus = 2
		goodsRecord.FailedReason = "解析搜索条件失败:" + err.Error()
		err = source.DB().Omit("goods_arr").Create(&goodsRecord).Error
		if err != nil {
			log.Log().Info("解析搜索条件失败,并且插入数据失败", zap.Any("err", goodsRecord.FailedReason+";"+err.Error()))
		}
		return
	} else {
		goodsRecord.SearchCriteria = string(searchText)
	}

	err = source.DB().Omit("goods_arr").Create(&goodsRecord).Error
	if err != nil {
		log.Log().Info("创建导入记录失败"+orderPN+":"+string(searchText), zap.Any("err", err))
		return
	}

	err = gat.RunGoodsConcurrent(nil, info, db, 1, orderPN)
	if err != nil {
		err = SetImportRecordFailed(orderPN, err.Error())
		return
	}

	fmt.Println("导入供应链商品全部完成")
	return
}

func SetImportRecordFailed(batch string, reason string) (err error) {
	err = source.DB().Model(publicModel.SupplyGoodsImportRecord{}).Where("batch=?", batch).Updates(&map[string]interface{}{"completion_status": 2, "failed_reason": reason}).Error
	return
}
func (gat *Guanaitong) RunGoodsConcurrent(wg *sync.WaitGroup, info request.GetGoodsSearch, db *gorm.DB, i int, orderPN string) (err error) {

	var ProductItem []model.GuanaitongProduct
	err = db.Find(&ProductItem).Error
	if err != nil {
		err = errors.New("获取优选缓存数据错误:" + err.Error())
		return
	}
	if len(ProductItem) > 0 {

		cateList := strings.Split(info.Categorys, ",")
		var cateId1, cateId2, cateId3 int
		if len(cateList) >= 2 {
			cateId1, err = strconv.Atoi(cateList[0])
			cateId2, err = strconv.Atoi(cateList[1])
			cateId3, err = strconv.Atoi(cateList[2])
		}

		var Item []publicModel.Goods

		var resultArr []string
		err = source.DB().Model(pmodel.Product{}).Where("gather_supply_id = ?", info.GatherSupplyID).Pluck("sn", &resultArr).Error
		if err != nil {
			err = errors.New("重复导入验证错误:" + err.Error())
			return
		}

		if len(ProductItem) <= 0 {
			fmt.Println("没有选择可导入的数据")
			err = errors.New("没有选择可导入的数据")
			return
		}

		Item = gat.ProductToGoods(ProductItem)
		idsArr := GetIdArr(Item)

		difference := collection.Collect(idsArr).Diff(resultArr).ToStringArray()

		//fmt.Println("查询到的导入数据：", idsArr)
		//fmt.Println("已经存在的数据：", resultArr)
		//fmt.Println("目前可以导入的数据：", difference)

		err = SetImportRepeat(orderPN, len(idsArr)-len(difference))
		if err != nil {
			err = errors.New("导入记录设置重复出现错误:" + err.Error())
			return
		}
		if len(difference) <= 0 {
			fmt.Println("没有可以导入的数据")
			err = source.DB().Model(publicModel.SupplyGoodsImportRecord{}).Where("batch=?", orderPN).Update("status", 2).Error
			err = errors.New("当前数据已经存在，请导入新数据")
			return
		}
		var goodsList []publicModel.Goods

		for _, v := range difference {

			for _, item := range Item {
				if item.SkuId == v {
					goodsList = append(goodsList, item)
				}
			}

		}

		var listGoods []*pmodel.Product

		err, listGoods, _ = gat.CommodityAssembly(goodsList, cateId1, cateId2, cateId3, GatherSupplyID, 1)
		if err != nil {
			err = errors.New("组装商品失败:" + err.Error())
			return
		}
		err = service2.FinalProcessing(listGoods, orderPN)
		if err != nil {
			err = errors.New("新建商品失败:" + err.Error())
			return
		}

		err = SetImported(listGoods)
		if err != nil {
			err = errors.New("修改商品导入状态失败:" + err.Error())
			return
		}
		err = SetImportRecordCompletion(orderPN)
		if err != nil {
			fmt.Println("变更导入记录状态错误", err)
			err = errors.New("变更导入记录状态错误:" + err.Error())
			return
		}

	} else {
		fmt.Println("获取失败的：", i, info.Limit, info.Source)
		err = errors.New("待导入商品数量为0")
	}
	return
}
func SetImported(listGoods []*pmodel.Product) (err error) {
	var skuUpdateMap []map[string]interface{}
	for _, item := range listGoods {
		var skuUpdateMapUpdateMapItem = make(map[string]interface{})
		skuUpdateMapUpdateMapItem["id"] = item.SourceGoodsID
		skuUpdateMapUpdateMapItem["is_import"] = 1
		skuUpdateMapUpdateMapItem["updated_at"] = time.Now().Format("2006-01-02 15:04:05")
		skuUpdateMap = append(skuUpdateMap, skuUpdateMapUpdateMapItem)
	}
	err = source.BatchUpdate(skuUpdateMap, "guanaitong_products", "id")
	return
}
func GetProductIds(list []*pmodel.Product) (resIds string) {
	var ids string
	for _, elem := range list {
		stringId := strconv.Itoa(int(elem.SourceGoodsID))
		ids = ids + stringId + ","
	}
	ids = ids[0 : len(ids)-1]
	resIds = ids
	return
}

func CreateGoods(goodsList []*pmodel.Product) (err error) {
	err = source.DB().CreateInBatches(&goodsList, 2000).Error
	if err != nil {
		return
	}
	var SupplyGoods []publicModel.SupplyGoods
	for _, goods := range goodsList {

		SupplyGoods = append(SupplyGoods, publicModel.SupplyGoods{
			SupplyGoodsID:  goods.SourceGoodsID,
			Source:         goods.Source,
			ProductID:      goods.ID,
			GatherSupplyID: goods.GatherSupplyID,
		})

		err = pMq.PublishMessage(goods.ID, pMq.Create, 0)
		if err != nil {
			return
		}

	}
	err = source.DB().CreateInBatches(SupplyGoods, 2000).Error
	return
}
func SetImportRepeat(batch string, quantity int) (err error) {
	if quantity <= 0 {
		return
	}
	err = source.DB().Model(publicModel.SupplyGoodsImportRecord{}).Where("batch=?", batch).Update("repeat_quantity", gorm.Expr("repeat_quantity + ?", quantity)).Error
	return
}

func GetOrderNo() (id string) {

	var node *leaf.IdNode
	var err error
	err, node = leaf.NewNode(20)
	if err != nil {
		return
	}
	err, id = node.NextId()
	if err != nil {
		return
	}
	return
}
func GetIdArr(list []publicModel.Goods) (arrIds []string) {
	for _, elem := range list {
		arrIds = append(arrIds, elem.SN)
	}
	return

}

func (gat *Guanaitong) GetCategory(info request.GetCategorySearch) (err error, data interface{}) {

	return
}
func (gat *Guanaitong) GetGroup() (err error, data interface{}) {
	return
}
func (gat *Guanaitong) GetCategoryChild(pid int, info request.GetCategoryChild) (err error, data interface{}) {
	var categorys []model.GuanaitongCategory
	err = source.DB().Where("parent_id = ?", pid).Find(&categorys).Error
	return err, categorys
}
func (gat *Guanaitong) RunConcurrent(wg *sync.WaitGroup, info request.GetCategorySearch, i int) (err error) {
	return
}
func (gat *Guanaitong) CommodityAssembly(list []publicModel.Goods, cateId1, cateId2, cateId3 int, gatherSupplyID uint, isCreate int) (err error, listGoods []*pmodel.Product, recordErrors []publicModel.SupplyGoodsImportRecordErrors) {

	idArr := GetIdArr(list)
	listArr := SplitArray(idArr, 100)
	var detailData = make(map[string]model.GuanaitongProduct)
	for index, item := range listArr {
		fmt.Println("循环分割id第", index, "次:", item)
		//stringIds := service.GetArrIds(item)
		fmt.Println("返回ids", item)
		var data map[string]model.GuanaitongProduct

		err, data = gat.BatchGetGoodsDetails(item)
		if err != nil {
			return
		}
		for detailIndex, detailItem := range data {
			detailData[detailIndex] = detailItem

		}
	}

	//_, detailData := BatchGetGoodsDetails(ids) //批量获取详情
	var riskManageRecord []publicModel.RiskManagementRecord

	for _, elem := range list {
		var isRisk int
		detail := detailData[elem.SN]
		realData := detail.Skus[0]
		var status int

		var costPrice, salePrice, activityPrice, guidePrice, originPrice uint

		goods := new(pmodel.Product)
		var maxPrice uint
		var minPrice, minCostPrice, minActivityPrice, minGuidePrice, minOriginPrice uint
		var minProfitRate float64
		for _, detailSku := range detail.Skus {
			var sku pmodel.Sku
			sku.Sn = detailSku.SkuId
			sku.SpecId = detailSku.SkuId
			sku.OriginalSkuID = int64(detailSku.ID)
			if uint(detailSku.LowestBuy) > goods.MinBuyQty {
				goods.MinBuyQty = uint(detailSku.LowestBuy)
			}
			err, costPrice, salePrice, activityPrice, guidePrice, originPrice = GetPricingPrice(uint(math.Round(detailSku.Price*100)), uint(math.Round(detailSku.SalePrice*100)), uint(math.Round(detailSku.MarketPrice*100)))
			if err != nil {
				return
			}
			sku.CostPrice = costPrice
			sku.Price = salePrice
			sku.OriginPrice = originPrice
			sku.GuidePrice = guidePrice
			sku.ActivityPrice = activityPrice
			sku.ID = 0
			sku.Stock = detailSku.Stock
			status += detailSku.SkuState

			if gatData.Management.ProductPriceStatus == 1 {
				if float64(sku.Price) < float64(sku.CostPrice)*float64(gatData.Management.Products)/100 {
					goods.IsDisplay = 0
					isRisk++
				}
			} else if gatData.Management.ProductPriceStatus == 2 {
				if (float64(sku.Price)-float64(sku.CostPrice))/float64(sku.CostPrice) < float64(gatData.Management.Profit)/100 {
					goods.IsDisplay = 0
					isRisk++
				}
			}
			for _, detailOption := range detailSku.Specs {
				sku.Options = append(sku.Options, pmodel.Option{
					SpecName:     "默认",
					SpecItemName: detailOption.SpecValue,
				})
			}

			sku.Title = detailSku.SkuName

			if sku.GuidePrice > 0 {
				sku.ProfitRate = utils.ExecProfitRate(sku.GuidePrice, sku.Price)
			} else {
				sku.ProfitRate = 0
			}

			if minProfitRate == 0 || sku.ProfitRate <= minProfitRate {
				minProfitRate = sku.ProfitRate
			}

			goods.Skus = append(goods.Skus, sku)
			goods.Stock += uint(detailSku.Stock)
			if sku.Price > maxPrice {
				maxPrice = sku.Price
			}
			if minPrice == 0 || sku.Price <= minPrice {
				minPrice = sku.Price
			}
			if minCostPrice == 0 || sku.CostPrice <= minCostPrice {
				minCostPrice = sku.CostPrice
			}
			if minActivityPrice == 0 || sku.ActivityPrice <= minActivityPrice {
				minActivityPrice = sku.ActivityPrice
			}
			if minGuidePrice == 0 || sku.GuidePrice <= minGuidePrice {
				minGuidePrice = sku.GuidePrice
			}
			if minOriginPrice == 0 || sku.OriginPrice <= minOriginPrice {
				minOriginPrice = sku.OriginPrice
			}

		}
		if status > 0 {
			status = 1
		}
		goods.IsDisplay = status
		goods.ProfitRate = minProfitRate
		goods.MinPrice = uint(int(goods.Price))
		goods.MaxPrice = maxPrice
		var brand = new(catemodel.Brand)
		if realData.BrandName != "" {
			brand.Name = realData.BrandName
			brand.Source = common2.GUANAITONG_SOURCE
			err = source.DB().Where(brand).FirstOrCreate(&brand).Error
			goods.BrandID = brand.ID
		}

		goods.Title = realData.ProductTitle
		goods.OriginPrice = minOriginPrice
		goods.Price = minPrice
		goods.CostPrice = minCostPrice
		goods.ActivityPrice = minActivityPrice
		goods.GuidePrice = minGuidePrice

		if gatData.Management.ProductPriceStatus == 1 {
			if float64(goods.Price) < float64(goods.CostPrice)*float64(gatData.Management.Products)/100 {
				goods.IsDisplay = 0
				isRisk++
			}
		} else if gatData.Management.ProductPriceStatus == 2 {
			if (float64(goods.Price)-float64(goods.CostPrice))/float64(goods.CostPrice) < float64(gatData.Management.Profit)/100 {
				goods.IsDisplay = 0
				isRisk++
			}
		}

		goods.ImageUrl = elem.Cover
		goods.SourceGoodsID = uint(detail.ID)
		goods.Sn = detail.SpuId
		goods.Source = common2.GUANAITONG_SOURCE
		goods.GatherSupplyID = GatherSupplyID

		if cateId1 == 0 || cateId2 == 0 || cateId3 == 0 {
			display := 1
			if len(detail.Skus[0].Categories) == 3 {
				var resCategory1 catemodel.Category
				var resCategory2 catemodel.Category
				var resCategory3 catemodel.Category

				for _, category := range detail.Skus[0].Categories {
					if category.CategoryLevel == 1 {
						resCategory1.Name = category.CategoryName
					}
					if category.CategoryLevel == 2 {
						resCategory2.Name = category.CategoryName
					}
					if category.CategoryLevel == 3 {
						resCategory3.Name = category.CategoryName
					}
				}
				resCategory1.Level = 1
				resCategory1.ParentID = 0
				resCategory1.IsDisplay = &display
				err = source.DB().Where("name = ? and level = ? and parent_id = ?", resCategory1.Name, 1, 0).FirstOrCreate(&resCategory1).Error
				if err != nil {
					return
				}
				resCategory2.Level = 2
				resCategory2.ParentID = resCategory1.ID
				resCategory2.IsDisplay = &display
				err = source.DB().Where("name = ? and level = ? and parent_id = ?", resCategory2.Name, 2, resCategory2.ParentID).FirstOrCreate(&resCategory2).Error
				if err != nil {
					return
				}
				resCategory3.Level = 3
				resCategory3.ParentID = resCategory2.ID
				resCategory3.IsDisplay = &display
				err = source.DB().Where("name = ? and level = ? and parent_id = ?", resCategory3.Name, 3, resCategory3.ParentID).FirstOrCreate(&resCategory3).Error
				if err != nil {
					return
				}
				goods.Category1ID = resCategory1.ID
				goods.Category2ID = resCategory2.ID
				goods.Category3ID = resCategory3.ID
			} else {
				continue
			}

		} else {
			goods.Category1ID = uint(cateId1)
			goods.Category2ID = uint(cateId2)
			goods.Category3ID = uint(cateId3)

		}

		goods.FreightType = 2

		/**
		处理轮播图
		*/
		var images []model.Pic
		var detailImages []model.Pic
		if len(realData.Pics) > 0 {
			for _, pic := range realData.Pics {
				if pic.PicType == 1 {
					images = append(images, pic)
				}
				if pic.PicType == 2 {
					detailImages = append(detailImages, pic)
				}

			}
			if len(images) > 0 {
				sort.Slice(images, func(i, j int) bool {
					return images[i].PicSort > images[j].PicSort // 倒序排序，Age 大的排在前面
				})
				goods.ImageUrl = images[0].PicUrl
			}
			if len(detailImages) > 0 {
				sort.Slice(detailImages, func(i, j int) bool {
					return detailImages[i].PicSort > detailImages[j].PicSort // 倒序排序，Age 大的排在前面
				})
			}
		}

		var gallery pmodel.Gallery
		for _, image := range images {
			gallery = append(gallery, pmodel.GalleryItem{
				Type: 1,
				Src:  image.PicUrl,
			})
		}
		goods.Gallery = gallery
		goods.DetailImages = "<p>"
		for _, dimage := range detailImages {
			goods.DetailImages += "<img src=\"" + dimage.PicUrl + "\">"
		}
		goods.DetailImages += "</p>"

		/**
		处理轮播图结束
		*/

		//处理资质json图片数组
		//--------处理详情json图片数组结束

		//处----------------理属性json数组
		//---------处理属性json数组结束
		//goods.Desc=detail.Description
		if isRisk > 0 {
			var riskRecord publicModel.RiskManagementRecord
			riskRecord.ProductID = goods.ID
			riskRecord.SourceGoodsID = goods.SourceGoodsID
			riskRecord.GatherSupplyID = goods.GatherSupplyID
			riskManageRecord = append(riskManageRecord, riskRecord)
		}

		if len(goods.Skus) > 0 {
			listGoods = append(listGoods, goods)
		} else {
			fmt.Println("无规格商品，不导入", goods.ID)
		}

	}
	err = source.DB().CreateInBatches(&riskManageRecord, 1000).Error

	return
}
func GetPricingPrice(price uint, salePrice uint, marketPrice uint) (err error, costPrice, agreementPrice, activityPrice, guidePrice, originPrice uint) {

	if gatData.Pricing.Strategy == 1 {
		originPrice = uint(salePrice)
		guidePrice = uint(marketPrice)
		activityPrice = uint(marketPrice)
		agreementPrice = uint(price)
		costPrice = uint(price)
	} else {
		var intXAdvice uint64
		if gatData.Pricing.SupplyAdvice == 1 {
			intXAdvice, _ = strconv.ParseUint(gatData.Pricing.SupplyAdviceAgreement, 10, 32)
			originPrice = price * uint(intXAdvice) / 100
		} else if gatData.Pricing.SupplyAdvice == 2 {
			intXAdvice, _ = strconv.ParseUint(gatData.Pricing.SupplyAdviceMarketing, 10, 32)
			originPrice = (marketPrice) * uint(intXAdvice) / 100
		} else if gatData.Pricing.SupplyAdvice == 3 {
			intXAdvice, _ = strconv.ParseUint(gatData.Pricing.SupplyAdviceOrigin, 10, 32)
			originPrice = (salePrice) * uint(intXAdvice) / 100
		} else {
			originPrice = salePrice
		}

		var intX uint64
		if gatData.Pricing.SupplySales == 1 {
			intX, _ = strconv.ParseUint(gatData.Pricing.SupplySalesAgreement, 10, 32)
			agreementPrice = price * uint(intX) / 100
		} else if gatData.Pricing.SupplySales == 2 {
			intX, _ = strconv.ParseUint(gatData.Pricing.SupplySalesMarketing, 10, 32)
			agreementPrice = marketPrice * uint(intX) / 100
		} else if gatData.Pricing.SupplySales == 3 {
			intX, _ = strconv.ParseUint(gatData.Pricing.SupplySalesOrigin, 10, 32)
			agreementPrice = salePrice * uint(intX) / 100
		} else {
			agreementPrice = price
		}

		var intCostX uint64
		if gatData.Pricing.SupplyCost == 1 {
			intCostX, _ = strconv.ParseUint(gatData.Pricing.SupplyCostAgreement, 10, 32)
			costPrice = uint(price) * uint(intCostX) / 100
		} else if gatData.Pricing.SupplyCost == 2 {
			intCostX, _ = strconv.ParseUint(gatData.Pricing.SupplyCostMarketing, 10, 32)
			costPrice = uint(marketPrice) * uint(intCostX) / 100
		} else if gatData.Pricing.SupplyCost == 3 {
			intCostX, _ = strconv.ParseUint(gatData.Pricing.SupplyCostOrigin, 10, 32)
			costPrice = uint(salePrice) * uint(intCostX) / 100
		} else {
			costPrice = uint(price)
		}

		var intGuideX uint64
		if gatData.Pricing.SupplyGuide == 1 {
			intGuideX, _ = strconv.ParseUint(gatData.Pricing.SupplyGuideAgreement, 10, 32)
			guidePrice = uint(price) * uint(intGuideX) / 100
		} else if gatData.Pricing.SupplyGuide == 2 {
			intGuideX, _ = strconv.ParseUint(gatData.Pricing.SupplyGuideMarketing, 10, 32)
			guidePrice = uint(marketPrice) * uint(intGuideX) / 100
		} else if gatData.Pricing.SupplyGuide == 3 {
			intGuideX, _ = strconv.ParseUint(gatData.Pricing.SupplyGuideOrigin, 10, 32)
			guidePrice = uint(salePrice) * uint(intGuideX) / 100
		} else {
			guidePrice = uint(marketPrice)
		}

		var intActivityX uint64
		if gatData.Pricing.SupplyActivity == 1 {
			intActivityX, _ = strconv.ParseUint(gatData.Pricing.SupplyActivityAgreement, 10, 32)
			activityPrice = uint(price) * uint(intActivityX) / 100
		} else if gatData.Pricing.SupplyActivity == 2 {
			intActivityX, _ = strconv.ParseUint(gatData.Pricing.SupplyActivityMarketing, 10, 32)
			activityPrice = uint(marketPrice) * uint(intActivityX) / 100
		} else if gatData.Pricing.SupplyActivity == 3 {
			intActivityX, _ = strconv.ParseUint(gatData.Pricing.SupplyActivityOrigin, 10, 32)
			activityPrice = uint(salePrice) * uint(intActivityX) / 100
		} else {
			activityPrice = uint(price)
		}
	}
	return

}

func (gat *Guanaitong) BatchGetGoodsDetails(ids []string) (err error, data map[string]model.GuanaitongProduct) {

	var detailList = make(map[string]model.GuanaitongProduct)
	var goods []model.GuanaitongProduct
	err = source.DB().Preload("Skus").Where("spu_id IN (?)", ids).Find(&goods).Error
	if err != nil {
		return
	}
	for _, good := range goods {
		detailList[good.SpuId] = good
	}
	data = detailList

	return
}
func (gat *Guanaitong) GoodsIsDisplayHandle(id int, isDisplay int) (err error) {
	var gatherSupplyIds []uint
	err = source.DB().Model(&publicModel.GatherSupply{}).Where("category_id = ?", common2.SUPPLY_HEHE).Pluck("id", &gatherSupplyIds).Error
	if err != nil || len(gatherSupplyIds) == 0 {
		return
	}
	var goods service.ProductForUpdate
	err = source.DB().Where("source_goods_id = ?", id).Where("gather_supply_id in ?", gatherSupplyIds).First(&goods).Error
	if err != nil {
		return
	}
	err = source.DB().Model(service.ProductForUpdate{}).Where("id = ?", goods.ID).Update("is_display", isDisplay).Error
	if err != nil {
		return
	}
	if isDisplay == 1 {
		err = pMq.PublishMessage(goods.ID, pMq.OnSale, 0)
	} else {
		err = pMq.PublishMessage(goods.ID, pMq.Undercarriage, 0)
	}
	if err != nil {
		return
	}
	return
}
func (gat *Guanaitong) GoodsStorageAdd(ids []int, supplyId uint) (err error, info interface{}) {
	return
}
func (gat *Guanaitong) GoodsPriceAlert(GoodsData callback2.GoodsCallBack) (err error) {
	return
}

type ProductListRespConfirm struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}
type ProductListResp struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		TotalCount int      `json:"total_count"`
		HasNext    bool     `json:"has_next"`
		DataList   []string `json:"data_list"`
	} `json:"data"`
}

type ProductDetailResp struct {
	Code int                   `json:"code"`
	Data []model.GuanaitongSku `json:"data"`
	Msg  string                `json:"msg"`
}
type ProductPriceResp struct {
	Code int         `json:"code"`
	Data []PriceData `json:"data"`
	Msg  string      `json:"msg"`
}
type PriceData struct {
	MarketPrice float64 `json:"market_price"`
	Price       float64 `json:"price"`
	SalePrice   float64 `json:"sale_price"`
	SkuId       string  `json:"sku_id"`
}

func (gat *Guanaitong) InitGoods() (err error) {
	err = source.DB().Exec("truncate table guanaitong_products").Error
	if err != nil {
		log.Log().Error("guanaitongProducts  truncate err ", zap.Any("err", err))
		return
	}
	err = source.DB().Exec("truncate table guanaitong_skus").Error
	if err != nil {
		log.Log().Error("guanaitongProducts  truncate err ", zap.Any("err", err))
		return
	}
	var hasNext bool
	var ids []string
	var i = 0
	var gatGoodsDetails []model.GuanaitongSku
	for {
		i++
		err, hasNext, ids = gat.GetGoodsIds(100, i)
		if err != nil {
			return
		}
		idArray := SplitArray(ids, 20)
		for _, array := range idArray {
			var details []model.GuanaitongSku
			err, details = gat.GetGoodsDetails(array)
			if err != nil {
				return
			}

			var priceData []PriceData
			err, priceData = gat.GetGoodsPrice(array)
			if err != nil {
				return
			}
			for _, detail := range details {
				for _, cate := range detail.Categories {
					err = source.DB().Where("category_id = ?", cate.CategoryId).FirstOrCreate(&cate).Error
					if err != nil {
						return
					}
					if cate.CategoryLevel == 1 {
						detail.Category1Id = cate.CategoryId
					}
					if cate.CategoryLevel == 2 {
						detail.Category2Id = cate.CategoryId
					}
					if cate.CategoryLevel == 3 {
						detail.Category3Id = cate.CategoryId
					}
				}
				for _, price := range priceData {
					if price.SkuId == detail.SkuId {
						detail.Price = price.Price
						detail.SalePrice = price.SalePrice
						detail.MarketPrice = price.MarketPrice
						detail.ProfitRate = utils.Decimal((detail.MarketPrice - detail.Price) / detail.MarketPrice)
					}
				}
				gatGoodsDetails = append(gatGoodsDetails, detail)
			}
		}
		time.Sleep(500 * time.Millisecond)
		if !hasNext {
			break
		}
	}
	var gatGoods []model.GuanaitongProduct
	var gatSkuMap = make(map[string][]model.GuanaitongSku)
	for _, gsd := range gatGoodsDetails {
		gatSkuMap[gsd.SpuId] = append(gatSkuMap[gsd.SpuId], gsd)
	}
	for _, gsm := range gatSkuMap {
		gatGoods = append(gatGoods, model.GuanaitongProduct{
			SpuId: gsm[0].SpuId,
		})
	}
	err = source.DB().CreateInBatches(&gatGoods, 500).Error
	if err != nil {
		return
	}
	err = source.DB().CreateInBatches(&gatGoodsDetails, 500).Error
	if err != nil {
		return
	}

	return
}

func (gat *Guanaitong) GetGoodsDetails(ids []string) (err error, data []model.GuanaitongSku) {
	var signModel = common.Sign{
		AppSecret: gatData.BaseInfo.AppSecret,
		AppId:     gatData.BaseInfo.AppId,
		Url:       gatData.BaseInfo.Host,
		Timestamp: strconv.Itoa(int(time.Now().Unix())),
	}
	var getSignMap = make(map[string]interface{})
	getSignMap["sku_ids"] = strings.Join(ids, ",")

	var result []byte
	err, result = signModel.PostForm(getSignMap, "/e-commerce/distribution-v2/product/searchSkuDetail")
	if err != nil {
		log.Log().Info("guanaitong getDetails post form failed：" + err.Error())
		return
	}
	var respConfirm ProductListRespConfirm
	err = json.Unmarshal(result, &respConfirm)
	if err != nil {
		log.Log().Info("guanaitong getDetails json unmarshal failed：" + err.Error())
		return
	}
	if respConfirm.Code != 0 {
		log.Log().Info("guanaitong getDetails request failed：" + respConfirm.Msg)
		return
	}
	var resp ProductDetailResp
	err = json.Unmarshal(result, &resp)
	if err != nil {
		log.Log().Info("guanaitong getDetails json unmarshal failed2：" + err.Error())
		return
	}
	data = resp.Data
	return
}

func (gat *Guanaitong) GetGoodsPrice(ids []string) (err error, data []PriceData) {
	var signModel = common.Sign{
		AppSecret: gatData.BaseInfo.AppSecret,
		AppId:     gatData.BaseInfo.AppId,
		Url:       gatData.BaseInfo.Host,
		Timestamp: strconv.Itoa(int(time.Now().Unix())),
	}
	var getSignMap = make(map[string]interface{})
	getSignMap["sku_ids"] = strings.Join(ids, ",")
	var result []byte
	err, result = signModel.PostForm(getSignMap, "/e-commerce/distribution-v2/product/searchSkuPrice")
	if err != nil {
		log.Log().Info("guanaitong getDetails post form failed：" + err.Error())
		return
	}
	var respConfirm ProductListRespConfirm
	err = json.Unmarshal(result, &respConfirm)
	if err != nil {
		log.Log().Info("guanaitong getDetails json unmarshal failed：" + err.Error())
		return
	}
	if respConfirm.Code != 0 {
		log.Log().Info("guanaitong getDetails request failed：" + respConfirm.Msg)
		return
	}
	var resp ProductPriceResp
	err = json.Unmarshal(result, &resp)
	if err != nil {
		log.Log().Info("guanaitong getDetails json unmarshal failed2：" + err.Error())
		return
	}
	data = resp.Data
	return
}

func (gat *Guanaitong) GetGoodsIds(pageSize int, pageIndex int) (err error, hasNext bool, Ids []string) {
	var signModel = common.Sign{
		AppSecret: gatData.BaseInfo.AppSecret,
		AppId:     gatData.BaseInfo.AppId,
		Url:       gatData.BaseInfo.Host,
		Timestamp: strconv.Itoa(int(time.Now().Unix())),
	}
	var getSignMap = make(map[string]interface{})
	getSignMap["page_index"] = pageIndex
	getSignMap["page_size"] = pageSize
	var result []byte
	err, result = signModel.PostForm(getSignMap, "/e-commerce/distribution-v2/product/querySkuByPage")
	if err != nil {
		log.Log().Info("guanaitong getDetails post form failed：" + err.Error())
		return
	}
	var respConfirm ProductListRespConfirm
	err = json.Unmarshal(result, &respConfirm)
	if err != nil {
		log.Log().Info("guanaitong getIds json unmarshal failed：" + err.Error())
		return
	}
	if respConfirm.Code != 0 {
		log.Log().Info("guanaitong getIds request failed：" + respConfirm.Msg)
		return
	}
	var resp ProductListResp
	err = json.Unmarshal(result, &resp)
	if err != nil {
		log.Log().Info("guanaitong getIds json unmarshal failed2：" + err.Error())
		return
	}
	hasNext = resp.Data.HasNext
	Ids = resp.Data.DataList
	return
}
func (gat *Guanaitong) GetSupplyBalance(GatherSupplyID uint) (err error, balance interface{}) {
	return
}
func SplitArray(arr []string, num int64) [][]string {
	max := int64(len(arr))
	//判断数组大小是否小于等于指定分割大小的值，是则把原数组放入二维数组返回
	if max <= num {
		return [][]string{arr}
	}
	//获取应该数组分割为多少份
	var quantity int64
	if max%num == 0 {
		quantity = max / num
	} else {
		quantity = (max / num) + 1
	}
	//声明分割好的二维数组
	var segments = make([][]string, 0)
	//声明分割数组的截止下标
	var start, end, i int64
	for i = 1; i <= quantity; i++ {
		end = i * num
		if i != quantity {
			segments = append(segments, arr[start:end])
		} else {
			segments = append(segments, arr[start:])
		}
		start = i * num
	}
	return segments
}

func InitCategory(list []publicModel.Goods, level uint, parentMap map[string]uint, pid uint) (err error, cateMap map[string]uint) {
	var cateData []catemodel.Category
	var pids []uint
	if pid > 0 {
		pids = append(pids, pid)
	}
	cateMap = make(map[string]uint)
	var cateName []string
	display := 1
	key := level - 1
	for _, ce := range list {
		var cateItem catemodel.Category
		cateList := strings.Split(ce.ThirdCategoryName, ",")
		if len(cateList) > int(key) && cateList[key] != "" {
			var repetCheck = 0
			for _, cd := range cateData {
				if cd.Name == cateList[key] {
					repetCheck++
				}
			}
			if repetCheck > 0 {
				continue //数组中已经存在此名称，直接跳过
			}
			cateItem.IsDisplay = &display
			if level == 1 {
				cateItem.ParentID = 0 //一级分类父id必须是0
			} else {
				if pid > 0 {
					cateItem.ParentID = pid //直接用导入时设置的分类
				} else {
					if _, ok := parentMap[cateList[key-1]]; ok {
						//此为存在
						cateItem.ParentID = parentMap[cateList[key-1]] //利用父级分类的name，从上级分类map中获取父id
						pids = append(pids, cateItem.ParentID)
					} else {
						continue
					}
				}
			}
			cateItem.Level = int(level)
			cateItem.Name = cateList[key]
			cateData = append(cateData, cateItem)
			cateName = append(cateName, cateItem.Name)
		}
	}
	var exitsCate []catemodel.Category
	err = source.DB().Where("name in ?", cateName).Where("level = ?", level).Where("parent_id in ?", pids).Find(&exitsCate).Error
	if err != nil {
		return
	}
	var saveCateData []catemodel.Category
	for _, cdCheck := range cateData {
		var isCheck = 0
		for _, ecCheck := range exitsCate {
			if ecCheck.Name == cdCheck.Name {
				isCheck++
			}
		}
		if isCheck == 0 {
			saveCateData = append(saveCateData, cdCheck) //重新生成一个数据库中不存在的数据的数组
		}
	}
	err = source.DB().CreateInBatches(saveCateData, 2000).Error
	if err != nil {
		return
	}
	saveCateData = append(saveCateData, exitsCate...) //合并新增的和已经有的
	for _, cd := range saveCateData {
		cateMap[cd.Name] = cd.ID
	}
	return
}

func SetImportRecordCompletion(batch string) (err error) {
	err = source.DB().Model(publicModel.SupplyGoodsImportRecord{}).Where("batch=?", batch).Update("completion_status", 1).Error
	return
}
