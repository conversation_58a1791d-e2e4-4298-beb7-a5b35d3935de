package cron

import (
	"encoding/json"
	common "guanaitong-supply/common"
	goods2 "guanaitong-supply/component/goods"
	"guanaitong-supply/mq"
	common2 "public-supply/common"
	"public-supply/model"
	"strconv"
	"strings"
	"time"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func HandleMessagePool() {
	task := cron.Task{
		Key:  "handlegatmessagepool",
		Name: "处理关爱通消息池",
		//Spec: "0 47 */1 * * *",
		Spec: "*/10 * * * * *",
		Handle: func(task cron.Task) {
			MessagePool()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

type MessagePoolResponse struct {
	Code int             `json:"code"`
	Msg  string          `json:"msg"`
	Data []mq.GatMessage `json:"data"`
}

func MessagePool() {
	var types = []int{1, 2, 3, 4, 5, 8, 9}
	var gatherSuppliers []model.GatherSupply
	err := source.DB().Where("category_id = ?", common2.SUPPLY_GAT).Find(&gatherSuppliers).Error
	if err != nil {
		return
	}
	for _, gatherSupply := range gatherSuppliers {
		var gat = goods2.Guanaitong{}
		err = gat.InitSetting(gatherSupply.ID)
		if err != nil {
			continue
		}
		var signModel = common.Sign{
			AppId:     gat.GatData.BaseInfo.AppId,
			AppSecret: gat.GatData.BaseInfo.AppSecret,
			Url:       gat.GatData.BaseInfo.Host,
		}
		var messages []mq.GatMessage
		for _, typeId := range types {
			var requestParams = make(map[string]interface{})
			requestParams["type"] = typeId
			signModel.Timestamp = strconv.Itoa(int(time.Now().Unix()))
			var result []byte
			err, result = signModel.PostForm(requestParams, "/e-commerce/distribution-v2/message/searchMessage")
			if err != nil {
				return
			}
			var respConfirm goods2.ProductListRespConfirm
			err = json.Unmarshal(result, &respConfirm)
			if err != nil {
				log.Log().Info("guanaitong messagePool json unmarshal failed：" + err.Error())
				return
			}
			if respConfirm.Code != 0 {
				log.Log().Info("guanaitong messagePool request failed：" + respConfirm.Msg)
				return
			}
			var resp MessagePoolResponse
			err = json.Unmarshal(result, &resp)
			if err != nil {
				log.Log().Info("guanaitong messagePool json unmarshal failed2：" + err.Error())
				return
			}
			for _, row := range resp.Data {
				row.GatherSupplyID = gatherSupply.ID
				messages = append(messages, row)
			}
		}
		var messageIds []string
		for _, message := range messages {
			messageIds = append(messageIds, message.Id)
			err = mq.PublishMessage(message)
		}
		var requestParams = make(map[string]interface{})
		requestParams["message_ids"] = strings.Join(messageIds, ",")
		signModel.Timestamp = strconv.Itoa(int(time.Now().Unix()))
		var result []byte
		err, result = signModel.PostForm(requestParams, "/e-commerce/distribution-v2/message/delMessage")
		if err != nil {
			return
		}
		var respConfirm goods2.ProductListRespConfirm
		err = json.Unmarshal(result, &respConfirm)
		if err != nil {
			log.Log().Info("guanaitong deletemessagePool json unmarshal failed：" + err.Error())
			return
		}
	}

	return
}
