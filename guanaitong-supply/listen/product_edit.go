package listener

import (
	"encoding/json"
	"go.uber.org/zap"
	"guanaitong-supply/component/goods"
	"guanaitong-supply/mq"
	"yz-go/component/log"
)

func PushCustomerGatProductEditHandles() {
	//商品信息修改
	mq.PushHandles("gatProductEdit", 1, func(message mq.GatMessage) error {
		if message.Type == 1 || message.Type == 3 {
			log.Log().Info("关爱通接到商品编辑消息", zap.Any("message", message))
			var result ProductMessage
			err := json.Unmarshal([]byte(message.Result), &result)
			if err != nil {
				return nil
			}
			var gat = goods.Guanaitong{}
			err = gat.InitSetting(message.GatherSupplyID)
			if err != nil {
				return nil
			}
			err = gat.ProductEdit(result.SkuId)
			if err != nil {
				log.Log().Error("关爱通商品编辑失败", zap.Error(err))
			}
			return nil
		}
		return nil
	})
}
