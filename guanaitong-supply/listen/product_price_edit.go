package listener

import (
	"encoding/json"
	"go.uber.org/zap"
	"guanaitong-supply/component/goods"
	"guanaitong-supply/mq"
	"yz-go/component/log"
)

func PushCustomerGatProductPriceEditHandles() {
	//商品信息修改
	mq.PushHandles("gatProductPriceEdit", 10, func(message mq.GatMessage) error {
		if message.Type == 2 {
			log.Log().Info("关爱通接到商品价格变动消息", zap.Any("message", message))
			var result ProductMessage
			err := json.Unmarshal([]byte(message.Result), &result)
			if err != nil {
				return nil
			}
			var gat = goods.Guanaitong{}
			err = gat.InitSetting(message.GatherSupplyID)
			if err != nil {
				return nil
			}
			err = gat.ProductPriceEdit(result.SkuId)
		}
		return nil
	})
}
