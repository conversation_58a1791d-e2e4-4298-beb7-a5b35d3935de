package cron

import (
	"encoding/json"
	"fmt"
	reqv3 "github.com/imroc/req/v3"
	"go.uber.org/zap"
	model7 "guangdian/model"
	"net/http"
	v1 "order/api/v1"
	model2 "order/model"
	orderRequest2 "order/request"
	"public-supply/common"
	"public-supply/model"
	setting2 "public-supply/setting"
	"strconv"
	"strings"
	"time"
	yjforder "yijifen/component/order"
	model3 "yijifen/model"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
	"yz-go/utils"
)

func PushGuangDianOrderHandle() {

	log.Log().Info("cron  PushGuangdianOrderHandle")

	var gatherList []model.GatherSupply
	err := source.DB().Where("category_id = ?", common.SUPPLY_GD).Where("deleted_at is null").Find(&gatherList).Error
	if err != nil {
		return
	}

	for _, v := range gatherList {

		CreateCronTaskSale(int(v.ID))

	}

}

var GuangdianSetting model7.Setting

func InitSetting(taskID int) {
	err, setting := setting2.GetSetting("gatherSupply" + strconv.Itoa(taskID))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}
	model3.SetYjfKd()
	err = json.Unmarshal([]byte(setting.Value), &GuangdianSetting)
	if err != nil {

		return
	}

}
func CreateCronTaskSale(taskID int) {

	InitSetting(taskID)

	var cronStr string

	cronStr = "0 */10 * * * *"

	cron.PushTask(cron.Task{
		Key:  "gdordersendupdate" + strconv.Itoa(taskID),
		Name: "gdordersendupdate订单发货" + strconv.Itoa(taskID),
		Spec: cronStr,
		Handle: func(task cron.Task) {
			OrderSend()

		},
		Status: cron.ENABLED,
	})

}

func OrderSend() {

	var order []model2.Order

	err := source.DB().Where("gather_supply_sn!='' and gather_supply_type=? and status=?", common.SUPPLY_GD, 1).Find(&order).Error
	if err != nil {
		return
	}

	for _, item := range order {
		//if item.ID != 82225 {
		//	continue
		//}
		orderInfoErr, orderInfo := SelectOrderInfo(item.GatherSupplySN)
		if orderInfoErr != nil {
			continue
		}

		//for _, orderInfoItem := range orderInfo.Data.Data {
		if orderInfo.Data.Code == "0" { //正常

			var orderItemIds []orderRequest2.OrderItemSendInfo
			var orderRequest v1.HandleOrderRequest
			var orderItems model2.OrderItem
			err = source.DB().Where("order_id=?", item.ID).First(&orderItems).Error

			orderItemIds = append(orderItemIds, orderRequest2.OrderItemSendInfo{
				ID:  orderItems.ID,
				Num: orderItems.Qty,
			})

			orderRequest.OrderID = orderItems.OrderID
			orderRequest.ExpressNo = strings.TrimSpace(orderInfo.Data.Data.ExpressNo) //过滤掉左右空格
			orderRequest.OrderItemIDs = orderItemIds
			expressName := GetExpressName(orderInfo.Data.Data.LogisticsName)
			_, orderRequest.CompanyCode = yjforder.ExpressList(expressName)
			log.Log().Info("guangdian  发货信息", zap.Any("info", orderRequest))
			err = yjforder.ExpressSent(orderRequest)

			if err != nil {
				log.Log().Error("guangdian ExpressSent", zap.Any("info", err))
			}

		}

		//}

	}

}

func GetExpressName(kd string) string {
	for _, item := range model3.KDArr {
		if item.Value == kd {
			return item.Label
		}

	}
	return ""
}

func SelectOrderInfo(orderID string) (err error, OrderInfo model7.ExpressData) {

	//var numbers interface{}
	y := GuangdianSetting
	var reqMap = make(map[string]interface{})
	var body = make(map[string]interface{})
	var reqHeader = map[string]string{}
	times := strconv.Itoa(int(time.Now().Unix() * 1000))
	sign := y.Key + ":" + y.Secret + ":" + times
	md5Sign := utils.MD5V([]byte(sign))
	reqMap["appId"] = y.AppId
	reqMap["timestamp"] = times
	reqMap["sign"] = md5Sign
	body["orderId"] = orderID

	reqMap["body"] = body
	url := y.ApiUrl + "/api/v1/guangdian/queryLogistics"
	client := reqv3.C()

	log.Log().Info("SelectOrderInfo请求info", zap.Any("info", reqMap))

	resp, err := client.R().SetRetryCount(2). //  重试次数
							SetRetryCondition(func(resp *reqv3.Response, err error) bool {
			return (err != nil) || (resp.StatusCode != http.StatusOK)
		}).
		SetRetryHook(func(resp *reqv3.Response, err error) {
			log.Log().Error("guangdian queryLogistics   重试 ", zap.Any("data", resp.String()), zap.Any("err", err))
		}).SetRetryBackoffInterval(1*time.Second, 59*time.Second).
		SetSuccessResult(&OrderInfo).SetBodyJsonMarshal(reqMap).SetHeaders(reqHeader).Post(url)
	log.Log().Info("queryLogistics 返回info", zap.Any("info", resp.String()))

	if err != nil {
		fmt.Println(err)
	}
	if resp.IsSuccessState() {
		fmt.Println(resp.Status)
	}

	return
}
