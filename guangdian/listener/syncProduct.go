package listener

import (
	"context"
	"fmt"
	"go.uber.org/zap"
	"time"
	YjfGoods "yijifen/component/goods"
	yjfMq "yijifen/mq"
	"yz-go/component/log"
	"yz-go/source"
)

var Yjf YjfGoods.Yjf

func PushYjfSyncProductCustomerHandles() {

	yjfMq.PushHandles("yjfSyncProductToLocal", 3, func(data yjfMq.SendData) error {

		fmt.Println(data)

		Yjf.InitSetting(data.GatherSupplyId)

		err := Yjf.SyncGoodsToLocal(data.Page)
		if data.Page == data.PageCount {
			var ctx = context.Background()
			err = source.Redis().SetEX(ctx, "yjfMqSyncStatus", "", time.Hour*10).Err()
		}
		if err != nil {
			log.Log().Error("PushyjfSyncProductCustomerHandles", zap.Any("err", err))
		}
		return nil
	})

}
