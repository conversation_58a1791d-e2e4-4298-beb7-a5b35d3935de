package model

import (
	"yz-go/request"
	"yz-go/source"
)

type RequestRechargeRecord struct {
	PhoneNumber string `json:"phone_number" form:"phone_number"`
	SettlementM string `json:"settlement_m" form:"settlement_m"`
}

type RequestNumberData struct {
	Code     string `json:"code" form:"code"`
	FuzzyKey string `json:"fuzzyKey" form:"fuzzyKey"`
	FuzzyTag string `json:"fuzzyTag" form:"fuzzyTag"`
}
type SearchApp struct {
	request.PageInfo
	AppId uint `json:"app_id"`
}

type SearchUserInfo struct {
	ThirdOrderSn string `json:"third_order_sn"`
}

type SearchSettlement struct {
	request.PageInfo
	UserID      string `json:"user_id"`
	NickName    string `json:"nick_name"`
	Phone       string `json:"phone"`
	AppID       string `json:"app_id"`
	PhoneNumber string `json:"phone_number"`
}

type GuangDianSettlement struct {
	source.Model
	SettlementM         string `json:"settlement_m"`
	BillingCycle        string `json:"billing_cycle"`
	PhoneNumber         string `json:"phone_number"`
	MenuName            string `json:"menu_name"`
	Monetary            string `json:"monetary"`
	DivisibleCommission string `json:"divisible_commission"`
	NonPaymentBrokerage string `json:"non_payment_brokerage"`
	AccessMonth         string `json:"access_month"`
	ActivationTime      string `json:"activation_time"`
	Status              int    `json:"status"`
	OrderID             uint   `json:"order_id" gorm:"unique"`
	AppID               uint   `json:"app_id" `
	UserID              uint   `json:"user_id" `
	RewardRatio         uint   `json:"reward_ratio" `
	RewardAmount        uint   `json:"reward_amount" `
}

type RechargeRecord struct {
	Status int                   `json:"status"`
	Msg    string                `json:"msg"`
	Data   []GuangDianSettlement `json:"data"`
}

type Numbers struct {
	Msg  string `json:"msg"`
	Code int    `json:"code"`
	Data struct {
		PageNum   int `json:"pageNum"`
		Total     int `json:"total"`
		TotalPage int `json:"totalPage"`
		NumList   []struct {
			AccessNum     string `json:"accessNum"`
			BeautifulTag  string `json:"beautifulTag"`
			ReserveFee    string `json:"reserveFee"`
			LevelId       string `json:"levelId"`
			DepositMonth  string `json:"depositMonth"`
			DepositAmount string `json:"depositAmount"`
		} `json:"numList"`
	} `json:"data"`
}
type Area struct {
	Msg  string `json:"msg"`
	Code int    `json:"code"`
	Data []struct {
		AreaNumber *string     `json:"areaNumber"`
		AreaCode   string      `json:"areaCode"`
		ParentId   *string     `json:"parentId"`
		AreaByname string      `json:"areaByname"`
		AreaName   string      `json:"areaName"`
		Id         string      `json:"id"`
		Children   interface{} `json:"children"`
	} `json:"data"`
}

type OrderData struct {
	GoodsId        string `json:"goodsId"`
	GoodsSkuId     string `json:"goodsSkuId"`
	PayMoney       string `json:"payMoney"`
	OfferCodes     string `json:"offerCodes"`
	Number         string `json:"number"`
	CustName       string `json:"custName"`
	IdenNr         string `json:"idenNr"`
	ContNumber     string `json:"contNumber"` //联系电话
	RegionId       string `json:"regionId"`   //地市编码（与号码接口的一致）
	PreNumber      string `json:"preNumber"`  //预占用
	ReceiveName    string `json:"receiveName"`
	ReceiveMobile  string `json:"receiveMobile"`
	Area           string `json:"area"`           //收货省市县编码，用英文逗号分割
	ReceiveAddress string `json:"receiveAddress"` //收货人详细地址 精确到门牌号
}
type OrderInfo struct {
	Msg  string `json:"msg"`
	Code int    `json:"code"`
	Data string `json:"data"`
}
type CancelInfo struct {
	Msg  string `json:"msg"`
	Code int    `json:"code"`
}

type StorageAuth struct {
	ApplicationLevels []SourceMap `json:"application_level_ids"`
}
type SourceMap struct {
	SourceID            uint  `json:"source_id"`
	ApplicationLevelIDs []int `json:"application_level_ids"`
}
type Setting struct {
	Key         string      `json:"key"`
	Secret      string      `json:"secret"`
	AppId       string      `json:"app_id"`
	ApiUrl      string      `json:"api_url"`
	Ratio       int         `json:"ratio"`
	DefaultCate string      `json:"default_cate"`
	DefaultImg  string      `json:"default_img"`
	StorageAuth StorageAuth `json:"storage_auth"`
}
type ExpressData struct {
	Msg  string `json:"msg"`
	Code int    `json:"code"`
	Data struct {
		Code string `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			TradeNo       interface{} `json:"tradeNo"`
			ExpressNo     string      `json:"expressNo"`
			LogisticsName string      `json:"logisticsName"`
			InfoList      []struct {
				Content       string `json:"content"`
				OperationTime string `json:"operationTime"`
			} `json:"infoList"`
		} `json:"data"`
	} `json:"data"`
}
