package service

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"go.uber.org/zap"
	"hbsk/model"
	"strconv"
	"strings"
	"time"
	"yz-go/component/log"
	"yz-go/source"
	"yz-go/utils"
)

var setting model.HolderSetting

var HttpUrl = "https://api.hbsk.com/"

func HmacSha256(data string, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

func SmsVerify(param model.SmsData) (err error) {
	url := HttpUrl + "share/holder/sms/verify"
	var resData []byte
	headerData := make(map[string]interface{})
	headerData["key"] = Key
	headerData["time"] = strconv.Itoa(int(time.Now().Unix()))
	jsonData, _ := json.Marshal(&param)
	shaStr := string(jsonData) + Secret
	sign := Sha256(shaStr)
	headerData["sign"] = sign
	headerData["data"] = string(jsonData)
	reqData, _ := json.Marshal(headerData)
	log.Log().Info("SmsVerify 请求", zap.Any("info", reqData))

	resData = utils.HttpPostJson(reqData, url)
	log.Log().Info("SmsVerify 返回", zap.Any("info", resData))

	var responseData model.PublicResponseData
	err = json.Unmarshal(resData, &responseData)
	if responseData.Code != 0 {
		err = errors.New(responseData.Text)
		return
	}

	return
}

//个人分账方添加
func ShareHolder(requestData model.RequestPersonHolder) (err error) {
	var personHolder model.PersonHolder

	err = source.DB().Where("card_account=?", requestData.CardAccount).First(&personHolder).Error
	if personHolder.ID > 0 {
		err = errors.New("已经存在相同开户卡，不能重复开户")
		return
	}

	var param model.PostPersonHolder
	Copy(&requestData).To(&param)

	url := HttpUrl + "share/holder"
	var resData []byte
	headerData := make(map[string]interface{})
	headerData["key"] = Key
	headerData["time"] = strconv.Itoa(int(time.Now().Unix()))
	jsonData, _ := json.Marshal(&param)
	shaStr := string(jsonData) + Secret
	sign := Sha256(shaStr)
	headerData["sign"] = sign
	headerData["data"] = string(jsonData)
	reqData, _ := json.Marshal(headerData)
	log.Log().Info("ShareHolder 请求", zap.Any("info", reqData))

	resData = utils.HttpPostJson(reqData, url)
	log.Log().Info("ShareHolder 返回", zap.Any("info", resData))

	var publicResponseData model.PublicResponseData

	var responseHolderData model.ResponseHolderData
	err = json.Unmarshal(resData, &publicResponseData)
	if publicResponseData.Code != 0 {
		log.Log().Info("info", zap.Any("info", publicResponseData))
		err = errors.New(publicResponseData.Text)
		return
	}
	err = json.Unmarshal([]byte(publicResponseData.Data), &responseHolderData)
	if err != nil {
		log.Log().Info("publicResponseData.Data", zap.Any("info", err))
		return
	}

	Copy(&requestData).To(&personHolder)

	err = CreatePersonHolder(publicResponseData, personHolder)
	return err

}

func SupplierDetail(supplierID uint) (data interface{}) {

	var personHolder model.PersonHolder
	var enterpriseHolder model.EnterpriseHolder
	var mapData = make(map[string]interface{})

	source.DB().Where("supplier_id=?", supplierID).First(&personHolder)
	source.DB().Where("supplier_id=?", supplierID).First(&enterpriseHolder)

	mapData["personHolder"] = personHolder
	mapData["enterpriseHolder"] = enterpriseHolder
	data = mapData
	return

}

//个人分账方修改
func UpdateShareHolder(requestData model.UpdatePersonHolder) (err error) {

	var param model.PostPersonHolder
	Copy(&requestData).To(&param)
	url := HttpUrl + "share/holder/change"
	var resData []byte
	headerData := make(map[string]interface{})
	headerData["key"] = Key
	headerData["time"] = strconv.Itoa(int(time.Now().Unix()))
	jsonData, _ := json.Marshal(&param)
	shaStr := string(jsonData) + Secret
	sign := Sha256(shaStr)
	headerData["sign"] = sign
	headerData["data"] = string(jsonData)
	reqData, _ := json.Marshal(headerData)
	log.Log().Info("UpdateShareHolder 请求：", zap.Any("", string(reqData)))
	resData = utils.HttpPostJson(reqData, url)
	log.Log().Info("UpdateShareHolder 返回：", zap.Any("", string(resData)))

	if err != nil {
		return
	}
	var publicResponseData model.PublicResponseData
	var responseHolderData model.ResponseHolderData
	err = json.Unmarshal(resData, &publicResponseData)
	if publicResponseData.Code != 0 {
		err = errors.New(publicResponseData.Text)
		return err
	}
	err = json.Unmarshal([]byte(publicResponseData.Data), &responseHolderData)
	if err != nil {
		return
	}
	if responseHolderData.Code == "" {
		return errors.New("解析返回data数据错误")
	}
	var personHolder model.PersonHolder
	Copy(&requestData).To(&personHolder)
	if personHolder.Code != "" {
		err = source.DB().Where("code=?", requestData.Code).Updates(&personHolder).Error
		if err != nil {
			return
		}

	}

	return err

}

func CreatePersonHolder(publicResponseData model.PublicResponseData, personHolder model.PersonHolder) (err error) {
	var responseHolderData model.ResponseHolderData

	err = json.Unmarshal([]byte(publicResponseData.Data), &responseHolderData)
	if err != nil {
		log.Log().Error("CreatePersonHolder Unmarshal", zap.Any("err", err))
	}
	personHolder.Code = responseHolderData.Code //返回分账方编号
	err = source.DB().Create(&personHolder).Error
	return

}

func UploadFile() (err error) {

	return err

}

func DeleteHolder(delete model.DeleteHolder) (err error) {
	if delete.Type == 1 {
		err = source.DB().Delete(&model.PersonHolder{}, "id=?", delete.ID).Error
	}

	if delete.Type == 2 {
		err = source.DB().Delete(&model.EnterpriseHolder{}, "id=?", delete.ID).Error
	}
	return err

}

func GetBranch(name, code string) string {

	for _, item := range model.BranchList {
		if strings.Contains(item.BranchName, name) && item.BankNo == code {
			return item.BranchId
		}

	}
	return ""

}

func ManualShareEnterpriseHolder(param model.ManualEnterpriseHolder) (err error) {
	param.Type = "company"
	err = source.DB().Save(&param).Error

	return
}
func ManualPersonHolderr(param model.ManualPersonHolder) (err error) {
	param.Type = "person"
	err = source.DB().Save(&param).Error

	return
}

//企业分账方添加
func ShareEnterpriseHolder(param model.EnterpriseHolder) (err error) {

	id := GetBranch(param.CardBranch, param.CardBank)
	if id == "" {
		err = errors.New("未匹配到当前支行，请核对支行名称")
		return
	}

	param.CardBranch = id

	url := HttpUrl + "share/holder"
	var resData []byte
	headerData := make(map[string]interface{})
	headerData["key"] = Key
	headerData["time"] = strconv.Itoa(int(time.Now().Unix()))
	jsonData, _ := json.Marshal(&param)
	shaStr := string(jsonData) + Secret
	sign := Sha256(shaStr)
	headerData["sign"] = sign
	headerData["data"] = string(jsonData)
	reqData, _ := json.Marshal(headerData)
	log.Log().Info("ShareEnterpriseHolder 请求数据： ", zap.Any("", string(reqData)))
	resData = utils.HttpPostJson(reqData, url)
	log.Log().Info("ShareEnterpriseHolder 返回数据： ", zap.Any("", string(resData)))

	if err != nil {
		return
	}
	var publicResponseData model.PublicResponseData

	err = json.Unmarshal(resData, &publicResponseData)
	if publicResponseData.Code != 0 {
		err = errors.New(publicResponseData.Text)
		return err
	}

	var responseData model.EnterpriseHolder
	err = json.Unmarshal([]byte(publicResponseData.Data), &responseData)
	if err != nil {
		return
	}
	param.Code = responseData.Code

	err = source.DB().Save(&param).Error

	return err

}

//企业分账方修改
func UpdateShareEnterpriseHolder(param model.EnterpriseHolder) (err error) {

	url := HttpUrl + "share/holder/change"
	var resData []byte
	headerData := make(map[string]interface{})
	headerData["key"] = Key
	headerData["time"] = strconv.Itoa(int(time.Now().Unix()))
	jsonData, _ := json.Marshal(&param)
	shaStr := string(jsonData) + Secret
	sign := Sha256(shaStr)
	headerData["sign"] = sign
	headerData["data"] = string(jsonData)
	reqData, _ := json.Marshal(headerData)
	log.Log().Info("UpdateShareEnterpriseHolder 请求：", zap.Any("", string(reqData)))

	resData = utils.HttpPostJson(reqData, url)
	log.Log().Info("UpdateShareEnterpriseHolder 返回：", zap.Any("", string(resData)))

	if err != nil {
		return
	}
	var publicResponseData model.PublicResponseData

	//var responseHolderData model.ResponseHolderData
	err = json.Unmarshal(resData, &publicResponseData)
	if publicResponseData.Code != 0 {
		err = errors.New(publicResponseData.Text)
		return err
	}

	err = source.DB().Updates(&param).Error

	return err

}

func CreateAccount(accountData model.HbAccount) (err error) {

	err = source.DB().Create(&accountData).Error
	if err != nil {
		return err
	}

	return err
}

func ShareHolderChange() (err error) {

	url := HttpUrl + "share/holder/change"
	var resData []byte
	headerData := make(map[string]interface{})
	headerData["key"] = "aaa"
	headerData["time"] = strconv.Itoa(int(time.Now().Unix()))
	data := "{}"
	headerData["sign"] = HmacSha256(data, "aaaa")
	headerData["data"] = data
	reqData, _ := json.Marshal(headerData)
	resData = utils.HttpPostJson(reqData, url)
	if err != nil {
		return
	}
	fmt.Println(resData)
	return

}

//分账账户信息查询
func ShareHolderQuery(code string) (err error, responseData string) {

	url := HttpUrl + "share/holder/query"
	headerData := make(map[string]interface{})
	headerData["key"] = Key
	headerData["time"] = strconv.Itoa(int(time.Now().Unix()))
	data := make(map[string]string)
	data["code"] = code
	dataByte, err := json.Marshal(data)
	shaStr := string(dataByte) + Secret
	sign := Sha256(shaStr)
	headerData["sign"] = sign
	headerData["data"] = string(dataByte)
	reqData, _ := json.Marshal(headerData)
	resData := utils.HttpPostJson(reqData, url)

	var publicData model.PublicResponseData
	json.Unmarshal(resData, &publicData)
	if err != nil {
		return
	}
	if publicData.Code != 0 {
		err = errors.New(publicData.Text)
		return
	}
	responseData = publicData.Data
	//err = json.Unmarshal([]byte(publicData.Data), &responseData)
	//if err != nil {
	//	err = errors.New("解析data返回数据失败")
	//	return
	//}

	return

}

//分账账户余额查询
func ShareHolderBalance(code string) (err error) {

	url := HttpUrl + "share/holder/balance"
	var resData []byte
	headerData := make(map[string]interface{})
	headerData["key"] = "d47d39b267bab6a5e84"
	headerData["time"] = strconv.Itoa(int(time.Now().Unix()))
	data := make(map[string]string)
	data["code"] = code
	dataByte, err := json.Marshal(data)
	headerData["sign"] = HmacSha256(string(dataByte), "a0c5e25c330a65d535b32c38f247bb98")
	headerData["data"] = string(dataByte)
	reqData, _ := json.Marshal(headerData)

	log.Log().Info("req", zap.Any("info", headerData))
	resData = utils.HttpPostJson(reqData, url)
	log.Log().Info("res", zap.Any("info", string(resData)))

	if err != nil {
		return
	}
	return

}

//分账方银行卡变更
func ShareHolderCardChange() (err error) {

	url := HttpUrl + "share/holder/card/change"
	var resData []byte
	headerData := make(map[string]interface{})
	headerData["key"] = "aaa"
	headerData["time"] = strconv.Itoa(int(time.Now().Unix()))
	data := make(map[string]string)
	data["code"] = "111"
	dataByte, err := json.Marshal(data)
	headerData["sign"] = HmacSha256(string(dataByte), "aaaa")
	headerData["data"] = data
	reqData, _ := json.Marshal(headerData)
	resData = utils.HttpPostJson(reqData, url)
	if err != nil {
		return
	}
	fmt.Println(resData)
	return

}

//商户账户查询
func ShareMerchantBalance() (err error) {

	url := HttpUrl + "share/merchant/balance"
	var resData []byte
	headerData := make(map[string]interface{})
	headerData["key"] = Key
	headerData["time"] = strconv.Itoa(int(time.Now().Unix()))
	data := make(map[string]string)
	//data["code"] = code
	dataByte, err := json.Marshal(data)
	shaStr := string(dataByte) + Secret
	sign := Sha256(shaStr)
	headerData["sign"] = sign
	headerData["data"] = data
	reqData, _ := json.Marshal(headerData)
	resData = utils.HttpPostJson(reqData, url)
	if err != nil {
		return
	}
	var publicData model.PublicResponseData
	err = json.Unmarshal(resData, &publicData)
	if err != nil {
		return
	}
	var balance model.Balance
	err = json.Unmarshal([]byte(publicData.Data), &balance)
	if err != nil {
		return
	}

	return

}
