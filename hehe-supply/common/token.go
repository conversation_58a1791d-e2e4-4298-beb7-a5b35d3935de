package common

import (
	"hehe-supply/model"
	"sort"
	"strconv"
	"strings"
	"time"
	"yz-go/source"
	"yz-go/utils"
)

func GetRequestParams(params map[string]interface{}, data *model.HeheSupplySetting) (err error, result map[string]interface{}) {

	nonce := utils.GenerateSubId(32)
	appKey := data.BaseInfo.AppKey
	appSecret := data.BaseInfo.AppSecret
	params["appid"] = appKey
	params["timestamp"] = strconv.Itoa(int(time.Now().Unix()))
	params["nonce_str"] = nonce
	var signature string
	//按照字母顺序遍历
	traverseMapInStringOrder(params, func(key string, value interface{}) {
		signature += key + "=" + source.Strval(value) + "&"
	})
	signature += "key=" + appSecret
	signature = utils.MD5V([]byte(signature))
	signature = strings.ToUpper(signature)
	params["sign"] = signature

	return err, params
}

type MapEntryHandler func(string, interface{})

func traverseMapInStringOrder(params map[string]interface{}, handler MapEntryHandler) {
	keys := make([]string, 0)
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	for _, k := range keys {
		handler(k, params[k])
	}
}
