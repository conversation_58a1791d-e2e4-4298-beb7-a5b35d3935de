package response

type ConfirmOrderResponse struct {
	ErrorCode int    `json:"error_code"`
	ErrorInfo string `json:"error_info"`
}
type ConfirmOrderResponseWithData struct {
	ErrorCode int    `json:"error_code"`
	ErrorInfo string `json:"error_info"`
	Data      struct {
		OrderNo      string      `json:"order_no"`
		Price        string      `json:"price"`
		ExpressPrice interface{} `json:"express_price"`
		OrderSplit   interface{} `json:"order_split"`
	} `json:"data"`
}

type OrderBeforeCheckResponse struct {
	ErrorCode int         `json:"error_code"`
	ErrorInfo string      `json:"error_info"`
	Data      interface{} `json:"data"`
}

type OrderBeforeCheckData struct {
	GoodsId      string      `json:"goodsId"`
	ExpressPrice interface{} `json:"express_price"`
	GoodsNum     string      `json:"goodsNum"`
	TradeType    int         `json:"trade_type"`
}

type ExpressList struct {
	ErrorCode int    `json:"error_code"`
	ErrorInfo string `json:"error_info"`
	Data      []struct {
		Id                  int    `json:"id"`
		ShippingCompanyName string `json:"shipping_company_name"`
		Code                string `json:"code"`
	} `json:"data"`
}
type WareHouse struct {
	ErrorCode int    `json:"error_code"`
	ErrorInfo string `json:"error_info"`
	Data      struct {
		Name     string `json:"name"`
		Mobile   string `json:"mobile"`
		Address  string `json:"address"`
		Province int    `json:"province"`
		City     int    `json:"city"`
		Region   int    `json:"region"`
		Detail   string `json:"detail"`
	} `json:"data"`
}
