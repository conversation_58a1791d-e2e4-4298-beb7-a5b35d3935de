package model

import (
	"gorm.io/gorm"
	"yz-go/source"
)

// InstitutionMigration 代理迁移表
type InstitutionMigration struct {
	source.Model
	Uid uint `json:"uid" form:"uid" gorm:"column:uid;comment:用户ID;"`
	TSC int  `json:"team_small_shop_count" form:"team_small_shop_count" gorm:"column:team_small_shop_count;comment:团队小商店数量;default:0;"`
	TDC int  `json:"team_distributor_count" form:"team_distributor_count" gorm:"column:team_distributor_count;comment:团队分销商数量;default:0;"`
}

// TableName 表名
func (InstitutionMigration) TableName() string {
	return "institutions"
}

// Institution 代理表
type Institution struct {
	source.Model
	Uid      uint `json:"uid" form:"uid" gorm:"column:uid;comment:用户ID;"`
	TSC      int  `json:"team_small_shop_count" form:"team_small_shop_count" gorm:"column:team_small_shop_count;comment:团队小商店数量;default:0;"`
	TDC      int  `json:"team_distributor_count" form:"team_distributor_count" gorm:"column:team_distributor_count;comment:团队分销商数量;default:0;"`
	UserInfo User `json:"user_info" gorm:"foreignKey:Uid;references:ID;comment:用户信息"`
}

// InstitutionTeamShopMigration 代理团队小商店迁移表
type InstitutionTeamShopMigration struct {
	source.Model
	InstitutionID  uint `json:"institution_id" form:"institution_id" gorm:"column:institution_id;comment:代理ID;"`
	InstitutionUid uint `json:"institution_uid" form:"institution_uid" gorm:"column:institution_uid;comment:代理用户ID;"`
	SmallShopID    uint `json:"small_shop_id" form:"small_shop_id" gorm:"column:small_shop_id;comment:小商店ID;"`
	SmallShopUid   uint `json:"small_shop_uid" form:"small_shop_uid" gorm:"column:small_shop_uid;comment:小商店用户ID;"`
	Layers         int  `json:"layers" form:"layers" gorm:"column:layers;comment:层级,1直推2间推;"`
}

// TableName 表名
func (InstitutionTeamShopMigration) TableName() string {
	return "institution_team_shops"
}

// InstitutionTeamShop 代理团队小商店表
type InstitutionTeamShop struct {
	source.Model
	InstitutionID  uint       `json:"institution_id" form:"institution_id" gorm:"column:institution_id;comment:代理ID;"`
	InstitutionUid uint       `json:"institution_uid" form:"institution_uid" gorm:"column:institution_uid;comment:代理用户ID;"`
	SmallShopID    uint       `json:"small_shop_id" form:"small_shop_id" gorm:"column:small_shop_id;comment:小商店ID;"`
	SmallShopUid   uint       `json:"small_shop_uid" form:"small_shop_uid" gorm:"column:small_shop_uid;comment:小商店用户ID;"`
	Layers         int        `json:"layers" form:"layers" gorm:"column:layers;comment:层级,1直推2间推;"`
	LayersName     string     `json:"layers_name" gorm:"-"`
	SmallShop      SmallShop  `json:"small_shop" gorm:"foreignKey:SmallShopID;references:ID;comment:小商店信息"`
	UserInfo       User       `json:"user_info" gorm:"foreignKey:SmallShopUid;references:ID;comment:用户信息"`
	ParentUserInfo ParentUser `json:"parent_user_info" gorm:"foreignKey:InstitutionUid;references:ID;comment:推荐人信息"`
}

func (i *InstitutionTeamShop) AfterFind(tx *gorm.DB) (err error) {
	if i.Layers == 1 {
		i.LayersName = "直推"
	} else {
		i.LayersName = "间推"
	}
	return
}

// InstitutionTeamDistributorMigration 代理团队分销商迁移表
type InstitutionTeamDistributorMigration struct {
	source.Model
	InstitutionID      uint `json:"institution_id" form:"institution_id" gorm:"column:institution_id;comment:代理ID;"`
	InstitutionUid     uint `json:"institution_uid" form:"institution_uid" gorm:"column:institution_uid;comment:代理用户ID;"`
	DistributorID      uint `json:"distributor_id" form:"distributor_id" gorm:"column:distributor_id;comment:分销商ID;"`
	DistributorUid     uint `json:"distributor_uid" form:"distributor_uid" gorm:"column:distributor_uid;comment:分销商用户ID;"`
	DistributorLevelID uint `json:"distributor_level_id" form:"distributor_level_id" gorm:"column:distributor_level_id;comment:分销商等级ID;"`
	Layers             int  `json:"layers" form:"layers" gorm:"column:layers;comment:层级,1直推2间推;"`
}

// TableName 表名
func (InstitutionTeamDistributorMigration) TableName() string {
	return "institution_team_distributors"
}

// InstitutionTeamDistributor 代理团队分销商表
type InstitutionTeamDistributor struct {
	source.Model
	InstitutionID        uint             `json:"institution_id" form:"institution_id" gorm:"column:institution_id;comment:代理ID;"`
	InstitutionUid       uint             `json:"institution_uid" form:"institution_uid" gorm:"column:institution_uid;comment:代理用户ID;"`
	DistributorID        uint             `json:"distributor_id" form:"distributor_id" gorm:"column:distributor_id;comment:分销商ID;"`
	DistributorUid       uint             `json:"distributor_uid" form:"distributor_uid" gorm:"column:distributor_uid;comment:分销商用户ID;"`
	DistributorLevelID   uint             `json:"distributor_level_id" form:"distributor_level_id" gorm:"column:distributor_level_id;comment:分销商等级ID;"`
	Layers               int              `json:"layers" form:"layers" gorm:"column:layers;comment:层级,1直推2间推;"`
	LayersName           string           `json:"layers_name" gorm:"-"`
	UserInfo             User             `json:"user_info" gorm:"foreignKey:DistributorUid;references:ID;comment:用户信息"`
	ParentUserInfo       ParentUser       `json:"parent_user_info" gorm:"foreignKey:InstitutionUid;references:ID;comment:推荐人信息"`
	DistributorLevelInfo DistributorLevel `json:"distributor_level_info" gorm:"foreignKey:DistributorLevelID;references:ID;comment:分销商等级信息"`
}

func (i *InstitutionTeamDistributor) AfterFind(tx *gorm.DB) (err error) {
	if i.Layers == 1 {
		i.LayersName = "直推"
	} else {
		i.LayersName = "间推"
	}
	return
}
