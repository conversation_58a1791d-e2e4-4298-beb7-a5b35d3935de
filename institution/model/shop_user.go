package model

import (
	"yz-go/source"
)

type User struct {
	source.Model
	CreatedAt      *source.LocalTime `json:"created_at" gorm:"<-:create"`
	Mobile         string            `json:"mobile" form:"mobile" gorm:"column:mobile;comment:手机号;type:char(20);size:20;"`
	Avatar         string            `json:"avatar" form:"avatar" gorm:"column:avatar;comment:用户头像url;type:varchar(255);size:255;"`
	Username       string            `json:"username" form:"username" gorm:"comment:用户登录名"`
	NickName       string            `json:"nickname" form:"nickName" gorm:"column:nick_name;comment:用户昵称;type:varchar(50);size:50;"`
	LevelID        uint              `json:"level_id" form:"level_id"`
	ParentId       uint              `json:"parent_id" form:"parent_id" gorm:"comment:推荐会员id;"`
	ParentUserInfo ParentUser        `json:"parent_user_info" gorm:"foreignKey:ParentId;references:ID;comment:推荐人信息"`
}

type ParentUser struct {
	source.Model
	Mobile   string `json:"mobile" form:"mobile" gorm:"column:mobile;comment:手机号;type:char(20);size:20;"`
	Avatar   string `json:"avatar" form:"avatar" gorm:"column:avatar;comment:用户头像url;type:varchar(255);size:255;"`
	Username string `json:"username" form:"username" gorm:"comment:用户登录名"`
	NickName string `json:"nickname" form:"nickName" gorm:"column:nick_name;comment:用户昵称;type:varchar(50);size:50;"`
}

// TableName 表名
func (ParentUser) TableName() string {
	return "users"
}
