package pay_product

import (
	"database/sql/driver"
	"encoding/json"
	"gorm.io/gorm"
	"yz-go/source"
)

type ProductForUpdate struct {
	source.Model
	Title          string `json:"title"`
	OriginPrice    uint   `json:"origin_price"`
	GuidePrice     uint   `json:"guide_price"`
	Price          uint   `json:"price"`
	Stock          uint   `json:"stock"`
	CostPrice      uint   `json:"cost_price"`
	ActivityPrice  uint   `json:"activity_price"`
	Sn             string `json:"sn"`
	Skus           []Sku  `json:"skus" gorm:"foreignKey:ProductID"`
	Code           string `json:"code"`
	Weight         int    `json:"weight" form:"weight" gorm:"-"`
	SkuID          uint   `json:"sku_id" form:"sku_id" gorm:"-"`
	IsDisplay      int    `json:"is_display"`
	GatherSupplyID uint   `json:"gather_supply_id" form:"gather_supply_id" gorm:"column:gather_supply_id;comment:供应链id;index;"` // 供应链id
	IsPlugin       int    `json:"is_plugin" gorm:"column:is_plugin;default:0;"`
	SupplyLine     string `json:"supply_line"`
	IsSupplyLine   uint   `json:"is_supply_line"`
	NotDiscount    int    `json:"not_discount"`
	NotFee         int    `json:"not_fee"`
}

func (ProductForUpdate) TableName() string {
	return "products"
}

type Sku struct {
	source.Model
	Title         string  `json:"title"`
	Price         uint    `json:"price"`
	CostPrice     uint    `json:"cost_price"`
	OriginPrice   uint    `json:"origin_price"`
	GuidePrice    uint    `json:"guide_price"`
	ActivityPrice uint    `json:"activity_price"`
	Stock         int     `json:"stock"`
	LockStock     int     `json:"lock_stock" gorm:"-"`
	Weight        int     `json:"weight"`
	IsDisplay     int     `json:"is_display"`
	OriginalSkuID int     `json:"original_sku_id"`
	SpecId        string  `json:"specId"`
	Sn            string  `json:"sn"`
	Barcode       string  `json:"barcode"`
	ImageUrl      string  `json:"image_url"`
	SupplierID    uint    `json:"supplier_id"`
	ProductID     uint    `json:"product_id"`
	Options       Options `json:"options"`

	SpecItems []SpecItem `json:"spec_items,omitempty" gorm:"many2many:sku_spec_items;"`                        // 规格信息
	Describe  string     `json:"describe" form:"describe" gorm:"type:longtext;column:describe;comment:sku详情;"` //sku详情
	Code      string     `json:"code"`
}

func (Sku) TableName() string {
	return "skus"
}
func (s *Sku) AfterFind(tx *gorm.DB) (err error) {
	if s.Options == nil {
		s.Options = Options{{SpecName: "规格", SpecItemName: s.Title}}
	}
	return
}

func (s *Sku) BeforeSave(tx *gorm.DB) (err error) {
	if s.SpecItems != nil {
		// 保存SpecItems的冗余到Options中
		for _, v := range s.SpecItems {
			if v.Spec == nil {
				continue
			}
			s.Options = append(s.Options, Option{
				SpecName:     v.Spec.Title,
				SpecItemName: v.Value,
			})
		}
	}
	return
}

// 规格
type Options []Option
type Option struct {
	SpecName     string `json:"spec_name"`      // 规格名
	SpecItemName string `json:"spec_item_name"` // 规格值
}

func (value *Options) Scan(data interface{}) error {
	//if strings.Contains(source.Strval(data), "+") {
	//	data = strings.Replace(source.Strval(data), "+", "&", -1)
	//}
	return json.Unmarshal([]byte(source.Strval(data)), &value)
}
func (value Options) Value() (driver.Value, error) {
	return json.Marshal(value)
}

type SpecItem struct {
	source.Model
	SpecID uint   `json:"spec_id" form:"spec_id" gorm:"comment:规格ID;index"`
	Spec   *Spec  `json:"spec,omitempty"`
	Value  string `json:"value" form:"value" gorm:"column:value;comment:规格值;type:varchar(255);size:255;"`
}

type Spec struct {
	source.Model
	Title string `json:"title" form:"title" gorm:"column:title;comment:名称;type:varchar(255);size:255;"`
}
