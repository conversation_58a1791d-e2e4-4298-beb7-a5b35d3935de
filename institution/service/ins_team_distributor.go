package service

import (
	"errors"
	"github.com/360EntSecGroup-Skylar/excelize"
	"gorm.io/gorm"
	"institution/model"
	"institution/request"
	orderService "order/service"
	"os"
	"strconv"
	"time"
	"user/relation"
	"yz-go/config"
	"yz-go/source"
	"yz-go/utils"
)

// buildQueryTeamDistributor 构建查询条件
func buildQueryTeamDistributor(db *gorm.DB, req request.InstitutionTeamDistributorListRequest) *gorm.DB {
	var err error
	if req.InstitutionID > 0 {
		db = db.Where("institution_id = ?", req.InstitutionID)
	}
	if req.InstitutionUid > 0 {
		db = db.Where("institution_uid = ?", req.InstitutionUid)
	}
	if req.Uid > 0 {
		db = db.Where("distributor_uid = ?", req.Uid)
	}
	if req.Mobile != "" {
		// 通过手机号模糊查询用户ids
		var uids []uint
		err = source.DB().Model(&model.User{}).Where("username like ?", "%"+req.Mobile+"%").Pluck("id", &uids).Error
		if err != nil {
			return db
		}
		db = db.Where("distributor_uid in (?)", uids)
	}
	if req.Nickname != "" {
		// 通过昵称模糊查询用户ids
		var uids []uint
		err = source.DB().Model(&model.User{}).Where("nickname like ?", "%"+req.Nickname+"%").Pluck("id", &uids).Error
		if err != nil {
			return db
		}
		db = db.Where("distributor_uid in (?)", uids)
	}
	if req.ParentNickname != "" {
		// 通过推荐人昵称模糊查询用户ids
		var uids []uint
		err = source.DB().Model(&model.User{}).Where("nickname like ?", "%"+req.ParentNickname+"%").Or("username like ?", "%"+req.ParentNickname+"%").Pluck("id", &uids).Error
		if err != nil {
			return db
		}
		db = db.Where("institution_uid in (?)", uids)
	}
	if req.Layers > 0 {
		db = db.Where("layers = ?", req.Layers)
	}
	if req.DistributorLevelID > 0 {
		db = db.Where("distributor_level_id = ?", req.DistributorLevelID)
	}
	if req.StartTime != "" {
		db = db.Where("created_at >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		db = db.Where("created_at <= ?", req.EndTime)
	}
	return db
}

// buildQueryTeamDistributorByChildren 构建查询条件
func buildQueryTeamDistributorByChildren(db *gorm.DB, req request.InstitutionTeamDistributorListRequest, childrenIDs []uint, layersMap map[int][]uint) *gorm.DB {
	var err error
	if req.Mobile != "" || req.Nickname != "" || req.ParentNickname != "" || req.Layers > 0 {
		if req.InstitutionUid > 0 {
			db = db.Where("uid = ?", req.InstitutionUid).Where("uid in (?)", childrenIDs)
		}
		if req.Mobile != "" {
			// 通过手机号模糊查询用户ids
			var uids []uint
			err = source.DB().Model(&model.User{}).Where("username like ?", "%"+req.Mobile+"%").Where("id in (?)", childrenIDs).Pluck("id", &uids).Error
			if err != nil {
				db = db.Where("uid = ?", 0)
			}
			db = db.Where("uid in (?)", uids)
		}
		if req.Nickname != "" {
			// 通过昵称模糊查询用户ids
			var uids []uint
			err = source.DB().Model(&model.User{}).Where("nickname like ?", "%"+req.Nickname+"%").Where("id in (?)", childrenIDs).Pluck("id", &uids).Error
			if err != nil {
				db = db.Where("uid = ?", 0)
			}
			db = db.Where("uid in (?)", uids)
		}
		if req.ParentNickname != "" {
			// 通过推荐人昵称模糊查询用户ids
			var parentIDs []uint
			err = source.DB().Model(&model.User{}).Where("nickname like ?", "%"+req.ParentNickname+"%").Or("username like ?", "%"+req.ParentNickname+"%").Pluck("id", &parentIDs).Error
			if err != nil {
				db = db.Where("uid = ?", 0)
			}
			var uids []uint
			err = source.DB().Model(&model.User{}).Where("parent_id in (?)", parentIDs).Where("id in (?)", childrenIDs).Pluck("id", &uids).Error
			if err != nil {
				db = db.Where("uid = ?", 0)
			}
			db = db.Where("uid in (?)", uids)
		}
		if req.Layers > 0 {
			if req.Layers == 1 {
				if _, ok := layersMap[1]; !ok {
					db = db.Where("uid = ?", 0)
				} else {
					db = db.Where("uid in (?)", layersMap[1])
				}
			} else {
				if _, ok := layersMap[2]; !ok {
					db = db.Where("uid = ?", 0)
				} else {
					db = db.Where("uid in (?)", layersMap[2])
				}
			}
		}
	} else {
		if req.DistributorLevelID > 0 {
			db = db.Where("level_id = ?", req.DistributorLevelID)
		}
		if req.StartTime != "" {
			db = db.Where("created_at >= ?", req.StartTime)
		}
		if req.EndTime != "" {
			db = db.Where("created_at <= ?", req.EndTime)
		}
	}
	db = db.Where("uid in (?)", childrenIDs)
	return db
}

// GetInstitutionTeamDistributorList 获取institution团队分销商列表
func GetInstitutionTeamDistributorList(req request.InstitutionTeamDistributorListRequest) (err error, list []model.InstitutionTeamDistributor, total int64) {
	if req.InstitutionID == 0 {
		err = errors.New("机构ID不能为空")
		return
	}
	// 查询代理的会员id
	var ins model.Institution
	err = source.DB().Model(&model.Institution{}).Where("id = ?", req.InstitutionID).First(&ins).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("机构不存在")
		return
	}
	if err != nil {
		return
	}
	// 查询所有的机构
	var institutions []model.Institution
	err = source.DB().Model(&model.Institution{}).Find(&institutions).Error
	if err != nil {
		err = errors.New("查询所有机构失败")
		return
	}
	// institutions转成map, key是uid value是bool
	agentsMap := make(map[uint]bool)
	for _, in := range institutions {
		agentsMap[in.Uid] = true
	}
	// 获取机构uid的所有下级uid
	visited := make(map[uint]bool)
	childrenMap := relation.FindUserChildrenWithLevel(ins.Uid, 1, visited, agentsMap)
	if len(childrenMap) == 0 {
		return
	}
	// 从childrenMap中获取所有的uid
	var childrenIDs []uint
	// 通过childrenMap重构一份下级uid和层级的map
	var childrenLevelMap = make(map[uint]int)
	childrenLevelMap = make(map[uint]int)
	// 通过childrenMap重构一份直推和间推的map
	var layersMap = make(map[int][]uint)
	layersMap = make(map[int][]uint)
	for _, row := range childrenMap {
		childrenIDs = append(childrenIDs, row.Uid)
		childrenLevelMap[row.Uid] = row.Level
		if row.Level == 1 {
			layersMap[1] = append(layersMap[1], row.Uid)
		} else {
			layersMap[2] = append(layersMap[2], row.Uid)
		}
	}
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	var distributors []model.Distributor
	db := source.DB().Model(&model.Distributor{}).Preload("LevelInfo").Preload("UserInfo").Preload("UserInfo.ParentUserInfo")
	db = buildQueryTeamDistributorByChildren(db, req, childrenIDs, layersMap)
	// 查询总数
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	err = db.Limit(limit).Offset(offset).Order("id desc").Find(&distributors).Error
	if err != nil {
		return
	}
	// 构建返回数据
	for _, dis := range distributors {
		layersName := "直推"
		if _, ok := childrenLevelMap[dis.Uid]; ok && childrenLevelMap[dis.Uid] > 1 {
			layersName = "间推"
		}
		list = append(list, model.InstitutionTeamDistributor{
			Model: source.Model{
				ID:        dis.ID,
				CreatedAt: dis.CreatedAt,
			},
			InstitutionID:        ins.ID,
			InstitutionUid:       ins.Uid,
			DistributorID:        dis.ID,
			DistributorUid:       dis.Uid,
			DistributorLevelID:   dis.LevelID,
			Layers:               childrenLevelMap[dis.Uid],
			LayersName:           layersName,
			UserInfo:             dis.UserInfo,
			ParentUserInfo:       dis.UserInfo.ParentUserInfo,
			DistributorLevelInfo: dis.LevelInfo,
		})
	}
	return
}

// ExportInstitutionTeamDistributor 导出institution团队分销商
func ExportInstitutionTeamDistributor(req request.InstitutionTeamDistributorListRequest) (err error, link string) {
	if req.InstitutionID == 0 {
		err = errors.New("机构ID不能为空")
		return
	}
	// 查询代理的会员id
	var ins model.Institution
	err = source.DB().Model(&model.Institution{}).Where("id = ?", req.InstitutionID).First(&ins).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("机构不存在")
		return
	}
	if err != nil {
		return
	}
	// 查询所有的机构
	var institutions []model.Institution
	err = source.DB().Model(&model.Institution{}).Find(&institutions).Error
	if err != nil {
		err = errors.New("查询所有机构失败")
		return
	}
	// institutions转成map, key是uid value是bool
	agentsMap := make(map[uint]bool)
	for _, in := range institutions {
		agentsMap[in.Uid] = true
	}
	// 获取机构uid的所有下级uid
	visited := make(map[uint]bool)
	childrenMap := relation.FindUserChildrenWithLevel(ins.Uid, 1, visited, agentsMap)
	if len(childrenMap) == 0 {
		return
	}
	// 从childrenMap中获取所有的uid
	var childrenIDs []uint
	// 通过childrenMap重构一份下级uid和层级的map
	var childrenLevelMap = make(map[uint]int)
	childrenLevelMap = make(map[uint]int)
	// 通过childrenMap重构一份直推和间推的map
	var layersMap = make(map[int][]uint)
	layersMap = make(map[int][]uint)
	for _, row := range childrenMap {
		childrenIDs = append(childrenIDs, row.Uid)
		childrenLevelMap[row.Uid] = row.Level
		if row.Level == 1 {
			layersMap[1] = append(layersMap[1], row.Uid)
		} else {
			layersMap[2] = append(layersMap[2], row.Uid)
		}
	}
	db := source.DB().Model(&model.Distributor{}).Preload("LevelInfo").Preload("UserInfo").Preload("UserInfo.ParentUserInfo")
	db = buildQueryTeamDistributorByChildren(db, req, childrenIDs, layersMap)
	// 查询总数
	var total int64
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	if total == 0 {
		err = errors.New("没有数据")
		return
	}
	var distributors []model.Distributor
	excelPage := total/500 + 1
	var links []string
	timeStr := time.Now().Format("20060102150405")
	path := config.Config().Local.Path + "/export_institution_team_distributor"
	for ei := 1; ei <= int(excelPage); ei++ {
		err = db.Limit(500).Offset(500 * (ei - 1)).Order("id desc").Find(&distributors).Error
		if err != nil {
			return
		}
		f := excelize.NewFile()
		index := f.NewSheet("Sheet1")
		f.SetCellValue("Sheet1", "A1", "推荐人昵称")
		f.SetCellValue("Sheet1", "B1", "推荐人手机号")
		f.SetCellValue("Sheet1", "C1", "会员ID")
		f.SetCellValue("Sheet1", "D1", "会员昵称")
		f.SetCellValue("Sheet1", "E1", "会员手机号")
		f.SetCellValue("Sheet1", "F1", "层级")
		f.SetCellValue("Sheet1", "G1", "分销商等级")
		f.SetCellValue("Sheet1", "H1", "开通时间")
		i := 2
		for _, v := range distributors {
			var createAt string
			if v.CreatedAt != nil {
				createAt = v.CreatedAt.Format("2006-01-02 15:04:05")
			}
			layersName := "直推"
			if _, ok := childrenLevelMap[v.Uid]; ok && childrenLevelMap[v.Uid] > 1 {
				layersName = "间推"
			}
			f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), v.UserInfo.ParentUserInfo.NickName)
			f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), v.UserInfo.ParentUserInfo.Mobile)
			f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), v.UserInfo.ID)
			f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), v.UserInfo.NickName)
			f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), v.UserInfo.Mobile)
			f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), layersName)
			f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), v.LevelInfo.Name)
			f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), createAt)
			i++
		}
		f.SetActiveSheet(index)
		exist, _ := utils.PathExists(path)
		if !exist {
			err = os.MkdirAll(path, os.ModePerm)
			if err != nil {
				return
			}
		}
		link = path + "/" + timeStr + "-" + strconv.Itoa(ei) + "代理团队分销商导出.xlsx"
		if err = f.SaveAs(link); err != nil {
			return
		}
		links = append(links, link)
	}
	if excelPage > 1 {
		link = path + "/" + timeStr + "代理团队分销商导出.zip"
		err = orderService.Zip(link, links)
	}
	return
}

func checkInstitutionTeamDistributorExist(distributorID, uid uint) (err error, exist bool) {
	var count int64
	err = source.DB().Model(&model.InstitutionTeamDistributor{}).Where("distributor_id = ? and distributor_uid = ?", distributorID, uid).Count(&count).Error
	if err != nil {
		return
	}
	if count > 0 {
		exist = true
	}
	return
}

func TeamDistributorHandle(uid uint) (err error) {
	// 通过uid查询分销商信息
	/*var distributor model.Distributor
	err = source.DB().Where("uid = ?", uid).First(&distributor).Error
	if err != nil {
		return
	}
	// 通过uid和分销商id查询InstitutionTeamDistributor中是否存在
	var exist bool
	err, exist = checkInstitutionTeamDistributorExist(distributor.ID, distributor.Uid)
	if err != nil {
		return
	}
	if exist {
		return
	}
	// 通过uid查询上级中的机构代理
	var institution model.Institution
	var layers int
	visited := make(map[uint]bool)
	err, institution, layers = GetParentInstitution(distributor.Uid, visited)
	if err != nil {
		return
	}
	// 添加InstitutionTeamDistributor
	institutionTeamDistributor := model.InstitutionTeamDistributor{
		InstitutionID:      institution.ID,
		InstitutionUid:     institution.Uid,
		DistributorID:      distributor.ID,
		DistributorUid:     distributor.Uid,
		DistributorLevelID: distributor.LevelID,
		Layers:             layers,
	}
	err = source.DB().Create(&institutionTeamDistributor).Error
	if err != nil {
		return
	}
	// 修改上级机构代理的团队分销商数量
	err = source.DB().Model(&model.Institution{}).Where("id = ?", institution.ID).Update("team_distributor_count", gorm.Expr("team_distributor_count + ?", 1)).Error
	if err != nil {
		return
	}*/
	return
}
