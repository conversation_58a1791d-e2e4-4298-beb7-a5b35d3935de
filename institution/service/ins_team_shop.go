package service

import (
	"errors"
	"github.com/360EntSecGroup-Skylar/excelize"
	"gorm.io/gorm"
	"institution/model"
	"institution/request"
	orderService "order/service"
	"os"
	"strconv"
	"time"
	"user/relation"
	"yz-go/config"
	"yz-go/source"
	"yz-go/utils"
)

/*// buildQuery 构建查询条件
func buildQueryTeamShop(db *gorm.DB, req request.InstitutionTeamShopListRequest) *gorm.DB {
	var err error
	if req.InstitutionID > 0 {
		db = db.Where("institution_id = ?", req.InstitutionID)
	}
	if req.Uid > 0 {
		db = db.Where("small_shop_uid = ?", req.Uid)
	}
	if req.Mobile != "" {
		// 通过手机号模糊查询用户ids
		var uids []uint
		err = source.DB().Model(&model.User{}).Where("username like ?", "%"+req.Mobile+"%").Pluck("id", &uids).Error
		if err != nil {
			return db
		}
		db = db.Where("small_shop_uid in (?)", uids)
	}
	if req.Nickname != "" {
		// 通过昵称模糊查询用户ids
		var uids []uint
		err = source.DB().Model(&model.User{}).Where("nickname like ?", "%"+req.Nickname+"%").Pluck("id", &uids).Error
		if err != nil {
			return db
		}
		db = db.Where("small_shop_uid in (?)", uids)
	}
	if req.ParentNickname != "" {
		// 通过推荐人昵称模糊查询用户ids
		var uids []uint
		err = source.DB().Model(&model.User{}).Where("nickname like ?", "%"+req.ParentNickname+"%").Or("username like ?", "%"+req.ParentNickname+"%").Pluck("id", &uids).Error
		if err != nil {
			return db
		}
		db = db.Where("institution_uid in (?)", uids)
	}
	if req.Layers > 0 {
		db = db.Where("layers = ?", req.Layers)
	}
	if req.SmallShopName != "" {
		// 通过小商店名称模糊查询小商店ids
		var smallShopIds []uint
		err = source.DB().Model(&model.SmallShop{}).Where("title like ?", "%"+req.SmallShopName+"%").Pluck("id", &smallShopIds).Error
		if err != nil {
			return db
		}
		db = db.Where("small_shop_id in (?)", smallShopIds)
	}
	if req.StartTime != "" {
		db = db.Where("created_at >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		db = db.Where("created_at <= ?", req.EndTime)
	}
	return db
}

// GetInstitutionTeamShopList 获取institution团队小商店列表
func GetInstitutionTeamShopList(req request.InstitutionTeamShopListRequest) (err error, list []model.InstitutionTeamShop, total int64) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	db := source.DB().Model(&model.InstitutionTeamShop{})
	db = buildQueryTeamShop(db, req)
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	err = db.Preload("SmallShop").Preload("UserInfo").Preload("ParentUserInfo").Limit(limit).Offset(offset).Order("id desc").Find(&list).Error
	return
}

// ExportInstitutionTeamShop 导出institution团队小商店
func ExportInstitutionTeamShop(req request.InstitutionTeamShopListRequest) (err error, link string) {
	// 创建db
	db := source.DB().Model(&model.InstitutionTeamShop{}).Preload("SmallShop").Preload("UserInfo").Preload("ParentUserInfo")
	db = buildQueryTeamShop(db, req)
	var total int64
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	if total == 0 {
		err = errors.New("没有数据")
		return
	}
	var institutionTeamShops []model.InstitutionTeamShop
	excelPage := total/500 + 1
	var links []string
	timeStr := time.Now().Format("20060102150405")
	path := config.Config().Local.Path + "/export_institution_team_shop"
	for ei := 1; ei <= int(excelPage); ei++ {
		err = db.Limit(500).Offset(500 * (ei - 1)).Order("id desc").Find(&institutionTeamShops).Error
		if err != nil {
			return
		}
		f := excelize.NewFile()
		index := f.NewSheet("Sheet1")
		f.SetCellValue("Sheet1", "A1", "推荐人昵称")
		f.SetCellValue("Sheet1", "B1", "推荐人手机号")
		f.SetCellValue("Sheet1", "C1", "会员ID")
		f.SetCellValue("Sheet1", "D1", "会员昵称")
		f.SetCellValue("Sheet1", "E1", "会员手机号")
		f.SetCellValue("Sheet1", "F1", "层级")
		f.SetCellValue("Sheet1", "G1", "小商店名称")
		f.SetCellValue("Sheet1", "H1", "开通时间")
		i := 2
		for _, institutionTeamShop := range institutionTeamShops {
			var createAt string
			if institutionTeamShop.CreatedAt != nil {
				createAt = institutionTeamShop.CreatedAt.Format("2006-01-02 15:04:05")
			}
			f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), institutionTeamShop.ParentUserInfo.NickName)
			f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), institutionTeamShop.ParentUserInfo.Mobile)
			f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), institutionTeamShop.UserInfo.ID)
			f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), institutionTeamShop.UserInfo.NickName)
			f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), institutionTeamShop.LayersName)
			f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), institutionTeamShop.SmallShop.Title)
			f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), createAt)
			i++
		}
		f.SetActiveSheet(index)
		exist, _ := utils.PathExists(path)
		if !exist {
			err = os.MkdirAll(path, os.ModePerm)
			if err != nil {
				return
			}
		}
		link = path + "/" + timeStr + "-" + strconv.Itoa(ei) + "代理团队小商店导出.xlsx"
		if err = f.SaveAs(link); err != nil {
			return
		}
		links = append(links, link)
	}
	if excelPage > 1 {
		link = path + "/" + timeStr + "代理团队小商店导出.zip"
		err = orderService.Zip(link, links)
	}
	return
}*/

// buildQueryTeamShopByChildren 构建查询条件
func buildQueryTeamShopByChildren(db *gorm.DB, req request.InstitutionTeamShopListRequest, childrenIDs []uint, layersMap map[int][]uint) *gorm.DB {
	var err error
	if req.Mobile != "" || req.Nickname != "" || req.ParentNickname != "" || req.Layers > 0 {
		if req.Mobile != "" {
			// 通过手机号模糊查询用户ids
			var uids []uint
			err = source.DB().Model(&model.User{}).Where("username like ?", "%"+req.Mobile+"%").Where("id in (?)", childrenIDs).Pluck("id", &uids).Error
			if err != nil {
				db = db.Where("uid = ?", 0)
			}
			db = db.Where("uid in (?)", uids)
		}
		if req.Nickname != "" {
			// 通过昵称模糊查询用户ids
			var uids []uint
			err = source.DB().Model(&model.User{}).Where("nickname like ?", "%"+req.Nickname+"%").Where("id in (?)", childrenIDs).Pluck("id", &uids).Error
			if err != nil {
				db = db.Where("uid = ?", 0)
			}
			db = db.Where("uid in (?)", uids)
		}
		if req.ParentNickname != "" {
			// 通过推荐人昵称模糊查询用户ids
			var parentIDs []uint
			err = source.DB().Model(&model.User{}).Where("nickname like ?", "%"+req.ParentNickname+"%").Or("username like ?", "%"+req.ParentNickname+"%").Pluck("id", &parentIDs).Error
			if err != nil {
				db = db.Where("uid = ?", 0)
			}
			var uids []uint
			err = source.DB().Model(&model.User{}).Where("parent_id in (?)", parentIDs).Where("id in (?)", childrenIDs).Pluck("id", &uids).Error
			if err != nil {
				db = db.Where("uid = ?", 0)
			}
			db = db.Where("uid in (?)", uids)
		}
		if req.Layers > 0 {
			if req.Layers == 1 {
				if _, ok := layersMap[1]; !ok {
					db = db.Where("uid = ?", 0)
				} else {
					db = db.Where("uid in (?)", layersMap[1])
				}
			} else {
				if _, ok := layersMap[2]; !ok {
					db = db.Where("uid = ?", 0)
				} else {
					db = db.Where("uid in (?)", layersMap[2])
				}
			}
		}
	} else {
		db = db.Where("uid in (?)", childrenIDs)
		if req.SmallShopName != "" {
			db = db.Where("title like ?", "%"+req.SmallShopName+"%")
		}
		if req.StartTime != "" {
			db = db.Where("created_at >= ?", req.StartTime)
		}
		if req.EndTime != "" {
			db = db.Where("created_at <= ?", req.EndTime)
		}
	}
	return db
}

// GetInstitutionTeamShopList 获取institution团队小商店列表
func GetInstitutionTeamShopList(req request.InstitutionTeamShopListRequest) (err error, list []model.InstitutionTeamShop, total int64) {
	if req.InstitutionID == 0 {
		err = errors.New("机构ID不能为空")
		return
	}
	// 查询代理的会员id
	var ins model.Institution
	err = source.DB().Model(&model.Institution{}).Where("id = ?", req.InstitutionID).First(&ins).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("机构不存在")
		return
	}
	if err != nil {
		return
	}
	// 查询所有的机构
	var institutions []model.Institution
	err = source.DB().Model(&model.Institution{}).Find(&institutions).Error
	if err != nil {
		err = errors.New("查询所有机构失败")
		return
	}
	// institutions转成map, key是uid value是bool
	agentsMap := make(map[uint]bool)
	for _, in := range institutions {
		agentsMap[in.Uid] = true
	}
	// 获取机构uid的所有下级uid
	visited := make(map[uint]bool)
	childrenMap := relation.FindUserChildrenWithLevel(ins.Uid, 1, visited, agentsMap)
	if len(childrenMap) == 0 {
		return
	}
	// 从childrenMap中获取所有的uid
	var childrenIDs []uint
	// 通过childrenMap重构一份下级uid和层级的map
	var childrenLevelMap = make(map[uint]int)
	childrenLevelMap = make(map[uint]int)
	// 通过childrenMap重构一份直推和间推的map
	var layersMap = make(map[int][]uint)
	layersMap = make(map[int][]uint)
	for _, row := range childrenMap {
		childrenIDs = append(childrenIDs, row.Uid)
		childrenLevelMap[row.Uid] = row.Level
		if row.Level == 1 {
			layersMap[1] = append(layersMap[1], row.Uid)
		} else {
			layersMap[2] = append(layersMap[2], row.Uid)
		}
	}
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	var smallShops []model.SmallShop
	db := source.DB().Model(&model.SmallShop{}).Preload("UserInfo").Preload("UserInfo.ParentUserInfo")
	// 搜索条件
	db = buildQueryTeamShopByChildren(db, req, childrenIDs, layersMap)
	// 查询总数
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	err = db.Limit(limit).Offset(offset).Order("id desc").Find(&smallShops).Error
	if err != nil {
		return
	}
	// 构建返回数据
	for _, shop := range smallShops {
		layersName := "直推"
		if _, ok := childrenLevelMap[shop.Uid]; ok && childrenLevelMap[shop.Uid] > 1 {
			layersName = "间推"
		}
		list = append(list, model.InstitutionTeamShop{
			Model: source.Model{
				ID:        shop.ID,
				CreatedAt: shop.CreatedAt,
			},
			InstitutionID:  req.InstitutionID,
			InstitutionUid: ins.Uid,
			SmallShopID:    shop.ID,
			SmallShopUid:   shop.Uid,
			LayersName:     layersName,
			SmallShop:      shop,
			UserInfo:       shop.UserInfo,
			ParentUserInfo: shop.UserInfo.ParentUserInfo,
		})
	}
	return
}

// ExportInstitutionTeamShop 导出institution团队小商店
func ExportInstitutionTeamShop(req request.InstitutionTeamShopListRequest) (err error, link string) {
	if req.InstitutionID == 0 {
		err = errors.New("机构ID不能为空")
		return
	}
	// 查询代理的会员id
	var ins model.Institution
	err = source.DB().Model(&model.Institution{}).Where("id = ?", req.InstitutionID).First(&ins).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("机构不存在")
		return
	}
	if err != nil {
		return
	}
	// 查询所有的机构
	var institutions []model.Institution
	err = source.DB().Model(&model.Institution{}).Find(&institutions).Error
	if err != nil {
		err = errors.New("查询所有机构失败")
		return
	}
	// institutions转成map, key是uid value是bool
	agentsMap := make(map[uint]bool)
	for _, in := range institutions {
		agentsMap[in.Uid] = true
	}
	// 获取机构uid的所有下级uid
	visited := make(map[uint]bool)
	childrenMap := relation.FindUserChildrenWithLevel(ins.Uid, 1, visited, agentsMap)
	if len(childrenMap) == 0 {
		return
	}
	// 从childrenMap中获取所有的uid
	var childrenIDs []uint
	// 通过childrenMap重构一份下级uid和层级的map
	var childrenLevelMap = make(map[uint]int)
	childrenLevelMap = make(map[uint]int)
	// 通过childrenMap重构一份直推和间推的map
	var layersMap = make(map[int][]uint)
	layersMap = make(map[int][]uint)
	for _, row := range childrenMap {
		childrenIDs = append(childrenIDs, row.Uid)
		childrenLevelMap[row.Uid] = row.Level
		if row.Level == 1 {
			layersMap[1] = append(layersMap[1], row.Uid)
		} else {
			layersMap[2] = append(layersMap[2], row.Uid)
		}
	}
	db := source.DB().Model(&model.SmallShop{}).Preload("UserInfo").Preload("UserInfo.ParentUserInfo")
	// 搜索条件
	db = buildQueryTeamShopByChildren(db, req, childrenIDs, layersMap)
	// 查询总数
	var total int64
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	if total == 0 {
		err = errors.New("没有数据")
		return
	}
	var smallShops []model.SmallShop
	excelPage := total/500 + 1
	var links []string
	timeStr := time.Now().Format("20060102150405")
	path := config.Config().Local.Path + "/export_institution_team_shop"
	for ei := 1; ei <= int(excelPage); ei++ {
		err = db.Limit(500).Offset(500 * (ei - 1)).Order("id desc").Find(&smallShops).Error
		if err != nil {
			return
		}
		f := excelize.NewFile()
		index := f.NewSheet("Sheet1")
		f.SetCellValue("Sheet1", "A1", "推荐人昵称")
		f.SetCellValue("Sheet1", "B1", "推荐人手机号")
		f.SetCellValue("Sheet1", "C1", "会员ID")
		f.SetCellValue("Sheet1", "D1", "会员昵称")
		f.SetCellValue("Sheet1", "E1", "会员手机号")
		f.SetCellValue("Sheet1", "F1", "层级")
		f.SetCellValue("Sheet1", "G1", "小商店名称")
		f.SetCellValue("Sheet1", "H1", "开通时间")
		i := 2
		for _, shop := range smallShops {
			layersName := "直推"
			if _, ok := childrenLevelMap[shop.Uid]; ok && childrenLevelMap[shop.Uid] > 1 {
				layersName = "间推"
			}
			var createAt string
			if shop.CreatedAt != nil {
				createAt = shop.CreatedAt.Format("2006-01-02 15:04:05")
			}
			f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), shop.UserInfo.ParentUserInfo.NickName)
			f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), shop.UserInfo.ParentUserInfo.Mobile)
			f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), shop.UserInfo.ID)
			f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), shop.UserInfo.NickName)
			f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), shop.UserInfo.Mobile)
			f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), layersName)
			f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), shop.Title)
			f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), createAt)
			i++
		}
		f.SetActiveSheet(index)
		exist, _ := utils.PathExists(path)
		if !exist {
			err = os.MkdirAll(path, os.ModePerm)
			if err != nil {
				return
			}
		}
		link = path + "/" + timeStr + "-" + strconv.Itoa(ei) + "代理团队小商店导出.xlsx"
		if err = f.SaveAs(link); err != nil {
			return
		}
		links = append(links, link)
	}
	if excelPage > 1 {
		link = path + "/" + timeStr + "代理团队小商店导出.zip"
		err = orderService.Zip(link, links)
	}
	return
}

func TeamShopHandle(uid, sid uint) (err error) {
	/*// 增加institution的InstitutionTeamShop
	// 通过uid和sid查询InstitutionTeamShop是否存在,存在直接返回
	var institutionTeamShop model.InstitutionTeamShop
	err = source.DB().Model(&model.InstitutionTeamShop{}).Where("small_shop_uid = ? and small_shop_id = ?", uid, sid).First(&institutionTeamShop).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	if err != nil {
		return
	}
	if institutionTeamShop.ID > 0 {
		return
	}
	// 通过uid查询上级中是否有institution
	var ins model.Institution
	var layers int
	visited := make(map[uint]bool)
	err, ins, layers = GetParentInstitution(uid, visited)
	if err != nil {
		return
	}
	if ins.ID == 0 {
		return
	}
	// 创建InstitutionTeamShop
	institutionTeamShop = model.InstitutionTeamShop{
		InstitutionID:  ins.ID,
		InstitutionUid: ins.Uid,
		SmallShopID:    sid,
		SmallShopUid:   uid,
		Layers:         layers,
	}
	err = source.DB().Create(&institutionTeamShop).Error
	if err != nil {
		return
	}
	// 更新Institution的团队小商店数量
	err = source.DB().Model(&model.Institution{}).Where("id = ?", ins.ID).Update("team_small_shop_count", gorm.Expr("team_small_shop_count + ?", 1)).Error*/
	return
}
