package order

import (
	afterSalesModel "after-sales/model"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	model5 "jd-supply/model"
	url2 "net/url"
	model3 "order/model"
	model4 "product/model"
	callback2 "public-supply/callback"
	common2 "public-supply/common"
	"public-supply/model"
	"public-supply/request"
	"public-supply/response"
	gsetting "public-supply/setting"
	"strconv"
	"strings"
	"time"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

type JD struct {
	dat      *model.SupplySetting
	Setting  *model.SupplySetting
	SupplyID uint
	Http     string
}

func (y *JD) UploadGatherSupplySN(request request.UpdateData) (err error, data interface{}) {
	//TODO implement me
	panic("implement me")
}

func (y *JD) GetRefundTypes(orderId uint, orderItemID uint) (err error, data []afterSalesModel.AfterSalesType) {
	return
}

var JDTokenKey = "accessToken"

func (y *JD) GetToken() (err error) {
	var ctx = context.Background()
	token, redisTokenErr := source.Redis().Get(ctx, "JDTokenKey").Result()
	if redisTokenErr != nil {
		log.Log().Error("JD get redis token err", zap.Any("err", redisTokenErr))

	}
	if token == "" {
		if y.dat.BaseInfo.Token != "" {
			return
		}
		url := y.Http + "/accessToken/"
		//password := "Wo123456"
		password := y.dat.BaseInfo.PassWord
		passwordMd5 := utils.MD5V([]byte(password))
		reqData := url2.Values{}
		reqData.Add("grantType", JDTokenKey)
		reqData.Add("appKey", y.dat.BaseInfo.AppKey)
		//reqData.Add("username", "yzkjSoft")
		reqData.Add("username", y.dat.BaseInfo.UserName)
		reqData.Add("password", passwordMd5)
		var tokenData model5.ResTokenData
		var header = make(map[string]string)
		err, resData := utils.PostForm(url, reqData, header)
		json.Unmarshal(resData, &tokenData)
		y.dat.BaseInfo.Token = tokenData.Result.AccessToken

		err = source.Redis().SetEX(ctx, "JDTokenKey", y.dat.BaseInfo.Token, time.Hour*20).Err()
		if err != nil {
			log.Log().Error("JD redis set  token err", zap.Any("err", err))
		}

	} else {
		y.dat.BaseInfo.Token = token
	}

	return
}

func (y *JD) AfterSalesBeforeCheck(request request.RequestAfterSale) (err error, info interface{}) {
	return
}

func (y *JD) AfterSalesPicture(request request.RequestAfterSalePicture) (err error, info interface{}) {
	return
}

func (y *JD) AfterSale(request request.AfterSale) (err error, info interface{}) {
	return
}

func (y *JD) OrderDelivery(OrderData callback2.OrderCallBack) (err error) {
	return
}

func (y *JD) SyncOrderExpNo(unionIdList []string) (err error, data []response.SyncOrderExpNoResponse) {
	return
}

func (y *JD) InitSetting(gatherSupplyID uint) (err error) {
	y.SupplyID = gatherSupplyID
	var setting model2.SysSetting
	err, setting = gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}
	err = json.Unmarshal([]byte(setting.Value), &y.dat)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	y.Setting = y.dat
	if y.dat.BaseInfo.AppKey == "" && y.dat.BaseInfo.AppSecret == "" {
		err = errors.New("请先配置供应链key")
		return
	}
	y.Http = y.dat.BaseInfo.ApiUrl
	y.GetToken()

	return
}

func (y *JD) CancelOrder(orderID uint) {

	var order model3.Order
	err := source.DB().Where("id=?", orderID).First(&order).Error
	if err != nil {
		log.Log().Info("yzh供应链订单取消错误", zap.Any("info", err))
		return
	}

	if order.GatherSupplyType == common2.SUPPLY_YZHNEW && order.GatherSupplySN != "" {
		y.InitSetting(order.GatherSupplyID)
		if y.dat == nil {
			return
		}
		log.Log().Info("yzhnew供应链订单取消order开始", zap.Any("info", order))

		url := y.Http + "open/api/order/cancelOrder"
		var resData []byte
		headerData := make(map[string]interface{})
		headerData["accessToken"] = y.dat.BaseInfo.Token
		headerData["parentOrderCode"] = order.GatherSupplySN
		reqData, _ := json.Marshal(headerData)

		log.Log().Info("yzhnew供应链订单取消order开始 请求数据", zap.Any("info", string(reqData)))

		resData = utils.HttpPostJson(reqData, url)
		var CancelOrder model5.CancelOrder
		err = json.Unmarshal(resData, &CancelOrder)
		if CancelOrder.Success == true {
			source.DB().Table("orders").Where("id=?", orderID).UpdateColumn("gather_supply_sn", gorm.Expr("CONCAT_WS('-',gather_supply_sn,?)", "取消成功"))
			return
		}
		return

	}

}

func (y *JD) OrderFreight(request request.RequestSaleBeforeCheck, regionCode int) (err error, res model5.OrderFreight) {

	var contentMap []interface{}
	for _, item := range request.Skus {
		var contentProduct = make(map[string]interface{})
		contentProduct["prodId"] = item.Sku.Sku
		contentProduct["prodNum"] = item.Number
		contentMap = append(contentMap, contentProduct)
	}

	url := y.Http + "/gateway"
	var resData []byte
	var content = make(map[string]interface{})
	content["prodGroup"] = contentMap
	content["regionCode"] = regionCode
	jsonData, err := json.Marshal(&content)
	headerData := url2.Values{}
	headerData.Add("appToken", y.dat.BaseInfo.Token)
	headerData.Add("method", "open.api.shop.order.getFreight")
	headerData.Add("version", "1.0")
	headerData.Add("seqNo", "123456789012345")
	headerData.Add("content", string(jsonData))

	var header = make(map[string]string)
	err, resData = utils.PostForm(url, headerData, header)
	if err != nil {
		return
	}
	err = json.Unmarshal(resData, &res)
	//if err != nil {
	//	return
	//}

	if res.Success != true {
		err = errors.New(res.ResultMessage)
		return
	}

	fmt.Println(resData)

	return

}

// 订单前置校验  返回运费   //立即购买校验
func (y *JD) OrderBeforeCheck(request request.RequestSaleBeforeCheck) (err error, res response.BeforeCheck) {
	res.Code = 1
	var skuStringArr []string
	var contentMap []interface{}
	for _, item := range request.LocalSkus {
		var id string
		var skus model4.Sku
		source.DB().Preload(clause.Associations).Where("id=?", item.Sku.Sku).First(&skus)
		id = strconv.Itoa(int(skus.OriginalSkuID))
		skuStringArr = append(skuStringArr, id)
		var contentProduct = make(map[string]interface{})

		contentProduct["prodId"] = skus.OriginalSkuID
		contentProduct["prodNum"] = item.Number
		contentMap = append(contentMap, contentProduct)
	}

	if len(skuStringArr) == 0 {
		res.Code = 0
		res.Msg = "请求sku异常"
		return
	}

	addressDetail := request.Address.Province + request.Address.City + request.Address.Area + request.Address.Description
	addErr, address := y.JDAddressFromAddress(addressDetail)
	if addErr != nil {
		return
	}

	if address.Result == 0 {
		err = errors.New("地址解析失败")
		return
	}

	var stockStatusList model5.JdGoodsStock
	err, stockStatusList = y.GetYzhGoodsStock(contentMap, address.Result)

	if err != nil {
		log.Log().Error("jd  SaleBeforeCheck 解析失败", zap.Any("info", err))
		return
	}

	if stockStatusList.Success == false {
		res.Code = 0
		res.Msg = "请求检测库存错误"
		log.Log().Error("jd  OrderBeforeCheck RESPONSESTATUS 检测库存错误", zap.Any("info", stockStatusList))
		return
	}
	//errSale := y.SaleCheck(skuStringArr)
	var noStockSkuCodes []string
	for _, item := range stockStatusList.Result {

		if item.ProdNum != 200 {
			res.Code = 0
			skuPid := strconv.Itoa(item.ProdId)
			noStockSkuCodes = append(noStockSkuCodes, skuPid)

		} else {
			skuCode := item.ProdId
			res.Skus = append(res.Skus, uint(skuCode))
		}
	}

	freightErr, freight := y.OrderFreight(request, address.Result)
	if freightErr != nil {
		return
	}
	//fmt.Println(freight)

	res.Freight = uint(freight.Result.Freight)

	if res.Code == 0 {
		var SkuCodes string
		for _, code := range noStockSkuCodes {
			SkuCodes += code
		}
		res.Msg = fmt.Sprintf("以下商品的sku库存不足%s、", SkuCodes)
	}
	return
}

func (y *JD) SaleCheck(id []string) (err error) {

	url := y.Http + "/open/api/goods/queryGoodsShelvesList"
	var resData []byte
	headerData := make(map[string]interface{})
	headerData["accessToken"] = y.dat.BaseInfo.Token
	headerData["goodsSkuCode"] = id
	reqData, _ := json.Marshal(headerData)
	resData = utils.HttpPostJson(reqData, url)
	var saleStatus model5.YzhSaleStatus
	err = json.Unmarshal(resData, &saleStatus)
	if err != nil {
		return
	}
	if saleStatus.Success != true {
		err = errors.New(saleStatus.Desc)
		return
	}
	for _, item := range saleStatus.Result {
		if item.ShelvesStatus != 1001 {
			err = errors.New("商品已经下架")
			return
		}
	}

	return
}

// 商品是否可售前置校验   下单支付校验
func (y *JD) SaleBeforeCheck(request request.RequestSaleBeforeCheck) (err error, res response.ResSaleBeforeCheck) {
	res.Code = 0
	var skuMap = make(map[uint]uint)
	var contentMap []interface{}
	log.Log().Info("jd SaleBeforeCheck 请求数据：", zap.Any("info", request))

	for _, item := range request.Skus {
		var skus model4.Sku
		source.DB().Preload(clause.Associations).Where("original_sku_id=?", item.Sku.Sku).First(&skus)
		skuMap[uint(skus.OriginalSkuID)] = skus.ID
		var contentProduct = make(map[string]interface{})

		contentProduct["prodId"] = item.Sku.Sku
		contentProduct["prodNum"] = item.Number
		contentMap = append(contentMap, contentProduct)

	}

	addressDetail := request.Address.Province + request.Address.City + request.Address.Area + request.Address.Description
	addErr, address := y.JDAddressFromAddress(addressDetail)
	if addErr != nil {
		return
	}

	if address.Result == 0 {
		err = errors.New("地址解析失败")
		return
	}
	log.Log().Info("jd GetYzhGoodsStock 请求数据：", zap.Any("info", contentMap), zap.Any("地址id:", address.Result))

	stockErr, stockStatusList := y.GetYzhGoodsStock(contentMap, address.Result)
	if stockErr != nil {
		return
	}

	//errSale := y.SaleCheck(skuStringArr)

	if stockStatusList.Success == false {
		res.Code = 1
		res.Msg = "请求检测库存错误"
		log.Log().Error("OrderBeforeCheck RESPONSESTATUS 检测库存错误", zap.Any("info", stockStatusList))
		return
	}

	for _, item := range stockStatusList.Result {
		//goodsCode, _ := sitem.GoodsSkuCode)
		goodsSkuCode := uint(item.ProdId)
		if item.ProdNum != 200 {
			res.Code = 1
			res.Msg = "存在不可售商品"

			if request.GatherSupplyID == 0 {
				res.Data.Ban = append(res.Data.Ban, goodsSkuCode)
			} else {
				res.Data.Ban = append(res.Data.Ban, skuMap[goodsSkuCode])
			}

		} else {
			log.Log().Info("jd res.Data  GatherSupplyID返回数据：", zap.Any("info", request.GatherSupplyID))

			if request.GatherSupplyID == 0 {
				res.Data.Available = append(res.Data.Available, goodsSkuCode)
			} else {
				res.Data.Available = append(res.Data.Available, skuMap[goodsSkuCode])
			}

		}
	}

	log.Log().Info("jd res.Data 返回数据：", zap.Any("info", res))

	return

}

func (y *JD) JDAddressFromAddress(address string) (err error, FromAddress model5.JDAddressFromAddress) {

	url := y.Http + "/gateway"
	var resData []byte
	var content = make(map[string]interface{})
	content["address"] = address
	jsonData, err := json.Marshal(&content)
	headerData := url2.Values{}
	headerData.Add("appToken", y.dat.BaseInfo.Token)
	headerData.Add("method", "open.api.shop.area.getJDAddressFromAddress")
	headerData.Add("version", "1.0")
	headerData.Add("seqNo", "123456789012345")
	headerData.Add("content", string(jsonData))

	var header = make(map[string]string)
	err, resData = utils.PostForm(url, headerData, header)
	if err != nil {
		return
	}

	err = json.Unmarshal(resData, &FromAddress)
	if err != nil {
		return
	}
	if FromAddress.Success != true {
		err = errors.New(FromAddress.ResultMessage)
		return
	}

	return

}

func (y *JD) GetYzhGoodsStock(contentMap []interface{}, regionCode int) (err error, ResYzhStock model5.JdGoodsStock) {
	url := y.Http + "/gateway"
	var resData []byte
	//headerData := make(map[string]interface{})

	var content = make(map[string]interface{})
	content["prodGroup"] = contentMap
	content["regionCode"] = regionCode

	jsonData, err := json.Marshal(&content)

	headerData := url2.Values{}
	headerData.Add("appToken", y.dat.BaseInfo.Token)
	headerData.Add("method", "open.api.shop.product.huiGetSellPrice")
	headerData.Add("version", "1.0")
	headerData.Add("seqNo", "123456789012345")
	headerData.Add("content", string(jsonData))

	var header = make(map[string]string)
	err, resData = utils.PostForm(url, headerData, header)
	if err != nil {
		return
	}
	err = json.Unmarshal(resData, &ResYzhStock)
	if err != nil {
		return
	}

	return
}

var ProvinceAddress, CityAddress, AreaAddress []model5.YzhAddress

func (y *JD) GetAreaAddress(Area, pid string) (AreaID string, err error) {

	queryAddressAreaUrl := y.Http + "open/api/address/queryAddressArea"
	var resDataArea []byte
	headerDataArea := make(map[string]interface{})
	headerDataArea["accessToken"] = y.dat.BaseInfo.Token
	headerDataArea["parentAddressCode"] = pid
	reqDataArea, err := json.Marshal(headerDataArea)
	var ResYzhAddress model5.ResYzhAddress
	resDataArea = utils.HttpPostJson(reqDataArea, queryAddressAreaUrl)
	err = json.Unmarshal(resDataArea, &ResYzhAddress)
	if ResYzhAddress.Success != true {
		log.Log().Info("yzhnew queryAddressArea供应链解析地址错误", zap.Any("info", string(resDataArea)))
		return
	}

	AreaAddress = ResYzhAddress.Result

	for _, item := range AreaAddress {
		if strings.Contains(item.AddressName, Area) || strings.Contains(Area, item.AddressName) {
			AreaID = item.AddressCode
			break
		}
	}

	return

}

func (y *JD) GetStreetAddress(Street, pid string) (StreetID string, err error) {

	queryAddressAreaUrl := y.Http + "open/api/address/queryAddressStreet"
	var resDataArea []byte
	headerDataArea := make(map[string]interface{})
	headerDataArea["accessToken"] = y.dat.BaseInfo.Token
	headerDataArea["parentAddressCode"] = pid
	reqDataArea, err := json.Marshal(headerDataArea)
	var ResYzhAddress model5.ResYzhAddress
	resDataArea = utils.HttpPostJson(reqDataArea, queryAddressAreaUrl)
	err = json.Unmarshal(resDataArea, &ResYzhAddress)
	if ResYzhAddress.Success != true {
		log.Log().Info("yzhnew queryAddressArea供应链解析地址错误", zap.Any("info", string(resDataArea)))
		return
	}

	AreaAddress = ResYzhAddress.Result

	for _, item := range AreaAddress {
		if strings.Contains(item.AddressName, Street) || strings.Contains(Street, item.AddressName) {
			StreetID = item.AddressCode
			break
		}
	}

	return

}

func (y *JD) GetGoodsPriceList(arr []string) int64 {

	queryUrl := y.Http + "open/api/goods/queryGoodsPriceList"
	var headerResData []byte
	headerData := make(map[string]interface{})
	headerData["accessToken"] = y.dat.BaseInfo.Token
	headerData["goodsSkuCode"] = arr
	var goodsPrice model5.GoodsPrice
	reqData, _ := json.Marshal(headerData)
	//	var goodsPrice model5.GoodsPrice
	headerResData = utils.HttpPostJson(reqData, queryUrl)
	json.Unmarshal(headerResData, &goodsPrice)
	log.Log().Info("yzhnew sku 获取实时价格", zap.Any("info", goodsPrice))

	if goodsPrice.Success == false {
		log.Log().Info("yzhnew sku 获取实时价格错误", zap.Any("info", goodsPrice))
		return 0

	}
	if len(goodsPrice.Result) > 0 {
		price, _ := strconv.ParseFloat(goodsPrice.Result[0].SellPrice, 64)

		SellPrice := utils.Yuan2Fen(price)
		return SellPrice
	}

	return 0

}

func (y *JD) GetCityAddress(City, pid string) (CityID string, err error) {
	var ResYzhAddress model5.ResYzhAddress
	//--------
	queryAddressCityUrl := y.Http + "open/api/address/queryAddressCity"
	var resDataCity []byte
	headerDataCity := make(map[string]interface{})
	headerDataCity["accessToken"] = y.dat.BaseInfo.Token
	headerDataCity["parentAddressCode"] = pid
	reqDataCity, _ := json.Marshal(headerDataCity)

	resDataCity = utils.HttpPostJson(reqDataCity, queryAddressCityUrl)
	err = json.Unmarshal(resDataCity, &ResYzhAddress)
	if ResYzhAddress.Success != true {
		log.Log().Info("yzhnew供应链解析地址错误", zap.Any("info", string(resDataCity)))
		return
	}

	CityAddress = ResYzhAddress.Result

	for _, item := range CityAddress {
		if strings.Contains(item.AddressName, City) || strings.Contains(City, item.AddressName) {
			CityID = item.AddressCode
			break
		}
	}

	return

}

func (y *JD) QueryCategoryList(level, pid uint) (data interface{}) {
	//url:="open/api/category/queryOneCategoryList"

	var ResYzhAddress model5.ResYzhAddress
	url := y.Http + "/gateway"
	var resData []byte
	//headerData := make(map[string]interface{})

	var content = make(map[string]interface{})
	//content["prodGroup"] = contentMap
	if pid == 0 {
		content["id"] = 100000000
	} else {
		content["id"] = pid
	}

	jsonData, err := json.Marshal(&content)

	headerData := url2.Values{}
	headerData.Add("appToken", y.dat.BaseInfo.Token)
	headerData.Add("method", "open.api.shop.area.huiGetArea")
	headerData.Add("version", "1.0")
	headerData.Add("seqNo", "123456789012345")
	headerData.Add("content", string(jsonData))

	var header = make(map[string]string)
	err, resData = utils.PostForm(url, headerData, header)
	if err != nil {
		return
	}
	var jdAddress JDAddress
	err = json.Unmarshal(resData, &jdAddress)
	if err != nil {
		return
	}

	if jdAddress.Success == true {

		ResYzhAddress.Success = jdAddress.Success

		for _, item := range jdAddress.Result {
			var YzhAddress model5.YzhAddress

			ID := strconv.Itoa(item.Id)
			ParentId := strconv.Itoa(item.ParentId)
			YzhAddress.AddressCode = ID
			YzhAddress.AddressName = item.Name
			YzhAddress.ParentAddressCode = ParentId
			ResYzhAddress.Result = append(ResYzhAddress.Result, YzhAddress)
		}

	}

	data = ResYzhAddress
	fmt.Println(resData)

	return

}

type JDAddress struct {
	Success    bool   `json:"success"`
	ResultCode string `json:"result_code"`
	Result     []struct {
		Id       int    `json:"id"`
		Name     string `json:"name"`
		ParentId int    `json:"parentId"`
		Level    int    `json:"level"`
	} `json:"result"`
}

func (y *JD) GetAllAddress() (err error, data interface{}) {
	var ResYzhAddress model5.ResYzhAddress
	//if len(ProvinceAddress) == 0 {
	url := y.Http + "open/api/address/queryAddressList"
	var resData []byte
	headerData := make(map[string]interface{})
	headerData["accessToken"] = y.dat.BaseInfo.Token
	reqData, err := json.Marshal(headerData)

	resData = utils.HttpPostJson(reqData, url)
	err = json.Unmarshal(resData, &ResYzhAddress)
	if ResYzhAddress.Success != true {
		log.Log().Info("yzhnew供应链解析地址错误", zap.Any("info", string(resData)))
		return
	}
	if err != nil {
		return
	}
	data = ResYzhAddress.Result

	return

}
func (y *JD) GetProvinceAddress(Province, City, Area string) (ProvinceID, CityID, AreaID string) {
	var ResYzhAddress model5.ResYzhAddress
	//if len(ProvinceAddress) == 0 {
	url := y.Http + "open/api/address/queryAddressProvince"
	var resData []byte
	headerData := make(map[string]interface{})
	headerData["accessToken"] = y.dat.BaseInfo.Token
	reqData, err := json.Marshal(headerData)

	resData = utils.HttpPostJson(reqData, url)
	err = json.Unmarshal(resData, &ResYzhAddress)
	if ResYzhAddress.Success != true {
		log.Log().Info("yzhnew供应链解析地址错误", zap.Any("info", string(resData)))
		return
	}
	if err != nil {
		return
	}
	ProvinceAddress = ResYzhAddress.Result

	for _, item := range ProvinceAddress {
		if strings.Contains(item.AddressName, Province) || strings.Contains(Province, item.AddressName) {
			ProvinceID = item.AddressCode
			break
		}
	}

	CityID, err = y.GetCityAddress(City, ProvinceID)

	//--------
	AreaID, err = y.GetAreaAddress(Area, CityID)

	return

}

// 地址映射
func (y *JD) CreateAddressMapping(address model.AddressMapping) (err error) {

	var addressMapping model.AddressMapping
	addressMapping.MyAddress = address.MyAddress
	addressMapping.OtherAddress = address.OtherAddress
	addressMapping.GatherSupply = address.GatherSupply

	err = source.DB().Where("my_address=? and other_address=? and gather_supply=?", address.MyAddress, address.OtherAddress, address.GatherSupply).FirstOrCreate(&addressMapping).Error

	return
}

// 地址映射
func (y *JD) ListAddressMapping() (addressMapping []model.AddressMapping) {

	source.DB().Find(&addressMapping)
	return
}

// 地址映射
func (y *JD) findAddressMapping(myAddress string) (err error, address string) {

	var addressMapping model.AddressMapping
	err = source.DB().Where("my_address=? and  gather_supply=?", myAddress, y.SupplyID).First(&addressMapping).Error
	address = addressMapping.OtherAddress
	return
}

// 确认下单
func (y *JD) ConfirmOrder(request request.RequestConfirmOrder) (err error, info *stbz.APIResult) {

	url := y.Http + "/gateway"
	var resData []byte
	var contentMap []interface{}

	for _, skuItem := range request.Skus {
		var contentProduct = make(map[string]interface{})

		var jdGoods model5.JdGoods
		err = source.DB().Where("prod_id=?", skuItem.Sku.Sku).First(&jdGoods).Error
		if err != nil {
			log.Log().Error("未查询到jd下单商品", zap.Any("info", skuItem))
			return
		}
		contentProduct["prodId"] = jdGoods.ProdId
		contentProduct["prodNum"] = skuItem.Number
		contentProduct["price"] = jdGoods.SalePrice
		contentMap = append(contentMap, contentProduct)

	}

	addressDetail := request.Address.Province + request.Address.City + request.Address.Area + request.Address.Description
	addErr, address := y.JDAddressFromAddress(addressDetail)
	if addErr != nil || address.Result == 0 {
		log.Log().Error("zyhx jd 下单错误地址解析失败", zap.Any("info", string(addressDetail)), zap.Any("err", addErr))
		return
	}

	var content = make(map[string]interface{})
	//prodGroup, _ := json.Marshal(&contentMap)
	content["thirdOrder"] = request.OrderSn.OrderSn
	content["prodGroup"] = contentMap
	content["name"] = request.Address.Consignee
	content["regionCode"] = address.Result
	content["address"] = request.Address.Description
	content["mobile"] = request.Address.Phone
	content["email"] = "<EMAIL>"
	content["submitState"] = "1"

	jsonData, err := json.Marshal(&content)

	headerData := url2.Values{}
	headerData.Add("appToken", y.dat.BaseInfo.Token)
	headerData.Add("method", "open.api.shop.order.huiSubmitOrder")
	headerData.Add("version", "1.0")
	headerData.Add("seqNo", "123456789012345")
	headerData.Add("content", string(jsonData))

	var header = make(map[string]string)
	err, resData = utils.PostForm(url, headerData, header)
	if err != nil {
		return
	}

	var orderRes OrderRes
	err = json.Unmarshal(resData, &orderRes)
	if err != nil {
		log.Log().Error("zyhx jd 下单解析返回订单信息错误", zap.Any("info", string(resData)))
		return
	}

	if orderRes.Success != true {
		log.Log().Error("zyhx jd 下单错误 ", zap.Any("info", orderRes))
		return
	}

	//fmt.Println(resData)

	var order model3.Order
	order.GatherSupplyType = common2.SUPPLY_ZYHX
	if orderRes.Success == true {
		if len(orderRes.Result.SuccessOrders) == 1 {
			order.GatherSupplySN = orderRes.Result.SuccessOrders[0].OrderNo
		} else {
			order.GatherSupplySN = "下单成功"
		}

	} else {
		order.GatherSupplyMsg = orderRes.ResultMessage
	}

	err = source.DB().Where("order_sn=?", request.OrderSn.OrderSn).Updates(&order).Error
	if err != nil {
		log.Log().Info("zyhx 保存三方单号失败", zap.Any("info", err))
	}

	for _, item := range orderRes.Result.SuccessOrders {
		var subOrder model3.Order
		err = source.DB().Where("order_sn=?", item.OutOrderNo).First(&subOrder).Error
		if err != nil {
			log.Log().Error("zyhx 子订单查询失败", zap.Any("info", err))
		}

		for _, pitem := range item.Products {
			var orderItem model3.OrderItem
			err = source.DB().Where("order_id=? and original_sku_id=?", subOrder.ID, pitem.ProdId).First(&orderItem).Error
			if err != nil {
				log.Log().Error("zyhx 子订单orderitem查询失败", zap.Any("info", err))
				continue
			}
			orderItem.GatherSupplySN = item.OrderNo
			err = source.DB().Save(&orderItem).Error
			if err != nil {
				log.Log().Error("zyhx 子订单orderitem-save失败", zap.Any("info", err))
			}
		}

	}

	return

}

type OrderDeliverInfo struct {
	Success       bool   `json:"success"`
	ResultCode    string `json:"result_code"`
	ResultMessage string `json:"result_message"`

	Result []struct {
		Carrier     string `json:"carrier"`
		DeliveryNo  string `json:"deliveryNo"`
		UpOrderNo   string `json:"upOrderNo"`
		OutOrderSku []struct {
			ProdId    int     `json:"prodId"`
			SkuId     string  `json:"skuId"`
			OldPrice  float64 `json:"oldPrice"`
			OutPrice  float64 `json:"outPrice"`
			CanVat    int     `json:"canVat"`
			OutNum    int     `json:"outNum"`
			BNeedGift bool    `json:"bNeedGift"`
		} `json:"outOrderSku"`
		OrderTrack []struct {
			MsgTime  string `json:"msgTime"`
			Content  string `json:"content"`
			Operator string `json:"operator"`
		} `json:"orderTrack"`
	} `json:"result"`
}

//type OrderDeliverInfo struct {
//	Success       bool   `json:"success"`
//	ResultCode    string `json:"result_code"`
//}

func (y *JD) OrderDeliver(orderSn string) (err error, orderDeliverInfo OrderDeliverInfo) {
	url := y.Http + "/gateway"
	var resData []byte
	var content = make(map[string]interface{})
	content["orderNo"] = orderSn
	jsonData, err := json.Marshal(&content)
	headerData := url2.Values{}
	headerData.Add("appToken", y.dat.BaseInfo.Token)
	headerData.Add("method", "open.api.shop.order.huiOrderTrack")
	headerData.Add("version", "1.0")
	headerData.Add("seqNo", "123456789012345")
	headerData.Add("content", string(jsonData))

	var header = make(map[string]string)
	err, resData = utils.PostForm(url, headerData, header)
	if err != nil {
		return
	}

	err = json.Unmarshal(resData, &orderDeliverInfo)
	if err != nil {
		return
	}

	if orderDeliverInfo.Success != true {
		err = errors.New(orderDeliverInfo.ResultMessage)
		return
	}

	fmt.Println(resData)

	return

}

type OrderRes struct {
	Success       bool   `json:"success"`
	ResultCode    string `json:"result_code"`
	ResultMessage string `json:"result_message"`

	Result struct {
		OutOrderNo    string `json:"outOrderNo"`
		SuccessOrders []struct {
			UpUserId     int     `json:"upUserId"`
			OutOrderNo   string  `json:"outOrderNo"`
			OrderNo      string  `json:"orderNo"`
			UpOrderNo    string  `json:"upOrderNo"`
			OrderFreight float64 `json:"orderFreight"`
			OrderPrice   float64 `json:"orderPrice"`
			Products     []struct {
				ProdId    int     `json:"prodId"`
				ProdNum   int     `json:"prodNum"`
				SalePrice float64 `json:"salePrice"`
			} `json:"products"`
			Msg  string `json:"msg"`
			Code string `json:"code"`
		} `json:"successOrders"`
		FailOrders []interface{} `json:"failOrders"`
	} `json:"result"`
}

// 物流查询
func (y *JD) ExpressQuery(request request.RequestExpress) (err error, data interface{}) {
	url := string(y.Http + "/open/api/order/queryOrderLogistics")
	var resData []byte
	headerData := make(map[string]interface{})
	headerData["accessToken"] = y.dat.BaseInfo.Token
	headerData["tparOrderCode"] = request.OrderSn
	headerData["parentOrderCode"] = request.GatherSupplySn
	reqData, _ := json.Marshal(headerData)
	resData = utils.HttpPostJson(reqData, url)
	//var ResYzhOrderDetail model5.ShipmentList
	data = string(resData)

	return

}
