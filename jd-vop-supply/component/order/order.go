package order

import (
	"context"
	"errors"
	"fmt"
	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
	jdVopPkg "jd-vop-supply/jd-vop-pkg"
	jdVopPkgAddress "jd-vop-supply/jd-vop-pkg/address"
	jdVopPkgOrder "jd-vop-supply/jd-vop-pkg/order"
	jdVopPkgProduct "jd-vop-supply/jd-vop-pkg/product"
	"jd-vop-supply/service"
	orderModel "order/model"
	productModel "product/model"
	publicSupplyCallback "public-supply/callback"
	publicSupplyCommon "public-supply/common"
	publicSupplyRequest "public-supply/request"
	publicSupplyResponse "public-supply/response"
	"time"
	"yz-go/component/log"
	"yz-go/source"
	"yz-go/utils"
)

type JdVop struct {
	SysSetting  service.SysSetting
	GatherId    uint
	AccessToken string
}

func (jd *JdVop) InitSetting(gatherId uint) (err error) {
	// 查询基础设置
	if jd.SysSetting, err = service.GetSetting(gatherId); err != nil {
		return
	}

	if jd.SysSetting.BaseInfo.ClientSecret == "" || jd.SysSetting.BaseInfo.ClientID == "" || jd.SysSetting.BaseInfo.Username == "" || jd.SysSetting.BaseInfo.Password == "" {
		err = errors.New("请先填写供应链配置")
		return
	}

	// 获取当前采集源ID
	jd.GatherId = gatherId

	// 初始化配置信息
	jdVopConfig := jdVopPkg.JdVopConfig{
		Username:     jd.SysSetting.BaseInfo.Username,
		Password:     jd.SysSetting.BaseInfo.Password,
		ClientId:     jd.SysSetting.BaseInfo.ClientID,
		ClientSecret: jd.SysSetting.BaseInfo.ClientSecret,
	}

	// 生成 access_token
	accessTokenKey := fmt.Sprintf("JD_VOP_ACCESS_TOKEN_%d", gatherId)

	var ctx = context.Background()
	if jd.AccessToken, _ = source.Redis().Get(ctx, accessTokenKey).Result(); jd.AccessToken == "" {
		var result jdVopPkg.AccessTokenResult
		if result, err = jdVopPkg.GetAccessToken(jdVopConfig); err != nil {
			return
		}

		if err = source.Redis().SetEX(ctx, accessTokenKey, result.AccessToken, 24*time.Hour).Err(); err != nil {
			return
		}

		jd.AccessToken = result.AccessToken
	}

	return
}

func (jd *JdVop) OrderBeforeCheck(request publicSupplyRequest.RequestSaleBeforeCheck) (err error, result publicSupplyResponse.BeforeCheck) {
	var skuInfo []jdVopPkgOrder.SkuInfo            // 查询运费使用
	var skuNumPrices []jdVopPkgProduct.SkuNumPrice // 下单前商品校验使用
	for _, item := range request.LocalSkus {
		// 查询商品信息
		var sku productModel.Sku
		if err = source.DB().Preload("Product").First(&sku, item.Sku.Sku).Error; err != nil {
			return
		}

		skuInfo = append(skuInfo, jdVopPkgOrder.SkuInfo{
			Num:   item.Number,
			SkuId: sku.Product.SourceGoodsID,
		})
		skuNumPrices = append(skuNumPrices, jdVopPkgProduct.SkuNumPrice{
			Num:   item.Number,
			Price: utils.Decimal(float64(sku.CostPrice) / 100),
			SkuId: sku.Product.SourceGoodsID,
		})
	}

	// 地址转换
	address := request.Address.Province + request.Address.City + request.Address.Area + request.Address.Street + request.Address.Description

	var transResult jdVopPkgAddress.TransResult
	if transResult, err = jdVopPkgAddress.Trans(jd.AccessToken, address); err != nil {
		return
	}

	// 下单前商品校验
	checkSkuSaleStateAndStockRequest := jdVopPkgProduct.CheckSkuSaleStateAndStockRequest{
		AccessToken:  jd.AccessToken,
		Province:     transResult.ProvinceId,
		City:         transResult.CityId,
		County:       transResult.CountyId,
		Town:         transResult.TownId,
		SkuNumPrices: skuNumPrices,
	}

	log.Log().Error("京东VOP下单前置校验参数：", zap.Any("Request", checkSkuSaleStateAndStockRequest))

	var checkSkuSaleStateAndStockResult []jdVopPkgProduct.CheckSkuSaleStateAndStockResult
	if checkSkuSaleStateAndStockResult, err = jdVopPkgProduct.CheckSkuSaleStateAndStock(checkSkuSaleStateAndStockRequest); err != nil {
		return
	}

	for _, item := range checkSkuSaleStateAndStockResult {
		if !item.CanPurchase {
			err = fmt.Errorf("校验商品失败[%d]", item.SkuID)
			log.Log().Error("京东VOP下单前置校验失败：", zap.Any("item", item))
			return
		}
	}

	//// todo 4.16 查询商品限购信息
	//
	//// todo 7.2 查询运费
	//
	//count := len(request.LocalSkus)
	//
	//numMap := make(map[int64]int, count)
	//var localSkuIds []uint
	//for _, item := range request.LocalSkus {
	//	numMap[item.Sku.Sku] = item.Number
	//
	//	localSkuIds = append(localSkuIds, uint(item.Sku.Sku))
	//}
	//
	//// 查询商品信息
	//var skus []productModel.Sku
	//if err = source.DB().Where("id IN (?)", localSkuIds).Preload("Product").Find(&skus).Error; err != nil {
	//	return
	//}
	//
	//// 组装商品区域购买限制参数、查询运费参数
	//var jdSkuIds []uint
	//
	//for _, sku := range skus {
	//	num, ok := numMap[int64(sku.ID)]
	//	if !ok {
	//		err = fmt.Errorf("商品[%d]数量有误", sku.ID)
	//		return
	//	}
	//
	//	jdSkuIds = append(jdSkuIds, sku.Product.SourceGoodsID)
	//	jdSkuInfo = append(jdSkuInfo, jdVopPkgOrder.SkuInfo{
	//		Num:   num,
	//		SkuId: sku.Product.SourceGoodsID,
	//	})
	//}
	//
	//// 查询商品区域购买限制
	//checkAreaLimitParams := jdVopPkgProduct.CheckAreaLimitParams{
	//	AccessToken: jd.AccessToken,
	//	Province:    transResult.ProvinceId,
	//	City:        transResult.CityId,
	//	County:      transResult.CountyId,
	//	Town:        transResult.TownId,
	//	SkuIds:      jdSkuIds,
	//}
	//
	//var checkAreaLimitResult []jdVopPkgProduct.CheckAreaLimitResult
	//if checkAreaLimitResult, err = jdVopPkgProduct.CheckAreaLimit(checkAreaLimitParams); err != nil {
	//	return
	//}
	//
	//for _, item := range checkAreaLimitResult {
	//	if item.IsAreaRestrict {
	//		err = fmt.Errorf("商品[%d]区域限制，请更换地址", item.SkuID)
	//		return
	//	}
	//}

	// 查询运费
	freightParams := jdVopPkgOrder.FreightParams{
		AccessToken: jd.AccessToken,
		Province:    transResult.ProvinceId,
		City:        transResult.CityId,
		County:      transResult.CountyId,
		Town:        transResult.TownId,
		Sku:         skuInfo,
		PaymentType: 4, // 默认方式 1:货到付款，4:预存款
	}

	var freightResult jdVopPkgOrder.FreightResult
	if freightResult, err = jdVopPkgOrder.Freight(freightParams); err != nil {
		return
	}

	// 返回默认值
	result.Code = 1
	result.Freight = uint(freightResult.Freight * 100)

	return
}

func (jd *JdVop) SaleBeforeCheck(request publicSupplyRequest.RequestSaleBeforeCheck) (err error, resData publicSupplyResponse.ResSaleBeforeCheck) {
	var skuNumPrices []jdVopPkgProduct.SkuNumPrice // 下单前商品校验使用
	for _, item := range request.LocalSkus {
		// 查询商品信息
		var sku productModel.Sku
		if err = source.DB().Preload("Product").First(&sku, item.Sku.Sku).Error; err != nil {
			return
		}
		skuNumPrices = append(skuNumPrices, jdVopPkgProduct.SkuNumPrice{
			Num:   item.Number,
			Price: utils.Decimal(float64(sku.CostPrice) / 100),
			SkuId: sku.Product.SourceGoodsID,
		})
	}

	// 地址转换
	address := request.Address.Province + request.Address.City + request.Address.Area + request.Address.Street + request.Address.Description

	var transResult jdVopPkgAddress.TransResult
	if transResult, err = jdVopPkgAddress.Trans(jd.AccessToken, address); err != nil {
		return
	}

	// 4.15 下单前商品校验
	checkSkuSaleStateAndStockRequest := jdVopPkgProduct.CheckSkuSaleStateAndStockRequest{
		AccessToken:  jd.AccessToken,
		Province:     transResult.ProvinceId,
		City:         transResult.CityId,
		County:       transResult.CountyId,
		Town:         transResult.TownId,
		SkuNumPrices: skuNumPrices,
	}

	var checkSkuSaleStateAndStockResult []jdVopPkgProduct.CheckSkuSaleStateAndStockResult
	if checkSkuSaleStateAndStockResult, err = jdVopPkgProduct.CheckSkuSaleStateAndStock(checkSkuSaleStateAndStockRequest); err != nil {
		return
	}

	resData.Code = 1
	for _, item := range checkSkuSaleStateAndStockResult {
		if item.CanPurchase {
			resData.Data.Available = append(resData.Data.Available, uint(item.SkuID))
		} else {
			log.Log().Error("京东VOP下单前置校验失败：", zap.Any("item", item))

			resData.Data.Ban = append(resData.Data.Ban, uint(item.SkuID))
		}
	}

	return
}

func (jd *JdVop) ConfirmOrder(request publicSupplyRequest.RequestConfirmOrder) (err error, info *stbz.APIResult) {
	// 查询订单
	var order orderModel.Order
	if err = source.DB().Where("order_sn = ?", request.OrderSn.OrderSn).First(&order).Error; err != nil {
		return
	}

	// 标记订单类型
	order.GatherSupplyType = publicSupplyCommon.SUPPLY_JD_VOP

	var submitOrderSku []jdVopPkgOrder.SubmitOrderSku
	for _, item := range request.LocalSkus {
		// 查询商品信息
		var sku productModel.Sku
		if err = source.DB().Preload("Product").First(&sku, item.Sku.Sku).Error; err != nil {
			return
		}

		submitOrderSku = append(submitOrderSku, jdVopPkgOrder.SubmitOrderSku{
			SkuId:     sku.Product.SourceGoodsID,
			Num:       item.Number,
			Price:     utils.Decimal(float64(sku.CostPrice) / 100),
			BNeedGift: false,
			//YanBao:    []jdVopPkgOrder.YanBaoSku{{SkuId: sku.Product.SourceGoodsID}},
		})
	}

	// 地址转换
	address := request.Address.Province + request.Address.City + request.Address.Area + request.Address.Street + request.Address.Description

	var transResult jdVopPkgAddress.TransResult
	if transResult, err = jdVopPkgAddress.Trans(jd.AccessToken, address); err != nil {
		return
	}

	// 提交订单
	params := jdVopPkgOrder.SubmitParams{
		AccessToken:          jd.AccessToken,
		ThirdOrder:           request.OrderSn.OrderSn,
		Sku:                  submitOrderSku,
		PaymentType:          4,
		IsUseBalance:         1,
		SubmitState:          1,
		Name:                 request.Address.Consignee,
		Mobile:               request.Address.Phone,
		Province:             transResult.ProvinceId,
		City:                 transResult.CityId,
		County:               transResult.CountyId,
		Town:                 transResult.TownId,
		Address:              request.Address.Description,
		InvoiceState:         2,
		InvoiceType:          24,
		SelectedInvoiceTitle: 5,
		InvoiceContent:       1,
		InvoicePhone:         jd.SysSetting.BaseInfo.TicketTel,
		RegCompanyName:       jd.SysSetting.BaseInfo.TicketNumber,
		RegCode:              jd.SysSetting.BaseInfo.TicketNumber,
		//RegAddr:"",
	}
	var submitResult jdVopPkgOrder.SubmitResult
	if submitResult, err = jdVopPkgOrder.Submit(params); err != nil {
		log.Log().Error("京东VOP下单错误", zap.Any("err", err))
		if err = source.DB().Model(&order).Updates(map[string]interface{}{"gather_supply_msg": err.Error()}).Error; err != nil {
			return
		}
		return
	}

	// 修改第三方订单号
	if err = source.DB().Model(&order).Updates(map[string]interface{}{"gather_supply_sn": submitResult.JdOrderId}).Error; err != nil {
		return
	}

	return
}

func (jd *JdVop) ExpressQuery(request publicSupplyRequest.RequestExpress) (err error, info interface{}) {
	return
}

func (jd *JdVop) AfterSalesBeforeCheck(request publicSupplyRequest.RequestAfterSale) (err error, info interface{}) {
	return
}

func (jd *JdVop) AfterSalesPicture(request publicSupplyRequest.RequestAfterSalePicture) (err error, info interface{}) {
	return
}

func (jd *JdVop) AfterSale(request publicSupplyRequest.AfterSale) (err error, info interface{}) {
	return
}

func (jd *JdVop) OrderDelivery(OrderData publicSupplyCallback.OrderCallBack) (err error) {
	return
}

func (jd *JdVop) GetAllAddress() (err error, data interface{}) {
	return
}
