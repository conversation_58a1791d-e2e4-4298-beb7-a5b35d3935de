package message

import (
	"encoding/json"
	"fmt"
	jdVopPkg "jd-vop-supply/jd-vop-pkg"
)

/**
 * 11.1 查询推送信息
 *
 * 查询推送信息接口
 */

func Query(accessToken, messageType string) (result string, err error) {
	url := "https://bizapi.jd.com/api/message/get"

	params := map[string]interface{}{
		"token": accessToken,
		"type":  messageType,
	}

	resp, err := jdVopPkg.PostForm(url, params)
	if err != nil {
		return
	}

	var response TrackResponse
	if err = json.Unmarshal(resp, &response); err != nil {
		return
	}

	if response.Success == false {
		err = fmt.Errorf("查询运费失败，code：%s，原因：%s", response.ResultCode, response.ResultMessage)
		return
	}
	return response.Result, nil
}

type TrackResponse struct {
	Success       bool   `json:"success"`
	Result        string `json:"result"`
	ResultCode    string `json:"resultCode"`
	ResultMessage string `json:"resultMessage"`
}
