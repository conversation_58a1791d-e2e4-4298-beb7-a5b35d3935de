package product

import (
	"encoding/json"
	"fmt"
	jdVopPkg "jd-vop-supply/jd-vop-pkg"
)

/**
 * 4.13 查询同类商品
 *
 * 查询被指定为同一类的商品，如同一款式不同颜色的商品，需要注意符合此条件的商品并不一定被指定为同类商品
 */

func SimilarSku(accessToken string, skuId uint) (result []SimilarSkuResult, err error) {
	url := "https://bizapi.jd.com/api/product/getSimilarSku"

	params := map[string]interface{}{
		"token": accessToken,
		"skuId": skuId,
	}

	resp, err := jdVopPkg.PostForm(url, params)
	if err != nil {
		return
	}

	var response SimilarSkuResponse
	if err = json.Unmarshal(resp, &response); err != nil {
		return
	}

	if response.Success == false {
		err = fmt.Errorf("查询同类商品失败，code：%s，原因：%s", response.ResultCode, response.ResultMessage)
		return
	}
	return response.Result, nil
}

type SimilarSkuResponse struct {
	Success       bool               `json:"success"`
	Result        []SimilarSkuResult `json:"result"`
	ResultCode    string             `json:"resultCode"`
	ResultMessage string             `json:"resultMessage"`
}

type SimilarSkuResult struct {
	Dim          int        `json:"dim"`          // 维度
	SaleName     string     `json:"saleName"`     // 销售名称
	SaleAttrList []SaleAttr `json:"saleAttrList"` // 商品销售标签列表
}

type SaleAttr struct {
	ImagePath string `json:"imagePath"` // 标签图片地址
	SaleValue string `json:"saleValue"` // 标签名称
	SkuIds    []uint `json:"skuIds"`    // 当前标签下的同类商品skuId集合
}
