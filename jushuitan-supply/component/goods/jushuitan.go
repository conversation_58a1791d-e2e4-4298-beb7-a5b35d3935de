package goods

import (
	"encoding/json"
	"errors"
	"fmt"
	gatherSupplyRequest "gather-supply/request"
	"jushuitan-supply/model"
	"jushuitan-supply/mq_init"
	"jushuitan-supply/mq_update"
	common2 "jushuitan/common"
	log2 "log"
	pmodel "product/model"
	service2 "product/service"
	callback2 "public-supply/callback"
	"public-supply/common"
	publicModel "public-supply/model"
	"public-supply/request"
	service3 "public-supply/service"
	"public-supply/setting"
	"strconv"
	"strings"
	"sync"
	"time"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"

	"github.com/chenhg5/collection"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type Jushuitan struct {
}

func (self *Jushuitan) ManuallyProductUpdate(productID uint) (err error) {
	//TODO implement me
	panic("implement me")
}

func (self *Jushuitan) SynchronizeProductsToLocal() (err error) {
	//TODO implement me
	panic("implement me")
}

var wg sync.WaitGroup

func (self *Jushuitan) ImportSelectGoodsRun(info publicModel.SelectGoods) (err error, list interface{}) {
	orderPN := GetOrderNo()
	common.GlobalOrderSN = orderPN
	goodsRecord := publicModel.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: len(info.List),
		Status:            1,
	}
	var resultInt []int
	var field string
	field = "source_goods_id"

	err = source.DB().Model(pmodel.Product{}).Where("gather_supply_id=?", info.GatherSupplyID).Pluck(field, &resultInt).Error
	if err != nil {
		log2.Println("查询供应链商品id错误", err)
		return
	}

	if len(info.List) <= 0 {
		log2.Println("导入空数据")
		err = errors.New("导入的是空数据")
		return
	}

	var idsArr []int
	idsArr = GetIdArr(info.List)

	difference := collection.Collect(idsArr).Diff(resultInt).ToIntArray()
	repeat := len(idsArr) - len(difference)
	goodsRecord.RepeatQuantity = repeat //重复数据
	if len(difference) <= 0 {
		goodsRecord.Status = 2
		goodsRecord.CompletionStatus = 1
		source.DB().Omit("goods_arr").Create(&goodsRecord) //当前没有商品可以导入
		return
	}
	source.DB().Omit("goods_arr").Create(&goodsRecord) //创建导入记录
	var goodsList []publicModel.Goods
	for _, v := range difference {
		for _, item := range info.List {
			if item.ID == v {
				goodsList = append(goodsList, item)
			}
		}

	}
	info.List = goodsList
	arrList := splitArray(info.List, 20)
	for index, item := range arrList {
		wg.Add(1)
		fmt.Println("循环", index)
		self.RunSelectGoodsConcurrent(orderPN, item, info.Categorys, info.Key, info.GatherSupplyID)
	}
	wg.Wait()
	err = SetImportRecordCompletion(orderPN)
	fmt.Println("全部完成：")

	return
}
func (self *Jushuitan) RunSelectGoodsConcurrent(orderPN string, list []publicModel.Goods, category string, key string, gatherSupplyID uint) (err error) {

	defer wg.Done()

	cateList := strings.Split(category, ",")
	var cateId1, cateId2, cateId3 int
	cateId1, err = strconv.Atoi(cateList[0])
	cateId2, err = strconv.Atoi(cateList[1])
	cateId3, err = strconv.Atoi(cateList[2])

	var listGoods []*pmodel.Product
	var recordError []publicModel.SupplyGoodsImportRecordErrors
	err, listGoods, recordError = self.CommodityAssembly(list, cateId1, cateId2, cateId3, gatherSupplyID, 0)
	if err != nil {
		return
	}
	if len(listGoods) > 0 {
		err = service3.FinalProcessing(listGoods, orderPN)
		if err != nil {
			return
		}

	}
	if len(recordError) > 0 {
		err = service3.FinalProcessingError(recordError, orderPN)
		if err != nil {
			return
		}
	}
	return

}
func (y *Jushuitan) ValidateConfig(params gatherSupplyRequest.ValidateConfigParams) (err error) {
	// 聚水潭供应链请求接口时，未使用供应链配置信息
	// 实际使用配置存储位置在应用--工具类--聚水潭，配置项为“jushuitan_setting”
	return
}

func (y *Jushuitan) InitSetting(gatherSupplyID uint) (err error) {
	var sysSetting model2.SysSetting
	err, sysSetting = setting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		return
	}
	GatherSupplyID = gatherSupplyID
	err = json.Unmarshal([]byte(sysSetting.Value), &JushuitanData)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	if JushuitanData.BaseInfo.AppKey == "" || JushuitanData.BaseInfo.AppSecret == "" {
		err = errors.New("请先配置供应链key")
		return
	}

	return
}
func (s *Jushuitan) DeleteGoods(id uint) (err error) {
	//TODO implement me
	return
}

func (s *Jushuitan) GetSupplyBalance(GatherSupplyID uint) (err error, balance interface{}) {

	return
}

type BaseInfoData struct {
	AppKey                string `json:"appKey"`
	AppSecret             string `json:"appSecret"`
	AccessToken           string `json:"access_token"`
	AccessTokenExpireTime int    `json:"access_token_expire_time"`
	RefreshToken          string `json:"refresh_token"`
	Weeks                 int    `json:"weeks"`
}

type JushuitanSupplySetting struct {
	BaseInfo   BaseInfoData           `json:"baseInfo"`
	UpdateInfo setting.UpdateInfoData `json:"update"`
	Pricing    setting.PricingData    `json:"pricing"`
	Management setting.Management     `json:"management"`
}

var JushuitanData *JushuitanSupplySetting

var GatherSupplyID uint

func (*Jushuitan) InitGoodsV1() (err error) {
	//去jushuitan拉取全量商品开始
	var createJushuitanGoods []model.JushuitanProduct
	var weekInt = int64(60 * 60 * 24 * 7)
	var endTime = time.Now().Unix()
	var startTime = endTime - weekInt
	var requestParams model.JushuitanGoodsRequest
	for i := int64(1); i <= int64(JushuitanData.BaseInfo.Weeks); i++ {
		endTime = endTime - ((i - 1) * weekInt)
		startTime = startTime - ((i - 1) * weekInt)
		requestParams.ModifiedBegin = time.Unix(startTime, 0).Format("2006-01-02 15:04:05")
		requestParams.ModifiedEnd = time.Unix(endTime, 0).Format("2006-01-02 15:04:05")
		requestParams.PageSize = 50
		requestParams.PageIndex = 1
		var jsonData []byte
		jsonData, err = json.Marshal(requestParams)
		if err != nil {
			return err
		}
		var responseByte []byte
		err, responseByte = common2.RequestJushuitan(jsonData, "https://openapi.jushuitan.com/open/mall/item/query")
		if err != nil {
			//log.Log().Error("聚水潭监听推送商品错误3", zap.Any("err", err), zap.Any("data", product))
			return nil
		}
		var response model.JushuitanGoodsResponse
		err = json.Unmarshal(responseByte, &response)
		if err != nil {
			//log.Log().Error("聚水潭监听推送商品错误4", zap.Any("err", err), zap.Any("data", product))
			return nil
		}
		if response.Code != 0 {
			err = errors.New(response.Msg)
			return
		}
		for _, item := range response.Data.Datas {
			item.GatherSupplyID = GatherSupplyID
			createJushuitanGoods = append(createJushuitanGoods, item)
		}
		if response.Data.PageCount > 1 {
			for page := 2; page <= response.Data.PageCount; page++ {
				requestParams.PageIndex = page

				jsonData, err = json.Marshal(requestParams)
				if err != nil {
					return
				}

				err, responseByte = common2.RequestJushuitan(jsonData, "https://openapi.jushuitan.com/open/mall/item/query")
				if err != nil {
					return
				}
				err = json.Unmarshal(responseByte, &response)
				if err != nil {
					return
				}
				if response.Code != 0 {
					err = errors.New(response.Msg)
					return
				}
				for _, item := range response.Data.Datas {
					item.GatherSupplyID = GatherSupplyID
					createJushuitanGoods = append(createJushuitanGoods, item)
				}
			}
		}
	}
	err = source.DB().Exec("truncate table jushuitan_products").Error
	if err != nil {
		return err
	}
	err = source.DB().CreateInBatches(&createJushuitanGoods, 1000).Error
	return
}

func (*Jushuitan) InitGoods() (err error) {
	//去jushuitan拉取全量商品开始
	var createJushuitanGoods []model.JushuitanGoodsListData
	//var weekInt = int64(60 * 60 * 24 * 6)
	//var endTime = time.Now().Unix() - 1
	//var startTime = endTime - weekInt
	var requestParams model.JushuitanGoodsRequestV2
	//for i := int64(1); i <= int64(JushuitanData.BaseInfo.Weeks); i++ {
	//endTime = endTime - weekInt
	//startTime = startTime - weekInt
	//requestParams.CreatedStartTime = time.Unix(startTime, 0).Format("2006-01-02 15:04:05")
	//requestParams.CreatedEndTime = time.Unix(endTime, 0).Format("2006-01-02 15:04:05")
	requestParams.Sort = "DESC"
	//requestParams.SortField = "CREATED"
	requestParams.PageSize = 100
	requestParams.PageNum = 1
	var jsonData []byte
	jsonData, err = json.Marshal(requestParams)
	if err != nil {
		return err
	}
	var responseByte []byte
	err, responseByte = common2.RequestJushuitan(jsonData, "https://openapi-hz.jushuitan.com/open/api/goods/inneropen/goods/querygoodslist")
	if err != nil {
		//log.Log().Error("聚水潭监听推送商品错误3", zap.Any("err", err), zap.Any("data", product))
		return nil
	}
	var response model.JushuitanGoodsResponseV2
	err = json.Unmarshal(responseByte, &response)
	if err != nil {
		//log.Log().Error("聚水潭监听推送商品错误4", zap.Any("err", err), zap.Any("data", product))
		return nil
	}
	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}
	if len(response.Data.List) == 0 {
		return
	}
	for _, item := range response.Data.List {
		//item.GatherSupplyID = GatherSupplyID
		createJushuitanGoods = append(createJushuitanGoods, item)
	}
	pageCount := response.Data.Total/100 + 1

	if response.Data.HasNext {
		for page := 2; page <= pageCount; page++ {
			requestParams.PageNum = page

			jsonData, err = json.Marshal(requestParams)
			if err != nil {
				return
			}

			err, responseByte = common2.RequestJushuitan(jsonData, "https://openapi-hz.jushuitan.com/open/api/goods/inneropen/goods/querygoodslist")
			if err != nil {
				return
			}
			time.Sleep(time.Millisecond * 400)

			err = json.Unmarshal(responseByte, &response)
			if err != nil {
				return
			}
			if response.Code != 0 {
				err = errors.New(response.Msg)
				return
			}
			for _, item := range response.Data.List {
				//item.GatherSupplyID = GatherSupplyID
				createJushuitanGoods = append(createJushuitanGoods, item)
			}
		}
	}

	//}
	var newIds []string
	for _, v := range createJushuitanGoods {
		newIds = append(newIds, v.StyleCode)
	}

	var oldData []model.JushuitanGoodsDetail
	err = source.DB().Find(&oldData).Error
	if err != nil {
		return
	}
	var newDataMap = make(map[string]model.JushuitanGoodsListData)
	for _, cj := range createJushuitanGoods {
		newDataMap[cj.StyleCode] = cj
	}

	var oldDataMap = make(map[string]model.JushuitanGoodsDetail)
	for _, oj := range oldData {
		oldDataMap[oj.StyleCode] = oj
	}
	var deleteIds []string
	for _, od := range oldData {
		if _, ok := newDataMap[od.StyleCode]; !ok {
			deleteIds = append(deleteIds, od.StyleCode)
		}
	}
	var addIds []string
	var updateIds []string
	for _, cjg := range createJushuitanGoods {
		if _, ok := oldDataMap[cjg.StyleCode]; !ok {
			addIds = append(addIds, cjg.StyleCode)
		} else {
			updateIds = append(updateIds, cjg.StyleCode)

		}
	}
	for _, addId := range addIds {
		var jushuitanGoodMessage mq_init.JushuitanGoodsMessage
		jushuitanGoodMessage.Data = newDataMap[addId]
		err = mq_init.PublishMessage(jushuitanGoodMessage)
		if err != nil {
			return
		}
	}

	for _, updateId := range updateIds {
		var jushuitanGoodMessage mq_update.JushuitanGoodsMessage
		jushuitanGoodMessage.Data = newDataMap[updateId]
		err = mq_update.PublishMessage(jushuitanGoodMessage)
		if err != nil {
			return
		}
	}

	// 删除主表和关联的 SKU 数据
	err = source.DB().Transaction(func(tx *gorm.DB) error {
		// 删除关联的 SKU
		if err := tx.Where("item_spu_id in (select item_spu_id from jushuitan_goods_details where style_code in ?)", deleteIds).Delete(&model.ItemSku{}).Error; err != nil {
			return err
		}
		// 删除主表数据
		if err := tx.Where("style_code in ?", deleteIds).Delete(&model.JushuitanGoodsDetail{}).Error; err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return
	}
	return
}

func (jushuitan *Jushuitan) GetGoods(info request.GetGoodsSearch) (err error, data interface{}, total int64, serverRatio int) {
	limit := info.Limit
	offset := info.Limit * (info.Page - 1)
	var jushuitanGoods []model.JushuitanGoodsDetail
	db := source.DB().Preload("ItemSkuList").Model(&model.JushuitanGoodsDetail{})
	//db.Where("gather_supply_id = ?", info.GatherSupplyID)

	if info.SearchWords != "" {
		db.Where("`item_name` like ?", "%"+info.SearchWords+"%")
	}
	if info.IsImport > 0 {
		var jushuitanProductIds []string
		err = source.DB().Model(&pmodel.Product{}).Where("gather_supply_id = ?", info.GatherSupplyID).Where("deleted_at is NULL").Pluck("code", &jushuitanProductIds).Error
		if err != nil {
			return
		}

		if info.IsImport == 1 {
			db.Where("`style_code` in ?", jushuitanProductIds)

		} else if info.IsImport == 2 {
			if len(jushuitanProductIds) > 0 {
				db.Where("`style_code` not in ?", jushuitanProductIds)
			}
		}

	}
	if info.CategoryID > 0 {
		//db.Where("`c_id` = ?", info.CategoryID)
	}
	if info.DistributorCoId != "" {
		db.Where("`distributor_co_id` = ?", info.DistributorCoId)
	}
	if info.RangeType != "" {
		if info.RangeType == "guide_price" {
			db.Where("`sale_price` >= ?", info.RangeForm*100)
			db.Where("`sale_price` <= ?", info.RangeTo*100)
		}
		if info.RangeType == "agreement_price" {
			db.Where("`supply_price` >= ?", info.RangeForm*100)
			db.Where("`supply_price` <= ?", info.RangeTo*100)
		}

	}

	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&jushuitanGoods).Error

	data = jushuitan.ProductToGoods(jushuitanGoods, info.GatherSupplyID)
	return
}

func (*Jushuitan) ProductToGoods(data []model.JushuitanGoodsDetail, gatherID uint) (list []publicModel.Goods) {
	var ids []string
	source.DB().Model(pmodel.Product{}).Where("gather_supply_id=?", GatherSupplyID).Pluck("code", &ids)
	for _, v := range data {

		var isImport = 0
		for _, id := range ids {
			if v.StyleCode == id {
				isImport = 1
			}
		}
		var rate float64
		if len(v.ItemSkuList) == 0 {
			continue
		}
		if v.ItemSkuList[0].SalePrice > v.ItemSkuList[0].SupplyPrice {
			rate = service2.Decimal((float64(v.ItemSkuList[0].SalePrice) - float64(v.ItemSkuList[0].SupplyPrice)) / (float64(v.ItemSkuList[0].SalePrice)))
		}
		var intXS uint64
		var salePrice uint
		if JushuitanData.Pricing.SupplySales == 1 {
			intXS, _ = strconv.ParseUint(JushuitanData.Pricing.SupplySalesGuide, 10, 32)
			salePrice = uint(v.ItemSkuList[0].SalePrice*100) * uint(intXS) / 100
		} else if JushuitanData.Pricing.SupplySales == 2 {
			intXS, _ = strconv.ParseUint(JushuitanData.Pricing.SupplySalesAgreement, 10, 32)
			salePrice = uint(v.ItemSkuList[0].SupplyPrice*100) * uint(intXS) / 100
		} else {
			salePrice = uint(v.ItemSkuList[0].SupplyPrice * 100)
		}
		var cover string
		if len(v.ItemPhoto.MainImageList) > 0 {
			cover = v.ItemPhoto.MainImageList[0]
		}
		var status int
		if v.ItemStatus == "cantDistribution" {
			status = 1
		} else {
			status = 0
		}
		var item publicModel.Goods
		item = publicModel.Goods{
			GatherSupplyID:    gatherID,
			ThirdCategoryName: "",
			ThirdBrandName:    "",
			IsImport:          uint(isImport),
			MarketPrice:       uint(v.ItemSkuList[0].SalePrice * 100),
			ID:                int(v.ID),
			ProductID:         int(0),
			TotalStock:        0,
			Cover:             cover,
			Status:            status,
			Stock:             0,
			Title:             v.ItemName,
			CategoryIds:       []string{},
			CostPrice:         uint(v.ItemSkuList[0].CostPrice * 100),
			AgreementPrice:    uint(v.ItemSkuList[0].SupplyPrice * 100),
			GuidePrice:        uint(v.ItemSkuList[0].SalePrice * 100),
			Rate:              rate,
			SalePrice:         salePrice,
			SN:                v.StyleCode,
			ShopName:          v.DistributorSupplierName,
		}
		if item.MarketPrice < item.AgreementPrice {
			item.Rate = 0
		}
		list = append(list, item)
	}
	return

}

func (jushuitan *Jushuitan) ImportGoodsRun(info request.GetGoodsSearch) (err error, data interface{}) {
	db := source.DB().Model(&model.JushuitanGoodsDetail{})
	//db.Where("gather_supply_id = ?", info.GatherSupplyID)

	if info.SearchWords != "" {
		db.Where("`item_name` like ?", "%"+info.SearchWords+"%")
	}
	if info.IsImport > 0 {
		var jushuitanProductIds []string
		err = source.DB().Model(&pmodel.Product{}).Where("gather_supply_id = ?", info.GatherSupplyID).Where("deleted_at is NULL").Pluck("code", &jushuitanProductIds).Error
		if err != nil {
			return
		}

		if info.IsImport == 1 {
			db.Where("`style_code` in ?", jushuitanProductIds)

		} else if info.IsImport == 2 {
			if len(jushuitanProductIds) > 0 {
				db.Where("`style_code` not in ?", jushuitanProductIds)
			}
		}

	}
	if info.CategoryID > 0 {
		//db.Where("`c_id` = ?", info.CategoryID)
	}
	if info.DistributorCoId != "" {
		db.Where("`distributor_co_id` = ?", info.DistributorCoId)
	}
	if info.RangeType != "" {
		if info.RangeType == "guide_price" {
			db.Where("`sale_price` >= ?", info.RangeForm*100)
			db.Where("`sale_price` <= ?", info.RangeTo*100)
		}
		if info.RangeType == "agreement_price" {
			db.Where("`supply_price` >= ?", info.RangeForm*100)
			db.Where("`supply_price` <= ?", info.RangeTo*100)
		}

	}

	var total int64
	err = db.Count(&total).Error
	searchText, err := json.Marshal(info)

	orderPN := GetOrderNo()
	goodsRecord := publicModel.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: int(total),
		Status:            1,
		SearchCriteria:    string(searchText),
	}

	source.DB().Omit("goods_arr").CreateInBatches(&goodsRecord, 500)

	err = jushuitan.RunGoodsConcurrent(nil, info, db, 1, orderPN)
	if err != nil {
		return
	}
	err = SetImportRecordCompletion(orderPN)
	if err != nil {
		fmt.Println("变更导入记录状态错误", err)

	}
	fmt.Println("导入供应链商品全部完成")
	return

}

func (jushuitan *Jushuitan) RunGoodsConcurrent(wg *sync.WaitGroup, info request.GetGoodsSearch, db *gorm.DB, i int, orderPN string) (err error) {

	var ProductItem []model.JushuitanGoodsDetail
	err = db.Find(&ProductItem).Error
	if err != nil {
		return
	}
	if len(ProductItem) > 0 {

		cateList := strings.Split(info.Categorys, ",")
		var cateId1, cateId2, cateId3 int
		if len(cateList) >= 2 {
			cateId1, err = strconv.Atoi(cateList[0])
			cateId2, err = strconv.Atoi(cateList[1])
			cateId3, err = strconv.Atoi(cateList[2])
		}

		var Item []publicModel.Goods

		var resultArr []string
		err = source.DB().Model(pmodel.Product{}).Where("gather_supply_id = ?", info.GatherSupplyID).Pluck("code", &resultArr).Error
		if err != nil {
			return
		}

		if len(ProductItem) <= 0 {
			fmt.Println("没有选择可导入的数据")

			return
		}

		Item = jushuitan.ProductToGoods(ProductItem, info.GatherSupplyID)
		idsArr := GetIdArrs(Item)

		difference := collection.Collect(idsArr).Diff(resultArr).ToStringArray()

		//fmt.Println("查询到的导入数据：", idsArr)
		//fmt.Println("已经存在的数据：", resultArr)
		//fmt.Println("目前可以导入的数据：", difference)

		err = SetImportRepeat(orderPN, len(idsArr)-len(difference))

		if len(difference) <= 0 {
			fmt.Println("没有可以导入的数据")
			err = source.DB().Model(publicModel.SupplyGoodsImportRecord{}).Where("batch=?", orderPN).Update("status", 2).Error

			err = errors.New("当前数据已经存在，请导入新数据")
			return
		}
		var goodsList []publicModel.Goods

		for _, v := range difference {

			for _, item := range Item {
				if item.SN == v {
					goodsList = append(goodsList, item)
				}
			}

		}

		var listGoods []*pmodel.Product
		err, listGoods, _ = jushuitan.CommodityAssembly(goodsList, cateId1, cateId2, cateId3, info.GatherSupplyID, 0)

		if len(listGoods) > 0 {
			service3.FinalProcessing(listGoods, orderPN)
		}

	} else {
		fmt.Println("获取失败的：", i, info.Limit, info.Source)
	}

	return
}

// 商品组装
func (jushuitan *Jushuitan) CommodityAssembly(list []publicModel.Goods, cateId1, cateId2, cateId3 int, gatherSupplyID uint, isUpdate int) (err error, listGoods []*pmodel.Product, recordErrors []publicModel.SupplyGoodsImportRecordErrors) {
	idArr := GetIdArrs(list)
	var data map[string]model.JushuitanGoodsDetail
	err, data = jushuitan.BatchGetGoodsDetails(idArr, isUpdate)
	if err != nil {
		return
	}
	//_, detailData := BatchGetGoodsDetails(ids) //批量获取详情
	var riskManageRecord []publicModel.RiskManagementRecord

	var shops []model.JushuitanShop
	err = source.DB().Find(&shops).Error
	if err != nil {
		return
	}
	var shopMap = make(map[string]model.JushuitanShop)
	var virtualStockShops []string
	for _, shop := range shops {
		shopMap[shop.ShopId] = shop
		if shop.IsVirtualStock == 1 {
			virtualStockShops = append(virtualStockShops, shop.ShopId)
		}
	}
	var sourceGoodsIds []uint
	for _, elem := range list {
		detail, ok := data[elem.SN]
		if !ok && isUpdate == 0 {
			continue
		}
		goods := new(pmodel.Product)

		if isUpdate == 1 {
			goods.ID = uint(elem.ID)
		}
		goods.Title = detail.ItemName
		goods.MD5 = detail.MD5
		var intXAdvice uint64
		if JushuitanData.Pricing.SupplyAdvice == 1 {
			intXAdvice, err = strconv.ParseUint(JushuitanData.Pricing.SupplyAdviceGuide, 10, 32)
			goods.OriginPrice = uint(detail.SupplyPrice*100) * uint(intXAdvice) / 100
		} else if JushuitanData.Pricing.SupplyAdvice == 2 {
			intXAdvice, err = strconv.ParseUint(JushuitanData.Pricing.SupplyAdviceAgreement, 10, 32)
			goods.OriginPrice = uint(detail.SupplyPrice*100) * uint(intXAdvice) / 100
		} else {
			goods.OriginPrice = uint(detail.SupplyPrice * 100)
		}
		var intX uint64
		if JushuitanData.Pricing.SupplySales == 1 {
			intX, err = strconv.ParseUint(JushuitanData.Pricing.SupplySalesGuide, 10, 32)
			goods.Price = uint(detail.SupplyPrice*100) * uint(intX) / 100
		} else if JushuitanData.Pricing.SupplySales == 2 {
			intX, err = strconv.ParseUint(JushuitanData.Pricing.SupplySalesAgreement, 10, 32)
			goods.Price = uint(detail.SupplyPrice*100) * uint(intX) / 100
		} else {
			goods.Price = uint(detail.SupplyPrice * 100)
		}
		goods.GuidePrice = goods.OriginPrice
		goods.CostPrice = uint(detail.SupplyPrice * 100)
		goods.ActivityPrice = goods.GuidePrice
		goods.Stock = elem.Stock
		//var status int
		//if detail.ItemStatus == "cantDistribution" {
		//	status = 1
		//} else {
		//	status = 0
		//}
		goods.IsDisplay = 0
		var riskRecord publicModel.RiskManagementRecord
		riskRecord.ProductID = goods.ID
		riskRecord.SourceGoodsID = goods.SourceGoodsID
		riskRecord.GatherSupplyID = goods.GatherSupplyID
		if JushuitanData.Management.ProductPriceStatus == 1 {
			if goods.Price < goods.CostPrice*(JushuitanData.Management.Products/100) {
				goods.IsDisplay = 0
				riskManageRecord = append(riskManageRecord, riskRecord)

			}
		} else if JushuitanData.Management.ProductPriceStatus == 2 {
			if (goods.Price-goods.CostPrice)/goods.CostPrice < JushuitanData.Management.Profit/100 {
				goods.IsDisplay = 0
				riskManageRecord = append(riskManageRecord, riskRecord)

			}
		}
		goods.ImageUrl = elem.Cover
		goods.Unit = elem.Unit
		goods.Sn = detail.ItemSpuId
		goods.Code = detail.StyleCode
		goods.Unit = "默认"
		goods.Source = common.JUSHUITAN_SOURCE
		goods.SourceGoodsID = detail.ID
		goods.GatherSupplyID = GatherSupplyID
		goods.JushuitanDistributorSupplierName = detail.DistributorSupplierName
		goods.JushuitanDistributorCoId = detail.DistributorCoId
		if _, jok := shopMap[goods.JushuitanDistributorCoId]; jok {
			goods.FreightType = 1
			goods.FreightTemplateID = shopMap[goods.JushuitanDistributorCoId].FreightTemplateID
			if shopMap[goods.JushuitanDistributorCoId].IsOpen == 1 {
				//走单独定价策略
				err, goods.CostPrice, goods.Price, goods.OriginPrice, goods.ActivityPrice, goods.GuidePrice = GetShopPricingPrice(uint(detail.SupplyPrice*100), shopMap[goods.JushuitanDistributorCoId])
			}
		}
		if isUpdate == 0 {
			if cateId1 == 0 || cateId2 == 0 || cateId3 == 0 {
				err = errors.New("必须选择分类")
				return
			} else {
				goods.Category1ID = uint(cateId1)
				goods.Category2ID = uint(cateId2)
				goods.Category3ID = uint(cateId3)
			}
		}

		//if detail.Brand != "" {
		//	var brand model3.Brand
		//	brand.Name = detail.Brand
		//	source.DB().Where("`name` = ?", brand.Name).FirstOrCreate(&brand)
		//	goods.BrandID = brand.ID
		//	goods.FreightType = 0
		//	goods.GatherSupplyID = elem.GatherSupplyID
		//}

		/**
		处理轮播图
		*/

		/**
		处理轮播图结束
		*/

		goods.MinPrice = uint(int(goods.Price))
		goods.MaxPrice = uint(int(goods.Price))
		var totalStock int
		//var spec = make(map[string][]string)
		//var specKeys []string

		//var canFixSku = 0
		goods.DetailImages = "<p>"
		for _, idi := range detail.ItemPhoto.ItemDetailImages {
			goods.DetailImages += "<img src=\"" + idi + "\">"
		}
		goods.DetailImages += "</p>"

		for _, mil := range detail.ItemPhoto.MainImageList {
			goods.Gallery = append(goods.Gallery, pmodel.GalleryItem{
				Type: 1,
				Src:  mil,
			})
		}
		var minProfitRate float64
		for sk, detailSku := range detail.ItemSkuList {
			var sku = pmodel.Sku{}
			//canFixSku++
			sku.Options = append(sku.Options, pmodel.Option{
				SpecName:     detail.FirstSpecName,
				SpecItemName: detailSku.FirstSpecValueName,
			})
			sku.Options = append(sku.Options, pmodel.Option{
				SpecName:     detail.SecondSpecName,
				SpecItemName: detailSku.SecondSpecValueName,
			})

			//for _, skuSaleAttr := range detailSku.SaleAttribute {
			//	var option pmodel.Option
			//	option.SpecName = skuSaleAttr.AttributeName
			//	option.SpecItemName = skuSaleAttr.AttributeValue
			//	sku.Options = append(sku.Options, option)
			//
			//}
			var intXSku uint64
			if JushuitanData.Pricing.SupplyAdvice == 1 {
				intXSku, err = strconv.ParseUint(JushuitanData.Pricing.SupplyAdviceGuide, 10, 32)
				sku.OriginPrice = uint(detailSku.SupplyPrice*100) * uint(intXSku) / 100
			} else if JushuitanData.Pricing.SupplyAdvice == 2 {
				intXSku, err = strconv.ParseUint(JushuitanData.Pricing.SupplyAdviceAgreement, 10, 32)
				sku.OriginPrice = uint(detailSku.SupplyPrice*100) * uint(intXSku) / 100
			} else {
				sku.OriginPrice = uint(detailSku.SupplyPrice * 100)
			}
			var intXS uint64
			if JushuitanData.Pricing.SupplySales == 1 {
				intXS, err = strconv.ParseUint(JushuitanData.Pricing.SupplySalesGuide, 10, 32)
				sku.Price = uint(detailSku.SupplyPrice*100) * uint(intXS) / 100
			} else if JushuitanData.Pricing.SupplySales == 2 {
				intXS, err = strconv.ParseUint(JushuitanData.Pricing.SupplySalesAgreement, 10, 32)
				sku.Price = uint(detailSku.SupplyPrice*100) * uint(intXS) / 100
			} else {
				sku.Price = uint(detailSku.SupplyPrice * 100)
			}
			sku.Title = detailSku.ShortName
			if sku.Title == "" {
				sku.Title = detailSku.FirstSpecValueName + " + " + detailSku.SecondSpecValueName
			}
			sku.Weight = int(detailSku.Weight * 1000)
			if sku.Weight == 0 {
				sku.Weight = 300
			}
			sku.CostPrice = uint(detailSku.SupplyPrice * 100)
			sku.IsDisplay = 1
			if goods.JushuitanDistributorCoId == "11849996" || goods.JushuitanDistributorCoId == "11530094" {
				//隆发国际供应链的商品sku默认库存为500,笑笑定制
				sku.Stock = 500
			}
			if collection.Collect(virtualStockShops).Contains(goods.JushuitanDistributorCoId) {
				//水滴新供应链虚拟库存,都上架
				if sku.Stock == 0 {
					sku.Stock = 9999
				}

			}
			totalStock += sku.Stock
			sku.GuidePrice = sku.OriginPrice
			sku.ActivityPrice = sku.OriginPrice
			sku.OriginalSkuID = 0
			sku.Sn = detailSku.ItemCode
			sku.Code = detailSku.SkuId
			sku.ImageUrl = detailSku.Pic
			if sku.GuidePrice > 0 {
				sku.ProfitRate = utils.ExecProfitRate(sku.GuidePrice, sku.Price)
			} else {
				sku.ProfitRate = 0
			}
			if sk == 0 {
				minProfitRate = sku.ProfitRate
			}
			if sku.ProfitRate <= minProfitRate {
				minProfitRate = sku.ProfitRate
			}
			if _, jok := shopMap[goods.JushuitanDistributorCoId]; jok {
				if shopMap[goods.JushuitanDistributorCoId].IsOpen == 1 {
					//走单独定价策略
					err, sku.CostPrice, sku.Price, sku.OriginPrice, sku.ActivityPrice, sku.GuidePrice = GetShopPricingPrice(uint(detailSku.SupplyPrice*100), shopMap[goods.JushuitanDistributorCoId])
				}
			}
			//err = source.Redis().LPush(context.Background(), "jushuitanSkuStockSync", sku.Sn).Err()
			goods.Skus = append(goods.Skus, sku)

		}
		if len(goods.Skus) > 0 {
			goods.ProfitRate = minProfitRate
		}
		//for _, ups := range detail.Ups {
		//	goods.Attrs = append(goods.Attrs, pmodel.Attr{
		//		Name:  ups.PName,
		//		Value: ups.PvValue,
		//	})
		//}

		//if totalStock == 0 {
		//	goods.IsDisplay = 0
		//}
		//处理资质json图片数组

		//--------处理详情json图片数组结束

		//处----------------理属性json数组

		//---------处理属性json数组结束
		//goods.Desc=detail.Description

		if len(goods.Skus) > 0 {
			listGoods = append(listGoods, goods)
			//err = source.Redis().LPush(context.Background(), "jushuitanUploadProduct", goods.SourceGoodsID).Err()
			sourceGoodsIds = append(sourceGoodsIds, goods.SourceGoodsID)
		} else {
			fmt.Println("无规格商品，不导入", goods.ID)
		}

	}
	err = source.DB().CreateInBatches(&riskManageRecord, 1000).Error
	if err != nil {
		return
	}
	//err = service.SetUploadProductIds(sourceGoodsIds)
	return
}

func GetSku(goods_attrs [][]pmodel.Option) [][]pmodel.Option {
	arrlen := len(goods_attrs) //列

	sku := make([][]pmodel.Option, 0)
	if arrlen == 0 {
		return sku
	}
	for _, val := range goods_attrs[0] {
		temps := make([]pmodel.Option, 0)
		temps = append(temps, val)
		sku = append(sku, temps)
	}
	for i := 0; i < arrlen-1; i++ {
		skuarr := make([][]pmodel.Option, 0)
		for _, val := range sku {
			for _, vals := range goods_attrs[i+1] {
				temp := make([]pmodel.Option, 0)
				temp = append(temp, val...)
				temp = append(temp, vals)
				skuarr = append(skuarr, temp)
			}
		}
		sku = skuarr
	}
	return sku
}
func InArray(need interface{}, haystack interface{}) bool {
	switch key := need.(type) {
	case int:
		for _, item := range haystack.([]int) {
			if item == key {
				return true
			}
		}
	case string:
		for _, item := range haystack.([]string) {
			if item == key {
				return true
			}
		}
	case int64:
		for _, item := range haystack.([]int64) {
			if item == key {
				return true
			}
		}
	case float64:
		for _, item := range haystack.([]float64) {
			if item == key {
				return true
			}
		}
	default:
		return false
	}
	return false
}

// 批量获取商品详情
func (*Jushuitan) BatchGetGoodsDetails(ids []string, isUpdate int) (err error, data map[string]model.JushuitanGoodsDetail) {
	var detailList = make(map[string]model.JushuitanGoodsDetail)

	fmt.Println("BatchGetGoodsDetails:", ids)
	var list []model.JushuitanGoodsDetail
	err = source.DB().Preload("ItemSkuList").Where("`style_code` in ?", ids).Find(&list).Error
	if err != nil {
		return
	}

	var exitsList []pmodel.Product
	err = source.DB().Where("`code` in ?", ids).Where("`gather_supply_id` = ?", GatherSupplyID).Find(&exitsList).Error
	if err != nil {
		return
	}
	var exitsMap = make(map[string]pmodel.Product)
	for _, e := range exitsList {
		exitsMap[e.Code] = e
	}
	fmt.Println("总解析数量：", len(list))
	for _, item := range list {
		if _, ok := exitsMap[item.StyleCode]; ok && isUpdate == 0 {
			continue
		}
		detailList[item.StyleCode] = item
	}
	fmt.Println("总解析数量1：", len(detailList))

	data = detailList
	return
}

func (*Jushuitan) GetGroup() (err error, data interface{}) {

	return

}

func (*Jushuitan) GetCategory(info request.GetCategorySearch) (err error, data interface{}) {

	return
}

func (*Jushuitan) GetCategoryChild(pid int, info request.GetCategoryChild) (err error, data interface{}) {

	type JushuitanCategoryResponse struct {
		Msg  string `json:"msg"`
		Code int    `json:"code"`
		Data struct {
			Datas []struct {
				ParentCId int    `json:"parent_c_id"`
				Name      string `json:"name"`
				CId       int    `json:"c_id"`
				Modified  string `json:"modified"`
			} `json:"datas"`
			PageIndex int  `json:"page_index"`
			HasNext   bool `json:"has_next"`
			DataCount int  `json:"data_count"`
			PageCount int  `json:"page_count"`
			PageSize  int  `json:"page_size"`
		} `json:"data"`
	}
	var requestParams model.JushuitanCategoryRequest
	requestParams.PageIndex = 1
	requestParams.PageSize = 100
	requestParams.ParentCIds = append(requestParams.ParentCIds, strconv.Itoa(pid))

	var jsonData []byte
	jsonData, err = json.Marshal(requestParams)
	if err != nil {
		return
	}

	var responseByte []byte
	err, responseByte = common2.RequestJushuitan(jsonData, "https://openapi.jushuitan.com/open/category/query")
	if err != nil {
		return
	}
	var response JushuitanCategoryResponse
	err = json.Unmarshal(responseByte, &response)
	if err != nil {
		return
	}
	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}
	return err, response.Data.Datas

}
func (*Jushuitan) RunConcurrent(wg *sync.WaitGroup, info request.GetCategorySearch, i int) (err error) {

	return
}

// 选品库增加商品
func (*Jushuitan) GoodsStorageAdd(ids []int, supplyId uint) (err error, info interface{}) {

	return

}

func (jst *Jushuitan) GoodsPriceAlert(GoodsData callback2.GoodsCallBack) (err error) {
	var shops []model.JushuitanShop
	err = source.DB().Find(&shops).Error
	if err != nil {
		return
	}
	var shopMap = make(map[string]model.JushuitanShop)
	var virtualStockShops []string
	for _, shop := range shops {
		shopMap[shop.ShopId] = shop
		if shop.IsVirtualStock == 1 {
			virtualStockShops = append(virtualStockShops, shop.ShopId)
		}
	}
	var allProducts []service2.ProductForUpdate
	var allJstProductsMap = make(map[string]model.JushuitanGoodsDetail)
	var idArr []string
	if len(GoodsData.Codes) > 0 {
		var codes []string
		for _, code := range GoodsData.Codes {
			if code != "" {
				codes = append(codes, code)
			}
		}
		err = source.DB().Preload("Skus").Where("code in ?", codes).Find(&allProducts).Error
		if err != nil {
			return
		}
		idArr = GoodsData.Codes
	} else {
		err = source.DB().Preload("Skus").Where("id in ?", GoodsData.Data.GoodsIds).Find(&allProducts).Error
		if err != nil {
			return
		}
		for _, productp := range allProducts {
			idArr = append(idArr, productp.Code)
		}
	}

	var list []model.JushuitanGoodsDetail
	err = source.DB().Preload("ItemSkuList").Where("`style_code` in ?", idArr).Where("deleted_at is null").Find(&list).Error
	if err != nil {
		return
	}
	for _, item := range list {
		allJstProductsMap[item.StyleCode] = item
	}

	var updateProducts []service2.ProductForUpdate
	for _, product := range allProducts {

		_, ok := allJstProductsMap[product.Code]
		if !ok {
			product.IsDisplay = 0
			for key, _ := range product.Skus {
				product.Skus[key].Stock = 0
			}
			updateProducts = append(updateProducts, product)
			continue
		}

		detail := allJstProductsMap[product.Code]
		//var status int
		//if detail.ItemStatus == "cantDistribution" {
		//	status = 1
		//} else {
		//	status = 0
		//}
		if product.MD5 == "" || product.MD5 != allJstProductsMap[product.Code].MD5 {
			product.MD5 = detail.MD5
			product.SourceGoodsID = detail.ID
			product.DetailImages = "<p>"
			for _, idi := range detail.ItemPhoto.ItemDetailImages {
				product.DetailImages += "<img src=\"" + idi + "\">"
			}
			product.DetailImages += "</p>"
			var gallery pmodel.Gallery
			for _, mil := range detail.ItemPhoto.MainImageList {
				gallery = append(gallery, pmodel.GalleryItem{
					Type: 1,
					Src:  mil,
				})
			}
			product.Gallery = gallery
			var pCostPrice, pPrice uint

			var skuList []service2.Sku
			var minProfitRate float64
			var totalStock int
			for sk, detailSku := range detail.ItemSkuList {
				var sku = service2.Sku{}
				for _, oldSku := range product.Skus {
					if oldSku.Sn == detailSku.ItemCode || oldSku.Title == detailSku.FirstSpecValueName+" + "+detailSku.SecondSpecValueName {
						sku = oldSku
					}
				}

				//canFixSku++
				var options pmodel.Options
				options = append(options, pmodel.Option{
					SpecName:     detail.FirstSpecName,
					SpecItemName: detailSku.FirstSpecValueName,
				})
				options = append(options, pmodel.Option{
					SpecName:     detail.SecondSpecName,
					SpecItemName: detailSku.SecondSpecValueName,
				})
				sku.Options = options
				//for _, skuSaleAttr := range detailSku.SaleAttribute {
				//	var option pmodel.Option
				//	option.SpecName = skuSaleAttr.AttributeName
				//	option.SpecItemName = skuSaleAttr.AttributeValue
				//	sku.Options = append(sku.Options, option)
				//
				//}
				var costPrice, salePrice, originPrice, activityPrice, guidePrice uint
				var intXSku uint64
				if JushuitanData.Pricing.SupplyAdvice == 1 {
					intXSku, err = strconv.ParseUint(JushuitanData.Pricing.SupplyAdviceGuide, 10, 32)
					originPrice = uint(detailSku.SupplyPrice*100) * uint(intXSku) / 100
				} else if JushuitanData.Pricing.SupplyAdvice == 2 {
					intXSku, err = strconv.ParseUint(JushuitanData.Pricing.SupplyAdviceAgreement, 10, 32)
					originPrice = uint(detailSku.SupplyPrice*100) * uint(intXSku) / 100
				} else {
					originPrice = uint(detailSku.SupplyPrice * 100)
				}
				guidePrice = sku.OriginPrice
				activityPrice = sku.OriginPrice
				var intXS uint64
				if JushuitanData.Pricing.SupplySales == 1 {
					intXS, err = strconv.ParseUint(JushuitanData.Pricing.SupplySalesGuide, 10, 32)
					salePrice = uint(detailSku.SupplyPrice*100) * uint(intXS) / 100
				} else if JushuitanData.Pricing.SupplySales == 2 {
					intXS, err = strconv.ParseUint(JushuitanData.Pricing.SupplySalesAgreement, 10, 32)
					salePrice = uint(detailSku.SupplyPrice*100) * uint(intXS) / 100
				} else {
					salePrice = uint(detailSku.SupplyPrice * 100)
				}
				costPrice = uint(detailSku.SupplyPrice * 100)
				if _, jok := shopMap[product.JushuitanDistributorCoId]; jok {
					product.FreightType = 1
					product.FreightTemplateID = shopMap[product.JushuitanDistributorCoId].FreightTemplateID
					if shopMap[product.JushuitanDistributorCoId].IsOpen == 1 {
						//走单独定价策略
						err, costPrice, salePrice, originPrice, activityPrice, guidePrice = GetShopPricingPrice(uint(detailSku.SupplyPrice*100), shopMap[product.JushuitanDistributorCoId])
					}
				}

				if sku.ID == 0 {
					sku.OriginPrice = originPrice
					sku.GuidePrice = guidePrice
					sku.ActivityPrice = activityPrice
					sku.Price = salePrice
					sku.CostPrice = costPrice
				} else {
					sku.Price = salePrice
					sku.CostPrice = costPrice

				}

				sku.Title = detailSku.ShortName
				if sku.Title == "" {
					sku.Title = detailSku.FirstSpecValueName + " + " + detailSku.SecondSpecValueName
				}
				sku.Weight = int(detailSku.Weight * 1000)
				if sku.Weight == 0 {
					sku.Weight = 300
				}
				sku.IsDisplay = 1

				sku.OriginalSkuID = 0
				sku.Sn = detailSku.ItemCode
				sku.Code = detailSku.SkuId
				sku.ImageUrl = detailSku.Pic
				if sku.GuidePrice > 0 {
					sku.ProfitRate = utils.ExecProfitRate(sku.GuidePrice, sku.Price)
				} else {
					sku.ProfitRate = 0
				}
				if sk == 0 {
					minProfitRate = sku.ProfitRate
				}
				if sku.ProfitRate <= minProfitRate {
					minProfitRate = sku.ProfitRate
				}

				if sk == 0 {
					pPrice = sku.Price
				}
				if sku.Price <= pPrice {
					pPrice = sku.Price
				}
				if sk == 0 {
					pCostPrice = sku.CostPrice
				}
				if sku.CostPrice <= pCostPrice {
					pCostPrice = sku.CostPrice
				}
				//todo 加上库存同步,等待云信本地缓存更新完成
				sku.Stock = detailSku.Stock
				if collection.Collect(virtualStockShops).Contains(product.JushuitanDistributorCoId) {
					//水滴新供应链虚拟库存,都上架
					if sku.Stock == 0 {
						sku.Stock = 9999
					}

				}

				totalStock += sku.Stock
				skuList = append(skuList, sku)

			}
			product.Skus = skuList
			if totalStock > 0 {
				if product.StatusLock == 0 {
					product.IsDisplay = 1
				}
			}
			product.Price = pPrice
			product.CostPrice = pCostPrice
			//product.GuidePrice = pGuidePrice
			//product.OriginPrice = pOriginPrice
			//product.ActivityPrice = pActivityPrice
			//if status == 0 {
			//	product.IsDisplay = status
			//}
			//风控
			//if JushuitanData.Management.ProductPriceStatus == 1 {
			//	if product.Price < product.CostPrice*(JushuitanData.Management.Products/100) {
			//		product.IsDisplay = 0
			//	}
			//} else if JushuitanData.Management.ProductPriceStatus == 2 {
			//	if (product.Price-product.CostPrice)/product.CostPrice < JushuitanData.Management.Profit/100 {
			//		product.IsDisplay = 0
			//	}
			//}
			product.ProfitRate = minProfitRate

			product.JushuitanUploadStatus = 0
			//err = source.Redis().LPush(context.Background(), "jushuitanUploadProduct", product.SourceGoodsID).Err()
			updateProducts = append(updateProducts, product)
		}

	}
	//updateGoods := szbao.ProductToGoods(needUpdateProducts, GatherSupplyID)
	//err, updateProducts = szbao.CommodityAssembly(updateGoods, 0, 0, 0, GatherSupplyID, 1)
	//if err != nil {
	//	return
	//}
	for _, updateProduct := range updateProducts {
		err = service2.UpdateProduct(updateProduct)
		if err != nil {
			log.Log().Info("jushuitan修改商品出错", zap.Any("data", updateProduct))
			continue
		}
	}
	return
}
