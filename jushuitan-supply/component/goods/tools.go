package goods

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/xingliuhua/leaf"
	"gorm.io/gorm"
	model2 "jushuitan-supply/model"
	pmodel "product/model"
	pMq "product/mq"
	"public-supply/model"
	"sort"
	"strconv"
	"yz-go/source"
)

func GetShopPricingPrice(price uint, jushuitanShop model2.JushuitanShop) (err error, costPrice, salePrice, originPrice, activityPrice, guidePrice uint) {

	var intX uint64

	var pricingData model2.PricingData
	err = json.Unmarshal([]byte(jushuitanShop.Strategy), &pricingData)
	if err != nil {
		return
	}

	intX, err = strconv.ParseUint(pricingData.Cost, 10, 32)
	costPrice = price * uint(intX) / 100

	intX, err = strconv.ParseUint(pricingData.Price, 10, 32)
	salePrice = price * uint(intX) / 100

	intX, err = strconv.ParseUint(pricingData.Guide, 10, 32)
	guidePrice = price * uint(intX) / 100

	intX, err = strconv.ParseUint(pricingData.Marketing, 10, 32)
	activityPrice = price * uint(intX) / 100

	intX, err = strconv.ParseUint(pricingData.Origin, 10, 32)
	originPrice = price * uint(intX) / 100

	return
}

func GetOrderNo() (id string) {

	var node *leaf.IdNode
	var err error
	err, node = leaf.NewNode(20)
	if err != nil {
		return
	}
	err, id = node.NextId()
	if err != nil {
		return
	}
	return
}

func GetIdArr(list []model.Goods) (arrIds []int) {
	for _, elem := range list {
		arrIds = append(arrIds, elem.ID)
	}
	return

}

func GetIdArrs(list []model.Goods) (arrIds []string) {
	for _, elem := range list {
		arrIds = append(arrIds, elem.SN)
	}
	return

}

func YzhGetIdArr(list []model.YzhGoodsDetail) (arrIds []int) {
	for _, elem := range list {
		arrIds = append(arrIds, elem.RESULTDATA.PRODUCTDATA.ProductId)
	}
	return

}
func splitArray(arr []model.Goods, num int64) [][]model.Goods {
	max := int64(len(arr))
	//判断数组大小是否小于等于指定分割大小的值，是则把原数组放入二维数组返回
	if max <= num {
		return [][]model.Goods{arr}
	}
	//获取应该数组分割为多少份
	var quantity int64
	if max%num == 0 {
		quantity = max / num
	} else {
		quantity = (max / num) + 1
	}

	fmt.Println("quantity数量：", quantity)
	//声明分割好的二维数组
	var segments = make([][]model.Goods, 0)
	//声明分割数组的截止下标
	var start, end, i int64
	for i = 1; i <= quantity; i++ {
		end = i * num
		if i != quantity {
			segments = append(segments, arr[start:end])
		} else {
			segments = append(segments, arr[start:])
		}
		start = i * num
	}
	return segments
}

// 分割数组，根据传入的数组和分割大小，将数组分割为大小等于指定大小的多个数组，如果不够分，则最后一个数组元素小于其他数组
func SplitArray(arr []int, num int64) [][]int {
	max := int64(len(arr))
	//判断数组大小是否小于等于指定分割大小的值，是则把原数组放入二维数组返回
	if max <= num {
		return [][]int{arr}
	}
	//获取应该数组分割为多少份
	var quantity int64
	if max%num == 0 {
		quantity = max / num
	} else {
		quantity = (max / num) + 1
	}
	//声明分割好的二维数组
	var segments = make([][]int, 0)
	//声明分割数组的截止下标
	var start, end, i int64
	for i = 1; i <= quantity; i++ {
		end = i * num
		if i != quantity {
			segments = append(segments, arr[start:end])
		} else {
			segments = append(segments, arr[start:])
		}
		start = i * num
	}
	return segments
}

func SetImportRepeat(batch string, quantity int) (err error) {
	if quantity <= 0 {
		return
	}
	err = source.DB().Model(model.SupplyGoodsImportRecord{}).Where("batch=?", batch).Update("repeat_quantity", gorm.Expr("repeat_quantity + ?", quantity)).Error
	return
}

func GetProductIds(list []*pmodel.Product) (resIds string) {
	var ids string
	for _, elem := range list {
		stringId := strconv.Itoa(int(elem.SourceGoodsID))
		ids = ids + stringId + ","
	}
	ids = ids[0 : len(ids)-1]
	resIds = ids
	return
}

func CreateGoods(goodsList []*pmodel.Product) (err error) {
	err = source.DB().CreateInBatches(&goodsList, 500).Error
	if err != nil {
		return
	}
	var SupplyGoods []model.SupplyGoods
	for _, goods := range goodsList {

		SupplyGoods = append(SupplyGoods, model.SupplyGoods{
			SupplyGoodsID:  goods.SourceGoodsID,
			Source:         goods.Source,
			ProductID:      goods.ID,
			GatherSupplyID: goods.GatherSupplyID,
		})

		err = pMq.PublishMessage(goods.ID, pMq.Create, 0)
		if err != nil {
			return
		}

		//err = source.Redis().LPush(context.Background(), "jushuitanUploadProduct", goods.ID).Err()
	}
	err = source.DB().CreateInBatches(SupplyGoods, 2000).Error
	return
}

func SetImportRecordCompletion(batch string) (err error) {
	err = source.DB().Model(model.SupplyGoodsImportRecord{}).Where("batch=?", batch).Update("completion_status", 1).Error
	return
}

type MapEntryHandler func(string, string)

// func printKeyValue(key string, value string) {
// 	fmt.Printf("key=%s, value=%s\n", key, value)
// }

// 按字母顺序遍历map
func TraverseMapInStringOrder(params map[string]string, handler MapEntryHandler) {
	keys := make([]string, 0)
	for k, _ := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	for _, k := range keys {
		handler(k, params[k])
	}
}

func Base64Encode(src []byte) []byte {
	return []byte(base64.StdEncoding.EncodeToString(src))
}
func Base64Decode(src string) ([]byte, error) {
	return base64.StdEncoding.DecodeString(src)
}
func AesEncryptCBC(origDataStr string, keystr string) string {
	origData := []byte(origDataStr)
	key := []byte(keystr)
	// 分组秘钥
	// NewCipher该函数限制了输入k的长度必须为16, 24或者32
	block, _ := aes.NewCipher(key)
	blockSize := block.BlockSize()                              // 获取秘钥块的长度
	origData = pkcs5Padding(origData, blockSize)                // 补全码
	blockMode := cipher.NewCBCEncrypter(block, key[:blockSize]) // 加密模式
	encrypted := make([]byte, len(origData))                    // 创建数组
	blockMode.CryptBlocks(encrypted, origData)                  // 加密
	return hex.EncodeToString(encrypted)
}
func AesDecryptCBC(ciphertext string, keystr string) (string, error) {
	encrypted, err := hex.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}

	key := []byte(keystr)
	block, _ := aes.NewCipher(key)                              // 分组秘钥
	blockSize := block.BlockSize()                              // 获取秘钥块的长度
	blockMode := cipher.NewCBCDecrypter(block, key[:blockSize]) // 加密模式
	decrypted := make([]byte, len(encrypted))                   // 创建数组
	blockMode.CryptBlocks(decrypted, encrypted)                 // 解密
	decrypted = pkcs5UnPadding(decrypted)                       // 去除补全码
	return string(decrypted), nil
}
func pkcs5Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}
func pkcs5UnPadding(origData []byte) []byte {
	length := len(origData)
	unpadding := int(origData[length-1])
	return origData[:(length - unpadding)]
}
