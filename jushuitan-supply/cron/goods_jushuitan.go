package cron

import (
	"fmt"
	"jushuitan-supply/component/goods"
	model2 "public-supply/model"
	"strconv"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func PushGoodsJushuitanHandle() {
	task := cron.Task{
		Key:  "goodsjushuitan",
		Name: "jushuitan供应链同步商品",
		Spec: "12 34 */4 * * *",
		//Spec: "0 */1 * * * *",
		Handle: func(task cron.Task) {
			GoodsJushuitanCron()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

var IsRun int

func GoodsJushuitanCron() {
	log.Log().Info("开始处理jushuitan商品自动同步")
	if IsRun == 1 {
		return
	}
	var gatherList []model2.GatherSupply
	err := source.DB().Where("`category_id` = ?", 14).Find(&gatherList).Error
	if err != nil || len(gatherList) == 0 {
		fmt.Println("未找到jushuitan供应链")
		return
	}
	IsRun = 1
	for _, v := range gatherList {
		goodsClass := goods.Jushuitan{}
		err = goodsClass.InitSetting(v.ID)
		if err != nil {
			return
		}
		err = goodsClass.InitGoods()
		if err != nil {
			log.Log().Info("id为" + strconv.Itoa(int(v.ID)) + "的聚水潭供应链商品初始化失败：" + err.Error())
			continue
		}

	}
	IsRun = 0
	return
}
