package listener

import (
	"gin-vue-admin/cmd/gva"
	"github.com/chenhg5/collection"
	"go.uber.org/zap"
	"jushuitan/model"
	"jushuitan/mq"
	"jushuitan/service"
	"yz-go/component/log"
	"yz-go/utils"
)

func PushCustomerNotifyHandles() {
	mq.PushHandles("notifyJushuitan", func(data mq.NotifyMessage) error {
		if collection.Collect(gva.GlobalAuth.ToolsPlugin).Contains(26) == false && utils.LocalEnv() != false {
			return nil
		}
		var err error
		switch data.Method {
		case model.JUSHUITAN_INVENTORY:
			err = service.JushuitanStockSync(data.Message)
			break
		case model.JUSHUITAN_LOGISTICS:
			err = service.JushuitanExpressSync(data.Message)
			break
		case model.JUSHUITAN_REFUND:
			err = service.JushuitanAfterSaleSync(data.Message)
			break
		}
		if err != nil {
			log.Log().Info("聚水潭回调方法错误", zap.Any("err", err.<PERSON>rror()))
		}
		return nil
	})
}
