package model

type UploadOrderParams struct {
	ShopId           int         `json:"shop_id"`
	SoId             string      `json:"so_id"`
	OrderDate        string      `json:"order_date"`
	ShopStatus       string      `json:"shop_status"`
	ShopBuyerId      interface{} `json:"shop_buyer_id"`
	ReceiverState    string      `json:"receiver_state"`
	ReceiverCity     string      `json:"receiver_city"`
	ReceiverDistrict string      `json:"receiver_district"`
	ReceiverAddress  string      `json:"receiver_address"`
	ReceiverName     string      `json:"receiver_name"`
	ReceiverPhone    string      `json:"receiver_phone"`
	ReceiverZip      string      `json:"receiver_zip,omitempty"`
	PayAmount        float64     `json:"pay_amount"`
	Freight          float64     `json:"freight"`
	Remark           string      `json:"remark,omitempty"`
	BuyerMessage     string      `json:"buyer_message,omitempty"`
	ShopModified     string      `json:"shop_modified"`
	//LId              string               `json:"l_id"`
	//LogisticsCompany string               `json:"logistics_company"`
	//Labels           string               `json:"labels"`
	//LcId             string               `json:"lc_id"`
	Items []JushuitanOrderItem `json:"items"`
	Pay   struct {
		OuterPayId    string      `json:"outer_pay_id"`
		PayDate       string      `json:"pay_date"`
		Payment       string      `json:"payment"`
		SellerAccount interface{} `json:"seller_account"`
		BuyerAccount  interface{} `json:"buyer_account"`
		Amount        float64     `json:"amount"`
	} `json:"pay"`
	//OrderExt struct {
	//	ExtDatas struct {
	//		DoorPlate   string `json:"door_plate"`
	//		CompanyName string `json:"company_name"`
	//	} `json:"ext_datas"`
	//} `json:"order_ext"`
	ShopName string `json:"-"`
}

type JushuitanOrderItem struct {
	SkuId     string  `json:"sku_id"`
	ShopSkuId string  `json:"shop_sku_id"`
	Amount    float64 `json:"amount"`
	BasePrice float64 `json:"base_price"`
	Qty       int     `json:"qty"`
	Name      string  `json:"name"`
	OuterOiId string  `json:"outer_oi_id"`
}
