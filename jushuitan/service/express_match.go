package service

import (
	"encoding/json"
	"errors"
	"jushuitan/common"
	"jushuitan/model"
	url2 "net/url"
	express2 "shipping/express"
	"yz-go/component/log"
	"yz-go/request"
	"yz-go/source"
	"yz-go/utils"
)

func GetJstExpressList(info request.PageInfo) (err error, data model.JushuitanExpressData) {

	var input = make(map[string]interface{})
	input["page_index"] = info.Page
	input["page_size"] = info.PageSize
	var inputJson []byte
	inputJson, err = json.Marshal(info)
	if err != nil {
		return
	}
	var inputParams = make(map[string]interface{})
	inputParams["biz"] = string(inputJson)
	err, inputParams = common.GetJushuitanSign(inputParams)
	if err != nil {
		return
	}

	reqData := url2.Values{}
	for key, value := range inputParams {
		reqData.Add(key, source.Strval(value))

	}

	var resData []byte
	err, resData = utils.PostForm("https://openapi.jushuitan.com/open/logisticscompany/query", reqData, nil)
	if err != nil {
		return
	}
	var response model.JushuitanExpressResponse
	err = json.Unmarshal(resData, &response)
	if err != nil {
		log.Log().Error("聚水潭请求出错" + err.Error())
		return
	}
	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	} else {
		data = response.Data
	}

	return
}

func GetExpressMatchInfoList(info request.PageInfo) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.ExpressMatch{})
	var applications []model.ExpressMatch
	// 如果有条件搜索 下方会自动创建搜索语句

	err = db.Count(&total).Error
	err = db.Order("created_at desc").Limit(limit).Offset(offset).Find(&applications).Error
	var expressMap = make(map[string]string)

	for _, ex := range express2.GetCompanyList() {
		expressMap[ex.Code] = ex.Name
	}
	for k, v := range applications {
		applications[k].SelfExpressName = expressMap[v.SelfExpressCode]
	}
	return err, applications, total
}

func SetExpressMatch(info model.ExpressMatch) (err error) {
	err = source.DB().Save(&info).Error
	return
}

func DeleteExpressMatch(application model.ExpressMatch) (err error) {
	err = source.DB().Delete(&application).Error
	return err
}
