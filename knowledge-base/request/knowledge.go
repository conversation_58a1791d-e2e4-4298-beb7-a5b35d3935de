package request

import (
	"knowledge-base/model"
	yzRequest "yz-go/request"
)

type KnowledgeBaseSearch struct {
	model.KnowledgeBase
	yzRequest.PageInfo
	AppID uint `json:"app_id"`
}

type KnowledgeBaseProductSearch struct {
	ID          uint   `json:"id" form:"id"`
	Title       string `json:"title"`
	Category1ID uint   `json:"category1_id" form:"category1_id"`
	Category2ID uint   `json:"category2_id" form:"category2_id"`
	Category3ID uint   `json:"category3_id" form:"category3_id"`

	yzRequest.PageInfo
}

type CreateKnowledgeBaseProduct struct {
	KnowledgeBaseID uint   `json:"collection_id"`
	ProductIDs      []uint `json:"product_ids"`
	//KnowledgeBaseProducts []model.KnowledgeBaseProduct `json:"collection_products" form:"collection_products"`
}

type ColumnStatus struct {
	ID     int `json:"id"`
	Status int `json:"status"`
}
