package goods

import (
	"bytes"
	catemodel "category/model"
	"encoding/json"
	"errors"
	"fmt"
	gatherSupplyRequest "gather-supply/request"
	common3 "kunsheng-supply/common"
	wmodel "kunsheng-supply/model"
	request2 "kunsheng-supply/request"
	"kunsheng-supply/response"
	log2 "log"
	"mime/multipart"
	pmodel "product/model"
	pservice "product/service"
	callback2 "public-supply/callback"
	"public-supply/common"
	common2 "public-supply/common"
	"public-supply/model"
	"public-supply/request"
	"public-supply/service"
	gsetting "public-supply/setting"
	"strconv"
	"strings"
	"sync"
	"time"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"

	"github.com/imroc/req/v3"

	"github.com/chenhg5/collection"
	"github.com/gogf/gf/frame/g"
	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type KunSheng struct {
	dat      *model.SupplySetting
	Setting  *model.SupplySetting
	SupplyID uint
	Http     string
}

func (y *KunSheng) ManuallyProductUpdate(productID uint) (err error) {
	//TODO implement me
	panic("implement me")
}

func (y *KunSheng) SynchronizeProductsToLocal() (err error) {
	log.Log().Info("开始同步坤盛供应链商品")

	// 清空表
	if err = source.DB().Exec("TRUNCATE TABLE kun_sheng_products").Error; err != nil {
		log.Log().Error("清空商品表失败", zap.Error(err))
		return err
	}

	pageIndex := 1
	pageSize := 100
	for {
		// 获取商品数据
		info := request.GetGoodsSearch{
			Page:  pageIndex,
			Limit: pageSize,
		}
		err, productResp, total, _ := y.GetProductsFromUpstream(info)
		if err != nil {
			log.Log().Error("获取商品数据失败", zap.Error(err))
			return err
		}

		// 保存商品数据
		for _, product := range productResp.Data.Record {

			// 使用FirstOrCreate避免重复数据
			result := source.DB().Where("commodity_bar_type_code = ? ", product.CommodityBarTypeCode).FirstOrCreate(&product)
			if result.Error != nil {
				log.Log().Error("保存商品数据失败", zap.Error(result.Error))
				continue
			}
		}

		// 判断是否还有下一页
		if int64(pageIndex*pageSize) >= total {
			break
		}
		pageIndex++
	}

	log.Log().Info("同步坤盛供应链商品完成")
	return nil
}

func (y *KunSheng) GetProductImages(itemNumbers string) (imagesResponse wmodel.ProductImagesResponse, err error) {
	// 构建请求参数
	reqParams := map[string]interface{}{
		"item_number": itemNumbers,
	}

	// 调用商品详情图片接口
	url := y.Http + "v2/product/images"

	// 添加签名
	times := time.Now().Unix()
	timestamp := strconv.FormatInt(times, 10)
	sign := common3.SignRequest(y.dat.BaseInfo.AppKey, y.dat.BaseInfo.AppSecret, times, reqParams)

	// 发送请求
	client := req.C()
	httpResponse, responseErr := client.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("sign", sign).
		SetHeader("key", y.dat.BaseInfo.AppKey).
		SetHeader("timestamp", timestamp).
		SetBody(reqParams).
		SetSuccessResult(&imagesResponse).
		Post(url)

	if responseErr != nil {
		return imagesResponse, fmt.Errorf("获取商品详情图片失败: %v", responseErr)
	}

	if httpResponse.IsErrorState() {
		return imagesResponse, fmt.Errorf("请求失败，状态码: %s", httpResponse.Status)
	}

	if imagesResponse.Code != 200 {
		return imagesResponse, errors.New(imagesResponse.Message)
	}

	return imagesResponse, nil
}

func (y *KunSheng) GetProductsFromUpstream(info request.GetGoodsSearch) (err error, productResponse wmodel.ProductResponse, total int64, ratio float64) {

	// 调用商品列表接口
	url := y.Http + "v2/product"
	// 构建请求参数
	reqParams := map[string]interface{}{
		"page_index": info.Page,
		"page_size":  info.Limit,
	}

	//if info.Articleno != "" {
	//	reqParams["item_number"] = info.Articleno
	//}
	if info.CommodityBarTypeCode != "" {
		reqParams["commodity_bar_type_code"] = info.CommodityBarTypeCode
	}
	if info.ItemNumber != "" {
		reqParams["item_number"] = info.ItemNumber
	}
	if info.BrandNames != "" {
		reqParams["brand"] = info.BrandNames
	}
	if info.CategoryStrID != "" {
		reqParams["cat_id"] = info.CategoryStrID
	}
	if info.SearchWords != "" {
		reqParams["goods_name"] = info.SearchWords
	}
	if info.MinMarketPrice != "" {
		reqParams["tag_price_start"] = info.MinMarketPrice
	}
	if info.MaxMarketPrice != "" {
		reqParams["tag_price_end"] = info.MaxMarketPrice
	}

	var resp wmodel.ProductResponse

	// 添加签名
	times := time.Now().Unix()

	timestamp := strconv.FormatInt(times, 10)

	sign := common3.SignRequest(y.dat.BaseInfo.AppKey, y.dat.BaseInfo.AppSecret, times, reqParams)
	client1 := req.C()
	// 发送请求
	responseData, err := client1.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("sign", sign).
		SetHeader("key", y.dat.BaseInfo.AppKey).
		SetHeader("timestamp", timestamp).
		SetBody(reqParams).
		SetSuccessResult(&resp).
		Post(url)

	if err != nil {
		return
	}

	if responseData.IsErrorState() {
		fmt.Println("1")
	}
	// 计算比率
	ratio = 0
	if resp.Data.Total > 0 {
		ratio = float64(len(resp.Data.Record)) / float64(resp.Data.Total) * 100
	}

	total = int64(resp.Data.Total)
	return nil, resp, total, ratio
}

func (y *KunSheng) GetGoods(info request.GetGoodsSearch) (err error, data interface{}, total int64, serverRatio int) {

	info.PageIndex = info.Page
	info.PageSize = info.Limit
	info.CatId = info.CategoryStrID
	info.Status = 0
	info.ItemNumber = info.Articleno
	info.Brand = info.BrandNames

	err, productsList, totalCount, _ := y.GetProductsFromUpstream(info)
	if err != nil {
		return
	}
	//db := source.DB().Model(&wmodel.KunShengProduct{})
	//
	//// 搜索条件
	//if info.SearchWords != "" {
	//	db = db.Where("`name` LIKE ? ", "%"+info.SearchWords+"%")
	//}
	//
	//if info.MinMarketPrice != "" && info.MaxMarketPrice != "" {
	//	db = db.Where("`tag_price` BETWEEN ? AND ?", info.MinMarketPrice, info.MaxMarketPrice)
	//}
	//
	//if info.Articleno != "" {
	//	db = db.Where("commodity_bar_type_code = ?", info.Articleno)
	//
	//}
	//
	//// 分类筛选
	//if info.CategoryStrID != "" {
	//	db = db.Where("cat_id = ?", info.CategoryStrID)
	//}
	//
	//// 品牌筛选
	//if info.BrandNames != "" {
	//	db = db.Where("brand = ?", info.BrandNames)
	//}
	//
	//// 价格区间筛选
	//if info.RangeType == "agreement_price" {
	//	db = db.Where("tag_price between ? and ?", info.RangeForm*100, info.RangeTo*100)
	//}
	//
	//// 导入状态筛选
	//if info.IsImport == 1 {
	//	db = db.Where("not EXISTS(select * from products where products.source_goods_id_string=kun_sheng_products.commodity_bar_type_code)")
	//} else if info.IsImport == 2 {
	//	db = db.Where("EXISTS(select * from products where products.source_goods_id_string=kun_sheng_products.commodity_bar_type_code)")
	//}
	//
	//// 排序
	//if info.Sort != "" {
	//	switch info.Type {
	//	case "created_time":
	//		db = db.Order("created_at " + info.Sort)
	//	case "agreement_price":
	//		db = db.Order("tag_price " + info.Sort)
	//	}
	//}
	//
	//// 分页
	//limit := info.Limit
	//offset := info.Limit * (info.Page - 1)
	//
	//// 查询总数
	var count int64
	count = totalCount
	//err = db.Count(&count).Error
	//if err != nil {
	//	return
	//}

	// 查询数据
	var products = productsList.Data.Record
	//err = db.Limit(limit).Offset(offset).Find(&products).Error
	//if err != nil {
	//	return
	//}

	// 组装返回数据
	var list []model.Goods
	for _, item := range products {
		var isImportItem uint
		var supplyGoods pmodel.Product
		itemErr := source.DB().Where("source_goods_id_string = ?", item.ItemNumber).Where("gather_supply_id = ?", info.GatherSupplyID).First(&supplyGoods).Error
		if itemErr == nil && supplyGoods.ID > 0 {
			isImportItem = 1
		}
		list = append(list, model.Goods{
			Title:             item.Name,
			MarketPrice:       uint(item.TagPrice * 100),
			AgreementPrice:    uint(item.TagPrice * 100),
			GuidePrice:        uint(item.TagPrice * 100),
			ActivityPrice:     uint(item.TagPrice * 100),
			ThirdBrandName:    item.Brand,
			Cover:             item.Image,
			ID:                int(item.ID),
			SN:                item.CommodityBarTypeCode,
			Unit:              item.Size,
			Color:             item.Color,
			ThirdCategoryName: item.CatName,
			IsImport:          isImportItem,
			//Stock:          uint(item.Stock),
		})
	}

	data = list
	total = count
	return
}

func (y *KunSheng) GetStock(request wmodel.StockRequest) (stockResponse wmodel.StockResponse, err error) {
	// 构建请求参数
	reqParams := map[string]interface{}{
		"page_index": request.PageIndex,
		"page_size":  request.PageSize,
	}

	// 添加可选的筛选条件
	if request.BarCode != "" {
		reqParams["bar_code"] = request.BarCode
	}
	if request.CommodityBarTypeCode != "" {
		reqParams["commodity_bar_type_code"] = request.CommodityBarTypeCode
	}
	if request.Size != "" {
		reqParams["size"] = request.Size
	}
	if request.ItemNumber != "" {
		reqParams["item_number"] = request.ItemNumber
	}
	if request.StyleCode != "" {
		reqParams["style_code"] = request.StyleCode
	}
	if request.ProductMarking != "" {
		reqParams["product_marking"] = request.ProductMarking
	}
	if request.Brand != "" {
		reqParams["brand"] = request.Brand
	}
	if request.StockUpdTime != "" {
		reqParams["stock_upd_time"] = request.StockUpdTime
	}

	// 调用库存查询接口
	url := y.Http + "v2/stock"

	// 添加签名
	times := time.Now().Unix()
	timestamp := strconv.FormatInt(times, 10)
	sign := common3.SignRequest(y.dat.BaseInfo.AppKey, y.dat.BaseInfo.AppSecret, times, reqParams)

	log.Log().Info("rerquestparam", zap.Any("reqParams", reqParams), zap.Any("sign", sign), zap.Any("timestamp", timestamp))
	// 发送请求
	client := req.C()
	httpResponse, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("sign", sign).
		SetHeader("key", y.dat.BaseInfo.AppKey).
		SetHeader("timestamp", timestamp).
		SetBody(reqParams).
		SetSuccessResult(&stockResponse).
		Post(url)

	if err != nil {
		return stockResponse, fmt.Errorf("库存查询失败: %v", err)
	}

	if httpResponse.IsErrorState() {
		return stockResponse, fmt.Errorf("请求失败，状态码: %s", httpResponse.Status)
	}

	if stockResponse.Code != 200 {
		return stockResponse, errors.New(stockResponse.Message)
	}

	return stockResponse, nil

}

func (y *KunSheng) ImportSelectGoodsRun(info model.SelectGoods) (err error, list interface{}) {
	orderPN := GetOrderNo()
	common.GlobalOrderSN = orderPN
	goodsRecord := model.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: len(info.List),
		Status:            1,
	}
	var result []string
	var field string
	field = "source_goods_id"
	if info.Key == "KunSheng" {
		field = "md5"
		err = source.DB().Model(pmodel.Product{}).Where("gather_supply_id=?", info.GatherSupplyID).Pluck(field, &result).Error
		if err != nil {
			log2.Println("查询供应链商品id错误", err)
			return
		}
	}
	if len(info.List) <= 0 {
		log2.Println("导入空数据")
		err = errors.New("导入的是空数据")
		return
	}
	var idsArr []string
	idsArr = GetSnArr(info.List)
	difference := collection.Collect(idsArr).Diff(result).All()
	repeat := len(idsArr) - len(difference)
	goodsRecord.RepeatQuantity = repeat //重复数据
	goodsRecord.Source = strconv.Itoa(common2.KUNSHENG_SOURCE)

	if len(difference) <= 0 {
		goodsRecord.Status = 2
		goodsRecord.CompletionStatus = 1
		source.DB().Omit("goods_arr").Create(&goodsRecord) //当前没有商品可以导入
		return
	}
	source.DB().Omit("goods_arr").Create(&goodsRecord)

	var goodsList []model.Goods
	for _, v := range difference {
		for _, item := range info.List {
			md5 := utils.MD5V([]byte(item.ShopName + item.SN))
			if md5 == v {
				goodsList = append(goodsList, item)
			}
		}

	}
	info.List = goodsList
	arrList := splitArray(info.List, 20)
	for _, item := range arrList {
		wg.Add(1)
		go y.RunSelectGoodsConcurrent(orderPN, item, info.Categorys, info.Key, info.GatherSupplyID)
	}
	wg.Wait()
	err = SetImportRecordCompletion(orderPN)
	fmt.Println("全部完成：")
	return
}

func (y *KunSheng) RunSelectGoodsConcurrent(orderPN string, list []model.Goods, category string, key string, gatherSupplyID uint) (err error) {

	defer wg.Done()

	cateList := strings.Split(category, ",")
	var cateId1, cateId2, cateId3 int
	cateId1, err = strconv.Atoi(cateList[0])
	cateId2, err = strconv.Atoi(cateList[1])
	cateId3, err = strconv.Atoi(cateList[2])

	var listGoods []*pmodel.Product

	var recordError []model.SupplyGoodsImportRecordErrors
	err, listGoods, recordError = y.CommodityAssembly(list, cateId1, cateId2, cateId3, gatherSupplyID, 0)
	if err != nil {
		return
	}
	if len(listGoods) > 0 {
		err = service.FinalProcessing(listGoods, orderPN)
		if err != nil {
			return
		}

	}
	if len(recordError) > 0 {
		err = service.FinalProcessingError(recordError, orderPN)
		if err != nil {
			return
		}
	}
	return

}
func (y *KunSheng) ValidateConfig(params gatherSupplyRequest.ValidateConfigParams) (err error) {

	return
}

func (y *KunSheng) DeleteGoods(id uint) (err error) {
	//TODO implement me
	return
}

func (y *KunSheng) GetToken() (err error) {

	return
}

func (y *KunSheng) GetSupplyBalance(GatherSupplyID uint) (err error, balance interface{}) {

	url := y.Http + "openapi/queryAccount.do"
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("sign", y.dat.BaseInfo.AppSecret)
	_ = writer.WriteField("username", y.dat.BaseInfo.UserName)
	_ = writer.WriteField("password", y.dat.BaseInfo.PassWord)
	err = writer.Close()
	if err != nil {
		return
	}
	//reqData, _ := json.Marshal(&reqValue)
	resData, _ := utils.PostFormData(url, payload, writer.FormDataContentType())
	//balance = string(resData)
	//字符串转浮点数，并且*100之后变为整数
	balance1, _ := strconv.ParseFloat(string(resData), 64)
	balance = balance1 * 100
	if err != nil {
		return
	}

	return
}

func (y *KunSheng) GoodsPriceAlert(GoodsData callback2.GoodsCallBack) (err error) {
	return
}

//func (y *KunSheng) CommodityAssembly(list []model.Goods, cateId1, cateId2, cateId3 int, gatherSupplyID uint, isCreate int) (err error, listGoods []*pmodel.Product) {
//	//panic("implement me")
//}

func (y *KunSheng) InitGoods() (err error) {
	return
}

func (y *KunSheng) CommodityAssembly(list []model.Goods, cateId1, cateId2, cateId3 int, gatherSupplyID uint, isCreate int) (err error, listGoods []*pmodel.Product, recordErrors []model.SupplyGoodsImportRecordErrors) {

	log.Log().Info("kunsheng CommodityAssembly 导入数据", zap.Any("", len(list)))
	if len(list) == 0 {
		log.Log().Error("选择可导商品数量为空", zap.Any("err", err))
		return
	}

	for _, item := range list {
		// 使用GetProductsFromUpstream获取商品信息
		info := request.GetGoodsSearch{
			Page:  1,
			Limit: 1,
		}
		info.CommodityBarTypeCode = item.SN
		getProductsErr, productResp, _, _ := y.GetProductsFromUpstream(info)
		if getProductsErr != nil || len(productResp.Data.Record) == 0 || productResp.Code != 200 {
			recordErrors = append(recordErrors, model.SupplyGoodsImportRecordErrors{
				SourceStringId: item.SN,
				Error:          "商品不存在",
				Title:          item.Title,
			})
			continue
		}

		// 将商品数据转换为InventoryListByGroup结构
		product := productResp.Data.Record[0]

		var goodsDetail []*pmodel.Product
		err, goodsDetail = y.CommodityAssemblyLocal(product, cateId1, cateId2, cateId3, item.ExpressDelivery)
		if err != nil {
			return
		}
		listGoods = append(listGoods, goodsDetail...)
	}

	return
}

func (y *KunSheng) GoodsStorageAdd(ids []int, supplyId uint) (err error, info interface{}) {
	//panic("implement me")

	return
}

func (y *KunSheng) InitSetting(gatherSupplyID uint) (err error) {

	y.Http = "https://uat.ksjx365.com/ksjxopen/api/"
	//y.dat.BaseInfo.AppKey = "a4a3f9cb259448d5aebb774cb304951b"
	//y.dat.BaseInfo.AppSecret = "iaiVW5WdXOq8_ha2NgNwJwCxV7srur03YOFIi6rL1Yk"

	y.SupplyID = gatherSupplyID
	var setting model2.SysSetting
	err, setting = gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}
	err = json.Unmarshal([]byte(setting.Value), &y.dat)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	//y.Setting = y.dat
	//if y.dat.BaseInfo.UserName == "" && y.dat.BaseInfo.AppSecret == "" {
	//	err = errors.New("请先配置供应链key")
	//	return
	//}
	//
	//y.Http = y.dat.BaseInfo.ApiUrl + "/api/"
	//y.dat.BaseInfo.AppSecret = y.dat.BaseInfo.AppSecret
	//y.dat.BaseInfo.UserName = y.dat.BaseInfo.UserName
	//y.dat.BaseInfo.PassWord = y.dat.BaseInfo.PassWord
	////
	////y.GetToken()
	//
	//if y.Http == "" {
	//	y.Http = "http://253.open.test.tmyd.KunShenggroup.com/api/"
	//}

	//y.dat.BaseInfo.Channel = "GOUSHIHUI"
	//y.dat.BaseInfo.AppSecret = "a7d1f3d8e1f541b7bde9736e86b4305b"

	return
}

func (y *KunSheng) GetBrandInfo() (brands interface{}, err error) {
	url := y.Http + "v2/brand/info"

	// 构建请求参数
	reqParams := map[string]interface{}{}

	// 添加签名
	times := time.Now().Unix()
	timestamp := strconv.FormatInt(times, 10)
	sign := common3.SignRequest(y.dat.BaseInfo.AppKey, y.dat.BaseInfo.AppSecret, times, reqParams)

	// 发送请求
	client := req.C()
	var resp wmodel.BrandResponse

	responseData, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("sign", sign).
		SetHeader("key", y.dat.BaseInfo.AppKey).
		SetHeader("timestamp", timestamp).
		SetBody(reqParams).
		SetSuccessResult(&resp).
		Post(url)

	if err != nil {
		return
	}
	if responseData.IsErrorState() {
		err = errors.New("request failed with status: " + responseData.Status)
		return
	}

	if resp.Code != 200 {
		err = errors.New(resp.Message)
		return
	}

	brands = resp.Data
	return
}

func (y *KunSheng) GetCategories(catName, catId string) (categories interface{}, err error) {
	url := y.Http + "v2/categories"

	// 构建请求参数
	reqParams := map[string]interface{}{}
	if catName != "" {
		reqParams["cat_name"] = catName
	}
	if catId != "" {
		reqParams["cat_id"] = catId
	}

	// 添加签名
	times := time.Now().Unix()
	timestamp := strconv.FormatInt(times, 10)
	sign := common3.SignRequest(y.dat.BaseInfo.AppKey, y.dat.BaseInfo.AppSecret, times, reqParams)

	// 发送请求
	client := req.C()
	var resp wmodel.CategoryResponse

	responseData, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("sign", sign).
		SetHeader("key", y.dat.BaseInfo.AppKey).
		SetHeader("timestamp", timestamp).
		SetBody(reqParams).
		SetSuccessResult(&resp).
		Post(url)

	if err != nil {
		return
	}
	if responseData.IsErrorState() {
		err = errors.New("request failed with status: " + responseData.Status)
		return
	}

	if resp.Code != 200 {
		err = errors.New(resp.Message)
		return
	}

	categories = resp.Data
	return
}

func (y *KunSheng) GetWareHouseNameInfo(wareHouse request2.WareHouse) (WareHouseName wmodel.WareHouseName, err error) {
	url := y.Http + "/openapi/getWareHouseNameInfo.do"
	payload := &bytes.Buffer{}
	var resData []byte

	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("sign", y.dat.BaseInfo.AppSecret)
	_ = writer.WriteField("page", wareHouse.Page)
	_ = writer.WriteField("rows", wareHouse.Rows)
	err = writer.Close()
	if err != nil {
		return
	}
	resData, err = utils.PostFormData(url, payload, writer.FormDataContentType())
	if err != nil {
		return
	}
	err = json.Unmarshal(resData, &WareHouseName)

	if err != nil {
		return
	}
	return
}

func (y *KunSheng) GetGoodsDetail(reqData wmodel.Article) (goodsDetail wmodel.GoodsDetail, err error) {
	url := y.Http + "/openapi/queryProductInfo.do"
	var resData []byte
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("sign", y.dat.BaseInfo.AppSecret)
	_ = writer.WriteField("goods_no", reqData.ArticleNO)
	err = writer.Close()
	if err != nil {
		return
	}
	resData, err = utils.PostFormData(url, payload, writer.FormDataContentType())
	if err != nil {
		return
	}
	err = json.Unmarshal(resData, &goodsDetail)

	if err != nil {
		return
	}
	return
}

func (y *KunSheng) GetDeliveryInfo(reqData request2.WareHouse) (deliveryInfo wmodel.DeliveryInfo, err error) {
	url := y.Http + "/openapi/getDeliveryInfo.do"
	var resData []byte
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("sign", y.dat.BaseInfo.AppSecret)
	_ = writer.WriteField("wareHouseName", reqData.WareHouseName)
	err = writer.Close()
	if err != nil {
		return
	}
	resData, err = utils.PostFormData(url, payload, writer.FormDataContentType())
	if err != nil {
		return
	}
	err = json.Unmarshal(resData, &deliveryInfo)

	if err != nil {
		return
	}
	return
}

func (y *KunSheng) GetDeliveryTimeList(reqData wmodel.DeliveryTimeData) (deliveryTimeList wmodel.DeliveryTimeList, err error) {
	url := y.Http + "/openapi/getDeliveryTimeList.do"
	var resData []byte
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("sign", y.dat.BaseInfo.AppSecret)
	_ = writer.WriteField("page", reqData.Page)
	_ = writer.WriteField("rows", reqData.Rows)
	err = writer.Close()
	if err != nil {
		return
	}
	resData, err = utils.PostFormData(url, payload, writer.FormDataContentType())
	if err != nil {
		return
	}
	err = json.Unmarshal(resData, &deliveryTimeList)

	if err != nil {
		return
	}
	return
}

func (y *KunSheng) GetCategoryInfo(catId string) (categoryInfo wmodel.Category, err error) {
	url := y.Http + "v2/categories"

	// 构建请求参数
	reqParams := map[string]interface{}{
		"cat_id":    catId,
		"is_subset": 0,
	}

	// 添加签名
	times := time.Now().Unix()
	timestamp := strconv.FormatInt(times, 10)
	sign := common3.SignRequest(y.dat.BaseInfo.AppKey, y.dat.BaseInfo.AppSecret, times, reqParams)

	// 发送请求
	client := req.C()
	var resp wmodel.CategoryResponse

	responseData, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("sign", sign).
		SetHeader("key", y.dat.BaseInfo.AppKey).
		SetHeader("timestamp", timestamp).
		SetBody(reqParams).
		SetSuccessResult(&resp).
		Post(url)

	if err != nil {
		return
	}

	if responseData.IsErrorState() {
		err = errors.New("request failed with status: " + responseData.Status)
		return
	}

	if resp.Code != 200 {
		err = errors.New(resp.Message)
		return
	}

	// 遍历找到对应的分类信息
	for _, categoryItem := range resp.Data {
		categoryItemID := strconv.Itoa(categoryItem.CatId)
		if categoryItemID == catId {
			categoryInfo = categoryItem
			break
		}
	}

	return
}

func (y *KunSheng) GetInventoryListByGroup(reqData request2.InventoryListByGroup) (inventoryListByGroupGoods response.InventoryListByGroupGoods, err error) {
	url := y.Http + "/openapi/getInventoryListByGroup.do"
	var resData []byte
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("sign", y.dat.BaseInfo.AppSecret)
	if reqData.Page != "" {
		_ = writer.WriteField("page", reqData.Page)
	}
	if reqData.Rows != "" {
		_ = writer.WriteField("rows", reqData.Rows)
	}
	if reqData.BrandNames != "" {
		_ = writer.WriteField("brand_names", reqData.BrandNames)
	}
	if reqData.Sizes != "" {
		_ = writer.WriteField("sizes", reqData.Sizes)
	}
	if reqData.Sexs != "" {
		_ = writer.WriteField("sexs", reqData.Sexs)
	}
	if reqData.Divisions != "" {
		_ = writer.WriteField("divisions", reqData.Divisions)
	}
	if reqData.Quarters != "" {
		_ = writer.WriteField("quarters", reqData.Quarters)
	}
	if reqData.Articleno != "" {
		_ = writer.WriteField("articleno", reqData.Articleno)
	}
	if reqData.MaxMarketPrice > 0 && reqData.MinMarketPrice > 0 {
		MaxMarketPrice := strconv.FormatFloat(float64(reqData.MaxMarketPrice), 'f', -1, 32)
		MinMarketPrice := strconv.FormatFloat(float64(reqData.MinMarketPrice), 'f', -1, 32)
		_ = writer.WriteField("maxMarketPrice", MaxMarketPrice)
		_ = writer.WriteField("minMarketPrice", MinMarketPrice)
	}
	if reqData.MaxDiscount > 0 && reqData.MinDiscount > 0 {
		MaxDiscount := strconv.FormatFloat(float64(reqData.MaxDiscount), 'f', -1, 32)
		MinDiscount := strconv.FormatFloat(float64(reqData.MinDiscount), 'f', -1, 32)
		_ = writer.WriteField("maxDiscount", MaxDiscount)
		_ = writer.WriteField("minDiscount", MinDiscount)
	}
	if reqData.WareHouseName != "" {
		_ = writer.WriteField("wareHouseNames", reqData.WareHouseName)
	}
	err = writer.Close()
	if err != nil {
		return
	}
	formDataf := writer.FormDataContentType()
	if err != nil {
		panic(err)
	}

	data := map[string]interface{}{
		"content-type": formDataf,
		// 这里还可以根据需求添加更多字段
	}

	resultJson, err := json.MarshalIndent(data, "", "\t")
	if err != nil {
		panic(err)
	}
	fmt.Println("请求数据：", string(resultJson))

	// 打印转换后的JSON数据
	resData, err = utils.PostFormData(url, payload, writer.FormDataContentType())
	if err != nil {
		return
	}
	err = json.Unmarshal(resData, &inventoryListByGroupGoods)

	if err != nil {
		return
	}
	return
}

func (y *KunSheng) GetStockListByGoodsNo(reqData request2.StockListByGoodsNo) (StockListByGoodsNoGoods response.GetStockListByGoodsNoGoods, err error) {
	url := y.Http + "/openapi/getStockListByGoodsNo.do"
	var resData []byte
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("sign", y.dat.BaseInfo.AppSecret)

	if reqData.Page != "" {
		_ = writer.WriteField("page", reqData.Page)
	}
	if reqData.Rows != "" {
		_ = writer.WriteField("rows", "300")
	}
	if reqData.Articleno != "" {
		_ = writer.WriteField("articleno", reqData.Articleno)
	}

	err = writer.Close()
	if err != nil {
		return
	}
	resData, err = utils.PostFormData(url, payload, writer.FormDataContentType())
	log.Log().Info("info", zap.Any("info", string(resData)))
	if err != nil {
		return
	}
	err = json.Unmarshal(resData, &StockListByGoodsNoGoods)

	if err != nil {
		return
	}

	var list []response.GetStockListByGoodsNoGood
	for _, item := range StockListByGoodsNoGoods.Rows {

		InnerNum, _ := strconv.Atoi(item.InnerNum)
		if item.WareHouseName == reqData.WareHouseName && InnerNum > 0 {
			list = append(list, item)
		}

	}

	StockListByGoodsNoGoods.Rows = list
	return
}
func (y *KunSheng) GetWarehouseDetail(reqData request2.WareHouse) (StockListByGoodsNoGoods response.GetWarehouseDetail, err error) {
	url := y.Http + "/openapi/warehouseDetail.do"
	var resData []byte
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("sign", y.dat.BaseInfo.AppSecret)

	if reqData.Page != "" {
		_ = writer.WriteField("page", reqData.Page)
	}
	if reqData.Rows != "" {
		_ = writer.WriteField("rows", reqData.Rows)
	}
	if reqData.WareHouseName != "" {
		_ = writer.WriteField("wareHouseName", reqData.WareHouseName)
	}

	err = writer.Close()
	if err != nil {
		return
	}
	resData, err = utils.PostFormData(url, payload, writer.FormDataContentType())
	log.Log().Info("获取天马仓库", zap.Any("info", resData))
	if err != nil {
		return
	}
	err = json.Unmarshal(resData, &StockListByGoodsNoGoods)

	if err != nil {
		err = errors.New("warehouseDetail接口：" + string(resData))
		log.Log().Error("天马返回数据", zap.Any("resData", string(resData)))
		return
	}
	if StockListByGoodsNoGoods.ErrorCode == "2" {
		err = errors.New(StockListByGoodsNoGoods.ErrorInfo)
		return
	}
	return
}

func (y *KunSheng) GetCommodityList(reqData request2.StockListByGoodsNo) (commodityGoods response.CommodityGoods, err error) {
	url := y.Http + "/openapi/getCommodityList.do"
	var resData []byte
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("sign", y.dat.BaseInfo.AppSecret)

	if reqData.Page != "" {
		_ = writer.WriteField("page", reqData.Page)
	}
	if reqData.Rows != "" {
		_ = writer.WriteField("rows", reqData.Rows)
	}
	if reqData.Articleno != "" {
		_ = writer.WriteField("articleno", reqData.Articleno)
	}

	err = writer.Close()
	if err != nil {
		return
	}
	resData, err = utils.PostFormData(url, payload, writer.FormDataContentType())
	if err != nil {
		return
	}
	err = json.Unmarshal(resData, &commodityGoods)

	if err != nil {
		return
	}
	return
}

func (y *KunSheng) RunUpdateGoodsRun(page interface{}) (err error) {
	//defer syncwg.Done()
	//url := string(y.Http + "open/api/goods/queryGoodsList")
	//var resData []byte
	//headerData := make(map[string]interface{})
	//
	//headerData["accessToken"] = y.dat.BaseInfo.Token
	//headerData["pageNum"] = page
	//
	//reqData, _ := json.Marshal(headerData)
	//
	//resData = utils.HttpPostJson(reqData, url)
	//if err != nil {
	//	return err
	//}
	////jsonData := string(resData)
	////fmt.Println("返回数据：", jsonData)
	//var modelData model.GoodsList
	//err = json.Unmarshal(resData, &modelData)
	//if err != nil {
	//	return err
	//}
	//
	//var lists []int64
	//
	//for _, item := range modelData.Result.GoodsSkuList {
	//	skuCode, _ := strconv.Atoi(item.GoodsSkuCode)
	//	lists = append(lists, int64(skuCode))
	//}
	//fmt.Println("pageDD:", page, len(lists))
	//
	//if len(lists) == 0 {
	//	return
	//}
	//
	//y.SynergeticProcess(lists)

	return
}

func (y *KunSheng) UpdateGoodsRun() (err error) {
	//source.DB().Unscoped().Delete(model.YzhProductTemp{}, "id >=1")
	//
	//url := string(y.Http + "open/api/goods/queryGoodsList")
	//var resData []byte
	//headerData := make(map[string]string)
	//
	//headerData["accessToken"] = y.dat.BaseInfo.Token
	//
	//reqData, _ := json.Marshal(headerData)
	//
	//resData = utils.HttpPostJson(reqData, url)
	//if err != nil {
	//	return err
	//}
	//
	//var modelData model.GoodsList
	//err = json.Unmarshal(resData, &modelData)
	//if err != nil {
	//	return err
	//}
	//
	//var count = modelData.Result.TotalCount
	//orderPN := utils.GetOrderNo()
	//goodsRecord := model.SupplyGoodsImportRecord{
	//	Batch:             orderPN,
	//	EstimatedQuantity: count,
	//	Status:            1,
	//	SearchCriteria:    "all",
	//}
	//
	//source.DB().Omit("goods_arr").Create(&goodsRecord)
	//
	//for i := 1; i <= modelData.Result.TotalPage; i++ {
	//	//syncwg.Add(1)
	//	y.RunUpdateGoodsRun(i)
	//}
	//
	////syncwg.Wait()
	//
	////source.DB().CreateInBatches(&yzhProduct, 1000)
	//
	////fmt.Println("全部结束")
	////return
	//
	//err = SetImportRecordCompletion(orderPN)
	//if err != nil {
	//	fmt.Println("变更导入记录状态错误", err)
	//}
	//fmt.Println("导入供应链商品全部完成")
	//time.Sleep(time.Second * 5)
	////source.DB().Exec("DROP TABLE IF EXISTS `yzh_product_goods`;")
	////source.DB().Exec("CREATE TABLE yzh_product_goods  select * from yzh_product_temps  ;")
	//log.Log().Info("批量同步yzh商品到本地全部完成！！！")
	return

}

func (y *KunSheng) ImportGoodsRun(info request.GetGoodsSearch) (err error, data interface{}) {

	info.PageIndex = info.Page
	info.Limit = 300
	info.CatId = info.CategoryStrID
	info.Status = 0
	info.ItemNumber = info.Articleno
	info.Brand = info.BrandNames

	productsErr, products, count, _ := y.GetProductsFromUpstream(info)
	if productsErr != nil && errors.Is(err, gorm.ErrRecordNotFound) {

		err = errors.New("商品查询错误,请检查配置项")
		return
	}

	orderPN := utils.GetOrderNo()
	GlobalOrderSN = orderPN
	goodsRecord := model.SupplyGoodsImportRecord{
		Batch:             orderPN,
		EstimatedQuantity: int(count),
		Status:            1,
		SearchCriteria:    "all",
		Source:            strconv.Itoa(common2.KUNSHENG_SOURCE),
	}

	source.DB().Omit("goods_arr").Create(&goodsRecord)
	var wg sync.WaitGroup
	for _, item := range products.Data.Record {
		wg.Add(1)
		y.ImportLocalGoods(&wg, item, orderPN, info.Categorys)
	}

	wg.Wait()
	err = SetImportRecordCompletion(orderPN)
	if err != nil {
		fmt.Println("变更导入记录状态错误", err)

	}
	fmt.Println("导入供应链商品全部完成")
	return

}

var limit = "50"
var pageSize = 50

func (y *KunSheng) RunImport(info request.GetGoodsSearch, orderPN string) (err error) {
	//var list []model.Goods

	//url := string(y.Http + "mcang/Mcang/getGoodsList")
	//var resData []byte
	//headerData := make(map[string]string)
	//reqValue := url2.Values{}
	//page := strconv.Itoa(info.Page)
	//headerData["pageNo"] = page
	//
	//headerData["pageSize"] = limit
	//if info.Category1ID > 0 {
	//	Category1ID := strconv.Itoa(info.Category1ID)
	//	headerData["classId"] = Category1ID
	//	reqValue.Add("classId", Category1ID)
	//}
	//if info.Category2ID > 0 {
	//	Category2ID := strconv.Itoa(info.Category2ID)
	//
	//	headerData["class2Id"] = Category2ID
	//	reqValue.Add("class2Id", Category2ID)
	//}
	//if info.Category3ID > 0 {
	//	Category3ID := strconv.Itoa(info.Category3ID)
	//
	//	headerData["class3Id"] = Category3ID
	//	reqValue.Add("class3Id", Category3ID)
	//}
	//if info.SearchWords != "" {
	//	headerData["goodName"] = info.SearchWords
	//	reqValue.Add("goodName", info.SearchWords)
	//}
	//if info.IsSort == "desc" {
	//	headerData["is_sort"] = "1"
	//	reqValue.Add("is_sort", "1")
	//}
	//if info.RangeType != "" {
	//	headerData["profitSpace"] = info.RangeType
	//	reqValue.Add("profitSpace", info.RangeType)
	//}
	//reqValue.Add("pageNo", page)
	//reqValue.Add("pageSize", limit)
	//
	////reqData, _ := json.Marshal(headerData)
	//var header = make(map[string]string)
	//
	//header["channelType"] = y.dat.BaseInfo.Channel
	//key := header["channelType"] + y.dat.BaseInfo.AppSecret
	//header["md5"] = strings.ToLower(Sign(headerData, key))
	//err, resData = utils.PostForm(url, reqValue, header)
	//if err != nil {
	//	return
	//}

	//var resGoodsData wmodel.ResData
	//err = json.Unmarshal(resData, &resGoodsData)
	//if resGoodsData.Data.List == nil {
	//	err = errors.New("未查询到相关条件商品")
	//	return
	//}
	//if resGoodsData.Data.DataCount == 0 {
	//	return
	//}
	//
	//var wg sync.WaitGroup
	//for _, item := range resGoodsData.Data.List {
	//	wg.Add(1)
	//	y.ImportLocalGoods(&wg, item, orderPN, info.Categorys)
	//}

	return

}

func (y *KunSheng) ImportLocalGoods(wg *sync.WaitGroup, detail wmodel.KunShengProduct, orderPN string, caters string) (recordErrors []model.SupplyGoodsImportRecordErrors) {
	defer wg.Done()
	ids := strings.Split(caters, ",")
	var cate1, cate2, cate3 int
	if len(ids) >= 3 {
		cate1, _ = strconv.Atoi(ids[0])
		cate2, _ = strconv.Atoi(ids[1])
		cate3, _ = strconv.Atoi(ids[2])
	}
	//var detail model.YzhGoodsDetail
	//detail.RESULTDATA.PRODUCTDATA = yzhList
	//detail.CategoryNames = yzhList.CateNames

	// 使用GetProductsFromUpstream获取商品信息
	info := request.GetGoodsSearch{
		Page:      1,
		Limit:     1,
		Articleno: detail.CommodityBarTypeCode,
	}
	getProductsErr, productResp, _, _ := y.GetProductsFromUpstream(info)
	if getProductsErr != nil || len(productResp.Data.Record) == 0 || productResp.Code != 200 {
		recordErrors = append(recordErrors, model.SupplyGoodsImportRecordErrors{
			SourceStringId: detail.CommodityBarTypeCode,
			Error:          "商品不存在",
			Title:          detail.Name,
		})
		return
	}

	err, listGoods := y.CommodityAssemblyLocal(detail, cate1, cate2, cate3, "")
	if err != nil {
		log.Log().Error("CommodityAssemblyLocal错误", zap.Any("err", err))
	}

	if len(listGoods) > 0 {
		err = service.FinalProcessing(listGoods, orderPN)
		if err != nil {
			return
		}

	}
	if len(recordErrors) > 0 {
		err = service.FinalProcessingError(recordErrors, orderPN)
		if err != nil {
			return
		}
	}
	return
}

func (y *KunSheng) SynergeticProcess(item []int64) (err error) {
	//url := string(y.Http + "/open/api/goods/queryGoodsDetail")
	//var resData []byte
	//headerData := make(map[string]interface{})
	//
	//headerData["accessToken"] = y.dat.BaseInfo.Token
	//headerData["goodsSkuCode"] = item
	//
	//reqData, _ := json.Marshal(headerData)
	//
	//resData = utils.HttpPostJson(reqData, url)
	//if err != nil {
	//	return err
	//}
	////var imgList = map[string][]model3.ImageList{}
	//imgList := y.QueryGoodsImageList(item)
	//
	////fmt.Println(imgList)
	//var yzhTempGoods model.YzhGoods
	//
	//err = json.Unmarshal(resData, &yzhTempGoods)
	//if err != nil {
	//	panic(err)
	//	return
	//}
	//var yzhProduct []model.YzhProductTemp
	//
	////yzhProduct = yzhTempGoods.Result
	//
	//for _, goodsItem := range yzhTempGoods.Result {
	//	var galleryList pmodel.Gallery
	//	var galleryItem pmodel.GalleryItem
	//	//if goodsItem.ShelvesStatus != 1001 {
	//	//	continue
	//	//}
	//	for _, imgItem := range imgList[goodsItem.GoodsSkuCode] {
	//		if imgItem.ImgMain == 1 {
	//			goodsItem.ImgMain = imgItem.ImgUrl
	//		} else {
	//			galleryItem.Type = 1
	//			galleryItem.Src = imgItem.ImgUrl
	//			galleryList = append(galleryList, galleryItem)
	//		}
	//
	//	}
	//
	//	SellPrice, _ := strconv.ParseFloat(goodsItem.SellPrice, 64)
	//	MarketPrice, _ := strconv.ParseFloat(goodsItem.MarketPrice, 64)
	//
	//	goodsItem.Gallery = galleryList
	//	goodsItem.Detail = goodsItem.DescDetail.GoodsDescribe
	//	strs := float64(SellPrice) * 100
	//	strm := float64(MarketPrice) * 100
	//	goodsItem.SellPrice = strconv.Itoa(int(strs))
	//	goodsItem.MarketPrice = strconv.Itoa(int(strm))
	//	yzhProduct = append(yzhProduct, goodsItem)
	//}
	//
	//source.DB().Create(&yzhProduct)
	////if err != nil {
	////	panic(err)
	////}
	//fmt.Println("准备创建数量", len(yzhProduct))
	//fmt.Println("总数", len(yzhProduct))

	return
}

var wg sync.WaitGroup

func (y *KunSheng) RunGoods(maps []int64, orderPN string) {

	//for index, item := range maps {
	//
	//	wg.Add(1)
	//if index%10 == 0 {
	//	time.Sleep(time.Second * 1)
	//}
	//go y.SynergeticProcess(item)
	//if index >1000 {
	//	return
	//}
	//yzhProduct.ProductImage=goodsDetail.RESULTDATA.PRODUCTIMAGE

	//var resultArr int
	//err = source.DB().Select("id").Model(model.SupplyGoods{}).Where("gather_supply_id=? and supply_goods_id=?", y.SupplyID,item).Pluck("supply_goods_id",&resultArr).Error
	//if err != nil {
	//	log.Log().Error("查询错误",zap.Any("err",err))
	//	continue
	//}

	//var listGoods []*pmodel.Product
	//
	//err, listGoods = y.CommodityAssemblyA(goodsDetail)
	//
	//if len(listGoods) > 0 {
	//	FinalProcessing(listGoods, orderPN)
	//}

	//
	//difference := collection.Collect(resultArr).WhereIn(goodsDetail.RESULTDATA.PRODUCTDATA)

	//}
	//wg.Wait()

}

// 获取分类数据
func (y *KunSheng) GetCategory(infof request.GetCategorySearch) (err error, data interface{}) {

	data, err = y.GetCategories("", "")

	return
}

func (y *KunSheng) GetGroup() (err error, data interface{}) {

	return

}

var AllCateData []model.RESULTDATA

func (y *KunSheng) GetYzhCategoryListAll() {
	AllCateData = nil
	var info request.GetCategoryChild
	err, data := y.GetCategoryChildSub(0, info)
	if err != nil {
		fmt.Println("获取以及分类错误")
	}
	AllCateData = data.([]model.RESULTDATA)
	for _, item := range AllCateData {

		err, cdata := y.GetCategoryChildSub(int(item.Code), info)
		if err != nil {
			fmt.Println("获取二级分类错误")
		}
		clist := cdata.([]model.RESULTDATA)
		AllCateData = append(AllCateData, clist...)
		for _, citem := range clist {

			time.Sleep(time.Microsecond * 100000)
			err, ccdata := y.GetCategoryChildSub(int(citem.Code), info)
			if err != nil {
				fmt.Println("获取二级分类错误")
			}
			cclist := ccdata.([]model.RESULTDATA)
			AllCateData = append(AllCateData, cclist...)
		}

	}

	fmt.Println(AllCateData)
}

func (y *KunSheng) GetCategoryChild(pid int, info request.GetCategoryChild) (err error, data interface{}) {
	//timeUnix := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	//url := string(y.Http + "mcang/Mcang/getGoodsClassify")
	//var resData []byte
	//headerData := make(map[string]string)

	//headerData["c_level"] = ""
	//headerData["c_parent_code"] = ""

	//url := string(y.Http + "mcang/Mcang/getGoodsClassify")
	//var resData []byte
	//headerData := make(map[string]string)
	//reqValue := url2.Values{}
	//code := strconv.Itoa(pid)
	//
	//headerData["c_parent_code"] = code
	//
	//reqValue.Add("c_parent_code", code)
	//
	////reqData, _ := json.Marshal(headerData)
	//var header = make(map[string]string)
	//
	//header["channelType"] = y.dat.BaseInfo.Channel
	//key := header["channelType"] + y.dat.BaseInfo.AppSecret
	//header["md5"] = strings.ToLower(Sign(headerData, key))
	//err, resData = utils.PostForm(url, reqValue, header)
	//if err != nil {
	//	return
	//}

	//var resCategory wmodel.ResCategory
	//err = json.Unmarshal(resData, &resCategory)
	//if err != nil {
	//	return
	//}
	////var yzhCategory []model.YzhCategory
	//var yzhCategory []model.YzhNewCategory
	//for _, cateItem := range resCategory.Data {
	//
	//	//code, _ := strconv.Atoi(cateItem.CategoryCode)
	//	//pidCode, _ := strconv.Atoi(cateItem.ParentCategoryCode)
	//	yzhCategory = append(yzhCategory, model.YzhNewCategory{
	//		//Id:    cateItem.CId,
	//		Title: cateItem.CName,
	//		Level: cateItem.CLevel,
	//		Pid:   cateItem.CParentCode,
	//		Code:  cateItem.CCode,
	//	})
	//}
	//data = yzhCategory
	return

}

func (y *KunSheng) GetImportCategoryChild(pid string, info request.GetCategoryChild) (err error, data []model.YzhNewCategory) {
	//
	//url := string(y.Http + "mcang/Mcang/getGoodsClassify")
	//var resData []byte
	//headerData := make(map[string]string)
	//reqValue := url2.Values{}
	//code := pid
	//
	//headerData["c_parent_code"] = code
	//
	//reqValue.Add("c_parent_code", code)
	//
	////reqData, _ := json.Marshal(headerData)
	//var header = make(map[string]string)
	//
	//header["channelType"] = y.dat.BaseInfo.Channel
	//key := header["channelType"] + y.dat.BaseInfo.AppSecret
	//header["md5"] = strings.ToLower(Sign(headerData, key))
	//err, resData = utils.PostForm(url, reqValue, header)
	//if err != nil {
	//	return
	//}

	//var resCategory wmodel.ResCategory
	//err = json.Unmarshal(resData, &resCategory)
	//if err != nil {
	//	return
	//}
	////var yzhCategory []model.YzhCategory
	//var yzhCategory []model.YzhNewCategory
	//for _, cateItem := range resCategory.Data {
	//
	//	//code, _ := strconv.Atoi(cateItem.CategoryCode)
	//	//pidCode, _ := strconv.Atoi(cateItem.ParentCategoryCode)
	//	yzhCategory = append(yzhCategory, model.YzhNewCategory{
	//		//Id:    cateItem.CId,
	//		Title: cateItem.CName,
	//		Level: cateItem.CLevel,
	//		Pid:   cateItem.CParentCode,
	//		Code:  cateItem.CCode,
	//	})
	//}
	//data = yzhCategory
	return

}

func (y *KunSheng) GetBrand(page string) (err error, data interface{}) {
	//y.Http = "https://uat.api.weipinshang.net/"
	////timeUnix := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	//url := string(y.Http + "mcang/Mcang/goodBrand")
	//var resData []byte
	//headerData := make(map[string]string)
	//reqValue := url2.Values{}
	//headerData["pageNo"] = page
	//headerData["pageSize"] = "20"
	//reqValue.Add("pageNo", page)
	//reqValue.Add("pageSize", "20")
	//
	////reqData, _ := json.Marshal(headerData)
	//var header = make(map[string]string)
	//
	//header["channelType"] = y.dat.BaseInfo.Channel
	//key := header["channelType"] + y.dat.BaseInfo.AppSecret
	//header["md5"] = strings.ToLower(Sign(headerData, key))
	//err, resData = utils.PostForm(url, reqValue, header)
	//if err != nil {
	//	return
	//}
	//
	//var yzhCateGory model.YzhNewCate
	//err = json.Unmarshal(resData, &yzhCateGory)
	//if err != nil {
	//	return
	//}
	////var yzhCategory []model.YzhCategory
	//var yzhCategory []model.YzhNewCategory
	//for _, cateItem := range yzhCateGory.Result {
	//
	//	//code, _ := strconv.Atoi(cateItem.CategoryCode)
	//	//pidCode, _ := strconv.Atoi(cateItem.ParentCategoryCode)
	//	yzhCategory = append(yzhCategory, model.YzhNewCategory{
	//		Id:    cateItem.CategoryCode,
	//		Title: cateItem.CategoryName,
	//		Level: cateItem.CategoryLevel,
	//		Pid:   cateItem.ParentCategoryCode,
	//		Code:  cateItem.CategoryCode,
	//	})
	//}
	//data = yzhCategory
	return

}

func (y *KunSheng) GetCategoryChildSub(pid int, info request.GetCategoryChild) (err error, data interface{}) {

	return

}

func (y *KunSheng) GetCategoryDetail(id uint) (err error, cate uint) {

	return

}

// 商品组装
func (y *KunSheng) CommodityAssemblyLocal(detail wmodel.KunShengProduct, cateId1, cateId2, cateId3 int, ExpressDelivery string) (err error, listGoods []*pmodel.Product) {
	//
	sourceCode := common2.KUNSHENG_SOURCE
	goods := new(pmodel.Product)

	product := pmodel.Product{}

	err = source.DB().Where("source_goods_id_string =  ? and source=?", detail.ItemNumber, sourceCode).Preload("Skus").First(&product).Error
	if product.ID > 0 {

		source.DB().Model(model.SupplyGoodsImportRecord{}).Where("batch=?", GlobalOrderSN).Updates(map[string]interface{}{
			"repeat_quantity": gorm.Expr("repeat_quantity + ?", 1),
		})
		return
	}

	GoodsSkuCode := detail.ItemNumber
	goods.SourceGoodsIDString = GoodsSkuCode
	var brand = new(catemodel.Brand)
	if detail.Brand != "" {
		brand.Name = detail.Brand
		brand.Source = sourceCode
		err = source.DB().Where(brand).FirstOrCreate(&brand).Error
		goods.BrandID = brand.ID
	}
	var costPrice, salePrice, originPrice, activityPrice, guidePrice uint
	var elem model.Goods

	CostPrice := detail.TagPrice

	elem.AgreementPrice = uint(CostPrice * 100) //协议价

	elem.Source = sourceCode

	err, costPrice, salePrice, originPrice, activityPrice, guidePrice = GetPricingPrice(elem, "gatherSupply"+strconv.Itoa(int(y.SupplyID)))

	goods.Title = detail.Name
	InnerNum, _ := strconv.Atoi(detail.Size)
	goods.Stock = uint(InnerNum)
	goods.OriginPrice = originPrice
	goods.Price = salePrice
	goods.CostPrice = costPrice
	goods.ActivityPrice = activityPrice
	goods.GuidePrice = guidePrice
	//goods.MD5 = md5Str
	//if len(stock.Result) > 0 {
	//	goods.Stock = uint(stock.Result[0].StockNum)
	//}
	// 获取商品详情图片

	//
	var Gallery pmodel.Gallery

	//if len(goods.Gallery) <= 0 {

	//bannerImg := strings.Split(detail.CBannerImages, ";")

	imagesResponse, err := y.GetProductImages(detail.ItemNumber)
	if err == nil && len(imagesResponse.Data) > 0 {
		// 将图片数组转换为HTML img标签字符串
		var imgTags []string
		for _, imgUrl := range imagesResponse.Data {
			for _, img := range imgUrl.Images {
				imgTags = append(imgTags, fmt.Sprintf("<img src=\"%s\" alt=\"商品详情图片\">", img))
				Gallery = append(Gallery, pmodel.GalleryItem{Type: 1, Src: img})
			}
		}
		goods.DetailImages = strings.Join(imgTags, "")
	} else {
		goods.DetailImages = fmt.Sprintf("<img src=\"%s\" alt=\"商品详情图片\">", detail.Image)
	}

	goods.IsDisplay = 1
	//
	////goods.TaxRate = int(detail.RESULTDATA.PRODUCTDATA.Tax)
	goods.ImageUrl = detail.Image
	//
	////goods.Unit = detail.GoodsUnit
	//if goods.Unit == "" {
	goods.Unit = "件"
	//goods.ShopName = detail.WareHouseName
	goods.ExpressDelivery = ExpressDelivery
	//goods.Wide
	//}
	goods.Source = sourceCode
	//goods.DetailImages = detail.CGoodsContent
	//
	//cateList := strings.Split(detail.CategoryNames, ",")
	var cate1, cate2, cate3 catemodel.Category
	display := 1
	//
	var dat model.SupplySetting
	err, setting := gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(y.SupplyID)))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}

	err = json.Unmarshal([]byte(setting.Value), &dat)
	if err != nil {
		return
	}

	if cateId1 > 0 {
		goods.Category1ID = uint(cateId1)
	}
	if cateId2 > 0 {
		goods.Category2ID = uint(cateId2)
	}
	if cateId3 > 0 {
		goods.Category3ID = uint(cateId3)
	}
	//
	if dat.UpdateInfo.CateGory == 1 {
		// 通过商品的三级分类ID获取完整分类信息
		categoryInfo, err := y.GetCategoryInfo(strconv.Itoa(detail.CatID))
		if err == nil {
			// 处理三级分类
			cate3.IsDisplay = &display
			cate3.ParentID = cate2.ID
			cate3.Level = 3
			cate3.Name = categoryInfo.CatName
			source.DB().Where("name=? and level=? and parent_id=?", categoryInfo.CatName, 3, cate2.ID).FirstOrCreate(&cate3)

			// 获取并处理二级分类
			parentInfo, err := y.GetCategoryInfo(strconv.Itoa(categoryInfo.ParentId))

			if err == nil {
				cate2.IsDisplay = &display
				cate2.Level = 2
				cate2.Name = parentInfo.CatName
				source.DB().Where("name=? and level=?", parentInfo.CatName, 2).FirstOrCreate(&cate2)
				cate3.ParentID = cate2.ID
				source.DB().Save(&cate3)

				// 获取并处理一级分类
				grandParentInfo, err := y.GetCategoryInfo(strconv.Itoa(parentInfo.ParentId))
				if err == nil {
					cate1.IsDisplay = &display
					cate1.ParentID = 0
					cate1.Level = 1
					cate1.Name = grandParentInfo.CatName
					source.DB().Where("name=? and level=? and parent_id=?", grandParentInfo.CatName, 1, 0).FirstOrCreate(&cate1)
					cate2.ParentID = cate1.ID
					source.DB().Save(&cate2)
				}
			}

			// 更新商品分类ID
			if cate1.ID > 0 {
				goods.Category1ID = cate1.ID
			}
			if cate2.ID > 0 {
				goods.Category2ID = cate2.ID
			}
			if cate3.ID > 0 {
				goods.Category3ID = cate3.ID
			}
		}
	}
	if cate1.ID != 0 {
		goods.Category1ID = cate1.ID

	}
	if cate2.ID != 0 {
		goods.Category2ID = cate2.ID

	}
	if cate3.ID != 0 {
		goods.Category3ID = cate3.ID

	}

	goods.FreightType = 2
	goods.GatherSupplyID = y.SupplyID
	//

	var skuList []pmodel.Sku

	goods.Gallery = Gallery

	//}
	///**
	//处理轮播图结束
	//*/
	//
	goods.MinPrice = goods.Price
	goods.MaxPrice = goods.Price
	//
	var minProfitRate float64

	goods.Skus = skuList

	if len(goods.Skus) == 0 {
		// 通过ItemNumber获取相同商品作为规格
		info := request.GetGoodsSearch{
			Page:  1,
			Limit: 100,
		}
		info.ItemNumber = detail.ItemNumber
		productErr, productResp, _, _ := y.GetProductsFromUpstream(info)
		if productErr == nil && len(productResp.Data.Record) > 0 {
			for _, productItem := range productResp.Data.Record {

				var sku pmodel.Sku
				var options pmodel.Options
				var option pmodel.Option

				option.SpecName = "尺码"
				option.SpecItemName = productItem.Size
				options = append(options, option)

				option.SpecName = "颜色"
				option.SpecItemName = productItem.Color
				options = append(options, option)

				sku.Title = productItem.Size + productItem.Color
				sku.Options = options
				sku.CostPrice = goods.CostPrice
				sku.Describe = productItem.Size

				// 获取库存信息
				stockRequest := wmodel.StockRequest{
					PageIndex:            1,
					PageSize:             10,
					CommodityBarTypeCode: productItem.CommodityBarTypeCode,
				}
				stockResponse, stockErr := y.GetStock(stockRequest)
				if stockErr == nil && len(stockResponse.Data.Record) > 0 {
					totalStock := 0
					for _, record := range stockResponse.Data.Record {
						totalStock += record.Stock
					}
					sku.Stock = totalStock
				} else {
					sku.Stock = 0
					log.Log().Error("获取商品库存失败", zap.Error(stockErr))
				}

				sku.Price = goods.Price
				sku.OriginPrice = goods.OriginPrice
				sku.GuidePrice = goods.GuidePrice
				sku.ActivityPrice = goods.ActivityPrice
				sku.OriginalSkuID = int64(goods.SourceGoodsID)
				sku.SpecId = productItem.CommodityBarTypeCode
				sku.ImageUrl = goods.ImageUrl

				if sku.GuidePrice > 0 {
					sku.ProfitRate = utils.ExecProfitRate(sku.GuidePrice, sku.Price)
				} else {
					sku.ProfitRate = 0
				}

				if minProfitRate == 0 || sku.ProfitRate < minProfitRate {
					minProfitRate = sku.ProfitRate
				}

				skuList = append(skuList, sku)
			}

			goods.Skus = skuList
			goods.SingleOption = 0
		} else {
			// 如果获取失败，使用原有商品信息创建单个SKU
			var sku pmodel.Sku
			var options pmodel.Options
			var option pmodel.Option

			option.SpecName = "尺码"
			option.SpecItemName = detail.Size
			options = append(options, option)

			option.SpecName = "颜色"
			option.SpecItemName = detail.Color
			options = append(options, option)

			sku.Title = detail.Size + detail.Color
			sku.Options = options
			sku.CostPrice = goods.CostPrice
			sku.Describe = detail.Size

			stockRequest := wmodel.StockRequest{
				PageIndex:            1,
				PageSize:             10,
				CommodityBarTypeCode: detail.CommodityBarTypeCode,
			}
			stockResponse, err := y.GetStock(stockRequest)
			if err == nil && len(stockResponse.Data.Record) > 0 {
				totalStock := 0
				for _, record := range stockResponse.Data.Record {
					totalStock += int(record.Stock)
				}
				sku.Stock = totalStock
			} else {
				sku.Stock = 0
				log.Log().Error("获取商品库存失败", zap.Error(err))
			}

			sku.Price = goods.Price
			sku.OriginPrice = goods.OriginPrice
			sku.GuidePrice = goods.GuidePrice
			sku.ActivityPrice = goods.ActivityPrice
			sku.OriginalSkuID = int64(goods.SourceGoodsID)
			sku.SpecId = detail.CommodityBarTypeCode
			sku.ImageUrl = goods.ImageUrl

			if sku.GuidePrice > 0 {
				sku.ProfitRate = utils.ExecProfitRate(sku.GuidePrice, sku.Price)
			} else {
				sku.ProfitRate = 0
			}
			minProfitRate = sku.ProfitRate

			skuList = append(skuList, sku)
			goods.Skus = skuList
			goods.SingleOption = 1
		}

	}
	//
	if len(goods.Skus) > 0 {
		goods.Stock = uint(skuList[0].Stock)
		goods.ProfitRate = minProfitRate
	}
	////---------处理属性json数组结束
	////goods.Desc=detail.Description
	//
	if len(goods.Skus) > 0 {
		listGoods = append(listGoods, goods)

	}

	return
}

// 商品组装
func (y *KunSheng) CommodityAssemblyLocalUpdate(detail model.YzhProductTemp, goods pservice.ProductForUpdate, cateId1, cateId2, cateId3 int) (err error, listGoods []*pmodel.Product) {
	var skuIds []int64
	skuCode, _ := strconv.Atoi(detail.GoodsSkuCode)
	skuIds = append(skuIds, int64(skuCode))
	//imgList := y.QueryGoodsImageList(skuIds)

	sourceCode := int(common2.YZH_NEW)
	//var goods pservice.ProductForUpdate
	//err = source.DB().Where("source_goods_id =  ? and source=?", detail.GoodsSkuCode, sourceCode).Preload("Skus").First(&goods).Error

	var strSkuCode []string
	strSkuCode = append(strSkuCode, detail.GoodsSkuCode)
	GoodsSkuCode, _ := strconv.Atoi(detail.GoodsSkuCode)
	goods.SourceGoodsID = uint(GoodsSkuCode)
	//var brand = new(catemodel.Brand)
	//if detail.BrandName != "" {
	//	brand.Name = detail.BrandName
	//	brand.Source = sourceCode
	//	err = source.DB().Where(brand).FirstOrCreate(&brand).Error
	//	goods.BrandID = brand.ID
	//}
	var costPrice, salePrice, originPrice, activityPrice, guidePrice uint
	var elem model.Goods

	SellPrice, spriceErr := strconv.ParseFloat(detail.SellPrice, 64)
	if spriceErr != nil {
		fmt.Println("价格错误", spriceErr)
	}
	MarketPrice, mpriceErr := strconv.ParseFloat(detail.MarketPrice, 64)

	MarketPriceUint := uint(MarketPrice * 100)
	SellPriceUint := uint(SellPrice * 100)

	if MarketPriceUint <= 0 || SellPriceUint <= 0 {
		return
	}

	if mpriceErr != nil {
		fmt.Println("价格错误", mpriceErr)
	}
	elem.AgreementPrice = uint(SellPriceUint)
	elem.GuidePrice = uint(MarketPriceUint)
	elem.ActivityPrice = uint(MarketPriceUint)
	elem.Source = sourceCode

	err, costPrice, salePrice, originPrice, activityPrice, guidePrice = GetPricingPrice(elem, "gatherSupply"+strconv.Itoa(int(y.SupplyID)))
	//var dat model.SupplySetting
	//err, setting := gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(y.SupplyID)))
	//if err != nil {
	//	fmt.Println("获取供应链key设置失败")
	//	return
	//}
	//err = json.Unmarshal([]byte(setting.Value), &dat)
	//if err != nil {
	//	return
	//}
	//stock, _ := y.GetStock(strSkuCode)

	//if stock.RESPONSESTATUS != "true" || stock.RESULTDATA.StockStatus != true {
	//	err = errors.New("库存无效")
	//	return
	//}

	goods.Title = detail.GoodsSkuName
	goods.OriginPrice = originPrice
	//if dat.UpdateInfo.CurrentPrice == 1 {
	goods.Price = salePrice
	//}

	//if dat.UpdateInfo.CostPrice == 1 {
	goods.CostPrice = costPrice
	//}

	goods.ActivityPrice = activityPrice
	goods.GuidePrice = guidePrice
	//if len(stock.Result) > 0 {
	//	if stock.Result[0].StockNum > 0 {
	//		goods.Stock = uint(stock.Result[0].StockNum)
	//	}
	//}

	goods.SingleOption = 1
	//goods.Sales = elem.Sale
	//status, _ := strconv.Atoi(detail.RESULTDATA.PRODUCTDATA.Status)
	if detail.ShelvesStatus == 1001 {
		goods.IsDisplay = 1
	} else {
		goods.IsDisplay = 0
	}

	goods.Unit = detail.GoodsUnit
	//goods.SourceGoodsID = uint(detail.RESULTDATA.PRODUCTDATA.ProductId)
	goods.Source = sourceCode
	//goods.DetailImages = detail.Detail

	//cateList := strings.Split(detail.CategoryNames, ",")
	//var cate1, cate2, cate3 catemodel.Category
	//display := 1
	//
	//err = json.Unmarshal([]byte(setting.Value), &dat)
	//if err != nil {
	//	return
	//}
	//
	//if dat.UpdateInfo.CateGory == 1 {
	//
	//	//if len(cateList) > 0 && cateList[0] != "" {
	//
	//	cate1.IsDisplay = &display
	//	cate1.ParentID = 0
	//	cate1.Level = 1
	//	cate1.Name = detail.FirstCategoryName
	//	source.DB().Where("name=? and level=? and parent_id=?", detail.FirstCategoryName, 1, 0).FirstOrCreate(&cate1)
	//	//	}
	//
	//	//if len(cateList) > 1 && cateList[1] != "" {
	//	cate2.IsDisplay = &display
	//	cate2.ParentID = cate1.ID
	//	cate2.Level = 2
	//	cate2.Name = detail.SecondCategoryName
	//	source.DB().Where("name=? and level=? and parent_id=?", detail.SecondCategoryName, 2, cate1.ID).FirstOrCreate(&cate2)
	//	//}
	//
	//	//if len(cateList) > 2 && cateList[2] != "" {
	//	cate3.IsDisplay = &display
	//	cate3.ParentID = cate2.ID
	//	cate3.Level = 3
	//	cate3.Name = detail.LastCategoryName
	//	source.DB().Where("name=? and level=? and parent_id=?", detail.LastCategoryName, 3, cate2.ID).FirstOrCreate(&cate3)
	//	//}
	//	//
	//
	//	goods.Category1ID = cate1.ID
	//	goods.Category2ID = cate2.ID
	//	goods.Category3ID = cate3.ID
	//}
	//
	//if cateId1 > 0 {
	//	goods.Category1ID = uint(cateId1)
	//}
	//if cateId2 > 0 {
	//	goods.Category2ID = uint(cateId2)
	//}
	//if cateId3 > 0 {
	//	goods.Category3ID = uint(cateId3)
	//}

	//}
	goods.Gallery = nil
	goods.FreightType = 2
	goods.GatherSupplyID = y.SupplyID

	var sku = pservice.Sku{}
	var skuList []pservice.Sku

	var galleryList pmodel.Gallery
	//var galleryItem pmodel.GalleryItem
	//if goodsItem.ShelvesStatus != 1001 {
	//	continue
	//}
	//for _, imgItem := range imgList[detail.GoodsSkuCode] {
	//	if imgItem.ImgMain == 1 {
	//		detail.ImgMain = imgItem.ImgUrl
	//	} else {
	//		galleryItem.Type = 1
	//		//galleryItem.Src = imgItem.ImgUrl
	//		galleryList = append(galleryList, galleryItem)
	//	}
	//
	//}

	if len(galleryList) > 0 {
		goods.Gallery = galleryList
	}

	//if len(detail.Gallery) > 0 {
	//	goods.Gallery = detail.Gallery
	//}
	//

	if detail.ImgMain != "" {
		goods.ImageUrl = detail.ImgMain
	}
	if len(goods.Gallery) == 0 && detail.ImgMain != "" {
		var item pmodel.GalleryItem
		var gallery pmodel.Gallery
		item.Src = detail.ImgMain
		item.Type = 1
		gallery = append(gallery, item)
		goods.Gallery = gallery
	}

	/**
	处理轮播图结束
	*/

	goods.MinPrice = goods.Price
	goods.MaxPrice = goods.Price

	//if len(goods.Skus) == 0 {
	var options pmodel.Options
	var option pmodel.Option
	option.SpecName = "规格"
	option.SpecItemName = "默认"
	options = append(options, option)
	if len(goods.Skus) > 0 {
		sku.ID = goods.Skus[0].ID
	}

	if len(goods.Skus) > 0 {

		sku.ID = goods.Skus[0].ID
	}

	sku.Title = "默认"
	sku.Options = options
	sku.Weight = 0
	sku.CostPrice = goods.CostPrice
	sku.Stock = int(goods.Stock)
	//sku.IsDisplay = goods.IsDisplay
	sku.Price = goods.Price
	sku.OriginPrice = goods.OriginPrice
	sku.GuidePrice = goods.GuidePrice
	sku.ActivityPrice = goods.ActivityPrice
	sku.OriginalSkuID = int(goods.SourceGoodsID)
	skuList = append(skuList, sku)

	//}

	goods.Skus = skuList
	goods.ProfitRate = pservice.Decimal((float64(goods.GuidePrice) - float64(goods.Price)) / float64(goods.GuidePrice) * 100)
	//---------处理属性json数组结束
	//goods.Desc=detail.Description
	if goods.ID > 0 {
		log.Log().Debug("yunzhonghe修改商品", zap.Any("id", goods.ID))

		err = pservice.UpdateProduct(goods)
		if err != nil {
			log.Log().Error("更新商品错误 update", zap.Any("err", err))
			return
		}
		//source.DB().Model(model.SupplyGoodsImportRecord{}).Where("batch=?", GlobalOrderSN).Updates(map[string]interface{}{
		//	"repeat_quantity": gorm.Expr("repeat_quantity + ?", 1),
		//})
	}

	return
}

var GlobalOrderSN string

// 批量获取商品详情
func (y *KunSheng) BatchGetGoodsDetails(ids string) (err error, data []wmodel.Goods) {

	return
}

func (*KunSheng) RunConcurrent(wg *sync.WaitGroup, info request.GetCategorySearch, i int) (err error) {

	defer wg.Done()
	var result *stbz.APIResult
	result, err = stbz.API(
		1,
		"/v2/Category/Lists",
		map[string]string{},
		g.Map{"page": i, "limit": info.Limit, "source": info.Source},
	)
	fmt.Println("循环：", i, info.Limit, info.Source)
	if result.Data != nil {
		datas := result.Data.(map[string]interface{})
		var cateItem []model.Category
		cateJson := datas["data"]
		mJson, _ := json.Marshal(cateJson)
		stringJson := string(mJson)
		err = json.Unmarshal([]byte(stringJson), &cateItem)
		mutex.Lock()
		if len(cateItem) > 0 {
			category = append(category, cateItem...)
		}
		mutex.Unlock()

	} else {
		fmt.Println("获取失败的：", i, info.Limit, info.Source)
	}

	return
}
