package order

import (
	_ "embed"
	"fmt"
	v1 "order/api/v1"
	"order/express"
	express2 "shipping/express"
	"sort"
	"strings"
	"yz-go/utils"
)

func ExpressSent(orderRequest v1.HandleOrderRequest) (err error) {
	err = v1.CallBackSendOrder(orderRequest)
	return
}

type MapEntryHandler func(string, string)

// 按字母顺序遍历map
func traverseMapInStringOrder(params map[string]string, handler MapEntryHandler) {
	keys := make([]string, 0)
	for k, _ := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	for _, k := range keys {
		handler(k, params[k])
	}
}
func Sign(maplist map[string]string, key string) string {

	var signature string
	//按照字母顺序遍历
	traverseMapInStringOrder(maplist, func(key string, value string) {
		if signature == "" {
			signature += key + "=" + value
		} else {
			signature += "&" + key + "=" + value
		}
	})

	signTemp := signature + key

	signKey := utils.MD5V([]byte(signTemp))

	return signKey

}

type Company struct {
	Code string `json:"code"`
	Name string `json:"name"`
}

func ExpressList(name string) (err error, code string) {

	for _, item := range express2.GetCompanyList() {
		if item.Name == name {
			code = item.Code
			fmt.Println(code)
			return
		} else if strings.Contains(item.Name, name) {
			code = item.Code
			fmt.Println(code)
			return
		} else if strings.Contains(name, item.Name) {
			code = item.Code
			fmt.Println(code)
			return
		}
	}
	return
}

type ExpressName struct {
	Name string `json:"name"`
	NO   string `json:"no"`
}

var Companies []Company

var kd string

type Response struct {
	Code int                    `json:"code"`
	Data []express.OrderExpress `json:"data"`
	Msg  string                 `json:"msg"`
}

type ExpressInfo struct {
	Data struct {
		Info struct {
			Name string `json:"name"`
			No   string `json:"no"`
		} `json:"info"`
	} `json:"data"`
}
