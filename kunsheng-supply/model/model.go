package model

type Goods struct {
	WareHouseName string  `json:"wareHouseName"`
	Sex           string  `json:"sex"`
	Division      string  `json:"division"`
	Marketprice   float64 `json:"marketprice"`
	UkSize        string  `json:"ukSize"`
	Articleno     string  `json:"articleno"`
	BrandName     string  `json:"brandName"`
	Discount      string  `json:"discount"`
	Quarter       string  `json:"quarter"`
	InnerNum      string  `json:"innerNum"`
	Size          string  `json:"size"`
	Weight        string  `json:"weight"`
	Color         string  `json:"color"`
	Barcode       string  `json:"barcode"`
	PicUrl        string  `json:"pic_url"`
}
type GoodsData struct {
	Total     int     `json:"total"`
	ErrorInfo string  `json:"error_info"`
	ErrorCode string  `json:"error_code"`
	Rows      []Goods `json:"rows"`
}
type WareHouseName struct {
	Total int `json:"total"`
	Rows  []struct {
		Brands                  string  `json:"brands"`
		Star                    int     `json:"star"`
		WarehouseRemark         string  `json:"warehouse_remark"`
		PickingNum              int     `json:"pickingNum"`
		Express                 string  `json:"express"`
		UpdateTime              string  `json:"updateTime"`
		Des                     string  `json:"des"`
		WarehouseExplainSetting string  `json:"warehouseExplainSetting"`
		PickingRate             int     `json:"pickingRate"`
		FirstWeight             float64 `json:"first_weight"`
		ReturnRemark            string  `json:"return_remark"`
		PostageType             bool    `json:"postage_type"`
		WareHouseName           string  `json:"wareHouseName"`
		PickingDate             string  `json:"pickingDate"`
	} `json:"rows"`
}

type Article struct {
	ArticleNO string `json:"articleno"`
}

type PublicSearch struct {
	Page string `json:"page"`
	Rows string `json:"rows"`
}

type DeliveryTimeData struct {
	PublicSearch
}

type GoodsDetail struct {
	ErrorInfo string `json:"error_info"`
	Data      struct {
		GoodsNo  string `json:"goods_no"`
		Describe string `json:"describe"`
		Title    string `json:"title"`
	} `json:"data"`
	ErrorCode int `json:"error_code"`
}
type DeliveryInfo struct {
	Total int `json:"total"`
	Page  int `json:"page"`
	Rows  []struct {
		AdditionalPostage float64 `json:"additional_postage"`
		Province          string  `json:"province"`
		DiliveryName      string  `json:"dilivery_name"`
		FirstPostage      float64 `json:"first_postage"`
	} `json:"rows"`
}
type DeliveryTimeList struct {
	Total int `json:"total"`
	Rows  []struct {
		WareHouseName   string `json:"wareHouseName"`
		DeliveryTimeDes string `json:"deliveryTimeDes"`
	} `json:"rows"`
}
type OrderInfo struct {
	Status    string `json:"status"`
	TradeId   string `json:"trade_id"`
	OrderSn   string `json:"order_sn"`
	Info      string `json:"info"`
	ErrorCode string `json:"error_code"`
	ErrorInfo string `json:"error_info"`
}
type OrdersDataInfos []OrdersDataInfo
type OrdersDataInfo struct {
	OrderSn       string      `json:"order_sn"`
	PushOrderSn   string      `json:"push_order_sn"`
	Name          string      `json:"name"`
	Tel           string      `json:"tel"`
	Mobile        string      `json:"mobile"`
	Address       string      `json:"address"`
	Province      string      `json:"province"`
	City          string      `json:"city"`
	Area          string      `json:"area"`
	Town          string      `json:"town"`
	PostCode      string      `json:"post_code"`
	Delivery      string      `json:"delivery"`
	WarehouseName string      `json:"warehouse_name"`
	PtName        string      `json:"pt_name"`
	Oaid          string      `json:"oaid"`
	GoodsInfos    []GoodsInfo `json:"goods_info"`
}

type GoodsInfo struct {
	OrderSnSub     string `json:"order_sn_sub"`
	PushOrderSnSub string `json:"push_order_sn_sub"`
	GoodsNo        string `json:"goods_no"`
	Size           string `json:"size"`
	Amount         int    `json:"amount"`
}
type ResponseOrderInfo struct {
	TradeId string `json:"trade_id"`
	Orders  []struct {
		GoodsNo string  `json:"goods_no"`
		OrderId int     `json:"order_id"`
		PostFee float64 `json:"post_fee"`
		Price   float64 `json:"price"`
		Size1   string  `json:"size1"`
		Size2   string  `json:"size2"`
	} `json:"orders"`
	PushOrderSn string `json:"push_order_sn"`
	OrderSn     string `json:"order_sn"`
	Status      string `json:"status"`
	Info        string `json:"info"`
}
