package f

import (
	"category/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"lease/request"
	service3 "lease/service"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
)

// GetSupplierIndex
// @Tags 获取有商品的租赁分类
// @Summary 获取有商品的租赁分类
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "获取有商品的租赁分类"
// @Success 200 {object} SupplierIndex
// @Router /api/lease/GetCategoryList [get]
func GetCategoryList(c *gin.Context) {
	var err error
	var getStoreDetatilInformation yzRequest.GetStoreDetatilInformation
	err = c.ShouldBindQuery(&getStoreDetatilInformation)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err,GetGatherSupply := service3.GetGatherSupply()
	if err != nil {
		log.Log().Error("标识查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("标识查询失败:请重启系统"+err.Error(), c)
		return
	}
	getStoreDetatilInformation.Type = 1
	getStoreDetatilInformation.Id = GetGatherSupply.ID

	if err,categorys:= service.GetCategoryListSupplierAndGatherSupplyAll(getStoreDetatilInformation); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("店铺暂无分类或者商品都没有上架", c)
		return
	} else {
		yzResponse.OkWithData(categorys, c)
	}
	return
}

// GetSupplierIndex
// @Tags 获取有租赁商品的品牌
// @Summary 获取有租赁商品的品牌
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "获取有租赁商品的品牌"
// @Success 200 {object} SupplierIndex
// @Router /api/lease/getBrandListGatherSupply [get]
func GetBrandListGatherSupply(c *gin.Context) {
	var err error
	var getStoreDetatilInformation yzRequest.GetStoreDetatilInformation
	_ = c.ShouldBindQuery(&getStoreDetatilInformation)

	err,GetGatherSupply := service3.GetGatherSupply()
	if err != nil {
		log.Log().Error("标识查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("标识查询失败:请重启系统"+err.Error(), c)
		return
	}

	if err,categorys:= service3.GetBrandListGatherSupply(GetGatherSupply.ID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("店铺暂无分类或者商品都没有上架", c)
		return
	} else {
		yzResponse.OkWithData(categorys, c)
	}
	return
}




// GetSupplierIndex
// @Tags 获取有租赁商品的供应商
// @Summary 获取有租赁商品的供应商
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "获取有租赁商品的供应商"
// @Success 200 {object} SupplierIndex
// @Router /api/lease/getSupplierListGatherSupply [get]
func GetSupplierListGatherSupply(c *gin.Context) {
	var err error
	var getStoreDetatilInformation yzRequest.GetStoreDetatilInformation
	_ = c.ShouldBindQuery(&getStoreDetatilInformation)

	err,GetGatherSupply := service3.GetGatherSupply()
	if err != nil {
		log.Log().Error("标识查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("标识查询失败:请重启系统"+err.Error(), c)
		return
	}

	if err,categorys:= service3.GetSupplierListGatherSupply(GetGatherSupply.ID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("暂无供应商"+err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(categorys, c)
	}
	return
}


// GetSupplierIndex
// @Tags 获取可服务器区域
// @Summary 获取可服务器区域
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "获取可服务器区域"
// @Success 200 {object} SupplierIndex
// @Router /api/lease/getLeaseCoverages [get]
func GetLeaseCoverages(c *gin.Context) {
	var getLeaseCoveragesSearch request.GetLeaseCoveragesSearch
	_ = c.ShouldBindQuery(&getLeaseCoveragesSearch)
	if getLeaseCoveragesSearch.PageSize == 0 {
		getLeaseCoveragesSearch.PageSize = 30
	}
	if err,list := service3.GetLeaseCoverages(getLeaseCoveragesSearch); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(list, "获取成功", c)
	}
	return
}


// @Tags Product
// @Summary 获取商品列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "获取商品列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取商品列表"}"
// @Router /api/lease/getProductList [get]
func GetProductList(c *gin.Context) {
	var pageInfo request.ProductSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list, total := service3.GetProductListFront(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}


// @Tags Product
// @Summary 获取租期根据商品id
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "获取租期根据商品id"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取商品列表"}"
// @Router /api/lease/getLeaseTenancyTermsByProductId [post]
func GetLeaseTenancyTermsByProductId(c *gin.Context) {
	var productIdSearch request.ProductIdSearch
	err := c.ShouldBindJSON(&productIdSearch)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list := service3.GetLeaseTenancyTermsByProductId(productIdSearch.ProductId); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(list,c)
	}
}


