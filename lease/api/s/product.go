package s

import (
	ufv1 "gin-vue-admin/admin/api/v1"
	v1 "gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	model2 "lease/model"
	"lease/request"
	service3 "lease/service"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	service2 "yz-go/service"
	"yz-go/utils"
)

// CreateProduct
// @Tags Product
// @Summary 创建Product
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Product true "创建Product"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /lease/createProduct [post]
func CreateProduct(c *gin.Context) {
	var product service3.ProductForUpdate
	err := c.ShouldBindJSON(&product)
	if err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err,GetGatherSupply := service3.GetGatherSupply()
	if err != nil {
		log.Log().Error("标识查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("标识查询失败:请重启系统"+err.Error(), c)
		return
	}
	product.GatherSupplyID = GetGatherSupply.ID


	err,supply :=service3.GetSupplierByUserId(ufv1.GetUserID(c))
	if err != nil {
		log.Log().Error("供应商不存在", zap.Any("err", err))
		yzResponse.FailWithMessage("供应商不存在", c)
		return
	}
	product.SupplierID = supply.ID
	var IsDisplay = product.IsDisplay
	if supply.NeedVerify == 1 {
		product.IsDisplay = 0
	}
	if err, id := service3.CreateProduct(product); err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		if supply.NeedVerify == 1 {
			err = service3.CreateLeaseAuditProduct(model2.LeaseAuditProduct{ProductID: id, SuppliersId: supply.ID,IsDisplay:IsDisplay})
			if err != nil {
				log.Log().Error("添加审核记录失败", zap.Any("err", err))
				yzResponse.FailWithMessage("添加审核记录失败:"+err.Error(), c)
				return
			}
		}
		service2.CreateOperationRecord(v1.GetUserID(c), 1, c.ClientIP(), "创建了商品'"+product.Title+"'")
		yzResponse.OkWithMessage("创建成功", c)
	}
}


// UpdateProduct
// @Tags Product
// @Summary 更新Product
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Product true "更新Product"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /lease/updateProduct [post]
func UpdateProduct(c *gin.Context) {
	var product service3.ProductForUpdate
	err := c.ShouldBindJSON(&product)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if product.ID == 0 {
		yzResponse.FailWithMessage("请提交商品id参数", c)
		return
	}
	err,supply :=service3.GetSupplierByUserId(ufv1.GetUserID(c))
	if err != nil {
		log.Log().Error("供应商不存在", zap.Any("err", err))
		yzResponse.FailWithMessage("供应商不存在", c)
		return
	}
	var IsDisplay = product.IsDisplay
	if supply.NeedVerify == 1 {
		product.IsDisplay = 0
	}
	if err := service3.UpdateProduct(product); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		if supply.NeedVerify == 1 {
			err = service3.CreateLeaseAuditProduct(model2.LeaseAuditProduct{ProductID: product.ID, SuppliersId: supply.ID,IsDisplay:IsDisplay})
			if err != nil {
				log.Log().Error("添加审核记录失败", zap.Any("err", err))
				yzResponse.FailWithMessage("添加审核记录失败:"+err.Error(), c)
				return
			}
		}
		service2.CreateOperationRecord(v1.GetUserID(c), 1, c.ClientIP(), "编辑了商品'"+product.Title+"'")
		yzResponse.OkWithMessage("更新成功", c)
	}
}
func SetAttributeStatus(c *gin.Context) {
	var columnStatus request.ColumnStatus
	err := c.ShouldBindJSON(&columnStatus)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := utils.Verify(columnStatus); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//err, supplier := service.GetSupplierByUserId(v1.GetUserID(c))
	//if err == nil && supplier.NeedVerify == 1 && columnStatus.Column == "is_display" && columnStatus.Status == 1{
	//	log.Log().Error("上架需要进入商品编辑页面中申请上架，待平台审核通过之后自动上架", zap.Any("err", err))
	//	yzResponse.FailWithMessage("上架需要进入商品编辑页面中申请上架，待平台审核通过之后自动上架", c)
	//	return
	//}
	if err, enable := service3.SetAttributeStatus(columnStatus); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"enable": enable}, c)
	}
}
//供应商获取商品列表
func GetProductList(c *gin.Context) {
	var pageInfo request.ProductSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err,supplyID :=ufv1.GetSupplierByUserId(ufv1.GetUserID(c))
	if err != nil {
		log.Log().Error("供应商不存在", zap.Any("err", err))
		yzResponse.FailWithMessage("供应商不存在", c)
		return
	}
	pageInfo.SupplierID = uint(supplyID)

	if err, list, total := service3.GetProductList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// @Tags Product
// @Summary 分页获取Product列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ProductSearch true "分页获取Product列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /product/getProductList [get]
func GetSupplyProductList(c *gin.Context) {
	var pageInfo request.ProductSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err,supplyID :=ufv1.GetSupplierByUserId(ufv1.GetUserID(c))

	pageInfo.SupplierID = uint(supplyID)
	if err, list, total, delTotal, upTotal, downTotal, noTotal := service3.GetAdminProductList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{
			"list":list,
			"total":total,
			"page":pageInfo.Page,
			"pageSize":pageInfo.PageSize,
			"delTotal": delTotal,  //删除数量
			"upTotal": upTotal,  //上架数量
			"downTotal": downTotal, //下架数量
			"noTotal": noTotal,   //售罄数量
			}, "获取成功", c)

	}
}
// GetOrderList
// @Tags 订单
// @Summary 分页获取列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.OrderAdminSearch true "分页获取列表"
// @Success 200 {object} yzResponse.PageResult
// @Router /order/list [post]
func GetOrderList(c *gin.Context) {
	var pageInfo request.OrderAdminSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err,supplyID :=ufv1.GetSupplierByUserId(ufv1.GetUserID(c))
	if err != nil {
		log.Log().Error("供应商不存在", zap.Any("err", err))
		yzResponse.FailWithMessage("供应商不存在", c)
		return
	}
	pageInfo.SupplierID = uint(supplyID)
	if err, list, total, WaitPayNum, WaitSendNum, WaitReceiveNum,WaitReturnNum, CompletedNum, ClosedNum, BackNum, RefundNum,CheckNum,WaitConfirmNum,ReturningNum := service3.GetAdminOrderInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		type PageResultWithNum struct {
			yzResponse.PageResult
			WaitPayNum     int64
			WaitSendNum    int64
			WaitReceiveNum int64
			CompletedNum   int64
			ClosedNum      int64
			BackNum        int64
			RefundNum      int64
			WaitReturnNum  int64
			CheckNum      int64
			WaitConfirmNum  int64
			ReturningNum int64
		}
		var pageResultWithNum PageResultWithNum
		pageResultWithNum.ReturningNum = ReturningNum

		pageResultWithNum.CheckNum = CheckNum
		pageResultWithNum.WaitConfirmNum = WaitConfirmNum

		pageResultWithNum.WaitPayNum = WaitPayNum
		pageResultWithNum.WaitSendNum = WaitSendNum
		pageResultWithNum.WaitReceiveNum = WaitReceiveNum
		pageResultWithNum.WaitReturnNum = WaitReturnNum

		pageResultWithNum.CompletedNum = CompletedNum
		pageResultWithNum.ClosedNum = ClosedNum
		pageResultWithNum.BackNum = BackNum
		pageResultWithNum.RefundNum = RefundNum
		pageResultWithNum.Page = pageInfo.Page
		pageResultWithNum.PageSize = pageInfo.PageSize
		pageResultWithNum.Total = total
		pageResultWithNum.List = list
		yzResponse.OkWithDetailed(pageResultWithNum, "获取成功", c)
	}
}


func DeleteProductList(c *gin.Context) {
	var pageInfo request.DeleteProductSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//获取供应链id
	err,gatherSupply := service3.GetGatherSupply()
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err,supplyID :=ufv1.GetSupplierByUserId(ufv1.GetUserID(c))
	if err != nil {
		log.Log().Error("供应商不存在", zap.Any("err", err))
		yzResponse.FailWithMessage("供应商不存在", c)
		return
	}
	pageInfo.AdminSupplierID = uint(supplyID)

	pageInfo.GatherSupplyID = gatherSupply.ID

	if err = service3.DeleteProductInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}
// @Tags Product
// @Summary 获取审核列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "获取审核列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取审核列表"}"
// @Router /lease/getLeaseAuditProductList [get]
//获取审核列表
func GetLeaseAuditProductList(c *gin.Context) {
	var pageInfo request.LeaseAuditProductSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err,supplyID :=ufv1.GetSupplierByUserId(ufv1.GetUserID(c))
	if err != nil {
		log.Log().Error("供应商不存在", zap.Any("err", err))
		yzResponse.FailWithMessage("供应商不存在", c)
		return
	}
	pageInfo.SuppliersId = uint(supplyID)
	if err, list, total := service3.GetLeaseAuditProductList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
func CancelAuditByIds(c *gin.Context) {
	var IDS request.ProductDetailSearch
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		log.Log().Error("取消审核失败!参数获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("参数获取失败"+err.Error(), c)
		return
	}

	if err := utils.Verify(IDS); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplier := service3.GetSupplierByUserId(ufv1.GetUserID(c))
	if err != nil {
		log.Log().Error("您不是供应商的成员，无法管理", zap.Any("err", err))
		yzResponse.FailWithMessage("您不是供应商的成员，无法管理", c)
		return
	}
	if err = service3.CancelAuditByIds(IDS, supplier.ID); err != nil {
		log.Log().Error("取消审核失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("取消审核成功,只会取消待审核的！！！", c)
	}
}

func UnscopedProductByIds(c *gin.Context) {
	var IDS request.ProductDetailSearch
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		log.Log().Error("批量恢复失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量恢复失败", c)
		return
	}
	if err := utils.Verify(IDS); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplier := service3.GetSupplierByUserId(ufv1.GetUserID(c))
	if err != nil {
		log.Log().Error("您不是供应商的成员，无法管理", zap.Any("err", err))
		yzResponse.FailWithMessage("您不是供应商的成员，无法管理", c)
		return
	}
	if err = service3.UnscopedProductByIds(IDS, supplier.ID); err != nil {
		log.Log().Error("批量恢复失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("批量恢复成功", c)
	}
}