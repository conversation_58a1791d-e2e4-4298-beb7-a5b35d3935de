package listener

import (
	"go.uber.org/zap"
	"lease/model"
	"order/mq"
	"yz-go/component/log"
	"yz-go/source"
)

func OrderStatusListener() {

	mq.PushHandles("orderStatusUpdateOrderLease", func(data mq.OrderMessage) (err error) {
		if data.MessageType == mq.Paid || data.MessageType == mq.Closed {
			var OrderLease model.OrderLease
			err = source.DB().Where("order_id = ?",data.OrderID).First(&OrderLease).Error
			if err != nil {
				//不是租赁订单跳过
				err = nil
				return
			}
			switch data.MessageType {
				case mq.Paid:
					if OrderLease.Status == model.WaitPay {
						OrderLease.Status = model.WaitSend
						err = source.DB().Where("id = ?",OrderLease.ID).Updates(&OrderLease).Error
						if err != nil {
							log.Log().Error("租赁监听订单支付改变订单状态:失败", zap.Any("err", err), zap.Any("OrderLease", OrderLease))
							//return nil
						}
					}else{
						log.Log().Error("租赁监听订单支付改变订单状态:无需变更", zap.Any("order_id", data.OrderID))
					}
					break
				case mq.Closed:
					OrderLease.Status = model.Closed
					err = source.DB().Where("id = ?",OrderLease.ID).Updates(&OrderLease).Error
					if err != nil {
						log.Log().Error("租赁监听订单支付改变订单状态:失败", zap.Any("err", err), zap.Any("OrderLease", OrderLease))
					}
					break

			}
		}
		return nil
	})
}
