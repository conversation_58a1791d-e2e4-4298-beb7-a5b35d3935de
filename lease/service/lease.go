package service

import (
	"errors"
	financeModel "finance/model"
	financeRequest "finance/request"
	"gin-vue-admin/cmd/gva"
	"github.com/chenhg5/collection"
	"github.com/jinzhu/copier"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"lease/listener"
	"lease/model"
	"lease/mq"
	"lease/request"
	"lease/setting"
	model4 "order/model"
	"order/order"
	model3 "product/model"
	"public-supply/common"
	publicModel "public-supply/model"
	model2 "region/model"
	"strconv"
	"time"
	"user/level"
	model5 "user/model"
	"yz-go/component/log"
	"yz-go/source"
	"yz-go/utils"
)

type LeaseReturnAddressList struct {
	model.LeaseReturnAddress
	Supplier Supplier `json:"supplier" gorm:"foreignkey:suppliers_id"`
}

func (LeaseReturnAddressList) TableName() string {
	return "lease_return_addresses"
}

type LeaseTenancyTermList struct {
	model.LeaseTenancyTerm
	Supplier Supplier `json:"supplier" gorm:"foreignkey:suppliers_id"`
}

func (LeaseTenancyTermList) TableName() string {
	return "lease_tenancy_terms"
}

// 如果含有time.Time 请自行import time包
type SupplierClerk struct {
	source.Model
	Sid    int `json:"sid" form:"sid" gorm:"column:sid;comment:供应商id;type:int;size:11;"`
	UserId int `json:"user_id" form:"user_id" gorm:"column:user_id;comment:账号id;type:int;size:11;"`
	JobId  int `json:"job_id" form:"job_id" gorm:"column:job_id;comment:岗位id;type:int;size:11;"`
	Uid    int `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;type:int;size:11;"`
}
type Supplier struct {
	ID         uint   `json:"id" form:"id" gorm:"primarykey"`
	Name       string `json:"name" form:"name" gorm:"column:name;comment:供应商名称;type:varchar(255);size:255;"`
	NeedVerify int    `json:"need_verify"`
	UserId     uint   `json:"user_id" gorm:"<-:create"`                                                                // 允许读和创建
	IsLease    int    `json:"is_lease" gorm:"column:is_lease;type:int;default:0;size:11;comment:供应商是否显示租赁菜单 1允许 0不允许"` //供应商是否显示租赁菜单 1允许 0不允许
}

// GetAfterSalesList
//
// @function: GetAfterSalesList
// @description: 分页获取LeaseReturnAddress记录
// @param: info request.LeaseReturnAddressSearch
// @return: err error, list interface{}, total int64
func GetLeaseReturnAddressList(info request.LeaseReturnAddressSearch) (err error, list []LeaseReturnAddressList, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&LeaseReturnAddressList{}).Preload("Supplier")
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.Keyword != "" {
		db.Where("username like '%" + info.Keyword + "%' or detail like '%" + info.Keyword + "%'")
	}
	if info.SuppliersId != 0 {
		db.Where("suppliers_id = ?", info.SuppliersId)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	err = db.Limit(limit).Order("created_at desc").Offset(offset).Find(&list).Error
	return err, list, total
}

// 创建归还地址
func CreateLeaseReturnAddress(address model.LeaseReturnAddress) (err error) {
	//如果设置默认 则其他变为非默认
	if address.IsDefault == 1 {
		source.DB().Model(&model.LeaseReturnAddress{}).Where("is_default", 1).Where("suppliers_id", address.SuppliersId).Update("is_default", 0)
	}
	err = source.DB().Create(&address).Error
	if err != nil {
		err = errors.New("创建失败" + err.Error())
	}
	return
}

// 删除归还地址
func DeleteLeaseReturnAddress(address model.LeaseReturnAddress) (err error) {
	if address.ID == 0 {
		err = errors.New("id必传")
		return
	}
	err = source.DB().Where("id = ?", address.ID).Delete(&address).Error
	if err != nil {
		err = errors.New("创建失败" + err.Error())
	}
	return
}

// 修改归还地址
func SaveLeaseReturnAddress(address model.LeaseReturnAddress) (err error) {
	if address.ID == 0 {
		err = errors.New("id必传")
		return
	}
	//如果设置默认 则其他变为非默认
	if address.IsDefault == 1 {
		source.DB().Model(&model.LeaseReturnAddress{}).Where("is_default", 1).Where("suppliers_id", address.SuppliersId).Update("is_default", 0)
	}
	var oldLeaseReturnAddress model.LeaseReturnAddress
	err = source.DB().Where("id = ?", address.ID).First(&oldLeaseReturnAddress).Error
	if err != nil {
		err = errors.New("租期不存在")
		return
	}
	address.SuppliersId = oldLeaseReturnAddress.SuppliersId
	err = source.DB().Where("id = ?", address.ID).Save(&address).Error
	if err != nil {
		err = errors.New("创建失败" + err.Error())
	}
	return
}
func GetLeaseReturnAddressById(id uint) (err error, address model.LeaseReturnAddress) {
	if id == 0 {
		err = errors.New("id必传")
		return
	}
	err = source.DB().Where("id = ?", id).First(&address).Error

	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("数据不存在")
		return
	}
	return
}

// GetAfterSalesList
//
// @function: GetAfterSalesList
// @description: 分页获取LeaseReturnAddress记录
// @param: info request.LeaseReturnAddressSearch
// @return: err error, list interface{}, total int64
func GetLeaseTenancyTermList(info request.LeaseTenancyTermSearch) (err error, list []LeaseTenancyTermList, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&LeaseTenancyTermList{}).Preload("Supplier")
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.Title != "" {
		db.Where("title like '%" + info.Title + "%'")
	}
	if info.SuppliersId != 0 {
		db.Where("suppliers_id = ?", info.SuppliersId)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	err = db.Limit(limit).Order("sort desc").Offset(offset).Find(&list).Error
	return err, list, total
}

// 创建归还地址
func CreateLeaseTenancyTerm(address model.LeaseTenancyTerm) (err error) {
	if address.NumDays == 0 {
		err = errors.New("租赁天数必须大于0")
		return
	}
	if address.PreferentialRatio > 9900 {
		err = errors.New("优惠比例不可大于99%")
		return
	}
	err = source.DB().Create(&address).Error
	if err != nil {
		err = errors.New("创建失败" + err.Error())
	}
	return
}

// 创建归还地址
func DeleteLeaseTenancyTerm(address model.LeaseTenancyTerm) (err error) {
	if address.ID == 0 {
		err = errors.New("id必传")
		return
	}

	err = source.DB().Where("id = ?", address.ID).Delete(&address).Error
	if err != nil {
		err = errors.New("创建失败" + err.Error())
	}
	return
}

// 修改归还地址
func SaveLeaseTenancyTerm(address model.LeaseTenancyTerm) (err error) {
	if address.ID == 0 {
		err = errors.New("id必传")
		return
	}
	if address.PreferentialRatio > 9900 {
		err = errors.New("优惠比例不可大于99%")
		return
	}
	if address.NumDays == 0 {
		err = errors.New("租赁天数必须大于0")
		return
	}
	var oldLeaseTenancyTerm model.LeaseTenancyTerm
	err = source.DB().Where("id = ?", address.ID).First(&oldLeaseTenancyTerm).Error
	if err != nil {
		err = errors.New("租期不存在")
		return
	}
	address.SuppliersId = oldLeaseTenancyTerm.SuppliersId
	err = source.DB().Where("id = ?", address.ID).Save(&address).Error
	if err != nil {
		err = errors.New("创建失败" + err.Error())
	}
	return
}
func GetLeaseTenancyTermById(id uint) (err error, address model.LeaseTenancyTerm) {
	if id == 0 {
		err = errors.New("id必传")
		return
	}
	err = source.DB().Where("id = ?", id).First(&address).Error

	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("数据不存在")
		return
	}
	return
}
func GetGatherSupply() (err error, gatherSupply publicModel.GatherSupply) {
	err = source.DB().Unscoped().Where("category_id = ?", common.LEASE).First(&gatherSupply).Error
	return
}

// 获取是否开启租赁插件
func GetIsLease() (isOpen int) {
	_, leaseSetting := setting.GetSysLeaseSetting("lease_setting0")
	isOpen = 0
	//存在这个插件 如果是本地默认存在  并且 是开启的状态
	if (collection.Collect(gva.GlobalAuth.MarketingPlugin).Contains(21) != false || utils.LocalEnv() != true) && leaseSetting.Value.IsOpen == 1 {
		isOpen = 1
	}

	//if utils.LocalEnv() != true {
	//	isOpen = 1
	//}

	return
}

// 获取可服务器区域 -- 分页版本
func GetLeaseCoveragesPage(search request.GetLeaseCoveragesSearch) (err error, regions []model2.Region, total int64) {
	var leaseCoverages []model.LeaseCoverage

	db := source.DB().Where("deleted_at is null")
	var regionModel model2.Region
	if search.Name != "" {
		err = source.DB().Where("name like ?", "%"+search.Name+"%").First(&regionModel).Error
		if err != nil {
			err = errors.New("区域不存在")
			return
		}
		//db.Where("")
	}
	err = db.Find(&leaseCoverages).Error
	if err != nil {
		err = errors.New("暂无任何商品设置服务区域" + err.Error())
		return
	}

	var regionsIds []int
	for _, item := range leaseCoverages {
		for _, leaseCoverageRegion := range item.LeaseCoverageRegions {
			//如果是筛选 不是这个id直接跳过
			if regionModel.ID != 0 && regionModel.Level == 1 && regionModel.ID != leaseCoverageRegion.ID {
				continue
			}

			for _, leaseCoverageRegionC := range leaseCoverageRegion.LeaseCoverageRegions {
				//如果是筛选 不是这个id直接跳过
				if regionModel.ID != 0 && regionModel.Level == 2 && regionModel.ID != leaseCoverageRegionC.ID {
					continue
				}
				for _, leaseCoverageRegionCc := range leaseCoverageRegionC.LeaseCoverageRegions {
					//如果是筛选 不是这个id直接跳过
					if regionModel.ID != 0 && regionModel.Level == 3 && regionModel.ID != leaseCoverageRegionCc.ID {
						continue
					}
					var is = 2 //是否已添加到区域id组
					for _, regionsId := range regionsIds {
						if regionsId == leaseCoverageRegionCc.ID {
							is = 1
						}
					}
					if is == 2 {
						regionsIds = append(regionsIds, leaseCoverageRegionCc.ID)
					}
				}
			}
		}
	}

	limit := search.PageSize
	offset := search.PageSize * (search.Page - 1)

	DB := source.DB().Model(&model2.Region{}).Where("id in ?", regionsIds).Where("level", 3)
	err = DB.Count(&total).Error
	err = DB.Limit(limit).Offset(offset).Find(&regions).Error
	if err != nil {
		err = errors.New("区域id错误" + err.Error())
		return
	}
	return
}

// 获取可服务器区域
func GetLeaseCoverages(search request.GetLeaseCoveragesSearch) (err error, regions []model2.Region) {
	var leaseCoverages []model.LeaseCoverage

	db := source.DB().Where("deleted_at is null")
	var regionModel model2.Region
	if search.Name != "" {
		err = source.DB().Where("name like ?", "%"+search.Name+"%").First(&regionModel).Error
		if err != nil {
			err = errors.New("区域不存在")
			return
		}
		//db.Where("")
	}
	err = db.Find(&leaseCoverages).Error
	if err != nil {
		err = errors.New("暂无任何商品设置服务区域" + err.Error())
		return
	}

	var regionsIds []int
	for _, item := range leaseCoverages {
		for _, leaseCoverageRegion := range item.LeaseCoverageRegions {
			//如果是筛选 不是这个id直接跳过
			if regionModel.ID != 0 && regionModel.Level == 1 && regionModel.ID != leaseCoverageRegion.ID {
				continue
			}

			for _, leaseCoverageRegionC := range leaseCoverageRegion.LeaseCoverageRegions {
				//如果是筛选 不是这个id直接跳过
				if regionModel.ID != 0 && regionModel.Level == 2 && regionModel.ID != leaseCoverageRegionC.ID {
					continue
				}
				for _, leaseCoverageRegionCc := range leaseCoverageRegionC.LeaseCoverageRegions {
					//如果是筛选 不是这个id直接跳过
					if regionModel.ID != 0 && regionModel.Level == 3 && regionModel.ID != leaseCoverageRegionCc.ID {
						continue
					}
					var is = 2 //是否已添加到区域id组
					for _, regionsId := range regionsIds {
						if regionsId == leaseCoverageRegionCc.ID {
							is = 1
						}
					}
					if is == 2 {
						regionsIds = append(regionsIds, leaseCoverageRegionCc.ID)
					}
				}
			}
		}
	}
	err = source.DB().Where("id in ?", regionsIds).Where("level", 3).Find(&regions).Error
	if err != nil {
		err = errors.New("区域id错误" + err.Error())
		return
	}
	return
}

type LeaseTenancyTerm struct {
	ID                uint   `json:"id" form:"id" gorm:"primarykey"`
	Title             string `json:"title" form:"title" gorm:"column:title;comment:名称;"`                                                                              // 名称
	NumDays           int    `json:"num_days" form:"num_days" gorm:"column:num_days;comment:天数;type:int(11);default:0"`                                               // 天数
	PreferentialRatio int    `json:"preferential_ratio" form:"preferential_ratio" gorm:"column:preferential_ratio;comment:preferential_ratio;type:int(11);default:0"` //优惠比例 存时*100 使用时需要/100
}

// 根据商品id获取租期
func GetLeaseTenancyTermsByProductId(productId uint) (err error, leaseTenancyTerm []LeaseTenancyTerm) {
	var product model3.Product
	err = source.DB().Where("id = ?", productId).First(&product).Error
	if err != nil {
		err = errors.New("商品不存在" + err.Error())
		return
	}
	db := source.DB().Model(&model.LeaseTenancyTerm{})
	var msg string
	if product.SupplierID != 0 {
		msg = "供应商"
	}
	db.Where("suppliers_id = ?", product.SupplierID)

	err = db.Find(&leaseTenancyTerm).Error
	if err != nil {
		err = errors.New(msg + "未设置租期,请联系管理员" + err.Error())
		return
	}
	if len(leaseTenancyTerm) == 0 {
		err = errors.New(msg + "未设置租期,请联系管理员")
		return
	}
	return
}

// 保存租赁订单
func CreateOrderLease(orderId uint, term model.LeaseTenancyTerm, orderItems []model4.OrderItem) (err error) {
	var orderLease model.OrderLease
	orderLease.OrderId = orderId
	orderLease.NumDays = term.NumDays
	orderLease.PreferentialRatio = term.PreferentialRatio
	orderLease.Status = 0
	err = source.DB().Create(&orderLease).Error
	if err == nil {
		var orderUpdate model4.Order
		orderUpdate.GatherSupplyType = common.LEASE
		err = source.DB().Where("id = ?", orderId).Updates(&orderUpdate).Error
		if err != nil {
			log.Log().Error("租赁 下单后更新订单失败,", zap.Any("err", err), zap.Any("orderId", orderId))
		}
		for _, orderItem := range orderItems {
			var orderItemLease model.OrderItemLease
			orderItemLease.OrderId = orderId
			orderItemLease.Price = orderItem.SupplyAmount
			orderItemLease.ProductId = orderItem.ProductID
			orderItemLease.SkuId = orderItem.SkuID
			orderItemLease.OrderLeaseId = orderLease.ID
			orderItemLease.OrderItemId = orderItem.ID
			err = source.DB().Create(&orderItemLease).Error
			if err != nil {
				log.Log().Error("租赁 创建子订单表失败,", zap.Any("err", err), zap.Any("orderLease", orderLease))
			}
		}
	} else {
		log.Log().Error("租赁 下单失败,", zap.Any("err", err), zap.Any("orderId", orderId))
	}
	return
}

// 申请归还
func ApplyReturn(orderId uint, thirdOrderSn string, userID uint, appId uint) (err error) {
	if orderId == 0 && thirdOrderSn == "" {
		err = errors.New("请提交订单id,或者第三方订单编号")
		return
	}
	var OrderLease model.OrderLease
	var orderModel model4.Order
	if orderId == 0 {
		err = source.DB().Where("third_order_sn = ?", thirdOrderSn).First(&orderModel).Error
	} else {
		err = source.DB().Where("id = ?", orderId).First(&orderModel).Error
	}
	if err != nil {
		err = errors.New("订单不存在" + err.Error())
		return
	}
	err = source.DB().Where("order_id = ?", orderModel.ID).First(&OrderLease).Error
	if err != nil {
		err = errors.New("订单不是租赁订单无法执行此操作" + err.Error())
		return
	}
	if userID != 0 && orderModel.UserID != userID {
		err = errors.New("订单不属于这个用户")
		return
	}
	if appId != 0 && orderModel.ApplicationID != appId {
		err = errors.New("订单不属于这个采购端")
		return
	}
	if OrderLease.Status != model.Returned && OrderLease.Status != model.RejectReturned {
		err = errors.New("订单状态是" + model.GetStatusName(OrderLease.Status) + "无法执行此操作")
		return
	}
	OrderLease.Status = model.AuditReturned
	OrderLease.ReturnApplyAt = &source.LocalTime{Time: time.Now()}
	err = source.DB().Where("id = ?", OrderLease.ID).Updates(&OrderLease).Error
	if err != nil {
		err = errors.New("申请归还失败" + err.Error())
		return
	}

	return
}

// 确认收货
func ConfirmReceive(orderId uint, thirdOrderSn string, userID uint, appId uint) (err error) {
	if orderId == 0 && thirdOrderSn == "" {
		err = errors.New("请提交订单id or 第三方订单号")
		return
	}

	var orderModel model4.Order
	if orderId == 0 {
		err = source.DB().Where("third_order_sn = ?", thirdOrderSn).First(&orderModel).Error
	} else {
		err = source.DB().Where("id = ?", orderId).First(&orderModel).Error
	}
	if err != nil {
		err = errors.New("订单不存在" + err.Error())
		return
	}

	if userID != 0 && orderModel.UserID != userID {
		err = errors.New("订单不属于这个用户")
		return
	}
	if appId != 0 && orderModel.ApplicationID != appId {
		err = errors.New("订单不属于这个用户")
		return
	}
	var OrderLease model.OrderLease
	err = source.DB().Where("order_id = ?", orderModel.ID).First(&OrderLease).Error
	if err != nil {
		err = errors.New("订单不是租赁订单无法执行此操作" + err.Error())
		return
	}

	if OrderLease.Status != model.WaitReceive {
		err = errors.New("订单状态是" + model.GetStatusName(OrderLease.Status) + "无法执行此操作")
		return
	}

	//中台订单先收货 -- 中台不能收货。不然就自动完成了

	//if err = order2.Received(orderId); err != nil {
	//	log.Log().Error("订单确认收货失败!", zap.Any("err", err))
	//	err = errors.New("订单确认收货失败"+err.Error())
	//	return
	//}

	//租赁订单状态变更
	OrderLease.StartTime = &source.LocalTime{Time: time.Now()}
	OrderLease.Status = model.Returned
	OrderLease.ReceivedAt = OrderLease.StartTime

	endTime := OrderLease.StartTime.AddDate(0, 0, +OrderLease.NumDays)
	OrderLease.EndTime = &source.LocalTime{Time: endTime}

	err = source.DB().Where("id = ?", OrderLease.ID).Updates(&OrderLease).Error
	if err != nil {
		err = errors.New("收货失败" + err.Error())
		return
	}

	return
}

// 根据商品id获取归还地址
func GetLeaseReturnAddressesByProductId(productId uint) (err error, leaseReturnAddress []model.LeaseReturnAddress) {
	var product model3.Product
	err = source.DB().Where("id = ?", productId).First(&product).Error
	if err != nil {
		err = errors.New("商品不存在")
		return
	}
	db := source.DB().Model(&model.LeaseReturnAddress{})
	var msg string
	if product.SupplierID != 0 {
		msg = "供应商"
	}
	db.Where("suppliers_id = ?", product.SupplierID)
	err = db.Find(&leaseReturnAddress).Error
	if err != nil {
		err = errors.New(msg + "未设置归还地址,请联系管理员" + err.Error())
		return
	}
	if len(leaseReturnAddress) == 0 {
		err = errors.New(msg + "未设置归还地址,请联系管理员")
		return
	}
	return
}

// 归还商品填写物流
func ReturnExpress(returnExpress request.ReturnExpress, userID uint, appId uint) (err error) {
	if returnExpress.OrderId == 0 && returnExpress.ThirdOrderSN == "" {
		err = errors.New("请提交订单id or 第三方订单编号")
		return
	}
	if returnExpress.CompanyCode == "" {
		err = errors.New("请提交物流公司编号")
		return
	}
	if returnExpress.CompanyName == "" {
		err = errors.New("请提交物流公司名称")
		return
	}
	if returnExpress.ExpressNo == "" {
		err = errors.New("请提交快递单号")
		return
	}
	if returnExpress.LeaseReturnAddressId == 0 {
		err = errors.New("请选择归还地址")
		return
	}

	var orderModel model4.Order
	if returnExpress.OrderId == 0 {
		err = source.DB().Where("third_order_sn = ?", returnExpress.ThirdOrderSN).First(&orderModel).Error
	} else {
		err = source.DB().Where("id = ?", returnExpress.OrderId).First(&orderModel).Error
	}
	if err != nil {
		err = errors.New("订单不存在" + err.Error())
		return
	}

	if userID != 0 && orderModel.UserID != userID {
		err = errors.New("订单不属于这个用户")
		return
	}
	if appId != 0 && orderModel.ApplicationID != appId {
		err = errors.New("订单不属于这个采购端")
		return
	}

	var OrderLease model.OrderLease
	err = source.DB().Where("order_id = ?", orderModel.ID).First(&OrderLease).Error
	if err != nil {
		err = errors.New("订单不是租赁订单无法执行此操作" + err.Error())
		return
	}

	if OrderLease.Status != model.ReturnedindSend {
		err = errors.New("订单状态是" + model.GetStatusName(OrderLease.Status) + "无法执行此操作")
		return
	}

	var LeaseReturnAddress model.LeaseReturnAddress
	err = source.DB().Where("id = ?", returnExpress.LeaseReturnAddressId).First(&LeaseReturnAddress).Error
	if err != nil {
		err = errors.New("归还地址不存在" + err.Error())
		return
	}
	var orderLeaseReturnAddress model.OrderLeaseReturnAddress
	err = copier.Copy(&orderLeaseReturnAddress, &LeaseReturnAddress)
	if err != nil {
		err = errors.New("记录归还地址失败" + err.Error())
		return
	}
	orderLeaseReturnAddress.ID = 0
	orderLeaseReturnAddress.CreatedAt = &source.LocalTime{Time: time.Now()}
	orderLeaseReturnAddress.UpdatedAt = &source.LocalTime{Time: time.Now()}

	err = source.DB().Create(&orderLeaseReturnAddress).Error
	if err != nil {
		err = errors.New("记录归还地址失败:" + err.Error())
		return
	}
	//租赁订单状态变更
	OrderLease.Status = model.WaitConfirmed
	OrderLease.ExpressNo = returnExpress.ExpressNo
	OrderLease.CompanyName = returnExpress.CompanyName
	OrderLease.CompanyCode = returnExpress.CompanyCode
	OrderLease.OrderLeaseReturnAddressId = orderLeaseReturnAddress.ID
	err = source.DB().Where("id = ?", OrderLease.ID).Updates(&OrderLease).Error
	if err != nil {
		err = errors.New("填写物流失败" + err.Error())
		return
	}

	return
}

// 确认发货
func Send(orderId uint) (err error) {
	if orderId == 0 {
		err = errors.New("请提交订单id参数")
		return
	}
	var orderLease model.OrderLease
	err = source.DB().Where("order_id = ?", orderId).First(&orderLease).Error
	if err != nil {
		err = errors.New("订单不存在" + err.Error())
		return
	}
	if orderLease.Status != model.WaitSend {
		err = errors.New("订单状态是" + model.GetStatusName(orderLease.Status) + "无法执行此操作")
		return
	}
	//中台订单先发货
	err = order.Send(orderLease.OrderId)
	if err != nil {
		err = errors.New("发货失败" + err.Error())
		return
	}
	orderLease.Status = model.WaitReceive
	orderLease.SentAt = &source.LocalTime{Time: time.Now()}
	err = source.DB().Model(&model.OrderLease{}).Where("id = ?", orderLease.ID).Updates(&orderLease).Error
	if err != nil {
		err = errors.New("发货失败" + err.Error())
		return
	}
	err = mq.PublishMessage(orderLease.ID, mq.LeaseOrderSend, 0)
	if err != nil {
		err = errors.New("消息推送失败" + err.Error())
		return
	}
	return
}

// 审核归还
func Audit(lease model.OrderLease) (err error) {
	if lease.OrderId == 0 {
		err = errors.New("请提交订单id参数")
		return
	}
	var orderLease model.OrderLease
	err = source.DB().Where("order_id = ?", lease.OrderId).First(&orderLease).Error
	if err != nil {
		err = errors.New("订单不存在" + err.Error())
		return
	}
	if orderLease.Status != model.AuditReturned {
		err = errors.New("订单状态是" + model.GetStatusName(orderLease.Status) + "无法执行此操作")
		return
	}
	switch lease.Status {
	case model.RejectReturned: //驳回
		orderLease.Status = model.RejectReturned
		break
	case model.ReturnedindSend: //同意，需要退货
		orderLease.Status = model.ReturnedindSend
		orderLease.ReturnApplySuccessAt = &source.LocalTime{Time: time.Now()}
		break
	case model.WaitConfirmed: //同步不需要退货
		orderLease.Status = model.WaitConfirmed
		orderLease.ReturnApplySuccessAt = &source.LocalTime{Time: time.Now()}
		break
	default:
		err = errors.New("审核状态提交错误")
		return
	}
	orderLease.AuditAt = &source.LocalTime{Time: time.Now()}
	err = source.DB().Model(&model.OrderLease{}).Where("id = ?", orderLease.ID).Updates(&orderLease).Error
	if err != nil {
		err = errors.New("审核失败" + err.Error())
		return
	}
	if lease.Status == model.RejectReturned {
		err = mq.PublishMessage(orderLease.ID, mq.LeaseOrderRejectAudit, 0)
	} else {
		//无需物流直接完成
		if lease.Status == model.WaitConfirmed {
			err = ConfirmedCompleted(lease.OrderId)
		} else {
			err = mq.PublishMessage(orderLease.ID, mq.LeaseOrderPassTheAudit, 0)
		}
	}
	if err != nil {
		err = errors.New("推送消息失败" + err.Error())
		return
	}
	return
}

// 确认归还 租赁状态变为完成，中台订单同步完成
func ConfirmedCompleted(orderId uint) (err error) {
	if orderId == 0 {
		err = errors.New("请提交订单id参数")
		return
	}
	var orderLease model.OrderLease
	err = source.DB().Where("order_id = ?", orderId).First(&orderLease).Error
	if err != nil {
		err = errors.New("订单不存在" + err.Error())
		return
	}
	if orderLease.Status != model.WaitConfirmed {
		err = errors.New("订单状态是" + model.GetStatusName(orderLease.Status) + "无法执行此操作")
		return
	}
	//兼容之前的，租赁调整为租赁发货时中台订单也同步发货，这里是兼容之前已经发货但是中台订单没有发货的情况 start
	var orderData order.Order
	err = source.DB().Model(&order.Order{}).Where("id = ?", orderLease.OrderId).First(&orderData).Error
	if err != nil {
		err = errors.New("中台订单不存在" + err.Error())
		return
	}
	if orderData.Status == 1 {
		//中台订单先发货
		err = order.Send(orderLease.OrderId)
		if err != nil {
			err = errors.New("发货失败" + err.Error())
			return
		}
	}
	//end

	err = order.Received(orderLease.OrderId)
	if err != nil {
		err = errors.New("确认失败:" + err.Error())
		return
	}
	orderLease.Status = model.Completed
	orderLease.ReturnAt = &source.LocalTime{Time: time.Now()}
	err = source.DB().Model(&model.OrderLease{}).Where("id = ?", orderLease.ID).Updates(&orderLease).Error
	if err != nil {
		err = errors.New("确认失败" + err.Error())
		return
	}

	err = mq.PublishMessage(orderLease.ID, mq.LeaseOrderComplete, 0)

	if err != nil {
		err = errors.New("推送消息失败" + err.Error())
		return
	}

	return
}

// 根据商品id获取租赁 协议
func GetSysLeaseSettingLeasingAgreement(productId uint) (err error, text string) {
	if productId == 0 {
		err = errors.New("请提交商品id")
		return
	}
	var product model3.Product
	err = source.DB().Where("id = ?", productId).First(&product).Error
	if err != nil {
		err = errors.New("商品不存在" + err.Error())
		return
	}
	err, leaseSetting := setting.GetSysLeaseSetting("lease_setting" + strconv.Itoa(int(product.SupplierID)))
	if err != nil {
		err = errors.New("供应商未设置配置，请联系管理员")
		return
	}
	text = leaseSetting.Value.LeasingAgreement
	return
}

// 商品最终金额，供应商id  检验是否大于起租金额
func VerifyLease(price uint, supplierId uint) (err error) {
	err, leaseSetting := setting.GetSysLeaseSetting("lease_setting" + strconv.Itoa(int(supplierId)))
	if err != nil {
		err = errors.New("供应商未设置配置，请联系管理员")
		return
	}
	if leaseSetting.Value.StartAmount > price {
		err = errors.New("购买商品金额少于起租金额")
		return
	}
	return
}

// 租期改变之后计算价格 与技术服务费 --计算一个商品的金额和技术服务费
func OrderPrice(productId uint, skusId uint, days int, preferentialRatio int, userId uint) (err error, price uint, fee uint) {
	price = 0
	var user model5.User
	err = source.DB().Where("id = ?", userId).First(&user).Error
	if err != nil {
		err = errors.New("用户错误")
		return
	}
	var sku model3.Sku
	err = source.DB().Where("id = ?", skusId).First(&sku).Error
	if err != nil {
		err = errors.New("规格不存在")
		return
	}
	var product model3.Product
	err = source.DB().Where("id = ?", sku.ProductID).First(&product).Error
	if err != nil {
		err = errors.New("商品不存在")
		return
	}

	levelDiscountPercent := 10000
	// levelDiscountPercent的单位是万分之一
	var levelDiscountPrice uint
	if user.LevelID > 0 {
		if product.UserPriceSwitch == 1 {
			var calculateRes bool
			levelDiscountPrice, _, calculateRes = product.UserPrice.GetProductLevelDiscountPrice(sku.Price, user.LevelID)
			// 商品没有设置该等级，使用默认折扣
			if calculateRes == false {
				// 等级折扣
				err, levelDiscountPercent, _ = level.GetLevelDiscountPercent(user.LevelID)
				if err != nil {
					return
				}
				err, levelDiscountPrice = level.GetLevelDiscountAmount(sku.Price, sku.CostPrice, levelDiscountPercent)
				if err != nil {
					return
				}
			}
		} else {
			// 等级折扣
			err, levelDiscountPercent, _ = level.GetLevelDiscountPercent(user.LevelID)
			if err != nil {
				return
			}
			err, levelDiscountPrice = level.GetLevelDiscountAmount(sku.Price, sku.CostPrice, levelDiscountPercent)
			if err != nil {
				return
			}
		}
	} else {
		levelDiscountPrice = sku.Price * uint(levelDiscountPercent) / 10000
	}
	price = LeasePrice(levelDiscountPrice, days, preferentialRatio)

	var feePercent int
	//var enable int
	percent := 0
	err, _, feePercent = level.GetLevelTechnicalServicesFeePercent(user.LevelID)
	if err != nil {
		return
	}
	if feePercent > 0 {
		percent = feePercent
	}
	/*if enable == 1 {
		percent = feePercent
	} else {
		var tradeSetting tradeModel.TradeValue
		err, tradeSetting = tradeModel.GetTradeSetting()
		if err != nil {
			return
		}
		percent = tradeSetting.ServerRadio
	}*/

	fee = price * uint(percent) / 10000
	return
}

// 租赁价格计算  （会员价格-会员价格*优惠比例)*租赁天数   -- 第一次除100因为比例存储时*100变为整数了 --第二次除因为是比例
func LeasePrice(Amount uint, days int, preferentialRatio int) (price uint) {
	price = (Amount - Amount*uint(preferentialRatio)/100/100) * uint(days)
	if price < 0 {
		price = 0
	}
	return
}

// 根据id查询 租期详情或者根据天数拼接租期详情
func GetLeaseTenancyTermByIdOrDays(leaseTenancyTermsId uint, days int) (err error, leaseTenancyTerm model.LeaseTenancyTerm) {
	//获取租期模型
	if leaseTenancyTermsId == 0 && days == 0 {
		err = errors.New("请选择租期.或者自定义租赁时间")
		return
	}

	if leaseTenancyTermsId != 0 {
		err = source.DB().Where("id = ?", leaseTenancyTermsId).First(&leaseTenancyTerm).Error
		if err != nil {
			err = errors.New("租期不存在")
		}
	} else {
		leaseTenancyTerm.NumDays = days
		leaseTenancyTerm.PreferentialRatio = 0 //默认无优惠
	}
	return
}

/**
消息成功之后商城通知中台 中台进行改变状态
*/

func MessageSuccess(appId uint, messageSuccess listener.LeaseOrderRequest) (err error) {
	db := source.DB().Where("application_id = ?", appId)

	if messageSuccess.LeaseOrderID != 0 {
		db.Where("order_lease_id = ?", messageSuccess.LeaseOrderID)
	}
	if messageSuccess.ThirdOrderSn != "" {
		db.Where("third_order_sn = ?", messageSuccess.ThirdOrderSn)
	}
	err = db.Where("after_sales_message_type = ?", messageSuccess.MessageType).Updates(&model.OrderLeasePushMessage{
		Status: 3,
	}).Error
	if err != nil {
		return
	}
	return
}

/*
*
获取所有状态不是成功的消息
*/
func GetMessageError(appId uint, messageSuccess listener.LeaseOrderRequest) (err error, message []model.OrderLeasePushMessage) {
	db := source.DB().Where("application_id = ?", appId).Limit(100).Where("status in (-1,1,2)").Order("created_at desc")

	if messageSuccess.MessageType != "" {
		db.Where("lease_order_message_type = ?", messageSuccess.MessageType)
	}

	if messageSuccess.ThirdOrderSn != "" {
		db.Where("third_order_sn = ?", messageSuccess.ThirdOrderSn)
	}

	err = db.Find(&message).Error
	if err != nil {
		return
	}
	return
}

// 根据第三方订单号获取租赁订单详情
func GetOrderLeaseByThirdOrderSn(thirdOrderSn string) (err error, lease model.OrderLease) {
	if thirdOrderSn == "" {
		err = errors.New("请提交订单号")
		return
	}
	var orderModel model4.Order
	err = source.DB().Where("third_order_sn = ?", thirdOrderSn).First(&orderModel).Error
	if err != nil {
		err = errors.New("订单不存在" + err.Error())
		return
	}
	err = source.DB().Where("order_id = ?", orderModel.ID).First(&lease).Error
	if err != nil {
		err = errors.New("租赁不存在" + err.Error())
		return
	}
	return
}

func GetPurchaseBalanceList(info financeRequest.UserBalanceSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&financeModel.PurchasingBalance{}).Joins("INNER join pay_infos on pay_infos.pay_sn = purchasing_balances.pay_sn").Joins("INNER join orders on orders.pay_info_id = pay_infos.id").Where("orders.gather_supply_type", common.LEASE)
	var PurchasingBalance []financeModel.PurchasingBalance
	// 如果有条件搜索 下方会自动创建搜索语句
	var joinWhere string
	joinWhere = "INNER join users on users.id = purchasing_balances.uid"

	if info.Type == 1 && info.Username != "" {

		joinWhere = "INNER join users on users.id = purchasing_balances.uid and users.id=" + info.Username
	}
	if info.Type == 2 && info.Username != "" {
		joinWhere = "INNER join users on users.id = purchasing_balances.uid and users.nick_name LIKE " + "'%" + info.Username + "%'"

	}
	if info.Type == 3 && info.Username != "" {
		joinWhere = "INNER join users on users.id = purchasing_balances.uid and users.username LIKE " + "'%" + info.Username + "%'"

	}
	if info.Type == 4 && info.Username != "" {
		joinWhere = "INNER join users on users.id = purchasing_balances.uid and users.mobile=" + info.Username

	}

	if info.Level > 0 {
		joinWhere = "INNER join users on users.id = purchasing_balances.uid and users.level_id=" + strconv.Itoa(info.Level)

	}

	if info.User.ID > 0 {
		db = db.Where("purchasing_balances.uid = ?", info.User.ID)
	}

	if info.PetSupplierID > 0 {
		db = db.Where("purchasing_balances.pet_supplier_id = ?", info.PetSupplierID)
	}
	db = db.Where("purchasing_balances.pay_type <= ?", 2)

	if info.MinBalance > 0 && info.MaxBalance > 0 {

		db = db.Where("purchasing_balances.balance BETWEEN ? AND ?", info.MinBalance, info.MaxBalance)

	}

	//if info.TimeS != "" && info.TimeE != "" {
	//
	//	db = db.Where("purchasing_balances.created_at BETWEEN ? AND ?", info.TimeS, info.TimeE)
	//
	//}
	//客户需要只填写一个时间就可以筛选
	if info.TimeS != "" {
		db = db.Where("purchasing_balances.created_at  >= ?", info.TimeS)
	}
	if info.TimeE != "" {
		db = db.Where("purchasing_balances.created_at  <= ?", info.TimeE)
	}
	if info.BusinessType > 0 {
		db = db.Where("purchasing_balances.business_type = ?", info.BusinessType)

	}
	db = db.Where("purchasing_balances.pay_type < 3")

	err = db.Joins(joinWhere).Count(&total).Error
	err = db.Select("purchasing_balances.*").Limit(limit).Offset(offset).Preload("User").Preload("Supplier").Order("purchasing_balances.id desc").Find(&PurchasingBalance).Error
	return err, PurchasingBalance, total
}
func GetGatherSupplieLease() (id uint) {
	var supply publicModel.GatherSupply
	err := source.DB().Unscoped().Where("category_id = ?", common.LEASE).First(&supply).Error
	if err != nil {
		err = nil
		id = 0
		return
	}
	id = supply.ID
	return
}
