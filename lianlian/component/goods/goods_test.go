package goods

import (
	lmodel "lianlian/model"
	"public-supply/callback"
	"public-supply/model"
	"public-supply/request"
	"reflect"
	"sync"
	"testing"
	"yz-go/source"
)

func TestForGetYzhListCode(t *testing.T) {

	var l LianLian

	l.UpdateGoodsRun()

	return
}

func TestLianLian_BatchGetGoodsDetails(t *testing.T) {
	type fields struct {
		dat       *model.SupplySetting
		SupplyID  uint
		Key       string
		Http      string
		Domain    string
		ChannelId string
	}
	type args struct {
		ids string
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantErr  error
		wantData map[int]model.GoodsDetail
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			li := &LianLian{
				dat:       tt.fields.dat,
				SupplyID:  tt.fields.SupplyID,
				Key:       tt.fields.Key,
				Http:      tt.fields.Http,
				Domain:    tt.fields.Domain,
				ChannelId: tt.fields.ChannelId,
			}
			gotErr, gotData := li.BatchGetGoodsDetails(tt.args.ids)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.<PERSON>rf("BatchGetGoodsDetails() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if !reflect.DeepEqual(gotData, tt.wantData) {
				t.Errorf("BatchGetGoodsDetails() gotData = %v, want %v", gotData, tt.wantData)
			}
		})
	}
}

func TestLianLian_CommodityAssembly(t *testing.T) {

}

func TestLianLian_CommodityAssemblyLocal(t *testing.T) {

}

func TestLianLian_CommodityAssemblyLocalUpdate(t *testing.T) {

}

func TestLianLian_DeleteGoods(t *testing.T) {
	type fields struct {
		dat       *model.SupplySetting
		SupplyID  uint
		Key       string
		Http      string
		Domain    string
		ChannelId string
	}
	type args struct {
		id uint
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &LianLian{
				dat:       tt.fields.dat,
				SupplyID:  tt.fields.SupplyID,
				Key:       tt.fields.Key,
				Http:      tt.fields.Http,
				Domain:    tt.fields.Domain,
				ChannelId: tt.fields.ChannelId,
			}
			if err := y.DeleteGoods(tt.args.id); (err != nil) != tt.wantErr {
				t.Errorf("DeleteGoods() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestLianLian_GetCateLoop(t *testing.T) {
	type fields struct {
		dat       *model.SupplySetting
		SupplyID  uint
		Key       string
		Http      string
		Domain    string
		ChannelId string
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &LianLian{
				dat:       tt.fields.dat,
				SupplyID:  tt.fields.SupplyID,
				Key:       tt.fields.Key,
				Http:      tt.fields.Http,
				Domain:    tt.fields.Domain,
				ChannelId: tt.fields.ChannelId,
			}
			y.GetCateLoop()
		})
	}
}

func TestLianLian_GetCategory(t *testing.T) {
	type fields struct {
		dat       *model.SupplySetting
		SupplyID  uint
		Key       string
		Http      string
		Domain    string
		ChannelId string
	}
	type args struct {
		infof request.GetCategorySearch
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantErr  error
		wantData interface{}
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &LianLian{
				dat:       tt.fields.dat,
				SupplyID:  tt.fields.SupplyID,
				Key:       tt.fields.Key,
				Http:      tt.fields.Http,
				Domain:    tt.fields.Domain,
				ChannelId: tt.fields.ChannelId,
			}
			gotErr, gotData := y.GetCategory(tt.args.infof)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("GetCategory() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if !reflect.DeepEqual(gotData, tt.wantData) {
				t.Errorf("GetCategory() gotData = %v, want %v", gotData, tt.wantData)
			}
		})
	}
}

func TestLianLian_GetCategoryChild(t *testing.T) {
	type fields struct {
		dat       *model.SupplySetting
		SupplyID  uint
		Key       string
		Http      string
		Domain    string
		ChannelId string
	}
	type args struct {
		pid  int
		info request.GetCategoryChild
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantErr  error
		wantData interface{}
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &LianLian{
				dat:       tt.fields.dat,
				SupplyID:  tt.fields.SupplyID,
				Key:       tt.fields.Key,
				Http:      tt.fields.Http,
				Domain:    tt.fields.Domain,
				ChannelId: tt.fields.ChannelId,
			}
			gotErr, gotData := y.GetCategoryChild(tt.args.pid, tt.args.info)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("GetCategoryChild() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if !reflect.DeepEqual(gotData, tt.wantData) {
				t.Errorf("GetCategoryChild() gotData = %v, want %v", gotData, tt.wantData)
			}
		})
	}
}

func TestLianLian_GetCategoryChildSub(t *testing.T) {
	type fields struct {
		dat       *model.SupplySetting
		SupplyID  uint
		Key       string
		Http      string
		Domain    string
		ChannelId string
	}
	type args struct {
		pid  int
		info request.GetCategoryChild
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantErr  error
		wantData interface{}
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &LianLian{
				dat:       tt.fields.dat,
				SupplyID:  tt.fields.SupplyID,
				Key:       tt.fields.Key,
				Http:      tt.fields.Http,
				Domain:    tt.fields.Domain,
				ChannelId: tt.fields.ChannelId,
			}
			gotErr, gotData := y.GetCategoryChildSub(tt.args.pid, tt.args.info)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("GetCategoryChildSub() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if !reflect.DeepEqual(gotData, tt.wantData) {
				t.Errorf("GetCategoryChildSub() gotData = %v, want %v", gotData, tt.wantData)
			}
		})
	}
}

func TestLianLian_GetGoods(t *testing.T) {
	type fields struct {
		dat       *model.SupplySetting
		SupplyID  uint
		Key       string
		Http      string
		Domain    string
		ChannelId string
	}
	type args struct {
		info request.GetGoodsSearch
	}
	tests := []struct {
		name            string
		fields          fields
		args            args
		wantErr         error
		wantData        interface{}
		wantTotal       int64
		wantServerRatio int
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			li := &LianLian{
				dat:       tt.fields.dat,
				SupplyID:  tt.fields.SupplyID,
				Key:       tt.fields.Key,
				Http:      tt.fields.Http,
				Domain:    tt.fields.Domain,
				ChannelId: tt.fields.ChannelId,
			}
			gotErr, gotData, gotTotal, gotServerRatio := li.GetGoods(tt.args.info)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("GetGoods() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if !reflect.DeepEqual(gotData, tt.wantData) {
				t.Errorf("GetGoods() gotData = %v, want %v", gotData, tt.wantData)
			}
			if gotTotal != tt.wantTotal {
				t.Errorf("GetGoods() gotTotal = %v, want %v", gotTotal, tt.wantTotal)
			}
			if gotServerRatio != tt.wantServerRatio {
				t.Errorf("GetGoods() gotServerRatio = %v, want %v", gotServerRatio, tt.wantServerRatio)
			}
		})
	}
}

func TestLianLian_GetGroup(t *testing.T) {
	type fields struct {
		dat       *model.SupplySetting
		SupplyID  uint
		Key       string
		Http      string
		Domain    string
		ChannelId string
	}
	tests := []struct {
		name     string
		fields   fields
		wantErr  error
		wantData interface{}
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &LianLian{
				dat:       tt.fields.dat,
				SupplyID:  tt.fields.SupplyID,
				Key:       tt.fields.Key,
				Http:      tt.fields.Http,
				Domain:    tt.fields.Domain,
				ChannelId: tt.fields.ChannelId,
			}
			gotErr, gotData := y.GetGroup()
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("GetGroup() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if !reflect.DeepEqual(gotData, tt.wantData) {
				t.Errorf("GetGroup() gotData = %v, want %v", gotData, tt.wantData)
			}
		})
	}
}

func TestLianLian_GetSupplyBalance(t *testing.T) {
	type fields struct {
		dat       *model.SupplySetting
		SupplyID  uint
		Key       string
		Http      string
		Domain    string
		ChannelId string
	}
	type args struct {
		GatherSupplyID uint
	}
	tests := []struct {
		name        string
		fields      fields
		args        args
		wantErr     error
		wantBalance interface{}
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &LianLian{
				dat:       tt.fields.dat,
				SupplyID:  tt.fields.SupplyID,
				Key:       tt.fields.Key,
				Http:      tt.fields.Http,
				Domain:    tt.fields.Domain,
				ChannelId: tt.fields.ChannelId,
			}
			gotErr, gotBalance := y.GetSupplyBalance(tt.args.GatherSupplyID)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("GetSupplyBalance() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if !reflect.DeepEqual(gotBalance, tt.wantBalance) {
				t.Errorf("GetSupplyBalance() gotBalance = %v, want %v", gotBalance, tt.wantBalance)
			}
		})
	}
}

func TestLianLian_GetToken(t *testing.T) {
	type fields struct {
		dat       *model.SupplySetting
		SupplyID  uint
		Key       string
		Http      string
		Domain    string
		ChannelId string
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &LianLian{
				dat:       tt.fields.dat,
				SupplyID:  tt.fields.SupplyID,
				Key:       tt.fields.Key,
				Http:      tt.fields.Http,
				Domain:    tt.fields.Domain,
				ChannelId: tt.fields.ChannelId,
			}
			if err := y.GetToken(); (err != nil) != tt.wantErr {
				t.Errorf("GetToken() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestLianLian_GoodsPriceAlert(t *testing.T) {
	type fields struct {
		dat       *model.SupplySetting
		SupplyID  uint
		Key       string
		Http      string
		Domain    string
		ChannelId string
	}
	type args struct {
		GoodsData callback.GoodsCallBack
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &LianLian{
				dat:       tt.fields.dat,
				SupplyID:  tt.fields.SupplyID,
				Key:       tt.fields.Key,
				Http:      tt.fields.Http,
				Domain:    tt.fields.Domain,
				ChannelId: tt.fields.ChannelId,
			}
			if err := y.GoodsPriceAlert(tt.args.GoodsData); (err != nil) != tt.wantErr {
				t.Errorf("GoodsPriceAlert() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestLianLian_GoodsStorageAdd(t *testing.T) {
	type fields struct {
		dat       *model.SupplySetting
		SupplyID  uint
		Key       string
		Http      string
		Domain    string
		ChannelId string
	}
	type args struct {
		ids      []int
		supplyId uint
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantErr  error
		wantInfo interface{}
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &LianLian{
				dat:       tt.fields.dat,
				SupplyID:  tt.fields.SupplyID,
				Key:       tt.fields.Key,
				Http:      tt.fields.Http,
				Domain:    tt.fields.Domain,
				ChannelId: tt.fields.ChannelId,
			}
			gotErr, gotInfo := y.GoodsStorageAdd(tt.args.ids, tt.args.supplyId)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("GoodsStorageAdd() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if !reflect.DeepEqual(gotInfo, tt.wantInfo) {
				t.Errorf("GoodsStorageAdd() gotInfo = %v, want %v", gotInfo, tt.wantInfo)
			}
		})
	}
}

func TestLianLian_ImportGoodsRun(t *testing.T) {
	type fields struct {
		dat       *model.SupplySetting
		SupplyID  uint
		Key       string
		Http      string
		Domain    string
		ChannelId string
	}
	type args struct {
		info request.GetGoodsSearch
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantErr  error
		wantData interface{}
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &LianLian{
				dat:       tt.fields.dat,
				SupplyID:  tt.fields.SupplyID,
				Key:       tt.fields.Key,
				Http:      tt.fields.Http,
				Domain:    tt.fields.Domain,
				ChannelId: tt.fields.ChannelId,
			}
			gotErr, gotData := y.ImportGoodsRun(tt.args.info)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("ImportGoodsRun() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if !reflect.DeepEqual(gotData, tt.wantData) {
				t.Errorf("ImportGoodsRun() gotData = %v, want %v", gotData, tt.wantData)
			}
		})
	}
}

func TestLianLian_ImportLocalGoods(t *testing.T) {

}

func TestLianLian_InitGoods(t *testing.T) {
	type fields struct {
		dat       *model.SupplySetting
		SupplyID  uint
		Key       string
		Http      string
		Domain    string
		ChannelId string
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &LianLian{
				dat:       tt.fields.dat,
				SupplyID:  tt.fields.SupplyID,
				Key:       tt.fields.Key,
				Http:      tt.fields.Http,
				Domain:    tt.fields.Domain,
				ChannelId: tt.fields.ChannelId,
			}
			if err := y.InitGoods(); (err != nil) != tt.wantErr {
				t.Errorf("InitGoods() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestLianLian_InitSetting(t *testing.T) {
	type fields struct {
		dat       *model.SupplySetting
		SupplyID  uint
		Key       string
		Http      string
		Domain    string
		ChannelId string
	}
	type args struct {
		gatherSupplyID uint
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &LianLian{
				dat:       tt.fields.dat,
				SupplyID:  tt.fields.SupplyID,
				Key:       tt.fields.Key,
				Http:      tt.fields.Http,
				Domain:    tt.fields.Domain,
				ChannelId: tt.fields.ChannelId,
			}
			if err := y.InitSetting(tt.args.gatherSupplyID); (err != nil) != tt.wantErr {
				t.Errorf("InitSetting() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestLianLian_RunConcurrent(t *testing.T) {
	type fields struct {
		dat       *model.SupplySetting
		SupplyID  uint
		Key       string
		Http      string
		Domain    string
		ChannelId string
	}
	type args struct {
		wg   *sync.WaitGroup
		info request.GetCategorySearch
		i    int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			li := &LianLian{
				dat:       tt.fields.dat,
				SupplyID:  tt.fields.SupplyID,
				Key:       tt.fields.Key,
				Http:      tt.fields.Http,
				Domain:    tt.fields.Domain,
				ChannelId: tt.fields.ChannelId,
			}
			if err := li.RunConcurrent(tt.args.wg, tt.args.info, tt.args.i); (err != nil) != tt.wantErr {
				t.Errorf("RunConcurrent() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestLianLian_RunGoods(t *testing.T) {
	type fields struct {
		dat       *model.SupplySetting
		SupplyID  uint
		Key       string
		Http      string
		Domain    string
		ChannelId string
	}
	type args struct {
		maps    []int64
		orderPN string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			y := &LianLian{
				dat:       tt.fields.dat,
				SupplyID:  tt.fields.SupplyID,
				Key:       tt.fields.Key,
				Http:      tt.fields.Http,
				Domain:    tt.fields.Domain,
				ChannelId: tt.fields.ChannelId,
			}
			y.RunGoods(tt.args.maps, tt.args.orderPN)
		})
	}
}

func TestLianLian_CommodityAssemblyLocalUpdate1(t *testing.T) {

	y := &LianLian{}
	var detail lmodel.LianProductNew

	source.DB().Where("id=?", 12).First(&detail)

	y.CommodityAssemblyLocalUpdate(detail, 1, 2, 3)

}

func TestLianLian_HttpTools(t *testing.T) {
	type fields struct {
		dat       *model.SupplySetting
		SupplyID  uint
		Key       string
		Http      string
		Domain    string
		ChannelId string
	}
	type args struct {
		citem lmodel.CityJson
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{name: ""},
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			l := &LianLian{
				dat:       tt.fields.dat,
				SupplyID:  tt.fields.SupplyID,
				Key:       tt.fields.Key,
				Http:      tt.fields.Http,
				Domain:    tt.fields.Domain,
				ChannelId: tt.fields.ChannelId,
			}
			l.InitSetting(62)
			l.HttpTools(1, 510600)
		})
	}
}
