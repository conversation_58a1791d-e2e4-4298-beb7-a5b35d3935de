package cron

import (
	"go.uber.org/zap"
	"lijing-supply/mq"
	model4 "product/model"
	"public-supply/model"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func PushStockHandle() {
	task := cron.Task{
		Key:  "cron_lijing_stock",
		Name: "丽晶轮询库存",
		//Spec: "*/1 * * * * *",
		Spec: "0 20 */1 * * *",
		Handle: func(task cron.Task) {
			OrderStock()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func OrderStock() {

	log.Log().Info("丽晶定时开始更新库存")
	var gatherSupply []model.GatherSupply

	err := source.DB().Where("category_id = 13").Find(&gatherSupply).Error
	if err != nil {
		return
	}
	for _, supply := range gatherSupply {

		err = BatchUpdateStock(supply.ID)
		if err != nil {
			log.Log().Info("丽晶定时发货请求失败:"+err.Error(), zap.Any("id", supply.ID))
			return
		}
	}

	return
}

func BatchUpdateStock(gatherSupplyID uint) (err error) {
	var productCount int64
	err = source.DB().Model(&model4.Product{}).Where("gather_supply_id = ?", gatherSupplyID).Count(&productCount).Error
	if err != nil {
		return
	}
	var pageSize = 20
	var pages = productCount/int64(pageSize) + 1
	var i int64

	for i = 1; i <= pages; i++ {
		var productIds []uint
		err = source.DB().Model(&model4.Product{}).Where("gather_supply_id = ?", gatherSupplyID).Offset((int(i)-1)*pageSize).Limit(pageSize).Pluck("id", &productIds).Error
		if err != nil {
			return
		}
		err = mq.PublishMessage(productIds, gatherSupplyID)
		if err != nil {
			return err
		}
	}

	return

}
