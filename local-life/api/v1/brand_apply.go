package v1

import (
	"github.com/gin-gonic/gin"
	"local-life/request"
	"local-life/service"
	llUtils "local-life/utils"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

// GetBrandApplyList 获取品牌入驻申请列表
func GetBrandApplyList(c *gin.Context) {
	var pageInfo request.BrandApplySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetBrandApplyList(pageInfo); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// FindBrandApply 获取品牌入驻申请详情
func FindBrandApply(c *gin.Context) {
	// brand_id当br_id使用
	var req request.FindBrandParams
	err := c.ShouldBindQuery(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := utils.GVerify(req, llUtils.BrandVerify); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// brand_id当br_id使用
	if err, brand := service.FindBrandReview(req.BrandID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"brand": brand}, c)
	}
}

// BrandApplyAudit 品牌入驻申请审核 通过/驳回（驳回理由）
func BrandApplyAudit(c *gin.Context) {
	var req request.BrandAuditParams
	err := c.ShouldBindJSON(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.BrandApplyAudit(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
}
