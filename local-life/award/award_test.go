package award

import (
	"fmt"
	"go.uber.org/zap"
	"testing"
	"yz-go/component/log"
)

func TestGenerateAward(t *testing.T) {

	log.Log().Error("localLifeSettleAward-结算失败", zap.Any("err", "asd"), zap.Uint("aid", 123))

	storeMap := make(map[uint]uint)
	var stores []Store
	stores = append(stores, Store{ID: 1, UserID: 1})
	stores = append(stores, Store{ID: 1, UserID: 1})
	stores = append(stores, Store{ID: 2, UserID: 2})
	for _, store := range stores {
		storeMap[store.ID] = store.UserID
	}
	fmt.Println(storeMap)

	var awardAmount, amount, usage uint
	amount = 10000
	usage = 3
	awardAmount = amount / usage
	fmt.Println(awardAmount)
}

type Store struct {
	ID     uint
	UserID uint
}
