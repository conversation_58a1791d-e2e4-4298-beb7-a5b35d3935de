package model

import (
	"github.com/satori/go.uuid"
	"gorm.io/gorm"
	"yz-go/source"
)

// LocalLifeBrand 本地生活品牌
type LocalLifeBrand struct {
	source.Model
	// --主体信息--
	// 公司/组织名称 string
	Name string `json:"name" form:"name" gorm:"column:name;comment:公司/组织名称;"`
	// 证件类型 int 【1:企业营业执照，2:事业单位法人证书，3:民办非企业单位登记证书，4:律师事务所营业许可证，5:社会团体法人登记证书，6:基层群众性自治组织特别法人统一社会信用代码证书，7:农业专业合作经济经济组织营业执照】
	CertificateType int `json:"certificate_type" form:"certificate_type" gorm:"column:certificate_type;comment:证件类型｜1:企业营业执照，2:事业单位法人证书，3:民办非企业单位登记证书，4:律师事务所营业许可证，5:社会团体法人登记证书，6:基层群众性自治组织特别法人统一社会信用代码证书，7:农业专业合作经济经济组织营业执照;"`
	// 营业执照编码
	CertificateCode string `json:"certificate_code" form:"certificate_code" gorm:"column:certificate_code;comment:营业执照编码;"`
	// 企业营业执照 img string
	BusinessLicenseImg string `json:"business_license_img" form:"business_license_img" gorm:"column:business_license_img;comment:企业营业执照;"`
	// 登陆账号id
	SysUID uint `json:"sys_uid" form:"sys_uid" gorm:"column:sys_uid;comment:登陆账号id;"`
	// --账号信息--
	// 联系人姓名 string  ***列表搜索项
	RealName string `json:"real_name" form:"real_name" gorm:"column:real_name;comment:联系人姓名;"`
	// 联系人电话 string  ***列表搜索项
	Mobile string `json:"mobile" form:"mobile" gorm:"column:mobile;comment:联系人电话;"`
	// 选择会员 uint 【会员表id】  ***列表搜索项
	UID uint `json:"uid" form:"uid" gorm:"column:uid;comment:中台会员id;"`
	// 账号
	Username string `json:"username" form:"username" gorm:"column:username;comment:账号;"`
	// 密码
	Password string `json:"password" form:"password" gorm:"column:password;comment:密码;"`

	// --经营信息--
	// 商品审核 int 【是否需要审核】
	ProductAudit int `json:"product_audit" form:"product_audit" gorm:"column:product_audit;default:1;comment:商品审核|1需要审核2不需要审核;"`
	// 结算方式
	SettlementMethod int `json:"settlement_method" form:"settlement_method" gorm:"column:settlement_method;default:1;comment:结算方式|1:中台代发2:线下结算;"`

	// --状态信息--
	// 状态 int 【启用，禁用】  ***列表搜索项
	Status int `json:"status" form:"status" gorm:"column:status;default:1;comment:状态|1启用2禁用;"`
	// 资料变动审核状态 int 【1待审核，2已审核，3已驳回】***列表搜索项
	InfoAuditStatus int `json:"info_audit_status" form:"info_audit_status" gorm:"column:info_audit_status;default:2;comment:资料变动审核状态|1待审核2已审核3已驳回;"`
	// 品牌入驻申请审核状态 int 【待审核，已审核】
	AuditStatus int `json:"audit_status" form:"audit_status" gorm:"column:audit_status;default:1;comment:审核状态|1待审核2已审核3驳回;"`

	// --渠道来源信息--
	// 来源 int 【自营】
	Source uint `json:"source" form:"source" gorm:"column:source;comment:来源|0自营;"`
	// 来源ID
	SourceID uint `json:"source_id" form:"source_id" gorm:"column:source_id;comment:来源ID;"`

	// --拓展信息--
	// 门店数量，商品数量，订单金额，订单数量，代金券总数，代金券未核销数量，次卡总数，次卡未核销数量
}

// LocalLifeBrandAuditRecord 品牌资料审核记录
type LocalLifeBrandAuditRecord struct {
	source.Model
	BrandID uint   `json:"brand_id" form:"brand_id" gorm:"column:brand_id;comment:品牌id;index;"`
	Type    int    `json:"type" form:"type" gorm:"column:type;comment:审核类型|0资料变动审核1品牌入驻申请审核;"`
	Status  int    `json:"status" form:"status" gorm:"column:status;default:2;comment:资料变动审核状态|1待审核2已审核3已驳回;"`
	Remark  string `json:"remark" form:"remark" gorm:"column:remark;comment:备注;"`
}

// LocalLifeBrandCategory 品牌经营范围
type LocalLifeBrandCategory struct {
	source.Model
	// --经营范围-- 拆出来，单独一个表，LocalLifeBrand.id   ***列表搜索项
	// 经营类目 struct []category{分类一二三级id}
	BrandID uint `json:"brand_id" form:"brand_id" gorm:"column:brand_id;comment:品牌id;index;"`
	// 一级分类id uint
	Category1ID uint                        `json:"category1_id" form:"category1_id" gorm:"column:category1_id;comment:一级分类id;"`
	Category1   LocalLifeCategoryByRelation `json:"category1" gorm:"foreignKey:Category1ID;references:ID;comment:一级分类;"`
	// 二级分类id uint
	Category2ID uint                        `json:"category2_id" form:"category2_id" gorm:"column:category2_id;comment:二级分类id;"`
	Category2   LocalLifeCategoryByRelation `json:"category2" gorm:"foreignKey:Category2ID;references:ID;comment:二级分类;"`
	// 三级分类id uint
	Category3ID uint                        `json:"category3_id" form:"category3_id" gorm:"column:category3_id;comment:三级分类id;"`
	Category3   LocalLifeCategoryByRelation `json:"category3" gorm:"foreignKey:Category3ID;references:ID;comment:三级分类;"`
}

type Categories []CategoryItem

type CategoryItem struct {
	// 一级分类id uint
	Category1ID uint `json:"category1_id" form:"category1_id" gorm:"column:category1_id;comment:一级分类id;"`
	// 二级分类id uint
	Category2ID uint `json:"category2_id" form:"category2_id" gorm:"column:category2_id;comment:二级分类id;"`
	// 三级分类id uint
	Category3ID uint `json:"category3_id" form:"category3_id" gorm:"column:category3_id;comment:三级分类id;"`
}

// LocalLifeBrandUpdateRecord 品牌编辑资料后，存储有变动的信息，用于审核。比如：联系人修改前后不一样，就记录下来。当审核通过或者驳回后，删除这条记录
type LocalLifeBrandUpdateRecord struct {
	source.Model
	BrandID uint `json:"brand_id" form:"brand_id" gorm:"column:brand_id;comment:品牌id;index;"`
	// 商标转让证明是否有变动 int
	TransferImg int `json:"transfer_img" form:"transfer_img" gorm:"column:transfer_img;comment:商标转让证明是否有变动;"`
	// 商标续展证明是否有变动 int
	RenewalImg int `json:"renewal_img" form:"renewal_img" gorm:"column:renewal_img;comment:商标续展证明是否有变动;"`
	// 联系人姓名是否有变动 int
	ContactsRealName int `json:"real_name" form:"real_name" gorm:"column:real_name;comment:联系人姓名是否有变动;"`
	// 联系人电话是否有变动 int
	ContactsMobile int `json:"mobile" form:"mobile" gorm:"column:mobile;comment:联系人电话是否有变动;"`
}

// LocalLifeBrandLrInfo ｜Brand Legal representative店铺法人信息 缩写：blr
type LocalLifeBrandLrInfo struct {
	source.Model
	BrandID uint `json:"brand_id" form:"brand_id" gorm:"column:brand_id;comment:品牌id;index;"`
	// --法人实名信息-- 拆出来，单独一个表，关联LocalLifeBrand.id
	// 法人姓名 string
	RealName string `json:"real_name" form:"real_name" gorm:"column:real_name;comment:法人姓名;"`
	// 法人证件号 string
	IDCard string `json:"id_card" form:"id_card" gorm:"column:id_card;comment:法人证件号;"`
	// 法人证件类型 int【1:身份证，2:港澳通行证，3：台湾通行证，4:护照】
	CertificateType int `json:"certificate_type" form:"certificate_type" gorm:"column:certificate_type;comment:证件类型｜1:身份证，2:港澳通行证，3：台湾通行证，4:护照;"`
	// 人像面 img string
	IDFrontImg string `json:"id_front_img" form:"id_front_img" gorm:"column:id_front_img;comment:人像面;"`
	// 国徽面 img string
	IDBackImg string `json:"id_back_img" form:"id_back_img" gorm:"column:id_back_img;comment:国徽面;"`
}

// LocalLifeBrandInfo 店铺品牌信息
type LocalLifeBrandInfo struct {
	source.Model
	BrandID uint `json:"brand_id" form:"brand_id" gorm:"column:brand_id;comment:品牌id;index;"`
	// --品牌信息-- 拆出来，单独一个表，关联LocalLifeShop.id
	// 品牌名称 string  ***列表搜索项
	Name string `json:"name" form:"name" gorm:"column:name;comment:品牌名称;"`
	// 品牌logo
	Logo string `json:"logo" form:"logo" gorm:"column:logo;comment:品牌logo;"`
	// 认证类型 int
	AuthType int `json:"auth_type" form:"auth_type" gorm:"column:auth_type;default:1;comment:认证类型｜1:上传商标注册证2:上传非商标品牌资质;"`
	// 商标注册证 img string
	TrademarkImg string `json:"trademark_img" form:"trademark_img" gorm:"column:trademark_img;comment:商标注册证;"`
	// 品牌中文名 string
	ChineseName string `json:"chinese_name" form:"chinese_name" gorm:"column:chinese_name;comment:品牌中文名;"`
	// 品牌英文名 string
	EnglishName string `json:"english_name" form:"english_name" gorm:"column:english_name;comment:品牌英文名;"`
	// 商标注册号 string
	TrademarkNum string `json:"trademark_num" form:"trademark_num" gorm:"column:trademark_num;comment:商标注册号;"`
	// 商标注册有效期类型 【是否长期】
	ValidType int `json:"valid_type" form:"valid_type" gorm:"column:valid_type;default:1;comment:商标注册有效期类型｜1:长期2:有限期限;"`
	// 商标注册有效期 date时间格式
	ValidAt *source.LocalTime `json:"valid_at" form:"valid_at" gorm:"column:valid_at;comment:商标注册有效期;"`
	// 品牌经营类型 int【自有品牌，一级代理授权品牌，二级代理授权品牌】
	BrandType int `json:"brand_type" form:"brand_type" gorm:"column:brand_type;default:1;comment:品牌经营类型｜1:自有品牌2:一级代理授权品牌3:二级代理授权品牌;"`
	// 商标转让证明 img string
	TransferImg string `json:"transfer_img" form:"transfer_img" gorm:"column:transfer_img;comment:商标转让证明;"`
	// 商标续展证明 img string
	RenewalImg string `json:"renewal_img" form:"renewal_img" gorm:"column:renewal_img;comment:商标续展证明;"`
}

// User 会员
type User struct {
	source.Model
	Mobile   string `json:"mobile" form:"mobile" gorm:"column:mobile;comment:手机号;type:char(20);size:20;"`
	Avatar   string `json:"avatar" form:"avatar" gorm:"column:avatar;comment:用户头像url;type:varchar(255);size:255;"`
	NickName string `json:"nickname" form:"nickName" gorm:"column:nick_name;comment:用户昵称;type:varchar(50);size:50;"`
	Username string `json:"username" form:"username" gorm:"comment:用户登录名"`
	LevelID  uint   `json:"level_id"`
	Status   int    `json:"status"`
	Remark   string `json:"remark"`
}

type SysUser struct {
	source.Model
	UUID        uuid.UUID `json:"uuid" gorm:"comment:用户UUID"`
	Username    string    `json:"userName" form:"userName"`
	Password    string    `json:"password" form:"password"`
	NickName    string    `json:"nickName" form:"nickName" gorm:"default:本地生活-品牌" `
	AuthorityId string    `json:"authorityId" gorm:"default:111"`
	HeaderImg   string    `json:"headerImg" form:"headerImg"`
}

type LocalLifeBrandByAdminList struct {
	source.Model
	// --主体信息--
	// 公司/组织名称 string
	Name string `json:"name" form:"name" gorm:"column:name;comment:公司/组织名称;"`
	// 证件类型 int 【1:企业营业执照，2:事业单位法人证书，3:民办非企业单位登记证书，4:律师事务所营业许可证，5:社会团体法人登记证书，6:基层群众性自治组织特别法人统一社会信用代码证书，7:农业专业合作经济经济组织营业执照】
	CertificateType int `json:"certificate_type" form:"certificate_type" gorm:"column:certificate_type;comment:证件类型｜1:企业营业执照，2:事业单位法人证书，3:民办非企业单位登记证书，4:律师事务所营业许可证，5:社会团体法人登记证书，6:基层群众性自治组织特别法人统一社会信用代码证书，7:农业专业合作经济经济组织营业执照;"`
	// 营业执照编码
	CertificateCode string `json:"certificate_code" form:"certificate_code" gorm:"column:certificate_code;comment:营业执照编码;"`
	// 企业营业执照 img string
	BusinessLicenseImg string `json:"business_license_img" form:"business_license_img" gorm:"column:business_license_img;comment:企业营业执照;"`
	// 登陆账号id
	SysUID uint `json:"sys_uid" form:"sys_uid" gorm:"column:sys_uid;comment:登陆账号id;"`
	// --账号信息--
	// 联系人姓名 string  ***列表搜索项
	RealName string `json:"real_name" form:"real_name" gorm:"column:real_name;comment:联系人姓名;"`
	// 联系人电话 string  ***列表搜索项
	Mobile string `json:"mobile" form:"mobile" gorm:"column:mobile;comment:联系人电话;"`
	// 选择会员 uint 【会员表id】  ***列表搜索项
	UID uint `json:"uid" form:"uid" gorm:"column:uid;comment:中台会员id;"`
	// 账号
	Username string `json:"username" form:"username" gorm:"column:username;comment:账号;"`

	// --经营信息--
	// 商品审核 int 【是否需要审核】
	ProductAudit int `json:"product_audit" form:"product_audit" gorm:"column:product_audit;default:1;comment:商品审核|1需要审核2不需要审核;"`
	// 结算方式
	SettlementMethod int `json:"settlement_method" form:"settlement_method" gorm:"column:settlement_method;default:1;comment:结算方式|1:中台代发2:线下结算;"`

	// --状态信息--
	// 状态 int 【启用，禁用】  ***列表搜索项
	Status int `json:"status" form:"status" gorm:"column:status;default:1;comment:状态|1启用2禁用;"`
	// 资料变动审核状态 int 【1待审核，2已审核，3已驳回】***列表搜索项
	InfoAuditStatus int `json:"info_audit_status" form:"info_audit_status" gorm:"column:info_audit_status;default:2;comment:资料变动审核状态|1待审核2已审核3已驳回;"`
	// 品牌入驻申请审核状态 int 【待审核，已审核】
	AuditStatus int `json:"audit_status" form:"audit_status" gorm:"column:audit_status;default:1;comment:审核状态|1待审核2已审核3驳回;"`

	// --渠道来源信息--
	// 来源 int 【自营】
	Source              int                      `json:"source" form:"source" gorm:"column:source;comment:来源|0自营;"`
	SourceID            uint                     `json:"source_id" form:"source_id" gorm:"column:source_id;comment:来源ID;"`
	BrandInfo           LocalLifeBrandInfo       `json:"brand_info" gorm:"foreignKey:BrandID;references:ID;comment:品牌信息;"`
	Categories          []LocalLifeBrandCategory `json:"categories" gorm:"foreignKey:BrandID;references:ID;comment:经营范围;"`
	SysUser             SysUser                  `json:"sys_user" gorm:"foreignKey:SysUID;references:ID;comment:登陆账号;"`
	User                User                     `json:"user" gorm:"foreignKey:UID;references:ID;comment:中台会员;"`
	Statistic           LocalLifeBrandStatistic  `json:"statistic" gorm:"foreignKey:BrandID;references:ID;comment:品牌统计;"`
	SourceName          string                   `json:"source_name" gorm:"-"`
	StatusName          string                   `json:"status_name" gorm:"-"`
	InfoAuditStatusName string                   `json:"info_audit_status_name" gorm:"-"`
	SupplyInfo          LocalLifeSyncSupplyChain `json:"supply_info" gorm:"foreignKey:Source;references:ID"`
}

func (LocalLifeBrandByAdminList) TableName() string {
	return "local_life_brands"
}

func (l *LocalLifeBrandByAdminList) AfterFind(tx *gorm.DB) (err error) {
	if l.Source == 0 {
		l.SourceName = "自营"
	} else {
		l.SourceName = l.SupplyInfo.Name
	}
	if l.Status == 1 {
		l.StatusName = "启用"
	} else {
		l.StatusName = "禁用"
	}
	if l.AuditStatus == 1 {
		l.InfoAuditStatusName = "待审核"
	} else if l.AuditStatus == 2 {
		l.InfoAuditStatusName = "已审核"
	} else {
		l.InfoAuditStatusName = "驳回"
	}
	return
}

type LocalLifeBrandByAdminUpdate struct {
	source.Model
	// --主体信息--
	// 公司/组织名称 string
	Name string `json:"name" form:"name" gorm:"column:name;comment:公司/组织名称;"`
	// 证件类型 int 【1:企业营业执照，2:事业单位法人证书，3:民办非企业单位登记证书，4:律师事务所营业许可证，5:社会团体法人登记证书，6:基层群众性自治组织特别法人统一社会信用代码证书，7:农业专业合作经济经济组织营业执照】
	CertificateType int `json:"certificate_type" form:"certificate_type" gorm:"column:certificate_type;comment:证件类型｜1:企业营业执照，2:事业单位法人证书，3:民办非企业单位登记证书，4:律师事务所营业许可证，5:社会团体法人登记证书，6:基层群众性自治组织特别法人统一社会信用代码证书，7:农业专业合作经济经济组织营业执照;"`
	// 营业执照编码
	CertificateCode string `json:"certificate_code" form:"certificate_code" gorm:"column:certificate_code;comment:营业执照编码;"`
	// 企业营业执照 img string
	BusinessLicenseImg string `json:"business_license_img" form:"business_license_img" gorm:"column:business_license_img;comment:企业营业执照;"`
	// 登陆账号id
	SysUID uint `json:"sys_uid" form:"sys_uid" gorm:"column:sys_uid;comment:登陆账号id;index;"`
	// --账号信息--
	// 联系人姓名 string  ***列表搜索项
	RealName string `json:"real_name" form:"real_name" gorm:"column:real_name;comment:联系人姓名;"`
	// 联系人电话 string  ***列表搜索项
	Mobile string `json:"mobile" form:"mobile" gorm:"column:mobile;comment:联系人电话;"`
	// 选择会员 uint 【会员表id】  ***列表搜索项
	UID uint `json:"uid" form:"uid" gorm:"column:uid;comment:中台会员id;index;"`

	// 账号
	Username string `json:"username" form:"username" gorm:"column:username;comment:账号;"`

	// --经营信息--
	// 商品审核 int 【是否需要审核】
	ProductAudit int `json:"product_audit" form:"product_audit" gorm:"column:product_audit;default:1;comment:商品审核|1需要审核2不需要审核;"`
	// 结算方式
	SettlementMethod int `json:"settlement_method" form:"settlement_method" gorm:"column:settlement_method;default:1;comment:结算方式|1:中台代发2:线下结算;"`

	// --状态信息--
	// 状态 int 【启用，禁用】  ***列表搜索项
	Status int `json:"status" form:"status" gorm:"column:status;default:1;comment:状态|1启用2禁用;"`
	// 资料变动审核状态 int 【1待审核，2已审核，3已驳回】***列表搜索项
	InfoAuditStatus int `json:"info_audit_status" form:"info_audit_status" gorm:"column:info_audit_status;default:2;comment:资料变动审核状态|1待审核2已审核3已驳回;"`
	// 品牌入驻申请审核状态 int 【待审核，已审核】
	AuditStatus int `json:"audit_status" form:"audit_status" gorm:"column:audit_status;default:1;comment:审核状态|1待审核2已审核3驳回;"`

	ReviewStatus int    `json:"review_status" gorm:"-"`
	Remark       string `json:"remark" gorm:"-"`

	// --渠道来源信息--
	// 来源 int 【自营】
	Source uint `json:"source" form:"source" gorm:"column:source;comment:来源|0自营;"`
	// 来源ID
	SourceID     uint                       `json:"source_id" form:"source_id" gorm:"column:source_id;comment:来源ID;"`
	BrandInfo    LocalLifeBrandInfo         `json:"brand_info" gorm:"foreignKey:BrandID;references:ID;comment:品牌信息;"`
	Categories   []LocalLifeBrandCategory   `json:"categories" gorm:"foreignKey:BrandID;references:ID;comment:经营范围;"`
	LrInfo       LocalLifeBrandLrInfo       `json:"lr_info" gorm:"foreignKey:BrandID;references:ID;comment:法人实名信息;"`
	UpdateRecord LocalLifeBrandUpdateRecord `json:"update_record" gorm:"foreignKey:BrandID;references:ID;comment:信息变动;"`
	AuditRecord  LocalLifeBrandAuditRecord  `json:"audit_record" gorm:"foreignKey:BrandID;references:ID;comment:信息审核记录;"`
	Statistics   LocalLifeBrandStatistic    `json:"statistics" gorm:"foreignKey:BrandID;references:ID;comment:品牌统计;"`
}

func (LocalLifeBrandByAdminUpdate) TableName() string {
	return "local_life_brands"
}

type LocalLifeBrandApplyByAdminList struct {
	source.Model
	Name            string                   `json:"name" form:"name" gorm:"column:name;comment:公司/组织名称;"`
	BrandInfo       LocalLifeBrandInfo       `json:"brand_info" gorm:"foreignKey:BrandID;references:ID;comment:品牌信息;"`
	Source          int                      `json:"source" form:"source" gorm:"column:source;comment:来源|0自营;"`
	SourceName      string                   `json:"source_name" gorm:"-"`
	Categories      []LocalLifeBrandCategory `json:"categories" gorm:"foreignKey:BrandID;references:ID;comment:经营范围;"`
	User            User                     `json:"user" gorm:"foreignKey:UID;references:ID;comment:中台会员;"`
	UID             uint                     `json:"uid" form:"uid" gorm:"column:uid;comment:中台会员id;"`
	Username        string                   `json:"username" form:"username" gorm:"column:username;comment:账号;"`
	RealName        string                   `json:"real_name" form:"real_name" gorm:"column:real_name;comment:联系人姓名;"`
	Mobile          string                   `json:"mobile" form:"mobile" gorm:"column:mobile;comment:联系人电话;"`
	AuditStatus     int                      `json:"audit_status" form:"audit_status" gorm:"column:audit_status;default:1;comment:审核状态|1待审核2已审核3驳回;"`
	InfoAuditStatus int                      `json:"info_audit_status" form:"info_audit_status" gorm:"column:info_audit_status;default:2;comment:资料变动审核状态|1待审核2已审核3已驳回;"`
	AuditStatusName string                   `json:"audit_status_name" gorm:"-"`
}

func (LocalLifeBrandApplyByAdminList) TableName() string {
	return "local_life_brands"
}

func (l *LocalLifeBrandApplyByAdminList) AfterFind(tx *gorm.DB) (err error) {
	if l.Source == 0 {
		l.SourceName = "自营"
	}
	if l.AuditStatus == 1 {
		l.AuditStatusName = "待审核"
	} else if l.AuditStatus == 2 {
		l.AuditStatusName = "已审核"
	} else if l.AuditStatus == 3 {
		l.AuditStatusName = "驳回"
	} else {
		l.AuditStatusName = "资料变动"
	}
	return
}

// LocalLifeBrandStatistic 本地生活品牌统计
type LocalLifeBrandStatistic struct {
	source.Model
	BrandID uint `json:"brand_id" form:"brand_id" gorm:"column:brand_id;comment:品牌id;index;"`
	// 门店数量，商品数量；订单金额，订单数量；代金券总数，代金券未核销数量；次卡总数，次卡未核销数量；团购券总数，团购券未核销数量。
	StoreCount     int  `json:"store_count" form:"store_count" gorm:"column:store_count;comment:门店数量;"`
	ProductCount   int  `json:"product_count" form:"product_count" gorm:"column:product_count;comment:商品数量;"`
	OrderAmount    uint `json:"order_amount" form:"order_amount" gorm:"column:order_amount;comment:订单金额;"`
	OrderCount     int  `json:"order_count" form:"order_count" gorm:"column:order_count;comment:订单数量;"`
	VoucherCount   uint `json:"voucher_count" form:"voucher_count" gorm:"column:voucher_count;comment:代金券总数;"`
	UnVoucherCount uint `json:"un_voucher_count" form:"un_voucher_count" gorm:"column:un_voucher_count;comment:代金券未核销数量;"`
	CardCount      uint `json:"card_count" form:"card_count" gorm:"column:card_count;comment:次卡总数;"`
	UnCardCount    uint `json:"un_card_count" form:"un_card_count" gorm:"column:un_card_count;comment:次卡未核销数量;"`
	GroupCount     uint `json:"group_count" form:"group_count" gorm:"column:group_count;comment:团购券总数;"`
	UnGroupCount   uint `json:"un_group_count" form:"un_group_count" gorm:"column:un_group_count;comment:团购券未核销数量;"`
}
