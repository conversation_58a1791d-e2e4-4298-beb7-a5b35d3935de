package model

import (
	"database/sql/driver"
	"encoding/json"
	"yz-go/source"
)

type LocalLifeStore struct {
	source.Model
	Name                 string    `json:"name" form:"name"`
	UserID               uint      `json:"user_id" form:"user_id"`
	ProvinceId           int       `json:"province_id" form:"province_id"`
	CityId               int       `json:"city_id" form:"city_id"`
	AreaId               int       `json:"area_id" form:"area_id"`
	AddressDetail        string    `json:"address_detail" form:"address_detail"`
	Longitude            string    `json:"longitude" form:"longitude"`
	Latitude             string    `json:"latitude" form:"latitude"`
	Tel                  string    `json:"tel" form:"tel"`
	OpenTime             OpenTimes `json:"open_time"`
	BusinessLicense      string    `json:"business_license"`
	FrontOfIDCard        string    `json:"front_of_id_card"`
	BackOfIDCard         string    `json:"back_of_id_card"`
	FacilitiesAndService string    `json:"facilities_and_service" gorm:"type:text(0);size:0;"`
	Status               int       `json:"status" gorm:"type:int(1);size:1;"` // 1开启 0关闭
	BrandID              uint      `json:"brand_id" form:"brand_id"`
	// 来源 int 【自营】
	Source uint `json:"source" form:"source" gorm:"column:source;comment:来源|0自营;"`
	// 来源ID
	SourceID uint `json:"source_id" form:"source_id" gorm:"column:source_id;comment:来源ID;"`
}

func (value OpenTimes) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *OpenTimes) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type OpenTimes []OpenTime
type OpenTime struct {
	Weeks []string `json:"weeks"`
	Times []Time   `json:"times"`
}
type Time struct {
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
}

type LocalLifeBatchStoreRecord struct {
	source.Model
	BrandID uint `json:"brand_id" form:"brand_id"`
	// 总数
	Total int `json:"total" form:"total"`
	// 成功数
	Success int `json:"success" form:"success"`
	// 失败数
	Fail int `json:"fail" form:"fail"`
	// 详情
	Details []LocalLifeBatchStoreRecordDetail `json:"details" form:"details" gorm:"foreignKey:RecordID;references:ID;"`
}

type LocalLifeBatchStoreRecordDetail struct {
	source.Model
	RecordID uint `json:"record_id" form:"record_id"`
	// 门店名称
	StoreName string `json:"store_name" form:"store_name"`
	// 会员手机号
	UserMobile string `json:"user_mobile" form:"user_mobile"`
	// 省份名称
	ProvinceName string `json:"province_name" form:"province_name"`
	// 城市名称
	CityName string `json:"city_name" form:"city_name"`
	// 区名称
	AreaName string `json:"area_name" form:"area_name"`
	// 详细地址
	AddressDetail string `json:"address_detail" form:"address_detail"`
	// 经度
	Longitude string `json:"longitude" form:"longitude"`
	// 纬度
	Latitude string `json:"latitude" form:"latitude"`
	// 创建状态 1成功 0失败
	Status int `json:"status" form:"status"`
	// 失败原因
	FailReason string `json:"fail_reason" form:"fail_reason"`
}
