package order

import (
	"errors"
	"gorm.io/gorm"
	"local-life/model"
	"local-life/mq"
	stock "local-life/product"
	paymentModel "payment/model"
	"sync"
	"time"
	"yz-go/source"
)

type Handle func(OperationOrder) error
type UseByCodeBeforeHandle func(OperationOrder) error
type UseByCodeAfterHandle func(UseByCodeParams) error
type UseBeforeHandle func(UseParams) error
type UseAfterHandle func(orderID, storeID uint, count int) error

type OperationOrder struct {
	model.Order
	PayInfo PayInfo
}

type UseParams struct {
	Order   OperationOrder
	OrderID uint
	StoreID uint
	Count   int
	Usage   uint
	Used    uint
}

type PayInfo struct {
	ID     uint
	Status paymentModel.PayStatus `json:"status"`
}

func (PayInfo) TableName() string {
	return "local_life_pay_infos"
}

func (o OperationOrder) TableName() string {
	return "local_life_orders"
}

type UseOperation struct {
	OperationOrder
}

func (o UseOperation) GetBeforeHandle() []UseBeforeHandle {
	return []UseBeforeHandle{
		vrfStore,
	}
}

type UseByCodeOperation struct {
	OperationOrder
}

func (o UseByCodeOperation) GetBeforeHandle() []UseBeforeHandle {
	return []UseBeforeHandle{
		useBeforeCheck,
		checkCount,
		vrfStore,
	}
}

func (o UseByCodeOperation) GetAfterHandle() []UseByCodeAfterHandle {
	return []UseByCodeAfterHandle{
		updateVrfCode,
	}
}

func updateVrfCode(params UseByCodeParams) (err error) {
	err = source.DB().Model(&model.LocalLifeOrderVrfCode{}).Where("id = ?", params.CodeID).Updates(model.LocalLifeOrderVrfCode{Status: model.Used, VUID: params.VUID, VID: params.VID}).Error
	return
}

func vrfStore(params UseParams) (err error) {
	if params.Order.ShopType == 1 {
		return
	}
	var orderStore model.LocalLifeOrderStore
	err = source.DB().Where("order_id = ? and store_id = ?", params.OrderID, params.StoreID).First(&orderStore).Error
	if err != nil {
		err = errors.New("该门店不允许使用")
		return
	}
	if orderStore.ID == 0 {
		err = errors.New("该门店不允许使用")
		return
	}
	return
}

func useBeforeCheck(params UseParams) (err error) {
	var orderItems []OrderItem
	err = source.DB().Where("order_id = ?", params.OrderID).Find(&orderItems).Error
	if err != nil {
		err = errors.New("订单商品不存在")
		return
	}
	if params.Order.PaidAt == nil {
		err = errors.New("订单未支付")
		return
	}
	for _, item := range orderItems {
		err = stock.UseBeforeCheck(*params.Order.PaidAt, item.OrderID)
		if err != nil {
			return
		}
	}
	return
}

func (o UseOperation) GetAfterHandle() []UseAfterHandle {
	return []UseAfterHandle{}
}

func checkCount(params UseParams) (err error) {
	if params.Usage < params.Used+uint(params.Count) {
		err = errors.New("核销数量超过订单数量")
		return
	}
	return
}

type UseByCodeParams struct {
	OrderID uint `json:"order_id"`
	StoreID uint `json:"store_id"`
	// 下单会员ID
	UID uint `json:"uid"`
	// 核销员ID Verification.ID
	VID uint `json:"vid"`
	// 核销会员ID
	VUID   uint `json:"v_uid"`
	CodeID uint `json:"code_id"`
}

// UseByCode 用户核销码核销
func UseByCode(params UseByCodeParams) (err error) {
	// 查询订单
	order := OperationOrder{}
	err = source.DB().Where("id = ?", params.OrderID).First(&order).Error
	if err != nil {
		return
	}
	// 操作结构
	useOperation := UseByCodeOperation{OperationOrder: order}
	useParams := UseParams{
		Order:   order,
		OrderID: params.OrderID,
		StoreID: params.StoreID,
		Count:   1,
		Usage:   order.Usage,
		Used:    order.Used,
	}
	// 核销前钩子
	for _, useHandle := range useOperation.GetBeforeHandle() {
		err = useHandle(useParams)
		if err != nil {
			return
		}
	}
	// 校验订单状态
	if order.Status != model.WaitUse && order.Status != model.PartUse {
		err = errors.New(model.GetStatusName(order.Status) + "的订单无法核销")
		return
	}
	if order.RefundStatus != model.ClosedRefund && order.RefundStatus != model.NotRefund {
		err = errors.New("售后订单无法核销")
		return
	}
	if order.Usage > order.Used+1 {
		order.Used += 1
		order.Status = model.PartUse
		err = source.DB().Where("id = ?", order.ID).Updates(model.LocalLifeOrder{Status: model.PartUse, Used: order.Used}).Error
		if err != nil {
			return
		}
	} else {
		order.Used += 1
		order.Status = model.Completed
		order.ReceivedAt = &source.LocalTime{Time: time.Now()}
		err = source.DB().Where("id = ?", order.ID).Updates(model.LocalLifeOrder{Status: model.Completed, ReceivedAt: order.ReceivedAt, Used: order.Used}).Error
		if err != nil {
			return
		}
	}
	// 核销后钩子
	for _, useHandle := range useOperation.GetAfterHandle() {
		err = useHandle(params)
		if err != nil {
			return
		}
	}
	// 消息队列
	if order.Status == model.Completed {
		err = mq.PublishMessage(params.OrderID, params.StoreID, mq.Received, 1, []uint{params.CodeID})
	} else {
		err = mq.PublishMessage(params.OrderID, params.StoreID, mq.PartUse, 1, []uint{params.CodeID})
	}
	if err != nil {
		return
	}
	return
}

// UseByDashboard 后台核销-概况-输码核销
func UseByDashboard(brandID, storeID uint, code string) (err error) {
	if code == "" {
		err = errors.New("核销码不能为空")
		return
	}
	if storeID == 0 {
		err = errors.New("门店ID不能为空")
		return
	}
	var vrfCode model.LocalLifeOrderVrfCode
	err = source.DB().Where("brand_id = ? and code = ?", brandID, code).First(&vrfCode).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	if err != nil {
		err = errors.New("核销码不存在")
		return
	}
	if vrfCode.ID == 0 {
		err = errors.New("核销码不存在")
		return
	}
	if vrfCode.Status == model.Used {
		err = errors.New("核销码已使用")
		return
	}
	// 查询订单
	order := OperationOrder{}
	err = source.DB().Where("id = ?", vrfCode.OrderID).First(&order).Error
	if err != nil {
		return
	}
	if order.Used+1 > order.Usage {
		err = errors.New("核销数量超过订单数量")
		return
	}
	// 操作结构
	useOperation := UseOperation{OperationOrder: order}
	params := UseParams{
		Order:   order,
		OrderID: order.ID,
		StoreID: storeID,
	}
	// 核销前钩子
	for _, useHandle := range useOperation.GetBeforeHandle() {
		err = useHandle(params)
		if err != nil {
			return
		}
	}
	// 校验订单状态
	if order.Status != model.WaitUse && order.Status != model.PartUse {
		err = errors.New(model.GetStatusName(order.Status) + "的订单无法核销")
		return
	}
	if order.Usage > order.Used+1 {
		order.Used += 1
		order.Status = model.PartUse
		err = source.DB().Where("id = ?", order.ID).Updates(model.LocalLifeOrder{Status: model.PartUse, Used: order.Used}).Error
		if err != nil {
			return
		}
	} else {
		order.Used += 1
		order.Status = model.Completed
		order.ReceivedAt = &source.LocalTime{Time: time.Now()}
		err = source.DB().Where("id = ?", order.ID).Updates(model.LocalLifeOrder{Status: model.Completed, ReceivedAt: order.ReceivedAt, Used: order.Used}).Error
		if err != nil {
			return
		}
	}
	// 核销后钩子
	for _, useHandle := range useOperation.GetAfterHandle() {
		err = useHandle(order.ID, storeID, 1)
		if err != nil {
			return
		}
	}
	// 更改核销码状态
	err = source.DB().Model(&model.LocalLifeOrderVrfCode{}).Where("id = ?", vrfCode.ID).Updates(model.LocalLifeOrderVrfCode{Status: model.Used}).Error
	if err != nil {
		return
	}
	var vrfCodeIDs []uint
	vrfCodeIDs = append(vrfCodeIDs, vrfCode.ID)
	// 消息队列
	if order.Status == model.Completed {
		err = mq.PublishMessage(order.ID, storeID, mq.Received, 1, vrfCodeIDs)
	} else {
		err = mq.PublishMessage(order.ID, storeID, mq.PartUse, 1, vrfCodeIDs)
	}
	if err != nil {
		return
	}
	return
}

// Use 后台核销
func Use(orderID, storeID uint, count int) (err error) {
	if count <= 0 {
		err = errors.New("核销数量必须大于0")
		return
	}
	// 查询订单是否存在
	order := OperationOrder{}
	err = source.DB().Where("id = ?", orderID).First(&order).Error
	if err != nil {
		return
	}
	// 操作结构
	useOperation := UseOperation{OperationOrder: order}
	params := UseParams{
		Order:   order,
		OrderID: orderID,
		StoreID: storeID,
		Count:   count,
		Usage:   order.Usage,
		Used:    order.Used,
	}
	// 核销前钩子
	for _, useHandle := range useOperation.GetBeforeHandle() {
		err = useHandle(params)
		if err != nil {
			return
		}
	}
	// 校验订单状态
	if order.Status != model.WaitUse && order.Status != model.PartUse {
		err = errors.New(model.GetStatusName(order.Status) + "的订单无法核销")
		return
	}
	// 查询vrfCodes
	var vrfCodes []model.LocalLifeOrderVrfCode
	err = source.DB().Where("order_id = ? and status = ?", orderID, model.Unused).Limit(count).Find(&vrfCodes).Error
	if err != nil || len(vrfCodes) < count {
		err = errors.New("核销码不足")
		return
	}
	var vrfCodeIDs []uint
	// 更改订单状态 因为有校验，所以这里只有两种结果，一种是全部核销，一种是部分核销
	if order.Usage > order.Used+uint(count) {
		order.Used += uint(count)
		order.Status = model.PartUse
		err = source.DB().Where("id = ?", order.ID).Updates(model.LocalLifeOrder{Status: model.PartUse, Used: order.Used}).Error
		if err != nil {
			return
		}
	} else {
		order.Used += uint(count)
		order.Status = model.Completed
		order.ReceivedAt = &source.LocalTime{Time: time.Now()}
		err = source.DB().Where("id = ?", order.ID).Updates(model.LocalLifeOrder{Status: model.Completed, ReceivedAt: order.ReceivedAt, Used: order.Used}).Error
		if err != nil {
			return
		}
	}
	// 核销后钩子
	for _, useHandle := range useOperation.GetAfterHandle() {
		err = useHandle(orderID, storeID, count)
		if err != nil {
			return
		}
	}
	for _, vrfCode := range vrfCodes {
		vrfCodeIDs = append(vrfCodeIDs, vrfCode.ID)
	}
	// 更改核销码状态
	err = source.DB().Model(&model.LocalLifeOrderVrfCode{}).Where("id in (?)", vrfCodeIDs).Updates(model.LocalLifeOrderVrfCode{Status: model.Used}).Error
	if err != nil {
		return
	}
	// 消息队列
	if order.Status == model.Completed {
		err = mq.PublishMessage(orderID, storeID, mq.Received, uint(count), vrfCodeIDs)
	} else {
		err = mq.PublishMessage(orderID, storeID, mq.PartUse, uint(count), vrfCodeIDs)
	}
	if err != nil {
		return
	}
	return
}

// UseByApi 接口核销
func UseByApi(orderID uint, codes []string) (err error) {
	var vrfCodes []model.LocalLifeOrderVrfCode
	err = source.DB().Where("order_id = ? and status = ? and code in ?", orderID, model.Unused, codes).Find(&vrfCodes).Error
	if err != nil {
		err = errors.New("核销码不存在")
		return
	}
	count := len(vrfCodes)
	if count != len(codes) {
		err = errors.New("核销码数量不匹配")
		return
	}
	// 查询订单
	order := OperationOrder{}
	err = source.DB().Where("id = ?", orderID).First(&order).Error
	if err != nil {
		return
	}
	if order.Used+uint(count) > order.Usage {
		err = errors.New("核销数量超过订单数量")
		return
	}
	// 校验订单状态
	if order.Status != model.WaitUse && order.Status != model.PartUse {
		err = errors.New(model.GetStatusName(order.Status) + "的订单无法核销")
		return
	}
	if order.Usage > order.Used+uint(count) {
		order.Used += uint(count)
		order.Status = model.PartUse
		err = source.DB().Where("id = ?", order.ID).Updates(model.LocalLifeOrder{Status: model.PartUse, Used: order.Used}).Error
		if err != nil {
			return
		}
	} else {
		order.Used += uint(count)
		order.Status = model.Completed
		order.ReceivedAt = &source.LocalTime{Time: time.Now()}
		err = source.DB().Where("id = ?", order.ID).Updates(model.LocalLifeOrder{Status: model.Completed, ReceivedAt: order.ReceivedAt, Used: order.Used}).Error
		if err != nil {
			return
		}
	}
	// 更改核销码状态
	var vrfCodeIDs []uint
	for _, vrfCode := range vrfCodes {
		vrfCodeIDs = append(vrfCodeIDs, vrfCode.ID)
	}
	err = source.DB().Model(&model.LocalLifeOrderVrfCode{}).Where("id in ?", vrfCodeIDs).Updates(model.LocalLifeOrderVrfCode{Status: model.Used}).Error
	if err != nil {
		return
	}
	// 消息队列
	if order.Status == model.Completed {
		err = mq.PublishMessage(order.ID, 0, mq.Received, uint(count), vrfCodeIDs)
	} else {
		err = mq.PublishMessage(order.ID, 0, mq.PartUse, uint(count), vrfCodeIDs)
	}
	if err != nil {
		return
	}
	return
}

type ClosedOperation struct {
	OperationOrder
}

func (o ClosedOperation) GetBeforeHandle() []Handle {
	return []Handle{}
}

func (o ClosedOperation) GetAfterHandle() []Handle {
	return []Handle{
		unlockStock,
	}
}

func ForceClose(orderID uint) (err error) {
	order := OperationOrder{}
	err = source.DB().Where("id = ?", orderID).Find(&order).Error
	if err != nil {
		return
	}
	// 操作结构
	operation := ClosedOperation{order}
	// 关闭前钩子
	for _, handle := range operation.GetBeforeHandle() {
		err = handle(order)
		if err != nil {
			return
		}
	}
	// 改变订单记录
	if order.Status == model.Closed {
		err = errors.New(model.GetStatusName(order.Status) + "的订单无法关闭")
		return
	}
	order.Status = model.Closed
	order.ClosedAt = &source.LocalTime{Time: time.Now()}
	err = source.DB().Where("id = ?", order.ID).Updates(model.LocalLifeOrder{Status: model.Closed, ClosedAt: order.ClosedAt}).Error
	if err != nil {
		return
	}
	// 关闭后钩子
	for _, handle := range operation.GetAfterHandle() {
		err = handle(order)
		if err != nil {
			return
		}
	}
	// 消息队列
	err = mq.PublishMessage(orderID, 0, mq.Closed, 0, []uint{})
	if err != nil {
		return
	}
	return
}

func Close(orderID uint) (err error) {
	// 查询订单是否存在
	order := OperationOrder{}
	err = source.DB().Where("id = ?", orderID).First(&order).Error
	if err != nil {
		return
	}
	// 操作结构
	operation := ClosedOperation{OperationOrder: order}
	// 关闭前钩子
	for _, handle := range operation.GetBeforeHandle() {
		err = handle(order)
		if err != nil {
			return
		}
	}
	// 改变订单记录
	if order.Status != model.WaitPay {
		err = errors.New(model.GetStatusName(order.Status) + "的订单无法关闭")
		return
	}
	order.Status = model.Closed
	order.ClosedAt = &source.LocalTime{Time: time.Now()}
	err = source.DB().Where("id = ?", order.ID).Updates(model.LocalLifeOrder{Status: model.Closed, ClosedAt: order.ClosedAt}).Error
	if err != nil {
		return
	}
	// 关闭后钩子
	for _, handle := range operation.GetAfterHandle() {
		err = handle(order)
		if err != nil {
			return
		}
	}
	// 消息队列
	err = mq.PublishMessage(orderID, 0, mq.Closed, 0, []uint{})
	if err != nil {
		return
	}
	return
}

type PayOperation struct {
	OperationOrder
}

func (o PayOperation) GetBeforeHandle() []Handle {
	return []Handle{}
}

func (o PayOperation) GetAfterHandle() []Handle {
	return []Handle{
		unlockStock,
		reduceStock,
		addSales,
	}
}

func Pay(orderID uint, payTypeID int, payInfoID uint) (err error) {
	order := OperationOrder{}
	err = source.DB().Preload("PayInfo").Where("id = ?", orderID).Find(&order).Error
	if err != nil {
		return
	}
	// 操作结构
	payOperation := PayOperation{order}
	// 付款前钩子
	for _, handle := range payOperation.GetBeforeHandle() {
		err = handle(order)
		if err != nil {
			return
		}
	}
	// 改变订单记录
	if order.Status != model.WaitPay {
		err = errors.New(model.GetStatusName(order.Status) + "的订单无法支付")
		return
	}
	order.Status = model.WaitUse
	order.PayTypeID = payTypeID
	order.PayInfoID = payInfoID
	// order.CanRefund = 1 是否可以退款
	order.PaidAt = &source.LocalTime{Time: time.Now()}
	err = source.DB().Where("id = ?", order.ID).Updates(model.LocalLifeOrder{Status: model.WaitUse, PayTypeID: payTypeID, PayInfoID: payInfoID, PaidAt: order.PaidAt}).Error
	if err != nil {
		return
	}
	// 付款后钩子
	for _, handle := range payOperation.GetAfterHandle() {
		err = handle(order)
		if err != nil {
			return
		}
	}
	// 消息队列
	err = mq.PublishMessage(orderID, 0, mq.Paid, 0, []uint{})
	if err != nil {
		return
	}
	return
}

func unlockStock(order OperationOrder) (err error) {
	var orderItems []OrderItem
	err = source.DB().Where("order_id = ?", order.ID).Find(&orderItems).Error
	if err != nil {
		return
	}
	for _, item := range orderItems {
		err = stock.RemoveLockStockItem(item.ID)
		if err != nil {
			return
		}
		err = stock.ReduceSkuLockStock(item.ProductID, int(item.Qty))
		if err != nil {
			return
		}
	}
	return
}

func lockStock(order OperationOrder) (err error) {
	var orderItems OrderItems
	err = source.DB().Where("order_id = ?", order.ID).Find(&orderItems).Error
	if err != nil {
		return
	}
	for _, item := range orderItems {
		err = stock.AddLockStockItem(item.ID)
		if err != nil {
			return
		}
		err = stock.AddSkuLockStock(item.ProductID, int(item.Qty))
		if err != nil {
			return
		}
	}
	return
}

type OrderItems []OrderItem

type OrderItem struct {
	model.LocalLifeOrderItem
}

func (OrderItem) TableName() string {
	return "local_life_order_items"
}

func reduceStock(order OperationOrder) (err error) {
	var orderItems []OrderItem
	err = source.DB().Where("order_id = ?", order.ID).Find(&orderItems).Error
	if err != nil {
		return
	}
	for _, item := range orderItems {
		err = stock.ReduceProductStock(item.ProductID, item.Qty)
		if err != nil {
			return
		}
	}
	return
}

func addSales(order OperationOrder) (err error) {
	var orderItems []OrderItem
	err = source.DB().Where("order_id = ?", order.ID).Find(&orderItems).Error
	if err != nil {
		return
	}
	for _, item := range orderItems {
		err = stock.AddSales(item.ProductID, item.Qty)
		if err != nil {
			return
		}
	}
	return
}

type ConfirmOperation struct {
	OperationOrder
}

func (o ConfirmOperation) GetAfterHandle() []Handle {
	return []Handle{
		lockStock,
	}
}

func (o ConfirmOperation) GetBeforeHandle() []Handle {
	return []Handle{}
}

func Confirm(orders []model.Order) (error, []model.Order) {
	var outOrders []model.Order

	// 使用通道收集错误和完成的订单
	errChan := make(chan error, len(orders))
	outOrdersChan := make(chan model.Order, len(orders))

	// 使用WaitGroup等待所有goroutine完成
	var wg sync.WaitGroup

	for _, orderModel := range orders {
		wg.Add(1)
		go func(orderModel model.Order) {
			defer wg.Done()

			order := OperationOrder{Order: orderModel}
			operation := ConfirmOperation{order}

			// 生成前钩子
			for _, handle := range operation.GetBeforeHandle() {
				err := handle(order)
				if err != nil {
					errChan <- err
					return
				}
			}

			err := source.DB().Save(&orderModel).Error
			order.ID = orderModel.ID
			if err != nil {
				errChan <- err
				return
			}

			// 生成后钩子
			for _, handle := range operation.GetAfterHandle() {
				err = handle(order)
				if err != nil {
					errChan <- err
					return
				}
			}

			// 消息队列
			err = mq.PublishMessage(order.ID, 0, mq.Created, 0, []uint{})
			if err != nil {
				errChan <- err
				return
			}

			outOrdersChan <- orderModel
		}(orderModel)
	}

	wg.Wait()
	close(errChan)
	close(outOrdersChan)

	// 检查是否有错误
	for e := range errChan {
		if e != nil {
			return e, nil
		}
	}

	// 收集完成的订单
	for o := range outOrdersChan {
		outOrders = append(outOrders, o)
	}
	return nil, outOrders
}
