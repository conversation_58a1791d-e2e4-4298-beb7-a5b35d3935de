package order

import (
	"errors"
	"fmt"
	"local-life/model"
	product2 "local-life/product"
	"yz-go/source"
)

type LocalLifeOrder struct {
	source.Model
	// 会员ID
	UID uint `json:"uid" form:"uid" gorm:"index"`
}

func GetOrderByID(id uint) (err error, order LocalLifeOrder) {
	err = source.DB().Where("id = ?", id).First(&order).Error
	return
}

func SaveRelations(orderID uint) (err error) {
	// 查询订单
	order, err := getOrder(orderID)
	if err != nil {
		return err
	}

	// 查询订单item
	orderItems, err := getOrderItems(orderID)
	if err != nil {
		return err
	}

	for _, item := range orderItems {
		// 查询订单商品
		product, err := getProduct(item.ProductID)
		if err != nil {
			return err
		}
		// 保存门店信息
		if order.ShopType == 2 {
			err = saveOrderStores(order, item, product)
			if err != nil {
				return err
			}
		}
		// 保存购买信息
		err = saveOrderPurchase(order, item, product)
		if err != nil {
			return err
		}
		// 保存结算信息
		err = saveOrderSettle(order, item, product)
		if err != nil {
			return err
		}
		// 保存商品套餐
		err = saveProductPackage(order, item, product)
		if err != nil {
			return err
		}
	}
	return nil
}

func saveOrderStores(order *model.LocalLifeOrder, item model.LocalLifeOrderItem, product *product2.LocalLifeProduct) error {
	var orderStores []model.LocalLifeOrderStore
	for _, store := range product.Stores {
		orderStores = append(orderStores, model.LocalLifeOrderStore{
			OrderID:     order.ID,
			OrderItemID: item.ID,
			ProductID:   product.ID,
			StoreID:     store.StoreID,
		})
	}
	if len(orderStores) > 0 {
		err := source.DB().Create(&orderStores).Error
		if err != nil {
			return fmt.Errorf("failed to save order stores: %w", err)
		}
	}
	return nil
}

func saveOrderPurchase(order *model.LocalLifeOrder, item model.LocalLifeOrderItem, product *product2.LocalLifeProduct) error {
	orderPurchase := model.LocalLifeOrderPurchase{
		OrderID:         order.ID,
		OrderItemID:     item.ID,
		ProductID:       product.ID,
		NeedMobile:      product.Purchase.NeedMobile,
		Expire:          product.Purchase.Expire,
		UnavailableDate: product.Purchase.UnavailableDate,
		Consume:         product.Purchase.Consume,
		BuyLimitRule:    product.Purchase.BuyLimitRule,
		BookingRule:     product.Purchase.BookingRule,
		WithDiscount:    product.Purchase.WithDiscount,
		UsageSheetRule:  product.Purchase.UsageSheetRule,
		UsagePeopleRule: product.Purchase.UsagePeopleRule,
		UsageDescribes:  product.Purchase.UsageDescribes,
	}
	err := source.DB().Create(&orderPurchase).Error
	if err != nil {
		return fmt.Errorf("failed to save order purchase: %w", err)
	}
	return nil
}

func saveOrderSettle(order *model.LocalLifeOrder, item model.LocalLifeOrderItem, product *product2.LocalLifeProduct) error {
	orderSettle := model.LocalLifeOrderSettle{
		OrderID:     order.ID,
		OrderItemID: item.ID,
		ProductID:   product.ID,
		Independent: product.Settle.Independent,
		StoreRate:   product.Settle.StoreRate,
		Ratio:       product.Settle.Ratio,
	}
	err := source.DB().Create(&orderSettle).Error
	if err != nil {
		return fmt.Errorf("failed to save order settle: %w", err)
	}
	return nil
}

func saveProductPackage(order *model.LocalLifeOrder, item model.LocalLifeOrderItem, product *product2.LocalLifeProduct) error {
	productPackage := model.LocalLifeOrderProductPackage{
		OrderID:     order.ID,
		OrderItemID: item.ID,
		ProductID:   product.ID,
		Packages:    product.Packages,
	}
	err := source.DB().Create(&productPackage).Error
	if err != nil {
		return fmt.Errorf("failed to save product package: %w", err)
	}
	return nil
}

func getOrder(orderID uint) (*model.LocalLifeOrder, error) {
	var order model.LocalLifeOrder
	err := source.DB().First(&order, orderID).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get order: %w", err)
	}
	return &order, nil
}

func getOrderItems(orderID uint) ([]model.LocalLifeOrderItem, error) {
	var orderItems []model.LocalLifeOrderItem
	err := source.DB().Where("order_id = ?", orderID).Find(&orderItems).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get order items: %w", err)
	}
	if len(orderItems) == 0 {
		return nil, errors.New("no order items found")
	}
	return orderItems, nil
}

func getProduct(productID uint) (*product2.LocalLifeProduct, error) {
	var product product2.LocalLifeProduct
	err := source.DB().Preload("Stores").Preload("Purchase").Preload("Settle").Where("id = ?", productID).First(&product).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get product: %w", err)
	}
	return &product, nil
}
