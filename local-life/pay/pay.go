package pay

import (
	"errors"
	"go.uber.org/zap"
	"local-life/model"
	operation "local-life/order"
	"local-life/payment/entrance"
	paymentModel "payment/model"
	"yz-go/component/log"
	"yz-go/source"
)

type PayInfo struct {
	model.LocalLifePayInfo
	Orders []model.LocalLifeOrder `json:"orders" gorm:"-"`
}

func (PayInfo) TableName() string {
	return "local_life_pay_infos"
}

func PaidBySort(order model.Order, payInfo PayInfo, sort []paymentModel.ApplicationPaySort) (err error) {
	var data map[string]interface{}
	var payTypeID int
	for _, v := range sort {
		//按照设置的顺序扣款
		err, data = entrance.PayService(model.UpdateBalance{
			Uid:       order.UID,
			Amount:    order.Amount,
			PayType:   v.PayTypeID,
			PayInfoID: int(payInfo.ID),
		})
		if err != nil {
			log.Log().Info("local_life for PaidBySort 支付失败", zap.Any("err", err.Error()))
			continue
		}
		payTypeID = v.PayTypeID
		break
	}
	if data["status"] == 1 {
		if err = operation.Pay(order.ID, payTypeID, payInfo.ID); err != nil {
			return
		}
	} else {
		return errors.New("支付失败了")
	}
	return
}

func GetPayInfo(userID uint, orderIDs []uint) (err error, payInfo PayInfo) {
	var orders []model.LocalLifeOrder
	err = source.DB().Where("id in ?", orderIDs).Find(&orders).Error
	if err != nil {
		return
	}
	var amount uint
	for _, o := range orders {
		amount += o.Amount
	}
	// 支付信息
	payInfo = PayInfo{
		LocalLifePayInfo: model.LocalLifePayInfo{
			UserID: userID,
			Amount: amount,
		},
	}
	err = source.DB().Save(&payInfo).Error
	if err != nil {
		return
	}
	// 保存关联订单
	var orderPayInfos []model.LocalLifeOrderPayInfo
	for _, o := range orders {
		orderPayInfos = append(orderPayInfos, model.LocalLifeOrderPayInfo{
			LocalLifePayInfoID: payInfo.ID,
			LocalLifeOrderID:   o.ID,
		})
	}
	err = source.DB().Save(&orderPayInfos).Error
	if err != nil {
		return
	}
	payInfo.Orders = orders
	return
}
