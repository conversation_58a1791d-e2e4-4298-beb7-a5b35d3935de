package pay

import (
	"encoding/json"
	"go.uber.org/zap"
	"gorm.io/gorm"
	localLifeModel "local-life/model"
	"notification/mq"
	"payment/model"
	"yz-go/component/log"
	"yz-go/source"
)

func ChangeBalance(business int, param model.UpdateBalance) (err error) {
	var Balance = model.Balance{}
	var action string
	var field string
	if param.OperationType == 1 {
		field = "purchasing_balance"
	}
	if param.OperationType == 2 {
		field = "settlement_balance"
	}
	if param.Action == 1 {
		action = field + "+ ?"
	}
	if param.Action == 2 {
		action = field + "- ?"
	}
	err = source.DB().Model(Balance).Where("uid = ? and type=?", param.Uid, param.PayType).Update(field, gorm.Expr(action, param.Amount)).Error

	if err != nil {
		return
	}

	source.DB().Where("uid=?", param.Uid).Where("type=?", param.PayType).First(&Balance)
	var orderIds []int
	source.DB().Model(localLifeModel.LocalLifeOrderPayInfo{}).Joins("INNER join local_life_pay_infos on local_life_pay_infos.id = local_life_order_pay_infos.local_life_pay_info_id").Where("local_life_pay_infos.pay_sn", param.PaySn).Pluck("local_life_order_pay_infos.local_life_order_id", &orderIds)
	byteOrder, _ := json.Marshal(orderIds)
	if param.OperationType == 1 {
		var purchasingData model.PurchasingBalance
		purchasingData.OrderID = string(byteOrder)
		purchasingData.Uid = param.Uid
		purchasingData.Amount = param.Amount
		//purchasingData.BusinessType = 4
		purchasingData.BusinessType = business
		purchasingData.PetSupplierID = param.PetSupplierID
		purchasingData.PayType = param.PayType
		purchasingData.Balance = Balance.PurchasingBalance
		purchasingData.Remarks = param.Remarks
		purchasingData.PaySN = param.PaySn
		if param.PayType > 2 {
			purchasingData.AccountId = param.PayType
			purchasingData.IsPlugin = 1
		}
		err = PurchasingBalanceChange(purchasingData)
	}
	return
}

func UpdateSettlementBalance(param localLifeModel.UpdateBalance) (err error) {
	var Balance = model.Balance{}
	var action string
	var field string
	if param.OperationType == 1 {
		field = "purchasing_balance"
	}
	if param.Action == 1 {
		action = field + "+ ?"
	}
	if param.Action == 2 {
		action = field + "- ?"
	}
	err = source.DB().Model(Balance).Where("uid = ? and type=?", param.Uid, param.Type).Update(field, gorm.Expr(action, param.Amount)).Error
	if err != nil {
		return
	}
	if param.OperationType == 1 {
		var purchasingData model.PurchasingBalance
		purchasingData.Uid = param.Uid
		purchasingData.Amount = param.Amount
		purchasingData.PayType = param.Type
		purchasingData.BusinessType = param.PayType
		purchasingData.PetSupplierID = param.PetSupplierID
		purchasingData.Remarks = param.Remarks
		purchasingData.IsLocalOrder = 1
		var changeBalance model.Balance
		if param.Type > 2 {
			source.DB().Where("uid=?", param.Uid).Where("type = ?", param.Type).First(&changeBalance)
		} else {
			source.DB().Where("uid=?", param.Uid).Where("type = ?", param.PayType).First(&changeBalance)
		}
		//充值
		if param.Action == 1 && param.PayType == 1 {
			purchasingData.BusinessType = 3
		}
		if param.Action == 1 && param.PayType == 2 {
			purchasingData.BusinessType = 4
		}

		purchasingData.Balance = changeBalance.PurchasingBalance
		if param.PayInfoID > 0 {
			var payInfo localLifeModel.LocalLifePayInfo

			source.DB().Select("pay_sn").First(&payInfo, "id = ?", param.PayInfoID)
			var orderIds []int

			source.DB().Model(localLifeModel.LocalLifeOrderPayInfo{}).Where("local_life_pay_info_id = ?", param.PayInfoID).Pluck("local_life_order_id", &orderIds)
			byteOrder, _ := json.Marshal(orderIds)
			purchasingData.PaySN = payInfo.PaySN
			purchasingData.OrderID = string(byteOrder)
			if len(orderIds) > 0 {
				var order localLifeModel.LocalLifeOrder
				err = source.DB().Where("id=?", uint(orderIds[0])).First(&order).Error
				if err != nil {
					log.Log().Error("UpdateSettlementBalance local life order err", zap.Any("err", err))
				} else {
					purchasingData.OrderSn = order.OrderSN
				}
			}
		}
		err = PurchasingBalanceChange(purchasingData)
	}
	return
}

func PurchasingBalanceChange(param model.PurchasingBalance) (err error) {
	err = source.DB().Create(&param).Error
	if param.BusinessType == 3 || param.BusinessType == 4 || param.BusinessType == 6 {
		err = mq.PublishMessage(param.Uid, "recharge", param.ID)
		if err != nil {
			return
		}
	}
	return
}
