package refund

import (
	"errors"
	"fmt"
	"local-life/model"
	"local-life/refund_entrance"
	"local-life/request"
	"time"
	"yz-go/source"
)

func Refund(PaySn string, amount uint, orderItemID uint) (err error) {
	var payInfo model.LocalLifePayInfo
	err = source.DB().Where("pay_sn = ?", PaySn).Find(&payInfo).Error
	// 校验
	if payInfo.Status != model.Paid && payInfo.Status != model.PartRefunded {
		err = errors.New(GetStatusName(payInfo.Status) + "状态的支付单无法执行退款操作")
		return
	}

	// 原路退款
	err = refund_entrance.RefundService(request.RefundParams{PaySN: PaySn, Amount: amount, UserID: payInfo.UserID, PayTypeID: payInfo.PayTypeID, OrderItemID: orderItemID})
	if err != nil {
		return
	}

	// 修改支付信息
	var updatePayInfo map[string]interface{}
	if amount > payInfo.Amount {
		err = errors.New(fmt.Sprintf("退款金额(￥%d)不能大于支付金额(￥%d)", amount/100, payInfo.Amount/100))
	} else {
		var payStatus model.PayStatus
		if payInfo.Amount-amount == 0 {
			payStatus = model.Refunded
		} else {
			payStatus = model.PartRefunded
		}
		updatePayInfo = map[string]interface{}{"amount": payInfo.Amount - amount, "status": payStatus, "refunded_amount": payInfo.RefundedAmount + amount, "refund_at": time.Now().Format("2006-01-02 15:04:05")}
	}
	payInfo.RefundAt = &source.LocalTime{Time: time.Now()}
	err = source.DB().Model(&model.LocalLifePayInfo{}).Where("id = ?", payInfo.ID).Updates(updatePayInfo).Error
	return
}

func GetStatusName(status model.PayStatus) string {
	Names := map[model.PayStatus]string{
		model.Refunded:   "已退款",
		model.InvalidPay: "已关闭",
		model.NotPay:     "待支付",
		model.Paid:       "已付款",
	}
	return Names[status]
}
