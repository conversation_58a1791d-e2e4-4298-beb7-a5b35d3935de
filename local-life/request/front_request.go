package request

import "local-life/model"

type ApplyBrandParams struct {
	Name               string                           `json:"name" form:"name" query:"name"`
	CertificateType    int                              `json:"certificate_type" form:"certificate_type" query:"certificate_type"`
	CertificateCode    string                           `json:"certificate_code" form:"certificate_code" query:"certificate_code"`
	BusinessLicenseImg string                           `json:"business_license_img" form:"business_license_img" query:"business_license_img"`
	RealName           string                           `json:"real_name" form:"real_name" query:"real_name"`
	Mobile             string                           `json:"mobile" form:"mobile" query:"mobile"`
	UID                uint                             `json:"uid" form:"uid" query:"uid"`
	Username           string                           `json:"username" form:"username" query:"username"`
	Password           string                           `json:"password" form:"password" query:"password"`
	ProductAudit       int                              `json:"product_audit" form:"product_audit" query:"product_audit"`
	SettlementMethod   int                              `json:"settlement_method" form:"settlement_method" query:"settlement_method"`
	BrandInfo          model.LocalLifeBrandInfoReview   `json:"brand_info" form:"brand_info" query:"brand_info"`
	Categories         model.ReviewCategories           `json:"categories" form:"categories" query:"categories"`
	LrInfo             model.LocalLifeBrandLrInfoReview `json:"lr_info" form:"lr_info" query:"lr_info"`
}
