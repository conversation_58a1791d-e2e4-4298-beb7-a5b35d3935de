package request

import (
	"local-life/model"
	yzRequest "yz-go/request"
)

type CreateSupplyChainParam struct {
	// 供应链名称
	Name string `json:"name" form:"name" binding:"required"`
}

type SupplyChainListParam struct {
	yzRequest.PageInfo
}

type SupplyChainFindParam struct {
	ID uint `json:"id" form:"id" binding:"required"`
}

type SyncCommonParam struct {
	// 供应链ID
	SupplyChainID uint `json:"supply_chain_id" form:"supply_chain_id" binding:"required"`
}

type SubmitSupplyChainParam struct {
	model.LocalLifeSyncSupplyChain
}
