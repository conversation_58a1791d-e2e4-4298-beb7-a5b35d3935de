package response

import (
	"gorm.io/gorm"
	"local-life/model"
	"region/service"
	"yz-go/source"
)

type Store struct {
	model.LocalLifeStore
	Province               string                                 `json:"province" gorm:"-"`
	City                   string                                 `json:"city" gorm:"-"`
	Area                   string                                 `json:"area" gorm:"-"`
	BrandID                uint                                   `json:"brand_id"`
	Brand                  model.LocalLifeBrand                   `json:"brand" gorm:"foreignKey:BrandID;references:ID"`
	User                   model.User                             `json:"user" gorm:"foreignKey:user_id;references:ID"`
	VerificationNum        int64                                  `json:"verification_num"`
	TotalAmount            uint                                   `json:"total_amount"`
	PersonAmount           uint                                   `json:"person_amount"` //人均消费
	StoreInvisibleProducts []model.LocalLifeStoreProductInvisible `json:"store_invisible_products" gorm:"foreignKey:StoreID;references:ID"`
	SourceName             string                                 `json:"source_name" gorm:"-"` //来源名称
	SupplyInfo             model.LocalLifeSyncSupplyChain         `json:"supply_info" gorm:"foreignKey:Source;references:ID"`
}

func (Store) TableName() string {
	return "local_life_stores"
}

type StoreBySync struct {
	model.LocalLifeStore
	StoreInvisibleProducts []model.LocalLifeStoreProductInvisible `json:"store_invisible_products" gorm:"foreignKey:StoreID;references:ID"`
}

func (StoreBySync) TableName() string {
	return "local_life_stores"
}

func (a *Store) AfterFind(tx *gorm.DB) (err error) {
	a.Province = service.GetRegionName(a.ProvinceId)
	a.City = service.GetRegionName(a.CityId)
	a.Area = service.GetRegionName(a.AreaId)
	err = tx.Model(&LocalLifeOrderVrfRecord{}).Where("store_id = ?", a.ID).Count(&a.VerificationNum).Error
	if err != nil {
		return err
	}
	err = tx.Model(&LocalLifeOrderVrfRecord{}).Select("COALESCE(SUM(store_settle_amount), 0)").Where("store_id = ?", a.ID).First(&a.TotalAmount).Error
	if err != nil {
		return err
	}
	var personCount int64
	err = tx.Model(&LocalLifeOrderVrfRecord{}).Select("DISTINCT uid").Where("store_id = ?", a.ID).Count(&personCount).Error
	if err != nil {
		return err
	}
	if personCount > 0 {
		a.PersonAmount = a.TotalAmount / uint(personCount)
	}
	if a.Source > 0 {
		a.SourceName = a.SupplyInfo.Name
	} else {
		a.SourceName = "自营"
	}
	return
}

// 核销员
type Verification struct {
	model.LocalLifeVerification
	Status int                  `json:"status" gorm:"type:int(1);size:1;default:1;comment:1开启 2关闭;"` // 1开启 2关闭
	User   model.User           `json:"user" gorm:"foreignKey:user_id;references:ID"`
	Store  model.LocalLifeStore `json:"store" gorm:"foreignKey:store_id;references:ID"`
}

func (Verification) TableName() string {
	return "local_life_verifications"
}

type ImageTag struct {
	source.Model
	BrandID      uint                     `json:"brand_id"`
	Sort         int                      `json:"sort"`
	Name         string                   `json:"name"`
	Status       int                      `json:"status"`
	TagItems     []model.LocalLifeTagItem `json:"tag_items" gorm:"foreignKey:TagID;references:ID"`
	TagItemCount int                      `json:"tag_item_count" gorm:"-"`
}

func (ImageTag) TableName() string {
	return "local_life_image_tags"
}

// 统计门店核销订单，未结算收益 已结算收益，待核销订单,支付订单数，退款订单数，在售商品
type BrandStatistics struct {
	VerifiedOrder     int64 `json:"verified_order"`      // 已核销订单
	WaitVerifiedOrder int64 `json:"wait_verified_order"` // 待核销订单
	SettledIncome     int64 `json:"settled_income"`      // 已结算收益
	WaitSettledIncome int64 `json:"wait_settled_income"` // 待结算收益
	PayOrderCount     int64 `json:"pay_order_count"`     // 支付订单数量
	RefundOrderCount  int64 `json:"refund_order_count"`  // 退款订单数
	SaleProductCount  int64 `json:"sale_product_count"`  //在售商品数量

}

// GetVerificationDetailsList 核销明细列表
type GetVerificationDetailsList struct {
	source.Model
	// 品牌ID
	BrandID uint `json:"brand_id" form:"brand_id" gorm:"index"`
	// 核销会员ID
	VUID uint `json:"v_uid" form:"v_uid" gorm:"default:0;index"`
	// 核销员ID Verification.ID
	VID uint `json:"vid" form:"vid" gorm:"default:0;column:vid;comment:核销员id;index;"`
	// 会员ID
	UID uint `json:"uid" form:"uid" gorm:"index"`
	// 订单ID
	OrderID uint `json:"order_id" form:"order_id" gorm:"index"`
	// 订单编号
	OrderSN uint `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:编号;index;"`
	// 实付金额
	PaymentAmount uint `json:"payment_amount" form:"payment_amount" gorm:"column:payment_amount;comment:实付金额(分);"`
	// 商品ID
	ProductID uint `json:"product_id" form:"product_id" gorm:"column:product_id;comment:产品id;index;"`
	// 门店ID
	StoreID uint `json:"store_id" form:"store_id" gorm:"column:store_id;comment:门店id;index;"`
	// CodeID
	CodeID uint `json:"code_id" form:"code_id" gorm:"column:code_id;comment:券码id;index;"`
	// 券码
	Code string `json:"code" form:"code" gorm:"column:code;comment:券码;type:varchar(255);size:255;"`
	// 商品名称
	ProductTitle string `json:"product_title" form:"product_title" gorm:"column:product_title;comment:商品名称;type:varchar(255);size:255;"`

	// 商品类型
	ProductType int `json:"product_type" form:"product_type" gorm:"column:product_type;comment:商品类型：1团购券2代金券3次卡;type:int;size:1;index;"`

	Verification model.LocalLifeVerification `json:"verification" gorm:"foreignKey:vid"` //核销员信息

	OrderItems LocalLifeOrderItems `json:"order_items" gorm:"foreignKey:order_id;references:order_id"` //子订单信息

	Store GetVerificationDetailsListStore `json:"store" gorm:"foreignKey:store_id"` //门店会员信息

	OrderAmount uint `json:"order_amount" form:"order_amount" gorm:"column:order_amount;"`

	TechnicalServicesFee uint `json:"technical_services_fee" form:"technical_services_fee" gorm:"column:technical_services_fee;"`
}

type GetVerificationDetailsListStore struct {
	source.Model
	Name   string     `json:"name" form:"name"`
	UserID uint       `json:"user_id" form:"user_id"`
	User   model.User `json:"user" gorm:"foreignKey:user_id;references:ID"`
}

func (GetVerificationDetailsListStore) TableName() string {
	return "local_life_stores"
}

func (GetVerificationDetailsList) TableName() string {
	return "local_life_order_vrf_records"
}
