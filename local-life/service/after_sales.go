package service

import (
	"errors"
	"fmt"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"local-life/connection"
	"local-life/model"
	"local-life/mqafter"
	"local-life/request"
	"local-life/response"
	localLifeServiceAdmin "local-life/service/admin"
	"yz-go/component/log"
	"yz-go/source"
)

/*
*

	获取当前订单可使用的售后方式
*/
func GetAfterSalesTypeNameMap(orderData model.LocalLifeOrder, orderItemID uint) (err error, data interface{}) {

	var afterSalesType = []model.AfterSalesType{}

	afterSalesType = append(afterSalesType, model.Refund)

	afterSalesTypeString := map[model.AfterSalesType]string{}

	for _, value := range afterSalesType {
		_, name := model.GetAfterSalesTypeName(value)
		afterSalesTypeString[value] = name
	}
	return nil, afterSalesTypeString
}

// GetReasons
//
// @function: GetReasons
// @description: 创建售后退货记录
// @param: afterSaleType model.AfterSaleType
// @param: isReceived bool
// @return: model.ReturnOrderExpress
func GetReasons() (reasons []model.RefundReason) {
	reasons = []model.RefundReason{
		model.DontWant,
		model.Others,
		model.ChangeOfPlans,
		model.UnclearUsageConditions,
		model.UnableToContactTheMerchant,
		model.UnableToScheduleSuitableTime,
		model.AvailableStoreDistance,
		model.OtherPlatformsMethodsOfferBetterDiscountsForPurchasing,
		model.PoorEvaluation,
		model.WorriedAboutSafetyIssues,
	}
	return
}

type OperationLocalLifeAfterSales struct {
	model.LocalLifeOrder
	request.AfterSales
	model.LocalLifeOrderItem
	response.GatherSupply
}
type Handle func(OperationLocalLifeAfterSales) error

type AfterSalesLocalLifeOperation struct {
	OperationLocalLifeAfterSales
}

var HandleFunc []Handle

func (o OperationLocalLifeAfterSales) GetBeforeHandle() []Handle {
	return HandleFunc
}
func SetBeforeHandle(handlefunc func(OperationLocalLifeAfterSales) error) {
	HandleFunc = append(HandleFunc, handlefunc)
	return
}

// ApplyAfterSales
//
// @function: ApplyAfterSales
// @description: 售后申请
// @param: ad request.AfterSales
// @return: err error
func ApplyAfterSales(requestCreateAfterSales request.AfterSales, userId uint, appId uint) (err error, afterSalesId uint) {

	err, orderItem := LocalLifeOrderItemByIdVerifyRefund(requestCreateAfterSales.OrderItemID)
	if err != nil {
		return
	}
	err, order := LocalLifeOrderByIdVerifyRefund(requestCreateAfterSales, orderItem)
	if err != nil {
		return
	}

	if requestCreateAfterSales.Num == 0 {
		err = errors.New("请填写退款商品数量")
		return
	}
	if requestCreateAfterSales.Num > orderItem.Qty {
		err = errors.New("退款商品数量大于下单数量")
		return
	}
	//通过个数计算退款金额   子订单支付金额/子订单商品数量
	requestCreateAfterSales.Amount = orderItem.Amount / orderItem.Qty * requestCreateAfterSales.Num //退款金额
	if orderItem.Amount < requestCreateAfterSales.Amount {
		log.Log().Error("退款金额大于商品金额")
		err = errors.New("退款金额大于商品金额")
		return
	}
	//如果技术服务费不等于0
	if requestCreateAfterSales.TechnicalServicesFee != 0 {
		//退款技术服务器不可大于订单的技术服务费
		if requestCreateAfterSales.TechnicalServicesFee > order.TechnicalServicesFee {
			log.Log().Error("退款的技术服务费金额大于订单技术服务费")
			err = errors.New("退款的技术服务费金额大于订单技术服务费")
			return
		}
		//新的订单存在子订单技术服务费，如果有退款的技术服务费,并且大于子订单技术服务费 就等于子订单的技术服务费金额
		if requestCreateAfterSales.TechnicalServicesFee > orderItem.TechnicalServicesFee {
			err = errors.New("退款的技术服务费金额大于子订单技术服务费")
			return
		}
	}
	var afterSalesData response.LocalLifeAfterSales
	err, afterSalesData = GetAfterSalesByOrderItemId(requestCreateAfterSales.OrderItemID)
	//如果不是查询不到的错误直接返回
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("售后查询失败" + err.Error())
		log.Log().Error("售后查询失败", zap.Any("err", err))
		return
	}
	//如果查询到记录 原纪录关闭就变为待审核继续使用
	if afterSalesData.ID > 0 {
		//关闭/驳回的重新开启
		if afterSalesData.Status == model.ClosedStatus || afterSalesData.Status == model.RejectStatus {
			afterSalesData.Status = model.WaitAuditStatus
			err = source.DB().Model(&model.LocalLifeAfterSale{}).Where("id = ?", afterSalesData.ID).Save(&afterSalesData).Error
			if err != nil {
				err = errors.New("售后修改失败" + err.Error())
				log.Log().Error("售后修改失败", zap.Any("err", err))
				return
			}
		}
		//如果不是待审核不让改
		if afterSalesData.Status != model.WaitAuditStatus {
			_, statusName := model.GetLocalLifeAfterSaleStatusName(afterSalesData.Status)
			err = errors.New(fmt.Sprintf("售后不可修改,售后状态:%s", statusName))
			log.Log().Error("售后申请", zap.Any("err", err))
			return
		}

		afterSalesData.Amount = requestCreateAfterSales.Amount
		afterSalesData.TechnicalServicesFee = requestCreateAfterSales.TechnicalServicesFee
		afterSalesData.ReasonType = requestCreateAfterSales.ReasonType
		afterSalesData.Description = requestCreateAfterSales.Description
		afterSalesData.RefundType = requestCreateAfterSales.RefundType
		afterSalesData.Reason = requestCreateAfterSales.Reason
		afterSalesData.RefundWay = requestCreateAfterSales.RefundWay
		afterSalesData.Num = requestCreateAfterSales.Num
		afterSalesData = localLifeServiceAdmin.GetLog(localLifeServiceAdmin.LogUserID{AdminId: 0, UserID: userId, AppId: appId}, afterSalesData)

		if err = UpdateAfterSales(afterSalesData); err != nil {
			log.Log().Error("修改申请记录失败!", zap.Any("err", err))
			err = errors.New("修改申请记录失败")
			return
		}
		afterSalesId = afterSalesData.ID
	} else {
		lifeAfterSales := response.LocalLifeAfterSales{}
		as := model.LocalLifeAfterSale{
			UserID:               order.UID,
			ApplicationID:        order.ApplicationID,
			ApplicationShopID:    order.ApplicationShopID,
			BrandID:              order.BrandID,
			GatherSupplyID:       order.GatherSupplyID,
			OrderSN:              order.OrderSN,
			ProductTitle:         orderItem.Title,
			ProductType:          order.OrderType,
			OrderItemID:          requestCreateAfterSales.OrderItemID,
			Amount:               requestCreateAfterSales.Amount,
			ReasonType:           requestCreateAfterSales.ReasonType,
			Description:          requestCreateAfterSales.Description,
			DetailImages:         requestCreateAfterSales.DetailImages,
			RefundType:           requestCreateAfterSales.RefundType,
			OrderID:              orderItem.OrderID,
			ProductID:            orderItem.ProductID,
			SkuID:                orderItem.SkuID,
			TechnicalServicesFee: requestCreateAfterSales.TechnicalServicesFee,
			Status:               model.WaitAuditStatus,
			Reason:               requestCreateAfterSales.Reason,
			RefundWay:            requestCreateAfterSales.RefundWay,
			Num:                  requestCreateAfterSales.Num,
		}
		lifeAfterSales.LocalLifeAfterSale = as

		lifeAfterSales = localLifeServiceAdmin.GetLog(localLifeServiceAdmin.LogUserID{AdminId: 0, UserID: userId, AppId: appId}, lifeAfterSales)

		err, lifeAfterSales = CreateAfterSales(lifeAfterSales)
		if err != nil {
			log.Log().Error("售后申请,创建失败!", zap.Any("err", err))
			err = errors.New("售后申请,创建失败")
			return
		}
		afterSalesId = lifeAfterSales.ID
	}
	//修改售后状态
	err = localLifeServiceAdmin.SaveOrderAndOrderItemRefundStatus(orderItem.ID, orderItem.OrderID, model.Refunding)
	if err != nil {
		return
	}

	if afterSalesData.ID != 0 {
		err = mqafter.PublishMessage(afterSalesData.ID, mqafter.AfterSalesUpdate, 0)
		if err != nil {
			log.Log().Error("售后修改推送消息失败!", zap.Any("err", err))
			err = errors.New("售后修改成功,推送消息失败" + err.Error())
			return
		}
	} else {
		err = mqafter.PublishMessage(afterSalesId, mqafter.AfterSalesCreate, 0)
		if err != nil {
			log.Log().Error("售后申请推送消息失败!", zap.Any("err", err))
			err = errors.New("售后申请成功,推送消息失败" + err.Error())
			return
		}
	}
	return
}

// SaveAfterSales
// @function: ApplyAfterSales
// @description: 售后修改
// @param: ad request.AfterSales
// @return: err error
func SaveAfterSales(requestCreateAfterSales request.AfterSales, userId uint, appId uint) (err error, afterSalesId uint) {

	err, orderItem := LocalLifeOrderItemByIdVerifyRefund(requestCreateAfterSales.OrderItemID)
	if err != nil {
		return
	}
	err, order := LocalLifeOrderByIdVerifyRefund(requestCreateAfterSales, orderItem)
	if err != nil {
		return
	}

	if requestCreateAfterSales.Num == 0 {
		err = errors.New("请填写退款商品数量")
		return
	}
	if requestCreateAfterSales.Num > orderItem.Qty {
		err = errors.New("退款商品数量大于下单数量")
		return
	}
	//通过个数计算退款金额   子订单支付金额/子订单商品数量
	requestCreateAfterSales.Amount = orderItem.Amount / orderItem.Qty * requestCreateAfterSales.Num //退款金额
	if orderItem.Amount < requestCreateAfterSales.Amount {
		log.Log().Error("退款金额大于商品金额")
		err = errors.New("退款金额大于商品金额")
		return
	}
	//如果技术服务费不等于0
	if requestCreateAfterSales.TechnicalServicesFee != 0 {
		//退款技术服务器不可大于订单的技术服务费
		if requestCreateAfterSales.TechnicalServicesFee > order.TechnicalServicesFee {
			log.Log().Error("退款的技术服务费金额大于订单技术服务费")
			err = errors.New("退款的技术服务费金额大于订单技术服务费")
			return
		}
		//新的订单存在子订单技术服务费，如果有退款的技术服务费,并且大于子订单技术服务费 就等于子订单的技术服务费金额
		if requestCreateAfterSales.TechnicalServicesFee > orderItem.TechnicalServicesFee {
			err = errors.New("退款的技术服务费金额大于子订单技术服务费")
			return
		}
	}
	var afterSalesData response.LocalLifeAfterSales
	err, afterSalesData = GetAfterSalesById(requestCreateAfterSales.Id)
	if err != nil {
		err = errors.New("售后查询失败" + err.Error())
		log.Log().Error("售后查询失败", zap.Any("err", err))
		return
	}
	if afterSalesData.Status == model.ClosedStatus {
		afterSalesData.Status = model.WaitAuditStatus
		err = source.DB().Model(&model.LocalLifeAfterSale{}).Where("id = ?", afterSalesData.ID).Save(&afterSalesData).Error
		if err != nil {
			err = errors.New("售后修改失败" + err.Error())
			log.Log().Error("售后修改失败", zap.Any("err", err))
			return
		}
	}
	//如果不是待审核不让改
	if afterSalesData.Status != model.WaitAuditStatus {
		_, statusName := model.GetLocalLifeAfterSaleStatusName(afterSalesData.Status)
		err = errors.New(fmt.Sprintf("售后不可修改,售后状态:%s", statusName))
		log.Log().Error("售后申请", zap.Any("err", err))
		return
	}

	afterSalesData.Amount = requestCreateAfterSales.Amount
	afterSalesData.TechnicalServicesFee = requestCreateAfterSales.TechnicalServicesFee
	afterSalesData.ReasonType = requestCreateAfterSales.ReasonType
	afterSalesData.Description = requestCreateAfterSales.Description
	afterSalesData.RefundType = requestCreateAfterSales.RefundType
	afterSalesData.Reason = requestCreateAfterSales.Reason
	afterSalesData.RefundWay = requestCreateAfterSales.RefundWay
	afterSalesData.Num = requestCreateAfterSales.Num
	afterSalesData = localLifeServiceAdmin.GetLog(localLifeServiceAdmin.LogUserID{AdminId: 0, UserID: userId, AppId: appId}, afterSalesData)

	if err = UpdateAfterSales(afterSalesData); err != nil {
		log.Log().Error("修改申请记录失败!", zap.Any("err", err))
		err = errors.New("修改申请记录失败")
		return
	}
	afterSalesId = afterSalesData.ID

	//修改售后状态
	err = localLifeServiceAdmin.SaveOrderAndOrderItemRefundStatus(orderItem.ID, orderItem.OrderID, model.Refunding)
	if err != nil {
		return
	}

	err = mqafter.PublishMessage(afterSalesData.ID, mqafter.AfterSalesUpdate, 0)
	if err != nil {
		log.Log().Error("售后修改推送消息失败!", zap.Any("err", err))
		err = errors.New("售后修改成功,推送消息失败" + err.Error())
		return
	}
	return
}

// 通过子订单id获取子订单信息 并验证是否可以退款
func LocalLifeOrderItemByIdVerifyRefund(orderItemId uint) (err error, data model.LocalLifeOrderItem) {
	err = source.DB().Where("id = ?", orderItemId).First(&data).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		err = errors.New("请选择要售后的子订单" + err.Error())
		return
	}
	if data.Used > 0 {
		log.Log().Error("已使用无法退款", zap.Any("orderItem", data))
		err = errors.New("已使用无法退款")
		return
	}
	return
}

// 通过子订单信息获取订单信息 并验证是否可以退款
func LocalLifeOrderByIdVerifyRefund(requestCreateAfterSales request.AfterSales, orderItem model.LocalLifeOrderItem) (err error, data model.LocalLifeOrder) {
	err = source.DB().Where("id = ?", orderItem.OrderID).First(&data).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		err = errors.New("获取订单信息失败" + err.Error())
		return
	}
	if data.Used > 0 {
		log.Log().Error("已使用无法退款", zap.Any("order", data))
		err = errors.New("已使用无法退款")
		return
	}
	if data.Status != model.WaitUse {
		err = errors.New("订单状态不支持售后" + model.GetStatusName(data.Status))
		return
	}
	//供应链检验
	if data.GatherSupplyID > 0 {
		var gatherSupply response.GatherSupply
		err = source.DB().Model(&response.GatherSupply{}).Where("id = ?", data.GatherSupplyID).First(&gatherSupply).Error
		if err != nil {
			log.Log().Error("供应链信息获取失败", zap.Any("err", err))
			err = errors.New("供应链信息获取失败" + err.Error())
			return
		}
		afterSalesOperation := AfterSalesLocalLifeOperation{OperationLocalLifeAfterSales: OperationLocalLifeAfterSales{data, requestCreateAfterSales, orderItem, gatherSupply}}
		// 申请前钩子,多用于供应链前置验证
		for _, handle := range afterSalesOperation.GetBeforeHandle() {
			err = handle(OperationLocalLifeAfterSales{data, requestCreateAfterSales, orderItem, gatherSupply})
			if err != nil {
				return
			}
		}
	}
	return
}

func GetAfterSalesByOrderItemId(orderItemId uint) (err error, data response.LocalLifeAfterSales) {
	err = source.DB().Model(&response.LocalLifeAfterSales{}).Where("order_item_id=?", orderItemId).First(&data).Error
	return
}
func GetAfterSalesById(id uint) (err error, data response.LocalLifeAfterSales) {
	err = source.DB().Model(&response.LocalLifeAfterSales{}).Where("id=?", id).First(&data).Error
	return
}

// GetAfterSalesAppByOrderItemId
// 采购端返回售后详情
func GetAfterSalesAppByOrderItemId(orderItemId uint) (err error, data model.LocalLifeAfterSale) {
	err = source.DB().Model(&response.LocalLifeAfterSales{}).Where("order_item_id=?", orderItemId).First(&data).Error
	return
}

// GetAfterSalesAppById
// 采购端返回售后详情
func GetAfterSalesAppById(id uint) (err error, data model.LocalLifeAfterSale) {
	err = source.DB().Model(&response.LocalLifeAfterSales{}).Where("id=?", id).First(&data).Error
	return
}

// UpdateAfterSales
// @author: [piexlmax](https://github.com/piexlmax)
// @function: UpdateAfterSales
// @description: 编辑售后信息
// @param: id uint
// @return: err error, AfterSales model.AfterSales
func UpdateAfterSales(afterSales response.LocalLifeAfterSales) (err error) {
	err = source.DB().Model(&response.LocalLifeAfterSales{}).Where("id = ?", afterSales.ID).Save(&afterSales).Error
	if err != nil {
		return
	}
	return
}

func CreateAfterSales(as response.LocalLifeAfterSales) (err error, res response.LocalLifeAfterSales) {
	err = source.DB().Model(&response.LocalLifeAfterSales{}).Create(&as).Error
	return err, as
}

/**
消息成功之后商城通知中台 中台进行改变状态
*/

func MessageSuccess(appId uint, messageSuccess connection.RequestParams) (err error) {
	err = source.DB().Where("application_id = ?", appId).Where("after_sale_id = ?", messageSuccess.Key).Where("after_sales_message_type = ?", messageSuccess.MessageType).Updates(&model.LocalLifeAfterSalesPushMessage{
		Status: 3,
	}).Error
	if err != nil {
		return
	}
	return
}

/*
*
获取所有状态不是成功的消息
*/
func GetMessageError(appId, appShopId uint) (err error, message []model.LocalLifeAfterSalesPushMessage) {
	db := source.DB()
	if appShopId != 0 {
		db = db.Where("application_shop_id = ?", appShopId)
	} else {
		db = db.Where("application_id = ?", appId)
	}
	err = db.Limit(100).Where("status in (-1,1,2)").Order("created_at asc").Find(&message).Error
	if err != nil {
		return
	}
	return
}
