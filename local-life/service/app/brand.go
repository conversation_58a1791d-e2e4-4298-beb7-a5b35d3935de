package app

import (
	"gorm.io/gorm/clause"
	"local-life/model"
	"local-life/request"
	"yz-go/source"
)

type LocalLifeBrand struct {
	source.Model
}

func GetBrandList(req request.BrandListRequest) (err error, list interface{}, total int64) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	if limit == 0 {
		limit = 10
	}
	if offset < 0 {
		offset = 0
	}
	if limit > 100 {
		limit = 100
	}
	db := source.DB().Model(&LocalLifeBrand{})
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	var brands []LocalLifeBrand
	err = db.Limit(limit).Offset(offset).Order("id desc").Find(&brands).Error
	return err, brands, total
}

func GetBrandDetail(brandID uint) (err error, brand model.LocalLifeBrandByAdminUpdate) {
	err = source.DB().Model(&model.LocalLifeBrandByAdminUpdate{}).Preload(clause.Associations).Preload("Categories.Category1").Preload("Categories.Category2").Preload("Categories.Category3").Where("id = ?", brandID).First(&brand).Error
	return
}

type LocalLifeBrandBySync struct {
	source.Model
	Categories []LocalLifeBrandCategory `json:"categories" gorm:"foreignKey:BrandID;references:ID;comment:经营范围;"`
}

func (LocalLifeBrandBySync) TableName() string {
	return "local_life_brands"
}

type LocalLifeBrandCategory struct {
	source.Model
	BrandID     uint `json:"brand_id" form:"brand_id" gorm:"column:brand_id;comment:品牌id;index;"`
	Category1ID uint `json:"category1_id" gorm:"column:category1_id;comment:一级分类id;"`
	Category2ID uint `json:"category2_id" gorm:"column:category2_id;comment:二级分类id;"`
	Category3ID uint `json:"category3_id" gorm:"column:category3_id;comment:三级分类id;"`
}

func GetBrandListBySync(req request.BrandListRequest) (err error, list interface{}, total int64) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	if limit == 0 {
		limit = 10
	}
	if offset < 0 {
		offset = 0
	}
	if limit > 100 {
		limit = 100
	}
	db := source.DB().Model(&LocalLifeBrandBySync{})
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	var brands []LocalLifeBrandBySync
	err = db.Preload(clause.Associations).Limit(limit).Offset(offset).Order("id desc").Find(&brands).Error
	return err, brands, total
}
