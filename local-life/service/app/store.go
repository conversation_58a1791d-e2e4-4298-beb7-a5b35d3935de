package app

import (
	"local-life/request"
	"local-life/response"
	"yz-go/source"
)

type LocalLifeStore struct {
	source.Model
}

func GetStoreList(req request.StoreListRequest) (err error, list interface{}, total int64) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	if limit == 0 {
		limit = 10
	}
	if offset < 0 {
		offset = 0
	}
	if limit > 100 {
		limit = 100
	}
	db := source.DB().Model(&LocalLifeStore{})
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	var brands []LocalLifeStore
	err = db.Limit(limit).Offset(offset).Order("id desc").Find(&brands).Error
	return err, brands, total
}

func GetStoreDetail(brandID uint) (err error, brand response.Store) {
	err = source.DB().Preload("StoreInvisibleProducts").Where("id = ?", brandID).First(&brand).Error
	return
}

func GetStoreDetailBySync(storeID uint) (err error, store response.StoreBySync) {
	err = source.DB().Preload("StoreInvisibleProducts").Where("id = ?", storeID).First(&store).Error
	return
}

func GetStoreDetailByIds(ids []uint) (err error, brands []response.Store) {
	err = source.DB().Preload("StoreInvisibleProducts").Where("id in (?)", ids).Find(&brands).Error
	return
}
