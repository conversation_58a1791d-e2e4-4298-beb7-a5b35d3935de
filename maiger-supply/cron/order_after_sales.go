package cron

import (
	afterSalesModel "after-sales/model"
	afterSalesRequest "after-sales/request"
	afterSalesService "after-sales/service"
	"go.uber.org/zap"
	maiGerSupplyOrder "maiger-supply/component/order"
	maiGerPkgAfter "maiger-supply/maiger-pkg/after"
	publicSupplyCommon "public-supply/common"
	publicSupply "public-supply/model"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func MaiGerAfterSalesCron() {
	// 获取所有迈戈供应链
	var gatherList []publicSupply.GatherSupply
	if err := source.DB().Where("`category_id` = ?", publicSupplyCommon.SUPPLY_MAIGER).Find(&gatherList).Error; err != nil {
		return
	}

	// 循环处理
	for _, gather := range gatherList {
		MaiGerAfterSales(gather.ID)
	}
}

func MaiGerAfterSales(gatherId uint) {
	task := cron.Task{
		Key:  "MaiGerOrderTrackCron",
		Name: "迈戈订单定时查询售后状态",
		Spec: "0 */10 * * * *",
		Handle: func(task cron.Task) {
			_ = MaiGerAfterSalesHandle(gatherId)
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)

}

func MaiGerAfterSalesHandle(gatherId uint) (err error) {
	m := maiGerSupplyOrder.MaiGerSupply{}

	if err = m.InitSetting(gatherId); err != nil {
		log.Log().Error("迈戈订单定时查询售后状态:初始化配置失败", zap.Any("gatherId", gatherId), zap.Any("err", err))
		return
	}

	var afters []afterSalesModel.AfterSalesMigration
	if err = source.DB().Where("status != ? and source = ? and syn_after_sales_id_string != ''", 4, publicSupplyCommon.SUPPLY_MAIGER).Order("id desc").Find(&afters).Error; err != nil {
		return
	}

	for _, after := range afters {
		detailParams := maiGerPkgAfter.DetailParams{
			AccessToken: m.AccessToken,
			Domain:      m.Host,
			AfterId:     after.SynAfterSalesIdString,
		}

		var detailResult maiGerPkgAfter.DetailResult
		if detailResult, err = maiGerPkgAfter.Detail(detailParams); err != nil {
			log.Log().Error("迈戈供应链查看售后详情失败", zap.Any("afterId", after.ID), zap.Any("err", err))
			continue
		}

		log.Log().Info("迈戈供应链查看售后详情", zap.Any("afterId", after.ID), zap.Any("detailResult", detailResult))

		// 退货退款
		if detailResult.ReturnType == 0 {
			// 1-待处理；2-处理中；3-已处理；4-已完成；6-取消售后
			if detailResult.AfterStatus == 2 {
				addressModel := afterSalesModel.ShopAddress{
					IsShow:   1,
					Contacts: detailResult.SupplierAddressVo.UserName,
					Tel:      detailResult.SupplierAddressVo.UserPhone,
					Address:  detailResult.SupplierAddressVo.Province + detailResult.SupplierAddressVo.City + detailResult.SupplierAddressVo.District + detailResult.SupplierAddressVo.Town + detailResult.SupplierAddressVo.Address,
				}
				source.DB().FirstOrCreate(&addressModel)

				_ = audit(after.ID, addressModel.ID)
			}
			if detailResult.AfterStatus == 4 {
				afterSalesSend := afterSalesRequest.AfterSalesSend{
					Id: after.ID,
				}

				_ = afterSalesService.Receive(afterSalesSend, 0, 0)
				_ = refund(after.ID)
			}
		}

		// 换货
		if detailResult.ReturnType == 2 {
			if detailResult.AfterStatus == 2 {
				addressModel := afterSalesModel.ShopAddress{
					IsShow:   1,
					Contacts: detailResult.SupplierAddressVo.UserName,
					Tel:      detailResult.SupplierAddressVo.UserPhone,
					Address:  detailResult.SupplierAddressVo.Province + detailResult.SupplierAddressVo.City + detailResult.SupplierAddressVo.District + detailResult.SupplierAddressVo.Town + detailResult.SupplierAddressVo.Address,
				}
				source.DB().FirstOrCreate(&addressModel)

				_ = audit(after.ID, addressModel.ID)
			}

			// 没有状态判断，只能判断SupplierLogisticsList
			if len(detailResult.SupplierLogisticsList) > 0 {
				SupplierLogisticsListItem := detailResult.SupplierLogisticsList[0]

				afterSalesSend := afterSalesRequest.AfterSalesSend{
					Id:          after.ID,
					Num:         after.BarterNum,
					CompanyName: SupplierLogisticsListItem.ShippingName,
					CompanyCode: SupplierLogisticsListItem.ShippingName,
					ExpressNo:   SupplierLogisticsListItem.LogisticsNo,
				}

				_ = afterSalesService.Receive(afterSalesSend, 0, 0)
			}
		}

		// 退款
		if detailResult.ReturnType == 7 {
			// 0-退款中；1-退款成功；2-退款失败；3-退款取消；4-未退款
			if detailResult.RefundStatus == 1 {
				_ = audit(after.ID, 0)
				_ = refund(after.ID)
			}
		}
	}

	// yiyatong/cron/OrderAfterSale.go:23
	return nil
}

// 审核通过
func audit(afterSaleId, addressId uint) (err error) {
	afterSalesAudit := afterSalesModel.AfterSalesAudit{
		Model:             source.Model{ID: afterSaleId},
		ShippingAddressID: addressId,
	}

	if err = afterSalesService.PassAudit(afterSalesAudit, 0, 0); err != nil {
		log.Log().Info("迈戈供应链售后审核通过失败", zap.Any("afterId", afterSaleId), zap.Any("err", err))
		return
	}
	return
}

// 退款
func refund(afterSaleId uint) (err error) {
	afterSales := afterSalesModel.AfterSales{
		Model: source.Model{ID: afterSaleId},
	}

	var returnMsg string
	if err, returnMsg = afterSalesService.Refund(afterSales, 0, 0); err != nil {
		log.Log().Info("迈戈供应链售后退款失败", zap.Any("afterId", afterSaleId), zap.Any("returnMsg", returnMsg), zap.Any("err", err))
		return
	}
	return
}
