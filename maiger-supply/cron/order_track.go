package cron

import (
	"go.uber.org/zap"
	maiGerSupplyOrder "maiger-supply/component/order"
	maiGerPkgOrder "maiger-supply/maiger-pkg/order"
	"order/api/v1"
	orderModel "order/model"
	orderRequest "order/request"
	publicSupplyCommon "public-supply/common"
	publicSupply "public-supply/model"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func MaiGerOrderTrackCron() {
	// 获取所有迈戈供应链 供应链
	var gatherList []publicSupply.GatherSupply
	if err := source.DB().Where("`category_id` = ?", publicSupplyCommon.SUPPLY_MAIGER).Find(&gatherList).Error; err != nil {
		return
	}

	// 循环处理
	for _, gather := range gatherList {
		MaiGerOrderTrack(gather.ID)
	}
}

func MaiGerOrderTrack(gatherId uint) {
	task := cron.Task{
		Key:  " MaiGerOrderTrackCron",
		Name: "迈戈供应链订单定时查询物流状态",
		Spec: "0 */10 * * * *",
		Handle: func(task cron.Task) {
			MaiGerOrderTrackHandle(gatherId)
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func MaiGerOrderTrackHandle(gatherId uint) {
	var err error

	m := maiGerSupplyOrder.MaiGerSupply{}
	if err = m.InitSetting(gatherId); err != nil {
		log.Log().Error("迈戈供应链订单定时查询物流状态:初始化配置失败", zap.Any("gatherId", gatherId), zap.Any("err", err))
		return
	}

	var orders []orderModel.Order
	if err = source.DB().Preload("OrderItems").Where("status = 1 and gather_supply_id = ?", gatherId).Find(&orders).Error; err != nil {
		log.Log().Error("迈戈供应链供应链定时查询物流，查询订单失败", zap.Any("gatherId", gatherId), zap.Any("err", err))
		return
	}

	if len(orders) == 0 {
		return
	}

	for _, order := range orders {
		if order.SendStatus == 1 {
			continue
		}

		trackParams := maiGerPkgOrder.TrackParams{
			AccessToken: m.AccessToken,
			Domain:      m.Host,
			OrderSn:     order.GatherSupplySN,
		}

		var trackResult []maiGerPkgOrder.TrackResult
		if trackResult, err = maiGerPkgOrder.Track(trackParams); err != nil {
			log.Log().Error("迈戈供应链查询订单配送信息失败", zap.Any("OrderId", order.ID), zap.Any("err", err))
			continue
		}

		if len(trackResult) == 0 {
			log.Log().Error("迈戈供应链查询订单配送信息失败", zap.Any("OrderId", order.ID), zap.Any("err", "未查询到物流信息"))
			continue
		}

		log.Log().Info("迈戈供应链查询订单配送信息结果", zap.Any("OrderId", order.ID), zap.Any("trackResult", trackResult))

		waybillCode := trackResult[0]
		if waybillCode.Carrier == "" || waybillCode.LogisticsNo == "" {
			log.Log().Error("迈戈供应链订单定时查询物流状态:物流信息为空", zap.Any("OrderId", order.ID), zap.Any("trackResult", trackResult))
			continue
		}

		var code string
		if err, code = maiGerSupplyOrder.ExpressList(waybillCode.Carrier); err != nil {
			log.Log().Error("迈戈供应链订单定时查询物流状态:获取物流公司错误", zap.Any("OrderId", order.ID), zap.Any("err", err))
			continue
		}

		if code == "" {
			log.Log().Error("迈戈供应链 订单定时查询物流状态:物流公司不存在", zap.Any("OrderId", order.ID))
			continue
		}

		for _, item := range order.OrderItems {
			if item.SendStatus == 1 {
				continue
			}

			expressSentParams := v1.HandleOrderRequest{
				OrderID:      order.ID,
				CompanyCode:  code,
				ExpressNo:    waybillCode.LogisticsNo,
				OrderItemIDs: []orderRequest.OrderItemSendInfo{{ID: item.ID, Num: item.Qty}},
			}
			if err = v1.CallBackSendOrder(expressSentParams); err != nil {
				log.Log().Error("订单发货失败", zap.Any("OrderId", order.ID), zap.Any("OrderItemId", item.ID), zap.Any("err", err))
				continue
			}
		}
	}

	// stbz-supply/cron/order_deliver.go:202
}
