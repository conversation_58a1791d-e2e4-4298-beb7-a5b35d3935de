package after

import (
	"encoding/json"
	"fmt"
	maigerpkg "maiger-supply/maiger-pkg"
)

type CancelParams struct {
	AccessToken string
	Domain      string
	AfterId     string // 售后ID
}

// 3.5 取消售后

func Cancel(p CancelParams) (err error) {
	router := "open/api/after/cancellation"

	params := map[string]interface{}{
		"afterId": p.AfterId,
	}

	// 执行HTTP请求
	resp, err := maigerpkg.GetJson(p.Domain, router, p.AccessToken, params)
	if err != nil {
		return
	}

	var response CancelResponse
	if err = json.Unmarshal(resp, &response); err != nil {
		return
	}

	if response.Code != "100" {
		err = fmt.<PERSON><PERSON><PERSON>("取消售后失败，code：%s，原因：%s", response.Code, response.Message)
		return
	}

	if response.Data != true {
		err = fmt.Errorf("取消售后失败，code：%s，原因：%s", response.Code, response.Message)
		return
	}

	return
}

type CancelResponse struct {
	Data    bool   `json:"data"`
	Code    string `json:"code"`    // 返回编码
	Message string `json:"message"` // 返回说明
}
