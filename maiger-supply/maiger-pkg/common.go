package maiger_pkg

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
)

func Token(domain, router string, params map[string]interface{}) ([]byte, error) {
	return PostForm(domain, router, "", params)
}

func GetJson(domain, router, token string, p map[string]interface{}) ([]byte, error) {
	// 构建查询参数
	query, err := urlValues(p)
	if err != nil {
		return nil, err
	}

	// 拼接完整的请求 URL
	fullUrl := domain + "/" + router
	if len(query) > 0 {
		fullUrl += "?" + query.Encode()
	}

	// 创建 HTTP 请求
	req, err := http.NewRequest("GET", fullUrl, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %v", err)
	}

	// 设置请求头
	req.Header.Set("OPEN-API-AUTH", token)
	//req.Header.Set("Content-Type", "application/json")

	// 创建 HTTP 客户端并发送请求
	client := &http.Client{}

	var resp *http.Response
	if resp, err = client.Do(req); err != nil {
		return nil, fmt.Errorf("error sending request: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应数据
	var body []byte
	if body, err = io.ReadAll(resp.Body); err != nil {
		return nil, fmt.Errorf("error reading response: %v", err)
	}

	return body, nil
}

func PostForm(domain, router, token string, p map[string]interface{}) ([]byte, error) {
	// 将数据编码为 x-www-form-urlencoded 格式
	query, err := urlValues(p)
	if err != nil {
		return nil, err
	}

	// 拼接完整的请求 URL
	fullUrl := domain + "/" + router

	// 创建请求
	req, err := http.NewRequest("POST", fullUrl, bytes.NewBufferString(query.Encode()))
	if err != nil {
		return nil, fmt.Errorf("error creating request: %v", err)
	}

	// 设置请求头
	if token != "" {
		req.Header.Set("OPEN-API-AUTH", token)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 创建 HTTP 客户端并发送请求
	client := &http.Client{}

	var resp *http.Response
	if resp, err = client.Do(req); err != nil {
		return nil, fmt.Errorf("error sending request: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应数据
	var body []byte
	if body, err = io.ReadAll(resp.Body); err != nil {
		return nil, fmt.Errorf("error reading response: %v", err)

	}

	return body, nil
}

func PostJson(domain, router, token string, params interface{}) ([]byte, error) {
	// 将数据编码为 JSON
	jsonBytes, err := json.Marshal(params)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal JSON: %v", err)
	}

	// 拼接完整的请求 URL
	fullUrl := domain + "/" + router

	// 创建请求
	req, err := http.NewRequest("POST", fullUrl, bytes.NewBuffer(jsonBytes))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// 设置请求头
	req.Header.Set("OPEN-API-AUTH", token)
	req.Header.Set("Content-Type", "application/json")

	// 创建 HTTP 客户端并发送请求
	client := &http.Client{}

	var resp *http.Response
	if resp, err = client.Do(req); err != nil {
		return nil, fmt.Errorf("error sending request: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应数据
	var body []byte
	if body, err = io.ReadAll(resp.Body); err != nil {
		return nil, fmt.Errorf("error reading response: %v", err)
	}

	return body, nil
}

func urlValues(p map[string]interface{}) (url.Values, error) {
	query := url.Values{}
	for key, value := range p {
		strValue, err := convertToString(value)
		if err != nil {
			return nil, fmt.Errorf("error converting key %s: %v", key, err)
		}
		query.Set(key, strValue)
	}
	return query, nil
}

// convertToString 将接口类型转换为字符串类型
func convertToString(value interface{}) (string, error) {
	switch v := value.(type) {
	case string:
		return v, nil
	case bool:
		return strconv.FormatBool(v), nil
	case int:
		return strconv.Itoa(v), nil
	case uint:
		return strconv.FormatUint(uint64(v), 10), nil
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64), nil
	default:
		return "", fmt.Errorf("unsupported type: %T", value)
	}
}
