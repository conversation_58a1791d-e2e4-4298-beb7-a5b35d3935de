package product

import (
	"encoding/json"
	"fmt"
	maigerpkg "maiger-supply/maiger-pkg"
)

type BrandPageParams struct {
	AccessToken string `json:"accessToken"`
	Domain      string
	PageNum     int    `json:"pageNum"`   // 页码
	PageSize    int    `json:"pageSize"`  // 每页数量
	BrandId     string `json:"brandId"`   // 品牌ID
	BrandName   string `json:"brandName"` // 品牌名称
}

// 4.1 查询品牌列表

func BrandPage(p BrandPageParams) (result BrandPageResult, err error) {
	router := "open/api/product/brand/listPage"

	params := map[string]interface{}{
		"pageNum":   p.PageNum,
		"pageSize":  p.PageSize,
		"brandId":   p.BrandId,
		"brandName": p.BrandName,
	}

	// 执行HTTP请求
	resp, err := maigerpkg.PostForm(p.Domain, router, p.AccessToken, params)
	if err != nil {
		return
	}

	var response BrandPageResponse
	if err = json.Unmarshal(resp, &response); err != nil {
		return
	}

	if response.Code != "100" {
		err = fmt.Errorf("查询品牌列表失败，code：%s，原因：%s", response.Code, response.Message)
		return
	}

	return response.Data, nil
}

type BrandPageResponse struct {
	Data    BrandPageResult `json:"data"`    // 分页数据
	Code    string          `json:"code"`    // 返回编码
	Message string          `json:"message"` // 返回说明
}

type BrandPageResult struct {
	PageNum   int     `json:"pageNum"`   // 页码
	PageSize  int     `json:"pageSize"`  // 每页条数
	Total     int     `json:"total"`     // 总数
	TotalPage int     `json:"totalPage"` // 总页数
	Brands    []Brand `json:"data"`      // 品牌列表
}

type Brand struct {
	BrandId   string `json:"brandId"`   // 品牌ID
	BrandName string `json:"brandName"` // 品牌名称
}
