package product

import (
	"encoding/json"
	"fmt"
	maigerpkg "maiger-supply/maiger-pkg"
)

type GoodsDescParams struct {
	AccessToken string `json:"accessToken"`
	Domain      string
	GoodsId     string `json:"goodsId"`
}

// 4.5 查询商品描述

func GoodsDesc(p GoodsDescParams) (result GoodsDescResult, err error) {
	router := "open/api/product/goods/findDetail"

	params := map[string]interface{}{
		"goodsId": p.GoodsId,
	}

	// 执行HTTP请求
	resp, err := maigerpkg.GetJson(p.Domain, router, p.AccessToken, params)
	if err != nil {
		return
	}

	var response GoodsDescResponse
	if err = json.Unmarshal(resp, &response); err != nil {
		return
	}

	if response.Code != "100" {
		err = fmt.Errorf("查询商品描述失败，code：%s，原因：%s", response.Code, response.Message)
		return
	}

	return response.Data, nil
}

type GoodsDescResponse struct {
	Code    string          `json:"code"`    // 返回编码
	Message string          `json:"message"` // 具体内容
	Data    GoodsDescResult `json:"data"`    // 返回数据
}

type GoodsDescResult struct {
	GoodsDesc            string                 `json:"goodsDesc"`            // 品牌Id
	GoodsDescType        int                    `json:"goodsDescType"`        // 商品描述类型
	GoodsDescGalleryList []GoodsDescGalleryList `json:"goodsDescGalleryList"` // 商品详情图片
}

type GoodsDescGalleryList struct {
	GoodsID string `json:"goodsId"` // 商品ID
	ImgUrl  string `json:"imgUrl"`  // 图片地址
}
