package token

import (
	"encoding/json"
	"fmt"
	maigerpkg "maiger-supply/maiger-pkg"
)

func GetAccessToken(domain, clientId, clientSecret string) (result TokenResult, err error) {
	router := "open/api/oauth/token"

	params := map[string]interface{}{
		"grant_type":    "client_credentials",
		"client_id":     clientId,
		"client_secret": clientSecret,
	}

	// 执行HTTP请求
	resp, err := maigerpkg.Token(domain, router, params)
	if err != nil {
		return
	}

	var response TokenResponse
	if err = json.Unmarshal(resp, &response); err != nil {
		return
	}

	if response.Code != "100" {
		err = fmt.Errorf("获取access_token失败，code：%s，原因：%s", response.Code, response.Message)
		return
	}

	return response.Data, nil
}

type TokenResponse struct {
	Data    TokenResult `json:"data"`
	Code    string      `json:"code"`
	Message string      `json:"message"`
}

type TokenResult struct {
	AccessToken  string `json:"accessToken"`
	RefreshToken string `json:"refreshToken"`
}
