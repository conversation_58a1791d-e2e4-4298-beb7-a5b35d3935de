package f

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"material-distribute/service"
	smallShopServiceTool "small-shop/service/tools"
	"yz-go/component/log"
	yzResponse "yz-go/response"
)

type ProductPosterParam struct {
	Id uint `json:"id"` // 商品id
	Qr uint `json:"qr"` // 1：H5海报，2：小程序海报
}

func ProductPoster(c *gin.Context) {
	var p ProductPosterParam
	if err := c.ShouldBindJSON(&p); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	domain := c.Request.Header.Get("X-Forwarded-Proto") + "://" + c.Request.Host

	var err error
	var link string
	if p.Qr == 2 {
		err, link = smallShopServiceTool.WXA_ProductDetailPoster(p.Id, domain)
	} else {
		err, link = smallShopServiceTool.H5_ProductDetailPoster(p.Id, domain)
	}

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}

func CenterList(c *gin.Context) {
	var search service.CenterListSearch
	if err := c.ShouldBindJSON(&search); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, total, list := service.CenterList(search); err != nil {
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     search.Page,
			PageSize: search.PageSize,
		}, "获取成功", c)
	}
}

func CenterGroup(c *gin.Context) {
	var search service.CenterGroupSearch
	if err := c.ShouldBindJSON(&search); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list := service.CenterGroup(search); err != nil {
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(list, c)
	}
}

func CenterDetail(c *gin.Context) {
	var p service.CenterDetailParams
	if err := c.ShouldBindJSON(&p); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.CenterDetail(p); err != nil {
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func CenterResource(c *gin.Context) {
	var p service.CenterDetailParams
	if err := c.ShouldBindJSON(&p); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, link := service.CenterResource(p.Id); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}
