package v1

import (
	"errors"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"material-distribute/model"
	"material-distribute/service"
	"material-distribute/sync/request"
	service2 "material-distribute/sync/service"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

func DeleteGroup(c *gin.Context) {
	var requestData model.MaterialGroup
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteGroup(requestData); err != nil {
		yzResponse.FailWithMessage("删除失败", c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

func UpdateGroup(c *gin.Context) {
	var requestData model.MaterialGroup
	err := c.ShouldBind<PERSON>(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateGroup(requestData); err != nil {
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

func CreateGroup(c *gin.Context) {
	var requestData model.MaterialGroup
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.CreateGroup(requestData); err != nil {
		yzResponse.FailWithMessage("创建失败", c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

func SavaBaseSetting(c *gin.Context) {
	var requestData model.BaseSetting
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.SavaBaseSetting(requestData); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}
func GetBaseSetting(c *gin.Context) {

	err, data := service.GetBaseSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func List(c *gin.Context) {
	var requestData model.Search
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, data, total := service.List(requestData); err != nil {
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     data,
			Total:    total,
			Page:     requestData.Page,
			PageSize: requestData.PageSize,
		}, "获取成功", c)

	}
}

func GetGroup(c *gin.Context) {
	var requestData model.Search
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, data, total := service.GetGroup(requestData); err != nil {
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     data,
			Total:    total,
			Page:     requestData.Page,
			PageSize: requestData.PageSize,
		}, "获取成功", c)

	}
}

func AppList(c *gin.Context) {
	var requestData model.Search
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)
	requestData.AppId = appID
	if err, data, total := service.AppList(requestData); err != nil {
		yzResponse.FailWithMessage("获取失败"+err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     data,
			Total:    total,
			Page:     requestData.Page,
			PageSize: requestData.PageSize,
		}, "获取成功", c)

	}
}

func VideoDetail(c *gin.Context) {
	var requestData model.DeleteStorageData
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)
	requestData.AppID = appID
	if err, data := service.VideoDetail(requestData); err != nil {
		yzResponse.FailWithMessage("获取失败"+err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)

	}
}
func AddStorage(c *gin.Context) {
	var requestData model.MaterialStorage
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)
	requestData.AppID = appID
	if err = service.AddStorage(requestData); err != nil {
		yzResponse.FailWithMessage("添加失败", c)
		return
	} else {
		yzResponse.Ok(c)

	}
}

func DeleteStorage(c *gin.Context) {
	var requestData model.DeleteStorageData
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)
	requestData.AppID = appID
	if err = service.DeleteStorage(requestData); err != nil {
		yzResponse.FailWithMessage("添加失败", c)
		return
	} else {
		yzResponse.Ok(c)

	}
}

func Create(c *gin.Context) {
	var requestData model.Material
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.Create(requestData); err != nil {
		yzResponse.FailWithMessage("创建失败", c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

func Update(c *gin.Context) {
	var requestData model.Material
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.Update(requestData); err != nil {
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

func Delete(c *gin.Context) {
	var requestData model.Material
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.Delete(requestData); err != nil {
		yzResponse.FailWithMessage("删除失败", c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

func GetSupplyList(c *gin.Context) {
	var requestData request.SupplySearch
	err := c.ShouldBindQuery(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list := service2.GetSupplyList(requestData.Name); err != nil {
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"list": list}, c)
	}
}
