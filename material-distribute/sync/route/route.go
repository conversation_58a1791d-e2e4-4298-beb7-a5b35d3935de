package route

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"material-distribute/sync/model"
	"material-distribute/sync/request"
	"material-distribute/sync/service"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

func FindSyncSetting(c *gin.Context) {
	err, setting := service.GetSyncSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取设置失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"setting": setting}, c)
}

func UpdateSyncSetting(c *gin.Context) {
	var setting model.MaterialSyncSetting
	err := c.ShouldBindJSON(&setting)
	if err != nil {
		return
	}
	err = service.SaveSyncSetting(setting)
	if err == nil {
		yzResponse.OkWithMessage("修改成功", c)
	} else {
		yzResponse.FailWithMessage("修改失败", c)
	}
}

func UpdateMaterial(c *gin.Context) {
	var requestData request.UpdateMaterial
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateMaterial(requestData); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("后台更新中，稍后查看", c)
	}
}

func ImportMaterial(c *gin.Context) {
	var requestData request.ImportMaterial
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.ImportMaterial(requestData); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("后台同步中，稍后查看", c)
	}
}

func GetMaterialDetail(c *gin.Context) {
	var requestData request.MaterialDetail
	err := c.ShouldBindQuery(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, material := service.GetMaterialDetail(requestData)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取素材失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"material": material}, c)
}

func GetMaterialList(c *gin.Context) {
	var requestData request.MaterialSearch
	err := c.ShouldBindQuery(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, data, total := service.GetMaterialList(requestData); err != nil {
		yzResponse.FailWithMessage("获取失败"+err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     data,
			Total:    total,
			Page:     requestData.Page,
			PageSize: requestData.PageSize,
		}, "获取成功", c)
	}
}

func ExportMaterialList(c *gin.Context) {
	var requestData request.ExportMaterial
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, materials := service.ExportMaterialListByApp(utils.GetAppID(c), requestData.Ids); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{
			"materials": materials,
		}, "获取成功", c)
	}
}

func GetSynDetail(c *gin.Context) {
	var requestData request.MaterialDetail
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)
	requestData.AppID = appID
	err, material := service.GetMaterialDetailByApp(requestData)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取素材失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"material": material}, c)
}

func GetSyncList(c *gin.Context) {
	var requestData request.MaterialSearch
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)
	requestData.AppID = appID
	if err, data, total := service.GetMaterialListByApp(requestData); err != nil {
		yzResponse.FailWithMessage("获取失败"+err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     data,
			Total:    total,
			Page:     requestData.Page,
			PageSize: requestData.PageSize,
		}, "获取成功", c)
	}
}
