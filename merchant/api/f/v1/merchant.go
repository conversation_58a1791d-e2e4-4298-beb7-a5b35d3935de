package v1

import (
	"errors"
	"github.com/gin-gonic/gin"
	"merchant/model"
	"merchant/request"
	"merchant/service"
	"strconv"
	v1 "user/api/f/v1"
	yzResponse "yz-go/response"
)

// 验证会员是否为招商
func VerifyIdentity(c *gin.Context) {
	uid := v1.GetUserID(c)
	if err, res := service.VerifyIdentity(uid); err != nil {
		//log.log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"res": res}, c)
	}
}

// 获取分销数据
func GetCenterInfo(c *gin.Context) {
	var pageInfo request.ApiAwardSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 等级名称, 分红设置
	uid := v1.GetUserID(c)
	pageInfo.Uid = uid
	var merchant model.Merchant
	err, merchant = service.GetMerchantByUserId(uid)
	if err != nil {
		err = errors.New("查询招商失败")
		yzResponse.FailWithMessage(err.Error(), c)
	}
	resData := make(map[string]string)
	resData["level_name"] = merchant.LevelInfo.Name
	supplierSettleInfo := merchant.LevelInfo.SupplierSettleInfo
	supplierAwardSetting := ""
	if supplierSettleInfo.AmountSwitch == 1 {
		supplierAwardSetting += "[订单实际支付金额]"
		if supplierSettleInfo.CostSwitch == 1 {
			supplierAwardSetting += "减[成本]"
		}
		if supplierSettleInfo.FreightSwitch == 1 {
			supplierAwardSetting += "减[运费]"
		}
		supplierAwardSetting += strconv.Itoa(supplierSettleInfo.FormulaRatio/100) + "%"
	}
	if supplierSettleInfo.SupplierRebateSwitch == 1 {
		supplierAwardSetting += "、供应商扣点" + strconv.Itoa(supplierSettleInfo.SupplierRebateRatio/100) + "%"
	}
	if supplierSettleInfo.BuyServiceSwitch == 1 {
		supplierAwardSetting += "、采购服务费" + strconv.Itoa(supplierSettleInfo.BuyServiceRatio/100) + "%"
	}
	resData["supplier_settle"] = supplierAwardSetting

	// 累计支付订单金额, 已完成订单金额
	var paidAmountTotal, receivedAmountTotal int64
	err, paidAmountTotal, receivedAmountTotal = service.GetChildOrderInfo(uid)
	if err != nil {
		err = errors.New("查询直推下级供应商订单数据失败")
		yzResponse.FailWithMessage(err.Error(), c)
	}
	resData["paid_amount_total"] = strconv.FormatInt(paidAmountTotal, 10)
	resData["received_amount_total"] = strconv.FormatInt(receivedAmountTotal, 10)
	// 今日分成, 累计分成, 已结算分成, 未结算分成
	var todayAmountTotal, settleAmountTotal, finishAmountTotal, waitAmountTotal int64
	err, todayAmountTotal, settleAmountTotal, finishAmountTotal, waitAmountTotal = service.GetStatistic(uid)
	if err != nil {
		err = errors.New("统计分成数据失败")
		yzResponse.FailWithMessage(err.Error(), c)
	}
	resData["today_amount_total"] = strconv.FormatInt(todayAmountTotal, 10)
	resData["settle_amount_total"] = strconv.FormatInt(settleAmountTotal, 10)
	resData["finish_amount_total"] = strconv.FormatInt(finishAmountTotal, 10)
	resData["wait_amount_total"] = strconv.FormatInt(waitAmountTotal, 10)
	// 搜索, 订单号,下单会员id/手机号, 分成状态, 分成类型
	// 分红金额合计(随搜索条件变动)
	// 列表:时间, 订单号, 下单会员, 订单金额, 分红基数, 分成比例, 分成金额, 分成状态
	if err, list, total, amountTotal := service.GetAwardListByApi(pageInfo); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{
			"center_info":  resData,
			"list":         list,
			"total":        total,
			"amount_total": amountTotal,
			"page":         pageInfo.Page,
			"pageSize":     pageInfo.PageSize,
		}, "获取成功", c)
	}
}
