package v1

import (
	"github.com/gin-gonic/gin"
	"merchant/request"
	"merchant/service"
	yzResponse "yz-go/response"
)

// 分页获取招商分成列表
func GetAwardList(c *gin.Context) {
	var pageInfo request.AwardSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total, amountTotal := service.GetAwardList(pageInfo); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{
			"list":         list,
			"total":        total,
			"amount_total": amountTotal,
			"page":         pageInfo.Page,
			"pageSize":     pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// 导出招商分成
func ExportAwardList(c *gin.Context) {
	var pageInfo request.AwardSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, link := service.ExportAwardList(pageInfo); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}
