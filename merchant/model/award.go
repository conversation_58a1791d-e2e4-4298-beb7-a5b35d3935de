package model

import (
	"gorm.io/gorm"
	"merchant/supplier"
	"merchant/user"
	"yz-go/source"
)

const (
	Wait = iota
	Settled
	Lost = -1
)

const (
	_ = iota
	SettleTypeOrder
	SettleTypeService
	SettleTypeSupplier
)

type MerchantAwardMigration struct {
	source.Model
	Uid            uint              `json:"uid" gorm:"column:uid;comment:会员id;index;"`
	SupplierID     uint              `json:"supplier_id" gorm:"column:supplier_id;comment:供应商id;index;"`
	LevelID        uint              `json:"level_id" gorm:"column:level_id;comment:等级id;"`
	LevelName      string            `json:"name" gorm:"name;comment:产生奖励时的等级名称;type:varchar(50);size:50;"`
	OrderID        uint              `json:"order_id" gorm:"column:order_id;comment:订单id;index;"`
	OrderSN        uint              `json:"order_sn" gorm:"column:order_sn;comment:编号;"`
	OrderAmount    uint              `json:"order_amount" gorm:"column:order_amount;comment:订单总金额(分);"`
	SettleAmount   uint              `json:"settle_amount" gorm:"column:settle_amount;comment:分成基数(分);"`
	SettleType     int               `json:"settle_type" gorm:"column:settle_type;comment:分成类型1:订单分成2:采购技术服务费分成3:供应商扣点分成;"`
	SettleTypeName string            `json:"settle_type_name" gorm:"-"`
	Ratio          int               `json:"ratio" gorm:"column:ratio;comment:分成比例;"`
	Amount         uint              `json:"amount" gorm:"column:amount;comment:分成金额"`
	Status         int               `json:"status" gorm:"column:status;comment:分红状态 0:未结算 1：已结算 -1:已失效;index;"`
	StatusName     string            `json:"status_name" gorm:"-"`
	SettleDays     int               `json:"settle_days" gorm:"column:settle_days;comment:结算天数;"`
	StatementAt    *source.LocalTime `json:"statement_at"` // 结算时间
}

func (MerchantAwardMigration) TableName() string {
	return "merchant_awards"
}

type MerchantAward struct {
	source.Model
	Uid            uint              `json:"uid" gorm:"column:uid;comment:会员id;index;"`
	SupplierID     uint              `json:"supplier_id" gorm:"column:supplier_id;comment:供应商id;index;"`
	LevelID        uint              `json:"level_id" gorm:"column:level_id;comment:等级id;"`
	LevelName      string            `json:"name" gorm:"name;comment:产生奖励时的等级名称;type:varchar(50);size:50;"`
	OrderID        uint              `json:"order_id" gorm:"column:order_id;comment:订单id;index;"`
	OrderSN        uint              `json:"order_sn" gorm:"column:order_sn;comment:编号;"`
	OrderAmount    uint              `json:"order_amount" gorm:"column:order_amount;comment:订单总金额(分);"`
	SettleAmount   uint              `json:"settle_amount" gorm:"column:settle_amount;comment:分成基数(分);"`
	SettleType     int               `json:"settle_type" gorm:"column:settle_type;comment:分成类型1:订单分成2:采购技术服务费分成3:供应商扣点分成;"`
	SettleTypeName string            `json:"settle_type_name" gorm:"-"`
	Ratio          int               `json:"ratio" gorm:"column:ratio;comment:分成比例;"`
	Amount         uint              `json:"amount" gorm:"column:amount;comment:分成金额"`
	Status         int               `json:"status" gorm:"column:status;comment:分红状态 0:未结算 1：已结算 -1:已失效;index;"`
	StatusName     string            `json:"status_name" gorm:"-"`
	SettleDays     int               `json:"settle_days" gorm:"column:settle_days;comment:结算天数;"`
	StatementAt    *source.LocalTime `json:"statement_at"` // 结算时间
	UserInfo       user.User         `json:"user_info" gorm:"foreignKey:Uid"`
	SupplierInfo   supplier.Supplier `json:"supplier_info" gorm:"foreignKey:SupplierID"`
}

func (award *MerchantAward) AfterFind(tx *gorm.DB) (err error) {
	if award.SettleType == SettleTypeOrder {
		award.SettleTypeName = "订单分成"
	} else if award.SettleType == SettleTypeService {
		award.SettleTypeName = "采购技术服务费分成"
	} else {
		award.SettleTypeName = "供应商扣点分成"
	}
	if award.Status == Settled {
		award.StatusName = "已结算"
	} else if award.Status == Lost {
		award.StatusName = "已失效"
	} else {
		award.StatusName = "未结算"
	}
	return
}
