package request

import yzRequest "yz-go/request"

const (
	_ = iota
	SourceAllUser
	SourceOrderPaidUser
	SourceSupplierUser
	SourceApplication
	SourceUserLevel
	SourceDistributorLevel
	SourceMerchantLevel
)

type MerchantByBatch struct {
	// 创建类型 1:单个 2:批量
	CreateType int `json:"create_type" form:"create_type"`
	// 会员id
	Uid uint `json:"uid" form:"uid"`
	// 等级id
	LevelID uint `json:"level_id" form:"level_id"`
	// 会员来源 1:全部会员 2:已消费会员(有已支付订单) 3:供应商 4:采购端会员 5:会员等级 6:分销商等级 7:招商等级
	SourceType int `json:"source_type" form:"source_type"`
	// source_type = 5时 会员等级id
	UserLevelID uint `json:"user_level_id" form:"user_level_id"`
	// source_type = 6时 分销等级id
	DistributorLevelID uint `json:"distributor_level_id" form:"distributor_level_id"`
	// source_type = 7时 招商等级id
	MerchantLevelID uint `json:"merchant_level_id" form:"merchant_level_id"`
}

type MerchantSearch struct {
	Uid     uint   `json:"uid" form:"uid"`           // 会员id
	Member  string `json:"member" form:"member"`     // 会员昵称,姓名,手机号
	LevelID uint   `json:"level_id" form:"level_id"` // 等级id
	StartAT string `json:"start_at" form:"start_at"` // 成为时间
	EndAT   string `json:"end_at" form:"end_at"`     // 成为时间
	yzRequest.PageInfo
}

type AwardSearch struct {
	Uid        uint   `json:"uid" form:"uid"`                  // 会员id
	Member     string `json:"member" form:"member"`            // 会员昵称,姓名,手机号
	LevelID    uint   `json:"level_id" form:"level_id"`        // 等级id
	OrderSn    uint   `json:"order_sn"  form:"order_sn"`       // 订单编号
	SettleType int    `json:"settle_type"  form:"settle_type"` // 分成类型
	Status     int    `json:"status"  form:"status"`           // 分成状态
	StartAT    string `json:"start_at" form:"start_at"`        // 分成时间
	EndAT      string `json:"end_at" form:"end_at"`            // 分成时间
	yzRequest.PageInfo
}

type ApiAwardSearch struct {
	Uid          uint   `json:"uid" form:"uid"`                     // 会员id
	OrderSn      uint   `json:"order_sn"  form:"order_sn"`          // 订单编号
	SupplierName string `json:"supplier_name" form:"supplier_name"` // 供应商名称
	Status       *int   `json:"status"  form:"status"`              // 分成状态
	SettleType   int    `json:"settle_type"  form:"settle_type"`    // 分成类型
	yzRequest.PageInfo
}

type LevelSearch struct {
	yzRequest.PageInfo
}
