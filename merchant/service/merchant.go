package service

import (
	"errors"
	"github.com/360EntSecGroup-Skylar/excelize"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"merchant/application"
	"merchant/distributor"
	"merchant/model"
	"merchant/order"
	"merchant/request"
	"merchant/supplier"
	"merchant/user"
	"os"
	"strconv"
	"time"
	"yz-go/config"
	"yz-go/source"
	"yz-go/utils"
)

type Merchant struct {
	source.Model
	Uid      uint      `json:"uid" gorm:"column:uid;comment:会员id;"`
	LevelID  uint      `json:"level_id" gorm:"column:level_id;comment:等级id;"`
	UserInfo user.User `json:"user_info" gorm:"foreignKey:Uid"`
}

// 修改分销商
func UpdateMerchant(merchant Merchant) (err error) {
	if merchant.Uid == 0 || merchant.LevelID == 0 {
		return errors.New("参数错误")
	}
	var vm model.Merchant
	if !errors.Is(source.DB().Where("uid = ? AND id != ?", merchant.Uid, merchant.ID).First(&vm).Error, gorm.ErrRecordNotFound) {
		return errors.New("该会员已经是招商商")
	}
	err = source.DB().Updates(&merchant).Error
	return
}

// 添加或修改招商
func AddOrEditMerchant(batch request.MerchantByBatch) (err error) {
	if batch.CreateType == 1 {
		err = createSingle(batch.Uid, batch.LevelID)
		if err != nil {
			return
		}
	} else {
		err = createManyOrUpdateMany(batch)
		if err != nil {
			return
		}
	}
	return
}

// 创建招商
func createMerchant(createUserIds []uint, levelId uint) (err error) {
	var merchants []model.Merchant
	for _, createUserId := range createUserIds {
		var merchant model.Merchant
		merchant.Uid = createUserId
		merchant.LevelID = levelId
		merchants = append(merchants, merchant)
	}
	if len(merchants) == 0 {
		return
	}
	// 创建
	err = source.DB().Create(&merchants).Error
	if err != nil {
		return
	}
	return
}

// 批量创建或批量修改
func createManyOrUpdateMany(batch request.MerchantByBatch) (err error) {
	var userIds, merchantIds, merchantUserIds, createUserIds []uint
	// 会员来源 1:全部会员 2:已消费会员(有已支付订单) 3:供应商 4:采购端会员 5:会员等级 6:分销商等级 7:招商等级
	if batch.SourceType == request.SourceAllUser {
		// 全部会员ids
		err, userIds = user.GetAllUserIds()
		if err != nil {
			return
		}
		// 通过会员ids 查询招商,得到的结果,修改
		err, merchantIds = getMerchantIdsByUserIds(userIds)
		if err != nil {
			return
		}
		// 通过招商ids 修改已存在的招商等级
		err = updateMerchantLevelIdByIds(merchantIds, batch.LevelID)
		if err != nil {
			return
		}
		// 全部招商会员ids
		err, merchantUserIds = getMerchantUserIds()
		if err != nil {
			return
		}
		// 要创建的会员ids
		err, createUserIds = user.GetUserIdsNotByMerchantUserIds(merchantUserIds)
		if err != nil {
			return
		}
		// 创建招商
		err = createMerchant(createUserIds, batch.LevelID)
		if err != nil {
			return
		}
	} else if batch.SourceType == request.SourceOrderPaidUser {
		// 存在已支付订单的会员ids
		err, userIds = order.GetUserIdsByPaid()
		if err != nil {
			return
		}
		// 通过会员ids 查询招商,得到的结果,修改
		err, merchantIds = getMerchantIdsByUserIds(userIds)
		if err != nil {
			return
		}
		// 通过招商ids 修改已存在的招商等级
		err = updateMerchantLevelIdByIds(merchantIds, batch.LevelID)
		if err != nil {
			return
		}
		// 招商会员ids
		err, merchantUserIds = getMerchantUserIds()
		if err != nil {
			return
		}
		// 要创建的会员ids
		err, createUserIds = order.GetUserIdsNotByMerchantUserIds(merchantUserIds)
		if err != nil {
			return
		}
		// 创建招商
		err = createMerchant(createUserIds, batch.LevelID)
		if err != nil {
			return
		}
	} else if batch.SourceType == request.SourceSupplierUser {
		// 供应商会员ids
		err, userIds = supplier.GetUserIds()
		if err != nil {
			return
		}
		// 通过会员ids 查询招商,得到的结果,修改
		err, merchantIds = getMerchantIdsByUserIds(userIds)
		if err != nil {
			return
		}
		// 通过招商ids 修改已存在的招商等级
		err = updateMerchantLevelIdByIds(merchantIds, batch.LevelID)
		if err != nil {
			return
		}
		// 招商会员ids
		err, merchantUserIds = getMerchantUserIds()
		if err != nil {
			return
		}
		// 要创建的会员ids
		err, createUserIds = supplier.GetUserIdsNotByMerchantUserIds(merchantUserIds)
		if err != nil {
			return
		}
		// 创建招商
		err = createMerchant(createUserIds, batch.LevelID)
		if err != nil {
			return
		}
	} else if batch.SourceType == request.SourceApplication {
		// 采购端会员ids
		err, userIds = application.GetUserIds()
		if err != nil {
			return
		}
		// 通过会员ids 查询招商,得到的结果,修改
		err, merchantIds = getMerchantIdsByUserIds(userIds)
		if err != nil {
			return
		}
		// 通过招商ids 修改已存在的招商等级
		err = updateMerchantLevelIdByIds(merchantIds, batch.LevelID)
		if err != nil {
			return
		}
		// 招商会员ids
		err, merchantUserIds = getMerchantUserIds()
		if err != nil {
			return
		}
		// 要创建的会员ids
		err, createUserIds = application.GetUserIdsNotByMerchantUserIds(merchantUserIds)
		if err != nil {
			return
		}
		// 创建招商
		err = createMerchant(createUserIds, batch.LevelID)
		if err != nil {
			return
		}
	} else if batch.SourceType == request.SourceUserLevel {
		// 会员ids 通过会员等级查询
		err, userIds = user.GetUserIdsByLevelId(batch.UserLevelID)
		if err != nil {
			return
		}
		// 通过会员ids 查询招商,得到的结果,修改
		err, merchantIds = getMerchantIdsByUserIds(userIds)
		if err != nil {
			return
		}
		// 通过招商ids 修改已存在的招商等级
		err = updateMerchantLevelIdByIds(merchantIds, batch.LevelID)
		if err != nil {
			return
		}
		// 招商会员ids
		err, merchantUserIds = getMerchantUserIds()
		if err != nil {
			return
		}
		// 要创建的会员ids
		err, createUserIds = user.GetUserIdsByLevelIdAndNotByMerchantUserIds(batch.UserLevelID, merchantUserIds)
		if err != nil {
			return
		}
		// 创建招商
		err = createMerchant(createUserIds, batch.LevelID)
		if err != nil {
			return
		}
	} else if batch.SourceType == request.SourceDistributorLevel {
		// 会员ids 通过分销等级查询
		err, userIds = distributor.GetUserIdsByLevelId(batch.DistributorLevelID)
		if err != nil {
			return
		}
		// 通过会员ids 查询招商,得到的结果,修改
		err, merchantIds = getMerchantIdsByUserIds(userIds)
		if err != nil {
			return
		}
		// 通过招商ids 修改已存在的招商等级
		err = updateMerchantLevelIdByIds(merchantIds, batch.LevelID)
		if err != nil {
			return
		}
		// 招商会员ids
		err, merchantUserIds = getMerchantUserIds()
		if err != nil {
			return
		}
		// 要创建的会员ids
		err, createUserIds = distributor.GetUserIdsByLevelIdAndNotByMerchantUserIds(batch.DistributorLevelID, merchantUserIds)
		if err != nil {
			return
		}
		// 创建招商
		err = createMerchant(createUserIds, batch.LevelID)
		if err != nil {
			return
		}
	} else {
		// 通过招商等级id 修改招商等级
		err = updateMerchantLevelIdByLevelId(batch.MerchantLevelID, batch.LevelID)
		if err != nil {
			return
		}
	}
	return
}

// 通过会员ids获取招商
func getMerchantUserIds() (err error, userIds []uint) {
	err = source.DB().Model(&model.Merchant{}).Pluck("uid", &userIds).Error
	return
}

// 通过会员ids获取招商ids
func getMerchantIdsByUserIds(userIds []uint) (err error, merchantIds []uint) {
	err = source.DB().Model(&model.Merchant{}).Where("uid in ?", userIds).Pluck("id", &merchantIds).Error
	return
}

// 通过招商ids修改招商等级
func updateMerchantLevelIdByIds(merchantIds []uint, levelId uint) (err error) {
	err = source.DB().Model(&model.Merchant{}).Where("id in ?", merchantIds).Update("level_id", levelId).Error
	return
}

// 通过等级id修改招商等级
func updateMerchantLevelIdByLevelId(beforeLevelId, levelId uint) (err error) {
	err = source.DB().Model(&model.Merchant{}).Where("level_id = ?", beforeLevelId).Update("level_id", levelId).Error
	return
}

// 单个创建
func createSingle(uid, levelId uint) (err error) {
	if uid == 0 || levelId == 0 {
		return errors.New("参数错误")
	}
	var merchant, verify model.Merchant
	if !errors.Is(source.DB().Where("uid = ? AND id != ?", uid, 0).First(&verify).Error, gorm.ErrRecordNotFound) {
		return errors.New("该会员已经是招商")
	}
	merchant.Uid = uid
	merchant.LevelID = levelId
	err = source.DB().Create(&merchant).Error
	return
}

// 分页获取招商列表
func GetMerchantList(info request.MerchantSearch) (err error, list interface{}, total int64, amountTotal uint) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(&model.Merchant{})
	var merchants []model.Merchant
	if info.Uid != 0 {
		db.Where("`uid` = ?", info.Uid)
	}
	if info.LevelID != 0 {
		db.Where("`level_id` = ?", info.LevelID)
	}
	if info.Member != "" {
		var userIds []uint
		err = source.DB().Model(&user.User{}).Where("username like ?", "%"+info.Member+"%").Or("mobile like ?", "%"+info.Member+"%").Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`uid` in ?", userIds)
	}
	if info.StartAT != "" {
		db.Where("`created_at` >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db.Where("`created_at` <= ?", info.EndAT)
	}
	err = db.Count(&total).Error

	err = source.DB().Model(&model.MerchantAward{}).Select("COALESCE(SUM(amount), 0)").First(&amountTotal).Error

	err = db.Preload("UserInfo").Preload("LevelInfo").Order("created_at desc").Limit(limit).Offset(offset).Find(&merchants).Error
	return err, merchants, total, amountTotal
}

// 导出招商
func ExportMerchantList(info request.MerchantSearch) (err error, link string) {
	db := source.DB().Model(&model.Merchant{}).Preload(clause.Associations)
	var merchants []model.Merchant
	if info.Uid != 0 {
		db.Where("`uid` = ?", info.Uid)
	}
	if info.LevelID != 0 {
		db.Where("`level_id` = ?", info.LevelID)
	}
	if info.Member != "" {
		var userIds []uint
		err = source.DB().Model(&user.User{}).Where("username like ?", "%"+info.Member+"%").Or("mobile like ?", "%"+info.Member+"%").Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`uid` in ?", userIds)
	}
	if info.StartAT != "" {
		db.Where("`created_at` >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db.Where("`created_at` <= ?", info.EndAT)
	}
	err = db.Preload(clause.Associations).Order("id DESC").Find(&merchants).Error
	if err != nil {
		return
	}
	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")
	// 设置单元格的值
	f.SetCellValue("Sheet1", "A1", "ID")
	f.SetCellValue("Sheet1", "B1", "成为招商时间")
	f.SetCellValue("Sheet1", "C1", "会员ID")
	f.SetCellValue("Sheet1", "D1", "用户名")
	f.SetCellValue("Sheet1", "E1", "供应商数量")
	f.SetCellValue("Sheet1", "F1", "招商等级")
	f.SetCellValue("Sheet1", "G1", "累计推荐订单总数")
	f.SetCellValue("Sheet1", "H1", "累计推荐订单总额")
	f.SetCellValue("Sheet1", "I1", "累计分成金额")
	f.SetCellValue("Sheet1", "J1", "已结算金额")
	f.SetCellValue("Sheet1", "K1", "未结算金额")
	i := 2
	for _, merchant := range merchants {
		f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), merchant.ID)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), merchant.CreatedAt)
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), merchant.Uid)
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), merchant.UserInfo.NickName)
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), merchant.SupplierTotal)
		f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), merchant.LevelInfo.Name)
		f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), merchant.RecommendOrderCountTotal)
		f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), merchant.RecommendOrderAmountTotal)
		f.SetCellValue("Sheet1", "I"+strconv.Itoa(i), merchant.SettleAmountTotal)
		f.SetCellValue("Sheet1", "J"+strconv.Itoa(i), merchant.FinishSettleAmount)
		f.SetCellValue("Sheet1", "K"+strconv.Itoa(i), merchant.WaitSettleAmount)
		i++
	}
	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)
	// 根据指定路径保存文件
	//year, month, day := time.Now().Format("2006-01-02 15:04:05")
	format := time.Now().Format("20060102150405")
	path := config.Config().Local.Path + "/export_merchant"
	exist, _ := utils.PathExists(path)
	if !exist {
		// 创建文件夹
		err = os.Mkdir(path, os.ModePerm)
		if err != nil {
			return
		}
	}
	link = path + "/" + format + "招商导出.xlsx"
	if err = f.SaveAs(link); err != nil {
		return
	}
	return err, link
}

// 通过等级id获取招商总数
func getMerchantCountByLevelId(levelId uint) (err error, total int64) {
	err = source.DB().Model(&model.Merchant{}).Where("level_id = ?", levelId).Count(&total).Error
	return
}

// 通过会员id获取招商
func GetMerchantByUserId(userId uint) (err error, merchant model.Merchant) {
	err = source.DB().Preload("LevelInfo").Where("uid = ?", userId).First(&merchant).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
		return
	}
	return
}

// 验证会员是否为招商
func VerifyIdentity(userId uint) (err error, res bool) {
	var merchant model.Merchant
	err = source.DB().Select("id, uid, level_id").Where("uid = ?", userId).First(&merchant).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
		res = false
		return
	}
	if err != nil {
		return
	}
	res = true
	return
}

// 通过会员id获取推荐订单统计
func GetChildOrderInfo(userId uint) (err error, paidAmountTotal, receivedAmountTotal int64) {
	var childIds, sids []uint
	err, childIds = user.GetChildIdsByUserId(userId)
	if err != nil {
		return
	}
	err, sids = supplier.GetSupplierIdsByUserIds(childIds)
	if err != nil {
		return
	}
	err, paidAmountTotal = order.GetAmountTotalBySids(sids, 1)
	err, receivedAmountTotal = order.GetAmountTotalBySids(sids, 3)
	return
}
