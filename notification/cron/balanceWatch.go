package cron

import (
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"notification/model"
	"notification/mq"
	"strconv"
	"time"
	"yz-go/component/log"
	"yz-go/config"
	"yz-go/cron"
	"yz-go/source"
)

func BalanceWatchHandle() {
	task := cron.Task{
		Key:  "balanceWatch",
		Name: "会员余额监测",
		Spec: "0 */5 * * * *",
		Handle: func(task cron.Task) {
			BalanceWatchCron()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func BalanceWatchCron() {
	startTime := time.Now()
	log.Log().Info("开始执行余额监控任务")

	// 获取配置的余额阈值
	balanceAmount, err := getBalanceThreshold()
	if err != nil {
		log.Log().Error("获取余额阈值配置失败", zap.Error(err))
		return
	}

	// 分页处理用户，避免一次性加载所有用户
	const batchSize = 100
	offset := 0
	totalProcessed := 0
	totalNotified := 0

	for {
		users, err := getUsersBatch(offset, batchSize)
		if err != nil {
			log.Log().Error("查询用户批次失败", zap.Error(err), zap.Int("offset", offset))
			return
		}

		if len(users) == 0 {
			break // 没有更多用户了
		}

		// 处理当前批次的用户
		notifiedCount := processUsersBatch(users, balanceAmount)
		totalProcessed += len(users)
		totalNotified += notifiedCount

		log.Log().Info("处理用户批次完成",
			zap.Int("批次大小", len(users)),
			zap.Int("通知数量", notifiedCount),
			zap.Int("累计处理", totalProcessed),
			zap.Int("累计通知", totalNotified))

		offset += batchSize

		// 如果返回的用户数少于批次大小，说明已经是最后一批
		if len(users) < batchSize {
			break
		}
	}

	duration := time.Since(startTime)
	log.Log().Info("余额监控任务完成",
		zap.Int("总处理用户数", totalProcessed),
		zap.Int("总通知数量", totalNotified),
		zap.Duration("耗时", duration))
}

// getBalanceThreshold 获取余额阈值配置
func getBalanceThreshold() (int, error) {
	var balanceAmountString string
	if config.Config().AttachmentType.SmsType == "alisms" {
		balanceAmountString = config.Config().Alisms.BalanceAmount
	} else if config.Config().AttachmentType.SmsType == "tencentsms" {
		balanceAmountString = config.Config().Tencentsms.BalanceAmount
	}

	balanceAmount, err := strconv.Atoi(balanceAmountString)
	if err != nil {
		return 0, err
	}
	return balanceAmount, nil
}

// getUsersBatch 分页查询用户
func getUsersBatch(offset, limit int) ([]model.User, error) {
	var users []model.User
	err := source.DB().
		Preload("AccountBalanceInfo", "type = 2").
		Where("status = 1").
		Offset(offset).
		Limit(limit).
		Find(&users).Error
	return users, err
}

// processUsersBatch 处理用户批次，返回发送通知的数量
func processUsersBatch(users []model.User, balanceAmount int) int {
	notifiedCount := 0

	for _, user := range users {
		if shouldNotifyUser(user, balanceAmount) {
			if sendBalanceNotification(user, balanceAmount) {
				notifiedCount++
			}
		}
	}

	return notifiedCount
}

// shouldNotifyUser 判断是否需要通知用户
func shouldNotifyUser(user model.User, balanceAmount int) bool {
	if user.AccountBalanceInfo.ID == 0 {
		return false // 没有账户余额信息
	}

	totalBalance := user.AccountBalanceInfo.SettlementBalance + user.AccountBalanceInfo.PurchasingBalance
	thresholdBalance := uint(balanceAmount) * 100

	// 余额低于阈值且大于0才需要通知
	return totalBalance <= thresholdBalance && totalBalance > 0
}

// sendBalanceNotification 发送余额通知
func sendBalanceNotification(user model.User, balanceAmount int) bool {
	// 检查是否已经发送过通知
	var existingRecord model.BalanceNotificationRecord
	err := source.DB().
		Where("user_id = ? AND balance_amount = ?", user.ID, balanceAmount).
		Order("created_at desc").
		First(&existingRecord).Error

	if err == nil {
		// 已经发送过通知，可以在这里添加时间间隔检查
		// 例如：24小时内不重复发送
		//if time.Since(existingRecord.CreatedAt.Time) < 24*time.Hour {
		return false
		//}
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Log().Error("查询通知记录失败", zap.Error(err), zap.Uint("用户ID", user.ID))
		return false
	}

	// 发送消息队列通知
	err = mq.PublishMessage(user.ID, "balance", 0)
	if err != nil {
		log.Log().Error("发送余额通知消息失败", zap.Error(err), zap.Uint("用户ID", user.ID))
		return false
	}

	// 记录通知历史
	err = source.DB().Create(&model.BalanceNotificationRecord{
		UserID:        user.ID,
		BalanceAmount: balanceAmount,
	}).Error

	if err != nil {
		log.Log().Error("创建通知记录失败", zap.Error(err), zap.Uint("用户ID", user.ID))
		return false
	}

	log.Log().Info("发送余额通知成功",
		zap.Uint("用户ID", user.ID),
		zap.Int("余额阈值", balanceAmount),
		zap.Uint("当前余额", user.AccountBalanceInfo.SettlementBalance+user.AccountBalanceInfo.PurchasingBalance))

	return true
}
