package model

import (
	_ "embed"
	"encoding/json"
	"fmt"
	"gin-vue-admin/admin/model"
	"gin-vue-admin/cmd/gva"
	"github.com/chenhg5/collection"
	model2 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

//go:embed menu.json
var menu string

func Migrate() (err error) {
	err = source.DB().AutoMigrate(
		model2.Notification{},
		BalanceNotificationRecord{},
		SupplierOrderNotificationRecord{},
	)

	// 菜单,权限
	menus := []model.SysMenu{}
	menuJson := menu
	err = json.Unmarshal([]byte(menuJson), &menus)
	if err != nil {
		fmt.Printf(err.Error())
		return
	}

	//if collection.Collect(gva.GlobalAuth.ToolsPlugin).Contains(11) == true {
	if collection.Collect(gva.GlobalAuth.ToolsPlugin).Contains(11) == true || utils.LocalEnv() == false {
		model.GVA_MENUS = append(model.GVA_MENUS, menus...)
	}

	return
}
