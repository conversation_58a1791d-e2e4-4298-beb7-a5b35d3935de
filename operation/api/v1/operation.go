package v1

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"operation/service"
	"yz-go/component/log"
	"yz-go/model"
	yzResponse "yz-go/response"
)

func GetOperationList(c *gin.Context) {

	var pageInfo model.OperationRecordSearch
	err := c.Should<PERSON>ind<PERSON>uery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetOperationList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func GetOperationUser(c *gin.Context) {
	err, list := service.GetUserList()
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List: list,
	}, "获取成功", c)
}

func GetOperationModule(c *gin.Context) {
	list := service.GetModuleList()

	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List: list,
	}, "获取成功", c)
}
