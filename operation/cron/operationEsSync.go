package cron

import (
	"github.com/olivere/elastic/v7"
	"operation/service"
	"time"
	"yz-go/cron"
	"yz-go/source"
)

func OperationEsSyncHandle() {
	task := cron.Task{
		Key:  "operationEsSync",
		Name: "操作记录es数据同步",
		Spec: "0 */1 * * * *",
		Handle: func(task cron.Task) {
			OperationEsSync()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func OperationEsSync() {
	var err error
	var updatedAt *source.LocalTime

	err, list := service.GetOperationListFromEs(elastic.BoolQuery{}, 0, "updated_at", 1)
	if err != nil {
		esError, ok := err.(*elastic.Error)
		if ok {
			if esError.Status == 404 {
				// es中没有数据时，初始化所有订单数据
				updatedAt = &source.LocalTime{time.Unix(0, 0)}
			}
		}
	} else {
		// es中有数据时，从数据库中同步最新的数据
		for _, search := range list {
			updatedAt = search.UpdatedAt
		}
	}

	err = service.RunOperationSyncEs(updatedAt)
	if err != nil {
		println(err.Error())
		return
	}
}
