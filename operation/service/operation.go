package service

import (
	"encoding/json"
	"yz-go/model"
	"yz-go/source"
)

func GetOperationList(info model.OperationRecordSearch) (err error, data interface{}, total int64) {
	// 如果创建了ES索引，从ES中搜索
	if EsIndexExists() {
		return GetOperationListByEs(info)
	}

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Preload("SysUser").Model(&model.OperationRecord{})
	var applications []model.OperationRecord
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.Name != "" {
		var userIds []uint
		err = source.DB().Model(model.SysUser{}).Where("username like ?", "%"+info.Name+"%").Or("nick_name like ?", "%"+info.Name+"%").Pluck("id", &userIds).Error
		db = db.Where("`id` in ?", userIds)
	}
	if info.UserID != 0 {
		db = db.Where("`user_id` = ?", info.UserID)
	}
	if info.ModuleID != 0 {
		db = db.Where("`module_id` = ?", info.ModuleID)
	}
	if info.Ip != "" {
		db = db.Where("`ip` like ?", "%"+info.Ip+"%")
	}
	if info.Detail != "" {
		db = db.Where("`detail` like ?", "%"+info.Detail+"%")
	}
	if info.Data != "" {
		db = db.Where("`data` like ?", "%"+info.Data+"%")
	}
	if info.StartAT != "" {
		db.Where("`created_at` >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db.Where("`created_at` <= ?", info.EndAT)
	}
	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Order("created_at desc").Find(&applications).Error
	moduleList := GetModuleList()
	for k, v := range applications {
		applications[k].ModuleName = moduleList[v.ModuleID]
		if len(applications[k].ProductLog.DetailContentItem) > 0 {
			productLog, _ := json.Marshal(applications[k].ProductLog)
			applications[k].Data = string(productLog)
		}
		if v.UserID == 0 {
			applications[k].SysUser.Username = "系统"
		}
	}
	return err, applications, total
}

func GetUserList() (err error, data interface{}) {

	db := source.DB().Model(model.SysUser{}).Where("authority_id != ?", "110")

	var pageList []model.SysUser

	err = db.Order("id asc").Find(&pageList).Error

	return err, pageList
}

func GetModuleList() (data map[uint]string) {
	list := make(map[uint]string)
	list[1] = "商品"
	list[2] = "会员"
	list[3] = "订单"
	list[4] = "供应商"
	list[5] = "供应链"
	list[6] = "采购端"
	list[7] = "店铺"
	list[8] = "系统"
	list[9] = "会员等级"
	list[10] = "商品池"
	return list
}
