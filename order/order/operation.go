package order

import (
	"encoding/json"
	"errors"
	"fmt"
	"go.uber.org/zap"
	"order/model"
	"order/mq"
	model2 "payment/model"
	"product/stock"
	"sync"
	"time"
	"yz-go/component/log"
	"yz-go/source"
	"yz-go/utils"
)

type PayInfo struct {
	ID     uint
	Status model2.PayStatus `json:"status"`
}

type OperationOrder struct {
	model.Order
	PayInfo PayInfo
}

func (o OperationOrder) TableName() string {
	return "orders"
}

type OperationHandle interface {
	GetOrder() OperationOrder
	GetBeforeHandle() []Handle // 前置钩子
	GetAfterHandle() []Handle  // 后置钩子
}
type Handle func(OperationOrder) error

type PayOperation struct {
	OperationOrder
}

func (o PayOperation) GetBeforeHandle() []Handle {
	return []Handle{}
}

func (o PayOperation) GetAfterHandle() []Handle {
	return []Handle{
		unlockStock,
		reduceStock,
		addSales,
	}
}

func Pay(orderID uint, payTypeID int, payInfoID uint) (err error) {
	// 订单记录
	order := OperationOrder{}
	err = source.DB().Preload("PayInfo").Where("id = ?", orderID).Find(&order).Error
	if err != nil {
		return
	}
	// 操作结构
	payOperation := PayOperation{order}
	// 付款前钩子
	for _, handle := range payOperation.GetBeforeHandle() {
		err = handle(order)
		if err != nil {
			return
		}
	}
	// 改变订单记录
	if order.Status != model.WaitPay {
		err = errors.New(model.GetStatusName(order.Status) + "的订单无法支付")
		return
	}
	log.Log().Info("info Pay ---", zap.Any("info", orderID), zap.Any("info", payTypeID), zap.Any("info", payInfoID))
	order.Status = model.WaitSend
	order.PayTypeID = payTypeID
	order.PayInfoID = payInfoID
	order.CanRefund = 1
	order.PaidAt = &source.LocalTime{Time: time.Now()}
	err = source.DB().Where("id = ?", order.ID).Updates(model.OrderModel{Status: model.WaitSend, PayTypeID: payTypeID, PayInfoID: payInfoID, PaidAt: order.PaidAt}).Error
	if err != nil {
		return
	}
	// 订单商品可以退款
	err = source.DB().Where("order_id = ?", order.ID).Updates(model.OrderItemModel{CanRefund: 1, RefundStatus: model.NotRefund}).Error
	if err != nil {
		return
	}
	// 付款后钩子
	for _, handle := range payOperation.GetAfterHandle() {
		err = handle(order)
		if err != nil {
			log.Log().Error("error Pay ----", zap.Any("info", err))
			err = nil
		}
	}
	// 消息队列
	log.Log().Info("info Pay ----", zap.Any("info", orderID), zap.Any("info", payTypeID), zap.Any("info", payInfoID))

	err = mq.PublishMessage(order.ID, mq.Paid, 0)
	if err != nil {
		return
	}
	return
}

type SendOperation struct {
	OperationOrder
}

func (o SendOperation) GetBeforeHandle() []Handle {
	return []Handle{}
}

func (o SendOperation) GetAfterHandle() []Handle {
	return []Handle{}
}

func Send(orderID uint) (err error) {
	order := OperationOrder{}
	err = source.DB().Where("id = ?", orderID).Find(&order).Error
	if err != nil {
		return
	}
	// 操作结构
	operation := SendOperation{order}
	// 发货前钩子
	for _, handle := range operation.GetBeforeHandle() {
		err = handle(order)
		if err != nil {
			return
		}
	}
	// 改变订单记录
	if order.Status != model.WaitSend {
		err = errors.New(model.GetStatusName(order.Status) + "的订单无法发货")
		return
	}

	order.Status = model.WaitReceive
	order.SendStatus = model.Sent
	order.SentAt = &source.LocalTime{Time: time.Now()}

	err = source.DB().Where("id = ?", order.ID).Updates(model.OrderModel{Status: model.WaitReceive, SendStatus: model.Sent, SentAt: order.SentAt}).Error
	if err != nil {
		return
	}
	// 发货后钩子
	for _, handle := range operation.GetAfterHandle() {
		err = handle(order)
		if err != nil {
			return
		}
	}
	// 消息队列
	err = mq.PublishMessage(order.ID, mq.Sent, 0)
	if err != nil {
		return
	}
	return
}

func Sending(orderID uint) (err error) {
	order := model.Order{}
	err = source.DB().Where("id = ?", orderID).Find(&order).Error
	if err != nil {
		return
	}
	if order.Status != model.WaitSend {
		return
	}
	if order.SendStatus == model.Sending {
		err = mq.PublishMessage(order.ID, mq.Sending, 0)
		if err != nil {
			return
		}
		return
	}
	err = source.DB().Where("id = ?", order.ID).Updates(model.OrderModel{SendStatus: model.Sending}).Error
	// 消息队列
	err = mq.PublishMessage(order.ID, mq.Sending, 0)
	if err != nil {
		return
	}
	return
}

func CancelSend(orderID uint) (err error) {
	order := model.Order{}
	err = source.DB().Where("id = ?", orderID).Find(&order).Error
	if err != nil {
		return
	}
	if order.Status != model.WaitSend && order.Status != model.WaitReceive {
		//部分发货和待收货的订单才可以取消发货
		return
	}
	err = source.DB().Where("id = ?", order.ID).Updates(model.OrderModel{SendStatus: model.NotSend, Status: model.WaitSend}).Error
	if order.Status == model.WaitReceive {
		// 消息队列
		err = mq.PublishMessage(order.ID, mq.CancelSend, 0)
		if err != nil {
			return
		}
	}
	return
}

type ReceiveOperation struct {
	OperationOrder
}

func (o ReceiveOperation) GetBeforeHandle() []Handle {
	return []Handle{}
}

func (o ReceiveOperation) GetAfterHandle() []Handle {
	return []Handle{}
}
func Received(orderID uint) (err error) {
	order := OperationOrder{}
	err = source.DB().Where("id = ?", orderID).Preload("OrderItems").Preload("OrderItems.AfterSales").Find(&order).Error
	if err != nil {
		return
	}
	//售后验证，如果存在非审核驳回或者关闭的售后 就无法确认收货以及自动收货
	for _, item := range order.OrderItems {
		//如果存在售后 并且状态不是关闭,也不是已完成
		if item.AfterSales.ID != 0 && item.AfterSales.Status != model.ClosedStatus && item.AfterSales.Status != model.CompletedStatus {
			//如果是待审核,驳回的可以确认收货  其他状态无法确定收货
			if item.AfterSales.Status == model.WaitAuditStatus {
				//审核记录
				var afterSalesAudit model.AfterSalesAudit
				err = source.DB().Where("after_sales_id = ?", item.AfterSales.ID).Last(&afterSalesAudit).Error
				if err != nil {
					err = errors.New("获取审核记录失败" + err.Error())
					return
				}
				if afterSalesAudit.Status != model.ClosedRefundStatus {
					err = errors.New("存在售后状态：" + afterSalesAudit.StatusName + "的订单无法收货")
					return
				}

			} else { //除了待审核（关闭除外）其他均 无法确认收货
				err = errors.New("存在售后状态：" + item.AfterSales.StatusName + "的订单无法收货")
				return
			}
		}
	}
	// 操作结构
	operation := ReceiveOperation{order}
	// 收货前钩子
	for _, handle := range operation.GetBeforeHandle() {
		err = handle(order)
		if err != nil {
			return
		}
	}
	// 改变订单记录
	if order.Status != model.WaitReceive {
		err = errors.New(model.GetStatusName(order.Status) + "的订单无法收货")
		return
	}

	order.Status = model.Completed
	order.ReceivedAt = &source.LocalTime{Time: time.Now()}
	err = source.DB().Where("id = ?", order.ID).Updates(model.OrderModel{Status: model.Completed, ReceivedAt: order.ReceivedAt}).Error
	if err != nil {
		return
	}
	// 收货后钩子
	for _, handle := range operation.GetAfterHandle() {
		err = handle(order)
		if err != nil {
			return
		}
	}
	// 消息队列
	err = mq.PublishMessage(order.ID, mq.Received, 0)
	if err != nil {
		return
	}
	return
}

type ClosedOperation struct {
	OperationOrder
}

func (o ClosedOperation) GetBeforeHandle() []Handle {
	return []Handle{}
}

func (o ClosedOperation) GetAfterHandle() []Handle {
	return []Handle{
		unlockStock,
	}
}

func BatchClose(OrderIds []uint) (err error) {
	for _, orderId := range OrderIds {
		if err = Close(orderId); err != nil {
			return
		}
	}
	return
}

func Close(orderID uint) (err error) {
	order := OperationOrder{}
	err = source.DB().Where("id = ?", orderID).Find(&order).Error
	if err != nil {
		return
	}
	// 操作结构
	operation := ClosedOperation{order}
	// 关闭前钩子
	for _, handle := range operation.GetBeforeHandle() {
		err = handle(order)
		if err != nil {
			return
		}
	}
	// 改变订单记录
	if order.Status != model.WaitPay {
		err = errors.New(model.GetStatusName(order.Status) + "的订单无法关闭")
		return
	}

	order.Status = model.Closed
	order.ClosedAt = &source.LocalTime{Time: time.Now()}

	err = source.DB().Where("id = ?", order.ID).Updates(model.OrderModel{Status: model.Closed, ClosedAt: order.ClosedAt}).Error
	if err != nil {
		return
	}
	// 关闭后钩子
	for _, handle := range operation.GetAfterHandle() {
		err = handle(order)
		if err != nil {
			return
		}
	}
	// 消息队列
	err = mq.PublishMessage(order.ID, mq.Closed, 0)
	if err != nil {
		return
	}
	return
}

// 支付之后 关闭订单 仅关闭订单不退款
func OnlyCloseOrder(orderID uint) (err error) {
	order := OperationOrder{}
	err = source.DB().Where("id = ?", orderID).Find(&order).Error
	if err != nil {
		return
	}
	//已关闭直接返回
	if order.Status == model.Closed {
		return
	}
	// 操作结构
	operation := ClosedOperation{order}
	// 关闭前钩子
	for _, handle := range operation.GetBeforeHandle() {
		err = handle(order)
		if err != nil {
			return
		}
	}

	order.Status = model.Closed
	order.ClosedAt = &source.LocalTime{Time: time.Now()}

	err = source.DB().Where("id = ?", order.ID).Updates(model.OrderModel{Status: model.Closed, ClosedAt: order.ClosedAt}).Error
	if err != nil {
		return
	}
	// 关闭后钩子
	for _, handle := range operation.GetAfterHandle() {
		err = handle(order)
		if err != nil {
			return
		}
	}
	// 消息队列
	err = mq.PublishMessage(order.ID, mq.Closed, 0)
	if err != nil {
		return
	}
	return
}
func ForceClose(orderID uint) (err error) {
	order := OperationOrder{}
	err = source.DB().Where("id = ?", orderID).Find(&order).Error
	if err != nil {
		return
	}
	// 操作结构
	operation := ClosedOperation{order}
	// 关闭前钩子
	for _, handle := range operation.GetBeforeHandle() {
		err = handle(order)
		if err != nil {
			return
		}
	}
	// 改变订单记录
	if order.Status == model.Closed {
		err = errors.New(model.GetStatusName(order.Status) + "的订单无法关闭")
		return
	}

	order.Status = model.Closed
	order.ClosedAt = &source.LocalTime{Time: time.Now()}

	err = source.DB().Where("id = ?", order.ID).Updates(model.OrderModel{Status: model.Closed, ClosedAt: order.ClosedAt}).Error
	if err != nil {
		return
	}
	// 关闭后钩子
	for _, handle := range operation.GetAfterHandle() {
		err = handle(order)
		if err != nil {
			return
		}
	}
	// 消息队列
	err = mq.PublishMessage(order.ID, mq.Closed, 0)
	if err != nil {
		return
	}
	return
}

type ConfirmOperation struct {
	OperationOrder
}
type CreatingHandle func(OperationOrder) error

func (o ConfirmOperation) GetBeforeHandle() []Handle {
	return []Handle{
		supplyCheck,
	}
}

type Sku struct {
	Sku           int64 `json:"sku" form:"sku"`
	OriginalSkuID int64 `json:"original_sku_id" form:"original_sku_id" gorm:"column:original_sku_id;comment:原sku_id;index;"` //商品原skuid

}
type RequestSaleBeforeCheck struct {
	Skus           []GoodsSpu           `json:"spu" form:"spu"`
	LocalSkus      []GoodsSpu           `json:"local_skus" form:"local_skus"`
	Address        ReceivingInformation `json:"address" form:"address"`
	GatherSupplyID uint                 `json:"gather_supply_id" form:"gather_supply_id"`
}

type GoodsSpu struct {
	Sku
	Number         int  `json:"number" form:"number"`
	GatherSupplyID uint `json:"gather_supply_id" form:"gather_supply_id"`
	//AgreementPrice uint  `json:"agreement_price" form:"agreement_price"`
}

type ReceivingInformation struct {
	Consignee   string `json:"consignee" form:"consignee"`     //收货人
	Phone       string `json:"phone" form:"phone"`             //联系方式
	Province    string `json:"province" form:"province"`       //省
	City        string `json:"city" form:"city"`               //市
	Area        string `json:"area" form:"area"`               //区
	Street      string `json:"street" form:"street"`           //街道
	Description string `json:"description" form:"description"` //详细信息

}
type CodeMsg struct {
	Code uint   `json:"code" form:"code"` //  1：成功，10001：地址错误
	Msg  string `json:"msg" form:"msg"`
}
type BeforeCheck struct {
	CodeMsg
	Freight uint   `json:"freight" form:"freight"`
	Skus    []uint `json:"skus" form:"skus"`
}

func supplyCheck(order OperationOrder) (err error) {
	if order.GatherSupplyID == 0 {
		return
	}
	var skus, LocalSkus []GoodsSpu
	var goodsSpu GoodsSpu
	for _, item := range order.OrderItems {
		goodsSpu = GoodsSpu{Sku: Sku{Sku: int64(item.OriginalSkuID)}, Number: int(item.Qty), GatherSupplyID: order.GatherSupplyID}
		localGoodsSpu := GoodsSpu{Sku: Sku{Sku: int64(item.SkuID)}, Number: int(item.Qty), GatherSupplyID: order.GatherSupplyID}
		skus = append(skus, goodsSpu)
		LocalSkus = append(LocalSkus, localGoodsSpu)
	}

	request := RequestSaleBeforeCheck{
		Skus:      skus,
		LocalSkus: LocalSkus,
		Address: ReceivingInformation{
			Consignee:   order.ShippingAddress.Realname,
			Phone:       order.ShippingAddress.Mobile,
			Province:    order.ShippingAddress.Province,
			City:        order.ShippingAddress.City,
			Area:        order.ShippingAddress.County,
			Street:      order.ShippingAddress.Town,
			Description: order.ShippingAddress.Detail,
		},
		GatherSupplyID: order.GatherSupplyID,
	}

	fmt.Println("步骤1")
	err, data := utils.Post("http://127.0.0.1:8888/api/gatherSupply/orderBeforeCheck", request, nil)
	var response BeforeCheck
	err = json.Unmarshal(data, &response)
	if err != nil {
		return
	}
	if response.Code != 1 {
		err = errors.New("校验商品状态：" + response.Msg)
		return
	}
	return
}
func reduceStock(order OperationOrder) (err error) {
	var orderItems []OrderItem
	err = source.DB().Where("order_id = ?", order.ID).Find(&orderItems).Error
	if err != nil {
		return
	}
	for _, item := range orderItems {

		err = stock.ReduceSkuStock(item.SkuID, item.Qty)
		if err != nil {
			return
		}
	}
	return
}
func unlockStock(order OperationOrder) (err error) {
	var orderItems []OrderItem
	err = source.DB().Where("order_id = ?", order.ID).Find(&orderItems).Error
	if err != nil {
		return
	}
	for _, item := range orderItems {
		err = stock.RemoveLockStockItem(item.ID)
		if err != nil {
			return
		}
		err = stock.ReduceSkuLockStock(item.SkuID, int(item.Qty))
		if err != nil {
			return
		}
	}
	return
}
func lockStock(order OperationOrder) (err error) {
	var orderItems []OrderItem
	err = source.DB().Where("order_id = ?", order.ID).Find(&orderItems).Error
	if err != nil {
		return
	}
	for _, item := range orderItems {
		err = stock.AddLockStockItem(item.ID)
		if err != nil {
			return
		}
		err = stock.AddSkuLockStock(item.SkuID, int(item.Qty))
		if err != nil {
			return
		}
	}
	return
}
func addSales(order OperationOrder) (err error) {
	var orderItems []OrderItem
	err = source.DB().Where("order_id = ?", order.ID).Find(&orderItems).Error
	if err != nil {
		return
	}
	for _, item := range orderItems {
		err = stock.AddSales(item.ProductID, item.Qty)
		if err != nil {
			return
		}
	}
	return
}

func (o ConfirmOperation) GetAfterHandle() []Handle {
	return []Handle{
		lockStock,
	}
}
func Confirm(orders []model.Order) (error, []model.Order) {
	var outOrders []model.Order

	// 使用通道收集错误和完成的订单
	errChan := make(chan error, len(orders))
	outOrdersChan := make(chan model.Order, len(orders))

	// 使用WaitGroup等待所有goroutine完成
	var wg sync.WaitGroup

	for _, orderModel := range orders {
		wg.Add(1)
		go func(orderModel model.Order) {
			defer wg.Done()

			order := OperationOrder{Order: orderModel}
			operation := ConfirmOperation{order}

			// 生成前钩子
			for _, handle := range operation.GetBeforeHandle() {
				err := handle(order)
				if err != nil {
					errChan <- err
					return
				}
			}

			err := source.DB().Save(&orderModel).Error
			order.ID = orderModel.ID
			if err != nil {
				errChan <- err
				return
			}

			// 生成后钩子
			for _, handle := range operation.GetAfterHandle() {
				err = handle(order)
				if err != nil {
					errChan <- err
					return
				}
			}

			// 消息队列
			err = mq.PublishMessage(order.ID, mq.Created, 0)
			if err != nil {
				errChan <- err
				return
			}

			outOrdersChan <- orderModel
		}(orderModel)
	}

	wg.Wait()
	close(errChan)
	close(outOrdersChan)

	// 检查是否有错误
	for e := range errChan {
		if e != nil {
			return e, nil
		}
	}

	// 收集完成的订单
	for o := range outOrdersChan {
		outOrders = append(outOrders, o)
	}

	return nil, outOrders
}
