package refund

import (
	"errors"
	"fmt"
	"math"
	"order/model"
	"order/mq"
	"order/order"
	"payment"
	"yz-go/source"
)

type Order struct {
	model.Order
	OrderItems []OrderItem
	PayInfo    PayInfo
}
type OrderItem struct {
	ID                   uint `json:"id"`
	OrderID              uint `json:"order_id"`
	RefundAmount         uint `json:"refund_amount"`
	SupplyAmount         uint `json:"supply_amount"`
	SendStatus           uint `json:"send_status"`
	Amount               uint `json:"amount"`
	Qty                  uint `json:"qty" `                                                                                                        // 商品数量
	TechnicalServicesFee uint `json:"technical_services_fee" form:"technical_services_fee" gorm:"column:technical_services_fee;comment:技术服务费(分);"` // 技术服务费
	CostAmount           uint `json:"cost_amount" form:"cost_amount" gorm:"column:cost_amount;comment:成本金额(元);"`                                   // 成本(分)

}
type PayInfo struct {
	ID     uint
	PaySn  string
	Status model.SendStatus `json:"status"`
}

func RefundOrder(orderID uint) (err error) {
	// 订单校验
	var orderModel Order
	err = source.DB().Preload("PayInfo").Preload("OrderItems").Where("id = ?", orderID).Find(&orderModel).Error
	if err != nil {
		return
	}
	if orderModel.Status == model.WaitPay || orderModel.Status == model.Closed {
		err = errors.New(fmt.Sprintf("%s状态的订单无法退款", model.GetStatusName(orderModel.Status)))
		return
	}

	// 退款
	err = payment.Refund(orderModel.PayInfo.PaySn, orderModel.Amount, 0, orderModel.OrderSN)
	if err != nil {
		return
	}
	// 记录退款金额  //运费也变为0--客户要求
	updateOrder := map[string]interface{}{"freight": 0, "amount": 0, "supply_amount": 0, "refund_amount": orderModel.RefundAmount + orderModel.Amount, "refund_status": model.RefundComplete}
	err = source.DB().Model(model.Order{}).Where("id = ?", orderModel.ID).Updates(updateOrder).Error
	if err != nil {
		return
	}

	for _, orderItem := range orderModel.OrderItems {
		updateOrderItem := map[string]interface{}{"amount": 0, "supply_amount": 0, "technical_services_fee": 0,
			"refund_amount": orderItem.RefundAmount + orderItem.Amount, "refund_status": model.RefundComplete, "can_refund": 0}
		err = source.DB().Model(model.OrderItem{}).Where("id = ?", orderItem.ID).Updates(updateOrderItem).Error
		if err != nil {
			return
		}
	}
	// 订单中所有商品都退款后，关闭订单
	err = order.ForceClose(orderModel.ID)
	err = mq.PublishMessage(orderModel.ID, mq.Refunded, 0)
	if err != nil {
		return
	}
	return
}

/*
*

		orderItemID 退款的子订单id
		amount  退款金额
		freight  退款运费
		technicalServicesFee 退款技术服务费
	    num 退款个数
*/
func RefundOrderItem(orderItemID uint, amount uint, freight uint, technicalServicesFee uint, num uint) (err error) {
	// 订单条目校验
	var orderItem OrderItem
	err = source.DB().Where("id = ?", orderItemID).Find(&orderItem).Error
	if err != nil {
		err = errors.New("子订单不存在" + err.Error())
		return
	}
	if orderItem.Amount < amount {
		err = errors.New(fmt.Sprintf("退款金额(￥%d)不能大于商品金额(￥%d)", amount/100, orderItem.Amount/100))
		return
	}

	// 订单校验
	var orderModel Order
	err = source.DB().Preload("PayInfo").Preload("OrderItems").Where("id = ?", orderItem.OrderID).Find(&orderModel).Error
	if err != nil {
		return
	}
	if orderModel.Status == model.WaitPay || orderModel.Status == model.Closed {
		err = errors.New(fmt.Sprintf("%s状态的订单无法退款", model.GetStatusName(orderModel.Status)))
		return
	}
	if orderModel.Amount < amount {
		err = errors.New(fmt.Sprintf("退款金额(￥%d)不能大于订单金额(￥%d)", amount/100, orderModel.Amount/100))
		return
	}
	if orderModel.Freight < freight {
		err = errors.New(fmt.Sprintf("退款运费金额(￥%d)不能大于订单运费金额(￥%d)", freight/100, orderModel.Freight/100))
		return
	}
	if orderModel.TechnicalServicesFee < technicalServicesFee {
		err = errors.New(fmt.Sprintf("退款技术服务费金额(￥%d)不能大于订单技术服务费金额(￥%d)", technicalServicesFee/100, orderModel.TechnicalServicesFee/100))
		return
	}
	refundTotal := amount + freight + technicalServicesFee //退款总额
	// 退款
	err = payment.Refund(orderModel.PayInfo.PaySn, refundTotal, orderItemID, orderModel.OrderSN)
	if err != nil {
		return
	}
	var newOrderSupplyAmount uint
	if orderModel.SupplyAmount > amount {
		newOrderSupplyAmount = orderModel.SupplyAmount - amount
	} else {
		newOrderSupplyAmount = 0
	}

	// 记录退款金额
	updateOrder := map[string]interface{}{"amount": orderModel.Amount - refundTotal, "supply_amount": newOrderSupplyAmount, "freight": orderModel.Freight - freight, "technical_services_fee": orderModel.TechnicalServicesFee - technicalServicesFee, "refund_amount": orderModel.RefundAmount + refundTotal, "goods_count": orderModel.GoodsCount - num, "refund_status": model.RefundComplete}
	err = source.DB().Model(model.Order{}).Where("id = ?", orderModel.ID).Updates(updateOrder).Error
	if err != nil {
		return
	}
	var newOrderItemSupplyAmount uint
	if orderItem.SupplyAmount > amount {
		newOrderItemSupplyAmount = orderItem.SupplyAmount - amount
	} else {
		newOrderItemSupplyAmount = 0
	}

	updateOrderItem := map[string]interface{}{"technical_services_fee": orderItem.TechnicalServicesFee - technicalServicesFee, "amount": orderItem.Amount - amount, "supply_amount": newOrderItemSupplyAmount, "refund_amount": orderItem.RefundAmount + amount, "refund_status": model.RefundComplete, "qty": orderItem.Qty - num, "can_refund": 0}

	//如果退款后，子订单已经没有商品，则修改状态发货状态为已发货
	if orderItem.Amount-amount == 0 || orderItem.Qty-num == 0 {
		updateOrderItem["send_status"] = 1 //子订单1是已发货 （测试得到的结果）
	}
	//如果是供应商订单则退款的时候成本价也减
	if orderModel.SupplierID > 0 {
		//如果是个数退款 成本也减去退款个数的成本
		if num > 0 {
			//求出每份商品的成本占比，乘退款个数再用成本-去这个就是剩余的成本
			updateOrderItem["cost_amount"] = orderItem.CostAmount - orderItem.CostAmount/orderItem.Qty*num
		} else {
			//求退款金额与支付金额的比例,之后成本扣除相应比例
			// 将 uint 转换为 float64，进行除法并保留两位小数
			result := float64(amount) / float64(orderItem.Amount)

			// 保留两位小数，乘以 100 然后四舍五入
			roundedResult := math.Round(result * 100)

			// 转换为 uint
			finalResult := uint(roundedResult)
			costAmount := finalResult * orderItem.CostAmount / 100
			updateOrderItem["cost_amount"] = orderItem.CostAmount - costAmount
		}
	}

	//子订单增加减去技术服务费
	err = source.DB().Model(model.OrderItem{}).Where("id = ?", orderItem.ID).Updates(updateOrderItem).Error
	if err != nil {
		return
	}
	// 待发货与待收货的订单，所有商品都退款后，关闭订单  增加完成状态 修复订单完成之后，售后完成订单不会自动关闭的问提
	if orderModel.Status == model.WaitSend || orderModel.Status == model.WaitReceive || orderModel.Status == model.Completed {
		//减退款金额。用于下面判断是否需要关闭订单
		for k, orderItemData := range orderModel.OrderItems {
			if orderItemData.ID == orderItem.ID {
				orderModel.OrderItems[k].Amount -= amount
			}
		}
		//是否需要关闭订单
		if shouldCloseNew(orderModel) {
			err = order.ForceClose(orderModel.ID)
			if err != nil {
				return
			}
			orderModel.Status = model.Closed
		}
		// 订单待发货，除了当前退款的商品其他都发货了，就把订单改为已发货
		if orderModel.Status == model.WaitSend {
			if shouldSend(orderItem.ID, orderModel) {
				err = order.Send(orderModel.ID)
				if err != nil {
					return
				}
			}
		}
		//if shouldClose(orderItem.ID, orderModel) {
		//	err = order.ForceClose(orderModel.ID)
		//	if err != nil {
		//		retur
		//	}
		//	orderModel.Status = model.Closed
		//}
	}

	err = mq.PublishMessage(orderModel.ID, mq.Refunded, 0)
	if err != nil {
		return
	}
	return
}

// 是否应该关闭订单
func shouldCloseNew(orderModel Order) bool {
	for _, item := range orderModel.OrderItems {
		if item.Amount != 0 { //因为子订单退款最终这个会变为0 所以为0时代表全部退款
			// 存在其他没退过款的商品，就不关闭
			return false
		}
	}
	// 所有商品都退款了，就关闭
	return true
}

func shouldClose(orderItemID uint, orderModel Order) bool {
	for _, item := range orderModel.OrderItems {
		if item.RefundAmount == 0 {
			// 除了当前退款商品
			if orderItemID == item.ID {
				continue
			}
			// 存在其他没退过款的商品，就不关闭
			return false
		}
	}
	// 所有商品都退款了，就关闭
	return true
}
func shouldSend(orderItemID uint, orderModel Order) bool {
	for _, item := range orderModel.OrderItems {
		if item.SendStatus == 0 {
			//子订单金额为0 或者数量为0
			if item.Amount == 0 || item.Qty == 0 {
				continue
			}
			// 存在其他没发货的商品，订单就不改为已发货状态
			return false
		}
	}
	// 所有商品都发货了，订单改为已发货
	return true
}
