package service

import (
	"fmt"
	"testing"
	"time"
	"yz-go/source"
)

func TestCreateOrderDocument(t *testing.T) {
	doc := OrderElasticSearch{}
	err := CreateOrderDocument(doc)
	if err != nil {
		println(err.Error())
	}
}
func TestSyncOrderDocument(t *testing.T) {
	err := RunOrderSyncEs(&source.LocalTime{Time: time.Now()})
	if err != nil {
		println(err.Error())
	}
}
func TestOrderSearch(t *testing.T) {
	layout := "2006-01-02 00:00:00"
	value := "2023-05-11 00:00:00"
	tt, err := time.Parse(layout, value)
	if err != nil {
		fmt.Println(err)
	}
	timestamp := tt.Unix()
	println(timestamp)
}
