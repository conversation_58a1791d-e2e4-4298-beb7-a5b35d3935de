package service

import "testing"

func TestUpdateExcelData1(t *testing.T) {
	type args struct {
		excel1Path string
		excel2Path string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "测试Excel更新",
			args: args{
				excel1Path: "c:\\Users\\<USER>\\GolandProjects\\demo\\order\\testdata\\订单（新）2025-04-03 17_43_40.xlsx",
				excel2Path: "c:\\Users\\<USER>\\GolandProjects\\demo\\order\\testdata\\20250403192742-3订单导出.xlsx",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := UpdateExcelData(tt.args.excel1Path, tt.args.excel2Path); (err != nil) != tt.wantErr {
				t.Errorf("UpdateExcelData() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
