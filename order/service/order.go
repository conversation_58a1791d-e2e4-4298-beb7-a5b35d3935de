package service

import (
	applicationService "application/service"
	"archive/zip"
	categoryModel "category/model"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"order/express"
	"order/model"
	"order/mq"
	mq_reconfirm "order/mq-reconfirm"
	"order/order"
	"order/request"
	"order/response"
	"os"
	"path/filepath"
	model2 "payment/model"
	productModel "product/model"
	"product/other"
	productRequest "product/request"
	productResponse "product/response"
	productService "product/service"
	"product/setting"
	"public-supply/common"
	"region/mapping"
	express2 "shipping/express"
	"strconv"
	"strings"
	suppplierModel "supplier/model"
	setting2 "yz-go/setting"

	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/olivere/elastic/v7"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	applicationModel "application/model"
	financeModel "finance/model"
	"time"
	userModel "user/model"
	"user/service"
	"yz-go/component/log"
	"yz-go/config"
	service2 "yz-go/service"

	fuluSupplyService "fulu-supply/service"
	supplyChainRequest "supply-chain/request"
	supplyChainResponse "supply-chain/response"
	yzGoSetting "yz-go/setting"
	"yz-go/source"
	"yz-go/utils"
)

type CloudOrder struct {
	OrderId      uint   `json:"order_id" form:"order_id" gorm:"column:order_id;comment:中台订单id;"`                   //中台订单id
	OrderSn      uint   `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:中台订单编号;"`                 //中台订单编号
	CloudOrderSn string `json:"cloud_order_sn" form:"cloud_order_sn" gorm:"column:cloud_order_sn;comment:云仓订单号;"` //云仓订单号
	CloudOrderId int    `json:"cloud_order_id"  form:"cloud_order_id" gorm:"column:cloud_order_id;comment:云仓订单id"` // 云仓订单id
}
type CloudOrderItem struct {
	CloudOrderId      uint   `json:"cloud_order_id" form:"cloud_order_id" gorm:"column:cloud_order_id;comment:cloudOrder表id;"`                 //cloudOrder表id
	OrderId           uint   `json:"order_id" form:"order_id" gorm:"column:order_id;comment:中台订单id;"`                                       //中台订单id
	OrderSn           uint   `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:中台订单编号;"`                                     //中台订单编号 //
	OrderItemId       uint   `json:"order_item_id" form:"order_item_id" gorm:"column:order_item_id;comment:中台子订单id;"`                      //中台子订单id
	CloudGoodsOrderSn string `json:"cloud_goods_order_sn" form:"cloud_goods_order_sn" gorm:"column:cloud_goods_order_sn;comment:云仓子订单号;"` //云仓子订单号
}

//
//@function: UpdateOrder
//@description: 更新Order记录
//@param: ad *model.Order
//@return: err error

func UpdateOrder(ad model.Order) (err error) {
	err = source.DB().Save(&ad).Error
	return err
}

// 获取子订单可修改的最大金额和数量
// 1.只有经过售后的可以修改运费,技术服务费，订单直接操作全额退款仅可以修改支付金额
func GetObtainTheMaximumModifiableAmountAndQuantity(orderItemId uint) (err error, data response.ObtainTheMaximumModifiableAmountAndQuantity, orderItem model.OrderItemModel, orderData model.Order, afterSales model.AfterSales) {
	err = source.DB().Where("id = ?", orderItemId).First(&orderItem).Error
	if err != nil {
		err = errors.New("子订单不存在" + err.Error())
		return
	}
	if orderItem.RefundAmount == 0 {
		err = errors.New("子订单未退款无法修改")
		return
	}
	data.Amount = orderItem.RefundAmount
	source.DB().Where("order_item_id = ?", orderItemId).First(&afterSales)
	//如果查询到了售后 可修改最大数量就是售后数量
	if afterSales.ID > 0 && afterSales.Status == model.CompletedStatus {
		data.Num += afterSales.Num
		data.TechnicalServicesFee = afterSales.TechnicalServicesFee
		data.Freight = afterSales.Freight
	}
	err = source.DB().Where("id = ?", orderItem.OrderID).First(&orderData).Error
	if err != nil {
		err = errors.New("获取订单失败" + err.Error())
		return
	}
	switch orderData.Status {
	case model.Closed:
		data.IsSaveSend = 1
		data.IsSaveCompleted = 1
		break
	case model.WaitSend:
		data.IsSaveCompleted = 1
		break
	case model.WaitReceive:
		data.IsSaveSend = 1
		break
	case model.Completed:
		data.IsSaveSend = 1
		break
	default:
		data.IsSaveSend = 0
		data.IsSaveCompleted = 0
		break
	}
	return
}

// 修改子订单的金额和数量，并调整订单金额
func UpdateOrderAmountAndQuantity(data request.UpdateAmountAndQuantityRequest) (err error) {
	err, obtainTheMaximumModifiableAmountAndQuantity, orderItem, orderData, afterSales := GetObtainTheMaximumModifiableAmountAndQuantity(data.OrderItemId)
	if err != nil {
		return
	}
	if data.Amount > obtainTheMaximumModifiableAmountAndQuantity.Amount {
		err = errors.New("大于可修改的最大金额")
		return
	}
	if data.TechnicalServicesFee > obtainTheMaximumModifiableAmountAndQuantity.TechnicalServicesFee {
		err = errors.New("大于可修改的最大技术服务费")
		return
	}
	if data.Freight > obtainTheMaximumModifiableAmountAndQuantity.Freight {
		err = errors.New("大于可修改的最大运费")
		return
	}
	if data.Num > obtainTheMaximumModifiableAmountAndQuantity.Num {
		err = errors.New("大于可修改的最大数量")
		return
	}

	refundTotal := data.Amount + data.Freight + data.TechnicalServicesFee //要还原的总额

	var newOrderSupplyAmount uint

	newOrderSupplyAmount = orderData.SupplyAmount + data.Amount
	// 还原金额 -- 订单
	updateOrder := map[string]interface{}{"amount": orderData.Amount + refundTotal, "supply_amount": newOrderSupplyAmount, "freight": orderData.Freight + data.Freight, "technical_services_fee": orderData.TechnicalServicesFee + data.TechnicalServicesFee, "refund_amount": orderData.RefundAmount - refundTotal, "goods_count": orderData.GoodsCount + data.Num}

	switch data.Status {
	case 1:
		if obtainTheMaximumModifiableAmountAndQuantity.IsSaveSend != 1 {
			err = errors.New("不可修改为待发货")
			return
		}
		updateOrder["status"] = 1
		updateOrder["send_status"] = model.Sending
		break
	case 3:
		if obtainTheMaximumModifiableAmountAndQuantity.IsSaveCompleted != 1 {
			err = errors.New("不可修改为已完成")
			return
		}
		updateOrder["status"] = 3
		break

	}
	//还原金额-- 子订单
	var newOrderItemSupplyAmount uint

	newOrderItemSupplyAmount = orderItem.SupplyAmount + data.Amount
	updateOrderItem := map[string]interface{}{"technical_services_fee": orderItem.TechnicalServicesFee + data.TechnicalServicesFee, "amount": orderItem.Amount + data.Amount, "supply_amount": newOrderItemSupplyAmount, "refund_amount": orderItem.RefundAmount - data.Amount, "qty": orderItem.Qty + data.Num}
	if data.Status == 1 {
		var sendRecords []model.ItemExpress
		err = source.DB().Where("order_item_id = ?", orderItem.ID).Find(&sendRecords).Error
		var sendNum uint
		for _, sendRecord := range sendRecords {
			//获取此item已经发货的数量(加上本次)
			sendNum += sendRecord.Num
		}
		if sendNum >= orderItem.Qty+data.Num {
			err = errors.New("已发货数量大于要修改的数量无法改成待发货")
			return
		}
		updateOrderItem["send_status"] = 0
	}

	err = source.DB().Model(model.Order{}).Where("id = ?", orderData.ID).Updates(updateOrder).Error
	if err != nil {
		err = errors.New("还原订单金额失败" + err.Error())
		return
	}

	//如果是供应商订单则退款的时候成本价也减 -- 成本无法还原,可能出现全退的情况导致成本为0
	//if orderData.SupplierID > 0 {
	//	//如果是个数退款 成本也减去退款个数的成本
	//	if data.Num > 0 {
	//		//求出每份商品的成本占比，乘退款个数再用成本-去这个就是剩余的成本
	//		updateOrderItem["cost_amount"] = orderItem.CostAmount + orderItem.CostAmount/orderItem.Qty*data.Num
	//	} else {
	//		//求退款金额与支付金额的比例,之后成本扣除相应比例
	//		// 将 uint 转换为 float64，进行除法并保留两位小数
	//		result := float64(data.Amount) / float64(orderItem.Amount)
	//
	//		// 保留两位小数，乘以 100 然后四舍五入
	//		roundedResult := math.Round(result * 100)
	//
	//		// 转换为 uint
	//		finalResult := uint(roundedResult)
	//		costAmount := finalResult * orderItem.CostAmount / 100
	//		updateOrderItem["cost_amount"] = orderItem.CostAmount + costAmount
	//	}
	//}
	//子订单增加减去技术服务费
	err = source.DB().Model(model.OrderItem{}).Where("id = ?", orderItem.ID).Updates(updateOrderItem).Error
	if err != nil {
		err = errors.New("还原子订单金额失败" + err.Error())
		return
	}
	if data.Freight > 0 || data.TechnicalServicesFee > 0 {
		updateAfterSales := map[string]interface{}{"freight": afterSales.Freight - data.Freight, "technical_services_fee": afterSales.TechnicalServicesFee - data.TechnicalServicesFee}
		err = source.DB().Model(model.AfterSales{}).Where("id = ?", afterSales.ID).Updates(updateAfterSales).Error
		if err != nil {
			err = errors.New("扣减售后运费技术服务费记录失败" + err.Error())
			return
		}
	}
	return
}

//
//@function: GetOrder
//@description: 根据id获取Order记录
//@param: id uint
//@return: err error, ad model.Order

func GetOrder(id uint) (err error, ad order.Order) {

	err = source.DB().Preload("OrderItems.AfterSales").Preload("OrderItems.AfterSales.AfterSalesAudit").Preload("OrderBill").Preload("OrderItems").Preload("User").Preload("ShippingAddress").Preload("OrderExpress.OrderItems").Preload("OrderExpress.SysUser").Where("id = ?", id).First(&ad).Error
	if err != nil {
		err = errors.New("查询失败,订单不存在")
		return
	}
	if ad.ID == 0 {
		err = errors.New("查询失败,订单不存在")
		return
	}
	for ok, orderE := range ad.OrderExpress {
		var itemExpress []model.ItemExpress
		err = source.DB().Where("order_express_id = ?", orderE.ID).Find(&itemExpress).Error
		if err != nil {
			return
		}
		var itemExpressMap = make(map[uint]model.ItemExpress)
		for _, itemExp := range itemExpress {
			itemExpressMap[itemExp.OrderItemID] = itemExp
		}
		for k, orderItem := range orderE.OrderItems {
			orderE.OrderItems[k].SendNum = itemExpressMap[orderItem.ID].Num
		}
		if strings.Contains(orderE.ExpressNo, ":") {
			orderE.ExpressNo = orderE.ExpressNo[:len(orderE.ExpressNo)-5]
		}
		ad.OrderExpress[ok] = orderE
	}

	return
}

//
//@function: GetOrder
//@description: 根据 order_sn获取Order记录
//@param: id uint
//@return: err error, ad model.Order

func GetOrderByOrderSn(orderSn uint) (err error, ad order.Order) {
	err = source.DB().Preload("OrderItems.AfterSales").Preload("OrderItems.AfterSales.AfterSalesAudit").Preload("OrderBill").Preload("OrderItems").Preload("User").Preload("ShippingAddress").Preload("OrderExpress.OrderItems").Where("order_sn = ?", orderSn).First(&ad).Error
	if err != nil {
		err = errors.New("查询失败,订单不存在")
		return
	}
	if ad.ID == 0 {
		err = errors.New("查询失败,订单不存在")
		return
	}
	for ok, orderE := range ad.OrderExpress {
		var itemExpress []model.ItemExpress
		err = source.DB().Where("order_express_id = ?", orderE.ID).Find(&itemExpress).Error
		if err != nil {
			return
		}
		var itemExpressMap = make(map[uint]model.ItemExpress)
		for _, itemExp := range itemExpress {
			itemExpressMap[itemExp.OrderItemID] = itemExp
		}
		for k, orderItem := range orderE.OrderItems {
			orderE.OrderItems[k].SendNum = itemExpressMap[orderItem.ID].Num
		}
		if strings.Contains(orderE.ExpressNo, ":") {
			orderE.ExpressNo = orderE.ExpressNo[:len(orderE.ExpressNo)-5]
		}
		ad.OrderExpress[ok] = orderE
	}

	return
}

//
//@function: GetOrder
//@description: 根据id获取Order记录
//@param: id uint
//@return: err error, ad model.Order

func GetOrderByThirdOrderSn(thirdOrderSN string) (err error, ad order.Order) {
	err = source.DB().Preload("OrderBill").Preload("OrderItems").Preload("User").Preload("ShippingAddress").Preload("OrderExpress.OrderItems").Where("third_order_sn = ?", thirdOrderSN).First(&ad).Error

	for ok, orderE := range ad.OrderExpress {
		var itemExpress []model.ItemExpress
		err = source.DB().Where("order_express_id = ?", orderE.ID).Find(&itemExpress).Error
		if err != nil {
			return
		}
		var itemExpressMap = make(map[uint]model.ItemExpress)
		for _, itemExp := range itemExpress {
			itemExpressMap[itemExp.OrderItemID] = itemExp
		}
		for k, orderItem := range orderE.OrderItems {
			orderE.OrderItems[k].SendNum = itemExpressMap[orderItem.ID].Num
		}
		ad.OrderExpress[ok] = orderE
	}

	return
}

// @function: GetOrder
// @description: third_order_sn
// @param: id uint
// @return: err error, ad model.Order
func GetOrdersByThirdOrderSn(thirdOrderSn string, appId uint) (err error, ad []order.Order) {
	err = source.DB().Preload("OrderBill").Preload("OrderItems").Preload("User").Preload("ShippingAddress").Preload("OrderExpress.OrderItems").Where("third_order_sn = ?", thirdOrderSn).Find(&ad).Error
	if err != nil {
		err = errors.New("查询失败")
		return
	}
	if len(ad) == 0 {
		err = errors.New("查询失败,订单不存在")
		return
	}
	for key, item := range ad {
		if item.ApplicationID != appId {
			err = errors.New("获取订单失败,不是这个应用的订单")
			return
		}
		for ok, orderE := range item.OrderExpress {
			var itemExpress []model.ItemExpress
			err = source.DB().Where("order_express_id = ?", orderE.ID).Find(&itemExpress).Error
			if err != nil {
				return
			}
			var itemExpressMap = make(map[uint]model.ItemExpress)
			for _, itemExp := range itemExpress {
				itemExpressMap[itemExp.OrderItemID] = itemExp
			}
			for k, orderItem := range orderE.OrderItems {
				orderE.OrderItems[k].SendNum = itemExpressMap[orderItem.ID].Num
			}
			ad[key].OrderExpress[ok] = orderE
		}
	}
	return
}

//
//@function: Note
//@description: 根据id获取Order记录
//@param: id uint
//@return: err error, ad model.Order

func Note(order model.Order) (err error) {
	err = source.DB().Model(&model.Order{}).Where("id = ?", order.ID).Update("note", order.Note).Error
	return
}

type GatherSupply struct {
	source.Model
	CategoryID uint   `json:"category_id" form:"category_id" gorm:"column:category_id;default:0;"`
	Name       string `json:"name" gorm:"column:name"`
}

func GetChangePriceButton(orderList []response.Order) (err error, list []response.Order) {
	var orderIDs []uint
	for _, item := range orderList {
		orderIDs = append(orderIDs, item.ID)
	}
	var OrderPriceChanges []model.OrderPriceChange
	err = source.DB().Where("order_id in ?", orderIDs).Find(&OrderPriceChanges).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	OrderPriceChangeMaps := make(map[uint]model.OrderPriceChange)
	for _, OrderPriceChange := range OrderPriceChanges {
		OrderPriceChangeMaps[OrderPriceChange.OrderID] = OrderPriceChange
	}
	for key, item := range orderList {
		var buttons []model.Button
		var ok bool
		buttons, ok = orderList[key].Button.([]model.Button)
		if !ok {
			buttons = []model.Button{}
		}
		_, existed := OrderPriceChangeMaps[item.ID]
		if !existed {
			switch item.Status {
			case model.WaitPay:
				orderList[key].Button = append(buttons, model.Button{Title: "改价", Url: "order/changePrice"})
			default:

			}

		} else {
			switch item.Status {
			case model.WaitPay:
				orderList[key].Button = append(buttons, model.Button{Title: "改价(已改价)", Url: "order/changePrice"}, model.Button{Title: "改价记录", Url: "order/getChangePriceList"})
			default:
				orderList[key].Button = append(buttons, model.Button{Title: "改价记录", Url: "order/getChangePriceList"})
			}
		}
	}
	return err, orderList
}
func GetPrintButton(orderList []response.Order) (err error, list []response.Order) {
	var orderIDs []uint
	for _, item := range orderList {
		orderIDs = append(orderIDs, item.ID)
	}
	var orderPrints []model.OrderPrint
	err = source.DB().Where("order_id in ?", orderIDs).Find(&orderPrints).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	orderPrintMaps := make(map[uint]model.OrderPrint)
	for _, orderPrint := range orderPrints {
		orderPrintMaps[orderPrint.OrderId] = orderPrint
	}
	var buttonMap map[model.OrderStatus][]model.Button
	for key, item := range orderList {
		_, existed := orderPrintMaps[item.ID]
		if !existed || orderPrintMaps[item.ID].Status == 0 {
			buttonMap = map[model.OrderStatus][]model.Button{
				model.WaitSend: {{Title: "未打印", Url: ""}, {Title: "单包裹打印", Url: "surfaceSingle/singlePackagePrint"}, {Title: "多包裹打印", Url: "surfaceSingle/morePackagePrint"}},
			}
		} else {
			buttonMap = map[model.OrderStatus][]model.Button{
				model.WaitSend:    {{Title: "已打印", Url: ""}, {Title: "原单号打印", Url: "surfaceSingle/historyPrint"}, {Title: "新单号打印", Url: "surfaceSingle/singlePackagePrint"}},
				model.WaitReceive: {{Title: "已打印", Url: ""}},
				model.Completed:   {{Title: "已打印", Url: ""}},
				model.Closed:      {{Title: "已打印", Url: ""}},
			}
		}
		orderList[key].PrintButton = buttonMap[item.Status]
	}
	return err, orderList
}

//
//@function: GetEsOrderInfoList
//@description: 分页获取Order记录
//@param: info request.OrderSearch
//@return: err error, list interface{}, total int64

func GetEsOrderInfoList(info request.OrderAdminSearch) (err error, list []response.Order, total int64, waitPayNum, waitSendNum, waitReceivedNum, completedNum, closedNum, refundingNum, refundNum int64) {
	pageSize := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	sortBy := "created_at"

	if info.Status != nil {
		if *info.Status == model.WaitReceive {
			sortBy = "sent_at"
		} else if *info.Status == model.WaitSend {
			sortBy = "paid_at"
		} else {
			sortBy = "created_at"
		}

	} else {
		sortBy = "created_at"

	}
	var ids []uint
	var boolQ, boolQCount, boolQTemp elastic.BoolQuery
	// 根据传来的搜索参数，包装es搜索条件（排除订单状态条件）
	boolQ = wrapOrderBoolQ(*elastic.NewBoolQuery(), info)
	err, ids = searchOrderIdsInEs(boolQ, offset, sortBy, pageSize)
	if err != nil {
		return
	}
	// 满足当前搜索条件的订单数量
	err, total = getOrderCountInEs(&boolQ)
	if err != nil {
		return
	}
	// 按照搜索条件，从es获取每种状态订单的数量
	infoTemp := info
	infoTemp.Status = nil
	infoTemp.RefundStatus = nil
	// es中的搜索条件，下面搜索每种状态时，要重新复制一个条件变量，否则订单状态的条件会叠加
	boolQCount = wrapOrderBoolQ(*elastic.NewBoolQuery(), infoTemp)
	// 待付款订单数量
	boolQTemp = boolQCount
	err, waitPayNum = getOrderCountInEs(boolQTemp.Must(elastic.NewTermQuery("status", model.WaitPay)))
	if err != nil {
		return
	}
	// 待发货订单数量
	boolQTemp = boolQCount
	err, waitSendNum = getOrderCountInEs(boolQTemp.Must(elastic.NewTermQuery("status", model.WaitSend)))
	if err != nil {
		return
	}
	// 待收货订单数量
	boolQTemp = boolQCount
	err, waitReceivedNum = getOrderCountInEs(boolQTemp.Must(elastic.NewTermQuery("status", model.WaitReceive)))
	if err != nil {
		return
	} // 已完成订单数量
	boolQTemp = boolQCount
	err, completedNum = getOrderCountInEs(boolQTemp.Must(elastic.NewTermQuery("status", model.Completed)))
	if err != nil {
		return
	}
	// 已关闭订单数量
	boolQTemp = boolQCount
	err, closedNum = getOrderCountInEs(boolQTemp.Must(elastic.NewTermQuery("status", model.Closed)))
	if err != nil {
		return
	}
	// 退款中订单数量
	boolQTemp = boolQCount
	err, refundingNum = getOrderCountInEs(boolQTemp.Must(elastic.NewTermQuery("refund_status", model.Refunding)))
	if err != nil {
		return
	}
	// 已退款订单数量
	boolQTemp = boolQCount
	err, refundNum = getOrderCountInEs(boolQTemp.Must(elastic.NewTermQuery("refund_status", model.RefundComplete)))
	if err != nil {
		return
	}
	// 从mysql获取订单数据
	order := response.Order{}
	db := source.DB().Model(&order).Preload(clause.Associations).Preload("OrderItems.AfterSales")
	// 通过es将搜索条件转换为id数组
	if len(ids) == 0 {
		return
	}
	db.Where("id in ?", ids)

	// 虽然es返回的id数组是排序过的，但mysql用in返回的结果需要重新排序
	db.Order(sortBy + " DESC")

	err = db.Find(&list).Error

	return
}

func GetEsOrderInfoListByExport(info request.OrderAdminSearch) (err error, ids []uint, total int64) {

	sortBy := "created_at"

	if info.Status != nil {
		if *info.Status == model.WaitReceive {
			sortBy = "sent_at"
		} else if *info.Status == model.WaitSend {
			sortBy = "paid_at"
		} else {
			sortBy = "created_at"
		}

	} else {
		sortBy = "created_at"

	}
	var boolQ elastic.BoolQuery
	// 根据传来的搜索参数，包装es搜索条件（排除订单状态条件）
	boolQ = wrapOrderBoolQ(*elastic.NewBoolQuery(), info)
	err, ids = searchOrderIdsInEs(boolQ, 0, sortBy, 5000)
	if err != nil {
		return
	}
	// 满足当前搜索条件的订单数量
	err, total = getOrderCountInEs(&boolQ)
	if err != nil {
		return
	}

	return
}
func GetOrderListFromEs(boolQ elastic.BoolQuery, offset int, sortBy string, pageSize int) (err error, productSearchs []OrderElasticSearch) {
	es, err := source.ES()
	var res *elastic.SearchResult
	res, err = es.Search("order").From(offset).Size(pageSize).Sort(sortBy, false).Query(&boolQ).Do(context.Background())
	if err != nil {
		return
	}
	//获取es搜索结果
	productSearchs, err = fillSearchResultInOrderElasticSearch(res)
	return
}

// 从es中搜索订单id数组
func searchOrderIdsInEs(boolQ elastic.BoolQuery, offset int, sortBy string, pageSize int) (err error, ids []uint) {
	err, productSearchs := GetOrderListFromEs(boolQ, offset, sortBy, pageSize)
	if err != nil {
		return
	}
	var idsString string
	for _, v := range productSearchs {
		idsString += "," + strconv.Itoa(int(v.ID))
		ids = append(ids, v.ID)
	}
	return
}

// 从es中搜索订单数量
func getOrderCountInEs(inBoolQ *elastic.BoolQuery) (err error, count int64) {
	var res *elastic.SearchResult
	es, err := source.ES()
	res, err = es.Search("order").TrackTotalHits(true).Query(inBoolQ).Do(context.Background())
	if err != nil {
		return
	}
	count = res.Hits.TotalHits.Value
	return
}

// 包装订单es搜索条件
func wrapOrderBoolQ(inBoolQ elastic.BoolQuery, info request.OrderAdminSearch) (boolQ elastic.BoolQuery) {
	boolQ = inBoolQ
	boolQ.Must(elastic.NewTermQuery("is_plugin", 0))
	var timeType string
	timeType = "created_at"
	if info.TimeType != nil {
		switch *info.TimeType {
		case 0:
			timeType = "created_at"
			break
		case 1:
			timeType = "paid_at"
			break
		case 2:
			timeType = "sent_at"
			break
		case 3:
			timeType = "received_at"
			break
		default:
			timeType = "created_at"
			break
		}
	}
	if info.StartAT != "" {
		t, err := time.ParseInLocation("2006-01-02 15:04:05", info.StartAT, time.Local)
		if err != nil {
			return
		}

		boolQ.Must(elastic.NewRangeQuery(timeType).Gte(t.Unix()))
	}
	if info.EndAT != "" {
		t, err := time.ParseInLocation("2006-01-02 15:04:05", info.EndAT, time.Local)
		if err != nil {
			return
		}
		boolQ.Must(elastic.NewRangeQuery(timeType).Lte(t.Unix()))
	}
	if info.ProductTitle != "" {
		boolQ.Must(elastic.NewMatchPhraseQuery("sku_titles", info.ProductTitle).Slop(2))
		// isSearch = true
	}

	if info.SupplierID != nil {
		if *info.SupplierID == 999999 {
			boolQ.Must(elastic.NewRangeQuery("supplier_id").Gt(0))
		} else {
			boolQ.Must(elastic.NewTermQuery("supplier_id", *info.SupplierID))
		}
		// isSearch = true
	}
	if info.ApplicationID > 0 {
		boolQ.Must(elastic.NewTermQuery("application_id", info.ApplicationID))
		// isSearch = true
	}
	if info.GatherSupplierID != nil {
		boolQ.Must(elastic.NewTermQuery("gather_supply_id", *info.GatherSupplierID))
		// isSearch = true
	}
	if len(info.PaySN) > 0 {
		boolQ.Must(elastic.NewTermQuery("pay_sn.keyword", info.PaySN))
		// isSearch = true
	}
	if info.PayTypeID > 0 {
		boolQ.Must(elastic.NewTermQuery("pay_type_id", info.PayTypeID))
		// isSearch = true
	}
	if info.Note != "" {
		boolQ.Must(elastic.NewWildcardQuery("note.keyword", "*"+info.Note+"*"))
		// isSearch = true
	}
	if info.OrderTypeNote != "" {
		boolQ.Must(elastic.NewWildcardQuery("order_type_note.keyword", "*"+info.OrderTypeNote+"*"))
		// isSearch = true
	}
	if info.OrderType > 0 {
		boolQ.Must(elastic.NewTermQuery("order_type", info.OrderType))
		// isSearch = true
	}
	if info.UserID > 0 {
		boolQ.Must(elastic.NewTermQuery("user_id", info.UserID))
		// isSearch = true
	}
	if info.GatherSupplierStatus != "" && info.GatherSupplierID != nil {
		boolQ.Should(elastic.NewTermQuery("gather_supply_sn.keyword", ""), elastic.NewTermQuery("gather_supply_sn.keyword", nil))
		// isSearch = true
	}
	if info.RefundStatus != nil {
		boolQ.Must(elastic.NewTermQuery("refund_status", info.RefundStatus))
		// isSearch = true
	}
	if info.ThirdOrderSN != "" {
		boolQ.Must(elastic.NewTermQuery("third_order_sn.keyword", info.ThirdOrderSN))
		// isSearch = true
	}
	if info.UserName != "" {
		boolQ.Must(elastic.NewTermQuery("realname.keyword", info.UserName))
		// isSearch = true
	}
	if info.NickName != "" {
		boolQ.Must(elastic.NewTermQuery("nick_name.keyword", info.NickName))
		// isSearch = true
	}
	if info.UserMobile != "" {
		boolQ.Must(elastic.NewTermQuery("mobile.keyword", info.UserMobile))
		// isSearch = true
	}
	if info.ShippingSN != "" {
		boolQ.Must(elastic.NewMatchPhraseQuery("express_no", info.ShippingSN))
		// isSearch = true
	}
	if info.Status != nil {
		if *info.Status == 5 {
			boolQ.Must(elastic.NewTermQuery("refund_status", model.Refunding))
		} else if *info.Status == 6 {
			boolQ.Must(elastic.NewTermQuery("refund_status", model.RefundComplete))
		} else {
			boolQ.Must(elastic.NewTermQuery("status", info.Status))
		}
		// isSearch = true
	}

	return boolQ
}

// 解析es搜索结构
func fillSearchResultInOrderElasticSearch(searchResult *elastic.SearchResult) (productSearch []OrderElasticSearch, err error) {

	if searchResult.Hits.TotalHits.Value > 0 {

		for _, hit := range searchResult.Hits.Hits {

			var t OrderElasticSearch
			err := json.Unmarshal(hit.Source, &t)
			if err != nil {
				// Deserialization failed
			}
			productSearch = append(productSearch, t)
		}
	}
	return
}
func needSearchInEs(info request.OrderAdminSearch) bool {
	var exist bool
	var err error
	exist, err = source.EsIndexExists("order")
	if err != nil {
		return false
	}
	if !exist {
		return false
	}
	if info.PaySN != "" {
		return true
	}
	if info.ProductTitle != "" {
		return true
	}

	if info.UserName != "" {
		return true
	}

	if info.UserMobile != "" {
		return true
	}

	if info.ShippingSN != "" {
		return true
	}

	if info.CloudOrderId != 0 {
		return true
	}
	if info.Note != "" {
		return true
	}
	if info.OrderType != 0 {
		return true
	}
	if info.OrderTypeNote != "" {
		return true
	}
	return false
}

// @function: GetOrderInfoList
// @description: 分页获取Order记录
// @param: info request.OrderSearch
// @return: err error, list interface{}, total int64
func GetOrderInfoList(info request.OrderAdminSearch) (err error, list []response.Order, total int64, WaitPayNum, WaitSendNum, WaitReceiveNum, CompletedNum, ClosedNum, BackNum, RefundNum int64) {
	if needSearchInEs(info) {
		return GetEsOrderInfoList(info)
	}
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	order := response.Order{}
	var dbs []*gorm.DB
	allDb := source.DB().Model(&order).Preload(clause.Associations).Preload("OrderItems.AfterSales")

	var WaitPayNumDb = source.DB().Model(&order)

	var WaitSendNumDb = source.DB().Model(&order)

	var WaitReceiveNumDb = source.DB().Model(&order)

	var CompletedNumDb = source.DB().Model(&order)

	var ClosedNumDb = source.DB().Model(&order)

	var BackNumDb = source.DB().Model(&order)

	var RefundNumDb = source.DB().Model(&order)
	dbs = append(dbs, allDb, WaitPayNumDb, WaitSendNumDb, WaitReceiveNumDb, CompletedNumDb, ClosedNumDb, BackNumDb, RefundNumDb)
	for _, db := range dbs {
		db.Where("is_plugin = ?", 0)
	}
	if info.PluginID != 0 {
		for _, db := range dbs {
			db.Where("plugin_id=?", info.PluginID)
		}
	}
	if info.SourceShopName != "" {
		for _, db := range dbs {
			db.Where("source_shop_name like ?", "%"+info.SourceShopName+"%")
		}
	}

	if info.SupplierID != nil {
		if *info.SupplierID == 999999 {
			for _, db := range dbs {
				db.Where("`supplier_id` > 0")
			}

		} else {
			for _, db := range dbs {
				db.Where("`supplier_id` = ?", &info.SupplierID).Where("`gather_supply_id` = 0")
			}

		}
	}

	if info.ApplicationID > 0 {
		for _, db := range dbs {
			db.Where("`application_id` = ?", info.ApplicationID)
		}

	}

	// 指定供应链
	if info.GatherSupplierID != nil {
		for _, db := range dbs {
			db.Where("gather_supply_id = ?", info.GatherSupplierID).Where("supplier_id = 0")
		}
	}

	if info.PaySN != "" {
		var payInfoIds []uint
		err = source.DB().Model(&response.PayInfo{}).Where("pay_sn = ?", info.PaySN).Pluck("id", &payInfoIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		for _, db := range dbs {
			db.Where("`pay_info_id` in ?", payInfoIds)
		}
	}
	if info.PayTypeID != 0 {
		for _, db := range dbs {
			db.Where("`pay_type_id` = ?", info.PayTypeID)
		}

	}
	if info.UserID > 0 {
		for _, db := range dbs {
			db.Where("`user_id` = ?", info.UserID)
		}
	}

	if info.OrderType > 0 {
		for _, db := range dbs {
			db.Where("`order_type` = ?", info.OrderType)
		}
	}

	if info.OrderTypeNote != "" {
		for _, db := range dbs {
			db.Where("order_type_note LIKE ?", "%"+info.OrderTypeNote+"%")
		}
	}

	if info.JushuitanBind != nil {
		for _, db := range dbs {
			db.Where("`jushuitan_bind` = ?", info.JushuitanBind)
		}

	}
	if info.GatherSupplierStatus != "" && info.GatherSupplierID != nil {
		for _, db := range dbs {
			db.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		}

	}
	//直播间id
	if info.ShareLiveRoomId != 0 {
		for _, db := range dbs {
			db.Where("`share_live_room_id` = ?", info.ShareLiveRoomId)
		}
	}
	if info.Status != nil {
		if *info.Status == 5 {
			for _, db := range dbs {
				db.Where("`refund_status` = ?", model.Refunding)
			}
		} else if *info.Status == 6 {
			for _, db := range dbs {
				db.Where("`refund_status` = ?", model.RefundComplete)
			}
		} else {
			allDb.Where("orders.status = ?", info.Status)

		}
	}
	if info.RefundStatus != nil {
		if *info.RefundStatus == -1 {
			for _, db := range dbs {
				db.Joins("left join after_sales on after_sales.order_id = orders.id").Where("after_sales.status = ?", model.ClosedRefund)
			}
		} else {
			for _, db := range dbs {
				db.Where("`refund_status` = ?", info.RefundStatus)
			}
		}

	}
	if info.OrderSN != "" {
		for _, db := range dbs {
			db.Where("`order_sn` = ?", info.OrderSN)
		}
	}

	if info.Note != "" {
		for _, db := range dbs {
			db.Where("note like ?", "%"+info.Note+"%")
		}
	}
	if info.SupplySN != "" {
		for _, db := range dbs {
			db.Where("gather_supply_sn like ?", "%"+info.SupplySN+"%")
		}
	}
	if info.ThirdOrderSN != "" {
		//变为=号原因：因为like%% 不走索引导致客户通过第三方单号查询超时 如果想用like 可使用 like xxx%
		for _, db := range dbs {
			db.Where("third_order_sn = ?", info.ThirdOrderSN)
		}
	}
	if info.IsWxMiniSend != 0 {
		for _, db := range dbs {
			db.Where("is_wx_mini_send = ?", info.IsWxMiniSend)
		}
	}
	var timeType string
	timeType = "created_at"
	if info.TimeType != nil {
		switch *info.TimeType {
		case 0:
			timeType = "created_at"
			break
		case 1:
			timeType = "paid_at"
			break
		case 2:
			timeType = "sent_at"
			break
		case 3:
			timeType = "received_at"
			break
		default:
			timeType = "created_at"
			break
		}
	}
	if info.StartAT != "" {
		for _, db := range dbs {
			db.Where("`"+timeType+"` >= ?", info.StartAT)
		}
	}
	if info.EndAT != "" {
		for _, db := range dbs {
			db.Where("`"+timeType+"` <= ?", info.EndAT)
		}
	}
	if info.ProductTitle != "" {
		orderIds := []uint{}
		err = source.DB().Model(model.OrderItem{}).Where("title like ?", "%"+info.ProductTitle+"%").Group("order_id").Pluck("order_id", &orderIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		for _, db := range dbs {
			db.Where("`id` in ?", orderIds)
		}

	}
	if info.AppShopName != "" {
		appShopId := []uint{}
		err = source.DB().Model(response.ApplicationShop{}).Where("shop_name like ?", "%"+info.AppShopName+"%").Pluck("id", &appShopId).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		for _, db := range dbs {
			db.Where("`application_shop_id` in ?", appShopId)
		}
	}
	if info.Mobile != "" {
		userIds := []uint{}
		err = source.DB().Model(response.User{}).Where("username like ? OR mobile like ?", "%"+info.Mobile+"%", "%"+info.Mobile+"%").Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		for _, db := range dbs {
			db.Where("`user_id` in ?", userIds)
		}

	}
	if info.NickName != "" {
		userIds := []uint{}
		err = source.DB().Model(response.User{}).Where("nick_name like ?", "%"+info.NickName+"%").Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		for _, db := range dbs {
			db.Where("`user_id` in ?", userIds)
		}

	}
	if info.UserName != "" {
		shippingIds := []uint{}
		err = source.DB().Model(model.ShippingAddress{}).Where("realname like ?", "%"+info.UserName+"%").Pluck("id", &shippingIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		for _, db := range dbs {
			db.Where("`shipping_address_id` in ?", shippingIds)
		}

	}
	if info.UserMobile != "" {
		userIds := []uint{}
		err = source.DB().Model(model.ShippingAddress{}).Where("mobile like ?", "%"+strings.Trim(info.UserMobile,
			" ")+"%").Pluck("id",
			&userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		for _, db := range dbs {
			db.Where("`shipping_address_id` in ?", userIds)
		}

	}

	if info.ShippingSN != "" {
		var orderIds []uint
		err = source.DB().Model(model.OrderExpress{}).Where("express_no = ?", info.ShippingSN).Pluck("order_id", &orderIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		for _, db := range dbs {
			db.Where("`id` in ?", orderIds)
		}

	}

	if info.CloudOrderId != 0 {
		var orderIds []uint
		source.DB().Model(&CloudOrderItem{}).Where("cloud_order_id = ?", info.CloudOrderId).Pluck("order_id", &orderIds)
		for _, db := range dbs {
			db.Where("id in ?", orderIds)
		}
	}

	err = allDb.Count(&total).Error
	if info.Status != nil {
		if *info.Status == model.WaitReceive {
			allDb.Order("sent_at DESC")
		} else if *info.Status == model.WaitSend {
			allDb.Order("paid_at DESC")
		} else {
			allDb.Order("created_at DESC")
		}

	} else {
		allDb.Order("created_at DESC")
	}
	err = allDb.Select("orders.*").Limit(limit).Offset(offset).Find(&list).Error
	err = WaitPayNumDb.Where("orders.status = ?", model.WaitPay).Count(&WaitPayNum).Error
	err = WaitSendNumDb.Where("orders.status = ?", model.WaitSend).Count(&WaitSendNum).Error
	err = WaitReceiveNumDb.Where("orders.status = ?", model.WaitReceive).Count(&WaitReceiveNum).Error
	err = CompletedNumDb.Where("orders.status = ?", model.Completed).Count(&CompletedNum).Error
	err = ClosedNumDb.Where("orders.status = ?", model.Closed).Count(&ClosedNum).Error
	//err = BackNumDb.Where("refund_status = ?", model.Refunding).Count(&BackNum).Error  //后端页面改版不需要查询
	//err = RefundNumDb.Where("refund_status = ?", model.RefundComplete).Count(&RefundNum).Error //后端页面改版不需要查询
	err, list = GetPrintButton(list)
	err, list = GetChangePriceButton(list)

	return err, list, total, WaitPayNum, WaitSendNum, WaitReceiveNum, CompletedNum, ClosedNum, BackNum, RefundNum
}

func GetGdOrderInfoList(info request.OrderAdminSearch) (err error, list []response.Order, total int64, WaitPayNum, WaitSendNum, WaitReceiveNum, CompletedNum, ClosedNum, BackNum, RefundNum int64) {
	if needSearchInEs(info) {
		return GetEsOrderInfoList(info)
	}
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	order := response.Order{}
	var dbs []*gorm.DB
	allDb := source.DB().Model(&order).Preload(clause.Associations).Preload("OrderItems.AfterSales")

	var WaitPayNumDb = source.DB().Model(&order)

	var WaitSendNumDb = source.DB().Model(&order)

	var WaitReceiveNumDb = source.DB().Model(&order)

	var CompletedNumDb = source.DB().Model(&order)

	var ClosedNumDb = source.DB().Model(&order)

	var BackNumDb = source.DB().Model(&order)

	var RefundNumDb = source.DB().Model(&order)
	dbs = append(dbs, allDb, WaitPayNumDb, WaitSendNumDb, WaitReceiveNumDb, CompletedNumDb, ClosedNumDb, BackNumDb, RefundNumDb)

	if info.PluginID != 0 {
		for _, db := range dbs {
			db.Where("plugin_id=?", info.PluginID)
		}
	}
	if info.SourceShopName != "" {
		for _, db := range dbs {
			db.Where("source_shop_name like ?", "%"+info.SourceShopName+"%")
		}
	}

	if info.SupplierID != nil {
		if *info.SupplierID == 999999 {
			for _, db := range dbs {
				db.Where("`supplier_id` > 0")
			}

		} else {
			for _, db := range dbs {
				db.Where("`supplier_id` = ?", &info.SupplierID).Where("`gather_supply_id` = 0")
			}

		}
	}

	if info.ApplicationID > 0 {
		for _, db := range dbs {
			db.Where("`application_id` = ?", info.ApplicationID)
		}

	}

	// 指定供应链
	if info.GatherSupplierID != nil {
		for _, db := range dbs {
			db.Where("gather_supply_id = ?", info.GatherSupplierID).Where("supplier_id = 0")
		}
	}

	if info.PaySN != "" {
		var payInfoIds []uint
		err = source.DB().Model(&response.PayInfo{}).Where("pay_sn = ?", info.PaySN).Pluck("id", &payInfoIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		for _, db := range dbs {
			db.Where("`pay_info_id` in ?", payInfoIds)
		}
	}
	if info.PayTypeID != 0 {
		for _, db := range dbs {
			db.Where("`pay_type_id` = ?", info.PayTypeID)
		}

	}
	if info.UserID > 0 {
		for _, db := range dbs {
			db.Where("`user_id` = ?", info.UserID)
		}
	}

	if info.JushuitanBind != nil {
		for _, db := range dbs {
			db.Where("`jushuitan_bind` = ?", info.JushuitanBind)
		}

	}
	if info.GatherSupplierStatus != "" && info.GatherSupplierID != nil {
		for _, db := range dbs {
			db.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		}

	}
	//直播间id
	if info.ShareLiveRoomId != 0 {
		for _, db := range dbs {
			db.Where("`share_live_room_id` = ?", info.ShareLiveRoomId)
		}
	}
	if info.Status != nil {
		if *info.Status == 5 {
			for _, db := range dbs {
				db.Where("`refund_status` = ?", model.Refunding)
			}
		} else if *info.Status == 6 {
			for _, db := range dbs {
				db.Where("`refund_status` = ?", model.RefundComplete)
			}
		} else {
			allDb.Where("`status` = ?", info.Status)

		}
	}
	if info.RefundStatus != nil {
		if *info.RefundStatus == -1 {
			for _, db := range dbs {
				db.Joins("left join after_sales on after_sales.order_id = orders.id").Where("after_sales.status = ?", model.ClosedRefund)
			}
		} else {
			for _, db := range dbs {
				db.Where("`refund_status` = ?", info.RefundStatus)
			}
		}

	}
	if info.OrderSN != "" {
		for _, db := range dbs {
			db.Where("`order_sn` = ?", info.OrderSN)
		}
	}

	if info.Note != "" {
		for _, db := range dbs {
			db.Where("note like ?", "%"+info.Note+"%")
		}
	}
	if info.SupplySN != "" {
		for _, db := range dbs {
			db.Where("gather_supply_sn like ?", "%"+info.SupplySN+"%")
		}
	}
	if info.ThirdOrderSN != "" {
		//变为=号原因：因为like%% 不走索引导致客户通过第三方单号查询超时 如果想用like 可使用 like xxx%
		for _, db := range dbs {
			db.Where("third_order_sn = ?", info.ThirdOrderSN)
		}
	}
	if info.IsWxMiniSend != 0 {
		for _, db := range dbs {
			db.Where("is_wx_mini_send = ?", info.IsWxMiniSend)
		}
	}
	var timeType string
	timeType = "created_at"
	if info.TimeType != nil {
		switch *info.TimeType {
		case 0:
			timeType = "created_at"
			break
		case 1:
			timeType = "paid_at"
			break
		case 2:
			timeType = "sent_at"
			break
		case 3:
			timeType = "received_at"
			break
		default:
			timeType = "created_at"
			break
		}
	}
	if info.StartAT != "" {
		for _, db := range dbs {
			db.Where("`"+timeType+"` >= ?", info.StartAT)
		}
	}
	if info.EndAT != "" {
		for _, db := range dbs {
			db.Where("`"+timeType+"` <= ?", info.EndAT)
		}
	}
	if info.ProductTitle != "" {
		orderIds := []uint{}
		err = source.DB().Model(model.OrderItem{}).Where("title like ?", "%"+info.ProductTitle+"%").Group("order_id").Pluck("order_id", &orderIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		for _, db := range dbs {
			db.Where("`id` in ?", orderIds)
		}

	}
	if info.AppShopName != "" {
		appShopId := []uint{}
		err = source.DB().Model(response.ApplicationShop{}).Where("shop_name like ?", "%"+info.AppShopName+"%").Pluck("id", &appShopId).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		for _, db := range dbs {
			db.Where("`application_shop_id` in ?", appShopId)
		}
	}
	if info.NickName != "" {
		userIds := []uint{}
		err = source.DB().Model(response.User{}).Where("nick_name like ?", "%"+info.NickName+"%").Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		for _, db := range dbs {
			db.Where("`user_id` in ?", userIds)
		}

	}
	if info.UserName != "" {
		shippingIds := []uint{}
		err = source.DB().Model(model.ShippingAddress{}).Where("realname like ?", "%"+info.UserName+"%").Pluck("id", &shippingIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		for _, db := range dbs {
			db.Where("`shipping_address_id` in ?", shippingIds)
		}

	}
	if info.UserMobile != "" {
		userIds := []uint{}
		err = source.DB().Model(model.ShippingAddress{}).Where("mobile like ?", "%"+strings.Trim(info.UserMobile,
			" ")+"%").Pluck("id",
			&userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		for _, db := range dbs {
			db.Where("`shipping_address_id` in ?", userIds)
		}

	}

	if info.ShippingSN != "" {
		var orderIds []uint
		err = source.DB().Model(model.OrderExpress{}).Where("express_no = ?", info.ShippingSN).Pluck("order_id", &orderIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		for _, db := range dbs {
			db.Where("`id` in ?", orderIds)
		}

	}

	if info.CloudOrderId != 0 {
		var orderIds []uint
		source.DB().Model(&CloudOrderItem{}).Where("cloud_order_id = ?", info.CloudOrderId).Pluck("order_id", &orderIds)
		for _, db := range dbs {
			db.Where("id in ?", orderIds)
		}
	}

	err = allDb.Count(&total).Error
	if err != nil {
		return
	}
	if *info.Status == model.WaitReceive {
		allDb.Order("sent_at DESC")
	} else if *info.Status == model.WaitSend {
		allDb.Order("paid_at DESC")
	} else {
		allDb.Order("created_at DESC")
	}
	err = allDb.Select("orders.*").Limit(limit).Offset(offset).Find(&list).Error
	err = WaitPayNumDb.Where("orders.status = ?", model.WaitPay).Count(&WaitPayNum).Error
	err = WaitSendNumDb.Where("orders.status = ?", model.WaitSend).Count(&WaitSendNum).Error
	err = WaitReceiveNumDb.Where("orders.status = ?", model.WaitReceive).Count(&WaitReceiveNum).Error
	err = CompletedNumDb.Where("orders.status = ?", model.Completed).Count(&CompletedNum).Error
	err = ClosedNumDb.Where("orders.status = ?", model.Closed).Count(&ClosedNum).Error
	//err = BackNumDb.Where("refund_status = ?", model.Refunding).Count(&BackNum).Error  //后端页面改版不需要查询
	//err = RefundNumDb.Where("refund_status = ?", model.RefundComplete).Count(&RefundNum).Error //后端页面改版不需要查询
	err, list = GetPrintButton(list)
	err, list = GetChangePriceButton(list)
	return err, list, total, WaitPayNum, WaitSendNum, WaitReceiveNum, CompletedNum, ClosedNum, BackNum, RefundNum
}

func AfterOrderOperationUserUpgrade(orderId uint) error {
	var order model.Order
	err := source.DB().Where("id = ?", orderId).First(&order).Error
	err = service.AutoUpgrade(order.UserID)
	return err
}

type StatusCounts []StatusCount
type StatusCount struct {
	Status int  `json:"status" form:"status" gorm:"column:status;comment:状态;type:smallint;size:3;"` // 订单状态
	Count  uint `json:"count" form:"count" gorm:"column:count;comment:累计数量;type:uint;size:11;"`   // 订单状态数量
}

type TotalData Total
type Total struct {
	AmountTotal uint `json:"amount_total" form:"amount_total" gorm:"column:amount_total;comment:总价;"`
	Count       uint `json:"count" form:"count" gorm:"column:count;comment:订单状态数量;type:uint;size:11;"` // 订单状态数量
}

type OrderLineCharts []OrderLineChart
type OrderLineChart struct {
	Amount uint64 `json:"amount" form:"amount" gorm:"column:amount;comment:总价;"`
	Count  uint   `json:"count" form:"count" gorm:"column:count;comment:数量;type:uint;size:11;"` // 订单数量
	Date   string `json:"date" form:"date" gorm:"column:date;comment:年月日;type:varchar(30);size:30;"`
	Status int    `json:"status" form:"status" gorm:"column:status;comment:状态;type:smallint;size:3;"` // 订单状态

}
type HotSellingProductsOrder struct {
	ProductId uint  `json:"product_id" form:"product_id"`
	SumQty    int64 `json:"sumQty" form:"sumQty"` // 订单状态数量
}

// 获取订单30天热销商品前30个
func GetHotSellingProductsOrders(userID uint) (err error, list interface{}) {
	// 获取本月的第一天
	now := time.Now()
	// 获取本月第一天
	firstDay := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	// 获取本月最后一天
	lastDay := firstDay.AddDate(0, 1, -1)
	var productIds []uint
	var hotSellingProductsOrders []HotSellingProductsOrder
	source.DB().Model(&model.OrderItem{}).Joins("INNER JOIN orders on orders.id = order_items.order_id and orders.is_plugin = 0").Where("order_items.created_at >= ? and order_items.created_at <= ?", firstDay, lastDay).Select("sum(order_items.Qty) as sumQty,order_items.product_id").Group("order_items.product_id").Order("sumQty desc").Limit(30).Find(&hotSellingProductsOrders)
	if len(hotSellingProductsOrders) > 0 {
		for _, item := range hotSellingProductsOrders {
			productIds = append(productIds, item.ProductId)
		}
	}
	if len(productIds) > 0 {
		var productCardListSearch productRequest.ProductCardListSearch

		var user productService.User
		err = source.DB().Preload("UserLevel").First(&user, userID).Error
		if err != nil {
			log.Log().Error("获取失败", zap.Any("err", err))
			err = errors.New("获取会员失败" + err.Error())
			return
		}
		if user.UserLevel.Discount > 0 {
			if productCardListSearch.MinPrice > 0 {
				productCardListSearch.MinPrice = productCardListSearch.MinPrice / (user.UserLevel.Discount / 10000)
				productCardListSearch.SearchByLevelPrice = 1
			}
			if productCardListSearch.MaxPrice > 0 {
				productCardListSearch.MaxPrice = productCardListSearch.MaxPrice / (user.UserLevel.Discount / 10000)
				productCardListSearch.SearchByLevelPrice = 1
			}
		}
		productCardListSearch.UserLevelID = user.LevelID

		productCardListSearch.Page = 1
		productCardListSearch.PageSize = 30
		productCardListSearch.Ids = &productIds
		var isDisplay = 1
		productCardListSearch.IsDisplay = &isDisplay

		err, list, _ = productService.GetProductCardList(productCardListSearch, user.UserLevel.Level)
	}

	return
}

// @function: GetTodayOrderTotal
// @description: 获取今日交易额,会员总额,今日订单数,待付款订单数，待发货订单数
// @return: err error, list interface
func GetTodayOrderTotal(supplierID uint, userId uint) (err error, list interface{}) {

	var dataMap map[string]uint

	var statusCount StatusCounts

	var afterSalesCount int64 //售后中的订单数量

	var total TotalData

	var yesterday TotalData

	dataMap = make(map[string]uint)

	var memberTotal uint

	startUnix := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 0, 0, 0, 0, time.Now().Location())

	endUnix := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 23, 59, 59, 0, time.Now().Location())

	yesterdayStartUnix := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day()-1, 0, 0, 0, 0, time.Now().Location())

	yesterdayEndUnix := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day()-1, 23, 59, 59, 0, time.Now().Location())

	total = GetGMV(supplierID, startUnix, endUnix, userId)

	yesterday = GetGMV(supplierID, yesterdayStartUnix, yesterdayEndUnix, userId)

	orderModel2 := source.DB().Model(model.Order{})
	if supplierID != 0 {
		orderModel2 = orderModel2.Where("`supplier_id` = ?", supplierID)
	}
	if userId != 0 {
		orderModel2 = orderModel2.Where("`user_id` = ?", userId)
	}

	err = orderModel2.Where("status in (1,2,3)").Pluck("COALESCE(SUM(amount), 0) as amount1", &memberTotal).Error

	orderModel3 := source.DB().Model(model.Order{})
	if supplierID != 0 {
		orderModel3 = orderModel3.Where("`supplier_id` = ?", supplierID)
	}
	if userId != 0 {
		orderModel3 = orderModel3.Where("`user_id` = ?", userId)
	}
	err = orderModel3.Where("status in (0,1)").Select("COALESCE(count(amount), 0) as count,status").Group("status").Find(&statusCount).Error
	dataMap["obligation_count"] = 0
	dataMap["wait_sending_count"] = 0

	var afterSales = source.DB().Model(&model.AfterSales{}).Where("after_sales.status in (0,1,2,3,5,6)").Joins("INNER join after_sales_audits on after_sales_audits.after_sales_id = after_sales.id and after_sales_audits.status != -1")
	if supplierID != 0 || userId != 0 {
		afterSales = afterSales.Joins("INNER join orders on orders.id = after_sales.order_id")
	}
	if supplierID != 0 {
		afterSales = afterSales.Where("orders.supplier_id = ?", supplierID)
	}
	if userId != 0 {
		afterSales = afterSales.Where("orders.user_id = ?", userId)
	}
	afterSales.Count(&afterSalesCount)
	for i := 0; i < len(statusCount); i++ {
		if statusCount[i].Status == 0 {
			dataMap["obligation_count"] = statusCount[i].Count
		}
		if statusCount[i].Status == 1 {
			dataMap["wait_sending_count"] = statusCount[i].Count
		}
	}
	var nowProductCount int64
	//有会员id代表是前端请求不需要这个
	if userId == 0 {
		source.DB().Model(&productModel.ProductModel{}).Joins("left join skus on skus.product_id = products.id").Where("products.is_display = 1").Count(&nowProductCount)
	}
	_, settingData := setting.GetProductSetting()

	dataMap["trading_volume_today"] = total.AmountTotal //今日交易额

	dataMap["today_order_count"] = total.Count //今日订单数

	dataMap["trading_volume_yesterday"] = yesterday.AmountTotal //昨日交易额

	dataMap["yesterday_order_count"] = yesterday.Count //昨日订单数

	dataMap["member_total"] = memberTotal

	dataMap["after_sales_count"] = uint(afterSalesCount)

	dataMap["nowProductCount"] = uint(nowProductCount)                   //今日在线商品数量
	dataMap["oldProductCount"] = uint(settingData.Value.OldProductCount) //昨日在线商品数量

	list = dataMap
	return
}

/*
*

	获取交易额
*/
func GetGMV(supplierID uint, startUnix time.Time, endUnix time.Time, userId uint) (totalData TotalData) {
	orderModel1 := source.DB().Model(model.Order{})
	if supplierID != 0 {
		orderModel1 = orderModel1.Where("`supplier_id` = ?", supplierID)
	}
	if userId != 0 {
		orderModel1 = orderModel1.Where("`user_id` = ?", userId)
	}
	orderModel1.Where("status in (1,2,3)").Where("`created_at` >= ?", startUnix).Where("`created_at` <= ?", endUnix).Select("COALESCE(SUM(amount), 0) as amount_total,COALESCE(count(id), 0) as count").First(&totalData)
	return
}

type UserCount struct {
	LevelId uint `json:"level_id" form:"level_id" gorm:"column:level_id;comment:总价;"`          //会员等级id
	Count   uint `json:"count" form:"count" gorm:"column:count;comment:数量;type:uint;size:11;"` // 会员数量
}

type ProductCount struct {
	GatherSupplyID uint `json:"gather_supply_id" form:"gather_supply_id" gorm:"column:gather_supply_id;comment:供应链id;index;"` // 供应链id
	Count          uint `json:"count" form:"count" gorm:"column:count;comment:数量;type:uint;size:11;"`                          // 数量
}
type SupplierCount struct {
	CategoryId uint `json:"category_id" form:"category_id" gorm:"column:category_id;comment:分类id;"`
	Count      uint `json:"count" form:"count" gorm:"column:count;comment:数量;type:uint;size:11;"` // 数量
}

type UserStatistics struct {
	Avatar    string `json:"avatar" form:"avatar" gorm:"column:avatar;comment:用户头像url;type:varchar(255);size:255;"`
	NickName  string `json:"nickname" form:"nickName" gorm:"column:nick_name;comment:用户昵称;type:varchar(50);size:50;"`
	Username  string `json:"username" form:"username" gorm:"comment:用户登录名"`
	LevelId   uint   `json:"level_id" form:"level_id" gorm:"column:level_id;comment:总价;"` //会员等级id
	Total     uint   `json:"total" form:"total" gorm:"column:total;comment:分类id;"`
	Count     uint   `json:"count" form:"count" gorm:"column:count;comment:数量;type:uint;size:11;"` // 数量
	LevelName string `json:"level_name" form:"level_name" gorm:"-"`                                  //会员等级名称

}

type UserCountResponse struct {
	LevelName string `json:"level_name" form:"level_name" gorm:"column:level_name;comment:总价;"`    //会员等级名称
	Count     uint   `json:"count" form:"count" gorm:"column:count;comment:数量;type:uint;size:11;"` // 会员数量
}

type ProductCountResponse struct {
	Name  string `json:"name" form:"name" gorm:"column:name;comment:类型名称;index;"`            // 类型名称
	Count uint   `json:"count" form:"count" gorm:"column:count;comment:数量;type:uint;size:11;"` // 会员数量
}

type SupplierCountResponse struct {
	Name  string `json:"name" form:"name" gorm:"column:name;comment:类型名称;index;"`            // 供应商分类名称
	Count uint   `json:"count" form:"count" gorm:"column:count;comment:数量;type:uint;size:11;"` // 数量
}

// GetTotal @function: GetTotal
// @description: 获取会员统计，商品统计，供应商统计，商品销量排行，采购端排行
// @return: err error, list interface
func GetTotal() (err error, data interface{}) {
	var dataMap = make(map[string]interface{})

	userCountResponses := UserStatisticsData()
	supplierCountResponses := SupplierStatisticsData()
	//productCountResponses := ProductStatisticsData(supplyChainRequest.RankingRequest{Type: 0})

	dataMap["userCountResponses"] = userCountResponses //会员统计
	//dataMap["productCountResponses"] = productCountResponses   //商品统计
	dataMap["supplierCountResponses"] = supplierCountResponses //供应商统计

	data = dataMap
	return
}

// UserStatisticsData 概况会员统计
// userCountResponses   会员统计返回数据（每个等级都有多少会员） 会员统计：按会员等级统计，取排名前 6 的等级，其他计入其他等级；最多一共 7 个指标；
// userStatistics  会员统计返回数据，按照订单金额，订单数量 返回前十个
func UserStatisticsData() (userCountResponses []UserCountResponse) {

	var userLevel []userModel.UserLevel

	//会员统计开始
	var userCounts []UserCount
	source.DB().Model(&userModel.User{}).Group("level_id").Select("level_id,count(id) as count").Order("count desc").Find(&userCounts)
	if len(userCounts) > 0 {
		var levelIds []uint
		for _, item := range userCounts {
			if item.LevelId > 0 {
				levelIds = append(levelIds, item.LevelId)
			}
		}
		var userLevelMap = make(map[uint]string)
		if len(levelIds) > 0 {
			//如果有才查询
			source.DB().Model(&userModel.UserLevel{}).Where("id in ?", levelIds).Select("id,name").Find(&userLevel)
			//处理等级数据 方面下面使用
			for _, item := range userLevel {
				userLevelMap[item.ID] = item.Name
			}
		}
		//会员统计：按会员等级统计，取排名前 6 的等级，其他计入其他等级；最多一共 7 个指标；
		var userCountResponseOther UserCountResponse //其他,需求只保留前六个，其他全部为其他
		userCountResponseOther.LevelName = "其他"
		for key, item := range userCounts {
			if key > 5 {
				userCountResponseOther.Count += item.Count
				continue
			}
			var userCountResponse UserCountResponse
			if item.LevelId == 0 {
				userCountResponse.LevelName = "默认等级"
				userCountResponse.Count = item.Count
			} else {
				userCountResponse.Count = item.Count

				if _, ok := userLevelMap[item.LevelId]; ok {
					userCountResponse.LevelName = userLevelMap[item.LevelId]
				} else {
					userCountResponse.LevelName = strconv.Itoa(int(item.LevelId))
				}

				//不存在直接赋值id
				if userCountResponse.LevelName == "" {
					userCountResponse.LevelName = strconv.Itoa(int(item.LevelId))
				}
			}
			userCountResponses = append(userCountResponses, userCountResponse)
		}
		if len(userCounts) > 6 {
			userCountResponses = append(userCountResponses, userCountResponseOther)
		}
	}

	return
}

// 采购排行
func UserProcurementRanking(rankingRequest supplyChainRequest.RankingRequest) (err error, userStatistics []UserStatistics) {
	var userLevel []userModel.UserLevel
	//如果有才查询
	source.DB().Model(&userModel.UserLevel{}).Select("id,name").Find(&userLevel)
	//处理等级数据 方面下面使用
	var userLevelMap = make(map[uint]string)
	for _, item := range userLevel {
		userLevelMap[item.ID] = item.Name
	}
	db := source.DB().Model(&userModel.User{}).Joins("left join orders on orders.user_id = users.id").Select("users.avatar,users.nick_name,users.username,users.level_id,sum(orders.amount) as total,count(orders.id) as count").Group("users.id").Order("total desc,count desc").Limit(10)
	//0全部 1今日2本周3本月4昨日5上周6上月
	end, start := rankingRequest.ProcurementRankingStartEneTime()

	db.Where("orders.status in (1,2,3)")

	if rankingRequest.Type > 0 {
		db.Where("orders.created_at >= ? and orders.created_at <= ?", start, end)
	}
	err = db.Find(&userStatistics).Error

	for key, item := range userStatistics {
		if item.LevelId == 0 {
			userStatistics[key].LevelName = "默认等级"
		} else {

			if _, ok := userLevelMap[item.LevelId]; ok {
				userStatistics[key].LevelName = userLevelMap[item.LevelId]
			} else {
				userStatistics[key].LevelName = strconv.Itoa(int(item.LevelId))
			}
		}
	}
	return
}

// 商品销量排行
func ProductProcurementRanking(rankingRequest supplyChainRequest.RankingRequest) (err error, productSalesData []supplyChainResponse.Product) {

	//商品销量排行
	db := source.DB().Model(&supplyChainResponse.Product{}).Preload("GatherSupply").Preload("Supplier")
	//排除卡券资源的商品  所以排除这个即可 -- 肖韶笑要求
	err, supplyIDs := fuluSupplyService.GetSupplyIDs()
	if err == nil && len(supplyIDs) > 0 {
		db.Where("products.gather_supply_id not in ?", supplyIDs)
	}
	//0全部 1今日2本周3本月4昨日5上周6上月
	end, start := rankingRequest.ProcurementRankingStartEneTime()

	//如果传时间了 就关联订单查询时间内的销量。如果没有传就使用商品表的销量
	if rankingRequest.Type > 0 {
		db.Joins("left join order_items on order_items.product_id = products.id")
		db.Where("order_items.created_at >= ? and order_items.created_at <= ?", start, end)
		db.Joins("left join orders on orders.ID = order_items.order_id")
		db.Where("orders.status in (1,2,3)")
		db.Group("products.id")
		db.Select("sum(order_items.qty) as order_sales,products.*")
	} else {
		db.Select("products.sales as order_sales,products.*")
	}
	err = db.Order("order_sales desc").Limit(10).Find(&productSalesData).Error
	if err != nil {
		return
	}
	return
}

// SupplierStatisticsData 供应商统计
// supplierCountResponses 供应商统计 供应商统计：按供应商分类进行统计供应商数量，取排名前 6 的分类，其他计入其他 最多 7 个指标；
func SupplierStatisticsData() (supplierCountResponses []SupplierCountResponse) {
	//供应商统计开始
	var supplierCounts []SupplierCount
	source.DB().Model(&suppplierModel.Supplier{}).Select("category_id,count(id) as count").Group("category_id").Order("count desc").Find(&supplierCounts)
	if len(supplierCounts) > 0 {
		var categoryIds []uint
		for _, item := range supplierCounts {
			categoryIds = append(categoryIds, item.CategoryId)
		}
		var supplierCategory []suppplierModel.SupplierCategory
		source.DB().Model(&suppplierModel.SupplierCategory{}).Select("id,name").Where("id in ?", categoryIds).Find(&supplierCategory)
		var supplierCategoryMap = make(map[uint]string)
		for _, item := range supplierCategory {
			supplierCategoryMap[item.ID] = item.Name
		}
		//供应商统计：按供应商分类进行统计供应商数量，取排名前 6 的分类，其他计入其他 最多 7 个指标；
		var supplierCountResponseOther SupplierCountResponse //其他,需求只保留前六个，其他全部为其他
		supplierCountResponseOther.Name = "其他"
		for key, item := range supplierCounts {
			if key > 5 {
				supplierCountResponseOther.Count += item.Count
			}
			var supplierCountResponse SupplierCountResponse

			if _, ok := supplierCategoryMap[item.CategoryId]; ok {
				supplierCountResponse.Name = supplierCategoryMap[item.CategoryId]
			} else {
				supplierCountResponse.Name = strconv.Itoa(int(item.CategoryId))
			}

			supplierCountResponse.Count = item.Count
			//不存在直接赋值id
			if supplierCountResponse.Name == "" {
				supplierCountResponse.Name = strconv.Itoa(int(item.CategoryId))
			}
			supplierCountResponses = append(supplierCountResponses, supplierCountResponse)
		}
		if len(supplierCounts) > 6 {
			supplierCountResponses = append(supplierCountResponses, supplierCountResponseOther)
		}
	}
	return
}

// ProductStatisticsData 商品统计 商品统计：按平台自营（含供应商）、供应链来统计商品数量，取排名前五位，其他计入其他里面，最多一共 6 个指标；这个方法只有今日与昨日
func ProductStatisticsData(rankingRequest supplyChainRequest.RankingRequest) (productCountResponses []ProductCountResponse) {
	//商品统计开始
	var productCount []ProductCount
	db := source.DB().Model(&productModel.ProductModel{}).Select("gather_supply_id,count(id) as count").Group("gather_supply_id").Order("count desc")
	//0全部 1今日4昨日
	if rankingRequest.Type > 0 {
		if rankingRequest.Type == 1 {
			db.Where("is_display = 1")
		}
		if rankingRequest.Type == 4 {
			err, settingData := setting.GetProductSetting()
			if err != nil {
				return
			}
			if settingData.Value.ProductStatisticsData == "" {
				return
			}
			err = json.Unmarshal([]byte(settingData.Value.ProductStatisticsData), &productCountResponses)
			if err != nil {
				return
			}
			return
		}

	}
	db.Find(&productCount)
	if len(productCount) > 0 {
		var gatherSupplyIDs []uint //供应链id
		for _, item := range productCount {
			if item.GatherSupplyID > 0 {
				gatherSupplyIDs = append(gatherSupplyIDs, item.GatherSupplyID)
			}
		}
		var gatherSupplyMap = make(map[uint]string)
		//如果有才查询
		if len(gatherSupplyIDs) > 0 {
			for _, gatherSupplyID := range gatherSupplyIDs {
				_, settingValue := yzGoSetting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
				var gettingData applicationService.GettingData
				_ = json.Unmarshal([]byte(settingValue), &gettingData)
				gatherSupplyMap[gatherSupplyID] = gettingData.BaseInfo.StoreName
			}
			//、商品统计：按平台自营（含供应商）、供应链来统计商品数量，取排名前五位，其他计入其他里面，最多一共 6 个指标；
			var productCountResponseOther ProductCountResponse //其他,需求只保留前五个，其他全部为其他
			productCountResponseOther.Name = "其他"
			for key, item := range productCount {
				if key > 4 {
					productCountResponseOther.Count += item.Count
					continue
				}
				var productCountResponse ProductCountResponse //商品统计返回数据
				if item.GatherSupplyID == 0 {
					productCountResponse.Name = "自营"
					productCountResponse.Count = item.Count
				} else {

					if _, ok := gatherSupplyMap[item.GatherSupplyID]; ok {
						productCountResponse.Name = gatherSupplyMap[item.GatherSupplyID]
					} else {
						productCountResponse.Name = strconv.Itoa(int(item.GatherSupplyID))
					}
					productCountResponse.Count = item.Count
					//不存在直接赋值id
					if productCountResponse.Name == "" {
						productCountResponse.Name = strconv.Itoa(int(item.GatherSupplyID))
					}
				}
				productCountResponses = append(productCountResponses, productCountResponse)
			}
			if len(productCount) > 5 {
				productCountResponses = append(productCountResponses, productCountResponseOther)
			}

		}
	}
	//商品统计结束
	return
}

//
//@function: GetOrderLineChart
//@description: 获取总订单,已完成,已发货折线图
//@return: err error, list interface

func GetOrderLineChart(supplyChainRequest supplyChainRequest.LineChartRequest, supplierID uint, userId uint) (err error, list interface{}) {

	var orderLineChart OrderLineCharts

	var dataMap map[string]interface{}

	dataMap = make(map[string]interface{})

	var ResponseOrderLineChartsPay OrderLineCharts

	var ResponseOrderLineChartsCompleted OrderLineCharts

	var ResponseOrderLineChartsDelivered OrderLineCharts

	var ResponseOrderLineChartsAmount OrderLineCharts

	beforeTime, endTime, day, types := supplyChainRequest.StartEneTime()

	//nowTime := time.Now()

	orderModel := source.DB().Model(model.Order{})
	if supplierID != 0 {
		orderModel = orderModel.Where("`supplier_id` = ?", supplierID)
	}
	if userId != 0 {
		orderModel = orderModel.Where("`user_id` = ?", userId)
	}
	if supplyChainRequest.Type == 2 {
		orderModel = orderModel.Select("COALESCE(SUM(amount), 0) as amount,LEFT(created_at,7) as date,status,COALESCE(count(id), 0) as count")
	} else {
		orderModel = orderModel.Select("COALESCE(SUM(amount), 0) as amount,LEFT(created_at,10) as date,status,COALESCE(count(id), 0) as count")
	}
	err = orderModel.Where("`created_at` >= ?", beforeTime).Where("`created_at` <= ?", endTime).Group("date,status").Order("date ASC").Find(&orderLineChart).Error

	//if len(orderLineChart) > 0 {

	var orderLineChartPay OrderLineChart
	var orderLineChartDelivered OrderLineChart
	var orderLineChartCompleted OrderLineChart
	var date string

	for i := 0; i < len(orderLineChart); i++ {

		if date == orderLineChart[i].Date && date != "" {

			//已支付
			if orderLineChart[i].Status == 1 || orderLineChart[i].Status == 2 || orderLineChart[i].Status == 3 {

				orderLineChartPay.Count += orderLineChart[i].Count
				orderLineChartPay.Amount += orderLineChart[i].Amount
				orderLineChartPay.Date = orderLineChart[i].Date

				ResponseOrderLineChartsAmount = append(ResponseOrderLineChartsAmount, orderLineChart[i]) //销售额
			}
			//已发货
			if orderLineChart[i].Status == 2 || orderLineChart[i].Status == 3 {

				orderLineChartDelivered.Count += orderLineChart[i].Count
				orderLineChartDelivered.Amount += orderLineChart[i].Amount
				orderLineChartDelivered.Date = orderLineChart[i].Date
			}
			//已完成
			if orderLineChart[i].Status == 3 {
				orderLineChartCompleted.Count += orderLineChart[i].Count
				orderLineChartCompleted.Amount += orderLineChart[i].Amount
				orderLineChartCompleted.Date = orderLineChart[i].Date
			}
		}
		//|| i >= len(orderLineChart)-1
		if date != orderLineChart[i].Date {
			if orderLineChartPay.Date != "" {
				ResponseOrderLineChartsPay = append(ResponseOrderLineChartsPay, orderLineChartPay)
			}
			if orderLineChartDelivered.Date != "" {
				ResponseOrderLineChartsDelivered = append(ResponseOrderLineChartsDelivered, orderLineChartDelivered)
			}
			if orderLineChartCompleted.Date != "" {
				ResponseOrderLineChartsCompleted = append(ResponseOrderLineChartsCompleted, orderLineChartCompleted)
			}

			date = orderLineChart[i].Date
			//初始化
			orderLineChartPay.Count = 0
			orderLineChartPay.Amount = 0
			orderLineChartPay.Date = orderLineChart[i].Date

			orderLineChartDelivered.Count = 0
			orderLineChartDelivered.Amount = 0
			orderLineChartDelivered.Date = orderLineChart[i].Date

			orderLineChartCompleted.Count = 0
			orderLineChartCompleted.Amount = 0
			orderLineChartCompleted.Date = orderLineChart[i].Date

			//已支付
			if orderLineChart[i].Status == 1 || orderLineChart[i].Status == 2 || orderLineChart[i].Status == 3 {

				orderLineChartPay.Count = orderLineChart[i].Count
				orderLineChartPay.Amount = orderLineChart[i].Amount
				orderLineChartPay.Date = orderLineChart[i].Date

				ResponseOrderLineChartsAmount = append(ResponseOrderLineChartsAmount, orderLineChart[i]) //销售额
			}
			//已发货
			if orderLineChart[i].Status == 2 || orderLineChart[i].Status == 3 {
				orderLineChartDelivered.Count = orderLineChart[i].Count
				orderLineChartDelivered.Amount = orderLineChart[i].Amount
				orderLineChartDelivered.Date = orderLineChart[i].Date
			}
			//已完成
			if orderLineChart[i].Status == 3 {
				orderLineChartCompleted.Count = orderLineChart[i].Count
				orderLineChartCompleted.Amount = orderLineChart[i].Amount
				orderLineChartCompleted.Date = orderLineChart[i].Date
			}
		}
	}

	//dataMap["pay"] = ResponseOrderLineChartsPay
	//
	//dataMap["completed"] = ResponseOrderLineChartsCompleted
	//
	//dataMap["delivered"] = ResponseOrderLineChartsDelivered

	dataMap["total"] = OrderLineChartTotal(orderLineChart, beforeTime, day, types) //总订单数量

	dataMap["amount"] = OrderLineChartTotal(ResponseOrderLineChartsAmount, beforeTime, day, types) //销售额 已支付 已完成 已发货

	dataMap["pay"] = OrderLineChartTotal(ResponseOrderLineChartsPay, beforeTime, day, types) //已支付订单数量

	dataMap["completed"] = PolishingOrderLineChart(ResponseOrderLineChartsCompleted, beforeTime, day, 2, types) //完成订单数量

	dataMap["delivered"] = PolishingOrderLineChart(ResponseOrderLineChartsDelivered, beforeTime, day, 3, types) //发货订单数量

	list = dataMap
	return
}

type LineChart struct {
	Date  string `json:"date" form:"date" gorm:"column:date;comment:年月日;type:varchar(30);size:30;"` //时间
	Total uint   `json:"total" form:"total" gorm:"column:total;comment:;type:uint;size:11;"`           //如果统计金额这里是金额单位是分，如果统计数量这里是数量
}

// 新增会员折线图
func GetAddUserLineChart(supplyChainRequest supplyChainRequest.LineChartRequest) (err error, list interface{}) {

	var addUserLineChart []LineChart

	var dataMap map[string]interface{}

	dataMap = make(map[string]interface{})

	beforeTime, endTime, day, types := supplyChainRequest.StartEneTime()

	orderModel := source.DB().Model(&userModel.User{})

	err = orderModel.Where("`created_at` >= ?", beforeTime).Where("`created_at` <= ?", endTime).Select("COALESCE(count(id), 0) as total,LEFT(created_at,10) as date").Group("date").Order("date ASC").Find(&addUserLineChart).Error

	dataMap["lineChart"] = LineChartData(addUserLineChart, beforeTime, day, types) //总订单数量

	list = dataMap
	return
}

// 站内余额充值折线图
func GetPurchasingBalanceLineChart(supplyChainRequest supplyChainRequest.LineChartRequest) (err error, list interface{}) {
	var addUserLineChart []LineChart

	var dataMap map[string]interface{}

	dataMap = make(map[string]interface{})

	beforeTime, endTime, day, types := supplyChainRequest.StartEneTime()

	orderModel := source.DB().Model(&financeModel.PurchasingBalance{}).Where("business_type = 4")

	err = orderModel.Where("`created_at` >= ?", beforeTime).Where("`created_at` <= ?", endTime).Select("COALESCE(sum(amount), 0) as total,LEFT(created_at,10) as date").Group("date").Order("date ASC").Find(&addUserLineChart).Error

	dataMap["lineChart"] = LineChartData(addUserLineChart, beforeTime, day, types)

	list = dataMap
	return
}

// 新增采购端数量折线图
func GetApplicationLineChart(supplyChainRequest supplyChainRequest.LineChartRequest) (err error, list interface{}) {
	var addUserLineChart []LineChart

	var dataMap map[string]interface{}

	dataMap = make(map[string]interface{})

	beforeTime, endTime, day, types := supplyChainRequest.StartEneTime()

	orderModel := source.DB().Model(&applicationModel.Application{})

	err = orderModel.Where("`created_at` >= ?", beforeTime).Where("`created_at` <= ?", endTime).Select("COALESCE(count(id), 0) as total,LEFT(created_at,10) as date").Group("date").Order("date ASC").Find(&addUserLineChart).Error

	dataMap["lineChart"] = LineChartData(addUserLineChart, beforeTime, day, types)

	list = dataMap
	return
}

// 新增商品数量折线图
func GetProductLineChart(supplyChainRequest supplyChainRequest.LineChartRequest) (err error, list interface{}) {
	var addUserLineChart []LineChart

	var dataMap map[string]interface{}

	dataMap = make(map[string]interface{})

	beforeTime, endTime, day, types := supplyChainRequest.StartEneTime()

	orderModel := source.DB().Model(&productModel.ProductModel{})

	err = orderModel.Where("`created_at` >= ?", beforeTime).Where("`created_at` <= ?", endTime).Select("COALESCE(count(id), 0) as total,LEFT(created_at,10) as date").Group("date").Order("date ASC").Find(&addUserLineChart).Error

	dataMap["lineChart"] = LineChartData(addUserLineChart, beforeTime, day, types)

	list = dataMap
	return
}

// @function: PolishingOrderLineChart
// @description: 补齐缺少的日期
// @param: OrderLineChart  beforeTime  maxdepth
// @return: err error, list interface
func PolishingOrderLineChart(orderLineChart OrderLineCharts, beforeTime time.Time, maxdepth int, status int, types int) (list OrderLineCharts) {
	var depth = 0

	for i := 0; i < maxdepth; i++ {
		var date string
		if types == 0 {
			date = beforeTime.AddDate(0, 0, i).Format("2006-01-02")
		} else {
			date = beforeTime.AddDate(0, i, 0).Format("2006-01")

		}
		if depth < len(orderLineChart) && date == orderLineChart[depth].Date {
			list = append(list, orderLineChart[depth])
			depth++
		} else {
			list = append(list, OrderLineChart{Date: date, Count: 0, Status: status})

		}
	}
	return
}

// @function: OrderLineChartTotal
// @description: 整合订单总数据
// @param: OrderLineChart  beforeTime  maxdepth
// @return: err error, list interface
// types 0 日  1月
func OrderLineChartTotal(orderLineChart OrderLineCharts, beforeTime time.Time, maxdepth, types int) (list OrderLineCharts) {

	for i := 0; i < maxdepth; i++ {
		var date string
		if types == 0 {
			date = beforeTime.AddDate(0, 0, i).Format("2006-01-02")
		} else {
			date = beforeTime.AddDate(0, i, 0).Format("2006-01")
		}

		var data = OrderLineChart{Date: date, Count: 0, Amount: 0}

		for olc := 0; olc < len(orderLineChart); olc++ {
			if date == orderLineChart[olc].Date {
				data.Count += orderLineChart[olc].Count
				data.Amount += orderLineChart[olc].Amount
			}
		}
		list = append(list, data)
	}
	return

}

// @function: OrderLineChartTotal
// @description: 整合新增会员,站内余额充值折线图数据
// @param: OrderLineChart  beforeTime  maxdepth
// @return: err error, list interface
func LineChartData(orderLineChart []LineChart, beforeTime time.Time, maxdepth int, types int) (list []LineChart) {

	for i := 0; i < maxdepth; i++ {
		var date string
		if types == 0 {
			date = beforeTime.AddDate(0, 0, i).Format("2006-01-02")
		} else {
			date = beforeTime.AddDate(0, i, 0).Format("2006-01")
		}
		var data = LineChart{Date: date, Total: 0}

		for olc := 0; olc < len(orderLineChart); olc++ {
			if date == orderLineChart[olc].Date {
				data.Total += orderLineChart[olc].Total
			}
		}
		list = append(list, data)
	}
	return

}

func GetOrderExpress(logisticRequest request.LogisticRequest) (err error, orderExpress []express.OrderExpress) {
	var orderModel []order.Order
	err = source.DB().Where("third_order_sn = ?", logisticRequest.OrderSn).Preload("OrderExpress.OrderItems").Preload("OrderExpress.ItemExpress").Preload("GatherSupply").Find(&orderModel).Error
	for _, v := range orderModel {
		for _, orderE := range v.OrderExpress {

			var itemExpressMap = make(map[uint]express.ItemExpress)
			for _, itemExp := range orderE.ItemExpress {
				itemExpressMap[itemExp.OrderItemID] = itemExp
			}
			for k, orderItem := range orderE.OrderItems {
				orderE.OrderItems[k].SendNum = itemExpressMap[orderItem.ID].Num
			}
			if len(orderE.OrderItems) == 0 && v.GatherSupply.CategoryID == 3 {
				orderE.OrderItems = append(orderE.OrderItems, express.ExpressOrderItem{
					ID:      0,
					SendNum: 0,
				})
				orderE.IsEmpty = 1
			}
			orderExpress = append(orderExpress, orderE)
		}
	}
	return
}

func GetCantConnectionOrderExpress(logisticRequest request.LogisticRequest, appId uint) (err error, orderExpress []express.OrderExpress) {
	var orderModel []order.Order
	//增加is_plugin = 0 避免查询到插件订单
	err = source.DB().Where("is_plugin = 0").Where("application_id = ?", appId).Where("status > 1").Where("is_connection in (0,1)").Preload("OrderExpress.OrderItems").Preload("OrderExpress.ItemExpress").Limit(20).Find(&orderModel).Error
	for _, v := range orderModel {
		for _, orderE := range v.OrderExpress {
			var itemExpress = orderE.ItemExpress
			var itemExpressMap = make(map[uint]express.ItemExpress)
			for _, itemExp := range itemExpress {
				itemExpressMap[itemExp.OrderItemID] = itemExp
			}
			for k, orderItem := range orderE.OrderItems {
				orderE.OrderItems[k].SendNum = itemExpressMap[orderItem.ID].Num
			}
			orderE.ThirdOrderSn = v.ThirdOrderSN
			orderExpress = append(orderExpress, orderE)
		}
	}
	return
}

func SetOrderConnected(logisticRequest request.SetConnectedRequest) (err error) {
	err = source.DB().Model(&order.Order{}).Where("third_order_sn in ?", logisticRequest.ThirdOrderSn).Preload("OrderExpress.OrderItems").Update("is_connection", 2).Error
	return
}

type SupplierSource struct {
	source.Model
	SupplierID               uint   `json:"supplier_id"`
	Name                     string `json:"name"`
	SupplierSourceCategoryID uint   `json:"supplier_source_category_id"`
}

type SupplierSourceCategory struct {
	source.Model
	SupplierID uint   `json:"supplier_id"`
	Name       string `json:"name"`
}

func ExportOrderInfoList(info request.OrderAdminSearch) (err error) {
	if info.StartAT == "" || info.EndAT == "" {
		info.EndAT = time.Now().Format("2006-01-02 15:04:05")
		info.StartAT = time.Now().AddDate(0, 0, -30).Format("2006-01-02 15:04:05")
	}
	go ExportOrder(info)
	return
}
func ExportOrder(info request.OrderAdminSearch) (err error, link string) {
	if info.StartAT == "" || info.EndAT == "" {
		info.StartAT = time.Now().AddDate(0, 0, -30).Format("2006-01-02 15:04:05")
		info.EndAT = time.Now().Format("2006-01-02 15:04:05")
	}
	info.PageSize = 5000
	order := response.Order{}
	db := source.DB().Model(&order).Preload(clause.Associations).Preload("OrderExpresss.OrderItems").Preload("OrderItems.AfterSales").Where("is_plugin = ?", 0)
	var orders []response.Order
	var total int64
	if needSearchInEs(info) {
		var ids []uint
		err, ids, total = GetEsOrderInfoListByExport(info)
		db.Where("id in ?", ids)
	} else {

		// 如果有条件搜索 下方会自动创建搜索语句

		if info.SupplierID != nil {
			if *info.SupplierID == 999999 {
				db.Where("`supplier_id` > 0")
			} else {
				db.Where("`supplier_id` = ?", &info.SupplierID)
			}
		}

		if info.ApplicationID > 0 {
			db.Where("`application_id` = ?", info.ApplicationID)
		}

		if info.GatherSupplierID != nil {
			db.Where("`gather_supply_id` = ?", &info.GatherSupplierID)
		}
		if info.Status != nil {
			db.Where("orders.status = ?", info.Status)
		}

		if info.OrderType > 0 {
			db.Where("orders.order_type = ?", info.OrderType)

		}
		if info.OrderTypeNote != "" {
			db.Where("orders.order_type_note LIKE ?", "%"+info.OrderTypeNote+"%")
		}
		if info.PaySN != "" {
			var payInfoIds []uint
			err = source.DB().Model(&response.PayInfo{}).Where("pay_sn = ?", info.PaySN).Pluck("id", &payInfoIds).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return
			}

			db.Where("`pay_info_id` in ?", payInfoIds)

		}
		if info.PayTypeID != 0 {
			db.Where("`pay_type_id` = ?", info.PayTypeID)
		}
		if info.UserID > 0 {
			db.Where("`user_id` = ?", info.UserID)
		}

		if info.OrderSN != "" {
			db.Where("`order_sn` = ?", info.OrderSN)
		}
		if info.ThirdOrderSN != "" {
			db.Where("`third_order_sn` like ?", "%"+info.ThirdOrderSN+"%")
		}
		var timeType string
		timeType = "created_at"
		if info.TimeType != nil {
			switch *info.TimeType {
			case 0:
				timeType = "created_at"
				break
			case 1:
				timeType = "paid_at"
				break
			case 2:
				timeType = "sent_at"
				break
			case 3:
				timeType = "received_at"
				break
			default:
				timeType = "created_at"
				break
			}
		}
		if info.StartAT != "" {
			db.Where("`"+timeType+"` >= ?", info.StartAT)
		}
		if info.EndAT != "" {
			db.Where("`"+timeType+"` <= ?", info.EndAT)
		}
		if info.ProductTitle != "" {
			orderIds := []uint{}
			err = source.DB().Model(model.OrderItem{}).Where("title like ?", "%"+info.ProductTitle+"%").Pluck("order_id", &orderIds).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return
			}

			db.Where("`id` in ?", orderIds)

		}

		if info.NickName != "" {
			userIds := []uint{}
			err = source.DB().Model(response.User{}).Where("nick_name like ?", "%"+info.NickName+"%").Pluck("id", &userIds).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return
			}

			db.Where("`user_id` in ?", userIds)

		}
		if info.UserName != "" {
			shippingIds := []uint{}
			err = source.DB().Model(model.ShippingAddress{}).Where("realname like ?", "%"+info.UserName+"%").Pluck("id", &shippingIds).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return
			}

			db.Where("`shipping_address_id` in ?", shippingIds)

		}
		if info.UserMobile != "" {
			userIds := []uint{}
			err = source.DB().Model(model.ShippingAddress{}).Where("mobile like ?", "%"+info.UserMobile+"%").Pluck("id", &userIds).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return
			}

			db.Where("`shipping_address_id` in ?", userIds)

		}

		if info.ShippingSN != "" {
			var orderIds []uint
			err = source.DB().Model(model.OrderExpress{}).Where("express_no = ?", info.ShippingSN).Pluck("order_id", &orderIds).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return
			}

			db.Where("`id` in ?", orderIds)

		}
		err = db.Count(&total).Error
		if err != nil {
			return
		}
	}

	var productExportRecord model.OrderExportRecord
	productExportRecord.OrderCount = total
	productExportRecord.StatusString = "导出中"
	productExportRecord.UserID = info.UserID
	productExportRecord.AdminID = info.AdminID

	if info.SupplierID != nil {
		productExportRecord.SearchSupplierID = *info.SupplierID
	}
	if info.IsSupplier == 1 {
		productExportRecord.SupplierID = *info.SupplierID

	}
	err = source.DB().Create(&productExportRecord).Error
	if err != nil {
		log.Log().Error("订单导出错误1：", zap.Any("err", err.Error()))
		return
	}
	excelPage := total/int64(info.PageSize) + 1
	var links []string
	timeString := time.Now().Format("20060102150405")
	var StartOrderAt, EndOrderAt *source.LocalTime
	path := config.Config().Local.Path + "/export_order"
	for ei := 1; ei <= int(excelPage); ei++ {
		err = db.
			Preload("OrderItems.Sku").
			Preload("OrderItems.Product").
			Preload("OrderItems").
			Preload("PayInfo").
			Preload("User").
			Preload("Supplier").
			Preload("GatherSupply").
			Order("created_at DESC").Limit(info.PageSize).Offset(info.PageSize * (ei - 1)).Find(&orders).Error
		if err != nil {
			return
		}
		f := excelize.NewFile()
		// 创建一个工作表
		index := f.NewSheet("Sheet1")
		// 设置单元格的值
		f.SetCellValue("Sheet1", "A1", "订单id")
		f.SetCellValue("Sheet1", "B1", "订单编号")
		f.SetCellValue("Sheet1", "C1", "三方单号")
		f.SetCellValue("Sheet1", "D1", "支付单号")
		f.SetCellValue("Sheet1", "E1", "子订单号")
		f.SetCellValue("Sheet1", "F1", "会员ID")
		f.SetCellValue("Sheet1", "G1", "粉丝昵称")
		f.SetCellValue("Sheet1", "H1", "收货人姓名")
		f.SetCellValue("Sheet1", "I1", "联系电话")
		f.SetCellValue("Sheet1", "J1", "省")
		f.SetCellValue("Sheet1", "K1", "市")
		f.SetCellValue("Sheet1", "L1", "区")
		f.SetCellValue("Sheet1", "M1", "收货地址")

		// 修改表头部分
		f.SetCellValue("Sheet1", "N1", "商品简码")
		f.SetCellValue("Sheet1", "O1", "商品名称")
		f.SetCellValue("Sheet1", "P1", "商品规格")
		f.SetCellValue("Sheet1", "Q1", "规格条码")
		f.SetCellValue("Sheet1", "R1", "规格编号")
		f.SetCellValue("Sheet1", "S1", "商品编号")
		f.SetCellValue("Sheet1", "T1", "商品数量")
		f.SetCellValue("Sheet1", "U1", "支付方式")
		f.SetCellValue("Sheet1", "V1", "技术服务费")
		f.SetCellValue("Sheet1", "W1", "商品单价")
		f.SetCellValue("Sheet1", "X1", "商品小计")
		f.SetCellValue("Sheet1", "Y1", "运费")
		f.SetCellValue("Sheet1", "Z1", "应收款")
		f.SetCellValue("Sheet1", "AA1", "状态")
		f.SetCellValue("Sheet1", "AB1", "下单时间")
		f.SetCellValue("Sheet1", "AC1", "付款时间")
		f.SetCellValue("Sheet1", "AD1", "发货时间")
		f.SetCellValue("Sheet1", "AE1", "完成时间")
		f.SetCellValue("Sheet1", "AF1", "快递公司")
		f.SetCellValue("Sheet1", "AG1", "快递单号")
		f.SetCellValue("Sheet1", "AH1", "订单备注")
		f.SetCellValue("Sheet1", "AI1", "用户备注")
		f.SetCellValue("Sheet1", "AJ1", "附加")
		f.SetCellValue("Sheet1", "AK1", "供货金额")
		f.SetCellValue("Sheet1", "AL1", "供应链单号")
		f.SetCellValue("Sheet1", "AM1", "供应链名称")
		f.SetCellValue("Sheet1", "AN1", "供应商名称")
		f.SetCellValue("Sheet1", "AO1", "商品来源")
		f.SetCellValue("Sheet1", "AP1", "来源分类")
		f.SetCellValue("Sheet1", "AQ1", "开票名称")
		f.SetCellValue("Sheet1", "AR1", "税收分类编码")
		f.SetCellValue("Sheet1", "AS1", "采购税率")
		f.SetCellValue("Sheet1", "AT1", "供应单价")
		f.SetCellValue("Sheet1", "AU1", "销售单价")
		f.SetCellValue("Sheet1", "AV1", "店铺")
		f.SetCellValue("Sheet1", "AW1", "成本小计")
		f.SetCellValue("Sheet1", "AX1", "多商城")
		f.SetCellValue("Sheet1", "AY1", "商品单位")
		i := 2

		var supplierSource []SupplierSource
		var supplierSourceCategory []SupplierSourceCategory
		err = source.DB().Find(&supplierSource).Error
		if err != nil {
			log.Log().Error("订单导出错误2：", zap.Any("err", err.Error()))
			return
		}
		err = source.DB().Find(&supplierSourceCategory).Error
		if err != nil {
			log.Log().Error("订单导出错误3：", zap.Any("err", err.Error()))
			return
		}
		var supplierSourceMap = make(map[uint]SupplierSource)
		var supplierSourceCategoryMap = make(map[uint]SupplierSourceCategory)
		for _, sS := range supplierSource {
			supplierSourceMap[sS.ID] = sS
		}
		for _, sSc := range supplierSourceCategory {
			supplierSourceCategoryMap[sSc.ID] = sSc
		}

		for k, v := range orders {
			//因为查询时是创建时间倒叙 所以第一个就是最晚下单时间
			if EndOrderAt == nil {
				EndOrderAt = v.CreatedAt
			}
			//因为查询时是创建时间倒叙 所以最后一个就是最早下单时间
			if k+1 == len(orders) {
				StartOrderAt = v.CreatedAt
			}

			// 剩余技术服务费(元)
			//restTechnicalServicesFee := decimal.NewFromInt(int64(v.TechnicalServicesFee)).DivRound(decimal.NewFromInt(100), 2)
			for ii, item := range v.OrderItems {
				//只有第一条记录显示运费
				if ii > 0 {
					v.Freight = 0
				}
				var wValue = "" //W位置的值
				//如果是代发货状态使用子订单的发货状态
				if v.Status == model.WaitSend {
					wValue = "订单状态:" + model.GetItemSendStatusName(item.SendStatus)
				} else {
					wValue = "订单状态:" + model.GetStatusName(v.Status)
				}

				//售后状态
				if item.AfterSales.ID != 0 {
					if item.AfterSales.Status == model.WaitAuditStatus {
						var afterSalesAudit model.AfterSalesAudit
						err = source.DB().Where("after_sales_id = ?", item.AfterSales.ID).Last(&afterSalesAudit).Error
						if err != nil {
							wValue += ",售后状态--查询失败:" + err.Error()
						} else {
							//如果不是驳回申请-因为驳回申请不会关闭主售后申请
							if afterSalesAudit.Status != model.ClosedRefundStatus {
								wValue += ",售后状态:" + item.AfterSales.StatusName
							}
						}
					} else if item.AfterSales.Status != model.ClosedStatus {
						//wValue = "售后状态:" + item.AfterSales.StatusName
						//var isAfterSalesStatus = 0
						//如果售后状态是完成 并且 子订单只是部分售后时 返回订单状态
						//if item.AfterSales.Status == model.CompletedStatus && item.Amount != 0 {
						//	isAfterSalesStatus = 1
						//}
						//if isAfterSalesStatus == 0 {
						wValue += ",售后状态:" + item.AfterSales.StatusName
						//}
					}
				}
				var companyNames []string
				var expressNos []string
				//对应子订单与物流
				for _, orderExpressItem := range v.OrderExpresss {
					for _, orderExpressOrderItem := range orderExpressItem.OrderItems {
						if orderExpressOrderItem.ID == item.ID {
							expressNos = append(expressNos, orderExpressItem.ExpressNo)

							var companyName string
							err, companyName = express2.GetCompanyByCode(orderExpressItem.CompanyCode)
							if err != nil {
								companyNames = append(companyNames, err.Error())
							} else {
								companyNames = append(companyNames, companyName)
							}
						}
					}
				}

				expressNoStr := strings.Join(expressNos, ",")
				companyNameStr := strings.Join(companyNames, ",")

				var sentAt string
				if v.SentAt != nil {
					sentAt = v.SentAt.Format("2006-01-02 15:04:05")
				}
				var createAt string
				if v.CreatedAt != nil {
					createAt = v.CreatedAt.Format("2006-01-02 15:04:05")
				}
				var payAt string
				if v.PaidAt != nil {
					payAt = v.PaidAt.Format("2006-01-02 15:04:05")
				}
				var receivedAt string
				if v.ReceivedAt != nil {
					receivedAt = v.ReceivedAt.Format("2006-01-02 15:04:05")
				}
				// 服务费(元)
				var technicalServicesFee decimal.Decimal
				//直接使用子订单技术服务费就可以不用计算
				//if ii+1 == len(v.OrderItems) {
				//	technicalServicesFee = restTechnicalServicesFee
				//} else {
				//	if v.ItemAmount-v.RefundAmount != 0 {
				//		technicalServicesFee = decimal.NewFromInt(int64(item.Amount*v.TechnicalServicesFee)).DivRound(decimal.NewFromInt(int64(v.ItemAmount-v.RefundAmount)*100), 2)
				//	}
				//	restTechnicalServicesFee = restTechnicalServicesFee.Sub(technicalServicesFee)
				//}

				technicalServicesFee = decimal.NewFromInt(int64(item.TechnicalServicesFee)).DivRound(decimal.NewFromInt(100), 2)

				// 商品单价
				var skuPrice decimal.Decimal
				if item.Qty != 0 {
					skuPrice = decimal.NewFromInt(int64(item.Amount)).DivRound(decimal.NewFromInt(int64(item.Qty)), 2).DivRound(decimal.NewFromInt(100), 2)
				} else {
					skuPrice = decimal.NewFromInt(0)
				}
				// 应收款(元)
				var amount decimal.Decimal

				amount = decimal.NewFromInt(int64(item.Amount)).DivRound(decimal.NewFromInt(100), 2).Add(technicalServicesFee)
				println(item.Amount, technicalServicesFee.String())
				println("应收款(元)", amount.String())

				var supplierSourceName string
				var supplierSourceCategoryName string
				if _, ok := supplierSourceMap[item.Product.SupplierSourceID]; !ok {
					supplierSourceName = ""
				} else {
					supplierSourceName = supplierSourceMap[item.Product.SupplierSourceID].Name
				}
				if _, ok := supplierSourceCategoryMap[item.Product.SupplierSourceCategoryID]; !ok {
					supplierSourceCategoryName = ""
				} else {
					supplierSourceCategoryName = supplierSourceCategoryMap[item.Product.SupplierSourceCategoryID].Name
				}
				var taxProductName, taxCode, taxRate, taxShortCode string
				if item.Product.BillPosition == 1 {
					taxShortCode = item.Product.ShortCode
					taxProductName = item.Product.TaxProductName
					taxCode = item.Product.TaxCode
					taxRate = strconv.Itoa(item.Product.TaxRate)
				} else {
					taxShortCode = item.Sku.ShortCode
					taxProductName = item.Sku.TaxProductName
					taxCode = item.Sku.TaxCode
					taxRate = strconv.Itoa(item.Sku.TaxRate)
				}

				f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), v.ID)
				f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), strconv.Itoa(int(v.OrderSN)))
				f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), v.ThirdOrderSN)
				f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), v.PayInfo.PaySn)
				f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), item.ID) //子订单号
				f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), v.User.ID)
				f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), v.User.NickName)
				f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), v.ShippingAddress.Realname)
				f.SetCellValue("Sheet1", "I"+strconv.Itoa(i), v.ShippingAddress.Mobile)
				f.SetCellValue("Sheet1", "J"+strconv.Itoa(i), v.ShippingAddress.Province)
				f.SetCellValue("Sheet1", "K"+strconv.Itoa(i), v.ShippingAddress.City)
				f.SetCellValue("Sheet1", "L"+strconv.Itoa(i), v.ShippingAddress.County)
				// 如果详细地址包含了省市区
				var shippingAddressContainsRegion bool
				shippingAddressContainsRegion, err = mapping.HasMatchingRegionsWhichScoreGreaterThan("ali_region", v.ShippingAddress.Detail, 25.0)
				if err != nil {
					err = nil
				}
				if strings.HasPrefix(v.ShippingAddress.Detail, v.ShippingAddress.Province+v.ShippingAddress.City+v.ShippingAddress.County+v.ShippingAddress.Town) || shippingAddressContainsRegion {
					f.SetCellValue("Sheet1", "M"+strconv.Itoa(i), v.ShippingAddress.Detail)
				} else {
					f.SetCellValue("Sheet1", "M"+strconv.Itoa(i), v.ShippingAddress.Province+v.ShippingAddress.City+v.ShippingAddress.County+v.ShippingAddress.Town+v.ShippingAddress.Detail)
				}

				// 修改数据赋值部分
				f.SetCellValue("Sheet1", "N"+strconv.Itoa(i), taxShortCode)
				f.SetCellValue("Sheet1", "O"+strconv.Itoa(i), item.Title)
				f.SetCellValue("Sheet1", "P"+strconv.Itoa(i), item.SkuTitle)
				f.SetCellValue("Sheet1", "Q"+strconv.Itoa(i), item.Sku.Barcode)
				f.SetCellValue("Sheet1", "R"+strconv.Itoa(i), item.Sku.Sn)
				f.SetCellValue("Sheet1", "S"+strconv.Itoa(i), item.Product.Sn)
				f.SetCellValue("Sheet1", "T"+strconv.Itoa(i), strconv.Itoa(int(item.Qty)))
				f.SetCellValue("Sheet1", "U"+strconv.Itoa(i), v.PayType)
				f.SetCellValue("Sheet1", "V"+strconv.Itoa(i), technicalServicesFee)
				f.SetCellValue("Sheet1", "W"+strconv.Itoa(i), skuPrice.StringFixed(2))
				f.SetCellValue("Sheet1", "X"+strconv.Itoa(i), strconv.FormatFloat(float64(item.Amount)/100, 'f', 2, 64))
				f.SetCellValue("Sheet1", "Y"+strconv.Itoa(i), strconv.FormatFloat(float64(v.Freight)/100, 'f', 2, 64))
				f.SetCellValue("Sheet1", "Z"+strconv.Itoa(i), amount.StringFixed(2))
				f.SetCellValue("Sheet1", "AA"+strconv.Itoa(i), wValue)
				f.SetCellValue("Sheet1", "AB"+strconv.Itoa(i), createAt)
				f.SetCellValue("Sheet1", "AC"+strconv.Itoa(i), payAt)
				f.SetCellValue("Sheet1", "AD"+strconv.Itoa(i), sentAt)
				f.SetCellValue("Sheet1", "AE"+strconv.Itoa(i), receivedAt)
				f.SetCellValue("Sheet1", "AF"+strconv.Itoa(i), companyNameStr)
				f.SetCellValue("Sheet1", "AG"+strconv.Itoa(i), expressNoStr)
				f.SetCellValue("Sheet1", "AH"+strconv.Itoa(i), v.Note)
				f.SetCellValue("Sheet1", "AI"+strconv.Itoa(i), v.Remark)
				f.SetCellValue("Sheet1", "AJ"+strconv.Itoa(i), "")
				f.SetCellValue("Sheet1", "AK"+strconv.Itoa(i), strconv.FormatFloat(float64(item.SupplyAmount)/100, 'f', 2, 64))
				f.SetCellValue("Sheet1", "AL"+strconv.Itoa(i), v.GatherSupplySN)
				f.SetCellValue("Sheet1", "AM"+strconv.Itoa(i), v.GatherSupply.Name)
				f.SetCellValue("Sheet1", "AN"+strconv.Itoa(i), v.Supplier.Name)
				f.SetCellValue("Sheet1", "AO"+strconv.Itoa(i), supplierSourceName)
				f.SetCellValue("Sheet1", "AP"+strconv.Itoa(i), supplierSourceCategoryName)
				f.SetCellValue("Sheet1", "AQ"+strconv.Itoa(i), taxProductName)
				f.SetCellValue("Sheet1", "AR"+strconv.Itoa(i), taxCode)
				f.SetCellValue("Sheet1", "AS"+strconv.Itoa(i), taxRate)
				f.SetCellValue("Sheet1", "AT"+strconv.Itoa(i), strconv.FormatFloat(float64(item.Sku.CostPrice)/100, 'f', 2, 64))
				f.SetCellValue("Sheet1", "AU"+strconv.Itoa(i), strconv.FormatFloat(float64(item.Sku.GuidePrice)/100, 'f', 2, 64))
				f.SetCellValue("Sheet1", "AV"+strconv.Itoa(i), v.SourceShopName)
				f.SetCellValue("Sheet1", "AW"+strconv.Itoa(i), strconv.FormatFloat(float64(item.CostAmount)/100, 'f', 2, 64))
				f.SetCellValue("Sheet1", "AX"+strconv.Itoa(i), v.ApplicationShop.ShopName)
				f.SetCellValue("Sheet1", "AY"+strconv.Itoa(i), item.Product.Unit)

				i++
			}
		}
		//设置表格Z的宽度
		f.SetColWidth("Sheet1", "Z", "Z", 30)
		// 设置工作簿的默认工作表
		f.SetActiveSheet(index)
		// 根据指定路径保存文件
		//year, month, day := time.Now().Format("2006-01-02 15:04:05")
		exist, _ := utils.PathExists(path)

		if !exist {
			// 创建文件夹及其所有父目录
			err = os.MkdirAll(path, os.ModePerm)
			if err != nil {
				fmt.Printf("mkdir failed![%v]\n", err)
			} else {
				fmt.Printf("mkdir success!\n")
			}
		}
		if productExportRecord.SupplierID != 0 {
			link = path + "/" + timeString + "-" + strconv.Itoa(ei) + "-" + strconv.Itoa(int(productExportRecord.SupplierID)) + "订单导出.xlsx"
		} else {
			link = path + "/" + timeString + "-" + strconv.Itoa(ei) + "订单导出.xlsx"
		}
		if err = f.SaveAs(link); err != nil {
			log.Log().Error("订单导出错误4：", zap.Any("err", err.Error()))
			return
		}
		links = append(links, link)

	}

	if excelPage > 1 {
		link = path + "/" + timeString + "订单导出.zip"
		err = Zip(link, links)
	}
	productExportRecord.Link = link
	productExportRecord.StatusString = "导出完成"
	productExportRecord.StartOrderAt = StartOrderAt
	productExportRecord.EndOrderAt = EndOrderAt

	err = source.DB().Save(&productExportRecord).Error
	if err != nil {
		log.Log().Error("订单导出错误5：", zap.Any("err", err.Error()))
		return
	}
	err = service2.PublishNotify("订单导出完成", "共导出了"+strconv.Itoa(int(total))+"件商品")

	return err, link
}
func FixOrderData() {

}
func ExportOrderFront(info request.OrderAdminSearch) (err error, link string) {
	if info.StartAT == "" || info.EndAT == "" {
		info.StartAT = time.Now().AddDate(0, 0, -30).Format("2006-01-02 15:04:05")
		info.EndAT = time.Now().Format("2006-01-02 15:04:05")
	}
	info.PageSize = 5000
	order := response.Order{}
	db := source.DB().Model(&order).Preload(clause.Associations).Preload("OrderExpresss.OrderItems").Preload("OrderItems.AfterSales")
	var orders []response.Order
	var total int64
	if needSearchInEs(info) {
		var ids []uint
		err, ids, total = GetEsOrderInfoListByExport(info)
		db.Where("id in ?", ids)
	} else {

		// 如果有条件搜索 下方会自动创建搜索语句

		if info.SupplierID != nil {
			if *info.SupplierID == 999999 {
				db.Where("`supplier_id` > 0")
			} else {
				db.Where("`supplier_id` = ?", &info.SupplierID)
			}
		}

		if info.ApplicationID > 0 {
			db.Where("`application_id` = ?", info.ApplicationID)
		}

		if info.GatherSupplierID != nil {
			db.Where("`gather_supply_id` = ?", &info.GatherSupplierID)
		}
		//120租赁订单,
		var notGatherSupplyType = [...]int{120}
		//此模块没有引定义供应链类型的模块无法直接使用 所以直接写数字
		if info.GatherSupplyType == 0 {
			db.Where("gather_supply_type not in ?", notGatherSupplyType)
		} else {
			db.Where("gather_supply_type = ?", info.GatherSupplyType)
		}
		if info.Status != nil {
			db.Where("`status` = ?", info.Status)
		}
		if info.PaySN != "" {
			var payInfoIds []uint
			err = source.DB().Model(&response.PayInfo{}).Where("pay_sn = ?", info.PaySN).Pluck("id", &payInfoIds).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return
			}

			db.Where("`pay_info_id` in ?", payInfoIds)

		}
		if info.PayTypeID != 0 {
			db.Where("`pay_type_id` = ?", info.PayTypeID)
		}
		if info.UserID > 0 {
			db.Where("`user_id` = ?", info.UserID)
		}

		if info.OrderSN != "" {
			db.Where("`order_sn` = ?", info.OrderSN)
		}
		if info.ThirdOrderSN != "" {
			db.Where("`third_order_sn` like ?", "%"+info.ThirdOrderSN+"%")
		}
		var timeType string
		timeType = "created_at"
		if info.TimeType != nil {
			switch *info.TimeType {
			case 0:
				timeType = "created_at"
				break
			case 1:
				timeType = "paid_at"
				break
			case 2:
				timeType = "sent_at"
				break
			case 3:
				timeType = "received_at"
				break
			default:
				timeType = "created_at"
				break
			}
		}
		if info.StartAT != "" {
			db.Where("`"+timeType+"` >= ?", info.StartAT)
		}
		if info.EndAT != "" {
			db.Where("`"+timeType+"` <= ?", info.EndAT)
		}
		if info.ProductTitle != "" {
			orderIds := []uint{}
			err = source.DB().Model(model.OrderItem{}).Where("title like ?", "%"+info.ProductTitle+"%").Pluck("order_id", &orderIds).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return
			}

			db.Where("`id` in ?", orderIds)

		}

		if info.NickName != "" {
			userIds := []uint{}
			err = source.DB().Model(response.User{}).Where("nick_name like ?", "%"+info.NickName+"%").Pluck("id", &userIds).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return
			}

			db.Where("`user_id` in ?", userIds)

		}
		if info.UserName != "" {
			shippingIds := []uint{}
			err = source.DB().Model(model.ShippingAddress{}).Where("realname like ?", "%"+info.UserName+"%").Pluck("id", &shippingIds).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return
			}

			db.Where("`shipping_address_id` in ?", shippingIds)

		}
		if info.UserMobile != "" {
			userIds := []uint{}
			err = source.DB().Model(model.ShippingAddress{}).Where("mobile like ?", "%"+info.UserMobile+"%").Pluck("id", &userIds).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return
			}

			db.Where("`shipping_address_id` in ?", userIds)

		}

		if info.ShippingSN != "" {
			var orderIds []uint
			err = source.DB().Model(model.OrderExpress{}).Where("express_no = ?", info.ShippingSN).Pluck("order_id", &orderIds).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return
			}

			db.Where("`id` in ?", orderIds)

		}
		err = db.Count(&total).Error
		if err != nil {
			return
		}
	}

	var productExportRecord model.OrderExportRecord
	productExportRecord.OrderCount = total
	productExportRecord.StatusString = "导出中"
	productExportRecord.UserID = info.UserID
	productExportRecord.AdminID = info.AdminID

	if info.SupplierID != nil {
		productExportRecord.SearchSupplierID = *info.SupplierID
	}
	if info.IsSupplier == 1 {
		productExportRecord.SupplierID = *info.SupplierID

	}
	err = source.DB().Create(&productExportRecord).Error
	if err != nil {
		log.Log().Error("订单导出错误1：", zap.Any("err", err.Error()))
		return
	}
	excelPage := total/int64(info.PageSize) + 1
	var links []string
	timeString := time.Now().Format("20060102150405")
	var StartOrderAt, EndOrderAt *source.LocalTime
	path := config.Config().Local.Path + "/export_order"
	for ei := 1; ei <= int(excelPage); ei++ {
		err = db.
			Preload("OrderItems.Sku").
			Preload("OrderItems.Product").
			Preload("OrderItems").
			Preload("PayInfo").
			Preload("User").
			Preload("Supplier").
			Preload("GatherSupply").
			Order("created_at DESC").Limit(info.PageSize).Offset(info.PageSize * (ei - 1)).Find(&orders).Error
		if err != nil {
			return
		}
		f := excelize.NewFile()
		// 创建一个工作表
		index := f.NewSheet("Sheet1")
		// 设置单元格的值
		f.SetCellValue("Sheet1", "A1", "订单id")
		f.SetCellValue("Sheet1", "B1", "订单编号")
		f.SetCellValue("Sheet1", "C1", "三方单号")
		f.SetCellValue("Sheet1", "D1", "支付单号")
		f.SetCellValue("Sheet1", "E1", "子订单号")
		f.SetCellValue("Sheet1", "F1", "会员ID")
		f.SetCellValue("Sheet1", "G1", "粉丝昵称")
		f.SetCellValue("Sheet1", "H1", "收货人姓名")
		f.SetCellValue("Sheet1", "I1", "联系电话")
		f.SetCellValue("Sheet1", "J1", "省")
		f.SetCellValue("Sheet1", "K1", "市")
		f.SetCellValue("Sheet1", "L1", "区")
		f.SetCellValue("Sheet1", "M1", "收货地址")

		f.SetCellValue("Sheet1", "N1", "商品简码")

		f.SetCellValue("Sheet1", "O1", "商品名称")
		f.SetCellValue("Sheet1", "P1", "规格条码")
		f.SetCellValue("Sheet1", "Q1", "规格编号")
		f.SetCellValue("Sheet1", "R1", "商品编号")
		f.SetCellValue("Sheet1", "S1", "商品数量")
		f.SetCellValue("Sheet1", "T1", "支付方式")
		f.SetCellValue("Sheet1", "U1", "技术服务费")
		f.SetCellValue("Sheet1", "V1", "商品单价")
		f.SetCellValue("Sheet1", "W1", "商品小计")
		f.SetCellValue("Sheet1", "X1", "运费")
		f.SetCellValue("Sheet1", "Y1", "应收款")
		f.SetCellValue("Sheet1", "Z1", "状态")
		f.SetCellValue("Sheet1", "AA1", "下单时间")
		f.SetCellValue("Sheet1", "AB1", "付款时间")
		f.SetCellValue("Sheet1", "AC1", "发货时间")
		f.SetCellValue("Sheet1", "AD1", "完成时间")
		f.SetCellValue("Sheet1", "AE1", "快递公司")
		f.SetCellValue("Sheet1", "AF1", "快递单号")
		//f.SetCellValue("Sheet1", "AG1", "订单备注")
		//f.SetCellValue("Sheet1", "AH1", "用户备注")
		//f.SetCellValue("Sheet1", "AI1", "附加")
		//f.SetCellValue("Sheet1", "AJ1", "供货金额")
		//f.SetCellValue("Sheet1", "AK1", "供应链单号")
		//f.SetCellValue("Sheet1", "AL1", "供应链名称")
		//f.SetCellValue("Sheet1", "AM1", "供应商名称")
		f.SetCellValue("Sheet1", "AN1", "商品来源")
		f.SetCellValue("Sheet1", "AO1", "来源分类")
		f.SetCellValue("Sheet1", "AP1", "开票名称")
		f.SetCellValue("Sheet1", "AQ1", "税收分类编码")
		f.SetCellValue("Sheet1", "AR1", "采购税率")
		//f.SetCellValue("Sheet1", "AS1", "供应单价")
		f.SetCellValue("Sheet1", "AT1", "销售单价")
		//f.SetCellValue("Sheet1", "AU1", "店铺")
		f.SetCellValue("Sheet1", "AV1", "商城名称")
		i := 2

		var supplierSource []SupplierSource
		var supplierSourceCategory []SupplierSourceCategory
		err = source.DB().Find(&supplierSource).Error
		if err != nil {
			log.Log().Error("订单导出错误2：", zap.Any("err", err.Error()))
			return
		}
		err = source.DB().Find(&supplierSourceCategory).Error
		if err != nil {
			log.Log().Error("订单导出错误3：", zap.Any("err", err.Error()))
			return
		}
		var supplierSourceMap = make(map[uint]SupplierSource)
		var supplierSourceCategoryMap = make(map[uint]SupplierSourceCategory)
		for _, sS := range supplierSource {
			supplierSourceMap[sS.ID] = sS
		}
		for _, sSc := range supplierSourceCategory {
			supplierSourceCategoryMap[sSc.ID] = sSc
		}

		for k, v := range orders {
			//因为查询时是创建时间倒叙 所以第一个就是最晚下单时间
			if EndOrderAt == nil {
				EndOrderAt = v.CreatedAt
			}
			//因为查询时是创建时间倒叙 所以最后一个就是最早下单时间
			if k+1 == len(orders) {
				StartOrderAt = v.CreatedAt
			}

			// 剩余技术服务费(元)
			restTechnicalServicesFee := decimal.NewFromInt(int64(v.TechnicalServicesFee)).DivRound(decimal.NewFromInt(100), 2)
			for ii, item := range v.OrderItems {

				var wValue = "" //W位置的值
				//如果是代发货状态使用子订单的发货状态
				if v.Status == model.WaitSend {
					wValue = model.GetItemSendStatusName(item.SendStatus)
				} else {
					wValue = model.GetStatusName(v.Status)
				}

				//售后状态
				if item.AfterSales.ID != 0 {
					if item.AfterSales.Status == model.WaitAuditStatus {
						var afterSalesAudit model.AfterSalesAudit
						err = source.DB().Where("after_sales_id = ?", item.AfterSales.ID).Last(&afterSalesAudit).Error
						if err != nil {
							wValue = "售后状态--查询失败:" + err.Error()
						} else {
							//如果不是驳回申请-因为驳回申请不会关闭主售后申请
							if afterSalesAudit.Status != model.ClosedRefundStatus {
								wValue = "售后状态:" + item.AfterSales.StatusName
							}
						}
					} else if item.AfterSales.Status != model.ClosedStatus {
						//wValue = "售后状态:" + item.AfterSales.StatusName
						var isAfterSalesStatus = 0
						//如果售后状态是完成 并且 子订单只是部分售后时 返回订单状态
						if item.AfterSales.Status == model.CompletedStatus && item.Amount != 0 {
							isAfterSalesStatus = 1
						}
						if isAfterSalesStatus == 0 {
							wValue = "售后状态:" + item.AfterSales.StatusName
						}
					}
				}
				var companyName string
				var expressNo string
				//对应子订单与物流
				for _, orderExpressItem := range v.OrderExpresss {
					for _, orderExpressOrderItem := range orderExpressItem.OrderItems {
						if orderExpressOrderItem.ID == item.ID {
							expressNo = orderExpressItem.ExpressNo
							err, companyName = express2.GetCompanyByCode(orderExpressItem.CompanyCode)
							if err != nil {
								companyName = err.Error()
							}
						}
					}
				}

				var sentAt string
				if v.SentAt != nil {
					sentAt = v.SentAt.Format("2006-01-02 15:04:05")
				}
				var createAt string
				if v.CreatedAt != nil {
					createAt = v.CreatedAt.Format("2006-01-02 15:04:05")
				}
				var payAt string
				if v.PaidAt != nil {
					payAt = v.PaidAt.Format("2006-01-02 15:04:05")
				}
				var receivedAt string
				if v.ReceivedAt != nil {
					receivedAt = v.ReceivedAt.Format("2006-01-02 15:04:05")
				}
				// 服务费(元)
				var technicalServicesFee decimal.Decimal
				if ii+1 == len(v.OrderItems) {
					technicalServicesFee = restTechnicalServicesFee
				} else {
					if v.ItemAmount-v.RefundAmount != 0 {
						technicalServicesFee = decimal.NewFromInt(int64(item.Amount*v.TechnicalServicesFee)).DivRound(decimal.NewFromInt(int64(v.ItemAmount-v.RefundAmount)*100), 2)
					}
					restTechnicalServicesFee = restTechnicalServicesFee.Sub(technicalServicesFee)
				}

				// 商品单价
				var skuPrice decimal.Decimal
				if item.Qty != 0 {
					skuPrice = decimal.NewFromInt(int64(item.Amount)).DivRound(decimal.NewFromInt(int64(item.Qty)), 2).DivRound(decimal.NewFromInt(100), 2)
				} else {
					skuPrice = decimal.NewFromInt(0)
				}
				// 应收款(元)
				var amount decimal.Decimal

				amount = decimal.NewFromInt(int64(item.Amount)).DivRound(decimal.NewFromInt(100), 2).Add(technicalServicesFee)
				println(item.Amount, technicalServicesFee.String())
				println("应收款(元)", amount.String())

				var supplierSourceName string
				var supplierSourceCategoryName string
				if _, ok := supplierSourceMap[item.Product.SupplierSourceID]; !ok {
					supplierSourceName = ""
				} else {
					supplierSourceName = supplierSourceMap[item.Product.SupplierSourceID].Name
				}
				if _, ok := supplierSourceCategoryMap[item.Product.SupplierSourceCategoryID]; !ok {
					supplierSourceCategoryName = ""
				} else {
					supplierSourceCategoryName = supplierSourceCategoryMap[item.Product.SupplierSourceCategoryID].Name
				}
				var taxProductName, taxCode, taxRate, taxShortCode string
				if item.Product.BillPosition == 1 {
					taxShortCode = item.Product.ShortCode
					taxProductName = item.Product.TaxProductName
					taxCode = item.Product.TaxCode
					taxRate = strconv.Itoa(item.Product.TaxRate)
				} else {
					taxShortCode = item.Sku.ShortCode
					taxProductName = item.Sku.TaxProductName
					taxCode = item.Sku.TaxCode
					taxRate = strconv.Itoa(item.Sku.TaxRate)
				}

				f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), v.ID)
				f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), strconv.Itoa(int(v.OrderSN)))
				f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), v.ThirdOrderSN)
				f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), v.PayInfo.PaySn)
				f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), item.ID) //子订单号
				f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), v.User.ID)
				f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), v.User.NickName)
				f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), v.ShippingAddress.Realname)
				f.SetCellValue("Sheet1", "I"+strconv.Itoa(i), v.ShippingAddress.Mobile)
				f.SetCellValue("Sheet1", "J"+strconv.Itoa(i), v.ShippingAddress.Province)
				f.SetCellValue("Sheet1", "K"+strconv.Itoa(i), v.ShippingAddress.City)
				f.SetCellValue("Sheet1", "L"+strconv.Itoa(i), v.ShippingAddress.County)
				// 如果详细地址包含了省市区
				var shippingAddressContainsRegion bool
				shippingAddressContainsRegion, err = mapping.HasMatchingRegionsWhichScoreGreaterThan("ali_region", v.ShippingAddress.Detail, 25.0)
				if err != nil {
					err = nil
				}
				if strings.HasPrefix(v.ShippingAddress.Detail, v.ShippingAddress.Province+v.ShippingAddress.City+v.ShippingAddress.County+v.ShippingAddress.Town) || shippingAddressContainsRegion {
					f.SetCellValue("Sheet1", "M"+strconv.Itoa(i), v.ShippingAddress.Detail)
				} else {
					f.SetCellValue("Sheet1", "M"+strconv.Itoa(i), v.ShippingAddress.Province+v.ShippingAddress.City+v.ShippingAddress.County+v.ShippingAddress.Town+v.ShippingAddress.Detail)
				}
				f.SetCellValue("Sheet1", "N"+strconv.Itoa(i), taxShortCode)
				f.SetCellValue("Sheet1", "O"+strconv.Itoa(i), item.Title+"["+item.SkuTitle+"]")
				f.SetCellValue("Sheet1", "P"+strconv.Itoa(i), item.Sku.Barcode)
				//if item.Sku.Sn == "" {
				f.SetCellValue("Sheet1", "Q"+strconv.Itoa(i), item.Sku.Sn)
				//} else {
				//	f.SetCellValue("Sheet1", "Q"+strconv.Itoa(i), item.Sku.Sn)
				//}
				//if item.Sku.Barcode == "" {
				f.SetCellValue("Sheet1", "R"+strconv.Itoa(i), item.Product.Sn)
				//} else {
				//	f.SetCellValue("Sheet1", "R"+strconv.Itoa(i), item.Sku.Barcode)
				//}
				f.SetCellValue("Sheet1", "S"+strconv.Itoa(i), strconv.Itoa(int(item.Qty)))
				f.SetCellValue("Sheet1", "T"+strconv.Itoa(i), v.PayType)
				f.SetCellValue("Sheet1", "U"+strconv.Itoa(i), technicalServicesFee)
				f.SetCellValue("Sheet1", "V"+strconv.Itoa(i), skuPrice)
				f.SetCellValue("Sheet1", "W"+strconv.Itoa(i), float64(item.Amount)/100)
				f.SetCellValue("Sheet1", "X"+strconv.Itoa(i), float64(v.Freight)/100)
				f.SetCellValue("Sheet1", "Y"+strconv.Itoa(i), amount)
				f.SetCellValue("Sheet1", "Z"+strconv.Itoa(i), wValue)
				f.SetCellValue("Sheet1", "AA"+strconv.Itoa(i), createAt)
				f.SetCellValue("Sheet1", "AB"+strconv.Itoa(i), payAt)
				f.SetCellValue("Sheet1", "AC"+strconv.Itoa(i), sentAt)
				f.SetCellValue("Sheet1", "AD"+strconv.Itoa(i), receivedAt)
				f.SetCellValue("Sheet1", "AE"+strconv.Itoa(i), companyName)
				f.SetCellValue("Sheet1", "AF"+strconv.Itoa(i), expressNo)
				//f.SetCellValue("Sheet1", "AG"+strconv.Itoa(i), v.Note)
				//f.SetCellValue("Sheet1", "AH"+strconv.Itoa(i), v.Remark)
				//f.SetCellValue("Sheet1", "AI"+strconv.Itoa(i), "")
				//f.SetCellValue("Sheet1", "AJ"+strconv.Itoa(i), float64(item.SupplyAmount)/100)
				//f.SetCellValue("Sheet1", "AK"+strconv.Itoa(i), v.GatherSupplySN)
				//f.SetCellValue("Sheet1", "AL"+strconv.Itoa(i), v.GatherSupply.Name)
				//f.SetCellValue("Sheet1", "AM"+strconv.Itoa(i), v.Supplier.Name)
				f.SetCellValue("Sheet1", "AN"+strconv.Itoa(i), supplierSourceName)
				f.SetCellValue("Sheet1", "AO"+strconv.Itoa(i), supplierSourceCategoryName)
				f.SetCellValue("Sheet1", "AP"+strconv.Itoa(i), taxProductName)
				f.SetCellValue("Sheet1", "AQ"+strconv.Itoa(i), taxCode)
				f.SetCellValue("Sheet1", "AR"+strconv.Itoa(i), taxRate)
				//f.SetCellValue("Sheet1", "AS"+strconv.Itoa(i), float64(item.Sku.CostPrice)/100)
				f.SetCellValue("Sheet1", "AT"+strconv.Itoa(i), float64(item.Sku.GuidePrice)/100)
				//f.SetCellValue("Sheet1", "AU"+strconv.Itoa(i), v.SourceShopName)
				f.SetCellValue("Sheet1", "AV"+strconv.Itoa(i), v.ApplicationShop.ShopName)
				i++
			}
		}
		// 设置工作簿的默认工作表
		f.SetActiveSheet(index)
		// 根据指定路径保存文件
		//year, month, day := time.Now().Format("2006-01-02 15:04:05")
		exist, _ := utils.PathExists(path)

		if !exist {
			// 创建文件夹
			err = os.Mkdir(path, os.ModePerm)
			if err != nil {
				fmt.Printf("mkdir failed![%v]\n", err)
			} else {
				fmt.Printf("mkdir success!\n")
			}
		}
		if productExportRecord.SupplierID != 0 {
			link = path + "/" + timeString + "-" + strconv.Itoa(ei) + "-" + strconv.Itoa(int(productExportRecord.SupplierID)) + "订单导出.xlsx"
		} else {
			link = path + "/" + timeString + "-" + strconv.Itoa(ei) + "订单导出.xlsx"
		}
		if err = f.SaveAs(link); err != nil {
			log.Log().Error("订单导出错误4：", zap.Any("err", err.Error()))
			return
		}
		links = append(links, link)

	}

	if excelPage > 1 {
		link = path + "/" + timeString + "订单导出.zip"
		err = Zip(link, links)
	}
	productExportRecord.Link = link
	productExportRecord.StatusString = "导出完成"
	productExportRecord.StartOrderAt = StartOrderAt
	productExportRecord.EndOrderAt = EndOrderAt

	err = source.DB().Save(&productExportRecord).Error
	if err != nil {
		log.Log().Error("订单导出错误5：", zap.Any("err", err.Error()))
		return
	}
	err = service2.PublishNotify("订单导出完成", "共导出了"+strconv.Itoa(int(total))+"件商品")

	return err, link
}

type OrderExportRecord struct {
	model.OrderExportRecord
	SearchSupplier model.Supplier `json:"search_supplier" gorm:"foreignkey:SearchSupplierID"`

	SysUser SysUser `json:"sys_user" gorm:"foreignkey:admin_id"`
	User    User    `json:"user"`
}
type SysUser struct {
	Id       uint   `json:"id"`
	NickName string `json:"nick_name"`
}

func GetOrderExportRecordList(info request.OrderExportRecordRequest) (err error, total int64, list interface{}) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&OrderExportRecord{}).Preload("SysUser").Preload("User").Preload("SearchSupplier")
	var applications []OrderExportRecord
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.StatusString != "" {
		db = db.Where("`status_string` LIKE ?", "%"+info.StatusString+"%")
	}

	if info.SupplierID > 0 {
		db = db.Where("`supplier_id` = ?", info.SupplierID)
	}
	if info.KeyWord != "" {
		var adminIds []uint
		var userIds []uint
		source.DB().Model(&SysUser{}).Where("nick_name like ?", "%"+info.KeyWord+"%").Pluck("id", &adminIds)
		source.DB().Model(&User{}).Where("nick_name like ? or username like ?", "%"+info.KeyWord+"%", "%"+info.KeyWord+"%").Pluck("id", &userIds)
		if len(adminIds) > 0 {
			db = db.Where("`admin_id` in ?", adminIds)
		}
		if len(userIds) > 0 {
			db = db.Where("`user_id` in ?", userIds)
		}
		if len(adminIds) == 0 && len(userIds) == 0 {
			return
		}
	}

	var timeString = ""
	switch info.DateType {
	case 0:
		timeString = "start_order_at"
		break
	case 1:
		timeString = "end_order_at"
		break
	case 2:
		timeString = "created_at"
		break
	default:
		timeString = "start_order_at"
	}
	if info.StartAt != "" {
		db.Where(""+timeString+" >= ?", info.StartAt)
	}
	if info.EndAt != "" {
		db.Where(""+timeString+" <= ?", info.EndAt)
	}

	if info.ErrorStatus == 1 {
		db = db.Where("`status_string` = ?", "导出完成")
	}

	if info.ErrorStatus == 2 {
		db = db.Where("`status_string` != ?", "导出完成")
	}

	err = db.Count(&total).Error
	err = db.Order("created_at desc").Limit(limit).Offset(offset).Find(&applications).Error

	return err, total, applications
}
func DeleteOrderExportRecord(application model.OrderExportRecord) (err error) {
	err = source.DB().Delete(&application).Error
	return err
}
func Zip(dest string, paths []string) error {
	zfile, err := os.Create(dest)
	if err != nil {
		return err
	}
	defer zfile.Close()
	zipWriter := zip.NewWriter(zfile)
	defer zipWriter.Close()
	for _, src := range paths {
		// remove the trailing path sepeartor if it is a directory
		src := strings.TrimSuffix(src, string(os.PathSeparator))
		err = filepath.Walk(src, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}
			// create local file header
			header, err := zip.FileInfoHeader(info)
			if err != nil {
				return err
			}
			// set compression method to deflate
			header.Method = zip.Deflate
			// set relative path of file in zip archive
			header.Name, err = filepath.Rel(filepath.Dir(src), path)
			if err != nil {
				return err
			}
			if info.IsDir() {
				header.Name += string(os.PathSeparator)
			}
			// create writer for writing header
			headerWriter, err := zipWriter.CreateHeader(header)
			if err != nil {
				return err
			}
			if info.IsDir() {
				return nil
			}
			f, err := os.Open(path)
			if err != nil {
				return err
			}
			defer f.Close()
			_, err = io.Copy(headerWriter, f)
			return err
		})
		if err != nil {
			return err
		}
	}
	return nil
}
func SaveBillImage(info request.SaveBillImage) (err error) {
	var bill model.Bill
	err = source.DB().First(&bill, info.ID).Error
	if err != nil {
		return
	}
	if bill.Type == 1 {
		//如果是电子发票，那么上传图片就应该变为待确认状态
		err = source.DB().Model(&model.Bill{}).Where("id = ?", info.ID).Update("status", 1).Error
	}
	err = source.DB().Model(&model.Bill{}).Where("id = ?", info.ID).Update("image", info.Image).Error
	return
}

func CancelLock(info request.SaveBillImage) (err error) {
	err = source.DB().Model(&model.Order{}).Where("id = ?", info.ID).Update("lock", 0).Error
	return
}
func ReConfirm(info request.SaveBillImage) (err error) {
	var orderModel model.Order
	err = source.DB().First(&orderModel, info.ID).Error
	if err != nil {
		return
	}
	if orderModel.Status == 1 && orderModel.GatherSupplyID > 0 {
		log.Log().Info("reConfirm 发送消息", zap.Any("info", info))
		err = mq_reconfirm.PublishMessage(info.ID, mq_reconfirm.Paid, 0)
		if err != nil {
			log.Log().Info("reConfirm err", zap.Any("err", err))
		}
	}
	return
}

type ApplicationOrderInfoResponse struct {
	Result int                    `json:"result"`
	Msg    string                 `json:"msg"`
	Data   []ApplicationOrderInfo `json:"data"`
}

type ApplicationOrderInfo struct {
	Price      string                 `json:"price"`
	GoodsPrice string                 `json:"goods_price"`
	GoodsTotal int                    `json:"goods_total"`
	StatusName string                 `json:"status_name"`
	PayName    string                 `json:"pay_name"`
	Nickname   string                 `json:"nickname"`
	Mobile     string                 `json:"mobile"`
	Username   string                 `json:"username"`
	Realname   string                 `json:"realname"`
	OrderSn    string                 `json:"order_sn"`
	OrderItem  []ApplicationOrderItem `json:"order_item"`
}

type ApplicationOrderItem struct {
	Total      int    `json:"total"`
	Title      string `json:"title"`
	GoodsPrice string `json:"goods_price"`
	Price      string `json:"price"`
	YzOptionId int    `json:"yz_option_id"`
}

func GetOrderInfoByOrderSn(info request.GetOrderBySn) (err error, data interface{}) {
	if len(info.OrderSns) == 0 {
		err = errors.New("orderSns数量不能为空")
		return
	}
	var applicationOrderInfoResponse ApplicationOrderInfoResponse
	var orderSns []uint
	var orderSn int
	for _, ordersn := range info.OrderSns {
		orderSn, err = strconv.Atoi(ordersn)
		if err != nil {
			return
		}
		orderSns = append(orderSns, uint(orderSn))
	}

	var orders []order.Order
	err = source.DB().Preload("User").Preload("OrderItems.Sku").Where("order_sn in ?", orderSns).Find(&orders).Error
	if err != nil {
		return
	}
	var appOrderInfos []ApplicationOrderInfo
	for _, v := range orders {
		var appOrderInfo ApplicationOrderInfo
		appOrderInfo.OrderSn = strconv.Itoa(int(v.OrderSN))
		appOrderInfo.PayName = v.PayType
		appOrderInfo.Nickname = v.User.NickName
		appOrderInfo.Username = v.User.Username
		appOrderInfo.Realname = v.User.Username
		var appOrderItems []ApplicationOrderItem
		for _, item := range v.OrderItems {
			var appOrderItem ApplicationOrderItem
			appOrderItem.GoodsPrice = strconv.Itoa(int(item.Price * item.Qty))
			appOrderItem.Price = strconv.FormatFloat(float64(item.Amount)/100, 'f', -1, 64)
			appOrderItem.YzOptionId = int(item.Sku.OriginalSkuID)
			appOrderItems = append(appOrderItems, appOrderItem)
		}
		appOrderInfo.OrderItem = appOrderItems
		appOrderInfos = append(appOrderInfos, appOrderInfo)
	}
	applicationOrderInfoResponse.Data = appOrderInfos
	applicationOrderInfoResponse.Result = 1
	applicationOrderInfoResponse.Msg = "OK"
	return err, applicationOrderInfoResponse
}

// 循环收货
func SynReceive(orders []model.Order) {
	for _, item := range orders {
		_ = order.Received(item.ID)
	}
}

// @function: UpdateShippingAddress
// @description: 修改收货信息
// @param: updateShippingAddress request.UpdateShippingAddress
// @return: err error
// isMq 是否需要推送消息，1是0否  （因为采购端同样调用这个来修改收货信息）
func UpdateShippingAddress(updateShippingAddress request.UpdateShippingAddress, isMq int) (err error) {
	if updateShippingAddress.OrderID == 0 && updateShippingAddress.ThirdOrderSN == "" {
		err = errors.New("请提交订单id或者第三方订单号")
		return
	}
	if updateShippingAddress.Province == "" || updateShippingAddress.ProvinceId == 0 {
		err = errors.New("请选择省")
		return
	}
	if updateShippingAddress.City == "" || updateShippingAddress.CityId == 0 {
		err = errors.New("请选择市")
		return
	}
	if updateShippingAddress.County == "" || updateShippingAddress.CountyId == 0 {
		err = errors.New("请选择区")
		return
	}
	//if updateShippingAddress.Town == "" || updateShippingAddress.TownId == 0 {
	//	err = errors.New("请选择街道")
	//	return
	//}
	if updateShippingAddress.Mobile == "" {
		err = errors.New("请填写联系方式")
		return
	}
	if updateShippingAddress.Realname == "" {
		err = errors.New("请填写收货人姓名")
		return
	}
	var orderModel model.Order

	if updateShippingAddress.OrderID != 0 {
		err = source.DB().Where("id = ?", updateShippingAddress.OrderID).First(&orderModel).Error
	} else {
		err = source.DB().Where("third_order_sn = ?", updateShippingAddress.ThirdOrderSN).First(&orderModel).Error
	}
	if orderModel.IsUpdateShippingAddress == 0 {
		log.Log().Error("订单无法修改收货地址", zap.Any("err", err))
		err = errors.New("订单无法修改收货地址")
		return
	}
	if orderModel.Status != model.WaitPay && orderModel.Status != model.WaitSend {
		err = errors.New("订单状态不支持修改收货地址")
		return
	}
	if err != nil {
		err = errors.New("订单不存在")
		return
	}
	var shippingAddressLog model.ShippingAddressLog

	var shippingAddress model.ShippingAddress
	err = source.DB().Where("id = ?", orderModel.ShippingAddressID).First(&shippingAddress).Error
	//可能存在没有收货地址的订单
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("订单收货地址不存在" + err.Error())
		return
	}
	//修改前
	shippingAddressLog.OldRealname = shippingAddress.Realname
	shippingAddressLog.OldMobile = shippingAddress.Mobile
	shippingAddressLog.OldCountryId = shippingAddress.CountryId
	shippingAddressLog.OldProvinceId = shippingAddress.ProvinceId
	shippingAddressLog.OldCityId = shippingAddress.CityId
	shippingAddressLog.OldTownId = shippingAddress.TownId
	shippingAddressLog.OldProvince = shippingAddress.Province
	shippingAddressLog.OldCity = shippingAddress.City
	shippingAddressLog.OldCounty = shippingAddress.County
	shippingAddressLog.OldCountyId = shippingAddress.CountyId
	shippingAddressLog.OldTown = shippingAddress.Town
	shippingAddressLog.OldDetail = shippingAddress.Detail
	shippingAddressLog.OldLng = shippingAddress.Lng
	shippingAddressLog.OldLat = shippingAddress.Lat

	shippingAddress.Realname = updateShippingAddress.Realname
	shippingAddress.Mobile = updateShippingAddress.Mobile
	shippingAddress.CountryId = updateShippingAddress.CountryId
	shippingAddress.ProvinceId = updateShippingAddress.ProvinceId
	shippingAddress.CityId = updateShippingAddress.CityId
	shippingAddress.TownId = updateShippingAddress.TownId
	shippingAddress.Province = updateShippingAddress.Province
	shippingAddress.City = updateShippingAddress.City
	shippingAddress.County = updateShippingAddress.County
	shippingAddress.CountyId = updateShippingAddress.CountyId

	shippingAddress.Town = updateShippingAddress.Town
	shippingAddress.Detail = updateShippingAddress.Detail
	shippingAddress.Lng = updateShippingAddress.Lng
	shippingAddress.Lat = updateShippingAddress.Lat

	err = source.DB().Save(&shippingAddress).Error

	if err != nil {
		err = errors.New("修改发货信息失败")
		return
	}

	//修改后
	shippingAddressLog.Realname = updateShippingAddress.Realname
	shippingAddressLog.Mobile = updateShippingAddress.Mobile
	shippingAddressLog.CountryId = updateShippingAddress.CountryId
	shippingAddressLog.ProvinceId = updateShippingAddress.ProvinceId
	shippingAddressLog.CityId = updateShippingAddress.CityId
	shippingAddressLog.TownId = updateShippingAddress.TownId
	shippingAddressLog.Province = updateShippingAddress.Province
	shippingAddressLog.City = updateShippingAddress.City
	shippingAddressLog.County = updateShippingAddress.County
	shippingAddressLog.CountyId = updateShippingAddress.CountyId

	shippingAddressLog.Town = updateShippingAddress.Town
	shippingAddressLog.Detail = updateShippingAddress.Detail
	shippingAddressLog.Lng = updateShippingAddress.Lng
	shippingAddressLog.Lat = updateShippingAddress.Lat
	shippingAddressLog.OrderID = orderModel.ID
	shippingAddressLog.UserID = orderModel.UserID
	shippingAddressLog.ThirdOrderSN = orderModel.ThirdOrderSN
	err = source.DB().Create(&shippingAddressLog).Error
	if err != nil {
		log.Log().Error("创建收货信息修改记录失败", zap.Any("updateShippingAddress", updateShippingAddress))
		err = errors.New("创建收货信息修改记录失败")
		return
	}
	//可能存在没有收货地址的订单
	if orderModel.ShippingAddressID == 0 {
		err = source.DB().Model(&model.OrderModel{}).Where("id = ?", orderModel.ID).Updates(&model.OrderModel{ShippingAddressID: shippingAddress.ID}).Error
		if err != nil {
			log.Log().Error("创建收货信息修改记录失败", zap.Any("updateShippingAddress", updateShippingAddress))
			err = errors.New("修改订单收货地址失败" + err.Error())
			return
		}
	}
	if isMq == 1 {
		err = mq.PublishMessage(orderModel.ID, mq.UpdateShippingAddress, 0)
		if err != nil {
			return
		}
	}
	return err
}

/*
*

	获取修改收货地址的历史
*/
func GetShippingAddressLog(updateShippingAddress request.UpdateShippingAddress) (addressLog []model.ShippingAddressLog, err error) {
	err = source.DB().Where("order_id = ?", updateShippingAddress.OrderID).Order("created_at desc").Find(&addressLog).Error
	return
}

func HandleNotifyApplicationOrderSend(id uint) (err error) {
	err = mq.PublishMessage(id, mq.Sent, 0)
	return
}

func HandleAllNotifyApplicationOrderSend() (err error) {
	var ids []uint
	err = source.DB().Model(&model.OrderModel{}).Where("status = 2").Pluck("id", &ids).Error
	if err != nil {
		return
	}
	for _, id := range ids {
		err = mq.PublishMessage(id, mq.Sent, 0)
	}
	return
}

func GetOrderByID(orderID uint) (err error, supplyOrder response.Order) {
	err = source.DB().Preload("OrderItems").Where("id = ?", orderID).First(&supplyOrder).Error
	return
}

func GetSupplyID() (err error, supplyID uint) {
	var supply GatherSupply
	err = source.DB().Unscoped().Model(&GatherSupply{}).Where("category_id = ?", common.LEVEL_BIND_PRODUCT).First(&supply).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	supplyID = supply.ID
	return

}

func UserLevelPurchase(orderID uint, messageType mq.OrderMessageType) (err error) {
	var supplyOrder response.Order
	err, supplyOrder = GetOrderByID(orderID)
	if err != nil {
		return
	}
	// 查询供应链id
	var supplyID uint
	err, supplyID = GetSupplyID()
	// 判断订单供应链id && 订单item长度等于1
	if supplyID != supplyOrder.GatherSupplyID || len(supplyOrder.OrderItems) != 1 {
		return
	}
	var record service.OrderRecord
	record.UserID = supplyOrder.UserID
	record.OrderAmount = supplyOrder.Amount
	record.OrderPayName = supplyOrder.PayType
	record.ProductID = supplyOrder.OrderItems[0].ProductID
	if messageType == mq.Created {
		err = service.OrderCreate(supplyOrder.ID, record)
	}
	if messageType == mq.Paid {
		err = service.OrderPaid(supplyOrder.ID, record)
	}
	if messageType == mq.Refunded {
		err = service.OrderRefunded(supplyOrder.ID)
	}
	return
}

// 记录昨日的总数以及饼图
func ProductOldCount() {
	var oldProductCount int64
	var err error
	source.DB().Model(&productModel.ProductModel{}).Joins("left join skus on skus.product_id = products.id").Where("products.is_display = 1").Count(&oldProductCount)

	if err != nil {
		log.Log().Error("查询商品上架数量失败", zap.Any("err", err))
		oldProductCount = 0
		err = nil
	}
	productStatisticsData := ProductStatisticsData(supplyChainRequest.RankingRequest{Type: 1})
	productStatisticsDataJosn, err := json.Marshal(productStatisticsData)
	_, settingData := setting.GetProductSetting()
	settingData.Key = setting.KeySetting
	settingData.Value.OldProductCount = oldProductCount
	if err == nil {
		settingData.Value.ProductStatisticsData = string(productStatisticsDataJosn)
	}
	err = setting.SaveProductSetting(settingData)

	if err != nil {
		log.Log().Error("保存商品上架数量失败", zap.Any("err", err), zap.Any("settingData", settingData), zap.Any("settingData", settingData))
		err = nil
	}
}

// 订单统计结构体
type OrderReportOrderTotal struct {
	Count      int64 `json:"count" form:"count" gorm:"column:count;"`                      //总数
	GoodsCount int64 `json:"goods_counts" form:"goods_counts" gorm:"column:goods_counts;"` //商品总数
	UserCount  int64 `json:"user_count" form:"user_count" gorm:"column:user_count;"`       //会员总数

	Total     int64  `json:"total" form:"total" gorm:"column:total;"` //总金额
	Status    int    `json:"status"`
	UserID    uint   `json:"user_id" form:"user_id" gorm:"column:user_id;comment:用户id;index;"`          // 用户id  根据用户统计使用
	Province  string `json:"province" `                                                                   // 省  区域统计使用
	City      string `json:"city"`                                                                        // 市  区域统计使用
	ProductId uint   `json:"product_id" form:"product_id" gorm:"column:product_id;comment:商品id;index;"` // 商品id  根据商品统计使用

	Category1ID uint `json:"category1_id" form:"category1_id" gorm:"index"` // 分类统计使用
	Category2ID uint `json:"category2_id" form:"category2_id" gorm:"index"` // 分类统计使用
	Category3ID uint `json:"category3_id" form:"category3_id" gorm:"index"` //分类统计使用

	SupplierID     uint `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商id;index;"`                // 供应商id
	GatherSupplyID uint `json:"gather_supply_id" form:"gather_supply_id" gorm:"column:gather_supply_id;comment:供应链id;index;"` // 供应链id
}

// 会员订单总统计
type UserOrderReportOrderTotal struct {
	Count  int64 `json:"count" form:"count" gorm:"column:count;"`                            //总数
	Total  int64 `json:"total" form:"total" gorm:"column:total;"`                            //总金额
	UserID uint  `json:"user_id" form:"user_id" gorm:"column:user_id;comment:用户id;index;"` // 用户id
}

type Product struct {
	ID uint `json:"id" form:"id" gorm:"primarykey"`

	Title string `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(255);size:255;index;"` // 标题

	ImageUrl string `json:"image_url" gorm:"column:image_url;comment:图片url;"` // 图片url

	Supplier     productResponse.Supplier `json:"supplier,omitempty" form:"supplier"` // 供应商
	GatherSupply GatherSupply             `json:"gather_supply" form:"supplier"`      // 供应链

	SupplierID     uint           `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商id;index;"`                // 供应商id
	GatherSupplyID uint           `json:"gather_supply_id" form:"gather_supply_id" gorm:"column:gather_supply_id;comment:供应链id;index;"` // 供应链id
	Category1ID    uint           `json:"category1_id" form:"category1_id" gorm:"index"`                                                   // 一级分类
	Category2ID    uint           `json:"category2_id" form:"category2_id" gorm:"index"`                                                   // 二级分类
	Category3ID    uint           `json:"category3_id" form:"category3_id" gorm:"index"`
	Category1      other.Category `json:"category_1" form:"category_1"`
	Category2      other.Category `json:"category_2" form:"category_2"`
	Category3      other.Category `json:"category_3" form:"category_3"`
}

// 订单报表
// 已支付订单：待发货+待收货+已完成；

//所有排序按已支付订单总额排序；
//
//订单均价=已支付订单总额/已支付订单总量；

//商品均价=已支付订单总额/订单商品总数；

//订单会员总数：统计结果中购买的会员数量，只计算已支付订单；

// 订单商品总数：统计订单中所有的商品数量，只计算已支付订单；
/**
  isUser  是否是用户,用户不需要会员排行,并且来源，店铺供应链必须显示前端的名字
*/
func GetOrderReportForms(info request.OrderAdminSearch, isUser int) (err error, data response.OrderReportFormsResponse) {

	data.OrderDataSummary, data.RequestTitle = GetOrderReportFormsOrderDataSummary(info, isUser)

	if isUser == 0 {
		data.UserOrderData, _ = GetOrderReportFormsUserOrder(info, isUser)
	}

	data.ReginOrderData, _ = GetOrderReportFormsReginOrder(info, isUser)

	data.ProductOrderData, _ = GetOrderReportFormsProductOrder(info, isUser)

	data.CategoryOrderData, _ = GetOrderReportFormsCategoryOrder(info, isUser)

	data.ShopOrderData, _ = GetOrderReportFormsShopOrder(info, isUser)

	return
}

// 表单导出需要的参数结构体
type OrderReportFormsExport struct {
	Data response.OrderReportFormsResponse `json:"data"`
	F    *excelize.File                    `json:"f"`
}

// 订单报表  导出表格
func GetOrderReportFormsExport(info request.OrderAdminSearch, isUser int) (err error, link string) {

	err, data := GetOrderReportForms(info, isUser)
	if err != nil {
		return
	}

	var orderReportFormsExport OrderReportFormsExport
	orderReportFormsExport.Data = data
	orderReportFormsExport.F = excelize.NewFile()

	orderReportFormsExport.OrderReportFormsExportOrderDataSummary() //订单数据汇总

	orderReportFormsExport.OrderReportFormsExportOrderDataUser() //会员订单数据汇总

	orderReportFormsExport.OrderReportFormsExportOrderDataRegin() //区域订单汇总

	orderReportFormsExport.OrderReportFormsExportOrderDataProduct() //商品订单汇总

	orderReportFormsExport.OrderReportFormsExportOrderDataCategory() //类目订单汇总

	orderReportFormsExport.OrderReportFormsExportOrderDataShop() //店铺订单汇总

	path := config.Config().Local.Path
	// 保存 Excel 文件
	filePath := path + "/订单报表.xlsx"
	// 检查文件是否存在
	if _, err = os.Stat(filePath); !os.IsNotExist(err) {
		// 文件存在，删除文件
		err = os.Remove(filePath)
		if err != nil {
			err = errors.New("移除旧的表格出错" + err.Error())
			return
		}
		fmt.Println("文件已删除:", filePath)
	}

	if err = orderReportFormsExport.F.SaveAs(filePath); err != nil {
		err = errors.New("保存文件时出错:" + err.Error())
	}
	return nil, filePath

}

// 订单数据汇总 -- 表格
func (oe OrderReportFormsExport) OrderReportFormsExportOrderDataSummary() {

	sheetName := "订单数据汇总"
	oe.F.NewSheet(sheetName)
	oe.F.DeleteSheet("Sheet1") // 删除默认的工作表
	var orderDataSummary = make(map[string]interface{})
	// 写入订单数据
	orderDataSummary = map[string]interface{}{
		"A1": "订单报表",
		"A2": "订单统计范围", "B1": oe.Data.RequestTitle,
		"A3": "订单总数（单）", "B3": oe.Data.OrderDataSummary.OrderCount,
		"A4": "订单总额（元）", "B4": utils.Fen2Yuan(uint(oe.Data.OrderDataSummary.OrderSum)),
		"A5": "待支付订单总数（单）", "B5": oe.Data.OrderDataSummary.WaitPayOrderCount,
		"A6": "待支付订单总额（元）", "B6": utils.Fen2Yuan(uint(oe.Data.OrderDataSummary.WaitPayOrderSum)),
		"A7": "待发货订单总数（单）", "B7": oe.Data.OrderDataSummary.WaitSendOrderCount,
		"A8": "待发货订单总额（元）", "B8": utils.Fen2Yuan(uint(oe.Data.OrderDataSummary.WaitSendOrderSum)),
		"A9": "待收货订单总数（单）", "B9": oe.Data.OrderDataSummary.WaitReceiveOrderCount,
		"A10": "待收货订单总额（元）", "B10": utils.Fen2Yuan(uint(oe.Data.OrderDataSummary.WaitReceiveOrderSum)),
		"A11": "已完成订单总数（单）", "B11": oe.Data.OrderDataSummary.CompletedOrderCount,
		"A12": "已完成订单总额（元）", "B12": utils.Fen2Yuan(uint(oe.Data.OrderDataSummary.CompletedOrderSum)),
		"A13": "已关闭订单总数（单）", "B13": oe.Data.OrderDataSummary.ClosedOrderCount,
		"A14": "已关闭订单总额（元）", "B14": utils.Fen2Yuan(uint(oe.Data.OrderDataSummary.ClosedOrderSum)),
	}

	oe.F.SetColWidth(sheetName, "A", "B", 30)

	for cell, value := range orderDataSummary {
		oe.F.SetCellValue(sheetName, cell, value)
	}
}

// 会员订单数据汇总--表格
func (oe OrderReportFormsExport) OrderReportFormsExportOrderDataUser() {

	sheet2 := "会员订单数据汇总"
	oe.F.NewSheet(sheet2)

	// 写入会员订单数据汇总表头
	headers := map[string]string{
		"A1": "订单报表",
		"A2": "订单统计范围", "B2": oe.Data.RequestTitle,
		"A3": "会员订单数据汇总", "B3": "（前十）",
		"A4": "排序", "B4": "会员昵称", "C4": "会员手机号", "D4": "待支付订单总量（单）",
		"E4": "待支付订单总额（元）", "F4": "已支付订单总量（单）", "G4": "已支付订单总额（元）",
		"H4": "已支付订单均价（元）", "I4": "售后订单总量（单）", "J4": "售后订单总额（元）",
	}

	for cell, header := range headers {
		oe.F.SetCellValue(sheet2, cell, header)
	}
	var row = 4
	for i, item := range oe.Data.UserOrderData {
		row++
		i++
		// 写入会员订单数据汇总表头
		bodys := map[string]string{
			"A" + strconv.Itoa(row): strconv.Itoa(i),
			"B" + strconv.Itoa(row): item.NickName,
			"C" + strconv.Itoa(row): item.Username,
			"D" + strconv.Itoa(row): strconv.FormatInt(item.WaitPayOrderCount, 10),
			"E" + strconv.Itoa(row): utils.Fen2Yuan(uint(item.WaitPayOrderSum)),
			"F" + strconv.Itoa(row): strconv.FormatInt(item.PayOrderCount, 10),
			"G" + strconv.Itoa(row): utils.Fen2Yuan(uint(item.PayOrderSum)),
			"H" + strconv.Itoa(row): utils.Fen2Yuan(uint(item.PayOrderAmount)),
			"I" + strconv.Itoa(row): strconv.FormatInt(item.RefundOrderCount, 10),
			"J" + strconv.Itoa(row): utils.Fen2Yuan(uint(item.RefundOrderSum)),
		}
		for cell, body := range bodys {
			oe.F.SetCellValue(sheet2, cell, body)
		}
	}
	oe.F.SetColWidth(sheet2, "B", "J", 30)
}

// 区域订单数据汇总--表格
func (oe OrderReportFormsExport) OrderReportFormsExportOrderDataRegin() {
	sheet3 := "区域订单数据汇总"
	oe.F.NewSheet(sheet3)

	// 写入会员订单数据汇总表头
	headers := map[string]string{
		"A1": "订单报表",
		"A2": "订单统计范围", "B2": oe.Data.RequestTitle,
		"A3": "区域订单数据汇总", "B3": "（前十）",
		"A4": "排序", "B4": "省", "C4": "市", "D4": "订单会员总数（人）",
		"E4": "订单商品总数（件）", "F4": "已支付订单总量（单）", "G4": "已支付订单总额（元）",
		"H4": "已支付订单均价（元）", "I4": "已支付商品均价（元）", "J4": "售后订单总量（单）", "K4": "售后订单总额（元）",
	}

	for cell, header := range headers {
		oe.F.SetCellValue(sheet3, cell, header)
	}
	var row = 4
	for i, item := range oe.Data.ReginOrderData {
		row++
		i++
		bodys := map[string]string{
			"A" + strconv.Itoa(row): strconv.Itoa(i),
			"B" + strconv.Itoa(row): item.Province,
			"C" + strconv.Itoa(row): item.City,
			"D" + strconv.Itoa(row): strconv.FormatInt(item.PayOrderUserCount, 10),
			"E" + strconv.Itoa(row): strconv.FormatInt(item.PayOrderProductCount, 10),
			"F" + strconv.Itoa(row): strconv.FormatInt(item.PayOrderCount, 10),
			"G" + strconv.Itoa(row): utils.Fen2Yuan(uint(item.PayOrderSum)),
			"H" + strconv.Itoa(row): utils.Fen2Yuan(uint(item.PayOrderAveragePrice)),
			"I" + strconv.Itoa(row): utils.Fen2Yuan(uint(item.PayOrderAverageProductPrice)),
			"J" + strconv.Itoa(row): strconv.FormatInt(item.RefundOrderCount, 10),
			"K" + strconv.Itoa(row): utils.Fen2Yuan(uint(item.RefundOrderSum)),
		}
		for cell, body := range bodys {
			oe.F.SetCellValue(sheet3, cell, body)
		}
	}
	oe.F.SetColWidth(sheet3, "B", "K", 30)
}

// 区域订单数据汇总--表格
func (oe OrderReportFormsExport) OrderReportFormsExportOrderDataProduct() {
	sheet4 := "商品订单数据汇总"
	oe.F.NewSheet(sheet4)

	// 写入会员订单数据汇总表头
	headers := map[string]string{
		"A1": "订单报表",
		"A2": "订单统计范围", "B2": oe.Data.RequestTitle,
		"A3": "商品订单数据汇总", "B3": "（前十）",
		"A4": "排序", "B4": "商品名称", "C4": "来源", "D4": "类目",
		"E4": "已支付订单总量（单）", "F4": "已支付订单总额（元）", "G4": "售后订单总量（单）",
		"H4": "售后订单总额（元）",
	}

	for cell, header := range headers {
		oe.F.SetCellValue(sheet4, cell, header)
	}
	var row = 4
	for i, item := range oe.Data.ProductOrderData {
		row++
		i++
		bodys := map[string]string{
			"A" + strconv.Itoa(row): strconv.Itoa(i),
			"B" + strconv.Itoa(row): item.Title,
			"C" + strconv.Itoa(row): item.SourceName,
			"D" + strconv.Itoa(row): item.Category1Name + "-" + item.Category2Name + "-" + item.Category3Name,
			"E" + strconv.Itoa(row): strconv.FormatInt(item.PayOrderCount, 10),
			"F" + strconv.Itoa(row): utils.Fen2Yuan(uint(item.PayOrderSum)),
			"G" + strconv.Itoa(row): strconv.FormatInt(item.RefundOrderCount, 10),
			"H" + strconv.Itoa(row): utils.Fen2Yuan(uint(item.RefundOrderSum)),
		}
		for cell, body := range bodys {
			oe.F.SetCellValue(sheet4, cell, body)
		}
	}
	oe.F.SetColWidth(sheet4, "B", "H", 30)
}

// 类目订单数据汇总--表格
func (oe OrderReportFormsExport) OrderReportFormsExportOrderDataCategory() {
	sheet5 := "类目订单数据汇总"
	oe.F.NewSheet(sheet5)

	// 写入会员订单数据汇总表头
	headers := map[string]string{
		"A1": "订单报表",
		"A2": "订单统计范围", "B2": oe.Data.RequestTitle,
		"A3": "类目订单数据汇总", "B3": "（前十）",
		"A4": "排序", "B4": "类目", "C4": "订单会员总数（人）",
		"D4": "订单商品总数（件）", "E4": "已支付订单总量（单）", "F4": "已支付订单总额（元）",
		"G4": "已支付订单均价（元）", "H4": "已支付商品均价（元）", "I4": "售后订单总量（单）", "J4": "售后订单总额（元）",
	}

	for cell, header := range headers {
		oe.F.SetCellValue(sheet5, cell, header)
	}
	var row = 4
	for i, item := range oe.Data.CategoryOrderData {
		row++
		i++
		bodys := map[string]string{
			"A" + strconv.Itoa(row): strconv.Itoa(i),
			"B" + strconv.Itoa(row): item.Category1Name + "-" + item.Category2Name + "-" + item.Category3Name,
			"C" + strconv.Itoa(row): strconv.FormatInt(item.PayOrderUserCount, 10),
			"D" + strconv.Itoa(row): strconv.FormatInt(item.PayOrderProductCount, 10),
			"E" + strconv.Itoa(row): strconv.FormatInt(item.PayOrderCount, 10),
			"F" + strconv.Itoa(row): utils.Fen2Yuan(uint(item.PayOrderSum)),
			"G" + strconv.Itoa(row): utils.Fen2Yuan(uint(item.PayOrderAveragePrice)),
			"H" + strconv.Itoa(row): utils.Fen2Yuan(uint(item.PayOrderAverageProductPrice)),
			"I" + strconv.Itoa(row): strconv.FormatInt(item.RefundOrderCount, 10),
			"J" + strconv.Itoa(row): utils.Fen2Yuan(uint(item.RefundOrderSum)),
		}
		for cell, body := range bodys {
			oe.F.SetCellValue(sheet5, cell, body)
		}
	}
	oe.F.SetColWidth(sheet5, "B", "J", 30)
}

// 类目订单数据汇总--表格
func (oe OrderReportFormsExport) OrderReportFormsExportOrderDataShop() {
	sheet6 := "店铺订单数据汇总"
	oe.F.NewSheet(sheet6)

	// 写入会员订单数据汇总表头
	headers := map[string]string{
		"A1": "订单报表",
		"A2": "订单统计范围", "B2": oe.Data.RequestTitle,
		"A3": "店铺订单数据汇总", "B3": "（前十）",
		"A4": "排序", "B4": "店铺名称", "C4": "订单会员总数（人）",
		"D4": "订单商品总数（件）", "E4": "已支付订单总量（单）", "F4": "已支付订单总额（元）",
		"G4": "已支付订单均价（元）", "H4": "已支付商品均价（元）", "I4": "售后订单总量（单）", "J4": "售后订单总额（元）", "K4": "待支付订单总量（单）", "L4": "待支付订单总额（元）",
	}

	for cell, header := range headers {
		oe.F.SetCellValue(sheet6, cell, header)
	}
	var row = 4
	for i, item := range oe.Data.ShopOrderData {
		row++
		i++
		bodys := map[string]string{
			"A" + strconv.Itoa(row): strconv.Itoa(i),
			"B" + strconv.Itoa(row): item.ShopName,
			"C" + strconv.Itoa(row): strconv.FormatInt(item.PayOrderUserCount, 10),
			"D" + strconv.Itoa(row): strconv.FormatInt(item.PayOrderProductCount, 10),
			"E" + strconv.Itoa(row): strconv.FormatInt(item.PayOrderCount, 10),
			"F" + strconv.Itoa(row): utils.Fen2Yuan(uint(item.PayOrderSum)),
			"G" + strconv.Itoa(row): utils.Fen2Yuan(uint(item.PayOrderAveragePrice)),
			"H" + strconv.Itoa(row): utils.Fen2Yuan(uint(item.PayOrderAverageProductPrice)),
			"I" + strconv.Itoa(row): strconv.FormatInt(item.RefundOrderCount, 10),
			"J" + strconv.Itoa(row): utils.Fen2Yuan(uint(item.RefundOrderSum)),
			"K" + strconv.Itoa(row): strconv.FormatInt(item.WaitPayOrderCount, 10),
			"L" + strconv.Itoa(row): utils.Fen2Yuan(uint(item.WaitPayOrderSum)),
		}
		for cell, body := range bodys {
			oe.F.SetCellValue(sheet6, cell, body)
		}
	}
	oe.F.SetColWidth(sheet6, "B", "J", 30)
}

// 订单数据汇总
func GetOrderReportFormsOrderDataSummary(info request.OrderAdminSearch, isUser int) (orderDataSummary response.OrderDataSummary, requestTitle string) {
	var err error
	//统计订单金额 待整理结构体   WaitPay WaitSend WaitReceive Completed Closed
	db := source.DB().Model(&response.Order{})
	err, db, requestTitle = OrderReportFormsDbRequest(db, info, isUser)
	if err != nil {
		return
	}
	var orderReportOrderTotal []OrderReportOrderTotal
	db.Select("count(id) as count,sum(amount) as total,status").Group("status").Find(&orderReportOrderTotal)
	for _, item := range orderReportOrderTotal {
		orderDataSummary.OrderCount += item.Count
		orderDataSummary.OrderSum += item.Total
		switch item.Status {
		case 0:
			orderDataSummary.WaitPayOrderCount = item.Count
			orderDataSummary.WaitPayOrderSum = item.Total
			break
		case 1:
			orderDataSummary.WaitSendOrderCount = item.Count
			orderDataSummary.WaitSendOrderSum = item.Total
			break
		case 2:
			orderDataSummary.WaitReceiveOrderCount = item.Count
			orderDataSummary.WaitReceiveOrderSum = item.Total
			break
		case 3:
			orderDataSummary.CompletedOrderCount = item.Count
			orderDataSummary.CompletedOrderSum = item.Total
			break
		case -1:
			orderDataSummary.ClosedOrderCount = item.Count
			orderDataSummary.ClosedOrderSum = item.Total
			break
		}
	}
	return
}

// 会员订单数据汇总（前十）
func GetOrderReportFormsUserOrder(info request.OrderAdminSearch, isUser int) (userOrderDatas []response.UserOrderData, requestTitle string) {
	var err error
	//会员订单数据总计 -- 先统计出前十的 之后通过前十的id去查询订单表拆分出每个订单状态有多少订单多少金额
	userOrderDb := source.DB().Model(&response.Order{})
	err, userOrderDb, _ = OrderReportFormsDbRequest(userOrderDb, info, isUser)
	if err != nil {
		return
	}
	if info.Status == nil || *info.Status == 5 || *info.Status == 6 {
		userOrderDb.Where("orders.status in ?", []int{1, 2, 3})
	}
	var userOrderReportOrderTotal []UserOrderReportOrderTotal
	userOrderDb.Select("count(id) as count,user_id,sum(amount) as total").Group("user_id").Order("total desc,count desc").Limit(10).Find(&userOrderReportOrderTotal)
	if len(userOrderReportOrderTotal) > 0 {
		var userIds []uint
		for _, item := range userOrderReportOrderTotal {
			userIds = append(userIds, item.UserID)
		}

		var userOrderReportOrder []OrderReportOrderTotal
		_, userOrderReportOrderDb, _ := OrderReportFormsDbRequest(source.DB().Model(&response.Order{}), info, isUser)
		userOrderReportOrderDb.Select("count(id) as count,user_id,sum(amount) as total,status").Group("user_id,status").Where("user_id in ?", userIds).Find(&userOrderReportOrder)

		var userDatas []response.User
		//用户信息
		source.DB().Model(&response.User{}).Where("id in ?", userIds).Find(&userDatas)
		//售价金额售后单数
		var afterSalesOrderReportOrder []OrderReportOrderTotal
		_, afterSalesOrderReportOrderDb, _ := OrderReportFormsDbRequest(source.DB().Model(&model.AfterSales{}).Joins("left join orders on orders.id = after_sales.order_id"), info, isUser)

		afterSalesOrderReportOrderDb.Select("count(after_sales.id) as count,after_sales.user_id,sum(after_sales.amount) as total").Group("after_sales.user_id").Where("after_sales.user_id in ?", userIds).Where("after_sales.status = 4").Find(&afterSalesOrderReportOrder)

		for _, item := range userOrderReportOrderTotal {
			var userOrderData response.UserOrderData

			for _, userItem := range userDatas {
				if userItem.ID == item.UserID {
					userOrderData.Username = userItem.Username
					userOrderData.NickName = userItem.NickName
				}
			}
			//用户订单数据
			for _, userOrderReportOrderTotalItem := range userOrderReportOrder {
				if userOrderReportOrderTotalItem.UserID == item.UserID {
					//已支付订单 包含待发货,待收货 已完成
					switch userOrderReportOrderTotalItem.Status {
					case 0:
						userOrderData.WaitPayOrderCount += userOrderReportOrderTotalItem.Count
						userOrderData.WaitPayOrderSum += userOrderReportOrderTotalItem.Total
						break
					case 1:
						userOrderData.PayOrderCount += userOrderReportOrderTotalItem.Count
						userOrderData.PayOrderSum += userOrderReportOrderTotalItem.Total
						break
					case 2:
						userOrderData.PayOrderCount += userOrderReportOrderTotalItem.Count
						userOrderData.PayOrderSum += userOrderReportOrderTotalItem.Total
						break
					case 3:
						userOrderData.PayOrderCount += userOrderReportOrderTotalItem.Count
						userOrderData.PayOrderSum += userOrderReportOrderTotalItem.Total
						break
					case -1:
						break
					}
				}
			}
			//用户售后数据
			for _, afterSalesOrderReportOrderTotalItem := range afterSalesOrderReportOrder {
				if afterSalesOrderReportOrderTotalItem.UserID == item.UserID {
					userOrderData.RefundOrderSum = afterSalesOrderReportOrderTotalItem.Total
					userOrderData.RefundOrderCount = afterSalesOrderReportOrderTotalItem.Count
				}
			}
			if userOrderData.PayOrderCount > 0 {
				userOrderData.PayOrderAmount = userOrderData.PayOrderSum / userOrderData.PayOrderCount
			}
			userOrderDatas = append(userOrderDatas, userOrderData)
		}
	}
	return
}

// 区域订单数据汇总（前十）
func GetOrderReportFormsReginOrder(info request.OrderAdminSearch, isUser int) (reginOrderDatas []response.ReginOrderData, requestTitle string) {
	var err error
	reginOrderDb := source.DB().Model(&response.Order{})
	err, reginOrderDb, _ = OrderReportFormsDbRequest(reginOrderDb, info, isUser)
	if err != nil {
		return
	}
	if info.Status == nil || *info.Status == 5 || *info.Status == 6 {
		reginOrderDb.Where("orders.status in ?", []int{1, 2, 3})
	}
	var reginOrderReportOrderTotal []OrderReportOrderTotal
	//先统计总数前十的，之后在拆分各个状态多少个 多少总额
	reginOrderDb.Joins("left join shipping_addresses on shipping_addresses.id = orders.shipping_address_id").Select("count(orders.id) as count,shipping_addresses.province,shipping_addresses.city,sum(amount) as total,count(DISTINCT orders.user_id) as user_count").Group("shipping_addresses.province,shipping_addresses.city").Order("total desc,count desc").Limit(10).Find(&reginOrderReportOrderTotal)
	if len(reginOrderReportOrderTotal) > 0 {
		var province []string
		var city []string

		for _, item := range reginOrderReportOrderTotal {
			province = append(province, item.Province)
			city = append(city, item.City)
		}
		//获取前十的地区每个状态订单都有多少个
		var reginOrderReportOrderStatusTotal []OrderReportOrderTotal
		_, reginOrderStatusDb, _ := OrderReportFormsDbRequest(source.DB().Model(&response.Order{}), info, isUser)
		reginOrderStatusDb.Joins("left join shipping_addresses on shipping_addresses.id = orders.shipping_address_id").Where("shipping_addresses.province in ?", province).Where("shipping_addresses.city in ?", city)
		reginOrderStatusDb.Select("count(orders.id) as count,shipping_addresses.province,shipping_addresses.city,sum(amount) as total,orders.status,count(orders.goods_count) as goods_counts").Group("shipping_addresses.province,shipping_addresses.city,orders.status").Find(&reginOrderReportOrderStatusTotal)

		//售价金额售后单数
		var afterSalesOrderReportOrder []OrderReportOrderTotal
		_, afterSalesOrderReportOrderDb, _ := OrderReportFormsDbRequest(source.DB().Model(&model.AfterSales{}).Joins("left join orders on orders.id = after_sales.order_id"), info, isUser)

		afterSalesOrderReportOrderDb.Select("count(after_sales.id) as count,sum(after_sales.amount) as total,shipping_addresses.province,shipping_addresses.city").Where("after_sales.status = 4").Where("shipping_addresses.province in ?", province).Where("shipping_addresses.city in ?", city)
		afterSalesOrderReportOrderDb.Joins("left join shipping_addresses on shipping_addresses.id = orders.shipping_address_id").Group("shipping_addresses.province,shipping_addresses.city").Find(&afterSalesOrderReportOrder)

		for _, item := range reginOrderReportOrderTotal {
			var reginOrderData response.ReginOrderData
			reginOrderData.Province = item.Province
			reginOrderData.City = item.City
			reginOrderData.PayOrderUserCount = item.UserCount
			for _, reginOrderReportOrderStatusTotalItem := range reginOrderReportOrderStatusTotal {
				if item.Province == reginOrderReportOrderStatusTotalItem.Province && item.City == reginOrderReportOrderStatusTotalItem.City {

					switch reginOrderReportOrderStatusTotalItem.Status {
					case 0:
						break
					case 1:
						reginOrderData.PayOrderCount += reginOrderReportOrderStatusTotalItem.Count
						reginOrderData.PayOrderSum += reginOrderReportOrderStatusTotalItem.Total
						reginOrderData.PayOrderProductCount += reginOrderReportOrderStatusTotalItem.GoodsCount
						break
					case 2:
						reginOrderData.PayOrderCount += reginOrderReportOrderStatusTotalItem.Count
						reginOrderData.PayOrderSum += reginOrderReportOrderStatusTotalItem.Total
						reginOrderData.PayOrderProductCount += reginOrderReportOrderStatusTotalItem.GoodsCount
						break
					case 3:
						reginOrderData.PayOrderCount += reginOrderReportOrderStatusTotalItem.Count
						reginOrderData.PayOrderSum += reginOrderReportOrderStatusTotalItem.Total
						reginOrderData.PayOrderProductCount += reginOrderReportOrderStatusTotalItem.GoodsCount
						break
					case -1:
						break
					}
				}
			}
			//售后数据
			for _, afterSalesOrderReportOrderTotalItem := range afterSalesOrderReportOrder {
				if item.Province == afterSalesOrderReportOrderTotalItem.Province && item.City == afterSalesOrderReportOrderTotalItem.City {
					reginOrderData.RefundOrderSum = afterSalesOrderReportOrderTotalItem.Total
					reginOrderData.RefundOrderCount = afterSalesOrderReportOrderTotalItem.Count
				}
			}
			if reginOrderData.PayOrderCount > 0 {
				reginOrderData.PayOrderAveragePrice = reginOrderData.PayOrderSum / reginOrderData.PayOrderCount //已支付订单均价
			}
			if reginOrderData.PayOrderProductCount > 0 {
				reginOrderData.PayOrderAverageProductPrice = reginOrderData.PayOrderSum / reginOrderData.PayOrderProductCount //已支付商品均价 （需求订单总额/商品总个数）
			}
			reginOrderDatas = append(reginOrderDatas, reginOrderData)
		}
	}
	return
}

// 商品订单单数据汇总（前十）
func GetOrderReportFormsProductOrder(info request.OrderAdminSearch, isUser int) (productOrderDatas []response.ProductOrderData, requestTitle string) {
	var err error
	productOrderDb := source.DB().Model(&response.Order{})
	if err != nil {
		return
	}
	err, productOrderDb, _ = OrderReportFormsDbRequest(productOrderDb, info, isUser)
	var productOrderReportOrderTotal []OrderReportOrderTotal
	if info.Status == nil || *info.Status == 5 || *info.Status == 6 {
		productOrderDb.Where("orders.status in ?", []int{1, 2, 3})
	}
	productOrderDb.Select("count(orders.id) as count,sum(orders.amount) as total,order_items.product_id").Joins("left join order_items on order_items.order_id = orders.id").Group("order_items.product_id").Order("total desc,count desc").Limit(10).Find(&productOrderReportOrderTotal)
	if len(productOrderReportOrderTotal) > 0 {
		var productIds []uint
		for _, item := range productOrderReportOrderTotal {
			productIds = append(productIds, item.ProductId)
		}

		//获取前十的商品每个状态订单都有多少个
		var productOrderReportOrderStatusTotal []OrderReportOrderTotal
		_, reginOrderStatusDb, _ := OrderReportFormsDbRequest(source.DB().Model(&response.Order{}), info, isUser)

		reginOrderStatusDb.Joins("left join order_items on order_items.order_id = orders.id").Where("order_items.product_id in ?", productIds)
		reginOrderStatusDb.Select("count(orders.id) as count,order_items.product_id,sum(orders.amount) as total,orders.status").Group("order_items.product_id,orders.status").Find(&productOrderReportOrderStatusTotal)

		//售价金额售后单数
		var afterSalesOrderReportOrder []OrderReportOrderTotal
		_, afterSalesOrderReportOrderDb, _ := OrderReportFormsDbRequest(source.DB().Model(&model.AfterSales{}).Joins("left join orders on orders.id = after_sales.order_id"), info, isUser)

		afterSalesOrderReportOrderDb.Select("count(after_sales.id) as count,after_sales.product_id,sum(after_sales.amount) as total").Where("after_sales.status = 4").Where("after_sales.product_id in ?", productIds)
		afterSalesOrderReportOrderDb.Group("after_sales.product_id").Find(&afterSalesOrderReportOrder)

		var productDatas []Product
		source.DB().Where("id in ?", productIds).Preload("Category1").Preload("Category2").Preload("Category3").Preload("Supplier").Preload("GatherSupply").Find(&productDatas)

		for _, item := range productOrderReportOrderTotal {
			var productOrderData response.ProductOrderData
			productOrderData.Id = item.ProductId
			for _, productItem := range productDatas {
				if item.ProductId == productItem.ID {
					productOrderData.Title = productItem.Title
					productOrderData.ImageUrl = productItem.ImageUrl
					if productItem.SupplierID == 0 && productItem.GatherSupplyID == 0 {
						productOrderData.SourceName = "平台自营"
					}
					if productItem.SupplierID > 0 {
						productOrderData.SourceName = productItem.Supplier.Name
					}
					if productItem.GatherSupplyID > 0 {
						productOrderData.SourceName = productItem.GatherSupply.Name
					}
					productOrderData.Category1Name = productItem.Category1.Name
					productOrderData.Category2Name = productItem.Category2.Name
					productOrderData.Category3Name = productItem.Category3.Name
				}
			}

			for _, productOrderReportOrderStatusTotalItem := range productOrderReportOrderStatusTotal {
				if item.ProductId == productOrderReportOrderStatusTotalItem.ProductId {

					switch productOrderReportOrderStatusTotalItem.Status {
					case 0:
						break
					case 1:
						productOrderData.PayOrderCount += productOrderReportOrderStatusTotalItem.Count
						productOrderData.PayOrderSum += productOrderReportOrderStatusTotalItem.Total
						break
					case 2:
						productOrderData.PayOrderCount += productOrderReportOrderStatusTotalItem.Count
						productOrderData.PayOrderSum += productOrderReportOrderStatusTotalItem.Total
						break
					case 3:
						productOrderData.PayOrderCount += productOrderReportOrderStatusTotalItem.Count
						productOrderData.PayOrderSum += productOrderReportOrderStatusTotalItem.Total
						break
					case -1:
						//productOrderData.PayOrderCount += productOrderReportOrderStatusTotalItem.Count
						//productOrderData.PayOrderSum += productOrderReportOrderStatusTotalItem.Total
						break
					}
				}
			}
			for _, afterSalesOrderReportOrderTotalItem := range afterSalesOrderReportOrder {
				if item.ProductId == afterSalesOrderReportOrderTotalItem.ProductId {
					productOrderData.RefundOrderSum = afterSalesOrderReportOrderTotalItem.Total
					productOrderData.RefundOrderCount = afterSalesOrderReportOrderTotalItem.Count
				}
			}
			productOrderDatas = append(productOrderDatas, productOrderData)
		}
	}
	return
}

// 商品类目订单数据汇总（前十）
func GetOrderReportFormsCategoryOrder(info request.OrderAdminSearch, isUser int) (categoryOrderDatas []response.CategoryOrderData, requestTitle string) {
	var err error
	categoryOrderDb := source.DB().Model(&response.Order{})
	err, categoryOrderDb, _ = OrderReportFormsDbRequest(categoryOrderDb, info, isUser)
	if err != nil {
		return
	}
	var categoryOrderReportOrderTotal []OrderReportOrderTotal
	if info.Status == nil || *info.Status == 5 || *info.Status == 6 {
		categoryOrderDb.Where("orders.status in ?", []int{1, 2, 3})
	}
	//获取前十订单的类目
	categoryOrderDb.Where("products.category1_id > 0").Joins("left join order_items on order_items.order_id = orders.id").Joins("left join products on products.id = order_items.product_id").Select("count(orders.id) as count,sum(orders.amount) as total,products.category1_id,products.category2_id,products.category3_id,count(DISTINCT orders.user_id) as user_count").Group("products.category1_id,products.category2_id,products.category3_id")
	categoryOrderDb.Order("total desc,count desc").Limit(10).Find(&categoryOrderReportOrderTotal)
	if len(categoryOrderReportOrderTotal) > 0 {
		var category1IDs, category2IDs, category3IDs []uint

		for _, item := range categoryOrderReportOrderTotal {
			category1IDs = append(category1IDs, item.Category1ID)
			category2IDs = append(category2IDs, item.Category2ID)
			category3IDs = append(category3IDs, item.Category3ID)
		}

		//获取前十的商品每个状态订单都有多少个
		var categoryOrderReportOrderStatusTotal []OrderReportOrderTotal
		_, categoryOrderStatusDb, _ := OrderReportFormsDbRequest(source.DB().Model(&response.Order{}), info, isUser)

		categoryOrderStatusDb.Joins("left join order_items on order_items.order_id = orders.id").Joins("left join products on products.id = order_items.product_id").Where("products.category1_id in ? and products.category2_id in ? and products.category3_id in ?", category1IDs, category2IDs, category3IDs)
		categoryOrderStatusDb.Select("count(orders.id) as count,sum(orders.amount) as total,orders.status,products.category1_id,products.category2_id,products.category3_id,count(orders.goods_count) as goods_counts").Group("products.category1_id,products.category2_id,products.category3_id,orders.status").Find(&categoryOrderReportOrderStatusTotal)

		//售价金额售后单数
		var afterSalesOrderReportOrder []OrderReportOrderTotal
		_, afterSalesOrderReportOrderDb, _ := OrderReportFormsDbRequest(source.DB().Model(&model.AfterSales{}).Joins("left join orders on orders.id = after_sales.order_id"), info, isUser)
		afterSalesOrderReportOrderDb.Joins("left join products on products.id = after_sales.product_id").Where("products.category1_id in ? and products.category2_id in ? and products.category3_id in ?", category1IDs, category2IDs, category3IDs).Select("count(after_sales.id) as count,sum(after_sales.amount) as total,products.category1_id,products.category2_id,products.category3_id").Where("after_sales.status = 4")
		afterSalesOrderReportOrderDb.Group("products.category1_id,products.category2_id,products.category3_id").Find(&afterSalesOrderReportOrder)

		var categorys []categoryModel.Category
		source.DB().Where("id in ? or id in ? or id in ?", category1IDs, category2IDs, category3IDs).Find(&categorys)

		for _, item := range categoryOrderReportOrderTotal {
			var categoryOrderData response.CategoryOrderData
			for _, category := range categorys {
				if item.Category1ID == category.ID {
					categoryOrderData.Category1Name = category.Name
				}
				if item.Category2ID == category.ID {
					categoryOrderData.Category2Name = category.Name
				}
				if item.Category3ID == category.ID {
					categoryOrderData.Category3Name = category.Name
				}
			}
			categoryOrderData.PayOrderUserCount = item.UserCount

			for _, categoryOrderReportOrderStatusTotalItem := range categoryOrderReportOrderStatusTotal {
				if item.Category1ID == categoryOrderReportOrderStatusTotalItem.Category1ID && item.Category2ID == categoryOrderReportOrderStatusTotalItem.Category2ID && item.Category3ID == categoryOrderReportOrderStatusTotalItem.Category3ID {
					switch categoryOrderReportOrderStatusTotalItem.Status {
					case 0:
						break
					case 1:
						categoryOrderData.PayOrderCount += categoryOrderReportOrderStatusTotalItem.Count
						categoryOrderData.PayOrderSum += categoryOrderReportOrderStatusTotalItem.Total
						categoryOrderData.PayOrderProductCount += categoryOrderReportOrderStatusTotalItem.GoodsCount
						break
					case 2:
						categoryOrderData.PayOrderCount += categoryOrderReportOrderStatusTotalItem.Count
						categoryOrderData.PayOrderSum += categoryOrderReportOrderStatusTotalItem.Total
						categoryOrderData.PayOrderProductCount += categoryOrderReportOrderStatusTotalItem.GoodsCount

						break
					case 3:
						categoryOrderData.PayOrderCount += categoryOrderReportOrderStatusTotalItem.Count
						categoryOrderData.PayOrderSum += categoryOrderReportOrderStatusTotalItem.Total
						categoryOrderData.PayOrderProductCount += categoryOrderReportOrderStatusTotalItem.GoodsCount

						break
					case -1:
						break
					}
				}
			}
			for _, afterSalesOrderReportOrderTotalItem := range afterSalesOrderReportOrder {
				if item.Category1ID == afterSalesOrderReportOrderTotalItem.Category1ID && item.Category2ID == afterSalesOrderReportOrderTotalItem.Category2ID && item.Category3ID == afterSalesOrderReportOrderTotalItem.Category3ID {
					categoryOrderData.RefundOrderSum = afterSalesOrderReportOrderTotalItem.Total
					categoryOrderData.RefundOrderCount = afterSalesOrderReportOrderTotalItem.Count
				}
			}
			if categoryOrderData.PayOrderCount > 0 {
				categoryOrderData.PayOrderAveragePrice = categoryOrderData.PayOrderSum / categoryOrderData.PayOrderCount //已支付订单均价
			}
			if categoryOrderData.PayOrderProductCount > 0 {
				categoryOrderData.PayOrderAverageProductPrice = categoryOrderData.PayOrderSum / categoryOrderData.PayOrderProductCount //已支付商品均价 （需求订单总额/商品总个数）
			}
			categoryOrderDatas = append(categoryOrderDatas, categoryOrderData)
		}
	}
	return
}

type GettingData struct {
	BaseInfo BaseInfoData `json:"baseInfo"`
}

type BaseInfoData struct {
	StoreName string `json:"storeName"`
}

// 店铺订单数据汇总（前十）
func GetOrderReportFormsShopOrder(info request.OrderAdminSearch, isUser int) (shopOrderDatas []response.ShopOrderData, requestTitle string) {
	var err error
	shopOrderDb := source.DB().Model(&response.Order{})
	err, shopOrderDb, _ = OrderReportFormsDbRequest(shopOrderDb, info, isUser)
	if err != nil {
		return
	}
	var shopOrderReportOrderTotal []OrderReportOrderTotal

	if info.Status == nil || *info.Status == 5 || *info.Status == 6 {
		shopOrderDb.Where("orders.status in ?", []int{1, 2, 3})
	}

	//获取前十订单的店铺
	shopOrderDb.Select("count(orders.id) as count,sum(orders.amount) as total,count(DISTINCT orders.user_id) as user_count,orders.supplier_id,orders.gather_supply_id").Group("orders.gather_supply_id,orders.supplier_id")
	shopOrderDb.Order("total desc,count desc").Limit(10).Find(&shopOrderReportOrderTotal)
	if len(shopOrderReportOrderTotal) > 0 {
		var supplierIds, gatherSupplyId []uint
		var whereString string //拼接的条件

		for _, item := range shopOrderReportOrderTotal {
			if item.SupplierID > 0 {
				supplierIds = append(supplierIds, item.SupplierID)
			}
			if item.GatherSupplyID > 0 {
				gatherSupplyId = append(gatherSupplyId, item.GatherSupplyID)

			}
			if whereString == "" {
				whereString += "(orders.supplier_id = " + strconv.Itoa(int(item.SupplierID)) + " and orders.gather_supply_id = " + strconv.Itoa(int(item.GatherSupplyID)) + ")"
			} else {
				whereString += " or (orders.supplier_id = " + strconv.Itoa(int(item.SupplierID)) + " and orders.gather_supply_id = " + strconv.Itoa(int(item.GatherSupplyID)) + ")"

			}
		}

		//获取前十的商品每个状态订单都有多少个
		var shopOrderReportOrderStatusTotal []OrderReportOrderTotal

		_, shopOrderStatusDb, _ := OrderReportFormsDbRequest(source.DB().Model(&response.Order{}), info, isUser)

		shopOrderStatusDb.Where(whereString)
		shopOrderStatusDb.Select("count(orders.id) as count,sum(amount) as total,orders.status,orders.gather_supply_id,orders.supplier_id,count(orders.goods_count) as goods_counts").Group("orders.supplier_id,orders.gather_supply_id,orders.status").Find(&shopOrderReportOrderStatusTotal)

		//售价金额售后单数
		var afterSalesOrderReportOrder []OrderReportOrderTotal
		_, afterSalesOrderReportOrderDb, _ := OrderReportFormsDbRequest(source.DB().Model(&model.AfterSales{}).Joins("left join orders on orders.id = after_sales.order_id"), info, isUser)
		afterSalesOrderReportOrderDb.Where(whereString).Select("count(after_sales.id) as count,sum(after_sales.amount) as total,orders.supplier_id,orders.gather_supply_id").Where("after_sales.status = 4")
		afterSalesOrderReportOrderDb.Group("orders.supplier_id,orders.gather_supply_id").Find(&afterSalesOrderReportOrder)

		var suppliers []productResponse.Supplier
		var gatherSupply []GatherSupply

		source.DB().Where("id in ?", supplierIds).Find(&suppliers) //供应商名称
		if isUser == 0 {
			source.DB().Where("id in ?", gatherSupplyId).Find(&gatherSupply) //供应链名称
		}
		for _, item := range shopOrderReportOrderTotal {
			var shopOrderData response.ShopOrderData

			if item.SupplierID == 0 && item.GatherSupplyID == 0 {
				shopOrderData.ShopName = "平台自营"
			}
			if item.SupplierID > 0 {
				shopOrderData.ShopName = "供应商id" + strconv.Itoa(int(item.SupplierID))
				for _, supplierItem := range suppliers {
					if supplierItem.ID == item.SupplierID {
						//如果不是用户请求
						if isUser == 0 {
							shopOrderData.ShopName = supplierItem.Name
						} else {
							shopOrderData.ShopName = supplierItem.ShopName
						}

					}
				}
			}
			if item.GatherSupplyID > 0 {
				shopOrderData.ShopName = "供应链id：" + strconv.Itoa(int(item.GatherSupplyID))
				//如果不是用户请求
				if isUser == 0 {
					for _, gatherSupplyItem := range gatherSupply {
						if gatherSupplyItem.ID == item.GatherSupplyID {
							shopOrderData.ShopName = gatherSupplyItem.Name
						}
					}
				} else {
					_, settingValue := setting2.GetSetting("gatherSupply" + strconv.Itoa(int(item.GatherSupplyID)))
					var gettingData GettingData
					_ = json.Unmarshal([]byte(settingValue), &gettingData)
					shopOrderData.ShopName = gettingData.BaseInfo.StoreName
				}

			}
			shopOrderData.PayOrderUserCount = item.UserCount

			for _, categoryOrderReportOrderStatusTotalItem := range shopOrderReportOrderStatusTotal {
				if item.SupplierID == categoryOrderReportOrderStatusTotalItem.SupplierID && item.GatherSupplyID == categoryOrderReportOrderStatusTotalItem.GatherSupplyID {
					switch categoryOrderReportOrderStatusTotalItem.Status {
					case 0:
						shopOrderData.WaitPayOrderCount += categoryOrderReportOrderStatusTotalItem.Count
						shopOrderData.WaitPayOrderSum += categoryOrderReportOrderStatusTotalItem.Total
						break
					case 1:
						shopOrderData.PayOrderCount += categoryOrderReportOrderStatusTotalItem.Count
						shopOrderData.PayOrderSum += categoryOrderReportOrderStatusTotalItem.Total
						shopOrderData.PayOrderProductCount += categoryOrderReportOrderStatusTotalItem.GoodsCount
						break
					case 2:
						shopOrderData.PayOrderCount += categoryOrderReportOrderStatusTotalItem.Count
						shopOrderData.PayOrderSum += categoryOrderReportOrderStatusTotalItem.Total
						shopOrderData.PayOrderProductCount += categoryOrderReportOrderStatusTotalItem.GoodsCount

						break
					case 3:
						shopOrderData.PayOrderCount += categoryOrderReportOrderStatusTotalItem.Count
						shopOrderData.PayOrderSum += categoryOrderReportOrderStatusTotalItem.Total
						shopOrderData.PayOrderProductCount += categoryOrderReportOrderStatusTotalItem.GoodsCount

						break
					case -1:
						break
					}
				}
			}
			for _, afterSalesOrderReportOrderTotalItem := range afterSalesOrderReportOrder {
				if item.SupplierID == afterSalesOrderReportOrderTotalItem.SupplierID && item.GatherSupplyID == afterSalesOrderReportOrderTotalItem.GatherSupplyID {
					shopOrderData.RefundOrderSum = afterSalesOrderReportOrderTotalItem.Total
					shopOrderData.RefundOrderCount = afterSalesOrderReportOrderTotalItem.Count
				}
			}

			if shopOrderData.PayOrderCount > 0 {
				shopOrderData.PayOrderAveragePrice = shopOrderData.PayOrderSum / shopOrderData.PayOrderCount //已支付订单均价
			}
			if shopOrderData.PayOrderProductCount > 0 {
				shopOrderData.PayOrderAverageProductPrice = shopOrderData.PayOrderSum / shopOrderData.PayOrderProductCount //已支付商品均价 （需求订单总额/商品总个数）
			}

			shopOrderDatas = append(shopOrderDatas, shopOrderData)
		}
	}
	return
}

// 报表条件拼接
func OrderReportFormsDbRequest(db *gorm.DB, info request.OrderAdminSearch, isUser int) (err error, resDb *gorm.DB, requestTitle string) {
	db.Where("orders.is_plugin = ?", 0)
	if info.PluginID != 0 {
		db.Where("orders.plugin_id=?", info.PluginID)
		requestTitle += "插件id:" + strconv.Itoa(int(info.PluginID)) + ";"
	}

	if info.OrderType != 0 {
		db.Where("orders.order_type=?", info.OrderType)
		orderTypeTitle := strconv.Itoa(int(info.OrderType))
		switch info.OrderType {
		case 1:
			orderTypeTitle = "套餐订单"
			break
		}
		requestTitle += "订单类型:" + orderTypeTitle + ";"
	}
	if info.OrderTypeNote != "" {
		orderTypeNoteTitle := "订单类型备注:"
		switch info.OrderType {
		case 1:
			orderTypeNoteTitle = "套餐名称:"
			break
		}
		db.Where("orders.order_type_note LIKE ?", "%"+info.OrderTypeNote+"%")
		requestTitle += orderTypeNoteTitle + info.OrderTypeNote + ";"
	}

	if info.SourceShopName != "" {
		db.Where("orders.source_shop_name like ?", "%"+info.SourceShopName+"%")
		requestTitle += "店铺名称:" + info.SourceShopName + ";"
	}

	if info.SupplierID != nil {
		if *info.SupplierID == 999999 {
			db.Where("orders.supplier_id > 0")
			requestTitle += "供应商:全部供应商;"
		} else {
			db.Where("orders.supplier_id = ?", &info.SupplierID).Where("orders.gather_supply_id = 0")
			requestTitle += "供应商id:" + strconv.Itoa(int(*info.SupplierID)) + ";"

		}
	}

	if info.ApplicationID > 0 {
		db.Where("orders.application_id = ?", info.ApplicationID)
		requestTitle += "采购端id:" + strconv.Itoa(int(info.ApplicationID)) + ";"

	}

	// 指定供应链
	if info.GatherSupplierID != nil {
		db.Where("orders.gather_supply_id = ?", info.GatherSupplierID).Where("orders.supplier_id = 0")
		requestTitle += "供应链id:" + strconv.Itoa(int(*info.GatherSupplierID)) + ";"

	}

	if info.PaySN != "" {
		var payInfoIds []uint
		err = source.DB().Model(&response.PayInfo{}).Where("pay_sn = ?", info.PaySN).Pluck("id", &payInfoIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("该支付单号并无数据" + err.Error())
			return
		}
		requestTitle += "支付单号:" + info.PaySN + ";"

		db.Where("orders.pay_info_id in ?", payInfoIds)

	}
	if info.PayTypeID != 0 {
		db.Where("orders.pay_type_id = ?", info.PayTypeID)
		requestTitle += "支付类型:" + model2.GetPayTypeName(info.PayTypeID) + ";"
	}
	if info.UserID > 0 {
		db.Where("orders.user_id = ?", info.UserID)
		if isUser != 1 {
			requestTitle += "用户id:" + strconv.Itoa(int(info.UserID)) + ";"
		}
	}

	if info.JushuitanBind != nil {
		db.Where("orders.jushuitan_bind = ?", info.JushuitanBind)
		requestTitle += "聚水潭绑定状态(1是):" + strconv.Itoa(int(*info.JushuitanBind)) + ";"

	}
	if info.GatherSupplierStatus != "" && info.GatherSupplierID != nil {
		db.Where("orders.gather_supply_sn = '' or gather_supply_sn = null")
		requestTitle += "异常订单;"

	}
	//直播间id
	if info.ShareLiveRoomId != 0 {
		db.Where("orders.share_live_room_id = ?", info.ShareLiveRoomId)
		requestTitle += "直播间id:" + strconv.Itoa(int(info.ShareLiveRoomId)) + ";"

	}
	if info.Status != nil {
		if *info.Status == 5 {
			db.Where("orders.refund_status = ?", model.Refunding)
			requestTitle += "售后状态:售后中;"
		} else if *info.Status == 6 {
			db.Where("orders.refund_status = ?", model.RefundComplete)
			requestTitle += "售后状态:售后完成;"
		} else {
			db.Where("orders.status = ?", info.Status)
			requestTitle += "订单状态:" + model.GetStatusName(*info.Status) + ";"
		}
	}

	if info.RefundStatus != nil {
		if *info.RefundStatus == -1 {
			requestTitle += "售后状态:售后关闭;"
			db.Joins("left join after_sales on after_sales.order_id = orders.id").Where("after_sales.status = ?", model.ClosedRefund)
		} else {
			db.Where("orders.refund_status = ?", info.RefundStatus)

			requestTitle += "售后状态:" + model.GetRefundStatusName(model.RefundStatus(*info.RefundStatus)) + ";"
		}

	}
	if info.OrderSN != "" {
		db.Where("orders.order_sn = ?", info.OrderSN)
		requestTitle += "订单号:" + info.OrderSN + ";"
	}

	if info.Note != "" {
		db.Where("orders.note like ?", "%"+info.Note+"%")
		requestTitle += "订单备注:" + info.Note + ";"

	}
	if info.SupplySN != "" {
		db.Where("orders.gather_supply_sn like ?", "%"+info.SupplySN+"%")
		requestTitle += "供应链单号:" + info.SupplySN + ";"

	}
	if info.ThirdOrderSN != "" {
		db.Where("orders.third_order_sn = ?", info.ThirdOrderSN)
		requestTitle += "第三方单号:" + info.SupplySN + ";"

	}
	if info.IsWxMiniSend != 0 {
		db.Where("orders.is_wx_mini_send = ?", info.IsWxMiniSend)
		requestTitle += "是否推送发货信息到微信小程序成功0待推送1推送成功:" + strconv.Itoa(int(info.IsWxMiniSend)) + ";"
	}
	var timeType, timeTitle string
	timeType = "orders.created_at"
	timeTitle = "订单创建时间"
	if info.TimeType != nil {
		switch *info.TimeType {
		case 0:
			timeType = "orders.created_at"
			break
		case 1:
			timeType = "orders.paid_at"
			timeTitle = "订单支付时间"
			break
		case 2:
			timeType = "orders.sent_at"
			timeTitle = "订单发货时间"
			break
		case 3:
			timeType = "orders.received_at"
			timeTitle = "订单收货时间"
			break
		default:
			timeType = "orders.created_at"
			break
		}
	}
	if info.StartAT != "" {
		db.Where(""+timeType+" >= ?", info.StartAT)
		requestTitle += timeTitle + "开始:" + info.StartAT + ";"
	}
	if info.EndAT != "" {
		db.Where(""+timeType+" <= ?", info.EndAT)
		requestTitle += timeTitle + "结束:" + info.EndAT + ";"
	}
	if info.ProductTitle != "" {
		orderIds := []uint{}
		err = source.DB().Model(model.OrderItem{}).Where("title like ?", "%"+info.ProductTitle+"%").Group("order_id").Pluck("order_id", &orderIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("该商品名称并无订单" + err.Error())
			return
		}
		requestTitle += "下单时商品名称:" + info.ProductTitle + ";"
		db.Where("orders.id in ?", orderIds)
	}
	if info.AppShopName != "" {
		appShopId := []uint{}
		err = source.DB().Model(response.ApplicationShop{}).Where("shop_name like ?", "%"+info.AppShopName+"%").Pluck("id", &appShopId).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("该子采购的名称并无订单" + err.Error())
			return
		}
		requestTitle += "采购端商城名称:" + info.AppShopName + ";"

		db.Where("orders.application_shop_id in ?", appShopId)

	}
	if info.NickName != "" {
		userIds := []uint{}
		err = source.DB().Model(response.User{}).Where("nick_name like ?", "%"+info.NickName+"%").Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("该昵称并无订单" + err.Error())
			return
		}
		requestTitle += "用户昵称:" + info.NickName + ";"

		db.Where("orders.user_id in ?", userIds)

	}
	if info.UserName != "" {
		shippingIds := []uint{}
		err = source.DB().Model(model.ShippingAddress{}).Where("realname like ?", "%"+info.UserName+"%").Pluck("id", &shippingIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("该昵称并无订单" + err.Error())
			return
		}
		requestTitle += "收货姓名:" + info.UserName + ";"

		db.Where("orders.shipping_address_id in ?", shippingIds)
	}
	if info.UserMobile != "" {
		userIds := []uint{}
		err = source.DB().Model(model.ShippingAddress{}).Where("mobile like ?", "%"+strings.Trim(info.UserMobile,
			" ")+"%").Pluck("id",
			&userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("该收货手机号并无订单" + err.Error())
			return
		}
		requestTitle += "收货手机号:" + info.UserMobile + ";"

		db.Where("orders.shipping_address_id in ?", userIds)
	}

	if info.ShippingSN != "" {
		var orderIds []uint
		err = source.DB().Model(model.OrderExpress{}).Where("express_no = ?", info.ShippingSN).Pluck("order_id", &orderIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("该快递单号并无订单" + err.Error())
			return
		}
		requestTitle += "发货单号:" + info.ShippingSN + ";"

		db.Where("orders.id in ?", orderIds)

	}

	if info.CloudOrderId != 0 {
		var orderIds []uint
		err = source.DB().Model(&CloudOrderItem{}).Where("cloud_order_id = ?", info.CloudOrderId).Pluck("order_id", &orderIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("该云仓订单id并无订单" + err.Error())
			return
		}
		requestTitle += "云仓订单id:" + strconv.Itoa(int(info.CloudOrderId)) + ";"

		db.Where("orders.id in ?", orderIds)
	}
	return nil, db, requestTitle
}
