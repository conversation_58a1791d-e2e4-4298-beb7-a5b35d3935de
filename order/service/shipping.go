package service

import (
	"errors"
	"go.uber.org/zap"
	express2 "order/express"
	"order/order"
	"order/request"
	orderResponse "order/response"
	"shipping/express"
	"shipping/response"
	"shipping/service"
	"strings"
	"yz-go/component/log"
	"yz-go/source"
)

func GetExpressInfo(orderID uint) (err error, orderExpressInfoResps []orderResponse.ExpressInfoResp) {
	var order order.Order
	err = source.DB().Preload("ShippingAddress").Preload("Supplier").Preload("OrderExpress").Preload("OrderExpress.OrderItems").First(&order, orderID).Error
	if err != nil {
		return
	}
	for _, v := range order.OrderExpress {
		var expressInfoResp response.ExpressInfoResp
		var orderExpressInfoResp orderResponse.ExpressInfoResp
		if v.CompanyCode == "FWSY" || v.CompanyCode == "SFEXPRESS" {
			if !strings.Contains(v.ExpressNo, ":") {
				if v.SendMobile != "" {
					if len(v.SendMobile) != 11 {
						err = errors.New("寄件人手机号格式不正确")
						return
					}
					var numberStr = v.SendMobile[7:]
					v.ExpressNo = v.ExpressNo + ":" + numberStr
				} else if order.Supplier.ExpressMobile != "" {
					if len(order.Supplier.ExpressMobile) != 11 {
						err = errors.New("寄件人手机号格式不正确")
						return
					}
					var numberStr = order.Supplier.ExpressMobile[7:]
					v.ExpressNo = v.ExpressNo + ":" + numberStr
				} else {
					if len(order.ShippingAddress.Mobile) != 11 {
						err = errors.New("收货人手机号格式不正确")
						return
					}
					var numberStr = order.ShippingAddress.Mobile[7:]
					v.ExpressNo = v.ExpressNo + ":" + numberStr
				}

			}
		}
		err, expressInfoResp = service.GetExpressInfo(v.ExpressNo, v.CompanyCode)
		if err != nil {
			return
		}
		expressInfoResp.ExpressCode = v.ExpressNo
		err, expressInfoResp.ExpressCom = express.GetCompanyByCode(v.CompanyCode)

		//因上面方法不少地方使用 原结构体不变，只改变返回的结构体
		orderExpressInfoResp.ExpressCode = expressInfoResp.ExpressCode
		orderExpressInfoResp.ExpressCom = expressInfoResp.ExpressCom
		orderExpressInfoResp.ExpressInfo = expressInfoResp.ExpressInfo
		orderExpressInfoResp.OrderItems = v.OrderItems
		for k, item := range orderExpressInfoResp.OrderItems {
			var itemExpress express2.ItemExpress
			source.DB().Where("order_item_id = ?", item.ID).Where("order_express_id = ?", v.ID).First(&itemExpress)
			orderExpressInfoResp.OrderItems[k].SendNum = itemExpress.Num

		}
		orderExpressInfoResps = append(orderExpressInfoResps, orderExpressInfoResp)
	}

	return
}

func GetExpressInfoNew(ID uint) (err error, orderExpressInfoResp orderResponse.ExpressInfoResp) {
	var orderExpress express2.OrderExpress
	err = source.DB().Preload("OrderItems").First(&orderExpress, ID).Error
	if err != nil {
		return
	}
	var order order.Order
	err = source.DB().Preload("ShippingAddress").Preload("Supplier").First(&order, orderExpress.OrderID).Error
	if err != nil {
		return
	}
	var expressInfoResp response.ExpressInfoResp
	if orderExpress.CompanyCode == "FWSY" || orderExpress.CompanyCode == "SFEXPRESS" {
		if !strings.Contains(orderExpress.ExpressNo, ":") {
			if orderExpress.SendMobile != "" {
				if len(orderExpress.SendMobile) != 11 {
					err = errors.New("寄件人手机号格式不正确")
					return
				}
				var numberStr = orderExpress.SendMobile[7:]
				orderExpress.ExpressNo = orderExpress.ExpressNo + ":" + numberStr
			} else if order.Supplier.ExpressMobile != "" {
				if len(order.Supplier.ExpressMobile) != 11 {
					err = errors.New("寄件人手机号格式不正确")
					return
				}
				var numberStr = order.Supplier.ExpressMobile[7:]
				orderExpress.ExpressNo = orderExpress.ExpressNo + ":" + numberStr
			} else {
				if len(order.ShippingAddress.Mobile) != 11 {
					err = errors.New("收货人手机号格式不正确")
					return
				}
				var numberStr = order.ShippingAddress.Mobile[7:]
				orderExpress.ExpressNo = orderExpress.ExpressNo + ":" + numberStr
			}

		}
	}
	if orderExpress.CompanyCode == "SXJE" {
		orderExpress.CompanyCode = "SXJD"
		//云签最开始给的物流编码是错误的，但鉴于中台用此编码的地方有很多，为了造成不必要的bug（尝试在数据库中直接修改为SXJD，但查询物流页面报错，遂放弃修改源json中的数据），所以只在查询物流信息接口之前修改一下
	}
	err, expressInfoResp = service.GetExpressInfo(orderExpress.ExpressNo, orderExpress.CompanyCode)
	if err != nil {
		return
	}
	log.Log().Info("请求物流信息返回数据", zap.Any("data", expressInfoResp))
	expressInfoResp.ExpressCode = orderExpress.ExpressNo
	if orderExpress.CompanyCode == "SXJD" {
		orderExpress.CompanyCode = "SXJE"
	}
	err, expressInfoResp.ExpressCom = express.GetCompanyByCode(orderExpress.CompanyCode)

	//因上面方法不少地方使用 原结构体不变，只改变返回的结构体
	orderExpressInfoResp.ExpressCode = expressInfoResp.ExpressCode
	orderExpressInfoResp.ExpressCom = expressInfoResp.ExpressCom
	orderExpressInfoResp.ExpressInfo = expressInfoResp.ExpressInfo
	orderExpressInfoResp.OrderItems = orderExpress.OrderItems
	for k, item := range orderExpressInfoResp.OrderItems {
		var itemExpress express2.ItemExpress
		source.DB().Where("order_item_id = ?", item.ID).Where("order_express_id = ?", orderExpress.ID).First(&itemExpress)
		orderExpressInfoResp.OrderItems[k].SendNum = itemExpress.Num

	}
	return
}

func GetExpressNo(orderID uint) (err error, orderExpressInfoResps []orderResponse.ExpressInfoResp) {
	var order order.Order
	err = source.DB().Preload("OrderExpress").Preload("OrderExpress.OrderItems").First(&order, orderID).Error
	if err != nil {
		return
	}
	return
}

func GetExpressSingleInfo(info request.OrderExpress) (err error, expressInfoResp response.ExpressInfoResp) {

	err, expressInfoResp = service.GetExpressInfo(info.ExpressNo, info.CompanyCode)
	if err != nil {
		return
	}
	expressInfoResp.ExpressCode = info.ExpressNo
	err, expressInfoResp.ExpressCom = express.GetCompanyByCode(info.CompanyCode)
	return
}
