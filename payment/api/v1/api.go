package v1

import (
	joinModel "convergence/model"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/gogf/gf/frame/g"
	"go.uber.org/zap"
	"io/ioutil"
	"net/http"
	"payment/callback"
	model2 "payment/model"
	Pay "payment/pay"
	"payment/request"
	"payment/service"
	v1 "user/api/f/v1"
	"yz-go/component/log"
	"yz-go/config"
	yzResponse "yz-go/response"
)

func GetPay(c *gin.Context) {

	var reqPayData model2.RequestPayData
	err := c.ShouldBindJSON(&reqPayData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	var face Pay.CommonInterface
	switch reqPayData.PayType {
	case model2.LAKALA:
		var lakala Pay.LakalaPay
		lakala.Uid = v1.GetUserID(c)
		lakala.Amount = reqPayData.Amount
		lakala.PaySn = reqPayData.PaySN
		face = &lakala
		break
	case model2.CONVERGENCEMINIPLUS: //汇聚小程序plus
		var miniPlus Pay.ConvergenceMiniPlus
		miniPlus.Uid = v1.GetUserID(c)
		miniPlus.Amount = reqPayData.Amount
		miniPlus.PaySn = reqPayData.PaySN
		miniPlus.PayType = int(reqPayData.PayType)
		face = &miniPlus
		break
	case 4: //PC扫码支付
		var convergenceWeChat Pay.WeChatPayCode
		face = &convergenceWeChat
		convergenceWeChat.ConvergenceWeChatRecharge = reqPayData
		break
	}

	if err, ResData := Pay.Payment(face); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(ResData, c)
	}

}
func GetRecharge(c *gin.Context) {
	var reqPayData model2.RequestPayData
	err := c.ShouldBindJSON(&reqPayData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	reqPayData.Uid = v1.GetUserID(c)
	var face Pay.CommonInterface
	switch reqPayData.PayType {
	case model2.LAKALA: //PC扫码支付
		var lakala Pay.LakalaPay
		lakala.Uid = reqPayData.Uid
		lakala.Amount = reqPayData.Amount
		lakala.PaySn = reqPayData.PaySN
		face = &lakala
		break
	case 4: //PC扫码支付
		var convergenceWeChat Pay.WeChatPayCode
		face = &convergenceWeChat
		convergenceWeChat.ConvergenceWeChatRecharge = reqPayData
		break
	}

	if err, ResData := Pay.Recharge(face); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(ResData, c)
	}
}

func GetPayment(c *gin.Context) {
	var reqPayment model2.ReqPayment
	err := c.ShouldBindJSON(&reqPayment)
	//if err != nil {
	//	log.Log().Error("获取参数失败", zap.Any("err", err))
	//	yzResponse.FailWithMessage(err.Error(), c)
	//	return
	//}

	err, data := service.GetPayment(reqPayment)
	if err != nil {
		log.Log().Error("充值失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(data, c)

}

func GetPaymentConfig(c *gin.Context) {

	data := config.Config().Join.BalanceSettings
	yzResponse.OkWithData(data, c)

}
func GetSuperPayment(c *gin.Context) {
	var reqPayment model2.ReqPayment
	c.ShouldBindJSON(&reqPayment)
	//if err != nil {
	//	log.Log().Error("获取参数失败", zap.Any("err", err))
	//	yzResponse.FailWithMessage(err.Error(), c)
	//	return
	//}

	err, data := service.GetSuperPayment(reqPayment)
	if err != nil {
		log.Log().Error("充值失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(data, c)

}

func NotifyPayment(c *gin.Context) {
	var notifyData model2.NotifyData
	err := c.ShouldBindJSON(&notifyData)
	log.Log().Info("lakala NotifyPayment", zap.Any("info", notifyData))

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if notifyData.OrderStatus != "2" {
		log.Log().Error("lakala支付回调支付失败", zap.Any("err", notifyData))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var lakala Pay.LakalaPay
	lakala.NotifyData = notifyData
	if err, ResData := lakala.NotifyPayment(); err != nil {
		log.Log().Error("lakala支付回调支付失败 NotifyPayment", zap.Any("err", err))

		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		fmt.Println(ResData)
		c.JSON(http.StatusOK, gin.H{"code": "SUCCESS", "message": "执行成功"})
	}
}

func NotifyRecharge(c *gin.Context) {
	var notifyData model2.NotifyData
	err := c.ShouldBindJSON(&notifyData)
	log.Log().Info("lakala NotifyRecharge", zap.Any("info", notifyData))

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if notifyData.OrderStatus != "2" {
		log.Log().Error("lakala充值回调支付失败", zap.Any("err", notifyData))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var lakala Pay.LakalaPay
	lakala.NotifyData = notifyData
	if err, ResData := lakala.NotifyRecharge(); err != nil {
		log.Log().Error("lakala充值回调支付失败 NotifyRecharge", zap.Any("err", err))

		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		fmt.Println(ResData)
		c.JSON(http.StatusOK, gin.H{"code": "SUCCESS", "message": "执行成功"})
	}
}
func ConvergenceMicroPlus(c *gin.Context) {
	var err error

	var param joinModel.PayNotify

	//notifyData, _ := c.GetRawData()
	//log.Log().Info("ConvergenceMicroPlus notify data ", zap.Any("info", string(notifyData)))
	err = c.ShouldBindQuery(&param)

	log.Log().Info("ConvergenceMicroPlus 支付回调数据!", zap.Any("err", param))

	data, err := ioutil.ReadAll(c.Request.Body)

	log.Log().Info(" ConvergenceMicroPlus支付回调数据1!", zap.Any("err", string(data)))

	if err != nil {
		log.Log().Info("支付回调数据解析错误!", zap.Any("err", err))

		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = callback.JoinPayNotify(param); err == nil {
		log.Log().Info("支付回调正常返回success!")
		c.String(http.StatusOK, "success")
	} else {
		log.Log().Error("支付回调异常!", zap.Any("err", err))
	}
}
func ConvergenceMicroPlusRefund(c *gin.Context) {
	var err error
	var param joinModel.RefundNotify
	err = c.ShouldBindQuery(&param)
	log.Log().Info("ConvergenceMicroPlus 退款回调数据!", zap.Any("err", param))

	data, err := ioutil.ReadAll(c.Request.Body)

	log.Log().Info(" ConvergenceMicroPlus退款回调数据1!", zap.Any("err", string(data)))

	if err != nil {
		log.Log().Info("支付回调数据解析错误!", zap.Any("err", err))

		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = callback.JoinPlusRefundNotify(param); err == nil {
		log.Log().Info("退款回调正常返回success!")
		c.String(http.StatusOK, "success")
	} else {
		log.Log().Error("支付回调异常!", zap.Any("err", err))
	}
}
func ConvergenceMicroPlusSmallShopRefund(c *gin.Context) {
	var err error
	var param joinModel.RefundNotify
	err = c.ShouldBindQuery(&param)
	log.Log().Info("ConvergenceMicroPlus 小商店退款回调数据!", zap.Any("err", param))

	data, err := ioutil.ReadAll(c.Request.Body)

	log.Log().Info(" ConvergenceMicroPlus小商店退款回调数据1!", zap.Any("err", string(data)))

	if err != nil {
		log.Log().Info("支付回调数据解析错误 小商店!", zap.Any("err", err))

		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	c.String(http.StatusOK, "success")

}

// @Tags 微信小程序API
// @Summary 设置配置文件
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "设置配置文件"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /wechatofficial/setWechatOfficial [post]
func DownloaderWechatCert(c *gin.Context) {
	var sys request.WechatDownloadCert
	_ = c.ShouldBindJSON(&sys)
	//因为前端提交的是加密的这里获取保存的密钥
	conf := *config.Config()
	sys.PayKey = conf.WechatPayment.PayKey
	//下载微信平台证书
	err, url, serial := service.Downloader(sys)

	if err != nil {
		log.Log().Error("平台证书下载失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("微信平台证书下载失败"+err.Error(), c)
		return
	}

	yzResponse.OkWithData(g.Map{"cert": url, "serial": serial}, c)
}
