package callback

import (
	"convergence/model"
	"go.uber.org/zap"
	pModel "payment/model"
	"payment/mq"
	"strconv"
	"yz-go/component/log"
)

func JoinPayNotify(param model.PayNotify) (err error) {
	log.Log().Info("收到回调通知", zap.Any("info", param))

	if param.Status == "100" {

		switch param.Mp {

		case strconv.Itoa(pModel.CONVERGENCEMINIPLUS):
			log.Log().Info("汇聚微信小程序plus支付", zap.Any("info", param))
			err = WechatPlusPayNotify(param)

		}

	}

	return

}

func JoinPlusRefundNotify(param model.RefundNotify) (err error) {
	log.Log().Info("收到plus 退款回调通知", zap.Any("info", param))

	if param.Status == "100" {
		log.Log().Info("收到plus 退款成功", zap.Any("info", param))

	}

	return

}

// 微信小程序plus支付回调
func WechatPlusPayNotify(param model.PayNotify) (err error) {
	var payInfo pModel.PayInfo
	orderSn, _ := strconv.Atoi(param.OrderNo)

	log.Log().Info("微信小程序plus支付回调!", zap.Any("info", payInfo))

	err = mq.PublishMessage(uint(orderSn), pModel.CONVERGENCEMINIPLUS)
	if err != nil {
		log.Log().Error("微信小程序plus支付回调!", zap.Any("err", payInfo))
		return
	}

	return

}
