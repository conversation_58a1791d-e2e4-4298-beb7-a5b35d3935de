package cron

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"payment"
	"payment/model"
	"payment/mq"
	"strconv"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func CheckUpdatePaidHandle() {
	task := cron.Task{
		Key:  "checkUpdatePaidHandle",
		Name: "定时更新支付状态",
		Spec: "0 */10 * * * *",
		Handle: func(task cron.Task) {
			err := checkUnprocessedPayOrders()
			if err != nil {
				log.Log().Error("CheckUpdatePaidHandle支付定时修复失败", zap.Any("err", err))
			}
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func StationBalancePaidHandle() {
	task := cron.Task{
		Key:  "stationBalancePaid",
		Name: "站内余额已支付",
		Spec: "0 */1 * * * *",
		Handle: func(task cron.Task) {
			err := StationBalancePaidCron()
			if err != nil {
				log.Log().Error("支付定时修复失败", zap.Any("err", err))
			}
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func StationBalancePaidCron() (err error) {
	var purchasingBalances []model.PurchasingBalance
	err = source.DB().Where("business_type = 2").Where("status = 0").Find(&purchasingBalances).Error
	if err != nil {
		return
	}
	for _, purchasingBalance := range purchasingBalances {
		var orderIds []int
		err = json.Unmarshal([]byte(purchasingBalance.OrderID), &orderIds)
		if err != nil {
			continue
		}
		var orders []model.Order
		err = source.DB().Where("id in ?", orderIds).Find(&orders).Error
		if err != nil {
			continue
		}
		isPay := true
		for _, order := range orders {
			if order.Status == 0 {
				err = mq.PublishMessage(purchasingBalance.PaySN, 2)
				if err != nil {
					continue
				}
				// 存在未支付的订单
				isPay = false
			}
		}
		if isPay {
			// 全部订单已支付，修改余额记录状态
			err = source.DB().Model(model.PurchasingBalance{}).Where("id = ?", purchasingBalance.ID).Update("status", 1).Error
			if err != nil {
				continue
			}
		}

	}
	return err
}

type Orders struct {
	ID        uint   `json:"id"`
	OrderSN   string `json:"order_sn"`
	Status    uint   `json:"status"`
	CreatedAt string `json:"created_at"`
	PayTypeID int    `json:"pay_type_id"`
	PayInfoID uint   `json:"pay_info_id"`
	PaySN     uint   `json:"pay_sn"`
}

// 核心检查逻辑：查询并处理支付状态异常的数据
func checkUnprocessedPayOrders() error {
	log.Log().Debug("checkUnprocessedPayOrders定时更新支付状态检测")
	// 1. 查询pay_infos表中状态为0（未处理）的记录
	var orders []Orders
	if err := source.DB().Select("orders.pay_info_id,orders.pay_type_id,orders.id,orders.created_at,orders.order_sn ,orders.status,pay_infos.pay_sn").
		Where("orders.status = ?", 1).
		Where("pay_infos.status = ?", 0). // 假设pay_infos.status=0表示未处理
		Joins("left join pay_infos on pay_infos.id = orders.pay_info_id").
		Find(&orders).Error; err != nil {
		return fmt.Errorf("checkUnprocessedPayOrders查询未处理支付记录失败: %v", err)
	}
	log.Log().Debug("checkUnprocessedPayOrders定时更新支付状态检测1", zap.Any("count", len(orders)))

	// 2. 遍历每条支付记录，检查关联订单状态

	// 检查每个订单的状态是否为已支付（假设order.status=1表示已支付）
	for _, orderPay := range orders {

		log.Log().Debug("checkUnprocessedPayOrders定时更新支付状态检测2", zap.Any("data", orderPay))

		// 3. 执行支付状态更新（复用现有逻辑）
		paySn := strconv.Itoa(int(orderPay.PaySN))
		if err := payment.Pay(paySn, orderPay.PayTypeID); err != nil {
			log.Log().Error("checkUnprocessedPayOrders支付状态变更失败", zap.String("pay_sn", paySn), zap.Error(err))
			continue
		}

	}
	return nil
}
