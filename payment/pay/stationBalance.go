package Pay

import (
	"errors"
	model3 "finance/model"
	fuluSupplyModel "fulu-supply/model"
	"go.uber.org/zap"
	_ "gorm.io/gorm"
	mq2 "notification/mq"
	"payment/model"
	model2 "payment/model"
	"payment/mq"
	"payment/request"
	"payment/service"
	"public-supply/common"
	"strconv"
	"yz-go/component/log"
	"yz-go/source"
)

type StationBalanceRefund struct {
	request.Refund
}

type StationBalanceRecharge struct {
	model.UpdateBalance
}

type StationBalance struct {
	StationBalanceRefund     interface{}
	StationBalanceRecharge   StationBalanceRecharge
	StationBalancePayment    interface{}
	StationBalanceSettlement interface{}
}

func (station *StationBalance) SplitAccount() (err error) {
	order := station.StationBalanceSettlement.(*model2.Order)
	var supplierOrder model.SupplierSettlement
	err = source.DB().Where("order_id = ?", order.ID).First(&supplierOrder).Error
	if order.SupplierID > 0 {

		err = service.PlatformSeparateBalance(supplierOrder, 2)
		if err != nil {
			var separate model.SeparateAccountingRecords
			separate.Amount = supplierOrder.SettlementAmount
			separate.SupplierID = order.SupplierID
			separate.OrderSN = order.OrderSN
			separate.Info = err.Error()
			source.DB().Create(&separate)
		}
	}

	return

}

func (station *StationBalance) AfterOperation() (err error) {
	panic("implement me")
}

func (station *StationBalance) BeforeOperation() (err error) {

	payment := station.StationBalancePayment.(model.UpdateBalance)
	if payment.Amount <= 0 {
		err = errors.New("扣款余额不能空")
		return
	}

	//var orderPayInfo model2.OrderPayInfo
	//
	//err = source.DB().Where("pay_info_id=?", payment.PayInfoID).First(&orderPayInfo).Error
	//if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
	//	err = errors.New("未查询到支付单")
	//	return
	//}
	//
	//var order model2.Order
	//err = source.DB().Where("id=?", orderPayInfo.OrderID).First(&order).Error
	//if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
	//	err = errors.New("未查询到支付单")
	//	return
	//}
	//if order.Status > 0 {
	//	err = errors.New("该订单已支付,请勿重复支付")
	//	return
	//}

	return
}

func (station *StationBalance) Init() (err error, resData interface{}) {
	//PayTypeList = append(PayTypeList, Type{Name: "站内余额", Code: 2, Status: 1, Mode: 1})

	return
}

type Order struct {
	ID                uint   `json:"id"`
	OrderSN           string `json:"order_sn"`
	PayTypeID         int    `json:"pay_type_id"`
	GatherSupplyID    uint   `json:"gather_supply_id"`
	ApplicationShopID uint   `json:"application_shop_id"`
}

func (station *StationBalance) Payment() (err error, resData interface{}) {
	payment := station.StationBalancePayment.(model.UpdateBalance)

	var data = make(map[string]interface{})
	data["status"] = 0

	var balance model.Balance
	err = source.DB().Where("uid = ?", payment.Uid).Where("type = ?", payment.PayType).First(&balance).Error
	if err != nil {
		return
	}

	if balance.ID <= 0 {
		err = errors.New("未查询到此用户余额")
		return
	}
	if balance.PurchasingBalance < payment.Amount {
		err = errors.New("余额不足")
		return
	}

	params := model.UpdateBalance{
		Uid:           payment.Uid,
		Amount:        payment.Amount,
		Type:          payment.PayType,
		PayType:       model.STATIONBALANCE,
		OperationType: model.BALANCE_PURCHASING,
		Action:        model.REDUCE,
		PayInfoID:     payment.PayInfoID,
	}

	data["status"] = 1
	var payInfo model2.PayInfo
	err = source.DB().Where("id=?", payment.PayInfoID).First(&payInfo).Error
	if err != nil {
		log.Log().Error("查询支付单失败", zap.Any("err", err.Error()))
		return
	}

	var orderPayInfo model.OrderPayInfo
	err = source.DB().First(&orderPayInfo, "pay_info_id=?", payInfo.ID).Error
	if err != nil {
		return
	}

	var isPay model3.PurchasingBalanceOrderIds

	err = source.DB().Where("order_id = ? and is_local_order = ?", orderPayInfo.OrderID, 0).First(&isPay).Error

	if isPay.ID > 0 {
		err = errors.New("已经支付，请勿重复支付")
		return
	}
	var order model.Order
	err = source.DB().First(&order, "id=?", orderPayInfo.OrderID).Error
	if err != nil {
		err = errors.New("订单不存在")
		return
	}

	var fuLuOrder fuluSupplyModel.FuluSupplyOrderRequest
	source.DB().Where("order_sn=?", order.OrderSN).First(&fuLuOrder)
	if fuLuOrder.ID > 0 {
		params.Remarks = "数字权益商品订单"
	}
	if order.GatherSupplyType == common.SUPPLY_CAKE {
		params.Remarks = "蛋糕叔叔订单"
	}
	if order.ApplicationShopID > 0 {
		var appShop model.ApplicationShop
		err = source.DB().First(&appShop, order.ApplicationShopID).Error
		if err != nil {
			params.Remarks = params.Remarks + "（采购端店铺：" + strconv.Itoa(int(order.ApplicationShopID)) + "不存在）"
			err = nil
		} else {
			params.Remarks = params.Remarks + "（采购端店铺：" + appShop.ShopName + "）"
		}
	}

	err = service.UpdateSettlementBalance(params)
	if err != nil {
		return
	}

	if params.Type >= 2 { // 站内余额 或采购账户 付款 支付类型
		err = mq.PublishMessage(payInfo.PaySN, params.Type)
		log.Log().Info("params.Type >= 2 pay ", zap.Any("info", payInfo.PaySN), zap.Any("err", err), zap.Any("type", params.Type))

	} else {
		err = mq.PublishMessage(payInfo.PaySN, params.PayType)
		log.Log().Info("else   pay ", zap.Any("info", payInfo.PaySN), zap.Any("err", err), zap.Any("type", params.Type))

	}
	err = mq2.PublishMessage(payment.Uid, "balance", 0)
	resData = data
	return

}

func (station *StationBalance) Refund() (err error, resData interface{}) {

	refund := station.StationBalanceRefund.(request.Refund)

	var payInfo model2.PayInfo
	var order Order
	err = source.DB().Where("pay_sn=?", refund.PaySN).First(&payInfo).Error

	if err != nil {
		log.Log().Error("StationBalance Refund payInfo 错误", zap.Any("err", err.Error()))

	}

	err = source.DB().Table("orders").Where("orders.order_sn=?", refund.OrderSN).First(&order).Error
	if err != nil {
		log.Log().Error("StationBalance Refund 错误", zap.Any("err", err.Error()))

	}

	balanceData := model.UpdateBalance{
		Uid:           refund.UserID,
		Amount:        refund.Amount,
		Type:          2,
		PayType:       model.STATIONBALANCE,
		OperationType: model.BALANCE_PURCHASING,
		Action:        model.PLUS,
		Remarks:       "订单号：" + order.OrderSN,
		PaySn:         payInfo.PaySN,
	}
	if order.ApplicationShopID > 0 {
		var appShop model.ApplicationShop
		err = source.DB().First(&appShop, order.ApplicationShopID).Error
		if err != nil {
			balanceData.Remarks = balanceData.Remarks + "（采购端店铺：" + strconv.Itoa(int(order.ApplicationShopID)) + "不存在）"
			err = nil
		} else {
			balanceData.Remarks = balanceData.Remarks + "（采购端店铺：" + appShop.ShopName + "）"
		}
	}
	//if order.GatherSupplyID > 0 {
	balanceData.PayType = order.PayTypeID
	log.Log().Info("采购账户退款", zap.Any("info", balanceData))
	//}
	err = service.ChangeBalance(model.BusinessStationBalanceRefund, balanceData)
	return

}

func (station *StationBalance) Recharge() (err error, resData interface{}) {
	var AccountType int
	if station.StationBalanceRecharge.Type == 0 {
		AccountType = model.STATIONBALANCE
	} else {
		AccountType = station.StationBalanceRecharge.Type
	}
	balanceData := model.UpdateBalance{
		Uid:           station.StationBalanceRecharge.Uid,
		PetSupplierID: station.StationBalanceRecharge.PetSupplierID,
		Amount:        station.StationBalanceRecharge.Amount,
		Type:          AccountType,
		PayType:       AccountType,
		OperationType: model.BALANCE_PURCHASING,
		Action:        station.StationBalanceRecharge.Action,
		Remarks:       station.StationBalanceRecharge.Remarks,
	}
	if balanceData.Action == 2 {
		err = service.ChangeBalance(model.BusinessStationBalanceDeduction, balanceData)
	} else {
		err = service.ChangeBalance(model.BusinessStationBalanceRecharge, balanceData)
	}

	return
}

func (station *StationBalance) Deduction() (err error, resData interface{}) {
	return
}

func (station *StationBalance) GetBalance() (err error, resData interface{}) {
	return
}

func (station *StationBalance) Settlement() (err error) {
	return
}
