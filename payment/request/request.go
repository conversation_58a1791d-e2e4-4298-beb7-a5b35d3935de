package request

type Refund struct {
	OrderSN     uint   `json:"order_sn" form:"order_sn" `
	UserID      uint   `json:"user_id" form:"user_id" `
	Amount      uint   `json:"amount" form:"amount" ` //分单位
	PayTypeID   int    `json:"pay_type_id" form:"pay_type_id" `
	PaySN       string `json:"pay_sn" form:"pay_sn" `
	OrderItemID uint   `json:"order_item_id"`
	Type        uint   `json:"type" form:"type" gorm:"column:type;comment:用来区分什么支付: 30000 数据通-聚合支付（无论pay_type_id是多少都是聚合支付）;"` //目前只有数据通-汇聚支付 此值为30000
}

type NoSepareteRefund struct {
	RefundSN uint `json:"refund_sn" form:"refund_sn" `
	Uid      uint `json:"uid"`
	Amount   uint `json:"amount"`
}

type WeChatPayRequestData struct {
	Uid       uint   `json:"uid" form:"uid" gorm:"column:uid;comment:会员ID;type:bigint;size:100;"`
	PayType   int    `json:"pay_type" form:"pay_type" gorm:"column:pay_type"`
	Amount    uint   `json:"amount" form:"amount" gorm:"column:amount;comment:金额;type:int;"`
	PayInfoID int    `json:"pay_info_id" form:"pay_info_id"`
	Ip        string `json:"ip" form:"ip"`
	Type      string `json:"type" form:"type"` // H5支付场景 示例值：iOS, Android, Wap

	PayCode int `json:"pay_code" form:"pay_code" gorm:"column:pay_code"`
}

// 支付/充值参数
type AggregatePaymentPayRequestData struct {
	Uid       uint   `json:"uid" form:"uid" gorm:"column:uid;comment:会员ID;type:bigint;size:100;"`
	PayType   int    `json:"pay_type" form:"pay_type" gorm:"column:pay_type"`                //充值要使用
	Amount    uint   `json:"amount" form:"amount" gorm:"column:amount;comment:金额;type:int;"` //充值要使用
	PayInfoID int    `json:"pay_info_id" form:"pay_info_id"`                                 //充值要使用--PC微信支付时使用
	Ip        string `json:"ip" form:"ip"`
	Type      string `json:"type" form:"type"`                                //充值要使用 P:PC页面版(仅快捷支付支持)，M:H5页面版（包含微信公众号），W:微信小程序，Z：支付宝小程序，示例值：M
	FrontUrl  string `json:"front_url" form:"front_url"`                      //页面跳转地址(目前仅快捷支付方式支持)， 支付完跳转至商户前端地址
	PayCode   int    `json:"pay_code" form:"pay_code" gorm:"column:pay_code"` //0正常 1小商店
	WxOpenid  string `json:"wx_openid" form:"wx_openid" gorm:"column:wx_openid;comment:微信openid;type:varchar(255);size:255;"`
	IsLogin   int    `json:"is_login"  form:"is_login" gorm:"column:is_login"` //是否是不登录支付0登录 1不登录
}

// 充值
type WeChatRechargeRequestData struct {
	Uid     uint   `json:"uid" form:"uid" gorm:"column:uid;comment:会员ID;type:bigint;size:100;"`
	PayType int    `json:"pay_type" form:"pay_type" gorm:"column:pay_type"` //3微信H5，4微信扫码，5微信6小程序
	Amount  uint   `json:"amount" form:"amount" gorm:"column:amount;comment:金额;type:int;"`
	Ip      string `json:"ip" form:"ip"`     //H5需要
	Type    string `json:"type" form:"type"` // H5支付场景 示例值：iOS, Android, Wap
}

type WeChatNotify struct {
	PayType int  `json:"pay_type" form:"pay_type" gorm:"column:pay_type"`
	PaySN   uint `json:"pay_sn" form:"pay_sn" gorm:"column:pay_sn;comment:编号;"` // 编号
	Type    int  `json:"type" form:"type" gorm:"column:type;comment:类型;"`       //0支付（默认没有参数也是支付） 1充值
}

// 下载微信平台证书参数
type WechatDownloadCert struct {
	MchID                      string `json:"mch_id" form:"mchid"`                                                //商户id
	MchCertificateSerialNumber string `json:"mch_certificate_serial_number" form:"mch_certificate_serial_number"` //证书序列号
	KeyPath                    string `json:"key_path" form:"key_path"`                                           //私钥路径
	PayKey                     string `json:"pay_key" form:"pay_key"`                                             //V3密钥
}
