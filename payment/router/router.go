package router

import (
	"github.com/gin-gonic/gin"
	v1 "payment/api/v1"
)

// 后台
func InitPaymentSuperRoute(Router *gin.RouterGroup) {
	PaymentRouter := Router.Group("payment")
	{
		PaymentRouter.GET("getPayment", v1.GetSuperPayment)                 // 获取支付/充值相关配置数据
		PaymentRouter.POST("downloaderWechatCert", v1.DownloaderWechatCert) //下载平台证书   //微信公众号使用
	}
}

// 前端请求支持的支付方式
func InitPaymentRoute(Router *gin.RouterGroup) {
	PaymentRouter := Router.Group("payment")
	{
		PaymentRouter.POST("getPay", v1.GetPay)                    // 获取支付
		PaymentRouter.POST("getRecharge", v1.GetRecharge)          // 获取充值
		PaymentRouter.POST("getPayment", v1.GetPayment)            // 获取支付/充值相关配置数据
		PaymentRouter.GET("getPaymentConfig", v1.GetPaymentConfig) // 获取支付设置

	}
}
func InitPublicNotifyRoute(Router *gin.RouterGroup) { // 支付统一回调处理
	PaymentRouter := Router.Group("payment")
	{
		PaymentRouter.POST("lakalaPayment", v1.NotifyPayment)                                            // 拉卡拉支付回调
		PaymentRouter.POST("lakalaRecharge", v1.NotifyRecharge)                                          // 拉卡拉充值回调
		PaymentRouter.Any("convergenceMicroPlus", v1.ConvergenceMicroPlus)                               // 汇聚小程序plus支付回调
		PaymentRouter.Any("convergenceMicroPlusRefund", v1.ConvergenceMicroPlusRefund)                   // 汇聚小程序plus退款回调
		PaymentRouter.Any("convergenceMicroPlusSmallShopRefund", v1.ConvergenceMicroPlusSmallShopRefund) // 汇聚小程序plus小商店退款回调
	}
}
