package service

import (
	"context"
	"crypto/x509"
	"errors"
	"flag"
	"fmt"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/downloader"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
	"os"
	"path/filepath"
	request2 "payment/request"
)

var (
	mchID             string
	mchSerialNo       string
	mchPrivateKeyPath string
	mchAPIv3Key       string

	wechatPayCertificatePath string
	outputPath               string
)

const errCodeParamError = 1
const OutputPath = "./data/goSupply/uploads/wechatCert" //微信平台证书下载路径
/**
下载微信平台证书
 */
func Downloader(wechatDownloadCert request2.WechatDownloadCert)(err error,url string,serial string) {
	mchID = wechatDownloadCert.MchID  //商户id
	mchSerialNo = wechatDownloadCert.MchCertificateSerialNumber  //证书序列号
	mchPrivateKeyPath = "./data/goSupply/"+wechatDownloadCert.KeyPath //私钥路径
	mchAPIv3Key = wechatDownloadCert.PayKey  //v3密钥
	//outputPath = "./data/goSupply/uploads/wechatCert"
    outputPath = OutputPath  //证书保存路径
	if err = checkArgs(); err != nil {
		err = errors.New("参数有误"+err.Error())
		return
	}

	ctx := context.Background()
	client, err := createClient(ctx)
	if err != nil {
		err = errors.New("初始化失败"+err.Error())
        return
	}

	d, err := downloader.NewCertificateDownloaderWithClient(ctx, client, mchAPIv3Key)

	if err != nil {
		err = errors.New("下载证书失败"+err.Error())
		return
	}
	//serial := downloader.MgrInstance().GetNewestCertificateSerial(ctx,mchID)
	err,url,serial = saveCertificate(ctx, d)
	if err != nil {
		err = errors.New("保存证书失败"+err.Error())
		return
	}
    return
}

func reportError(message string, err error) {
	_, _ = fmt.Fprintf(os.Stderr, message+" %v\n", err)
}

func printUsageAndExit() {
	_, _ = fmt.Fprintf(os.Stderr, "usage of wechatpay_download_certs:\n")
	flag.PrintDefaults()
	os.Exit(errCodeParamError)
}

type paramError struct {
	name    string
	value   string
	message string
}

// Error 输出 paramError
func (e paramError) Error() string {
	if e.value != "" {
		return fmt.Sprintf("%v(%v) %v", e.name, e.value, e.message)
	}
	return fmt.Sprintf("%v %v", e.name, e.message)
}

// revive:disable:cyclomatic
func checkArgs() error {
	if mchID == "" {
		return paramError{"商户号", mchID, "必传"}
	}

	if mchSerialNo == "" {
		return paramError{"商户证书序列号", mchSerialNo, "必传"}
	}

	if mchPrivateKeyPath == "" {
		return paramError{"商户私钥路径", mchPrivateKeyPath, "必传"}
	}

	fileInfo, err := os.Stat(mchPrivateKeyPath)
	if err != nil {
		return paramError{"商户私钥路径", mchPrivateKeyPath, fmt.Sprintf("有误: %v", err)}
	}
	if fileInfo.IsDir() {
		return paramError{"商户私钥路径", mchPrivateKeyPath, "不是合法的文件路径"}
	}

	if mchAPIv3Key == "" {
		return paramError{"商户APIv3密钥", mchAPIv3Key, "必传"}
	}

	if wechatPayCertificatePath != "" {
		fileInfo, err := os.Stat(wechatPayCertificatePath)
		if err != nil {
			return paramError{"商户平台证书路径", wechatPayCertificatePath, fmt.Sprintf("有误：%v", err)}
		}
		if fileInfo.IsDir() {
			return paramError{"商户平台证书路径", wechatPayCertificatePath, "不是合法的文件路径"}
		}
	}

	err = os.MkdirAll(outputPath, os.ModePerm)
	if err != nil {
		return paramError{"证书下载保存目录", outputPath, fmt.Sprintf("创建失败：%v", err)}
	}

	return nil
}

// revive:enable:cyclomatic

func saveCertificates(ctx context.Context, d *downloader.CertificateDownloader) (err error,filename string) {
	for serialNo, certContent := range d.ExportAll(ctx) {
		filename = fmt.Sprintf("wechatpay_%v.pem", serialNo)
		outputFilePath := filepath.Join(outputPath, filename)
        var f  *os.File
		f, err = os.Create(outputFilePath)
		if err != nil {
			err = errors.New("创建证书文件:失败"+err.Error())
			return
		}

		_, err = f.WriteString(certContent + "\n")
		if err != nil {
			err = errors.New("写入证书到:失败"+err.Error())
			return
		}

		fmt.Printf("写入证书到`%v`成功\n", outputFilePath)
	}
	return
}

func saveCertificate(ctx context.Context, d *downloader.CertificateDownloader) (err error,filename string,serial string) {
	for serialNo, certContent := range d.ExportAll(ctx) {
		filename = fmt.Sprintf("wechatpay_%v.pem", serialNo)
		outputFilePath := filepath.Join(outputPath, filename)
		var f  *os.File
		f, err = os.Create(outputFilePath)
		if err != nil {
			err = errors.New("创建证书文件:失败"+err.Error())
			return
		}

		_, err = f.WriteString(certContent + "\n")
		if err != nil {
			err = errors.New("写入证书到:失败"+err.Error())
			return
		}
		serial = serialNo
		fmt.Printf("写入证书到`%v`成功\n", outputFilePath)
		break
	}
	filename ="/uploads/wechatCert/"+filename
	return
}

func createClient(ctx context.Context) (*core.Client, error) {
	privateKey, err := utils.LoadPrivateKeyWithPath(mchPrivateKeyPath)
	if err != nil {
		return nil, fmt.Errorf("商户私钥有误：%v", err)
	}

	var client *core.Client
	if wechatPayCertificatePath != "" {
		wechatPayCertificate, err := utils.LoadCertificateWithPath(wechatPayCertificatePath)
		if err != nil {
			return nil, fmt.Errorf("平台证书有误：%v", err)
		}
		client, err = core.NewClient(
			ctx, option.WithMerchantCredential(mchID, mchSerialNo, privateKey),
			option.WithWechatPayCertificate([]*x509.Certificate{wechatPayCertificate}),
		)
		if err != nil {
			return nil, fmt.Errorf("创建 Client 失败：%v", err)
		}
	} else {
		client, err = core.NewClient(
			ctx, option.WithMerchantCredential(mchID, mchSerialNo, privateKey), option.WithoutValidator(),
		)
		if err != nil {
			return nil, fmt.Errorf("创建 Client 失败：%v", err)
		}
	}

	return client, nil
}
