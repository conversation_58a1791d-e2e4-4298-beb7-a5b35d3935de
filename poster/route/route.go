package route

import (
	"github.com/gin-gonic/gin"
	fv1 "poster/api/f/v1"
	v1 "poster/api/v1"
)

// InitAdminPrivateRouter 后端私有路由
func InitAdminPrivateRouter(Router *gin.RouterGroup) {
	PosterRouter := Router.Group("poster")
	{
		// 海报详情
		PosterRouter.GET("detail", v1.GetPosterDetail)
		// 海报管理
		PosterRouter.GET("list", v1.GetPosterList)
		// 添加海报
		PosterRouter.POST("add", v1.AddPoster)
		// 更新海报
		PosterRouter.PUT("update", v1.UpdatePoster)
		// 删除海报
		PosterRouter.DELETE("delete", v1.DeletePoster)
		// 修改海报状态
		PosterRouter.PUT("changeStatus", v1.ChangePosterStatus)
	}

	TagRouter := Router.Group("poster/tag")
	{
		// 标签详情
		TagRouter.GET("detail", v1.GetTagDetail)
		// 标签列表
		TagRouter.GET("list", v1.GetTagList)
		// 添加标签
		TagRouter.POST("add", v1.AddTag)
		// 更新标签
		TagRouter.PUT("update", v1.UpdateTag)
		// 删除标签
		TagRouter.DELETE("delete", v1.DeleteTag)
	}
	// 扫码记录
	ScanRecordRouter := Router.Group("poster/scanRecord")
	{
		// 扫码记录列表
		ScanRecordRouter.GET("list", v1.GetScanRecordList)
	}
}

// InitUserPrivateRouter 前端私有路由
func InitUserPrivateRouter(Router *gin.RouterGroup) {
	PosterRouter := Router.Group("poster")
	{
		// 海报管理
		PosterRouter.GET("list", fv1.GetPosterList)
		// 生成海报
		PosterRouter.POST("generate", fv1.GeneratePoster)
		// 海报详情
		PosterRouter.GET("detail", fv1.GetPosterDetail)
		// 上传海报
		PosterRouter.POST("upload", fv1.Upload)
		// 增加海报记录
		PosterRouter.POST("addRecord", fv1.AddPosterRecord)
		// 海报类型
		PosterRouter.GET("types", fv1.GetPosterTypes)
		// 标签列表
		PosterRouter.GET("tags", fv1.GetAllTag)
		// 获取base64图片
		PosterRouter.GET("base", fv1.GetBase64Image)
	}
}

func InitUserPublicRouter(Router *gin.RouterGroup) {
	ProductAlbumApiRouter := Router.Group("poster")
	{
		ProductAlbumApiRouter.POST("uploadtest", fv1.Upload)
	}
}
