package service

import (
	"errors"
	"gorm.io/gorm"
	"poster/model"
	"yz-go/source"
)

// GetPosterRecordByPosterIDAndUserID 通过海报id和会员id获取海报记录
func GetPosterRecordByPosterIDAndUserID(posterID, userID uint) (err error, record model.PosterRecord, isRecord bool) {
	err = source.DB().Model(&model.PosterRecord{}).Where("poster_id = ? AND uid = ?", posterID, userID).First(&record).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	if err != nil {
		return
	}
	if record.ID != 0 {
		isRecord = true
	}
	return
}

// AddPosterRecord 添加海报记录
func AddPosterRecord(record model.PosterRecord) (err error) {
	err = source.DB().Create(&record).Error
	return
}
