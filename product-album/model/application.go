package model

import "yz-go/source"

type Application struct {
	ID               uint             `json:"id"`
	SupplierID       uint             `json:"supplier_id"`
	AppLevelID       uint             `json:"app_level_id"`
	MemberId         int              `json:"memberId" form:"memberId" gorm:"column:member_id;comment:;type:int;size:10;"`
	ApplicationLevel ApplicationLevel `json:"applicationLevel" gorm:"foreignKey:AppLevelID"`
}
type ApplicationLevel struct {
	source.Model
	ServerRadio int `json:"serverRadio" form:"serverRadio" gorm:"column:server_radio;comment:手续费比例(万分之一);type:int;size:10;"` // 手续费比例(万分之一)
}

func (ApplicationLevel) TableName() string {
	return "application_level"
}
func (Application) TableName() string {
	return "application"
}
