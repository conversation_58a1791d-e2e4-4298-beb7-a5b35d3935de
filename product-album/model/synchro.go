package model

import (
	"yz-go/source"
)

func (Synchro) TableName() string {
	return "product_album_synchro"
}

// Synchro 同步供应链共享专辑记录表
type Synchro struct {
	source.Model
	GatherId       uint `json:"gather_id" form:"gather_id" gorm:"index;not null;comment:供应链 ID"`
	GatherAlbumID  uint `json:"gather_album_id" form:"gather_album_id" gorm:"index;not null;comment:供应链专辑ID"`
	ProcureAlbumID uint `json:"procure_album_id" form:"procure_album_id" gorm:"index;not null;comment:导入本地专辑ID"`
}

func (SynchroTag) TableName() string {
	return "product_album_synchro_tag"
}

// SynchroTag 同步供应链共享专辑标签记录表
type SynchroTag struct {
	source.Model
	GatherId     uint `json:"gather_id" form:"gather_id" gorm:"index;not null;comment:供应链 ID"`
	GatherTagID  uint `json:"gather_tag_id" form:"gather_tag_id" gorm:"index;not null;comment:供应链标签ID"`
	ProcureTagID uint `json:"procure_tag_id" form:"procure_tag_id" gorm:"index;not null;comment:导入本地标签ID"`
}
