package service

import (
	"encoding/json"
	"fmt"
	"product-album/request"
	"testing"
	yzRequest "yz-go/request"
)

func TestGetGatherList(t *testing.T) {
	search := request.GatherListSearch{
		Name: "",
	}
	err, list := GetGatherList(search)
	if err != nil {
		fmt.Print(err)
	}

	fmt.Println(list)
}

func TestGetGatherAlbumList(t *testing.T) {
	search := request.GatherAlbumListSearch{
		PageInfo: yzRequest.PageInfo{
			Page:     1,
			PageSize: 10,
		},
		GatherID:  41,
		AlbumName: "",
		IsImport:  1,
	}
	err, list, total := GetGatherAlbumList(search)
	if err != nil {
		fmt.Print(err)
	}

	fmt.Println(list, total)
}

func TestGetGatherAlbumGoodsList(t *testing.T) {
	appGatherAlbumListSearch := request.AppGatherAlbumDetailSearch{
		AlbumId: 11,
		PageInfo: yzRequest.PageInfo{
			Page:     1,
			PageSize: 10,
		},
	}
	search := request.GatherAlbumGoodsListSearch{
		AppGatherAlbumDetailSearch: appGatherAlbumListSearch,
		GatherId:                   41,
	}
	err, album, list, total := GetGatherAlbumGoodsList(search)
	if err != nil {
		fmt.Print(err)
	}

	// 将结构体序列化为JSON格式的字节切片
	albumJson, err := json.Marshal(album)
	if err != nil {
		fmt.Println("album序列化失败：", err)
		return
	}
	listJson, err := json.Marshal(list)
	if err != nil {
		fmt.Println("list序列化失败：", err)
		return
	}
	// 打印JSON格式的字节切片
	fmt.Println(string(albumJson), string(listJson), total)
	fmt.Println(string(listJson))
	fmt.Println(total)
}

func TestUpdateGatherAlbum(t *testing.T) {
	var albumId uint = 2
	var gatherId uint = 41
	if err := UpdateGatherAlbum(gatherId, albumId); err != nil {
		fmt.Print(err)
	}

	fmt.Println("ok")
}

func TestImportGatherAlbum(t *testing.T) {
	params := request.ImportGatherAlbumParams{
		Ids:      []uint{1, 2},
		Uid:      2,
		GatherId: 41,
	}

	if err := ImportGatherAlbum(params); err != nil {
		fmt.Print(err)
	}

	fmt.Println("ok")
}

func TestGetAlbumBindMap(t *testing.T) {
	var id uint = 41

	albumBindMap, err := getAlbumBindMap(id)
	if err != nil {
		fmt.Print(err)
	}

	fmt.Println(albumBindMap)
}
