package v1

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"product/model"
	"product/request"
	"product/service"
	"strings"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/source"
)

func GetProductVerifyList(c *gin.Context) {
	var pageInfo request.ProductSearch
	err := c.Should<PERSON>ind<PERSON>uery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//handleProductSkuNames()
	if err, list, total := service.GetProductVerifyList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
func handleProductSkuNames() {
	var products []service.ProductForUpdate
	var cursor uint
	for {
		source.DB().Preload("Skus", "title = ''").Where("gather_supply_id = ?", 15).Where("id > ?", cursor).Limit(5000).Find(&products)
		if len(products) == 0 {
			break
		}
		var updateSkuMap []map[string]interface{}
		var err error
		for k, product := range products {
			for _, v := range product.Skus {
				var skuTitle []string
				for _, option := range v.Options {
					skuTitle = append(skuTitle, option.SpecItemName)
				}
				updateSkuRow := make(map[string]interface{})
				updateSkuRow["title"] = strings.Join(skuTitle, "+")
				updateSkuRow["id"] = v.ID
				updateSkuMap = append(updateSkuMap, updateSkuRow)
				if len(updateSkuMap) >= 1000 {
					err = source.BatchUpdate(updateSkuMap, "skus", "")
					if err != nil {
						return
					}
					updateSkuMap = []map[string]interface{}{}
				}
			}

			if k == len(products)-1 {
				cursor = product.ID
			}
		}
		err = source.BatchUpdate(updateSkuMap, "skus", "")
	}

}
func Verify(c *gin.Context) {

	var verifyInfo request.VerifyInfo
	if err := c.ShouldBindJSON(&verifyInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err := service.Verify(verifyInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithMessage("审核成功", c)
	}
}

func VerifyByIds(c *gin.Context) {

	var verifyInfo request.VerifyByIds
	err := c.ShouldBindJSON(&verifyInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err = service.VerifyByIds(verifyInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithMessage("审核成功", c)
	}
}

func ProductTransfer(c *gin.Context) {

	var verifyInfo model.ProductTransfer
	err := c.ShouldBindJSON(&verifyInfo)
	if err != nil {
		log.Log().Error("转移失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err = service.ProductTransfer(verifyInfo); err != nil {
		log.Log().Error("转移失败", zap.Any("err", err))
		yzResponse.FailWithMessage("转移失败", c)
		return
	} else {
		yzResponse.OkWithMessage("转移成功", c)
	}
}
func ProductMerge(c *gin.Context) {

	var verifyInfo service.ProductMerge
	err := c.ShouldBindJSON(&verifyInfo)
	if err != nil {
		log.Log().Error("合并失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err = verifyInfo.Init().Exec().Error; err != nil {
		log.Log().Error("合并失败", zap.Any("err", err))
		yzResponse.FailWithMessage("合并失败", c)
		return
	} else {
		yzResponse.OkWithMessage("合并成功", c)
	}
}
func ProductTransferRecord(c *gin.Context) {
	var pageInfo request.ProductSearch
	err := c.ShouldBindQuery(&pageInfo)

	//var data interface{}
	err, list, total := service.ProductTransferRecord(pageInfo)
	if err != nil {
		log.Log().Error("转移失败", zap.Any("err", err))
		yzResponse.FailWithMessage("转移失败", c)
		return
	} else {
		//yzResponse.OkWithData(data, c)
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
func ProductMergeRecord(c *gin.Context) {
	var pageInfo request.ProductSearch
	err := c.ShouldBindQuery(&pageInfo)

	//var data interface{}
	err, list, total := service.ProductMergeRecord(pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		//yzResponse.OkWithData(data, c)
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func GetProductVerifyCount(c *gin.Context) {

	total, err := service.GetProductVerifyCount()
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(total, c)

	return
}
