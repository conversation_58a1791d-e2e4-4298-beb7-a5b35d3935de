package cron

import (
	"context"
	"fmt"
	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"
	"product/model"
	"product/service"
	"time"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func ProductSyncHandle() {
	task := cron.Task{
		Key:  "productSync",
		Name: "商品增量同步",
		Spec: "0 */1 * * * *", // 每1分钟执行一次
		Handle: func(task cron.Task) {
			ProductIncrementalSyncWithDeleted() // 使用包含删除处理的完整版本
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

// ProductIncrementalSyncCron 商品增量同步定时任务
func ProductIncrementalSyncCron() {
	startTime := time.Now()
	log.Log().Info("开始执行商品增量同步任务")

	// 获取上次同步时间
	lastSyncTime, err := getLastSyncTime()
	if err != nil {
		log.Log().Error("获取上次同步时间失败", zap.Error(err))
		return
	}

	// 查询需要同步的商品数量
	var updateCount int64
	err = source.DB().Model(&model.Product{}).
		Where("updated_at > ?", lastSyncTime).
		Where("deleted_at IS NULL").
		Count(&updateCount).Error
	if err != nil {
		log.Log().Error("查询更新商品数量失败", zap.Error(err))
		return
	}

	if updateCount == 0 {
		log.Log().Info("没有需要同步的商品")
		return
	}

	log.Log().Info("发现需要同步的商品", zap.Int64("数量", updateCount))

	// 执行增量同步
	err = executeIncrementalSync(lastSyncTime)
	if err != nil {
		log.Log().Error("增量同步执行失败", zap.Error(err))
		return
	}

	// 更新最后同步时间
	err = updateLastSyncTime(startTime)
	if err != nil {
		log.Log().Error("更新同步时间失败", zap.Error(err))
		return
	}

	duration := time.Since(startTime)
	log.Log().Info("商品增量同步任务完成",
		zap.Int64("同步商品数量", updateCount),
		zap.Duration("耗时", duration))
}

// getLastSyncTime 获取上次同步时间
func getLastSyncTime() (*source.LocalTime, error) {
	ctx := context.Background()

	// 从Redis获取上次同步时间
	lastSyncStr, err := source.Redis().Get(ctx, "product_last_sync_time").Result()
	if err != nil {
		// 如果Redis中没有记录，使用1小时前作为初始时间
		oneHourAgo := time.Now().Add(-1 * time.Hour)
		localTime := source.LocalTime{Time: oneHourAgo}
		return &localTime, nil
	}

	// 解析时间字符串
	lastSyncTime, err := time.Parse(time.RFC3339, lastSyncStr)
	if err != nil {
		// 解析失败，使用1小时前作为默认时间
		oneHourAgo := time.Now().Add(-1 * time.Hour)
		localTime := source.LocalTime{Time: oneHourAgo}
		return &localTime, nil
	}

	localTime := source.LocalTime{Time: lastSyncTime}
	return &localTime, nil
}

// updateLastSyncTime 更新最后同步时间
func updateLastSyncTime(syncTime time.Time) error {
	ctx := context.Background()

	// 将时间存储到Redis
	timeStr := syncTime.Format(time.RFC3339)
	err := source.Redis().Set(ctx, "product_last_sync_time", timeStr, 7*24*time.Hour).Err()
	if err != nil {
		return err
	}

	log.Log().Info("更新同步时间成功", zap.String("时间", timeStr))
	return nil
}

// executeIncrementalSync 执行增量同步
func executeIncrementalSync(lastSyncTime *source.LocalTime) error {
	// 分批处理商品，避免一次性处理过多数据
	const batchSize = 100
	offset := 0

	for {
		var products []model.Product
		err := source.DB().
			Where("updated_at > ?", lastSyncTime).
			Where("deleted_at IS NULL").
			Offset(offset).
			Limit(batchSize).
			Find(&products).Error

		if err != nil {
			return fmt.Errorf("查询商品失败: %v", err)
		}

		if len(products) == 0 {
			break // 没有更多商品了
		}

		// 处理当前批次的商品
		err = processBatchProducts(products)
		if err != nil {
			log.Log().Error("处理商品批次失败",
				zap.Error(err),
				zap.Int("批次大小", len(products)),
				zap.Int("偏移量", offset))
			// 继续处理下一批，不中断整个同步过程
		} else {
			log.Log().Info("处理商品批次成功",
				zap.Int("批次大小", len(products)),
				zap.Int("偏移量", offset))
		}

		offset += batchSize

		// 如果返回的商品数少于批次大小，说明已经是最后一批
		if len(products) < batchSize {
			break
		}

		// 添加短暂延迟，避免对数据库造成过大压力
		time.Sleep(100 * time.Millisecond)
	}

	return nil
}

// processBatchProducts 处理商品批次
func processBatchProducts(products []model.Product) error {
	if len(products) == 0 {
		return nil
	}

	// 使用批量同步提高效率
	err := syncProductsBatch(products)
	if err != nil {
		// 如果批量同步失败，尝试逐个同步
		log.Log().Warn("批量同步失败，尝试逐个同步", zap.Error(err))
		return syncProductsIndividually(products)
	}

	return nil
}

// syncProductsBatch 批量同步商品到ES
func syncProductsBatch(products []model.Product) error {
	if len(products) == 0 {
		return nil
	}

	// 获取ES客户端
	es, err := source.ES()
	if err != nil {
		return fmt.Errorf("获取ES客户端失败: %v", err)
	}

	// 确定ES索引名称
	indexName := "product" + getCurrentProductIndex()

	// 构建批量请求
	bulkRequest := es.Bulk()

	for _, product := range products {
		// 构建商品ES文档
		productDoc, err := buildProductESDocument(product)
		if err != nil {
			log.Log().Error("构建商品ES文档失败",
				zap.Error(err),
				zap.Uint("商品ID", product.ID))
			continue
		}

		// 添加到批量请求
		indexReq := elastic.NewBulkIndexRequest().
			Index(indexName).
			Id(fmt.Sprintf("%d", product.ID)).
			Doc(productDoc)

		bulkRequest = bulkRequest.Add(indexReq)
	}

	// 执行批量请求
	ctx := context.Background()
	bulkResponse, err := bulkRequest.Do(ctx)
	if err != nil {
		return fmt.Errorf("批量同步到ES失败: %v", err)
	}

	// 检查批量操作结果
	if bulkResponse.Errors {
		var failedItems []string
		for _, item := range bulkResponse.Items {
			for action, result := range item {
				if result.Error != nil {
					failedItems = append(failedItems, fmt.Sprintf("%s:%s - %s", action, result.Id, result.Error.Reason))
				}
			}
		}

		if len(failedItems) > 0 {
			log.Log().Error("部分商品同步失败",
				zap.Strings("失败项目", failedItems),
				zap.Int("总数量", len(products)),
				zap.Int("失败数量", len(failedItems)))
		}
	}

	log.Log().Info("批量同步商品到ES成功",
		zap.Int("商品数量", len(products)),
		zap.String("索引名称", indexName),
		zap.Duration("耗时", time.Duration(bulkResponse.Took)*time.Millisecond))

	return nil
}

// syncProductsIndividually 逐个同步商品（批量同步失败时的备用方案）
func syncProductsIndividually(products []model.Product) error {
	var errors []string
	successCount := 0

	for _, product := range products {
		err := syncSingleProduct(product)
		if err != nil {
			errorMsg := fmt.Sprintf("商品ID:%d - %s", product.ID, err.Error())
			errors = append(errors, errorMsg)
			log.Log().Error("同步单个商品失败",
				zap.Error(err),
				zap.Uint("商品ID", product.ID),
				zap.String("商品标题", product.Title))
		} else {
			successCount++
		}
	}

	if len(errors) > 0 {
		log.Log().Error("部分商品逐个同步失败",
			zap.Strings("错误信息", errors),
			zap.Int("成功数量", successCount),
			zap.Int("失败数量", len(errors)))
	}

	log.Log().Info("逐个同步商品完成",
		zap.Int("成功数量", successCount),
		zap.Int("失败数量", len(errors)))

	return nil
}

// syncSingleProduct 同步单个商品到ES
func syncSingleProduct(product model.Product) error {
	// 获取ES客户端
	es, err := source.ES()
	if err != nil {
		return fmt.Errorf("获取ES客户端失败: %v", err)
	}

	// 构建商品ES文档
	productDoc, err := buildProductESDocument(product)
	if err != nil {
		return fmt.Errorf("构建商品ES文档失败: %v", err)
	}

	// 确定ES索引名称（使用当前活跃的索引）
	indexName := "product" + getCurrentProductIndex()

	// 同步到ES
	ctx := context.Background()
	_, err = es.Index().
		Index(indexName).
		Id(fmt.Sprintf("%d", product.ID)).
		BodyJson(productDoc).
		Do(ctx)

	if err != nil {
		return fmt.Errorf("同步商品到ES失败: %v", err)
	}

	log.Log().Debug("同步商品到ES成功",
		zap.Uint("商品ID", product.ID),
		zap.String("商品标题", product.Title),
		zap.String("索引名称", indexName))

	return nil
}

// getCurrentProductIndex 获取当前活跃的商品索引
func getCurrentProductIndex() string {
	ctx := context.Background()

	// 从Redis获取当前索引
	productIndex, err := source.Redis().Get(ctx, "productIndex").Result()
	if err != nil {
		// 如果Redis中没有记录，检查ES中存在的索引
		es, err := source.ES()
		if err != nil {
			log.Log().Error("获取ES客户端失败", zap.Error(err))
			return "0" // 默认返回索引0
		}

		// 检查索引0是否存在
		exists, err := es.IndexExists("product0").Do(context.Background())
		if err != nil {
			log.Log().Error("检查ES索引失败", zap.Error(err))
			return "0"
		}

		if exists {
			productIndex = "0"
		} else {
			// 检查索引1是否存在
			exists, err := es.IndexExists("product1").Do(context.Background())
			if err != nil {
				log.Log().Error("检查ES索引失败", zap.Error(err))
				return "0"
			}
			if exists {
				productIndex = "1"
			} else {
				productIndex = "0" // 默认使用索引0
			}
		}

		// 将索引信息存储到Redis
		source.Redis().Set(ctx, "productIndex", productIndex, 0)
	}

	return productIndex
}

// buildProductESDocument 构建商品ES文档
func buildProductESDocument(product model.Product) (map[string]interface{}, error) {
	// 构建ES文档结构
	doc := map[string]interface{}{
		"id":                 product.ID,
		"title":              product.Title,
		"sub_title":          product.SubTitle,
		"image":              product.Image,
		"images":             product.Images,
		"price":              product.Price,
		"market_price":       product.MarketPrice,
		"cost_price":         product.CostPrice,
		"stock":              product.Stock,
		"sales":              product.Sales,
		"is_display":         product.IsDisplay,
		"is_recommend":       product.IsRecommend,
		"is_hot":             product.IsHot,
		"is_new":             product.IsNew,
		"sort":               product.Sort,
		"category1_id":       product.Category1ID,
		"category2_id":       product.Category2ID,
		"category3_id":       product.Category3ID,
		"brand_id":           product.BrandID,
		"supplier_id":        product.SupplierID,
		"gather_supply_id":   product.GatherSupplyID,
		"description":        product.Description,
		"content":            product.Content,
		"unit":               product.Unit,
		"weight":             product.Weight,
		"volume":             product.Volume,
		"virtual_sales":      product.VirtualSales,
		"warning_stock":      product.WarningStock,
		"keywords":           product.Keywords,
		"bar_code":           product.BarCode,
		"is_show_stock":      product.IsShowStock,
		"is_sub":             product.IsSub,
		"is_integral":        product.IsIntegral,
		"integral_num":       product.IntegralNum,
		"is_member_discount": product.IsMemberDiscount,
		"is_postage":         product.IsPostage,
		"is_good":            product.IsGood,
		"is_benefit":         product.IsBenefit,
		"ficti":              product.Ficti,
		"browse":             product.Browse,
		"code_path":          product.CodePath,
		"soure_link":         product.SoureLink,
		"video_link":         product.VideoLink,
		"temp_id":            product.TempID,
		"spec_type":          product.SpecType,
		"activity":           product.Activity,
		"attrs":              product.Attrs,
		"created_at":         product.CreatedAt,
		"updated_at":         product.UpdatedAt,
	}

	return doc, nil
}

// syncDeletedProducts 同步已删除的商品（从ES中移除）
func syncDeletedProducts(lastSyncTime *source.LocalTime) error {
	// 查询在指定时间后被软删除的商品
	var deletedProducts []model.Product
	err := source.DB().Unscoped().
		Where("updated_at > ?", lastSyncTime).
		Where("deleted_at IS NOT NULL").
		Find(&deletedProducts).Error

	if err != nil {
		return fmt.Errorf("查询已删除商品失败: %v", err)
	}

	if len(deletedProducts) == 0 {
		return nil
	}

	// 获取ES客户端
	es, err := source.ES()
	if err != nil {
		return fmt.Errorf("获取ES客户端失败: %v", err)
	}

	// 确定ES索引名称
	indexName := "product" + getCurrentProductIndex()

	// 构建批量删除请求
	bulkRequest := es.Bulk()

	for _, product := range deletedProducts {
		deleteReq := elastic.NewBulkDeleteRequest().
			Index(indexName).
			Id(fmt.Sprintf("%d", product.ID))

		bulkRequest = bulkRequest.Add(deleteReq)
	}

	// 执行批量删除
	ctx := context.Background()
	bulkResponse, err := bulkRequest.Do(ctx)
	if err != nil {
		return fmt.Errorf("批量删除ES文档失败: %v", err)
	}

	// 检查删除结果
	if bulkResponse.Errors {
		var failedItems []string
		for _, item := range bulkResponse.Items {
			for action, result := range item {
				if result.Error != nil && result.Error.Type != "not_found" {
					// 忽略not_found错误，因为文档可能已经不存在
					failedItems = append(failedItems, fmt.Sprintf("%s:%s - %s", action, result.Id, result.Error.Reason))
				}
			}
		}

		if len(failedItems) > 0 {
			log.Log().Error("部分商品删除失败",
				zap.Strings("失败项目", failedItems))
		}
	}

	log.Log().Info("批量删除ES中的商品成功",
		zap.Int("删除数量", len(deletedProducts)),
		zap.String("索引名称", indexName))

	return nil
}

// ProductIncrementalSyncWithDeleted 包含删除处理的增量同步
func ProductIncrementalSyncWithDeleted() {
	startTime := time.Now()
	log.Log().Info("开始执行商品增量同步任务（包含删除处理）")

	// 获取上次同步时间
	lastSyncTime, err := getLastSyncTime()
	if err != nil {
		log.Log().Error("获取上次同步时间失败", zap.Error(err))
		return
	}

	// 1. 同步已删除的商品
	err = syncDeletedProducts(lastSyncTime)
	if err != nil {
		log.Log().Error("同步已删除商品失败", zap.Error(err))
		// 继续执行，不中断整个同步过程
	}

	// 2. 查询需要同步的商品数量（未删除的）
	var updateCount int64
	err = source.DB().Model(&model.Product{}).
		Where("updated_at > ?", lastSyncTime).
		Where("deleted_at IS NULL").
		Count(&updateCount).Error
	if err != nil {
		log.Log().Error("查询更新商品数量失败", zap.Error(err))
		return
	}

	if updateCount == 0 {
		log.Log().Info("没有需要同步的商品")
		// 即使没有商品需要同步，也要更新同步时间
		updateLastSyncTime(startTime)
		return
	}

	log.Log().Info("发现需要同步的商品", zap.Int64("数量", updateCount))

	// 3. 执行增量同步
	err = executeIncrementalSync(lastSyncTime)
	if err != nil {
		log.Log().Error("增量同步执行失败", zap.Error(err))
		return
	}

	// 4. 更新最后同步时间
	err = updateLastSyncTime(startTime)
	if err != nil {
		log.Log().Error("更新同步时间失败", zap.Error(err))
		return
	}

	duration := time.Since(startTime)
	log.Log().Info("商品增量同步任务完成（包含删除处理）",
		zap.Int64("同步商品数量", updateCount),
		zap.Duration("耗时", duration))
}

// ProductFullSyncHandle 全量同步任务（保留原有功能）
func ProductFullSyncHandle() {
	task := cron.Task{
		Key:  "productFullSync",
		Name: "商品全量同步",
		Spec: "0 0 2 * * *", // 每天凌晨2点执行一次全量同步
		Handle: func(task cron.Task) {
			ProductFullSyncCron()
		},
		Status: cron.DISABLED, // 默认禁用，需要时可以启用
	}
	cron.PushTask(task)
}

// ProductFullSyncCron 全量同步定时任务（原有逻辑）
func ProductFullSyncCron() {
	log.Log().Info("开始执行商品全量同步任务")

	// 调用原有的全量同步逻辑
	err := service.Sync(0) // 0表示全量同步
	if err != nil {
		log.Log().Error("全量同步失败", zap.Error(err))
		return
	}

	log.Log().Info("商品全量同步任务完成")
}
