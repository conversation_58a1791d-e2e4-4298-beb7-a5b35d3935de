package cron

import (
	"context"
	"fmt"
	"github.com/olivere/elastic/v7"
	"product/model"
	"yz-go/common_data"
	"yz-go/cron"
	"yz-go/source"
	"yz-go/utils"
)

func ProductSyncHandle() {
	task := cron.Task{
		Key:  "productSync",
		Name: "商品自动同步",
		//Spec: "45 1/10 * * * *",
		Spec: "34 34 2 * * *",
		//Spec: "0 */1 * * * *",
		Handle: func(task cron.Task) {
			ProductCheckCron()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func ProductCheckCron() {
	es, err := source.ES()
	if err != nil {
		return
	}

	var productTotal int64
	err = source.DB().Model(&model.Product{}).Where("deleted_at is NULL").Count(&productTotal).Error
	if err != nil {
		return
	}
	boolQ := elastic.NewBoolQuery()
	total, err := es.Count("product" + common_data.GetOldProductIndex()).Query(boolQ).Do(context.Background())
	if err != nil || total != productTotal {
		fmt.Println("开始自动执行商品同步任务")
		err, _ := utils.Post("http://127.0.0.1:8888/product/autoSync", nil, nil)
		if err != nil {
			return
		}
	} else {
		fmt.Println("无需执行商品同步任务")
	}
	return
}
