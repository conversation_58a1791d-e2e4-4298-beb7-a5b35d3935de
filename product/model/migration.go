package model

import (
	notify_mq "application/notify-mq"
	_ "embed"
	"encoding/json"
	"fmt"
	"gin-vue-admin/admin/model"
	"gin-vue-admin/cmd/gva"
	"go.uber.org/zap"
	"io/ioutil"
	"product/mq"
	"strconv"
	"time"
	"yz-go/component/log"
	"yz-go/source"
)

//go:embed menu.json
var menu string

func Migrate() (err error) {
	// 菜单，角色菜单
	menus := []model.SysMenu{}
	menuJson := menu
	err = json.Unmarshal([]byte(menuJson), &menus)

	model.GVA_MENUS = append(model.GVA_MENUS, menus...)
	if err != nil {
		return
	}
	err = source.DB().AutoMigrate(
		ProductModel{},
		ProductUnit{},
		SkuModel{},
		Spec{},
		SpecItemModel{},
		CollectionModel{},
		CollectionProductModel{},
		Storage{},
		ProductVerifyModel{},
		ProductTransferRecordModel{},
		TopicModel{},
		TopicProduct{},
		ProductExportRecord{},
		SkuSpecItem{},
		ProductMergeRecordModel{},
	)
	if err != nil {
		return
	}

	var productUnitTotal int64
	// 执行商品单位迁移
	if err = source.DB().Model(&ProductUnit{}).Count(&productUnitTotal).Error; err != nil {
		return
	}
	if productUnitTotal == 0 {
		// 从商品表分组查询已存在的单位

		var units []string
		if err = source.DB().Model(&ProductModel{}).Where("unit <> ''").Group("unit").Pluck("unit", &units).Error; err != nil {
			return
		}

		var productUnits []ProductUnit
		for _, item := range units {
			productUnits = append(productUnits, ProductUnit{
				Title: item,
			})
		}

		// 插入商品单位表
		if len(productUnits) > 0 {
			if err = source.DB().Create(&productUnits).Error; err != nil {
				return
			}
		}
	}

	//var goods []Product

	//if source.DB().Migrator().HasTable(&Product{}) {
	//	if source.DB().Migrator().HasColumn(&Product{}, "deleted_at") && source.DB().Migrator().HasColumn(&Product{}, "gallery") && source.DB().Migrator().HasColumn(&Product{}, "id") {
	//		err = source.DB().Select("id,gallery").Where("deleted_at is NULL").Find(&goods).Error
	//		if err != nil {
	//			return
	//		}
	//		var updateGoodsGalleryMap []map[string]interface{}
	//		for _, good := range goods {
	//			if len(good.Gallery) > 9 {
	//				var i = 1
	//				var gallery = Gallery{}
	//				for _, g := range good.Gallery {
	//					if i > 9 {
	//						break
	//					}
	//					gallery = append(gallery, g)
	//					i++
	//				}
	//				var updateGoodsGalleryItem = make(map[string]interface{})
	//				updateGoodsGalleryItem["id"] = good.ID
	//				updateGoodsGalleryItem["gallery"], err = json.Marshal(gallery)
	//				updateGoodsGalleryMap = append(updateGoodsGalleryMap, updateGoodsGalleryItem)
	//			}
	//		}
	//		err = source.BatchUpdate(updateGoodsGalleryMap, "products", "")
	//	}
	//}

	//initSales()
	//initMinPrice()
	//err = SetProductName()
	//err = SetProductCategory()
	//err = ChangeProductCategoryIds()
	//err = InitProductProfitRate()
	//err = InitProductProfitRateV2()
	//err = InitProductIsBill()
	targetTime, _ := time.Parse("2006-01-02 15:04:05", "2025-05-19 18:00:00")
	if gva.GlobalAuth.EncryptID == "gOA6Z" && time.Now().Before(targetTime) {
		//七件事专用
		go PushDeleteGoodsMessages()
	}
	return
}
func PushDeleteGoodsMessages() {
	log.Log().Info("开始执行七件事删除商品消息推送")
	var productsIds []uint
	source.DB().Model(&Product{}).Unscoped().Where("deleted_at > '2025-04-30 18:00:00'").Pluck("id", &productsIds)
	for _, v := range productsIds {
		err := notify_mq.PublishMessage(v, mq.Delete, 0, 0)
		if err != nil {
			log.Log().Error("推送删除商品消息失败", zap.Any("err", err))
		}
	}
	log.Log().Info("结束执行七件事删除商品消息推送")
}
func InitProductIsBill() (err error) {
	var count int64
	err = source.DB().Model(&Product{}).Where("is_bill is not null").Count(&count).Error
	if err != nil {
		return
	}
	if count == 0 {
		var products []Product
		err = source.DB().Select("id,bill_position,tax_code").Where("is_plugin = 0").Where("is_bill is null").Where("bill_position = 1").Find(&products).Error
		if err != nil {
			return
		}

		var products2 []Product
		err = source.DB().Select("id,bill_position,tax_code").Preload("Skus").Where("is_plugin = 0").Where("is_bill is null").Where("bill_position = 2").Find(&products2).Error
		if err != nil {
			return
		}

		var productUpdateMap []map[string]interface{}
		for _, v := range products {

			var productUpdateMapItem = make(map[string]interface{})
			productUpdateMapItem["id"] = v.ID
			productUpdateMapItem["is_bill"] = 0
			if v.TaxCode != "" {
				productUpdateMapItem["is_bill"] = 1
			}
			productUpdateMap = append(productUpdateMap, productUpdateMapItem)

		}

		for _, v := range products2 {

			var productUpdateMapItem = make(map[string]interface{})
			productUpdateMapItem["id"] = v.ID
			productUpdateMapItem["is_bill"] = 0
			productUpdateMapItem["updated_at"] = time.Now().Format("2006-01-02 15:04:05")

			for _, vsku := range v.Skus {
				if vsku.TaxCode != "" {
					productUpdateMapItem["is_bill"] = 1
				}
			}

			productUpdateMap = append(productUpdateMap, productUpdateMapItem)

		}
		if len(productUpdateMap) > 0 {

			err = source.BatchUpdate(productUpdateMap, "products", "id")
			if err != nil {
				return
			}
		}
	}

	return
}
func InitProductProfitRate() (err error) {
	var total int64
	err = source.DB().Model(&Product{}).Where("profit_rate is null").Count(&total).Error
	if err != nil {
		return
	}
	var pages = int(total/1000) + 1
	for i := 1; i <= pages; i++ {
		var products []Product
		err = source.DB().Select("id,price,guide_price").Limit(1000).Preload("Skus").Where("profit_rate is null").Find(&products).Error
		if err != nil {
			return
		}

		var productUpdateMap []map[string]interface{}
		for _, v := range products {
			var maxPrice uint
			var minPrice uint

			var maxGuidePrice uint
			var minGuidePrice uint

			var minProfitRate float64 = 0
			var maxProfitRate float64 = 0
			for pk, psku := range v.Skus {
				if psku.GuidePrice > 0 {
					v.Skus[pk].ProfitRate = decimal((float64(psku.GuidePrice) - float64(psku.Price)) / float64(psku.GuidePrice) * 100)
				} else {
					v.Skus[pk].ProfitRate = 0
				}

			}
			for sk, sku := range v.Skus {
				if sku.Price > maxPrice {
					maxPrice = sku.Price
				}
				if minPrice == 0 || sku.Price <= minPrice {
					minPrice = sku.Price
				}
				if sku.GuidePrice > maxGuidePrice {
					maxGuidePrice = sku.GuidePrice
				}
				if minGuidePrice == 0 || sku.GuidePrice <= minGuidePrice {
					minGuidePrice = sku.GuidePrice
				}

				if sku.ProfitRate > maxProfitRate {
					maxProfitRate = sku.ProfitRate
				}
				if sk == 0 {
					minProfitRate = sku.ProfitRate
				}
				if sku.ProfitRate <= minProfitRate {
					minProfitRate = sku.ProfitRate
				}

			}

			var productUpdateMapItem = make(map[string]interface{})
			productUpdateMapItem["id"] = v.ID
			productUpdateMapItem["profit_rate"] = minProfitRate
			productUpdateMapItem["updated_at"] = time.Now().Format("2006-01-02 15:04:05")
			productUpdateMap = append(productUpdateMap, productUpdateMapItem)

		}
		if len(productUpdateMap) > 0 {

			err = source.BatchUpdate(productUpdateMap, "products", "id")
			if err != nil {
				return
			}
		}
	}

	return
}

func InitProductProfitRateV2() (err error) {
	var products []Product
	err = source.DB().Model(&Product{}).Preload("Skus").Where("gather_supply_id = 25 and jushuitan_distributor_co_id = 12632290 and profit_rate>=44").Find(&products).Error
	if err != nil {
		return
	}
	var productUpdateMap []map[string]interface{}
	for _, v := range products {

		var minProfitRate float64 = 0
		var maxProfitRate float64 = 0
		for pk, psku := range v.Skus {
			if psku.GuidePrice > 0 {
				v.Skus[pk].ProfitRate = decimal((float64(psku.GuidePrice) - float64(psku.Price)) / float64(psku.GuidePrice) * 100)
			} else {
				v.Skus[pk].ProfitRate = 0
			}

		}
		for sk, sku := range v.Skus {

			if sku.ProfitRate > maxProfitRate {
				maxProfitRate = sku.ProfitRate
			}
			if sk == 0 {
				minProfitRate = sku.ProfitRate
			}
			if sku.ProfitRate <= minProfitRate {
				minProfitRate = sku.ProfitRate
			}

		}

		if maxProfitRate-minProfitRate > 0.1 {
			var productUpdateMapItem = make(map[string]interface{})
			productUpdateMapItem["id"] = v.ID
			productUpdateMapItem["is_display"] = 1
			productUpdateMapItem["is_display"] = 1
			productUpdateMap = append(productUpdateMap, productUpdateMapItem)

		}

	}
	if len(productUpdateMap) > 0 {

		err = source.BatchUpdate(productUpdateMap, "products", "id")
		if err != nil {
			return
		}

	}

	products = []Product{}
	err = source.DB().Model(&Product{}).Preload("Skus").Where("gather_supply_id = 25 and jushuitan_distributor_co_id = 11000008 and profit_rate>=35").Find(&products).Error
	if err != nil {
		return
	}
	productUpdateMap = []map[string]interface{}{}
	for _, v := range products {

		var minProfitRate float64 = 0
		var maxProfitRate float64 = 0
		for pk, psku := range v.Skus {
			if psku.GuidePrice > 0 {
				v.Skus[pk].ProfitRate = decimal((float64(psku.GuidePrice) - float64(psku.Price)) / float64(psku.GuidePrice) * 100)
			} else {
				v.Skus[pk].ProfitRate = 0
			}

		}
		for sk, sku := range v.Skus {

			if sku.ProfitRate > maxProfitRate {
				maxProfitRate = sku.ProfitRate
			}
			if sk == 0 {
				minProfitRate = sku.ProfitRate
			}
			if sku.ProfitRate <= minProfitRate {
				minProfitRate = sku.ProfitRate
			}

		}

		if maxProfitRate-minProfitRate > 0.1 {
			var productUpdateMapItem = make(map[string]interface{})
			productUpdateMapItem["id"] = v.ID
			productUpdateMapItem["is_display"] = 1
			productUpdateMap = append(productUpdateMap, productUpdateMapItem)

		}

	}
	if len(productUpdateMap) > 0 {

		err = source.BatchUpdate(productUpdateMap, "products", "id")
		if err != nil {
			return
		}

	}
	return
}

func decimal(value float64) float64 {
	var err error
	value, err = strconv.ParseFloat(fmt.Sprintf("%0.1f", value), 64)
	if err != nil {
		log.Log().Error("数据转换失败", zap.Any("err", err))
		return 0
	}
	return value
}
func ChangeProductCategoryIds() (err error) {
	var newProducts []Product
	err = source.DB().Select("id,price,category1_id,category2_id,category3_id").Find(&newProducts).Error
	if err != nil {
		return
	}
	var oldProducts []Product
	err = source.DB().Select("id,price,category1_id,category2_id,category3_id").Table("products1").Find(&oldProducts).Error
	if err != nil {
		return
	}
	var oldProductsMap = make(map[uint]Product)
	for _, v := range oldProducts {
		oldProductsMap[v.ID] = v
	}
	var newProductsUpdateMap []map[string]interface{}

	for _, newP := range newProducts {
		updateproductThirdRow := make(map[string]interface{})
		if oldProductsMap[newP.ID].Category1ID != newP.Category1ID || oldProductsMap[newP.ID].Category2ID != newP.Category2ID || oldProductsMap[newP.ID].Category3ID != newP.Category3ID {
			updateproductThirdRow["category1_id"] = oldProductsMap[newP.ID].Category1ID
			updateproductThirdRow["category2_id"] = oldProductsMap[newP.ID].Category2ID
			updateproductThirdRow["category3_id"] = oldProductsMap[newP.ID].Category3ID
			updateproductThirdRow["price"] = oldProductsMap[newP.ID].Price
			updateproductThirdRow["id"] = newP.ID
			newProductsUpdateMap = append(newProductsUpdateMap, updateproductThirdRow)
		}
		if len(newProductsUpdateMap) == 500 {
			err = source.BatchUpdate(newProductsUpdateMap, "products", "id")
			if err != nil {
				return
			}
			newProductsUpdateMap = []map[string]interface{}{}
		}
	}
	err = source.BatchUpdate(newProductsUpdateMap, "products", "id")

	return
}

func initProduct() {
	db := source.DB().Model(&Product{})
	var count int64

	err := db.Count(&count).Error
	if err != nil {
		return
	}
	if count <= 0 {
		Json, err := ioutil.ReadFile("./product.json")
		var category []Product
		json.Unmarshal([]byte(Json), &category)
		err = source.DB().Debug().Create(&category).Error
		if err != nil {
			return
		}
	} else {
		return
	}

}

type ProductSales struct {
	source.Model
	OrderItems OrderItems `json:"order_items" gorm:"foreignKey:ProductID"`
}

func (i ProductSales) TableName() string {
	return "products"
}

type OrderItems []OrderItem
type OrderItem struct {
	source.Model
	Qty       uint `json:"qty" form:"qty" gorm:"column:qty;comment:商品数量;"`                      // 商品数量
	ProductID uint `json:"product_id" form:"product_id" gorm:"column:product_id;comment:产品id;"` // 产品
	Order     Order
	OrderID   uint `json:"order_id"`
}
type Order struct {
	source.Model
	Status int `json:"status"`
}

func (i OrderItems) TableName() string {
	return "order_items"
}
func initSales() {
	var count int64
	source.DB().Model(&ProductSales{}).Where("deleted_at is NULL").Count(&count)
	for i := 1; i <= int(count)/1000+1; i++ {
		var products []ProductSales
		source.DB().Preload("OrderItems.Order").Limit(1000).Offset(1000 * (i - 1)).Find(&products)
		var updateProductMap []map[string]interface{}
		for _, item := range products {
			var updateItemMap = make(map[string]interface{})
			var sales uint
			for _, orderItem := range item.OrderItems {
				if orderItem.Order.Status >= 1 {
					sales += orderItem.Qty
				}
			}
			if sales == 0 {
				continue
			}
			updateItemMap["id"] = item.ID
			updateItemMap["sales"] = sales
			updateProductMap = append(updateProductMap, updateItemMap)
		}
		if len(updateProductMap) > 0 {
			source.BatchUpdate(updateProductMap, "products", "")

		}
	}

}

func initMinPrice() {
	var count int64
	source.DB().Model(&Product{}).Where("gather_supply_id = ?", 15).Where("deleted_at is NULL").Count(&count)
	for i := 1; i <= int(count)/1000+1; i++ {
		var products []Product
		source.DB().Select("id").Preload("Skus").Where("gather_supply_id = ?", 15).Where("deleted_at is NULL").Limit(1000).Offset(1000 * (i - 1)).Find(&products)
		var updateProductMap []map[string]interface{}
		for _, item := range products {
			var updateItemMap = make(map[string]interface{})
			var maxPrice uint
			var minPrice uint
			for _, sku := range item.Skus {
				if sku.Price > maxPrice {
					maxPrice = sku.Price
				}
				if minPrice == 0 || sku.Price <= minPrice {
					minPrice = sku.Price
				}
			}
			updateItemMap["id"] = item.ID
			updateItemMap["min_price"] = minPrice
			updateItemMap["max_price"] = maxPrice
			updateProductMap = append(updateProductMap, updateItemMap)
		}
		if len(updateProductMap) > 0 {
			source.BatchUpdate(updateProductMap, "products", "id")

		}
	}

}

type Category struct {
	ID        uint   `json:"id"`
	Name      string `json:"name" form:"name"`
	Image     string `json:"image" form:"image"`
	Level     int    `json:"level" form:"level"`
	ParentID  uint   `json:"parent_id"`
	IsDisplay int    `json:"is_display"`
}
type ShopCategory struct {
	CatID    uint           `json:"cat_id"`
	ParentID uint           `json:"parent_id"`
	Name     string         `json:"name"`
	CatClass int            `json:"cat_class"`
	State    int            `json:"state"`
	Children []ShopCategory `json:"children" gorm:"foreignKey:ParentID;references:CatID"` //子分类
}

func (shopCate ShopCategory) TableName() string {
	return "shop_category"
}
func SetProductName() (err error) {
	var categories []Category
	err = source.DB().Find(&categories).Error
	if err != nil {
		return
	}
	var updateThirdCate []map[string]interface{}
	for _, tc := range categories {
		updateThirdCateRow := make(map[string]interface{})
		updateThirdCateRow["id"] = tc.ID
		updateThirdCateRow["name"] = tc.Name + "111"
		updateThirdCate = append(updateThirdCate, updateThirdCateRow)
	}
	err = source.BatchUpdate(updateThirdCate, "categories", "id")
	return
}
func SetProductCategory() (err error) {
	var shopCate []ShopCategory
	err = source.DB().Find(&shopCate).Error
	for k, sc := range shopCate {
		shopCate[k].CatClass = sc.CatClass + 1
	}
	list := paresCategory(shopCate, 0, 0, 3)
	for _, v := range list {
		var category Category
		category.ParentID = 0
		category.Name = v.Name
		category.Level = v.CatClass
		category.IsDisplay = v.State
		err = source.DB().Create(&category).Error
		if err != nil {
			fmt.Println(err.Error())
		}
		for _, child := range v.Children {
			var childCategory Category
			childCategory.ParentID = category.ID
			childCategory.Name = child.Name
			childCategory.Level = child.CatClass
			childCategory.IsDisplay = child.State
			err = source.DB().Create(&childCategory).Error
			if err != nil {
				fmt.Println(err.Error())
			}
			for _, tchild := range child.Children {
				var tchildCategory Category
				tchildCategory.ParentID = childCategory.ID
				tchildCategory.Name = tchild.Name
				tchildCategory.Level = tchild.CatClass
				tchildCategory.IsDisplay = tchild.State
				err = source.DB().Create(&tchildCategory).Error
				if err != nil {
					fmt.Println(err.Error())
				}
			}
		}

	}
	return
}

func paresCategory(categories []ShopCategory, parentId uint, depth int, maxDepth int) (list []ShopCategory) {
	for _, v1 := range categories {
		if v1.ParentID == parentId {
			v1.Children = []ShopCategory{}
			if depth+1 < maxDepth {
				v1.Children = paresCategory(categories, v1.CatID, depth+1, maxDepth)
				if v1.Children == nil {
					v1.Children = []ShopCategory{}
				}
			}
			list = append(list, v1)
		}

	}
	return
}
