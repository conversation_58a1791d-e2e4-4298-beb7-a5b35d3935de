package response

type TaobaoProduct struct {
	Error string    `json:"error"`
	Item  TBProduct `json:"item"`
}

type ItemImg struct {
	Url string `json:"url"`
}

type PropsAttr struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type SkuItem struct {
	Price          interface{} `json:"price"`
	TotalPrice     uint        `json:"total_price"`
	OrginalPrice   interface{} `json:"orginal_price"`
	PropertiesName string      `json:"properties_name"`
	Quantity       interface{} `json:"quantity"`
	SkuId          interface{} `json:"sku_id"`
}
type SkusItem struct {
	Sku []SkuItem `json:"sku"`
}

type TBProduct struct {
	NumID           string      `json:"num_iid"`
	SuggestivePrice interface{} `json:"suggestive_price"`
	Title           string      `json:"title"`
	DescShort       string      `json:"desc_short"`
	Price           interface{} `json:"price"`
	OrginalPrice    interface{} `json:"orginal_price"`
	Num             interface{} `json:"num"`   //库存
	Sales           interface{} `json:"sales"` //库存
	PostFee         interface{} `json:"post_fee"`
	PicUrl          string      `json:"pic_url"`
	Desc            string      `json:"desc"`        //商品详情
	DescImg         []string    `json:"desc_img"`    //商品详情图片
	ItemImgs        []ItemImg   `json:"item_imgs"`   //商品图片
	ExpressFee      interface{} `json:"express_fee"` //运费
	Unit            string      `json:"unit"`
	Props           []PropsAttr `json:"props"`
	Skus            SkusItem    `json:"skus"`
}
