package service

import (
	"context"
	"errors"
	"fmt"
	"product/model"
	"product/request"
	"product/response"
	"strconv"
	"unicode/utf8"
	levelModel "user/level"
	"user/setting"
	"yz-go/common_data"
	"yz-go/component/log"
	response2 "yz-go/response"
	"yz-go/source"

	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// GetProductCardList
//
// @function: GetProductCardList
// @description: 按条件分页获取Product Card销售信息列表
// @param: info request.ProductCardListSearch
// @return: err error, list []response.Product, total int64
// level 1-10 按照等级列表权重正序  未登录或者默认传1即可
func GetProductCardList(info request.ProductCardListSearch, level int) (err error, list []response2.ProductNew, total int64) {
	if info.MaxPrice != 0 && info.MaxPrice <= info.MinPrice {
		err = errors.New("最大金额不能小于最小金额")
		return
	}
	var profitString = "profit"         //利润筛选字段
	var priceString = "agreement_price" //批发价筛选字段
	//根据等级赋值筛选字段
	if level != 0 {
		profitString = "level_" + strconv.Itoa(level) + "_profit"
		priceString = "level_" + strconv.Itoa(level) + "_price" //不是筛选超级批发价暂时去掉这里
	}
	if info.PageSize == 0 {
		info.PageSize = 10
	}
	if info.Page == 0 {
		info.Page = 1
	}
	if info.Page > 100 {
		info.Page = 100
	}
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	es, err := source.ES()
	if err != nil {
		return
	}
	db := source.DB()
	//db = db.Where("`is_display` = ?", 1)
	db = db.Where("deleted_at is NULL")
	db = db.Where("freeze = 0")

	boolQ := elastic.NewBoolQuery()
	if info.Title != "" {
		//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		boolQ.Should(elastic.NewMatchPhraseQuery("search_title", info.Title).Slop(1), elastic.NewWildcardQuery("title.keyword", "*"+info.Title+"*")).MinimumShouldMatch("1")
	}

	filterQ := elastic.NewBoolQuery()
	if info.Ids != nil {

		IDs := make([]interface{}, len(*info.Ids))
		for index, value := range *info.Ids {
			IDs[index] = value
		}
		filterQ.Must(elastic.NewTermsQuery("id", IDs...))

	}
	if info.IsDisplay != nil {
		filterQ.Must(elastic.NewMatchQuery("is_display", &info.IsDisplay))
		db = db.Where("`is_display` = ?", &info.IsDisplay)
	} else {
		filterQ.Must(elastic.NewMatchQuery("is_display", 1))
		db = db.Where("`is_display` = ?", 1)
	}
	filterQ.Must(elastic.NewMatchQuery("is_plugin", 0))
	if info.SupplierID != 0 {
		//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		filterQ.Must(elastic.NewMatchQuery("supplier_id", info.SupplierID))
	}
	if info.Category1ID != 0 {
		//es = es.Query(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		filterQ.Must(elastic.NewMatchQuery("category_1_id", info.Category1ID))

	}
	if info.Category2ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_2_id", info.Category2ID))
	}
	if info.Category3ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_3_id", info.Category3ID))
	}

	if info.IsRecommend != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_recommend", info.IsRecommend))
	}
	if info.IsNew != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_new", info.IsNew))
	}
	if info.IsHot != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_hot", info.IsHot))
	}
	if info.IsPromotion != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_promotion", info.IsPromotion))
	}
	var userSetting *setting.SysSetting
	err, userSetting = levelModel.GetUserSetting()
	if err != nil {
		return
	}
	if info.SearchByLevelPrice == 1 {
		if userSetting.Value.DiscountType == 0 {
			if info.MinPrice != 0 {
				filterQ.Must(elastic.NewRangeQuery("agreement_price").Gte(info.MinPrice))
			}
			if info.MaxPrice != 0 {
				filterQ.Must(elastic.NewRangeQuery("agreement_price").Lte(info.MaxPrice))
			}
		} else {
			if info.MinPrice != 0 {
				filterQ.Must(elastic.NewRangeQuery("cost_price").Gte(info.MinPrice))
			}
			if info.MaxPrice != 0 {
				filterQ.Must(elastic.NewRangeQuery("cost_price").Lte(info.MaxPrice))
			}
		}
	} else {
		if info.MinPrice != 0 {
			filterQ.Must(elastic.NewRangeQuery("agreement_price").Gte(info.MinPrice))
		}
		if info.MaxPrice != 0 {
			filterQ.Must(elastic.NewRangeQuery("agreement_price").Lte(info.MaxPrice))
		}
	}

	if info.PriceForm != 0 {
		filterQ.Must(elastic.NewRangeQuery("" + priceString + "").Gte(info.PriceForm))
	}
	if info.PriceTo != 0 {
		filterQ.Must(elastic.NewRangeQuery("" + priceString + "").Lte(info.PriceTo))
	}

	if info.ProfitForm != 0 {
		filterQ.Must(elastic.NewRangeQuery("" + profitString + "").Gte(info.ProfitForm))
	}
	if info.ProfitTo != 0 {
		filterQ.Must(elastic.NewRangeQuery("" + profitString + "").Lte(info.ProfitTo))
	}

	if info.OriginRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery("min_origin_rate").Gte(Decimal(float64(*info.OriginRate.From))))
	}
	if info.OriginRate.To != nil && *info.OriginRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("max_origin_rate").Lte(Decimal(float64(*info.OriginRate.To))))
	}

	//排除指定source商品
	filterQ.MustNot(elastic.NewMatchQuery("source", 109))
	// 指定供应链
	if info.GatherSupplyID != nil {
		filterQ.Must(elastic.NewMatchQuery("gather_supplier_id", info.GatherSupplyID))
	} else {
		var specialSupplyIDs []uint
		err, specialSupplyIDs = getSupplyIDs()
		if len(specialSupplyIDs) > 0 {
			supplyIDs := make([]interface{}, len(specialSupplyIDs))
			for index, value := range specialSupplyIDs {
				supplyIDs[index] = value
			}
			filterQ.MustNot(elastic.NewTermsQuery("gather_supplier_id", supplyIDs...))
		}
	}

	boolQ.Filter(filterQ)
	sort := "sort"
	asc := false
	//调整switch与备注一致 增加一些排序兼容H5使用
	// 1综合降2价格降3价格升4销量降5销量升6创建时间降7创建时间升8利润降9利润升
	if info.SortBy != 0 {
		switch info.SortBy {
		case 1:
			//sort = "id"
			//asc = false
			//db = db.Order("id desc")
		case 2:
			sort = "agreement_price"
			asc = true
		case 3:
			sort = "agreement_price"
			asc = false
		case 4:
			sort = "sales"
			asc = false
		case 5:
			sort = "sales"
			asc = true
		case 6:
			sort = "created_at"
			asc = false
		case 7:
			sort = "created_at"
			asc = true
		case 8:
			sort = profitString
			asc = false
		case 9:
			sort = profitString
			asc = true
			break
		case 10:
			sort = "origin_rate"
			asc = false
			break
		case 11:
			sort = "origin_rate"
			asc = true
			break
		}
	}
	//es执行搜索
	total, err = es.Count("product" + common_data.GetOldProductIndex()).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	if total > int64(info.PageSize*100) {
		total = int64(info.PageSize * 100)
	}
	res, err := es.Search("product"+common_data.GetOldProductIndex()).Size(limit).From(offset).Sort(sort, asc).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	var ids []uint

	var productSearchs []ProductElasticSearch
	//获取es搜索结果
	productSearchs, err = GetSearchResult(res)
	var idsString string
	for _, v := range productSearchs {
		idsString += "," + strconv.Itoa(int(v.ID))
		ids = append(ids, v.ID)
	}
	if len(ids) > 0 {
		//按照es查询出来的id顺序进行排序
		idsString = idsString[2:utf8.RuneCountInString(idsString)]
	}

	db = db.Where("id in ?", ids)

	var productCards []response2.ProductNew
	//因为排序会因为in 导致乱了 所以增加order进行排序
	var percent, isDefault int
	err, percent, isDefault = levelModel.GetLevelDiscountPercent(info.UserLevelID)
	if err != nil {
		return
	}

	err = db.Preload("Skus").Preload("Supplier").Order("FIND_IN_SET(id,'" + idsString + "')").Find(&productCards).Error
	for itemKey, item := range productCards {
		productCards[itemKey].NormalPrice = item.Price
		productCards[itemKey].Level = level
		if productCards[itemKey].UserPriceSwitch == 1 {
			var calculateRes bool
			productCards[itemKey].LevelPrice, _, calculateRes = productCards[itemKey].UserPrice.GetProductLevelDiscountPrice(item.Price, info.UserLevelID)
			// 商品没有设置该等级，使用默认折扣
			if calculateRes == false {
				if isDefault == 0 {
					err, productCards[itemKey].LevelPrice = levelModel.GetLevelDiscountAmount(item.Price, uint(item.ExecPrice), percent)
					if err != nil {
						return
					}
					productCards[itemKey].Price = productCards[itemKey].LevelPrice
				} else {
					productCards[itemKey].LevelPrice = item.Price
				}
			}
		} else {
			if isDefault == 0 {
				err, productCards[itemKey].LevelPrice = levelModel.GetLevelDiscountAmount(item.Price, uint(item.ExecPrice), percent)
				if err != nil {
					return
				}
				productCards[itemKey].Price = productCards[itemKey].LevelPrice
			} else {
				productCards[itemKey].LevelPrice = item.Price
			}
		}

		productCards[itemKey].LevelProfit = item.GuidePrice - int(productCards[itemKey].LevelPrice)

		var maxOriginPrice, minOriginPrice, maxGuidePrice, maxActivityPrice, minPrice, maxPrice, minNormalPrice, maxNormalPrice uint
		for skuKey, sku := range item.Skus {
			item.Skus[skuKey].NormalPrice = sku.Price
			if minNormalPrice == 0 || item.Skus[skuKey].NormalPrice <= minNormalPrice {
				minNormalPrice = item.Skus[skuKey].NormalPrice
			}
			if item.Skus[skuKey].NormalPrice > maxNormalPrice {
				maxNormalPrice = item.Skus[skuKey].NormalPrice
			}
			if productCards[itemKey].UserPriceSwitch == 1 {
				var calculateRes bool
				item.Skus[skuKey].Price, _, calculateRes = productCards[itemKey].UserPrice.GetProductLevelDiscountPrice(item.Skus[skuKey].Price, info.UserLevelID)
				// 商品没有设置该等级，使用默认折扣
				if calculateRes == false {
					if isDefault == 0 {
						err, item.Skus[skuKey].Price = levelModel.GetLevelDiscountAmount(item.Skus[skuKey].Price, item.Skus[skuKey].ExecPrice, percent)
						if err != nil {
							return
						}
					}
				}
			} else {
				if isDefault == 0 {
					err, item.Skus[skuKey].Price = levelModel.GetLevelDiscountAmount(item.Skus[skuKey].Price, item.Skus[skuKey].ExecPrice, percent)
					if err != nil {
						return
					}
				}
			}
			if minPrice == 0 || item.Skus[skuKey].Price <= minPrice {
				minPrice = item.Skus[skuKey].Price
			}
			if item.Skus[skuKey].Price > maxPrice {
				maxPrice = item.Skus[skuKey].Price
			}
			if item.Skus[skuKey].OriginPrice > maxOriginPrice {
				maxOriginPrice = item.Skus[skuKey].OriginPrice
			}
			if item.Skus[skuKey].GuidePrice > maxGuidePrice {
				maxGuidePrice = item.Skus[skuKey].GuidePrice
			}
			if item.Skus[skuKey].ActivityPrice > maxActivityPrice {
				maxActivityPrice = item.Skus[skuKey].ActivityPrice
			}
			if minOriginPrice == 0 || item.Skus[skuKey].OriginPrice <= minOriginPrice {
				minOriginPrice = item.Skus[skuKey].OriginPrice
			}
		}
		productCards[itemKey].MaxOriginPrice = maxOriginPrice
		productCards[itemKey].MinOriginPrice = minOriginPrice
		productCards[itemKey].MaxGuidePrice = maxGuidePrice
		productCards[itemKey].MaxActivityPrice = maxActivityPrice
		productCards[itemKey].MinPrice = int(minPrice)
		productCards[itemKey].MaxPrice = int(maxPrice)
		productCards[itemKey].MaxNormalPrice = maxNormalPrice
		productCards[itemKey].MinNormalPrice = minNormalPrice
	}
	return err, productCards, total
}

func GetProductStorageCenterList(info request.ProductStorageSearch, appLevelId uint, PetSupplierID uint, userID uint, userLevel int) (err error, list interface{}, total int64) {
	log.Log().Info("请求数据", zap.Any("params", info))
	limit := info.PageSize
	if limit > 100 {
		limit = 100
	}
	if info.Page > 100 {
		info.Page = 100
	}
	offset := info.PageSize * (info.Page - 1)
	// 获取用户等级排序值
	userLevelSort := levelModel.GetLevelSort(userLevel)
	// 初始化默认值
	var levelKey string
	if userLevelSort >= 1 && userLevelSort <= 10 {
		levelKey = fmt.Sprintf("level_%d", userLevelSort)
	}
	// 根据 info.Type 动态设置值
	if info.Type == "" {
		info.Type = "sort"
	} else if info.Type == "discount" {
		// 如果 userLevelSort 在 1 到 10 的范围内，动态生成列名；否则使用默认值 "sort"
		if levelKey != "" {
			info.Type = levelKey + "_min_discount"
		} else {
			info.Type = "sort"
		}
	}
	// 创建db
	var es *elastic.Client
	es, err = source.ES()
	if err != nil {
		return
	}

	//获取boolQ
	var boolQ *elastic.BoolQuery
	err, boolQ = GetProductStorageBoolQ(info, appLevelId, PetSupplierID, userID, userLevelSort)
	if err != nil {
		return
	}

	if info.CollectionID != 0 {
		var collection model.Collection
		err = source.DB().First(&collection, info.CollectionID).Error
		if err != nil {
			return
		}
		filterQ := elastic.NewBoolQuery()
		filterQ.Must(elastic.NewMatchQuery("is_display", 1))
		if collection.Type == 1 {
			total = int64(collection.Num)
			if info.PageSize*info.Page >= int(total) {
				limit = int(total) - offset
			}
			var relations []uint
			err = source.DB().Model(&model.CollectionProduct{}).Where("collection_id = ?", info.CollectionID).Pluck("product_id", &relations).Error
			if err != nil {
				return
			}
			relationIds := make([]interface{}, len(relations))
			for index, value := range relations {
				relationIds[index] = value
			}
			filterQ.Must(elastic.NewTermsQuery("id", relationIds...))

		} else if collection.Type == 2 {
			total = int64(collection.Filter.CategoryProductNum)
			if info.PageSize*info.Page >= int(total) {
				limit = int(total) - offset
			}
			category1Ids := make([]interface{}, len([]uint(collection.Filter.Category1ID)))
			for index, value := range []uint(collection.Filter.Category1ID) {
				category1Ids[index] = value
			}
			filterQ.Must(elastic.NewTermsQuery("category_1_id", category1Ids...))

			category2Ids := make([]interface{}, len([]uint(collection.Filter.Category2ID)))
			for index, value := range []uint(collection.Filter.Category2ID) {
				category2Ids[index] = value
			}
			filterQ.Must(elastic.NewTermsQuery("category_2_id", category2Ids...))

			category3Ids := make([]interface{}, len([]uint(collection.Filter.Category3ID)))
			for index, value := range []uint(collection.Filter.Category3ID) {
				category3Ids[index] = value
			}
			filterQ.Must(elastic.NewTermsQuery("category_3_id", category3Ids...))

		} else if collection.Type == 3 {
			total = int64(collection.Filter.AttributeProductNum)
			if info.PageSize*info.Page >= int(total) {
				limit = int(total) - offset
			}
			switch collection.Filter.AttributeType {
			case 1:
				filterQ.Must(elastic.NewMatchQuery("is_hot", 1))
				break
			case 2:
				filterQ.Must(elastic.NewMatchQuery("is_recommend", 1))
				break
			case 3:
				filterQ.Must(elastic.NewMatchQuery("is_new", 1))
				break
			case 4:
				filterQ.Must(elastic.NewMatchQuery("is_promotion", 1))
				break
			}

		} else if collection.Type == 4 {
			total = int64(collection.Filter.StatisticProductNum)
			if info.PageSize*info.Page >= int(total) {
				limit = int(total) - offset
			}
			if collection.Filter.StatisticType == 1 {
				info.Type = "sales"
				info.Sort = false
			} else if collection.Filter.StatisticType == 2 {
				info.Type = "created_at"
				info.Sort = false
			}
		}
		boolQ.Filter(filterQ)

	}
	var realTotal int64
	realTotal, err = es.Count("product" + common_data.GetOldProductIndex()).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	if info.CollectionID != 0 {
		if realTotal <= total {
			total = realTotal
		}
	} else {
		total = realTotal
	}

	//es执行搜索
	log.Log().Info("导入数据的详情", zap.Any("info", boolQ))
	res, err := es.Search("product"+common_data.GetOldProductIndex()).Size(limit).From(offset).Sort(info.Type, info.Sort).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	//获取es搜索结果
	var listAll []ProductElasticSearch
	listAll, err = GetSearchResult(res)
	var listCut []ProductElasticSearchCut
	err, listCut = ProductTransAppPrice(listAll, userID, appLevelId, info.AppID, 0)

	return err, listCut, total
}

func GetProductStorageBoolQ(info request.ProductStorageSearch, appLevelId uint, PetSupplierID uint, userID uint, userLevelSort int) (err error, boolQuery *elastic.BoolQuery) {
	log.Log().Info("选品参数", zap.Any("data", info))
	boolQ := elastic.NewBoolQuery()
	// 福禄供应链
	var supply GatherSupply
	err = source.DB().Unscoped().Where("category_id = ?", 98).First(&supply).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查询供应链失败")
		return
	}
	//db := source.DB().Model(&model.Product{})
	//db = db.Where("deleted_at is NULL")
	// 如果有条件搜索 下方会自动创建搜索语句
	//增加这个的原因是客户同时使用商品名称与供应商名称进行筛选时会得到不属于这个供应商的数据，所以增加这个条件
	//屏蔽search_title这个的原因是匹配满足两项刚好 如果三相，容易查询不到
	//单独筛选名称与供应商名称不变
	if info.Title != "" && info.SupplierName != "" {
		titleBool := elastic.NewBoolQuery().Should(
			elastic.NewMatchPhraseQuery("search_title", info.Title).Slop(1),
			elastic.NewWildcardQuery("title.keyword", "*"+info.Title+"*"),
		).MinimumShouldMatch("1")

		supplierBool := elastic.NewBoolQuery().Should(
			elastic.NewMatchPhraseQuery("supplier_name", info.SupplierName).Slop(1),
			elastic.NewMatchPhraseQuery("supplier_shop_name", info.SupplierName).Slop(1),
		).MinimumShouldMatch("1")

		boolQ.Must(titleBool, supplierBool)
	} else if info.Title != "" {
		boolQ.Should(elastic.NewMatchPhraseQuery("search_title", info.Title).Slop(1), elastic.NewWildcardQuery("title.keyword", "*"+info.Title+"*")).MinimumShouldMatch("1")
	} else if info.SupplierName != "" {
		boolQ.Should(
			elastic.NewMatchPhraseQuery("supplier_name", info.SupplierName).Slop(1),
			elastic.NewMatchPhraseQuery("supplier_shop_name", info.SupplierName).Slop(1),
		).MinimumShouldMatch("1")
	}
	if info.IsImport != nil {
		if *info.IsImport == 1 {
			boolQ.Must(elastic.NewMatchQuery("import_apps", strconv.Itoa(int(info.AppID))).Analyzer("whitespace"))
		} else {
			boolQ.MustNot(elastic.NewMatchQuery("import_apps", strconv.Itoa(int(info.AppID))).Analyzer("whitespace"))
		}

	}
	if info.SupplyLineId != "" {
		boolQ.MustNot(elastic.NewWildcardQuery("supply_line.keyword", "*"+info.SupplyLineId+"*"))
		//	boolQ.MustNot(elastic.NewTermQuery("supply_line.keyword", info.SupplyLineId))

	}
	filterQ := elastic.NewBoolQuery()
	if appLevelId > 0 {
		var ids []uint
		err = source.DB().Model(&GatherSupplyApplicationLevel{}).Where("application_level_id = ?", appLevelId).Pluck("gather_supply_id", &ids).Error
		if err != nil {
			return
		}
		var sourceIds []uint
		err = source.DB().Model(&GatherSupplyApplicationLevel{}).Where("application_level_id = ?", appLevelId).Where("gather_supply_id in ?", ids).Pluck("source_id", &sourceIds).Error
		if err != nil {
			return
		}
		ids = append(ids, 0)
		status := make([]interface{}, len(ids))
		for index, value := range ids {
			status[index] = value
		}

		filterQ.Must(elastic.NewTermsQuery("gather_supplier_id", status...))
		//中台和云仓的商品不加限制
		sourceIds = append(sourceIds, 0)
		sourceIds = append(sourceIds, 98)
		statusS := make([]interface{}, len(sourceIds))
		for index, value := range sourceIds {
			statusS[index] = value
		}

		filterQ.Must(elastic.NewTermsQuery("source", statusS...))
		log.Log().Info("GetProductStorageInfoList ", zap.Any("info", sourceIds))
	}
	//千人千价 只查询指定商品
	var user Users
	err = source.DB().Preload("ThousandsPrices.Products").Preload("ThousandsPrices.ThousandsPricesProducts").First(&user, userID).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	if err != nil {
		return
	}
	var thousandsProductIds []uint
	if len(user.ThousandsPrices.Products) > 0 && user.ThousandsPrices.Status == 1 && user.ThousandsPrices.FilterImport == 1 {
		for _, product := range user.ThousandsPrices.Products {
			thousandsProductIds = append(thousandsProductIds, product.ID)
		}
	}
	if len(thousandsProductIds) > 0 {
		thousandsStatus := make([]interface{}, len(thousandsProductIds))
		for indext, valuet := range thousandsProductIds {
			thousandsStatus[indext] = valuet
		}
		filterQ.Must(elastic.NewTermsQuery("id", thousandsStatus...))
	}
	goodsIds := make([]interface{}, len(info.GoodsIds))
	for index, value := range info.GoodsIds {
		goodsIds[index] = value
	}
	if len(info.GoodsIds) > 0 {
		filterQ.Must(elastic.NewTermsQuery("id", goodsIds...))
	}
	filterQ.MustNot(elastic.NewMatchQuery("source", 109))
	var applicationSetting ApplicationSetting
	err, applicationSetting = GetApplicationSetting()
	if err != nil {
		return
	}
	if applicationSetting.Value.MultiPetSupplier == 1 {
		var petSupplierIDs []uint
		err = source.DB().Model(&ApplicationPetSupplier{}).Where("application_id = ?", info.AppID).Pluck("pet_supplier_id", &petSupplierIDs).Error
		if err != nil {
			return
		}
		if len(petSupplierIDs) > 0 {
			petSuppliers := make([]interface{}, len(petSupplierIDs))
			for index, value := range petSupplierIDs {
				petSuppliers[index] = value
			}
			filterQ.Must(elastic.NewTermsQuery("supplier_id", petSuppliers...))

		} else if info.SupplierID != nil {

			filterQ.Must(elastic.NewMatchQuery("supplier_id", info.SupplierID))

		}
	} else {
		if PetSupplierID != 0 {
			filterQ.Must(elastic.NewMatchQuery("supplier_id", PetSupplierID))

		} else if info.SupplierID != nil {

			filterQ.Must(elastic.NewMatchQuery("supplier_id", info.SupplierID))

			//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		}
	}
	var banSupplierIds []uint
	err = source.DB().Model(response.Supplier{}).Where("is_storage = 1").Pluck("id", &banSupplierIds).Error
	if err != nil {
		return
	}
	if len(banSupplierIds) > 0 {
		banSuppliers := make([]interface{}, len(banSupplierIds))
		for index, value := range banSupplierIds {
			banSuppliers[index] = value
		}
		filterQ.MustNot(elastic.NewTermsQuery("supplier_id", banSuppliers...))
	}
	if info.Category1ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_1_id", info.Category1ID))

	}
	if info.Category2ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_2_id", info.Category2ID))
	}
	if info.Category3ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_3_id", info.Category3ID))
	}
	if info.Source != nil {
		filterQ.Must(elastic.NewMatchQuery("source", &info.Source))
	} else {
		//filterQ.MustNot(elastic.NewMatchQuery("source", 99))
	}
	if info.IsTaxLogo != nil {
		// 等于3的时候不用查询，没有‘未设置’状态（韦平原型设计的有‘未设置’状态）
		if *info.IsTaxLogo == 1 || *info.IsTaxLogo == 2 {
			filterQ.Must(elastic.NewMatchQuery("is_tax_logo", info.IsTaxLogo))
		}
	}
	if info.TaxRate != nil {
		orQuery := elastic.NewBoolQuery().Should(
			elastic.NewMatchQuery("tax_rate", info.TaxRate),
			elastic.NewTermQuery("sku_tax_rates", info.TaxRate),
		)
		filterQ.Must(orQuery)
	}
	if info.AlbumId != nil {
		filterQ.Must(elastic.NewTermQuery("album_ids", info.AlbumId))
	}
	if info.CollectId != nil {
		// 查询商品专辑
		var collectionModel model.CollectionModel
		source.DB().Where("id = ?", info.CollectId).First(&collectionModel)

		switch collectionModel.Type {
		// 根据商品专辑设置 筛选绑定商品
		case 1:
			filterQ.Must(elastic.NewTermQuery("collect_ids", info.CollectId))
		// 根据商品专辑设置 按分类筛选商品
		case 2:
			// 将 Category1ID 转换为 []interface{}
			category1ID := make([]interface{}, len(collectionModel.Filter.Category1ID))
			for i, id := range collectionModel.Filter.Category1ID {
				category1ID[i] = id
			}
			category2ID := make([]interface{}, len(collectionModel.Filter.Category2ID))
			for i, id := range collectionModel.Filter.Category2ID {
				category2ID[i] = id
			}
			category3ID := make([]interface{}, len(collectionModel.Filter.Category3ID))
			for i, id := range collectionModel.Filter.Category3ID {
				category3ID[i] = id
			}
			filterQ.Must(elastic.NewTermsQuery("category_1_id", category1ID...))
			filterQ.Must(elastic.NewTermsQuery("category_2_id", category2ID...))
			filterQ.Must(elastic.NewTermsQuery("category_3_id", category3ID...))
		// 根据商品专辑设置 按营销属性筛选商品
		case 3:
			switch collectionModel.Filter.AttributeType {
			case 1:
				filterQ.Must(elastic.NewMatchQuery("is_hot", 1))
			//case 2:
			//	filterQ.Must(elastic.NewMatchQuery("is_recommend", 1))
			case 3:
				filterQ.Must(elastic.NewMatchQuery("is_new", 1))
			case 4:
				filterQ.Must(elastic.NewMatchQuery("is_promotion", 1))
			}
		// 根据商品专辑设置 按商品数据筛选商品
		case 4:
		}
	}
	filterQ.Must(elastic.NewMatchQuery("is_plugin", 0))
	// 指定供应链
	if info.GatherSupplyID != nil {
		var zer = uint(0)
		var zero *uint
		zero = &zer
		if info.GatherSupplyID == zero {
			filterQ.Must(elastic.NewMatchQuery("supplier_id", info.GatherSupplyID))
			filterQ.Must(elastic.NewMatchQuery("gather_supplier_id", info.GatherSupplyID))

		} else {
			filterQ.Must(elastic.NewMatchQuery("gather_supplier_id", info.GatherSupplyID))
		}
	} else {
		if supply.ID != 0 {
			filterQ.MustNot(elastic.NewMatchQuery("gather_supplier_id", supply.ID))
		}
	}
	if info.IsRecommend != nil {
		filterQ.Must(elastic.NewMatchQuery("is_recommend", &info.IsRecommend))
	}
	if info.IsBill != nil {
		filterQ.Must(elastic.NewMatchQuery("is_bill", &info.IsBill))
	}
	if info.IsNew != nil {
		filterQ.Must(elastic.NewMatchQuery("is_new", &info.IsNew))
	}
	if info.IsHot != nil {
		filterQ.Must(elastic.NewMatchQuery("is_hot", &info.IsHot))
	}
	if info.IsPromotion != nil {
		filterQ.Must(elastic.NewMatchQuery("is_promotion", &info.IsPromotion))
	}
	if info.IsDisplay != nil {
		filterQ.Must(elastic.NewMatchQuery("is_display", &info.IsDisplay))
	}
	if info.FreightType == 2 {
		filterQ.MustNot(elastic.NewMatchQuery("freight_type", 3))
	}
	if info.FreightType == 1 {
		filterQ.Must(elastic.NewMatchQuery("freight_type", 3))
	}
	if info.AgreementPrice.From != nil {
		filterQ.Must(elastic.NewRangeQuery("agreement_price").Gte(*info.AgreementPrice.From * 100))
	}
	if info.AgreementPrice.To != nil && *info.AgreementPrice.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("agreement_price").Lte(*info.AgreementPrice.To * 100))
	}
	if info.ActivityPrice.From != nil {
		filterQ.Must(elastic.NewRangeQuery("activity_price").Gte(*info.ActivityPrice.From * 100))
	}
	if info.ActivityPrice.To != nil && *info.ActivityPrice.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("activity_price").Lte(*info.ActivityPrice.To * 100))
	}
	if info.GuidePrice.From != nil {
		filterQ.Must(elastic.NewRangeQuery("guide_price").Gte(*info.GuidePrice.From * 100))
	}
	if info.GuidePrice.To != nil && *info.GuidePrice.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("guide_price").Lte(*info.GuidePrice.To * 100))
	}
	// 定义默认值
	grossRateColumn := "gross_profit_rate"
	marketRateColumn := "market_rate"
	levelMinDiscountColumn := "level_1_min_discount"
	levelMaxDiscountColumn := "level_1_max_discount"
	// 如果 userLevelSort 在 1 到 10 的范围内，动态生成列名
	if userLevelSort >= 1 && userLevelSort <= 10 {
		levelKey := fmt.Sprintf("level_%d", userLevelSort)
		grossRateColumn = levelKey + "_gross_rate"
		marketRateColumn = levelKey + "_profit_rate"
		levelMinDiscountColumn = levelKey + "_min_discount"
		levelMaxDiscountColumn = levelKey + "_max_discount"
	}
	// 折扣范围查询
	if info.Discount.From != nil || (info.Discount.To != nil && *info.Discount.To > 0) {
		// 8000/10000 = 0.8 * 1000 = 800
		// 构建两个独立的范围查询（分别检查 min 和 max 是否在目标区间）
		minInRange := buildRangeQueryForDiscount(levelMinDiscountColumn, info.Discount.From, info.Discount.To)
		maxInRange := buildRangeQueryForDiscount(levelMaxDiscountColumn, info.Discount.From, info.Discount.To)
		// 用 OR 关系组合（满足任意一个即可）
		filterQ.Should(minInRange, maxInRange)
		// 显式声明至少满足一个条件（其实 Should 默认就是 OR）
		filterQ.MinimumShouldMatch("1")
	}

	if info.ProfitRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery("profit_rate").Gte(*info.ProfitRate.From))
	}
	if info.ProfitRate.To != nil && *info.ProfitRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("profit_rate").Lte(*info.ProfitRate.To))
	}
	if info.GrossProfitRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery(grossRateColumn).Gte(*info.GrossProfitRate.From))
	}
	if info.GrossProfitRate.To != nil && *info.GrossProfitRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery(grossRateColumn).Lte(*info.GrossProfitRate.To))
	}
	if info.MarketRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery(marketRateColumn).Gte(*info.MarketRate.From))
	}
	if info.MarketRate.To != nil && *info.MarketRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery(marketRateColumn).Lte(*info.MarketRate.To))
	}
	if info.Profit.From != nil {
		filterQ.Must(elastic.NewRangeQuery("profit").Gte(*info.Profit.From * 100))
	}
	if info.Profit.To != nil && *info.Profit.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("profit").Lte(*info.Profit.To * 100))
	}
	if info.OriginRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery("min_origin_rate").Gte(Decimal(float64(*info.OriginRate.From))))
	}
	if info.OriginRate.To != nil && *info.OriginRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("max_origin_rate").Lte(Decimal(float64(*info.OriginRate.To))))
	}
	if info.ActivityRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery("activity_rate").Gte(float64(*info.ActivityRate.From)))
	}
	if info.ActivityRate.To != nil && *info.ActivityRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("activity_rate").Lte(float64(*info.ActivityRate.To)))

	}
	boolQ.Filter(filterQ)
	return err, boolQ
}

// buildDiscountRangeQuery 构建范围查询
func buildRangeQueryForDiscount(field string, from, to *float64) *elastic.RangeQuery {
	rangeQ := elastic.NewRangeQuery(field)
	if from != nil {
		rangeQ.Gte(*from * 100)
	}
	if to != nil && *to > 0 {
		rangeQ.Lte(*to * 100)
	}
	return rangeQ
}
