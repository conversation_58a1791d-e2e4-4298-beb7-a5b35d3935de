package service

import (
	"product/model"
	"yz-go/source"
)

// SaveSysSetting
//@author: [piexlmax](https://github.com/piexlmax)
//@function: SaveSysSetting
//@description: 保存SysTop记录
//@param: data *setting.SysSetting
//@return: err error
func SaveProductSetting(data model.ProductSetting) (err error) {
	if data.ID != 0{
		err = source.DB().Updates(&data).Error
	}else {
		err = source.DB().Create(&data).Error
	}
	return err
}


func GetProductSetting() (err error, sysSetting model.ProductSetting) {

	err = source.DB().Where("`key` = ?", "product_setting").First(&sysSetting).Error

	return
}