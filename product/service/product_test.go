package service

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"product/model"
	"product/request"
	"reflect"
	"strings"
	"testing"
	"time"
	yzRequest "yz-go/request"
	response2 "yz-go/response"
	"yz-go/source"
)

func TestName(t *testing.T) {
	pathStr := "/Users/<USER>/Downloads/supply2/"
	//dirs, err := os.ReadDir("/Users/<USER>/Downloads/supply2/")
	//for _, dir := range dirs {
	//	println(dir.Info())
	//}
	var sqlFiles []string
	var strucFiles []string

	err := filepath.Walk(pathStr, func(path string, info os.FileInfo, err error) error {
		if strings.Contains(path, ".DS_Store") {
			return nil
		}
		if strings.Contains(path, "structure") {
			strucFiles = append(strucFiles, path)
			return nil
		}
		if strings.Contains(path, ".sql") {
			sqlFiles = append(sqlFiles, path)
		}
		return nil
	})
	for _, file := range strucFiles {
		break
		fmt.Println(file)
		c, ioErr := os.ReadFile(file)
		if ioErr != nil {
			// handle error.
		}
		sqls := string(c)
		sqlArr := strings.Split(sqls, ";\n")
		for _, sql := range sqlArr {
			sql = strings.TrimSpace(sql)
			if sql == "" {
				continue
			}
			err = source.DB().Exec(sql).Error
			if err != nil {
				log.Println("数据库导入失败:" + file + err.Error())
				return
			} else {
				log.Println(sql, "\t success!")
			}
		}
	}
	r := true
	for _, file := range sqlFiles {
		if strings.Contains(file, "sys_operation_records") {
			r = false
		}
		if r {
			continue
		}
		fmt.Println(file)
		c, ioErr := os.ReadFile(file)
		if ioErr != nil {
			// handle error.
		}
		sqls := string(c)
		sqlArr := strings.Split(sqls, ";\nINSERT")
		for i, sql := range sqlArr {
			sql = strings.TrimSpace(sql)
			if i > 0 {
				sql = "INSERT " + strings.TrimSpace(sql)

			}
			if sql == "" {
				continue
			}
			err = source.DB().Exec(sql).Error
			if err != nil {
				log.Println("数据库导入失败:" + file + err.Error())
				return
			} else {
				log.Println(sql, "\t success!")
			}
		}
	}

	if err != nil {
		println(err)
		return
	}
}
func TestCreateProduct(t *testing.T) {
	type args struct {
		product ProductForUpdate
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			"默认",
			args{
				ProductForUpdate{},
			},
			false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err, _ := CreateProduct(tt.args.product); (err != nil) != tt.wantErr {
				t.Errorf("CreateProduct() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestDeleteProduct(t *testing.T) {
	type args struct {
		product model.Product
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := DeleteProduct(tt.args.product); (err != nil) != tt.wantErr {
				t.Errorf("DeleteProduct() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestDeleteProductByIds(t *testing.T) {

}

func TestGetProduct(t *testing.T) {
	type args struct {
		id uint
	}
	tests := []struct {
		name    string
		args    args
		wantErr error
	}{
		{
			name: "正常",
			args: args{
				6,
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErr, gotProduct := GetProduct(tt.args.id)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("GetProduct() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if reflect.DeepEqual(gotProduct, model.Product{}) {
				t.Errorf("GetProduct() 为空")
			}
		})
	}
}

func TestGetProductInfoList(t *testing.T) {
	type args struct {
		info request.ProductSearch
	}
	tests := []struct {
		name      string
		args      args
		wantErr   error
		wantList  interface{}
		wantTotal int64
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErr, gotList, gotTotal := GetProductInfoList(tt.args.info)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("GetProductInfoList() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if !reflect.DeepEqual(gotList, tt.wantList) {
				t.Errorf("GetProductInfoList() gotList = %v, want %v", gotList, tt.wantList)
			}
			if gotTotal != tt.wantTotal {
				t.Errorf("GetProductInfoList() gotTotal = %v, want %v", gotTotal, tt.wantTotal)
			}
		})
	}
}

type productM struct {
	ID int
}

func TestSql(t *testing.T) {
	var p productM
	var err error
	err = source.DB().Model(&Product{}).Where("id = ?", 23388).Find(&p).Error
	if err != nil {
		return
	}
}
func TestExcelTemplate(t *testing.T) {

	_, link := ExportSkuEditExcelTemplate(23515)
	println(link)
}
func TestImportExcelTemplate(t *testing.T) {
	var err error
	err = ImportSkuEditExcel("20221027143755订单导出.xlsx")
	if err != nil {
		println(err.Error())
	}
	println(1)
}

func TestUpdateProductByExcel(t *testing.T) {
	var err error
	err = UpdateProductByExcel("C:\\Users\\<USER>\\OneDrive\\文档\\WeChat Files\\a53665124\\FileStorage\\File\\2023-09\\丽晶商品list—更新.xlsx")
	if err != nil {
		println(err.Error())
	}
	println(1)
}
func TestGetProduct1(t *testing.T) {
	type args struct {
		id uint
	}
	tests := []struct {
		name        string
		args        args
		wantErr     error
		wantProduct model.Product
	}{
		{
			name: "normal",
			args: args{
				id: 1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErr, gotProduct := GetProduct(tt.args.id)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("GetProduct() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if gotErr != nil {
				t.Errorf("GetProduct() gotProduct = %v, want %v", gotProduct, tt.wantProduct)
			}
		})
	}
}

func TestGetExportProductList(t *testing.T) {
	var info request.ProductExportRecordRequest
	info.SysUserID = 713
	err, total, list := GetProductExportRecordList(info)
	if err != nil {
		panic(err.Error())
	}
	println(total)
	if list == nil {
		panic(1)
	}
}
func TestExportProduct(t *testing.T) {
	info := request.ProductSearch{}
	info.ID = 100004
	err, gotLink := ExportProduct(info)
	if err != nil {
		panic(err.Error())
	}
	println(gotLink)
}

func TestGetProductCardList(t *testing.T) {
	type args struct {
		info  request.ProductCardListSearch
		level int
	}
	tests := []struct {
		name      string
		args      args
		wantErr   error
		wantList  []response2.ProductNew
		wantTotal int64
	}{
		// TODO: Add test cases.
		{
			name: "默认",
			args: args{
				info: request.ProductCardListSearch{
					PageInfo: yzRequest.PageInfo{
						Page:     1,
						PageSize: 10,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErr, gotList, gotTotal := GetProductCardList(tt.args.info, tt.args.level)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("GetProductCardList() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if !reflect.DeepEqual(gotList, tt.wantList) {
				t.Errorf("GetProductCardList() gotList = %v, want %v", gotList, tt.wantList)
			}
			if gotTotal != tt.wantTotal {
				t.Errorf("GetProductCardList() gotTotal = %v, want %v", gotTotal, tt.wantTotal)
			}
		})
	}
}

func TestTimeCompare(t *testing.T) {
	cacheLoadAt = time.Now()
	nextMin := cacheLoadAt.Add(2 * time.Minute)
	println(nextMin.Before(time.Now()))
}

func TestPutProductStorage(t *testing.T) {
	go func() {
		// 定时缓存采购端导入的商品id记录
		CronLoadStorageCache()
	}()
	time.Sleep(3 * time.Second)
	for {
		isLoaded, alreadyImportProductIdsFromCache := GetAppProductIDs(1)
		println(isLoaded, len(alreadyImportProductIdsFromCache))
		time.Sleep(3 * time.Second)
	}
}

func TestGetProductInfoList1(t *testing.T) {
	type args struct {
		info request.ProductSearch
	}
	product := model.Product{}
	product.Title = "红脆李"
	tests := []struct {
		name      string
		args      args
		wantErr   error
		wantList  interface{}
		wantTotal int64
	}{
		// TODO: Add test cases.
		{
			name: "默认",
			args: args{
				info: request.ProductSearch{
					PageInfo: yzRequest.PageInfo{
						Page:     1,
						PageSize: 10,
					},
					Product: product,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErr, gotList, gotTotal := GetProductInfoList(tt.args.info)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("GetProductInfoList() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if !reflect.DeepEqual(gotList, tt.wantList) {
				t.Errorf("GetProductInfoList() gotList = %v, want %v", gotList, tt.wantList)
			}
			if gotTotal != tt.wantTotal {
				t.Errorf("GetProductInfoList() gotTotal = %v, want %v", gotTotal, tt.wantTotal)
			}
		})
	}
}

func TestGetProductStock(t *testing.T) {
	type args struct {
		id []int
	}
	tests := []struct {
		name    string
		args    args
		wantErr error
	}{
		{
			name: "正常",
			args: args{
				[]int{2, 3, 4},
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErr, gotProduct := GetProductStock(tt.args.id)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("GetProduct() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if reflect.DeepEqual(gotProduct, model.Product{}) {
				t.Errorf("GetProduct() 为空")
			}
		})
	}
}
