package model

import (
	"yz-go/source"
)

type SelectGoods struct {
	List           []Goods `json:"list" form:"list"`           //三方分类名
	Categorys      string  `json:"categorys" form:"categorys"` //三方分类名
	Key            string  `json:"key"`
	GatherSupplyID uint    `json:"gather_supply_id"`
	BrandID        int     `json:"brand_id"`
	SysUserID      uint    `json:"sys_user_id"`
}

type GoodsStorage struct { //选品库
	source.Model
	SourceGoodsID uint `json:"source_goods_id" form:"source_goods_id" gorm:"index"` //原始id
	SupplyID      uint `json:"supply_id" form:"supply_id" gorm:"index"`             //供应链id
}
type Goods struct {
	ThirdCategoryName   string      `json:"third_category_name" form:"third_category_name"` // 三方分类名
	Sale                uint        `json:"sale" form:"sale"`                               // 销量
	ID                  int         `json:"id" form:"id"`                                   // 商品id   spu id
	ProductID           int         `json:"parent_id" form:"parent_id"`                     // 商品id   spu id
	ProductStrID        string      `json:"product_str_id" form:"product_str_id"`           // 商品字符串id标识
	Unit                string      `json:"unit" form:"unit"`                               // 单位
	Stock               uint        `json:"stock" form:"stock"`                             // 库存
	TotalStock          int         `json:"total_stock" form:"total_stock"`                 // 总库存
	Source              int         `json:"source" form:"source"`                           // 商品源
	Cover               string      `json:"cover" form:"cover"  `                           // 封面
	Status              int         `json:"status" form:"status"`                           // 上下架  状态 0.下架 1.正常
	IsFreeShipping      int         `json:"is_free_shipping" form:"is_free_shipping"`       // 是否包邮   1包邮0不包邮
	BrandId             int         `json:"brand_id" form:"brand_id"`                       // 品牌
	Rate                float64     `json:"rate" form:"rate"`                               // 利润率
	ActivityRate        float64     `json:"activity_rate" form:"activity_rate"`             // 营销利润率
	CostPrice           uint        `json:"cost_price" form:"cost_price"`                   // 成本价
	AgreementPrice      uint        `json:"agreement_price" form:"agreement_price"`         // 协议价
	GuidePrice          uint        `json:"guide_price" form:"guide_price"`                 // 指导价
	ActivityPrice       uint        `json:"activity_price" form:"activity_price"`           // 营销价
	SalePrice           interface{} `json:"sale_price" form:"sale_price"`                   // 销售价
	MarketPrice         uint        `json:"market_price" form:"market_price"`               // 市场价
	Title               string      `json:"title" form:"title"`                             // 标题
	CategoryIds         []string    `json:"category_ids" form:"category_ids"`               // 分类id
	ThirdBrandName      string      `json:"third_brand_name" form:"third_brand_name"`       // 三方品牌
	GatherSupplyID      uint        `json:"gather_supply_id"`
	IsImport            uint        `json:"is_import"`
	LianProduct         interface{} `json:"lian_product"`
	LinePrice           uint        `json:"line_price"`
	SN                  string      `json:"sn"`
	WangWangAccount     string      `json:"WangWangAccount"`
	ShopName            string      `json:"shop_name"`
	Color               string      `json:"color"`
	ExpressDelivery     string      `json:"express_delivery"`
	Weight              string      `json:"weight"`
	OfficialDistriPrice string      `json:"official_distri_price"`
	BasePrice           string      `json:"base_price"`
	SuggestPrice        string      `json:"suggest_price"`
	SkuId               string      `json:"sku_id"`
	FreightPrice        uint        `json:"freight_price"`
}

type GoodsDetail struct {
	ThirdCategoryName string `json:"third_category_name" form:"third_category_name"` //三方分类名

	ID             int         `json:"id" form:"id"`                           //商品id   spu id
	MarketPrice    uint        `json:"market_price" form:"market_price"`       //市场价
	Source         int         `json:"source" form:"source"`                   //市场价
	AgreementPrice uint        `json:"agreement_price" form:"agreement_price"` //协议价
	GuidePrice     uint        `json:"guide_price" form:"guide_price"`         //指导价
	SalePrice      uint        `json:"sale_price" form:"sale_price"`           //销售价
	ActivityPrice  uint        `json:"activity_price" form:"activity_price"`   //销售价
	CostPrice      uint        `json:"cost_price" form:"cost_price"`           //成本价
	Description    string      `json:"description" form:"description"`         //描述
	Covers         []string    `json:"covers" form:"covers"`                   //轮播图
	Attributes     []Attribute `json:"attributes" form:"attributes"`           //营销价
	Specs          Specs       `json:"specs" form:"specs"`
	Title          string      `json:"title" form:"title"`
	Stock          uint        `json:"stock" form:"stock"`
	Status         uint        `json:"status" form:"status"`
	Sale           uint        `json:"sale" form:"sale"`
	Cover          string      `json:"cover" form:"cover"`
	Unit           string      `json:"unit" form:"unit"`
	ThirdID        uint        `json:"third_id" form:"third_id"`
	ThirdBrandName string      `json:"third_brand_name" form:"third_brand_name"` //三方品牌
}

type Attribute struct {
	Name  string `json:"name" form:"name"`
	Value string `json:"value" form:"value"`
}
type Specs struct {
	Names   []SpecsName   `json:"names" form:"names"`
	Values  []SpecsValue  `json:"values" form:"values"`
	Options []SpecsOption `json:"options" form:"options"`
}

type SpecsName struct {
	ID      int    `json:"id" form:"id"`
	GoodsID int    `json:"goods_id" form:"goods_id"`
	Name    string `json:"name" form:"name"`
	Sort    int    `json:"sort" form:"sort"`
}
type SpecsValue struct {
	ID         int    `json:"id" form:"id"`
	GoodsID    int    `json:"goods_id" form:"goods_id"`
	SpecNameID int    `json:"spec_name_id" form:"spec_name_id"`
	Name       string `json:"name" form:"name"`
	Sort       int    `json:"sort" form:"sort"`
}
type SpecsOption struct {
	ID             int    `json:"id" form:"id"`
	GoodsID        int    `json:"goods_id" form:"goods_id"`
	SpecValueIds   string `json:"spec_value_ids" form:"spec_value_ids"`
	SpecValueNames string `json:"spec_value_names	" form:"spec_value_names	"`
	MarketPrice    uint   `json:"market_price" form:"market_price"`     //市场价
	ActivityPrice  uint   `json:"activity_price" form:"activity_price"` //营销
	GuidePrice     uint   `json:"guide_price" form:"guide_price"`
	AgreementPrice uint   `json:"agreement_price" form:"agreement_price"` //协议价
	SalePrice      uint   `json:"sale_price" form:"sale_price"`           //销售
	CostPrice      uint   `json:"cost_price" form:"cost_price"`           //成本
	OldGoodsID     uint   `json:"old_goods_id" form:"old_goods_id"`       //id

	Stock  int    `json:"stock" form:"stock"`
	Weight int    `json:"weight" form:"weight"` //重量
	Image  string `json:"image" form:"image"`   //图片
	Status int    `json:"status" form:"status"` //状态   '上下架   0 下架 1上架'

}

type Ids struct {
	Ids string `json:"ids" form:"ids"`
}

type SupplyGoods struct {
	source.Model
	SupplyGoodsID  uint `json:"supply_goods_id"`
	Source         int  `json:"source"`
	ProductID      uint `json:"product_id" gorm:"index;"`
	GatherSupplyID uint `json:"gather_supply_id" gorm:"index;"`
}

type RiskManagementRecord struct {
	source.Model
	GatherSupplyID uint `json:"gather_supply_id" gorm:"index"`
	SourceGoodsID  uint `json:"source_goods_id" gorm:"index"`
	ProductID      uint `json:"product_id" gorm:"index"`
	Type           int  `json:"type" gorm:"index"` //0风控 1数据不存在
}
