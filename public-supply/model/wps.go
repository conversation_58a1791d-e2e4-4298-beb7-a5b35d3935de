package model

type WpsNotifyData struct {
	CMsgid   string `json:"c_msgid"`
	CContent struct {
		McOrderNo    string `json:"mcOrderNo"`
		RefuseStatus string `json:"refuse_status"`
		Type         int    `json:"type"`
		Msg          string `json:"msg"`
	} `json:"c_content"`
	Type int `json:"type"`
	Time int `json:"time"`
}
type WpsNotifyShelvesData struct {
	CMsgid   string `json:"c_msgid"`
	CContent []struct {
		CFatherGoodsId string `json:"c_father_goods_id"`
		CRolesId       int    `json:"c_roles_id"`
		Type           int    `json:"type"`
		State          int    `json:"state"`
		Msg            string `json:"msg"`
		Time           string `json:"time"`
	} `json:"c_content"`
	Type int    `json:"type"`
	Time string `json:"time"`
}

type WpsNotifyProductData struct {
	CMsgid   string `json:"c_msgid"`
	CContent struct {
		Goods []struct {
			CFatherGoodsId string `json:"c_father_goods_id"`
			CGoodsId       string `json:"c_goods_id"`
		} `json:"goods"`
		Type int    `json:"type"`
		Msg  string `json:"msg"`
		Time string `json:"time"`
	} `json:"c_content"`
	Type int `json:"type"`
}

type WpsNotifyAfterSale struct {
	CMsgid   string `json:"c_msgid"`
	CContent struct {
		McOrderNo    string `json:"mcOrderNo"`
		RefuseType   string `json:"refuse_type"`
		RefuseStatus string `json:"refuse_status"`
		Type         int    `json:"type"`
		Address      string `json:"address"`
		Phone        string `json:"phone"`
		Name         string `json:"name"`
		Msg          string `json:"msg"`
		Time         string `json:"time"`
	} `json:"c_content"`
	Type int    `json:"type"`
	Time string `json:"time"`
}
