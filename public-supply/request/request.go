package request

import (
	"public-supply/model"
	yzRequest "yz-go/request"
)

type GetCategoryChild struct {
	Pid            int    `json:"pid" form:"pid"`
	StrID          string `json:"str_id" form:"str_id"`
	Source         int    `json:"source" form:"source"`
	Key            string `json:"key" form:"key"`
	GatherSupplyID uint   `json:"gather_supply_id"`
}

type RequestSource struct {
	Id uint `json:"id" form:"id"`
}
type GetGroup struct {
	Key            string `json:"key" form:"key"`
	Pid            int    `json:"pid" form:"pid"`
	Source         int    `json:"source" form:"source"`
	GatherSupplyID uint   `json:"gather_supply_id"`
}
type UpdateProduct struct {
	Key            string `json:"key" form:"key"`
	ID             uint   `json:"product_id" form:"product_id"`
	GatherSupplyID uint   `json:"gather_supply_id"`
}
type GetToken struct {
	GatherSupplyID uint   `json:"gather_supply_id"`
	Code           string `json:"code"`
}
type GetCategorySearch struct {
	ImportGoods
	PageSize int `json:"pageSize" form:"pageSize" query:"pageSize"` //

	Key            string `json:"key"`
	Level          uint   `json:"level"`
	Pid            uint   `json:"pid"`
	GatherSupplyID uint   `json:"gather_supply_id"`
}

type KSRequestData struct {
	PageIndex            int    `json:"page_index"`
	PageSize             int    `json:"page_size"`
	BarCode              string `json:"bar_code"`
	CommodityBarTypeCode string `json:"commodity_bar_type_code"`
	Size                 string `json:"size"`
	ItemNumber           string `json:"item_number"`
	StyleCode            string `json:"style_code"`
	ProductMarking       string `json:"product_marking"`
	Brand                string `json:"brand"`
	CatId                string `json:"cat_id"`
	CatName              string `json:"cat_name"`
	Status               int    `json:"status"`
	UpdTimeStart         string `json:"upd_time_start"`
	UpdTimeEnd           string `json:"upd_time_end"`
}

type GetGoodsSearch struct {
	KSRequestData
	//BrandIds []int  `json:"brand_ids" form:"brand_ids"`
	Page                 int          `json:"page" form:"page"`
	Limit                int          `json:"limit" form:"limit"`
	Source               *int         `json:"source" form:"source"`
	CategoryID           int          `json:"category_id" form:"category_id"`
	Category1ID          int          `json:"category1_id" form:"category1_id"`
	Category2ID          int          `json:"category2_id" form:"category2_id"`
	Category3ID          int          `json:"category3_id" form:"category3_id"`
	CategoryStrID        string       `json:"category_str_id" form:"category_str_id"`
	Category1StrID       string       `json:"category1_str_id" form:"category1_str_id"`
	Category2StrID       string       `json:"category2_str_id" form:"category2_str_id"`
	Category3StrID       string       `json:"category3_str_id" form:"category3_str_id"`
	CategoryLevel        string       `json:"category_level" form:"category_level"`
	SearchWords          string       `json:"search_words" form:"search_words"`
	TypeName             string       `json:"type_name" form:"type_name"`
	BrandStrID           string       `json:"brand_str_id" form:"brand_str_id"`
	BrandID              int          `json:"brand_id" form:"brand_id"`
	BrandNames           string       `json:"brand_names" form:"brand_names"`
	RangeType            string       `json:"range_type" form:"range_type"`
	IsSort               string       `json:"is_sort" form:"is_sort"`
	RangeForm            uint         `json:"range_from" form:"range_from"`
	RangeTo              uint         `json:"range_to" form:"range_to"`
	OfficialDistriPrice  Section      `json:"official_distri_price" form:"official_distri_price"`
	BasePrice            Section      `json:"base_price" form:"base_price"`
	SuggestPrice         Section      `json:"suggest_price" form:"suggest_price"`
	AgreementPrice       Section      `json:"agreement_price" form:"agreement_price"` //协议
	GuidePrice           Section      `json:"guide_price" form:"guide_price"`         //指导
	ActivityPrice        Section      `json:"activity_price" form:"activity_price"`   //营销
	PromotionRate        SectionFloat `json:"promotion_rate" form:"promotion_rate"`   // 利润率
	ActivityRate         SectionFloat `json:"activity_rate" form:"activity_rate"`
	Profits              Section      `json:"profits" form:"profits"`
	Discount             Section      `json:"discount" form:"discount"`                   //折扣
	GrossProfitRate      Section      `json:"gross_profit_rate" form:"gross_profit_rate"` //毛利率
	CreatedTime          Section      `json:"created_time" form:"created_time"`
	Type                 string       `json:"type" form:"type"`
	Sort                 string       `json:"sort" form:"sort"`
	IsFreeShipping       int          `json:"is_free_shipping" form:"is_free_shipping"`
	ShopWords            string       `json:"shop_words" form:"shop_words"`
	GroupID              int          `json:"group_id" form:"group_id"`
	Categorys            string       `json:"categorys" form:"categorys"`
	Key                  string       `json:"key"`
	GatherSupplyID       uint         `json:"gather_supply_id"`
	ThirdGatherSupplyID  *uint        `json:"third_gather_supply_id"`
	GoodsID              uint         `json:"goods_id"`
	GoodsIds             []uint       `json:"goods_ids"`
	SeparateIds          []int        `json:"separate_ids"`
	IsDisplay            *int         `json:"is_display"`
	Recommend            int          `json:"recommend" form:"recommend"`
	SupplierID           *uint        `json:"supplier_id"`
	IsRecommend          *int         `json:"is_recommend"`
	IsImport             int          `json:"is_import"`      //1 未导入
	SelfIsImport         *int         `json:"self_is_import"` //1 未导入 0已导入
	IsNew                *int         `json:"is_new"`
	IsHot                *int         `json:"is_hot"`
	IsPromotion          *int         `json:"is_promotion"`
	IsBill               *int         `json:"is_bill"`     // 赋码状态
	IsTaxLogo            *int         `json:"is_tax_logo"` // 1 含税 2 不含税 3 未设置
	TaxRate              *int         `json:"tax_rate"`    // 税率
	AlbumId              *int         `json:"album_id"`    // 共享专辑搜索
	CollectId            *int         `json:"collect_id"`  // 商品专辑搜索
	GoodsType            *int         `json:"goods_type"`
	DistributorCoId      string       `json:"distributor_co_id"`
	WareHouseName        string       `json:"ware_house_name"`
	Articleno            string       `json:"articleno"`               //货号
	CommodityBarTypeCode string       `json:"commodity_bar_type_code"` //货号
	Sexs                 string       `json:"sexs"`
	Quarters             string       `json:"quarters"`
	Divisions            string       `json:"divisions"`
	MaxDiscount          string       `json:"maxDiscount"`
	MinDiscount          string       `json:"minDiscount"`
	MaxMarketPrice       string       `json:"maxMarketPrice"`
	MinMarketPrice       string       `json:"minMarketPrice"`
	SysUserID            uint         `json:"sys_user_id"`
	IsFreeTax            uint         `json:"is_free_tax"`
	WdtShopId            string       `json:"wdt_shop_id"`
	BynGoodsSearch
}

// BynGoodsSearch 必应鸟商品搜索
type BynGoodsSearch struct {
	Title       string        `json:"title" form:"title"`               // 商品名称
	IsDisplay   int           `json:"is_display" form:"is_display"`     // 上架（1是-1否）
	IsStock     int           `json:"is_stock" form:"is_stock"`         // 是否有库存(1有-1无,查询商品stock是否大于0)
	CouponType  int           `json:"coupon_type" form:"coupon_type"`   // 卡券类型(1直冲2卡券)
	CategoryIds []BynCategory `json:"category_ids" form:"category_ids"` // 分类ids
	BrandIds    []BynBrand    `json:"brand_ids" form:"brand_ids"`       // 品牌ids
}

type BynCategory struct {
	ID int `json:"id" form:"id"`
}
type BynBrand struct {
	ID int `json:"id" form:"id"`
}

type Section struct {
	From int `json:"from" form:"from"`
	To   int `json:"to" form:"to"`
}
type SectionFloat struct {
	From float64 `json:"from" form:"from"`
	To   float64 `json:"to" form:"to"`
}

type ImportGoods struct {
	Page   int `json:"page" form:"page"`
	Limit  int `json:"limit" form:"limit"`
	Source int `json:"source" form:"source"`
}
type GatherSupplySearch struct {
	model.GatherSupply
	CategoryID uint   `json:"category_id"`
	Source     string `json:"source"`
	Batch      string `json:"batch"`
	IsPlugin   int    `json:"is_plugin"`
	yzRequest.PageInfo
}
type LianLian struct {
	IdCard       string `json:"id_card"`
	Memo         string `json:"memo"`
	ThirdOrderSN string `json:"third_order_sn" form:"third_order_sn"`
	TravelDate   string `json:"travelDate" form:"travelDate"`
}
type RequestConfirmOrder struct {
	OrderSn
	RequestSaleBeforeCheck
	BynPreOrder
	LianLian      LianLian          `json:"lian_lian"`
	GuangDian     GuangDianPreOrder `json:"guang_dian"`
	CakeOrderData CakeOrderData     `json:"cake_order_data"`
	Remark        string            `json:"remark"`
}

type RequestConfirmOrderData struct {
	LianLian LianLian `json:"lian_lian"`
}
type CakeJsonData struct {
	CakeOrderData CakeOrderData `json:"cake_order_data"`
}

type CakeOrderData struct {
	AddressId          string      `json:"address_id" form:"address_id"` //uid
	DistributionRuleId string      `json:"distribution_rule_id"`
	DeliveryAmount     string      `json:"delivery_amount"`
	CityId             string      `json:"city_id"`
	BuyerMsg           string      `json:"buyer_msg"`
	Group              interface{} `json:"group"`
	BuyerPhone         string      `json:"buyer_phone"`
	ShipType           string      `json:"ship_type"`
}

type HeheOrderData struct {
}

// BynPreOrder byn下单参数
type BynPreOrder struct {
	// 商品ID
	GoodsID uint `json:"goods_id" form:"goods_id"`
	// 规格ID
	SpecID int `json:"spec_id" form:"spec_id"`
	// 优惠券ID，不传走默认优惠券
	CouponID int `json:"coupon_id" form:"coupon_id"`
	// count 数量，至少为1
	Count int `json:"count" form:"count"`
	// out_trade_no 外部订单号
	OutTradeNo string `json:"out_trade_no" form:"out_trade_no"`
	// recharge_number 账号，直冲
	RechargeNumber string `json:"recharge_number" form:"recharge_number"`
	// card 卡号，油卡使用
	Card string `json:"card" form:"card"`
	// username 用户名，油卡使用
	Username string `json:"username" form:"username"`
	// coupon_type 卡券类型 1直冲2卡券
	CouponType int `json:"coupon_type" form:"coupon_type"`
}

type OrderSn struct {
	OrderSn string `json:"orderSn" form:"orderSn"`
}

type Sku struct {
	Sku int64 `json:"sku" form:"sku"`
}
type RequestExpress struct {
	//OrderSn
	OrderSn        string `json:"orderSn" form:"orderSn"`
	GatherSupplySn string `json:"gather_supply_sn" form:"gather_supply_sn"`
	Sku            string `json:"sku" form:"sku"`
}

type RequestAfterSale struct {
	OrderSn
	Sku
	GatherSupplyID uint `json:"gather_supply_id"`
}

type RequestAfterSalePicture struct {
	RequestAfterSale
	Pictures []string `json:"pictures" form:"pictures"`
}

type AfterSale struct {
	RequestAfterSale
	Num             int    `json:"num" form:"num" `                                //申请售后的商品数量
	GoodsFee        uint   `json:"goodsFee" form:"goodsFee" `                      //退款金额,退货退款时需要填写
	LogisticFee     uint   `json:"logisticFee" form:"logisticFee"`                 //期望退还的运费金额（分）
	ServiceTypeCode string `json:"serviceTypeCode	" form:"serviceTypeCode	"` //售后类型code  必须填写校验接口中serviceType项返回的code
	PickTypeCode    string `json:"pickTypeCode" form:"pickTypeCode"`               //取件类型code 必须返回校验接口中pickType返回的code

	/**
	包装情况code
	必须根据校验接口的返回情况，决定该项是否必须
	如果必须，必须填写校验接口packageType中返回的code
	*/
	PackageTypeCode string `json:"packageTypeCode" form:"packageTypeCode"`
	ReturnTypeCode  string `json:"returnTypeCode" form:"returnTypeCode"` // 反件情况code必须根据校验接口的返回情况，决定该项是否必须如果必须，必须填写校验接口returnType中返回的code

	/**
	原因类型code
	必须根据校验接口的返回情况，决定该项是否必须
	如果必须，必须填写校验接口reasonsType中返回的code
	*/
	ReasonsTypeCode    string `json:"reasonsTypeCode" form:"reasonsTypeCode"`
	ReasonsDescription string `json:"reasonsDescription" form:"reasonsDescription"` // 申请售后的原因，详细说明。 限制最长150个文字
	ServiceTime        string `json:"serviceTime" form:"serviceTime"`               // 苏宁 上门维修服务时间

	/**
	凭证图片列表
	必须根据校验接口的返回情况，决定该项是否必须
	如果必须，必须填写上传售后凭证接口返回的url
	最多填写5个url
	*/
	Vouchers []string             `json:"vouchers" form:"vouchers"`
	UserInfo ReceivingInformation `json:"userInfo" form:"userInfo"`
}

type UpdateData struct {
	ID             uint   `json:"id"`
	GatherSupplySN string `json:"gather_supply_sn"`
	UpdateType     string `json:"update_type"` // 1 主订单 2 子订单
	Key            string `json:"key"`
	GatherSupplyID uint   `json:"gather_supply_id"`
}
type RequestSaleBeforeCheck struct {
	Skus           []GoodsSpu           `json:"spu" form:"spu"`
	LocalSkus      []GoodsSpu           `json:"local_skus" form:"local_skus"`
	Address        ReceivingInformation `json:"address" form:"address"`
	GatherSupplyID uint                 `json:"gather_supply_id" form:"gather_supply_id"`
	AppID          uint                 `json:"app_id"`
	OrderSN        uint                 `json:"order_sn" form:"order_sn"`
	ThirdOrderSN   string               `json:"third_order_sn" form:"third_order_sn"`
	LianLian       LianLian             `json:"lian_lian"`
	Lease          Lease                `json:"lease"`
	GuangDian      GuangDianPreOrder    `json:"guang_dian"`
	CakeData       CakeOrderData        `json:"cake_order_data"`
}

type Lease struct {
	LeaseTenancyTermsId uint `json:"lease_tenancy_terms_id"`
}

type GuangDianPreOrder struct {
	Number      string `json:"number"`
	IdenNr      string `json:"idenNr"`
	RegionId    string `json:"regionId"`
	Area        string `json:"area"`
	Consignee   string `json:"consignee"`
	AreaName    string `json:"area_name"`
	Phone       string `json:"phone"`
	Description string `json:"description" form:"description"` //详细信息

}

type GoodsSpus []GoodsSpu
type GoodsSpu struct {
	Sku
	Number         int  `json:"number" form:"number"`
	GatherSupplyID uint `json:"gather_supply_id" form:"gather_supply_id"`

	//AgreementPrice uint  `json:"agreement_price" form:"agreement_price"`
}

type ReceivingInformation struct {
	Consignee   string `json:"consignee" form:"consignee"`     //收货人
	Phone       string `json:"phone" form:"phone"`             //联系方式
	ProvinceId  int    `json:"province_id"`                    // 省id
	CityId      int    `json:"city_id"`                        // 市id
	CountyId    int    `json:"county_id"`                      // 区id
	TownId      int    `json:"town_id"`                        // 街id
	Province    string `json:"province" form:"province"`       //省
	City        string `json:"city" form:"city"`               //市
	Area        string `json:"area" form:"area"`               //区
	Street      string `json:"street" form:"street"`           //街道
	Description string `json:"description" form:"description"` //详细信息

}

type ConfirmRequest struct {
	OrderSn       string               `json:"order_sn"`
	Spu           []GoodsSpu           `json:"spu"`
	Address       ReceivingInformation `json:"address"`
	Remark        string               `json:"remark"`
	GuangDian     GuangDianPreOrder    `json:"guang_dian"`
	CakeOrderData CakeOrderData        `json:"cake_order_data"`
}
