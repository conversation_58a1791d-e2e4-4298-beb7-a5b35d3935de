package setting

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"product/service"
	"strconv"
	"yz-go/cache"
	"yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

type UpdateInfoData struct {
	CurrentPrice           uint     `json:"currentPrice"`
	CostPrice              uint     `json:"costPrice"`
	CateGory               uint     `json:"cateGory"`
	Cron                   string   `json:"cron"`
	OriginalPrice          uint     `json:"originalPrice"`
	ActivityPrice          uint     `json:"activity_price"`
	GuidePrice             uint     `json:"guide_price"`
	BaseInfo               uint     `json:"baseInfo"`
	Bill                   uint     `json:"bill"`
	SplitSku               uint     `json:"splitSku"`
	ImageWatermark         uint     `json:"imageWatermark"`
	CreateBrand            uint     `json:"createBrand"`
	Sales                  uint     `json:"sales"`
	AutoImportProduct      uint     `json:"autoImportProduct"`
	AutoImportProductCron  string   `json:"autoImportProductCron"`
	ManuallyUpdateSettings []string `json:"manually_update_settings"`
}

type Management struct {
	//售价
	ProductPriceStatus uint `json:"productPriceStatus"`
	Products           uint `json:"products"`
	//利润率
	Profit uint `json:"profit"`
}
type NewStbz struct {
	SupplySalesExecA string `json:"supply_sales_exec_a"`
	SupplySalesExecB string `json:"supply_sales_exec_b"`
	SupplySalesExecC string `json:"supply_sales_exec_c"`
	SupplySalesExecD string `json:"supply_sales_exec_d"`
}

// 中台定价策略
type PricingData struct {
	Strategy uint   `json:"strategy"` //策略开关
	OpenTime string `json:"openTime"` //策略开关

	JDSales          uint    `json:"JDSales"`
	JDCostPrice      uint    `json:"JDCostPrice"`
	JDGuidePrice     uint    `json:"JDGuidePrice"`
	JDSalesGuide     string  `json:"JDSalesGuide"`
	JDSalesAgreement string  `json:"JDSalesAgreement"`
	JDSalesMarketing string  `json:"JDSalesMarketing"`
	JDSalesList      NewStbz `json:"JDSalesList"`

	JDCostAgreement  string `json:"JDCostAgreement"`
	JDCostMarketing  string `json:"JDCostMarketing"`
	JDGuideAgreement string `json:"JDGuideAgreement"`
	JDGuideMarketing string `json:"JDGuideMarketing"`

	ALSalesMarketing string  `json:"ALSalesMarketing"`
	ALSalesAgreement string  `json:"ALSalesAgreement"`
	ALCostAgreement  string  `json:"ALCostAgreement"`
	ALCostMarketing  string  `json:"ALCostMarketing"`
	ALGuideAgreement string  `json:"ALGuideAgreement"`
	ALGuideMarketing string  `json:"ALGuideMarketing"`
	ALSalesGuide     string  `json:"ALSalesGuide"`
	ALSales          uint    `json:"ALSales"`
	ALCost           uint    `json:"ALCost"`
	ALGuide          uint    `json:"ALGuide"`
	ALSalesList      NewStbz `json:"ALSalesList"`

	TMSalesGuide     string  `json:"TMSalesGuide"`
	TMSalesAgreement string  `json:"TMSalesAgreement"`
	TMSalesMarketing string  `json:"TMSalesMarketing"`
	TMCostAgreement  string  `json:"TMCostAgreement"`
	TMCostMarketing  string  `json:"TMCostMarketing"`
	TMGuideAgreement string  `json:"TMGuideAgreement"`
	TMGuideMarketing string  `json:"TMGuideMarketing"`
	TMSales          uint    `json:"TMSales"`
	TMCost           uint    `json:"TMCost"`
	TMGuide          uint    `json:"TMGuide"`
	TMSalesList      NewStbz `json:"TMSalesList"`

	SupplySales          uint   `json:"SupplySales"`
	SupplySalesGuide     string `json:"SupplySalesGuide"`
	SupplySalesOrigin    string `json:"SupplySalesOrigin"`
	SupplySalesAgreement string `json:"SupplySalesAgreement"`
	SupplySalesMarketing string `json:"SupplySalesMarketing"`
	SupplySalesFreight   string `json:"SupplySalesFreight"`
	SupplySalesIsTax     uint   `json:"SupplySalesIsTax"`
	SupplySalesTax       string `json:"SupplySalesTax"`

	SupplySalesExec  string `json:"SupplySalesExec"`
	SupplySalesExecA string `json:"supply_sales_exec_a"`
	SupplySalesExecB string `json:"supply_sales_exec_b"`
	SupplySalesExecC string `json:"supply_sales_exec_c"`
	SupplySalesExecD string `json:"supply_sales_exec_d"`

	SupplyCost          uint   `json:"SupplyCost"`
	SupplyCostAgreement string `json:"SupplyCostAgreement"`
	SupplyCostMarketing string `json:"SupplyCostMarketing"`
	SupplyCostGuide     string `json:"SupplyCostGuide"`
	SupplyCostOrigin    string `json:"SupplyCostOrigin"`
	SupplyCostFreight   string `json:"SupplyCostFreight"`

	SupplyAdvice          uint   `json:"SupplyAdvice"`
	SupplyAdviceAgreement string `json:"SupplyAdviceAgreement"`
	SupplyAdviceMarketing string `json:"SupplyAdviceMarketing"`
	SupplyAdviceGuide     string `json:"SupplyAdviceGuide"`
	SupplyAdviceOrigin    string `json:"SupplyAdviceOrigin"`
	SupplyAdviceFreight   string `json:"SupplyAdviceFreight"`

	SupplyAdviceExecA string `json:"supply_advice_exec_a"`
	SupplyAdviceExecB string `json:"supply_advice_exec_b"`
	SupplyAdviceExecC string `json:"supply_advice_exec_c"`
	SupplyAdviceExecD string `json:"supply_advice_exec_d"`
	SupplyAdviceExecE string `json:"supply_advice_exec_e"`
	SupplyAdviceExecF string `json:"supply_advice_exec_f"`

	SupplyGuide          uint   `json:"SupplyGuide"`
	SupplyGuideAgreement string `json:"SupplyGuideAgreement"`
	SupplyGuideMarketing string `json:"SupplyGuideMarketing"`
	SupplyGuideGuide     string `json:"SupplyGuideGuide"`
	SupplyGuideOrigin    string `json:"SupplyGuideOrigin"`
	SupplyGuideFreight   string `json:"SupplyGuideFreight"`

	SupplyActivity          uint    `json:"SupplyActivity"`
	SupplyActivityAgreement string  `json:"SupplyActivityAgreement"`
	SupplyActivityMarketing string  `json:"SupplyActivityMarketing"`
	SupplyActivityGuide     string  `json:"SupplyActivityGuide"`
	SupplyActivityOrigin    string  `json:"SupplyActivityOrigin"`
	SupplySalesList         NewStbz `json:"SupplySalesList"`
	SupplyActivityFreight   string  `json:"SupplyActivityFreight"`

	HNSupplySalesGuide      string  `json:"HNSupplySalesGuide"`
	HNSupplySalesAgreement  string  `json:"HNSupplySalesAgreement"`
	HNSupplySalesMarketing  string  `json:"HNSupplySalesMarketing"`
	HNSupplyCostAgreement   string  `json:"HNSupplyCostAgreement"`
	HNSupplyCostMarketing   string  `json:"HNSupplyCostMarketing"`
	HNSupplyGuideAgreement  string  `json:"HNSupplyGuideAgreement"`
	HNSupplyGuideMarketing  string  `json:"HNSupplyGuideMarketing"`
	HNSupplySales           uint    `json:"HNSupplySales"`
	HNSupplyCost            uint    `json:"HNSupplyCost"`
	HNSupplyGuide           uint    `json:"HNSupplyGuide"`
	HNSupplyAdvice          uint    `json:"HNSupplyAdvice"`
	HNSupplyAdviceAgreement string  `json:"HNSupplyAdviceAgreement"`
	HNSupplyAdviceGuide     string  `json:"HNSupplyAdviceGuide"`
	HNSupplySalesList       NewStbz `json:"HNSupplySalesList"`

	//特卖一仓
	TMYCSupplySalesGuide      string  `json:"TMYCSupplySalesGuide"`
	TMYCSupplySalesAgreement  string  `json:"TMYCSupplySalesAgreement"`
	TMYCSupplySalesMarketing  string  `json:"TMYCSupplySalesMarketing"`
	TMYCSupplyCostAgreement   string  `json:"TMYCSupplyCostAgreement"`
	TMYCSupplyCostMarketing   string  `json:"TMYCSupplyCostMarketing"`
	TMYCSupplyGuideAgreement  string  `json:"TMYCSupplyGuideAgreement"`
	TMYCSupplyGuideMarketing  string  `json:"TMYCSupplyGuideMarketing"`
	TMYCSupplySales           uint    `json:"TMYCSupplySales"`
	TMYCSupplyCost            uint    `json:"TMYCSupplyCost"`
	TMYCSupplyGuide           uint    `json:"TMYCSupplyGuide"`
	TMYCSupplyAdvice          uint    `json:"TMYCSupplyAdvice"`
	TMYCSupplyAdviceAgreement string  `json:"TMYCSupplyAdviceAgreement"`
	TMYCSupplyAdviceGuide     string  `json:"TMYCSupplyAdviceGuide"`
	TMYCSupplySalesList       NewStbz `json:"TMYCSupplySalesList"`

	//华东一仓
	HDYCSupplySalesGuide      string  `json:"HDYCSupplySalesGuide"`
	HDYCSupplySalesAgreement  string  `json:"HDYCSupplySalesAgreement"`
	HDYCSupplySalesMarketing  string  `json:"HDYCSupplySalesMarketing"`
	HDYCSupplyCostAgreement   string  `json:"HDYCSupplyCostAgreement"`
	HDYCSupplyCostMarketing   string  `json:"HDYCSupplyCostMarketing"`
	HDYCSupplyGuideAgreement  string  `json:"HDYCSupplyGuideAgreement"`
	HDYCSupplyGuideMarketing  string  `json:"HDYCSupplyGuideMarketing"`
	HDYCSupplySales           uint    `json:"HDYCSupplySales"`
	HDYCSupplyCost            uint    `json:"HDYCSupplyCost"`
	HDYCSupplyGuide           uint    `json:"HDYCSupplyGuide"`
	HDYCSupplyAdvice          uint    `json:"HDYCSupplyAdvice"`
	HDYCSupplyAdviceAgreement string  `json:"HDYCSupplyAdviceAgreement"`
	HDYCSupplyAdviceGuide     string  `json:"HDYCSupplyAdviceGuide"`
	HDYCSupplySalesList       NewStbz `json:"HDYCSupplySalesList"`

	//淘宝
	TBSupplySalesGuide      string  `json:"TBSupplySalesGuide"`
	TBSupplySalesAgreement  string  `json:"TBSupplySalesAgreement"`
	TBSupplySalesMarketing  string  `json:"TBSupplySalesMarketing"`
	TBSupplyCostAgreement   string  `json:"TBSupplyCostAgreement"`
	TBSupplyCostMarketing   string  `json:"TBSupplyCostMarketing"`
	TBSupplyGuideAgreement  string  `json:"TBSupplyGuideAgreement"`
	TBSupplyGuideMarketing  string  `json:"TBSupplyGuideMarketing"`
	TBSupplySales           uint    `json:"TBSupplySales"`
	TBSupplyCost            uint    `json:"TBSupplyCost"`
	TBSupplyGuide           uint    `json:"TBSupplyGuide"`
	TBSupplyAdvice          uint    `json:"TBSupplyAdvice"`
	TBSupplyAdviceAgreement string  `json:"TBSupplyAdviceAgreement"`
	TBSupplyAdviceGuide     string  `json:"TBSupplyAdviceGuide"`
	TBSupplySalesList       NewStbz `json:"TBSupplySalesList"`

	//跨境一仓
	KJYCSupplySalesGuide      string  `json:"KJYCSupplySalesGuide"`
	KJYCSupplySalesAgreement  string  `json:"KJYCSupplySalesAgreement"`
	KJYCSupplySalesMarketing  string  `json:"KJYCSupplySalesMarketing"`
	KJYCSupplyCostAgreement   string  `json:"KJYCSupplyCostAgreement"`
	KJYCSupplyCostMarketing   string  `json:"KJYCSupplyCostMarketing"`
	KJYCSupplyGuideAgreement  string  `json:"KJYCSupplyGuideAgreement"`
	KJYCSupplyGuideMarketing  string  `json:"KJYCSupplyGuideMarketing"`
	KJYCSupplySales           uint    `json:"KJYCSupplySales"`
	KJYCSupplyCost            uint    `json:"KJYCSupplyCost"`
	KJYCSupplyGuide           uint    `json:"KJYCSupplyGuide"`
	KJYCSupplyAdvice          uint    `json:"KJYCSupplyAdvice"`
	KJYCSupplyAdviceAgreement string  `json:"KJYCSupplyAdviceAgreement"`
	KJYCSupplyAdviceGuide     string  `json:"KJYCSupplyAdviceGuide"`
	KJYCSupplySalesList       NewStbz `json:"KJYCSupplySalesList"`

	//天猫精选
	TMJXSupplySalesGuide      string  `json:"TMJXSupplySalesGuide"`
	TMJXSupplySalesAgreement  string  `json:"TMJXSupplySalesAgreement"`
	TMJXSupplySalesMarketing  string  `json:"TMJXSupplySalesMarketing"`
	TMJXSupplyCostAgreement   string  `json:"TMJXSupplyCostAgreement"`
	TMJXSupplyCostMarketing   string  `json:"TMJXSupplyCostMarketing"`
	TMJXSupplyGuideAgreement  string  `json:"TMJXSupplyGuideAgreement"`
	TMJXSupplyGuideMarketing  string  `json:"TMJXSupplyGuideMarketing"`
	TMJXSupplySales           uint    `json:"TMJXSupplySales"`
	TMJXSupplyCost            uint    `json:"TMJXSupplyCost"`
	TMJXSupplyGuide           uint    `json:"TMJXSupplyGuide"`
	TMJXSupplyAdvice          uint    `json:"TMJXSupplyAdvice"`
	TMJXSupplyAdviceAgreement string  `json:"TMJXSupplyAdviceAgreement"`
	TMJXSupplyAdviceGuide     string  `json:"TMJXSupplyAdviceGuide"`
	TMJXSupplySalesList       NewStbz `json:"TMJXSupplySalesList"`

	//厂家直销
	CJZXSupplySalesGuide      string  `json:"CJZXSupplySalesGuide"`
	CJZXSupplySalesAgreement  string  `json:"CJZXSupplySalesAgreement"`
	CJZXSupplySalesMarketing  string  `json:"CJZXSupplySalesMarketing"`
	CJZXSupplyCostAgreement   string  `json:"CJZXSupplyCostAgreement"`
	CJZXSupplyCostMarketing   string  `json:"CJZXSupplyCostMarketing"`
	CJZXSupplyGuideAgreement  string  `json:"CJZXSupplyGuideAgreement"`
	CJZXSupplyGuideMarketing  string  `json:"CJZXSupplyGuideMarketing"`
	CJZXSupplySales           uint    `json:"CJZXSupplySales"`
	CJZXSupplyCost            uint    `json:"CJZXSupplyCost"`
	CJZXSupplyGuide           uint    `json:"CJZXSupplyGuide"`
	CJZXSupplyAdvice          uint    `json:"CJZXSupplyAdvice"`
	CJZXSupplyAdviceAgreement string  `json:"CJZXSupplyAdviceAgreement"`
	CJZXSupplyAdviceGuide     string  `json:"CJZXSupplyAdviceGuide"`
	CJZXSupplySalesList       NewStbz `json:"CJZXSupplySalesList"`

	YCSalesGuide     string  `json:"YCSalesGuide"`
	YCSalesAgreement string  `json:"YCSalesAgreement"`
	YCSalesMarketing string  `json:"YCSalesMarketing"`
	YCCostAgreement  string  `json:"YCCostAgreement"`
	YCCostMarketing  string  `json:"YCCostMarketing"`
	YCGuideAgreement string  `json:"YCGuideAgreement"`
	YCGuideMarketing string  `json:"YCGuideMarketing"`
	YCSales          uint    `json:"YCSales"`
	YCCost           uint    `json:"YCCost"`
	YCGuide          uint    `json:"YCGuide"`
	YCSalesList      NewStbz `json:"YCSalesList"`

	//云仓优选

	YCYXSalesGuide     string  `json:"YCYXSalesGuide"`
	YCYXSalesAgreement string  `json:"YCYXSalesAgreement"`
	YCYXSalesMarketing string  `json:"YCYXSalesMarketing"`
	YCYXCostAgreement  string  `json:"YCYXCostAgreement"`
	YCYXCostMarketing  string  `json:"YCYXCostMarketing"`
	YCYXGuideAgreement string  `json:"YCYXGuideAgreement"`
	YCYXGuideMarketing string  `json:"YCYXGuideMarketing"`
	YCYXSales          uint    `json:"YCYXSales"`
	YCYXCost           uint    `json:"YCYXCost"`
	YCYXGuide          uint    `json:"YCYXGuide"`
	YCYXSalesList      NewStbz `json:"YCYXSalesList"`

	//YZH供应链

	YzhSupplySalesAgreement string `json:"YzhSupplySalesAgreement"`
	YzhSupplySalesMarketing string `json:"YzhSupplySalesMarketing"`
	YzhSupplyCostAgreement  string `json:"YzhSupplyCostAgreement"`
	YzhSupplyCostMarketing  string `json:"YzhSupplyCostMarketing"`

	YzhSupplyOriginAgreement string `json:"YzhSupplyOriginAgreement"`
	YzhSupplyOriginMarketing string `json:"YzhSupplyOriginMarketing"`

	YzhSupplyActivityAgreement string `json:"YzhSupplyActivityAgreement"`
	YzhSupplyActivityMarketing string `json:"YzhSupplyActivityMarketing"`

	YzhSupplyGuideAgreement string `json:"YzhSupplyGuideAgreement"`
	YzhSupplyGuideMarketing string `json:"YzhSupplyGuideMarketing"`

	YzhSupplySales    uint `json:"YzhSupplySales"`
	YzhSupplyCost     uint `json:"YzhSupplyCost"`
	YzhSupplyOrigin   uint `json:"YzhSupplyOrigin"`   //建议零售价
	YzhSupplyActivity uint `json:"YzhSupplyActivity"` //营销价
	YzhSupplyGuide    uint `json:"YzhSupplyGuide"`    //指导价

	YzhNewSupplySalesAgreement     string `json:"YzhNewSupplySalesAgreement"`
	YzhNewSupplySalesMarketing     string `json:"YzhNewSupplySalesMarketing"`
	YzhNewSupplySalesOriginalPrice string `json:"YzhNewSupplySalesOriginalPrice"`

	YzhNewSupplyCostAgreement     string `json:"YzhNewSupplyCostAgreement"`
	YzhNewSupplyCostMarketing     string `json:"YzhNewSupplyCostMarketing"`
	YzhNewSupplyCostOriginalPrice string `json:"YzhNewSupplyCostOriginalPrice"`

	YzhNewSupplyOriginAgreement     string `json:"YzhNewSupplyOriginAgreement"`
	YzhNewSupplyOriginMarketing     string `json:"YzhNewSupplyOriginMarketing"`
	YzhNewSupplyOriginOriginalPrice string `json:"YzhNewSupplyOriginOriginalPrice"`

	YzhNewSupplyActivityAgreement     string `json:"YzhNewSupplyActivityAgreement"`
	YzhNewSupplyActivityMarketing     string `json:"YzhNewSupplyActivityMarketing"`
	YzhNewSupplyActivityOriginalPrice string `json:"YzhNewSupplyActivityOriginalPrice"`

	YzhNewSupplyGuideAgreement     string `json:"YzhNewSupplyGuideAgreement"`
	YzhNewSupplyGuideMarketing     string `json:"YzhNewSupplyGuideMarketing"`
	YzhNewSupplyGuideOriginalPrice string `json:"YzhNewSupplyGuideOriginalPrice"`

	YzhNewSupplySales    uint `json:"YzhNewSupplySales"`
	YzhNewSupplyCost     uint `json:"YzhNewSupplyCost"`
	YzhNewSupplyOrigin   uint `json:"YzhNewSupplyOrigin"`   //建议零售价
	YzhNewSupplyActivity uint `json:"YzhNewSupplyActivity"` //营销价
	YzhNewSupplyGuide    uint `json:"YzhNewSupplyGuide"`    //指导价

	CakeSupplyType       uint `json:"CakeSupplyType"`       //供货价格类型
	CakeSupplyCostType   uint `json:"CakeSupplyCostType"`   //成本类型
	CakeSupplyOriginType uint `json:"CakeSupplyOriginType"` //建议零售类型

	//供货
	CakeSupplyPrice      string `json:"CakeSupplyPrice"`      // 价格系数
	CakeSupplyMarketing  string `json:"CakeSupplyMarketing"`  //市场
	CakeSupplySettlement string `json:"CakeSupplySettlement"` //结算

	//成本
	CakeSupplyCostPrice      string `json:"CakeSupplyCostPrice"`      //成本
	CakeSupplyCostMarketing  string `json:"CakeSupplyCostMarketing"`  //成本市场
	CakeSupplyCostSettlement string `json:"CakeSupplyCostSettlement"` //成本结算

	//建议零售
	CakeSupplyOriginPrice      string `json:"CakeSupplyOriginPrice"`      //成本
	CakeSupplyOriginMarketing  string `json:"CakeSupplyOriginMarketing"`  //成本市场
	CakeSupplyOriginSettlement string `json:"CakeSupplyOriginSettlement"` //成本结算

	HeheSales          uint   `json:"HeheSales"`
	HeheSalesAgreement string `json:"HeheSalesAgreement"`
	HeheSalesLine      string `json:"HeheSalesLine"`

	HeheCost          uint   `json:"HeheCost"`
	HeheCostAgreement string `json:"HeheCostAgreement"`
	HeheCostLine      string `json:"HeheCostLine"`

	HeheAdvice          uint   `json:"HeheAdvice"`
	HeheAdviceAgreement string `json:"HeheAdviceAgreement"`
	HeheAdviceLine      string `json:"HeheAdviceLine"`

	HeheGuide          uint   `json:"HeheGuide"`
	HeheGuideAgreement string `json:"HeheGuideAgreement"`
	HeheGuideLine      string `json:"HeheGuideLine"`

	HeheActivity          uint   `json:"HeheActivity"`
	HeheActivityAgreement string `json:"HeheActivityAgreement"`
	HeheActivityLine      string `json:"HeheActivityLine"`
}
type SupplySetting struct {
	BaseInfo    BaseInfoData   `json:"baseInfo"`
	Cake        []int64        `json:"cake"`
	RouterKey   string         `json:"router_key"`
	UpdateInfo  UpdateInfoData `json:"update"`
	Pricing     PricingData    `json:"pricing"`
	StorageAuth StorageAuth    `json:"storage_auth"`
	Cloud       Cloud          `json:"cloud"`
}
type Cloud struct {
	CloudUserId     uint `json:"cloud_user_id"`      //云仓下单使用使用
	CloudIsSynOrder uint `json:"cloud_is_syn_order"` //是否同步云仓订单默认是 1是 2否

	CloudIsAutoUndercarriage uint `json:"cloud_is_auto_undercarriage"` //是否中台下架云仓自动下架 1是 0否
	CloudIsPushAutoShelf     uint `json:"cloud_is_push_auto_shelf"`    //推送成功自动上架  1是  0否

	CloudMarketing                     uint   `json:"cloud_marketing"`                       //云仓市场价设置 0 市场价 1现价 2 成本价
	CloudMarketingMarketingCoefficient string `json:"cloud_marketing_marketing_coefficient"` //市场价订价系数
	CloudPriceCoefficient              string `json:"cloud_marketing_price_coefficient"`     //现价订价系数
	CloudCostCoefficient               string `json:"cloud_marketing_cost_coefficient"`      //成本价订价系数

	CloudGuide                     uint   `json:"cloud_guide"`                       //云仓指导价设置 0 市场价 1现价 2 成本价 3指导价
	CloudGuideMarketingCoefficient string `json:"cloud_guide_marketing_coefficient"` //市场价订价系数
	CloudGuidePriceCoefficient     string `json:"cloud_guide_price_coefficient"`     //现价订价系数
	CloudGuideCostCoefficient      string `json:"cloud_guide_cost_coefficient"`      //成本价订价系数
	CloudGuideGuideCoefficient     string `json:"cloud_guide_guide_coefficient"`     //指导价订价系数

	CloudSettlement                     uint   `json:"cloud_settlement"`                       //云仓结算价设置 0 市场价 1现价 2 成本价
	CloudSettlementMarketingCoefficient string `json:"cloud_settlement_marketing_coefficient"` //市场价订价系数
	CloudSettlementPriceCoefficient     string `json:"cloud_settlement_price_coefficient"`     //现价订价系数
	CloudSettlementCostCoefficient      string `json:"cloud_settlement_cost_coefficient"`      //成本价订价系数

}
type BaseInfoData struct {
	Host      string `json:"host"`
	ApiUrl    string `json:"apiUrl"`
	AppKey    string `json:"appKey"`
	AppSecret string `json:"appSecret"`
}

// 订单售后策略
type OrderAfterSalesStrategy struct {
	Type int `json:"type"` //订单售后类型 0自动提供 1审核后提交
}

type SourceMap struct {
	SourceID            uint  `json:"source_id"`
	ApplicationLevelIDs []int `json:"application_level_ids"`
}
type StorageAuth struct {
	ApplicationLevels []SourceMap `json:"application_level_ids"`
}
type GatherSupplyApplicationLevel struct {
	source.Model
	GatherSupplyID     uint `json:"gather_supply_id"`
	ApplicationLevelID uint `json:"application_level_id"`
	SourceID           uint `json:"source_id"`
}

func SetSetting(set *model.SysSetting) (err error) {
	var sysSetting *model.SysSetting
	if err = source.DB().Where("`key` = ?", set.Key).First(&sysSetting).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}

	// 更新时，保留敏感字段原值
	if sysSetting.ID > 0 {
		// 解析新设置的 Value 为 map
		var oldValue map[string]interface{}
		if err = json.Unmarshal([]byte(sysSetting.Value), &oldValue); err != nil {
			return
		}

		oldBaseInfo, _ := oldValue["baseInfo"].(map[string]interface{})

		// 解析新设置的 Value 为 map
		var newValue map[string]interface{}
		if err = json.Unmarshal([]byte(set.Value), &newValue); err != nil {
			return
		}

		// 将脱敏字段保持原值
		if newBaseInfo, ok := newValue["baseInfo"].(map[string]interface{}); ok {
			for _, k := range settingMaskFields() {
				_, nExist := newBaseInfo[k]
				if !nExist {
					continue
				}

				oValue, oExist := oldBaseInfo[k]
				if !oExist {
					continue
				}

				newBaseInfo[k] = oValue
			}
		}

		// 将处理后的 newValue 重新序列化为 JSON
		var newValueByte []byte
		if newValueByte, err = json.Marshal(newValue); err != nil {
			return
		}

		set.Value = string(newValueByte)
	}

	if sysSetting.Key == "" {
		err = source.DB().Create(&set).Error

	} else {
		sysSetting.Value = set.Value
		source.DB().Omit("created_at").Save(sysSetting)

		var setting SupplySetting
		err = json.Unmarshal([]byte(sysSetting.Value), &setting)

		gatherSupplyString := sysSetting.Key[12:]
		var gatherSupplyID int
		gatherSupplyID, err = strconv.Atoi(gatherSupplyString)

		var allApplicationLevelIDs []uint
		err = source.DB().Model(&service.ApplicationLevel{}).Pluck("id", &allApplicationLevelIDs).Error

		var GatherSupplyApplicationLevels []GatherSupplyApplicationLevel
		for _, v := range setting.StorageAuth.ApplicationLevels {
			for _, cv := range v.ApplicationLevelIDs {
				if cv == 0 {
					for _, appLevelID := range allApplicationLevelIDs {
						GatherSupplyApplicationLevels = append(GatherSupplyApplicationLevels, GatherSupplyApplicationLevel{
							GatherSupplyID:     uint(gatherSupplyID),
							ApplicationLevelID: appLevelID,
							SourceID:           v.SourceID,
						})
					}
					break
				} else {
					GatherSupplyApplicationLevels = append(GatherSupplyApplicationLevels, GatherSupplyApplicationLevel{
						GatherSupplyID:     uint(gatherSupplyID),
						ApplicationLevelID: uint(cv),
						SourceID:           v.SourceID,
					})
				}

			}

		}
		err = source.DB().Where("gather_supply_id = ?", gatherSupplyID).Delete(&GatherSupplyApplicationLevel{}).Error

		if len(GatherSupplyApplicationLevels) > 0 {
			err = source.DB().Create(&GatherSupplyApplicationLevels).Error
			if err != nil {
				return
			}
		}
		cache.ClearAllApplicationLevelSources()
	}
	var ctx = context.Background()

	source.Redis().Del(ctx, "lianlianSetting")

	return
}

func GetSetting(key string) (err error, setting model.SysSetting) {
	err = source.DB().Where("`key` = ?", key).First(&setting).Error

	return
}

func ShowSetting(key string) (err error, setting model.SysSetting) {
	if err, setting = GetSetting(key); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil
		}
		return
	}

	// 将 setting.Value 解析为 map
	var settingValue = make(map[string]interface{})
	if err = json.Unmarshal([]byte(setting.Value), &settingValue); err != nil {
		return
	}

	// 将 baseInfo 内容根据 settingMaskFields 进行脱敏
	if baseInfo, ok := settingValue["baseInfo"].(map[string]interface{}); ok {
		for _, m := range settingMaskFields() {
			for k, v := range baseInfo {
				if k == m {
					baseInfo[k] = utils.MaskMiddle(fmt.Sprintf("%v", v), 2, 2)
				}
			}
		}
	}

	// 将处理后的 settingValue 重新序列化为 JSON
	var settingValueByte []byte
	if settingValueByte, err = json.Marshal(settingValue); err != nil {
		return
	}

	// 将处理后的 JSON 赋值给 setting.Value
	setting.Value = string(settingValueByte)

	return
}

type UpdateMask struct {
	Key   string `json:"key"`
	Field string `json:"field"`
	Value string `json:"value"`
}

func UpdateSettingMask(p UpdateMask) (err error) {
	// 验证提交字段是否在掩码字段中
	if !utils.IsStringInSlice(p.Field, settingMaskFields()) {
		err = errors.New("字段验证错误，请重试")
		return
	}

	// 查询设置
	var sysSetting model.SysSetting
	if err, sysSetting = GetSetting(p.Key); err != nil {
		return
	}

	// 解析新设置的 Value 为 map
	var value map[string]interface{}
	if err = json.Unmarshal([]byte(sysSetting.Value), &value); err != nil {
		return
	}

	// 根据字段名设置字段的值
	if baseInfo, ok := value["baseInfo"].(map[string]interface{}); ok {
		for k, _ := range baseInfo {
			if k == p.Field {
				baseInfo[k] = p.Value
			}
		}
	}

	// 将处理后的 settingValue 重新序列化为 JSON
	var valueByte []byte
	if valueByte, err = json.Marshal(value); err != nil {
		return
	}

	sysSetting.Value = string(valueByte)

	if err = source.DB().Omit("created_at").Save(sysSetting).Error; err != nil {
		return
	}

	return
}

// 敏感字段
func settingMaskFields() []string {
	return []string{"appSecret", "appsecret", "distributor_appsecret", "passWord", "password", "client_id", "client_secret", "ticket_number"}
}
