package request

import (
	userModel "user/model"
	yzRequest "yz-go/request"
)

type AccountRequest struct {
	AccountName string `json:"account_name"`
}

type AccountSearch struct {
	Uid      uint   `json:"uid"`
	Mobile   string `json:"mobile" form:"mobile" gorm:"column:mobile;comment:手机号;type:char(20);size:20;"`
	NickName string `json:"nickname" form:"nickName" gorm:"column:nick_name;comment:用户昵称;type:varchar(50);size:50;"`
	yzRequest.PageInfo
}
type UserBalanceSearch struct {
	MinBalance    uint   `json:"min_balance" form:"min_balance"`
	MaxBalance    uint   `json:"max_balance" form:"max_balance"`
	BusinessType  int    `json:"business_type" form:"business_type"`
	TimeS         string `json:"times" form:"times" gorm:"column:times;comment:起始时间;type:varchar(255);size:255;"`
	TimeE         string `json:"timee" form:"timee" gorm:"column:timee;comment:结束时间;type:varchar(255);size:255;"`
	Type          int    `json:"type" form:"type" gorm:"column:type;comment:类型;type:varchar(200);size:200;"`
	SettID        int    `json:"sett_id" form:"sett_id" gorm:"column:type;comment:结算ID;type:varchar(200);size:200;"`
	PayType       int    `json:"pay_type" form:"pay_type" gorm:"column:type;comment:支付方式;type:varchar(200);size:200;"`
	Level         int    `json:"level" form:"level" gorm:"column:level;comment:会员等级;type:varchar(200);size:200;"`
	OrderSn       int    `json:"order_sn" form:"order_sn" gorm:"column:order_sn;"`
	SupplierID    uint   `json:"supplier_id" form:"supplier_id"`
	PetSupplierID uint   `json:"pet_supplier_id" form:"pet_supplier_id"`
	WithdrawalID  uint   `json:"withdrawal_id" form:"withdrawal_id"`
	userModel.User

	yzRequest.PageInfo
}
