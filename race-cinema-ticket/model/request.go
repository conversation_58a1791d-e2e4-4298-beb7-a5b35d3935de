package model

type RegionsData struct {
	Code string `json:"code"`
}

type RequestId struct {
	ID uint `json:"id"`
}

type RefundData struct {
	OrderSN uint `json:"order_sn"`
}

type MovieListData struct {
	Page     int    `json:"page" form:"page" query:"page"` //页码
	Size     int    `json:"size" form:"size" query:"size"` //数量
	Keywords string `json:"keywords"`
	Type     string `json:"type"`
	CityCode string `json:"cityCode"`
}
type MovieDatesData struct {
	CityCode string `json:"city_code"`
	MovieId  string `json:"movie_id"`
}
type MovieCinemaListData struct {
	MovieDatesData
	RegionCode   string `json:"regionCode"`
	CinemaLineId string `json:"cinemaLineId"`
	Keywords     string `json:"keywords"`
	Page         int    `json:"page"`
	Size         int    `json:"size"`
	Date         string `json:"date"`
	Longitude    string `json:"longitude"`
	Latitude     string `json:"latitude"`
}
type SearchCinemaListData struct {
	CityCode     string `json:"cityCode"`
	RegionCode   string `json:"regionCode"`
	CinemaLineId string `json:"cinemaLineId"`
	Keywords     string `json:"keywords"`
	Page         int    `json:"page"`
	Longitude    string `json:"longitude"`
	Latitude     string `json:"latitude"`
	Size         int    `json:"size"`
}

type CinemaMoviesData struct {
	CinemaId string `json:"cinema_id"`
}
type ShowSeatsData struct {
	ShowId string `json:"show_id"`
}
type MovieOrderCheckData struct {
	ShowSeatsData
	SeatIds []string `json:"seat_ids"`
}

//
//type CallbackNotificationData struct {
//	SelectOrderData
//	Tickets string `json:"tickets"  ` //
//
//}

type OrderSubmitData struct {
	CinemaId  string `json:"cinema_id"  binding:"required" err:"影院id不能空"`
	MovieId   string `json:"movie_id" binding:"required" err:"影片id不能空"`
	UserID    uint   `json:"user_id"  `
	AppID     uint   `json:"app_id"  `
	AppShopID uint   `json:"app_shop_id"  `
	MovieOrderCheckData
	ChannelOrderNo  string  `json:"channel_order_no" binding:"required" err:"单号不能空"`
	Mobile          string  `json:"mobile" `
	AllowChanged    string  `json:"allow_changed"`
	MovieTitle      string  `json:"movie_title" binding:"required" err:"影片标题不能空"`
	CinemaName      string  `json:"cinema_name" binding:"required" err:"影院名称不能空"`
	MoviePic        string  `json:"movie_pic"  binding:"required" err:"影片图片不能空"`
	NickName        string  `json:"nick_name"`
	Quantity        uint    `json:"quantity" binding:"required" err:"数量不能空"`
	OriginPrice     float64 `json:"origin_price"`
	SettlePrice     float64 `json:"settle_price"`
	AccountsPayable float64 `json:"accounts_payable"`
	TotalPrice      float64 `json:"total_price"`
	SalePrice       float64 `json:"sale_price"`
	CityCode        string  `json:"city_code" binding:"required" err:"城市编码不能空"`
	RegionCode      string  `json:"region_code" binding:"required" err:"区县编码不能空"`
	CityName        string  `json:"city_name" binding:"required" err:"城市名称不能空"`
	RegionName      string  `json:"region_name" binding:"required" err:"区县名称不能空"`
	CinemaAddress   string  `json:"cinema_address" binding:"required" err:"影院地址不能空"`
}

type ShowListData struct {
	CinemaId string `json:"cinema_id"`
	MovieId  string `json:"movieId"`
	Date     string `json:"date"`
}
type GetOrderData struct {
	ChannelOrderNo string `json:"channel_order_no"`
	OutOrderNo     string `json:"out_order_no"`
}
