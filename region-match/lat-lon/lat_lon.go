package lat_lon

import (
	"context"
	"fmt"
	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"
	"yz-go/source"
)

type LocationDoc struct {
	ProvinceID   string            `json:"province_id"`
	ProvinceName string            `json:"province_name"`
	CityID       string            `json:"city_id"`
	CityName     string            `json:"city_name"`
	DistrictID   string            `json:"district_id"`
	DistrictName string            `json:"district_name"`
	StreetID     string            `json:"street_id"`
	StreetName   string            `json:"street_name"`
	Location     *elastic.GeoPoint `json:"location"`
}

func SaveLatLonToEsIndex(index string, data []LocationDoc) error {
	es, err := source.ES()
	if err != nil {
		return err
	}
	exists, err := es.IndexExists(index).Do(context.Background())
	if err != nil {
		return err
	}
	if exists {
		_, err = es.DeleteIndex(index).Do(context.Background())
		if err != nil {
			return err
		}
	}

	// 创建索引并指定 location 字段为 geo_point
	mapping := `
{
  "mappings": {
    "properties": {
      "province_id":   { "type": "keyword" },
      "province_name": { "type": "text" },
      "city_id":       { "type": "keyword" },
      "city_name":     { "type": "text" },
      "district_id":   { "type": "keyword" },
      "district_name": { "type": "text" },
      "street_id":     { "type": "keyword" },
      "street_name":   { "type": "text" },
      "location": {
        "type": "geo_point"
      }
    }
  }
}`

	_, err = es.CreateIndex(index).BodyString(mapping).Do(context.Background())
	if err != nil {
		return fmt.Errorf("创建索引失败: %w", err)
	}

	bulkRequest := es.Bulk().Index(index)
	for i := range data {
		doc := elastic.NewBulkIndexRequest().Id(data[i].StreetID).Doc(data[i])
		bulkRequest = bulkRequest.Add(doc)
	}
	//执行es新建
	if len(data) > 0 {
		_, err = bulkRequest.Do(context.Background())
		if err != nil {
			fmt.Println("执行es操作出错："+err.Error(), zap.Any("err", err))
			return err
		}
	}

	return nil
}
