package models

import "github.com/olivere/elastic/v7"

type LocationDoc struct {
	ProvinceID   string            `json:"province_id"`
	ProvinceName string            `json:"province_name"`
	CityID       string            `json:"city_id"`
	CityName     string            `json:"city_name"`
	DistrictID   string            `json:"district_id"`
	DistrictName string            `json:"district_name"`
	StreetID     string            `json:"street_id"`
	StreetName   string            `json:"street_name"`
	Location     *elastic.GeoPoint `json:"location"`
}
