package models

import (
	_ "embed"
	"go.uber.org/zap"
	"region-match/region"
	regionYyt "region-match/region-yyt"
	"yz-go/component/log"
	"yz-go/source"
)

func Migrate() (err error) {
	// 中台供应链地址经纬度
	go func() {
		err = source.RunIfEsIndexCreatedAtBefore("yz_lat_lon", 2025, 5, 25, 12, 12, func() {
			err = region.MigrateStreetLatLon()
			if err != nil {
				log.Log().Error("region-match 初始化 yz_lat_lon 失败", zap.Any("err", err))
			}
		})
	}()

	// 怡亚通供应链地址经纬度
	go func() {
		err = source.RunIfEsIndexCreatedAtBefore("yyt_lat_lon", 2025, 5, 25, 12, 12, func() {
			err = regionYyt.MigrateStreetLatLon()
			if err != nil {
				log.Log().Error("region-match 初始化 yyt_lat_lon 失败", zap.Any("err", err))
			}
		})
	}()
	return
}
