package region_yyt

import (
	"bytes"
	_ "embed"
	"encoding/csv"
	"github.com/olivere/elastic/v7"
	latLon "region-match/lat-lon"
	"strconv"
)

//go:embed street_lat_lon.csv
var streetLatLon []byte

func MigrateStreetLatLon() (err error) {
	data, err := GetStreetLatLonFromCsv()
	if err != nil {
		return
	}

	return latLon.SaveLatLonToEsIndex("yyt_lat_lon", data)
}

func GetStreetLatLonFromCsv() (data []latLon.LocationDoc, err error) {
	reader := csv.NewReader(bytes.NewReader(streetLatLon))

	rows, err := reader.ReadAll()
	if err != nil {
		return
	}

	if len(rows) > 0 {
		rows = rows[1:]
	}

	for _, row := range rows {
		lat, _ := strconv.ParseFloat(row[8], 64)
		lon, _ := strconv.ParseFloat(row[9], 64)

		locationDoc := latLon.LocationDoc{
			ProvinceID:   row[0],
			ProvinceName: row[1],
			CityID:       row[2],
			CityName:     row[3],
			DistrictID:   row[4],
			DistrictName: row[5],
			StreetID:     row[6],
			StreetName:   row[7],
			Location: &elastic.GeoPoint{
				Lat: lat,
				Lon: lon,
			},
		}
		data = append(data, locationDoc)
	}
	return data, nil
}
