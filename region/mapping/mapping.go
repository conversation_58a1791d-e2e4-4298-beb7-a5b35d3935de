package mapping

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"
	"strconv"
	"strings"
	"yz-go/component/log"
	"yz-go/source"
)

type Region struct {
	ID        int      `json:"id"`
	ParentID  int      `json:"-"`
	Name      string   `json:"name"`
	RegionIDs []int    `json:"region_ids"`
	Names     []string `json:"names"`
	FirstName string   `json:"first_name"`
	FullName  string   `json:"full_name"`
	Parent    *Region  `json:"-"`
	Score     float64  `json:"score"` // 匹配分数
	HasChild  bool     `json:"-"`     // 是否有子地区
}

func (receiver *Region) Fill() (err error) {
	// 从当前地区开始，一直向上查找父级地区，直到找不到父级地区
	var parentIDs []int
	var Names []string

	parentIDs = append(parentIDs, receiver.ID)
	Names = append(Names, receiver.Name)
	parentRegion := receiver.Parent
	for parentRegion.ID != 0 {
		parentIDs = append([]int{parentRegion.ID}, parentIDs...)
		Names = append([]string{parentRegion.Name}, Names...)
		parentRegion = parentRegion.Parent
	}
	receiver.RegionIDs = parentIDs
	receiver.Names = Names
	receiver.FullName = strings.Join(Names, ",")
	receiver.FirstName = Names[0]
	return nil
}

type Regions []Region

func (receiver Regions) GetLeafRegions() (regions Regions) {
	for i := range receiver {
		if !receiver[i].HasChild {
			regions = append(regions, receiver[i])
		}
	}
	return
}
func (receiver Regions) FillRegionParent(regionMap map[int]*Region) (err error) {
	// 遍历每个地区，为每个地区找到其父级地区
	for i := range receiver {
		// 获取当前地区的父级地区 ID
		parentID := receiver[i].ParentID
		// 根据父级地区 ID 从地区映射中获取父级地区结构体指针
		parentRegion, ok := regionMap[parentID]
		if !ok {
			parentRegion = &Region{}
		}
		// 将找到的父级地区设置为当前地区的 Parent 字段
		receiver[i].Parent = parentRegion

		parentRegion.HasChild = true

		// 更新父级地区 ID，继续查找更高级的父级地区
		parentID = parentRegion.ParentID
	}
	for i := range receiver {
		// 为每个地区填充 RegionIDs 和 FullName 字段
		err = receiver[i].Fill()
	}

	return nil
}
func SaveRegions(indexName string, regions Regions) (err error) {
	if err != nil {
		println(err)
	}
	// 创建一个地区 ID 到地区结构体的映射
	regionMap := make(map[int]*Region)
	for i := range regions {
		regionMap[regions[i].ID] = &regions[i]
	}

	// 为每个地区找到其父级地区
	err = regions.FillRegionParent(regionMap)
	if err != nil {
		return
	}
	// 获取所有最低层级的地区
	regions = regions.GetLeafRegions()
	// 保存到 es
	err = SaveRegionsToEsIndex(indexName, regions)
	if err != nil {
		return
	}
	println(len(regions))
	return
}
func SaveRegionsToEsIndex(indexName string, regions Regions) (err error) {
	es, err := source.ES()
	if err != nil {
		return
	}
	exists, err := es.IndexExists(indexName).Do(context.Background())
	if err != nil {
		return
	}
	if exists {
		_, err = es.DeleteIndex(indexName).Do(context.Background())
		if err != nil {
			return
		}
	}

	//// 重新创建索引
	//_, err = es.CreateIndex(indexName).BodyString(
	//	fmt.Sprintf(`{
	//	  "settings": {
	//		"analysis": {
	//		  "analyzer": {
	//			"default": {
	//			  "type": "%s"
	//			}
	//		  }
	//		}
	//	  },
	//	  "mappings": {
	//		"properties": {
	//		  "full_name": {
	//			"type": "text",
	//			"analyzer": "%s",
	//			"search_analyzer": "%s"
	//		  }
	//		}
	//	  }
	//	}`, "ik_smart", "ik_smart", "ik_smart")).
	//	Do(context.Background())
	//if err != nil {
	//	return
	//}
	bulkRequest := es.Bulk().Index(indexName)
	for i := range regions {
		doc := elastic.NewBulkIndexRequest().Id(strconv.Itoa(regions[i].ID)).Doc(regions[i])
		bulkRequest = bulkRequest.Add(doc)

	}
	//执行es新建
	if len(regions) > 0 {
		_, err = bulkRequest.Do(context.Background())
		if err != nil {
			fmt.Println("执行es操作出错："+err.Error(), zap.Any("err", err))
			log.Log().Error("执行es操作出错："+err.Error(), zap.Any("err", err))
		}
	}
	return
}
func HasMatchingRegionsWhichScoreGreaterThan(indexName string, fullName string, score float64) (bool, error) {
	es, err := source.ES()
	if err != nil {
		return false, err
	}
	// 创建一个搜索请求
	boolQuery := elastic.NewBoolQuery().
		Must(
			elastic.NewMatchQuery("full_name", fullName),
		)
	searchResult, err := es.Search().Index(indexName).Query(boolQuery).MinScore(score).Do(context.Background())
	if err != nil {
		return false, err
	}
	return len(searchResult.Hits.Hits) > 0, nil
}

func GetMatchingRegionsTranslateBy(indexName string, fullName string, name string, translateIndexName string) (regions []Region, err error) {
	translateRegions, err := GetMatchingRegionsByText(translateIndexName, fullName, name)
	if err != nil {
		return
	}
	if len(translateRegions) == 0 {
		return GetMatchingRegionsByText(indexName, fullName, name)
	}
	return GetMatchingRegionsByText(indexName, translateRegions[0].FullName, translateRegions[0].Name)
}
func GetMatchingRegionsByText(indexName string, fullName string, name string) (regions []Region, err error) {
	es, err := source.ES()
	if err != nil {
		return
	}
	var firstName string

	// 创建一个搜索请求
	// 将fullName按照逗号分割成数组
	fullNames := strings.Split(fullName, ",")
	// 遍历fullName数组，如果包含"直辖"字符，则删除这个元素，然后重新拼接成字符串
	for i := 0; i < len(fullNames); i++ {
		// 如果是第一个元素，则将firstName设置为这个元素
		if i == 0 {
			firstName = fullNames[i]
		}
		if strings.Contains(fullNames[i], "直辖") {
			fullNames = append(fullNames[:i], fullNames[i+1:]...)
		}
	}
	// 如果name包含"直辖"字符，则将name置为空
	if strings.Contains(name, "直辖") {
		name = ""
	}
	boolQuery := elastic.NewBoolQuery().
		Must(
			elastic.NewMatchQuery("first_name", firstName).Boost(2.0),
			//elastic.NewMatchQuery("name", name),
			elastic.NewMatchQuery("full_name", fullName),
		)
	searchResult, err := es.Search().Index(indexName).Query(boolQuery).Do(context.Background())
	if err != nil {
		return
	}
	// 遍历搜索结果
	for _, hit := range searchResult.Hits.Hits {
		var region Region
		err = json.Unmarshal(hit.Source, &region)
		if err != nil {
			return
		}
		region.Score = *hit.Score
		regions = append(regions, region)
	}
	return
}
