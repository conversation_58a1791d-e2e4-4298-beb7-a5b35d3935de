package model

import (
	_ "embed"
	"encoding/json"
	"errors"
	"github.com/gookit/color"
	"go.uber.org/zap"
	"gorm.io/gorm"
	region2 "region/region"
	"strconv"
	"yz-go/component/log"
	"yz-go/source"
)

//go:embed menu.json
var menu string

//go:embed regions.json
var regions string

type JRegion struct {
	Code     string
	Name     string
	Children []JRegion
}

func (jRegion JRegion) ToRegions(parentId, level int) (regions []Region) {
	jRegionId, err := strconv.Atoi(jRegion.Code)
	if err != nil {
		return
	}
	region := Region{
		ID:       jRegionId,
		Name:     jRegion.Name,
		ParentID: parentId,
		Level:    level,
	}
	// 保存自己
	regions = append(regions, region)
	if len(jRegion.Children) > 0 {
		// 保存子区域
		for _, _jRegion := range jRegion.Children {
			regions = append(regions, _jRegion.ToRegions(jRegionId, level+1)...)
		}
	}
	return
}
func Migrate() (err error) {
	err = source.DB().AutoMigrate(
		Region{},
	)
	if err != nil {
		color.Error.Println("Migrate Region错误 ", err.Error())
		return
	}
	var count int64
	err = source.DB().Model(&Region{}).Count(&count).Error
	if err != nil {
		color.Error.Println("Migrate Region Count错误 ", err.Error())
		return
	}

	if count == 0 {
		var jRegions []JRegion
		err = json.Unmarshal([]byte(regions), &jRegions)
		if err != nil {
			color.Error.Println("Migrate Region ReadFile错误 ", err.Error())
			return
		}
		for _, jRegion := range jRegions {
			regions := jRegion.ToRegions(0, 1)
			err = source.DB().CreateInBatches(regions, 1000).Error
			if err != nil {
				color.Error.Println("Migrate Region CreateInBatches错误 ", err.Error())
				return
			}
		}
	}
	//这些可以屏蔽，客户已经都有这些地址了。新客户不需要这些
	//var region Region
	//err = source.DB().Where("parent_id = 3301").Where("name = ?", "临平区").First(&region).Error
	////不存在
	//if region.ID == 0 && errors.Is(err, gorm.ErrRecordNotFound) {
	//	err = addRegin("浙江省", "杭州市", "临平区", "", 3)
	//}
	//var SanYaRegion Region
	////增加三级 的三亚市
	//err = source.DB().Where("id = 659004505").First(&SanYaRegion).Error
	//if SanYaRegion.ID == 0 && errors.Is(err, gorm.ErrRecordNotFound) {
	//	err = addRegin("海南省", "三亚市", "三亚市", "", 3)
	//}
	//些可以屏蔽，客户已经都有这些地址了。新客户不需要这些
	//var tairegion Region
	////台湾省
	//err = source.DB().Where("id = 710000").First(&tairegion).Error
	////不存在
	//if tairegion.ID == 0 && errors.Is(err, gorm.ErrRecordNotFound) {
	//	err = addRegin("台湾", "", "", "", 1)
	//	err = addRegin("澳门特别行政区", "", "", "", 1)
	//	err = addRegin("香港特别行政区", "", "", "", 1)
	//}

	var haiNanregion Region
	//海南省 -- 珠海市  //增加市的同名县
	err = source.DB().Where("id = 469027000").First(&haiNanregion).Error
	//不存在
	if haiNanregion.ID == 0 && errors.Is(err, gorm.ErrRecordNotFound) {
		err = source.DB().Model(&Region{}).Where("id = 46 or id = 460000 or parent_id like '46%' ").Delete(&Region{}).Error
		if err != nil {
			log.Log().Error("删除旧海南省失败", zap.Any("err", err))
		}
		err = addRegin("海南省", "", "", "", 1)
		if err != nil {
			log.Log().Error("创建海南省失败", zap.Any("err", err))
		}
	}
	go func() {
		err = source.RunIfEsIndexCreatedAtBefore("ali_region", 2024, 10, 15, 15, 16, func() {
			//初始化地址
			err = region2.LoadAliRegionsFromCSV()
			if err != nil {
				log.Log().Error("初始化地址失败", zap.Any("err", err))
			}
		})

	}()
	go func() {
		err = source.RunIfEsIndexCreatedAtBefore("youxuan_region", 2024, 10, 15, 15, 16, func() {
			//初始化地址
			err = region2.LoadYouxuanRegionsFromCSV()
			if err != nil {
				log.Log().Error("初始化地址失败", zap.Any("err", err))
			}
		})

	}()
	go func() {
		err = source.RunIfEsIndexCreatedAtBefore("gat_region", 2024, 10, 15, 15, 16, func() {
			//初始化地址
			err = region2.LoadGuanaitongRegionsFromCSV()
			if err != nil {
				log.Log().Error("初始化地址失败", zap.Any("err", err))
			}
		})

	}()
	go func() {
		err = source.RunIfEsIndexCreatedAtBefore("yz_region", 2024, 10, 15, 15, 16, func() {
			//初始化地址
			err = region2.LoadRegionsFromCSV()
			if err != nil {
				log.Log().Error("初始化地址失败", zap.Any("err", err))
			}
		})

	}()
	return
}

/*
*
province  省名称
city  市名称
county  区名称
town  街道名称
level  层级， 如果是新增区及区以下的就是3 市及市一下就是2
*/
func addRegin(province string, city string, county string, town string, level int) (err error) {
	var jRegions []JRegion
	err = json.Unmarshal([]byte(regions), &jRegions)
	if err != nil {
		color.Error.Println("Migrate Region ReadFile错误 ", err.Error())
		return
	}
	for _, jRegion := range jRegions {
		if jRegion.Name == province {
			if level == 1 {
				if jRegion.Name == province {
					code := 0
					regions := jRegion.ToRegions(code, 1)
					err = source.DB().CreateInBatches(regions, 1000).Error
					break
				}
				continue
			}
			for _, sRegions := range jRegion.Children {
				if sRegions.Name == city {
					if level == 2 {
						if sRegions.Name == city {
							code, _ := strconv.Atoi(jRegion.Code)
							regions := sRegions.ToRegions(code, 2)
							err = source.DB().CreateInBatches(regions, 1000).Error
							break
						}
						continue
					}
					for _, qRegions := range sRegions.Children {
						if qRegions.Name == county {
							if level == 3 {
								if sRegions.Name == city {
									code, _ := strconv.Atoi(sRegions.Code)
									regions := qRegions.ToRegions(code, 3)
									err = source.DB().CreateInBatches(regions, 1000).Error
									break
								}
								continue
							}
						}
						for _, tRegions := range qRegions.Children {
							if tRegions.Name == town {
								if level == 4 {
									if tRegions.Name == town {
										code, _ := strconv.Atoi(qRegions.Code)
										regions := tRegions.ToRegions(code, 4)
										err = source.DB().CreateInBatches(regions, 1000).Error
										break
									}
								}

							}

						}

					}
				}
			}
			break
		}
	}
	return nil
}
