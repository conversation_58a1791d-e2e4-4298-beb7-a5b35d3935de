package model

import (
	"context"
	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"
	"testing"
	"yz-go/component/log"
)

func TestName(t *testing.T) {
	err := Migrate()
	if err != nil {
		t.Fatal(err)
	}
}

//	func TestJson(t *testing.T) {
//		var regions []JRegion
//		var provinces []Region
//		err := source.DB().Where("level = ?", 1).Find(provinces).Error
//		if err != nil {
//			t.Fatal(err)
//		}
//		for _, province := range provinces {
//			var jProvince = JRegion{
//				Code: strconv.Itoa(province.ID),
//				Name: province.Name,
//			}
//			var cities []Region
//			err = source.DB().Where("parent_id = ?", province.ID).Where("level = ?", 2).Find(cities).Error
//			if err != nil {
//				t.Fatal(err)
//			}
//			for _, city := range cities {
//				var jCity = JRegion{
//					Code: strconv.Itoa(city.ID),
//					Name: city.Name,
//				}
//				var counties []Region
//				err = source.DB().Where("parent_id = ?", city.ID).Where("level = ?", 3).Find(counties).Error
//				if err != nil {
//					t.Fatal(err)
//				}
//				regions = append(regions,jProvince)
//			}
//			regions = append(regions,jProvince)
//		}
//	}
func TestMigrate(t *testing.T) {
	var es *elastic.Client
	_, err := es.DeleteIndex("a").Do(context.Background())
	if err != nil {
		log.Log().Error("删除索引失败", zap.Any("err", err))
	}

}
