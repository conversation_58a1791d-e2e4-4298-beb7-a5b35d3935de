package service

import (
	"fmt"
	"testing"
)

func TestGet(t *testing.T) {
	regionMap := map[int]string{}
	regionMap[460105100] = "长安街"
	fmt.Println(regionMap[460105100])
}
func TestGetRegionName(t *testing.T) {
	type args struct {
		id int
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			args: args{
				id: 460105100,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetRegionName(tt.args.id)
			if got != tt.want {
				t.Errorf("GetRegionName() = %v, want %v", got, tt.want)
			}
			println(got)
		})
	}
}
