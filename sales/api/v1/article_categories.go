package v1

import (
	v1 "gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"sales/model"
	"sales/request"
	"sales/service"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	service2 "yz-go/service"
	"yz-go/source"
)

// @Tags 文章分类
// @Summary 创建
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ArticleCategories true "创建"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /sales/createArticleCategories [post]
func CreateArticleCategories(c *gin.Context) {
	var articleCategoriesSearch model.ArticleCategories

	var err error
	err = c.ShouldBindJSON(&articleCategoriesSearch)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err := service.CreateArticleCategories(articleCategoriesSearch); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 7, c.ClientIP(), "新增文章分类'"+articleCategoriesSearch.Title+"'")
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// @Tags 文章分类
// @Summary 删除
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ArticleCategories true "删除"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /sales/deleteArticleCategories [post]
func DeleteArticleCategories(c *gin.Context) {
	var articleCategorieSearch model.ArticleCategories
	var err error
	err = c.ShouldBindJSON(&articleCategorieSearch)
	err = source.DB().First(&articleCategorieSearch, articleCategorieSearch.ID).Error
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err = service.DeleteArticleCategories(articleCategorieSearch); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 7, c.ClientIP(), "删除文章分类'"+articleCategorieSearch.Title+"'")
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// @Tags 文章分类
// @Summary 更新
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ArticleCategories true "更新"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /sales/updateArticleCategories [post]
func UpdateArticleCategories(c *gin.Context) {
	var articleCategoriesSearch model.ArticleCategories
	var err error
	err = c.ShouldBindJSON(&articleCategoriesSearch)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateArticleCategories(articleCategoriesSearch); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 7, c.ClientIP(), "修改文章分类'"+articleCategoriesSearch.Title+"'")
		yzResponse.OkWithMessage("更新成功", c)
	}
}

// @Tags 文章分类
// @Summary 用id查询
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ArticleCategories true "用id查询"
// @Success 200 {object} model.ArticleCategories
// @Router /sales/findArticleCategories [post]
func FindArticleCategoriesByID(c *gin.Context) {
	var articleCategoriesSearch model.ArticleCategories
	var err error
	err = c.ShouldBindQuery(&articleCategoriesSearch)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, articleCategories := service.FindArticleCategoriesByID(articleCategoriesSearch.ID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"rearticleCategoriesSearch": articleCategories}, c)
	}
}

// @Tags 文章分类
// @Summary 分页获取列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ArticleCategoriesSearch true "分页获取列表"
// @Success 200 {string} string []model.ArticleCategories
// @Router /sales/getArticleCategoriesList [post]
func GetArticleCategoriesList(c *gin.Context) {
	var pageInfo request.ArticleCategoriesSearch
	var err error
	err = c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetArticleCategoriesInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// @Tags 文章分类
// @Summary 用id查询
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ArticleCategories true "用id查询"
// @Success 200 {object} model.ArticleCategories
// @Router /sales/findArticleCategories [post]
func GetArticleCategories(c *gin.Context) {
	var articleCategoriesSearch request.ArticleCategories
	var err error
	err = c.ShouldBindQuery(&articleCategoriesSearch)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, articleCategories := service.GetArticleCategories(articleCategoriesSearch); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"rearticleCategoriesSearch": articleCategories}, c)
	}
}
