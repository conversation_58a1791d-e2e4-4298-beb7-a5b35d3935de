package v1

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	materialDistributeModel "material-distribute/model"
	videoDistributeModel "video-distribute/model"

	productAlbumService "product-album/service"
	"sales/model"
	"sales/request"
	"sales/service"
	"sales/setting"
	shareLiveSetting "share-live/setting"
	"yz-go/component/log"
	yzResponse "yz-go/response"
)

// @Tags 移动端 首页 商品显示设置
// @Summary 移动端 首页 商品显示列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.LinkSearch true "移动端 首页 商品显示列表"
// @Success 200 {string} string []model.WapHomeSetting
// @Router /sales/getWapHomeSettingList [get]
func GetWapHomeSettingList(c *gin.Context) {
	var pageInfo request.WapHomeSettingList
	var err error
	err = c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetWapHomeSettingList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// @Tags 移动端 首页 商品显示设置
// @Summary 移动端 首页 商品显示创建
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.LinkSearch true "移动端 首页 商品显示创建"
// @Success 200 {string} string []model.WapHomeSetting
// @Router /sales/createWapHomeSetting [post]
func CreateWapHomeSetting(c *gin.Context) {
	var wapHomeSetting model.WapHomeSetting
	var err error
	err = c.ShouldBindJSON(&wapHomeSetting)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.CreateWapHomeSetting(wapHomeSetting); err != nil {
		log.Log().Error("创建失败", zap.Any("err", err))
		yzResponse.FailWithMessage("创建失败"+err.Error(), c)
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// @Tags 移动端 首页 商品显示设置
// @Summary 移动端 首页 商品显示修改
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.LinkSearch true "移动端 首页 商品显示修改"
// @Success 200 {string} string []model.WapHomeSetting
// @Router /sales/createWapHomeSetting [post]
func UpdateWapHomeSetting(c *gin.Context) {
	var wapHomeSetting model.WapHomeSetting
	var err error
	err = c.ShouldBindJSON(&wapHomeSetting)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.UpdateWapHomeSetting(wapHomeSetting); err != nil {
		log.Log().Error("修改失败", zap.Any("err", err))
		yzResponse.FailWithMessage("修改失败"+err.Error(), c)
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}

// @Tags 移动端 首页 商品显示设置
// @Summary 移动端 首页 商品显示删除
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.LinkSearch true "移动端 首页 商品显示删除"
// @Success 200 {string} string []model.WapHomeSetting
// @Router /sales/deleteWapHomeSetting [post]
func DeleteWapHomeSetting(c *gin.Context) {
	var wapHomeSetting model.WapHomeSetting
	var err error
	err = c.ShouldBindJSON(&wapHomeSetting)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.DeleteWapHomeSetting(wapHomeSetting); err != nil {
		log.Log().Error("删除失败", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败"+err.Error(), c)
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// @Tags 移动端 首页 商品显示设置
// @Summary 移动端 首页 商品显示列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.LinkSearch true "移动端 首页 商品显示列表"
// @Success 200 {string} string []model.WapHomeSetting
// @Router /sales/getWapDiamondAreaList [get]
func GetWapDiamondAreaList(c *gin.Context) {
	var pageInfo request.WapDiamondAreaList
	var err error
	err = c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetWapDiamondAreaList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// @Tags 移动端 首页 商品显示设置
// @Summary 移动端 首页 商品显示创建
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.LinkSearch true "移动端 首页 商品显示创建"
// @Success 200 {string} string []model.WapDiamondArea
// @Router /sales/createWapDiamondArea [post]
func CreateWapDiamondArea(c *gin.Context) {
	var wapHomeSetting model.WapDiamondArea
	var err error
	err = c.ShouldBindJSON(&wapHomeSetting)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.CreateWapDiamondArea(wapHomeSetting); err != nil {
		log.Log().Error("创建失败", zap.Any("err", err))
		yzResponse.FailWithMessage("创建失败"+err.Error(), c)
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// @Tags 移动端 首页 商品显示设置
// @Summary 移动端 首页 商品显示修改
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.LinkSearch true "移动端 首页 商品显示修改"
// @Success 200 {string} string []model.WapDiamondArea
// @Router /sales/createWapDiamondArea [post]
func UpdateWapDiamondArea(c *gin.Context) {
	var wapHomeSetting model.WapDiamondArea
	var err error
	err = c.ShouldBindJSON(&wapHomeSetting)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.UpdateWapDiamondArea(wapHomeSetting); err != nil {
		log.Log().Error("修改失败", zap.Any("err", err))
		yzResponse.FailWithMessage("修改失败"+err.Error(), c)
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}

// @Tags 移动端 首页 商品显示设置
// @Summary 移动端 首页 商品显示删除
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.LinkSearch true "移动端 首页 商品显示删除"
// @Success 200 {string} string []model.WapDiamondArea
// @Router /sales/deleteWapDiamondArea [post]
func DeleteWapSharedSpecialSession(c *gin.Context) {
	var wapHomeSetting model.WapSharedSpecialSession
	var err error
	err = c.ShouldBindJSON(&wapHomeSetting)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.DeleteWapSharedSpecialSession(wapHomeSetting); err != nil {
		log.Log().Error("删除失败", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败"+err.Error(), c)
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// @Tags 移动端 首页 商品显示设置
// @Summary 移动端 首页 商品显示修改
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.LinkSearch true "移动端 首页 商品显示修改"
// @Success 200 {string} string []model.WapDiamondArea
// @Router /sales/createWapDiamondArea [post]
func UpdateWapSharedSpecialSessionStatus(c *gin.Context) {
	var wapHomeSetting model.WapSharedSpecialSession
	var err error
	err = c.ShouldBindJSON(&wapHomeSetting)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.UpdateWapSharedSpecialSessionStatus(wapHomeSetting); err != nil {
		log.Log().Error("修改失败", zap.Any("err", err))
		yzResponse.FailWithMessage("修改失败"+err.Error(), c)
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}

// @Tags 移动端 首页 商品显示设置
// @Summary 移动端 首页 商品显示修改
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.LinkSearch true "移动端 首页 商品显示修改"
// @Success 200 {string} string []model.WapDiamondArea
// @Router /sales/createWapDiamondArea [post]
func UpdateWapSharedSpecialSessionSort(c *gin.Context) {
	var wapHomeSetting model.WapSharedSpecialSession
	var err error
	err = c.ShouldBindJSON(&wapHomeSetting)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.UpdateWapSharedSpecialSessionSort(wapHomeSetting); err != nil {
		log.Log().Error("修改失败", zap.Any("err", err))
		yzResponse.FailWithMessage("修改失败"+err.Error(), c)
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}

// GetPageSetupSetting @Tags 页面设置
// @Summary 获取页面设置
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "获取页面设置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /sales/getPageSetupSetting [get]
func GetWapProductIntroductionSysSetting(c *gin.Context) {
	var sys setting.WapProductIntroductionSysSetting
	_ = c.ShouldBindJSON(&sys)
	err, pageSetupSetting := setting.GetWapProductIntroductionSysSetting()
	if err != nil {
		yzResponse.FailWithMessage("暂无数据", c)
		return
	}
	yzResponse.OkWithData(pageSetupSetting, c)
}

// @Tags 页面设置
// @Summary 设置页面设置
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "设置页面设置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /sales/setPageSetupSetting [post]
func SetWapProductIntroductionSysSetting(c *gin.Context) {
	var sys setting.WapProductIntroductionSysSetting
	_ = c.ShouldBindJSON(&sys)

	if sys.Value.LiveType == 1 && shareLiveSetting.GetIsOpenShareLiveSetting() == 0 {
		yzResponse.FailWithMessage("共享直播插件未开启", c)
		return
	}

	if sys.Value.MaterialType == 1 && materialDistributeModel.GetIsOpenMaterialSetting() == 0 {
		yzResponse.FailWithMessage("素材分发插件未开启", c)
		return
	}
	if sys.Value.VideoType == 1 && videoDistributeModel.GetIsOpenVideoSetting() == 0 {
		yzResponse.FailWithMessage("视频分发插件未开启", c)
		return
	}

	if err := setting.SaveWapProductIntroductionSetting(sys); err != nil {
		log.Log().Error("设置失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("设置失败", c)
		return
	} else {
		yzResponse.OkWithData("设置成功", c)
	}
}

// GetPageSetupSetting @Tags 页面设置
// @Summary 获取页面设置
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "获取页面设置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /sales/getPageSetupSetting [get]
func GetSharedSpecialSessionSysSetting(c *gin.Context) {
	var sys setting.SharedSpecialSessionSysSetting
	_ = c.ShouldBindJSON(&sys)
	err, pageSetupSetting := setting.GetSharedSpecialSessionSysSetting()
	if err != nil {
		yzResponse.FailWithMessage("暂无数据", c)
		return
	}
	yzResponse.OkWithData(pageSetupSetting, c)
}

// @Tags 页面设置
// @Summary 设置页面设置
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "设置页面设置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /sales/setPageSetupSetting [post]
func SetSharedSpecialSessionSysSetting(c *gin.Context) {
	var sys setting.SharedSpecialSessionSysSetting
	_ = c.ShouldBindJSON(&sys)
	if sys.Value.SharedType == 1 {
		if productAlbumService.IsProductAlbumOpen() == false {
			yzResponse.FailWithMessage("共享专辑插件未开启", c)
			return
		}
	}
	if sys.Value.SharedType == 1 {
		_, wapSharedSpecialSessions := service.GetWapSharedSpecialSessionsAdmin()
		if len(wapSharedSpecialSessions) < 3 {
			yzResponse.FailWithMessage("需要勾选3个展示的专辑才可保存", c)
			return
		}
	}
	if err := setting.SaveSharedSpecialSessionSetting(sys); err != nil {
		log.Log().Error("设置失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("设置失败", c)
		return
	} else {
		yzResponse.OkWithData("设置成功", c)
	}
}

// @Tags 移动端 首页 商品显示设置
// @Summary 移动端 首页 商品显示列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.LinkSearch true "移动端 首页 商品显示列表"
// @Success 200 {string} string []model.WapHomeSetting
// @Router /sales/getWapDiamondAreaList [get]
func GetWapSharedSpecialSessionList(c *gin.Context) {
	var pageInfo request.WapSharedSpecialSessionList
	var err error
	err = c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetWapSharedSpecialSessionList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// @Tags 移动端 首页 商品显示设置
// @Summary 移动端 首页 商品显示创建
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.LinkSearch true "移动端 首页 商品显示创建"
// @Success 200 {string} string []model.WapDiamondArea
// @Router /sales/createWapDiamondArea [post]
func CreateWapSharedSpecialSession(c *gin.Context) {
	var wapHomeSetting model.WapSharedSpecialSession
	var err error
	err = c.ShouldBindJSON(&wapHomeSetting)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.CreateWapSharedSpecialSession(wapHomeSetting); err != nil {
		log.Log().Error("创建失败", zap.Any("err", err))
		yzResponse.FailWithMessage("创建失败"+err.Error(), c)
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// @Tags 移动端 首页 商品显示设置
// @Summary 移动端 首页 商品显示修改
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.LinkSearch true "移动端 首页 商品显示修改"
// @Success 200 {string} string []model.WapDiamondArea
// @Router /sales/createWapDiamondArea [post]
func UpdateWapSharedSpecialSession(c *gin.Context) {
	var wapHomeSetting model.WapSharedSpecialSession
	var err error
	err = c.ShouldBindJSON(&wapHomeSetting)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.UpdateWapSharedSpecialSession(wapHomeSetting); err != nil {
		log.Log().Error("修改失败", zap.Any("err", err))
		yzResponse.FailWithMessage("修改失败"+err.Error(), c)
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}

// @Tags 移动端 首页 商品显示设置
// @Summary 移动端 首页 商品显示删除
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.LinkSearch true "移动端 首页 商品显示删除"
// @Success 200 {string} string []model.WapDiamondArea
// @Router /sales/deleteWapDiamondArea [post]
func DeleteWapDiamondArea(c *gin.Context) {
	var wapHomeSetting model.WapDiamondArea
	var err error
	err = c.ShouldBindJSON(&wapHomeSetting)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.DeleteWapDiamondArea(wapHomeSetting); err != nil {
		log.Log().Error("删除失败", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败"+err.Error(), c)
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// @Tags 移动端 首页 商品显示设置
// @Summary 移动端 首页 商品显示修改
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.LinkSearch true "移动端 首页 商品显示修改"
// @Success 200 {string} string []model.WapDiamondArea
// @Router /sales/createWapDiamondArea [post]
func UpdateWapDiamondAreaStatus(c *gin.Context) {
	var wapHomeSetting model.WapDiamondArea
	var err error
	err = c.ShouldBindJSON(&wapHomeSetting)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.UpdateWapDiamondAreaStatus(wapHomeSetting); err != nil {
		log.Log().Error("修改失败", zap.Any("err", err))
		yzResponse.FailWithMessage("修改失败"+err.Error(), c)
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}

// GetPageSetupSetting @Tags 页面设置
// @Summary 获取页面设置
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "获取页面设置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /sales/getPageSetupSetting [get]
func GetWapStoreSectionSysSetting(c *gin.Context) {
	var sys setting.WapStoreSectionSysSetting
	_ = c.ShouldBindJSON(&sys)
	err, pageSetupSetting := setting.GetWapStoreSectionSysSetting()
	if err != nil {
		yzResponse.FailWithMessage("暂无数据", c)
		return
	}
	yzResponse.OkWithData(pageSetupSetting, c)
}

// @Tags 页面设置
// @Summary 设置页面设置
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "设置页面设置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /sales/setPageSetupSetting [post]
func SetWapStoreSectionSysSetting(c *gin.Context) {
	var sys setting.WapStoreSectionSysSetting
	_ = c.ShouldBindJSON(&sys)

	if err := setting.SaveWapStoreSectionSetting(sys); err != nil {
		log.Log().Error("设置失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("设置失败", c)
		return
	} else {
		yzResponse.OkWithData("设置成功", c)
	}
}

// @Tags 移动端 首页 商品显示设置
// @Summary 移动端 首页 商品显示列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.LinkSearch true "移动端 首页 商品显示列表"
// @Success 200 {string} string []model.WapHomeSetting
// @Router /sales/getWapDiamondAreaList [get]
func GetWapStoreSectionList(c *gin.Context) {
	var pageInfo request.WapStoreSectionList
	var err error
	err = c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetWapStoreSectionList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// @Tags 移动端 首页 商品显示设置
// @Summary 移动端 首页 商品显示创建
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.LinkSearch true "移动端 首页 商品显示创建"
// @Success 200 {string} string []model.WapDiamondArea
// @Router /sales/createWapDiamondArea [post]
func CreateWapStoreSection(c *gin.Context) {
	var wapHomeSetting model.WapStoreSection
	var err error
	err = c.ShouldBindJSON(&wapHomeSetting)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.CreateWapStoreSection(wapHomeSetting); err != nil {
		log.Log().Error("创建失败", zap.Any("err", err))
		yzResponse.FailWithMessage("创建失败"+err.Error(), c)
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// @Tags 移动端 首页 商品显示设置
// @Summary 移动端 首页 商品显示删除
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.LinkSearch true "移动端 首页 商品显示删除"
// @Success 200 {string} string []model.WapDiamondArea
// @Router /sales/deleteWapDiamondArea [post]
func DeleteWapStoreSection(c *gin.Context) {
	var wapHomeSetting model.WapStoreSection
	var err error
	err = c.ShouldBindJSON(&wapHomeSetting)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.DeleteWapStoreSection(wapHomeSetting); err != nil {
		log.Log().Error("删除失败", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败"+err.Error(), c)
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// @Tags 移动端 首页 商品显示设置
// @Summary 移动端 首页 商品显示修改
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.LinkSearch true "移动端 首页 商品显示修改"
// @Success 200 {string} string []model.WapDiamondArea
// @Router /sales/createWapDiamondArea [post]
func UpdateWapStoreSectionStatus(c *gin.Context) {
	var wapHomeSetting model.WapStoreSection
	var err error
	err = c.ShouldBindJSON(&wapHomeSetting)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.UpdateWapStoreSectionStatus(wapHomeSetting); err != nil {
		log.Log().Error("修改失败", zap.Any("err", err))
		yzResponse.FailWithMessage("修改失败"+err.Error(), c)
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}

// @Tags 移动端 首页 商品显示设置
// @Summary 移动端 首页 商品显示修改
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.LinkSearch true "移动端 首页 商品显示修改"
// @Success 200 {string} string []model.WapDiamondArea
// @Router /sales/createWapDiamondArea [post]
func UpdateWapStoreSectionSort(c *gin.Context) {
	var wapHomeSetting model.WapStoreSection
	var err error
	err = c.ShouldBindJSON(&wapHomeSetting)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.UpdateWapStoreSectionSort(wapHomeSetting); err != nil {
		log.Log().Error("修改失败", zap.Any("err", err))
		yzResponse.FailWithMessage("修改失败"+err.Error(), c)
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}
