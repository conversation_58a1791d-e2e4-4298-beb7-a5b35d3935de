package service

import (
	"errors"
	"gorm.io/gorm"
	"sales/model"
	"sales/request"
	"yz-go/source"
)

type MoveParams struct {
	ID          uint   `json:"id" validate:"required"`                         // ID
	ParentId    uint   `json:"parent_id"`                                      // 父级ID
	MoveOperate string `json:"move_operate" validate:"required,oneof=up down"` // up上移 down下移
}

func MoveLink(p MoveParams) (err error) {
	db := searchLinkGorm(request.LinkSearch{
		Link: model.Link{
			ParentID: p.ParentId,
		},
	})

	// 默认排序
	db.Order("sort desc")

	// 格式排序
	if err = formatSort(db); err != nil {
		return
	}

	// 上移、下移
	var currentLink, replaceLink model.Link
	if err = source.DB().Find(&currentLink, p.ID).Error; err != nil {
		return
	}

	if p.MoveOperate == "up" {
		err, replaceLink = getReplaceLink(db, currentLink.Sort+1)
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("已经是第一个了")
			return
		}
	} else {
		err, replaceLink = getReplaceLink(db, currentLink.Sort-1)
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("已经是最后一个了")
			return
		}
	}

	// 执行操作
	sortMap := []map[string]interface{}{
		{
			"id":   currentLink.ID,
			"sort": replaceLink.Sort,
		},
		{
			"id":   replaceLink.ID,
			"sort": currentLink.Sort,
		},
	}
	if err = source.BatchUpdate(sortMap, "links", "id"); err != nil {
		return
	}

	return
}

func formatSort(db *gorm.DB) (err error) {
	var links []model.Link
	if err = db.Find(&links).Error; err != nil {
		return
	}

	// 重新排序(格式化排序)
	sort := len(links)
	var sortMap []map[string]interface{}
	for _, item := range links {
		sortMap = append(sortMap, map[string]interface{}{
			"id":   item.ID,
			"sort": sort,
		})
		sort--
	}

	if err = source.BatchUpdate(sortMap, "links", ""); err != nil {
		return
	}

	return
}

// 获取被替换位置分类
func getReplaceLink(db *gorm.DB, sort int) (err error, link model.Link) {
	err = db.Where("`sort` = ?", sort).First(&link).Error
	return
}
