package setting

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"gorm.io/gorm"
	"yz-go/model"
	"yz-go/source"
)

type SysSetting struct {
	model.SysSetting
	Value Value `json:"value"`
}
type Value struct {
	ListSetting                ListSetting                `json:"pc_list_setting"`  //列表设置数据设置
	DetailsSetting             DetailsSetting             `json:"details_setting"`  //详情页数据设置
	LanguageSetting            LanguageSetting            `json:"language_setting"` //语言设置
	WebBottomNavigationSetting WebBottomNavigationSetting `json:"web_bottom_navigation_setting"`
}

type ListSetting struct {
	IsSuperWholesalPrice      int `json:"is_super_wholesal_price"`       //PC是否显示超级批发价 0显示1不显示
	IsSuggestedRetailPrice    int `json:"is_suggested_retail_price"`     //PC是否显示零售价 0显示1不显示
	IsWebSuperWholesalPrice   int `json:"is_web_super_wholesal_price"`   //移动端是否显示超级批发价 0显示1不显示
	IsWebPrice                int `json:"is_web_price"`                  //移动端是否显示批发价 0显示1不显示
	IsWebSuggestedRetailPrice int `json:"is_web_suggested_retail_price"` //移动端是否显示零售价 0显示1不显示
}
type DetailsSetting struct {
	IsSuperWholesalPrice      int `json:"is_super_wholesal_price"`       //PC是否显示超级批发价 0显示1不显示
	IsPrice                   int `json:"is_price"`                      //PC是否显示批发价 0显示1不显示
	IsSuggestedRetailPrice    int `json:"is_suggested_retail_price"`     //PC是否显示零售价 0显示1不显示
	IsWebSuperWholesalPrice   int `json:"is_web_super_wholesal_price"`   //移动端是否显示超级批发价 0显示1不显示
	IsWebPrice                int `json:"is_web_price"`                  //移动端是否显示批发价 0显示1不显示
	IsWebSuggestedRetailPrice int `json:"is_web_suggested_retail_price"` //移动端是否显示零售价 0显示1不显示
}

type LanguageSetting struct {
	SuperWholesalPrice   string `json:"super_wholesal_price"`   //超级批发价对应名称默认 超级批发价
	Price                string `json:"price"`                  //批发价对应名称 默认批发价
	SuggestedRetailPrice string `json:"suggested_retail_price"` //建议零售价对应名称 默认 建议零售价
}

// web 底部导航设置  0开启 1关闭默认0
type WebBottomNavigationSetting struct {
	IsClassifyNavigation     int `json:"is_classify_navigation"`      //分类导航
	IsPromotionNavigation    int `json:"is_promotion_navigation"`     //推广
	IsShoppingCartNavigation int `json:"is_shopping_cart_navigation"` //购物车
	IsMemberCenterNavigation int `json:"is_member_center_navigation"` //会员中心
}

// 底部导航
var BottomNavigation = map[string]string{
	"is_classify_navigation":      "分类",
	"is_promotion_navigation":     "推广",
	"is_shopping_cart_navigation": "购物车",
	"is_member_center_navigation": "会员中心",
}

func (value Value) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *Value) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

func (value ListSetting) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *ListSetting) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

func (value DetailsSetting) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *DetailsSetting) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

func (value LanguageSetting) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *LanguageSetting) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

var pageSetupSetting *Value

func GetSysSetting(key string) (err error, sysSetting SysSetting) {
	err = source.DB().Where("`key` = ?", key).First(&sysSetting).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	return
}

// SaveSysSettingMini
// @author: [piexlmax](https://github.com/piexlmax)
// @function: SaveSysSettingMini
// @description: 保存SaveSysSettingMini记录
// @param: data *setting.SysSettingMini
// @return: err error
func SavePageSetupSetting(data SysSetting) (err error) {
	if data.ID != 0 {
		var oldData SysSetting
		err = source.DB().Where("id = ?", data.ID).Find(&oldData).Error
		if err != nil {
			return
		}

		oldData.Value = data.Value
		err = source.DB().Save(&oldData).Error
	} else {
		err = source.DB().Create(&data).Error
	}
	Reset()
	return err
}
func Get() (err error, setting Value) {
	if pageSetupSetting == nil {
		var sysSetting SysSetting
		err, sysSetting = GetSysSetting("page_setup_setting")
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		pageSetupSetting = &sysSetting.Value
	}
	return err, *pageSetupSetting
}

func Reset() {
	//重置全局变量 start
	pageSetupSetting = nil
}
