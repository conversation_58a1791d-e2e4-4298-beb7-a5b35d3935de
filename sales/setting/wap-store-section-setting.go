package setting

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"gorm.io/gorm"
	"yz-go/model"
	"yz-go/source"
)

type WapStoreSectionSysSetting struct {
	model.SysSetting
	Value WapStoreSectionValue `json:"value"`
}
type WapStoreSectionValue struct {
	StoreSectionType int `json:"store_section_type"` //  0关闭 1开启

}

func (WapStoreSectionSysSetting) TableName() string {
	return "sys_settings"
}

func (value WapStoreSectionValue) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *WapStoreSectionValue) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

var WapStoreSectionSetting *WapStoreSectionValue

var WapStoreSectionSysSettingKey = "wap_store_section_setting"

func GetWapStoreSectionSysSetting() (err error, sysSetting WapStoreSectionSysSetting) {
	err = source.DB().Where("`key` = ?", WapStoreSectionSysSettingKey).First(&sysSetting).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	return
}

// SaveSysSettingMini
// @author: [piexlmax](https://github.com/piexlmax)
// @function: SaveSysSettingMini
// @description: 保存SaveSysSettingMini记录
// @param: data *setting.SysSettingMini
// @return: err error
func SaveWapStoreSectionSetting(data WapStoreSectionSysSetting) (err error) {
	data.Key = WapStoreSectionSysSettingKey
	if data.ID == 0 {
		_, getWapStoreSectionSysSettingData := GetWapStoreSectionSysSetting()
		if getWapStoreSectionSysSettingData.ID != 0 {
			data.ID = getWapStoreSectionSysSettingData.ID
		}
	}
	if data.ID != 0 {
		var oldData WapStoreSectionSysSetting
		err = source.DB().Where("id = ?", data.ID).Find(&oldData).Error
		if err != nil {
			return
		}

		oldData.Value = data.Value
		err = source.DB().Save(&oldData).Error
	} else {
		err = source.DB().Create(&data).Error
	}
	ResetWapStoreSection()
	return err
}
func GetWapStoreSection() (err error, setting WapStoreSectionValue) {
	if WapStoreSectionSetting == nil {
		var sysSetting WapStoreSectionSysSetting
		err, sysSetting = GetWapStoreSectionSysSetting()
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		WapStoreSectionSetting = &sysSetting.Value
	}
	return err, *WapStoreSectionSetting
}

func ResetWapStoreSection() {
	//重置全局变量 start
	WapStoreSectionSetting = nil
}
