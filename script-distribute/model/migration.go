package model

import (
	_ "embed"
	"encoding/json"
	"gin-vue-admin/admin/model"
	"gin-vue-admin/cmd/gva"
	"github.com/chenhg5/collection"
	"yz-go/source"
	"yz-go/utils"
)

//go:embed menu.json
var menu string

func Migrate() (err error) {
	err = source.DB().AutoMigrate(
		ScriptCategory{},
		ScriptStorage{},
		Script{},
		ScriptMaterial{},
	)

	//修改字段类型为longtext 原本为 text
	//if source.DB().Migrator().HasTable(&Material{}) {
	//	if source.DB().Migrator().HasColumn(&Material{}, "img_url") {
	//		var columnTypes []gorm.ColumnType
	//		columnTypes, err = source.DB().Migrator().ColumnTypes(&Material{})
	//		if err != nil {
	//			return nil
	//		}
	//		for _, columnType := range columnTypes {
	//			if columnType.Name() == "img_url" && columnType.DatabaseTypeName() == "varchar" {
	//				err = source.DB().Migrator().AlterColumn(&Material{}, "img_url")
	//			}
	//		}
	//	}
	//}

	menus := []model.SysMenu{}
	err = json.Unmarshal([]byte(menu), &menus)
	if collection.Collect(gva.GlobalAuth.ResourcesPlugin).Contains(29) == true || utils.LocalEnv() == false {
		model.GVA_MENUS = append(model.GVA_MENUS, menus...)
	}
	return
}
