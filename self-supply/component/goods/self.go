package goods

import (
	catemodel "category/model"
	"context"
	"encoding/json"
	"public-supply/callback"

	"errors"
	"fmt"
	gatherSupplyRequest "gather-supply/request"
	"gin-vue-admin/cmd/gva"
	"github.com/chenhg5/collection"
	"github.com/gogf/gf/frame/g"
	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
	"gorm.io/gorm"
	log2 "log"
	"math"
	pmodel "product/model"
	"product/mq"
	"product/other"
	request2 "product/request"
	service2 "product/service"
	callback2 "public-supply/callback"
	"public-supply/common"
	"public-supply/model"
	"public-supply/request"
	"public-supply/service"
	setting2 "public-supply/setting"
	pmq "public-supply/work_mq"
	"strconv"
	"strings"
	"sync"
	"time"

	"yz-go/component/log"
	model2 "yz-go/model"
	yzResponse "yz-go/response"
	"yz-go/source"
	"yz-go/utils"
)

type Self struct {
}

func (self *Self) ManuallyProductUpdate(productID uint) (err error) {

	var product pmodel.Product
	err = source.DB().Where("id=?", productID).First(&product).Error
	if err != nil {
		return
	}
	if product.SourceGoodsID == 0 {
		return
	}

	strID := time.Now().Format("20060102150405") + strconv.Itoa(int(product.ID))
	//查询商品信息
	err = pmq.PublishMessage(callback.CallBackType{
		Type:  "goods.alter",
		MsgID: strID,
		Data: callback.GoodsIds{
			GoodsIds: []int{int(product.SourceGoodsID)},
		},
	})

	return
}

func (self *Self) SynchronizeProductsToLocal() (err error) {
	//TODO implement me
	panic("implement me")
}

var wg sync.WaitGroup

func (self *Self) ImportSelectGoodsRun(info model.SelectGoods) (err error, list interface{}) {
	orderPN := GetOrderNo()
	common.GlobalOrderSN = orderPN
	goodsRecord := model.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: len(info.List),
		Status:            1,
	}
	var resultInt []int
	var field string
	field = "source_goods_id"

	err = source.DB().Model(pmodel.Product{}).Where("gather_supply_id=?", info.GatherSupplyID).Pluck(field, &resultInt).Error
	if err != nil {
		log2.Println("查询供应链商品id错误", err)
		return
	}

	if len(info.List) <= 0 {
		log2.Println("导入空数据")
		err = errors.New("导入的是空数据")
		return
	}

	var idsArr []int
	idsArr = GetIdArr(info.List)

	difference := collection.Collect(idsArr).Diff(resultInt).ToIntArray()
	repeat := len(idsArr) - len(difference)
	goodsRecord.RepeatQuantity = repeat //重复数据
	if len(difference) <= 0 {
		goodsRecord.Status = 2
		goodsRecord.CompletionStatus = 1
		source.DB().Omit("goods_arr").Create(&goodsRecord) //当前没有商品可以导入
		return
	}
	source.DB().Omit("goods_arr").Create(&goodsRecord) //创建导入记录
	var goodsList []model.Goods
	for _, v := range difference {
		for _, item := range info.List {
			if item.ID == v {
				goodsList = append(goodsList, item)
			}
		}

	}
	info.List = goodsList
	arrList := splitArray(info.List, 20)
	for index, item := range arrList {
		wg.Add(1)
		fmt.Println("循环", index)
		err = self.RunSelectGoodsConcurrent(orderPN, item, info.Categorys, info.Key, info.GatherSupplyID)
		if err != nil {
			err = SetImportRecordFailed(orderPN, err.Error())
			return
		}
	}
	wg.Wait()
	err = SetImportRecordCompletion(orderPN)
	fmt.Println("全部完成：")

	return
}
func (self *Self) RunSelectGoodsConcurrent(orderPN string, list []model.Goods, category string, key string, gatherSupplyID uint) (err error) {

	defer wg.Done()

	cateList := strings.Split(category, ",")
	var cateId1, cateId2, cateId3 int
	cateId1, err = strconv.Atoi(cateList[0])
	cateId2, err = strconv.Atoi(cateList[1])
	cateId3, err = strconv.Atoi(cateList[2])

	var listGoods []*pmodel.Product
	var recordError []model.SupplyGoodsImportRecordErrors
	err, listGoods, recordError = self.CommodityAssembly(list, cateId1, cateId2, cateId3, gatherSupplyID, 0)
	if err != nil {
		return
	}
	if len(listGoods) > 0 {
		err = service.FinalProcessing(listGoods, orderPN)
		if err != nil {
			return
		}

	}
	if len(recordError) > 0 {
		err = service.FinalProcessingError(recordError, orderPN)
		if err != nil {
			return
		}
	}
	return

}

func (st *Self) ValidateConfig(p gatherSupplyRequest.ValidateConfigParams) (err error) {
	var setting model2.SysSetting
	if err, setting = setting2.GetSetting("gatherSupply" + strconv.Itoa(int(p.GatherSupplyID))); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}

	// 如果记录存在，直接使用存储设置验证
	if setting.ID > 0 {
		selfData = nil
		if err = json.Unmarshal([]byte(setting.Value), &selfData); err != nil {
			return
		}

		if selfData.BaseInfo.AppKey != "" && selfData.BaseInfo.AppSecret != "" && selfData.BaseInfo.Host != "" {
			if err, _ = FetchToken(selfData.BaseInfo.AppKey, selfData.BaseInfo.AppSecret, selfData.BaseInfo.Host); err != nil {
				return
			}
			return
		}

		return errors.New("校验错误，请完善配置信息")
	}

	// 否则用参数验证
	if err, _ = FetchToken(p.AppKey, p.AppSecret, p.Host); err != nil {
		return
	}

	return
}

func (s *Self) DeleteGoods(id uint) (err error) {
	//TODO implement me
	return
}

func FetchToken(AppKey, AppSecret, Host string) (err error, token string) {
	params := GetTokenRequest{
		AppKey:    AppKey,
		AppSecret: AppSecret,
	}
	var result []byte
	var response TokenResponse
	if err, result = utils.Post(Host+"/app/application/getToken", params, nil); err != nil {
		return
	}
	if err = json.Unmarshal(result, &response); err != nil {
		return
	}

	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}
	return err, response.Data.Token
}

type BalanceResponse struct {
	Code int                 `json:"code"`
	Data BalanceResponseData `json:"data"`
	Msg  string              `json:"msg"`
}
type BalanceResponseData struct {
	GoinBalance int `json:"goin_balance"`
}

func (s *Self) GetSupplyBalance(GatherSupplyID uint) (err error, balance interface{}) {

	var header map[string]string
	err, header = InitToken(GatherSupplyID)
	if err != nil {
		return
	}

	err, result := utils.Post(selfData.BaseInfo.Host+"/app/application/getUserGoinBalance", nil, header)
	var response BalanceResponse
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}
	balance = response.Data.GoinBalance
	return
}

type BaseInfoData struct {
	AppKey    string `json:"appKey"`
	AppSecret string `json:"appSecret"`
}

type SupplySetting struct {
	BaseInfo   BaseInfoData            `json:"baseInfo"`
	UpdateInfo setting2.UpdateInfoData `json:"update"`
	Pricing    setting2.PricingData    `json:"pricing"`
	Management setting2.Management     `json:"management"`
}
type SelfSupplySetting struct {
	SupplySetting
	BaseInfo SelfBaseInfoData `json:"baseInfo"`
}

type SelfBaseInfoData struct {
	BaseInfoData
	Host string `json:"host"`
}

var selfData *SelfSupplySetting

type Token struct {
	Token  string `json:"token"`
	Expire int64  `json:"expiresAt"`
}

type GetTokenRequest struct {
	AppKey    string `json:"app_key" validate:"required"`
	AppSecret string `json:"app_secret"  validate:"required"`
}

type Response struct {
	Code int        `json:"code"`
	Data PageResult `json:"data"`
	Msg  string     `json:"msg"`
}

type SourceResponse struct {
	Code int         `json:"code"`
	Data interface{} `json:"data"`
	Msg  string      `json:"msg"`
}

type PageResult struct {
	List        []service2.ProductElasticSearch `json:"list"`
	Total       int64                           `json:"total"`
	Page        int                             `json:"page"`
	PageSize    int                             `json:"pageSize"`
	NextUrl     string                          `json:"next_url"`
	ServerRatio int                             `json:"server_ratio"`
}
type BatchResponse struct {
	Code int             `json:"code"`
	Data BatchPageResult `json:"data"`
	Msg  string          `json:"msg"`
}
type BatchPageResult struct {
	List     []other.Product `json:"list"`
	Total    int64           `json:"total"`
	Page     int             `json:"page"`
	PageSize int             `json:"pageSize"`
	NextUrl  string          `json:"next_url"`
}
type TokenResponse struct {
	Code int    `json:"code"`
	Data Token  `json:"data"`
	Msg  string `json:"msg"`
}

var SelfToken *Token

func (*Self) InitSetting(gatherSupplyID uint) (err error) {
	var setting model2.SysSetting
	err, setting = setting2.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		return
	}

	selfData = nil
	err = json.Unmarshal([]byte(setting.Value), &selfData)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	if selfData.BaseInfo.AppKey == "" || selfData.BaseInfo.AppSecret == "" || selfData.BaseInfo.Host == "" {
		err = errors.New("请先配置供应链key")
		return
	}

	//if SelfToken == nil || SelfToken.Expire < time.Now().Unix()*1000 {
	//var self = Self{}
	//err = self.InitToken()
	//if err != nil {
	//	return
	//}
	//}
	return
}
func InitSetting(gatherSupplyID uint) (err error, selfSupplySetting SelfSupplySetting) {
	var setting model2.SysSetting
	err, setting = setting2.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		return
	}

	err = json.Unmarshal([]byte(setting.Value), &selfSupplySetting)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	if selfSupplySetting.BaseInfo.AppKey == "" || selfSupplySetting.BaseInfo.AppSecret == "" || selfSupplySetting.BaseInfo.Host == "" {
		err = errors.New("请先配置供应链key")
		return
	}
	//
	//if SelfToken == nil || SelfToken.Expire < time.Now().Unix()*1000 {
	//	var self = Self{}
	//	err = self.InitToken()
	//	if err != nil {
	//		return
	//	}
	//}
	return
}

func (*Self) InitGoods() (err error) {
	return
}

func InitToken(gatherSupplyID uint) (err error, header map[string]string) {
	var h = make(map[string]string)
	var ctx = context.Background()
	var token string

	token, err = source.Redis().Get(ctx, "gatherSupply"+strconv.Itoa(int(gatherSupplyID))).Result()

	if token == "" || err != nil {
		err, token = GetToken(gatherSupplyID)
		if err != nil {
			return
		}
		h["x-token"] = token

	} else {
		h["x-token"] = token
		var url string
		if selfData == nil {
			var setting SelfSupplySetting
			err, setting = InitSetting(gatherSupplyID)
			if err != nil {
				return
			}
			url = setting.BaseInfo.Host
		} else {
			url = selfData.BaseInfo.Host
		}
		var result []byte
		err, result = utils.Get(url+"/app/application/getSupplySource", h)
		var response SourceResponse
		err = json.Unmarshal(result, &response)
		if err != nil {
			return
		}

		if response.Code == 6 {
			err, token = GetToken(gatherSupplyID)
			if err != nil {
				return
			}
			h["x-token"] = token

		}
	}

	return err, h
}

func GetToken(gatherSupplyID uint) (err error, token string) {
	var ctx = context.Background()

	var selfSettingData SelfSupplySetting
	err, selfSettingData = InitSetting(gatherSupplyID)
	if err != nil {
		return
	}
	var requestParam GetTokenRequest
	requestParam.AppSecret = selfSettingData.BaseInfo.AppSecret
	requestParam.AppKey = selfSettingData.BaseInfo.AppKey
	var result []byte
	err, result = utils.Post(selfSettingData.BaseInfo.Host+"/app/application/getToken", requestParam, nil)
	var response TokenResponse
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}
	token = response.Data.Token
	err = source.Redis().Set(ctx, "gatherSupply"+strconv.Itoa(int(gatherSupplyID)), response.Data.Token, time.Second*86400).Err()
	if err != nil {
		return
	}
	return
}
func GetGoodsRequest(info request.GetGoodsSearch) (requestParam request2.ProductStorageSearch) {
	requestParam.Page = info.Page
	if requestParam.Page == 0 {
		requestParam.Page = 1
	}
	requestParam.PageSize = info.Limit
	requestParam.Source = info.Source
	one := 1
	requestParam.IsDisplay = &one
	requestParam.SupplyLineId = gva.GlobalAuth.EncryptID
	if info.ThirdGatherSupplyID != nil {
		requestParam.GatherSupplyID = info.ThirdGatherSupplyID

	}
	if info.Category1ID > 0 {
		requestParam.Category1ID = info.Category1ID
	}
	if info.Category2ID > 0 {
		requestParam.Category2ID = info.Category2ID
	}
	if info.Category3ID > 0 {
		requestParam.Category3ID = info.Category3ID
	}
	if info.IsFreeShipping > 0 {
		requestParam.FreightType = info.IsFreeShipping
	}
	if info.SearchWords != "" {
		requestParam.Title = info.SearchWords
	}
	if info.ShopWords != "" {
		requestParam.SupplierName = info.ShopWords
	}
	if info.SupplierID != nil {
		requestParam.SupplierID = info.SupplierID
	}
	if info.SelfIsImport != nil {
		requestParam.IsImport = info.SelfIsImport
	}
	if info.IsTaxLogo != nil {
		requestParam.IsTaxLogo = info.IsTaxLogo
	}
	if info.TaxRate != nil {
		requestParam.TaxRate = info.TaxRate
	}
	if info.AlbumId != nil {
		requestParam.AlbumId = info.AlbumId
	}
	if info.CollectId != nil {
		requestParam.CollectId = info.CollectId
	}
	if info.IsBill != nil {
		requestParam.IsBill = info.IsBill
	}
	if info.IsNew != nil {
		requestParam.IsNew = info.IsNew
	}
	if info.IsHot != nil {
		requestParam.IsHot = info.IsHot
	}
	if info.IsRecommend != nil {
		requestParam.IsRecommend = info.IsRecommend
	}
	if info.IsPromotion != nil {
		requestParam.IsPromotion = info.IsPromotion
	}

	if info.Source != nil {
		requestParam.Source = info.Source
	}

	if info.Type != "" {
		requestParam.Type = info.Type

	}
	if info.Sort != "" {
		if info.Sort == "desc" {
			requestParam.Sort = false
		} else {
			requestParam.Sort = true

		}

	}
	if len(info.GoodsIds) > 0 {
		requestParam.GoodsIds = info.GoodsIds
	}

	requestParam.AgreementPrice.From = &info.AgreementPrice.From
	if info.AgreementPrice.To > 0 {
		requestParam.AgreementPrice.To = &info.AgreementPrice.To

	}

	requestParam.ActivityPrice.From = &info.ActivityPrice.From
	if info.ActivityPrice.To > 0 {
		requestParam.ActivityPrice.To = &info.ActivityPrice.To
	}

	requestParam.GuidePrice.From = &info.GuidePrice.From
	if info.GuidePrice.To > 0 {
		requestParam.GuidePrice.To = &info.GuidePrice.To

	}

	requestParam.Profit.From = &info.Profits.From
	if info.Profits.To > 0 {
		requestParam.Profit.To = &info.Profits.To
	}

	requestParam.OriginRate.From = &info.PromotionRate.From
	if info.PromotionRate.To > 0 {
		requestParam.OriginRate.To = &info.PromotionRate.To
	}

	requestParam.ActivityRate.From = &info.ActivityRate.From
	if info.ActivityRate.To > 0 {
		requestParam.ActivityRate.To = &info.ActivityRate.To
	}
	return
}
func (*Self) GetGoods(info request.GetGoodsSearch) (err error, data interface{}, total int64, serverRatio int) {

	fmt.Println("page", info.Page, "limit", info.Limit, "source", info.Source)

	requestParam := GetGoodsRequest(info)

	var header map[string]string
	if err, header = InitToken(info.GatherSupplyID); err != nil {
		return
	}

	err, result := utils.Post(selfData.BaseInfo.Host+"/app/product/storage/list", requestParam, header)
	var response Response
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}
	serverRatio = response.Data.ServerRatio
	total = response.Data.Total
	fmt.Println("查询商品返回count:", total, info.Page, info.Limit)
	var self = Self{}
	data = self.ProductToGoods(response.Data.List, info.GatherSupplyID)
	fmt.Println("newdata", data, "olddata", response.Data.List)

	return
}
func (*Self) GetSource(GatherSupplyID uint) (err error, data interface{}) {
	var header map[string]string
	err, header = InitToken(GatherSupplyID)
	if err != nil {
		return
	}
	err, result := utils.Get(selfData.BaseInfo.Host+"/app/application/getSupplySource", header)
	var response SourceResponse
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}
	data = response.Data
	return
}

func (*Self) GetGatherSupplyAndSource(GatherSupplyID uint) (err error, data interface{}) {
	var header map[string]string
	err, header = InitToken(GatherSupplyID)
	if err != nil {
		return
	}
	err, result := utils.Get(selfData.BaseInfo.Host+"/app/application/getGatherSupplyAndSource", header)
	var response SourceResponse
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}
	data = response.Data
	return
}
func (*Self) ProductToGoods(data []service2.ProductElasticSearch, gatherID uint) (list []model.Goods) {

	for _, v := range data {
		var isImport uint
		var supplyGoods pmodel.Product
		err := source.DB().Where("source_goods_id = ?", v.ID).Where("gather_supply_id = ?", gatherID).First(&supplyGoods).Error
		if err == nil && supplyGoods.ID > 0 {
			isImport = 1
		}
		var categoryIds []string
		categoryIds = append(categoryIds, strconv.Itoa(int(v.Category1ID)))
		categoryIds = append(categoryIds, strconv.Itoa(int(v.Category2ID)))
		categoryIds = append(categoryIds, strconv.Itoa(int(v.Category3ID)))
		list = append(list, model.Goods{
			GatherSupplyID:    gatherID,
			ThirdCategoryName: v.RecommendCategoryStr,
			Sale:              uint(v.Sales),
			MarketPrice:       v.MarketPrice,
			ActivityRate:      Decimal(v.ActivityRate * float64(100)),
			Rate:              Decimal(v.PromotionRate * float64(100)),
			ID:                int(v.ID),
			Unit:              v.Unit,
			TotalStock:        v.Stock,
			ActivityPrice:     v.ActivityPrice,
			Source:            v.Source,
			Cover:             v.ImageUrl,
			Status:            v.IsDisplay,
			IsFreeShipping:    v.FreightType,
			AgreementPrice:    v.AgreementPrice,
			Stock:             uint(v.Stock),
			GuidePrice:        v.GuidePrice,
			SalePrice:         v.SalePrice,
			CostPrice:         v.CostPrice,
			Title:             v.Title,
			ThirdBrandName:    v.RecommendBrandStr,
			CategoryIds:       categoryIds,
			IsImport:          isImport,
		})
	}
	return
}
func (*Self) ImportGoodsRun(info request.GetGoodsSearch) (err error, data interface{}) {
	var self = Self{}
	if err != nil {
		return
	}
	fmt.Println("page", info.Page, "limit", info.Limit, "source", info.Source)
	info.Limit = 100

	requestParam := GetGoodsRequest(info)

	searchText, err := json.Marshal(info)
	if err != nil {
		return
	}
	fmt.Println("导入第一搜索条件", string(searchText))
	var header map[string]string
	err, header = InitToken(info.GatherSupplyID)
	if err != nil {
		return
	}
	err, result := utils.Post(selfData.BaseInfo.Host+"/app/product/storage/list", requestParam, header)
	var response yzResponse.Response
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}
	datas := response.Data.(map[string]interface{})
	var count = datas["total"].(float64)
	var limit = float64(info.Limit)
	var forCount = count / limit
	var counts = int(math.Ceil(forCount))
	fmt.Println("获取回来总数：", count, "计算总页数：", counts, info.Limit, info.Page)

	orderPN := GetOrderNo()
	goodsRecord := model.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: int(count),
		Status:            1,
		SearchCriteria:    string(searchText),
	}

	source.DB().Omit("goods_arr").Create(&goodsRecord)
	for i := 1; i <= counts; i++ {
		if i > 25 {
			break
		}

		err = self.RunGoodsConcurrent(info, i, requestParam, orderPN)
		if err != nil {
			log.Log().Error("中台供应链导入商品错误:" + err.Error())

			err = SetImportRecordFailed(orderPN, err.Error())
			return
		}
	}

	err = SetImportRecordCompletion(orderPN)
	if err != nil {
		fmt.Println("变更导入记录状态错误", err)

	}
	fmt.Println("导入供应链商品全部完成")
	return

}

func (*Self) RunGoodsConcurrent(info request.GetGoodsSearch, i int, search request2.ProductStorageSearch, orderPN string) (err error) {
	search.Page = i
	search.PageSize = info.Limit
	var header map[string]string
	err, header = InitToken(info.GatherSupplyID)
	if err != nil {
		return
	}
	err, result := utils.Post(selfData.BaseInfo.Host+"/app/product/storage/list", search, header)
	var response yzResponse.Response
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}
	fmt.Println("循环：", i, info.Limit, info.Source, search.Page)
	if response.Data != nil {

		cateList := strings.Split(info.Categorys, ",")
		var cateId1, cateId2, cateId3 int
		if len(cateList) >= 2 {
			cateId1, err = strconv.Atoi(cateList[0])
			cateId2, err = strconv.Atoi(cateList[1])
			cateId3, err = strconv.Atoi(cateList[2])
		}

		dataMap := response.Data.(map[string]interface{})

		var ProductItem []service2.ProductElasticSearch
		var Item []model.Goods
		listJson, err1 := json.Marshal(dataMap["list"])
		if err1 != nil {
			return err1
		}
		err = json.Unmarshal(listJson, &ProductItem)
		if err != nil {
			return
		}

		if len(ProductItem) <= 0 {
			fmt.Println("没有选择可导入的数据")
			log.Log().Info("没有选择可导入的数据", zap.Any("data", dataMap))
			return
		}
		var self = Self{}
		Item = self.ProductToGoods(ProductItem, info.GatherSupplyID)
		idsArr := GetIdArr(Item)

		var resultArr []int
		err = source.DB().Model(pmodel.Product{}).Where("gather_supply_id = ?", info.GatherSupplyID).Where("source_goods_id in ?", idsArr).Pluck("source_goods_id", &resultArr).Error
		if err != nil {
			return
		}
		difference := collection.Collect(idsArr).Diff(resultArr).ToIntArray()
		//fmt.Println("查询到的导入数据：", idsArr)
		//fmt.Println("已经存在的数据：", resultArr)
		//fmt.Println("目前可以导入的数据：", difference)

		err = SetImportRepeat(orderPN, len(idsArr)-len(difference))

		if len(difference) <= 0 {
			fmt.Println("没有可以导入的数据")
			log.Log().Info("没有选择可导入的数据2", zap.Any("data", dataMap))
			return
		}
		var goodsList []model.Goods

		for _, v := range difference {

			for _, item := range Item {
				if int(item.ID) == v {
					goodsList = append(goodsList, item)
				}
			}

		}

		var listGoods []*pmodel.Product

		err = self.InitSetting(info.GatherSupplyID)
		if err != nil {
			return
		}
		var recordError []model.SupplyGoodsImportRecordErrors
		err, listGoods, recordError = self.CommodityAssembly(goodsList, cateId1, cateId2, cateId3, info.GatherSupplyID, 0)
		if err != nil {
			return
		}
		if len(listGoods) > 0 {
			err = service.FinalProcessing(listGoods, orderPN)
			if err != nil {
				return
			}
		}
		if len(recordError) > 0 {
			err = service.FinalProcessingError(recordError, orderPN)
			if err != nil {
				return
			}
		}
	} else {
		fmt.Println("获取失败的：", i, info.Limit, info.Source)
		err = errors.New("获取失败：" + strconv.Itoa(i) + " " + strconv.Itoa(info.Limit))
	}

	return
}

type SettingResponse struct {
	Code int `json:"code"`
	Data struct {
		Setting ApplicationValue `json:"setting"`
	} `json:"data"`
	Msg string `json:"msg"`
}
type ApplicationValue struct {
	IsOpenApply     int    `json:"is_open_apply"`     // 是否开启前台申请应用
	ApplyDesc       string `json:"apply_desc"`        // 申请说明
	IsOpenAgreement int    `json:"is_open_agreement"` // 是否开启申请协议
	Agreement       string `json:"agreement"`
	EditPrice       int    `json:"edit_price"` //0不可以改价 1可以改价
}

func (*Self) GetSetting(gatherSupplyID uint) (err error, setting SettingResponse) {
	var header map[string]string
	err, header = InitToken(gatherSupplyID)
	if err != nil {
		return
	}
	url := selfData.BaseInfo.Host + "/app/application/getApplicationSetting"
	err, result := utils.Post(url, nil, header)
	err = json.Unmarshal(result, &setting)
	if err != nil {
		err = nil
		return
	}
	return
}

// 商品组装
func (self *Self) CommodityAssembly(list []model.Goods, cateId1, cateId2, cateId3 int, gatherSupplyID uint, isUpdate int) (err error, listGoods []*pmodel.Product, recordErrors []model.SupplyGoodsImportRecordErrors) {

	var response SettingResponse
	err, response = self.GetSetting(gatherSupplyID)
	if err != nil {
		return
	}
	idArr := GetIdArr(list)
	listArr := SplitArray(idArr, 100)
	var detailData = make(map[uint]other.Product)
	for index, item := range listArr {
		fmt.Println("循环分割id第", index, "次:", item)
		//stringIds := service.GetArrIds(item)
		fmt.Println("返回ids", item)
		var data map[uint]other.Product

		err, data = self.BatchGetGoodsDetails(gatherSupplyID, item)
		for detailIndex, detailItem := range data {
			detailData[detailIndex] = detailItem

		}
	}

	var riskManageRecord []model.RiskManagementRecord
	for _, elem := range list {
		detail := detailData[uint(elem.ID)]
		var isRisk int
		goods := new(pmodel.Product)
		var brand = new(catemodel.Brand)
		if elem.ThirdBrandName != "" {
			brand.Name = elem.ThirdBrandName
			brand.Source = elem.Source
			err = source.DB().Where(brand).FirstOrCreate(&brand).Error
			goods.BrandID = brand.ID
		}
		var costPrice, salePrice, activityPrice, guidePrice, originPrice uint

		elem.Source = 101
		if response.Data.Setting.EditPrice == 0 {
			elem.MarketPrice = detail.MarketPrice
			elem.ActivityPrice = detail.ActivityPrice
			elem.GuidePrice = detail.GuidePrice
			elem.CostPrice = detail.CostPrice
			elem.AgreementPrice = detail.AgreementPrice
			err, costPrice, salePrice, activityPrice, guidePrice, originPrice = GetPricingPrice(elem, selfData)
		} else {
			costPrice = detail.Price
			salePrice = detail.Price
			originPrice = detail.MarketPrice
			activityPrice = detail.ActivityPrice
			guidePrice = detail.GuidePrice
		}

		goods.Title = elem.Title
		goods.OriginPrice = originPrice
		goods.Price = salePrice
		goods.CostPrice = costPrice
		goods.ActivityPrice = activityPrice
		goods.GuidePrice = guidePrice
		goods.Stock = detail.Stock
		if selfData.UpdateInfo.Sales == 1 {
			goods.Sales = elem.Sale
		}
		goods.IsDisplay = elem.Status

		if selfData.Management.ProductPriceStatus == 1 {
			if float64(goods.Price) < float64(goods.CostPrice)*float64(selfData.Management.Products)/100 {
				goods.IsDisplay = 0
				isRisk++
			}
		} else if selfData.Management.ProductPriceStatus == 2 {
			if (float64(goods.Price)-float64(goods.CostPrice))/float64(goods.CostPrice) < float64(selfData.Management.Profit)/100 {
				goods.IsDisplay = 0
				isRisk++
			}
		}

		goods.ImageUrl = elem.Cover
		goods.VideoUrl = detail.VideoUrl
		goods.Unit = detail.Unit
		goods.Barcode = detail.Barcode
		goods.Sn = detail.Sn
		goods.Desc = detail.Desc
		goods.SourceGoodsID = uint(elem.ID)
		goods.Source = elem.Source
		goods.SourceName = detail.SourceName
		if cateId1 == 0 || cateId2 == 0 || cateId3 == 0 {
			display := 1
			if detail.Category1.Name == "" || detail.Category2.Name == "" || detail.Category3.Name == "" {
				recordErrors = append(recordErrors, model.SupplyGoodsImportRecordErrors{Error: "分类不存在", SourceId: uint(elem.ID), Title: elem.Title})
				continue
			}
			var resCategory1 catemodel.Category
			resCategory1.Name = detail.Category1.Name
			resCategory1.Level = 1
			resCategory1.ParentID = 0
			resCategory1.IsDisplay = &display
			resCategory1.Image = detail.Category1.Image
			err = source.DB().Where("name = ? and level = ? and parent_id = ?", detail.Category1.Name, 1, 0).FirstOrCreate(&resCategory1).Error
			if err != nil {
				recordErrors = append(recordErrors, model.SupplyGoodsImportRecordErrors{Error: err.Error(), SourceId: uint(elem.ID), Title: elem.Title})
				return
			}
			var resCategory2 catemodel.Category
			resCategory2.Name = detail.Category2.Name
			resCategory2.Level = 2
			resCategory2.ParentID = resCategory1.ID
			resCategory2.IsDisplay = &display
			resCategory2.Image = detail.Category2.Image
			err = source.DB().Where("name = ? and level = ? and parent_id = ?", resCategory2.Name, 2, resCategory2.ParentID).FirstOrCreate(&resCategory2).Error
			if err != nil {
				recordErrors = append(recordErrors, model.SupplyGoodsImportRecordErrors{Error: err.Error(), SourceId: uint(elem.ID), Title: elem.Title})
				return
			}
			var resCategory3 catemodel.Category
			resCategory3.Name = detail.Category3.Name
			resCategory3.Level = 3
			resCategory3.ParentID = resCategory2.ID
			resCategory3.IsDisplay = &display
			resCategory3.Image = detail.Category3.Image
			err = source.DB().Where("name = ? and level = ? and parent_id = ?", resCategory3.Name, 3, resCategory3.ParentID).FirstOrCreate(&resCategory3).Error
			if err != nil {
				recordErrors = append(recordErrors, model.SupplyGoodsImportRecordErrors{Error: err.Error(), SourceId: uint(elem.ID), Title: elem.Title})
				return
			}
			goods.Category1ID = resCategory1.ID
			goods.Category2ID = resCategory2.ID
			goods.Category3ID = resCategory3.ID
		} else {
			goods.Category1ID = uint(cateId1)
			goods.Category2ID = uint(cateId2)
			goods.Category3ID = uint(cateId3)

		}

		goods.FreightType = 2
		goods.GatherSupplyID = elem.GatherSupplyID

		/**
		处理轮播图
		*/

		goods.Gallery = detail.Gallery
		/**
		处理轮播图结束
		*/

		for _, v := range detail.Skus {
			if response.Data.Setting.EditPrice == 0 {
				err, costPrice, salePrice, activityPrice, guidePrice, originPrice = GetPricingPrice(model.Goods{
					AgreementPrice: v.Price,
					ActivityPrice:  v.ActivityPrice,
					GuidePrice:     v.GuidePrice,
					CostPrice:      v.CostPrice,
					MarketPrice:    v.OriginPrice,
					Source:         elem.Source,
				}, selfData)
			} else {
				costPrice = v.CostPrice
				salePrice = v.Price
				activityPrice = v.ActivityPrice
				guidePrice = v.GuidePrice
				originPrice = v.OriginPrice
			}

			v.CostPrice = costPrice
			v.Price = salePrice
			v.OriginPrice = originPrice
			v.GuidePrice = guidePrice
			v.ActivityPrice = activityPrice
			v.OriginalSkuID = int64(v.ID)
			v.ID = 0
			if selfData.Management.ProductPriceStatus == 1 {
				if float64(v.Price) < float64(v.CostPrice)*float64(selfData.Management.Products)/100 {
					goods.IsDisplay = 0
					isRisk++
				}
			} else if selfData.Management.ProductPriceStatus == 2 {
				if (float64(v.Price)-float64(v.CostPrice))/float64(v.CostPrice) < float64(selfData.Management.Profit)/100 {
					goods.IsDisplay = 0
					isRisk++
				}
			}
			goods.Skus = append(goods.Skus, v)
		}
		max, min := GetSkuPrice(goods.Skus)
		goods.MinPrice = min
		goods.MaxPrice = max
		if len(goods.Skus) > 0 {
			goods.ProfitRate = Decimal((float64(goods.Skus[0].GuidePrice) - float64(goods.Skus[0].Price)) / float64(goods.Skus[0].GuidePrice) * 100)
		}

		//处理资质json图片数组

		goods.Qualifications = detail.Qualifications
		if detail.SupplyLine != "" {
			goods.SupplyLine = detail.SupplyLine + " " + gva.GlobalAuth.EncryptID
			goods.IsSupplyLine = 1
		}
		goods.DetailImages = detail.DetailImages
		//--------处理详情json图片数组结束

		//处----------------理属性json数组
		var attrList pmodel.Attrs
		var attr pmodel.Attr
		for _, attrItem := range detail.Attrs {
			attr.Name = attrItem.Name
			attr.Value = attrItem.Value
			attrList = append(attrList, attr)
		}

		goods.Attrs = attrList
		//---------处理属性json数组结束
		//goods.Desc=detail.Description
		if isRisk > 0 {
			var riskRecord model.RiskManagementRecord
			riskRecord.ProductID = goods.ID
			riskRecord.SourceGoodsID = goods.SourceGoodsID
			riskRecord.GatherSupplyID = goods.GatherSupplyID
			riskManageRecord = append(riskManageRecord, riskRecord)

		}
		if len(goods.Skus) > 0 {
			listGoods = append(listGoods, goods)
		} else {
			recordErrors = append(recordErrors, model.SupplyGoodsImportRecordErrors{Error: "无规格商品，不导入", SourceId: uint(elem.ID), Title: elem.Title})

			fmt.Println("无规格商品，不导入", goods.ID)
		}

	}
	err = source.DB().CreateInBatches(&riskManageRecord, 1000).Error
	return
}

// 批量获取商品详情
func (*Self) BatchGetGoodsDetails(gatherSupplyID uint, ids []int) (err error, data map[uint]other.Product) {

	var detailList = make(map[uint]other.Product)

	fmt.Println("BatchGetGoodsDetails:", ids)
	var requestParam request2.ProductDetailSearch
	requestParam.Ids = ids
	requestParam.SupplyLineId = gva.GlobalAuth.EncryptID
	log.Log().Info("getSupplyLineId:" + requestParam.SupplyLineId)
	var header map[string]string
	err, header = InitToken(gatherSupplyID)
	if err != nil {
		return
	}
	err, result := utils.Post(selfData.BaseInfo.Host+"/app/product/storage/detailList", requestParam, header)
	var response BatchResponse
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}

	fmt.Println("总解析数量：", len(response.Data.List))
	for _, item := range response.Data.List {
		detailList[item.ID] = item
	}
	fmt.Println("总解析数量1：", len(detailList))

	data = detailList

	return
}

type BatchStockResponse struct {
	Code int                  `json:"code"`
	Data BatchStockPageResult `json:"data"`
	Msg  string               `json:"msg"`
}
type BatchStockPageResult struct {
	List     []service2.ProductStock `json:"list"`
	Total    int64                   `json:"total"`
	Page     int                     `json:"page"`
	PageSize int                     `json:"pageSize"`
	NextUrl  string                  `json:"next_url"`
}

func (*Self) BatchGetGoodsStock(gatherSupplyID uint, ids []int) (err error, data map[uint]service2.ProductStock) {

	var detailList = make(map[uint]service2.ProductStock)

	var requestParam request2.ProductDetailSearch
	requestParam.Ids = ids
	var header map[string]string
	err, header = InitToken(gatherSupplyID)
	if err != nil {
		return
	}
	err, result := utils.Post(selfData.BaseInfo.Host+"/app/product/storage/stockList", requestParam, header)
	var response BatchStockResponse
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}

	fmt.Println("总解析数量：", len(response.Data.List))
	for _, item := range response.Data.List {
		detailList[item.ID] = item
	}
	fmt.Println("总解析数量1：", len(detailList))

	data = detailList

	return
}

// 获取胜天半子分类数据
func (self *Self) GetCategory(info request.GetCategorySearch) (err error, data interface{}) {

	if err != nil {
		return
	}
	category = nil
	info.Page = 1
	info.Limit = 200
	var header map[string]string
	err, header = InitToken(info.GatherSupplyID)
	if err != nil {
		return
	}
	err, result := utils.GetWithHeader(selfData.BaseInfo.Host+"/app/category/lists?page=1&pageSize=1", header)
	var response yzResponse.Response
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}
	datas := response.Data.(map[string]interface{})
	var count float64 = datas["total"].(float64)
	var forCount float64 = count / float64(info.Limit)
	var page = int(math.Ceil(forCount))
	res := make(map[string]interface{})
	res["count"] = count
	res["page"] = page

	data = res
	err, _ = self.ImportCategory(page, info, "self")
	return
}

func (*Self) GetGroup() (err error, data interface{}) {

	if err != nil {
		return
	}
	var result *stbz.APIResult
	result, err = stbz.API(
		stbz.Method.GET,
		string(common.GET_TAG_LIST),
		map[string]string{},
		g.Map{"page": 1, "limit": 100},
	)

	fmt.Println("返回的code", result.Code)

	if result.Code != 1 {
		err = errors.New(result.Msg)
		return
	}

	datas := result.Data.(map[string]interface{})

	data = datas["list"]

	return

}

func (*Self) GetCategoryChild(pid int, info request.GetCategoryChild) (err error, data interface{}) {
	var header map[string]string
	err, header = InitToken(info.GatherSupplyID)
	if err != nil {
		return
	}
	err, result := utils.GetWithHeader(selfData.BaseInfo.Host+"/app/category/getCategory?parent_id="+strconv.Itoa(pid)+"&source="+strconv.Itoa(info.Source), header)
	var response yzResponse.Response
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}

	data = response.Data
	aaa, _ := json.Marshal(data)
	fmt.Println("GetGetCategoryChild：", string(aaa))
	//if  result.Data!=nil{
	//
	//}

	return

}
func (*Self) RunConcurrent(wg *sync.WaitGroup, info request.GetCategorySearch, i int) (err error) {

	var header map[string]string
	err, header = InitToken(info.GatherSupplyID)
	if err != nil {
		return
	}
	err, result := utils.GetWithHeader(selfData.BaseInfo.Host+"/app/category/lists?page="+strconv.Itoa(i)+"&pageSize="+strconv.Itoa(info.Limit), header)
	var response yzResponse.Response
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}
	if response.Data != nil {
		datas := response.Data.(map[string]interface{})
		var cateItem []catemodel.Category
		var mJson []byte
		mJson, err = json.Marshal(datas["list"])
		if err != nil {
			return
		}
		err = json.Unmarshal(mJson, &cateItem)
		if err != nil {
			return
		}
		if len(cateItem) > 0 {
			category = append(category, cateItem...)
		}

	} else {
		fmt.Println("获取失败的：", i, info.Limit, info.Source)
	}

	return
}

// 选品库增加商品
func (*Self) GoodsStorageAdd(ids []int, supplyId uint) (err error, info interface{}) {

	var strArr []int
	for _, item := range ids {
		strArr = append(strArr, item)
	}

	var requestParam request2.ProductDetailSearch
	requestParam.Ids = strArr
	var header map[string]string
	err, header = InitToken(supplyId)
	if err != nil {
		return
	}
	//log.Log().Info("请求body", zap.Any("info", requestParam))
	//log.Log().Info("请求header", zap.Any("info", header))
	err, result := utils.Post(selfData.BaseInfo.Host+"/app/product/storage/addStorage", requestParam, header)
	var response yzResponse.Response
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}

	info = result

	fmt.Println("选品库增加商品:", string(result))
	if response.Code == 0 {
		var insertGoodStorage []model.GoodsStorage
		for _, item := range ids {
			insertGoodStorage = append(insertGoodStorage, model.GoodsStorage{SourceGoodsID: uint(item), SupplyID: supplyId})
		}
		err = source.DB().CreateInBatches(&insertGoodStorage, 500).Error
		if err != nil {
			log.Log().Error("供应链选品入库错误", zap.Any("info", info))
		}
	}

	return

}

func (*Self) DeleteStorage(ids uint, supplyId uint) (err error) {

	var requestParam request2.ProductDetailSearch
	requestParam.Ids = append(requestParam.Ids, int(ids))
	var header map[string]string
	err, header = InitToken(supplyId)
	if err != nil {
		return
	}
	//log.Log().Info("请求body", zap.Any("info", requestParam))
	//log.Log().Info("请求header", zap.Any("info", header))

	err, result := utils.Post(selfData.BaseInfo.Host+"/app/product/storage/deleteStorage", requestParam, header)
	var response yzResponse.Response
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}

	fmt.Println("选品库删除商品:", result)
	if response.Code == 0 {
		for _, item := range requestParam.Ids {

			err = source.DB().Where(model.GoodsStorage{SourceGoodsID: uint(item), SupplyID: supplyId}).Delete(&model.GoodsStorage{}).Error
			if err != nil {
				log.Log().Error("本地选品库删除商品错误", zap.Any("info", err.Error()))
			}
		}
	}

	return

}
func (self *Self) GoodsStockAlert(GoodsData callback2.GoodsCallBack) (err error) {
	var products []service2.ProductForUpdate
	err = source.DB().Preload("Skus").Where("source_goods_id in ?", GoodsData.Data.GoodsIds).Find(&products).Error
	if err != nil || len(products) == 0 {
		log.Log().Error("回调查询商品不存在", zap.Any("data", GoodsData.Data.GoodsIds))
		return
	}
	var gatherSupply []pmodel.GatherSupply
	if err = source.DB().Find(&gatherSupply).Error; err != nil {
		return
	}

	var gatherSupplyMap = make(map[int]pmodel.GatherSupply)
	for _, v := range gatherSupply {
		gatherSupplyMap[v.ID] = v
	}

	var productsGroup = make(map[uint][]service2.ProductForUpdate)
	var productsGroupIds = make(map[uint][]int)
	for _, reproduct := range products {
		if _, ok := gatherSupplyMap[int(reproduct.GatherSupplyID)]; !ok || gatherSupplyMap[int(reproduct.GatherSupplyID)].CategoryID != 2 {
			//不是中台供应链商品跳过
			continue
		}
		productsGroup[reproduct.GatherSupplyID] = append(productsGroup[reproduct.GatherSupplyID], reproduct)
		productsGroupIds[reproduct.GatherSupplyID] = append(productsGroupIds[reproduct.GatherSupplyID], int(reproduct.SourceGoodsID))
	}

	for gatherSupplyID, productGroup := range productsGroup {
		if err = self.InitSetting(gatherSupplyID); err != nil {
			log.Log().Error("供应链配置出错", zap.Any("info", gatherSupplyID))
			return
		}

		var data = make(map[uint]service2.ProductStock)
		listArr := SplitArray(productsGroupIds[gatherSupplyID], 100)
		for _, arrItem := range listArr {
			var item map[uint]service2.ProductStock
			if err, item = self.BatchGetGoodsStock(gatherSupplyID, arrItem); err != nil {
				log.Log().Info("获取商品失败", zap.Any("info", GoodsData))
				err = nil
				continue
			}
			for _, i := range item {
				data[i.ID] = i
			}
		}
		for _, product := range productGroup {
			itemID := product.SourceGoodsID
			for _, v := range data[itemID].Skus {
				for k, sku := range product.Skus {
					if v.ID == uint(sku.OriginalSkuID) {
						product.Skus[k].Stock = v.Stock
					}
				}

			}
			err = service2.UpdateProduct(product)
			if err != nil {
				//log.Log().Error("供应商商品回调修改失败", zap.Any("err", err))
				//log.Log().Error("供应商商品回调修改失败", zap.Any("product", product))
				continue
			}
		}
	}
	return
}
func (self *Self) GoodsPriceAlert(GoodsData callback2.GoodsCallBack) (err error) {
	var products []service2.ProductForUpdate
	err = source.DB().Preload("Skus").Where("source_goods_id in ?", GoodsData.Data.GoodsIds).Find(&products).Error
	if err != nil || len(products) == 0 {
		log.Log().Error("回调查询商品不存在", zap.Any("data", GoodsData.Data.GoodsIds))
		return
	}

	var gatherSupply []pmodel.GatherSupply
	if err = source.DB().Find(&gatherSupply).Error; err != nil {
		return
	}

	var gatherSupplyMap = make(map[int]pmodel.GatherSupply)
	for _, v := range gatherSupply {
		gatherSupplyMap[v.ID] = v
	}

	var productsGroup = make(map[uint][]service2.ProductForUpdate)
	var productsGroupIds = make(map[uint][]int)
	for _, reproduct := range products {
		if _, ok := gatherSupplyMap[int(reproduct.GatherSupplyID)]; !ok || gatherSupplyMap[int(reproduct.GatherSupplyID)].CategoryID != 2 {
			//不是中台供应链商品跳过
			continue
		}
		productsGroup[reproduct.GatherSupplyID] = append(productsGroup[reproduct.GatherSupplyID], reproduct)
		productsGroupIds[reproduct.GatherSupplyID] = append(productsGroupIds[reproduct.GatherSupplyID], int(reproduct.SourceGoodsID))
	}

	var riskManageRecord []model.RiskManagementRecord

	for gatherSupplyID, productGroup := range productsGroup {
		if err = self.InitSetting(gatherSupplyID); err != nil {
			log.Log().Error("供应链配置出错", zap.Any("info", gatherSupplyID))
			return
		}

		var data = make(map[uint]other.Product)
		listArr := SplitArray(productsGroupIds[gatherSupplyID], 100)
		for _, arrItem := range listArr {
			var item map[uint]other.Product
			if err, item = self.BatchGetGoodsDetails(gatherSupplyID, arrItem); err != nil {
				log.Log().Info("获取商品失败", zap.Any("info", GoodsData))
				err = nil
				continue
			}
			for _, i := range item {
				data[i.ID] = i
			}
		}

		for _, product := range productGroup {
			//if product.SourceGoodsID == 23428 {
			//	log.Log().Info("更新商品测试", zap.Any("info", product))
			//}

			var riskRecord model.RiskManagementRecord
			var isRisk int
			if _, ok := data[product.SourceGoodsID]; !ok {
				//if product.SourceGoodsID == 23428 {
				//	log.Log().Info("更新商品测试2", zap.Any("info2", data[product.SourceGoodsID]))
				//}
				isRisk++
				riskRecord.Type = 1

				//供应链商品不存在时，将商品下架
				err = source.DB().Model(&pmodel.Product{}).Where("id = ?", product.ID).Update("is_display", 0).Error
				if err != nil {
					return
				}

				err = mq.PublishMessage(product.ID, mq.Undercarriage, 0)
				if err != nil {
					return
				}

				continue
			}

			itemID := product.SourceGoodsID
			var goods model.Goods

			goods.GuidePrice = data[itemID].GuidePrice
			goods.AgreementPrice = data[itemID].AgreementPrice
			goods.ActivityPrice = data[itemID].ActivityPrice
			goods.MarketPrice = data[itemID].MarketPrice
			goods.CostPrice = data[itemID].CostPrice
			goods.Source = 101
			product.IsDisplay = data[itemID].IsDisplay
			product.SingleOption = data[itemID].SingleOption
			// 更新商品信息
			if selfData.UpdateInfo.BaseInfo == 1 {
				product.Title = data[itemID].Title
				product.Attrs = data[itemID].Attrs
				product.Gallery = data[itemID].Gallery
				product.DetailImages = data[itemID].DetailImages
				product.ImageUrl = data[itemID].ImageUrl
			}
			// 更新商品发票信息
			if selfData.UpdateInfo.Bill == 1 {
				product.BillPosition = data[itemID].BillPosition
				product.IsBill = data[itemID].IsBill
				product.TaxCode = data[itemID].TaxCode
				product.TaxProductName = data[itemID].TaxProductName
				product.TaxShortName = data[itemID].TaxShortName
				product.TaxOption = data[itemID].TaxOption
				product.TaxUnit = data[itemID].TaxUnit
				product.FavorablePolicy = data[itemID].FavorablePolicy
				product.IsFavorablePolicy = data[itemID].IsFavorablePolicy
				product.FreeOfTax = data[itemID].FreeOfTax
				product.ShortCode = data[itemID].ShortCode
				product.TaxMeasurePrice = data[itemID].TaxMeasurePrice
				product.TaxRate = data[itemID].TaxRate
				product.IsTaxLogo = data[itemID].IsTaxLogo
			}
			var pcostPrice, psalePrice, pactivityPrice, pguidePrice, poriginPrice uint
			if product.ID == 82234 {
				log.Log().Debug("huijie问题打印", zap.Any("data", goods), zap.Any("setting", selfData))
			}
			err, pcostPrice, psalePrice, pactivityPrice, pguidePrice, poriginPrice = GetPricingPrice(goods, selfData)
			if product.ID == 82234 {
				log.Log().Debug("huijie问题打印1", zap.Any("data", pcostPrice), zap.Any("setting", psalePrice))
			}
			if data[itemID].SupplyLine != "" {
				product.SupplyLine = data[itemID].SupplyLine + " " + gva.GlobalAuth.EncryptID
				product.IsSupplyLine = 1
			}
			// 更新分类
			if selfData.UpdateInfo.CateGory == 1 {
				if data[itemID].Category1.Name == "" || data[itemID].Category2.Name == "" || data[itemID].Category3.Name == "" {
					continue
				}
				display := 1
				var resCategory1 catemodel.Category
				resCategory1.Name = data[itemID].Category1.Name
				resCategory1.Level = 1
				resCategory1.ParentID = 0
				resCategory1.IsDisplay = &display
				err = source.DB().Where("name = ? and level = ? and parent_id = ?", data[itemID].Category1.Name, 1, 0).FirstOrCreate(&resCategory1).Error
				if err != nil {
					return
				}
				var resCategory2 catemodel.Category
				resCategory2.Name = data[itemID].Category2.Name
				resCategory2.Level = 2
				resCategory2.ParentID = resCategory1.ID
				resCategory2.IsDisplay = &display
				err = source.DB().Where("name = ? and level = ? and parent_id = ?", resCategory2.Name, 2, resCategory2.ParentID).FirstOrCreate(&resCategory2).Error
				if err != nil {
					return
				}
				var resCategory3 catemodel.Category
				resCategory3.Name = data[itemID].Category3.Name
				resCategory3.Level = 3
				resCategory3.ParentID = resCategory2.ID
				resCategory3.IsDisplay = &display
				err = source.DB().Where("name = ? and level = ? and parent_id = ?", resCategory3.Name, 3, resCategory3.ParentID).FirstOrCreate(&resCategory3).Error
				if err != nil {
					return
				}
				product.Category1ID = resCategory1.ID
				product.Category2ID = resCategory2.ID
				product.Category3ID = resCategory3.ID
			}

			// 更新价格
			//单独控制商品是否更新价格
			if selfData.UpdateInfo.CostPrice == 1 && product.IsAutoUpdateCost == 1 {
				product.CostPrice = pcostPrice
			}
			if selfData.UpdateInfo.CurrentPrice == 1 && product.IsAutoUpdatePrice == 1 {
				product.Price = psalePrice
			}

			product.GuidePrice = pguidePrice
			product.ActivityPrice = pactivityPrice
			product.OriginPrice = poriginPrice
			if product.StatusLock == 0 {
				product.IsDisplay = data[itemID].IsDisplay
			}
			product.SourceName = data[itemID].SourceName
			//if SupplySettings.UpdateInfo.BaseInfo==1{
			//	product.Title=data[item].
			//}
			if selfData.Management.ProductPriceStatus == 1 {
				if product.Price < product.CostPrice*(selfData.Management.Products/100) {
					product.IsDisplay = 0
					isRisk++
				}
			} else if selfData.Management.ProductPriceStatus == 2 && product.CostPrice > 0 {
				if (product.Price-product.CostPrice)/product.CostPrice < selfData.Management.Profit/100 {
					product.IsDisplay = 0
					isRisk++
				}
			}
			var productSkus = product.Skus
			product.Skus = []service2.Sku{}
			var minProfitRate float64
			for _, v := range data[itemID].Skus {

				var skuForUpdate service2.Sku
				for _, productSku := range productSkus {
					if uint(productSku.OriginalSkuID) == v.ID {
						skuForUpdate = productSku
					}
				}
				err, pcostPrice, psalePrice, pactivityPrice, pguidePrice, poriginPrice = GetPricingPrice(model.Goods{
					AgreementPrice: v.Price,
					ActivityPrice:  v.ActivityPrice,
					GuidePrice:     v.GuidePrice,
					MarketPrice:    v.OriginPrice,
					CostPrice:      v.CostPrice,
					Source:         101,
				}, selfData)
				if selfData.UpdateInfo.BaseInfo == 1 {
					skuForUpdate.Title = v.Title
				}
				if selfData.UpdateInfo.CostPrice == 1 && product.IsAutoUpdateCost == 1 {
					skuForUpdate.CostPrice = pcostPrice

				}
				if selfData.UpdateInfo.CurrentPrice == 1 && product.IsAutoUpdatePrice == 1 {
					skuForUpdate.Price = psalePrice
				}

				skuForUpdate.OriginalSkuID = int(v.ID)
				skuForUpdate.GuidePrice = pguidePrice
				skuForUpdate.OriginPrice = poriginPrice
				skuForUpdate.ActivityPrice = pactivityPrice
				skuForUpdate.Sn = v.Sn
				skuForUpdate.Weight = v.Weight
				skuForUpdate.Stock = v.Stock
				skuForUpdate.IsDisplay = v.IsDisplay
				skuForUpdate.ProductID = v.ProductID
				skuForUpdate.SupplierID = v.SupplierID
				skuForUpdate.ImageUrl = v.ImageUrl
				skuForUpdate.Options = v.Options
				skuForUpdate.OriginalSkuID = int(v.ID)
				if selfData.Management.ProductPriceStatus == 1 {
					if skuForUpdate.Price < skuForUpdate.CostPrice*(selfData.Management.Products/100) {
						product.IsDisplay = 0
						isRisk++
					}
				} else if selfData.Management.ProductPriceStatus == 2 && product.CostPrice > 0 {
					if skuForUpdate.CostPrice > 0 {
						if (skuForUpdate.Price-product.CostPrice)/skuForUpdate.CostPrice < selfData.Management.Profit/100 {
							product.IsDisplay = 0
							isRisk++
						}
					}

				}
				skuForUpdate.ProfitRate = utils.ExecProfitRate(skuForUpdate.GuidePrice, skuForUpdate.Price)
				if selfData.UpdateInfo.Bill == 1 {

					skuForUpdate.TaxCode = v.TaxCode
					skuForUpdate.TaxProductName = v.TaxProductName
					skuForUpdate.TaxShortName = v.TaxShortName
					skuForUpdate.TaxOption = v.TaxOption
					skuForUpdate.TaxUnit = v.TaxUnit
					skuForUpdate.FavorablePolicy = v.FavorablePolicy
					skuForUpdate.IsFavorablePolicy = v.IsFavorablePolicy
					skuForUpdate.FreeOfTax = v.FreeOfTax
					skuForUpdate.ShortCode = v.ShortCode
					skuForUpdate.TaxMeasurePrice = v.TaxMeasurePrice
					skuForUpdate.TaxRate = v.TaxRate
					skuForUpdate.IsTaxLogo = v.IsTaxLogo

				}
				product.Skus = append(product.Skus, skuForUpdate)
				if minProfitRate == 0 || minProfitRate > skuForUpdate.ProfitRate {
					minProfitRate = skuForUpdate.ProfitRate
				}
			}
			if len(product.Skus) > 0 {
				product.ProfitRate = minProfitRate
			}
			if product.ID == 82234 {
				log.Log().Debug("huijie问题打印2", zap.Any("data", product))
			}
			//log.Log().Debug("self修改商品", zap.Any("id", product.ID))
			if isRisk > 0 {
				riskRecord.ProductID = product.ID
				riskRecord.SourceGoodsID = product.SourceGoodsID
				riskRecord.GatherSupplyID = product.GatherSupplyID
				riskManageRecord = append(riskManageRecord, riskRecord)
			}
			err = service2.UpdateProduct(product)
			if err != nil {
				//log.Log().Error("供应商商品回调修改失败", zap.Any("err", err))
				//log.Log().Error("供应商商品回调修改失败", zap.Any("product", product))
				continue
			}

			err = callback2.DeleteGoodsMsg(GoodsData.MsgID)
			if err != nil {
				log.Log().Error("胜天半子订单发货完成删除记录err", zap.Any("info", err))
				return
			}
			//time.Sleep(2 * 100 * time.Millisecond)

		}
	}
	err = source.DB().CreateInBatches(&riskManageRecord, 1000).Error
	return
}
