package goods

import (
	catemodel "category/model"
	"fmt"
	"github.com/xingliuhua/leaf"
	"go.uber.org/zap"
	"gorm.io/gorm"
	pmodel "product/model"
	pMq "product/mq"
	"public-supply/model"
	"public-supply/request"
	"sort"
	"strconv"
	"yz-go/component/log"
	ylog "yz-go/component/log"
	"yz-go/source"
)

func GetOrderNo() (id string) {

	var node *leaf.IdNode
	var err error
	err, node = leaf.NewNode(20)
	if err != nil {
		return
	}
	err, id = node.NextId()
	if err != nil {
		return
	}
	return
}

func GetIdArr(list []model.Goods) (arrIds []int) {
	for _, elem := range list {
		arrIds = append(arrIds, elem.ID)
	}
	return

}
func Decimal(value float64) float64 {
	var err error
	value, err = strconv.ParseFloat(fmt.Sprintf("%0.2f", value), 64)
	if err != nil {
		log.Log().Error("数据转换失败", zap.Any("err", err))
		return 0
	}
	return value
}
func decimal(value float64) float64 {
	var err error
	value, err = strconv.ParseFloat(fmt.Sprintf("%0.1f", value), 64)
	if err != nil {
		log.Log().Error("数据转换失败", zap.Any("err", err))
		return 0
	}
	return value
}
func YzhGetIdArr(list []model.YzhGoodsDetail) (arrIds []int) {
	for _, elem := range list {
		arrIds = append(arrIds, elem.RESULTDATA.PRODUCTDATA.ProductId)
	}
	return

}
func splitArray(arr []model.Goods, num int64) [][]model.Goods {
	max := int64(len(arr))
	//判断数组大小是否小于等于指定分割大小的值，是则把原数组放入二维数组返回
	if max <= num {
		return [][]model.Goods{arr}
	}
	//获取应该数组分割为多少份
	var quantity int64
	if max%num == 0 {
		quantity = max / num
	} else {
		quantity = (max / num) + 1
	}

	fmt.Println("quantity数量：", quantity)
	//声明分割好的二维数组
	var segments = make([][]model.Goods, 0)
	//声明分割数组的截止下标
	var start, end, i int64
	for i = 1; i <= quantity; i++ {
		end = i * num
		if i != quantity {
			segments = append(segments, arr[start:end])
		} else {
			segments = append(segments, arr[start:])
		}
		start = i * num
	}
	return segments
}

// 分割数组，根据传入的数组和分割大小，将数组分割为大小等于指定大小的多个数组，如果不够分，则最后一个数组元素小于其他数组
func SplitArray(arr []int, num int64) [][]int {
	max := int64(len(arr))
	//判断数组大小是否小于等于指定分割大小的值，是则把原数组放入二维数组返回
	if max <= num {
		return [][]int{arr}
	}
	//获取应该数组分割为多少份
	var quantity int64
	if max%num == 0 {
		quantity = max / num
	} else {
		quantity = (max / num) + 1
	}
	//声明分割好的二维数组
	var segments = make([][]int, 0)
	//声明分割数组的截止下标
	var start, end, i int64
	for i = 1; i <= quantity; i++ {
		end = i * num
		if i != quantity {
			segments = append(segments, arr[start:end])
		} else {
			segments = append(segments, arr[start:])
		}
		start = i * num
	}
	return segments
}

func GetPricingPrice(elem model.Goods, dat *SelfSupplySetting) (err error, costPrice, salePrice, activityPrice, guidePrice, originPrice uint) {

	var intX uint64
	var intXa uint64
	var intXb uint64
	var intXc uint64
	var intXd uint64
	var intXe uint64
	var intXf uint64

	if dat.Pricing.Strategy == 2 { //本地定价策略关闭

		if elem.Source == 101 { //中台本地

			//销售价计算
			if dat.Pricing.SupplySales == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplySalesGuide, 10, 32)
				salePrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.SupplySales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplySalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplySales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplySalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplySales == 4 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplySalesExec, 10, 32)
				salePrice = elem.AgreementPrice + ((elem.GuidePrice - elem.AgreementPrice) * uint(intX) / 100)
			}
			if dat.Pricing.SupplySales == 5 {
				intXa, err = strconv.ParseUint(dat.Pricing.SupplySalesExecA, 10, 32)
				intXb, err = strconv.ParseUint(dat.Pricing.SupplySalesExecB, 10, 32)
				intXc, err = strconv.ParseUint(dat.Pricing.SupplySalesExecC, 10, 32)
				intXd, err = strconv.ParseUint(dat.Pricing.SupplySalesExecD, 10, 32)
				salePrice = elem.AgreementPrice*uint(intXa)/100 + ((elem.GuidePrice - elem.AgreementPrice) * uint(intXb) / 100)
				if float64(salePrice)/float64(elem.AgreementPrice) > float64(intXc)/100 {
					salePrice = elem.AgreementPrice * uint(intXd) / 100
				}
			}
			if dat.Pricing.SupplySales == 6 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplySalesOrigin, 10, 32)
				salePrice = elem.MarketPrice * uint(intX) / 100
			}
			//销售价计算结束

			//成本价
			if dat.Pricing.SupplyCost == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyCostGuide, 10, 32)
				costPrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyCost == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyCost == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyCost == 4 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyCostOrigin, 10, 32)
				costPrice = elem.MarketPrice * uint(intX) / 100
			}

			//成本价计算结束
			//
			//指导价
			if dat.Pricing.SupplyGuide == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyGuideGuide, 10, 32)
				guidePrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyGuide == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyGuideAgreement, 10, 32)
				guidePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyGuide == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyGuideMarketing, 10, 32)
				guidePrice = elem.ActivityPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyGuide == 4 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyGuideOrigin, 10, 32)
				guidePrice = elem.MarketPrice * uint(intX) / 100
			}

			//指导价计算结束
			//
			//营销价
			if dat.Pricing.SupplyActivity == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyActivityGuide, 10, 32)
				activityPrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyActivity == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyActivityAgreement, 10, 32)
				activityPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyActivity == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyActivityMarketing, 10, 32)
				activityPrice = elem.ActivityPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyActivity == 4 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyActivityOrigin, 10, 32)
				activityPrice = elem.MarketPrice * uint(intX) / 100
			}
			//营销价计算结束
			//
			//建议零售价
			if dat.Pricing.SupplyAdvice == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyAdviceGuide, 10, 32)
				originPrice = elem.GuidePrice * uint(intX) / 100

				intXa, err = strconv.ParseUint(dat.Pricing.SupplyAdviceExecA, 10, 32)
				intXb, err = strconv.ParseUint(dat.Pricing.SupplyAdviceExecB, 10, 32)
				intXc, err = strconv.ParseUint(dat.Pricing.SupplyAdviceExecC, 10, 32)
				intXd, err = strconv.ParseUint(dat.Pricing.SupplyAdviceExecD, 10, 32)
				intXe, err = strconv.ParseUint(dat.Pricing.SupplyAdviceExecE, 10, 32)
				intXf, err = strconv.ParseUint(dat.Pricing.SupplyAdviceExecF, 10, 32)
				if float64(elem.GuidePrice)/float64(elem.AgreementPrice) > float64(intXa)/100 && intXa > 0 && intXb > 0 {
					originPrice = elem.AgreementPrice * uint(intXb) / 100
				}
				if float64(elem.GuidePrice)/float64(elem.AgreementPrice) > float64(intXc)/100 && intXc > 0 && intXd > 0 {
					originPrice = elem.AgreementPrice * uint(intXd) / 100
				}
				if float64(elem.GuidePrice)/float64(elem.AgreementPrice) > float64(intXe)/100 && intXe > 0 && intXf > 0 {
					originPrice = elem.AgreementPrice * uint(intXf) / 100
				}
			}
			if dat.Pricing.SupplyAdvice == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyAdviceAgreement, 10, 32)
				originPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyAdvice == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyAdviceMarketing, 10, 32)
				originPrice = elem.ActivityPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyAdvice == 4 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyAdviceOrigin, 10, 32)
				originPrice = elem.MarketPrice * uint(intX) / 100
			}
			//建议零售价计算结束

		}

		if elem.Source == 134 { //中台本地

			//销售价计算
			if dat.Pricing.SupplySales == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplySalesGuide, 10, 32)
				salePrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.SupplySales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplySalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplySales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplySalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplySales == 4 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplySalesExec, 10, 32)
				salePrice = elem.AgreementPrice + ((elem.GuidePrice - elem.AgreementPrice) * uint(intX) / 100)
			}
			if dat.Pricing.SupplySales == 5 {
				intXa, err = strconv.ParseUint(dat.Pricing.SupplySalesExecA, 10, 32)
				intXb, err = strconv.ParseUint(dat.Pricing.SupplySalesExecB, 10, 32)
				intXc, err = strconv.ParseUint(dat.Pricing.SupplySalesExecC, 10, 32)
				intXd, err = strconv.ParseUint(dat.Pricing.SupplySalesExecD, 10, 32)
				salePrice = elem.AgreementPrice*uint(intXa)/100 + ((elem.GuidePrice - elem.AgreementPrice) * uint(intXb) / 100)
				if float64(salePrice)/float64(elem.AgreementPrice) > float64(intXc)/100 {
					salePrice = elem.AgreementPrice * uint(intXd) / 100
				}
			}
			//销售价计算结束

			//成本价
			if dat.Pricing.SupplyCost == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyCostGuide, 10, 32)
				costPrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyCost == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyCost == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//成本价计算结束
			//
			//指导价
			if dat.Pricing.SupplyGuide == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyGuideGuide, 10, 32)
				guidePrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyGuide == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyGuideAgreement, 10, 32)
				guidePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyGuide == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyGuideMarketing, 10, 32)
				guidePrice = elem.ActivityPrice * uint(intX) / 100
			}

			//指导价计算结束
			//
			//营销价
			if dat.Pricing.SupplyActivity == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyActivityGuide, 10, 32)
				activityPrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyActivity == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyActivityAgreement, 10, 32)
				activityPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyActivity == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyActivityMarketing, 10, 32)
				activityPrice = elem.ActivityPrice * uint(intX) / 100
			}
			//营销价计算结束
			//
			//建议零售价
			if dat.Pricing.SupplyAdvice == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyAdviceGuide, 10, 32)
				originPrice = elem.GuidePrice * uint(intX) / 100

				intXa, err = strconv.ParseUint(dat.Pricing.SupplyAdviceExecA, 10, 32)
				intXb, err = strconv.ParseUint(dat.Pricing.SupplyAdviceExecB, 10, 32)
				intXc, err = strconv.ParseUint(dat.Pricing.SupplyAdviceExecC, 10, 32)
				intXd, err = strconv.ParseUint(dat.Pricing.SupplyAdviceExecD, 10, 32)
				intXe, err = strconv.ParseUint(dat.Pricing.SupplyAdviceExecE, 10, 32)
				intXf, err = strconv.ParseUint(dat.Pricing.SupplyAdviceExecF, 10, 32)
				if float64(elem.GuidePrice)/float64(elem.AgreementPrice) > float64(intXa)/100 && intXa > 0 && intXb > 0 {
					originPrice = elem.AgreementPrice * uint(intXb) / 100
				}
				if float64(elem.GuidePrice)/float64(elem.AgreementPrice) > float64(intXc)/100 && intXc > 0 && intXd > 0 {
					originPrice = elem.AgreementPrice * uint(intXd) / 100
				}
				if float64(elem.GuidePrice)/float64(elem.AgreementPrice) > float64(intXe)/100 && intXe > 0 && intXf > 0 {
					originPrice = elem.AgreementPrice * uint(intXf) / 100
				}
			}
			if dat.Pricing.SupplyAdvice == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyAdviceAgreement, 10, 32)
				originPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyAdvice == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyAdviceMarketing, 10, 32)
				originPrice = elem.ActivityPrice * uint(intX) / 100
			}
			//建议零售价计算结束

		}

	} else {
		salePrice = elem.AgreementPrice
		costPrice = elem.CostPrice
		originPrice = elem.MarketPrice
		activityPrice = elem.ActivityPrice
		guidePrice = elem.GuidePrice
	}

	return

}

//获取本地策略价格

func GetArrIds(list []int) (resIds string) {
	var ids string
	for _, elem := range list {
		stringId := strconv.Itoa(elem)
		ids = ids + stringId + ","
	}
	ids = ids[0 : len(ids)-1]
	resIds = ids
	return
}
func GetSkuPrice(skuList []pmodel.Sku) (maxPrice, minPrice uint) {
	var priceList []int
	for _, item := range skuList {
		priceList = append(priceList, int(item.Price))
	}
	if len(priceList) <= 0 {
		maxPrice = 0
		minPrice = 0
		return
	}
	sort.Ints(priceList)
	minPrice = uint(priceList[0])
	//sort.Sort(sort.Reverse(sort.IntSlice(priceList)))
	maxPrice = uint(priceList[len(priceList)-1])

	return
}

// //导入胜天半子分类数据
func (self *Self) ImportCategory(counts int, info request.GetCategorySearch, key string) (err error, data interface{}) {

	//err=DeleteCategory(info.Source)
	for i := 1; i <= counts; i++ {
		//if i%15 == 0 {
		//	time.Sleep(time.Second * 1)
		//}
		self.RunConcurrent(nil, info, i)

	}

	data = category

	ylog.Log().Error("获取栏目!", zap.Any("err", category))

	//BatchImportDatabaseA(info)
	BatchImportDatabase()
	fmt.Println("全部完成：", len(category), info.Source)
	return
}

func BatchImportDatabase() {

	for _, elem := range category {
		if elem.Level == 1 {
			var oldID = elem.ID
			elem.Model = source.Model{}
			err, newID := CreateCategory(&elem)
			if newID > 0 {

				FindChildCategory(oldID, newID)
				fmt.Println("插入成功", elem.ID, newID, elem.Name, elem.Level)
			}
			if err != nil {
				fmt.Println("插入失败", elem.Name, elem.ID, err)
			}

		}

	}

}

func FindChildCategory(oldID uint, newID uint) {

	for _, elem := range category {

		if elem.ParentID == oldID {
			var childOldID = elem.ID
			elem.ParentID = newID
			elem.Model = source.Model{}
			err, childNewID := CreateCategory(&elem)
			if childNewID > 0 {

				FindChildCategory(childOldID, childNewID)
			}
			if err != nil {
				fmt.Println("插入失败", elem.Name, childOldID, err)
			}

		}

	}

}

func CreateCategory(category *catemodel.Category) (err error, id uint) {

	err = source.DB().Where("level = ? ", category.Level).Where("name = ?", category.Name).FirstOrCreate(&category).Error

	return err, category.ID

}

func SetImportRepeat(batch string, quantity int) (err error) {
	if quantity <= 0 {
		return
	}
	err = source.DB().Model(model.SupplyGoodsImportRecord{}).Where("batch=?", batch).Update("repeat_quantity", gorm.Expr("repeat_quantity + ?", quantity)).Error
	return
}

func GetProductIds(list []*pmodel.Product) (resIds string) {
	var ids string
	for _, elem := range list {
		stringId := strconv.Itoa(int(elem.SourceGoodsID))
		ids = ids + stringId + ","
	}
	ids = ids[0 : len(ids)-1]
	resIds = ids
	return
}

func CreateGoods(goodsList []*pmodel.Product) (err error) {
	err = source.DB().CreateInBatches(&goodsList, 2000).Error
	if err != nil {
		return
	}
	var SupplyGoods []model.SupplyGoods
	for _, goods := range goodsList {

		SupplyGoods = append(SupplyGoods, model.SupplyGoods{
			SupplyGoodsID:  goods.SourceGoodsID,
			Source:         goods.Source,
			ProductID:      goods.ID,
			GatherSupplyID: goods.GatherSupplyID,
		})

		err = pMq.PublishMessage(goods.ID, pMq.Create, 0)
		if err != nil {
			return
		}

	}
	err = source.DB().CreateInBatches(SupplyGoods, 2000).Error
	return
}

func SetImportRecordCompletion(batch string) (err error) {
	err = source.DB().Model(model.SupplyGoodsImportRecord{}).Where("batch=?", batch).Update("completion_status", 1).Error
	return
}
func SetImportRecordFailed(batch string, reason string) (err error) {
	err = source.DB().Model(model.SupplyGoodsImportRecord{}).Where("batch=?", batch).Updates(&map[string]interface{}{"completion_status": 2, "failed_reason": reason}).Error
	return
}
