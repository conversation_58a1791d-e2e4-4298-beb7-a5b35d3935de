package order

import (
	"after-sales/model"
	request3 "after-sales/request"
	afterSalesService "after-sales/service"
	"encoding/json"
	"errors"
	"fmt"
	v1 "order/api/v1"
	orderModel "order/model"
	orderOrder "order/order"
	request2 "order/request"
	"order/service"
	productModel "product/model"
	callback2 "public-supply/callback"
	"public-supply/common"
	"public-supply/request"
	"public-supply/response"
	gSupplyRes "public-supply/response"
	"self-supply/component/goods"
	"strconv"
	"sync"
	"time"
	"yz-go/component/log"
	model2 "yz-go/model"
	yzResponse "yz-go/response"
	"yz-go/source"
	"yz-go/utils"

	"github.com/gogf/gf/frame/g"
	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

func init() {
	afterSalesService.SetBeforeHandle(func(sales afterSalesService.OperationAfterSales) (err error) {
		if sales.GatherSupply.CategoryID == common.SUPPLY_SELF {
			var orderClass = &Self{}
			err = orderClass.InitSetting(sales.GatherSupply.ID)
			if err != nil {
				return
			}

			var resOrder *stbz.APIResult

			err, resOrder = orderClass.GetOrderByThirdOrderSn(request3.AfterSales{ThirdOrderSN: strconv.Itoa(int(sales.Order.OrderSN))})
			var resOrderData []orderOrder.Order
			if resOrder == nil || resOrder.Data == nil {
				return errors.New("resOrder or resOrder.Data is nil")

			}
			datas, ok := resOrder.Data.(map[string]interface{})
			if !ok {
				return errors.New("resOrder.Data is not of type map[string]interface{}")

			}
			orderDetail1, _ := json.Marshal(datas["read"])

			err = json.Unmarshal(orderDetail1, &resOrderData)
			if err != nil {
				log.Log().Error("获取中台供应链订单详情失败"+strconv.Itoa(int(sales.Order.OrderSN)), zap.Any("err", err.Error()))
				err = errors.New("获取中台供应链订单详情失败" + strconv.Itoa(int(sales.Order.OrderSN)) + err.Error())
				return
			}
			var sku productModel.Sku
			err = source.DB().Unscoped().Where("id = ?", sales.OrderItem.SkuID).First(&sku).Error
			if err != nil {
				log.Log().Info("售后处理:查询售后商品规格失败", zap.Any("err", err))
				err = errors.New("售后处理:查询售后商品规格失败" + err.Error())
				return
			}
			var ThirdOrderId, ThirdOrderItemId, Amount, TechnicalServicesFee, Freight uint
			for _, orderData := range resOrderData {
				for _, orderDataItem := range orderData.OrderItems {
					if orderDataItem.SkuID == uint(sku.OriginalSkuID) {
						ThirdOrderId = orderDataItem.OrderID
						ThirdOrderItemId = orderDataItem.ID
						Amount = orderDataItem.Amount
						TechnicalServicesFee = orderDataItem.TechnicalServicesFee
						// 根据退款方式计算技术服务费
						if orderDataItem.TechnicalServicesFee > 0 {
							if sales.AfterSales.RefundWay == model.RefundWayPrice && sales.AfterSales.Amount > 0 {
								// 按退款金额比例计算技术服务费
								refundRatio := float64(sales.AfterSales.Amount) / float64(sales.OrderItem.Amount)
								TechnicalServicesFee = uint(float64(orderDataItem.TechnicalServicesFee) * refundRatio)
							} else if sales.AfterSales.RefundWay == model.RefundWayNum && sales.AfterSales.Num > 0 {
								// 按退款个数比例计算技术服务费
								refundRatio := float64(orderDataItem.TechnicalServicesFee) / float64(orderDataItem.Qty)
								TechnicalServicesFee = uint(float64(sales.AfterSales.Num) * refundRatio)
							}
						}

						// 根据退款方式计算技术服务费
						if orderDataItem.Amount > 0 {
							if sales.AfterSales.RefundWay == model.RefundWayPrice && sales.AfterSales.Amount > 0 {
								// 按退款金额比例计算退款金额
								refundRatio := float64(sales.AfterSales.Amount) / float64(sales.OrderItem.Amount)
								Amount = uint(float64(orderDataItem.Amount) * refundRatio)
							} else if sales.AfterSales.RefundWay == model.RefundWayNum && sales.AfterSales.Num > 0 {
								// 按退款个数比例计算退款金额
								refundRatio := float64(orderDataItem.Amount) / float64(orderDataItem.Qty)
								Amount = uint(float64(sales.AfterSales.Num) * refundRatio)
							}
						}
						Freight = orderData.Freight
						break
					}
				}
			}
			var BarterSku productModel.Sku
			//如果是换货,查询换货规格的第三方规格id
			if sales.AfterSales.RefundType == model.Barter {
				err = source.DB().Unscoped().Where("id = ?", sales.AfterSales.BarterSkuID).First(&BarterSku).Error
				if err != nil {
					log.Log().Info("售后处理:查询售后换货商品规格失败"+strconv.Itoa(int(sales.AfterSales.BarterSkuID)), zap.Any("err", err))
					err = errors.New("售后处理:查询售后换货商品规格失败" + strconv.Itoa(int(sales.AfterSales.BarterSkuID)) + err.Error())
					return
				}
			}
			var responseData *stbz.APIResult

			err, responseData = orderClass.SelfAfterSalesBeforeCheck(request3.AfterSales{
				OrderID:              ThirdOrderId,
				ThirdOrderSN:         strconv.Itoa(int(sales.Order.OrderSN)),
				OrderItemID:          ThirdOrderItemId,
				Amount:               Amount,
				TechnicalServicesFee: TechnicalServicesFee,
				Freight:              Freight,
				ReasonType:           sales.AfterSales.ReasonType,
				Reason:               sales.AfterSales.Reason,
				Description:          sales.AfterSales.Description,
				IsReceived:           sales.AfterSales.IsReceived,
				DetailImages:         sales.AfterSales.DetailImages,
				RefundType:           sales.AfterSales.RefundType,
				BarterSkuID:          uint(BarterSku.OriginalSkuID),
				BarterNum:            sales.AfterSales.BarterNum,
				BarterSkuTitle:       BarterSku.Title,
				ImageUrl:             sales.AfterSales.ImageUrl,
				RefundWay:            sales.AfterSales.RefundWay,
				Num:                  sales.AfterSales.Num,
			})
			if err != nil {
				return
			}
			if responseData.Code != 0 {
				err = errors.New(responseData.Msg)
				return
			}
		}
		return
	})
	//获取
	afterSalesService.RegisterAfterSalesTypeHandler(func(sales afterSalesService.OperationAfterSalesType) (err error, data interface{}) {
		if sales.GatherSupply.CategoryID == common.SUPPLY_SELF {
			var orderClass = &Self{}
			err = orderClass.InitSetting(sales.GatherSupply.ID)
			if err != nil {
				return
			}
			var resOrder *stbz.APIResult
			err, resOrder = orderClass.GetAfterSalesTypeNameMap(request3.AfterSalesTypeSearch{
				OrderID:     sales.Order.ID,
				OrderItemID: sales.OrderItemID,
			}, sales.Order)
			if err != nil {
				return
			}
			if resOrder.Code > 0 {
				err = errors.New(resOrder.Msg)
				return
			}
			if resOrder.Data == nil {
				err = errors.New("售后类型数据为空")
				return
			}
			data = resOrder.Data
		}
		return
	})
}

type Self struct{}

func (s *Self) UploadGatherSupplySN(request request.UpdateData) (err error, data interface{}) {
	//TODO implement me
	panic("implement me")
}

func (s *Self) GetRefundTypes(orderId uint, orderItemID uint) (err error, data []model.AfterSalesType) {
	return
}

func (s *Self) GetAllAddress() (err error, data interface{}) {
	return
}

var selfData *goods.SelfSupplySetting
var SelfToken *goods.Token

func (*Self) InitSetting(gatherSupplyID uint) (err error) {
	var setting model2.SysSetting
	err = source.DB().Where("`key` = ?", "gatherSupply"+strconv.Itoa(int(gatherSupplyID))).First(&setting).Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("请先配置供应链信息")
		return
	}

	err = json.Unmarshal([]byte(setting.Value), &selfData)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	if selfData.BaseInfo.AppKey == "" || selfData.BaseInfo.AppSecret == "" || selfData.BaseInfo.Host == "" {
		err = errors.New("请先配置供应链key")
		return
	}

	//if SelfToken == nil || SelfToken.Expire < time.Now().Unix()*1000 {
	//	err = InitToken()
	//	if err != nil {
	//		return
	//	}
	//}
	return
}

func InitToken() (err error, header map[string]string) {

	var requestParam goods.GetTokenRequest
	requestParam.AppSecret = selfData.BaseInfo.AppSecret
	requestParam.AppKey = selfData.BaseInfo.AppKey
	err, result := utils.Post(selfData.BaseInfo.Host+"/app/application/getToken", requestParam, nil)
	var response goods.TokenResponse
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}
	SelfToken = &goods.Token{
		Token:  response.Data.Token,
		Expire: response.Data.Expire,
	}
	var h = make(map[string]string)
	h["x-token"] = SelfToken.Token
	return err, h
}

type BeforeCheckCache struct {
	RequestKey    string
	ErrorCache    error
	ExpiredAt     int64
	ResponseCache response.BeforeCheck
}

var beforeCheckMap map[string]BeforeCheckCache
var beforeCheckMapMu sync.Mutex

func GetBeforeCheckCache(requestData request.RequestSaleBeforeCheck) (hasCache bool, err error, data response.BeforeCheck) {
	now := time.Now().Unix()

	if beforeCheckMap == nil {
		beforeCheckMap = make(map[string]BeforeCheckCache)
	}
	requestKey, err := json.Marshal(&requestData)
	if err != nil {
		log.Log().Error("stbz下单校验接口，缓存的参数key转为json失败", zap.Any("requestData", requestData))
		err = nil
		return
	}
	key := utils.MD5V(requestKey)
	for cacheKey, beforeCheckCache := range beforeCheckMap {
		if beforeCheckCache.ExpiredAt < now {
			// 过期的删除
			delete(beforeCheckMap, cacheKey)
		} else {
			if cacheKey == key {
				// 匹配并未过期返回
				return true, beforeCheckCache.ErrorCache, beforeCheckCache.ResponseCache
			}
		}
	}
	// 未匹配到返回无缓存
	return
}

func SetBeforeCheckCache(requestData request.RequestSaleBeforeCheck, err error, data response.BeforeCheck) {
	beforeCheckMapMu.Lock()
	defer beforeCheckMapMu.Unlock()
	if beforeCheckMap == nil {
		beforeCheckMap = make(map[string]BeforeCheckCache)
	}
	requestKey, err := json.Marshal(&requestData)
	key := utils.MD5V(requestKey)
	// 以参数json MD5为key，3秒内缓存返回值
	beforeCheckMap[key] = BeforeCheckCache{ExpiredAt: time.Now().Unix() + 3, ErrorCache: err, ResponseCache: data}
}
func (s *Self) OrderBeforeCheck(requestData request.RequestSaleBeforeCheck) (err error, data response.BeforeCheck) {
	hasCache, err, data := GetBeforeCheckCache(requestData)
	if hasCache {
		// 3秒内有相同参数的请求时，直接返回缓存
		return
	}
	err, data = s.orderBeforeCheck(requestData)
	// 不存在时正常调用，缓存结果
	SetBeforeCheckCache(requestData, err, data)
	return
}

// 订单前置校验  返回运费
func (*Self) orderBeforeCheck(request request.RequestSaleBeforeCheck) (err error, data response.BeforeCheck) {

	var resData response.SelfBeforeCheck
	var jsonData []byte
	var header map[string]string
	err, header = InitToken()
	if err != nil {
		return
	}
	err, result := utils.Post(selfData.BaseInfo.Host+"/app/order/beforeCheck", g.Map{"lian_lian": request.LianLian, "guang_dian": request.GuangDian, "spu": request.Skus, "address": request.Address}, header)
	var response yzResponse.Response
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}

	interfaceData := response

	if interfaceData.Code == 0 {
		jsonData, err = json.Marshal(interfaceData.Data)
		err = json.Unmarshal(jsonData, &resData)

		freight, _ := strconv.Atoi(resData.Freight)

		data.Freight = uint(freight)
		data.Msg = response.Msg
		data.Code = 1
	} else {
		data.Msg = response.Msg
		data.Code = uint(response.Code)
	}

	return

}

// 商品是否可售前置校验
func (*Self) SaleBeforeCheck(request request.RequestSaleBeforeCheck) (err error, resData gSupplyRes.ResSaleBeforeCheck) {
	var header map[string]string
	err, header = InitToken()
	if err != nil {
		return
	}
	err, result := utils.Post(selfData.BaseInfo.Host+"/app/order/availableCheck", g.Map{"spu": request.Skus, "address": request.Address}, header)
	var response yzResponse.Response
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}

	saleData, err := json.Marshal(response.Data)

	_ = json.Unmarshal(saleData, &resData.Data)
	fmt.Println("返回可售数据", resData)

	return

}

// 确认下单
func (*Self) ConfirmOrder(requestParam request.RequestConfirmOrder) (err error, info *stbz.APIResult) {
	var header map[string]string
	err, header = InitToken()
	if err != nil {
		return
	}
	log.Log().Info("ConfirmOrder供应链商品准备下单", zap.Any("info", requestParam))
	var response *stbz.APIResult
	err, result := utils.Post(selfData.BaseInfo.Host+"/app/order", request.ConfirmRequest{
		Spu:       requestParam.Skus,
		Address:   requestParam.Address,
		OrderSn:   requestParam.OrderSn.OrderSn,
		Remark:    requestParam.Remark,
		GuangDian: requestParam.GuangDian,
	}, header)
	if err != nil {
		return
	}
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.Code == 0 {
		response.Code = 1
	}
	info = response
	log.Log().Info("下单回调数据", zap.Any("result", result))

	return
}

// 物流查询
func (*Self) ExpressQuery(request request.RequestExpress) (err error, info interface{}) {
	var header map[string]string
	err, header = InitToken()
	if err != nil {
		return
	}
	var response yzResponse.Response
	err, result := utils.Post(selfData.BaseInfo.Host+"/app/order/Logistic", g.Map{"order_sn": request.OrderSn, "sku": request.Sku}, header)

	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	info = response

	return

}

func (*Self) AfterSalesBeforeCheck(request request.RequestAfterSale) (err error, info interface{}) {
	var result *stbz.APIResult
	result, err = stbz.API(
		stbz.Method.GET,
		string(common.AFTER_SALE_BEFORE_CHECK),
		map[string]string{},
		g.Map{"orderSn": request.OrderSn, "sku": request.Sku},
	)

	info = result

	return

}

func (*Self) AfterSalesPicture(request request.RequestAfterSalePicture) (err error, info interface{}) {
	var result *stbz.APIResult
	result, err = stbz.API(
		stbz.Method.GET,
		string(common.AFTER_SALE_PICTURE),
		map[string]string{},
		g.Map{"orderSn": request.OrderSn, "sku": request.Sku, "pictures": request.Pictures},
	)

	info = result

	return

}

func (*Self) AfterSale(request request.AfterSale) (err error, info interface{}) {

	//nnn,_:=json.Marshal(request)
	//
	//fmt.Println(string(nnn))

	var result *stbz.APIResult
	result, err = stbz.API(
		stbz.Method.POST,
		string(common.AFTER_SALE),
		map[string]string{},
		g.Map{"orderSn": request.OrderSn, "sku": request.Sku, "num": request.Num, "goodsFee": request.GoodsFee, "logisticFee": request.LogisticFee, "serviceTypeCode": request.ServiceTypeCode,
			"pickTypeCode": request.PickTypeCode, "packageTypeCode": request.PackageTypeCode, "returnTypeCode": request.ReturnTypeCode, "reasonsTypeCode": request.ReasonsTypeCode,
			"reasonsDescription": request.ReasonsDescription, "serviceTime": request.ServiceTime, "vouchers": request.Vouchers, "userInfo": request.UserInfo,
		},
	)

	info = result

	return

}

// 发货
func (*Self) OrderDelivery(OrderData callback2.OrderCallBack) (err error) {

	var orderInfo orderModel.Order
	err = source.DB().Preload("OrderItems").Where("order_sn =  ?", OrderData.Data.OrderSn).First(&orderInfo).Error
	if err != nil {
		//log.Log().Error("订单不存在", zap.Any("info", err.Error()))
		return
	}
	var orderRequest v1.HandleOrderRequest
	var info, jsonData interface{}
	var requestExpress request.RequestExpress
	var expressInfo Response
	requestExpress.OrderSn = OrderData.Data.OrderSn
	requestExpress.Sku = strconv.FormatInt(OrderData.Data.Sku, 10)
	var orderClass = Self{}
	err = orderClass.InitSetting(orderInfo.GatherSupplyID)
	if err != nil {
		log.Log().Error("前置校验失败", zap.Any("info", err.Error()))
		return
	}
	err, info = orderClass.ExpressQuery(requestExpress)
	if err != nil {
		log.Log().Error("查询物流信息错误", zap.Any("info", err.Error()))
		return
	}
	jsonData, err = json.Marshal(info)
	err = json.Unmarshal(jsonData.([]byte), &expressInfo)
	if err != nil {
		log.Log().Error("解析物流信息错误1", zap.Any("info", err.Error()))
		return
	}
	//log.Log().Info("供应链订单信息回调", zap.Any("info", expressInfo.Data))
	//log.Log().Info("供应链订单信息回调1", zap.Any("info", orderInfo.OrderItems))

	for _, v := range expressInfo.Data {
		var ids []request2.OrderItemSendInfo
		for _, orderItem := range v.OrderItems {
			for _, localOrderItem := range orderInfo.OrderItems {

				if orderItem.SkuID == localOrderItem.OriginalSkuID {

					if (orderItem.SendStatus == 1 || orderItem.SendStatus == 2) && (localOrderItem.SendStatus == 0 || localOrderItem.SendStatus == 2) {
						//上游和本地的订单item发货状态必须符合要求
						ids = append(ids, request2.OrderItemSendInfo{
							ID:  localOrderItem.ID,
							Num: orderItem.SendNum,
						})
					}

				}
				if orderItem.SkuID == 0 && orderItem.ID == 0 {
					ids = append(ids, request2.OrderItemSendInfo{
						ID:  0,
						Num: 0,
					})
				}
			}
		}
		orderRequest.OrderID = orderInfo.ID
		orderRequest.ExpressNo = v.ExpressNo
		orderRequest.OrderItemIDs = ids
		orderRequest.CompanyCode = v.CompanyCode
		orderRequest.IsEmpty = v.IsEmpty
		err = ExpressSent(orderRequest)
		if err != nil {
			log.Log().Info(strconv.Itoa(int(v.OrderID))+"发货error："+err.Error(), zap.Any("data", orderRequest))
			continue
		}
		err = source.DB().Model(&orderModel.Order{}).Where("id = ?", orderInfo.ID).Update("gather_supply_msg", orderInfo.GatherSupplyMsg+",已发货").Error
		if err != nil {
			return
		}
		err = callback2.DeleteOrderMsg(OrderData.MsgID)
		if err != nil {
			log.Log().Error("胜天半子订单发货完成删除记录err", zap.Any("info", err))
		}
	}

	return
}

func (*Self) CronOrderDelivery(gatherSupplyID uint) (err error) {

	var orderList []orderModel.Order
	err = source.DB().Preload("OrderItems").Where("gather_supply_id = ?", gatherSupplyID).Where("status = 1").Find(&orderList).Error
	if err != nil {
		//log.Log().Error("订单不存在", zap.Any("info", err.Error()))
		return
	}
	for _, orderInfo := range orderList {
		var orderRequest v1.HandleOrderRequest
		var info, jsonData interface{}
		var requestExpress request.RequestExpress
		var expressInfo Response
		requestExpress.OrderSn = strconv.Itoa(int(orderInfo.OrderSN))
		requestExpress.Sku = ""
		var orderClass = Self{}
		err = orderClass.InitSetting(orderInfo.GatherSupplyID)
		if err != nil {
			log.Log().Error("前置校验失败", zap.Any("info", err.Error()))
			return
		}
		err, info = orderClass.ExpressQuery(requestExpress)
		if err != nil {
			log.Log().Error("查询物流信息错误", zap.Any("info", err.Error()))
			return
		}
		jsonData, err = json.Marshal(info)
		err = json.Unmarshal(jsonData.([]byte), &expressInfo)
		if err != nil {
			log.Log().Error("解析物流信息错误1", zap.Any("info", err.Error()))
			return
		}
		//log.Log().Info("供应链订单信息回调", zap.Any("info", expressInfo.Data))
		//log.Log().Info("供应链订单信息回调1", zap.Any("info", orderInfo.OrderItems))

		for _, v := range expressInfo.Data {
			var ids []request2.OrderItemSendInfo
			for _, orderItem := range v.OrderItems {
				for _, localOrderItem := range orderInfo.OrderItems {

					if orderItem.SkuID == localOrderItem.OriginalSkuID {

						if (orderItem.SendStatus == 1 || orderItem.SendStatus == 2) && (localOrderItem.SendStatus == 0 || localOrderItem.SendStatus == 2) {
							//上游和本地的订单item发货状态必须符合要求
							ids = append(ids, request2.OrderItemSendInfo{
								ID:  localOrderItem.ID,
								Num: orderItem.SendNum,
							})
						}

					}
					if orderItem.SkuID == 0 && orderItem.ID == 0 {
						ids = append(ids, request2.OrderItemSendInfo{
							ID:  0,
							Num: 0,
						})
					}
				}
			}
			orderRequest.OrderID = orderInfo.ID
			orderRequest.ExpressNo = v.ExpressNo
			orderRequest.OrderItemIDs = ids
			orderRequest.CompanyCode = v.CompanyCode
			orderRequest.IsEmpty = v.IsEmpty
			err = ExpressSent(orderRequest)
			if err != nil {
				log.Log().Info(strconv.Itoa(int(v.OrderID))+"发货error："+err.Error(), zap.Any("data", orderRequest))
				continue
			}
			err = source.DB().Model(&orderModel.Order{}).Where("id = ?", orderInfo.ID).Update("gather_supply_msg", orderInfo.GatherSupplyMsg+",已发货").Error
			if err != nil {
				return
			}
		}
	}

	return
}

// 修改订单发货
func (*Self) UpdateOrderDelivery(OrderData callback2.OrderCallBack) (err error) {

	var orderInfo orderModel.Order
	err = source.DB().Preload("OrderItems").Where("order_sn =  ?", OrderData.Data.OrderSn).First(&orderInfo).Error
	if err != nil {
		//log.Log().Error("订单不存在", zap.Any("info", err.Error()))
		return
	}
	var orderRequest v1.HandleOrderRequest
	var info, jsonData interface{}
	var requestExpress request.RequestExpress
	var expressInfo Response
	requestExpress.OrderSn = OrderData.Data.OrderSn
	requestExpress.Sku = strconv.FormatInt(OrderData.Data.Sku, 10)
	var orderClass = Self{}
	err = orderClass.InitSetting(orderInfo.GatherSupplyID)
	if err != nil {
		log.Log().Error("前置校验失败", zap.Any("info", err.Error()))
		return
	}
	err, info = orderClass.ExpressQuery(requestExpress)
	if err != nil {
		log.Log().Error("查询物流信息错误", zap.Any("info", err.Error()))
		return
	}
	jsonData, err = json.Marshal(info)
	err = json.Unmarshal(jsonData.([]byte), &expressInfo)
	if err != nil {
		log.Log().Error("解析物流信息错误1", zap.Any("info", err.Error()))
		return
	}
	//log.Log().Info("供应链订单信息回调", zap.Any("info", expressInfo.Data))
	//log.Log().Info("供应链订单信息回调1", zap.Any("info", orderInfo.OrderItems))

	for _, v := range expressInfo.Data {
		var itemExpress orderModel.ItemExpress
		var ids []request2.OrderItemSendInfo
		for _, orderItem := range v.OrderItems {
			for _, localOrderItem := range orderInfo.OrderItems {
				if orderItem.SkuID == localOrderItem.OriginalSkuID {
					err = source.DB().Where("order_item_id = ?", localOrderItem.ID).Where("num = ?", orderItem.SendNum).First(&itemExpress).Error
					if err != nil {
						log.Log().Error("同步修改物流：查询旧物流失败无法修改", zap.Any("info", err.Error()))
						return
					}
					ids = append(ids, request2.OrderItemSendInfo{
						ID:  localOrderItem.ID,
						Num: orderItem.SendNum,
					})
				}
			}
		}
		//orderRequest.OrderID = orderInfo.ID
		//orderRequest.ExpressNo = v.ExpressNo
		//orderRequest.OrderItemIDs = ids
		//orderRequest.CompanyCode = v.CompanyCode
		//orderRequest.IsEmpty = v.IsEmpty
		//err = ExpressSent(orderRequest)
		var orderExpress request2.OrderExpress
		orderExpress.ID = itemExpress.OrderExpressID
		orderExpress.ExpressNo = v.ExpressNo
		orderExpress.CompanyCode = v.CompanyCode
		err = service.UpdateOrderExpresse(orderExpress)
		if err != nil {
			log.Log().Info(strconv.Itoa(int(v.OrderID))+"发货error："+err.Error(), zap.Any("data", orderRequest))
			continue
		}
	}

	return
}
func (*Self) SyncOrderExpNo(unionIdList []string) (err error, data []response.SyncOrderExpNoResponse) {
	return
}

// 申请售后
func (*Self) AfterSalesCreate(requestParam request3.AfterSales) (err error, info *stbz.APIResult) {
	var header map[string]string
	err, header = InitToken()
	if err != nil {
		return
	}
	log.Log().Info("中台供应链同步售后", zap.Any("info", requestParam))
	var responseData *stbz.APIResult
	err, result := utils.Post(selfData.BaseInfo.Host+"/app/afterSales/create", requestParam, header)

	err = json.Unmarshal(result, &responseData)
	if err != nil {
		return
	}

	info = responseData

	fmt.Println("售后申请", string(result))
	return
}

// 获取订单详情
func (*Self) GetOrderByThirdOrderSn(requestParam request3.AfterSales) (err error, info *stbz.APIResult) {
	var header map[string]string
	err, header = InitToken()
	if err != nil {
		return
	}
	log.Log().Info("中台供应链获取订单详情orderDetailByThirdOrderSn", zap.Any("info", requestParam))
	var responseData *stbz.APIResult
	err, result := utils.Get(selfData.BaseInfo.Host+"/app/order/orderDetailByThirdOrderSn?third_order_sn="+requestParam.ThirdOrderSN, header)

	err = json.Unmarshal(result, &responseData)
	if err != nil {
		return
	}
	info = responseData

	fmt.Println("中台供应链获取订单详情", string(result))
	return
}

// 获取售后详情
func (*Self) GetAfterSalesByAfterSalesId(requestParam request3.AfterSales) (err error, info *stbz.APIResult) {
	var header map[string]string
	err, header = InitToken()
	if err != nil {
		return
	}
	log.Log().Info("中台供应链获取售后详情get", zap.Any("info", requestParam))
	var responseData *stbz.APIResult
	err, result := utils.Get(selfData.BaseInfo.Host+"/app/afterSales/get?id="+strconv.Itoa(int(requestParam.Id)), header)

	err = json.Unmarshal(result, &responseData)
	if err != nil {
		return
	}
	info = responseData

	fmt.Println("中台供应链获取售后详情", string(result))
	return
}

// 通过本地订单号获取退货地址
func (*Self) GetAfterSalesShopAddress(thirdOrderSN string, afterSalesId uint) (err error, info *stbz.APIResult) {
	var header map[string]string
	err, header = InitToken()
	if err != nil {
		return
	}
	log.Log().Info("通过本地订单号获取退货地址findShopAddressByOrderId", zap.Any("info", thirdOrderSN), zap.Any("afterSalesId", afterSalesId))
	var responseData *stbz.APIResult
	var result []byte

	err, result = utils.Get(selfData.BaseInfo.Host+"/app/supplier/findShopAddressByOrderId?third_order_sn="+thirdOrderSN+"&after_sales_id="+strconv.Itoa(int(afterSalesId)), header)

	err = json.Unmarshal(result, &responseData)
	if err != nil {
		return
	}
	info = responseData

	fmt.Println("通过本地订单号获取退货地址", string(result))
	return
}

// 收货发货
func (*Self) AfterSalesSend(requestParam request3.SendRequest) (err error, info *stbz.APIResult) {
	var header map[string]string
	err, header = InitToken()
	if err != nil {
		return
	}
	log.Log().Info("中台供应链同步售后send", zap.Any("info", requestParam))
	var responseData *stbz.APIResult
	err, result := utils.Post(selfData.BaseInfo.Host+"/app/afterSales/send", requestParam, header)

	err = json.Unmarshal(result, &responseData)
	if err != nil {
		return
	}

	info = responseData

	fmt.Println("售后申请", string(result))
	return
}

// 换货--用户收货
func (*Self) AfterSalesUserReceive(requestParam request3.AfterSales) (err error, info *stbz.APIResult) {
	var header map[string]string
	err, header = InitToken()
	if err != nil {
		return
	}
	log.Log().Info("中台供应链同步售后userReceive", zap.Any("info", requestParam))
	var responseData *stbz.APIResult
	err, result := utils.Post(selfData.BaseInfo.Host+"/app/afterSales/userReceive", requestParam, header)

	err = json.Unmarshal(result, &responseData)
	if err != nil {
		return
	}

	info = responseData

	fmt.Println("售后申请", string(result))
	return
}

func (*Self) AfterSalesUserClose(requestParam request3.AfterSales) (err error, info *stbz.APIResult) {
	var header map[string]string
	err, header = InitToken()
	if err != nil {
		return
	}
	log.Log().Info("中台供应链同步售后userReceive", zap.Any("info", requestParam))
	var responseData *stbz.APIResult
	err, result := utils.Post(selfData.BaseInfo.Host+"/app/afterSales/close", requestParam, header)

	err = json.Unmarshal(result, &responseData)
	if err != nil {
		return
	}

	info = responseData

	fmt.Println("售后申请", string(result))
	return
}

func (*Self) SelfAfterSalesBeforeCheck(requestParam request3.AfterSales) (err error, info *stbz.APIResult) {
	var header map[string]string
	err, header = InitToken()
	if err != nil {
		return
	}
	var responseData *stbz.APIResult
	err, result := utils.Post(selfData.BaseInfo.Host+"/app/afterSales/afterSalesBeforeCheck", requestParam, header)
	if err != nil {
		err = errors.New("请求售后检验API失败" + err.Error())
		return
	}
	err = json.Unmarshal(result, &responseData)
	if err != nil {
		err = errors.New("请求售后检验API失败1" + err.Error())
		return
	}

	info = responseData

	return
}

func (self *Self) GetAfterSalesTypeNameMap(requestParam request3.AfterSalesTypeSearch, order orderModel.Order) (err error, info *stbz.APIResult) {

	var resOrder *stbz.APIResult

	err, resOrder = self.GetOrderByThirdOrderSn(request3.AfterSales{ThirdOrderSN: strconv.Itoa(int(order.OrderSN))})
	var resOrderData []orderOrder.Order
	if resOrder == nil || resOrder.Data == nil {
		err = errors.New("resOrder or resOrder.Data is nil")
		return

	}
	datas, ok := resOrder.Data.(map[string]interface{})
	if !ok {
		err = errors.New("resOrder.Data is not of type map[string]interface{}")
		return
	}
	orderDetail1, _ := json.Marshal(datas["read"])

	err = json.Unmarshal(orderDetail1, &resOrderData)
	if err != nil {
		log.Log().Error("获取中台供应链订单详情失败"+order.ThirdOrderSN, zap.Any("err", err.Error()))
		err = errors.New("获取中台供应链订单详情失败" + order.ThirdOrderSN + err.Error())
		return
	}

	skuID := order.OrderItems[0].SkuID
	//如果申请的时候传了子订单id 使用这个子订单对应的商品,如果没有默认使用第一个
	if requestParam.OrderItemID != 0 {
		for _, orderItem := range order.OrderItems {
			if requestParam.OrderItemID == orderItem.ID {
				skuID = orderItem.SkuID
				break
			}
		}
	}
	var sku productModel.Sku
	err = source.DB().Unscoped().Where("id = ?", skuID).First(&sku).Error
	if err != nil {
		log.Log().Info("售后处理:查询售后商品规格失败", zap.Any("err", err))
		err = errors.New("售后处理:查询售后商品规格失败" + err.Error())
		return
	}

	var ThirdOrderId, ThirdOrderItemId uint
	for _, orderData := range resOrderData {
		for _, orderDataItem := range orderData.OrderItems {
			if orderDataItem.SkuID == uint(sku.OriginalSkuID) {
				ThirdOrderId = orderDataItem.OrderID
				ThirdOrderItemId = orderDataItem.ID
				break
			}
		}
	}

	var header map[string]string
	err, header = InitToken()
	if err != nil {
		return
	}
	var responseData *stbz.APIResult
	err, result := utils.Get(selfData.BaseInfo.Host+"/app/afterSales/getAfterSalesTypeNameMap?order_id="+strconv.Itoa(int(ThirdOrderId))+"&order_item_id="+strconv.Itoa(int(ThirdOrderItemId)), header)
	if err != nil {
		err = errors.New("请求售后方式API失败" + err.Error())
		return
	}
	err = json.Unmarshal(result, &responseData)
	if err != nil {
		err = errors.New("请求售后方式API失败1" + err.Error())
		return
	}

	info = responseData

	return
}
