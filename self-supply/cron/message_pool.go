package cron

import (
	model2 "application/model"
	"encoding/json"
	funcallback "gather-supply/component/callback"
	"go.uber.org/zap"
	"product/model"
	"public-supply/callback"
	"public-supply/common"
	pubmodel "public-supply/model"
	mq "public-supply/work_mq"
	"self-supply/component/goods"
	"strings"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
	"yz-go/utils"
)

func PushMessagePoolTask() {
	task := cron.Task{
		Key:  "message_pool_self_supply",
		Name: "中台供应链定时查询消息池",
		Spec: "*/10 * * * * *",
		Handle: func(task cron.Task) {
			MessagePoolTask()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func MessagePoolTask() {
	var selfGathers []model.GatherSupply
	err := source.DB().Where("category_id =?", common.SUPPLY_SELF).Where("deleted_at is null").Find(&selfGathers).Error
	if err != nil {
		return
	}
	for _, gather := range selfGathers {
		var header map[string]string
		var setting goods.SelfSupplySetting
		err, setting = goods.InitSetting(uint(gather.ID))
		if err != nil {
			log.Log().Info("消息池请求setting错误：", zap.Any("err", err))
			continue
		}
		if err, header = goods.InitToken(uint(gather.ID)); err != nil {
			log.Log().Info("消息池请求token错误：", zap.Any("err", err))
			continue
		}
		pageSize := 100
		requestParam := map[string]interface{}{
			"page_size": pageSize,
		}
		var result []byte
		err, result = utils.Post(setting.BaseInfo.Host+"/app/application/getMessage", requestParam, header)
		if err != nil {
			log.Log().Info("消息池请求错误：", zap.Any("err", err))
			continue
		}
		var messageList MessagePoolResponse
		err = json.Unmarshal(result, &messageList)
		if err != nil {
			continue
		}
		if len(messageList.Data.List) > 0 {
			log.Log().Info("消息池返回数据", zap.Any("data", messageList.Data.List))
			var ids []uint
			//遍历消息池数据，插入CallBack方法中
			for _, message := range messageList.Data.List {
				ids = append(ids, message.ID)

				if find := strings.Contains(message.TypeString, "order"); find {

					if message.TypeString == string(callback.ORDER_SENDING) {
						//中台供应链暂不处理部分发货消息，原因：一个订单：买了十个商品（同一个），上游分四次发货，第一次发一个第二次发两个，第三次发三个，第四次发7个，之后下游接收到四次消息，每次优先使用第一个快递记录发货，这样第一个快递就发了至少三次，第二个约2-3次，第三个同上。第四次接收发货消息时无法对最后一个快递发货（因为最后一个要发7个）
						continue
					}
					var orderSn struct {
						OrderSn string `json:"order_sn"`
					}
					err = json.Unmarshal([]byte(message.Content), &orderSn)
					if err != nil {
						continue
					}
					if strings.Contains(message.TypeString, "fulu") {
						callBackInterface := funcallback.NewCallback("fulu")
						requestType := pubmodel.RequestCallBackType{
							SelfCallBackType: pubmodel.SelfCallBackType{
								MessageType: message.TypeString,
								OrderSn:     orderSn.OrderSn,
							},
						}
						if err = callBackInterface.CallBackMessage(requestType); err != nil {
							log.Log().Info("供应链回调处理出错，", zap.Any("err", err.Error()))
							continue
						}
						continue
					}
					err = mq.PublishMessage(callback.CallBackType{
						Type:  message.TypeString,
						MsgID: "self" + message.Content,
						Data: callback.OrderData{
							OrderSn: orderSn.OrderSn,
						}})
					if err != nil {
						log.Log().Error("CallBackMessage 消息发送失败", zap.Any("info", err.Error()))
					}

				}

				if find := strings.Contains(message.TypeString, "goods"); find {
					var productId struct {
						ProductId int `json:"product_id"`
					}
					err = json.Unmarshal([]byte(message.Content), &productId)
					if err != nil {
						continue
					}
					if productId.ProductId == 0 {
						//log.Log().Error("测试消息用，返回")
						continue
					}
					if strings.Contains(message.TypeString, "equity") {
						callBackInterface := funcallback.NewCallback("fulu")
						requestType := pubmodel.RequestCallBackType{
							SelfCallBackType: pubmodel.SelfCallBackType{
								MessageType: message.TypeString,
								ProductID:   uint(productId.ProductId),
							},
						}
						if err = callBackInterface.CallBackMessage(requestType); err != nil {
							log.Log().Info("供应链回调处理出错，", zap.Any("err", err.Error()))
							continue
						}
						continue
					}
					var product model.Product
					err = source.DB().Select("id").Where("source_goods_id=?", productId.ProductId).First(&product).Error
					if err != nil {
						continue
					}

					err = mq.PublishMessage(callback.CallBackType{
						Type:  message.TypeString,
						MsgID: "self" + message.Content,
						Data: callback.GoodsIds{
							GoodsIds: []int{productId.ProductId},
						},
					})
					if err != nil {
						log.Log().Error("CallBackMessage 消息发送失败", zap.Any("info", err.Error()))
					}

				}

			}
			requestParam = map[string]interface{}{
				"ids": ids,
			}
			err, result = utils.Post(setting.BaseInfo.Host+"/app/application/deleteMessage", requestParam, header)
			if err != nil {
				log.Log().Info("消息池请求错误：", zap.Any("err", err))
				continue
			}
		}

	}

}

type MessagePoolResponse struct {
	Code int `json:"code"`
	Data struct {
		List []model2.MessagePoolResponse `json:"list"`
	} `json:"data"`
	Message string `json:"message"`
}
