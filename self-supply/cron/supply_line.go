package cron

import (
	model2 "product/model"
	"public-supply/callback"
	"public-supply/model"
	mq "public-supply/work_mq"
	"yz-go/cron"
	"yz-go/source"
)

func PushSupplyLineHandle() {
	task := cron.Task{
		Key:  "supply_line_self",
		Name: "定时请求标识",
		Spec: "57 16 */1 * * *",
		//Spec: "0 */1 * * * *",
		Handle: func(task cron.Task) {
			SupplyLine()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func SupplyLine() {

	var gatherSupply []model.GatherSupply

	err := source.DB().Where("category_id = 2").Find(&gatherSupply).Error
	if err != nil {
		return
	}
	for _, supply := range gatherSupply {
		var productIds []uint

		err = source.DB().Model(&model2.Product{}).Where("gather_supply_id = ?", supply.ID).Where("deleted_at is NULL").Where("is_supply_line = 0").Pluck("source_goods_id", &productIds).Error
		if err != nil {
			return
		}
		var goodsIds []int
		for _, v := range productIds {
			goodsIds = append(goodsIds, int(v))

			if err != nil {
				return
			}
			if len(goodsIds) >= 100 {
				err = mq.PublishMessage(callback.CallBackType{
					Type:  string(callback.GOODS_PRICE_ALERT),
					MsgID: "selfdasdadsa",
					Data: callback.GoodsIds{
						GoodsIds: goodsIds,
					},
				})
				goodsIds = []int{}
			}
		}
		if err != nil {
			return
		}
		err = mq.PublishMessage(callback.CallBackType{
			Type:  string(callback.GOODS_PRICE_ALERT),
			MsgID: "selfdasdadsa",
			Data: callback.GoodsIds{
				GoodsIds: goodsIds,
			},
		})
	}

	return
}
