package listener

import (
	"go.uber.org/zap"
	"public-supply/callback"
	self "self-supply/component/goods"
	"self-supply/mq"
	"yz-go/component/log"
)

func PushSelfBatchHandles() {
	mq.PushHandles("selfBatchHandle", func(product mq.SelfBatchHandleMessage) error {
		goodsModel := self.Self{}
		err := goodsModel.GoodsPriceAlert(callback.GoodsCallBack{
			Type:  string(callback.GOODS_PRICE_ALERT),
			MsgID: "selfdasdadsa",
			Data: callback.GoodsIds{
				GoodsIds: product.Ids,
			},
		})
		if err != nil {
			log.Log().Info("商品更新失败", zap.Any("data", product))
			return nil
		}
		return nil
	})
}
