package common

import (
	"github.com/shopspring/decimal"
	"strconv"
)

var IsAllPushUser = 0 //是否在进行一键用户导入 1是 0否
var IsAllPushOrder = 0 //是否在进行一键订单导入 1是 0否
/**
  补0
*/
func Complement(data int)(res int,err error)  {

	stringData := strconv.Itoa(data)
	num := 6 - len(stringData)
	for i :=0;i<num;i++ {
		stringData +="0"
	}
	res,err = strconv.Atoi(stringData)
	return
}
//分转元
func Fen2yuan(price uint)(res float64)  {
    d := decimal.New(1,2)
    res,_  = decimal.NewFromInt(int64(price)).DivRound(d,2).Float64()
	return
}