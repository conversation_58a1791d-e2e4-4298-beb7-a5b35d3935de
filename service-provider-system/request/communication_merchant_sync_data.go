package request

import (
	yzRequest "yz-go/request"
)
type MemberOperationSearch struct {
	yzRequest.PageInfo
	StartAT string `json:"start_at"  form:"start_at"`
	EndAT   string `json:"end_at" form:"end_at"`
	Type    uint   `json:"type" form:"type"`
}


type CommunicationMerchantSyncDataSearch struct {
	yzRequest.PageInfo
	PaymentStoreCode string `json:"payment_store_code" form:"payment_store_code"`
	StoreName        string `json:"store_name" form:"store_name"`
}