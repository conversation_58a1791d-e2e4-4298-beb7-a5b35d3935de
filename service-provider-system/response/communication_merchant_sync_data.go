package response

import (
	"service-provider-system/model"
)

// 主商家数据
type GetCommunicationMerchantSyncDataList struct {
	model.CommunicationMerchantSyncData
	CommunicationPaymentInfo model.CommunicationPaymentInfo `json:"communication_payment_info" gorm:"foreignKey:payment_store_code;references:payment_store_code"`
}

func (GetCommunicationMerchantSyncDataList) TableName() string {
	return "communication_merchant_sync_data"
}
