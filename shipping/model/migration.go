package model

import (
	"encoding/json"
	"strings"
	"yz-go/source"

	"gorm.io/gorm/clause"
)

func Migrate() (err error) {
	err = source.DB().AutoMigrate(
		ShippingMethod{},
		ExpressTemplate{},
		ExpressTemplateItem{},
	)
	if err != nil {
		return
	}
	err = source.DB().Delete(&ShippingMethod{}, "id > ?", 0).Error
	if err != nil {
		return
	}
	shippingMethods := []ShippingMethod{
		{
			Model: source.Model{
				ID: 1,
			},
			Name:   "快递",
			Code:   "express",
			Sort:   1,
			Status: 0,
		}, {
			Model: source.Model{
				ID: 2,
			},
			Name:   "门店自提",
			Code:   "self_delivery",
			Sort:   2,
			Status: 0,
		},
		{
			Model: source.Model{ //租赁 -- 状态变为1 不在公用时显示
				ID: 3,
			},
			Name:   "配送上门",
			Code:   "door_delivery",
			Sort:   3,
			Status: 1,
		},
	}
	err = source.DB().Clauses(clause.OnConflict{DoNothing: true}).Model(&ShippingMethod{}).Create(&shippingMethods).Error
	if err != nil {
		return
	}

	/** 删除订单收货地址和用户收货地址detail中的省市区县街道字符
	update `addresses` as a left join `regions` as p on `a`.`province_id` = p.id left join `regions` as c on `a`.city_id = c.id left join `regions` as co on `a`.`county_id` = co.id left join `regions` as t on `a`.`town_id` = t.id set a.`detail` = REPLACE(REPLACE(REPLACE(REPLACE(a.`detail`, p.name, ''), c.name, ''), co.name, ''), t.name, '') where a.`detail` like "%市%"

	update `shipping_addresses` as a left join `regions` as p on `a`.`province_id` = p.id left join `regions` as c on `a`.city_id = c.id left join `regions` as co on `a`.`county_id` = co.id left join `regions` as t on `a`.`town_id` = t.id set a.`detail` = REPLACE(REPLACE(REPLACE(REPLACE(a.`detail`, p.name, ''), c.name, ''), co.name, ''), t.name, '') where a.`detail` like "%市%"

	UPDATE `regions` SET `name` = '重庆市' WHERE `id` = 5001;
	UPDATE `regions` SET `name` = '上海市' WHERE `id` = 3101;
	UPDATE `regions` SET `name` = '天津市' WHERE `id` = 1201;
	UPDATE `regions` SET `name` = '北京市' WHERE `id` = 1101;
	**/
	InitTemplates()
	return
}

func InitTemplates() {
	var data []ExpressTemplateItem
	err := source.DB().Where("regions like ? or  regions like ? or regions like ? or  regions like ?", "%:46,%", "%:71,%", "%:81,%", "%:82,%").Find(&data).Error
	if err != nil {
		return
	}
	if len(data) > 0 {
		var billUpdate []map[string]interface{}

		for _, item := range data {
			var jsonData []byte
			jsonData, err = json.Marshal(item.Regions)
			if err != nil {
				return
			}
			jsonDataString := string(jsonData)
			jsonDataString = strings.Replace(jsonDataString, ":46,", ":460000,", -1)
			jsonDataString = strings.Replace(jsonDataString, ":71,", ":710000,", -1)
			jsonDataString = strings.Replace(jsonDataString, ":81,", ":810000,", -1)
			jsonDataString = strings.Replace(jsonDataString, ":82,", ":820000,", -1)
			billUpdateRow := make(map[string]interface{})
			billUpdateRow["regions"] = jsonDataString
			billUpdateRow["id"] = item.ID
			billUpdate = append(billUpdate, billUpdateRow)
		}

		if len(billUpdate) > 0 {
			err = source.BatchUpdate(billUpdate, "express_template_items", "")
			if err != nil {
				return
			}
		}
	}

}
