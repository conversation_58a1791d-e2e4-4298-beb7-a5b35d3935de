import {confirm} from "@/decorators/decorators";
import TagsDialog from "../components/tagsDialog";
import {mapGetters} from "vuex";

export default {
    name: "productAlbumIndex",
    components: {TagsDialog},
    props: {
        productID: {
            type: Number,
            required: false
        }
    },
    data() {
        return {
            shareIsShow: false,
            getAlbumSetting: {},
            title: "新增", // 标题 新增,编辑 字样
            isShow: false, // 弹出层是否显示
            // 初始化表单
            formData: {
                name: "", // 专辑名称
                describe: "", // 描述
                covers: [ // 封面图片 *限制一张
                    {src: ""}
                ],
                relations: [ // 标签 *最多选五个  数据格式 {tag_id:}

                ],
                is_share: 2, // 是否共享  1:是/共享 2:否/私有
            },
            coverImgs: [],// 所有封面图片 *后台+自上传 格式:{url:"",come} come 1=后台 2=自上传
            checkedTags: [], // 选中的标签
            // 表单验证
            rules: {
                name: {required: true, message: "请输入专辑名称", trigger: "blur"}
            }
        }
    },
    /*watch: {
        getAlbumSetting(val) {
            this.getAllCover(val.value.default_cover)
        }
    },*/
    computed: {
        token() {
            return this.$ls.getToken()
        },
        // ...mapGetters("albumSetting", ["getAlbumSetting"])
    },
    methods: {
        // 关闭dialog
        handleClose() {
            try {
                this.$refs.form.resetFields()
            } catch {
            } finally {
                this.isShow = false
                this.title = "新增"
                this.getAlbumSetting = {}
                // 初始化表单
                this.formData = {
                    name: "",
                    describe: "",
                    covers: [
                        {src: ""}
                    ],
                    relations: [],
                    is_share: 2,
                }
                this.coverImgs = []
                this.checkedTags = []
            }
        },
        // 删除选中tag
        handleTagsClose(index) {
            this.checkedTags.splice(index, 1)
        },
        // 获取选中的标签
        getCheckedTags(tags) {
            let allTags = this.$fn.removeRep(this.checkedTags, tags)
            if (allTags.length > 5) {
                this.$message.error("标签最多选择5个")
                return false
            }
            this.checkedTags = allTags
        },
        // 打开所有标签dialog
        openTagsDialog() {
            this.$refs.tagsDialog.init()
        },
        // 删除自上传图片
        @confirm("提示", "确认删除?")
        delCoverImgs(index) {
            this.coverImgs.splice(index, 1)
        },
        // 点击遮罩层选中封面
        checkedUrl(url) {
            this.formData.covers[0].src = url
        },
        // 初始化
        init() {
            this.isShow = true
            // 获取后台所有封面
            this.$get("/productAlbumApi/findSetting").then(res => {
                if (res.code === 0) {
                    this.getAlbumSetting = JSON.parse(res.data.setting.value)
                    let permissions = this.getAlbumSetting.permissions
                    this.shareIsShow = permissions.indexOf(this.$ls.getUserId()) !== -1
                    this.getAllCover(this.getAlbumSetting.default_cover)
                }
            })
        },
        // 获取后台所有封面
        getAllCover(default_cover) {
            this.coverImgs = default_cover.map(item => ({
                url: item.src,
                come: 1
            }))
        },
        // 图片上传成功钩子
        handleCoverSuccess(res) {
            this.coverImgs.push({
                url: res.data.file.url,
                come: 2
            })
        },
        // 限制图片最大10M
        beforeUpload(file) {
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                this.$message.error('上传头像图片大小不能超过 10MB!');
            }
            return isLt10M;
        },
        // 确认/提交
        confirm() {
            this.$refs.form.validate((valid) => {
                if (!valid) return false;
                if (this.checkedTags && this.checkedTags.length > 0) {
                    this.formData.relations = this.checkedTags.map((i) => ({
                        tag_id: i.id
                    }))
                }
                if (this.productID) {
                    this.formData.relation_product = [
                        {
                            product_id: this.productID
                        }
                    ]
                }
                this.$post("/productAlbumApi/createAlbum", this.formData).then(res => {
                    if (res.code === 0) {
                        this.$message.success(res.msg)
                        this.$emit("success")
                        this.handleClose()
                    }
                })
            })
        }
    }
}