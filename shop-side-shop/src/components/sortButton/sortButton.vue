<template>
  <div class="f fac pointer">
    <div @click="onClickText">
      <slot></slot>
    </div>
    <div class=" yz-icon-d-caret">
      <i :class="['el-icon-caret-top', upClass]" @click="onClickUp"></i>
      <i :class="['el-icon-caret-bottom', downClass]" @click="onClickDown"></i>
    </div>
  </div>
</template>

<script>
import Emitter from 'element-ui/src/mixins/emitter'
export default {
  name: 'SortButton',
  mixins: [Emitter],
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  computed: {
    upClass () {
      if (this.$parent.value.value !== this.value) {
        return 'grey'
      }
      // 点击销量默认值改成由高到低
      if(this.value === 'hot') {
        return this.$parent.value.sort === '2' ? 'red' : 'grey' //1:升序，2：降序
      } else {
        return this.$parent.value.sort === '1' ? 'red' : 'grey' //1:升序，2：降序
      }
    },
    downClass () {
      if (this.$parent.value.value !== this.value) {
        return 'grey'
      }
      // 点击销量默认值改成由高到低
      if(this.value === 'hot') {
        return this.$parent.value.sort === '1' ? 'red' : 'grey' //1:升序，2：降序
      } else {
        return this.$parent.value.sort === '2' ? 'red' : 'grey' //1:升序，2：降序
      }
    }
  },
  methods: {
    onClickText() {
      console.log('onClickSlot');
      if (this.$parent.value.value !== this.value) {
        return this.onClickUp();
      }
      return this.$parent.value.sort === '1'
        ? this.onClickDown()
        : this.onClickUp(); //1:升序，2：降序
    },
    onClickUp () {
      this.dispatch('SortButtonGroup', 'change', { value: this.value, sort: '1' })
    },
    onClickDown () {
      this.dispatch('SortButtonGroup', 'change', { value: this.value, sort: '2' })
    }
  }
}
</script>

<style lang="scss" scoped>
.pointer {
  cursor: pointer;
  user-select: none;
}
.yz-icon-d-caret {
  display: flex;
  flex-direction: column;
  // margin: 0 3px;
  width: 10px;
  i {
    color: #8D8D8D;
  }
  .el-icon-caret-top{
    height: 7px;
  }
  .red {
    color: #F11111;
  }
}
</style>
