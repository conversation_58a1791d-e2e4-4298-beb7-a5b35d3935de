<template>
  <div class="f fac pointer">
    <div @click="onClickText">
      <slot></slot>
    </div>
    <div class="yz-icon-d-caret">
      <i :class="['el-icon-caret-top', upClass]" @click="onClickUp"></i>
      <i :class="['el-icon-caret-bottom', downClass]" @click="onClickDown"></i>
    </div>
  </div>
</template>

<script>
import Emitter from 'element-ui/src/mixins/emitter';

export default {
  name: 'SortButtonTwo',
  mixins: [Emitter],
  props: {
    sort_type: {
      type: Number,
      default: 0
    }
  },
  computed: {
    upClass () {
      if (this.$parent.value.sort_type !== this.sort_type) {
        return 'grey';
      }
      return this.$parent.value.sort === true ? 'red' : 'grey'; // 升序为 true，降序为 false
    },
    downClass () {
      if (this.$parent.value.sort_type !== this.sort_type) {
        return 'grey';
      }
      return this.$parent.value.sort === false ? 'red' : 'grey'; // 降序为 false，升序为 true
    }
  },
  methods: {
    onClickText() {
      console.log('onClickSlot');
      // 如果当前排序项不是选中的排序项，则选择新的排序项
      if (this.$parent.value.sort_type !== this.sort_type) {
        return this.onClickUp();
      }
      // 否则切换升降序
      return this.$parent.value.sort === true
        ? this.onClickDown() // 当前为升序，点击切换为降序
        : this.onClickUp(); // 当前为降序，点击切换为升序
    },
    onClickUp() {
      // 发送排序升序事件
      this.dispatch('SortButtonGroup', 'change', { sort_type: this.sort_type, sort: true });
    },
    onClickDown() {
      // 发送排序降序事件
      this.dispatch('SortButtonGroup', 'change', { sort_type: this.sort_type, sort: false });
    }
  }
}
</script>

<style lang="scss" scoped>
.pointer {
  cursor: pointer;
  user-select: none;
}
.yz-icon-d-caret {
  display: flex;
  flex-direction: column;
  width: 10px;
  i {
    color: #8D8D8D;
  }
  .el-icon-caret-top {
    height: 7px;
  }
  .red {
    color: #F11111;
  }
}
</style>
