export default {
    data() {
        return {
            isLoading: false,
            page: 1,
            pageSize: 20,
            total: 0,
            hasMore: true,
            // 将 timer 移到 data 中
            timer: null
        };
    },
    mounted() {
        window.addEventListener('scroll', this.handleScroll);
    },
    beforeDestroy() {
        window.removeEventListener('scroll', this.handleScroll);
    },
    methods: {
        disableScroll() {
            // document.body.style.overflow = 'hidden';
        },
        enableScroll() {
            // document.body.style.overflow = 'auto';
        },
        handleScroll() {
            if (this.isLoading || !this.hasMore) return;
            // 如果计时器存在，说明还在节流时间内，直接返回
            if (this.timer) return;

            const { scrollTop, scrollHeight, clientHeight } = document.documentElement;
            if (scrollTop + clientHeight >= scrollHeight - 900) {
                this.loadNextPage();
                // 设置1.5秒的节流计时器
                this.timer = setTimeout(() => {
                    this.timer = null;
                }, 500);
            }
        },
        homeHandleScroll() {
            if (this.isLoading || !this.hasMore) return;
            // 如果计时器存在，说明还在节流时间内，直接返回
            if (this.timer) return;

            const { scrollTop, scrollHeight, clientHeight } = document.documentElement;
            if (scrollTop + clientHeight >= scrollHeight - 900) {
                this.loadNextPage();
                // 设置1.5秒的节流计时器
                this.timer = setTimeout(() => {
                    this.timer = null;
                }, 500);
            }
        }
    },

};