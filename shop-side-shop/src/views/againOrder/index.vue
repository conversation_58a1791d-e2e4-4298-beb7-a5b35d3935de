<template>
    <div class="mt20">
        <div class="inner cart-box">
            <!-- 批量选择配送信息 -->
            <div class="distribution-box bgw">
                <p>
                    批量选择配送信息
                </p>
                <el-divider class="mtb"></el-divider>
                <el-form :model="formData" label-width="80px" label-position="left">
                    <el-form-item label="配送方式">
                        <el-radio-group
                            v-model="shippingMethodsGlobalTag"
                            class="cart-radio-group"
                            @change="shippingMethodsGlobalChange()"
                        >
                            <el-radio
                                v-for="item in shipping_methods"
                                :key="item.id"
                                :label="item.id"
                            >
                                {{item.name}}
                            </el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="收货地址">
                        <el-button size="small" @click="openSelectAddress"
                            >选择收货人及地址</el-button
                        >
                        <el-button size="small" @click="openRecipientsDialog"
                            >新增收货人及地址</el-button
                        >
                    </el-form-item>
                    <el-form-item label="已选地址">
                        <p>{{addressStr}}</p>
                    </el-form-item>
                </el-form>
            </div>
            <!-- 购物车列表 -->
            <div class="mt20"></div>
            <el-row class="bgw cart-table-head">
                <el-col :span="2">
                    <el-checkbox
                        :indeterminate="isIndeterminate"
                        v-model="checkAll"
                        @change="handleCheckAllChange"
                        >全选
                    </el-checkbox>
                </el-col>
                <el-col :span="13">
                    <p class="text-center">商品信息</p>
                </el-col>
                <el-col :span="2">
                    <p class="text-center">单价</p>
                </el-col>
                <el-col :span="3">
                    <p class="text-center">数量</p>
                </el-col>
                <el-col :span="2">
                    <p class="text-center">小计</p>
                </el-col>
                <el-col :span="2">
                    <p class="text-center">操作</p>
                </el-col>
                <!-- <el-col :span="2" class="text-center">
                    <el-button type="text" class="del-text-ben">删除</el-button>
                </el-col> -->
            </el-row>
            <!-- 商家 -->
            <el-row class="bgw mt20 cart-table-con" v-for="item in shopping_carts">
                <el-col>
                    <el-checkbox
                        v-if="item.is_expired === 0"
                        class="order-checkbox"
                        @change="handleCheckedCitiesChange(item)"
                        v-model="checkedCities"
                        :label="item.id"
                        :value="item.id"
                    >
                        <!--好像是不可以写两个参数-->
                        <div
                            v-if="item.supplier.id"
                            class="f fac"
                            @click.stop.prevent="$_blank('/store',{sid:item.supplier.id,type:0})"
                        >
                            <!--                        <div v-if="item.supplier.id" class="f fac"   @click="$router.push({path:'/store', query:{sid:item.supplier.id, type:0}})">-->
    
                            <el-avatar
                                size="large"
                                :src="item.supplier.shop_logo"
                            ></el-avatar>
                            <p>{{item.supplier.name}}</p>
                        </div>
                        <div v-else class="f fac">
                            <el-avatar
                                size="large"
                                :src="item.supplier.shop_logo"
                            ></el-avatar>
                            <p>{{item.supplier.name}}</p>
                        </div>
                    </el-checkbox>
                    <!-- 商品失效显示 -->
                    <div v-if="item.is_expired === 1" class="order-checkbox">
                        <!--好像是不可以写两个参数-->
                        <div
                            v-if="item.supplier.id"
                            class="f fac"
                            @click.stop.prevent="$_blank('/store',{sid:item.supplier.id,type:0})"
                        >
                            <el-avatar
                                size="large"
                                :src="item.supplier.shop_logo"
                            ></el-avatar>
                            <p class="ml10">{{item.supplier.name}}</p>
                        </div>
                        <div v-else class="f fac">
                            <el-avatar
                                size="large"
                                :src="item.supplier.shop_logo"
                            ></el-avatar>
                            <p class="ml10">{{item.supplier.name}}</p>
                        </div>
                    </div>
                </el-col>
                <el-col>
                    <el-divider class="mtb"></el-divider>
                </el-col>
                <!-- 商品 -->
                <el-row v-if="item.is_expired === 0" class="order-goods-list-box">
                    <el-col :span="15">
                        <div
                            class="f fac"
                            @click.stop="$_blank('/goodsDetail',{goods_id:item.product.id})"
                        >
                            <!-- <img :src="item.product.image_url" class="order-goods-img"> -->
                            <m-image
                                :src="item.sku.image_url ? item.sku.image_url : item.product.image_url"
                                class="order-goods-img"
                                :size="['120px','120px']"
                            ></m-image>
                            <div class="order-goods-text">
                                <p>{{item.product.title}}</p>
                                <p class="mt20">
                                    规格:{{item.sku.title}}
                                    <span 
                                        class='icon-icon_edit iconfont'
                                        style='color: #AEAEAE;font-size: 14px;'
                                        @click.stop="updateSku(item)"
                                    ></span>
                                </p>
                            </div>
                        </div>
                    </el-col>
                    <el-col :span="2">
                        <p class="text-center table-jc">
                            ￥{{item.sku.price|formatF2Y}}
                        </p>
                    </el-col>
                    <el-col :span="3" class="table-jc">
                        <el-input-number
                            :disabled="item.is_expired === 1"
                            size="small"
                            :min="1"
                            :max="item.stock"
                            v-model="item.qty"
                            @change="orderQtyChange(item)"
                        >
                        </el-input-number>
                    </el-col>
                    <el-col :span="2">
                        <p class="text-center table-jc">
                            ￥{{item.sku.price*item.qty|formatF2Y}}
                        </p>
                    </el-col>
                    <el-col :span="2" class="table-jc text-center">
                        <a href="javascript:;" @click="deleteOrderDialog(item)">
                            <i class="iconfont icon-pc_line_delete"></i>
                        </a>
                    </el-col>
                </el-row>
                <!-- 失效时显示 -->
                <el-row
                    v-if="item.is_expired === 1"
                    class="order-goods-list-box"
                    style="color: #999999;"
                >
                    <el-col :span="15">
                        <div
                            class="f fac"
                            @click.stop="$_blank('/goodsDetail',{goods_id:item.product.id})"
                        >
                            <!-- <img :src="item.product.image_url" class="order-goods-img"> -->
                            <div class="order-goods-expired">失效</div>
                            <m-image
                                :src="item.product.image_url"
                                class="order-goods-img"
                                :size="['120px','120px']"
                            ></m-image>
                            <div class="order-goods-text">
                                <p>{{item.product.title}}</p>
                                <p class="mt20">
                                    规格:{{item.sku.title}}
                                    <span 
                                        class='icon-icon_edit iconfont'
                                        style='color: #AEAEAE;font-size: 14px;'
                                        @click.stop="updateSku(item)"
                                    ></span>
                                </p>
 
                            </div>
                        </div>
                    </el-col>
                    <el-col :span="2">
                        <p class="text-center table-jc">￥0.00</p>
                    </el-col>
                    <el-col :span="3" class="table-jc">
                        <el-input-number
                            :disabled="item.is_expired === 1"
                            size="small"
                            :min="1"
                            v-model="item.qty"
                            @change="orderQtyChange(item)"
                        >
                        </el-input-number>
                    </el-col>
                    <el-col :span="2">
                        <p class="text-center table-jc">￥0.00</p>
                    </el-col>
                    <el-col :span="2" class="table-jc text-center">
                        <a href="javascript:;" @click="deleteOrderDialog(item)">
                            <i class="iconfont icon-pc_line_delete"></i>
                        </a>
                    </el-col>
                </el-row>
                <!-- 配送信息 -->
                <el-col>
                    <div v-if="item.is_expired === 0" class="distribution-box bgw">
                        <p>
                            选择配送信息
                        </p>
                        <el-divider class="mtb"></el-divider>
                        <el-form
                            :model="formData"
                            label-width="80px"
                            label-position="left"
                        >
                            <el-form-item label="配送方式">
                                <el-radio-group
                                    v-model="item.shipping_method_id"
                                    class="cart-radio-group"
                                >
                                    <el-radio
                                        v-for="shippingMethodsItem in item.shipping_methods"
                                        :key="shippingMethodsItem.id"
                                        :label="shippingMethodsItem.id"
                                        @change="shippingMethodsChange(item)"
                                    >
                                        {{shippingMethodsItem.name}}
                                    </el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="收货地址">
                                <el-button
                                    size="small"
                                    @click="openSelectAddressById(item)"
                                    >选择收货人及地址</el-button
                                >
                                <el-button
                                    size="small"
                                    @click="openRecipientsDialogById(item.id)"
                                    >新增收货人及地址</el-button
                                >
                            </el-form-item>
                            <el-form-item label="已选地址">
                                <p>{{assemblyAddress(item.address)}}</p>
                            </el-form-item>
                        </el-form>
                    </div>
                    <!-- 商品失效显示 -->
                    <div v-if="item.is_expired === 1" class="distribution-box bgw">
                        <el-divider class="mtb"></el-divider>
                        <el-form
                            :model="formData"
                            label-width="80px"
                            label-position="left"
                        >
                            <el-form-item label="失效原因:">
                                <div class="f fac fjsb">
                                    <p style="width: 900px;">
                                        {{ item.expired_message }}
                                    </p>
                                    <el-button
                                        style="width: 82px; height: 32px;"
                                        size="mini"
                                        @click="resetChoose(item)"
                                        >重新选择</el-button
                                    >
                                </div>
                            </el-form-item>
                        </el-form>
                    </div>
                </el-col>
            </el-row>
        </div>
        <!-- 底部结算 -->
        <div class="footer-account-box bgw">
            <div class="inner f fac fjsb">
                <div class="left-box f fac">
                    <el-checkbox
                        :indeterminate="isIndeterminate"
                        v-model="checkAll"
                        @change="handleCheckAllChange"
                        >全选
                    </el-checkbox>
                    <p class="ppp" @click="deleteBatchDialog">删除选中商品</p>
                    <p class="red-p">已选{{goods_count}}件商品</p>
                </div>
                <div class="right-box f fac">
                    <span>总价(含快递)</span>
                    <span>{{amount|formatF2Y}}</span>
                    <el-button
                        @click="cartSubmit"
                        v-loading.fullscreen.lock="fullscreenLoading"
                        >结算</el-button
                    >
                </div>
            </div>
        </div>
        <!-- 新增收货人及地址 -->
        <RecipientsDialog
            ref="recipientsDialog"
            @callBackAddressList="callBackNewAddress"
        ></RecipientsDialog>
        <!-- 选择收货人及地址 -->
        <select-address
            ref="selectAddress"
            @openQrBox="openQrBox"
            @addAddress="addAddress"
        ></select-address>
        <!-- 确认框 -->
        <qr-box
            ref="qrBox"
            title="提示信息"
            @handleClose="qrBoxHandleClose"
            content="您确定用批量选择的配送信息，作为下面全部订单的配送信息吗"
        ></qr-box>
    
        <!-- 新增收货人及地址 -->
        <RecipientsDialog
            ref="recipientsDialogById"
            @callBackAddressList="callBackNewAddressById"
        ></RecipientsDialog>
        <!-- 选择收货人及地址 -->
        <select-address
            ref="selectAddressById"
            @openQrBox="openQrBoxById"
            @addAddress="addAddress"
        ></select-address>
        <skuPopup ref="skuPopup" @onOpenSkuPopup="onOpenSkuPopup"></skuPopup>
    </div>

</template>

<script>
//新增收货人地址
import RecipientsDialog from '@/views/personalCenter/recipients/components/dialog';
//选择收货地址
import SelectAddress from '../buyerCart/components/selectAddress.vue';
import skuPopup from './components/skuPopup.vue'

export default {
    name: 'againOrderIndex',
    components: { RecipientsDialog, SelectAddress,skuPopup },
    data() {
        return {
            fullscreenLoading: false,
            checkAll: false,
            isIndeterminate: false,

            // 配送方式
            disModeList: [
                { label: '快递', value: 1 },
                { label: '物流到付', value: 2 },
                { label: '自提', value: 3 },
            ],
            formData: {
                // 配送方式
                disMode: 1,
            },
            addressGlobalTemp: {},
            address: {},
            addressStr: '请选择地址',

            checkedCities: [],
            shopping_carts: [],
            shippingMethodsGlobalTag: 0,
            shipping_methods: [],
            amount: 0,
            goods_count: 0,
            tempId: -1,

            order_id: null, // 再来一单订单id
        };
    },
    mounted() {
        window.addEventListener('scroll', this.handleScroll, true);
        this.order_id = this.$route.query.order_id ? parseInt(this.$route.query.order_id) : 0;
        this.initCart();
    },
    methods: {
        // 切换商品id
        onOpenSkuPopup(obj) {
            let that = this;
            let para = {
                id: obj.id,
                sku_id: obj.sku_id,
                order_id:this.order_id
            };
            that.$post('/shoppingcart/updateByReorder', para)
                .then(function(res) {
                    if (res.code == 0) {
                        that.setCartView(res.data);
                    }
                })
                .catch(function(res) {
                    console.log(res);
                });
        },
        updateSku(item) {
            this.$refs.skuPopup.info(item.product.id,item.id)
        },
        addAddress() {
            this.openRecipientsDialogById(this.tempId);
        },
        handleScroll() {
            let scrollTop =
                document.body.scrollTop || document.documentElement.scrollTop;
            /*if (scrollTop > 1200) {
                this.headIshow = true
            } else {
                this.headIshow = false
            }*/
        },

        //全选
        handleCheckAllChange(val) {
            this.checkedAll(val);
            //this.checkedCities = val ? this.checkedCities : [];
            //this.isIndeterminate = false;
        },

        //单选
        handleCheckedCitiesChange(item) {
            let isChecked = item.checked == 1 ? 0 : 1;
            console.log(item.checked);
            
            this.checkedById(item, isChecked);
        },

        //更新全选数据
        handleCheckedCities() {
            let checkedCount = this.checkedCities.length;
            // 排除已经失效的商品
            const validItems = this.shopping_carts.filter(
                (item) => item.is_expired === 0,
            );
            // this.checkAll = checkedCount === this.shopping_carts.length;
            // this.isIndeterminate = checkedCount > 0 && checkedCount < this.shopping_carts.length;
            this.checkAll = checkedCount === validItems.length;
            this.isIndeterminate =
                checkedCount > 0 && checkedCount < validItems.length;
        },
        // 重新选择
        resetChoose(item) {
            this.handleCheckedCitiesChange(item);
        },

        // 打开新增收货人
        openRecipientsDialog() {
            this.$refs.recipientsDialog.dialogIsShow = true;
            this.$refs.recipientsDialog.isEditAddress = false;
            this.$refs.recipientsDialog.title = '添加收货地址';
            this.$refs.recipientsDialog.initFormData();
        },
        // 打开选择收货地址
        openSelectAddress() {
            this.$refs.selectAddress.isShow = true;
            if (this.address) {
                this.$refs.selectAddress.initAddress(this.address.id);
            } else {
                this.$refs.selectAddress.initAddress();
            }
        },
        // 打开确认框
        openQrBox(item) {
            let that = this;
            this.$refs.qrBox.isShow = true;
            that.addressGlobalTemp = item;
        },

        //新增地址回调
        callBackNewAddress(item) {
            this.openQrBox(item);
        },

        //确认回调
        qrBoxHandleClose(isSubmit) {
            let that = this;
            if (isSubmit) {
                console.log(that.addressGlobalTemp,'?????');
                that.address = that.addressGlobalTemp;
                that.addressStr = that.assemblyAddress(that.address);
                that.addressGlobal();
            }
        },

        //更新购物view
        setCartView(data) {
            let that = this;
            that.shopping_carts = data.shopping_carts;
            that.shipping_methods = data.shipping_methods;
            that.amount = data.amount;
            that.goods_count = data.goods_count;

            //初始化选中
            that.checkedCities = [];
            that.shopping_carts.forEach((item) => {
                item.supplier = item.supplier ? item.supplier : {}
                if (item.checked === 1) {
                    that.checkedCities.push(item.id);
                }
            });
            that.handleCheckedCities();
        },

        //初始化购物车
        initCart() {
            let that = this;
            let para = {
                order_id:this.order_id
            };
            that.$post('/shoppingcart/listByReorder', para)
                .then(function(res) {
                    if (res.code == 0) {
                        that.setCartView(res.data);
                    }
                })
                .catch(function(res) {
                    console.log(res);
                });
        },

        //增加item
        orderQtyChange(item) {
            let that = this;
            let para = {
                id: item.id,
                qty: item.qty > item.stock ? item.stock : item.qty,
                order_id:this.order_id
            };
            that.$post('/shoppingcart/updateByReorder', para)
                .then(function(res) {
                    if (res.code == 0) {
                        that.setCartView(res.data);
                    }
                })
                .catch(function(res) {
                    console.log(res);
                });
        },

        //删除item
        deleteOrderDialog(item) {
            let that = this;
            this.$confirm('确定要删除吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    that.deleteOrderById(item);
                })
                .catch(() => {});
        },
        deleteOrderById(item) {
            let that = this;
            let para = {
                id: item.id,
                order_id:this.order_id
            };
            that.$post('/shoppingcart/deleteByReorder', para)
                .then(function(res) {
                    if (res.code == 0) {
                        that.setCartView(res.data);
                        that.$message({
                            message: res.msg,
                            type: 'success',
                        });
                    }
                })
                .catch(function(res) {
                    console.log(res);
                });
        },

        //批量删除
        deleteBatchDialog() {
            let that = this;
            if (that.checkedCities == 0) {
                that.$message.error('请选择要删除的商品');
                return;
            }

            this.$confirm('确定要删除吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    that.deleteBatchByIds();
                })
                .catch(() => {});
        },
        deleteBatchByIds() {
            let that = this;
            let para = {
                ids: that.checkedCities,
                order_id:this.order_id
            };
            that.$post('/shoppingcart/deleteByReorder/batch', para)
                .then(function(res) {
                    if (res.code == 0) {
                        that.setCartView(res.data);
                    }
                })
                .catch(function(res) {
                    console.log(res);
                });
        },

        //物流类型切换
        shippingMethodsChange(item) {
            let that = this;
            let para = {
                id: item.id,
                shipping_method_id: item.shipping_method_id,
                order_id:this.order_id
            };
            that.$post('/shoppingcart/updateByReorder', para)
                .then(function(res) {
                    if (res.code == 0) {
                        that.setCartView(res.data);
                    }
                })
                .catch(function(res) {
                    console.log(res);
                });
        },

        //物流类型全局切换
        shippingMethodsGlobalChange() {
            let that = this;
            let para = {
                shipping_method_id: that.shippingMethodsGlobalTag,
                order_id:this.order_id
            };
            that.$post('/shoppingcart/updateByReorder/batch', para)
                .then(function(res) {
                    if (res.code == 0) {
                        that.setCartView(res.data);
                    }
                })
                .catch(function(res) {
                    console.log(res);
                });
        },

        //选中处理
        checkedById(item, isChecked) {
            let that = this;
            let para = {
                id: item.id,
                checked: isChecked,
                order_id:this.order_id
            };
            that.$post('/shoppingcart/updateByReorder', para)
                .then(function(res) {
                    if (res.code == 0) {
                        that.setCartView(res.data);
                    }
                })
                .catch(function(res) {
                    console.log(res);
                });
        },

        //全选处理
        checkedAll(isChecked) {
            let that = this;
            let para = {
                checked: isChecked ? 1 : 0,
                order_id:this.order_id
            };
            that.$post('shoppingcart/updateByReorder/batch', para)
                .then(function(res) {
                    if (res.code == 0) {
                        that.setCartView(res.data);
                    }
                })
                .catch(function(res) {
                    console.log(res);
                });
        },

        //全局处理地址
        addressGlobal() {
            let that = this;
            let para = {
                address_id: that.address.id,
                order_id:this.order_id
            };

            that.$post('shoppingcart/updateByReorder/batch', para)
                .then(function(res) {
                    if (res.code == 0) {
                        that.setCartView(res.data);
                    }
                })
                .catch(function(res) {
                    console.log(res);
                });
        },

        //组装地址
        assemblyAddress(address) {
            let addressStr = '';
            if (!address) {
                return '请选择收货地址';
            }
            addressStr =
                address.realname +
                '  ' +
                address.mobile +
                '  ' +
                address.province +
                '  ' +
                address.city +
                '  ' +
                address.county +
                '  ' +
                address.town +
                '  ' +
                address.detail;
            return addressStr;
        },

        // 打开新增收货人
        openRecipientsDialogById(id) {
            this.tempId = id;
            this.$refs.recipientsDialogById.dialogIsShow = true;
            this.$refs.recipientsDialogById.isEditAddress = false;
            this.$refs.recipientsDialogById.title = '添加收货地址';
            this.$refs.recipientsDialogById.initFormData();
        },
        // 打开选择收货地址
        openSelectAddressById(item) {
            this.tempId = item.id;
            this.$refs.selectAddressById.isShow = true;
            if (item.address) {
                this.$refs.selectAddressById.initAddress(item.address.id);
            } else {
                this.$refs.selectAddressById.initAddress();
            }
        },

        //新增地址ById
        callBackNewAddressById(item) {
            this.openQrBoxById(item);
        },

        // 打开确认框
        openQrBoxById(item) {
            let that = this;
            that.orderAddressChange(item);
        },

        //修改地址
        orderAddressChange(item) {
            let that = this;
            if (that.tempId < 0) {
                return;
            }
            let para = {
                id: that.tempId,
                address_id: item.id,
                order_id:this.order_id
            };
            that.$post('/shoppingcart/updateByReorder', para)
                .then(function(res) {
                    if (res.code == 0) {
                        //复位
                        that.tempId = -1;
                        that.setCartView(res.data);
                    }
                })
                .catch(function(res) {
                    console.log(res);
                });
        },

        // 结算
        cartSubmit() {
            let that = this;
            if (that.checkedCities.length == 0) {
                that.$message.error('请选择结算商品');
                return;
            }
            that.fullscreenLoading = true

            let para = {
                buy_id: 0,
                buy_way: 4
            };
            that.$post('/trade/checkout', para)
                .then(function(res) {
                    if (res.code == 0) {
                        /* that.$_blank("settlementAmount",{
                        "buy_id":0
                    }); */

                        that.$router.push({
                            path: 'settlementAmount',
                            query: {
                                buy_id: 0,
                                buy_way: 4,
                            },
                        });
                    } else if (res.code == 2) {
                        that.$confirm('当前等级暂无购买权限', '提示', {
                            confirmButtonText: '去升级',
                            cancelButtonText: '取消',
                        }).then(() => {
                            that.$router.push({
                                path: '/personalCenter/memberRights',
                            });
                        });
                    } else {
                        that.initCart();
                        that.$message.error(res.msg);
                    }
                    that.fullscreenLoading = false
                })
                .catch(function(res) {
                    that.fullscreenLoading = false
                    console.log(res);
                });
        },
    },
};
</script>

<style lang="scss" scoped>
.cart-box {
  margin-bottom: 100px;

  .distribution-box {
    padding: 15px 10px;
  }

  .cart-table-head {
    padding: 15px 30px;

    ::v-deep .el-checkbox {
      .el-checkbox__label {
        padding-left: 25px;
      }
    }
  }

  .cart-table-con {
    padding: 15px 30px;

    .order-goods-list-box {
      height: 0;
      .order-goods-expired {
        width: 36px;
        height: 24px;
        margin-right: 20px;
        line-height: 24px;
        text-align: center;
        border-radius: 10px;
        background-color: #EFEFEF;
      }
      .order-goods-img {
        border: 1px solid #c0c0c0;
        width: 118px !important;
        height: 118px !important;
        margin-right: 15px;
      }

      .order-goods-text {
        p {
          line-height: 25px;
        }
      }
    }

    ::v-deep .order-checkbox.el-checkbox {
      .el-checkbox__input {
        top: -12px;
      }

      .el-checkbox__label {
        padding-left: 25px;

        .el-avatar {
          margin-right: 10px;
        }
      }
    }
  }
}

.mtb {
  margin-bottom: 15px !important;
  margin-top: 15px !important;
}

::v-deep .cart-radio-group.el-radio-group {
  .el-radio {
    margin-right: 30px;

    &.is-checked {
      .el-radio__input.is-checked {
        .el-radio__inner {
          border-color: $theme-color;
          background: $theme-color;
          // √样式
          &::after {
            content: "";
            width: 7px;
            height: 3px;
            border: 1px solid white;
            border-top: transparent;
            border-right: transparent;
            text-align: center;
            display: block;
            position: absolute;
            top: 3px;
            left: 2px;
            vertical-align: middle;
            transform: rotate(-45deg);
            border-radius: 0px;
            background: none;
          }
        }
      }

      .el-radio__label {
        color: #606266;
      }
    }
  }
}

.del-text-ben {
  margin: 0;
  padding: 0;
  border: 0;
  color: #333333;
}

.table-jc {
  height: 120px;
  line-height: 120px;
}

//底部结算
.footer-account-box {
  width: 100%;
  position: fixed;
  z-index: 999;
  bottom: 0;
  height: 80px;
  box-shadow: 0px -3px 6px 0px rgba(0, 0, 0, 0.13);

  .left-box {
    font-size: 16px;
    margin-left: 31px;

    ::v-deep .el-checkbox {
      .el-checkbox__label {
        font-size: 16px;
      }
    }

    p.ppp {
      margin-left: 18px;
      margin-right: 18px;
      cursor: pointer;
    }

    p.red-p {
      color: #f42121;
    }
  }

  .right-box {
    span:nth-child(1) {
      font-size: 16px;
      color: #666666;
    }

    span:nth-child(2) {
      font-size: 24px;
      font-weight: bold;
      color: #f42121;
      margin-left: 13px;
      margin-right: 39px;
    }

    .el-button {
      width: 139px;
      height: 78px;
      background: #f42121;
      border-color: #f42121;
      color: white;
      font-size: 24px;
      border-radius: 0;
    }
  }
}
</style>
