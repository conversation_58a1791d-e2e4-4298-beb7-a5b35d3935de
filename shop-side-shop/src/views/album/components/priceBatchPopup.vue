<template>
  <div>
    <!-- 专辑导入(批量改价) start -->
    <div>
      <el-dialog
        width="32%" 
        :title="title"
        :visible="priceBatchShow"
        @open="onOpenPricePopup"
        @close="onClosePricePopup"
      >
        <template v-if="isTip === 1">
          <div class="con-pop">
            <div class="d-bf mt_20">
              <div class="mr_20 w-80">定价策略:</div>
              <el-radio-group v-model="currentType">
                <el-radio
                  v-for="(item, index) in typeList"
                  :key="index"
                  :label="item.name"
                  class="mr_20"
                  @change="onChangeTypes(item.name)"
                >
                  {{item.label}}
                </el-radio>
              </el-radio-group>            
            </div>
            <div class="d-bf mt_20">
              <div class="mr_20 w-80">定价比例:</div>
              <el-input
                v-model="price_proportion"
                size="small"
                clearable
              >
                <template slot="append">%</template>
              </el-input>         
            </div>
          </div>
        </template>
        <template v-else>
          <div>恭喜您，商品专辑导入小商店成功！</div>
        </template>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="onConfirmPricePopup">确 定</el-button>
        </span>
      </el-dialog>
    </div>
    <!-- 批量改价 end -->
  </div>
</template>
<script>
export default {
  name: 'PriceBatchPopup',
  props: {
    priceBatchShow: {
      type: Boolean,
      default: false,
    },
    checkboxValue: {
      type: Array,
      default: () => [],
    },
    isTip: {
      type: Number,
      default: 0,
    },
    ratioValues: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  computed: {
    title: function () {
      if (this.isTip === 1) {
        return '导入专辑提示窗'
      } else {
        return '加入成功提示'
      }
    },
  },
  data() {
    return {
      currentType: 2,
      typeList: [
        {
          name: 2,
          label: '协议价',
        },
        {
          name: 0,
          label: '建议零售价',
        },
        {
          name: 1,
          label: '指导价',
        },
        {
          name: 3,
          label: '营销价',
        },
      ],
      price_proportion: 100,

    }
  },
  methods: {
    onChangeTypes(item){
      switch (item) { 
        case 2:
          this.price_proportion = this.ratioValues.agreement_price_ratio
          break;
        case 0:
          this.price_proportion = this.ratioValues.origin_price_ratio
          break;
        case 1:
          this.price_proportion = this.ratioValues.guide_price_ratio
          break;
        case 3:
          this.price_proportion = this.ratioValues.activity_price_ratio
          break;     
        default:
          break;
      }
    },
    onOpenPricePopup() {
      this.$emit('onOpenPriceBatch')
      this.onSwitchTypes()
      this.onChangeTypes(this.currentType)
    },
    onSwitchTypes(){
      if (this.ratioValues.is_agreement_price === 1) {
        this.currentType = 2 
      }
      if (this.ratioValues.is_origin_price === 1) {
        this.currentType = 0
      }
      if (this.ratioValues.is_guide_price === 1) {
        this.currentType = 1
      }
      if (this.ratioValues.is_activity_price === 1) {
        this.currentType = 3
      }
    },
    onClosePricePopup(flg = '') {
      this.price = 0
      this.price_proportion = 100
      this.currentType = 2
      this.$emit('onClosePriceBatch', flg)
    },
    onConfirmPricePopup() {
      if (this.price_proportion < 100) {
        this.$message.error('定价比例不能小于100')
        return
      }
      const params = {
        product_ids: this.checkboxValue,
        price_proportion: this.price_proportion * 100,
        currentType: this.currentType,
      }
      this.price = 0
      this.price_proportion = 100
      this.currentType = 2
      this.$emit('onConfirmPriceBatch', params)
    },
  },
}
</script>
<style scoped>
/* 样式穿透失效 */
/* ::v-deep .el-input {
  margin-bottom: 10px;
  height: 20px;
  padding: 3px 9px!important;
} */

::v-deep .el-col {
  align-self: flex-start;
}
</style>
<style lang="scss" scoped>
.con-pop {
  padding: 15px 30px 30px 30px;
  // height: 440px;
}
.con-pop-2 {
  padding: 15px 30px 30px 30px;
  // min-height: 300px;
  // max-height: 350px;
}
.max-w-330 {
  max-width: 330px;
}
.w-80 {
  width: 80px;
}
.w-120 {
  width: 120px;
}
.h-37 {
  height: 37px;
}
.p-20 {
	padding: 20rpx;
}
.bg-white {
	background-color: #fff;
}
.b-r-10 {
	border-radius: 10rpx;
}
.d-f {
	display: flex;
}
.ml_20 {
  margin-left: 20px;
}
.mb_20 {
  margin-bottom: 20px;
}
.mb_10 {
  margin-bottom: 10px;
}
.pr_20 {
  padding-right: 20px;
}
.fs-2 {
	font-size: 28rpx;
}
.ell {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	/* 定义显示的行数*/
	overflow: hidden;
}
.d-bf {
	display: flex;
	align-items: center;
	// justify-content: space-between;
}
.mt_30 {
  margin-top: 30px;
}
.mt_20 {
  margin-top: 20px;
}
.mt_10 {
  margin-top: 10px;
}
.mt_5 {
  margin-top: 5px;
}
.c-orange {
	color: #f15353;
}
.c-fa {
	color: #ffaa29;
}
.fs-0-5 {
	font-size: 12px;
}
.fs-2 {
	font-size: 14px;
}
.mr_5 {
  margin-right: 5px;
}
.mr_10 {
  margin-right: 10px;
}
.mr_20 {
  margin-right: 20px;
}
.mr_3 {
  margin-right: 3px;
}
.ml_20 {
  margin-left: 20px;
}
.ml_30 {
  margin-left: 30px;
}
.mr_30 {
  margin-right: 30px;
}
.ml_5 {
  margin-left: 5px;
}
.ml_85 {
  margin-left: 85px;
}
.f {
	display: flex;
}
.fac {
	align-items: center;
}
.align-end {
	align-items: flex-end;
}
.font-12 {
  font-size: 12px;
}
.line-20 {
  line-height: 20px;
}
.c-8a {
	color: #8a8a8a;
}
.text-center {
  text-align: center;
}
::v-deep.el-radio__input.is-checked .el-radio__inner {
    border-color: $theme-color;
    background: $theme-color;
}
::v-deep.el-radio__input.is-checked+.el-radio__label {
    color: $theme-color;
}
::v-deep.el-button--primary {
    color: #FFF;
    background-color: $theme-color;
    border-color: $theme-color;
}








</style>
