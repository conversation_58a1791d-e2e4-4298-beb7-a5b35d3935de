
import SortButton from '../components/sortButton.vue'
import SortButtonGroup from '../components/sortButtonGroup.vue'
import {scrollTo} from "@/utils/scroll-to";
import scrollPaginationMixin from '@/mixin/scrollPaginationMixin.js';

export default {
  name: "albumList",
  mixins: [scrollPaginationMixin],
  components: { SortButton, SortButtonGroup },
  data() {
    return {
      url: 'https://tse3-mm.cn.bing.net/th/id/OIP-C.E2W7zRqaO9IzGY2QCyb7DwHaNL?w=121&h=214&c=7&r=0&o=5&pid=1.7',
      searchForm:{
        uid:'',// 会员id
        member:'',// 会员昵称,姓名,手机号
        tag_name:'', //标签名称
        album_name:'',//专辑名称
      },
      sortForm: {
        value: 'is_new', // 热卖is_hot, 最新is_new, 热门导入hot_import, 热门访问hot_browse
        sort: '1' // 1为升序，2为降序
      },
      albumData:[
        // {
        //   id: "",
        //   name: "", //专辑名称
        //   covers: [  // 封面图片 *限制一张
        //       {
        //           src: ""
        //       }
        //   ],
        //   relations: [ // 标签 *最多选五个  数据格式 {tag_id:}
        //       {
        //           id: "",
        //           product_album_id: "",
        //           tag_id: "",
        //           tag: {
        //               name: ""  //标签名称
        //           }
        //       }
        //   ],
        // relation_product:[
        //   {
        //       id: 2,
        //       product_album_id: 3,
        //       product_id: 5,
        //       product: {
        //           id: 5,
        //           title: "wqm供应航的单规格商品",
        //           origin_price: 2500,
        //           guide_price: 3500,
        //           price: 2000,
        //           cost_price: 1900,
        //           activity_price: 0,
        //           min_price: 2000,
        //           image_url: "https://supply-chain-yunzmall.oss-cn-beijing.aliyuncs.com/202262/1654132554logo.png",
        //       }
        //   }
        // ],
        //   is_share: 1,  // 是否共享  1:是/共享 2:否/私有
        //   share_name: "",
        //   import_count: 0, //导入数量
        //   browse_count: 0, //访问数量
        //   product_count: 1,//商品12？
        //   sales: 0, //销量
        //   time_name: "6月24日",  //发布时间
        //   user_info: {
        //       avatar: "https://cube.elemecdn.com/3/7c/********************************.png",//头像
        //       username: "18346054904",
        //       nickname: "昵称昵称",  //会员昵称
        //   }
        // }
      ],
      coverImgs: [],// 所有封面图片 *后台+自上传 格式:{url:"",come} come 1=后台 2=自上传
      checkedTags: [], // 选中的标签
    }
  },
  mounted() {
      this.init()
      this.getAllTag()
  },
  methods: {
    loadNextPage() {
      this.page++;
      this.init(this.page,true)
  },
    choosetag(data){
        let para = {
          page: this.page,
          pageSize: this.pageSize,
          [this.sortForm.value]: this.sortForm.sort,
          uid: this.searchForm.uid,//会员id
          member:this.searchForm.member,//会员昵称,姓名,手机号
          tag_name:data,//标签名称
          album_name:this.searchForm.album_name,// 专辑名称
        }
        this.$get("/productAlbumApi/getAlbumListByApi",para).then(res=> {
          if (res.code == 0) {
            this.albumData = res.data.list
            this.page = res.data.page
            this.pageSize = res.data.pageSize
            this.total = res.data.total
          }
        }).catch(function (res) {
          console.log(res);
        });

    },
    disposeProducts(products){
      if(products && products.length > 3){
        let arr = []
        products.forEach(item=>{
          if(item.product.id && arr.length < 3){
            arr.push({
              ...item
            })
          }
        })
        return arr
      }else{
        return products
      }
    },
    init(page = this.page, addList = false) {
      this.isLoading = true
      let para = {
        page,
        pageSize: 21,
        [this.sortForm.value]: this.sortForm.sort,
        uid: this.searchForm.uid,//会员id
        member:this.searchForm.member,//会员昵称,姓名,手机号
        tag_name:this.searchForm.tag_name,//标签名称
        album_name:this.searchForm.album_name,// 专辑名称
      }
      this.$get("/productAlbumApi/getAlbumListByApi",para).then(res=> {
        this.isLoading = false
        if (res.code == 0) {
          this.albumData = addList === true ? this.albumData.concat(res.data.list) : res.data.list || []

          this.total = res.data.total
          this.hasMore = res.data.total > this.albumData.length;

        }
      }).catch(function (res) {
          this.isLoading = false
          console.log(res);
      });
    },
    /* handleCurrentChange(page) {
      this.page = page
      scrollTo(0, 800);
      this.init()
    }, */
    /* handleSizeChange(size) {
      this.pageSize = size
      this.init()
    }, */
    // 获取后台所有封面
    async getAllCover() {
      // 假数据 缺获取接口
      /*let dummyData = [
          'https://supply-chain-yunzmall.oss-cn-beijing.aliyuncs.com/202269/1654754274flipped-aurora.png',
          'https://supply-chain-yunzmall.oss-cn-beijing.aliyuncs.com/202262/1654132554logo.png',
          'https://supply-chain-yunzmall.oss-cn-beijing.aliyuncs.com/2022531/1653960662github.png'
      ]
      this.coverImgs = dummyData.map(item => ({
          url: item,
          come: 1
      }))*/
    },
    //获取所有标签
    async getAllTag(){
      this.$get("/productAlbumApi/getAllTag").then(res=> {
        if (res.code == 0) {
          this.checkedTags = res.data.tags
          console.log('checkedTags',this.checkedTags)
        }
      }).catch(function (res) {
          console.log(res);
      });
    },
    formatSortRes(item){
      switch (this.sortForm.value) {
        case 'hot_import':
          return '导入'+ item.import_count
        case 'hot_browse':
          return '访问'+ item.browse_count
        default:
          return '已售'+ item.sales_total
      }
    }
  },
}
