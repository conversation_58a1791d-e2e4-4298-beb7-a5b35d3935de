import Dialog from '../components/dialog'

export default {
    name: "batchImportIndex",
    components: {Dialog},
    data() {
        return {
            // 配送方式  1=快递 2=物流到付 3=自提
            deliverType: 1,
            delNotarizeIsShow: false,
            deliverList: [
                {name: '快递', value: 1},
                {name: '物流到付', value: 2},
                {name: '自提', value: 3}
            ]
        }
    },
    methods: {
        openEdit() {
            this.$refs.Dialog.dialogVisible = true
        },
        delNotarizeClose() {
            this.delNotarizeIsShow = false
        },
        del() {
            this.delNotarizeIsShow = true
        }
    }
}
