<div class="goodsInfo-box">
    <!-- <div class="f fw goods-info-top" v-for="item in attrs">
        <p>{{item.name}}: {{item.value}}</p>
    </div> -->
    <template v-if="attrs && attrs.length>0">
        <el-row :gutter="20">
            <el-col :span="6" v-for="item in attrs" :key="item.id">
                <p class="goods-params-p">{{item.name}}: {{item.value}}</p>
            </el-col>
        </el-row>
        <el-divider class="mt20"></el-divider>
    </template>
    <div class="mt20 goods-imgs-box">
        <!-- <m-image :src="url" v-for="url in detailImages"></m-image> -->
        <!-- <div v-html="detailImages" class="cont-box quill-editor"></div> -->
        <div v-if="detailImages" class="cont-box ql-container ql-snow">
            <div class="ql-editor" v-html="detailImages">
            </div>
        </div>
        <m-empty v-else></m-empty>
    </div>
</div>