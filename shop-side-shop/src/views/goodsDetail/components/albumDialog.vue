<template>
  <div>
    <el-dialog
        :visible="visible"
        @open="onOpen"
        @close="onClose">
      <div class="ml10">
        <span class="tit">选品专辑</span>
        <el-button type="primary" @click="openProductAlbumDialog" class="btn01">添加新专辑</el-button>

      </div>
      <el-form :inline="true" class="demo-form-inline ml10 mt40">
        <el-form-item>
          <el-input placeholder="请输入专辑名称" v-model="formData.searchName" clearable class="w300 h42"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" class="ml10" @click="onOpen">搜索</el-button>
        </el-form-item>
      </el-form>
      <el-table
          :data="tableData"
          class="mt20"
          style="width: 100%">
        <el-table-column
            label="专辑封面"
            width="220"
            align="center">
          <template slot-scope="scope">
            <el-image
                v-if="scope.row.covers[0].src"
                style="width: 100px; height: 50px"
                :src="scope.row.covers[0].src">
            </el-image>
            <el-image v-else>
              <div slot="error" class="image-slot image-none">
                <p>暂无封面</p>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column
            prop="name"
            label="专辑名称"
            align="center"
        >
        </el-table-column>
        <el-table-column
            prop="address"
            label="操作"
            width="180"
            align="center">
          <template slot-scope="scope">
            <el-button
                :disabled="scope.row.is_check"
                @click="handleEdit(scope.$index, scope.row)">
              {{ scope.row.is_check ? "已加入" : "加入" }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          background
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100,200]"
          :style="{display: 'flex',justifyContent: 'flex-end',marginRight: '20px'}"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          layout="total, sizes,prev, pager, next, jumper"
          class="mt30">
      </el-pagination>
    </el-dialog>
    <product-album ref="productAlbum" :productID="product_id" @success="reload"></product-album>
  </div>
</template>
<script>
import ProductAlbum from "@/components/productAlbum";

export default {
  name: "AlbumDialog",
  components: {ProductAlbum},
  // 弹窗显示/隐藏
  props: {
    visible: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      innerVisiable: false,
      isSwitch: false,
      path: this.$path,
      product_id: parseInt(this.$route.query.goods_id),
      formData: {
        searchName: '',
      },
      tableData: [],
      albumForm: {
        name: "前端添加测试",
        describe: "我是描述",
        covers: [
          {src: "https://jhgyl-1309576924.cos.ap-guangzhou.myqcloud.com/202269/1654745839高夫洗面奶.png"}
        ],
        relations: [
          {
            tag_id: 2
          }
        ],
        is_share: 1
      },
      rules: {
        name: [
          {required: true, message: '请输入专辑名称', trigger: 'blur'},
          {min: 3, max: 5, message: '长度在 3 到 5 个字符', trigger: 'blur'}
        ],
      },
      page: 1,
      pageSize: 10,
      total: 0,
    };
  },
  methods: {
    onOpen() {
      let para = {
        page: this.page,
        pageSize: this.pageSize,
        album_name: this.formData.searchName
      }
      this.$nextTick(() => {
        this.$get("/productAlbumApi/getAlbumListByUid", para).then(res => {
          if (res.code == 0) {
            this.tableData = []
            res.data.list.forEach(item => {
              let isCheck = false
              isCheck = item.relation_product.find(item2 => item2.product_id === this.product_id)
              this.tableData.push({
                ...item,
                is_check: isCheck ? true : false
              })
              console.log('onOpen this.tableData',this.tableData)
            })
            this.total = res.data.total;
          }
        }).catch(function (res) {
          console.log(res);
        });
      })
    },
    handleCurrentChange(page) {
      this.page = page;
      this.onOpen();
    },
    handleSizeChange(size) {
      this.pageSize = size;
      this.onOpen();
    },
    onCreate() {
      let para = {
        name: this.albumForm.name,
        describe: this.albumForm.describe,
        covers: this.albumForm.covers,
        relations: this.albumForm.relations,
      }
      if (this.isSwitch == true) {
        para.is_share = 1
      }
      this.$post("/productAlbumApi/createAlbum", para).then(res => {
        console.log('已提交', res)
        this.innerVisiable = false
        this.$emit('update:visible', false)
        if (res.code == 0) {
          this.$message({
            type: "success",
            message: "添加成功",
          })
        } else {
          this.$message({
            type: "error",
            message: "添加失败",
          });
        }
      }).catch(function (res) {
        console.log(res);
      });
    },
    onClose() {
      this.$emit('update:visible', false)
      this.formData.searchName = ''
    },
    onCreateClose() {
      this.innerVisiable = false
    },
    onCreateClosed() {
      this.$refs.ruleForm.resetFields()
      this.albumForm.name = ''
      this.albumForm.describe = ''
      this.albumForm.covers[0].src = ''
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePictureCardPreview(res) {
      this.albumForm.covers[0].src = "res.data.file.url";
    },
    beforeAvatarUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error('上传头像图片大小不能超过 10MB!');
      }
      return isLt10M;
    },
    //加入
    handleEdit(index, row) {
      let para = {
        product_album_id: row.id,
        product_id: parseInt(this.$route.query.goods_id),
      }
      this.$post("/productAlbumApi/addProductToAlbum", para).then(res => {
        if (res.code == 0) {
          this.$message.success('添加商品成功');
          row.is_check =true
        } else if (res.code == 7) {
          this.$message.error('商品已存在该专辑');
        }
      }).catch(function (res) {
        console.log(res);
      });
    },
    openProductAlbumDialog() {
      this.$refs.productAlbum.init()
    },
    reload() {
      this.onOpen()
      console.log("新增成功! success回调")
    }
  },
}
</script>
<style lang="scss" scoped>
.ml10 {
  margin-left: 10px;
}

.mt20 {
  margin-top: 20px;
}

.mt30 {
  margin-top: 30px;
}

.mt40 {
  margin-top: 40px;
}

.w300 {
  width: 300px;
}

.h42 {
  height: 42px;
}

.tit {
  font-size: 16px;
  font-weight: bold;
}

.btn01 {
  margin: 0 0 0 30px;
}

::v-deep .el-dialog__header {
  border-bottom: none !important;
}

::v-deep .el-dialog__body {
  padding: 10px 20px 30px 20px;
}

.el-button--primary {
  color: #FFF;
  background-color: $theme-color;
  border-color: $theme-color;
}

.el-button.is-disabled {
  &:hover {
    color: #C0C4CC;
    cursor: not-allowed;
    background-image: none;
    background-color: #FFF;
    border-color: #EBEEF5;
  }
}

::v-deep .el-dialog {
  width: 790px
}

::v-deep .el-dialog__title {
  font-size: 16px;
  font-weight: bold;
  color: #333333
}

.upload-image {
  width: 148px;
  height: 148px;
}

::v-deep .el-pagination.is-background .el-pager li {
  &:hover {
    color: $theme-color
  }

  &:not(.disabled).active {
    background-color: $theme-color;
  }
}

::v-deep .el-select-dropdown__item.selected.hover {
  color: $theme-color
}

::v-deep .el-select .el-input.is-focus .el-input__inner {
  border-color: $theme-color;
}

::v-deep .el-pagination__sizes .el-input .el-input__inner {
  &:hover, &:focus {
    border-color: $theme-color;
  }
}

::v-deep .el-pagination__jump .el-input__inner {
  &:hover, &:focus {
    border-color: $theme-color;
  }
}

::v-deep .el-button {
  &:hover, &:focus {
    color: $theme-color;
    border-color: $theme-color;
    background-color: #fff;
  }
}

::v-deep .el-input__inner {
  &:hover, &:focus {
    border-color: $theme-color;
  }
}

::v-deep .el-dialog__headerbtn .el-dialog__close:hover {
  color: $theme-color;
}

::v-deep.image-none {
  padding: 10px 20px;
  color: #C0C4CC;
  border: 1px solid #e4e5e6;
}
</style>