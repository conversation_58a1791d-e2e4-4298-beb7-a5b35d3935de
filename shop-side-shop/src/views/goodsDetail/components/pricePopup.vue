<template>
  <div>
    <!-- 单规格改价 start -->
    <div>
      <el-dialog
        width="32%" 
        :title="title"        
        :visible="pricePopupShow"
        @close="onClosePricePopup('pricePopupShow')"
        @open="onOpenPricePopup"
      >
        <div class="con-pop">
          <template v-if="isTip === 1">
            <div class="p-20 bg-white b-r-10 d-f">
              <el-image
                style="width: 140px; height: 140px"
                :src="product.image_url"
              >
                <el-icon
                  slot="error"
                  size="40"
                  color="#d0d0d1"
                  name="photo"
                ></el-icon>
              </el-image>
              <div class="ml_20 max-w-330">
                <!-- 标题 start-->
                <div class="d-f h-37">
                  <div class="pr_20">
                    <p class="fs-2 ell">
                      {{ product.title }}
                    </p>
                  </div>
                </div>
                <!-- 标题 end-->
                <div class="d-bf mt_10">
                  <div class="c-orange">
                    <span class="fs-0-5 mr_5">协议价</span>
                    <span class="fs-2">
                      {{ $fn.changeMoneyF2Y(product.max_price) }}元
                    </span>
                  </div>
                </div>
                <div class="d-bf mt_5">
                  <div class="c-orange">
                    <span class="fs-0-5 mr_5">建议零售价</span>
                    <span class="fs-2">{{ $fn.changeMoneyF2Y(product.max_origin_price) }}元</span>
                  </div>
                </div>
                <div class="d-bf mt_5">
                  <div class="c-orange">
                    <span class="fs-0-5 mr_5">指导价</span>
                    <span class="fs-2">{{ $fn.changeMoneyF2Y(product.max_guide_price) }}元</span>
                  </div>
                </div>
                <div class="d-bf mt_5">
                  <div class="c-orange">
                    <span class="fs-0-5 mr_5">营销价</span>
                    <span class="fs-2">{{ $fn.changeMoneyF2Y(product.max_activity_price) }}元</span>
                  </div>
                </div>
                <!-- 协议价 max_price 建议零售价 max_origin_price 指导价 max_guide_price  营销价 market_price -->
                <!-- <div class="d-bf mt_10">
                  <div class="c-orange">
                    <p class="fs-0-5">利润</p>
                    <p class="fs-2">{{ $fn.changeMoneyF2Y(product.profit) }}元</p>
                  </div>
                </div> -->
              </div>
            </div>
            <div class="d-bf mt_20">
              <div class="mr_20 w-80">定价策略:</div>
              <el-radio-group v-model="currentType">
                <el-radio
                  v-for="(item, index) in typeList"
                  :key="index"
                  :label="item.name"
                  class="mr_20"
                  @change="onChangeTypes(item.name)"
                >
                  {{item.label}}
                </el-radio>
              </el-radio-group>
            </div>
            <div class="d-bf mt_20">
              <div class="mr_20 w-80">定价比例:</div>
              <el-input
                v-model="price_proportion"
                @blur="computedSellingPrice"
                size="small"
                clearable
              >
                <template slot="append">%</template>
              </el-input>         
            </div>
            <!-- 批量改价 start -->
            <div class="ml_85 mt_20">
              <div class="f align-end">
                <div style="width: 150px">
                  <el-input
                    v-model="price"
                    size="small"
                    clearable
                  >
                    <template slot="append">元</template>
                  </el-input>
                </div>
                <p class="c-fa font-12 ml_20 line-20" @click="calculateRatio">按此金额填充比例</p>
              </div>
              <div class="mt_10 fs-1 c-8a font-12">
                注意协议价不含技术服务费，当前技术服务费比例
                {{ $fn.changeMoneyF2Y(product.technical_services_fee) }}
                元！
              </div>
            </div>
            <div class="f fac mt_20">
              <div class="mr_20">预估销售价:</div>
              {{ estimate_price }}
              <div class="ml_10">元</div>
            </div>
            <div class="mt_10 fs-1 c-8a font-12">
              此金额为按照当前中台售价的预估销售价，销售价会按照定价策略同步中台商品价格变化
            </div>
            <div class="f fac mt_10 c-orange">
              <div class="mr_20">利润:</div>
              <p>{{ $fn.changeMoneyF2Y(profit) }}元</p>
            </div>
          </template>
          <template v-else>
            <div>恭喜您，该商品加入小商店成功！</div>
          </template>
          <div class="text-center mt_30">
            <el-button class="w-120" type="primary" @click="onConfirmPricePopup">确定</el-button>
          </div>
        </div>
      </el-dialog>
    </div>
    <!-- 单规格改价 end -->
  </div>
</template>
<script>
export default {
  name: 'PricePopup',
  props: {
    headTitle: {
      type: String,
      default: () => '',
    },
    pricePopupShow: {
      type: Boolean,
      default: false,
    },
    editPopupShow: {
      type: Boolean,
      default: false,
    },
    product: {
      type: Object,
      default: () => {},
    },
    currentAlbumIndex: {
      type: Number,
      default: 0,
    },
    albumsData: {
      type: Array,
      default: () => [],
    },
    isTip: {
      type: Number,
      default: false,
    },
    ratioValues: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  computed: {
    estimate_price() {
      let price = this.$fn.changeMoneyY2F(this.price)
      if (!price) {
        switch (this.currentType) {
          case 0:
            price = this.product.max_guide_price
            break
          case 1:
            price = this.product.max_origin_price
            break
          case 2:
            price = this.product.max_price
            break
          case 3:
            price = this.product.max_activity_price
            break
        }
      }
      return this.$fn.changeMoneyF2Y(price)
    },
    profit() {
      return this.$fn.changeMoneyY2F(this.estimate_price) - this.product.max_price
    },
    title: function () {
      if (this.isTip === 1) {
        return '加入小商店'
      } else {
        return '加入成功提示'
      } 
    },
  },
  data() {
    return {
      currentType: 2,
      typeList: [
        {
          name: 2,
          label: '协议价',
        },
        {
          name: 0,
          label: '建议零售价',
        },
        {
          name: 1,
          label: '指导价',
        },
        {
          name: 3,
          label: '营销价',
        },
      ],
      price_proportion: 100,
      price: 0,
      settingId: null,
      takeGroup: 'is_max_price',
      takeValues: [
        {
          name: '协议价',
          value: 'is_max_price',
        },
        {
          name: '建议零售价',
          value: 'is_max_origin_price',
        },
        {
          name: '指导价',
          value: 'is_max_guide_price',
        },
        {
          name: '营销价',
          value: 'is_max_activity_price',
        },
      ],
      formData: {
        is_max_price: 0, // 协议价是否勾选
        is_max_origin_price: 0, // 建议零售价是否勾选
        is_max_guide_price: 0, // 指导价是否勾选
        is_max_activity_price: 0, // 营销价是否勾选
        max_price_ratio: 0, // 协议价比例
        max_origin_price_ratio: 0, // 建议零售价比例
        max_guide_price_ratio: 0, // 指导价比例
        max_activity_price_ratio: 0, // 营销价比例
        tip: 0, // 提示是否开启
        temp: 'is_max_price',
      },
    }
  },
  methods: {
    onChangeTypes(item){
      switch (item) { 
        case 2:
          this.price_proportion = this.ratioValues.agreement_price_ratio
          break;
        case 0:
          this.price_proportion = this.ratioValues.origin_price_ratio
          break;
        case 1:
          this.price_proportion = this.ratioValues.guide_price_ratio
          break;
        case 3:
          this.price_proportion = this.ratioValues.activity_price_ratio
          break;     
        default:
          break;
      }
    },
    // 计算预估售价
    computedSellingPrice() {
      if (this.price_proportion < 100) {
        this.$message.error('定价比例不能小于100')
        return
      }
      switch (this.currentType) {
        case 0:
          this.price = this.$fn.changeMoneyF2Y(
            (this.product.max_origin_price * this.price_proportion) / 100,
          )
          break
        case 1:
          this.price = this.$fn.changeMoneyF2Y(
            (this.product.max_guide_price * this.price_proportion) / 100,
          )
          break
        case 2:
          this.price = this.$fn.changeMoneyF2Y(
            (this.product.max_price * this.price_proportion) / 100,
          )
          break
        case 3:
          this.price = this.$fn.changeMoneyF2Y(
            (this.product.max_activity_price * this.price_proportion) / 100,
          )
          break
      }
    },
    // 计算比例
    calculateRatio() {
      if (!this.price) {
        this.$message.error('请填写金额')
        return
      }
      let n1, n2
      /**
       * 协议价 max_price 建议零售价 max_origin_price 指导价 max_guide_price  营销价 market_price
       * {
          name: 2,
          label: '协议价',
        },
        {
          name: 1,
          label: '建议零售价',
        },
        {
          name: 0,
          label: '指导价',
        },
        {
          name: 3,
          label: '营销价',
        },
       */
      switch (this.currentType) {
        case 1:
          if (this.$fn.changeMoneyY2F(this.price) < this.product.max_guide_price) {
            this.$message.error(
              '填写金额不能小于' + this.$fn.changeMoneyF2Y(this.product.max_guide_price),
            )
            return
          }
          n1 = this.accSub(this.$fn.changeMoneyY2F(this.price), this.product.max_guide_price)
          n2 = this.accDiv(n1, this.product.max_guide_price)
          break
        case 0:
          if (this.$fn.changeMoneyY2F(this.price) < this.product.max_origin_price) {
            this.$message.error(
              '填写金额不能小于' + this.$fn.changeMoneyF2Y(this.product.max_origin_price),
            )
            return
          }
          n1 = this.accSub(this.$fn.changeMoneyY2F(this.price), this.product.max_origin_price)
          n2 = this.accDiv(n1, this.product.max_origin_price)
          break
        case 2:
          if (this.$fn.changeMoneyY2F(this.price) < this.product.max_price) {
            this.$message.error(
              '填写金额不能小于' + this.$fn.changeMoneyF2Y(this.product.max_price),
            )
            return
          }
          n1 = this.accSub(this.$fn.changeMoneyY2F(this.price), this.product.max_price)
          n2 = this.accDiv(n1, this.product.max_price)
          break
        case 3:
          if (this.$fn.changeMoneyY2F(this.price) < this.product.max_activity_price) {
            this.$message.error(
              '填写金额不能小于' + this.$fn.changeMoneyF2Y(this.product.max_activity_price),
            )
            return
          }
          n1 = this.accSub(this.$fn.changeMoneyY2F(this.price), this.product.max_activity_price)
          n2 = this.accDiv(n1, this.product.max_activity_price)
          break
      }
      this.price_proportion = Number((n2 + 1) * 100).toFixed(2)
    },
    onOpenPricePopup() {
      this.$emit('onOpenPricePopup')
      this.price = this.$fn.changeMoneyF2Y(this.product.sale_price)
      this.onSwitchTypes()
      this.onChangeTypes(this.currentType)
    },
    onSwitchTypes(){
      if (this.ratioValues.is_agreement_price === 1) {
        this.currentType = 2 
      }
      if (this.ratioValues.is_origin_price === 1) {
        this.currentType = 0
      }
      if (this.ratioValues.is_guide_price === 1) {
        this.currentType = 1
      }
      if (this.ratioValues.is_activity_price === 1) {
        this.currentType = 3
      }
    },
    onOpenEditPopup() {
      this.$emit('onOpenEditPopup')
    },
    onConfirmPricePopup() {
      if (this.price_proportion < 100) {
        this.$message.error('定价比例不能小于100')
        return
      }
      const params = {
        product_ids: [this.product.id],
        price_proportion: this.price_proportion * 100,
        currentType: this.currentType,
      }
      this.price = 0
      this.price_proportion = 100
      this.currentType = 2
      this.$emit('onConfirmPricePopup', params)
    },
    onConfirmEditPopup() {
      const params = {
        product_ids: [this.product.id],
      }
      switch (this.takeGroup) {
        case 'is_max_price':
          params.price_proportion = this.formData.max_price_ratio * 100
          params.currentType = 2
          break
        case 'is_max_origin_price':
          params.price_proportion = this.formData.max_origin_price_ratio * 100
          params.currentType = 0
          break
        case 'is_max_guide_price':
          params.price_proportion = this.formData.max_guide_price_ratio * 100
          params.currentType = 1
          break
        case 'is_max_activity_price':
          params.price_proportion = this.formData.max_activity_price_ratio * 100
          params.currentType = 3
          break
        default:
          break
      }
      this.$emit('onConfirmPricePopup', params)
      this.takeGroup = 'is_max_price'
      this.formData = {
        is_max_price: 0, // 协议价是否勾选
        is_max_origin_price: 0, // 建议零售价是否勾选
        is_max_guide_price: 0, // 指导价是否勾选
        is_max_activity_price: 0, // 营销价是否勾选
        max_price_ratio: 0, // 协议价比例
        max_origin_price_ratio: 0, // 建议零售价比例
        max_guide_price_ratio: 0, // 指导价比例
        max_activity_price_ratio: 0, // 营销价比例
        tip: 0, // 提示是否开启
        temp: 'is_max_price',
      }
    },
    onClosePricePopup(flg = '') {
      this.price = 0
      this.price_proportion = 100
      this.currentType = 2

      this.formData = {
        is_max_price: 0, // 协议价是否勾选
        is_max_origin_price: 0, // 建议零售价是否勾选
        is_max_guide_price: 0, // 指导价是否勾选
        is_max_activity_price: 0, // 营销价是否勾选
        max_price_ratio: 0, // 协议价比例
        max_origin_price_ratio: 0, // 建议零售价比例
        max_guide_price_ratio: 0, // 指导价比例
        max_activity_price_ratio: 0, // 营销价比例
        tip: 0, // 提示是否开启
        temp: 'is_max_price',
      }
      this.$emit('onClosePricePopup', flg)
    },
    onCloseEditPopup() {
      this.$emit('onCloseEditPopup')
    },
  },
}
</script>
<style scoped>
/* 样式穿透失效 */
/* ::v-deep .el-input {
  margin-bottom: 10px;
  height: 20px;
  padding: 3px 9px!important;
} */
::v-deep .el-col {
  align-self: flex-start;
}
</style>
<style lang="scss" scoped>
.con-pop {
  padding: 15px 30px 0 30px;
  // height: 440px;
}
.con-pop-2 {
  padding: 15px 30px 30px 30px;
  // min-height: 300px;
  // max-height: 350px;
}
.max-w-330 {
  max-width: 330px;
}
.w-80 {
  width: 80px;
}
.w-120 {
  width: 120px;
}
.h-37 {
  height: 37px;
}
.p-20 {
	padding: 20rpx;
}
.bg-white {
	background-color: #fff;
}
.b-r-10 {
	border-radius: 10rpx;
}
.d-f {
	display: flex;
}
.ml_20 {
  margin-left: 20px;
}
.mb_20 {
  margin-bottom: 20px;
}
.mb_10 {
  margin-bottom: 10px;
}
.pr_20 {
  padding-right: 20px;
}
.fs-2 {
	font-size: 28rpx;
}
.ell {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	/* 定义显示的行数*/
	overflow: hidden;
}
.d-bf {
	display: flex;
	align-items: center;
	// justify-content: space-between;
}
.mt_30 {
  margin-top: 30px;
}
.mt_20 {
  margin-top: 20px;
}
.mt_10 {
  margin-top: 10px;
}
.mt_5 {
  margin-top: 5px;
}
.c-orange {
	color: #f15353;
}
.c-fa {
	color: #ffaa29;
}
.fs-0-5 {
	font-size: 12px;
}
.fs-2 {
	font-size: 14px;
}
.mr_5 {
  margin-right: 5px;
}
.mr_10 {
  margin-right: 10px;
}
.mr_20 {
  margin-right: 20px;
}
.mr_3 {
  margin-right: 3px;
}
.ml_20 {
  margin-left: 20px;
}
.ml_30 {
  margin-left: 30px;
}
.mr_30 {
  margin-right: 30px;
}
.ml_5 {
  margin-left: 5px;
}
.ml_85 {
  margin-left: 85px;
}
.f {
	display: flex;
}
.fac {
	align-items: center;
}
.align-end {
	align-items: flex-end;
}
.font-12 {
  font-size: 12px;
}
.line-20 {
  line-height: 20px;
}
.c-8a {
	color: #8a8a8a;
}
.text-center {
  text-align: center;
}
::v-deep.el-radio__input.is-checked .el-radio__inner {
    border-color: $theme-color;
    background: $theme-color;
}
::v-deep.el-radio__input.is-checked+.el-radio__label {
    color: $theme-color;
}
::v-deep.el-button--primary {
    color: #FFF;
    background-color: $theme-color;
    border-color: $theme-color;
}








</style>
