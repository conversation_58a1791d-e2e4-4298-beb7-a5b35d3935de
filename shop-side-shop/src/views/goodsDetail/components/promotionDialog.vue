<template>
    <div>
        <el-dialog width="912px" title="推广链接" :visible="isShow" @close="onClose">
            <p>链接:【商品标颎】五谷磨房小天才黑芝麻核桃丸糖儿童独立包装营养零食 小天才芝麻丸1盒”84g</p>
            <p>【优惠价】￥48</p>
            <p>【下单链接】https:/u.jd.com/30zzZA1</p>
            <el-button type="text" class="red" @click="resetfun">复制链接</el-button>
            <div style="width: 260px;margin: 20px auto;">
                <el-image style="width: 260px; height: 400px;margin: 0 auto;" :src="image_url"></el-image>
            </div>
            <div style="width: 100px;margin: 0 auto;">
                <el-button class="but-true" @click="hb">下载海报</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    data() {
        return {
            isShow: false,
            id: null,
            image_url: '',
        };
    },
    methods: {
        info(id) {
            this.isShow = true;
            this.id = id;
        },
        // 复制链接
        resetfun() {},
        hb() {

        },
        onClose() {
            this.isShow = false;
            this.id = null;
        },
    },
};
</script>

<style lang="scss" scoped>
p {
    font-size: 14px;
    color: #666666;
    line-height: 28px;
}

.red {
    color: #f42121;
}


.but-true {
    background-color: #f42121;
    color: #fff;
    margin: 0 auto;
}
</style>