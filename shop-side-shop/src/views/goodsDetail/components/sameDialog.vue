<template>
    <div>
        <el-dialog width="912px" title="查同款" :visible="visible" @close="onClose">
            <el-form :model="formData" label-width="110px" ref="form" :rules="rules">
                <el-form-item label="支持API对接:">可将供应链商品同步到商城，自主定价销售，赚取差价!</el-form-item>
                <el-form-item label="支持CPS对接:">商城端配置后，会员可搜索商品，跳转到京东、淘宝、拼多多、唯品会等平台购买，商城运营方获得佣金奖励!</el-form-item>
                <el-form-item label="所属平台:">
                    <div class="f fac">
                        <template v-for="item in sameList">
                            <div class="same-box red" v-if='item.id === sameID' :key='item.id' @click='sameFun(item.id)'>{{ item.name }}</div>
                            <div class="same-box" v-else :key='item.id' @click='sameFun(item.id)'>{{ item.name }}</div>
                        </template>
                    </div>
                </el-form-item>
                <el-form-item label="关键词:">
                    <div class="f fac">
                        <el-input
                            class="w360"
                            type="textarea"
                            autosize
                            resize="none"
                            v-model="formData.title"
                        ></el-input>
                        <el-button class="but-true ml_10" @click="search">搜索</el-button>
                    </div>
                </el-form-item>
                <el-form-item label="排序:">
                    <div class="f fac check-all">
                        <template v-for="item in sort">
                            <div
                                v-if="item.id == 1 && item.id == sortid"
                                :key="item.id"
                                style="cursor: pointer; color: #f42121"
                            >{{ item.name }}</div>
                            <div
                                v-else-if="item.id == 1 && item.id != sortid"
                                :key="item.id"
                                @click="newest"
                                style="cursor: pointer"
                            >{{ item.name }}</div>
                        </template>
                        <SortButtonGroup v-model="sortForm" @change="onChangeSort">
                            <template v-for="item in sort">
                                <SortButton v-if="item.id != 1" :key="item.id" :value="item.value">
                                    <space-between
                                        v-if="item.id == sortid"
                                        style="color: #f42121"
                                    >{{ item.name }}</space-between>
                                    <span v-else @click="sortid = item.id">{{ item.name }}</span>
                                </SortButton>
                            </template>
                        </SortButtonGroup>
                    </div>
                </el-form-item>
            </el-form>
            <div class="commodity" v-loading="isLoading">
                <template v-for="item in shop">
                    <div class="commodity-card" :key="item.id">
                        <div class="commodity-card-true">
                            <div style="position: relative;width: 206px;height: 206px;">
                                <img class="commodityimg" :src="item.image_url" alt />
                                <div class="commodityimg-hover-button" @click='promotion(item.id)'>立即推广</div>
                            </div>
                            <div class="commodity-name">
                                <span class="tag">京东</span>
                                {{ 'alhfadlfkjhafdjlkhfajklhfjkaldfhaflkjdjhfjaklfdhjakldfjhajldfkhafdksl' }}
                            </div>
                            <div class="price">
                                <div class="price-box">
                                    <div class="price-num">¥{{ 5909.09 }}</div>
                                    <div class="price-name">现价</div>
                                </div>
                                <div class="price-box">
                                    <div
                                        class="price-num"
                                        style="text-decoration-line: line-through;color: #A5A5A5;"
                                    >¥{{ 5909.09 }}</div>
                                    <div class="price-name">原价</div>
                                </div>
                                <div class="price-box">
                                    <div class="price-num">{{ 2.05 }}%</div>
                                    <div class="price-name">佣金比例</div>
                                </div>
                            </div>
                            <div class="f fac fjsb sales">
                                <div>销量:550</div>
                                <div style="color:#F42121">预估收益:¥5.50</div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </el-dialog>
        <promotionDialog ref='promotionDialog'></promotionDialog>
    </div>
</template>

<script>
import SortButtonGroup from '@/components/sortButton/sortButtonGroup.vue';
import SortButton from '@/components/sortButton/sortButton.vue';
import promotionDialog from './promotionDialog.vue'

export default {
    components: { SortButtonGroup, SortButton, promotionDialog },
    data() {
        return {
            visible: false,
            rules: {},

            isLoading: false,
            shop: [{}, {}, {}, {}, {}],
            total: 0,
            page: 1,
            pageSize: 8,

            formData: {
                title: ''
            },
            
            // 查同款
            sameList: [
                {name: '京东',id: 'jd'},
                {name: '淘宝',id: 'taobao'},
                {name: '拼多多',id: 'pdd'},
                {name: '唯品会',id: 'vip'},
            ],
            sameID: '',
            sort: [
                { id: 1, name: '综合', value: 'created_at' },
                { id: 2, name: '价格', value: 'agreement_price' },
                { id: 3, name: '销量', value: 'guide_price' },
                { id: 4, name: '佣金比例', value: 'activity_price' },
            ],
            
            sortForm: {
                // 最新上架:'created_at', 协议价:agreement_price,
                // 指导价:guide_price, 营销价:activity_price,
                // 利润律:market_rate, 毛利率:gross_profit_rate
                value: '',
                sort: '1', // 1为升序，2为降序
            },
            sortid: 0,
        };
    },
    methods: {
        info(value,title) {
            this.visible = true;
            this.sameID = value;
            this.formData.title = title;
            this.getList();
        },
        search() {},
        async getList() {
            let api = '';
            switch (this.sameID) {
                case 'jd':
                    api = '/cps/getApplicationApply'
                    break;
                case 'taobao':
                    api = '/cps/taobao/superSearchMaterial?'
                    break;
                case 'pdd':
                    
                    break;
                case 'vip':
                    
                    break;
            }
            // let data = {
            //     page: this.page,
            //     pageSize: this.pageSize
            // }
            api = 'page=' + this.page + '&pageSi'
            let res = await this.$post(api,data);
            if (res.code === 0) {
                this.list = res.data.list;
                this.total = res.data.total;
            }
        },
        // 切换所属平台
        sameFun(value) {
            this.sameID = value
        },
        // 排序最新
        newest() {
            (this.sortForm = {
                value: 'created_at',
                sort: '2',
            }),
                (this.sortid = 1);
            // 从新调调用商品数据
            // this.getGoodsList(1, 0);
        },
        // 筛选排序
        onChangeSort(res) {
            let val = this.sort.find((item) => item.value == res.value);
            this.sortid = val.id;
            // 从新调调用商品数据
            // this.getGoodsList(1, 0);
        },
        // 立即推广
        promotion(id) {
            this.visible = false;
            this.$emit('promotion',id);
        },
        onOpen() {},
        onClose() {
            this.visible = false;
        },
    },
};
</script>

<style lang="scss" scoped>
.same-box {
    cursor: pointer;
    margin-right: 25px;
}

.red {
    color: #F42121;
}

.but-true {
    background-color: #f42121;
    color: #fff;
}

.w360 {
    width: 360px;
}

.mr_20 {
    margin-right: 25px;
}

.check-all {
    display: flex;

    div {
        margin-right: 25px;
        cursor: pointer;
    }
}

.commodity {
    width: 880px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(1, 1fr);

    .commodity-card {
        width: 206px;
        height: 324px;
        background-color: #fff;
        margin-bottom: 15px;
        overflow: hidden;
        cursor: pointer;
        border: 1px solid #e9e9e9;

        .commodity-card-true {
            .commodityimg {
                width: 206px;
                height: 206px;
            }

            .commodityimg-hover-button {
                display: none;
                width: 206px;
                height: 30px;
                position: absolute;
                bottom: 0px;
                background-color: #f42121;
                color: #FFFFFF;
                font-size: 14px;
                line-height: 30px;
                text-align: center;
            }

            .commodity-name {
                height: 32px;
                margin: 6px;
                font-size: 12px;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 2;

                .tag {
                    padding: 0 5px;
                    background-color: rgba($color: #f42121, $alpha: 0.1);
                    color: #f42121;
                }
            }

            .price {
                display: flex;
                margin-top: 15px;

                .price-box {
                    width: 69px;
                    height: 26px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-direction: column;

                    .price-name {
                        font-size: 10px;
                        color: #333333;
                    }

                    .price-num {
                        font-size: 12px;
                        color: #f42121;
                    }
                }
            }

            .sales {
                font-size: 10px;
                width: 190px;
                margin: 13px auto 0;
            }
        }
    }

    .commodity-card:hover .commodity-card-true .commodityimg-hover-button {
        display: block;
    }
}
</style>