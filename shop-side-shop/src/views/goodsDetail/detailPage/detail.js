// import GoodsItem from "@/components/goodsItem/index"
import GoodsInfo from '../components/goodsInfo';
import GoodsComment from '../components/goodsComment';
import Qualification from '../components/qualification';
import AlbumDialog from '../components/albumDialog';
import PricePopup from '../components/pricePopup.vue';
import ls from '@/utils/ls';
import { mapGetters } from 'vuex';
import { confirm } from '@/decorators/decorators';
import productMaterial from '../components/productMaterial'
import videoMaterial from '../components/videoMaterial'
import sameDialog from '../components/sameDialog'
import promotionDialog from '../components/promotionDialog.vue'

export default {
    name: 'GoodsDetail',
    components: {
        GoodsInfo,
        GoodsComment,
        Qualification,
        AlbumDialog,
        PricePopup,
        productMaterial,
        videoMaterial,
        sameDialog,
        promotionDialog
    },
    computed: {
        ...mapGetters('home', ['getFramework']),
    },
    data() {
        return {
            fullscreenLoading: true,
            sid: null,
            // 物流部分
            logistics: {
                province: {},
                city: {},
                county: {},
                town: {},
                qty: 1,
                money: 0,
                realname: '',
                mobile: '',
            },
            areaIndex: null,
            areaIndex2: null,
            cityIndex: null,
            areaList: [],
            cityList: [],
            //物流部分结束
            minBuyQtyErrTextIsShow: false,
            // 是否登录
            isLogin: this.$ls.getUserId() ? true : false,
            errHintIsShow: false,
            // 是否为多规格商品 1为单规格 0为多规格
            single_option: 1,
            // 第一层数据
            sku_select: {},
            // 第二层数组
            sku_select_options: [],
            headIshow: false,
            goodsActiveName: '1',
            goodsSkuGroupRadio: '',
            skus: [],
            options: [],
            supplier: {},
            supplierType: 0,
            isFavorite: false,
            isFavoriteStore: false,
            product: {},
            // 建议零售价-min
            order_price_min: 0,
            // 建议零售价-max
            order_price_max: 0,
            // 移动像素
            moveNum: 0,
            // 大图图片地址
            bigImgUrl: '',
            bigImgType: 1,
            // 缩略图数组
            sltList: [],
            // 缩略图选中下标
            imgIndex: 0,
            // 当前页
            pageNum: 1,
            // total
            pageTotal: 0,
            min: 1,
            // 相关产品数据
            relationList: [],
            // 店铺产品数据
            supplierProList: [],
            // 描述相符
            des_level: 0,
            // 卖家服务
            shop_level: 0,
            // 物流服务
            express_level: 0,
            feedback_rate: 0,
            dialog: {
                visible: false,
                //pageStatus: 0,
                propRow: {
                    id: '',
                    code: '', // 中台code
                    name: '', // 中台名称
                    cloud_code: '', // 云仓code
                    cloud_name: '', // 云仓快递名称
                },
            },
            //改价
            pricePopupShow: false,
            editPopupShow: false,
            priceBatchShow: false,
            // product: {}, // 单条商品数据源
            price: '', // 单规格销售价
            headTitle: '商品改价', // Popup标题
            pushChannelsShow: false,
            checkboxValue: [],
            isTip: 0, // 0-关闭提示窗，1-开启提示窗
            isExist: true,
            ratioValues: {},
            is_storage: 0, // 1已加入选品库 0未加入
            specificationID: 0, // 保存规格的ID
            specificationDescribe: '', // 保存规格的商品详情
            specificationAttrs: '', // 保存规格的商品详情
            specification_detail_images: '', // 保存默认的商品详情
            isAddressShow: true, // 是否展开列表
            selectId: 0, // 选中地址ID
            addressPage: 1, // 地址当前页数
            addressPageSize: 3, // 地址每页条数
            addressList: [], // 列表地址

            sku_origin_price: 0, // sku规格建议零售价价格
            sku_normal_price: 0, // sku规格批发价价格
            sku_price: 0, // sku规格超级批发价价格
            sku_profit_rate: null, // sku规格利润率
            sku_gross_profit_rate: null, // sku规格毛利率
            sku_min_discount: null, // sku规格折扣
            sku_min_discount_ratio: null, // sku规格折扣率
            checkSkuText: '',

            // 查同款
            sameList: [
                {name: '京东',id: 'jd'},
                {name: '淘宝',id: 'taobao'},
                {name: '拼多多',id: 'pdd'},
                {name: '唯品会',id: 'vip'},
            ]
        };
    },
    mounted() {
        // window.addEventListener('scroll', this.handleScroll, true)
        this.initGoodsInfo();
        this.initFavorite();

        // this.initFavoriteStore();
        var galleryThumbs = new this.Swiper('.gallery-thumbs', {
            spaceBetween: 5,
            slidesPerView: 5,
            freeMode: true,
            watchSlidesVisibility: true,
            watchSlidesProgress: true,
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
        });
        new this.Swiper('.gallery-top', {
            spaceBetween: 10,
            thumbs: {
                swiper: galleryThumbs,
            },
        });
        this.getRelationList();
        this.getSupplierProList();
        this.getArea();
        this.getPriceInit();
        this.getExist();
        // this.getAddressList();
    },
    methods: {
        // 查同款
        sameInfo(value) {
            this.$refs.sameDialog.info(value,this.product.title);
        },
        // 查看推广链接
        promotion(id) {
            this.$refs.promotionDialog.info(id);
        },
        // 获取收货地址列表
        async getAddressList() {
            const data = {
                page: this.addressPage,
                pageSize: this.addressPageSize,
            };
            const res = await this.$post('/address/list', data);
            if (res.code === 0) {
                this.addressList = res.data.list;
                res.data.list.forEach((item) => {
                    if (item.is_default) {
                        this.selectId = item.id;
                        this.logistics.province.id = item.province_id;
                        this.logistics.province.name = item.province;
                        this.logistics.city.id = item.city_id;
                        this.logistics.city.name = item.city;
                        this.logistics.county.id = item.county_id;
                        this.logistics.county.name = item.county;
                        this.logistics.town.id = item.town_id;
                        this.logistics.town.name = item.town;
                        this.logistics.realname = item.realname;
                        this.logistics.mobile = item.mobile;
                        this.getFreight();
                    }
                });
            }
        },
        // 是否展开列表
        addressListShow() {
            this.isAddressShow = !this.isAddressShow;
            if (this.isAddressShow) {
                this.getAddressList();
            } else {
                this.addressList.splice(1);
            }
        },
        // 是否选择地址
        chooseAddress(item) {
            if (item.id !== this.selectId) {
                this.selectId = item.id;
                this.logistics.province.id = item.province_id;
                this.logistics.province.name = item.province;
                this.logistics.city.id = item.city_id;
                this.logistics.city.name = item.city;
                this.logistics.county.id = item.county_id;
                this.logistics.county.name = item.county;
                this.logistics.town.id = item.town_id;
                this.logistics.town.name = item.town;
                this.logistics.realname = item.realname;
                this.logistics.mobile = item.mobile;
                this.areaIndex = null;
                this.areaIndex2 = null;
                this.cityIndex = null;
                this.cityList = [];
                this.getFreight();
            } else {
                this.selectId = 0;
                this.logistics.province.id = '';
                this.logistics.province.name = '';
                this.logistics.city.id = '';
                this.logistics.city.name = '';
                this.logistics.county.id = 0;
                this.logistics.county.name = '';
                this.logistics.town.id = 0;
                this.logistics.town.name = '';
                this.logistics.realname = '';
                this.logistics.mobile = '';

                this.areaIndex = null;
                this.areaIndex2 = null;
                this.cityIndex = null;
                this.cityList = [];
            }
        },
        // 加入选品库
        async addSelection() {
            let res = await this.$get('/application/getApplicationApply');
            if (res.code === 0) {
                let data = {
                    ids: [parseInt(this.$route.query.goods_id)],
                };
                let that = this;
                this.$post('/product/addStorageCenter', data)
                    .then((i) => {
                        if (i.code == 0) {
                            that.initGoodsInfo();
                            that.$message.success(i.msg);
                        } else {
                            that.$message.error(i.msg);
                        }
                    })
                    .catch(() => {});
            } else {
                this.$router.push('/personalCenter/apiProcurement');
            }
        },
        // 移除选品库
        delSelection() {
            let data = {
                ids: [parseInt(this.$route.query.goods_id)],
            };
            let that = this;
            this.$post('/product/deleteStorageCenter', data)
                .then((i) => {
                    if (i.code == 0) {
                        that.initGoodsInfo();
                        that.$message.success(i.msg);
                    } else {
                        that.$message.error(i.msg);
                    }
                })
                .catch(() => {});
        },
        getPriceInit() {
            const api = '/smallShop/setting/getShopProductSetting';
            this.$get(api)
                .then((res) => {
                    if (res.code === 0) {
                        this.isTip = res.data.setting.value.tip;
                        this.ratioValues = res.data.setting.value;
                    }
                })
                .catch((Error) => {
                    console.log(Error);
                });
        },
        onDeleteProduct() {
            this.$confirm('是否移出该商品？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            })
                .then(() => {
                    let params = {
                        product_ids: [],
                    };
                    params.product_ids[0] = parseInt(
                        this.$route.query.goods_id,
                    );
                    this.$del('/smallShop/product/deleteProduct', params)
                        .then((res) => {
                            if (res.code === 0) {
                                this.$message.success(res.msg);
                                //   this.init()
                                this.getPriceInit();
                                this.getExist();
                            } else {
                                this.$message.error(res.msg);
                            }
                        })
                        .catch(function (res) {
                            console.log(res);
                        });
                })
                .catch(() => {});
        },
        getExist() {
            let params = {
                id: parseInt(this.$route.query.goods_id),
                page: this.page,
                pageSize: this.pageSize,
            };
            this.$post('/smallShop/product/exist', params)
                .then((res) => {
                    if (res.code == 0) {
                        this.isExist = res.data.exist;
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },
        // 以下：单规格改价
        // 单规格改价popup
        onOpenEditPopup() {
            this.editPopupShow = true;
        },
        // 确认商品管理popup
        onConfirmPricePopup(p) {
            this.onAddProduct(p);
            this.onClosePricePopup();
        },
        // 确认改价popup
        onConfirmEditPopup(p) {
            this.onChangePrice(p);
            this.onCloseEditPopup();
        },
        // 关闭商品管理popup
        onClosePricePopup(flg = '') {
            if (!flg) {
                this.pricePopupShow = false;
                this.editPopupShow = false;
            } else {
                this[flg] = false;
            }
        },
        // 关闭批量改价popup
        onCloseEditPopup() {
            this.editPopupShow = false;
        },
        // 提交选品
        onAddProduct(params) {
            const api = '/smallShop/product/addByProductID';
            let newParams = {
                product_ids: [],
                price_proportion: params.price_proportion,
                price_type: params.currentType,
            };
            newParams.product_ids[0] = params.product_ids[0];
            this.$post(api, newParams, true)
                .then((res) => {
                    if (res.code === 0) {
                        // this.getProductServe()
                        this.getPriceInit();
                        this.getExist();
                        this.$message.success(res.msg);
                    } else {
                        this.$message.error(res.msg);
                    }
                })
                .catch((Error) => {
                    console.log(Error);
                });
        },
        // 打开商品管理popup
        onOpenPricePopup(item) {
            this.pricePopupShow = true;
            this.product = item;
            console.log('kk this.product', this.product);
            // this.$refs.pricePopup.price_proportion =
            //     item.small_shop_product_sale.price_proportion / 100
            // this.$refs.pricePopup.price_type = item.small_shop_product_sale.price_type
            if (item?.small_shop_product_sale?.price_proportion) {
                this.$refs.pricePopup.price_proportion =
                    item.small_shop_product_sale.price_proportion / 100;
            }
            if (item?.small_shop_product_sale?.price_type) {
                this.$refs.pricePopup.price_type =
                    item.small_shop_product_sale.price_type;
            }
            this.$nextTick(() => {
                this.$refs.pricePopup.computedSellingPrice();
            });
        },

        // 下载素材
        @confirm('提示', '是否下载?')
        downloadMaterial() {
            this.$get('/product/resource', { id: this.product.id }).then(
                ({ code, data, msg }) => {
                    if (code === 0) {
                        let link = this.$path + '/' + data.link;
                        this.$fn.exportTable(link);
                    } else {
                        this.$message.error(msg);
                    }
                },
            );
        },
        // 点击省
        handleProvinceClick(index, index2, pid) {
            console.log('qwewqeqweqw',this.areaList, index, index2, pid);
            this.areaIndex = index;
            this.areaIndex2 = index2;
            this.cityIndex = null;
            this.getArea(pid);
        },
        // 点击市
        handleCityClick(index, city) {
            this.cityIndex = index;
            this.logistics.province =
                {...this.areaList[this.areaIndex][this.areaIndex2]};
            this.logistics.city = city;
            this.logistics.county = {
                id: 0,
                name: '',
            };
            this.logistics.town = {
                id: 0,
                name: '',
            };
            this.logistics.realname = '';
            this.logistics.mobile = '';
            this.selectId = 0;
            this.getFreight();
        },
        // 获取物流运费
        async getFreight() {
            if (this.logistics.city && this.logistics.qty > 0) {
                let params = {
                    sku_id: this.sku_select_options[0].id,
                    qty: this.logistics.qty,
                    province_id: this.logistics.province.id,
                    province: this.logistics.province.name,
                    city_id: this.logistics.city.id,
                    city: this.logistics.city.name,
                    county_id:  this.logistics.county.id,
                    county: this.logistics.county.name,
                    town_id: this.logistics.town.id,
                    town: this.logistics.town.name,
                    detail: this.logistics.province.name + this.logistics.city.name + this.logistics.county.name + this.logistics.town.name,
                    realname: this.logistics.realname,
                    mobile: this.logistics.mobile,
                };
                let res = await this.$post('/trade/getProductFreight', params);
                if (res.code === 0) {
                    this.logistics.money = parseInt(res.data.freight);
                } else {
                    this.$message.error(res.msg);
                }
            }
        },
        // 获取地区
        async getArea(pid = 0) {
            let res = await this.$get('/region/list', { parent_id: pid });
            if (res.code === 0) {
                let list = res.data.list;
                if (pid === 0) {
                    this.areaList = [];
                    // 每行显示个数
                    let rowNum = 7;
                    // 计算共有多少列
                    let verticalNum = Math.ceil(list.length / rowNum);
                    // 截取起始小标位
                    let sliceNum = 0;
                    for (let i = 0; i < verticalNum; i++) {
                        let newList = list.slice(sliceNum, sliceNum + rowNum);
                        sliceNum += rowNum;
                        this.areaList.push(newList);
                    }
                } else {
                    this.cityList = [];
                    this.cityList = list;
                }
            } else {
                this.$message.error(res.msg);
            }
        },
        // 加入选聘专辑
        onCreate() {
            this.dialog.visible = true;
        },
        // 规格切换大图
        handleBigImg(imgUrl) {
            if (imgUrl) {
                this.bigImgUrl = imgUrl;
            }
        },
        // 点击规格切换详情
        cutSpecification(res) {
            // 多规格时切换
            if (!this.single_option) {
                // 超级批发价
                this.sku_price = res.price;
                // 批发价
                this.sku_normal_price = res.normal_price;
                // 建议零售价
                this.sku_origin_price = res.origin_price;
                // 利润率
                this.sku_profit_rate = res.profit_rate;
                // 毛利率
                this.sku_gross_profit_rate = res.gross_profit_rate;
                // 折扣
                this.sku_min_discount = res.min_discount;
                // 折扣率
                this.sku_min_discount_ratio = res.min_discount_ratio;
                this.pageNum = 1;
                this.moveNum = 0;
                this.$refs.imgBox.style.transform =
                    'translate3d(' + this.moveNum + 'px, 0px, 0px)';
                this.imgIndex = 0;
                this.sltList = [];
                // 大图
                if (res.image_url) {
                    this.bigImgUrl = res.image_url;
                }
                // 标题
                if (res.title) {
                    // this.product.title = res.title;
                    this.checkSkuText = res.options[1].spec_item_name;
                }
                // 描述
                if (res.desc) {
                    this.product.desc = res.desc;
                } else {
                    this.product.desc = '';
                }
                // 缩略图
                if (res.gallery !== null && res.gallery.length > 0) {
                    this.sltList.push(...res.gallery);
                    // 切换视频
                    if (res.video_url) {
                        // this.bigImgUrl = res.video_url;
                        this.sltList.unshift({
                            type: 2,
                            src: res.video_url,
                        });
                    }
                } else {
                    this.sltList.push(...this.product.gallery);
                    // 切换视频 误删
                    // if (this.product.video_url) {
                    //     this.bigImgUrl = this.product.video_url;
                    //     this.sltList.unshift({
                    //         type: 2,
                    //         src: this.product.video_url,
                    //     });
                    // }
                }

                this.specificationID = res.id;
                this.specificationDescribe = res.describe;
                if (res.attrs !== null && res.attrs.length > 0) {
                    this.specificationAttrs = res.attrs;
                } else {
                    this.specificationAttrs = null;
                }
                if (this.$refs.goodsInfo) {
                    // 如果没有设置规格详情 则会展示默认的商品详情
                    if (res.describe) {
                        this.$refs.goodsInfo.detailImages = res.describe;
                    } else {
                        this.$refs.goodsInfo.detailImages =
                            this.specification_detail_images;
                    }
                    // 属性
                    if (res.attrs !== null && res.attrs.length > 0) {
                        this.$refs.goodsInfo.attrs = res.attrs;
                    } else {
                        this.$refs.goodsInfo.attrs = this.product.attrs;
                    }
                }
            }
        },
        // 复制
        copyOrder_sn(order_sn) {
            var input = document.createElement('input'); // 直接构建input
            input.value = order_sn; // 设置内容
            document.body.appendChild(input); // 添加临时实例
            input.select(); // 选择实例内容
            document.execCommand('Copy'); // 执行复制
            document.body.removeChild(input); // 删除临时实例
            this.$message({
                type: 'success',
                message: '复制成功',
            });
        },
        // 切换tabs
        handleTabsClick(tab, event) {
            this.$nextTick(() => {
                switch (tab.name) {
                    case '1':
                        this.$refs.goodsInfo.attrs = this.product.attrs;
                        // 判断是否为多规格单规格
                        if (this.single_option) {
                            this.$refs.goodsInfo.detailImages =
                                this.product.detail_images;
                        } else {
                            // 如果为 规格详情 为空则会显示默认的详情
                            if (this.specificationDescribe) {
                                this.$refs.goodsInfo.detailImages =
                                    this.specificationDescribe;
                            } else {
                                this.$refs.goodsInfo.detailImages =
                                    this.product.detail_images;
                            }
                            // 属性
                            if (this.specificationAttrs) {
                                this.$refs.goodsInfo.attrs =
                                    this.specificationAttrs;
                            } else {
                                this.$refs.goodsInfo.attrs = this.product.attrs;
                            }
                        }

                        break;
                    case '2':
                        this.$refs.goodsComment.page = 1;
                        this.$refs.goodsComment.pageSize = 10;
                        this.$refs.goodsComment.getComment();
                        this.$refs.goodsComment.sales = this.product.sales;
                        this.$refs.goodsComment.feedback_rate =
                            this.product.feedback_rate;
                        this.$refs.goodsComment.des_level = this.des_level;
                        this.$refs.goodsComment.shop_level = this.shop_level;
                        this.$refs.goodsComment.express_level =
                            this.express_level;
                        this.$refs.goodsComment.feedback_rate =
                            this.feedback_rate;
                        break;
                    case '3':
                        this.$refs.qualification.qualificationData =
                            this.product.qualifications;
                        break;
                    case '4':
                        break;
                    case '5':
                        this.$refs.videoMaterial.info()
                        break;
                }
            });
        },
        // 切换大图
        handleClickTab(item, index) {
            this.imgIndex = index;
            this.bigImgUrl = item.src;
            this.bigImgType = item.type;
            /* this.$nextTick(() => {
                this.$refs.mvideo.init();
            }); */
        },
        // 左右切换
        tabBtn(type) {
            this.pageTotal = Math.ceil(this.sltList.length / 5);

            switch (type) {
                case 'last':
                    if (this.pageNum > 1) {
                        this.pageNum = this.pageNum - 1;
                        this.moveNum = this.moveNum + 76.5 * 5;
                    }
                    break;
                case 'next':
                    if (this.pageNum < this.pageTotal) {
                        this.pageNum = this.pageNum + 1;
                        this.moveNum = this.moveNum - 76.5 * 5;
                    }
                    break;
            }
            this.$refs.imgBox.style.transform =
                'translate3d(' + this.moveNum + 'px, 0px, 0px)';
        },
        handleScroll() {
            let scrollTop =
                document.body.scrollTop || document.documentElement.scrollTop;
            if (scrollTop > this.$refs.goodsCentDiv.offsetTop) {
                this.headIshow = true;
            } else {
                this.headIshow = false;
            }
        },
        // 获取供应商
        getSupplier() {
            this.$get('/supplier/getGoodsDetatilStoreInformation', {
                goods_id: parseInt(this.$route.query.goods_id),
            }).then((res) => {
                if (res.code === 0) {
                    this.sid = res.data.id;
                    this.supplier = res.data.supplier;
                    this.supplierType = res.data.type;
                } else {
                    this.$message.error(res.msg);
                }
            });
        },
        // getSupplier(supplier_id = 0) {
        //     this.$get("/supplier/saleInfo", {id: supplier_id}).then(res => {
        //         if (res.code === 0) {
        //             this.supplier = res.data.supplier
        //         } else {
        //             this.$message.error(res.msg)
        //         }
        //     })
        // },
        /**
         * 获取建议零售价 最大,最小
         * @param type min - 最小  max - 最大
         */
        getOrderPrice(type = 'min', arr) {
            let newArr = [];
            arr.forEach((item) => {
                item.skus.forEach((item2) => {
                    newArr.push(item2.origin_price);
                });
            });
            let num = newArr[0];
            switch (type) {
                case 'min':
                    newArr.forEach((item) => {
                        if (item < num) {
                            num = item;
                        }
                    });
                    break;
                case 'max':
                    newArr.forEach((item) => {
                        if (item > num) {
                            num = item;
                        }
                    });
                    break;
            }
            return num;
        },
        //初始化数据
        initGoodsInfo() {
            let that = this;
            let para = {
                id: parseInt(this.$route.query.goods_id),
            };
            that.$get('/product/getByLogin', para)
                .then(function (res) {
                    if (res.code == 0) {
                        // 判断是不是课程
                        if (res.data.product.plugin_id === 18) {
                            // 判断是否存在章节ID存在跳转视频详情
                            if (that.$route.query.subsection_id) {
                                const goods_id = res.data.product.id;
                                that.$router.push(
                                    `/videoDetail?goods_id=${goods_id}`,
                                );
                            } else {
                                const goods_id = res.data.product.id;
                                that.$router.push(
                                    `/courseDetail?goods_id=${goods_id}`,
                                );
                            }
                        }
                        that.is_storage = res.data.is_storage;
                        that.product = res.data.product;
                        that.updateSeo(
                            that.product.title,
                            that.product.title,
                            true,
                        );
                        // that.order_price = res.data.product.origin_price;
                        that.des_level = res.data.product.des_level;
                        that.shop_level = res.data.product.shop_level;
                        that.express_level = res.data.product.express_level;
                        that.feedback_rate = res.data.product.feedback_rate;

                        // that.getSupplier(res.data.product.supplier_id);
                        // that.supplier = res.data.product.supplier;
                        // that.initFavoriteStore()

                        that.order_price_min = that.getOrderPrice(
                            'min',
                            res.data.product.sku_select.options,
                        );
                        that.order_price_max = that.getOrderPrice(
                            'max',
                            res.data.product.sku_select.options,
                        );
                        if (that.product.video_url) {
                            that.bigImgUrl = that.product.video_url;
                            that.bigImgType = 2;
                            that.sltList.push({
                                type: 2,
                                src: that.product.video_url,
                            });
                        } else {
                            that.bigImgUrl =
                                that.sltList.length > 0 && that.sltList != null
                                    ? that.sltList[0].src
                                    : '';
                            that.bigImgType = 1;
                        }
                        that.sltList.push(...(res.data.product.gallery || []));
                        that.single_option = res.data.product.single_option;
                        if (
                            res.data.product.sku_select.options != null &&
                            res.data.product.sku_select.options.length > 0
                        ) {
                            that.sku_select = res.data.product.sku_select;
                            that.goodsSkuGroupRadio =
                                that.sku_select.options[0].title;
                            // 单规格默认数量=1
                            if (that.sku_select.options[0].skus.length === 1) {
                                that.sku_select.options[0].skus[0].qty = 1;
                                that.sku_select.options[0].skus[0].min = 1;
                            }
                            that.fullscreenLoading = false;
                            that.product = res.data.product;
                            that.updateSeo(
                                that.product.title,
                                that.product.title,
                                true,
                            );
                            // that.order_price = res.data.product.origin_price;
                            that.des_level = res.data.product.des_level;
                            that.shop_level = res.data.product.shop_level;
                            that.express_level = res.data.product.express_level;
                            that.feedback_rate = res.data.product.feedback_rate;

                            that.sltList = res.data.product.gallery || [];
                            that.getSupplier(res.data.product.supplier_id);
                            // that.supplier = res.data.product.supplier;
                            // that.initFavoriteStore()

                            that.order_price_min = that.getOrderPrice(
                                'min',
                                res.data.product.sku_select.options,
                            );
                            that.order_price_max = that.getOrderPrice(
                                'max',
                                res.data.product.sku_select.options,
                            );
                            if (that.product.video_url) {
                                that.bigImgUrl = that.product.video_url;
                                that.sltList.unshift({
                                    type: 2,
                                    src: that.product.video_url,
                                });
                            } else {
                                that.bigImgUrl =
                                    that.sltList.length > 0 &&
                                    that.sltList != null
                                        ? that.sltList[0].src
                                        : '';
                            }
                            if (
                                res.data.product.sku_select.options != null &&
                                res.data.product.sku_select.options.length > 0
                            ) {
                                that.sku_select = res.data.product.sku_select;
                                that.goodsSkuGroupRadio =
                                    that.sku_select.options[0].title;
                                // 单规格默认数量=1
                                if (
                                    that.sku_select.options[0].skus.length === 1
                                ) {
                                    that.sku_select.options[0].skus[0].qty = 1;
                                    that.sku_select.options[0].skus[0].min = 1;
                                }
                                that.sku_select_options =
                                    that.sku_select.options[0].skus;
                            }
                            that.$nextTick(() => {
                                that.$refs.goodsInfo.attrs =
                                    res.data.product.attrs;
                                that.$refs.goodsInfo.detailImages =
                                    res.data.product.detail_images;
                            });

                            // 保存默认的商品详情
                            that.specification_detail_images =
                                res.data.product.detail_images;
                            that.specificationDescribe =
                                res.data.product.detail_images;
                            that.specificationAttrs = res.data.product.attrs;

                            /*that.product = res.data.Product;
                    that.skus = res.data.Product.skus;
                    that.goodsSkuGroupRadio = res.data.Product.skus[0].id;
                    that.options = res.data.Product.skus[0].options;
                    that.supplier = res.data.Product.supplier;

                    that.sltList = that.product.detail_images;
                    that.bigImgUrl = that.sltList[0]*/
                            /* that.$nextTick(() => {
                                that.$refs.mvideo.init();
                            }); */
                        }
                        that.getAddressList();
                    } else {
                        that.$message.error(res.msg);
                        that.fullscreenLoading = false;
                        // that.product = {}
                    }
                })
                .catch(function (res) {
                    console.error(res);
                });
        },
        // 获取相关产品列表
        getRelationList() {
            let apiUrl = this.$ls.getUserId()
                ? 'product/relation/listLogin'
                : 'product/relation/list';
            this.$get(apiUrl, {
                product_id: parseInt(this.$route.query.goods_id),
                page: 1,
                pageSize: 5,
            }).then((res) => {
                if (res.code === 0) {
                    this.relationList = res.data.list;
                } else {
                    this.relationList = [];
                }
            });
        },
        // 获取店铺推荐
        getSupplierProList() {
            let apiUrl = this.$ls.getUserId()
                ? 'product/supplier/listLogin'
                : 'product/supplier/list';
            this.$get(apiUrl, { page: 1, pageSize: 2 }).then((res) => {
                if (res.code === 0) {
                    this.supplierProList = res.data.list;
                    this.initFavoriteStore();
                } else {
                    this.supplierProList = [];
                }
            });
        },
        // 切换商品规格
        handleChangeGoodsSkuGroupRadio(item) {
            this.sku_select.options.forEach((k) => {
                k.skus.forEach((k2) => {
                    k2.qty = 0;
                });
            });
            this.sku_select_options = item.skus;
            // this.order_price = item.skus[0].origin_price
        },

        //获取收藏状态
        initFavorite() {
            let that = this;
            let para = {
                item_id: parseInt(this.$route.query.goods_id),
                type: 1,
                // "uid": ls.getUserId()
            };

            that.$post('/favorite/findFavorite', para)
                .then(function (res) {
                    if (res.code == 0) {
                        that.isFavorite = res.data.enable == 2 ? false : true;
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },

        //商品收藏
        favorite() {
            if (!this.isLogin) {
                this.$message.error('请先登录');
                return false;
            }
            let that = this;
            let para = {
                item_id: parseInt(this.$route.query.goods_id),
                type: 1,
            };

            that.$post('/favorite/createFavorite', para)
                .then(function (res) {
                    if (res.code == 0) {
                        that.$message.success(res.msg);
                        that.isFavorite = res.data.enable == 2 ? false : true;
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },

        //获取店铺收藏状态
        initFavoriteStore() {
            let that = this;
            if (this.supplier.id) {
                let para = {
                    item_id: this.supplier.id,
                    type: 2,
                    // "uid": ls.getUserId()
                };
                that.$post('/favorite/findFavorite', para)
                    .then(function (res) {
                        if (res.code == 0) {
                            that.isFavoriteStore =
                                res.data.enable == 2 ? false : true;
                        }
                    })
                    .catch(function (res) {
                        console.log(res);
                    });
            }
        },

        //收藏店铺
        favoriteStore() {
            if (!this.isLogin) {
                this.$message.error('请先登录');
                this.goLogin();
                return false;
            }
            let that = this;
            let para = {
                item_id: that.supplier.id,
                type: 2,
            };

            that.$post('/favorite/createFavorite', para)
                .then(function (res) {
                    if (res.code == 0) {
                        that.$message.success(res.msg);
                        that.isFavoriteStore =
                            res.data.enable == 2 ? false : true;
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },
        //加入购物车
        addCart() {
            if (!this.isLogin) {
                this.$message.error('请先登录');
                this.goLogin();
                return false;
            }

            if (
                this.$store.state.userLevelPrice.userLevelPriceTitle &&
                !this.$fn.isMatchingPriceAuthority(this.product.id)
            ) {
                this.$message.error('暂无购买该商品权限');
                return;
            }

            // 验证起批量
            if (this.product.min_buy_qty && this.product.min_buy_qty > 1) {
                if (this.verifyMinBuyQty()) {
                    return false;
                }
            }
            let that = this;
            let optionsTemp = [];
            let options = that.sku_select.options;
            if (options != null && options.length > 0) {
                for (let i = 0; i < options.length; i++) {
                    for (let j = 0; j < options[i].skus.length; j++) {
                        if (options[i].skus[j].qty > 0) {
                            let skuItem = {};
                            skuItem.qty = options[i].skus[j].qty;
                            skuItem.sku_id = options[i].skus[j].id;
                            skuItem.address_id = parseInt(this.selectId);
                            optionsTemp.unshift(skuItem);
                        }
                    }
                }
                if (optionsTemp.length <= 0) {
                    that.errHintIsShow = true;
                    // that.$message.error("请选择商品规格");
                    return;
                }
            }
            that.errHintIsShow = false;
            let para = {
                shopping_carts: optionsTemp,
            };
            that.$post('/shoppingcart/add', para)
                .then(function (res) {
                    if (res.code == 0) {
                        that.$message.success(res.msg);
                        that.$router.push('/buyerCart');
                    } else {
                        that.$message.error(res.msg);
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },

        //立即购买
        buy() {
            if (!this.isLogin) {
                this.$message.error('请先登录');
                this.goLogin();
                return false;
            }
            // 验证起批量
            if (this.product.min_buy_qty && this.product.min_buy_qty > 1) {
                if (this.verifyMinBuyQty()) {
                    return false;
                }
            }
            let that = this;
            let optionsTemp = [];
            let options = that.sku_select.options;
            if (options != null && options.length > 0) {
                for (let i = 0; i < options.length; i++) {
                    for (let j = 0; j < options[i].skus.length; j++) {
                        if (options[i].skus[j].qty > 0) {
                            let skuItem = {};
                            skuItem.qty = options[i].skus[j].qty;
                            skuItem.sku_id = options[i].skus[j].id;
                            skuItem.address_id = parseInt(this.selectId);
                            optionsTemp.unshift(skuItem);
                        }
                    }
                }
                if (optionsTemp.length <= 0) {
                    that.errHintIsShow = true;
                    // that.$message.error("请选择商品规格");
                    return;
                }
            }
            that.errHintIsShow = false;
            let para = {
                items: optionsTemp,
            };
            that.$post('/trade/buy', para)
                .then(function (res) {
                    if (res.code == 0) {
                        that.$message.success(res.msg);
                        that.$router.push({
                            path: '/settlementAmount',
                            query: { buy_id: res.data.buy_id },
                        });
                    } else if (res.code == 2) {
                        that.$confirm('当前等级暂无购买权限', '提示', {
                            confirmButtonText: '去升级',
                            cancelButtonText: '取消',
                        }).then(() => {
                            that.$router.push({
                                path: '/personalCenter/memberRights',
                            });
                        });
                    } else {
                        that.$message.error(res.msg);
                    }
                })
                .catch(function (res) {
                    console.log(res, '===>buy');
                });
        },
        // 验证起批量
        verifyMinBuyQty() {
            let num = 0;
            this.sku_select_options.forEach((item) => {
                num += item.qty;
            });
            let flg = num >= this.product.min_buy_qty ? false : true;
            this.minBuyQtyErrTextIsShow = flg;
            return flg;
        },
    },
};
