<template>
  <el-dialog
      title="移动到..."
      :visible="isShow"
      width="40%"
      :before-close="handleClose">
    <el-input v-model="saveData.sort" placeholder="请输入文章排序(数字越大排序越前,默认0)"></el-input>
    <el-tree class="mt_8" ref="treeRef" @node-click="handleChecked" :current-node-key="currentKey"
             :default-expanded-keys="defKey"
             :expand-on-click-node="false" :data="treeList"
             node-key="id" highlight-current :props="defaultProps">
      <div class="node-containe f fac fjsb" slot-scope="{node,data}">
        <span>{{ node.label }}</span>
        <div class="hidden">
          <span style="color: rgb(191, 191, 191);">移动为</span>
          <el-radio-group class="ml_10" v-model="data.moveType">
            <el-radio :label="1">同级</el-radio>
            <el-radio class="ml_10" v-if="data.pid === 0" :label="2">子级</el-radio>
          </el-radio-group>
        </div>
        <div class="hidden selector-tree-placeholder"></div>
      </div>
    </el-tree>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirm">确 定</el-button>
      <el-button @click="handleClose">取 消</el-button>
    </div>
  </el-dialog>

</template>

<script>
export default {
  name: "moveToDialog",
  data() {
    return {
      checkedRow: {},
      saveData: {
        id: null,
        title: "",
        desc: "",
        hidden: 0, // 0=否 1=是
        pid: null,
        knowledge_base_id: null,
        sort: null,
      },
      currentKey: "",
      defKey: [],
      isShow: false,
      treeList: [],
      defaultProps: {
        children: 'children',
        label: 'title'
      }
    }
  },
  methods: {
    handleChecked(data) {
      let node = this.$refs.treeRef.getNode(data.id)
      if (!node.expanded) node.expanded = true
      this.checkedRow = node.data
    },
    // 提交
    async confirm() {
      let params = {
        article_id: this.saveData.id,
      }
      if(!this.checkedRow.moveType) {
        this.isShow = false
      } else {
        switch (this.checkedRow.moveType) {
          case 1: // 同级
            params.pid = this.checkedRow.pid
            break;
          case 2: // 子级
            params.pid = this.checkedRow.id
            break;
        }
        const {code, msg} = await this.$post("/article/UpdateArticlePid", params)
        if (code === 0) {
          this.$message.success(msg)
          this.handleClose()
          this.$emit("reload")
        } else {
          this.$message.error(msg)
        }
      }
    },
    // 初始化
    init(row, list) {
      this.isShow = true
      this.treeList = this.$fn.deepClone(list)
      this.treeList.forEach(item => {
        this.$set(item, "moveType", 1)
        if (item.children && item.children.length) {
          item.children.forEach(item2 => {
            this.$set(item2, "moveType", 1)
          })
        }
      })
      this.defKey = [row.id]
      this.$nextTick(() => {
        this.currentKey = row.id
        this.$refs['treeRef'].setCurrentKey(this.currentKey)
      })
      this.saveData = this.$fn.deepClone(row)
    },
    handleClose() {
      this.isShow = false
      this.saveData = {
        id: null,
        title: "",
        desc: "",
        hidden: 0, // 0=否 1=是
        pid: null,
        knowledge_base_id: null,
        sort: null,
      }
      this.currentKey = ""
      this.defKey = []
      this.treeList = []
      this.checkedRow = {}
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-tree {
  background: transparent;

  .el-tree-node {
    .el-tree-node__content {
      margin-bottom: 8px;
      padding-top: 0 !important;
      padding-bottom: 0 !important;
    }
  }
}

.el-tree-node.is-current > .el-tree-node__content {
  .node-containe {
    .hidden {
      display: block;
    }
  }
}

.node-containe {
  width: 100%;
  position: relative;

  .selector-tree-placeholder {
    display: block;
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #66B1FF;
  }

  .hidden {
    display: none;
  }
}
</style>