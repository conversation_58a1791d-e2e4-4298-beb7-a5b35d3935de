<template>
    <el-dialog
      title="选择区域"
      :visible="isShow"
      width="800px"
      fil
      @open="onInit"
      :modal-append-to-body="false"
      :append-to-body="true"
      :before-close="handleClose"
    >
    <el-row :gutter="20">
      <el-col :span="10">
        <el-input v-model="name" placeholder="请输入内容"></el-input>
      </el-col>
      <el-col :span="6">
        <el-button class="confirm-btn" @click="onInit">搜索</el-button>
      </el-col>
    </el-row>
    <div style="margin-top: 20px">
      <el-scrollbar class="scrollbar-wrapper">
        <template v-for="item in areaList">
          <el-tag 
            v-if="item.name" 
            size="small" 
            class="mt10"
            :type="isChoose === item.name ?'warning':'info'"
            :key="item.id" 
            @click="onSelectArea(item)"
          >
            {{item.name}}
          </el-tag>
        </template>
      </el-scrollbar>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button class="confirm-btn" @click="confirm">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name:"areaDialog",
  data() {
    return {
      isShow: false,
      isChoose: '',
      name:'',
      areaList:[],
      selectArea: {},
    }
  },
  methods: {
    onSelectArea(item){
      this.selectArea = item
      this.isChoose = item.name
    },
    onInit(){
      this.$get("/lease/getLeaseCoverages", {name: this.name}).then(res => {
          if (res.code == 0) {
            console.log(res);  
            this.areaList = res.data 
            this.areaList.unshift({name:'全部区域',id:'all'})
            console.log('this.areaList',this.areaList); 
          }
      }).catch(function (res) {
          console.log(res);
      });
    },
    confirm() {
      this.$emit("submitted", this.selectArea)
      this.handleClose()
    },
    handleClose() {
      this.isShow = false
    },
  },
}
</script>
<style lang="scss" scoped>
.el-tag {
  margin-right: 20px;
  padding:0 24px;
  min-width: 98px;
  height: 34px;
  line-height: 34px;
  text-align: center;
  cursor: pointer;
}
.scrollbar-wrapper {
  height: 50vh;
  overflow-x: hidden !important;
}
</style>
