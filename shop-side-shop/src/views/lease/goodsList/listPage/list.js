import {mapGetters} from "vuex";

/**
 * 获取数组对象指定k的下标
 */
 Array.prototype.indexOfJSON = function (kName, value) {
    for (var i = 0; i < this.length; i++) {
        if (this[i][kName] == value) return i;
    }
    return -1;
};
import Breadcrumb from "../components/breadcrumb";
import AreaDialog from "../components/areaDialog";
import zhLang from 'element-ui/lib/locale/lang/zh-CN';
import SortButton from '@/components/sortButton/sortButton.vue'
import SortButtonGroup from '@/components/sortButton/sortButtonGroup.vue'
export default {
    name:"leaseNewList",
    components: { 
        Breadcrumb,
        SortButton,
        SortButtonGroup, 
        AreaDialog
    },
    directives: {
        "watch-height": {
          inserted: function (el, binding) {
            const h = parseInt(window.getComputedStyle(el).height);
            binding.value(h);
          },
          componentUpdated(el, binding) {
            const h = parseInt(window.getComputedStyle(el).height);
            binding.value(h);
          },
        },
    },
    computed: {
        ...mapGetters("home", ["getFramework"])
    },
    data() {
        return {
            category1_id: parseInt(this.$route.query.category1_id) || null,
            category2_id: parseInt(this.$route.query.category2_id) || null,
            category3_id: parseInt(this.$route.query.category3_id) || null,
            category4_id: parseInt(this.$route.query.category4_id) || null,
            category5_id: parseInt(this.$route.query.category4_id) || null,
            classify_list: [],
            classify2_list: [],
            classify3_list: [],
            classify4_list: [],  // 品牌   
            classify5_list: [],  // 供应商       
            isUnfold: false, // 一级分类是否展开            
            isUnfold2: false,// 二级分类是否展开
            isUnfold3: false,// 三级分类是否展开
            isUnfold4: false,// 品牌是否展开
            isUnfold5: false,// 供应商是否展开
            isShowFirstExpand: true,
            isShowSecondExpand: true,
            isShowThirdExpand: true,
            isShowForthExpand: true,
            isShowFifthExpand: true,
            loading: false,    
            selectArea:{
                name:'请选择区域'
            }, 
            selectAreaId:null,       
            breadcrumbData: [],// 面包屑
            goodsList: [],
            formData:{
                min_rate:null, // 最低利润率
                max_rate:null, // 最高利润率
                min_price:null, // 最低价格
                max_price:null, // 最高价格
                is_new: 0, // 新品
                is_hot: null, // 热卖
                is_promotion: 0, // 促销
            },
            sortForm:{
                value:'default', //按"默认"排序
                sort:'up', // 升序
            },
            page: 1,
            pageSize: 20,
            total: 0,
        }
    },
    watch: {
        $route(value) {
            if (value.path === "/lease/goodsList") {
                this.fetch()
            }
        }
    },
    mounted() {
        this.init()
        // this.fetch()
    },
    filters: {
        unfoldText: function (status) {
            return status ? '收起' : '展开'
        }
    },
    methods: {
        onOpenArea() {
            this.$refs.areaDialog.isShow = true
        },
        onSelectArea(val){
            this.selectArea = val
            this.selectAreaId = val.id
            if(val.id==='all'){
                this.selectAreaId = null
            }
            console.log('this.selectArea',this.selectArea);
            this.fetch()
        },
        checkFirstExpand(height) {
            this.isShowFirstExpand = height > 960;
          },
        checkSecondExpand(height) {
            this.isShowSecondExpand = height > 90;
        },
        checkThirdExpand(height) {
            this.isShowThirdExpand = height > 90;
        },
        checkForthExpand(height) {
            this.isShowForthExpand = height > 90;
        },
        checkFifthExpand(height) {
            this.isShowFifthExpand = height > 90;
        },
        // 点击二级分类的“全部”
        handleAllClassiyfClick() {
            let query = {
                category1_id: this.category1_id,
                category2_id: null,
                category3_id: null,
            }
            if(this.$route.query.keyword){
                query.keyword = this.$route.query.keyword
            }
            if(this.$route.query.t){
                query.t = this.$route.query.t
            }
            this.$router.replace({
                query
            })
            this.jointBreadcrumb()
            this.fetch(1)
        },
        pagination(val) {
            this.page = val.page
            this.fetch()
        },
        // 分类重新后取赋值, 解决面包屑二三级分类值不变问题
        initCategory() {
            this.category1_id = parseInt(this.$route.query.category1_id) || null
            this.category2_id = parseInt(this.$route.query.category2_id) || null
            // 二级全部
            if (this.$route.query.category2_id && !this.$route.query.category3_id) {
                this.category3_id = this.category2_id
            } else {
                this.category3_id = parseInt(this.$route.query.category3_id) || null
            }
        },
        // 拼接面包屑
        jointBreadcrumb() {
            this.initCategory();
            this.breadcrumbData = []
            let arr = [
                { name: "首页" }
            ]
            if (this.category1_id && this.$route.query.category1_id) {
                let obj = this.classify_list[this.classify_list.indexOfJSON('id', this.category1_id)]
                console.log('obj', obj);
                arr.push({
                    name: obj.name
                })
                if (this.category2_id && this.$route.query.category2_id) {
                    if (obj.children && obj.children.length > 0) {
                        let c2 = {}
                        c2 = obj.children.find((v) => {
                            return v.id === this.category2_id
                        })
                        arr.push({
                            name: c2.name
                        })
                        if (this.category3_id && this.$route.query.category3_id) {
                            if (c2.children && c2.children.length > 0) {
                                let c3 = {}
                                c3 = c2.children.find((v) => {
                                    return v.id === this.category3_id
                                })
                                if(c3) {
                                    arr.push({
                                        name: c3.name
                                    })
                                }
                            }
                        }
                    }
                }
            } else {
                arr.push({
                    name: "全部分类"
                })
            }
            this.breadcrumbData = arr;
        },
        // 切换一级分类
        handleClassify1Change(val) {
            this.classify3_list = []
            this.goodsList = []
            let query = {
                category1_id: val,
                category2_id: null,
                category3_id: null,
            }
            if(this.$route.query.keyword){
                query.keyword = this.$route.query.keyword
            }
            if(this.$route.query.t){
                query.t = this.$route.query.t
            }
            this.$router.replace({
                query
            })
            this.getClassify2(val)
            this.jointBreadcrumb()
            this.fetch(1)
        },
        // 切换二级分类
        handleClassify2Change(cid) {
            this.classify3_list = []
            const clssify2_Obj = this.classify2_list.find(item=>item.id === cid)
            this.classify3_list = clssify2_Obj.children
            let query = {
                category1_id: this.category1_id,
                category2_id: this.category2_id,
                category3_id: null,
            }
            this.$router.replace({
                query
            })
            console.log('this.category1_id', this.category1_id);
            if(this.$route.query.keyword){
                query.keyword = this.$route.query.keyword
            }
            if(this.$route.query.t){
                query.t = this.$route.query.t
            }
            this.getClassify2(parseInt(this.category1_id))
            // this.getClassify2()
            this.jointBreadcrumb()
            this.fetch(1)
        },
        // 切换三级分类
        handleClassify3Change(cid) {
            let query = {
                category1_id: this.category1_id,
                category2_id: this.category2_id,
                category3_id: cid,
            }
            if(this.$route.query.keyword){
                query.keyword = this.$route.query.keyword
            }
            if(this.$route.query.t){
                query.t = this.$route.query.t
            }
            this.$router.replace({
                query
            })
            this.jointBreadcrumb()
            this.fetch(1)
        },
        // 切换品牌
        handleClassify4Change(val) {
            let query = {
                supplier_id: this.category4_id
            }
            if(this.$route.query.keyword){
                query.keyword = this.$route.query.keyword
            }
            if(this.$route.query.t){
                query.t = this.$route.query.t
            }
            this.$router.replace({
                query
            })
            this.fetch(1)
        },
        // 切换供应商
        handleClassify5Change(val) {
            let query = {
                brand_id: this.category5_id
            }
            if(this.$route.query.keyword){
                query.keyword = this.$route.query.keyword
            }
            if(this.$route.query.t){
                query.t = this.$route.query.t
            }
            this.$router.replace({
                query
            })
            this.fetch(1)
        },
        // 获取二级分类
        getClassify2(val) {
            if (val) {
                this.classify2_list = this.classify_list[this.classify_list.indexOfJSON('id', val)].children
                // 二级分类增加全部选项
                if(!this.classify2_list.find(item=>item.name==="全部")){
                    this.classify2_list.unshift({
                        id: val,
                        name: "全部"
                    })     
                } 
                // 三级分类增加全部选项
                let c3Obj = this.classify2_list[this.classify2_list.indexOfJSON("id",parseInt(this.category2_id))]              
                this.classify3_list = c3Obj && c3Obj.children ? c3Obj.children : [{id: this.category2_id,name: "全部"}]
                if(!this.classify3_list.find(item=>item.name === "全部")){
                    this.classify3_list.unshift({
                        id: this.category2_id,
                        name: "全部"
                    })
                }
                if (!this.$route.query.category3_id) {
                    this.category3_id = parseInt(this.$route.query.category2_id)
                } else {
                    this.category3_id = parseInt(this.$route.query.category3_id)
                }
            }
        },
        // 获取品牌
        getBrands(){
            this.$get("lease/getBrandListGatherSupply").then(res => {
                if (res.code === 0) {
                    this.classify4_list = res.data               
                    this.classify4_list.unshift({
                        // id: val,
                        name: "全部"
                    })     
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        // 获取供应商
        getSupliers(){
            this.$get("lease/getSupplierListGatherSupply").then(res => {
                if (res.code === 0) {
                    this.classify5_list = res.data               
                    this.classify5_list.unshift({
                        // id: val,
                        name: "全部"
                    })     
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        // 初始化
        init() {
            // 获取全部分类 category/tree lease/getCategoryList
            this.$get("lease/getCategoryList").then(res => {
                if (res.code === 0) {
                    this.classify_list = res.data
                    this.jointBreadcrumb()
                    this.getClassify2(this.category1_id)
                    this.fetch()
                } else {
                    this.$message.error(res.msg)
                }
            })
            this.getBrands()
            this.getSupliers()
        },
        // 获取商品
        fetch(page = this.page) {
            this.loading = true;
            /**
             * 排序查询：
             * formatObj键 => 1:向上up; 2:向下down。
             * formatObj值 => 1:默认降; 6:默认升; 4:销量降; 7:销量升; 2:价格降; 3:价格升; 9:利润率降; 10:利润率升; 5:发布时间降; 8:发布时间升。
             */
            const formatObj = {
                default: { 
                    1: 8,
                    2: 5,
                },
                hot: {
                    1: 7,
                    2: 4,
                },
                price: {
                    1: 3,
                    2: 2,
                },
                profit: {
                    1: 10,
                    2: 9,
                },
            }
            let params = {
                category1_id: this.category1_id || null,
                category2_id: null,
                category3_id: null,
                region_id: this.selectAreaId || null,
                brand_id: this.category4_id || null, // 品牌id
                supplier_id: this.category5_id || null, // 供应商id
                sort_by: formatObj[this.sortForm.value][this.sortForm.sort], //排序
                origin_rate:{ // 利润率
                    from: Number(this.formData.min_rate)|| null,
                    to: Number(this.formData.max_rate)|| null
                },
                min_price: this.$fn.changeMoneyY2F(Number(this.formData.min_price))|| null, //最低价格
                max_price: this.$fn.changeMoneyY2F(Number(this.formData.max_price))|| null, //最高价格
                is_new: this.formData.is_new, //新品
                is_hot: this.formData.is_hot || null, //热卖
                is_promotion: this.formData.is_promotion, //促销
                page,
                pageSize: this.pageSize,
            }
            // 处理二级分类id
            if (this.category1_id && this.category2_id) {
                params.category2_id = this.category2_id
                if (this.category3_id) {
                    params.category3_id = this.category3_id
                    this.classify2_list.map(item => {
                        if (item.id === this.category3_id) {
                            params.category3_id = null
                        }
                    })
                }
            }
            if(this.$route.query.keyword){
                params.title = this.$route.query.keyword
            }
            this.$get("lease/getProductList", params).then(res => {
                this.loading = false;
                if (res.code === 0) {
                    this.total = res.data.total
                    this.goodsList = res.data.list
                    zhLang.el.pagination.total = `共 {total} ${this.total / this.pageSize === 100 ? '+' : ''} 条`
                } else {
                    this.total = 0
                    this.goodsList = []
                }
            }).catch((res) => {
                this.loading = false
                console.log(res)
            });
        }
    },
}