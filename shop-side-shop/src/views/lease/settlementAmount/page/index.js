//新增收货人地址total
import RecipientsDialog from "@/views/personalCenter/recipients/components/dialog";
//选择收货地址
import SelectAddress from "@/views/buyerCart/components/selectAddress";
// 新增发票信息
import invoiceDialog from "../components/orderInvoiceDialog";
// 自定义租赁时间
import DefinitionDialog from "../components/definitionDialog";
// 租赁协议
import UserProtocolDialog from "../components/userProtocolDialog.vue"
export default {
  name: "leaseSettlementAmountIndex",
  components: { RecipientsDialog, SelectAddress, invoiceDialog, DefinitionDialog, UserProtocolDialog },
  data() {
    return {
      radio:1,
      isChoose: '',
      formData: {},
      orders: [],
      goods_count: 0,
      fatherObj:{},
      sonArr:[],
      cookedTotal:0, // 重算“商品总价”
      rawTotal: 0, // 商品总价
      rawExpress: 0, // 总运费
      rawService: 0, // 技术服务费
      amount: 0, // 应付总额(含运费)
      currentTenancyDays: 0,
      currentTenancyId: 0,
      currentCount: 0,
      currentRatio: 0,
      tempKey: "",
      buy_id: 0,
      product_id: 0,
      sku_id: 0,
      setting: {},
      tenancyList: [],
      addDaysBtn: [
        // {id:0, num_days:2}
      ],
      checked: true,
    };
  },
  async mounted() {
    this.buy_id = parseInt(this.$route.query.buy_id)
    this.product_id = parseInt(this.$route.query.product_id)
    await this.initCart()
    this.getTenancy()
  },
  methods: {
    // 打开用户协议
    openUserProtocol() {
      this.$refs.userProtocolDialog.isShow = true
      this.$refs.userProtocolDialog.getAgreement()
    },
    /**
     * 一、描述：前端计算价格
     * 1. 非自定义租期时,重算相关价格（此时有折扣）
     * 2. 自定义租期时,重算相关价格（此时无折扣）
     * 二、公式：
     * 1. 折扣后“商品总价” =（商品总价-（商品总价*折扣））*天数
     * 2. 无折扣“商品总价” = 商品总价 * 天数
    */
    onChangePrice(days, ratio, id){
      this.isChoose = days
      ratio = ratio / 10000
      this.currentRatio = ratio / 10000
      console.log('raido', ratio);
      // 自定义
      if (!ratio) {
        this.cookedTotal = this.rawTotal * days 
      }
      // 非自定义
      this.cookedTotal = (this.rawTotal - (this.rawTotal * ratio)) * days 
      let obj = this.sonArr[0]
      obj.amount = this.cookedTotal
      this.sonArr.splice(0, 1, obj)
      // 重算“应付总额(含运费)”
      this.amount = this.cookedTotal + this.rawExpress + this.rawService
      this.currentTenancyDays = days
      this.currentTenancyId = id
    },
    // 后端计算价格
    onGetPrice(days, ratio, id){
      this.currentTenancyId = id
      this.currentRatio = ratio / 10000
      this.isChoose = id
      let params = {
        product_id: this.product_id,
        sku_id: this.sku_id,
        num_days: days,
        preferential_ratio: ratio || 0,
        nums: this.currentCount,
      }
      this.$post("/lease/leaseOrderPrice", params).then(res => {
        if (res.code === 0) {
          console.log('this.sonArr', this.sonArr)
          this.sonArr[0].amount = res.data.price // 商品总价
          this.sonArr[2].amount = res.data.fee // 技术服务费
          this.amount = this.sonArr[0].amount + this.sonArr[1].amount + this.sonArr[2].amount // 应付总额(含运费)
        } else {
          this.$message.error(res.msg)
          return
        }
      })
    },
    setViewData(data) {
      let that = this;
      that.disposalOrders(data.orders)
      that.goods_count = data.goods_count
      that.fatherObj = data.amount_detail
      that.sonArr = data.amount_detail.amount_items
      this.rawTotal = that.sonArr[0].amount // 初始“商品总价”
      this.rawExpress = that.sonArr[1].amount // 总运费
      this.rawService = that.sonArr[2].amount // 技术服务费
      that.amount = data.amount; // 应付总额(含运费)
    },
    //自定义租赁时间
    addTenancy(val){
      this.addDaysBtn.push({
        num_days: Number(val)
      })
    },
    // 去支付
    payment() {
      if(!this.isChoose){
        this.$message.error('支付前请先选择租期');
      }   
      if(!this.checked){
        this.$message.error('支付前请先阅读并同意《租赁协议》');
        return
      }
      let that = this;
      for (let i = 0; i < that.orders.length; i++) {
        const order = that.orders[i];
        if (order.address == null || order.address.id == 0) {
          this.$message.error("请选择收货地址");
          return;
        }
        if (order.shipping_method_id == 0) {
          this.$message.error("请选择配送方式");
          return;
        }
      }
      this.orderConfirm();
    },
    // 订单提交
    orderConfirm() {
      let that = this;
      let para = {
        orders: that.orders,
        buy_id: that.buy_id,
        lease_tenancy_terms_id: this.currentTenancyId,
        num_days: this.currentTenancyDays,
      };
      this.$post("/lease/confirm", para).then((res) => {
        if (res.code === 0) {
          this.$router.push({
            path: "payment",
            query: {
              para: res.data.order_ids
            }
          })
        }else{
          this.$message.error(res.msg)
        }
      });
    },
    //根据商品id获取租期
    getTenancy(){
      let that = this;
      let para = {
        product_id: that.product_id
      };
      this.$post("/lease/getLeaseTenancyTermsByProductId", para).then(function (res) {
        if (res.code == 0) {
          that.tenancyList = res.data
          console.log('that.tenancyList[0]',that.tenancyList[0]);
          let x = that.tenancyList[0].num_days
          let y = that.tenancyList[0].preferential_ratio
          let z = that.tenancyList[0].id
          that.onGetPrice(x, y, z)
          that.isChoose = that.tenancyList[0]?.id
        } else {
          this.$message.error(res.msg)
        }
      }).catch(function (res) {
        console.log(res);
      });
    },
    // 初始化数据
    async initCart() {
      let res1 = await this.$get("/bill/findTradeSetting")
      this.setting = res1.data.setting.value
      let para = {
        "buy_id": this.buy_id
      };
      let res2 = await this.$post("/trade/checkout", para)
      if (res2.code == 0) {
        this.sku_id = res2.data.orders[0].order_items[0].sku_id
        this.currentCount = res2.data.goods_count
        this.setViewData(res2.data);
        this.disposalOrders(res2.data.orders);
      } else {
        this.$message.error(res2.msg)
      }
    },
    // 处理 默认配送方式
    disposalOrders(orders) {
      let that = this;
      orders.forEach(item => {
        if (item.shipping_method_id === 0) {
          item.shipping_method_id = item.shipping_methods[0].id
        }
      })
      that.orders = orders;
    },
    //组装地址
    assemblyAddress(address) {
      let addressStr = "";
      if (!address) {
        return "请选择收货地址";
      }
      addressStr = address.realname + "  " + address.mobile + "  " + address.province + "  " + address.city + "  " + address.county + "  " + address.town + "  " + address.detail;
      return addressStr;
    },

    //自定义租赁时间
    openDefinition(){
      this.$refs.leaseTenancyById.dialogVisible = true;
    },

    // 打开新增收货人
    openRecipientsDialogById(key) {
      this.tempKey = key;
      this.$refs.recipientsDialogById.dialogIsShow = true;
      this.$refs.recipientsDialogById.isEditAddress = false;
      this.$refs.recipientsDialogById.title = "添加收货地址";
      this.$refs.recipientsDialogById.initFormData();
    },
    addAddress() {
      this.openRecipientsDialogById(this.tempKey)
    },
    // 打开选择收货地址
    openSelectAddressById(item) {
      this.tempKey = item.key;
      this.$refs.selectAddressById.isShow = true;
      if (item.address) {
        this.$refs.selectAddressById.initAddress(item.address.id);
      } else {
        this.$refs.selectAddressById.initAddress();
      }
    },

    //新增地址ById
    callBackNewAddressById(item) {
      this.openQrBoxById(item);
    },

    // 打开确认框
    openQrBoxById(item) {
      let that = this;
      if (item == null) {
        return;
      }
      that.orderAddressChange(item);
    },

    //物流类型切换
    shippingMethodsChange(item) {
      let that = this;

      let orderItemsTemp = item.order_items;
      let orderItemsPara = [];
      orderItemsTemp.forEach(element => {
        orderItemsPara.push(
          {
            shopping_cart_id: element.shopping_cart_id
          }
        )
      });

      let orders = [];
      let element = {
        shipping_method_id: item.shipping_method_id,
        key: item.key,
        order_items: orderItemsPara
      }

      orders.push(element);

      let para = {
        "orders": orders,
        "buy_id": that.buy_id
      };
      that.$post("/trade/checkout", para).then(function (res) {
        if (res.code == 0) {
          //复位
          that.tempKey = "";
          that.setViewData(res.data);
        } else {
          this.$message.error(res.msg)
        }
      }).catch(function (res) {
        console.log(res);
      });
    },

    //修改地址
    orderAddressChange(item) {
      let that = this;
      if (!that.tempKey) {
        return;
      }

      let orderItemsTemp = [];
      that.orders.forEach(element => {
        if (element.key == that.tempKey) {
          orderItemsTemp = element.order_items;
        }
      });

      let orderItemsPara = [];
      orderItemsTemp.forEach(element => {
        orderItemsPara.push(
          {
            shopping_cart_id: element.shopping_cart_id
          }
        )
      });

      let orders = [];
      let element = {
        address_id: item.id,
        key: that.tempKey,
        order_items: orderItemsPara
      }

      orders.push(element);


      let para = {
        "orders": orders,
        "buy_id": that.buy_id
      };
      that.$post("/trade/checkout", para).then(function (res) {
        if (res.code == 0) {
          //复位
          that.tempKey = "";
          that.setViewData(res.data);
        } else {
          this.$message.error(res.msg)
        }
      }).catch(function (res) {
        console.log(res);
      });
    },

    // 发票信息弹框
    openInvoiceDialog() {
      this.$refs["invoiceDialogById"].dialogVisible = true;
      this.$nextTick(() => {
        this.$refs["invoiceDialogById"].setting = this.setting;
        this.$refs["invoiceDialogById"].invoiceType = this.setting.bill_type[0]
      })
    },

    // 修改发票信息
    editInvoice(invoice) {
      // 暂时没有提交 收票人手机号，收票人邮箱
      let { type, detailType, accountType, personName, accountNumber,
        companyName, registerAddress, registerPhone, bank, bankAccount, email, mobile, address_id
      } = invoice;
      this.$refs["invoiceDialogById"].dialogVisible = false;
      this.orders.forEach(item => {
        if (!item.order_bill) item.order_bill = {};
        // detailType  1.商品明细 2.商品类别 3.不开发票 
        if (invoice.detailType || invoice.type == 2) {
          item.order_bill.type = type;
          item.order_bill.account_type = accountType;
          item.order_bill.person_name = personName;
          item.order_bill.company_code = accountNumber;
          item.order_bill.detail_type = detailType;
          item.order_bill.company_name = companyName;
          item.order_bill.sign_address = registerAddress;
          item.order_bill.sign_mobile = registerPhone;
          item.order_bill.opening_bank = bank;
          item.order_bill.bank_account = bankAccount;
          item.order_bill.email = email;
          item.order_bill.mobile = mobile + '';
          item.order_bill.address_id = address_id;
          this.$forceUpdate();
        }
      });
    },

  }
};