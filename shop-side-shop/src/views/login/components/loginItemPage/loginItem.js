import { mapGetters } from "vuex"
import UserProtocolDialog from "../userProtocolDialog.vue"
export default {
    name: "loginItem",
    components: { UserProtocolDialog },
    data() {
        return {
            title: '登录',
            logoUrl: "",
            // 背景图片
            bgImg: 'background: url(' + require('../../../../assets/images/loginbg.png') + ') no-repeat center center'
        }
    },
    computed: {
        ...mapGetters("home", ["getFramework"])
    },
    mounted() {
        
        switch (this.$route.name) {
            case 'Login':
                this.title = '登录'
                break;
            case 'Register':
                this.title = '注册'
                break;
        }
        setTimeout(() => {
            this.logoUrl = this.getFramework.header.logo_src
        }, 200)
    },
    methods: {
        // 打开用户协议
        openUserProtocol() {
            this.$refs.userProtocolDialog.isShow = true
            this.$refs.userProtocolDialog.getAgreement()
        }
    }
}
