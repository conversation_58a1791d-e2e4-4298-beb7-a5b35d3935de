import LoginItem from '../components/loginItem'
// import FindPasswordDialog from "../components/findPasswordDialog";
import ForgetPasswordDialog from "../components/forgetPasswordDialog";
import "quill/dist/quill.snow.css";
export default {
    name: "Login",
    data() {
        return {
            isShow: false,
            audit_agreement:"",
            loginForm: {
                isAutoLogin: false,
                username: '',
                captcha_code: null,
                password: "",
                captcha_key: ""
            },
            countDown: 60,
            codeIsShow: true,
            loginType: 1, // 登录方式 1 = 手机号登录 2 = 账号密码登录
        }
    },
    components: {LoginItem, ForgetPasswordDialog},
    filters: {
        loginTypeToText: function (val) {
            let s = "";
            switch (val) {
                case 1:
                    s = "账号密码"
                    break;
                case 2:
                    s = "手机号"
                    break;
            }
            return s
        }
    },
    mounted() {
        this.$fn.logOut();
    },
    methods: {
        // 打开忘记密码
        openForgetPassword() {
            this.$refs.forgetPasswordDialog.isShow = true
        },
        // 打开找回密码
        openFindPassword() {
            this.$refs.findPasswordDialog.isShow = true
        },
        // 切换登录方式
        switchoverLogin() {
            if (this.loginType === 1) {
                this.loginType = 2
            } else if (this.loginType === 2) {
                this.loginType = 1
            }
        },
        jump() {
            let query = {}
            if (this.$route.query.shared) {
                query.shared = this.$route.query.shared
            }
            this.$router.push({
                path: '/register',
                query
            })
        },
        // 发送验证码
        sendCode() {
            if (!this.loginForm.username) {
                this.$message.error("请填写手机号!")
            } else {
                this.codeIsShow = false
                this.$post("/user/sendCode", {
                    mobile: this.loginForm.username,
                    type: "login"
                }).then(r => {
                    if (r.code === 0) {
                        this.loginForm.captcha_key = r.data.captcha_key
                        // this.loginForm.captcha_code = r.data.captcha_code
                        this.codeIsShow = false
                        let time = setInterval(() => {
                            if (this.countDown <= 0) {
                                clearInterval(time)
                                this.countDown = 60
                                this.codeIsShow = true
                            } else {
                                this.countDown = this.countDown - 1
                            }
                        }, 1000)
                    } else {
                        this.codeIsShow = true
                        this.$message.error(r.msg)
                    }
                })
            }
        },
        // 关闭账号注册审核提示弹窗
        dialogClose(){
            this.isShow = false
            this.audit_agreement = ""
        },
        //登录
        login() {
            let that = this;
            if (!that.loginForm.username) {
                that.$message.error("请填写账号");
                return;
            }
            if (this.loginType === 1) {
                if (!that.loginForm.captcha_code) {
                    that.$message.error("请填写验证码");
                    return;
                }
            } else if (that.loginType === 2) {
                if (!that.loginForm.password) {
                    that.$message.error("请填写密码");
                    return;
                }
            } else {
                that.$message.error("系统未知错误");
                return;
            }

            let para = {
                "captcha": "",
                "captchaId": "",
                "userName": that.loginForm.username,
            };
            let submitUrl = ""
            if (that.loginType === 1) {
                para.captcha_code = that.loginForm.captcha_code
                para.captcha_key = that.loginForm.captcha_key
                submitUrl = "/user/loginWithCode"
            }
            if (that.loginType === 2) {
                para.password = that.loginForm.password
                submitUrl = "/user/login"
            }
            if (this.$route.query.shared) {
                para.pid = parseInt(this.$fn.decode(this.$route.query.shared))
            }
            that.$post(submitUrl, para).then(function (res) {
                if (res.code == 0) {
                    // 审核中
                    if (res.data.status === 0) {
                        that.isShow = true
                        that.$get("/user/findSettingAccord").then(settingRes=>{
                            that.audit_agreement = settingRes.data.audit_agreement
                        })
                        // that.$message.warning(res.msg)
                    } else {
                        //存储token
                        that.$ls.save("token", res.data.token);
                        that.$ls.save("user", res.data.user);
                        that.$message.success(res.msg);
                        that.$router.push('/');
                        that.loginForm = {
                            isAutoLogin: false,
                            username: '',
                            captcha_code: null,
                            captcha_key: ""
                        }
                    }
                } else {
                    that.$message.error(res.msg);
                }
            }).catch(function (res) {
                console.log(res.msg);
            });
        }
    }
}
