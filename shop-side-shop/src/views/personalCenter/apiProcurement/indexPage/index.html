<div class="apiPro-box bgw">
    <p class="title-p f fac fjsb">
        <span>API采购</span>
        <span v-if="reject.isReject" class="color-red">您的申请已被驳回,请修改后重新提交</span>
        <span v-if="status === 0" class="color-red">申请审核中,如需加急,请联系客服!</span>
    </p>
    <p class="mt10">可通过API对接已有商城系统,实现API商品同步、订单同步。</p>
    <!-- 步骤部分 -->
    <div class="step-box f fac">
        <div class="step-item active">
            <div class="f fac">
                <div class="step-num-div">1</div>
                <div class="divider-box" :class="step >= 2 ? 'bg-green' : ''"></div>
            </div>
            <p class="step-p">申请API</p>
        </div>
        <div class="step-item" :class="step >= 2 ? 'active' : ''">
            <div class="f fac">
                <div class="step-num-div">2</div>
                <div class="divider-box" :class="step >= 3 ? 'bg-green' : ''"></div>
            </div>
            <p class="step-p">等待审核</p>
        </div>
        <div class="step-item" :class="step >= 3 ? 'active' : ''">
            <div class="f fac">
                <div class="step-num-div">3</div>
            </div>
            <p class="step-p">申请成功</p>
        </div>
    </div>
    <div class="my-swiper-box" v-show="step !== 3">
        <div class="swiper-container">
            <div class="swiper-wrapper">
                <div class="swiper-slide" :class="formData.appLevelId === item.id ? 'level-active' : ''"
                    v-for="(item,index) in levelList" @click="selectLevel(item)" :key="item.id">
                    <div class="item-top">{{ item.levelName }}</div>
                    <div class="text-center">
                        <p>{{ item.serverRadio / 100 }}%</p>
                        <p>技术服务费比例</p>
                        <br>
                        <p>{{ item.numMax }}</p>
                        <p>选款数量限制</p>
                    </div>
                    <div class="item-bottom" :class="formData.appLevelId === item.id ? 'bg-show' : ''"></div>
                </div>
            </div>
        </div>
        <div v-if="levelList.length > 5" class="arrows-button swiper-button-prev swiper-button-black"></div>
        <div v-if="levelList.length > 5" class="arrows-button swiper-button-next swiper-button-black"></div>
    </div>
    <div v-if="step === 3" class="mt20">
        <p class="title-p">应用信息</p>
        <!--        <p class="mt10">应用等级: 等级名称</p>
                <p class="mt10">应用描述: 技术服务费: 10%,选品限制: 1000款</p>
                <p class="mt10">appKey: 1231asdasd <a href="javascript:;" class="color-red ml10">复制</a></p>
                <p class="mt10">appSecret: 123123 <a href="javascript:;" class="color-red ml10">复制</a> <a href="javascript:;" class="color-blue ml10">重新生成</a></p>-->
        <el-form :model="formData" label-width="120px" class="mt20">
            <el-form-item label="应用等级:">
                <span>{{ formData.applicationLevel.levelName }}</span>
            </el-form-item>
            <el-form-item label="应用描述:">
                <span>技术服务费: {{ formData.applicationLevel.serverRadio / 100 }}%, 选品限制: {{
                    formData.applicationLevel.numMax }}款</span>
            </el-form-item>
            <el-form-item label="商城:">
                <el-radio-group v-model="formData.is_multi_shop">
                    <el-radio :label="2">单商城</el-radio>
                    <el-radio class="ml20" :label="1">多商城</el-radio>
                  </el-radio-group>
            </el-form-item>
            <el-form-item v-if="formData.is_multi_shop == 1">
                <el-button style="width: 68px; height: 24px; padding: 0 20px;" @click="addAddress">新增</el-button>
            </el-form-item>
            <el-form-item  v-if="formData.is_multi_shop == 1">
                <div v-for="item in application_shops" style="background-color: #F5F6F8;" class="f fac fjsb mb10">
                    <div class="mt20 mb20" style="margin-left: 26px;">
                        <p>appKey: <span class="ml10 mr10">application{{ item.application_id }}_{{ item.id }}</span><a style="color: #fe0f0f;" href="javascript:;" @click="copyKey(item)">复制</a></p>
                        <p>appSecret: <span class="ml10">{{ item.app_secret.substring(0, 10) }}{{ item.app_secret.substring(10, 21).replace(/./g, '*') }}{{ item.app_secret.substring(21) }}</span><a class="ml10 mr10" style="color: #fe0f0f;" href="javascript:;" @click="copySecret(item)">复制</a><a style="color: #4697ED;" href="javascript:;" @click="getKey(item)">重新生成</a></p>
                        <p>回调地址: <span class="ml10">{{ item.callback_link }}</span></p>
                    </div>
                    <div style="margin-right: 26px;">
                        <el-button style="background-color: #29BA9C; color: #ffffff; width: 80px; height: 30px; padding: 0 20px;" @click="deleteAddress(item)">删除</el-button>
                    </div>
                </div>
            </el-form-item>
            <el-form-item v-if="formData.is_multi_shop == 2" label="appKey:">
                <p>application{{ formData.id }} <a href="javascript:;" class="color-red ml10"
                        @click="$fn.copyFn(`application${formData.id}`)">复制</a></p>
            </el-form-item>
            <el-form-item v-if="formData.is_multi_shop == 2" label="appSecret:">
                <p>{{ formData.appSecret | formatSecret }} <a href="javascript:;" class="color-red ml10"
                        @click="$fn.copyFn(formData.appSecret)">复制</a> <a href="javascript:;" class="color-blue ml10"
                        @click="anewReactAppSecret">重新生成</a>
                </p>
            </el-form-item>
            <el-form-item v-if="formData.is_multi_shop == 2" label="回调地址:" prop="callBackLink">
                <el-input v-model="formData.callBackLink" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="抖音cps回调地址:" prop="callBackLinkCps">
                <el-input v-model="formData.callBackLinkCps" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="聚推联盟回调地址:" prop="callBackLinkJhCps">
                <el-input v-model="formData.callBackLinkJhCps" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="IP白名单:" prop="ipList">
                <el-input type="textarea" v-model="formData.ipList" :rows="6" placeholder="请输入"></el-input>
                <p class="color-gray">
                    多个IP之间请用英文逗号间隔
                </p>
            </el-form-item>
        </el-form>
        <div class="text-center">
            <el-button class="confirm-btn" @click="saveSetting">保存设置</el-button>
        </div>
    </div>
    <p class="title-p mt20">申请说明</p>
    <div class="ql-editor mt10" v-html="agreement"></div>
    <p class="title-p mt20" v-if="reject.isReject">驳回原因</p>
    <div class="mt10" v-if="reject.isReject">{{ reject.reason }}</div>
    <p class="title-p mt20">申请资料</p>
    <el-form :disabled="step >= 2 && !reject.isReject" :model="formData" :rules="rules" ref="form" label-width="130px"
        class="mt20">
        <el-form-item label="公司名称:" prop="companyName">
            <el-input v-model="formData.companyName" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="公司介绍:" prop="companyIntro">
            <el-input v-model="formData.companyIntro" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item :error="areaError">
            <span slot="label"><span class="required-color">*</span> 省市区:</span>
            <el-cascader clearable class="w100" size="large" :options="areaOption" v-model="selectedOptions"
                @change="handleChangeSearch">
            </el-cascader>
        </el-form-item>
        <el-form-item label="详细地址:" prop="address">
            <el-input v-model="formData.address" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="信用代码:" prop="creditCode">
            <el-input v-model="formData.creditCode" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="营业执照:" prop="businessLicense">

            <el-upload class="avatar-uploader" :show-file-list="false" :action="path+'/common/upload'"
                :headers="{'x-token':''}" :on-success="handleBusinessImgSuccess" :before-upload="beforeAvatarUpload"
                accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                <img v-if="formData.businessLicense" :src="formData.businessLicense" class="upload-img">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
        </el-form-item>
        <el-form-item label="法人姓名:" prop="legalPersonName">
            <el-input v-model="formData.legalPersonName" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="法人身份证号码:" prop="idCardNumber">
            <el-input v-model="formData.idCardNumber" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="法人身份证正面:" prop="idCardFront">

            <el-upload class="avatar-uploader" :show-file-list="false" :action="path+'/common/upload'"
                :headers="{'x-token':''}" :on-success="handleidCardFrontImgSuccess" :before-upload="beforeAvatarUpload"
                accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                <img v-if="formData.idCardFront" :src="formData.idCardFront" class="upload-img">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
        </el-form-item>
        <el-form-item label="法人身份证背面:" prop="idCardBackend">

            <el-upload class="avatar-uploader" :show-file-list="false" :action="path+'/common/upload'"
                :headers="{'x-token':''}" :on-success="handleidCardBackendImgSuccess"
                :before-upload="beforeAvatarUpload"
                accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                <img v-if="formData.idCardBackend" :src="formData.idCardBackend" class="upload-img">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
        </el-form-item>
        <el-form-item label="联系人姓名:" prop="contactsName">
            <el-input v-model="formData.contactsName" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="联系人电话:" prop="contactsPhontnumber">
            <el-input v-model="formData.contactsPhontnumber" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="联系人邮箱:" prop="contactsEmail">
            <el-input v-model="formData.contactsEmail" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="采购端名称:" prop="appName">
            <el-input v-model="formData.appName" placeholder="请输入"></el-input>
        </el-form-item>
        <div class="text-center">
            <el-button class="confirm-btn" v-if="step === 1 || reject.isReject" @click="$clicks(handleSubmitClick)">立即提交
            </el-button>
            <el-button class="confirm-btn" disabled v-else-if="step === 2 && !reject.isReject">审核中</el-button>
        </div>
    </el-form>
    <agreement-dialog ref="agreementDialog"></agreement-dialog>
    <el-dialog
        title="新增"
        :visible.sync="dialogVisible"
        width="30%"
    >
        <el-form
                ref="addressData"
                :model="addressData"
                :rules="rules"
                label-width="100px"
        >
            <el-form-item label="商城名称:" prop="app_shop_name">
                <el-input
                    v-model="addressData.app_shop_name"
                    clearable
                    placeholder="请输入"
                ></el-input>
            </el-form-item>
            <el-form-item label="回调地址:" prop="callBackLink">
                <div class="f fac">
                    <el-input
                        v-model="addressData.callBackLink"
                        clearable
                        placeholder="请输入"
                    ></el-input>
                    <el-button style="background-color: #29BA9C; color: #ffffff;" @click="verifyfun"
                        >校验</el-button
                    >
                </div>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button style="background-color: #29BA9C;color: #ffffff;" @click="createAddress">确定</el-button>
            <el-button @click="dialogVisible = false">取消</el-button>
        </span>
    </el-dialog>
</div>