import AgreementDialog from '@/components/agreementDialog';
import 'quill/dist/quill.snow.css';
import { regionData } from 'element-china-area-data';
import verify from '@/utils/verify';
import { confirm } from '@/decorators/decorators';

export default {
    name: 'apiProcurementIndex',
    components: { AgreementDialog },
    filters: {
        formatSecret: function (str) {
            if (str) {
                let leng = str.length;
                let cent = Math.ceil(leng / 2);
                return str.replace(str.slice(cent - 5, cent + 5), '**********');
            }
        },
    },
    data() {
        return {
            dialogVisible: false, // 多商城弹层
            addressData: {
                app_shop_name: '', // 商城名称
                callBackLink: '', // 回调地址
            },
            reject: {
                isReject: false, // 是否被驳回
                reason: '',
            },
            status: null, // 审核状态
            agreementData: {},
            // 地区选择错误提示
            areaError: '',
            areaOption: [], // 地区数据
            selectedOptions: [],
            // 1 = 申请API 2 = 等待审核 3 = 申请成功
            step: 1,
            levelList: [],
            agreement: '暂无说明', // 申请说明
            formData: {
                callBackLink: '',
                callBackLinkCps: '',
                callBackLinkJhCps: '',
                ipList: '',
                appName: '', // 采购端名称
                appSecret: '',
                appLevelId: null, // 采购等级
                companyName: '', // 公司名称
                companyIntro: '', // 公司简介
                provinceId: '', // 省
                cityId: '', // 市
                districtId: '', // 区县
                address: '', // 详细地址
                creditCode: '', // 信用代码
                businessLicense: '', // 营业执照
                legalPersonName: '', // 法人姓名
                idCardNumber: '', // 法人身份证号码
                idCardFront: '', // 身份证正面
                idCardBackend: '', // 身份证背面
                contactsName: '', // 联系人姓名
                contactsPhontnumber: '', // 联系人电话
                contactsEmail: '', // 联系人邮箱
                is_multi_shop: 2, // 2-单商城 1-多商城
            },
            application_id: '', // 多商城ID
            application_shops: [], // 多商城数组
            path: this.$path,
            rules: {
                app_shop_name: {
                    required: true,
                    message: '请输入商城名称',
                    trigger: 'blur',
                },
                callBackLink: {
                    required: true,
                    message: '请输入回调地址',
                    trigger: 'blur',
                },
                companyName: {
                    required: true,
                    message: '请输入公司名称',
                    trigger: 'blur',
                },
                address: {
                    required: true,
                    message: '请输入详细地址',
                    trigger: 'blur',
                },
                creditCode: [
                    {
                        required: true,
                        message: '请输入信用代码',
                        trigger: 'blur',
                    },
                    {
                        validator: (rule, value, callback) => {
                            if (
                                verify.checkCreditCode15(value) ||
                                verify.checkCreditCode18(value)
                            ) {
                                callback();
                            } else {
                                return callback(
                                    new Error('信用代码格式不正确'),
                                );
                            }
                        },
                        trigger: 'blur',
                    },
                ],
                businessLicense: {
                    required: true,
                    message: '请上传营业执照',
                    trigger: 'blur',
                },
                legalPersonName: {
                    required: true,
                    message: '请输入法人姓名',
                    trigger: 'blur',
                },
                idCardNumber: [
                    {
                        required: true,
                        message: '请输入法人身份证号码',
                        trigger: 'blur',
                    },
                    {
                        validator: (rule, value, callback) => {
                            if (verify.checkCardId(value)) {
                                callback();
                            } else {
                                return callback(
                                    new Error('身份证号码格式错误'),
                                );
                            }
                        },
                        trigger: 'blur',
                    },
                ],
                idCardFront: {
                    required: true,
                    message: '请上传身份证正面照',
                    trigger: 'blur',
                },
                idCardBackend: {
                    required: true,
                    message: '请上传身份证背面照',
                    trigger: 'blur',
                },
                contactsName: {
                    required: true,
                    message: '请输入联系人姓名',
                    trigger: 'blur',
                },
                contactsPhontnumber: [
                    {
                        required: true,
                        message: '请输入联系人电话',
                        trigger: 'blur',
                    },
                    {
                        validator: (rule, value, callback) => {
                            if (verify.checkPhone(value)) {
                                callback();
                            } else {
                                return callback(new Error('手机号格式不正确'));
                            }
                        },
                        trigger: 'blur',
                    },
                ],
                contactsEmail: {
                    validator: (rule, value, callback) => {
                        if (value) {
                            if (verify.checkEmail(value)) {
                                callback();
                            } else {
                                return callback(new Error('邮箱格式不正确'));
                            }
                        } else {
                            callback();
                        }
                    },
                    trigger: 'blur',
                },
            },
        };
    },
    mounted() {
        this.init();
    },
    methods: {
        // 新增多商城回调地址
        addAddress() {
            this.dialogVisible = true;
            this.addressData.app_shop_name = '';
            this.addressData.callBackLink = '';
            this.$refs.addressData.resetFields();
        },
        // 校验
        async verifyfun() {
            if (!this.addressData.callBackLink) {
                this.$message.error('请输入回调地址');
                return;
            }
            const res = await this.$post('/application/validateCallback', {
                link: this.addressData.callBackLink,
            });
            if (res.code == 0) {
                this.$message.success(res.msg);
            } else {
                this.$message.error(res.msg);
            }
        },
        // 创建多商城
        async createAddress() {
            const data = {
                shop_name: this.addressData.app_shop_name,
                callback_link: this.addressData.callBackLink,
                application_id: parseInt(this.application_id),
            };
            if (!this.addressData.app_shop_name) {
                this.$message.error('请输入商城名称');
                return;
            }
            if (!this.addressData.callBackLink) {
                this.$message.error('请输入回调地址');
                return;
            }
            const res = await this.$post(
                '/applicationShop/createApplicationShop',
                data,
            );
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.dialogVisible = false;
                this.init();
            }
        },
        // 删除多商城
        @confirm('提示', '确认要删除这条信息吗?')
        async deleteAddress(item) {
            const data = {
                application_id: parseInt(item.application_id),
                id: parseInt(item.id),
                shop_name: item.shop_name,
                callback_link: item.callback_link,
                app_secret: item.app_secret,
            };
            console.log('asdasdas', data);
            const res = await this.$del(
                '/applicationShop/deleteApplicationShop',
                data,
            );
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.init();
            }
        },
        // 多商城复制Key
        copyKey(item) {
            this.$fn.copyFn(
                'application' + item.application_id + '_' + item.id,
            );
        },
        // 多商城复制Secret
        copySecret(item) {
            this.$fn.copyFn(item.app_secret);
        },
        // 多商城重新生成密钥
        async getKey(item) {
            const params = {
                id: parseInt(item.application_id),
                shop_id: parseInt(item.id),
            };
            this.$confirm('是否确认重新生成?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                this.$get('/application/createShopKeySecret', params).then(
                    (res) => {
                        if (res.code === 0) {
                            this.$message.success(res.msg);
                            this.init();
                        } else {
                            this.$message.error(res.msg);
                        }
                    },
                );
            });
        },
        // 重新生成appSecret
        anewReactAppSecret() {
            this.$confirm('是否确认重新生成?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    this.$get('/application/createApplicationKeySecret', {
                        id: this.formData.id,
                    }).then((res) => {
                        if (res.code === 0) {
                            this.formData.appSecret = res.data.appSecret;
                        } else {
                            this.$message.error(res.msg);
                        }
                    });
                })
                .catch(() => {});
        },
        // 保存设置
        saveSetting() {
            this.$put('/application/updateApplication', this.formData).then(
                (res) => {
                    if (res.code === 0) {
                        this.$message.success(res.msg);
                    } else {
                        this.$message.error(res.msg);
                    }
                },
            );
        },
        // 选中地区数据
        handleChangeSearch(value) {
            this.areaError = '';
            this.formData.provinceId = parseInt(
                value[0] == null ? '0' : value[0],
            ); // 省
            this.formData.cityId = parseInt(value[1] == null ? '0' : value[1]); // 市
            this.formData.districtId = parseInt(
                value[2] == null ? '0' : value[2],
            ); // 区县
        },
        // 校验
        checkFormData() {
            // if (!this.formData.appLevelId) {
            //     this.$message.error("请选择申请等级!")
            //     return false
            // }
            this.areaError = '';
            if (this.selectedOptions.length === 0) {
                this.areaError = '请选择省市区';
                return false;
            }

            return true;
        },
        // 提交申请
        handleSubmitClick() {
            if (!this.checkFormData()) {
                return false;
            }
            this.$refs.form.validate((valid) => {
                if (!valid) return false;
                if (this.formData.id) {
                    delete this.formData.id;
                }
                this.$post(
                    '/application/createApplicationApply',
                    this.formData,
                ).then((res) => {
                    if (res.code === 0) {
                        this.$message.success(res.msg);
                        this.step = 2;
                        this.reject.isReject = false;
                        this.reject.reason = '';
                    } else {
                        this.$message.error(res.msg);
                    }
                });
            });
        },
        // 获取设置
        async getSetting() {
            let res = await this.$get('/application/findApplicationSetting');
            if (res.code === 0) {
                // 获取协议是否开始,展示协议
                let agreementData = {
                    is_open_agreement: res.data.setting.value.is_open_agreement,
                    agreement: res.data.setting.value.agreement,
                };
                this.agreementData = agreementData;
                this.agreement = res.data.setting.value.apply_desc;
            } else {
                this.$message.error(res.msg);
            }
        },
        // 协议部分操作
        agreementFn() {
            if (this.agreementData.is_open_agreement === 1) {
                this.$refs.agreementDialog.isShow = true;
                this.$nextTick(() => {
                    this.$refs.agreementDialog.agreement =
                        this.agreementData.agreement;
                });
            }
        },
        // 获取审核状态
        async getApplyStatus() {
            let res = await this.$get('/application/getApplicationApply');
            if (res.code === 0) {
                let data = res.data.reapplication;
                this.application_id = res.data.reapplication.application_id;
                this.application_shops =
                    res.data.reapplication.application_shops;
                this.status = data.status;
                switch (data.status) {
                    case 0: // 未审核
                        this.step = 2;
                        break;
                    case 1: // 通过
                        this.step = 3;
                        break;
                    case 2: // 驳回
                        this.step = 2;
                        this.reject.isReject = true;
                        this.reject.reason = data.reason;
                        break;
                }
                this.selectedOptions.push(
                    data.application.provinceId.toString(),
                );
                this.selectedOptions.push(data.application.cityId.toString());
                this.selectedOptions.push(
                    data.application.districtId.toString(),
                );
                this.setFrom(data.application);
            } else {
                this.step = 1;
                this.agreementFn();
            }
        },
        //赋值表单
        setFrom(val) {
            const keys = Object.keys(val);
            const that = this;
            keys.forEach((element) => {
                that.formData[element] = val[element];
            });
            if (!that.formData.is_multi_shop) {
                that.formData.is_multi_shop = 2;
            }
        },
        // 获取应用等级
        async getLevelList() {
            let res = await this.$get('/application/getApplicationLevelList');
            if (res.code === 0) {
                this.levelList = res.data.list;
                this.levelList.sort((a, b) => a.sort - b.sort);
                console.log(this.levelList);
                this.formData.appLevelId = res.data.list.length
                    ? res.data.list[0].id
                    : null;
                this.$nextTick(() => {
                    this.initSwiper();
                });
            } else {
                this.$message.error(res.msg);
            }
        },
        handleBusinessImgSuccess(res) {
            if (res.code === 0) {
                this.formData.businessLicense = res.data.file.url;
            } else {
                this.$message.error(res.msg);
            }
        },
        // 身份证正面
        handleidCardFrontImgSuccess(res) {
            if (res.code === 0) {
                this.formData.idCardFront = res.data.file.url;
            } else {
                this.$message.error(res.msg);
            }
        },
        // 身份证背面
        handleidCardBackendImgSuccess(res) {
            if (res.code === 0) {
                this.formData.idCardBackend = res.data.file.url;
            } else {
                this.$message.error(res.msg);
            }
        },
        beforeAvatarUpload(file) {
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                this.$message.error('上传图片大小不能超过 10MB!');
            }
            return isLt10M;
        },
        selectLevel(item) {
            this.formData.appLevelId = item.id;
        },
        async init() {
            /* let appStatusRes = await this.$get("/application/getApplicationStatus")
            if (appStatusRes.code === 0) {
                this.$alert('您已注册采购端', '提示', {
                    confirmButtonText: '确定',
                    showClose: false,
                    callback: action => {
                        this.$router.back()
                    }
                });
            } else {
                await this.getSetting()
                await this.getApplyStatus()
                await this.getLevelList()
                this.areaOption = regionData
            } */
            await this.getSetting();
            await this.getApplyStatus();
            await this.getLevelList();
            this.areaOption = regionData;
        },
        initSwiper() {
            new this.Swiper('.swiper-container', {
                slidesPerView: 5,
                spaceBetween: 30,
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
            });
        },
    },
};
