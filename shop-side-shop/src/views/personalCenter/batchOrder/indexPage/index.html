<!-- 批量下单 -->
<div class="batchOrder-box bgw">
    <p class="title-p">批量下单</p>
    <div class="item f" v-if="!progressIsShow && step === 1">
        <i class="iconfont icon-pc_download"></i>
        <div>
            <p>下载批量下单表格</p>
            <el-button @click="download">下载批量下单表格模板</el-button>
        </div>
    </div>
    <div class="item item2 f" v-if="step === 1">
        <i v-if="!progressIsShow && step === 1" class="iconfont icon-pc_update"></i>
        <div>
            <template v-if="!progressIsShow && step === 1">
                <p>填写完信息后上传表格 <span>上传符合模板的Excel文件后,可为您批量下单</span></p>
                <el-upload
                        class="avatar-uploader"
                        :action="path+'/shoppingcart/uploadExcel'"
                        :headers="{'x-token':$ls.getToken()}"
                        accept=".xls,.xlsx,.csv"
                        :on-progress="uploadProgress"
                        :on-success="handleBatchOrderSuccess" :before-upload="beforeBatchOrderUpload"
                        :show-file-list="false">
                    <el-button>选择文件并上传</el-button>
                    <span class="gs-span">只支持上传小于3M，.xls .xlsx .csv格式的文件</span>
                </el-upload>
                <p class="remark-p">备注：<span>最多只支持500笔订单</span>，导入表格前请认真检查相关的信息，确保收货人、手机号、购买数量等信息正确无误</p>
            </template>
            <div class="progress-box f fac" v-if="progressIsShow && step === 1">
                <i class="iconfont icon-excel"></i>
                <div class="f1">
                    <p>{{ uploadName }}</p>
                    <el-progress :stroke-width="10" :percentage="uploadPercent" color="#FF3838"></el-progress>
                </div>
            </div>
        </div>
    </div>
    <!-- v-if="step === 2 && uploadReturnData.list.length > 0"-->
    <div v-if="step === 2 && uploadReturnData.list.length > 0" class="batchOrderTable-div">
        <div class="mt20 f fac fjc result-div">
            <i class="el-icon-success"></i>
            <p class="result-text">表格导入成功,请核对您的信息,确认无误后点击提交订单即可完成批量下单</p>
        </div>
        <div class="mt20 f fac remark-div">
            <i class="el-icon-warning color-red"></i>
            <p class="color-red ml10">备注: 红色字体显示的是错误的原因,您可点编辑按钮进行修改</p>
        </div>
        <el-table class="mt10" border :data="uploadReturnData.list">
            <el-table-column type="index" label="序号" width="55" align="center"></el-table-column>
            <el-table-column label="商品名称" prop="product_name" align="center" width="90"
                             show-overflow-tooltip></el-table-column>
            <el-table-column label="下单号" align="center">
                <template slot-scope="scope">
                    <span :class="scope.row.purchase_sn_err ? 'color-red' : ''">{{ scope.row.purchase_sn }}</span>
                </template>
            </el-table-column>
            <el-table-column label="第一规格" align="center">
                <template slot-scope="scope">
                    <span>{{ scope.row.sku_name }}</span>
                </template>
            </el-table-column>
            <el-table-column label="第二规格" align="center">
                <template slot-scope="scope">
                    <span>{{ scope.row.sku2_name }}</span>
                </template>
            </el-table-column>
            <el-table-column label="第三方订单编号" align="center">
                <template slot-scope="scope">
                    <span>{{ scope.row.third_order_sn }}</span>
                </template>
            </el-table-column>
            <el-table-column label="收货人姓名" align="center">
                <template slot-scope="scope">
                    <span :class="scope.row.realname_err ? 'color-red' : ''">{{ scope.row.realname }}</span>
                </template>
            </el-table-column>
            <el-table-column label="收货人手机号" align="center">
                <template slot-scope="scope">
                    <span :class="scope.row.mobile_err ? 'color-red' : ''">{{ scope.row.mobile }}</span>
                </template>
            </el-table-column>
            <el-table-column label="收货人省份" align="center">
                <template slot-scope="scope">
                    <span :class="scope.row.province_err ? 'color-red' : ''">{{ scope.row.province }}</span>
                </template>
            </el-table-column>
            <el-table-column label="收货人城市" align="center">
                <template slot-scope="scope">
                    <span :class="scope.row.city_err ? 'color-red' : ''">{{ scope.row.city }}</span>
                </template>
            </el-table-column>
            <el-table-column label="收货人区县" align="center">
                <template slot-scope="scope">
                    <span :class="scope.row.county_err ? 'color-red' : ''">{{ scope.row.county }}</span>
                </template>
            </el-table-column>
            <el-table-column label="收货人详细地址" align="center">
                <template slot-scope="scope">
                    <span>{{ scope.row.detail }}</span>
                </template>
            </el-table-column>
            <el-table-column label="购买数量" align="center">
                <template slot-scope="scope">
                    <span>{{ scope.row.qty }}</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" fixed="right" width="120">
                <template slot-scope="scope">
                    <el-button type="text" class="color-red" @click="edit(scope.row)">编辑</el-button>
                    <el-button type="text" class="color-red" @click="del(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="submit-div mt20">
            <div class="f fac fjsb">
                <div>
                    <span>配送方式:</span>
                    <el-radio-group v-model="formData.shipping_method_id" class="m-radio-group ml10">
                        <el-radio v-for="item in shippingMethodList" :key="item.id" :label="item.id">
                            {{ item.name }}
                        </el-radio>
                    </el-radio-group>
                </div>
                <div class="f fac">
                    <p>
                        共{{ uploadReturnData.list.length }}条数据,其中{{ uploadReturnData.success_count }}条正确,{{ uploadReturnData.fail_count }}条有错误</p>
                    <el-button class="def-button ml10" @click="delErrData">一键删除错误数据</el-button>
                    <el-button class="red-button ml10 mr0" :loading="commitLoad" @click="submitOrder">
                        提交订单
                    </el-button>
                </div>
            </div>
            <p class="color-red text-right mt10">只提交数据正确的订单</p>
        </div>
        <edit-order-dialog ref="editOrderDialog" @reload="reloadOrderlist"></edit-order-dialog>
    </div>
</div>
