import {mapGetters} from "vuex";
import menu from "./menu"

export default {
    name: "PersonalCenterIndexMain",
    data() {
        return {
            menuList: [],
            /**
             * 搜索框部分
             */
            keyword: "", // 输入框
            searchType: 1, // 搜索类型
            searchList: [], // 搜索类型option
            apiProcurementIsShow: true, // API采购是否显示
            agencyApplyIsShow: false, // 申请区域代理是否显示
            distributorIsShow: false, // 分销商管理是否显示
            merchantIsShow: false, // 招商员管理是否显示
            knowledgeIsShow: false, // 知识库菜单是否显示
            cinemaTicketIsShow: false, // 电影票是否显示
        }
    },
    computed: {
        ...mapGetters("home", ["getFramework"]),
    },
    watch: {
        $route(to, from) {
            // console.log(to.name.indexOf('myWallet'),'123123');
        }
    },
    mounted() {
        this.getsWhetherToTurnOnLeaseSearch()
        this.initConfig()
        this.handleMenuHeight()
    },
    methods: {
        // 搜索
        handleSearchClick() {
            let url = ""
            // 1 = 商品搜索页 2 = 租赁商品列表
            switch (this.searchType) {
                case 1:
                    url = "/searchList"
                    break;
                case 2:
                    url = "/lease/goodsList"
                    break;
                default:
                    url = "/searchList"
                    break;
            }
            this.$router.push(
                {path: url, query: {keyword: this.keyword, k: Math.ceil(Math.random() * 100), t: this.searchType}}
            )
        },
        // 获取是否开启租赁搜索
        async getsWhetherToTurnOnLeaseSearch() {
            this.searchList = [
                {
                    title: "商品",
                    value: 1
                }
            ]
            const {code, data} = await this.$get('/lease/getIsLease')
            if (code === 0) {
                if (data.is_open === 1) {
                    this.searchList.push({
                        title: "租赁",
                        value: 2
                    })
                }
            }
        },
        // 处理左侧菜单高度
        handleMenuHeight() {
            for (let i = 0; i < this.menuList.length; i++) {
                this.$refs['menuItem' + i][0].style.height = this.$refs['menuItem' + i][0].scrollHeight + "px"
            }
        },
        upState(refDom, menu, index) {
            console.log(this.menuList[index]);
            switch (menu.status) {
                case 1: // 目前是展开状态
                    this.$refs[refDom][0].style.height = '23px'
                    this.$set(this.menuList[index], 'status', 2)
                    break;
                case 2: // 目前是收起状态
                    this.$refs[refDom][0].style.height = this.$refs[refDom][0].scrollHeight + "px"
                    this.$set(this.menuList[index], 'status', 1)
                    break;
            }
        },
        async initConfig() {
            let hiddenMenus = []
            try {
                // 获取区域代理配置
                let applyRes = await this.$get("/areaAgency/getApplyStatus");
                if (applyRes.code === 0) {
                    this.agencyApplyIsShow = applyRes.data.isApply === 1 ? true : false
                    // isApply 1=开启菜单 0=关闭菜单
                    // status 申请状态 0待审核1通过-1驳回   只有status=1 push 区域代理，其他push 申请代理
                    if(!this.agencyApplyIsShow){
                        hiddenMenus.push('areaAgencyApply')
                        hiddenMenus.push('areaAgencyOrderAward')
                    }else {
                        if(applyRes.data.status !== 1){
                            hiddenMenus.push('areaAgencyOrderAward')
                        } else {
                            hiddenMenus.push('areaAgencyApply')
                        }
                    }
                } else {
                    this.$message.error(applyRes.msg)
                }
                // 获取api采购设置接口
                let res = await this.$get("/application/findApplicationSetting");
                if (res.code === 0) {
                    this.apiProcurementIsShow = res.data.setting.value.is_open_apply === 1 ? true : false
                    if(!this.apiProcurementIsShow){
                        hiddenMenus.push('apiProcurementIndex')
                    }
                } else {
                    this.$message.error(res.msg)
                }
                // 获取是否为招商员
                let merchantRes = await this.$get("/merchant/verifyIdentity");
                if (merchantRes.code === 0) {
                    this.merchantIsShow = merchantRes.data.res
                    if(!this.merchantIsShow){
                        hiddenMenus.push('merchant')
                    }
                } else {
                    this.$message.error(merchantRes.msg)
                }
                //获取验证会员是否为分销商Ω
                let distributorRes = await this.$get("/distributor/verifyIdentity");
                if (distributorRes.code === 0) {
                    this.distributorIsShow = distributorRes.data.res
                    if(!this.distributorIsShow){
                        hiddenMenus.push('distributorManage')
                    }
                } else {
                    this.$message.error(distributorRes.msg)
                }
                // 获取知识库菜单显示否
                let knowledgeRes = await this.$get("/knowledge/getAuth");
                if (knowledgeRes.code === 0) {
                    this.knowledgeIsShow = knowledgeRes.data.auth === 1 ? true : false
                    if(!this.knowledgeIsShow){
                        hiddenMenus.push('knowledge')
                    }
                } else {
                    this.$message.error(knowledgeRes.msg)
                }
                // 获取电影票是否显示
                let cinemaTicketRes = await this.$get("/plugin/getPluginList")
                if (cinemaTicketRes.code === 0 && cinemaTicketRes.data.ResourcesPlugin && cinemaTicketRes.data.ResourcesPlugin.length) {
                    // 电影票
                    this.cinemaTicketIsShow = cinemaTicketRes.data.ResourcesPlugin.indexOf(23) === -1 ? false : true
                    if(!this.cinemaTicketIsShow){
                        hiddenMenus.push('cinemaTicketOrder')
                    }
                    // 小商店管理
                    let smallShopIsShow = cinemaTicketRes.data.MarketingPlugin.indexOf(17) === -1 ? false : true
                    if(!smallShopIsShow){
                        hiddenMenus.push('smallShopManage')
                    }
                    // 视频号小店
                    let videoIsShow = cinemaTicketRes.data.ChannelPlugin.indexOf(31) === -1 ? false : true
                    if(!videoIsShow){
                        hiddenMenus.push('videoMarkShop')
                    }
                }
            } catch {
            } finally {
                if (!hiddenMenus) {
                    return;
                }
                menu.forEach((item, index) => {
                    if (item.child && item.child.length) {
                        item.child.forEach((item2, index2) => {
                            if (hiddenMenus.includes(item2.name)) {
                                this.$set(menu[index].child[index2], 'isShow', false)
                            }
                        })
                    }

                })
                this.menuList = menu;
            }


        }
    }
}