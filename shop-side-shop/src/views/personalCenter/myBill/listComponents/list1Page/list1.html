<div>
    <el-table :data="list">
        <el-table-column label="申请时间" align="center">
            <template slot-scope="scope">
                {{scope.row.created_at | formatDate}}
            </template>
        </el-table-column>
        <el-table-column label="发票金额" align="center">
            <template slot-scope="scope">
                ￥{{scope.row.amount | formatF2Y}}
            </template>
        </el-table-column>
        <el-table-column label="开票主体" align="center">
            <template slot-scope="scope">
                <span v-if="scope.row.drawer === 0">平台开票</span>
                <span v-if="scope.row.drawer > 0">{{ scope.row.supplier ? scope.row.supplier.name : "-" }}</span>
            </template>
        </el-table-column>
        <el-table-column label="发票类型" align="center">
            <template slot-scope="scope">
                <span v-if="scope.row.type === 1">电子普通发票</span>
                <span v-if="scope.row.type === 2">增值税专用发票</span>
            </template>
        </el-table-column>
        <el-table-column label="发票抬头" align="center">
            <template slot-scope="scope">
                <span v-if="scope.row.account_type === 1">{{scope.row.person_name}}</span>
                <span v-if="scope.row.account_type === 2 || scope.row.type === 2">{{scope.row.company_name}}</span>
            </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
            <template slot-scope="scope">
                <div v-if="status === 1">
                    <el-button size="mini" class="red-btn" @click="handleOperation(scope.row.id,2)">确认</el-button>
                    <el-button size="mini" class="search-btn" @click="handleOperation(scope.row.id,0)">拒绝</el-button>
                </div>
                <el-button type="text" class="color-red" @click="jumpDetail(scope.row)">查看</el-button>
            </template>
        </el-table-column>
    </el-table>
    <Pagination :total="total" @pagination="pagination" :limit="pageSize" layout="total, prev, pager, next, jumper">
    </Pagination>
</div>