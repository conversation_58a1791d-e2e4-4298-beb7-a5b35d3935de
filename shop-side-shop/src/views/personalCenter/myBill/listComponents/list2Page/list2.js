import mDaterange from "@/components/mDate/daterange";
import InvoiceDialog from "../invoiceDialog";
export default {
    name: "list2",
    components: { mDaterange, InvoiceDialog },
    data() {
        return {
            ids: [],
            setting: {},
            list: [],
            page: 1,
            pageSize: 10,
            total: 0,
            amount_type: 1, // 1 订单发票 2技术服务费发票
            status: 0, // -1 = 已过期
            searchForm: {
                name: "",
                start_at: "",
                end_at: ""
            },
            date: []
        }
    },
    filters: {
        formatStatus: function (stauts) {
            let s = "";
            switch (stauts) {
                case 1:
                    s = "待发货"
                    break;
                case 2:
                    s = "待收货"
                    break;
                case 3:
                    s = "已完成"
                    break;
            }
            return s;
        }
    },
    methods: {
        reList(flg) {
            if (flg) {
                this.ids = []
            }
            this.fetch()
        },
        // 拼装地址
        addressFn(addressObj) {
            return addressObj.province + addressObj.city + addressObj.county + addressObj.detail;
        },
        // 打开开票dialog  status 1 = 开单票  2 = 批量开票
        openInvoiceDialog(id, status) {
            this.$refs.invoiceDialog.isShow = true
            this.$nextTick(() => {
                this.$refs.invoiceDialog.setting = this.setting
                this.$refs.invoiceDialog.formData.type = this.setting.bill_type[0]
                this.$refs.invoiceDialog.id = id
                this.$refs.invoiceDialog.bill_type = status
                this.$refs.invoiceDialog.amount_type = this.amount_type
            })
        },
        // 跳转订单详情
        jumpOrderDetail(id, status = 'n') {
            this.$_blank("/personalCenter/myOrderDetail", {
                oid: id,
                status
            })
        },
        pagination(page) {
            this.page = page.page
            this.fetch()
        },
        search() {
            if (this.date && this.date.length > 0) {
                this.searchForm.start_at = this.date[0]
                this.searchForm.end_at = this.date[1]
            } else {
                this.searchForm.start_at = ""
                this.searchForm.end_at = ""
            }
            this.page = 1
            this.fetch()
        },
        async fetch() {
            let settingRes = await this.$get("/bill/findTradeSetting")
            if (settingRes.code === 0) {
                this.setting = settingRes.data.setting.value;
            } else {
                this.$message.error(settingRes.msg)
                return
            }
            let params = {
                page: this.page,
                pageSize: this.pageSize,
                amount_type: this.amount_type
            }
            if (this.status === -1) {
                params.expire_status = 1
            } else {
                params.status = 0
            }
            if (this.searchForm.name) {
                params.name = this.searchForm.name
            }
            if (this.searchForm.start_at && this.searchForm.end_at) {
                params.start_at = this.searchForm.start_at
                params.end_at = this.searchForm.end_at
            }
            let url = "";
            switch (this.amount_type) {
                case 1: // 普通发票
                    url = "/bill/getOrderBillList"
                    break;
                case 2: // 技术服务费发票
                    url = "/bill/getTechFeeBillList"
                    break;
            }
            let res = await this.$get(url, params)
            if (res.code === 0) {
                this.list = res.data.list
                this.total = res.data.total
            } else {
                this.$message.error(res.msg)
            }
        }
    }
}