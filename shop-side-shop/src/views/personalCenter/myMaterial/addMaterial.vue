<template>
    <div class="bgw addMaterial-box">
        <div class="top-box f fac fjsb">
            <p class="title-p">发布素材</p>
        </div>
        <el-form
            :model="formData"
            label-width="190px"
            ref="form"
            :rules="rules"
            class="mt20"
        >
            <el-form-item label="素材标题:" prop="title">
                <el-input
                    v-model="formData.title"
                    class="w365"
                    placeholder="请输入"
                ></el-input>
            </el-form-item>
            <el-form-item label="素材分组:" prop="name">
                <el-select
                    v-model="formData.group_id"
                    clearable
                    filterable
                    class="w365"
                >
                    <el-option
                        v-for="item in centerGroupList"
                        :key="item.id"
                        :label="item.title"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="素材文案:" prop="content" class="w555">
                <el-input
                    type="textarea"
                    :maxlength="175"
                    :rows="6"
                    show-word-limit
                    v-model="formData.content"
                    placeholder="请输入"
                ></el-input>
            </el-form-item>
            <el-form-item label="上传图片:" prop="img_url">
                <div class="f fac fw w555">
                    <template v-if="images.length">
                        <div
                            style="width: 150px; height: 150px"
                            v-for="(img, index) in images"
                            :key="img.id"
                            class="images-div"
                        >
                            <m-image
                                :src="img"
                                style="width: 100%; height: 100%"
                            ></m-image>
                            <div class="mask-div">
                                <i
                                    class="el-icon-delete-solid shou"
                                    @click="delImage(index)"
                                ></i>
                            </div>
                        </div>
                    </template>
                    <el-upload
                        :action="`${$path}/common/upload`"
                        :headers="{ 'x-token': token }"
                        :show-file-list="false"
                        :on-success="handleCoverSuccess"
                        :before-upload="beforeUpload"
                        class="cover-uploader f fac"
                        accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF"
                    >
                        <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                </div>
                <p class="color-gray">支持上传多张图片,图片限制在10M以内</p>
            </el-form-item>
            <el-form-item label="上传视频:" prop="name">
                <div class="f fac">
                    <div
                        style="width: 150px; height: 150px; margin-right: 10px"
                        v-if="formData.video_url"
                    >
                        <video
                            controls="controls"
                            style="width: 100%; height: 100%"
                            :src="formData.video_url"
                        ></video>
                    </div>
                    <el-upload
                        class="cover-uploader"
                        :headers="{ 'x-token': token }"
                        :action="`${$path}/common/upload`"
                        :show-file-list="false"
                        :on-success="handleVideoSuccess"
                        :before-upload="beforeVideoUpload"
                        accept=".mp4,.3gp,.avi"
                    >
                        <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                </div>
                <p class="color-gray">建议视频大小不超过20M</p>
            </el-form-item>
            <el-form-item label="关联商品:" prop="name">
                <el-button class="red" size="small" @click="addGoods"
                    >+ 选择商品</el-button
                >
                <!-- <div class="shop-box f fac mt10">
                    <m-image
                        :src="formData.src"
                        :size="['48px', '48px']"
                        style="border-radius: 8px;"
                    ></m-image>
                    <div style="margin-left: 8px;">
                        <div class="shop-name">
                            李一桐同款 高充绒量羽绒衣，可到零看的下30°
                        </div>
                        <div class="shop-num">
                            ￥<span class="shop-num2">699.00</span>
                        </div>
                    </div>
                </div> -->
                <div class="shop-box f fac mt10" v-if="selectProduct.id">
                    <m-image
                        :src="selectProduct.thumb"
                        :size="['48px', '48px']"
                        style="border-radius: 8px;"
                    ></m-image>
                    <div style="margin-left: 8px;">
                        <div class="shop-name">{{ selectProduct.title }}</div>
                        <div class="shop-num mt_5">
                            ￥<span class="shop-num2">{{
                                selectProduct.price | formatF2Y
                            }}</span>
                        </div>
                    </div>
                </div>
            </el-form-item>
            <el-form-item prop="name">
                <el-button
                    style="width: 90px;height: 32px;font-size: 12px;"
                    class="confirm-btn"
                    @click="addMaterial"
                >
                    确 认</el-button
                >
                <el-button
                    style="width: 90px;height: 32px;font-size: 12px;"
                    class="cancel-btn"
                    @click="goback"
                >
                    取 消</el-button
                >
            </el-form-item>
        </el-form>
        <selectGoodsDialog
            ref="selectGoodsDialog"
            @getSelectProduct="getSelectProduct"
        ></selectGoodsDialog>
    </div>
</template>

<script>
import selectGoodsDialog from '../shortVideo/components/selectGoodsDialog.vue';
export default {
    name: 'addMaterial',
    components: { selectGoodsDialog },
    data() {
        return {
            rules: {
                title: {
                    required: true,
                    message: '请输入素材标题',
                    trigger: 'blur',
                },
            },
            formData: {
                title: '',
                product_id: null,
                content: '',
                group_id: null,
                video_url: '',
                img_url: '',
            },
            images: [], // 图片
            centerGroupList: [], // 分组列表
            selectProduct: {},
        };
    },
    computed: {
        token() {
            return this.$ls.getToken();
        },
    },
    mounted() {
        this.getOptionsList();
    },
    methods: {
        // 确认上传
        addMaterial() {
            this.$refs.form.validate(async (valid) => {
                if (!valid) return;
                if (this.images.length) {
                    this.formData.img_url = this.images.join(',');
                }
                let data = {
                    video_url: this.formData.video_url,
                    title: this.formData.title,
                    content: this.formData.content,
                    product_id: parseInt(this.formData.product_id),
                    user_id: this.$ls.getUserId(),
                    img_url: this.formData.img_url,
                };
                if (this.formData.group_id && this.formData.group_id !== '') {
                    data.group_id = parseInt(this.formData.group_id);
                }
                let res = await this.$post('/material/self/create', data);
                if (res.code === 0) {
                    this.$message.success(res.msg);
                    this.$router.back();
                } else {
                    this.$message.error(res.msg);
                }
            });
        },
        // 返回上一页
        goback() {
            this.$router.back();
        },
        // 获取分组列表
        async getOptionsList() {
            const res = await this.$post('/material/center/group', {
                title: '',
            });
            if (res.code === 0) {
                this.centerGroupList = res.data;
            }
        },
        // 选择商品
        addGoods() {
            this.$refs.selectGoodsDialog.info(this.selectProduct);
        },
        // 确认商品
        getSelectProduct(res) {
            this.selectProduct = res;
            this.formData.product_id = parseInt(this.selectProduct.id);
        },
        // 图片上传成功钩子
        handleCoverSuccess(res) {
            this.images.push(res.data.file.url);
        },
        // 删除图片
        delImage(index) {
            this.images.splice(index, 1);
        },
        // 素材
        handleVideoSuccess(res) {
            this.formData.video_url = res.data.file.url;
        },
        // 限制图片最大10M
        beforeUpload(file) {
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                this.$message.error('上传头像图片大小不能超过 10MB!');
            }
            return isLt10M;
        },
        // 限制视频大小
        beforeVideoUpload(file) {
            const videoSize = file.size / 1024 / 1024 < 20;
            if (!videoSize) {
                this.$message.error('上传视频大小不能超过 20MB!');
            }
            return videoSize;
        },
    },
};
</script>

<style lang="scss" scoped>
.addMaterial-box {
    padding: 15px 12px;

    .top-box {
        width: 100%;

        p.title-p {
            font-size: 16px;
            font-weight: bold;
        }
    }

    .el-form-item {
        margin-top: 20px;
    }
}

.w365 {
    width: 365px;
}
.w555 {
    width: 555px;
}

::v-deep .cover-uploader .el-upload {
    width: 150px;
    height: 150px;
    margin-bottom: 10px;
    border: 1px dotted #cecece;
    line-height: 150px;
    text-align: center;
    i {
        font-size: 18px;
        color: #cecece;
    }

    .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        line-height: 150px;
        text-align: center;
    }
}

.el-button.red {
    background: #f42121;
    border: 1px solid #f42121;
    color: white;
    margin-right: 19px;
    width: 108px;
    height: 32px;
    font-size: 12px;
    border-radius: 3px;
    padding: 0;

    &:hover {
        background: #f42121;
        border: 1px solid #f42121;
        color: white;
        margin-right: 19px;
        width: 108px;
        height: 32px;
        font-size: 12px;
        border-radius: 3px;
        padding: 0;
    }
}

.el-button.red.is-disabled {
    color: #c0c4cc;
    cursor: not-allowed;
    background-image: none;
    background-color: #fff;
    border-color: #ebeef5;
}
.shop-box {
    width: 500px;
    background: #f5f5f5;
    border-radius: 12px;
    padding: 12px;

    .shop-name {
        font-size: 14px;
        font-weight: 500;
        line-height: 19px;
    }

    .shop-num {
        font-size: 14px;
        line-height: 19px;
        color: #f15353;

        .shop-num2 {
            font-size: 20px;
        }
    }
}
.images-div {
    margin-right: 10px;
    margin-bottom: 10px;
    position: relative;
    .mask-div {
        display: none;
        width: 150px;
        height: 150px;
        background: rgba($color: #000000, $alpha: 0.6);
    }
    &:hover {
        .mask-div {
            position: absolute;
            display: flex;
            top: 0;
            left: 0;
            align-items: center;
            justify-content: center;
            i {
                color: #fff;
                font-size: 18px;
            }
        }
    }
}
</style>
