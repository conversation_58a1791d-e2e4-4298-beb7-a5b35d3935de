<template>
    <div class="perIndex-box">
        <div class="con-about">
            <h3 class="font20">[{{ shopName }}]会员享专属特权</h3>
            <div class="con-avatar">
                <div class="item-avatar">
                    <el-avatar :size="35" :src="user.avatar"></el-avatar>
                    <div class="avatar-name">
                        {{ user.level.name ? user.level.name : '' }}
                    </div>
                </div>
                <div class="item-validity" v-if="user.validity === '长期有效'">
                    <p class="font16">
                        尊敬的[{{ user.nickname }}]，您的[{{
                            user.level.name
                        }}]会员{{ user.validity }}
                    </p>
                </div>
                <div class="item-validity" v-else>
                    <p class="font16">
                        尊敬的[{{ user.nickname }}]，您的[{{
                            user.level.name
                        }}]会员{{ user.validity }}到期
                    </p>
                </div>
            </div>
        </div>
        <h3 class="f fac fjsb">
            升级/续费会员
            <el-button
                size="small"
                type="text"
                class="red-btn-txt"
                @click="onRecord"
                >开通记录</el-button
            >
        </h3>
        <div class="bgw order-center-box">
            <el-table :data="tableData" style="width: 100%">
                <el-table-column label="等级名称" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.name }}
                    </template>
                </el-table-column>
                <el-table-column label="折扣" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.discount | formatF2Y }}%
                    </template>
                </el-table-column>
                <el-table-column label="技术服务费比例" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.server_ratio | formatF2Y }}%
                    </template>
                </el-table-column>
                <el-table-column label="有效期" align="center">
                    <template slot-scope="scope">
                        <p v-if="scope.row.pay_upgrade === 0">长期有效</p>
                        <p v-if="scope.row.pay_upgrade === 1">
                            {{ scope.row.expire_days }} 天
                        </p>
                    </template>
                </el-table-column>
                <el-table-column label="费用" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.pay_product.price | formatF2Y }} 元
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center">
                    <template slot-scope="scope">
                        <el-button
                            v-if="
                                scope.row.pay_upgrade === 1 &&
                                scope.row.purchase_type === 2
                            "
                            type="danger"
                            size="mini"
                            @click="onPay(scope.row)"
                            >续费</el-button
                        >
                        <el-button
                            v-if="
                                scope.row.pay_upgrade === 1 &&
                                scope.row.purchase_type === 1
                            "
                            size="mini"
                            @click="onPay(scope.row)"
                            >升级</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <p class="notice">
                注意：升级会员后，原会员等级有效期会清空，以新等级会员有效期为准，续费会累计
            </p>
            <!-- <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[12, 24, 48, 96]"
            :style="{ display: 'flex', justifyContent: 'flex-end', marginRight: '20px', }"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes, prev, pager, next, jumper"
            class="mt30"
          >
          </el-pagination> -->
        </div>
        <h3>权益说明</h3>
        <div class="bgw order-center-box txt-wrap">
            <p class="ql-editor line24" v-html="equity_statement"></p>
        </div>
        <Payment :visible.sync="paymentVisible" :propRow="propRow"></Payment>
        <Record :visible.sync="recordVisible"></Record>
    </div>
</template>
<script>
import Payment from './payment';
import Record from './paymentRecord';
export default {
    name: 'MemberRights',
    components: { Payment, Record },
    data() {
        return {
            tableData: [],
            paymentRow: {
                id: '',
            },
            paymentVisible: false,
            // upgradeVisible: false,
            recordVisible: false,
            propRow: {},
            equity_statement: '',
            shopName: '',
            user: {
                level: {
                    name: '',
                },
                validity: '',
            },
            page: 1,
            pageSize: 12,
            total: 0,
        };
    },
    mounted() {
        this.getTableData();
        this.getSetting();
        // this.getSupplyChain()
        this.getShopName();
        this.getUser();
    },
    methods: {
        getTableData() {
            let params = {
                page: this.page,
                pageSize: this.pageSize,
            };
            this.$post('/user/getRenewLevels', params).then((res) => {
                if (res.code === 0) {
                    this.tableData = res.data.list;
                    this.page = res.data.page;
                    this.pageSize = res.data.pageSize;
                    this.total = res.data.total;
                } else {
                    this.$message.error(res.msg);
                }
            });
        },
        handleCurrentChange(page) {
            this.page = page;
            this.getTableData();
        },
        handleSizeChange(size) {
            this.pageSize = size;
            this.getTableData();
        },
        getSetting() {
            this.$post('/user/findSetting').then((res) => {
                if (res.code === 0) {
                    this.equity_statement =
                        res.data.setting.value.equity_statement;
                } else {
                    this.$message.error(res.msg);
                }
            });
        },
        getShopName() {
            this.$get('/userPurchase/getShopName').then((res) => {
                if (res.code === 0) {
                    this.shopName = res.data.shop_name;
                } else {
                    this.$message.error(res.msg);
                }
            });
        },
        getUser() {
            this.$post('/center/index')
                .then((res) => {
                    if (res.code == 0) {
                        this.user = res.data.user;
                        // this.user.validity = '2024-09-17 08:00:00';
                        if (this.user.validity !== '长期有效') {
                            const end = this.user.validity.indexOf(' ');
                            this.user.validity = this.user.validity.slice(
                                0,
                                end,
                            );
                            this.user.validity = this.user.validity.replace(
                                '-',
                                '年',
                            );
                            this.user.validity = this.user.validity.replace(
                                '-',
                                '月',
                            );
                            this.user.validity =
                                this.user.validity.concat('号');
                            // console.log('this.user.validity',this.user.validity);
                        }
                        this.order = res.data.order;
                        this.dispatch_list = this.order.dispatch_list;
                        this.balance_settings = res.data.balance_settings;
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },
        onPay(row) {
            this.paymentVisible = true;
            this.propRow = row;
        },
        onUpgrade() {
            // this.upgradeVisible = true
            this.paymentVisible = true;
            this.propRow = row;
        },
        onRecord() {
            this.recordVisible = true;
        },
    },
};
</script>
<style lang="scss" scoped>
.notice {
    margin: 25px 0 10px 15px;
    color: #606266;
}
.con-about {
    padding: 30px 0 40px 0;
    text-align: center;
    background: #fff;
}
.item-avatar,
.item-validity {
    display: inline-block;
    margin-right: 20px;
    vertical-align: middle;
    color: #606266;
}

.item-avatar {
    position: relative;
    width: 60px;
    text-align: center;
    font-size: 12px;
}
.avatar-name {
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translate(-50%, 0);
    padding: 2px 5px;
    width: 100%;
    border-radius: 20px;
    // background: rgb(237, 144, 79);
    background: #f2f2f2;
    // color: white;
    color: #333333;
}
.item-validity {
    margin-top: 10px;
    p {
        margin-top: 5px;
    }
}
.line24 {
    line-height: 24px;
}
.txt-wrap {
    word-wrap: break-word;
    word-break: normal;
    p {
        color: #606266;
    }
}
::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: $theme-color;
}
.ml-30 {
    margin-left: 30px;
}
.mt-10 {
    margin-top: 10px;
}
.font16 {
    font-size: 16px;
}
.font20 {
    font-size: 20px;
}
.perIndex-box {
    .perIndex-top-box {
        // padding: 27px;
        padding: 15px 27px 39px 27px;
        .avatar-info-box {
            position: relative;

            .avatar-info-left {
                .temporary-box {
                    position: absolute;
                    border-radius: 30px;
                    // width: 105px;
                    // height: 18px;
                    background: url('../../../../assets/images/label_bg_03.png')
                        no-repeat center center;
                    // line-height: 18px;
                    bottom: -12px;
                    left: -12px;
                    padding: 3px 15px;
                    font-size: 12px;
                    p {
                        color: white;
                    }
                }
            }

            .avatar-info-right {
                position: absolute;
                left: 115px;
                //bottom: -8px;
                p.tutor-p {
                    cursor: pointer;
                }
                .phone-p {
                    font-size: 18px;
                    font-weight: bold;
                }

                p:nth-child(2) {
                    margin: 12px 0;
                }
            }
        }

        p.number-p {
            font-size: 18px;
            font-weight: bold;
        }

        p.text-p {
            margin-top: 20px;
        }
    }

    .order-center-box {
        padding: 30px 10px 15px 10px;

        .top-box {
            padding-bottom: 30px;
            margin-bottom: 15px;
            border-bottom: 1px solid #f7f7f7;
            text-align: center;

            i {
                font-size: 40px;
            }

            .icon-p {
                margin-top: 16px;
            }
        }

        .order-goods-img {
            width: 70px;
            height: 70px;
            border: 1px solid #c0c0c0;
            margin-right: 12px;
        }

        .order-goods-title-p {
            margin-bottom: 15px;
        }
    }
    /* .special-offer-box {
    display: grid;
    grid-template-columns: repeat(4, 230px);
    grid-row-gap: 13px;
    grid-column-gap: 13px;

    .special-offer-item {
      height: 322px;
      background-color: white;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        opacity: 0.8;

        .special-offer-product-img-box {
          .el-image {
            width: 160px !important;
            height: 160px !important;
          }
        }
      }

      .special-offer-product-img-box {
        height: 230px;
        display: flex;
        justify-content: center;
        align-items: center;

        .el-image {
          width: 150px !important;
          height: 150px !important;
          transition: all 0.3s ease;
        }
      }

      .special-offer-b-box {
        padding: 10px;
        font-size: 14px;
        p {
          height: 38px;
        }
        span.special-offer-title {
          color: #a5a5a5;
          margin-right: 10px;
        }

        span.special-offer-number {
          color: $theme-color;
        }
      }
    }
  } */
}

.red-p {
    color: #f42121;
}
.red-btn-txt {
    margin-right: 10px;
    font-size: 14px;
    color: $theme-color;
}
h3 {
    font-weight: bold;
    margin: 15px 0 15px 10px;
}
::v-deep .el-dialog {
    font-size: 16px;
    border-radius: 10px;

    .el-dialog__header {
        border-bottom: 1px solid #f0f0f0;
    }

    .el-dialog__body {
        .user-info-box {
        }
    }
}

.mt30 {
    margin-top: 30px;
}
</style>
