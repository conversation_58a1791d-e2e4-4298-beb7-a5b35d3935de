/**
 * 获取数组对象指定k的下标
 */
Array.prototype.indexOfJSON = function (kName, value) {
    for (var i = 0; i < this.length; i++) {
        if (this[i][kName] == value) return i;
    }
    return -1;
};
import AddressDialog from "@/views/personalCenter/recipients/components/dialog"
export default {
    name: "selectAddress",
    components: { AddressDialog },
    data() {
        return {
            pid: null,
            oid:null,
            isShow: false,
            isCheck: {},
            addressList: [],
            addressTemp: null,
            form: {
                company_code: '', //归还公司快递码
                company_name: '',//归还快递公司名称
                express_no: '', //归还快递单号
                lease_return_address_id: null,//归还地址id
                order_id: null, //订单id
            },
            expressData:[],
        }
    },
    mounted() {
        this.initAddress(); 
    },
    methods: {
        onOpen(){
            this.getExpress()
        },
        getExpress(){
            this.$get("/shipping/company/list").then(res=> {
                if (res.code === 0) {
                    this.expressData = res.data.list
                }
            }).catch(function (res) {
                console.log(res);
            });
        },
        addAddress() {
            this.handleClose();
            this.$emit("addAddress")
        },
        openDialog(item) {
            this.$refs.addressDialog.dialogIsShow = true
            this.$nextTick(() => {
                this.$refs.addressDialog.isEditAddress = true;
                this.$refs.addressDialog.title = "编辑收货地址";
                this.$refs.addressDialog.initFormData(item);
            })
        },
        handleClose() {
            this.isShow = false;
        },
        notarizeClick() {
            let that = this;
            if (!that.addressTemp) {
                that.$message.error("请选择收货地址");
                // return;
            }else{
                if(that.form.company_code && that.form.express_no){
                    console.log('可提交')
                    this.handleClose();
                    const found = this.expressData.find(element => element.code === this.form.company_code);
                    that.form.company_name =found.name
                    that.form.order_id = parseInt(that.$route.query.oid) || that.oid
                    this.$emit('submitted', that.form);
                }else{
                    console.log('不可提交')
                    that.$message.error("请填写归还快递与归还快递单号");
                }
            }
            // this.handleClose();
            // const found = this.expressData.find(element => element.code === this.form.company_code);
            // that.form.company_name =found.name
            // that.form.order_id = parseInt(that.$route.query.oid)
            // this.$emit('submitted', that.form);
        },
        //初始化数据
        initAddress(checkid = null) {
            let that = this;
            that.addressTemp = null;
            let para = {
                product_id: this.pid
            };
            that.$post("/lease/getLeaseReturnAddressesByProductId", para).then(function (res) {
                if (res.code == 0) {
                    that.addressList = res.data
                    let index = that.addressList.findIndex((item) => item.is_default === 1)
                    that.addressTemp = that.addressList[index]
                    that.form.lease_return_address_id = that.addressTemp.id
                    if (checkid) {
                        that.setCheck(checkid)
                    }else{
                        that.isCheck = that.addressList[index]
                    }
                }
            }).catch(function (res) {
                console.log(res);
            });
        },
        setCheck(id) {
            this.isCheck = this.addressList[this.addressList.indexOfJSON('id', id)]
            this.addressTemp = this.isCheck
        },
        radioChange(item) {
            let that = this;
            that.addressTemp = item;
            that.form.lease_return_address_id = that.addressTemp.id
        }
    }
}
