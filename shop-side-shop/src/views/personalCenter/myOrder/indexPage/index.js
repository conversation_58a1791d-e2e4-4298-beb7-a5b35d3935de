import CreateCommentDialog from '../components/createCommentDialog';
import SelectAddress from '../components/selectAddress';
import ReturnDetailsDialog from '../components/returnDetailsDialog.vue';
import { confirm } from '@/decorators/decorators';
import orderStatementDialog from '../components/orderStatementDialog.vue';
export default {
    name: 'myOrderIndex',
    components: {
        CreateCommentDialog,
        SelectAddress,
        ReturnDetailsDialog,
        orderStatementDialog,
    },
    data() {
        return {
            // 再来一单按钮loading
            againLoading:false,
            groupType: 1, // 1普通订单 2抖音cps 3数字权益商品订单 4租赁订单
            douyinCpsDisplay: 0, //1显示 0隐藏
            FuluDisplay: 0, // 1显示 0隐藏
            leaseDisplay: 1, // 1显示 0隐藏
            activeName: this.$route.query.status
                ? this.$route.query.status
                : 'orderAll',
            tabsList: [
                { label: '全部', name: 'orderAll' },
                { label: '待付款', name: 'order0' },
                { label: '待发货', name: 'order1' },
                { label: '待收货', name: 'order2' },
                { label: '已完成', name: 'order3' },
                { label: '关闭', name: 'order4' },
            ],
            activeNameCps: 'orderAll',
            tabsListCps: [
                { label: '全部', name: 'orderAll' },
                { label: '已支付', name: 'order0' },
                { label: '确认收货', name: 'order1' },
                { label: '结束', name: 'order2' },
                { label: '退款', name: 'order3' },
            ],
            activeNameFulu: 'orderAll',
            tabsListFulu: [
                { label: '全部', name: 'orderAll' },
                { label: '待付款', name: 'order0' },
                { label: '待发货', name: 'order1' },
                { label: '待收货', name: 'order2' },
                { label: '已完成', name: 'order3' },
                { label: '关闭', name: 'order4' },
            ],
            activeNameLease: 'orderAll',
            tabsListLease: [
                { label: '全部', name: 'orderAll' },
                { label: '待付款', name: 'order0' },
                { label: '待发货', name: 'order1' },
                { label: '待收货', name: 'order2' },
                { label: '待归还', name: 'order5' },
                { label: '已完成', name: 'order3' },
                { label: '关闭', name: 'order4' },
                // {label: '申请归还', name: 'order6'},
                // {label: '归还中', name: 'order7'},
                // {label: '待确认', name: 'order8'},
                // {label: '驳回申请归还', name:'order9'},
            ],
            selectLease: [
                { label: '全部', status: 'orderAll' },
                { label: '待付款', status: 'order0' },
                { label: '待发货', status: 'order1' },
                { label: '待收货', status: 'order2' },
                { label: '待归还', status: 'order5' },
                { label: '已完成', status: 'order3' },
                { label: '关闭', status: 'order4' },
                { label: '申请归还', status: 'order6' },
                { label: '归还中', status: 'order7' },
                { label: '待确认', status: 'order8' },
                { label: '驳回申请归还', status: 'order9' },
            ],
            orderList: [],
            orderListCps: [],
            orderListFulu: [],
            orderListLease: [],
            page: 1,
            pageSize: 10,
            total: 0,
            pageCps: 1,
            pageSizeCps: 10,
            totalCps: 0,
            pageFulu: 1,
            pageSizeFulu: 10,
            totalFulu: 0,
            pageLease: 1,
            pageSizeLease: 10,
            totalLease: 0,
            searchDate: [],
            searchDateCps: [],
            searchFuluDate: [],
            searchLeaseDate: [],
            searchForm: {
                product_title: '',
                order_sn: '', // 订单编号
                start_at: '', // 开始日期
                end_at: '', // 结束日期
                shop_name: '', // 商城名称
            },
            searchFormCps: {
                order_sn: null, // 订单编号
                start_at: '', // 开始日期
                end_at: '', // 结束日期
            },
            searchFormFulu: {
                product_title: '',
                third_order_sn: null, // 第三方订单号
                order_sn: '', // 订单编号
                start_at: '', // 开始日期
                end_at: '', // 结束日期
                shop_name: '', // 商城名称
            },
            searchFormLease: {
                product_title: '',
                order_sn: '', // 订单编号
                start_at: '', // 开始日期
                end_at: '', // 结束日期
                status: null, // 租赁订单状态
            },
            batchIDObj: [],
            // 全选订单按钮
            checkAll: false,
        };
    },
    filters: {
        // 格式化订单状态
        formatStatus: function (status) {
            let name = '';
            switch (status) {
                case 0:
                    name = '未 分 成';
                    break;
                case 1:
                    name = '已 分 成';
                    break;
            }
            return name;
        },
        optStatus: function (status) {
            let name = '';
            switch (status) {
                case 0: //待付款
                    // name = "确认收货"
                    break;
                case 1: // 待发货
                    // name = ""
                    break;
                case 2: // 待收货
                    name = '确认收货';
                    break;
                case 3: //已完成
                    // name = ""
                    break;
                case -1: //关闭
                    // name = ""
                    break;
                case 4: // 待归还
                    name = '申请归还';
                    break;
                case 5: // 申请归还
                    // name = ""
                    break;
                case 6: // 归还中
                    name = '填写归还';
                    break;
                case 7: // 待确认(审核中)
                    name = '归还详情';
                    break;
                case -2: // 驳回申请归还
                    // name = ""
                    break;
            }
            return name;
        },
    },
    mounted() {
        this.initOrderInfo();
        this.getLeaseList();
    },
    methods: {
        // 再来一单
        async oneMoreOrder(id) {
            this.againLoading = true;
            let res = await this.$post('/shoppingcart/addByReorder',{order_id:id});
            if (res.code === 0) {
                this.$router.push('/againOrder?order_id=' + id)
            }
            this.againLoading = false;
        },
        // 订单报表
        openOrderStatementDialog() {
            let data = {
                page: this.page,
                pageSize: this.pageSize,
                searchDate: this.searchDate,
                activeName: this.activeName,
                searchForm: this.searchForm,
            };
            this.$refs.orderStatementDialog.init(data);
        },
        // 租赁订单
        lease_order() {
            this.groupType = 4;
        },
        // 租赁列表
        // order_leases.status：
        // 0：待支付，1：待发货，2：待收货，3：已完成，
        // 4：待归还，5：申请归还，6：归还中--需要物流，7：待确认
        // -1：关闭，-2：驳回申请归还
        getLeaseList() {
            let that = this;
            if (this.searchLeaseDate && this.searchLeaseDate.length > 0) {
                this.searchFormLease.start_at = this.searchLeaseDate[0];
                this.searchFormLease.end_at = this.searchLeaseDate[1];
            } else {
                this.searchFormLease.start_at = '';
                this.searchFormLease.end_at = '';
            }
            let para = {
                page: that.pageLease,
                pageSize: that.pageSizeLease,
                ...this.searchFormLease,
            };
            switch (that.activeNameLease) {
                case 'orderAll':
                    delete para.status;
                    break;
                case 'order0': //待付款
                    para.status = '0';
                    break;
                case 'order1': //待发货
                    para.status = '1';
                    break;
                case 'order2': //待收货
                    para.status = '2';
                    break;
                case 'order3': //已完成
                    para.status = '3';
                    break;
                case 'order4': //关闭
                    para.status = '-1';
                    break;
                case 'order5': //待归还
                    para.status = '4';
                    break;
                case 'order6': //申请归还
                    para.status = '5';
                    break;
                case 'order7': //归还中
                    para.status = '6';
                    break;
                case 'order8': //待确认
                    para.status = '7';
                    break;
                case 'order9': //驳回申请归还
                    para.status = '-2';
                    break;
                default:
                    break;
            }
            that.$get('/lease/getOrderList', para)
                .then(function (res) {
                    if (res.code == 0) {
                        that.orderListLease = res.data.list;
                        that.totalLease = res.data.total;
                        that.tabsListLease.forEach((element) => {
                            switch (element.name) {
                                case 'orderAll': // 全部
                                    element.num =
                                        typeof res.data?.AllNum === 'number'
                                            ? res.data?.AllNum
                                            : 0; // 全部
                                    // element.num =
                                    //     (typeof res.data?.WaitPayNum === 'number' ? res.data?.WaitPayNum : 0) +
                                    //     (typeof res.data?.WaitSendNum === 'number' ? res.data?.WaitSendNum : 0) +
                                    //     (typeof res.data?.WaitReceiveNum === 'number' ? res.data?.WaitReceiveNum : 0) +
                                    //     (typeof res.data?.WaitReturnNum === 'number' ? res.data?.WaitReturnNum : 0) +
                                    //     (typeof res.data?.CompletedNum === 'number' ? res.data?.CompletedNum : 0) +
                                    //     (typeof res.data?.ClosedNum === 'number' ? res.data?.ClosedNum : 0)
                                    break;
                                case 'order0': //待付款
                                    element.num =
                                        typeof res.data?.WaitPayNum === 'number'
                                            ? res.data?.WaitPayNum
                                            : 0; // 待付款
                                    break;
                                case 'order1': // 待发货
                                    element.num =
                                        typeof res.data?.WaitSendNum ===
                                        'number'
                                            ? res.data?.WaitSendNum
                                            : 0; // 待发货
                                    break;
                                case 'order2': // 待收货
                                    element.num =
                                        typeof res.data?.WaitReceiveNum ===
                                        'number'
                                            ? res.data?.WaitReceiveNum
                                            : 0; // 待收货
                                    break;
                                case 'order5': //待归还
                                    element.num =
                                        typeof res.data?.WaitReturnNum ===
                                        'number'
                                            ? res.data?.WaitReturnNum
                                            : 0; // 待归还
                                    break;
                                case 'order3': //已完成
                                    element.num =
                                        typeof res.data?.CompletedNum ===
                                        'number'
                                            ? res.data?.CompletedNum
                                            : 0; // 已完成
                                    break;
                                case 'order4': //关闭
                                    element.num =
                                        typeof res.data?.ClosedNum === 'number'
                                            ? res.data?.ClosedNum
                                            : 0; // 关闭
                                    break;
                                // case 'order6': //申请归还
                                //     element.num = typeof res.data?.ClosedNum === 'number' ? res.data?.ClosedNum : 0 // 关闭
                                //     break;
                                // case 'order7': //归还中
                                //     element.num = typeof res.data?.ClosedNum === 'number' ? res.data?.ClosedNum : 0 // 关闭
                                //     break;
                                // case 'order8': //待确认
                                //     element.num = typeof res.data?.ClosedNum === 'number' ? res.data?.ClosedNum : 0 // 关闭
                                //     break;
                                // case 'order9': //驳回申请归还
                                //     element.num = typeof res.data?.ClosedNum === 'number' ? res.data?.ClosedNum : 0 // 关闭
                                //     break;
                                default:
                                    break;
                            }
                        });
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },
        fulu_order() {
            this.groupType = 3;
            this.getFuluList();
        },
        // 福禄列表
        getFuluList() {
            let that = this;

            let para = {
                page: that.pageFulu,
                pageSize: that.pageSizeFulu,
                ...this.searchFormFulu,
            };
            if (this.searchFuluDate && this.searchFuluDate.length > 0) {
                para.start_at = this.searchFuluDate[0];
                para.end_at = this.searchFuluDate[1];
            } else {
                para.start_at = '';
                para.end_at = '';
            }

            switch (that.activeNameFulu) {
                case 'orderAll':
                    break;
                case 'order0':
                    para.status = '0';
                    break;
                case 'order1':
                    para.status = '1';
                    break;
                case 'order2':
                    para.status = '2';
                    break;
                case 'order3':
                    para.status = '3';
                    break;
                case 'order4':
                    para.status = '-1';
                    break;
                default:
                    break;
            }
            that.$get('/fuluSupply/api/order/list', para)
                .then(function (res) {
                    if (res.code == 0) {
                        that.orderListFulu = res.data.list;
                        that.totalFulu = res.data.total;
                        that.tabsListFulu.forEach((element) => {
                            switch (element.name) {
                                case 'orderAll': // 全部
                                    element.num =
                                        (typeof res.data?.WaitPayNum ===
                                        'number'
                                            ? res.data?.WaitPayNum
                                            : 0) +
                                        (typeof res.data?.WaitSendNum ===
                                        'number'
                                            ? res.data?.WaitSendNum
                                            : 0) +
                                        (typeof res.data?.WaitReceiveNum ===
                                        'number'
                                            ? res.data?.WaitReceiveNum
                                            : 0) +
                                        (typeof res.data?.CompletedNum ===
                                        'number'
                                            ? res.data?.CompletedNum
                                            : 0) +
                                        (typeof res.data?.ClosedNum === 'number'
                                            ? res.data?.ClosedNum
                                            : 0);
                                    break;
                                case 'order0':
                                    element.num =
                                        typeof res.data?.WaitPayNum === 'number'
                                            ? res.data?.WaitPayNum
                                            : 0; // 待付款
                                    break;
                                case 'order1':
                                    element.num =
                                        typeof res.data?.WaitSendNum ===
                                        'number'
                                            ? res.data?.WaitSendNum
                                            : 0; // 待发货
                                    break;
                                case 'order2':
                                    element.num =
                                        typeof res.data?.WaitReceiveNum ===
                                        'number'
                                            ? res.data?.WaitReceiveNum
                                            : 0; // 待收货
                                    break;
                                case 'order3':
                                    element.num =
                                        typeof res.data?.CompletedNum ===
                                        'number'
                                            ? res.data?.CompletedNum
                                            : 0; // 已完成
                                    break;
                                case 'order4':
                                    element.num =
                                        typeof res.data?.ClosedNum === 'number'
                                            ? res.data?.ClosedNum
                                            : 0; // 关闭
                                    break;
                                default:
                                    break;
                            }
                        });
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },
        // cps导出
        exportTableCps() {
            let data = {
                page: this.pageCps,
                pageSize: this.pageSizeCps,
                ...this.searchFormCps,
            };
            if (this.searchDateCps && this.searchDateCps.length > 0) {
                data.start_at = this.searchDateCps[0];
                data.end_at = this.searchDateCps[1];
            } else {
                data.start_at = '';
                data.end_at = '';
            }
            switch (this.activeNameCps) {
                case 'orderAll':
                    break;
                case 'order0':
                    data.status = '0';
                    break;
                case 'order1':
                    data.status = '1';
                    break;
                case 'order2':
                    data.status = '2';
                    break;
                case 'order3':
                    data.status = '-1';
                    break;
                default:
                    break;
            }
            this.$post('/cps/exportOrderList', data).then((res) => {
                if (res.code === 0) {
                    window.open(this.$path + '/' + res.data.link);
                } else {
                    this.$message.error(res.msg);
                }
            });
        },
        //cps订单同步
        asynOrder(id) {
            let that = this;
            this.$confirm('确认要同步订单?, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    this.$post('/cps/setOrderDisplay', { id: id }).then(
                        function (res) {
                            if (res.code == 0) {
                                that.$message.success(res.msg);
                            }
                        },
                    );
                })
                .catch(function (res) {});
        },
        goods_order() {
            this.groupType = 1;
        },
        cps_order() {
            this.groupType = 2;
            this.initOrderCps();
        },
        //cps确定按钮
        handleSearchClickCps() {
            this.pageCps = 1;
            this.pageSizeCps = 10;
            this.totalCps = 0;
            if (this.searchDateCps && this.searchDateCps.length > 0) {
                this.searchFormCps.start_at = this.searchDateCps[0];
                this.searchFormCps.end_at = this.searchDateCps[1];
            } else {
                this.searchFormCps.start_at = '';
                this.searchFormCps.end_at = '';
            }
            this.initOrderCps();
        },
        //cps获取订单列表
        initOrderCps() {
            let that = this;
            let data = {
                page: that.pageCps,
                pageSize: that.pageSizeCps,
                ...this.searchFormCps,
            };
            switch (that.activeNameCps) {
                case 'orderAll':
                    break;
                case 'order0':
                    data.status = '0';
                    break;
                case 'order1':
                    data.status = '1';
                    break;
                case 'order2':
                    data.status = '2';
                    break;
                case 'order3':
                    data.status = '-1';
                    break;
                default:
                    break;
            }
            that.$post('/cps/getOrderList', data)
                .then(function (res) {
                    if (res.code == 0) {
                        that.orderListCps = res.data.list;
                        that.totalCps = res.data.total;
                    }
                })
                .catch(function (res) {});
        },
        //cps Tab切换
        handleClickCps(tab) {
            this.pageCps = 1;
            this.orderListCps = [];
            this.initOrderCps();
        },
        //cps分页
        paginationCps(val) {
            this.pageCps = val.pageCps;
            this.initOrderCps();
        },
        //导出
        async exportTable() {
            let param = {
                page: this.page,
                pageSize: this.pageSize,
                ...this.searchForm,
            };
            if (this.searchDate && this.searchDate.length > 0) {
                param.start_at = this.searchDate[0];
                param.end_at = this.searchDate[1];
            } else {
                param.start_at = '';
                param.end_at = '';
                await this.$alert(
                    '未选择时间范围将默认只导出最近一个月的订单',
                    '提示',
                );
            }
            switch (this.activeName) {
                case 'orderAll':
                    break;
                case 'order0':
                    param.status = '0';
                    break;
                case 'order1':
                    param.status = '1';
                    break;
                case 'order2':
                    param.status = '2';
                    break;
                case 'order3':
                    param.status = '3';
                    break;
                case 'order4':
                    param.status = '-1';
                    break;

                default:
                    break;
            }
            this.$get('/order/export', param).then((res) => {
                if (res.code === 0) {
                    let link = this.$path + '/' + res.data.link;
                    this.$fn.exportTable(link);
                } else {
                    this.$message.error(res.msg);
                }
            });
        },
        exportTableFulu() {
            let param = {
                page: this.pageFulu,
                pageSize: this.pageSizeFulu,
                ...this.searchFormFulu,
            };
            if (this.searchFuluDate && this.searchFuluDate.length > 0) {
                param.start_at = this.searchFuluDate[0];
                param.end_at = this.searchFuluDate[1];
            } else {
                param.start_at = '';
                param.end_at = '';
            }
            switch (this.activeNameFulu) {
                case 'orderAll':
                    break;
                case 'order0':
                    param.status = '0';
                    break;
                case 'order1':
                    param.status = '1';
                    break;
                case 'order2':
                    param.status = '2';
                    break;
                case 'order3':
                    param.status = '3';
                    break;
                case 'order4':
                    param.status = '-1';
                    break;

                default:
                    break;
            }
            this.$get('/fuluSupply/api/order/export', param).then((res) => {
                if (res.code === 0) {
                    let link = this.$path + '/' + res.data.link;
                    this.$fn.exportTable(link);
                } else {
                    this.$message.error(res.msg);
                }
            });
        },
        exportTableLease() {
            let param = {
                page: this.pageLease,
                pageSize: this.pageSizeLease,
                ...this.searchFormLease,
            };
            if (this.searchLeaseDate && this.searchLeaseDate.length > 0) {
                param.start_at = this.searchLeaseDate[0];
                param.end_at = this.searchLeaseDate[1];
            } else {
                param.start_at = '';
                param.end_at = '';
            }
            // order_leases.status：
            // 0：待支付，1：待发货，2：待收货，3：已完成，
            // 4：待归还，5：申请归还，6：归还中--需要物流，7：待确认
            // -1：关闭，-2：驳回申请归还
            switch (this.activeNameLease) {
                case 'orderAll':
                    delete param.status;
                    break;
                case 'order0':
                    param.status = '0';
                    break;
                case 'order1':
                    param.status = '1';
                    break;
                case 'order2':
                    param.status = '2';
                    break;
                case 'order3':
                    param.status = '3';
                    break;
                case 'order4':
                    param.status = '-1';
                    break;
                case 'order5':
                    param.status = '4';
                    break;
                default:
                    break;
            }
            this.$get('/lease/exportOrderList', param).then((res) => {
                if (res.code === 0) {
                    let link = this.$path + '/' + res.data.link;
                    this.$fn.exportTable(link);
                } else {
                    this.$message.error(res.msg);
                }
            });
        },
        // 打开创建评论
        handleOpenCommentClick(order) {
            this.$refs.createCommentDialog.isShow = true;
            this.$refs.createCommentDialog.setForm(order);
        },
        handleSearchClick() {
            this.page = 1;
            this.pageSize = 10;
            this.total = 0;
            if (this.searchDate && this.searchDate.length > 0) {
                this.searchForm.start_at = this.searchDate[0];
                this.searchForm.end_at = this.searchDate[1];
            } else {
                this.searchForm.start_at = '';
                this.searchForm.end_at = '';
            }
            this.initOrderInfo();
        },
        handleSearchClickFulu() {
            this.pageFulu = 1;
            this.pageSizeFulu = 10;
            this.totalFulu = 0;
            if (this.searchFuluDate && this.searchFuluDate.length > 0) {
                this.searchFormFulu.start_at = this.searchFuluDate[0];
                this.searchFormFulu.end_at = this.searchFuluDate[1];
            } else {
                this.searchFormFulu.start_at = '';
                this.searchFormFulu.end_at = '';
            }
            this.getFuluList();
        },
        handleSearchClickLease() {
            this.pageLease = 1;
            this.pageSizeLease = 10;
            this.totalLease = 0;
            if (this.searchLeaseDate && this.searchLeaseDate.length > 0) {
                this.searchLeaseDate.start_at = this.searchLeaseDate[0];
                this.searchLeaseDate.end_at = this.searchLeaseDate[1];
            } else {
                this.searchLeaseDate.start_at = '';
                this.searchLeaseDate.end_at = '';
            }
            this.getLeaseList();
        },
        //跳转至发票详情
        jumpBillDetail(bill_id) {
            this.$_blank('/personalCenter/myBillDetail', {
                detail_id: bill_id,
            });
        },
        jumpOrderDetail(order, status = 'n') {
            let params = {
                status,
            };
            switch (typeof order) {
                case 'number':
                    params.oid = order;
                    break;
                case 'object':
                    params.statuCode = order.order_leases.status;
                    params.oid = order.id;
                    params.end_at = order.order_leases.end_at;
                    params.isLease = 1;
                    break;
                default:
                    break;
            }
            this.$_blank('/personalCenter/myOrderDetail', params);
        },
        // 跳转商品详情
        goGoodsDialog(item) {
            this.$_blank('/goodsDetail', { goods_id: item.product_id });
        },
        goLeaseGoodsDialog(item) {
            this.$_blank('/lease/goodsDetail', { goods_id: item.product_id });
        },
        //获取商品订单列表
        initOrderInfo() {
            let that = this;

            let para = {
                page: that.page,
                pageSize: that.pageSize,
                pc_client: 1,
                ...this.searchForm,
            };

            switch (that.activeName) {
                case 'orderAll':
                    break;
                case 'order0':
                    para.status = '0';
                    break;
                case 'order1':
                    para.status = '1';
                    break;
                case 'order2':
                    para.status = '2';
                    break;
                case 'order3':
                    para.status = '3';
                    break;
                case 'order4':
                    para.status = '-1';
                    break;
                default:
                    break;
            }
            that.$get('/order/list', para)
                .then(function (res) {
                    if (res.code == 0) {
                        that.leaseDisplay = res.data.LeaseDisplay;
                        that.orderList = res.data.list;
                        that.total = res.data.total;
                        that.douyinCpsDisplay = res.data.DouyinCpsDisplay;
                        that.FuluDisplay = res.data.FuluDisplay;
                        that.tabsList.forEach((element) => {
                            switch (element.name) {
                                case 'orderAll': // 全部
                                    element.num =
                                        (typeof res.data?.WaitPayNum ===
                                        'number'
                                            ? res.data?.WaitPayNum
                                            : 0) +
                                        (typeof res.data?.WaitSendNum ===
                                        'number'
                                            ? res.data?.WaitSendNum
                                            : 0) +
                                        (typeof res.data?.WaitReceiveNum ===
                                        'number'
                                            ? res.data?.WaitReceiveNum
                                            : 0) +
                                        (typeof res.data?.CompletedNum ===
                                        'number'
                                            ? res.data?.CompletedNum
                                            : 0) +
                                        (typeof res.data?.ClosedNum === 'number'
                                            ? res.data?.ClosedNum
                                            : 0);
                                    break;
                                case 'order0':
                                    element.num =
                                        typeof res.data?.WaitPayNum === 'number'
                                            ? res.data?.WaitPayNum
                                            : 0; // 待付款
                                    break;
                                case 'order1':
                                    element.num =
                                        typeof res.data?.WaitSendNum ===
                                        'number'
                                            ? res.data?.WaitSendNum
                                            : 0; // 待发货
                                    break;
                                case 'order2':
                                    element.num =
                                        typeof res.data?.WaitReceiveNum ===
                                        'number'
                                            ? res.data?.WaitReceiveNum
                                            : 0; // 待收货
                                    break;
                                case 'order3':
                                    element.num =
                                        typeof res.data?.CompletedNum ===
                                        'number'
                                            ? res.data?.CompletedNum
                                            : 0; // 已完成
                                    break;
                                case 'order4':
                                    element.num =
                                        typeof res.data?.ClosedNum === 'number'
                                            ? res.data?.ClosedNum
                                            : 0; // 关闭
                                    break;
                                default:
                                    break;
                            }
                        });
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },

        handleClick(tab) {
            this.page = 1;
            this.orderList = [];
            this.initOrderInfo();
        },
        handleClickFulu(tab) {
            this.pageFulu = 1;
            this.orderListFulu = [];
            this.getFuluList();
        },
        handleClickLease(tab) {
            this.pageLease = 1;
            this.orderListLease = [];
            this.getLeaseList();
        },
        pagination(val) {
            this.checkAll = false;
            this.page = val.page;
            this.pageSize = val.limit;
            this.initOrderInfo();
        },
        paginationFulu(val) {
            this.pageFulu = val.page;
            this.getFuluList();
        },
        paginationLease(val) {
            this.pageLease = val.page;
            this.getLeaseList();
        },
        //订单操作
        orderOperationAction(id, operationItem, bill_id = 0) {
            if (
                !operationItem ||
                operationItem.code == '' ||
                operationItem.code == undefined
            ) {
                return;
            }
            switch (operationItem.code) {
                case 'close':
                    this.orderOperationCloseDialog(id, operationItem);
                    break;
                case 'pay':
                    this.orderOperationPay(id, operationItem);
                    break;
                case 'receive':
                    this.orderOperationReceive(id, operationItem);
                    break;
                case 'express_info':
                    this.jumpOrderDetail(id, 'y');
                    break;
                case 'bill':
                    this.jumpBillDetail(bill_id);
                    break;
                case 'refund_all':
                    this.fullRefund(id);
                    break;
                default:
                    break;
            }
        },
        /**
         * 整单退款
         * @param order_id 订单ID
         */
        @confirm('提示', '确定整单退款？')
        async fullRefund(order_id) {
            const { code, msg } = await this.$post(
                '/afterSales/createAfterSalesAll',
                { order_id },
            );
            if (code === 0) {
                this.$message.success(msg);
                this.initOrderInfo();
            } else {
                this.$message.error(msg);
            }
        },
        onClickLeaseOpt(status, item) {
            switch (status) {
                // case 0: // 待付款 -> 取消订单 、支付
                //     this.closeOrder(item.id) // 取消订单
                //     break;
                case 2: // 待收货
                    this.orderReceived(item.id); // 确认收货
                    break;
                case 4: // 待归还
                    this.returnOrder(item.id); //申请归还
                    break;
                case 6: // 归还中
                    this.addressOrder(item); // 填写归还
                    break;
                case 7: // 待确认
                    this.returnDetails(item.id); // 归还详情
                    break;
                // case 1: // 待发货
                //     break;
                // case 3: //已完成
                //     break;
                // case 5: // 申请归还
                //     break;
                // case -1: //关闭
                //     break;
                // case -2: // 驳回申请归还
                //     break;
            }
        },
        // 关闭订单
        closeOrder(id) {
            console.log('111');
            this.$confirm('是否取消此订单?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            })
                .then(() => {
                    this.$get('/order/close', { order_id: id })
                        .then((res) => {
                            if (res.code === 0) {
                                this.$message.success(res.msg);
                                this.getLeaseList();
                                this.getOrderFind(
                                    parseInt(this.$route.query.oid),
                                );
                            } else {
                                this.$message.error(res.msg);
                            }
                        })
                        .catch(function (res) {
                            console.log(res);
                        });
                })
                .catch(() => {});
        },
        // 申请归还
        returnOrder(id) {
            console.log('id', id);
            this.$confirm('是否申请归还?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            })
                .then(() => {
                    this.$post('/lease/applyReturn', { order_id: id })
                        .then((res) => {
                            if (res.code === 0) {
                                this.$message.success(res.msg);
                                this.getLeaseList();
                                this.getOrderFind(
                                    parseInt(this.$route.query.oid),
                                );
                            } else {
                                this.$message.error(res.msg);
                            }
                        })
                        .catch(function (res) {
                            console.log(res);
                        });
                })
                .catch(() => {});
        },
        // 填写归还
        addressOrder(item) {
            // console.log('pid',item.order_items[0].product_id)
            this.openSelectAddress(item.order_items[0].product_id, item.id);
        },
        // 打开填写归还
        openSelectAddress(id, oid) {
            this.$refs.selectAddress.isShow = true;
            this.$refs.selectAddress.pid = id;
            this.$refs.selectAddress.oid = oid;
            if (this.address) {
                this.$refs.selectAddress.initAddress(this.address.id);
            } else {
                this.$refs.selectAddress.initAddress();
            }
        },
        //提交归还地址
        onSubmitExpress(params) {
            console.log('params', params);
            this.$post('/lease/returnExpress', params)
                .then((res) => {
                    if (res.code === 0) {
                        this.$message({
                            type: 'success',
                            message: '归还成功',
                        });
                        this.getLeaseList();
                        this.getOrderFind(parseInt(this.$route.query.oid));
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },
        // 归还详情弹窗
        returnDetails(id) {
            this.$refs.returnDetailsDialog.isShow = true;
            this.$refs.returnDetailsDialog.oid = id;
        },
        // 跳转至申请退款/退货
        jumpRefund(order, item_id) {
            this.$_blank('/refund', {
                order_id: this.$fn.encode(order.id),
                order_item_id: this.$fn.encode(item_id),
            });
        },
        // 确认收货
        orderOperationReceive(id, operationItem) {
            if (this.groupType === 4) {
                //租赁订单详情
                console.log('租赁订单详情 确认收货');
                this.$confirm('是否【' + operationItem.title + '】?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                })
                    .then(() => {
                        this.$post('/lease/confirmReceive', {
                            order_id: id,
                        }).then((res) => {
                            if (res.code === 0) {
                                this.$message.success(res.msg);
                                this.getLeaseList();
                            } else {
                                this.$message.error(res.msg);
                            }
                        });
                    })
                    .catch(() => {});
            } else {
                //商品订单详情
                console.log('商品订单详情 确认收货');
                this.$confirm('是否【' + operationItem.title + '】?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                })
                    .then(() => {
                        this.$get('/order/receive', { order_id: id }).then(
                            (res) => {
                                if (res.code === 0) {
                                    this.$message.success(res.msg);
                                    this.initOrderInfo();
                                } else {
                                    this.$message.error(res.msg);
                                }
                            },
                        );
                    })
                    .catch(() => {});
            }
        },
        orderReceived(id) {
            this.$confirm('是否确认收货?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            })
                .then(() => {
                    this.$post('/lease/confirmReceive', { order_id: id }).then(
                        (res) => {
                            if (res.code === 0) {
                                this.$message.success(res.msg);
                                this.getLeaseList();
                            } else {
                                this.$message.error(res.msg);
                            }
                        },
                    );
                })
                .catch(() => {});
        },
        //订单关闭
        orderOperationCloseDialog(id, operationItem) {
            this.$confirm('是否【' + operationItem.title + '】?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            })
                .then(() => {
                    this.orderOperationClose(id, operationItem);
                })
                .catch(() => {});
        },

        orderOperationClose(id, operationItem) {
            let that = this;
            let para = {
                order_id: id,
            };
            this.$get('/order/close', para)
                .then(function (res) {
                    if (res.code == 0) {
                        console.log(res);
                        that.$message.success(res.msg);
                        that.initOrderInfo();
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },

        //订单支付
        orderOperationPay(id, operationItem) {
            let that = this;
            that.$router.push('/payment?para=' + id);
            // let para = {
            //     "order_ids": [id]
            // }
            // this.$post("/trade/cashier", para).then(function (res) {
            //     console.log(res);
            //     if (res.code == 0) {
            //         ///goodsDetail?goods_id=${item.id}`
            //         that.$router.push("/payment?para="+id);
            //     }
            // }).catch(function (res) {
            //     console.log(res);
            // });
        },
        // 勾选订单
        batchChange(value) {
            let a = false;
            let val = this.batchIDObj.includes(value);
            if (val) {
                this.batchIDObj = this.batchIDObj.filter(
                    (item) => item != value,
                );
            } else {
                this.batchIDObj.push(value);
            }

            //  判断 是否 是全选
            for (let index = 0; index < this.orderList.length; index++) {
                if (
                    undefined ==
                    this.batchIDObj.find(
                        (item) => item == this.orderList[index].id,
                    )
                ) {
                    a = true;
                    break;
                }
            }
            if (!a) {
                this.checkAll = true;
            } else {
                this.checkAll = false;
            }
        },
        // 批量支付
        bulkPayment() {
            let that = this;
            if (this.batchIDObj.length == 0) {
                that.$message('至少选中一件商品');
                return;
            }
            that.$router.push('/payment?para=' + this.batchIDObj);
            // this.$get("/order/close", para).then(function (res) {
            //     if (res.code == 0) {
            //         that.$message.success(res.msg);
            //         that.initOrderInfo();
            //     }
            // }).catch(function (res) {
            //     console.log(res);
            // });
        },
        // 批量取消
        batchCancellation() {
            let that = this;
            if (this.batchIDObj.length == 0) {
                that.$message('至少选中一件商品');
                return;
            }

            this.$post('/order/batchClose', { order_ids: this.batchIDObj })
                .then(function (res) {
                    if (res.code == 0) {
                        that.$message.success(res.msg);
                        that.batchIDObj = [];
                        that.page = 1;
                        that.initOrderInfo();
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },
        // 全选
        checkAllfun() {
            if (this.checkAll) {
                for (let i = 0; i < this.orderList.length; i++) {
                    let val = this.batchIDObj.find(
                        (item) => item == this.orderList[i].id,
                    );
                    if (!val) {
                        this.batchIDObj.push(this.orderList[i].id);
                    }
                }
            } else {
                for (let i = 0; i < this.orderList.length; i++) {
                    let val = this.batchIDObj.find(
                        (item) => item == this.orderList[i].id,
                    );
                    if (val) {
                        this.batchIDObj = this.batchIDObj.filter(
                            (item) => item != this.orderList[i].id,
                        );
                    }
                }
            }
            console.log(this.batchIDObj);
        },
    },
};
