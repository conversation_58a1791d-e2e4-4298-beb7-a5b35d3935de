<div class="list-box">
    <div class="search-box f fac fjsb">
        <div class="search-left-box">
            <m-date v-model="dateValue" size="small"></m-date>
            <el-select v-model="business_type" size="small" class="ml10" placeholder="业务类型" clearable>
                <el-option v-for="item in typeOptios" :key="item.id" :label="item.name" :value="item.value"></el-option>
            </el-select>
<!--            <el-input v-model="order_sn" placeholder="订单号" class="ml10" style="width: 220px;" size="small"></el-input>-->
            <el-input v-model="pay_sn" placeholder="支付编号" class="ml10" style="width: 220px;" size="small"></el-input>
            <el-button size="small" class="search-btn ml10" @click="search">确定</el-button>

        </div>
        <el-button class="red-btn" size="small" @click="exportTable">导出</el-button>
    </div>
    <el-table :data="list" class="mt10">
        <el-table-column align="center" label="日期">
            <template slot-scope="scope">
                {{ scope.row.created_at | formatDate }}
            </template>
        </el-table-column>
        <el-table-column align="center" label="收入/支出">
            <template slot-scope="scope">
                <span v-if="scope.row.business_type === 1">-{{ scope.row.amount | formatF2Y }}</span>
                <span v-if="scope.row.business_type === 2">-{{ scope.row.amount | formatF2Y }}</span>
                <span v-if="scope.row.business_type === 3">+{{ scope.row.amount | formatF2Y }}</span>
                <span v-if="scope.row.business_type === 4">+{{ scope.row.amount | formatF2Y }}</span>
                <span v-if="scope.row.business_type === 5">+{{ scope.row.amount | formatF2Y }}</span>
                <span v-if="scope.row.business_type === 6">-{{ scope.row.amount | formatF2Y }}</span>
            </template>
        </el-table-column>
        <el-table-column align="center" label="业务类型">
            <template slot-scope="scope">
                <p>
                    <!-- <span v-if="scope.row.business_type === 1">汇聚余额采购</span> -->
                    <span v-if="scope.row.business_type === 2">站内余额采购</span>
                    <!-- <span v-if="scope.row.business_type === 3">汇聚余额充值</span> -->
                    <span v-if="scope.row.business_type === 4">站内余额充值</span>
                    <span v-if="scope.row.business_type === 5">站内余额退款</span>
                    <span v-if="scope.row.business_type === 6">站内余额扣除</span>
                </p>
                <p>支付编号: <el-button type="text" @click="jumpOrderDetail(scope.row)">{{scope.row.pay_sn}}</el-button>
                </p>
<!--                <p>订单号: {{scope.row.order_sn}}
                </p>-->
            </template>
        </el-table-column>
        <el-table-column align="center" label="余额">
            <template slot-scope="scope">
                {{ scope.row.balance | formatF2Y }}
            </template>
        </el-table-column>
        <el-table-column align="center" label="备注">
            <template slot-scope="scope">
                {{ scope.row.remarks }}
            </template>
        </el-table-column>
    </el-table>
    <Pagination :total="total" @pagination="pagination" :limit="pageSize" layout="total, prev, pager, next">
    </Pagination>
</div>