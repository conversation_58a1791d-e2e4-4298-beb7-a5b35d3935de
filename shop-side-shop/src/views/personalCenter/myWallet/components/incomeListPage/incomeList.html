<div class="list-box">
    <div class="search-box f fac fjsb">
        <div class="search-left-box">
            <m-date v-model="dateValue" size="small"></m-date>
            <el-select v-model="income_type" size="small" class="ml10" placeholder="业务类型" clearable>
                <el-option v-for="item in typeOptios" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
            <el-button size="small" class="search-btn ml10" @click="search">确定</el-button>

        </div>
        <el-button class="red-btn" size="small" @click="exportTable">导出</el-button>
    </div>
    <el-table :data="list" class="mt10">
        <el-table-column align="center" label="日期">
            <template slot-scope="scope">
                {{ scope.row.created_at | formatDate }}
            </template>
        </el-table-column>
        <el-table-column align="center" label="收入变动金额">
            <template slot-scope="scope">
                {{ scope.row.amount | formatF2Y }}
            </template>
        </el-table-column>
        <el-table-column align="center" label="收入类型">
            <template slot-scope="scope">
                {{ scope.row.income_name }}
            </template>
        </el-table-column>
        <el-table-column align="center" label="收入变动后余额">
            <template slot-scope="scope">
                {{ scope.row.balance | formatF2Y }}
            </template>
        </el-table-column>
        <el-table-column align="center" label="订单号">
            <template slot-scope="scope">
                {{ scope.row.order_sn }}
            </template>
        </el-table-column>
    </el-table>
    <Pagination :total="total" @pagination="pagination" :limit="pageSize" layout="total, prev, pager, next">
    </Pagination>
</div>