let polling = null;
import QRCode from 'qrcodejs2'

export default {
    name: 'rechargeDialog',
    props: {
        station_balance_recharge: Array,
        default: () => [],
    },
    data() {
        return {
            status: 1, // 1 = this.title 2 = 扫码支付
            title: '充值余额',
            pay_type: 1, //充值方式(1汇聚,2平台余额)
            qr_code: '',
            isWechart: 0, // 1-站内微信支付
            isShow: false,
            formData: {
                amount: 0,
            },
            show_type: '', //支付返回类型
            pay_code: '', // 聚合支付返回的类型
        };
    },
    methods: {
        // 汇聚充值
        confirm() {
            if (this.formData.amount <= 0) {
                this.$message.error('请输入要充值的金额数');
                return false;
            }
            let num = this.formData.amount;
            let params = {
                amount: this.$fn.changeMoneyY2F(num),
                pay_type: this.pay_type,
            };
            this.$post('/finance/userJoinRecharge', params).then((res) => {
                if (res.code === 0) {
                    this.status = 2;
                    this.qr_code = res.data.rd_Pic;
                    // 开始轮询支付状态
                    polling = setInterval(() => {
                        this.$post('/finance/getRechargeStatus', {
                            pay_sn: parseInt(res.data.r2_OrderNo),
                        }).then((res) => {
                            if (res.code === 0) {
                                // pay_status 1 = 已支付 0 = 未支付
                                if (res.data.pay_status === 1) {
                                    // clearInterval(polling)
                                    this.$message.success(res.msg);
                                    this.handleClose();
                                    this.$emit('reload');
                                }
                            }
                        });
                    }, 1500);
                } else {
                    this.$message.error(res.msg);
                    return false;
                }
            });
        },
        // 站内充值
        async payWechart(url, type, code) {
            this.pay_code = code
            //api:1.汇聚微信支付,2.微信支付
            if (this.formData.amount <= 0) {
                this.$message.error('请输入要充值的金额数');
                return false;
            }
            let num = this.formData.amount;
            let params = {
                amount: this.$fn.changeMoneyY2F(num),
                pay_type: type,
            };
            if (code === 'ALIPAY') {
                params.type = 'P'
                const res = await this.$post(url, params)
                if (res.code === 0) {
                    this.status = 2;
                    this.$nextTick(() => {
                        const qrCodeElement = this.$refs.qrCodeALiPayUrl;
                        if (qrCodeElement) {
                            qrCodeElement.innerHTML = ''; // 清空内容
                            new QRCode(qrCodeElement, {
                                text: res.data.fields.code, // 转换为二维码的内容
                                width: 160,
                                height: 160,
                                colorDark: '#000000',
                                colorLight: '#ffffff',
                                correctLevel: QRCode.CorrectLevel.H
                            });
                        } else {
                            console.error("二维码容器元素不可用");
                        }
                    });
                    polling = setInterval(() => {
                        this.$post('/finance/getStationRechargeStatus', {
                            pay_sn: parseInt(res.data.pay_sn),
                        }).then((res2) => {
                            if (res2.code === 0) {
                                // pay_status 1 = 已支付 0 = 未支付
                                if (res2.data.pay_status === 1) {
                                    // clearInterval(polling)
                                    this.$message.success(res2.msg);
                                    this.handleClose();
                                    this.$emit('reload');
                                }
                            }
                        });
                    }, 1500);
                }
            } else if (code === 'QUICKPAY') {
                params.type = 'P'
                const res = await this.$post(url, params)
                if (res.code === 0) {
                    window.open(res.data.fields.counter_url)
                    polling = setInterval(() => {
                        this.$post('/finance/getStationRechargeStatus', {
                            pay_sn: parseInt(res.data.pay_sn),
                        }).then((res2) => {
                            if (res2.code === 0) {
                                // pay_status 1 = 已支付 0 = 未支付
                                if (res2.data.pay_status === 1) {
                                    // clearInterval(polling)
                                    this.$message.success(res2.msg);
                                    this.handleClose();
                                    this.$emit('reload');
                                }
                            }
                        });
                    }, 1500);
                }
            } else if (code === 'WECHAT') {
                params.type = 'P'
                const res = await this.$post('/finance/rechargePayId', params)
                if (res.code === 0) {
                    this.status = 2;
                    this.$nextTick(() => {
                        const qrCodeWeChatUrl = this.$refs.qrCodeWeChatUrl;
                        if (qrCodeWeChatUrl) {
                            qrCodeWeChatUrl.innerHTML = ''; // 清空内容
                            new QRCode(qrCodeWeChatUrl, {
                                text: origin + '/h5/?menu#/packageB/member/juHeRecharge?amount=' + this.$fn.changeMoneyY2F(num) + '&pay_type=' + type + '&pay_info_id=' + res.data.pay_info_id, // 转换为二维码的内容
                                width: 160,
                                height: 160,
                                colorDark: '#000000',
                                colorLight: '#ffffff',
                                correctLevel: QRCode.CorrectLevel.H
                            });
                            polling = setInterval(() => {
                                this.$post('/finance/getStationRechargeStatus', {
                                    pay_sn: parseInt(res.data.pay_sn),
                                }).then((res2) => {
                                    if (res2.code === 0) {
                                        // pay_status 1 = 已支付 0 = 未支付
                                        if (res2.data.pay_status === 1) {
                                            // clearInterval(polling)
                                            this.$message.success(res2.msg);
                                            this.handleClose();
                                            this.$emit('reload');
                                        }
                                    }
                                });
                            }, 1500);
                        } else {
                            console.error("二维码容器元素不可用");
                        }
                    });
                    /* console.log('ewqewqeqweq', res);
                    let origin = window.location.origin
                    console.log('qweqwewqe', window.location);
                    this.$refs.qrCodeWeChatUrl.innerHTML = '';
                    new QRCode(this.$refs.qrCodeWeChatUrl, {
                        text: origin + '/h5/?menu#/packageA/goodsorder/juHeOrderPay/juHeOrderPay?amount=' + this.$fn.changeMoneyY2F(num) + '&pay_type=' + pay_type + '&pay_info_id=' + res.data, // 转换为二维码的内容
                        width: 160,
                        height: 160,
                        colorDark: '#000000',
                        colorLight: '#ffffff',
                        correctLevel: QRCode.CorrectLevel.H
                    }); */
                }
            } else {
                //     let api
                //     switch (item) {
                //         case 1:
                //             api = '/finance/wechatRechargePay' //汇聚微信支付
                //             params.pay_type = 1
                //             break;
                //         case 2:
                //             api = '/finance/weChatRecharge' //微信支付
                //             params.pay_type = 4
                //             this.isWechart = 1
                //             break;
                //         default:
                //             break;
                //     }
                this.$post(url, params).then((res) => {
                    if (res.code === 0) {
                        this.status = 2;
                        this.show_type = res.data.show_type;
                        // this.qr_code = res.data.rd_Pic;
                        if (type === 4) {
                            // 站内微信
                            this.qr_code = location.origin + '/' + res.data.img;
                            // console.log('微信支付 this.qr_code',this.qr_code)
                        } else {
                            // console.log('站内-汇聚微信支付')
                            if (type === 7000) {
                                // 跳转到拉卡拉支付平台
                                window.open(res.data.url, '_blank');
                            } else {
                                this.qr_code = res.data.rd_Pic;
                            }
                        }
                        // 开始轮询支付状态
                        polling = setInterval(() => {
                            const params = {};
                            if (type === 7000 || type === 4) {
                                params.pay_sn = parseInt(res.data.pay_sn);
                            } else {
                                params.pay_sn = parseInt(res.data.r2_OrderNo);
                            }
                            this.$post(
                                '/finance/getStationRechargeStatus',
                                params,
                            ).then((res) => {
                                if (res.code === 0) {
                                    // pay_status 1 = 已支付 0 = 未支付
                                    if (res.data.pay_status === 1) {
                                        // clearInterval(polling)
                                        this.$message.success(res.msg);
                                        this.handleClose();
                                        this.$emit('reload');
                                    }
                                }
                            });
                        }, 1500);
                    } else {
                        this.$message.error(res.msg);
                        return false;
                    }
                });
            }
        },
        handleClose() {
            this.formData.amount = 0;
            this.status = 1;
            this.qr_code = '';
            clearInterval(polling);
            this.isShow = false;
        },
    },
};
