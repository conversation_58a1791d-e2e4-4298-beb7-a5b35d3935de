<!-- 个人资料 -->
<div class="personal-box bgw">
    <p class="title-p">个人资料</p>
    <div class="form-box f">
        <div class="avatar-box">
            <el-avatar v-if="formData.avatar" fit="fill" :size="80" :src="formData.avatar"></el-avatar>
            <el-avatar v-else :size="80" fit="fill"
                src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png">
            </el-avatar>
            <el-upload class="avatar-uploader" :show-file-list="false" :action="path+'/common/upload'"
                :headers="{'x-token':token}" :on-success="handleAvatarImgSuccess" :before-upload="beforeAvatarUpload"
                accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                <img src="../../../../assets/images/edit.png" class="edit-img">
            </el-upload>

        </div>
        <el-form :model="formData" label-width="90px" :rules="rules">
            <el-form-item label="手机号">
                <el-input class="w365" v-model="formData.username" disabled></el-input>
                <el-button class="ml10" size="mini" type="danger" @click="changeUsername">修改手机号</el-button>
            </el-form-item>
            <el-form-item label="用户昵称">
                <el-input class="w365" v-model="formData.nickname"></el-input>
            </el-form-item>
            <el-form-item label="微信号">
                <el-input class="w365" v-model="formData.wx_username"></el-input>
            </el-form-item>
            <el-form-item label="微信二维码">

                <el-upload class="avatar-uploader1" :show-file-list="false" :action="path+'/common/upload'"
                    :headers="{'x-token':token}" :on-success="handleWeChatQrCodeImgSuccess"
                    :before-upload="beforeAvatarUpload"
                    accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                    <img v-if="formData.qr_code" :src="formData.qr_code" class="ewm-img">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
            </el-form-item>
            <el-form-item label="登录密码">
                <el-button size="mini" type="danger" @click="openFindPassword">修改</el-button>
            </el-form-item>
            <el-form-item label="身份证"  prop="id_card">
                <el-input class="w365" v-model="formData.id_card"></el-input>
            </el-form-item>
            <el-form-item label="支付宝账号">
                <el-input class="w365" v-model="formData.ali_account"></el-input>
            </el-form-item>
            <el-form-item label="姓名">
                <el-input class="w365" v-model="formData.full_name"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button class="saveBtn" @click="save()">保存</el-button>
            </el-form-item>
        </el-form>
    </div>
    <find-password-dialog ref="findPasswordDialog"></find-password-dialog>
    <el-dialog
        title="提示"
        :visible.sync="dialogVisible"
        width="30%"
    >
    <el-form :model="mobileForm">
            <el-form-item prop="username">
                <el-input placeholder="请填写手机号" v-model="mobileForm.username">
                </el-input>
            </el-form-item>
            <el-form-item prop="captcha_code" class="code-box">
                <el-input placeholder="请输入验证码" v-model="mobileForm.captcha_code">
                    <a slot="append" href="javascript:;" @click="sendCode" v-if="codeIsShow">发送验证码</a>
                    <p v-else class="countDown-p" slot="append">{{ countDown }}s</p>
                </el-input>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="danger" @click="changeUsernameMobile">确 定</el-button>
        </span>
    </el-dialog>
</div>