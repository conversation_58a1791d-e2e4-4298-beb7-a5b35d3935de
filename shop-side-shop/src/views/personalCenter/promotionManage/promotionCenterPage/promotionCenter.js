export default {
    name: "promotion<PERSON>enter",
    data() {
        return {
            IsAgent: "",
            formData: {
                mobile: '',//创建人电话
                invite_code: '',//邀请码
                avatar: '',//头像
                nickname: "",//创建人
                parent_user:{}
            }
        }
    },
    mounted() {
        this.ageInfo()
    },
    computed: {
        getInviteLink() {
            // return location.origin + "/invite?id=" + this.$ls.getUserId()
            return location.origin + "/?shared=" + this.$fn.encode(this.$ls.getUserId())
        }
    },
    methods: {
        ageInfo() {
            this.$post("user/agentInfo").then(res => {
                if (res.code === 0) {
                    this.IsAgent = res.data.IsAgent
                    this.formData.id = res.data.User.id
                    this.formData.mobile = res.data.User.parent_user.mobile ? res.data.User.parent_user.mobile : res.data.User.parent_user.username
                    this.formData.avatar = res.data.User.avatar
                    this.formData.nickname = res.data.User.parent_user.nickname
                    this.formData.invite_code = res.data.User.invite_code
                    this.formData.parent_user = res.data.User.parent_user
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        linkcopy(code) {
            var input = document.createElement("input"); // 直接构建input
            input.value = code;// 设置内容
            document.body.appendChild(input); // 添加临时实例
            input.select(); // 选择实例内容
            document.execCommand("Copy"); // 执行复制
            document.body.removeChild(input); // 删除临时实例
            this.$message({
                type: "success",
                message: "复制成功"
            });
        }
    }
}