<!-- 收件人管理 -->
<div class="recipients-box">
    <div class="top bgw f fac fjsb">
        <p class="title-p">收件人管理</p>
        <el-button @click="openDialog(false)">添加收件人</el-button>
    </div>
    <table cellspacing="0" cellpadding="0" class="w100 table-box">
        <thead class=" bgw">
        <tr>
            <td>收货人</td>
            <td>收货人手机号</td>
            <td>所在地区</td>
            <td>详细地址</td>
            <td>操作</td>
        </tr>
        </thead>
        <tbody>
        <tr v-for="item in addressList" :key="item.id">
            <td>{{item.realname}}</td>
            <td>{{item.mobile}}</td>
            <td>{{item.province}} {{item.city}} {{item.county}} {{item.town}}</td>
            <td>{{item.detail}}</td>
            <td>
                <el-button @click="openDialog(true,item)" type="text">修改</el-button>
                <el-divider direction="vertical"></el-divider>
                <el-button type="text" @click="deleteItemDialog(item)">删除</el-button>
                <el-divider direction="vertical"></el-divider>
                <el-button type="text" style="color: #000000" v-if="item.is_default" @click="setDefaultItem(item)">
                    默认地址
                </el-button>
                <el-button type="text" v-else @click="setDefaultItem(item)">设为默认</el-button>
            </td>
        </tr>
        </tbody>
    </table>
    <Pagination :total="total" @pagination="pagination" :limit="pageSize" layout="total, prev, pager, next">
    </Pagination>
    <Dialog ref="dialog" @callBackAddressList="initAddressList"></Dialog>
</div>
