<template>
  <div class="order-invoice-dialog">
    <el-dialog
        :visible="dialogVisible"
        width="814px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="handleClose"
    >
      <span slot="title">发票信息</span>
      <div class="header-tabs">
        <el-radio-group
            v-model="invoiceType"
            class="tab-select"
            @change="changeInvoiceType"
        >
          <!-- <el-radio :label="1" border>电子发票</el-radio> -->
          <!-- <el-radio :label="2" border>纸质发票</el-radio> -->
          <!-- <el-radio :label="2" border>增值税发票</el-radio> -->
          <el-radio
              v-for="item in setting.bill_type"
              :key="item.id"
              border
              :label="item"
          >{{ item === 1 ? "电子发票" : "增值税专用发票" }}
          </el-radio>
        </el-radio-group>
      </div>
      <div class="tips" v-if="invoiceType == 1">
        电子普通发票是税务机关认可的有效凭证，与纸质普通发票具有同等法律效力，可用于报销入账，售后维权等。
      </div>

      <el-form
          :model="formData"
          :rules="rules_1"
          size="small"
          ref="ordinaryInvoiceForm"
          label-width="130px"
          class="invoice-form"
      >
        <!-- 普通电子发票 -->
        <template v-if="invoiceType == 1">
          <el-form-item label="发票内容">
            <el-radio-group
                v-model="formData.detailType"
                class="tab-select"
                @change="changeDetailType"
            >
              <el-radio :label="1" border>商品明细</el-radio>
              <el-radio :label="2" border>商品类别</el-radio>
              <el-radio :label="0" border>不开发票</el-radio>
            </el-radio-group>
          </el-form-item>
          <template v-if="formData.detailType !== 0">
            <el-form-item label="发票抬头">
              <el-radio-group
                  v-model="formData.accountType"
                  class="tab-select"
                  @change="changeAccountType"
              >
                <el-radio :label="1" border>个人</el-radio>
                <el-radio :label="2" border>单位</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
                v-if="formData.accountType == 1"
                label="个人名称"
                prop="personName"
            >
              <el-input
                  v-model="formData.personName"
                  placeholder="请填写“个人”或您的姓名"
              ></el-input>
            </el-form-item>
            <el-form-item
                v-if="formData.accountType == 2"
                label="单位名称"
                prop="companyName"
            >
              <el-input
                  v-model="formData.companyName"
                  placeholder="请填写单位名称"
              ></el-input>
            </el-form-item>
            <el-form-item
                v-if="formData.accountType == 2"
                label="纳税人识别号"
                prop="accountNumber"
            >
              <el-input
                  v-model="formData.accountNumber"
                  placeholder="请填写纳税人识别号"
              ></el-input>
            </el-form-item>
            <el-form-item label="更多选填选项" v-if="formData.accountType == 2">
              <div class="more-info-box">
                <div class="f fac fjsb" @click="handleExtend">
                  <span>填写单位地址、电话、开户银行及账号</span>
                  <i v-if="!formData.isExtend" class="el-icon-arrow-down"></i>
                  <i v-else class="el-icon-arrow-up"></i>
                </div>
                <div v-if="formData.isExtend">
                  <el-form-item label="单位地址" label-width="80px" style="">
                    <el-input
                        v-model="formData.registerAddress"
                        placeholder="请填写单位地址"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="单位电话" label-width="80px" style="">
                    <el-input
                        v-model="formData.registerPhone"
                        placeholder="请填写单位电话"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="开户银行" label-width="80px" style="">
                    <el-input
                        v-model="formData.bank"
                        placeholder="请填写开户银行"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="银行账号" label-width="80px" style="">
                    <el-input
                        v-model="formData.bankAccount"
                        placeholder="请填写银行账号"
                    ></el-input>
                  </el-form-item>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="收票人手机" prop="mobile">
              <el-input
                  v-model.number="formData.mobile"
                  placeholder="请填写收票人手机号"
              ></el-input>
            </el-form-item>
            <el-form-item label="收票人邮箱" prop="email">
              <el-input
                  v-model="formData.email"
                  placeholder="请填写收票人邮箱"
              ></el-input>
            </el-form-item>
          </template>
        </template>

        <!-- 增值税发票 -->
        <template v-if="invoiceType == 2">
          <el-form-item label="单位名称" prop="companyName">
            <el-input
                v-model="formData.companyName"
                placeholder="请填写单位名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="纳税人识别号" prop="accountNumber">
            <el-input
                v-model="formData.accountNumber"
                placeholder="请填写纳税人识别号"
            ></el-input>
          </el-form-item>
          <el-form-item label="注册地址" prop="registerAddress">
            <el-input
                v-model="formData.registerAddress"
                placeholder="请填写单位地址"
            ></el-input>
          </el-form-item>
          <el-form-item label="注册电话" prop="registerPhone">
            <el-input
                v-model="formData.registerPhone"
                placeholder="请填写注册电话"
            ></el-input>
          </el-form-item>
          <el-form-item label="开户银行" prop="bank">
            <el-input
                v-model="formData.bank"
                placeholder="请填写开户银行"
            ></el-input>
          </el-form-item>
          <el-form-item label="银行账号" prop="bankAccount">
            <el-input
                v-model="formData.bankAccount"
                placeholder="请填写银行账号"
            ></el-input>
          </el-form-item>
          <el-form-item label="收票人手机" prop="mobile">
            <el-input
                v-model="formData.mobile"
                placeholder="请填写收票人手机"
            ></el-input>
          </el-form-item>
          <el-form-item label="收票人邮箱" prop="email">
            <el-input
                v-model="formData.email"
                placeholder="请填写收票人邮箱"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <span slot="label"><span class="color-red">*</span> 收票地址</span>
            <el-button @click="openSelectAddressById">选择收票地址</el-button>
            <el-button @click="openRecipientsDialogById">新增收票地址</el-button>
            <p>{{ address }}</p>
          </el-form-item>
        </template>
      </el-form>
      <div class="text-center cz-btn-box">
        <el-button @click="handleSubmit">确定</el-button>
        <el-button @click="handleClose">取消</el-button>
      </div>
    </el-dialog>
    <!-- 新增收货人及地址 -->
    <RecipientsDialog ref="recipientsDialogById" @callBackAddressList="callBackNewAddressById"></RecipientsDialog>
    <!-- 选择收货人及地址 -->
    <select-address ref="selectAddressById" @openQrBox="resSelectAdd"></select-address>
  </div>
</template>

<script>
//新增收货人地址
import RecipientsDialog from "@/views/personalCenter/recipients/components/dialog";
//选择收货地址
import SelectAddress from "@/views/buyerCart/components/selectAddress";
import verify from "@/utils/verify";

export default {
  components: {RecipientsDialog, SelectAddress},
  data() {
    return {
      // 选中地址
      address: "",
      dialogVisible: false,
      invoiceType: 1, // 1-普通发票 2-增税发票
      setting: {},
      // 普通发票
      formData: {
        accountType: 1,
        personName: "",
        accountNumber: "",
        detailType: 1,
        mobile: "",
        email: "",
        isExtend: false,
        companyName: "",
        registerAddress: "",
        registerPhone: "",
        bank: "",
        bankAccount: "",
        address_id: null,
      },
      rules_1: {
        personName: {
          required: true,
          message: "请填写单位名称",
          trigger: "change",
        },
        companyName: {
          required: true,
          message: "请填写单位名称",
          trigger: "change",
        },
        accountNumber: {
          required: true,
          message: "请填写纳税人识别号",
          trigger: "change",
        },
        registerAddress: {
          required: true,
          message: "请填写注册地址",
          trigger: "change",
        },
        registerPhone: {
          required: true,
          message: "请填写注册电话",
          trigger: "change",
        },
        bank: {required: true, message: "请填写开户银行", trigger: "change"},
        bankAccount: {
          required: true,
          message: "请填写开户账号",
          trigger: "change",
        },
        address_id: {
          required: true,
          message: "请选择收票地址",
          trigger: "change",
        },
        mobile:[ 
          {
            required: true,
            message: "请填写手机号",
            trigger: "change",
          },
          {
            validator: (rule, value, callback) => {
                let res = verify.checkPhone(value);
                return res ? callback() : callback(new Error("手机号格式不正确"));
            },
          },
        ],
        email: {
          validator: (rule, value, callback) => {
            if (value) {
              if (verify.checkEmail(value)) {
                callback()
              } else {
                return callback(new Error("邮箱格式不正确"))
              }
            } else {
              callback()
            }
          },
          trigger: "blur"
        }
      },
    };
  },

  methods: {
    callBackNewAddressById(data) {
      if (!data) {
        return;
      }
      this.formData.address_id = data.id
      this.address = this.assemblyAddress(data)
    },
    openRecipientsDialogById() {
      this.$refs.recipientsDialogById.dialogIsShow = true;
      this.$refs.recipientsDialogById.isEditAddress = false;
      this.$refs.recipientsDialogById.title = "新增收票地址";
      this.$refs.recipientsDialogById.initFormData();
    },
    resSelectAdd(data) {
      this.formData.address_id = data.id
      this.address = this.assemblyAddress(data)
    },
    //组装地址
    assemblyAddress(address) {
      let addressStr = "";
      if (!address) {
        return "请选择收货地址";
      }
      addressStr = address.realname + "  " + address.mobile + "  " + address.province + "  " + address.city + "  " + address.county + "  " + address.town + "  " + address.detail;
      return addressStr;
    },
    // 打开选择收票地址
    openSelectAddressById() {
      this.$refs.selectAddressById.isShow = true;
      let addId = null;
      if (this.formData.address_id) addId = this.formData.address_id
      this.$refs.selectAddressById.initAddress(addId);
    },
    handleClose() {
      this.dialogVisible = false;
    },
    handleExtend() {
      this.formData.isExtend = !this.formData.isExtend;
    },
    handleSubmit() {
      this.$refs["ordinaryInvoiceForm"].validate((valid) => {
        if (valid) {
          if (this.invoiceType === 2 && !this.formData.address_id) {
            this.$message.error("请选择收票地址")
            return
          }
          let json = {type: this.invoiceType, ...this.formData};
          this.$emit("submitForm", json);
        } else {
          this.$message.error("请先完成表单");
        }
      });
    },

    // 发票类型
    changeInvoiceType() {
      this.initForm();
    },
    // 发票内容
    changeDetailType(val) {
      if (val === 0) {
        this.initForm();
        this.dialogVisible = false;
        this.invoiceType = 1 // 普通电子发票
        this.formData.detailType = 0 // 不开发票
        let json = {type: this.invoiceType, ...this.formData};
        this.$emit("submitForm", json);
      }
    },
    // 抬头类型
    changeAccountType(val) {
      this.initForm();
      this.formData.accountType = val;
    },
    initForm() {
      this.address = ""
      this.formData = {
        accountType: 1,
        personName: "",
        accountNumber: "",
        detailType: 1,
        mobile: "",
        email: "",
        isExtend: false,
        companyName: "",
        registerAddress: "",
        registerPhone: "",
        bank: "",
        bankAccount: "",
        address_id: null,
      };
      this.$nextTick(() => {
        this.$refs["ordinaryInvoiceForm"].resetFields();
      });
    },
  },
};
</script>
<style lang='scss' rel='stylesheet/scss' scoped>
.order-invoice-dialog {
  ::v-deep .tab-select {
    .el-radio__input {
      display: none;
    }

    .is-checked {
      border-color: #f42121 !important;

      .el-radio__label {
        color: #f42121;
      }
    }
  }

  .tips {
    margin-top: 20px;
    padding: 10px 20px;
    border-radius: 5px;
    color: #333333;
    background-color: #efefef;
  }

  .invoice-form {
    margin-top: 30px;
    width: 70%;

    .el-form-item {
      margin-bottom: 20px;
    }

    .more-info-box {
      padding: 3px 5px;
      border: 1px solid #dcdfe6;
      cursor: pointer;
      overflow: hidden;
    }
  }

  .cz-btn-box {
    margin-top: 15px;

    .el-button {
      width: 118px;
      height: 37px;
      font-size: 16px;
      border-radius: 3px;
      padding: 0;

      &:nth-child(1) {
        background: #f42121;
        border: 1px solid #f42121;
        color: white;
        margin-right: 19px;
      }

      &:last-child {
        background: #cccccc;
        border: 1px solid #cccccc;
        color: #333333;
      }
    }
  }
}
</style>