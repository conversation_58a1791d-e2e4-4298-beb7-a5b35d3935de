<template>
    <div>
        <div class="bgw">
            <!-- 排序 -->
            <div class="check-all mt_15">
                <div class="check-all-left">
                    <SortButtonGroup v-model="sortForm" @change="onChangeSort">
                        <template v-for="item in sort">
                            <sortButtonTwo :key="item.id" :sort_type="item.id">
                                <span
                                    v-if="item.id == sortid"
                                    style="color: #f42121"
                                >
                                    {{ item.name }}
                                </span>
                                <span v-else @click="sortid = item.id">
                                    {{ item.name }}
                                </span>
                            </sortButtonTwo>
                        </template>
                    </SortButtonGroup>
                </div>
            </div>
        </div>
        <div class="bgw mt_15 f fac select-gallery fw">
            <div
                class="gallery-box mb20 mr_15"
                v-for="item in tableDate"
                :key="item.id"
            >
                <div class="gallery-title">{{ item.name }}</div>
                <div class="gallery-time">
                    最近选品时间: {{ item.recent_selection_at | formatDate }}
                </div>
                <div class="mt20" v-if="item.products">
                    <img
                        v-for="items in item.products"
                        :key="items.id"
                        class="mr10"
                        style="width: 110px; height: 110px"
                        :src="items.image_url"
                    />
                </div>
                <div class="f fac fjsa mt10">
                    <div class="f-column fac gallery-money">
                        <div>{{ item.order_amount | formatF2Y }}</div>
                        <div class="mt5 fs14">累计订单金额（元）</div>
                    </div>
                    <div class="f-column fac gallery-money">
                        <div>{{ item.browse_count }}</div>
                        <div class="mt5 fs14">访问次数（次）</div>
                    </div>
                </div>
                <div class="f fac fjsa mt20 fs12">
                    <div class="red shou" @click="galleryDetail(item)">
                        查看详情（商品{{ item.product_count }}）
                    </div>
                    <div class="gray">已售 {{ item.product_sale_count }}</div>
                </div>
            </div>
        </div>
        <div class="paging">
            <Pagination
                :current-page.sync="paginationValue.currentpage"
                :limit="paginationValue.limit"
                :page="paginationValue.page"
                :total="paginationValue.total"
                layout="total, prev, pager, next, jumper"
                @pagination="pagination"
            ></Pagination>
        </div>
    </div>
</template>

<script>
import SortButtonGroup from '@/components/sortButton/sortButtonGroup.vue';
import sortButtonTwo from '@/components/sortButton/sortButtonTwo.vue';

export default {
    name: 'list',
    components: { SortButtonGroup, sortButtonTwo },
    data() {
        return {
            sort: [
                { id: 1, name: '商品数量', sort_type: 1 },
                { id: 2, name: '订单数量', sort_type: 2 },
                { id: 3, name: '订单金额', sort_type: 3 },
                { id: 4, name: '最近选品', sort_type: 4 },
                { id: 5, name: '最新共享', sort_type: 5 },
                { id: 6, name: '热门访问', sort_type: 6 },
            ],
            sortForm: {
                sort_type: null, // 1商品数量 2订单数量 3订单金额 4最近选品时间 5最新共享时间 6热门访问
                sort: null, // true 为升序，false 为降序
            },
            sortid: 0, // 当前排序id
            // 分页数据
            paginationValue: {
                page: 1,
                limit: 24,
                total: 0,
                currentpage: 1,
            },
            tableDate: [],
        };
    },
    mounted() {
        this.getProductCollectionList();
    },
    methods: {
        // 处理排序变化
        onChangeSort(res) {
            let val = this.sort.find(
                (item) => item.sort_type === res.sort_type,
            );
            this.sortid = val.id;
            this.getProductCollectionList();
        },
        // 获取选品库列表
        async getProductCollectionList() {
            const params = {
                sort_type: this.sortForm.sort_type,
                sort: this.sortForm.sort,
                page: this.paginationValue.page,
                pageSize: this.paginationValue.limit,
            };
            const res = await this.$get(
                '/application/productCollection/list',
                params,
            );
            if (res.code === 0) {
                this.tableDate = res.data.list;
                this.paginationValue.total = res.data.total;
            }
        },
        // 查看详情
        galleryDetail(item) {
            window.open(
                `/shareSelectGalleryDetail?app_id=${item.app_id}`,
                '_blank',
            );
        },
        // 分页
        pagination(res) {
            this.paginationValue.page = res.page; // 修改页数
            this.getProductCollectionList(); // 从新调调用商品数据
        },
    },
};
</script>

<style lang="scss" scoped>
.bgw {
    border-radius: 5px;
    width: 1200px;
    margin: 0 auto;
}
.select-gallery {
    margin: 0 auto;
    background-color: #f7f7f7;
    .gallery-box {
        width: 385px;
        background-color: #ffffff;
        border-radius: 5px;
        padding: 15px 10px;
        box-sizing: border-box;
        .gallery-title {
            font-size: 18px;
            color: #101010;
            font-weight: 600;
        }
        .gallery-time {
            font-size: 14px;
            color: #101010;
        }
        .gallery-money {
            font-size: 18px;
            color: #101010;
            font-weight: 600;
            .fs14 {
                font-size: 14px;
            }
        }
    }
    .fjsa {
        justify-content: space-around;
    }
}
.red {
    color: #f42121;
}
.gray {
    color: #363738;
    font-weight: 600;
}
.fs12 {
    font-size: 14px;
}
.check-all {
    width: 100%;
    margin: 0 auto;
    height: 65px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-left: 22px;

    .check-all-left {
        display: flex;

        div {
            margin-right: 30px;
            cursor: pointer;
        }
    }
}
.paging {
    width: 1200px;
    margin: 0 auto 20px;
    display: flex;
    justify-content: end;
}
</style>
