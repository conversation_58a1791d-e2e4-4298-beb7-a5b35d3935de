<!--
 * @Author: jwt <EMAIL>
 * @Date: 2022-06-28 14:06:16
 * @LastEditors: jwt <EMAIL>
 * @LastEditTime: 2022-06-28 14:17:55
 * @FilePath: \element-ui-demo\src\components\SearchButton.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="container">
    <el-button type="text" :class="[textClass]" @click="onClickUp"><slot></slot></el-button>
  </div>
</template>

<script>
import Emitter from 'element-ui/src/mixins/emitter'
export default {
  name: 'SearchButton',
  mixins: [Emitter],
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  computed: {
    textClass () {
      return this.$parent.value === this.value ? 'red' : 'grey'
    }
  },
  methods: {
    onClickUp () {
      this.dispatch('SearchButtonGroup', 'change', this.value)
    },
    onClickDown () {
      this.dispatch('SearchButtonGroup', 'change', this.value)
    }
  }
}
</script>

<style lang="scss" scoped>
.container{
  .red {
    color: red;
  }
  .grey {
    color: grey;
  }
}
</style>
