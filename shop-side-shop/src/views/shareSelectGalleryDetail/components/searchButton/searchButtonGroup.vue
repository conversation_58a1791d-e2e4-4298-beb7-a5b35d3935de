<!--
 * @Author: jwt <EMAIL>
 * @Date: 2022-06-28 14:06:02
 * @LastEditors: jwt <EMAIL>
 * @LastEditTime: 2022-06-28 14:09:18
 * @FilePath: \element-ui-demo\src\components\SearchButtonGroup.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="container">
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'SearchButtonGroup',
  componentName: 'SearchButtonGroup',
  model: {
    event: 'change'
  },
  props: {
    value: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.container{
  display: flex;
  justify-content: center;
}
</style>
