import SortButton from '@/components/sortButton/sortButton.vue'
import SortButtonGroup from '@/components/sortButton/sortButtonGroup.vue'
import albumDialog from "../components/albumDialog.vue";
import {mapGetters} from "vuex";
export default {
    name: "SearchList",
    components: { 
        SortButton,
        SortButtonGroup,
        albumDialog,
    },
    data() {
        return {
            loading: false,
            formData:{
                category_1_id: null,
                category_2_id: null,
                category_3_id: null,
                source: null, // 来源
                grossProfitRate: null, // 毛利率
                profitRate: null, // 利润率
                agreementPrice: null, // 协议价
                guidePrice: null, // 指导价
                marketingPrice: null,// 营销价
                discount: null, // 折扣
                self_is_import: null, // 是否已导入
                marketing: null, // 营销属性
                d1: null,
                d2: null,
                a1: null,
                a2: null,
                g1: null,
                g2: null,
                p1: null,
                p2: null,
            },

            sortForm:{
                value:'default', //按"默认"排序
                sort:'up', // 升序
            },    
            
            category1: [], // 一级类目
            category2: [], // 二级类目
            category3: [], // 三级类目
            sourceList: [], // 来源
            grossProfitRate: [ // 毛利率
                { label: '0-35%', value: 1 },
                { label: '35%-50%', value: 2 },
                { label: '50%-75%', value: 3 },
                { label: '75%及以上', value: 4 },
                { label: '自定义区间', value: 5 },
            ],
            profitRateOptios: [ // 利润率
                { label: '0-50%', value: 1 },
                { label: '50%-150%', value: 2 },
                { label: '150%-300%', value: 3 },
                { label: '300%及以上', value: 4 },
                { label: '自定义区间', value: 5 },
            ],
            agreementPriceOptions: [ // 协议价
                { label: '0-200', value: 1 },
                { label: '200-500', value: 2 },
                { label: '500-1000', value: 3 },
                { label: '1000元以上', value: 4 },
                { label: '自定义区间', value: 5 },
            ],
            // 指导价/营销价optios
            guide_marketing_PriceOptions: [
                { label: '0-200', value: 1 },
                { label: '200-500', value: 2 },
                { label: '500-1000', value: 3 },
                { label: '1000元以上', value: 4 },
            ],
            // 折扣
            discountList: [
                { label: '0-3折', value: 1 },
                { label: '3-5折', value: 2 },
                { label: '5-8折', value: 3 },
                { label: '8折以上', value: 4 },
                { label: '自定义区间', value: 5 },
            ],
            my_center_total: null, // 选品库数量
            checked: false, // 全选商品的按钮
            sort: [
                // 排序数组
                { id: 1, name: '最新上架', value: 'created_at' },
                { id: 2, name: '供货价', value: 'agreement_price' },
                { id: 3, name: '指导价', value: 'guide_price' },
                { id: 4, name: '营销价', value: 'activity_price' },
                { id: 5, name: '利润率', value: 'market_rate' },
                { id: 6, name: '毛利率', value: 'gross_profit_rate' },
                { id: 7, name: '折扣', value: 'discount' },
            ],
            sortid: 0, // 排序id
            sortForm: {
                // 最新上架:'created_at', 供货价:agreement_price,
                // 指导价:guide_price, 营销价:activity_price,
                // 利润律:market_rate, 毛利率:gross_profit_rate
                // 排序:discount
                value: '',
                sort: '1', // 1为升序，2为降序
            },
            centerDialogVisible: false, // 弹幕开关
            centerDialogVisibleID: 0, // 判断打开弹窗渲染的数据
            selection_list:[],
            keyword: '',
            goodsList: [],
            page: 1,
            pageSize: 20,
            total: 0,

            dialog: {
                visible: false,
                propRow: {},
            },
        }
    },
    watch: {
        $route(value) {
            if (value.path === "/searchList") {
                this.page = 1
                this.search()
            }
        }
    },
    computed: {
        ...mapGetters("home", ["getFramework"])
    },
    mounted() {
        this.search();
        this.categoryTree(); // 获取分类
        this.getMyGatherSupplyAndSource() // 获取来源
    },
    methods: {
        // 获取搜索数据
        getParams() {
            let params = {
                title: this.$route.query.keyword ? this.$route.query.keyword : "",
            };
            // 一级类目
            if (this.formData.category_1_id) {
                params.category_1_id = this.formData.category_1_id
            }
            // 二级类目
            if (this.formData.category_2_id) {
                params.category_2_id = this.formData.category_2_id
            }
            // 三级类目
            if (this.formData.category_3_id) {
                params.category_3_id = this.formData.category_3_id
            }
            // 来源
            if (this.formData.source) {
                let sourceObj =  this.formData.source.split('-')
                // console.log(str1);
                if (sourceObj.length === 3) {
                    let str = this.sourceList.find(
                        (item) => item.source_name == sourceObj[2]
                    );
                    if (str.type) {
                        params.gather_supply_id = str.gather_supply_id;
                    } else {
                        params.source = str.source_id;
                    }                    
                }
            }
            // 毛利率 gross_profit_rate
            if (this.formData.grossProfitRate) {
                if (this.formData.grossProfitRate == 5) {
                    params.gross_profit_rate = {
                        from: Number(this.formData.g1),
                        to: Number(this.formData.g2),
                    };
                } else {
                    const rate = [
                        { from: 0, to: 35 },
                        { from: 35, to: 50 },
                        { from: 50, to: 75 },
                        { from: 75, to: 9999 },
                    ];
                    params.gross_profit_rate = rate[this.formData.grossProfitRate - 1];
                }
            }

            // 利润率 profit_rate
            if (this.formData.profitRate) {
                if (this.formData.profitRate == 5) {
                    params.profit_rate = {
                        from: Number(this.formData.p1),
                        to: Number(this.formData.p2),
                    };
                } else {
                    const rate = [
                        { from: 0, to: 50 },
                        { from: 50, to: 150 },
                        { from: 150, to: 300 },
                        { from: 300, to: 9999 },
                    ];
                    params.profit_rate = rate[this.formData.profitRate - 1];
                }
            }

            // 协议价 agreement_price
            if (this.formData.agreementPrice) {
                if (this.formData.agreementPrice == 5) {
                    params.agreement_price = {
                        from: Number(this.formData.a1) ,
                        to: Number(this.formData.a2),
                    };
                } else {
                    const section = [
                        { from: 0, to: 200 },
                        { from: 200, to: 500 },
                        { from: 500, to: 1000 },
                        { from: 1000, to: 99999 },
                    ];
                    params.agreement_price =
                        section[this.formData.agreementPrice - 1];
                }
            }

            // 指导价 guide_price
            if (this.formData.guidePrice) {
                const rate = [
                    { from: 0, to: 200 },
                    { from: 200, to: 500 },
                    { from: 500, to: 1000 },
                    { from: 1000, to: 99999 },
                ];
                params.guide_price = rate[this.formData.guidePrice - 1];
            }

            // 折扣 discount
            if (this.formData.discount) {
                if (this.formData.discount == 5) {
                    params.discount = {
                        from: Number(this.formData.d1),
                        to: Number(this.formData.d2)
                    }
                } else {
                    const rate = [
                        {from: 0, to: 3},
                        {from: 3, to: 5},
                        {from: 5, to: 8},
                        {from: 8, to: 10},
                    ]
                    params.discount = rate[this.formData.discount - 1]
                }
            }

            // 营销价 activity_price
            if (this.formData.marketingPrice) {
                const rate = [
                    { from: 0, to: 200 },
                    { from: 200, to: 500 },
                    { from: 500, to: 1000 },
                    { from: 1000, to: 99999 },
                ];
                params.activity_price = rate[this.formData.marketingPrice - 1];
            }

            // 是否已导入 is_import
            if (this.formData.self_is_import) {
                params.is_import = parseInt(this.formData.self_is_import);
            }

            // 营销属性
            if (this.formData.marketing) {
                params[this.formData.marketing] = 1;
            }
            return params
        },
        // 加入商品专辑
        addAlbum() {
            this.dialog.visible = true;
            this.dialog.propRow = this.getParams()
        },
        // 关闭导入对话框
        nolead() {
            this.centerDialogVisible = false;
        },
        // 开启导入对话框
        tolead(res) {
            this.centerDialogVisibleID = res;
            this.centerDialogVisible = true;
        },
        acktrue() {
            let params = {}
            if (this.centerDialogVisibleID) {
                params = this.getParams()
                // 排序
                params.type = this.sortForm.value;
                switch (this.sortForm.sort) {
                    case '1':
                        params.sort = true;
                        break;
                    case '2':
                        params.sort = false;
                        break;
                }

                this.$post('/product/importGoods', params)
                    .then((res) => {
                        if (res.code == 0) {
                            this.$message.success(res.msg);
                            this.resetfun(); // 重置数据
                            // 重置待移除选品库的数据
                            this.selection_list = [];
                            this.centerDialogVisible = false; // 关闭弹窗
                        } else {
                            this.$message.error(res.msg);
                        }
                    })
                    .catch((res) => {
                        console.log(res);
                    });
    
            } else {
                if (this.selection_list.length == 0) {
                    this.$message('至少选中一个商品');
                    return;
                }
                params = {
                    ids: this.selection_list,
                };
                this.$post('/product/addStorageCenter', params)
                    .then((res) => {
                        if (res.code == 0) {
                            this.$message.success(res.msg);
                            this.resetfun(); // 重置数据
                            this.selection_list = []; // 清空导入商品的数组
                            this.centerDialogVisible = false; // 关闭弹窗
                        } else {
                            this.$message.error(res.msg);
                        }
                    })
                    .catch((res) => {
                        console.log(res);
                    });
            }
        },
        // 获取分类
        categoryTree() {
            this.$get('/category/tree').then(res => {
                if (res.code === 0) {
                    this.category1 = res.data.categories
                }
            })
        },
        // 来源
        getMyGatherSupplyAndSource() {
            let sourceList = [
                {
                    gather_supply_id: -1,
                    source_id: -1,
                    source_name: '全部',
                    type: 1,
                },
                {
                    gather_supply_id: 0,
                    source_id: -1,
                    source_name: '平台自营',
                    type: 1,
                },
            ];
            this.$get('/application/getMyGatherSupplyAndSource')
                .then((res) => {
                    this.sourceList = sourceList.concat(res.data);
                })
                .catch((res) => {
                    console.log(res);
                });
        },
        // 搜索
        goSearch() {
            this.page = 1;
            this.total = 0;
            this.search()
        },
        // 获取商品
        async search() {
            this.checked = false; // 改变全选状态
            this.loading = true;
            let params = this.getParams()
            params.page = this.page;
            params.pageSize = this.pageSize;
            // 排序
            params.type = this.sortForm.value;
            switch (this.sortForm.sort) {
                case '1':
                    params.sort = true;
                    break;
                case '2':
                    params.sort = false;
                    break;
            }

            let res = {}
            if(this.$fn.isLogin()){
                params.is_display = 1
                res = await this.$post("product/listLoginNew", params)
            }else{
                res = await this.$post("product/listNew", params)
            }
            this.loading = false;
            if (res.code === 0) {
                this.goodsList = res.data.list?res.data.list:[];
                this.total = res.data.total;
                this.my_center_total = res.data.my_center_total;
                this.page = res.data.page

                // 全选是否开启
                if (this.selection_list.length > 0) {
                    let k = false
                    this.goodsList.forEach(element => {
                        let index = this.selection_list.find(item => item === element.id)
                        if (index === undefined) {
                            k = true;
                        }
                    });
                    if (!k) {
                        this.checked = true
                    }
                }

            }
            // profit_rate 利润率
            // gross_profit_rate 毛利率
            // agreement_price 协议价
            // guide_price 指导价
            // discount 折扣
            // activity_price 营销价 
            // is_display 是否已导入
        },
        // 重置搜索
        resetfun() {
            this.page = 1;
            this.pageSize = 20;
            this.total = 0;
            // 重置排序
            this.sortForm = {
                value: '',
                sort: '1',
            };
            this.sortid = 0;

            // 重置筛选条件
            this.formData = {
                category_1_id: null,
                category_2_id: null,
                category_3_id: null,
                source: null, // 来源
                grossProfitRate: null, // 毛利率
                profitRate: null, // 利润率
                agreementPrice: null, // 协议价
                guidePrice: null, // 指导价
                marketingPrice: null,// 营销价
                discount: null, // 折扣
                self_is_import: null, // 是否已导入
                marketing: null, // 营销属性
                d1: null,
                d2: null,
                a1: null,
                a2: null,
                g1: null,
                g2: null,
                p1: null,
                p2: null,
            }

            // 重置类目表
            this.category2 = [];
            this.category3 = [];

            this.search()
        },
        // 我的选品库
        toMySel() {
            this.$router.push('/personalCenter/mySelectionList');
        },
        // 跳转商品详情
        goodsDetail(res) {
            this.$_blank(`/goodsDetail?goods_id=${res}`);
        },
        // 类目
        getCategory(level, cid) {
            let list = this.category1.find(item => item.id === this.formData.category_1_id);
            // 二级分类
            if (level === 2) {
                this.category2 = [];
                this.category3 = [];
                this.formData.category_2_id = null
                this.formData.category_3_id = null
                this.category2 = list.children
            }
            if (level === 3) {
                this.category3 = [];
                this.formData.category_3_id = null
                let list2 = this.category2.find(item => item.id === cid)
                this.category3 = list2.children
            }
        },
        // 全选
        checkedfun() {
            let goodsList = this.goodsList.filter((item) => item.is_import != 1);
            let val;
            if (this.checked) {
                for (let index = 0; index < goodsList.length; index++) {
                    val = this.selection_list.find(
                        (item) => item == goodsList[index].id,
                    );
                    if (!val) {
                        this.selection_list.push(goodsList[index].id);
                    }
                }
            } else {
                for (let index = 0; index < goodsList.length; index++) {
                    val = this.selection_list.find(
                        (item) => item == goodsList[index].id,
                    );
                    if (val) {
                        this.selection_list = this.selection_list.filter(
                            (item) => item != goodsList[index].id,
                        );
                    }
                }
            }
        },
        // 加入选品
        addOption(res) {
            let a = false;
            let val = this.selection_list.find((item) => item == res);
            if (!val) {
                this.selection_list.push(res);
            }

            let goods = this.goodsList.filter((item) => item.is_import != 1);
            // 判断 是否 是全选
            for (let index = 0; index < goods.length; index++) {
                if (
                    undefined ==
                    this.selection_list.find((item) => item == goods[index].id)
                ) {
                    a = true;
                    break;
                }
            }
            if (!a) {
                this.checked = true;
            }
        },
        // 移除选品
        delOption(res) {
            let val = this.selection_list.find((item) => item == res);
            this.checked = false;
            if (val) {
                this.selection_list = this.selection_list.filter(
                    (item) => item != res,
                );
            }
        },
        // 最新上架
        newest() {
            this.page = 1;
            this.pageSize = 20;
            this.total = 0;
            (this.sortForm = {
                value: 'created_at',
                sort: '2',
            }),
            (this.sortid = 1);
            this.search()
        },
        // 切换排序
        onChangeSort(res) {
            this.page = 1;
            this.pageSize = 20;
            this.total = 0;
            let val = this.sort.find((item) => item.value == res.value);
            this.sortid = val.id;
            
            this.search();
        },
        pagination(val) {
            this.page = val.page;
            this.search()
        },
    }
};