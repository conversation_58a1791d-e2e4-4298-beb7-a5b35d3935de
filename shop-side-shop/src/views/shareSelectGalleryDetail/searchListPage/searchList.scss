/*商品列表部分*/
// .special-offer-box {
//     display: grid;
//     grid-template-columns: repeat(5, 230px);
//     grid-row-gap: 13px;
//     grid-column-gap: 13px;

//     .special-offer-item {
//         height: 338px;
//         background-color: white;
//         transition: all 0.3s ease;

//         &:hover {
//             box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
//             opacity: 0.8;

//             .special-offer-product-img-box {
//                 .el-image {
//                     width: 160px !important;
//                     height: 160px !important;
//                 }
//             }
//         }
//         &:nth-child(5) {
//             margin-right: 0;
//         }

//         .special-offer-product-img-box {
//             height: 230px;
//             display: flex;
//             justify-content: center;
//             align-items: center;

//             .el-image {
//                 width: 150px !important;
//                 height: 150px !important;
//                 transition: all 0.3s ease;
//             }
//         }

//         .special-offer-b-box {
//             padding: 0 10px;
//             font-size: 14px;

//             p {
//                 height: 38px;
//             }

//             span.special-offer-title {
//                 color: #a5a5a5;
//                 margin-right: 10px;
//             }

//             span.special-offer-number {
//                 color: $theme-color;
//             }
//         }
//     }
// }
/*以下：排序与筛选*/
// .con-sort {
// padding: 0 30px 0 26px;
// width: 334px;
// }
// .con-checkbox {
// margin-left: 190px;
// .el-checkbox {
//     margin-right: 30px;
// }
// }
// ::v-deep.el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner {
// background-color: $theme-color;
// border-color: $theme-color;
// }
// ::v-deep.el-checkbox__input.is-checked+.el-checkbox__label {
// color: $theme-color;
// }
// ::v-deep.el-form-item {
// width: 45%;
// .el-input {
//     margin-right: 7px;
// }
// }


.search-box {
    padding: 16px;
    border-radius: 12px;

    .merchandise-screen-select {
        width: 258px;
        height: 40px;
        border-radius: 8px;
        margin-right: 15px;
    }

    .merchandise-screen-select-title {
        width: 258px;
        height: 40px;
        border-radius: 8px;
        margin-right: 14px;
        border: 1px solid #dee0e5;
        margin-bottom: 16px;

        .selecl-sty {
            width: 192px;
            border: none;
        }

        ::v-deep .el-select .el-input .el-input__inner {
          height: 15px;
        }

        .select-name {
            padding-left: 16px;
            font-size: 14px;
            height: 40px;
            width: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #606266;
        }
        
        .selectc {
            width: 70px;
        }
        
        .selectcs {
            width: 172px;
        }

        ::v-deep .el-input__inner {
            border: 0;
            border-radius: 8px;
        }
    }

    .merchandise-screen-select-eles {
        width: 400px;
        height: 40px;
        margin-right: 15px;
        margin-bottom: 16px;

        .select-name {
            padding: 0 16px;
            font-size: 14px;
            height: 40px;
            width: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #606266;
        }
        .input-sty {
            width: 140px;
            height: 40px;
        }

        .input-bin {
            width: 140px;
            height: 40px;
            display: flex;
            border: 1px solid #dee0e5;
            border-radius: 8px;

            .input {
                width: 100px;
                height: 40px;
            }

            ::v-deep .el-input__inner {
                border: 0;
                border-radius: 8px;
            }

            .bin {
                width: 40px;
                height: 40px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 0 8px 8px 0;
                background-color: #f5f7fa;
            }
        }

        .line {
            font-size: 24px;
            padding: 0 5px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .but-true {
        background-color: #f42121;
        color: #fff;
    }

    button {
        width: 100px;
        height: 40px;
        border-radius: 8px;
    }

    .selection-num {
        color: #f42121;
        font-size: 14px;
        margin-left: 12px;
        cursor: pointer;
    }

}

.check-all {
    height: 65px;
    font-size: 14px;

    .f {
        display: flex;
        div {
            margin-right: 30px;
            cursor: pointer;
        }
    }
    .check-all-right {
        display: flex;

        div {
            margin-left: 30px;
            font-weight: 500;
            cursor: pointer;
        }
    }
}

.commodity {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(1, 1fr);
    gap: 10px;
    .commodity-card {
        width: 225px;
        height: 338px;
        background-color: #fff;
        border-radius: 12px;
        margin-bottom: 15px;
        overflow: hidden;
        cursor: pointer;

        .commodity-card-true {
            display: block;

            .commodityimg {
                width: 225px;
                height: 225px;
            }

            .commodity-name {
                height: 38px;
                margin: 0 8px;
                font-size: 14px;
                margin-top: 12px;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 2;
            }

            .price {
                display: flex;
                margin-top: 15px;

                .price-box {
                    width: 112px;
                    height: 26px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-direction: column;

                    .price-name {
                        font-size: 12px;
                        color: #a4a4a4;
                    }

                    .price-num {
                        font-size: 14px;
                        color: #f42121;
                    }
                }

                .fen {
                    border-right: 1px #dee0e5 solid;
                }
            }
        }

        .commodity-card-hover {
            display: none;

            .commodityimg-hover {
                width: 225px;
                height: 189px;
                box-sizing: border-box;
                overflow: hidden;
                position: relative;

                img {
                    width: 225px;
                    height: auto;
                    position: absolute;
                    bottom: 0;
                    object-fit: cover;
                }
            }

            .commodityimg-hover-button {
                width: 225px;
                height: 32px;
                background-color: #f42121;
                opacity: 0.6;
                display: flex;
                align-items: center;
                color: #fff;
                position: absolute;
                bottom: 0px;

                div {
                    width: 112px;
                    height: 23px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
            }

            .commodity-name {
                height: 38px;
                margin: 0 8px;
                font-size: 14px;
                margin-top: 12px;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 2;
            }

            .price {
                display: flex;
                margin-top: 15px;

                .price-box {
                    width: 114px;
                    height: 26px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-direction: column;

                    .price-name {
                        font-size: 12px;
                        color: #a4a4a4;
                    }

                    .price-num {
                        font-size: 14px;
                        color: #f42121;
                    }
                }

                .fen {
                    border-right: 1px #dee0e5 solid;
                }
            }

            .det-price {
                width: 100%;
                height: 46px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                background-color: #f7f7f7;
                margin-top: 8px;
                border-radius: 8px;

                .det-price-box {
                    width: 25%;
                    height: 46px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-direction: column;

                    .det-price-name {
                        font-size: 11px;
                        color: #a4a4a4;
                    }

                    .det-price-num {
                        font-size: 11px;
                        color: #333333;
                    }
                }
            }
        }
    }

    .commodity-card:hover {
        border: 1px solid #f42121;
        box-sizing: border-box;
    }

    .commodity-card:hover .commodity-card-true {
        display: none;
    }

    .commodity-card:hover .commodity-card-hover {
        display: block;
    }
}

// 单选框样式
::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #f42121;
    border-color: #f42121;
}

::v-deep .el-checkbox__inner:hover {
    border-color: #f42121;
}

::v-deep .el-checkbox__inner:focus {
    border-color: #f42121;
}

::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #f42121;
}

::v-deep .el-form-item {
    margin-bottom: 16px;
}


.title-con {
    width: 100%;
    display: flex;
    justify-content: center;
    font-size: 20px;
    font-weight: bold;
}

.title-text {
    width: 100%;
    display: flex;
    justify-content: center;
    font-size: 12px;
    margin-top: 12px;
}

.title-button {
    display: flex;
    width: 276px;
    margin: 0 auto;
    justify-content: space-between;
    margin-top: 30px;

    button {
        width: 130px;
        height: 40px;
        border-radius: 8px;
    }

    .ackbutton {
        background-color: #f42121;
        color: #ffffff;
    }
}