import Breadcrumb from "@/views/goodsList/components/breadcrumb";
import {mapGetters} from "vuex"
import ProtocolDialog from "../components/protocolDialog"

export default {
    name: "supplierRegister",
    components: {Breadcrumb, ProtocolDialog},
    data() {
        return {
            freeze: null,
            path: this.$path,
            // 1=供应商申请  2=审核中 3=未通过  4=通过
            step: 1,
            // 分类option
            typeOption: [],
            formData: {
                // uid: this.$ls.getUserId(), // 会员id
                name: "", // 供应商名称
                username: "", // 账号
                password: "", // 密码
                avatar: "", // 头像
                license_link: "", // 营业执照
                id_card_link: "", // 身份证正面
                id_card_back_link: "", // 身份证背面
                realname: "", // 姓名
                mobile: "", // 手机号
                category_id: null, // 分类id
                status: 2, // 审核状态
            },
            // 表单校验
            rules: {
                name: {
                    required: true,
                    message: "请输入供应商名称",
                    trigger: "blur"
                },
                avatar: {
                    required: true,
                    message: "请上传头像",
                    trigger: "blur"
                },
                license_link: {
                    required: true,
                    message: "请上传营业执照",
                    trigger: "blur"
                },
                id_card_link: {
                    required: true,
                    message: "请上传身份证正面",
                    trigger: "blur"
                },
                id_card_back_link: {
                    required: true,
                    message: "请上传身份证反面",
                    trigger: "blur"
                },
                realname: {
                    required: true,
                    message: "请输入姓名",
                    trigger: "blur"
                },
                category_id: {
                    required: true,
                    message: "请选择分类",
                    trigger: "change"
                },
                username: {
                    required: true,
                    message: "请输入账号",
                    trigger: "blur"
                },
                password: {
                    required: true,
                    message: "请输入密码",
                    trigger: "blur"
                },
                mobile: {
                    required: true,
                    validator: (rule, value, callback) => {
                        if (this.$verify.checkPhone(value)) {
                            callback()
                        } else {
                            return callback(new Error('手机号格式不正确'))
                        }
                    },
                    trigger: "blur"
                }
            }
        }
    },
    mounted() {
        this.getApplyStatus();
    },
    computed: {
        ...mapGetters("home", ["getFramework"]),
        token() {
            return this.$ls.getToken()
        }
    },
    methods: {
        jumpAdmin() {
            let url = ""
            if (this.getFramework.footer.address_admin) {
                url = this.getFramework.footer.address_admin
            } else {
                url = window.location.hostname === "localhost" ? "http://localhost:8080/login" : window.location.origin + "/super"
            }
            window.open(url, "_blank");
        },
        // 重新申请
        resetApply() {
            try {
                this.$refs.form.resetFields()
            } catch {
            } finally {
                this.getSupplierTypeList()
                this.step = 1
            }
        },
        // 查询供应商申请状态
        getApplyStatus() {
            this.$get("/supplier/getSupplierApplyStatus").then(res => {
                if (res.code === 0) {
                    let isApply = res.data.isApply
                    if (isApply === 1) { // 已申请
                        // 冻结
                        this.freeze = res.data.freeze ? res.data.freeze : null
                        let status = res.data.restatus
                        switch (status) {
                            case 0:
                                // 待审核~审核中
                                this.step = 2
                                break;
                            case 1:
                                // 审核通过
                                this.step = 4
                                break;
                            case -1:
                                // 审核驳回
                                this.step = 3
                                break;
                        }
                    } else if (isApply === 0) { // 未申请
                        this.$refs.protocolDialog.isShow = true
                        this.$refs.protocolDialog.getProtocol()
                        this.step = 1
                        this.getSupplierTypeList()
                    }
                } else { // 报错
                    // this.$message.error(res.msg)
                    this.$refs.protocolDialog.isShow = true
                    this.$refs.protocolDialog.getProtocol()
                }
            })
        },
        // 获取供应商分类
        getSupplierTypeList() {
            this.$get("/supplier/getSupplierCategoryList").then(res => {
                if (res.code === 0) {
                    this.typeOption = res.data.list
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        handleAvatarSuccess(res) {
            this.formData.avatar = res.data.file.url;
        },
        // 营业执照
        handleLicenseSuccess(res){
            this.formData.license_link = res.data.file.url;
        },
        // 身份证正面
        handleIdCardSuccess(res){
            this.formData.id_card_link = res.data.file.url;
        },
        // 身份证背面
        handleIdCardBackSuccess(res) {
            this.formData.id_card_back_link = res.data.file.url;
        },
        beforeAvatarUpload(file) {
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                this.$message.error('上传头像图片大小不能超过 10MB!');
            }
            return isLt10M;
        },
        // 提交表单
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.$post("/supplier/createSupplierApply", this.formData).then(res => {
                        if (res.code === 0) {
                            this.step = 2
                        } else {
                            this.$message.error(res.msg)
                        }
                    })
                } else {
                    return false;
                }
            });
        }
    }
}