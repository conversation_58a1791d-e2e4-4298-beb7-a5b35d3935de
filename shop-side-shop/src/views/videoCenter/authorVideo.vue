<template>
    <div class="card">
        <div class="author f fac">
            <m-image :src="user.avatar" class="user-avatar"></m-image>
            <div style="margin-left: 12px;">
                <div class="authorName">{{ user.nickname ? user.nickname : '用户' + user.username.slice(-4) }}</div>
                <div class="authorNum">{{ total }}作品</div>
            </div>
        </div>
        <div class="f fac video-box">
            <template v-for="item in list">
                <div class="video-card" @click="details(item.id)">
                    <div class="img-box">
                        <m-image :src="item.cover_url" class="video-img"></m-image>
                        <div class="f fac fjc imgbut">
                            <i class="iconfont icon-you icon-but"></i>
                        </div>
                    </div>
                    <div class="title">{{ item.title }}</div>
                    <div class="user-card f fac">
                        <m-image :src="item.user.avatar" class="img-user-avatar"></m-image>
                        <div style="margin-left: 6px;">
                            <div class="f fac fjsb" style="width: 173px;margin-bottom: 6px;">
                                <div class="user-name">{{ item.user.nickname ? item.user.nickname : '用户' + item.user.username.slice(-4) }}</div>
                                <div class="f fac user-time">
                                    <i class="iconfont icon-a-liulan1 icon-yan"></i>
                                    <span>{{ item.visit_number }}</span>
                                </div>
                            </div>
                            <div class="user-time">{{ item.created_at | formatDate }}</div>
                        </div>
                    </div>
                </div>
            </template>
        </div>
        <Pagination :total="total" @pagination="pagination" :page="page" :limit="pageSize" layout="total, prev, pager, next, jumper">
        </Pagination>
        <videoDialog ref="videoDialog" @close="close"></videoDialog>
    </div>
</template>

<script>
import videoDialog from './components/videoDialog.vue';

export default {
    name: 'authorVideo',
    components: {videoDialog},
    data() {
        return {
            list: [],
            page: 1,
            pageSize: 20,
            total: 0,
            user: {}
        }
    },
    mounted() {
        this.getVideoList()
    },
    methods: {
        // 关闭刷新页面
        close() {
            this.getVideoList()
        },
        pagination(p) {
            this.page = p.page
            this.getVideoList()
        },
        // 获取视频列表
        async getVideoList() {
            let params = {
                page: this.page,
                pageSize: this.pageSize,
                user_id: parseInt(this.$route.query.id)
            }
            let res = await this.$post('video/center/list', params)
            if (res.code === 0) {
                this.list = res.data.list;
                this.user = res.data.list[0].user
                this.total = res.data.total;
            }
        },
        async details(id) {
            let params = {
                id: parseInt(id),
            }
            let res = await this.$post('video/center/detail', params)
            if (res.code === 0) {
                this.$refs.videoDialog.info(res.data)
            } else {
                this.$message.error(res.msg)
            }
        },
    }
}
</script>
<style lang="scss" scoped>

@font-face {
  font-family: "iconfont"; /* Project id 432132 */
  src: url('//at.alicdn.com/t/c/font_432132_0f8wvdl2t93m.woff2?t=1733812878421') format('woff2'),
       url('//at.alicdn.com/t/c/font_432132_0f8wvdl2t93m.woff?t=1733812878421') format('woff'),
       url('//at.alicdn.com/t/c/font_432132_0f8wvdl2t93m.ttf?t=1733812878421') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-you:before {
  content: "\ebe7";
}

.icon-a-liulan1:before {
  content: "\ec31";
}

.icon-but {
    font-size: 30px;
    color: #FFFFFF;
}

.icon-yan {
    font-size: 10px;
    color: #808080;
    margin-right: 3px;
}
.card {
    width: 1200px;
    margin: 15px auto;
}

.author {
    width: 100%;
    height: 96px;
    background: #FFFFFF;
    border-radius: 8px;
    padding-left: 16px;
    box-sizing: border-box;

    .user-avatar {
        min-width: 64px !important; 
        min-height: 64px !important;
        max-width: 64px !important;
        max-height: 64px !important; 
        border-radius: 50% !important;
    }
    .authorName {
        font-weight: bold;
        font-size: 16px;
        color: #232426;
    }

    .authorNum {
        margin-top: 8px;
        color: #808080;
        font-size: 16px;
        font-weight: 400;
    }
}

.video-box {
    width: 100%;
    margin-top: 15px;
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(1, 1fr);
    gap: 10px;

    .video-card {
        width: 227px;
        height: 271px;
        border-radius: 8px;
        background: #FFF;
        cursor: pointer;

        .img-box {
            position: relative;
            
            .video-img {
                border-radius: 8px 8px 0 0 !important;
                min-width: 227px !important;
                min-height: 170px !important;
                max-width: 227px !important;
                max-height: 170px !important;
            }

            .imgbut {
                width: 42px;
                height: 42px;
                position: absolute;
                background: rgba($color: #000000, $alpha: 0.3);
                border-radius: 50%;
                top: 64px;
                left: 92px;
                color: #AAAAB3;
                // font-size: 42px;
            }
        }

        .title {
            width: 211px;
            height: 40px;
            margin: 10px auto 0;
            font-weight: 500;
            font-size: 14px;
            color: #232426;
            line-height: 20px;
            word-break: break-all;
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .user-card {
            width: 211px;
            margin: 5px auto;

            .img-user-avatar {
                min-width: 32px !important;
                min-height: 32px !important;
                max-width: 32px !important;
                max-height: 32px !important;
                border-radius: 50% !important;
            }

            .user-name {
                width: 106px;
                height: 14px;
                font-weight: 400;
                font-size: 12px;
                color: #232426;
                word-break: break-all;
                text-overflow: ellipsis;
                overflow: hidden;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
            }

            .user-time {
                height: 14px;
                font-weight: 400;
                font-size: 12px;
                color: #808080;
            }
        }
    }
}
</style>