<template>
    <div class="card">
        <div class="f fac">
            <div class="f fac pl_26 top-video">
                <i class="el-icon-search" style="color: #AAAAB3;"></i>
                <el-input
                    class="input-sty"
                    v-model="value"
                    clearable
                    placeholder="请输入"
                ></el-input>
                <el-button class="red" style="width: 56px;" @click="search"
                    >查询</el-button
                >
            </div>
            <el-button
                class="red"
                style="margin-left: 10px;"
                @click="goAddVideo"
                >+ 发布视频</el-button
            >
        </div>
        <div class="f fac tabs-box">
            <template v-for="item in tabsList">
                <div
                    class="f fac fjsb tabs-true"
                    @click="tabsfun(item.id)"
                    v-if="item.id == tabsId"
                >
                    {{ item.title }}
                </div>
                <div
                    class="f fac fjsb tabs-false"
                    @click="tabsfun(item.id)"
                    v-else
                >
                    {{ item.title }}
                </div>
            </template>
        </div>
        <div class="f fac video-box">
            <template v-for="item in list">
                <div class="video-card">
                    <div class="img-box" @click="details(item.id)">
                        <m-image
                            :src="item.cover_url"
                            class="video-img"
                        ></m-image>
                        <div class="f fac fjc imgbut">
                            <i class="iconfont icon-you icon-but"></i>
                        </div>
                    </div>
                    <div class="title" @click="details(item.id)">
                        {{ item.title }}
                    </div>
                    <div class="user-card f fac">
                        <div @click="authorVideo(item.user.id)">
                            <m-image
                                :src="item.user.avatar"
                                class="img-user-avatar"
                            ></m-image>
                        </div>
                        <div style="margin-left: 6px;">
                            <div
                                class="f fac fjsb"
                                style="width: 173px;margin-bottom: 6px;"
                            >
                                <div
                                    class="user-name"
                                    @click="authorVideo(item.user.id)"
                                >
                                    {{
                                        item.user.nickname
                                            ? item.user.nickname
                                            : '用户' +
                                              item.user.username.slice(-4)
                                    }}
                                </div>
                                <div
                                    class="f fac user-time"
                                    @click="details(item.id)"
                                >
                                    <i
                                        class="iconfont icon-a-liulan1 icon-yan"
                                    ></i>
                                    <span>{{ item.visit_number }}</span>
                                </div>
                            </div>
                            <div
                                class="user-time"
                                @click="authorVideo(item.user.id)"
                            >
                                {{ item.created_at | formatDate }}
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>
        <Pagination
            :total="total"
            @pagination="pagination"
            :page="page"
            :limit="pageSize"
            layout="total, prev, pager, next, jumper"
        >
        </Pagination>
        <videoDialog ref="videoDialog" @close="close"></videoDialog>
    </div>
</template>

<script>
import videoDialog from './components/videoDialog.vue';
export default {
    name: 'videoCenterList',
    components: { videoDialog },
    data() {
        return {
            value: '',
            list: [],
            tabsList: [],
            tabsId: 0,
            page: 1,
            pageSize: 20,
            total: 0,
        };
    },
    mounted() {
        this.getVideoList();
        this.getCenterGroup();
    },
    methods: {
        // 获取分组
        async getCenterGroup() {
            let params = {
                title: '',
            };
            let res = await this.$post('video/center/group', params);
            if (res.code === 0) {
                this.tabsList = [{ id: 0, title: '全部' }, ...res.data];
            }
        },
        search() {
            this.page = 1;
            this.getVideoList();
        },
        // 关闭刷新页面
        close() {
            this.getVideoList();
        },
        // 获取视频列表
        async getVideoList() {
            let params = {
                page: this.page,
                pageSize: this.pageSize,
            };
            if (this.tabsId && this.tabsId !== 0) {
                params.group_id = this.tabsId;
            }
            if (this.value && this.value !== '') {
                params.title = this.value;
            }
            let res = await this.$post('video/center/list', params);
            if (res.code === 0) {
                this.list = res.data.list;
                this.total = res.data.total;
            }
        },
        // 打开商品详情
        async details(id) {
            let params = {
                id: parseInt(id),
            };
            let res = await this.$post('video/center/detail', params);
            if (res.code === 0) {
                this.$refs.videoDialog.info(res.data);
            } else {
                this.$message.error(res.msg);
            }
        },
        // 前往作者视频
        authorVideo(id) {
            this.$router.push({
                name: 'authorVideo',
                query: { id },
            });
        },
        // 发布视频
        goAddVideo() {
            this.$router.push({ name: 'addVideo' });
        },
        // 选择
        tabsfun(id) {
            this.tabsId = id;
            this.getVideoList();
        },
        // 分页
        pagination(p) {
            this.page = p.page;
            this.getVideoList();
        },
    },
};
</script>

<style lang="scss" scoped>
/* 定义滚动条整体部分 */
::-webkit-scrollbar {
    height: 6px; /* 滚动条高度 */
    background-color: #f5f5f5;
}

/* 定义滚动条轨道 */
::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3); /* 内阴影 */
    border-radius: 10px; /* 圆角 */
}

/* 定义滚动条滑块 */
::-webkit-scrollbar-thumb {
    border-radius: 10px; /* 圆角 */
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3); /* 内阴影 */
    cursor: pointer;
}
@font-face {
    font-family: 'iconfont';
    /* Project id 432132 */
    src: url('//at.alicdn.com/t/c/font_432132_0f8wvdl2t93m.woff2?t=1733812878421')
            format('woff2'),
        url('//at.alicdn.com/t/c/font_432132_0f8wvdl2t93m.woff?t=1733812878421')
            format('woff'),
        url('//at.alicdn.com/t/c/font_432132_0f8wvdl2t93m.ttf?t=1733812878421')
            format('truetype');
}

.iconfont {
    font-family: 'iconfont' !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-you:before {
    content: '\ebe7';
}

.icon-a-liulan1:before {
    content: '\ec31';
}

.icon-but {
    font-size: 30px;
    color: #ffffff;
}

.icon-yan {
    font-size: 10px;
    color: #808080;
    margin-right: 3px;
}

.el-button.red {
    background: #f42121;
    border: 1px solid #f42121;
    color: white;
    width: 96px;
    height: 32px;
    font-size: 12px;
    border-radius: 3px;
    padding: 0;

    &:hover {
        background: #f42121;
        border: 1px solid #f42121;
        color: white;
        width: 96px;
        height: 32px;
        font-size: 12px;
        border-radius: 3px;
        padding: 0;
    }
}

.el-button.red.is-disabled {
    color: #c0c4cc;
    cursor: not-allowed;
    background-image: none;
    background-color: #fff;
    border-color: #ebeef5;
}

.card {
    width: 1200px;
    margin: 15px auto;
}

.top-video {
    background: #ffffff;
    border-radius: 8px;
    width: 1090px;
    height: 48px;
    padding-right: 8px;
    box-sizing: border-box;
}

.input-sty {
    margin-right: 20px;
}

.input-sty ::v-deep .el-input__inner {
    border: none;
}

.tabs-box {
    width: 100%;
    margin-top: 15px;
    border-radius: 8px;
    background: #ffffff;
    padding-bottom: 14px;
    padding-right: 14px;
    box-sizing: border-box;
    overflow-x: scroll;
    white-space: nowrap;

    .tabs-true {
        height: 28px;
        margin-top: 14px;
        padding: 0 12px;
        border-radius: 17px;
        background-color: #f42121;
        color: #ffffff;
        margin-left: 25px;
        cursor: pointer;
        flex-shrink: 0;
    }

    .tabs-false {
        height: 28px;
        margin-top: 14px;
        padding: 0 12px;
        margin-left: 25px;
        cursor: pointer;
        flex-shrink: 0;
    }
}

.video-box {
    width: 100%;
    margin-top: 15px;
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(1, 1fr);
    gap: 12px;

    .video-card {
        width: 227px;
        height: 271px;
        border-radius: 8px;
        background: #fff;
        cursor: pointer;

        .img-box {
            position: relative;

            .video-img {
                border-radius: 8px 8px 0 0 !important;
                min-width: 227px !important;
                min-height: 170px !important;
                max-width: 227px !important;
                max-height: 170px !important;
            }

            .imgbut {
                width: 42px;
                height: 42px;
                position: absolute;
                background: rgba($color: #000000, $alpha: 0.3);
                border-radius: 50%;
                top: 64px;
                left: 92px;
                color: #aaaab3;
                // font-size: 42px;
            }
        }

        .title {
            width: 211px;
            height: 40px;
            margin: 10px auto 0;
            font-weight: 500;
            font-size: 14px;
            color: #232426;
            line-height: 20px;
            word-break: break-all;
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .user-card {
            width: 211px;
            margin: 5px auto;

            .img-user-avatar {
                min-width: 32px !important;
                min-height: 32px !important;
                max-width: 32px !important;
                max-height: 32px !important;
                border-radius: 50% !important;
            }

            .user-name {
                width: 106px;
                height: 14px;
                font-weight: 400;
                font-size: 12px;
                color: #232426;
                word-break: break-all;
                text-overflow: ellipsis;
                overflow: hidden;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
            }

            .user-time {
                height: 14px;
                font-weight: 400;
                font-size: 12px;
                color: #808080;
            }
        }
    }
}
</style>
