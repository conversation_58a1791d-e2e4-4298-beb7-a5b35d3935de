
###
POST {{api}}/api/shoppingcart/add
Content-Type: application/json
x-token: {{f-token}}
x-user-id: 1

{
  "shopping_carts": [
    {
      "sku_id": 33408,
      "qty": 11
    }
  ]
}
###
POST https://supply.hhwl886.com/api/shoppingcart/export
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiODE2MzY2MjItODkwYi00YzA1LTk2ZGYtMTk0YzU0NzVhMmU0IiwiVXNlcm5hbWUiOiIiLCJJRCI6NzUsIkFwcElEIjowLCJCdWZmZXJUaW1lIjo4NjQwMCwiZXhwIjoxNjc3MzA4NDEzLCJpc3MiOiJxbVBsdXMiLCJuYmYiOjE2NzY3MDI2MTN9.N0KuhgK0Ea0TB2OiuJF6UWyxti-vvrgfhIN5NqlPzG0
x-user-id: 1

{
  
}

###
POST {{api}}/api/shoppingcart/update
Content-Type: application/json
x-token: {{f-token}}
x-user-id: 1

{
  "id": 7650,
  "qty": 2
}

###
POST {{api}}/api/shoppingcart/update/batch
Content-Type: application/json
x-token: {{f-token}}
x-user-id: 1

{
  "checked": 1,
  "address_id": 2,
  "shipping_method_id": 1
}

###
POST {{api}}/api/shoppingcart/list
Content-Type: application/json
x-token: {{f-token}}
x-user-id: 1

{
}

###
DELETE {{api}}/api/shoppingcart/deleteBatchOrderById
Content-Type: application/json
x-token: {{f-token}}
x-user-id: 1

{
  "id": 34
}

###
DELETE {{api}}/api/shoppingcart/deleteBatchOrderByError
Content-Type: application/json
x-token: {{f-token}}
x-user-id: 1

{

}


###
POST {{api}}/api/shoppingcart/listByReorder
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiODAwMjU1YTgtNzRkMC00YTliLThiMWYtMzIzMDJkOTI5MzViIiwiVXNlcm5hbWUiOiIiLCJJRCI6NTcsIkFwcElEIjowLCJCdWZmZXJUaW1lIjo4NjQwMCwiZXhwIjoxNzQ0NzY3MzI0LCJpc3MiOiJxbVBsdXMiLCJuYmYiOjE3NDQxNjE1MjR9._mY8dEXrCkKf75AAc8nmoPGO715p4DrphHcST_RryCg
x-user-id: 1

{

}