package v1

import (
	"github.com/gin-gonic/gin"
	"small-shop-video/request"
	"small-shop-video/service"
	videoUtils "small-shop-video/utils"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

func GetCategoriesByCondition(c *gin.Context) {
	var pageInfo request.CategorySearch
	err := c.ShouldBind<PERSON>uery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.<PERSON>rror(), c)
		return
	}
	if err := utils.GVerify(pageInfo, videoUtils.CategorySearchVerify); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, categories := service.GetCategoriesByCondition(pageInfo.FCatId, pageInfo.Level); err != nil {
		yzResponse.FailWithMessage(err.<PERSON><PERSON><PERSON>(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{"categories": categories}, "获取成功", c)
	}
}
