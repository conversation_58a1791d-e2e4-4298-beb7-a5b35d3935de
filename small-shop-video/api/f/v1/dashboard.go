package v1

import (
	"github.com/gin-gonic/gin"
	"small-shop-video/service"
	v1 "user/api/f/v1"
	yzResponse "yz-go/response"
)

func Dashboard(c *gin.Context) {
	uid := v1.GetUserID(c)
	var err error
	var info map[string]interface{}
	info = make(map[string]interface{})
	err, info = service.Dashboard(uid)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(gin.H{"info": info}, c)
}
