package v1

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"small-shop-video/model"
	"small-shop-video/service"
	videoUtils "small-shop-video/utils"
	wechat "small-shop-video/weixin"
	"strconv"
	v1 "user/api/f/v1"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

func BindVideoShop(c *gin.Context) {
	var request model.VideoShopByCreate
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	uid := v1.GetUserID(c)
	if request.SmallShopID == 0 {
		err, smallShop := model.FindSmallShopByUid(uid)
		if err != nil {
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		request.SmallShopID = smallShop.ID
	}
	request.Uid = uid
	if request.ID == 0 {
		var vs model.VideoShopByCreate
		err, vs = service.FindVideoShopByUid(uid)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		if vs.ID != 0 {
			request.ID = vs.ID
		}
	}

	if err = utils.GVerify(request, videoUtils.CreateShopVerify); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, videoShop := service.FindVideoShopByUid(uid)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if videoShop.ID != 0 {
		str := service.GetReplaceString()
		if request.Secret == str {
			request.Secret = videoShop.Secret
		}
		if request.Token == str {
			request.Token = videoShop.Token
		}
		if request.MsgSecret == str {
			request.MsgSecret = videoShop.MsgSecret
		}
		request.Status = videoShop.Status
		request.Title = videoShop.Title
		request.Logo = videoShop.Logo
	}
	if videoShop.Appid != request.Appid || videoShop.Secret != request.Secret {
		// 请求接口
		wechatApi := wechat.Wechat{}
		err = wechatApi.GetAccessToken(request.Appid, request.Secret)
		if err != nil {
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		err, request.Title, request.Logo = wechatApi.GetChannelInfo()
		if err != nil {
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
	}

	if request.ID == 0 {
		// 创建
		if err = service.CreateVideoShop(request); err != nil {
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
	} else {
		// 修改
		if err = service.VideoShopBind(request); err != nil {
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
	}
	yzResponse.OkWithMessage("绑定成功", c)
}

func FindInfo(c *gin.Context) {
	uid := v1.GetUserID(c)
	err, videoShop := service.FindVideoShopByUid(uid)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	str := service.GetReplaceString()
	if videoShop.Secret != "" {
		videoShop.Secret = str
	}
	if videoShop.Token != "" {
		videoShop.Token = str
	}
	if videoShop.MsgSecret != "" {
		videoShop.MsgSecret = str
	}
	yzResponse.OkWithData(gin.H{"info": videoShop}, c)
}

func FindCallUrl(c *gin.Context) {
	var smallShop model.SmallShop
	var err error
	if err, smallShop = model.FindSmallShopByUid(v1.GetUserID(c)); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var domain string
	if c.Request.Header.Get("X-Forwarded-Proto") != "" {
		domain = c.Request.Header.Get("X-Forwarded-Proto") + "://" + c.Request.Host
	} else {
		domain = c.Request.Host
	}

	domain += "/supplyapi/api/smallShopVideo/callback?small_shop_id=" + strconv.Itoa(int(smallShop.ID))
	yzResponse.OkWithData(gin.H{"callback_url": domain}, c)
}
