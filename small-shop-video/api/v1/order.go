package v1

import (
	"github.com/gin-gonic/gin"
	"small-shop-video/confirm"
	"small-shop-video/request"
	"small-shop-video/service"
	yzResponse "yz-go/response"
)

func ConfirmDetail(c *gin.Context) {
	var page request.WechatOrder
	err := c.ShouldBindQuery(&page)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, detail := confirm.ConfirmDetail(page.OrderID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(detail, "获取成功", c)
		return
	}
}

func Confirm(c *gin.Context) {
	var params confirm.AdminOrderParams
	err := c.ShouldBind<PERSON>(&params)
	if err != nil {
		yzResponse.FailWithMessage(err.<PERSON><PERSON><PERSON>(), c)
		return
	}
	err = confirm.OfflineConfirm(params)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("成功", c)
}

func UpdateOrderIsOffline(c *gin.Context) {
	var page request.WechatOrder
	err := c.ShouldBindQuery(&page)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = service.UpdateOrderIsOffline(page.OrderID)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("成功", c)
}

func GetOrderList(c *gin.Context) {
	var search request.AdminOrderSearch
	err := c.ShouldBindQuery(&search)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetOrderInfoListAdmin(search); err != nil {
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     search.Page,
			PageSize: search.PageSize,
		}, "获取成功", c)
	}
}

func GetOrderDetail(c *gin.Context) {
	var page request.WechatOrder
	err := c.ShouldBindQuery(&page)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, order := service.GetOrderDetailAdmin(page.OrderID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(order, "获取成功", c)
		return
	}
}
