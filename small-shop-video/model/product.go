package model

import (
	"database/sql/driver"
	"encoding/json"
	"gorm.io/gorm"
	"strconv"
	"yz-go/source"
)

type VideoShopProduct struct {
	source.Model
	SmallShopID     uint   `json:"small_shop_id" form:"small_shop_id" gorm:"column:small_shop_id;comment:小商店id;index;"`
	SmallShopUID    uint   `json:"small_shop_uid" form:"small_shop_uid" gorm:"column:small_shop_uid;comment:小商店店主会员id;index;"`
	ProductID       uint   `json:"product_id" form:"product_id" gorm:"column:product_id;comment:中台商品id;index;"`
	WechatProductID string `json:"wechat_product_id" form:"wechat_product_id" gorm:"column:wechat_product_id;comment:线上商品id;index;"`
	ProductTitle    string `json:"product_title" form:"product_title" gorm:"column:product_title;comment:线上商品名称;"`
	ProductImage    string `json:"product_image" form:"product_image" gorm:"column:product_image;comment:线上商品图片;"`
	DraftData       string `json:"draft_data" form:"draft_data" gorm:"column:draft_data;comment:草稿数据;type:longtext;"`
	OnlineData      string `json:"online_data" form:"online_data" gorm:"column:online_data;comment:线上数据;type:longtext;"`
	MinPrice        uint   `json:"min_price" form:"min_price" gorm:"column:min_price;comment:最低价格;"`
	MaxPrice        uint   `json:"max_price" form:"max_price" gorm:"column:max_price;comment:最高价格;"`
	GuidePrice      uint   `json:"guide_price" form:"guide_price" gorm:"column:guide_price;comment:指导价(单位:分);"` //指导价
	Price           uint   `json:"price" form:"price" gorm:"column:price;comment:批发价(单位:分);"`                   // 批发价
	Cat1ID          string `json:"cat_1_id" form:"cat_1_id" gorm:"column:cat_1_id;comment:一级类目id;"`
	Cat2ID          string `json:"cat_2_id" form:"cat_2_id" gorm:"column:cat_2_id;comment:二级类目id;"`
	Cat3ID          string `json:"cat_3_id" form:"cat_3_id" gorm:"column:cat_3_id;comment:三级类目id;"`
	BrandID         string `json:"brand_id" form:"brand_id" gorm:"column:brand_id;comment:品牌id;"`
	OnlineStatus    int    `json:"online_status" form:"online_status" gorm:"column:online_status;comment:线上商品状态;type:smallint;size:1;index;"`
	DraftStatus     int    `json:"draft_status" form:"draft_status" gorm:"column:draft_status;comment:草稿商品状态;type:smallint;size:1;index;"`
	StatusName      string `json:"status_name" gorm:"-"` // 状态名
}

func (p *VideoShopProduct) AfterFind(tx *gorm.DB) (err error) {
	//1:编辑中, 2:审核中, 3:审核失败, 5:上架, 6:回收站, 11:自主下架, 13:违规下架/风控系统下架
	if p.OnlineStatus == 1 {
		p.StatusName = "编辑中"
	}
	if p.OnlineStatus == 2 {
		p.StatusName = "审核中"
	}
	if p.OnlineStatus == 3 {
		p.StatusName = "审核失败"
	}
	if p.OnlineStatus == 5 {
		p.StatusName = "上架"
	}
	return
}

// 中台商品 视频号草稿
type VideoShopProductDraft struct {
	source.Model
	ProductID     uint         `json:"product_id" form:"product_id" gorm:"column:product_id;comment:中台商品id;index;"`
	IsVideoShop   int          `json:"is_video_shop" form:"is_video_shop" gorm:"column:is_video_shop;default:0;type:smallint;size:1;index;"`
	WechatProduct ProductDraft `json:"wechat_product" form:"wechat_product" gorm:"column:wechat_product;comment:草稿数据;type:longtext;"`
}

type UpdateProductDraft struct {
	// 视频号商品id
	ProductID string `json:"product_id"`
	UpdateDraft
}

type UpdateDraft struct {
	// 视频号商品id
	// ProductID string `json:"product_id"`
	// 商家自定义商品ID
	OutProductID string `json:"out_product_id"`
	// 标题，最少3字符，最多60字符。文本内容规则请看注意事项
	Title string `json:"title"`
	// 副标题，最多18字符
	SubTitle string `json:"sub_title"`
	// 主图，多张，列表，最少1张，最多9张
	HeadImgs []string `json:"head_imgs"`
	// 发货方式，若为无需快递（仅对部分类目开放），则无需填写运费模版id。0:快递发货（默认），1:无需快递
	DeliverMethod int `json:"deliver_method"`
	// 商品详情信息
	DescInfo struct {
		// 商品详情图片（最多20张）
		Imgs []string `json:"imgs"`
		// 商品详情文本
		Desc string `json:"desc"`
	} `json:"desc_info"`
	// 商品类目，大小恒等于3（一二三级类目）
	Cats []struct {
		// 类目ID，需要先通过获取类目接口/category/availablesoncategories/get拿到可用的cat_id；这里的cat_id顺序与一，二，三级类目严格一致，即数组下标为0的是一级类目，数组下标为1的是二级类目，数组下标为2的是三级类目
		CatId string `json:"cat_id"`
	} `json:"cats"`
	// 商品参数，部分类目有必填的参数，具体参考文档获取类目信息中的字段attr.product_attr_list[].is_required
	Attrs []struct {
		// 属性键key（属性自定义用）
		AttrKey string `json:"attr_key"`
		Type    string `json:"type"`
		// 属性值value（属性自定义用）
		AttrValue string `json:"attr_value"`
	} `json:"attrs"`
	// 商品编码
	SpuCode string `json:"spu_code"`
	// 品牌id
	BrandId string `json:"brand_id"`
	// 商品资质图片(最多5张)
	Qualifications []string `json:"qualifications"`
	// 运费模板
	ExpressInfo struct {
		// 运费模板ID（先通过获取运费模板接口merchant/getfreighttemplatelist拿到），若deliver_method=1，则不用填写
		TemplateId string `json:"template_id"`
		// 商品重量，单位克，若当前运费模版计价方式为[按重量]，则必填
		Weight int `json:"weight"`
	} `json:"express_info"`
	// 规格 长度最少为1，最大为500
	Skus    []WUPSku `json:"skus"`
	Listing int      `json:"listing"`
}

type ProductDraft struct {
	// 视频号商品id
	// ProductID string `json:"product_id"`
	// 商家自定义商品ID
	OutProductID string `json:"out_product_id"`
	// 标题，最少3字符，最多60字符。文本内容规则请看注意事项
	Title string `json:"title"`
	// 副标题，最多18字符
	SubTitle string `json:"sub_title"`
	// 主图，多张，列表，最少1张，最多9张
	HeadImgs []string `json:"head_imgs"`
	// 发货方式，若为无需快递（仅对部分类目开放），则无需填写运费模版id。0:快递发货（默认），1:无需快递
	DeliverMethod int `json:"deliver_method"`
	// 商品详情信息
	DescInfo struct {
		// 商品详情图片（最多20张）
		Imgs []string `json:"imgs"`
		// 商品详情文本
		Desc string `json:"desc"`
	} `json:"desc_info"`
	// 商品类目，大小恒等于3（一二三级类目）
	Cats []struct {
		// 类目ID，需要先通过获取类目接口/category/availablesoncategories/get拿到可用的cat_id；这里的cat_id顺序与一，二，三级类目严格一致，即数组下标为0的是一级类目，数组下标为1的是二级类目，数组下标为2的是三级类目
		CatId string `json:"cat_id"`
	} `json:"cats"`
	// 商品参数，部分类目有必填的参数，具体参考文档获取类目信息中的字段attr.product_attr_list[].is_required
	Attrs []struct {
		// 属性键key（属性自定义用）
		AttrKey string `json:"attr_key"`
		Type    string `json:"type"`
		// 属性值value（属性自定义用）
		AttrValue string `json:"attr_value"`
	} `json:"attrs"`
	// 商品编码
	SpuCode string `json:"spu_code"`
	// 品牌id
	BrandId string `json:"brand_id,omitempty"`
	// 商品资质图片(最多5张)
	Qualifications []string `json:"qualifications"`
	// 运费模板
	ExpressInfo struct {
		// 运费模板ID（先通过获取运费模板接口merchant/getfreighttemplatelist拿到），若deliver_method=1，则不用填写
		TemplateId string `json:"template_id"`
		// 商品重量，单位克，若当前运费模版计价方式为[按重量]，则必填
		Weight int `json:"weight"`
	} `json:"express_info"`
	// 规格 长度最少为1，最大为500
	Skus    []WPSku `json:"skus"`
	Listing int     `json:"listing"`
}

type ProductDraftNotBrand struct {
	// 视频号商品id
	// ProductID string `json:"product_id"`
	// 商家自定义商品ID
	OutProductID string `json:"out_product_id"`
	// 标题，最少3字符，最多60字符。文本内容规则请看注意事项
	Title string `json:"title"`
	// 副标题，最多18字符
	SubTitle string `json:"sub_title"`
	// 主图，多张，列表，最少1张，最多9张
	HeadImgs []string `json:"head_imgs"`
	// 发货方式，若为无需快递（仅对部分类目开放），则无需填写运费模版id。0:快递发货（默认），1:无需快递
	DeliverMethod int `json:"deliver_method"`
	// 商品详情信息
	DescInfo struct {
		// 商品详情图片（最多20张）
		Imgs []string `json:"imgs"`
		// 商品详情文本
		Desc string `json:"desc"`
	} `json:"desc_info"`
	// 商品类目，大小恒等于3（一二三级类目）
	Cats []struct {
		// 类目ID，需要先通过获取类目接口/category/availablesoncategories/get拿到可用的cat_id；这里的cat_id顺序与一，二，三级类目严格一致，即数组下标为0的是一级类目，数组下标为1的是二级类目，数组下标为2的是三级类目
		CatId string `json:"cat_id"`
	} `json:"cats"`
	// 商品参数，部分类目有必填的参数，具体参考文档获取类目信息中的字段attr.product_attr_list[].is_required
	Attrs []struct {
		// 属性键key（属性自定义用）
		AttrKey string `json:"attr_key"`
		Type    string `json:"type"`
		// 属性值value（属性自定义用）
		AttrValue string `json:"attr_value"`
	} `json:"attrs"`
	// 商品编码
	SpuCode string `json:"spu_code"`
	// 商品资质图片(最多5张)
	Qualifications []string `json:"qualifications"`
	// 运费模板
	ExpressInfo struct {
		// 运费模板ID（先通过获取运费模板接口merchant/getfreighttemplatelist拿到），若deliver_method=1，则不用填写
		TemplateId string `json:"template_id"`
		// 商品重量，单位克，若当前运费模版计价方式为[按重量]，则必填
		Weight int `json:"weight"`
	} `json:"express_info"`
	// 规格 长度最少为1，最大为500
	Skus    []WPSku `json:"skus"`
	Listing int     `json:"listing"`
}

type WSkus []WSku

type WPSku struct {
	OutSkuID       string   `json:"out_sku_id" form:"out_sku_id" gorm:"column:out_sku_id;comment:中台规格id;"`
	ThumbImg       string   `json:"thumb_img" form:"thumb_img" gorm:"column:thumb_img;comment:规格图片;"`
	SalePrice      int      `json:"sale_price" form:"sale_price" gorm:"column:sale_price;comment:售价;"`
	StockNum       int      `json:"stock_num" form:"stock_num" gorm:"column:stock_num;comment:库存;"`
	SkuCode        string   `json:"sku_code" form:"sku_code" gorm:"column:sku_code;comment:编码;"`
	SkuAttrs       SkuAttrs `json:"sku_attrs" form:"sku_attrs" gorm:"column:sku_attrs;comment:销售属性;"`
	SkuDeliverInfo struct {
		StockType int `json:"stock_type"`
	} `json:"sku_deliver_info"`
}

type WUPSku struct {
	ProductID      string   `json:"product_id" form:"product_id" gorm:"column:product_id;comment:视频号商品id;"`
	SkuID          string   `json:"sku_id,omitempty" form:"sku_id" gorm:"column:sku_id;comment:视频号规格id;"`
	OutSkuID       string   `json:"out_sku_id" form:"out_sku_id" gorm:"column:out_sku_id;comment:中台规格id;"`
	ThumbImg       string   `json:"thumb_img" form:"thumb_img" gorm:"column:thumb_img;comment:规格图片;"`
	SalePrice      int      `json:"sale_price" form:"sale_price" gorm:"column:sale_price;comment:售价;"`
	StockNum       int      `json:"stock_num" form:"stock_num" gorm:"column:stock_num;comment:库存;"`
	SkuCode        string   `json:"sku_code" form:"sku_code" gorm:"column:sku_code;comment:编码;"`
	SkuAttrs       SkuAttrs `json:"sku_attrs" form:"sku_attrs" gorm:"column:sku_attrs;comment:销售属性;"`
	SkuDeliverInfo struct {
		StockType int `json:"stock_type"`
	} `json:"sku_deliver_info"`
}

type WSku struct {
	source.Model
	ProductID    string   `json:"product_id" form:"product_id" gorm:"column:product_id;comment:视频号商品id;"`
	SkuID        string   `json:"sku_id" form:"sku_id" gorm:"column:sku_id;comment:视频号规格id;"`
	OutSkuID     string   `json:"out_sku_id" form:"out_sku_id" gorm:"column:out_sku_id;comment:中台规格id;"`
	OutProductID string   `json:"out_product_id" form:"out_product_id" gorm:"column:out_product_id;comment:中台商品id;"`
	ThumbImg     string   `json:"thumb_img" form:"thumb_img" gorm:"column:thumb_img;comment:规格图片;"`
	SalePrice    int      `json:"sale_price" form:"sale_price" gorm:"column:sale_price;comment:售价;"`
	StockNum     int      `json:"stock_num" form:"stock_num" gorm:"column:stock_num;comment:库存;"`
	SkuCode      string   `json:"sku_code" form:"sku_code" gorm:"column:sku_code;comment:编码;"`
	SkuAttrs     SkuAttrs `json:"sku_attrs" form:"sku_attrs" gorm:"column:sku_attrs;comment:销售属性;"`
	Status       int      `json:"status" form:"status" gorm:"column:status;comment:sku状态;"`
	/*SkuDeliverInfo struct {
		StockType int `json:"stock_type"`
	} `json:"sku_deliver_info"`*/
}

func GetSkuTitleMap(ids []string) (err error, skuTitleMap map[string]string) {
	skuTitleMap = make(map[string]string)
	var skuIds []string
	var wskus []WSku
	err = source.DB().Model(&WSku{}).Where("sku_id in ?", ids).Find(&wskus).Error
	if err != nil {
		return err, skuTitleMap
	}
	wskuMap := make(map[string]string)
	for _, w := range wskus {
		wskuMap[w.OutSkuID] = w.SkuID
		skuIds = append(skuIds, w.OutSkuID)
	}
	// 视频号规格id  中台规格id
	var skus []Sku
	err = source.DB().Where("id in ?", skuIds).Find(&skus).Error
	if err != nil {
		return err, skuTitleMap
	}
	for _, s := range skus {
		if len(s.Options) > 0 {
			skuTitleMap[wskuMap[strconv.Itoa(int(s.ID))]] = s.Options[0].SpecName + ":" + s.Options[0].SpecItemName
		} else {
			skuTitleMap[wskuMap[strconv.Itoa(int(s.ID))]] = s.Title
		}
	}
	return err, skuTitleMap
}

func (WSku) TableName() string {
	return "video_shop_skus"
}

func (value SkuAttrs) Value() (driver.Value, error) {
	return json.Marshal(value)
}

func (value *SkuAttrs) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type SkuAttrs []SkuAttr

type SkuAttr struct {
	AttrKey   string `json:"attr_key"`
	AttrValue string `json:"attr_value"`
}

func (value ProductDraft) Value() (driver.Value, error) {
	return json.Marshal(value)
}

func (value *ProductDraft) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type Product struct {
	source.Model
	Title         string  `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(255);size:255;index;"` // 标题
	ImageUrl      string  `json:"image_url" gorm:"column:image_url;comment:图片url;"`                                    // 图片url
	IsVideoShop   int     `json:"is_video_shop" form:"is_video_shop" gorm:"column:is_video_shop;comment:视频号商品（1是0否）;default:0;type:smallint;size:1;index;"`
	OriginPrice   uint    `json:"origin_price" form:"origin_price" gorm:"column:origin_price;comment:市场价(单位:分);"`       // 市场价 市场价
	GuidePrice    uint    `json:"guide_price" form:"guide_price" gorm:"column:guide_price;comment:供货价(单位:分);"`          //指导价
	Price         uint    `json:"price" form:"price" gorm:"column:price;comment:供货价(单位:分);"`                            // 供货价、给采购端的协议价
	CostPrice     uint    `json:"cost_price" form:"cost_price" gorm:"column:cost_price;comment:成本价(单位:分);"`             // 成本价，上游给的协议价
	ActivityPrice uint    `json:"activity_price" form:"activity_price" gorm:"column:activity_price;comment:营销价(单位:分);"` // 营销价
	DetailImages  string  `json:"detail_images" form:"detail_images" gorm:"column:detail_images;comment:详情图json数组;type:text;"`
	Skus          []Sku   `json:"skus,omitempty" form:"skus"` // sku数组
	Gallery       Gallery `json:"gallery"`                    // 相册
}

// 相册
type Gallery []GalleryItem
type GalleryItem struct {
	Type int    `json:"type"` // 类型（1图片2视频）
	Src  string `json:"src"`  // 资源链接
}

func (value Gallery) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *Gallery) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type Sku struct {
	source.Model
	Title         string  `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(255);size:255;"`        // 标题
	Price         uint    `json:"price" form:"price" gorm:"column:price;comment:供货价(单位:分);"`                            // 供货价(单位:分)
	CostPrice     uint    `json:"cost_price" form:"cost_price" gorm:"column:cost_price;comment:成本价(单位:分);"`             // 成本价(单位:分)
	OriginPrice   uint    `json:"origin_price" form:"origin_price" gorm:"column:origin_price;comment:市场价(单位:分);"`       // 市场价/建议零售价(单位:分)
	GuidePrice    uint    `json:"guide_price" form:"guide_price" gorm:"column:guide_price;comment:指导价(单位:分);"`          // 指导价（分为单位）
	ActivityPrice uint    `json:"activity_price" form:"activity_price" gorm:"column:activity_price;comment:营销价(单位:分);"` // 营销价
	Stock         int     `json:"stock" form:"stock" gorm:"column:stock;comment:库存数量;"`                                 // 库存数量
	Weight        int     `json:"weight" form:"weight" gorm:"column:weight;comment:重量（g）;"`                             // 重量（g）
	ProductID     uint    `json:"product_id" form:"product_id" gorm:"index"`
	Code          string  `json:"code" form:"code" gorm:"column:code;comment:自定义编码;type:varchar(255);size:255;"`        // 自定义编码
	Sn            string  `json:"sn" form:"sn" gorm:"column:sn;comment:产品编号;type:text;"`                                // 编码
	Barcode       string  `json:"barcode" form:"barcode" gorm:"column:barcode;comment:条形码;type:varchar(255);size:255;"` // 条形码
	ImageUrl      string  `json:"image_url" gorm:"column:image_url;comment:图片url;"`
	Options       Options `json:"options" form:"options" gorm:"column:options;comment:规格json列表(冗余信息);type:text;"` // 规格冗余信息
}

// 规格
type Options []Option
type Option struct {
	SpecName     string `json:"spec_name"`      // 规格名
	SpecItemName string `json:"spec_item_name"` // 规格值
}

func (value *Options) Scan(data interface{}) error {
	//if strings.Contains(source.Strval(data), "+") {
	//	data = strings.Replace(source.Strval(data), "+", "&", -1)
	//}
	return json.Unmarshal([]byte(source.Strval(data)), &value)
}
func (value Options) Value() (driver.Value, error) {
	return json.Marshal(value)
}

type VideoShopImage struct {
	source.Model
	ImageUrl       string `json:"image_url" form:"image_url" gorm:"column:image_url;comment:中台图片url;"`
	WechatImageUrl string `json:"wechat_image_url" form:"wechat_image_url" gorm:"column:wechat_image_url;comment:线上图片url;"`
}
