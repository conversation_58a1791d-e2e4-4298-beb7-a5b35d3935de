package model

import "yz-go/source"

type Region struct {
	ID       int    `json:"id" gorm:"primarykey"`
	ParentID int    `json:"parent_id" gorm:"column:parent_id;comment:父id;index"`                          //父ID
	Name     string `json:"name" gorm:"column:name;comment:地区名;type:varchar(32);size:32;"`                //地名
	Level    int    `json:"level" gorm:"column:level;comment:级别(1省，2市，3区，4街);type:tinyint;size:1;index;"` //等级
}

func GetRegionId(name string) (err error, id int) {
	var address Region
	err = source.DB().Where("name like ?", "%"+name+"%").First(&address).Error
	if err != nil {
		return
	}
	return err, address.ID
}

func GetRegionIdAndName(str string) (id uint, name string) {
	var address Region
	source.DB().Where("name like ?", "%"+name+"%").First(&address)
	return uint(address.ID), address.Name
}
