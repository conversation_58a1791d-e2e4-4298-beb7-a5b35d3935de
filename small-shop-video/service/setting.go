package service

import (
	"small-shop-video/model"
	"yz-go/source"
)

func GetSetting() (err error, setting model.Setting) {
	err = source.DB().Where("`key` = ?", "small_shop_video_setting").First(&setting).Error
	return
}

func SaveSetting(setting model.Setting) (err error) {
	setting.Key = "small_shop_video_setting"
	if setting.ID != 0 {
		err = source.DB().Save(&setting).Error
	} else {
		err = source.DB().Create(&setting).Error
	}
	return err
}
