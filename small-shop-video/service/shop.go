package service

import (
	"errors"
	"gorm.io/gorm"
	"small-shop-video/model"
	"small-shop-video/request"
	"strings"
	"time"
	"yz-go/source"
)

func Repeat(sid, id uint) (err error, res bool) {
	res = true
	var shop model.VideoShopByCreate
	err, shop = FindVideoShopBySID(sid, id)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if shop.ID == 0 {
		res = false
	}
	return
}

func FindVideoShopBySID(sid, id uint) (err error, shop model.VideoShopByCreate) {
	db := source.DB().Where("small_shop_id = ?", sid)
	if id != 0 {
		db.Where("id != ?", id)
	}
	err = db.First(&shop).Error
	return
}

func FindVideoShop(id uint) (err error, shop model.VideoShopByCreate) {
	err = source.DB().Where("id = ?", id).First(&shop).Error
	return
}

func FindVideoShopByUid(uid uint) (err error, shop model.VideoShopByCreate) {
	err = source.DB().Where("uid = ?", uid).First(&shop).Error
	return
}

func FindDefaultVideoShop() (err error, shop model.VideoShopByCreate) {
	var setting model.Setting
	err, setting = GetSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if setting.Values.VID == 0 {
		err = errors.New("基础设置未设置默认视频号")
		return
	}
	err = source.DB().Where("id = ?", setting.Values.VID).First(&shop).Error
	return
}

func CreateVideoShop(shop model.VideoShopByCreate) (err error) {
	err = source.DB().Create(&shop).Error
	return
}

func VideoShopBind(shop model.VideoShopByCreate) (err error) {
	err = source.DB().Save(&shop).Error
	return
}

func UpdateVideoShop(shop model.VideoShopByCreate) (err error) {
	err = source.DB().Model(&model.VideoShopByCreate{}).Where("id = ?", shop.ID).Updates(map[string]interface{}{
		"appid":         shop.Appid,
		"secret":        shop.Secret,
		"small_shop_id": shop.SmallShopID,
		"title":         shop.Title,
		"logo":          shop.Logo,
	}).Error
	return
}

func GetReplaceString() (str string) {
	return strings.Repeat("*", 10)
}

func ChangeStatus(id uint) (err error) {
	var shop model.VideoShopByCreate
	err, shop = FindVideoShop(id)
	if err != nil {
		return
	}
	if shop.Status == 1 {
		shop.Status = 0
	} else {
		shop.Status = 1
	}
	err = source.DB().Model(&shop).Where("id = ?", id).Update("status", shop.Status).Error
	return
}

func GetVideoShopList(info request.VideoShopSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(&model.VideoShop{})
	var shops []model.VideoShop
	if info.UID != 0 {
		db.Where("`uid` = ?", info.UID)
	}
	if info.Member != "" {
		var userIDs []uint
		err = source.DB().Model(model.User{}).Where("username like ?", "%"+info.Member+"%").Or("mobile like ?", "%"+info.Member+"%").Pluck("id", &userIDs).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`uid` in ?", userIDs)
	}
	if info.SmallShopID != 0 {
		db.Where("`small_shop_id` = ?", info.SmallShopID)
	}
	if info.SmallShopTitle != "" {
		var sids []uint
		err = source.DB().Model(model.SmallShop{}).Where("title like ?", "%"+info.SmallShopTitle+"%").Pluck("id", &sids).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`small_shop_id` in ?", sids)
	}
	if info.UserLevelID != 0 {
		var userIDs []uint
		err = source.DB().Model(model.User{}).Where("level_id = ?", info.UserLevelID).Pluck("id", &userIDs).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`uid` in ?", userIDs)
	}
	if info.Title != "" {
		db.Where("`title` like ?", "%"+info.Title+"%")
	}
	err = db.Count(&total).Error
	err = db.Preload("SmallShopInfo").Preload("UserInfo").Preload("UserInfo.LevelInfo").Order("id desc").Limit(limit).Offset(offset).Find(&shops).Error
	return err, shops, total
}

//结构体基类
type Balance struct {
	PurchasingBalance uint `json:"purchasing_balance" form:"purchasing_balance" gorm:"column:purchasing_balance;comment:采购余额;type:bigint;size:100;"`
	SettlementBalance uint `json:"settlement_balance" form:"settlement_balance" gorm:"column:settlement_balance;comment:结算余额;type:bigint;size:100;"`
	Type              int  `json:"type" form:"type" gorm:"column:type;comment:余额类型(1汇聚2平台);type:smallint;size:1;index;"` //1汇聚 2 站内
	Uid               int  `json:"uid" form:"uid" gorm:"column:uid;comment:会员ID;type:bigint;size:100;index;"`
}

//站内账户余额
type AccountBalance struct {
	source.Model
	Balance
}

func Dashboard(uid uint) (err error, info map[string]interface{}) {
	var smallShop model.SmallShop
	err, smallShop = model.FindSmallShopByUid(uid)
	if err != nil {
		return
	}
	info = make(map[string]interface{})
	// 待处理: 待付款, 待发货, 售后
	var wait_pay, wait_send, after_sales int64
	err = source.DB().Model(&model.VideoShopOrder{}).Where("small_shop_id = ? and wechat_order_status = ?", smallShop.ID, model.WechatWaitPay).Count(&wait_pay).Error
	if err != nil {
		return
	}
	err = source.DB().Model(&model.VideoShopOrder{}).Where("small_shop_id = ? and wechat_order_status = ?", smallShop.ID, model.WechatWaitSend).Count(&wait_send).Error
	if err != nil {
		return
	}
	err = source.DB().Model(&model.VideoShopOrder{}).Where("small_shop_id = ? and after_sales_status != ?", smallShop.ID, 0).Count(&after_sales).Error
	if err != nil {
		return
	}
	waitHandle := make(map[string]interface{})
	waitHandle["wait_pay"] = wait_pay
	waitHandle["wait_send"] = wait_send
	waitHandle["after_sales"] = after_sales
	info["wait_handle"] = waitHandle
	// 数据概况: 累计订单, 累计订单金额, 账户余额
	var order_count, order_price_sum int64
	err = source.DB().Model(&model.VideoShopOrder{}).Where("small_shop_id = ?", smallShop.ID).Count(&order_count).Error
	if err != nil {
		return
	}
	err = source.DB().Model(&model.VideoShopOrder{}).Select("COALESCE(SUM(order_price), 0)").Where("small_shop_id = ?", smallShop.ID).First(&order_price_sum).Error
	if err != nil {
		return
	}
	var goinBalance AccountBalance
	err = source.DB().Where("`uid` = ?", uid).Where("`type` = ?", 2).Find(&goinBalance).Error
	if err != nil {
		return
	}

	statistics := make(map[string]interface{})
	statistics["order_count"] = order_count
	statistics["order_price_sum"] = order_price_sum
	statistics["account_balance"] = goinBalance.PurchasingBalance
	info["statistics"] = statistics
	// 今日数据: 支付金额, 订单数, 支付订单数
	t := time.Now()
	// 今日开始时间戳
	todayStartUnix := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
	var pay_sum, today_order_count, pay_order_count int64
	err = source.DB().Model(&model.VideoShopOrder{}).Select("COALESCE(SUM(order_price), 0)").Where("small_shop_id = ? and wechat_order_status > ? and created_at >= ?", smallShop.ID, model.WechatWaitPay, todayStartUnix).First(&pay_sum).Error
	if err != nil {
		return
	}
	err = source.DB().Model(&model.VideoShopOrder{}).Where("small_shop_id = ? and created_at >= ?", smallShop.ID, todayStartUnix).Count(&today_order_count).Error
	if err != nil {
		return
	}
	err = source.DB().Model(&model.VideoShopOrder{}).Where("small_shop_id = ? and wechat_order_status > ? and created_at >= ?", smallShop.ID, model.WechatWaitPay, todayStartUnix).Count(&pay_order_count).Error
	if err != nil {
		return
	}
	todayStatistics := make(map[string]interface{})
	todayStatistics["pay_sum"] = pay_sum
	todayStatistics["order_count"] = today_order_count
	todayStatistics["pay_order_count"] = pay_order_count
	info["today_statistics"] = todayStatistics
	// 昨日数据: 支付金额, 订单数, 支付订单数
	// yesterday_statistics[pay_sum,order_count,pay_order_count]
	ts := time.Now().AddDate(0, 0, -1)
	// 昨日开始时间戳
	yesterdayStartUnix := time.Date(ts.Year(), ts.Month(), ts.Day(), 0, 0, 0, 0, ts.Location())
	var y_pay_sum, y_order_count, y_pay_order_count int64
	err = source.DB().Model(&model.VideoShopOrder{}).Select("COALESCE(SUM(order_price), 0)").Where("small_shop_id = ? and wechat_order_status > ? and created_at >= ? and created_at <= ?", smallShop.ID, model.WechatWaitPay, yesterdayStartUnix, todayStartUnix).First(&y_pay_sum).Error
	if err != nil {
		return
	}
	err = source.DB().Model(&model.VideoShopOrder{}).Where("small_shop_id = ? and created_at >= ? and created_at <= ?", smallShop.ID, yesterdayStartUnix, todayStartUnix).Count(&y_order_count).Error
	if err != nil {
		return
	}
	err = source.DB().Model(&model.VideoShopOrder{}).Where("small_shop_id = ? and wechat_order_status > ? and created_at >= ? and created_at <= ?", smallShop.ID, model.WechatWaitPay, yesterdayStartUnix, todayStartUnix).Count(&y_pay_order_count).Error
	if err != nil {
		return
	}
	yesterdayStatistics := make(map[string]interface{})
	yesterdayStatistics["pay_sum"] = y_pay_sum
	yesterdayStatistics["order_count"] = y_order_count
	yesterdayStatistics["pay_order_count"] = y_pay_order_count
	info["yesterday_statistics"] = yesterdayStatistics
	// 商品管理: 销售中, 审核中
	var sale_count, audit_count int64
	err = source.DB().Model(&model.VideoShopProduct{}).Where("small_shop_id = ? and online_status = ?", smallShop.ID, 5).Count(&sale_count).Error
	if err != nil {
		return
	}
	err = source.DB().Model(&model.VideoShopProduct{}).Where("small_shop_id = ? and draft_status = ?", smallShop.ID, 2).Count(&audit_count).Error
	if err != nil {
		return
	}
	productAdmin := make(map[string]interface{})
	productAdmin["sale_count"] = sale_count
	productAdmin["audit_count"] = audit_count
	info["product_admin"] = productAdmin
	return err, info
}
