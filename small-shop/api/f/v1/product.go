package v1

import (
	"github.com/gin-gonic/gin"
	"small-shop/request"
	"small-shop/response"
	"small-shop/service"
	v1 "user/api/f/v1"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	"yz-go/source"
)

func GetProductList(c *gin.Context) {
	var pageInfo request.ProductStorageSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userId := v1.GetUserID(c)
	if err, list, total := service.GetProductList(pageInfo, userId); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func GetMyProductList(c *gin.Context) {
	var pageInfo request.ProductStorageSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userId := v1.GetUserID(c)
	pageInfo.IsChoose = 1
	if err, list, total := service.GetMyProductList(pageInfo, userId); err != nil {
		//if err, list, total := service.GetMyProductListTest(pageInfo, userId); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

type DeleteProductByProductIDRequest struct {
	ProductIDs []uint `json:"product_ids"`
}

func DeleteProduct(c *gin.Context) {
	var reqData DeleteProductByProductIDRequest
	err := c.ShouldBindJSON(&reqData)
	if err != nil {
		//log.log().Error("参数解析失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("参数解析失败", c)
		return
	}
	userId := v1.GetUserID(c)
	err, smallShop := service.FindSmallShop(userId)
	if err != nil {
		//log.log().Error("未绑定小商店", zap.Any("err", err))
		yzResponse.FailWithMessage("未绑定小商店", c)
		return
	}
	if err = service.DeleteProductSale(smallShop.ID, reqData.ProductIDs); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

func FindProduct(c *gin.Context) {
	var reqId yzRequest.GetById
	err := c.ShouldBindQuery(&reqId)
	if err != nil {
		//log.log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	userID := v1.GetUserID(c)
	var user service.User
	err = source.DB().First(&user, userID).Error
	if err != nil {
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	err, reProduct := service.GetProductSalesInfo(reqId.Id, user.LevelID)
	if err != nil {
		//log.log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	yzResponse.OkWithData(response.FindProduct{Product: reProduct, DesLevel: reProduct.DesLevel, ShopLevel: reProduct.ShopLevel, ExpressLevel: reProduct.ExpressLevel, Level: reProduct.Level}, c)
}

func ChangePrice(c *gin.Context) {
	var req request.ChangePriceReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		//log.log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	userId := v1.GetUserID(c)
	err, smallShop := service.FindSmallShop(userId)
	if err != nil {
		//log.log().Error("未绑定小商店", zap.Any("err", err))
		yzResponse.FailWithMessage("未绑定小商店", c)
		return
	}
	if err = service.ChangeProductPrice(req, smallShop.ID, smallShop.Uid); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("改价成功", c)
	}
}

func BatchChangePrice(c *gin.Context) {
	var req request.BatchChangePriceReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		//log.log().Error("参数解析失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("参数解析失败", c)
		return
	}
	if req.PriceProportion < 10000 {
		yzResponse.FailWithMessage("比例不能小于100%", c)
		return
	}
	userId := v1.GetUserID(c)
	err, smallShop := service.FindSmallShop(userId)
	if err != nil {
		//log.log().Error("未绑定小商店", zap.Any("err", err))
		yzResponse.FailWithMessage("未绑定小商店", c)
		return
	}
	if err = service.BatchChangePrice(req, smallShop.ID, smallShop.Uid); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("批量改价成功", c)
	}
}

func VerifyProductExist(c *gin.Context) {
	var req request.ProductID
	err := c.ShouldBindJSON(&req)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, exist := service.VerifyProductExist(req, v1.GetUserID(c)); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{
			"exist": exist,
		}, "获取成功", c)
	}
}
