package v1

import (
	"github.com/gin-gonic/gin"
	"github.com/gogf/gf/frame/g"
	"go.uber.org/zap"
	"small-shop/request"
	"small-shop/service"
	wechatModel "small-shop/wechat-model"
	"yz-go/component/log"
	yzResponse "yz-go/response"
)

func WechatLogin(c *gin.Context) {
	var wechatLogin request.WechatLogin
	err := c.ShouldBindQuery(&wechatLogin)
	if err != nil {
		log.Log().Error("参数获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if wechatLogin.Code == "" {
		if wechatLogin.Url == "" {
			log.Log().Error("参数错误!", zap.Any("err", err))
			yzResponse.FailWithMessage("请提交授权之后跳回页面链接", c)
			return
		}
		wErr, wechatSetting := wechatModel.Get()
		if wErr != nil || wechatSetting.AppId == "" || wechatSetting.AppSecret == "" || wechatSetting.Token == "" || wechatSetting.AesKey == "" || wechatSetting.IsOpen == 2 {
			yzResponse.OkWithData(g.Map{"code": 203, "msg": "用户没有配置微信公众号,或者已关闭"}, c)
			return
		}
		//授权回调链接
		var redirectUrl string
		err, redirectUrl = service.GetRedirect(wechatLogin.Url)
		if err != nil {
			log.Log().Error("获取授权链接失败!", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		yzResponse.OkWithData(g.Map{"code": 201, "redirect_url": redirectUrl}, c)
		return
	} else {
		uErr, userModel, wechatData := service.GetWxUser(wechatLogin.Code)
		if wechatData.IsRegister == 1 {
			yzResponse.OkWithData(g.Map{"code": 202, "msg": "需要注册", "data": wechatData}, c)
			return
		}
		if uErr != nil {
			log.Log().Error("获取微信用户信息失败!", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		if userModel.Status == 0 {
			yzResponse.OkWithDetailed(g.Map{"status": userModel.Status}, "等待管理员审核", c)
			return
		}
		TokenNext(c, *userModel)
	}
}

func GetJsConfig(c *gin.Context) {
	var wechatLogin request.WechatLogin
	err := c.ShouldBindQuery(&wechatLogin)
	if err != nil {
		log.Log().Error("参数获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, data := service.GetJsConfig(wechatLogin.Url)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(data, c)
	return
}

func IsOpenWechat(c *gin.Context) {
	err, data := wechatModel.Get()
	if err != nil {
		data.IsOpen = 2
	}
	if data.Status != 1 {
		data.IsOpen = 2
	}
	if data.IsOpen != 1 {
		data.IsOpen = 2
	}
	// 1开启 其他都是关闭
	yzResponse.OkWithData(gin.H{"is_open": data.IsOpen}, c)
	return
}
