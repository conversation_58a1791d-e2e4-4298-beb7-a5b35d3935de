package v1

import (
	"github.com/gin-gonic/gin"
	"small-shop/request"
	"small-shop/service"
	yzResponse "yz-go/response"
)

func PaymentRecord(c *gin.Context) {
	var requestData request.Refund
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var data interface{}
	if err, data = service.PaymentRecord(requestData); err != nil {
		//log.log().Error("查询支付单失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(data, c)
}
