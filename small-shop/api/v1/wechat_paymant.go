package v1

import (
	"github.com/gin-gonic/gin"
	"path"
	"small-shop/model"
	"small-shop/service"
	yzResponse "yz-go/response"
)

func FindWechatPaymentSetting(c *gin.Context) {
	err, setting := model.GetWechatPaymentSetting()
	if err != nil {
		yzResponse.FailWithMessage("获取微信支付设置失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"setting": setting}, c)
}

func UpdateWechatPaymentSetting(c *gin.Context) {
	var setting model.WechatPaymentSetting
	err := c.ShouldBindJSON(&setting)
	if err != nil {
		return
	}
	err = model.UpdateWechatPaymentSetting(setting)
	if err == nil {
		yzResponse.OkWithMessage("修改成功", c)
	} else {
		yzResponse.FailWithMessage("修改失败", c)
	}
}

func UploadWechatCertFile(c *gin.Context) {
	var _, header, err = c.Request.FormFile("file")
	if err != nil {
		yzResponse.FailWithMessage("接收文件失败", c)
		return
	}
	ext := path.Ext(header.Filename)

	if ext != ".pem" {
		yzResponse.FailWithMessage("上传微信证书文件格式不正确", c)
		return
	}
	_, url, err := service.WechatUploadFile(header, c)
	if err != nil {
		yzResponse.FailWithMessage("上传文件失败", c)
		return
	}
	//上传素材部分
	yzResponse.OkWithDetailed(url, "上传成功:", c)
}

func UpdateWechatPaymentMask(c *gin.Context) {
	var params service.UpdateMask
	if err := c.ShouldBindJSON(&params); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateWechatPaymentMask(params); err != nil {
		yzResponse.FailWithMessage("设置失败，"+err.Error(), c)
	}
	yzResponse.OkWithData("设置成功", c)
}
