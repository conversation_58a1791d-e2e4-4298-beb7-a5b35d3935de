package v1

import (
	"github.com/gin-gonic/gin"
	"order/request"
	service3 "order/service"
	"small-shop/service"
	small_shop_order "small-shop/small-shop-order"
	v1 "user/api/f/v1"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
)

type OrderInfo struct {
	small_shop_order.OrderInfo
	Operations []small_shop_order.Operation `json:"operations"`
	DetailUrl  string                       `json:"detail_url"`
}

func FindOrder(c *gin.Context) {
	var reqId yzRequest.GetById
	err := c.ShouldBindQuery(&reqId)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := v1.GetUserID(c)
	err, info := small_shop_order.GetOrder(userID, reqId.Id)
	if err != nil {
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	err, operations := info.UserGetOperationList()
	if err != nil {
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	orderInfo := Order{
		SmallShopOrder: info,
		Operations:     operations,
	}
	yzResponse.OkWithData(gin.H{"order": orderInfo}, c)
}

func FindOrderItem(c *gin.Context) {
	var reqId yzRequest.GetByOrderItemId
	err := c.ShouldBindQuery(&reqId)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, info := small_shop_order.GetOrderItem(reqId.OrderItemId)
	if err != nil {
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"order_item": info}, c)
}

type Order struct {
	small_shop_order.SmallShopOrder
	Operations []small_shop_order.Operation `json:"operations"`
}

func GetOrderList(c *gin.Context) {
	var pageInfo request.OrderSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	pageInfo.SmallShopUserID = v1.GetUserID(c)
	err, orderInfoList, total := small_shop_order.GetOrderListByUID(pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var list []OrderInfo
	for _, info := range orderInfoList {
		err, operationList := info.UserGetOperationList()
		if err != nil {
			return
		}
		err, detailUrl := info.GetDetailUrl()
		if err != nil {
			return
		}
		list = append(list, OrderInfo{info, operationList, detailUrl})
	}
	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
	return
}

func GetExpressInfo(c *gin.Context) {
	var reqId yzRequest.GetById
	_ = c.ShouldBindQuery(&reqId)
	if reqId.Id == 0 {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	err, supplyOrderID := service.GetSupplyOrderID(reqId.Id)
	if err != nil {
		yzResponse.FailWithMessage("查询中台订单失败", c)
		return
	}
	if err, reshipping := service3.GetExpressInfo(supplyOrderID); err != nil {
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"reshipping": reshipping}, c)
	}
}
