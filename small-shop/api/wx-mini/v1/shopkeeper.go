package v1

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"small-shop/model"
	"small-shop/request"
	"small-shop/service"
	v1 "user/api/f/v1"
	yzResponse "yz-go/response"
)

func GetInfo(c *gin.Context) {
	var req request.Shopkeeper
	err := c.ShouldBindQuery(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if req.SID == 0 {
		yzResponse.FailWithMessage("店主id为空", c)
		return
	}
	var smallShop model.SmallShop
	if err, smallShop = service.GetSmallShopPreloadByID(req.SID); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"title": smallShop.Title, "avatar": smallShop.UserInfo.Avatar}, c)
	}
}

func GetPayTypesByH5(c *gin.Context) {
	var err error
	var types []map[string]interface{}
	err, types = service.GetPayTypesByH5(1)
	if err != nil {
		yzResponse.FailWithMessage("获取支付方式失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"pay_types": types}, c)
}

func GetSetting(c *gin.Context) {
	err, setting := service.GetSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取设置失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"setting": setting}, c)
}
func FindProtocolSetting(c *gin.Context) {
	err, setting := service.GetProtocolSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取设置失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"setting": setting}, c)
}
func GetShopSetting(c *gin.Context) {
	var req request.Shopkeeper
	err := c.ShouldBindQuery(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if req.SID == 0 {
		yzResponse.FailWithMessage("店主id为空", c)
		return
	}
	var setting model.ShopSetting
	if err, setting = service.GetShopSetting(req.SID); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"setting": setting}, c)
	}
}

func IsShopkeeper(c *gin.Context) {
	var req request.Shopkeeper
	err := c.ShouldBindQuery(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if req.SID == 0 {
		yzResponse.FailWithMessage("店主id为空", c)
		return
	}
	var result bool
	err, result = service.IsShopkeeper(v1.GetUserID(c), req.SID)
	if err != nil {
		yzResponse.FailWithMessage("检验会员身份失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"is_shopkeeper": result}, c)
}
