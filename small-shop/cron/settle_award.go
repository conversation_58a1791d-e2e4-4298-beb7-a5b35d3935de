package cron

import (
	"errors"
	incomeModel "finance/model"
	incomeService "finance/service"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"small-shop/model"
	"small-shop/service"
	"time"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func PushSettleHandle() {
	task := cron.Task{
		Key:  "smallShopSettleAward",
		Name: "小商店店主收益结算",
		// 每半小时执行一次
		Spec: "0 */30 * * * *",
		Handle: func(task cron.Task) {
			Settle()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func Settle() {
	//log.Log().Info("小商店收益结算,开始")

	var err error
	var awards []model.SettleAward
	err = source.DB().Model(&model.SettleAward{}).Preload("SmallShopInfo").Where("UNIX_TIMESTAMP(`created_at`) + (IFNULL(`settle_days`, 0) * 86400) <= ?", time.Now().Unix()).Where("status = ? AND order_completed = ?", 0, 1).Find(&awards).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		//log.Log().Info("未找到待结算收益结算,返回")
		log.Log().Error(err.Error(), zap.Any("err", err))
		return
	}
	for _, award := range awards {
		if award.SmallShopInfo.ID == 0 {
			//log.Log().Info("小商店不存在,返回")
			return
		}
		// 结算
		err = service.SettleAward(award)
		if err != nil {
			//log.Log().Info("结算失败,返回")
			log.Log().Error(err.Error(), zap.Any("err", err))
			return
		}
		// 修改小商店统计
		err = service.UpdateSmallShopBySettle(award.Amount, award.SmallShopInfo)
		if err != nil {
			//log.Log().Info("修改店主统计失败,返回")
			log.Log().Error(err.Error(), zap.Any("err", err))
			return
		}
		// 增加收入
		var income incomeModel.UserIncomeDetails
		income.UserID = int(award.SUID)
		income.IncomeType = incomeModel.SmallShop
		income.Amount = award.Amount
		err = incomeService.IncreaseIncome(income)
		if err != nil {
			//log.Log().Info("增加收入失败,返回")
			log.Log().Error(err.Error(), zap.Any("err", err))
			return
		}
		//log.Log().Info("小商店收益结算,成功")
	}
}
