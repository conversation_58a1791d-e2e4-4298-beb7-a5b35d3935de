package model

import (
	"product/model"
	"yz-go/source"
)

type PriceType int

const (
	ORIGIN   PriceType = iota // 原价
	GUIDE                     // 指导价（建议销售价）
	PRICE                     // 协议价（售价）
	ACTIVITY                  // 营销价 (上游供应链活动价格)
)

type SmallShopProductSaleMigration struct {
	source.Model
	PriceProportion uint      `json:"price_proportion" form:"price_proportion" gorm:"column:price_proportion;default:0;comment:供货价(单位:分);"` // 供货价、给采购端的协议价
	PriceType       PriceType `json:"price_type" form:"price_type" gorm:"column:price_type;default:0;comment:价格比例(单位：万分之一);"`               // 单位：万分之一
	//如果有activity_price时，那么cost_price为activity_price，否则为price
	IsDisplay int  `json:"is_display" form:"is_display" gorm:"column:is_display;default:0;comment:上架（1是0否）;type:smallint;size:1;index;"` // 上架（1是0否）
	Sales     uint `json:"sales" form:"sales" gorm:"column:sales;default:0;comment:销量;"`                                                 // 销量
	Sort      int  `json:"sort" form:"sort" gorm:"column:sort;default:0;comment:商品排序;type:int;"`                                         //     商品排序（序号小的在前）

	SmallShopID    uint    `json:"small_shop_id" from:"small_shop_id" gorm:"column:small_shop_id;index"`
	ProductID      uint    `json:"product_id" from:"product_id" gorm:"column:product_id;default:0;index"`
	Price          uint    `json:"price" from:"price" gorm:"column:price;default:0;comment:小商店销售价;"`
	Profit         int     `json:"profit" from:"profit" gorm:"column:profit;default:0;comment:利润;"`                              //利润
	ProfitRate     float64 `json:"profit_rate" from:"profit_rate" gorm:"column:profit_rate;default:0;comment:利润率;"`              //利润率
	ProfitDiscount float64 `json:"profit_discount" from:"profit_discount" gorm:"column:profit_discount;default:0;comment:利润折扣;"` //利润折扣
	PluginID       int     `json:"plugin_id" gorm:"column:plugin_id;default:0;"`                                                 //商品表的插件id用于区别是不是课程商品 课程：18

}

func (SmallShopProductSaleMigration) TableName() string {
	return "small_shop_product_sales"
}

// SmallShopProductSale 销售信息
type SmallShopProductSale struct {
	source.Model
	PriceProportion uint      `json:"price_proportion" form:"price_proportion" gorm:"column:price_proportion;default:0;comment:供货价(单位:分);"` // 供货价、给采购端的协议价
	PriceType       PriceType `json:"price_type" form:"price_type" gorm:"column:price_type;default:0;comment:价格比例(单位：万分之一);"`               // 单位：万分之一
	//如果有activity_price时，那么cost_price为activity_price，否则为price
	IsDisplay int  `json:"is_display" form:"is_display" gorm:"column:is_display;default:0;comment:上架（1是0否）;type:smallint;size:1;index;"` // 上架（1是0否）
	Sales     uint `json:"sales" form:"sales" gorm:"column:sales;default:0;comment:销量;"`                                                 // 销量
	Sort      int  `json:"sort" form:"sort" gorm:"column:sort;default:0;comment:商品排序;type:int;"`                                         //     商品排序（序号小的在前）

	SmallShopID    uint    `json:"small_shop_id" from:"small_shop_id" gorm:"column:small_shop_id;index"`
	ProductID      uint    `json:"product_id" from:"product_id" gorm:"column:product_id;default:0;index"`
	Price          uint    `json:"price" from:"price" gorm:"column:price;default:0;comment:小商店销售价;"`
	Profit         int     `json:"profit" from:"profit" gorm:"column:profit;default:0;comment:利润;"`                              //利润
	ProfitRate     float64 `json:"profit_rate" from:"profit_rate" gorm:"column:profit_rate;default:0;comment:利润率;"`              //利润率
	ProfitDiscount float64 `json:"profit_discount" from:"profit_discount" gorm:"column:profit_discount;default:0;comment:利润折扣;"` //利润折扣
	PluginID       int     `json:"plugin_id" gorm:"column:plugin_id;default:0;"`                                                 //商品表的插件id用于区别是不是课程商品 课程：18

	Product model.Product `json:"product" gorm:"foreignKey:ProductID"` //
}

type SmallShopCategory struct {
	source.Model
	SmallShopID uint `json:"small_shop_id" from:"product_id" gorm:"index"`
	CategoryID  uint `json:"category_id" from:"category_id" gorm:"index"`
	Level       int  `json:"level" validate:"required" form:"level" gorm:"column:level;comment:层级;type:smallint;size:1;"`
}
type Product struct {
	model.Product
	SmallShopProductSale SmallShopProductSale `json:"small_shop_product_sale" gorm:"foreignKey:ProductID"`
}
type Sku struct {
	model.Sku
}

type ProductShopkeeper struct {
	source.Model
	Title     string `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(255);size:255;index;"`
	Price     uint   `json:"price" form:"price" gorm:"column:price;comment:供货价(单位:分);"`                                          // 供货价、给采购端的协议价
	Sales     uint   `json:"sales" form:"sales" gorm:"column:sales;comment:销量;"`                                                 // 销量
	IsDisplay int    `json:"is_display" form:"is_display" gorm:"column:is_display;comment:上架（1是0否）;type:smallint;size:1;index;"` // 上架（1是0否）
	ImageUrl  string `json:"image_url" gorm:"column:image_url;comment:图片url;"`                                                   // 图片url
}

func (ProductShopkeeper) TableName() string {
	return "products"
}

type SmallShopProductPond struct {
	source.Model
	ProductID uint `json:"product_id" form:"product_id" gorm:"column:product_id;default:0;index"`
}
