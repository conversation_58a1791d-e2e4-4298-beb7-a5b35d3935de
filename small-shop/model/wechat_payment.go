package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"gorm.io/gorm"
	"yz-go/model"
	"yz-go/source"
)

type WechatPaymentSetting struct {
	model.SysSetting
	Value WechatPaymentValue `json:"value"`
}
type WechatPaymentValue struct {
	// 微信支付状态1开始2关闭
	IsWxPayOpen int `json:"iswxpayopen"`
	// 微信H5状态1开始2关闭
	IsWxH5Open int `json:"iswxh5open"`
	// 微信扫码支付1开启2关闭
	IsWxCodeOpen int `json:"iswxcodeopen"`
	// AppID
	AppId string `json:"appid"`
	// AppSecret
	AppSecret string `json:"appsecret"`
	// 支付商户号
	Mchid string `json:"mch_id"`
	// 支付密钥
	PayKey string `json:"pay_key"`
	// cert证书文件路径
	CertPath string `json:"cert_path"`
	// cert证书文件路径
	KeyPath string `json:"key_path"`
	// 证书编号
	MchCertificateSerialNumber string `json:"mch_certificate_serial_number"`
	// 微信平台证书
	Cert string `json:"cert"`
	// 微信平台证书序列号
	Serial string `json:"serial"`
}

func (value WechatPaymentValue) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *WechatPaymentValue) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

func (WechatPaymentSetting) TableName() string {
	return "sys_settings"
}

func GetWechatPaymentSetting() (err error, setting WechatPaymentSetting) {
	err = source.DB().Where("`key` = ?", "small_shop_wechat_payment_setting").First(&setting).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	return
}

func UpdateWechatPaymentSetting(setting WechatPaymentSetting) (err error) {
	setting.Key = "small_shop_wechat_payment_setting"
	if setting.ID != 0 {
		err = source.DB().Updates(&setting).Error
	} else {
		err = source.DB().Create(&setting).Error
	}
	return err
}
