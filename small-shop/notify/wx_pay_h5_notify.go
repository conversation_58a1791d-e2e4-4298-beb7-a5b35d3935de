package notify

import (
	"context"
	"crypto/x509"
	"encoding/json"
	"errors"
	"github.com/gin-gonic/gin"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/auth/verifiers"
	"github.com/wechatpay-apiv3/wechatpay-go/core/notify"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments"
	"github.com/wechatpay-apiv3/wechatpay-go/services/refunddomestic"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
	"go.uber.org/zap"
	request2 "payment/request"
	"small-shop/model"
	"small-shop/payment"
	smallshoporder "small-shop/small-shop-order"
	"strconv"
	"yz-go/component/log"
	"yz-go/source"
)

func WxPayH5Notify(c *gin.Context) (err error) {
	var paySetting model.WechatPaymentSetting
	err, paySetting = model.GetWechatPaymentSetting()
	if err != nil {
		err = errors.New("微信支付配置获取失败")
		return
	}
	// 使用 utils 提供的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
	cert, err := utils.LoadCertificateWithPath("./data/goSupply/" + paySetting.Value.Cert)
	if err != nil {
		err = errors.New("证书不存在" + err.Error())
		return
		//log.Print("load merchant private key error")
	}
	handler := notify.NewNotifyHandler(
		paySetting.Value.PayKey, verifiers.NewSHA256WithRSAVerifier(core.NewCertificateMapWithList([]*x509.Certificate{cert})),
	)
	content := make(map[string]interface{})

	notifyReq, err := handler.ParseNotifyRequest(context.Background(), c.Request, content)
	if err != nil {
		log.Log().Error("微信支付回调解密错误", zap.Any("err", err.Error()))
		err = errors.New("微信支付回调:微信支付回调解密错误" + err.Error())
		return
	}
	if notifyReq.EventType == "TRANSACTION.SUCCESS" {
		var plaintext payments.Transaction
		_ = json.Unmarshal([]byte(notifyReq.Resource.Plaintext), &plaintext)

		var weChatNotify request2.WeChatNotify
		_ = json.Unmarshal([]byte(*plaintext.Attach), &weChatNotify)
		log.Log().Info("微信支付充值回调:微信请求数据", zap.Any("notifyReq", notifyReq), zap.Any("plaintext:解密数据", plaintext), zap.Any("weChatNotify", weChatNotify))

		var payInfo model.SmallShopPayInfo
		err = source.DB().Where("pay_sn = ?", weChatNotify.PaySN).First(&payInfo).Error
		if err != nil {
			log.Log().Error("微信支付回调:获取支付数据错误", zap.Any("err", err.Error()))
			err = errors.New("微信支付回调:获取支付数据错误" + err.Error())
			return
		}
		payInfo.TransactionId = *plaintext.TransactionId
		err = source.DB().Save(&payInfo).Error
		if err != nil {
			err = errors.New("填写微信支付单号出错" + err.Error())
			return
		}
		err = payment.Pay(strconv.Itoa(int(payInfo.PaySN)), weChatNotify.PayType)
		if err != nil {
			log.Log().Error(strconv.Itoa(int(payInfo.PaySN))+"微信支付回调:支付状态变更失败!", zap.Any("info", err))
			err = errors.New("微信支付回调:支付状态变更失败" + err.Error())
			return
		}
		var orderList []model.SmallShopOrderPayInfo
		err = source.DB().Where("small_shop_pay_info_id =  ?", payInfo.ID).Find(&orderList).Error
		if err != nil {
			log.Log().Error("微信支付回调处理出错!", zap.Any("info", err))
			err = errors.New("微信支付回调处理出错" + err.Error())
			return
		}
		for _, item := range orderList {
			err = smallshoporder.Pay(item.SmallShopOrderID, weChatNotify.PayType, item.SmallShopPayInfoID)
			if err != nil {
				log.Log().Error("微信支付回调：订单支付出错!", zap.Any("err", err.Error()))
				err = errors.New("微信支付回调：订单支付出错" + err.Error())
				return
			}
		}
	} else {
		log.Log().Error("微信支付回调:提示支付失败!", zap.Any("notifyReq", notifyReq))
	}

	return
}

func WxRefundNotifyH5(c *gin.Context) (err error) {
	log.Log().Debug("收到微信退款回调")
	var paySetting model.WechatPaymentSetting
	err, paySetting = model.GetWechatPaymentSetting()
	if err != nil {
		err = errors.New("微信支付配置获取失败")
		return
	}
	// 使用 utils 提供的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
	cert, err := utils.LoadCertificateWithPath("./data/goSupply/" + paySetting.Value.Cert)
	if err != nil {
		err = errors.New("证书不存在" + err.Error())
		return
		//log.Print("load merchant private key error")
	}
	handler := notify.NewNotifyHandler(
		paySetting.Value.PayKey, verifiers.NewSHA256WithRSAVerifier(core.NewCertificateMapWithList([]*x509.Certificate{cert})),
	)
	content := make(map[string]interface{})

	notifyReq, err := handler.ParseNotifyRequest(context.Background(), c.Request, content)
	if err != nil {
		log.Log().Error("微信退款解密错误", zap.Any("err", err.Error()))
		err = errors.New("微信退款:微信退款解密错误" + err.Error())
		return
	}

	var plaintext refunddomestic.Refund

	_ = json.Unmarshal([]byte(notifyReq.Resource.Plaintext), &plaintext)

	log.Log().Error("微信退款:微信请求数据", zap.Any("notifyReq", notifyReq), zap.Any("plaintext:解密数据", plaintext))
	var wechatRefund model.SmallShopWechatRefund
	err = source.DB().Where("refund_sn = ?", plaintext.OutRefundNo).First(&wechatRefund).Error
	if err != nil {
		log.Log().Error("微信退款:获取退款数据", zap.Any("err", err.Error()))
		err = errors.New("微信退款:获取退款数据" + err.Error())
		return
	}
	if plaintext.SuccessTime != nil {
		wechatRefund.Status = 2
		err = source.DB().Save(&wechatRefund).Error
		if err != nil {
			log.Log().Error("微信退款:退款成功，保存记录失败", zap.Any("err", err))
			err = errors.New("微信退款:退款成功，保存记录失败" + err.Error())
			return
		}
	}
	log.Log().Error("微信退款:退款失败", zap.Any("解密数据plaintext", plaintext))
	return
}
