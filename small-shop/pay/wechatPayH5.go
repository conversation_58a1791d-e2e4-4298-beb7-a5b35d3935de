package pay

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/h5"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
	"gorm.io/gorm"
	"io/ioutil"
	model3 "payment/model"
	"payment/request"
	"small-shop/model"
	request2 "small-shop/request"
	service2 "small-shop/service"
	"strconv"
	"time"
	"yz-go/config"
	"yz-go/source"
)

type WechatPayH5 struct {
	ConvergenceWeChatPayment interface{}
	ConvergenceWeChatRefund  interface{}
}

func (weChat *WechatPayH5) SplitAccount() (err error) {

	return
}

func (weChat *WechatPayH5) Increase() (err error) {
	return
}

func (weChat *WechatPayH5) AfterOperation() (err error) {
	return
}

func (weChat *WechatPayH5) BeforeOperation() (err error) {
	paymentData := weChat.ConvergenceWeChatPayment.(request.WeChatPayRequestData)
	var orderPayInfo model.SmallShopOrderPayInfo
	err = source.DB().Where("small_shop_pay_info_id = ?", paymentData.PayInfoID).First(&orderPayInfo).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("未查询到支付单")
		return
	}
	var order model.SmallShopOrder
	err = source.DB().Where("id = ?", orderPayInfo.SmallShopOrderID).First(&order).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("未查询到支付单")
		return
	}
	if order.Status > 0 {
		err = errors.New("该订单已支付,请勿重复支付")
		return
	}
	return
}

func (weChat *WechatPayH5) Init() (err error, resData interface{}) {
	PayTypeList = append(PayTypeList, Type{Name: "微信H5", Code: model3.WECHATPAYH5CODE, Status: 1, Mode: 1})
	return
}

func (weChat *WechatPayH5) Payment() (err error, resData interface{}) {
	err = weChat.BeforeOperation()
	if err != nil {
		return
	}
	payment := weChat.ConvergenceWeChatPayment.(request.WeChatPayRequestData)

	var payInfo model.SmallShopPayInfo
	err = source.DB().Where("id = ?", payment.PayInfoID).First(&payInfo).Error
	if err != nil {
		err = errors.New("查询支付单失败" + err.Error())
		return
	}
	err, paySetting := model.GetWechatPaymentSetting()
	if err != nil {
		err = errors.New("配置获取失败")
		return
	}
	var (
		mchID                      = paySetting.Value.Mchid                      // 商户号
		mchCertificateSerialNumber = paySetting.Value.MchCertificateSerialNumber // 商户证书序列号
		mchAPIv3Key                = paySetting.Value.PayKey                     // 商户APIv3密钥
	)
	mchPrivateKey, err := utils.LoadPrivateKeyWithPath("./data/goSupply/" + paySetting.Value.KeyPath)
	if err != nil {
		err = errors.New("证书不存在" + err.Error())
		return
	}
	ctx := context.Background()
	// 使用商户私钥等初始化 client，并使它具有自动定时获取微信支付平台证书的能力
	opts := []core.ClientOption{
		option.WithWechatPayAutoAuthCipher(mchID, mchCertificateSerialNumber, mchPrivateKey, mchAPIv3Key),
	}
	client, err := core.NewClient(ctx, opts...)
	if err != nil {
		err = errors.New("new wechat pay client err" + err.Error())
		return
	}
	config.Config().Local.Host, err = service2.LocUrl()
	if err != nil {
		return
	}
	// 查询小商店会员信息
	var user model.SmallShopUser
	err = source.DB().Where("id = ?", payInfo.SmallShopUserID).First(&user).Error
	if err != nil {
		err = errors.New("获取用户信息失败" + err.Error())
		return
	}
	if user.WxOpenid == "" {
		err = errors.New("用户没有绑定微信")
		return
	}
	//设置额外信息 支付通知会请求返回
	var weChatNotify request.WeChatNotify
	weChatNotify.PaySN = payInfo.PaySN
	weChatNotify.PayType = model3.WECHATPAYH5CODE
	attach, err := json.Marshal(weChatNotify)
	if err != nil {
		err = errors.New("数组转json失败" + err.Error())
		return
	}
	var orderList model.SmallShopOrderPayInfo
	err = source.DB().Where("small_shop_pay_info_id = ?", payInfo.ID).First(&orderList).Error
	if err != nil {
		err = errors.New("微信支付获取商品信息错误" + err.Error())
		return
	}
	var orderItems []model.SmallShopOrderItem
	err = source.DB().Where("small_shop_order_id = ?", orderList.SmallShopOrderID).Find(&orderItems).Error
	if err != nil {
		err = errors.New("微信支付获取商品信息错误1" + err.Error())
		return
	}
	var description string
	for _, item := range orderItems {
		description += item.Title + "-" + item.SkuTitle
	}
	if len(description) > 100 {
		description = description[:100] //保持描述120个字符
	}
	var notifyUrl = config.Config().Local.Host + "/supplyapi/api/smallShop/finance/wechatPayH5Notify"
	svc := h5.H5ApiService{Client: client}
	resData, result, err := svc.Prepay(ctx, h5.PrepayRequest{
		Appid:       core.String(paySetting.Value.AppId),
		Mchid:       core.String(mchID),
		Description: core.String(description),
		OutTradeNo:  core.String(strconv.Itoa(int(payInfo.PaySN))),
		TimeExpire:  core.Time(time.Now()),
		Attach:      core.String(string(attach)),
		NotifyUrl:   core.String(notifyUrl),
		Amount: &h5.Amount{
			Currency: core.String("CNY"),
			Total:    core.Int64(int64(payInfo.Amount)),
		},
		SceneInfo: &h5.SceneInfo{
			PayerClientIp: core.String(payment.Ip),
			H5Info: &h5.H5Info{
				Type: core.String(payment.Type),
			},
		},
	})
	if err != nil {
		var apiError core.APIError
		res, _ := ioutil.ReadAll(result.Response.Body)
		_ = json.Unmarshal(res, &apiError)
		err = errors.New(apiError.Message)
		return
	}
	return
}

func (weChat *WechatPayH5) Refund() (err error, resData interface{}) {
	refund := weChat.ConvergenceWeChatRefund.(request2.EntranceRefund)
	err = service2.WechatRefund(refund, 1)
	return
}

func (weChat *WechatPayH5) Recharge() (err error, resData interface{}) {
	return
}

func (weChat *WechatPayH5) Deduction() (err error, resData interface{}) {
	return
}

func (weChat *WechatPayH5) GetBalance() (err error, resData interface{}) {
	return
}

func (weChat *WechatPayH5) Settlement() (err error) {
	return
}
