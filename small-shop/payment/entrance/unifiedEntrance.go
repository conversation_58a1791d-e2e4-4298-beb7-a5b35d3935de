package entrance

import (
	"errors"
	paymentModel "payment/model"
	paymentpay "payment/pay"
	paymentRequest "payment/request"
	paymentPay "small-shop/pay"
	"small-shop/request"
)

func RefundService(reqParam request.EntranceRefund) (err error) {
	//log.Log().Debug("杨洋测试:RefundService()")
	var refundFace paymentPay.CommonInterface
	if reqParam.Type == paymentModel.AGGREGATE_PAYMENT {
		var refundRequest paymentRequest.Refund
		refundRequest.Amount = reqParam.Amount
		refundRequest.PaySN = reqParam.PaySN
		refundRequest.UserID = reqParam.UserID
		refundRequest.PayTypeID = reqParam.PayTypeID
		refundRequest.Type = reqParam.Type
		//数据通-聚合支付
		var convergenceBalance paymentpay.AggregatePaymentPay
		convergenceBalance.ConvergenceWeChatRefund = refundRequest
		refundFace = &convergenceBalance
	} else {
		switch reqParam.PayTypeID {
		case paymentModel.CONVERGENCEWECHAT:
			var convergenceWeChat paymentPay.ConvergenceWeChat
			convergenceWeChat.ConvergenceWeChatRefund = reqParam
			refundFace = &convergenceWeChat
			break
		case paymentModel.WXMINICODE:
			var convergenceWeChat paymentPay.WxMiniPay
			convergenceWeChat.ConvergenceWeChatRefund = reqParam
			refundFace = &convergenceWeChat
			break
		case paymentModel.CONVERGENCEMINIPLUS:
			var convergenceWeChat paymentPay.ConvergenceMiniPlus
			convergenceWeChat.ConvergenceWeChatRefund = reqParam
			refundFace = &convergenceWeChat
			break
		case paymentModel.BACKSTAGESTATIONBALANCE:
			// 后台支付的订单，不执行退款
			return
		case paymentModel.LAKALA:
			err = errors.New("拉卡拉支付不支持后台退款，请联系管理员")
			return
		default:
			err = errors.New("退款方式异常")
			return
		}
	}

	if err, _ = paymentPay.Refund(refundFace); err != nil {
		return
	}

	return
}
