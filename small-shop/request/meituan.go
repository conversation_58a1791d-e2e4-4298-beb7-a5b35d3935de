package request

type MeituanDetailPosterRequest struct {
	// 小商店id
	ShopID uint `json:"shop_id"`
	// 商品id
	ProductID string `json:"product_id"`
	// 商品名称
	ProductName string `json:"product_name"`
	// 商品主图
	ProductImg string `json:"product_img"`
	// 商品价格 sellPrice 价格单位为元 4.1
	ProductPrice string `json:"product_price"`
}

type MeituanShareLinkRequest struct {
	// 小商店id
	ShopID uint `json:"shop_id"`
	// 商品id
	ProductID string `json:"product_id"`
}
