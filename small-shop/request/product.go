package request

import (
	yzRequest "yz-go/request"
	"yz-go/source"
)

type ProductID struct {
	ID uint `json:"id" form:"id" query:"id"`
}

type ProductPosterRequest struct {
	SID       uint `json:"sid" form:"sid" query:"sid"`
	ProductID uint `json:"product_id" form:"product_id" query:"product_id"`
}

type ProductStorageSearch struct {
	yzRequest.PageInfo
	SID            uint    `json:"sid" form:"sid" query:"sid"`
	Category1ID    uint    `json:"category1_id" form:"category1_id"`                                                             // 一级分类
	Category2ID    uint    `json:"category2_id" form:"category2_id"`                                                             // 二级分类
	Category3ID    uint    `json:"category3_id" form:"category3_id"`                                                             // 三级分类
	SupplierID     uint    `json:"supplier_id" form:"supplier_id"`                                                               // 供应商ID
	SortBy         int     `json:"sort_by" form:"sort_by"`                                                                       // 1综合降2价格降3价格升4销量降5销量升6创建时间降7创建时间升8利润降9利润升
	MinPrice       int     `json:"min_price" form:"min_price"`                                                                   // 最低售价（分）
	MaxPrice       int     `json:"max_price" form:"max_price"`                                                                   // 最高售价（分）
	Title          string  `json:"title" form:"title"`                                                                           // 产品标题模糊
	IsNew          int     `json:"is_new" form:"is_new"`                                                                         // 新品
	IsRecommend    int     `json:"is_recommend" form:"is_recommend"`                                                             // 推荐
	IsHot          *int    `json:"is_hot" form:"is_hot"`                                                                         // 热卖
	IsPromotion    int     `json:"is_promotion" form:"is_promotion"`                                                             // 促销
	GatherSupplyID *uint   `json:"gather_supply_id" form:"gather_supply_id" gorm:"column:gather_supply_id;comment:供应链id;index;"` // 供应链id
	IsDisplay      *int    `json:"is_display" form:"is_display"`                                                                 // 上架下架
	ProfitForm     int     `json:"profit_form" form:"profit_form"`                                                               // 最低售价（分）
	ProfitTo       int     `json:"profit_to" form:"profit_to"`                                                                   // 最高售价（分）
	PriceForm      int     `json:"price_form" form:"price_form"`                                                                 // 最低售价（分）
	PriceTo        int     `json:"price_to" form:"price_to"`                                                                     // 最高售价（分）
	OriginRate     Between `json:"origin_rate" form:"origin_rate"`                                                               //常规利润率
	IsChoose       int     `json:"is_choose" form:"is_choose"`                                                                   // 只看未选
	IsVideoShop    int     `json:"is_video_shop" form:"is_video_shop"`                                                           // 2:已推送,1:可推送,-1:不可推送,其他:全部
	ProductIds     []uint  `json:"product_ids" form:"product_ids"`                                                               //商品ids  查询指定商品
	ShareLiveId    uint    `json:"share_live_id" form:"share_live_id"`                                                           //直播间id //直播间请求小商店商品列表使用
	MaterialType   int     `json:"material_type" form:"material_type"`                                                           // 1短视频2素材分发3直播
}

type Between struct {
	From *int `json:"from" form:"from"`
	To   *int `json:"to" form:"to"`
}

type BetweenTime struct {
	From *source.LocalTime `json:"from"`
	To   *source.LocalTime `json:"to"`
}

type ChangePriceReq struct {
	ProductID uint `json:"product_id" form:"product_id" query:"product_id"` // 商品id
	Strategy
}

type BatchChangePriceReq struct {
	ProductIDs []uint `json:"product_ids" form:"product_ids" query:"product_ids"` // 商品ids
	Strategy
}

type DygProductShareLinkRequest struct {
	ShopID    uint `json:"shop_id"`
	ProductID int  `json:"product_id"`
}
