package request

import "product/request"

type SyncProductToSelectedRequest struct {
	ProductID uint `json:"product_id" form:"product_id" query:"product_id"`
}

type SyncProductToSelectedBatchRequest struct {
	ProductIDs []uint `json:"product_ids" form:"product_ids" query:"product_ids"`
	// 商品搜索条件
	request.ProductSearch
}

type ProductToSelectedRequest struct {
	ProductIDs []uint `json:"product_ids" form:"product_ids" query:"product_ids"`
}
