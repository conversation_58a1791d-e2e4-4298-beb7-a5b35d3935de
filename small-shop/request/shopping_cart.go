package request

type ShoppingCartUpdate struct {
	ID               uint `json:"id" form:"id"`                                 // sku id
	Qty              uint `json:"qty" form:"qty"`                               // 数量
	AddressID        uint `json:"address_id" form:"address_id"`                 // 收货地址id
	ShippingMethodID int  `json:"shipping_method_id" form:"shipping_method_id"` // 配送方式id
	SkuId            uint `json:"sku_id" form:"sku_id"`                         // 更改的规格id
	Checked          *int `json:"checked" form:"checked"`                       // 是否选中
	SID              uint `json:"sid" form:"sid"`                               // 小商店 id
}

type BatchUpdate struct {
	Checked          *int `json:"checked" form:"checked"`                       // 全选
	AddressID        uint `json:"address_id" form:"address_id"`                 // 收货地址id
	ShippingMethodID int  `json:"shipping_method_id" form:"shipping_method_id"` // 配送方式id
	SID              uint `json:"sid" form:"sid"`                               // 小商店 id
}
