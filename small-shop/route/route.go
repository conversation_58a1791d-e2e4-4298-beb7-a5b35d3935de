package route

import (
	"github.com/gin-gonic/gin"
	smallShopFv1 "small-shop/api/f/v1"
	v12 "small-shop/api/shopkeeper/v1"
	v1 "small-shop/api/v1"
	v2 "small-shop/api/wechat/f/v1"
	v13 "small-shop/api/wx-mini/v1"
)

func InitAdminPublicRouter(Router *gin.RouterGroup) {
	IRouter := Router.Group("smallShop")
	{
		IRouter.POST("fix", v1.Fix)
	}
}

// InitAdminPrivateRouter 后台接口
func InitAdminPrivateRouter(Router *gin.RouterGroup) {
	// 中台管理小商店
	SmallShopRouter := Router.Group("smallShop")
	{
		// 获取微信支付（公众号）设置
		SmallShopRouter.GET("findWechatPaymentSetting", v1.FindWechatPaymentSetting)
		// 修改微信支付（公众号）设置
		SmallShopRouter.PUT("updateWechatPaymentSetting", v1.UpdateWechatPaymentSetting)
		// 上传微信支付证书文件
		SmallShopRouter.POST("uploadWechatCertFile", v1.UploadWechatCertFile)
		// 重置
		SmallShopRouter.POST("updateWechatPaymentMask", v1.UpdateWechatPaymentMask)

		//聚合支付基础设置
		SmallShopRouter.GET("findSmallShopAggregatedPaymentSetting", v1.FindSmallShopAggregatedPaymentSetting)     // 查询Setting
		SmallShopRouter.PUT("updateSmallShopAggregatedPaymentSetting", v1.UpdateSmallShopAggregatedPaymentSetting) // 更新Setting
		// 基础设置
		SmallShopRouter.GET("findSetting", v1.FindSetting)         // 查询Setting
		SmallShopRouter.PUT("updateSetting", v1.UpdateSetting)     // 更新Setting
		SmallShopRouter.GET("getPayTypes", v1.GetPayTypes)         // 获取支付方式
		SmallShopRouter.GET("getPayTypesByH5", v1.GetPayTypesByH5) // 获取支付方式
		SmallShopRouter.POST("syncUser", v1.SyncUser)              // 同步用户
		//基础设置-底部按钮设置
		SmallShopRouter.GET("findSmallShopNavigationSetting", v1.FindSmallShopNavigationSetting)     // 查询Setting
		SmallShopRouter.PUT("updateSmallShopNavigationSetting", v1.UpdateSmallShopNavigationSetting) // 更新Setting
		// 腾讯地图设置 弃用，使用附件中的设置 todo
		SmallShopRouter.GET("findTencentMapSetting", v1.FindTencentMapSetting)
		SmallShopRouter.PUT("updateTencentMapSetting", v1.UpdateTencentMapSetting)
		// 小程序设置
		SmallShopRouter.GET("findWxSetting", v1.FindWxSetting)                  // 获取小程序设置
		SmallShopRouter.PUT("updateWxSetting", v1.UpdateWxSetting)              // 修改小程序设置
		SmallShopRouter.POST("setConvergenceSetting", v1.SetConvergenceSetting) // 汇聚支付设置
		SmallShopRouter.GET("getConvergenceSetting", v1.GetConvergenceSetting)  // 汇聚支付获取
		SmallShopRouter.POST("uploadWechatMiniFile", v1.UploadWechatMiniFile)   // 上传JS安全文件
		SmallShopRouter.POST("downloaderWechatCert", v1.DownloaderWechatCert)   // 下载平台证书

		// 公众号设置
		SmallShopRouter.GET("findWechatOfficial", v1.FindWechatOfficial)     // 获取公众号设置
		SmallShopRouter.PUT("updateWechatOfficial", v1.UpdateWechatOfficial) // 修改公众号设置
		SmallShopRouter.POST("resetAppSecret", v1.ResetAppSecret)            // 重置AppSecret
		// 自定义菜单
		SmallShopRouter.GET("getMenuButton", v1.GetMenuButton)        // 获取按钮
		SmallShopRouter.POST("setMenuButton", v1.SetMenuButton)       // 设置按钮
		SmallShopRouter.POST("uploadFileWechat", v1.UploadFileWechat) // 上传微信公众号素材
		SmallShopRouter.GET("getMaterial", v1.GetMaterial)            // 获取资源  （微信公众号API）
		SmallShopRouter.POST("delMaterial", v1.DelMaterial)           // 删除资源
		// 上传js文件 使用店铺-微信公众号的上传js文件接口

		// 协议设置
		SmallShopRouter.GET("findProtocolSetting", v1.FindProtocolSetting)     // 查询Setting
		SmallShopRouter.PUT("updateProtocolSetting", v1.UpdateProtocolSetting) // 更新Setting
		// 小商店
		SmallShopRouter.POST("createSmallShop", v1.CreateSmallShop)                  // 创建小商店
		SmallShopRouter.GET("getAllSmallShop", v1.GetAllSmallShop)                   // 全部小商店(订单列表搜索使用)
		SmallShopRouter.GET("getSmallShopsList", v1.GetSmallShopsList)               // 小商店管理
		SmallShopRouter.GET("getSmallShopListBySetting", v1.GetSmallShopListByTitle) // 基础设置获取小商店列表
		SmallShopRouter.PUT("shopOpen", v1.ShopOpen)                                 // 开启小商店
		SmallShopRouter.PUT("shopClose", v1.ShopClose)                               // 关闭小商店
		SmallShopRouter.GET("getShopkeepersList", v1.GetShopkeepersList)             // 店主管理
		// 小商店会员
		SmallShopRouter.GET("getUserList", v1.GetUserList)        // 小商店会员
		SmallShopRouter.PUT("changeBlack", v1.ChangeBlack)        // 加入黑名单
		SmallShopRouter.PUT("changeWhite", v1.ChangeWhite)        // 加入白名单
		SmallShopRouter.GET("getUserInfo", v1.GetUserInfo)        // 获取用户信息
		SmallShopRouter.POST("exportUserList", v1.ExportUserList) // 导出用户列表
		// 收益管理
		SmallShopRouter.GET("getAwardsList", v1.GetAwardsList) // 收益管理
		// 小商店申请
		SmallShopRouter.GET("getAppliersList", v1.GetAppliersList) // 申请管理
		SmallShopRouter.PUT("passApply", v1.PassApply)             // 审核通过
		SmallShopRouter.PUT("failApply", v1.FailApply)             // 驳回审核
		// 订单管理
		SmallShopRouter.GET("getOrderList", v1.GetOrderList)                    // 订单列表
		SmallShopRouter.GET("exportOrderList", v1.ExportOrderList)              // 导出订单列表
		SmallShopRouter.POST("updateShippingAddress", v1.UpdateShippingAddress) // 修改订单收货信息
		SmallShopRouter.GET("getShippingAddressLog", v1.GetShippingAddressLog)  // 获取订单收货地址修改记录
		SmallShopRouter.GET("getOrderDetail", v1.FindOrder)                     // 订单详情
		SmallShopRouter.POST("order/pay", v1.PayOrder)                          // 中台确认付款
		SmallShopRouter.POST("order/receive", v1.ReceiveOrder)                  // 中台确认收货
		SmallShopRouter.POST("order/close", v1.CloseOrder)                      // 中台关闭小商店订单
		SmallShopRouter.POST("order/refund", v1.Refund)                         // 退款
		SmallShopRouter.POST("order/itemRefund", v1.ItemRefund)                 // 退款
		// 查看快递 GET  order/express   参数supply_order_id
		SmallShopRouter.POST("order/note", v1.UpdateNote) // 卖家留言
		// 售后
		SmallShopRouter.GET("afterSales/findByOrderItemId", v1.FindAfterSalesByOrderItemIds) // 根据orderItemId获取售后详情（获取最近的一个）
		// 支付记录
		SmallShopRouter.POST("finance/getPaymentRecord", v1.PaymentRecord) //支付记录

		SmallShopRouter.POST("getDeliveryListWxMini", v13.GetDeliveryListWxMini)
		SmallShopRouter.POST("uploadShippingInfo", v13.UploadShippingInfo)

		// 商品池-商品池的商品列表
		SmallShopRouter.GET("product/getProductListByPond", v1.GetProductListByPond)
		// 商品池-添加商品的商品列表
		SmallShopRouter.GET("product/getProductListByPondAdd", v1.GetProductListByPondAdd)
		// 商品池-添加or批量添加
		SmallShopRouter.POST("product/addPond", v1.AddProductToPond)
		// 商品池-移除or批量移除
		SmallShopRouter.POST("product/removePond", v1.RemoveProductFromPond)
		// 商品池-添加筛选商品
		SmallShopRouter.POST("product/addPondBySearch", v1.AddProductToPondBySearch)

		// 指定专辑-专辑列表
		SmallShopRouter.GET("productAlbum/getAlbumListByPond", v1.GetAlbumListByPond)
		// 指定专辑-添加专辑的专辑列表
		SmallShopRouter.GET("productAlbum/getAlbumListByPondAdd", v1.GetAlbumListByPondAdd)
		// 指定专辑-添加or批量添加
		SmallShopRouter.POST("productAlbum/addPond", v1.AddAlbumToPond)
		// 指定专辑-移除or批量移除
		SmallShopRouter.POST("productAlbum/removePond", v1.RemoveAlbumFromPond)
		// 指定专辑-添加筛选专辑
		SmallShopRouter.POST("productAlbum/addPondBySearch", v1.AddAlbumToPondBySearch)

		// 指定选品-列表
		SmallShopRouter.GET("product/getSelectedList", v1.GetSelectedList)
		// 指定选品-增加选品的商品列表
		SmallShopRouter.GET("product/getProductListBySelectedAdd", v1.GetProductListBySelectedAdd)
		// 指定选品-增加选品
		SmallShopRouter.POST("product/addSelected", v1.AddProductToSelected)
		// 指定选品-移除选品
		SmallShopRouter.POST("product/removeSelected", v1.RemoveProductFromSelected)
		// 指定选品-单个同步
		SmallShopRouter.POST("product/syncSelected", v1.SyncProductToSelected)
		// 指定选品-批量同步
		SmallShopRouter.POST("product/syncSelectedBatch", v1.SyncProductToSelectedBatch)
	}
}

// InitShopkeeperPrivateRouter 店主鉴权接口
func InitShopkeeperPrivateRouter(Router *gin.RouterGroup) {
	// 店主管理小商店
	SmallShopRouter := Router.Group("smallShop")
	{
		// 小商店中心
		SmallShopRouter.GET("center/getCenterInfo", smallShopFv1.GetCenterInfo) // 小商店中心
		// 访问小店集合接口
		SmallShopRouter.GET("access", smallShopFv1.Access)
		// 获取小程序码
		SmallShopRouter.GET("setting/getMiniQrCode", smallShopFv1.GetMiniQrCode)
		// 设置
		SmallShopRouter.GET("setting/getMiniAppID", smallShopFv1.GetMiniAppID)           // 查询appid和原始id
		SmallShopRouter.GET("setting/getShopSetting", smallShopFv1.GetShopSetting)       // 查询小商店设置
		SmallShopRouter.PUT("setting/updateShopSetting", smallShopFv1.UpdateShopSetting) // 更新小商店设置
		// 定价策略设置
		SmallShopRouter.GET("setting/getShopProductSetting", smallShopFv1.GetShopProductSetting)       // 查询定价策略
		SmallShopRouter.PUT("setting/updateShopProductSetting", smallShopFv1.UpdateShopProductSetting) // 更新定价策略
		// 专辑
		SmallShopRouter.GET("album/getAlbumList", smallShopFv1.GetAlbumList)                             // 获取专辑库
		SmallShopRouter.GET("album/getAllTag", smallShopFv1.GetAllTag)                                   // 获取全部专辑标签
		SmallShopRouter.GET("album/getSmallShopAlbumListByUid", smallShopFv1.GetSmallShopAlbumListByUid) // 获取已导入的专辑
		SmallShopRouter.GET("album/getAllTagByImported", smallShopFv1.GetAllTagByImported)               // 获取已导入专辑的标签
		SmallShopRouter.DELETE("album/removeAlbum", smallShopFv1.RemoveAlbum)                            // 移除专辑
		SmallShopRouter.POST("album/importAlbum", smallShopFv1.ImportAlbum)                              // 导入专辑
		SmallShopRouter.POST("album/importAllAlbum", smallShopFv1.ImportAllAlbum)                        // 导入全部专辑
		SmallShopRouter.GET("album/getAlbum", smallShopFv1.FindAlbum)                                    // 查看专辑
		SmallShopRouter.GET("album/getProductList", smallShopFv1.GetProductListByAlbumID)                // 获取专辑商品
		// 专辑详情请求, 返回是否已加入小店
		SmallShopRouter.POST("album/exist", smallShopFv1.VerifyAlbumExist)
		// 分类
		SmallShopRouter.GET("category/getCategoryByParentID", smallShopFv1.GetCategoryByParentID) // 获取分类
		// 店主管理商品
		SmallShopRouter.POST("product/addByProductID", v12.AddProductSaleByProductID)   // 提交选品
		SmallShopRouter.GET("product/getProductList", smallShopFv1.GetProductList)      // 获取商品列表-在线选品
		SmallShopRouter.GET("product/getMyProductList", smallShopFv1.GetMyProductList)  // 获取商店商品列表-商品管理
		SmallShopRouter.DELETE("product/deleteProduct", smallShopFv1.DeleteProduct)     // 批量or单个 删除商品
		SmallShopRouter.GET("product/findProduct", smallShopFv1.FindProduct)            // 商店商品详情
		SmallShopRouter.POST("product/changePrice", smallShopFv1.ChangePrice)           // 改价
		SmallShopRouter.POST("product/batchChangePrice", smallShopFv1.BatchChangePrice) // 批量改价
		// 商品详情请求, 返回是否已加入小店
		SmallShopRouter.POST("product/exist", smallShopFv1.VerifyProductExist)
		// 店主管理订单
		SmallShopRouter.GET("smallShopOrder/get", v12.FindOrder)                  // 订单详情
		SmallShopRouter.GET("smallShopOrder/list", v12.GetOrderList)              // 店主获取订单列表
		SmallShopRouter.POST("smallShopOrder/pay", v12.PayOrder)                  // 店主确认付款
		SmallShopRouter.POST("smallShopOrder/receive", v12.ReceiveOrder)          // 店主确认收货
		SmallShopRouter.POST("smallShopOrder/close", v12.CloseOrder)              // 店主关闭订单
		SmallShopRouter.POST("smallShopOrder/getExpressInfo", v12.GetExpressInfo) // 店主获取快递信息
		SmallShopRouter.GET("smallShopOrder/getOrderItem", v12.FindOrderItem)     //通过子订单id 获取子订单详情
		// 售后
		SmallShopRouter.GET("afterSales/list", v12.GetAfterSalesList)                          // 售后列表
		SmallShopRouter.GET("afterSales/find", v12.FindAfterSales)                             // 根据id获取售后详情
		SmallShopRouter.GET("afterSales/getByOrderItemId", v12.FindAfterSalesByOrderItemId)    // 根据orderItemId获取售后详情（获取最近的一个）
		SmallShopRouter.GET("afterSales/getShopAddressByOrderId", v12.GetShopAddressByOrderID) // 获取默认退货地
		SmallShopRouter.GET("getAfterSalesCount", v12.GetAfterSalesCount)                      // 获取正在进行的售后数量
		SmallShopRouter.POST("afterSales/applyVideo", v12.ApplyVideoAfterSales)                // 店主申请视频号订单中台售后

		SmallShopRouter.POST("afterSales/video/create", v12.VideoCreateAfterSales)   // 店主申请售后
		SmallShopRouter.POST("afterSales/video/save", v12.VideoSaveAfterSales)       // 店主修改售后
		SmallShopRouter.POST("afterSales/video/close", v12.VideoClose)               // 店主关闭售后
		SmallShopRouter.POST("afterSales/video/send", v12.VideoSend)                 // 店主售后填写退货物流
		SmallShopRouter.POST("afterSales/video/userReceive", v12.VideoUserReceive)   // 店主售后换货确认收货
		SmallShopRouter.GET("afterSales/video/reason/list", v12.VideoGetReasonList)  // 店主获取售后原因列表
		SmallShopRouter.GET("afterSales/video/get", v12.VideoFindAfterSales)         // 店主通过售后id获取售后详情
		SmallShopRouter.GET("afterSales/video/findShopAddress", v12.FindShopAddress) // 店主获取默认退货地址
		SmallShopRouter.GET("afterSales/video/getList", v12.VideoGetAfterSalesList)  // 店主获取售后列表
	}
}

// InitSmallShopKeeperPrivateRouter 店主接口
func InitSmallShopKeeperPrivateRouter(Router *gin.RouterGroup) {
	// 店主管理小商店
	SmallShopRouter := Router.Group("smallShop")
	{
		// 店铺申请
		SmallShopRouter.POST("apply/subApply", smallShopFv1.SubApply)            // 提交申请
		SmallShopRouter.GET("apply/getApplyStatus", smallShopFv1.GetApplyStatus) // 获取可提交申请状态
		// 小商店中心
		SmallShopRouter.GET("center/showCenter", smallShopFv1.ShowCenter) // 小商店按钮
	}
	public := Router.Group("curriculum") //课程中台店主用接口
	{
		//--------------------------------中台小商店  新增接口
		public.POST("getCourseCategorys", v1.GetCourseCategorys)         //获取课程商品使用的分类
		public.POST("getMyCourseProductList", v1.GetMyCourseProductList) //获取课程商品列表 //需求仅有已导入的课程列表
		//一键导入课程
		public.POST("importCourseProduct", v1.ImportCourseProduct) //一件导入课程商品到小商店

	}
}

// InitSmallShopUserPrivateRouter 会员接口
func InitSmallShopUserPrivateRouter(Router *gin.RouterGroup) {
	// 快递
	ShippingRouter := Router.Group("shipping")
	{
		ShippingRouter.GET("list", v13.GetShippingList)        // 获取Shipping列表
		ShippingRouter.GET("company/list", v13.GetCompanyList) // 获取快递公司列表
		ShippingRouter.GET("info", v13.GetExpressInfoByCode)   // 获取物流详情
	}
	// 基础设置
	SettingRouter := Router.Group("setting")
	{
		SettingRouter.GET("get", v13.GetSetting) // 查询Setting

	}
	// 店主信息
	ShopkeeperRouter := Router.Group("shopkeeper")
	{
		ShopkeeperRouter.GET("info", v13.GetInfo)           // 用户信息
		ShopkeeperRouter.GET("setting", v13.GetShopSetting) // 获取店主设置
		// 当前小程序会员是否是平台店主
		ShopkeeperRouter.GET("isShopkeeper", v13.IsShopkeeper)
	}
	// 店主设置
	// 小商店会员使用小商店
	SmallShopRouter := Router.Group("user")
	{
		SmallShopRouter.POST("info", smallShopFv1.GetInfo)                            // 用户信息
		SmallShopRouter.POST("update", smallShopFv1.UpdateUser)                       // 修改用户信息
		SmallShopRouter.POST("address/add", smallShopFv1.CreateAddress)               // 创建收货地址
		SmallShopRouter.POST("address/delete", smallShopFv1.DeleteAddress)            // 删除收货地址
		SmallShopRouter.POST("address/delete/batch", smallShopFv1.DeleteAddressByIds) // 批量删除收货地址
		SmallShopRouter.POST("address/update", smallShopFv1.UpdateAddress)            // 更新收货地址
		SmallShopRouter.POST("address/get", smallShopFv1.FindAddress)                 // 根据ID获取收货地址
		SmallShopRouter.POST("address/list", smallShopFv1.GetAddressList)             // 获取收货地址列表
	}
	// 会员使用购物车
	SmallShopShoppingCartRouter := Router.Group("shoppingCart")
	{
		SmallShopShoppingCartRouter.POST("add", smallShopFv1.CreateShoppingCart)               // 新建ShoppingCart
		SmallShopShoppingCartRouter.GET("list", smallShopFv1.GetShoppingCartList)              // 获取ShoppingCart列表
		SmallShopShoppingCartRouter.POST("delete/batch", smallShopFv1.DeleteShoppingCartByIds) // 批量删除ShoppingCart
		SmallShopShoppingCartRouter.POST("update", smallShopFv1.UpdateShoppingCart)            // 更新ShoppingCart
		SmallShopShoppingCartRouter.POST("update/batch", smallShopFv1.BatchUpdateShoppingCart) // 批量更新ShoppingCart
	}
	// 会员下单
	SmallShopTradeRouter := Router.Group("trade")
	{
		SmallShopTradeRouter.POST("checkout", smallShopFv1.ShoppingCartCheckout) // 购物车结算
		SmallShopTradeRouter.POST("confirm", smallShopFv1.ShoppingCartConfirm)   // 购物车下单
		SmallShopTradeRouter.POST("cashier", smallShopFv1.Cashier)               // 收银台信息
		SmallShopTradeRouter.POST("buy", smallShopFv1.ProductCheckout)           // 立即购买
	}
	// 会员订单
	SmallShopOrderRouter := Router.Group("order")
	{
		SmallShopOrderRouter.POST("close", smallShopFv1.CloseOrder)     // 小商店会员关闭自己的订单
		SmallShopOrderRouter.POST("receive", smallShopFv1.ReceiveOrder) // 小商店会员确认收货
		SmallShopOrderRouter.GET("expressInfo", v13.GetExpressInfo)     // 快递信息
		SmallShopOrderRouter.GET("list", v13.GetOrderList)              // 订单列表
		SmallShopOrderRouter.GET("get", v13.FindOrder)                  // 订单详情
		SmallShopOrderRouter.GET("getOrderItem", v13.FindOrderItem)     //通过子订单id 获取子订单详情
	}
	// 会员售后
	SmallShopAfterSalesRouter := Router.Group("alterSales")
	{
		SmallShopAfterSalesRouter.POST("close", smallShopFv1.Close)                                           // 关闭售后
		SmallShopAfterSalesRouter.POST("create", smallShopFv1.CreateAfterSales)                               // 小商店会员申请售后
		SmallShopAfterSalesRouter.POST("send", smallShopFv1.Send)                                             // 小商店会员售后填写退货物流
		SmallShopAfterSalesRouter.POST("userReceive", smallShopFv1.UserReceive)                               // 小商店会员售后换货确认收货
		SmallShopAfterSalesRouter.GET("reason/list", smallShopFv1.GetReasonList)                              // 小商店会员获取售后原因列表
		SmallShopAfterSalesRouter.GET("get", smallShopFv1.FindAfterSales)                                     // 小商店会员通过售后id获取售后详情
		SmallShopAfterSalesRouter.GET("findShopAddressByOrderId", smallShopFv1.FindShopAddressByOrderID)      // 获取默认退货地址
		SmallShopAfterSalesRouter.GET("getAfterSalesByOrderItemId", smallShopFv1.FindAfterSalesByOrderItemId) // 获取通过order_item_id获取售后详情
		SmallShopAfterSalesRouter.GET("getList", smallShopFv1.GetAfterSalesList)                              // 获取售后列表
	}
	// 会员购物查看商品
	SmallShopProductRouter := Router.Group("product")
	{
		SmallShopProductRouter.POST("list", v13.GetProductList) // 商品列表
		SmallShopProductRouter.GET("poster", v13.GetPosterInfo) // 商品海报
		SmallShopProductRouter.GET("find", v13.FindProduct)     // 商品详情
	}
	// 特卖专辑
	SmallShopProductAlbumRouter := Router.Group("album")
	{
		SmallShopProductAlbumRouter.GET("list", v13.GetAlbumList)                               // 特卖专辑列表
		SmallShopProductAlbumRouter.GET("findAlbum", v13.FindAlbum)                             // 查看专辑
		SmallShopProductAlbumRouter.GET("getProductListByAlbumID", v13.GetProductListByAlbumID) // 获取专辑商品
	}
	// 会员联系客服
	SmallShopServiceRouter := Router.Group("service")
	{
		SmallShopServiceRouter.GET("qrCode", v13.GetQrCode) // 获取店长二维码
	}
	// 会员查看分类
	SmallShopCategoryRouter := Router.Group("category")
	{
		SmallShopCategoryRouter.GET("getListByParentID", v13.GetCategoryByParentID) // 通过父级id获取分类
	}

	PayNotifyRouter := Router.Group("finance")
	{
		PayNotifyRouter.POST("joinPlusPay", smallShopFv1.JoinPlusPay)                 //汇聚plus支付
		PayNotifyRouter.POST("lakalaPay", smallShopFv1.LakalaPay)                     //lakala支付
		PayNotifyRouter.POST("wechatPay", smallShopFv1.WechatPay)                     //小商店会员汇聚支付
		PayNotifyRouter.POST("wechatPayment", smallShopFv1.WechatPayment)             //小商店会员微信支付
		PayNotifyRouter.POST("aggregatePaymentPay", smallShopFv1.AggregatePaymentPay) //小商店数据通-聚合支付

	}

	public := Router.Group("curriculum") //课程中台前端用接口
	{
		//--------------------------------中台小商店  新增接口
		public.POST("smallShopSelectCurriculumProductDetail", v1.SmallShopSelectCurriculumProductDetail) // 小商店查询单条课程详情以及是否购买
		public.POST("getCourseOrderList", v1.GetCourseOrderList)                                         //已购买的课程订单列表
		public.POST("getVideoUrl", v1.GetVideoUrl)                                                       //生成课程视频 防盗链url
		public.POST("findLecturerAndCurriculum", v1.FindLecturerAndCurriculum)                           //查询单条讲师并且关联查询课程
		//查询单条讲师信息并且关联课程列表  // 中台查询课程是否有试看权限  -- 使用H5的即可 位置  course-distribution/route/router.go:79
	}
	// 抖音团购
	DouyinGroupRouter := Router.Group("douyinGroup")
	{
		// 分类列表
		DouyinGroupRouter.GET("category/list", v13.GetDouyinCategoryList)
		// 抖音城市列表
		DouyinGroupRouter.GET("city/list", v13.GetDouyinCities)
		// 商品列表
		// DouyinGroupRouter.POST("product/list", v13.GetDouyinProductList)
		// 商品详情
		DouyinGroupRouter.POST("product/detail", v13.GetProductDetail)
		// 转链
		DouyinGroupRouter.POST("product/link", v13.GetProductLink)
		// 文案-详情地址
		DouyinGroupRouter.POST("product/shareLink", v13.GetShareLink)
		// 获取分享海报
		DouyinGroupRouter.POST("product/poster", v13.GetPoster)
		// 获取腾讯地图key
		DouyinGroupRouter.GET("getTencentMapKey", v13.GetTencentMapKey)
	}
	// 聚合
	CpsRouter := Router.Group("cps")
	{
		// 获取活动列表
		CpsRouter.GET("activity/list", v13.GetActivityList)
		// 生成链接
		CpsRouter.POST("generateLink", v13.GenerateLink)
		// 获取美团商品列表
		// CpsRouter.POST("meituan/getCouponList", v13.GetMeituanCouponList)
		// 获取美团分类
		CpsRouter.GET("meituan/getCategoryList", v13.GetMeituanCategoryList)
		// 获取美团城市
		CpsRouter.GET("meituan/getCityList", v13.GetMeituanCityList)
		// 获取美团商品海报
		CpsRouter.POST("meituan/getCouponPoster", v13.GetMeituanCouponPoster)
		// 文案-详情地址
		CpsRouter.POST("meituan/shareLink", v13.GetMeituanShareLink)
	}
}

func InitSmallShopUserPublicRouter(Router *gin.RouterGroup) {
	// 会员未登录状态下使用的接口
	CpsRouter := Router.Group("cps")
	{
		// 获取美团商品列表
		CpsRouter.POST("meituan/getCouponList", v13.GetMeituanCouponList)
	}
	DouyinGroupRouter := Router.Group("douyinGroup")
	{
		// 商品列表
		DouyinGroupRouter.POST("product/list", v13.GetDouyinProductList)
	}
	SmallShopRouter := Router.Group("user")
	{

		SmallShopRouter.POST("sendCode", smallShopFv1.SendSmsCode) // 发送验证码
		SmallShopRouter.POST("checkCode", smallShopFv1.CheckCode)
		SmallShopRouter.POST("register", smallShopFv1.Register)                 // 新建User
		SmallShopRouter.POST("login", smallShopFv1.Login)                       // 密码登录
		SmallShopRouter.POST("wechatMiniRegister", smallShopFv1.WechatRegister) // 微信/小程序-绑定手机号/注册/已有账号绑微信/小程序
		SmallShopRouter.POST("wechatH5Register", smallShopFv1.WechatH5Register)
		SmallShopRouter.GET("findSmallShopNavigationSetting", v1.FindSmallShopNavigationSetting) // 查询Setting
		// 验证码登录
		SmallShopRouter.POST("loginWithCode", smallShopFv1.LoginWithCode)
	}
	// 基础设置
	SettingRouter := Router.Group("setting")
	{
		SettingRouter.GET("getSetting", v13.GetSetting)                   // 查询Setting
		SettingRouter.GET("findProtocolSetting", v13.FindProtocolSetting) //查询小商店协议  两个 标题+内容
		// H5获取支付方式
		SettingRouter.GET("getPayTypesByH5", v13.GetPayTypesByH5)

	}
	PayNotifyRouter := Router.Group("finance")
	{
		//PayNotifyRouter.POST("lakalaPay", smallShopFv1.LakalaPay)                    //lakala支付
		//PayNotifyRouter.POST("wechatPay", smallShopFv1.WechatPay)                    //小商店会员汇聚支付
		//PayNotifyRouter.POST("wechatPayment", smallShopFv1.WechatPayment)            //小商店会员微信支付
		PayNotifyRouter.Any("lakalaPayNotify", smallShopFv1.LakalaPayNotify)         //lakala回调
		PayNotifyRouter.Any("joinPayNotify", smallShopFv1.JoinPayNotifyBySmallShop)  //小商店会员汇聚支付回调地址
		PayNotifyRouter.Any("wechatPayMiniNotify", smallShopFv1.WechatPayMiniNotify) //小商店会员微信支付回调地址
		PayNotifyRouter.Any("wechatPayH5Notify", smallShopFv1.WechatPayH5Notify)
		PayNotifyRouter.POST("wxRefundNotifyMini", smallShopFv1.WxRefundNotifyMini) //微信小程序退款回调
		PayNotifyRouter.POST("wxRefundNotifyH5", smallShopFv1.WxRefundNotifyH5)
		PayNotifyRouter.Any("joinPlusPayNotify", smallShopFv1.JoinPlusPayNotify)                                     //汇聚微信小程序plus回调
		PayNotifyRouter.Any("convergenceMicroPlusSmallShopRefund", smallShopFv1.ConvergenceMicroPlusSmallShopRefund) //汇聚微信小程序退款回调

	}
}

func InitWechatMiniPublicRouter(Router *gin.RouterGroup) {
	WechatMiniRouter := Router.Group("smallShopWechatMini")
	{
		WechatMiniRouter.Any("login", v13.WxMiniLogin)                                // 小程序授权登录
		WechatMiniRouter.GET("shopkeeper/setting", v13.GetShopSetting)                // 获取店主设置
		WechatMiniRouter.GET("shopkeeper/info", v13.GetInfo)                          // 获取店主信息
		WechatMiniRouter.POST("product/list", v13.GetProductList)                     // 商品列表
		WechatMiniRouter.GET("product/find", v13.FindProduct)                         // 商品详情
		WechatMiniRouter.GET("category/getListByParentID", v13.GetCategoryByParentID) // 通过父级id获取分类
		WechatMiniRouter.GET("category/categoryList", v13.CategoryList)               // 通过父级id获取分类

		WechatMiniRouter.GET("album/list", v13.GetAlbumList)                               // 特卖专辑列表
		WechatMiniRouter.GET("album/findAlbum", v13.FindAlbum)                             // 查看专辑
		WechatMiniRouter.GET("album/getProductListByAlbumID", v13.GetProductListByAlbumID) // 获取专辑商品
	}

	WechatRouter := Router.Group("wechat")
	{
		// 回调
		WechatRouter.Any("serveWechat", v2.ServeWechat)
		// 授权登录
		WechatRouter.Any("login", smallShopFv1.WechatLogin)
		// 获取H5调用微信JS的配置
		WechatRouter.GET("getJsConfig", smallShopFv1.GetJsConfig)
		// 是否开启公众号
		WechatRouter.GET("isOpenWechat", smallShopFv1.IsOpenWechat)
	}
}
