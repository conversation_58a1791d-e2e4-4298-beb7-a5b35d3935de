package service

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-redis/redis/v8"
	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"
	"gorm.io/gorm"
	_ "image/gif"
	_ "image/jpeg"
	model2 "product/model"
	productMq "product/mq"
	productService "product/service"
	shareLiveModel "share-live/model"
	"small-shop/model"
	"small-shop/mq"
	"small-shop/request"
	"small-shop/response"
	"strconv"
	"time"
	"user/level"
	"yz-go/common_data"
	"yz-go/component/log"
	"yz-go/source"
)

type SmallShopProductSale struct {
	model.SmallShopProductSale
}

func Decimal(value float64) float64 {
	var err error
	value, err = strconv.ParseFloat(fmt.Sprintf("%0.2f", value), 64)
	if err != nil {
		return 0
	}
	return value
}

func FindSmallShopProductSale(productID, sid uint) (err error, sale SmallShopProductSale) {
	err = source.DB().Model(&SmallShopProductSale{}).Where("small_shop_id = ? and product_id = ?", sid, productID).First(&sale).Error
	return
}

func FindProduct(req request.ProductPosterRequest) (err error, product response.Product) {
	err = source.DB().Model(&response.Product{}).Where("id = ?", req.ProductID).Where("freeze = 0").Preload("Skus").First(&product).Error
	if err != nil {
		return
	}
	var sale SmallShopProductSale
	err, sale = FindSmallShopProductSale(req.ProductID, req.SID)
	if err != nil {
		return
	}
	product.ShopPrice = product.Price
	if sale.PriceProportion != 0 {
		var agreetmentPrice uint
		agreetmentPrice = 0
		if product.MinPrice > 0 {
			agreetmentPrice = product.MinPrice
		} else {
			agreetmentPrice = product.Price
		}
		switch sale.PriceType {
		case model.ORIGIN:
			if product.OriginPrice != 0 {
				product.ShopPrice = product.OriginPrice * sale.PriceProportion / 10000
			}
		case model.GUIDE:
			if product.GuidePrice != 0 {
				product.ShopPrice = product.GuidePrice * sale.PriceProportion / 10000
			}
		case model.PRICE:
			if agreetmentPrice > 0 {
				product.ShopPrice = agreetmentPrice * sale.PriceProportion / 10000
			}
		case model.ACTIVITY:
			if product.ActivityPrice != 0 {
				product.ShopPrice = product.ActivityPrice * sale.PriceProportion / 10000
			}
		}
		for k, sku := range product.Skus {
			product.Skus[k].ShopPrice = sku.Price
			switch sale.PriceType {
			case model.ORIGIN:
				if sku.OriginPrice != 0 {
					product.Skus[k].ShopPrice = sku.OriginPrice * sale.PriceProportion / 10000
				}
			case model.GUIDE:
				if sku.GuidePrice != 0 {
					product.Skus[k].ShopPrice = sku.GuidePrice * sale.PriceProportion / 10000
				}
			case model.PRICE:
				if sku.Price != 0 {
					product.Skus[k].ShopPrice = sku.Price * sale.PriceProportion / 10000
				}
			case model.ACTIVITY:
				if sku.ActivityPrice != 0 {
					product.Skus[k].ShopPrice = sku.ActivityPrice * sale.PriceProportion / 10000
				}
			}

		}
	}

	return
}

func FindProductByPoster(id uint) (err error, product ProductByPoster) {
	err = source.DB().Model(&ProductByPoster{}).Where("id = ?", id).First(&product).Error
	return
}

type MyProductList struct {
	ID                   uint    `json:"id"`
	Title                string  `json:"title"`
	ImageUrl             string  `json:"image_url"`
	Sales                int     `json:"sales"`
	ShopPrice            uint    `json:"shop_price"`
	Profit               int     `json:"profit"`                 //利润
	ProfitRate           float64 `json:"profit_rate"`            //利润率
	TechnicalServicesFee uint    `json:"technical_services_fee"` // 技术服务费
	AgreementPrice       uint    `json:"agreement_price"`        //协议价
	GuidePrice           uint    `json:"guide_price"`            //指导价
	ActivityPrice        uint    `json:"activity_price"`         //营销价
	OriginPrice          uint    `json:"origin_price"`           //市场价
	// 会员等级折扣比例
	LevelDiscountPercent int `json:"level_discount_percent"`
	//SmallShopIdString    string  `json:"small_shop_id_string"`
	SmallShopProductSale struct {
		PriceProportion uint            `json:"price_proportion"`
		PriceType       model.PriceType `json:"price_type"`
	} `json:"small_shop_product_sale"`
}

type MyProductListTest struct {
	ID                uint   `json:"id"`
	SmallShopIdString string `json:"small_shop_id_string"`
}

func GetMyProductListTest(info request.ProductStorageSearch, userID uint) (err error, list []MyProductListTest, total int64) {
	es, err := source.ES()
	boolQ := elastic.NewBoolQuery()
	/*eIDs := make([]interface{}, 27)
	eIDs[0] = 1050201
	eIDs[1] = 1050193
	eIDs[2] = 1050512
	eIDs[3] = 1052654
	eIDs[4] = 1052655
	eIDs[5] = 1050051
	eIDs[6] = 1051298
	eIDs[7] = 1049965
	eIDs[8] = 1056358
	eIDs[9] = 1057229
	eIDs[10] = 1049642
	eIDs[11] = 1049853
	eIDs[12] = 1055852
	eIDs[13] = 1054781
	eIDs[14] = 1056408
	eIDs[15] = 1052675
	eIDs[16] = 1093842
	eIDs[17] = 1050824
	eIDs[18] = 1049582
	eIDs[19] = 1049974
	eIDs[20] = 1051611
	eIDs[21] = 1057781
	eIDs[22] = 1057783
	eIDs[23] = 1057782
	eIDs[24] = 1057780
	eIDs[25] = 1052664
	eIDs[26] = 1057279
	filterQ := elastic.NewBoolQuery()
	filterQ.Must(elastic.NewTermsQuery("id", eIDs...))*/
	boolQ.Must(elastic.NewMatchQuery("small_shop_id_string", strconv.Itoa(5)).Analyzer("whitespace"))
	//boolQ.Filter(filterQ)
	//res, err := es.Search("product" + common_data.GetOldProductIndex()).Size(100).From(0).Query(boolQ).Do(context.Background())
	res, err := es.Search("product" + common_data.GetOldProductIndex()).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	if res.Hits.TotalHits.Value == 0 {
		//log.Log().Error("小商店暂无商品")
		err = nil
		return err, list, total
	}
	for _, hit := range res.Hits.Hits {
		var t MyProductListTest
		err = json.Unmarshal(hit.Source, &t)
		if err != nil {
			//log.Log().Error("es转结构失败")
			err = nil
			return err, list, total
		}
		list = append(list, t)
	}
	return
}

type VideoShopProduct struct {
	ID          uint `json:"id" form:"id" gorm:"primarykey"`
	SmallShopID uint `json:"small_shop_id" form:"small_shop_id" gorm:"column:small_shop_id;comment:小商店id;index;"`
	ProductID   uint `json:"product_id" form:"product_id" gorm:"column:product_id;comment:中台商品id;index;"`
}

func GetMyProductList(info request.ProductStorageSearch, userID uint) (err error, list []MyProductList, total int64) {
	// 查询会员基础设置
	err, userSetting := level.GetUserSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	// 查询会员
	var user UserModel
	err = source.DB().Model(&UserModel{}).Where("id = ?", userID).First(&user).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	var smallShop SmallShop
	err, smallShop = FindSmallShop(userID)
	if err != nil {
		return
	}
	db := source.DB().Model(&SmallShopProductSale{}).Preload("Product").Where("small_shop_id = ?", smallShop.ID)
	var productIDs []uint
	if info.Title != "" || info.Category1ID != 0 || info.Category2ID != 0 || info.Category3ID != 0 || info.IsRecommend != 0 || info.IsNew != 0 || info.IsHot != nil || info.IsPromotion != 0 || info.IsVideoShop == 1 || info.IsVideoShop == -1 {
		// 创建db
		es, err := source.ES()
		if err != nil {
			return err, list, total
		}
		boolQ := elastic.NewBoolQuery()
		if info.Title != "" {
			boolQ.Must(elastic.NewMatchPhraseQuery("search_title", info.Title).Slop(2))
		}
		filterQ := elastic.NewBoolQuery()
		if info.Category1ID != 0 {
			filterQ.Must(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		}
		if info.Category2ID != 0 {
			filterQ.Must(elastic.NewMatchQuery("category_2_id", info.Category2ID))
		}
		if info.Category3ID != 0 {
			filterQ.Must(elastic.NewMatchQuery("category_3_id", info.Category3ID))
		}
		if info.IsRecommend != 0 {
			filterQ.Must(elastic.NewMatchQuery("is_recommend", info.IsRecommend))
		}
		if info.IsNew != 0 {
			filterQ.Must(elastic.NewMatchQuery("is_new", info.IsNew))
		}
		if info.IsHot != nil {
			filterQ.Must(elastic.NewMatchQuery("is_hot", info.IsHot))
		}
		if info.IsPromotion != 0 {
			filterQ.Must(elastic.NewMatchQuery("is_promotion", info.IsPromotion))
		}
		if info.IsVideoShop == 1 {
			filterQ.Must(elastic.NewMatchQuery("is_video_shop", 1))
		}
		if info.IsVideoShop == -1 {
			filterQ.Must(elastic.NewMatchQuery("is_video_shop", 0))
		}
		// is_video_shop
		boolQ.Must(elastic.NewMatchQuery("small_shop_id_string", strconv.Itoa(int(smallShop.ID))).Analyzer("whitespace"))
		boolQ.Filter(filterQ)
		res, err := es.Search("product" + common_data.GetOldProductIndex()).Size(10000).From(0).Query(boolQ).Do(context.Background())
		if err != nil {
			return err, list, total
		}
		if res.Hits.TotalHits.Value == 0 {
			return err, list, total
		}

		for _, hit := range res.Hits.Hits {
			var t MyProductList
			err = json.Unmarshal(hit.Source, &t)
			if err != nil {
				return err, list, total
			}
			productIDs = append(productIDs, t.ID)
		}
		if len(productIDs) > 0 {
			db.Where("product_id in ?", productIDs)
		}
	}
	// 已推送
	if info.IsVideoShop == 2 {
		var vproductIDs []uint
		err = source.DB().Model(&VideoShopProduct{}).Where("small_shop_id = ?", smallShop.ID).Pluck("product_id", &vproductIDs).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return err, list, total
		}
		if len(vproductIDs) > 0 {
			for _, d := range vproductIDs {
				productIDs = append(productIDs, d)
			}
			db.Where("product_id in ?", productIDs)
		}
	}

	limit := info.PageSize
	if limit > 100 {
		limit = 100
	}
	if info.Page > 100 {
		info.Page = 100
	}
	offset := info.PageSize * (info.Page - 1)
	sql := "price desc"
	// 2价格降3价格升4销量降5销量升8利润降9利润升
	if info.SortBy != 0 {
		switch info.SortBy {
		case 2:
			sql = "price desc"
		case 3:
			sql = "price asc"
		case 4:
			sql = "sales desc"
		case 5:
			sql = "sales asc"
		case 8:
			sql = "profit desc"
		case 9:
			sql = "profit asc"
		case 10:
			sql = "profit_rate desc"
		case 11:
			sql = "profit_rate asc"
		}
	}
	// min_price - max_price
	if info.MinPrice > 0 {
		db.Where("price >= ?", info.MinPrice)
	}
	if info.MaxPrice > 0 {
		db.Where("price <= ?", info.MaxPrice)
	}
	// profit_form - profit_to
	if info.ProfitForm > 0 {
		db.Where("profit_discount >= ?", info.ProfitForm)
	}
	if info.ProfitTo > 0 {
		db.Where("profit_discount <= ?", info.ProfitTo)
	}
	db.Order(sql)
	var productSales []SmallShopProductSale
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	err = db.Limit(limit).Offset(offset).Find(&productSales).Error
	if err != nil {
		return
	}
	// 查询技术服务费比例
	var percent, discountPercent int
	err, percent = GetTechnicalServicePercent(user)
	if err != nil {
		percent = 0
	}
	// 查询会员等级折扣比例
	err, discountPercent = GetLevelDiscountPercent(user)
	// 利润和利润率每页实时计算
	for _, item := range productSales {
		var agreementPrice uint
		agreementPrice = 0
		if item.Product.MinPrice > 0 {
			agreementPrice = item.Product.MinPrice
		} else {
			agreementPrice = item.Product.Price
		}
		// 计算基数
		var basePrice uint
		if item.Product.UserPriceSwitch == 1 {
			var calculateRes bool
			basePrice, _, calculateRes = item.Product.UserPrice.GetProductLevelDiscountPrice(agreementPrice, user.LevelID)
			// 商品没有设置该等级，使用默认折扣
			if calculateRes == false {
				if userSetting.Value.DiscountType == 0 {
					basePrice = agreementPrice * uint(discountPercent) / 10000
				} else {
					basePrice = item.Product.CostPrice * uint(discountPercent) / 10000
				}
			}
		} else {
			if userSetting.Value.DiscountType == 0 {
				basePrice = agreementPrice * uint(discountPercent) / 10000
			} else {
				basePrice = item.Product.CostPrice * uint(discountPercent) / 10000
			}
		}
		// 小商店利润
		var profit int
		// 小商店利润率
		var profitRate float64
		// 技术服务费
		var fee uint
		if percent == 0 {
			fee = 0
		} else {
			fee = (basePrice * uint(percent)) / 10000
		}
		// 店主采购价 = 中台售价 + 技术服务费
		shopPrice := basePrice + fee

		switch item.PriceType {
		case model.ORIGIN:
			if item.Product.OriginPrice != 0 {
				item.Price = item.Product.OriginPrice * item.PriceProportion / 10000
			}
		case model.GUIDE:
			if item.Product.GuidePrice != 0 {
				item.Price = item.Product.GuidePrice * item.PriceProportion / 10000
			}
		case model.PRICE:
			if agreementPrice > 0 {
				item.Price = agreementPrice * item.PriceProportion / 10000
			}
		case model.ACTIVITY:
			if item.Product.ActivityPrice != 0 {
				item.Price = item.Product.ActivityPrice * item.PriceProportion / 10000
			}
		}

		// 计算利润
		if item.Price < shopPrice {
			profit = 0
		} else {
			// 店主利润 = 小商店售价 - （中台售价+技术服务费）
			profit = int(item.Price - shopPrice)
			if profit < 0 {
				profit = 0
			}
		}
		if profit == 0 || item.Price == 0 {
			profitRate = 0
		} else {
			// 小商店利润率
			profitRate = Decimal(float64(profit) / float64(item.Price))
		}

		row := MyProductList{}
		row.ID = item.ProductID
		row.ImageUrl = item.Product.ImageUrl
		row.Title = item.Product.Title
		row.Sales = int(item.Product.Sales)
		row.ShopPrice = item.Price
		row.Profit = profit
		row.ProfitRate = profitRate
		row.TechnicalServicesFee = (basePrice * uint(percent)) / 10000
		row.AgreementPrice = item.Product.Price
		row.GuidePrice = item.Product.GuidePrice
		row.ActivityPrice = item.Product.ActivityPrice
		row.OriginPrice = item.Product.OriginPrice
		row.LevelDiscountPercent = discountPercent
		row.SmallShopProductSale.PriceType = item.PriceType
		row.SmallShopProductSale.PriceProportion = item.PriceProportion

		list = append(list, row)
	}
	return err, list, total
}

// 素材
type Material struct {
	source.Model
	Sort      uint   `json:"sort"`
	Title     string `json:"title"`
	Content   string `json:"content"`
	ImgUrl    string `json:"img_url" gorm:"type:text"`
	VideoUrl  string `json:"video_url"`
	IsDisplay *uint  `json:"is_display" gorm:"default:0"`
	ProductID uint   `json:"product_id"`
	GroupID   uint   `json:"group_id"`
}
type MaterialGroup struct {
	source.Model
	Title string `json:"title"`
	Type  int    `json:"type"`
	Auth  string `json:"auth"`
	Num   int64  `json:"num"`
}

// 短视频
type ShortVideo struct {
	source.Model
	Sort          uint   `json:"sort"`
	Title         string `json:"title"`
	CoverUrl      string `json:"cover_url"`
	VideoUrl      string `json:"video_url"`
	IsDisplay     *uint  `json:"is_display" gorm:"default:0"`
	IsRecommend   *uint  `json:"is_recommend" gorm:"default:0"`   //是否推荐1是0否
	LikeNum       uint   `json:"like_num" gorm:"default:0"`       //喜欢数量
	ForwardingNum uint   `json:"forwarding_num" gorm:"default:0"` //转发数量
	ProductID     uint   `json:"product_id"`
	GroupID       uint   `json:"group_id"`
}
type VideoGroup struct {
	source.Model
	Title   string `json:"title"`
	Type    int    `json:"type"`
	Auth    string `json:"auth"`
	AuthStr string `json:"AuthStr"`
	Num     int64  `json:"num"`
}

func GetProductList(info request.ProductStorageSearch, userID uint) (err error, list []ProductElasticSearchCopy, total int64) {
	var setting model.Setting
	err, setting = GetSetting()
	if err != nil {
		return
	}
	if info.MaxPrice != 0 && info.MaxPrice <= info.MinPrice {
		err = errors.New("最大金额不能小于最小金额")
		return
	}
	var profitString = "profit"         //利润筛选字段
	var priceString = "agreement_price" //批发价筛选字段
	if info.PageSize == 0 {
		info.PageSize = 10
	}
	if info.Page == 0 {
		info.Page = 1
	}
	if info.Page > 100 {
		info.Page = 100
	}
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	var smallShop SmallShop
	err, smallShop = FindSmallShop(userID)
	if err != nil {
		return
	}
	// 创建db
	es, err := source.ES()
	if err != nil {
		return
	}
	db := source.DB()
	//db = db.Where("`is_display` = ?", 1)
	db = db.Where("deleted_at is NULL")
	db = db.Where("freeze = 0")
	// 如果有条件搜索 下方会自动创建搜索语句
	boolQ := elastic.NewBoolQuery()
	if info.Title != "" {
		boolQ.Must(elastic.NewMatchPhraseQuery("search_title", info.Title).Slop(2))
	}
	filterQ := elastic.NewBoolQuery()

	switch info.MaterialType {
	case 1: //短视频
		var videoGroupIds []uint
		//查询无限制的分组
		source.DB().Model(&VideoGroup{}).Where("type = 4").Pluck("id", &videoGroupIds)
		//没有无限制直接返回
		if len(videoGroupIds) == 0 {
			return
		}
		source.DB().Model(&ShortVideo{}).Joins("left join products on products.id = short_videos.product_id").Where("short_videos.group_id in ?", videoGroupIds).Where("short_videos.is_display = 1").Where("products.is_display = 1").Pluck("product_id", &info.ProductIds)
		//没有直播商品直接返回
		if len(info.ProductIds) == 0 {
			return
		}
		break
	case 2: //素材分发
		var materialGroupIds []uint
		//查询无限制的分组
		source.DB().Model(&MaterialGroup{}).Where("type = 4").Pluck("id", &materialGroupIds)
		//没有无限制直接返回
		if len(materialGroupIds) == 0 {
			return
		}
		source.DB().Model(&Material{}).Joins("left join products on products.id = materials.product_id").Where("materials.group_id in ?", materialGroupIds).Where("products.is_display = 1").Pluck("product_id", &info.ProductIds)
		//没有直播商品直接返回
		if len(info.ProductIds) == 0 {
			return
		}
		break
	case 3: //直播
		var shareLiveRoomId []uint
		//获取待直播或者直播中 或者 有回放并且开启回放的
		source.DB().Model(&shareLiveModel.ShareLiveRoom{}).Where("status = 1 or status = 0 or (is_transcribe = 1 and  is_playback = 1)").Pluck("id", &shareLiveRoomId)
		if len(shareLiveRoomId) == 0 {
			return
		}
		source.DB().Model(&shareLiveModel.ShareLiveRoomProduct{}).Joins("left join products on products.id = share_live_room_products.product_id").Where("share_live_room_id in ?", shareLiveRoomId).Where("products.is_display = 1").Pluck("product_id", &info.ProductIds)
		//没有直播商品直接返回
		if len(info.ProductIds) == 0 {
			return
		}
		break
	}
	if setting.Values.PickProductLimit == 1 {
		var pids []uint
		pids, err = GetProductPondProductIDs()
		if err != nil {
			return
		}
		if len(pids) > 0 {
			rpids := make([]interface{}, len(pids))
			for index, value := range pids {
				rpids[index] = value
			}
			filterQ.Must(elastic.NewTermsQuery("id", rpids...))
		}
	}
	//如果指定商品 就不排除已导入的商品
	if len(info.ProductIds) > 0 {
		ProductIds := make([]interface{}, len(info.ProductIds))
		for index, value := range info.ProductIds {
			ProductIds[index] = value
		}
		filterQ.Must(elastic.NewTermsQuery("id", ProductIds...))
	} else {
		//排除已导入的商品
		var existProductIds []uint
		err = source.DB().Model(&model.SmallShopProductSale{}).Where("small_shop_id = ?", smallShop.ID).Pluck("product_id", &existProductIds).Error
		if err != nil {
			return
		}
		eIDs := make([]interface{}, len(existProductIds))
		for index, value := range existProductIds {
			eIDs[index] = value
		}

		filterQ.MustNot(elastic.NewTermsQuery("id", eIDs...))
	}
	// 排除商品来源:周边游、会员权益、数字权益、电影票、蛋糕叔叔的商品
	sources := make([]interface{}, 2)
	// 周边游
	sources[0] = 109
	// 蛋糕叔叔
	sources[1] = 110
	filterQ.MustNot(elastic.NewTermsQuery("source", sources...))
	filterQ.Must(elastic.NewMatchQuery("is_plugin", 0))
	if info.IsDisplay != nil {
		filterQ.Must(elastic.NewMatchQuery("is_display", &info.IsDisplay))
		db = db.Where("`is_display` = ?", &info.IsDisplay)
	} else {
		filterQ.Must(elastic.NewMatchQuery("is_display", 1))
		db = db.Where("`is_display` = ?", 1)
	}

	if info.SupplierID != 0 {
		//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		filterQ.Must(elastic.NewMatchQuery("supplier_id", info.SupplierID))
	}
	if info.Category1ID != 0 {
		//es = es.Query(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		filterQ.Must(elastic.NewMatchQuery("category_1_id", info.Category1ID))

	}
	if info.Category2ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_2_id", info.Category2ID))
	}
	if info.Category3ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_3_id", info.Category3ID))
	}

	if info.IsRecommend != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_recommend", info.IsRecommend))
	}
	if info.IsNew != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_new", info.IsNew))
	}
	if info.IsHot != nil {
		filterQ.Must(elastic.NewMatchQuery("is_hot", info.IsHot))
	}
	if info.IsPromotion != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_promotion", info.IsPromotion))
	}
	if info.MinPrice != 0 {
		filterQ.Must(elastic.NewRangeQuery("agreement_price").Gte(info.MinPrice))
	}
	if info.MaxPrice != 0 {
		filterQ.Must(elastic.NewRangeQuery("agreement_price").Lte(info.MaxPrice))
	}

	if info.PriceForm != 0 {
		filterQ.Must(elastic.NewRangeQuery("" + priceString + "").Gte(info.PriceForm))
	}
	if info.PriceTo != 0 {
		filterQ.Must(elastic.NewRangeQuery("" + priceString + "").Lte(info.PriceTo))
	}

	if info.ProfitForm != 0 {
		filterQ.Must(elastic.NewRangeQuery("" + profitString + "").Gte(info.ProfitForm))
	}
	if info.ProfitTo != 0 {
		filterQ.Must(elastic.NewRangeQuery("" + profitString + "").Lte(info.ProfitTo))
	}

	if info.OriginRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery("min_origin_rate").Gte(Decimal(float64(*info.OriginRate.From))))
	}
	if info.OriginRate.To != nil && *info.OriginRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("max_origin_rate").Lte(Decimal(float64(*info.OriginRate.To))))
	}

	// 指定供应链
	if info.GatherSupplyID != nil {
		filterQ.Must(elastic.NewMatchQuery("gather_supplier_id", info.GatherSupplyID))
	} else {
		var specialSupplyIDs []uint
		err, specialSupplyIDs = getSupplyIDs()
		if len(specialSupplyIDs) > 0 {
			supplyIDs := make([]interface{}, len(specialSupplyIDs))
			for index, value := range specialSupplyIDs {
				supplyIDs[index] = value
			}
			filterQ.MustNot(elastic.NewTermsQuery("gather_supplier_id", supplyIDs...))
		}
	}

	boolQ.Filter(filterQ)
	sort := "sort"
	asc := false
	//调整switch与备注一致 增加一些排序兼容H5使用
	// 1综合降2价格降3价格升4销量降5销量升6创建时间降7创建时间升8利润降9利润升
	if info.SortBy != 0 {
		switch info.SortBy {
		case 1:
			//sort = "id"
			//asc = false
			//db = db.Order("id desc")
		case 2:
			sort = "agreement_price"
			asc = true
		case 3:
			sort = "agreement_price"
			asc = false
		case 4:
			sort = "sales"
			asc = false
		case 5:
			sort = "sales"
			asc = true
		case 6:
			sort = "created_at"
			asc = false
		case 7:
			sort = "created_at"
			asc = true
		case 8:
			sort = profitString
			asc = false
		case 9:
			sort = profitString
			asc = true
			break
		case 10:
			sort = "origin_rate"
			asc = false
			break
		case 11:
			sort = "origin_rate"
			asc = true
			break
		}
	}
	total, err = es.Count("product" + common_data.GetOldProductIndex()).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	if total > int64(info.PageSize*100) {
		total = int64(info.PageSize * 100)
	}
	res, err := es.Search("product"+common_data.GetOldProductIndex()).Size(limit).From(offset).Sort(sort, asc).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	// 查询会员
	var user UserModel
	err = source.DB().Model(&UserModel{}).Where("id = ?", userID).First(&user).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}

	//获取es搜索结果
	list, err = GetSearchResult(res, user, smallShop.ID)
	if err != nil {
		return
	}
	return err, list, total
}

func getSupplyIDs() (err error, supplyIDs []uint) {
	var categoryIDs []uint
	categoryIDs = append(categoryIDs, 98)
	categoryIDs = append(categoryIDs, 111)
	err = source.DB().Model(&GatherSupply{}).Unscoped().Where("category_id in ?", categoryIDs).Pluck("id", &supplyIDs).Error
	return
}

type MiniProductList struct {
	ID          uint   `json:"id"`
	Title       string `json:"title"`
	ImageUrl    string `json:"image_url"`
	Sales       int    `json:"sales"`
	ShopPrice   uint   `json:"shop_price"`
	MarketPrice uint   `json:"market_price"`
	OriginPrice uint   `json:"origin_price"`
	PluginID    int    `json:"plugin_id" gorm:"column:plugin_id;default:0;"` //商品表的插件id用于区别是不是课程商品 课程：18

}

func GetSmallShopProductList(info request.ProductStorageSearch) (err error, list []MiniProductList, total int64) {
	// 创建db
	es, err := source.ES()
	boolQ := elastic.NewBoolQuery()
	if info.Title != "" {
		boolQ.Must(elastic.NewMatchPhraseQuery("search_title", info.Title).Slop(2))
	}
	filterQ := elastic.NewBoolQuery()
	filterQ.Must(elastic.NewMatchQuery("is_display", 1))
	if info.Category1ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_1_id", info.Category1ID))
	}
	if info.Category2ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_2_id", info.Category2ID))
	}
	if info.Category3ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_3_id", info.Category3ID))
	}
	if info.IsRecommend != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_recommend", info.IsRecommend))
	}
	if info.IsNew != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_new", info.IsNew))
	}
	if info.IsHot != nil {
		filterQ.Must(elastic.NewMatchQuery("is_hot", info.IsHot))
	}
	if info.IsPromotion != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_promotion", info.IsPromotion))
	}
	limit := info.PageSize
	if limit > 100 {
		limit = 100
	}
	if info.Page > 100 {
		info.Page = 100
	}
	offset := info.PageSize * (info.Page - 1)
	if info.SortBy != 0 {
		if info.SortBy == 4 || info.SortBy == 5 || info.SortBy == 6 || info.SortBy == 1 {
			sort := "sales"
			asc := false
			if info.SortBy == 5 {
				asc = true
			} else if info.SortBy == 6 {
				sort = "created_at"
				asc = false
			} else if info.SortBy == 1 {
				sort = "sort"
				asc = true
			}
			boolQ.Filter(filterQ)
			boolQ.Must(elastic.NewMatchQuery("small_shop_id_string", strconv.Itoa(int(info.SID))).Analyzer("whitespace"))
			total, err = es.Count("product" + common_data.GetOldProductIndex()).Query(boolQ).Do(context.Background())
			if err != nil {
				return err, list, total
			}
			if total > int64(info.PageSize*100) {
				total = int64(info.PageSize * 100)
			}
			res, err := es.Search("product"+common_data.GetOldProductIndex()).Size(limit).From(offset).Sort(sort, asc).Query(boolQ).Do(context.Background())
			if err != nil {
				return err, list, total
			}
			if res.Hits.TotalHits.Value == 0 {
				err = errors.New("小商店暂无商品")
				return err, list, total
			}
			var productIDs []uint
			for _, hit := range res.Hits.Hits {
				var t MiniProductList
				err = json.Unmarshal(hit.Source, &t)
				if err != nil {
					return err, list, total
				}
				productIDs = append(productIDs, t.ID)
			}
			var smallShopProductSales []SmallShopProductSale
			err = source.DB().Model(&SmallShopProductSale{}).Preload("Product").Where("small_shop_id = ? and product_id in ?", info.SID, productIDs).Find(&smallShopProductSales).Error
			if err != nil {
				return err, list, total
			}
			var smallShopProductSalesMap = make(map[uint]SmallShopProductSale)
			for _, smallShopProductSale := range smallShopProductSales {
				smallShopProductSalesMap[smallShopProductSale.ProductID] = smallShopProductSale
			}
			for _, hit := range res.Hits.Hits {
				var t MiniProductList
				err = json.Unmarshal(hit.Source, &t)
				if err != nil {
					return err, list, total
				}
				if _, existed := smallShopProductSalesMap[t.ID]; existed {
					t.ShopPrice = smallShopProductSalesMap[t.ID].Price
					t.OriginPrice = smallShopProductSalesMap[t.ID].Product.OriginPrice
				}
				list = append(list, t)
			}
			return err, list, total
		}
	}

	boolQ.Filter(filterQ)
	boolQ.Must(elastic.NewMatchQuery("small_shop_id_string", strconv.Itoa(int(info.SID))).Analyzer("whitespace"))
	res, err := es.Search("product" + common_data.GetOldProductIndex()).Size(10000).From(0).Query(boolQ).Do(context.Background())
	if err != nil {
		return err, list, total
	}
	if res.Hits.TotalHits.Value == 0 {
		err = errors.New("小商店暂无商品")
		return err, list, total
	}
	var productIDs []uint
	for _, hit := range res.Hits.Hits {
		var t MiniProductList
		err = json.Unmarshal(hit.Source, &t)
		if err != nil {
			return err, list, total
		}
		productIDs = append(productIDs, t.ID)
	}

	db := source.DB().Model(&SmallShopProductSale{}).Preload("Product").Where("small_shop_id = ? and product_id in ?", info.SID, productIDs)
	sql := "price desc"
	// 1综合降2价格降3价格升4销量降5销量升
	if info.SortBy != 0 {
		switch info.SortBy {
		case 2:
			sql = "price desc"
		case 3:
			sql = "price asc"
			//case 4:
			//	sql = "sales desc"
			//case 5:
			//	sql = "sales asc"
		}
	}
	var productSales []SmallShopProductSale
	err = db.Count(&total).Error
	if err != nil {
		return err, list, total
	}
	err = db.Order(sql).Order("id desc").Limit(limit).Offset(offset).Find(&productSales).Error
	if err != nil {
		return err, list, total
	}
	for _, item := range productSales {
		row := MiniProductList{}
		row.ID = item.ProductID
		row.ImageUrl = item.Product.ImageUrl
		row.Title = item.Product.Title
		row.Sales = int(item.Product.Sales)
		row.ShopPrice = item.Price
		row.PluginID = item.PluginID //课程18
		row.OriginPrice = item.Product.OriginPrice
		list = append(list, row)
	}
	return err, list, total
}

func GetSearchResult(searchResult *elastic.SearchResult, user UserModel, sid uint) (productSearch []ProductElasticSearchCopy, err error) {
	if searchResult.Hits.TotalHits.Value <= 0 {
		return productSearch, err
	}
	// 查询会员基础设置
	err, userSetting := level.GetUserSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return productSearch, err
	}
	// 查询技术服务费比例
	var percent, discountPercent int
	err, percent = GetTechnicalServicePercent(user)
	if err != nil {
		percent = 0
	}
	// 查询会员等级折扣比例
	err, discountPercent = GetLevelDiscountPercent(user)
	// 获取商品ids
	var productIDs []uint
	for _, hit := range searchResult.Hits.Hits {
		var t ProductElasticSearchCopy
		err = json.Unmarshal(hit.Source, &t)
		if err != nil {
			return productSearch, err
		}
		productIDs = append(productIDs, t.ID)
	}
	// 查询商品信息
	var products []model.Product
	err = source.DB().Model(&model.Product{}).Preload("Skus").Where("id in ?", productIDs).Find(&products).Error
	if err != nil {
		return productSearch, err
	}
	// 转成map
	var productMap = make(map[uint]model.Product)
	for _, product := range products {
		productMap[product.ID] = product
	}
	// 查询已加入小商店的商品
	var existProductSales []model.SmallShopProductSale
	err = source.DB().Model(&model.SmallShopProductSale{}).Where("small_shop_id = ? and product_id in ?", sid, productIDs).Find(&existProductSales).Error
	if err != nil {
		return productSearch, err
	}
	// 转成map
	var existProductSalesMap = make(map[uint]uint)
	for _, sale := range existProductSales {
		existProductSalesMap[sale.ProductID] = sale.ProductID
	}
	var discount, isDefault int
	err, discount, isDefault = level.GetLevelDiscountPercent(user.LevelID)
	if err != nil {
		return
	}
	for _, hit := range searchResult.Hits.Hits {
		var t ProductElasticSearchCopy
		err = json.Unmarshal(hit.Source, &t)
		if err != nil {
			return productSearch, err
		}
		if _, ok := existProductSalesMap[t.ID]; ok {
			t.IsChoose = 1
		}
		// 计算基数
		var basePrice uint
		if p, ok := productMap[t.ID]; ok {
			if p.UserPriceSwitch == 1 {
				var calculateRes bool
				basePrice, _, calculateRes = p.UserPrice.GetProductLevelDiscountPrice(p.Price, user.LevelID)
				// 商品没有设置该等级，使用默认折扣
				if calculateRes == false {
					if userSetting.Value.DiscountType == 0 {
						basePrice = t.AgreementPrice * uint(discountPercent) / 10000
					} else {
						basePrice = t.CostPrice * uint(discountPercent) / 10000
					}
					if isDefault == 0 {
						err, p.Price = level.GetLevelDiscountAmount(p.Price, p.CostPrice, discount)
						if err != nil {
							return productSearch, err
						}
					}
				}
			} else {
				if isDefault == 0 {
					err, p.Price = level.GetLevelDiscountAmount(p.Price, p.CostPrice, discount)
					if err != nil {
						return
					}
				}
			}

			var maxOriginPrice, minOriginPrice, maxGuidePrice, maxActivityPrice, minPrice, maxPrice, minDiscount, maxDiscount, minDiscountRatio, maxDiscountRatio uint
			for itemKey := range p.Skus {
				if p.UserPriceSwitch == 1 {
					var calculateRes bool
					p.Skus[itemKey].Price, _, calculateRes = p.UserPrice.GetProductLevelDiscountPrice(p.Skus[itemKey].Price, user.LevelID)
					// 商品没有设置该等级，使用默认折扣
					if calculateRes == false {
						if isDefault == 0 {
							err, p.Skus[itemKey].Price = level.GetLevelDiscountAmount(p.Skus[itemKey].Price, p.Skus[itemKey].CostPrice, discount)
							if err != nil {
								return
							}
						}
					}
				} else {
					if isDefault == 0 {
						err, p.Skus[itemKey].Price = level.GetLevelDiscountAmount(p.Skus[itemKey].Price, p.Skus[itemKey].CostPrice, discount)
						if err != nil {
							return
						}
					}
				}

				if minPrice == 0 || p.Skus[itemKey].Price <= minPrice {
					minPrice = p.Skus[itemKey].Price
				}
				if p.Skus[itemKey].Price > maxPrice {
					maxPrice = p.Skus[itemKey].Price
				}
				if p.Skus[itemKey].OriginPrice > maxOriginPrice {
					maxOriginPrice = p.Skus[itemKey].OriginPrice
				}
				if p.Skus[itemKey].GuidePrice > maxGuidePrice {
					maxGuidePrice = p.Skus[itemKey].GuidePrice
				}
				if p.Skus[itemKey].ActivityPrice > maxActivityPrice {
					maxActivityPrice = p.Skus[itemKey].ActivityPrice
				}
				if minOriginPrice == 0 || p.Skus[itemKey].OriginPrice <= minOriginPrice {
					minOriginPrice = p.Skus[itemKey].OriginPrice
				}

				// 利润 = (市场价 - 批发价)
				// 利润率 = 利润 / 批发价
				profit := p.Skus[itemKey].OriginPrice - p.Skus[itemKey].Price
				if profit < 0 {
					profit = 0
				}
				if t.MinProfit == 0 || profit <= t.MinProfit {
					t.MinProfit = profit
				}
				if profit > t.MaxProfit {
					t.MaxProfit = profit
				}
				// 利润率 2000分 / 10000分 = 0.2 * 1000 = 2000 前端 / 100 = 20% = 0.2 * 100 = 20%
				var profitRate float64
				if profit > 0 && p.Skus[itemKey].Price > 0 {
					profitRate = Decimal((float64(profit) / float64(p.Skus[itemKey].Price)) * 100)

				}

				// 毛利率 = 利润 / 市场价
				// 毛利率 2000分 / 10000分 = 0.2 * 1000 = 2000 前端 / 100 = 20% = 0.2 * 100 = 20%
				var grossProfitRate float64
				if profit > 0 && p.Skus[itemKey].OriginPrice > 0 {
					grossProfitRate = Decimal((float64(profit) / float64(p.Skus[itemKey].OriginPrice)) * 100)
				}

				// 最大利润和最小利润
				if t.MinProfitRate == 0 || profitRate <= t.MinProfitRate {
					t.MinProfitRate = profitRate
				}
				if profitRate > t.MaxProfitRate {
					t.MaxProfitRate = profitRate
				}
				// 毛利率
				if t.MinGrossProfitRate == 0 || grossProfitRate <= t.MinGrossProfitRate {
					t.MinGrossProfitRate = grossProfitRate
				}
				if grossProfitRate > t.MaxGrossProfitRate {
					t.MaxGrossProfitRate = grossProfitRate
				}

				// 计算折扣
				var discountRow, discountRowRatio uint
				if p.Skus[itemKey].Price > 0 && p.Skus[itemKey].OriginPrice > 0 {
					discountRow = uint(Decimal((float64(p.Skus[itemKey].Price) / float64(p.Skus[itemKey].OriginPrice)) * 1000))
					discountRowRatio = uint(Decimal((float64(p.Skus[itemKey].Price) / float64(p.Skus[itemKey].OriginPrice)) * 10000))
				}
				// 最大折扣和最小折扣
				if minDiscount == 0 || discountRow <= minDiscount {
					minDiscount = discountRow
				}
				if discountRow > maxDiscount {
					maxDiscount = discountRow
				}
				if minDiscountRatio == 0 || discountRowRatio <= minDiscountRatio {
					minDiscountRatio = discountRowRatio
				}
				if discountRowRatio > maxDiscountRatio {
					maxDiscountRatio = discountRowRatio
				}
			}
			// 建议零售价
			t.MaxOriginPrice = maxOriginPrice
			t.MinOriginPrice = minOriginPrice
			t.MaxGuidePrice = maxGuidePrice
			t.MaxActivityPrice = maxActivityPrice
			// 超级批发价
			t.MinPrice = minPrice
			t.MaxPrice = maxPrice
			// 折扣
			t.MinDiscount = minDiscount
			t.MaxDiscount = maxDiscount
			t.MinDiscountRatio = minDiscountRatio
			t.MaxDiscountRatio = maxDiscountRatio

		} else {
			if userSetting.Value.DiscountType == 0 {
				basePrice = t.AgreementPrice * uint(discountPercent) / 10000
			} else {
				basePrice = t.CostPrice * uint(discountPercent) / 10000
			}
		}

		t.OriginPrice = t.MarketPrice
		if basePrice == 0 || percent == 0 {
			t.TechnicalServicesFee = 0
		} else {
			// 技术服务费
			var fee uint
			fee = (basePrice * uint(percent)) / 10000
			t.TechnicalServicesFee = fee
		}
		t.ProfitRate = t.ProfitRate / 100
		productSearch = append(productSearch, t)
	}
	return productSearch, err
}

type ProductElasticSearchCopy struct {
	productService.ProductElasticSearch
	ShopPrice            uint                 `json:"shop_price"`             // 小商店价格
	OriginPrice          uint                 `json:"origin_price"`           // 建议零售价
	TechnicalServicesFee uint                 `json:"technical_services_fee"` // 技术服务费
	IsChoose             int                  `json:"is_choose"`
	SmallShopProductSale SmallShopProductSale `json:"small_shop_product_sale"`
	MaxOriginPrice       uint                 `json:"max_origin_price" gorm:"-"`
	MinOriginPrice       uint                 `json:"min_origin_price" gorm:"-"`
	MaxGuidePrice        uint                 `json:"max_guide_price" gorm:"-"`
	MaxActivityPrice     uint                 `json:"max_activity_price" gorm:"-"`
	MaxPrice             uint                 `json:"max_price"`
	MinPrice             uint                 `json:"min_price"`
	MinDiscount          uint                 `json:"min_discount" gorm:"-"`
	MaxDiscount          uint                 `json:"max_discount" gorm:"-"`
	MinDiscountRatio     uint                 `json:"min_discount_ratio" gorm:"-"`
	MaxDiscountRatio     uint                 `json:"max_discount_ratio" gorm:"-"`
	// 利润
	MinProfit uint `json:"min_profit" gorm:"-"`
	MaxProfit uint `json:"max_profit" gorm:"-"`
	// 利润率
	MinProfitRate float64 `json:"min_profit_rate" gorm:"-"`
	MaxProfitRate float64 `json:"max_profit_rate" gorm:"-"`
	// 毛利率
	MinGrossProfitRate float64 `json:"min_gross_profit_rate" gorm:"-"`
	MaxGrossProfitRate float64 `json:"max_gross_profit_rate" gorm:"-"`
}

type User struct {
	ID        uint      `json:"id" form:"id" gorm:"primarykey"`
	Username  string    `json:"username" form:"username" gorm:"comment:用户登录名"`
	NickName  string    `json:"nickname" form:"nickName" gorm:"column:nick_name;comment:用户昵称;type:varchar(50);size:50;"`
	LevelID   uint      `json:"level_id" form:"level_id"`
	UserLevel UserLevel `json:"user_level" form:"user_level" gorm:"foreignKey:LevelID;references:ID"`
}

type UserLevel struct {
	source.Model
	Level    int `json:"level" form:"level"`
	Discount int `json:"discount" form:"discount"`
}

func DeleteProductSale(smallShopID uint, productIDs []uint) (err error) {
	//清除购物车相关商品
	err = source.DB().Delete(&model.SmallShopShoppingCart{}, "small_shop_id = ? and product_id in ?", smallShopID, productIDs).Error
	if err != nil {
		err = errors.New("清除购物车商品失败" + err.Error())
		return
	}

	err = source.DB().Delete(&SmallShopProductSale{}, "small_shop_id = ? and product_id in ?", smallShopID, productIDs).Error
	if err != nil {
		return
	}
	for _, productID := range productIDs {
		_ = productMq.PublishMessage(productID, productMq.Edit, 0)
	}
	return
}

func GetProductSalesInfo(id uint, levelID uint) (err error, product response.Product) {
	var percent, isDefault int
	err, percent, isDefault = level.GetLevelDiscountPercent(levelID)
	if err != nil {
		return
	}
	err = source.DB().Where("id = ?", id).Where("freeze = 0").Preload("Skus").First(&product).Error
	product.NormalPrice = product.Price
	if product.UserPriceSwitch == 1 {
		var calculateRes bool
		product.Price, _, calculateRes = product.UserPrice.GetProductLevelDiscountPrice(product.Price, levelID)
		// 商品没有设置该等级，使用默认折扣
		if calculateRes == false {
			err, product.Price = level.GetLevelDiscountAmount(product.Price, product.ExecPrice, percent)
			if err != nil {
				return
			}
		}
	} else {
		err, product.Price = level.GetLevelDiscountAmount(product.Price, product.ExecPrice, percent)
		if err != nil {
			return
		}
	}

	var maxOriginPrice, minOriginPrice, maxGuidePrice, maxActivityPrice, minPrice, maxPrice, minNormalPrice, maxNormalPrice uint
	for optionKey, option := range product.SkuSelect.Options {
		for skuKey, sku := range option.Skus {
			if product.UserPriceSwitch == 1 {
				var calculateRes bool
				product.SkuSelect.Options[optionKey].Skus[skuKey].Price, _, calculateRes = product.UserPrice.GetProductLevelDiscountPrice(sku.Price, levelID)
				// 商品没有设置该等级，使用默认折扣
				if calculateRes == false {
					err, product.SkuSelect.Options[optionKey].Skus[skuKey].Price = level.GetLevelDiscountAmount(sku.Price, sku.ExecPrice, percent)
					if err != nil {
						return
					}
				}
			} else {
				err, product.SkuSelect.Options[optionKey].Skus[skuKey].Price = level.GetLevelDiscountAmount(sku.Price, sku.ExecPrice, percent)
				if err != nil {
					return
				}
			}
		}
	}
	for itemKey, sku := range product.Skus {
		product.Skus[itemKey].NormalPrice = sku.Price
		if minNormalPrice == 0 || product.Skus[itemKey].NormalPrice <= minNormalPrice {
			minNormalPrice = product.Skus[itemKey].NormalPrice
		}
		if product.Skus[itemKey].NormalPrice > maxNormalPrice {
			maxNormalPrice = product.Skus[itemKey].NormalPrice
		}
		if product.UserPriceSwitch == 1 {
			var calculateRes bool
			sku.Price, _, calculateRes = product.UserPrice.GetProductLevelDiscountPrice(sku.Price, levelID)
			// 商品没有设置该等级，使用默认折扣
			if calculateRes == false {
				if isDefault == 0 {
					err, sku.Price = level.GetLevelDiscountAmount(sku.Price, sku.ExecPrice, percent)
					if err != nil {
						return
					}
				}
			}
		} else {
			if isDefault == 0 {
				err, sku.Price = level.GetLevelDiscountAmount(sku.Price, sku.ExecPrice, percent)
				if err != nil {
					return
				}
			}
		}
		if minPrice == 0 || sku.Price <= minPrice {
			minPrice = sku.Price
		}
		if sku.Price > maxPrice {
			maxPrice = sku.Price
		}
		if sku.OriginPrice > maxOriginPrice {
			maxOriginPrice = sku.OriginPrice
		}
		if sku.GuidePrice > maxGuidePrice {
			maxGuidePrice = sku.GuidePrice
		}
		if sku.ActivityPrice > maxActivityPrice {
			maxActivityPrice = sku.ActivityPrice
		}
		if minOriginPrice == 0 || sku.OriginPrice <= minOriginPrice {
			minOriginPrice = sku.OriginPrice
		}
	}
	//product.MaxOriginPrice = maxOriginPrice
	//product.MinOriginPrice = minOriginPrice
	//product.MaxGuidePrice = maxGuidePrice
	//product.MaxActivityPrice = maxActivityPrice
	product.MinPrice = minPrice
	product.MaxPrice = maxPrice
	//product.MaxNormalPrice = maxNormalPrice
	//product.MinNormalPrice = minNormalPrice

	return
}

func UpdateSalesByProductID(productID uint) (err error) {
	// 查询中台商品
	var product Product
	err = source.DB().Model(&Product{}).Where("id = ?", productID).First(&product).Error
	if err != nil {
		return
	}
	// 通过product_id修改小商店商品的销量
	err = source.DB().Model(&SmallShopProductSale{}).Where("product_id = ?", productID).Update("sales", product.Sales).Error
	return
}

func ListenerUpdateProduct(productID uint) (err error) {
	// 查询小商店商品
	var smallShopProduct SmallShopProductSale
	err = source.DB().Model(&SmallShopProductSale{}).Where("product_id = ?", productID).First(&smallShopProduct).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	// 查询店主
	var smallShop SmallShop
	err = source.DB().Model(&SmallShop{}).Where("id = ?", smallShopProduct.SmallShopID).First(&smallShop).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if smallShopProduct.ID == 0 || smallShop.ID == 0 {
		err = nil
		return
	}
	// 拼参数 调用ChangeProductPrice函数
	var changePriceReq request.ChangePriceReq
	changePriceReq.ProductID = productID
	changePriceReq.PriceType = smallShopProduct.PriceType
	changePriceReq.PriceProportion = smallShopProduct.PriceProportion
	err = ChangeProductPrice(changePriceReq, smallShop.ID, smallShop.Uid)
	return
}

func ChangeProductPrice(changePriceReq request.ChangePriceReq, sid, uid uint) (err error) {
	// 查询商品
	var product Product
	err = source.DB().Model(&Product{}).Where("id = ?", changePriceReq.ProductID).First(&product).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	// 查询会员
	var user UserModel
	err = source.DB().Model(&UserModel{}).Where("id = ?", uid).First(&user).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	// 查询会员基础设置
	err, userSetting := level.GetUserSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	// 查询会员等级折扣比例
	var discountPercent int
	err, discountPercent = GetLevelDiscountPercent(user)
	// 查询技术服务费比例
	var percent int
	err, percent = GetTechnicalServicePercent(user)
	if err != nil {
		return
	}
	// 技术服务费
	var fee uint
	// 小商店售价
	var price uint
	// 小商店利润
	var profit int
	// 小商店利润率
	var profitRate float64
	// 利润折扣
	var profitDiscount float64
	var agreementPrice uint
	agreementPrice = 0
	if product.MinPrice > 0 {
		agreementPrice = product.MinPrice
	} else {
		agreementPrice = product.Price
	}
	switch changePriceReq.PriceType {
	case model.ORIGIN:
		if product.OriginPrice != 0 {
			price = product.OriginPrice * changePriceReq.PriceProportion / 10000
		}
	case model.GUIDE:
		if product.GuidePrice != 0 {
			price = product.GuidePrice * changePriceReq.PriceProportion / 10000
		}
	case model.PRICE:
		if agreementPrice > 0 {
			price = agreementPrice * changePriceReq.PriceProportion / 10000
		}
	case model.ACTIVITY:
		if product.ActivityPrice != 0 {
			price = product.ActivityPrice * changePriceReq.PriceProportion / 10000
		}
	}

	// 计算基数
	var basePrice uint
	if product.UserPriceSwitch == 1 {
		var calculateRes bool
		basePrice, _, calculateRes = product.UserPrice.GetProductLevelDiscountPrice(product.Price, user.LevelID)
		// 商品没有设置该等级，使用默认折扣
		if calculateRes == false {
			if userSetting.Value.DiscountType == 0 {
				basePrice = agreementPrice * uint(discountPercent) / 10000
			} else {
				basePrice = product.CostPrice * uint(discountPercent) / 10000
			}
		}
	} else {
		if userSetting.Value.DiscountType == 0 {
			basePrice = agreementPrice * uint(discountPercent) / 10000
		} else {
			basePrice = product.CostPrice * uint(discountPercent) / 10000
		}
	}

	if percent == 0 {
		fee = 0
	} else {
		fee = (basePrice * uint(percent)) / 10000
	}
	// 店主采购价 = 中台售价 + 技术服务费
	shopPrice := basePrice + fee
	// 计算利润
	if price < shopPrice {
		profit = 0
	} else {
		// 店主利润 = 小商店售价 - （中台售价+技术服务费）
		profit = int(price - shopPrice)
		if profit < 0 {
			profit = 0
		}
	}
	if profit == 0 || price == 0 {
		profitRate = 0
		profitDiscount = 0
	} else {
		// 小商店利润率
		profitRate = Decimal(float64(profit) / float64(price))
		// 小商店利润折扣
		profitDiscount = Decimal(profitRate * 10)
	}
	err = source.DB().Model(&SmallShopProductSale{}).Where("small_shop_id = ? and product_id = ?", sid, changePriceReq.ProductID).Updates(map[string]interface{}{"price_type": changePriceReq.PriceType, "price_proportion": changePriceReq.PriceProportion, "price": price, "profit": profit, "profit_rate": profitRate, "profit_discount": profitDiscount}).Error
	return
}

func BatchChangePrice(req request.BatchChangePriceReq, sid, uid uint) (err error) {
	var updateSalesMap []map[string]interface{}
	// 查询商品
	var products []Product
	err = source.DB().Model(&Product{}).Where("id in ?", req.ProductIDs).Find(&products).Error
	if err != nil {
		return
	}
	// 查询会员
	var user UserModel
	err = source.DB().Model(&UserModel{}).Where("id = ?", uid).First(&user).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	// 查询会员基础设置
	err, userSetting := level.GetUserSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	// 查询会员等级折扣比例
	var discountPercent int
	err, discountPercent = GetLevelDiscountPercent(user)
	// 查询技术服务费比例
	var percent int
	err, percent = GetTechnicalServicePercent(user)
	if err != nil {
		return
	}
	for _, item := range products {
		var agreementPrice uint
		agreementPrice = 0
		if item.MinPrice > 0 {
			agreementPrice = item.MinPrice
		} else {
			agreementPrice = item.Price
		}
		// 计算基数
		var basePrice uint
		if item.UserPriceSwitch == 1 {
			var calculateRes bool
			basePrice, _, calculateRes = item.UserPrice.GetProductLevelDiscountPrice(item.Price, user.LevelID)
			// 商品没有设置该等级，使用默认折扣
			if calculateRes == false {
				if userSetting.Value.DiscountType == 0 {
					basePrice = agreementPrice * uint(discountPercent) / 10000
				} else {
					basePrice = item.CostPrice * uint(discountPercent) / 10000
				}
			}
		} else {
			if userSetting.Value.DiscountType == 0 {
				basePrice = agreementPrice * uint(discountPercent) / 10000
			} else {
				basePrice = item.CostPrice * uint(discountPercent) / 10000
			}
		}

		var sale SmallShopProductSale
		err = source.DB().Model(&SmallShopProductSale{}).Where("small_shop_id = ? and product_id = ?", sid, item.ID).First(&sale).Error
		if err != nil {
			err = errors.New("查询小商店商品失败")
			return
		}
		// 技术服务费
		var fee uint
		// 小商店售价
		var price uint
		// 小商店利润
		var profit int
		// 小商店利润率
		var profitRate float64
		// 利润折扣
		var profitDiscount float64
		switch req.PriceType {
		case model.ORIGIN:
			if item.OriginPrice != 0 {
				price = item.OriginPrice * req.PriceProportion / 10000
			}
		case model.GUIDE:
			if item.GuidePrice != 0 {
				price = item.GuidePrice * req.PriceProportion / 10000
			}
		case model.PRICE:
			if agreementPrice > 0 {
				price = agreementPrice * req.PriceProportion / 10000
			}
		case model.ACTIVITY:
			if item.ActivityPrice != 0 {
				price = item.ActivityPrice * req.PriceProportion / 10000
			}
		}

		if percent == 0 {
			fee = 0
		} else {
			fee = (basePrice * uint(percent)) / 10000
		}
		// 店主采购价 = 中台售价 + 技术服务费
		shopPrice := basePrice + fee
		// 计算利润
		if price < shopPrice {
			profit = 0
		} else {
			// 店主利润 = 小商店售价 - （中台售价+技术服务费）
			profit = int(price - shopPrice)
			if profit < 0 {
				profit = 0
			}
		}
		if profit == 0 || price == 0 {
			profitRate = 0
			profitDiscount = 0
		} else {
			// 小商店利润率
			profitRate = Decimal(float64(profit) / float64(price))
			// 小商店利润折扣
			profitDiscount = Decimal(profitRate * 10)
		}

		row := make(map[string]interface{})
		row["id"] = sale.ID
		row["price"] = price
		row["price_type"] = req.PriceType
		row["price_proportion"] = req.PriceProportion
		row["profit"] = profit
		row["profit_rate"] = profitRate
		row["profit_discount"] = profitDiscount
		updateSalesMap = append(updateSalesMap, row)
		// 改价消息
		err = mq.PublishMessage(item.ID, sid, mq.Change, 0)
		if err != nil {
			return
		}
	}

	if len(updateSalesMap) > 0 {
		err = source.BatchUpdate(updateSalesMap, "small_shop_product_sales", "id")
		if err != nil {
			return
		}
	}
	return
}

type MD5 struct {
	SID             uint            `json:"sid"`
	ProductIDs      []uint          `json:"product_ids"`
	PriceProportion uint            `json:"price_proportion"`
	PriceType       model.PriceType `json:"price_type"`
}

func LockTest() {
	md5Model := MD5{
		SID:             1,
		ProductIDs:      []uint{1, 2, 3},
		PriceProportion: 10000,
		PriceType:       model.PRICE,
	}
	md5Str, _ := json.Marshal(md5Model)
	hash := md5.New()
	hash.Write(md5Str)
	lockKey := hex.EncodeToString(hash.Sum(nil))
	lockValue := "locked"
	lockExpiration := 100 * time.Second
	var ctx = context.Background()
	// 获取锁
	var redisLocked string
	redisLocked, _ = source.Redis().Get(ctx, lockKey).Result()
	if redisLocked == lockValue {
		fmt.Println("正在处理中，请稍后再试")
		return
	}
	// 设置锁
	_ = source.Redis().Set(ctx, lockKey, lockValue, lockExpiration).Err()
	fmt.Println("处理中")
}

func DelKey(lockKey string) {
	var ctx = context.Background()
	source.Redis().Del(ctx, lockKey)
}

func AddProductSale(smallShopID uint, productIDs []uint, priceProportion uint, priceType model.PriceType) (err error) {
	log.Log().Error("AddProductSale", zap.Any("smallShopID", smallShopID), zap.Any("productIDs", productIDs), zap.Any("priceProportion", priceProportion), zap.Any("priceType", priceType))
	// 增加分布式锁 处理商品重复添加
	md5Model := MD5{
		SID:             smallShopID,
		ProductIDs:      productIDs,
		PriceProportion: priceProportion,
		PriceType:       priceType,
	}
	md5Str, err := json.Marshal(md5Model)
	if err != nil {
		return
	}
	hash := md5.New()
	hash.Write(md5Str)
	lockKey := hex.EncodeToString(hash.Sum(nil))
	lockValue := "locked"
	lockExpiration := 2 * time.Second
	var ctx = context.Background()
	// 获取锁
	var redisLocked string
	redisLocked, err = source.Redis().Get(ctx, lockKey).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		return
	}
	if redisLocked == lockValue {
		err = errors.New("正在处理中，请稍后再试")
		return
	}
	// 设置锁
	err = source.Redis().Set(ctx, lockKey, lockValue, lockExpiration).Err()
	if err != nil {
		return
	}
	// 店主
	var smallShop model.SmallShop
	err, smallShop = GetSmallShopPreloadByID(smallShopID)
	if err != nil {
		return
	}
	// 查询会员
	var user UserModel
	err = source.DB().Model(&UserModel{}).Where("id = ?", smallShop.Uid).First(&user).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	// 查询会员基础设置
	err, userSetting := level.GetUserSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	// 查询会员等级折扣比例
	var discountPercent int
	err, discountPercent = GetLevelDiscountPercent(user)
	// 查询技术服务费比例
	var percent int
	err, percent = GetTechnicalServicePercent(user)
	if err != nil {
		return
	}
	// 查询商品
	var products []Product
	err = source.DB().Model(&Product{}).Where("id in ?", productIDs).Find(&products).Error
	if err != nil {
		return
	}
	// 商品map
	productMap := map[uint]Product{}
	// 分类id []
	var categoryIDs []CategoryID
	var categoryIDsSql []uint
	removeCategoryIDs := make(map[uint]interface{})
	for _, product := range products {
		productMap[product.ID] = product
		if product.Category1ID != 0 {
			if _, ok := removeCategoryIDs[product.Category1ID]; !ok {
				removeCategoryIDs[product.Category1ID] = true
				categoryIDs = append(categoryIDs, CategoryID{ID: product.Category1ID, Level: 1})
				categoryIDsSql = append(categoryIDsSql, product.Category1ID)
			}
		}
		if product.Category2ID != 0 {
			if _, ok := removeCategoryIDs[product.Category2ID]; !ok {
				removeCategoryIDs[product.Category2ID] = true
				categoryIDs = append(categoryIDs, CategoryID{ID: product.Category2ID, Level: 2})
				categoryIDsSql = append(categoryIDsSql, product.Category2ID)
			}
		}
		if product.Category3ID != 0 {
			if _, ok := removeCategoryIDs[product.Category3ID]; !ok {
				removeCategoryIDs[product.Category3ID] = true
				categoryIDs = append(categoryIDs, CategoryID{ID: product.Category3ID, Level: 3})
				categoryIDsSql = append(categoryIDsSql, product.Category3ID)
			}
		}
	}
	// 查询分类是否存在
	var existCategories []model.SmallShopCategory
	existCategoriesMap := map[uint]model.SmallShopCategory{}
	// 小商店分类
	var smallShopCategories []model.SmallShopCategory
	if len(categoryIDs) > 0 {
		err = source.DB().Model(&model.SmallShopCategory{}).Where("category_id in ? and small_shop_id = ?", categoryIDsSql, smallShopID).Find(&existCategories).Error
		if err != nil {
			return
		}
		if len(existCategories) > 0 {
			for _, item := range existCategories {
				existCategoriesMap[item.CategoryID] = item
			}
			for _, categoryID := range categoryIDs {
				if _, ok := existCategoriesMap[categoryID.ID]; !ok {
					smallShopCategories = append(smallShopCategories, model.SmallShopCategory{SmallShopID: smallShopID, CategoryID: categoryID.ID, Level: categoryID.Level})
				}
			}
		} else {
			for _, categoryID := range categoryIDs {
				smallShopCategories = append(smallShopCategories, model.SmallShopCategory{SmallShopID: smallShopID, CategoryID: categoryID.ID, Level: categoryID.Level})
			}
		}
	}

	// 已存在的小商店商品
	var existProductSales []model.SmallShopProductSale
	verifyIDs := make(map[uint]interface{})
	err = source.DB().Model(&model.SmallShopProductSale{}).Where("product_id in ? and small_shop_id = ?", productIDs, smallShopID).Find(&existProductSales).Error
	if err != nil {
		return
	}
	for _, exist := range existProductSales {
		verifyIDs[exist.ProductID] = true
	}
	// 小商店商品
	var productSales []model.SmallShopProductSale
	for _, id := range productIDs {
		if _, ok := verifyIDs[id]; !ok {
			// 技术服务费
			var fee uint
			// 小商店售价
			var price uint
			// 小商店利润
			var profit int
			// 小商店利润率
			var profitRate float64
			// 利润折扣
			var profitDiscount float64
			productOnly, e := productMap[id]
			if !e {
				continue
			}
			var agreementPrice uint
			agreementPrice = 0
			if productOnly.MinPrice > 0 {
				agreementPrice = productOnly.MinPrice
			} else {
				agreementPrice = productOnly.Price
			}
			// 计算基数
			var basePrice uint
			if p, o := productMap[id]; o {
				if p.UserPriceSwitch == 1 {
					var calculateRes bool
					basePrice, _, calculateRes = p.UserPrice.GetProductLevelDiscountPrice(p.Price, user.LevelID)
					// 商品没有设置该等级，使用默认折扣
					if calculateRes == false {
						if userSetting.Value.DiscountType == 0 {
							basePrice = agreementPrice * uint(discountPercent) / 10000
						} else {
							basePrice = p.CostPrice * uint(discountPercent) / 10000
						}
					}
				}
			} else {
				if userSetting.Value.DiscountType == 0 {
					if agreementPrice > 0 {
						basePrice = agreementPrice * uint(discountPercent) / 10000
					}
				} else {
					basePrice = productOnly.CostPrice * uint(discountPercent) / 10000
				}
			}

			switch priceType {
			case model.ORIGIN:
				if productOnly.OriginPrice != 0 {
					price = productOnly.OriginPrice * priceProportion / 10000
				}
			case model.GUIDE:
				if productOnly.GuidePrice != 0 {
					price = productOnly.GuidePrice * priceProportion / 10000
				}
			case model.PRICE:
				if agreementPrice > 0 {
					price = agreementPrice * priceProportion / 10000
				}
			case model.ACTIVITY:
				if productOnly.ActivityPrice != 0 {
					price = productOnly.ActivityPrice * priceProportion / 10000
				}
			}
			if percent == 0 {
				fee = 0
			} else {
				fee = (basePrice * uint(percent)) / 10000
			}
			// 店主采购价 = 中台售价 + 技术服务费
			shopPrice := basePrice + fee
			// 计算利润
			if price < shopPrice {
				profit = 0
			} else {
				// 店主利润 = 小商店售价 - （中台售价+技术服务费）
				profit = int(price - shopPrice)
				if profit < 0 {
					profit = 0
				}
			}
			if profit == 0 || price == 0 {
				profitRate = 0
				profitDiscount = 0
			} else {
				// 小商店利润率
				profitRate = Decimal(float64(profit) / float64(price))
				// 小商店利润折扣
				profitDiscount = Decimal(profitRate * 10)
			}
			productSales = append(productSales, model.SmallShopProductSale{PluginID: productOnly.PluginID, SmallShopID: smallShopID, ProductID: id, PriceProportion: priceProportion, PriceType: priceType, Price: price, Profit: profit, ProfitRate: profitRate, ProfitDiscount: profitDiscount})
		}
	}
	if len(productSales) > 0 {
		err = source.DB().Model(&model.SmallShopProductSale{}).Save(&productSales).Error
		if err != nil {
			return
		}
	}
	if len(smallShopCategories) > 0 {
		err = source.DB().Model(&model.SmallShopCategory{}).Save(&smallShopCategories).Error
		if err != nil {
			return
		}
	}
	if len(productIDs) > 0 {
		err = EsRun(productIDs, smallShopID)
		if err != nil {
			return
		}
	}
	// 删除锁
	err = source.Redis().Del(ctx, lockKey).Err()
	return
}

func EsRun(productIDs []uint, smallShopID uint) (err error) {
	es, err := source.ES()
	boolQ := elastic.NewBoolQuery()
	filterQ := elastic.NewBoolQuery()
	eIDs := make([]interface{}, len(productIDs))
	for index, id := range productIDs {
		eIDs[index] = id
	}
	filterQ.Must(elastic.NewTermsQuery("id", eIDs...))
	boolQ.Filter(filterQ)
	res, err := es.Search("product" + common_data.GetOldProductIndex()).Size(len(productIDs)).From(0).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	var data map[string]interface{}
	for _, hit := range res.Hits.Hits {
		var productElasticSearch productService.ProductElasticSearch
		err = json.Unmarshal(hit.Source, &productElasticSearch)
		if productElasticSearch.SmallShopIdString == "" {
			productElasticSearch.SmallShopIdString = strconv.Itoa(int(smallShopID))
		} else {
			productElasticSearch.SmallShopIdString = productElasticSearch.SmallShopIdString + " " + strconv.Itoa(int(smallShopID))
		}
		var jsonData []byte
		jsonData, err = json.Marshal(productElasticSearch)
		if err != nil {
			return
		}
		err = json.Unmarshal(jsonData, &data)
		if err != nil {
			return
		}
		var updateRes *elastic.UpdateResponse
		updateRes, err = es.Update().
			Index("product" + common_data.GetOldProductIndex()).
			Id(strconv.Itoa(int(productElasticSearch.ID))).
			Doc(data).
			Do(context.Background())
		if err != nil {
			return
		}
		if updateRes.Result != "updated" {
			return
		}
	}

	return
}

type Product struct {
	ID            uint `json:"id" form:"id" gorm:"primarykey"`
	MinPrice      uint `json:"minPrice" form:"minPrice" gorm:"column:min_price;comment:最低价(单位:分);"`
	Price         uint `json:"price" form:"price" gorm:"column:price;comment:供货价(单位:分);"`
	CostPrice     uint `json:"cost_price" form:"cost_price" gorm:"column:cost_price;comment:成本价(单位:分);"`             // 成本价，上游给的协议价
	OriginPrice   uint `json:"origin_price" form:"origin_price" gorm:"column:origin_price;comment:市场价(单位:分);"`       // 市场价 市场价
	GuidePrice    uint `json:"guide_price" form:"guide_price" gorm:"column:guide_price;comment:供货价(单位:分);"`          //指导价
	ActivityPrice uint `json:"activity_price" form:"activity_price" gorm:"column:activity_price;comment:营销价(单位:分);"` // 营销价
	Category1ID   uint `json:"category1_id" form:"category1_id" gorm:"index"`                                              // 一级分类
	Category2ID   uint `json:"category2_id" form:"category2_id" gorm:"index"`                                              // 二级分类
	Category3ID   uint `json:"category3_id" form:"category3_id" gorm:"index"`
	PluginID      int  `json:"plugin_id" gorm:"column:plugin_id;default:0;"`
	Sales         uint `json:"sales" form:"sales" gorm:"column:sales;comment:销量;"` // 销量
	// 会员价独立开关
	UserPriceSwitch int `json:"user_price_switch" gorm:"column:user_price_switch;comment:会员价独立开关;"`
	// 会员价设置
	UserPrice model2.UserPrice `json:"user_price" gorm:"column:user_price;comment:会员价设置;"`
}

type ProductByPoster struct {
	ID            uint   `json:"id" form:"id" gorm:"primarykey"`
	Title         string `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(255);size:255;index;"`
	ImageUrl      string `json:"image_url" gorm:"column:image_url;comment:图片url;"`
	OriginPrice   uint   `json:"origin_price" form:"origin_price" gorm:"column:origin_price;comment:市场价(单位:分);"`       // 市场价 市场价
	GuidePrice    uint   `json:"guide_price" form:"guide_price" gorm:"column:guide_price;comment:供货价(单位:分);"`          //指导价
	Price         uint   `json:"price" form:"price" gorm:"column:price;comment:供货价(单位:分);"`                            // 供货价、给采购端的协议价
	CostPrice     uint   `json:"cost_price" form:"cost_price" gorm:"column:cost_price;comment:成本价(单位:分);"`             // 成本价，上游给的协议价
	ActivityPrice uint   `json:"activity_price" form:"activity_price" gorm:"column:activity_price;comment:营销价(单位:分);"` // 营销价
}

func (ProductByPoster) TableName() string {
	return "products"
}

type CategoryID struct {
	ID    uint `json:"id" form:"id"`
	Level int  `json:"level" form:"level"`
}

func VerifyProductExist(req request.ProductID, uid uint) (err error, exist bool) {
	var smallShop SmallShop
	err, smallShop = FindSmallShop(uid)
	if err != nil {
		return
	}
	var sale SmallShopProductSale
	err = source.DB().Where("small_shop_id = ? and product_id = ?", smallShop.ID, req.ID).First(&sale).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if sale.ID != 0 {
		exist = true
	}
	err = nil
	return err, exist
}
