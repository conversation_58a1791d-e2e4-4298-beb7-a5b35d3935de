package service

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"mime/multipart"
	"os"
	"strconv"
	"yz-go/component/log"
	setting2 "yz-go/setting"

	//"github.com/silenceper/wechat/v2/miniprogram"
	paymentModel "payment/model"
	paymentSetting "payment/setting"

	"small-shop/model"
	"yz-go/config"
	"yz-go/source"
)

func GetProtocolSetting() (err error, setting model.ProtocolSetting) {
	err = source.DB().Where("`key` = ?", "small_shop_protocol_setting").First(&setting).Error
	return
}

func SaveProtocolSetting(setting model.ProtocolSetting) (err error) {
	setting.Key = "small_shop_protocol_setting"
	if setting.ID != 0 {
		err = source.DB().Updates(&setting).Error
	} else {
		err = source.DB().Create(&setting).Error
	}
	return err
}

func GetSetting() (err error, setting model.Setting) {
	err = source.DB().Where("`key` = ?", "small_shop_setting").First(&setting).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	var payTypes model.PayTypes
	//聚合支付 -- 数据通
	communicationPaymentInfoData := paymentModel.GetSmallShopCommunicationPaymentInfoData(paymentModel.SCENE_WECHAT_MINI)

	for _, item := range setting.Values.PayTypes {
		//聚合支付 前后台支付方式名称不一样所以需要单独判断
		if item.Type == "30000" {
			if len(communicationPaymentInfoData) > 0 {
				for _, itemPaymentInfo := range communicationPaymentInfoData {
					if item.ID == itemPaymentInfo.ID+paymentModel.AGGREGATE_PAYMENT {
						item.Name = itemPaymentInfo.PayTypeName
						item.PayType = itemPaymentInfo.PayType
						payTypes = append(payTypes, item)
					}
				}
			}
		} else {
			payTypes = append(payTypes, item)
		}
	}
	setting.Values.PayTypes = payTypes
	return
}

func SaveSetting(setting model.Setting) (err error) {
	setting.Key = "small_shop_setting"
	if setting.ID != 0 {
		err = source.DB().Updates(&setting).Error
	} else {
		err = source.DB().Create(&setting).Error
	}
	return err
}

func GetPayTypes() (err error, types []map[string]interface{}) {
	type1 := map[string]interface{}{
		"id":   1,
		"name": "微信官方支付",
	}
	type2 := map[string]interface{}{
		"id":   2,
		"name": "汇聚微信支付",
	}
	type3 := map[string]interface{}{
		"id":   3,
		"name": "拉卡拉收银台",
	}
	types = append(types, type1)
	types = append(types, type2)
	types = append(types, type3)
	types = append(types, map[string]interface{}{
		"id":   4,
		"name": "微信支付",
	})
	//聚合支付 -- 数据通
	communicationPaymentInfoData := paymentModel.GetSmallShopCommunicationPaymentInfoData(paymentModel.SCENE_WECHAT_MINI)
	if len(communicationPaymentInfoData) > 0 {
		for _, item := range communicationPaymentInfoData {
			types = append(types, map[string]interface{}{
				"id":   paymentModel.AGGREGATE_PAYMENT + int(item.ID),
				"name": "聚合支付-" + item.PayTypeName,
				"type": strconv.Itoa(paymentModel.AGGREGATE_PAYMENT),
			})
		}
	}
	return
}

func GetPayTypesByH5(mode int) (err error, types []map[string]interface{}) {
	type1 := map[string]interface{}{
		"id":   1,
		"name": "微信官方支付",
	}
	type2 := map[string]interface{}{
		"id":   2,
		"name": "汇聚微信支付",
	}
	type3 := map[string]interface{}{
		"id":   3,
		"name": "拉卡拉收银台",
	}
	types = append(types, type1)
	types = append(types, type2)
	types = append(types, type3)
	types = append(types, map[string]interface{}{
		"id":   4,
		"name": "微信支付",
	})
	//聚合支付 -- 数据通
	communicationPaymentInfoData := paymentModel.GetSmallShopCommunicationPaymentInfoData(paymentModel.SCENE_H5)

	if len(communicationPaymentInfoData) > 0 {
		for _, item := range communicationPaymentInfoData {
			name := "聚合支付-" + item.PayTypeName
			if mode == 1 {
				name = item.PayTypeName
			}
			types = append(types, map[string]interface{}{
				"id":       paymentModel.AGGREGATE_PAYMENT + int(item.ID),
				"name":     name,
				"type":     strconv.Itoa(paymentModel.AGGREGATE_PAYMENT),
				"pay_type": item.PayType,
			})
		}
	}
	return
}

//var Miniprogram *miniprogram.MiniProgram

func SaveWxSetting(setting model.WxSetting) (err error) {
	if setting.ID != 0 {
		err = source.DB().Updates(&setting).Error
	} else {
		err = source.DB().Create(&setting).Error
	}
	model.ResetMini()
	paymentSetting.ResetMini()
	//Miniprogram = nil
	return
}

func ConvergenceSetting(data model.ConvergenceSetting) (err error) {

	err = setting2.SetSetting("smallShopConvergenceSetting", data)

	return
}

func GetConvergenceSetting() (data string, err error) {

	err, data = setting2.GetSetting("smallShopConvergenceSetting")
	//上面返回空的时候会返回 请先到后台进行提现设置  导致无法判断空后台也会提示请先保存配置
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("请先保存配置")
		return
	}

	return
}

func UploadFile(file *multipart.FileHeader, c *gin.Context) (p string, fullP string, err error) {
	// 读取文件后缀
	//if config.Config().Local.Path == "" {
	configPath := "./data/goSupply/uploads/smallShopWechatMiniCert"

	// 尝试创建此路径
	mkdirErr := os.MkdirAll(configPath, os.ModePerm)
	if mkdirErr != nil {
		log.Log().Error("function os.MkdirAll() Filed", zap.Any("err", mkdirErr.Error()))
		return "", "", errors.New("function os.MkdirAll() Filed, err:" + mkdirErr.Error())
	}

	fmt.Println(configPath + "/" + file.Filename)
	// 上传文件到指定的路径
	err = c.SaveUploadedFile(file, configPath+"/"+file.Filename)
	if err != nil {
		return
	}
	filename := file.Filename
	p = "uploads/smallShopWechatMiniCert/" + filename
	fullP = config.Config().Local.Host + p
	return fullP, p, nil
}

type SysSettingModel struct {
	source.Model
	Key   string          `json:"key"`
	Value SysSettingValue `json:"value"`
}

func (SysSettingModel) TableName() string {
	return "sys_settings"
}

type SysSettingValue struct {
	ShopName string `json:"shop_name"`
	ShopLogo string `json:"shop_logo"`
}

func (value SysSettingValue) Value() (driver.Value, error) {
	return json.Marshal(value)
}

func (value *SysSettingValue) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

func GetSysSetting() (err error, sysSetting SysSettingModel) {
	err = source.DB().Where("`key` = ?", "shop_setting").First(&sysSetting).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}

	return
}
