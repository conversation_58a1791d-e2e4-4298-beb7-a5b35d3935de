package service

import (
	"errors"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	uuid "github.com/satori/go.uuid"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	orderService "order/service"
	"os"
	"small-shop/model"
	"small-shop/request"
	"small-shop/response"
	"strconv"
	"time"
	"yz-go/component/log"
	"yz-go/config"
	yzRequest "yz-go/request"
	"yz-go/source"
	"yz-go/utils"
)

// Login
//
// @function: Login
// @description: 用户登录
// @param: u *model.User
// @return: err error, userInter *model.User
func Login(u *model.SmallShopUser) (err error, userInter *model.SmallShopUser) {
	var userSecret model.SmallShopUser
	u.Password = utils.MD5V([]byte(u.Password))
	err = source.DB().Where("username = ? AND password = ?", u.Username, u.Password).First(&userSecret).Error

	return err, &userSecret
}

func LoginWithCode(u *model.SmallShopUser) (err error, userInter *model.SmallShopUser) {
	var userSecret model.SmallShopUser
	err = source.DB().Where("username = ?", u.Username).First(&userSecret).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("用户名不存在")
		}
		err = errors.New("查询会员失败")
		return err, &userSecret
	}
	if userSecret.Status == 0 {
		return errors.New("您还未通过审核，请联系管理员审核后登录"), userInter
	}
	if userSecret.Status == -1 {
		return errors.New("您已被拉黑,请联系管理员"), userInter
	}
	return err, &userSecret
}

// GetUserInfo
//
// @function: GetUserInfo
// @description: 获取用户信息
// @param: u *model.User
// @return: err error, userInter *model.User
func GetUserInfo(uid uint) (err error, userInter *model.SmallShopUser) {
	err = source.DB().Where("id = ?", uid).First(&userInter).Error
	return
}

func UpdateUser(user model.SmallShopUser) (err error) {
	err = source.DB().Model(&model.SmallShopUser{}).Where("id = ?", user.ID).Updates(map[string]interface{}{"avatar": user.Avatar, "nick_name": user.NickName, "username": user.Username}).Error
	return
}

//
//@function: FindUserByUuid
//@description: 通过uuid获取用户信息
//@param: uuid string
//@return: err error, user *model.SysUser

func FindUserByUuid(uuid string) (err error, user *model.SmallShopUser) {
	var u model.SmallShopUser
	if err = source.DB().Where("`uuid` = ?", uuid).First(&u).Error; err != nil {
		return errors.New("用户不存在"), &u
	}
	return nil, &u
}

//
//@function: Register
//@description: 用户注册
//@param: u model.User
//@return: err error, userInter model.SysUser

func Register(u model.SmallShopUser) (err error, userInter model.SmallShopUser) {
	var user model.SmallShopUser
	if !errors.Is(source.DB().Where("username = ?", u.Username).First(&user).Error, gorm.ErrRecordNotFound) { // 判断用户名是否注册
		return errors.New("用户名已注册"), user
	}
	// 否则 附加uuid 密码md5简单加密 注册
	u.Password = utils.MD5V([]byte(u.Password))
	u.UUID = uuid.NewV4()

	u.Avatar = "https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"

	err = source.DB().Create(&u).Error
	if err != nil {
		return
	}

	return
}

//
//@function: CreateAddress
//@description: 创建Address记录
//@param: address model.SmallShopUserAddress
//@return: err error

func CreateAddress(address model.SmallShopUserAddress) (err error, addressWithId model.SmallShopUserAddress) {
	nameLen := len(address.Realname)
	if nameLen > 45 || nameLen < 3 {
		return errors.New("收货人长度错误，请输入1-15个中文字符，如其他字符则请输入3-45个"), addressWithId
	}

	if address.IsDefault != nil && *address.IsDefault == true {
		err = source.DB().Model(model.SmallShopUserAddress{}).Where("`small_shop_user_id` = ?", address.SmallShopUserID).Update("is_default", 0).Error
	}
	err = source.DB().Create(&address).Error
	return err, address
}

func DeleteAddress(address model.SmallShopUserAddress) (err error) {
	err = source.DB().Delete(&address).Error
	return err
}

func DeleteAddressByIds(ids yzRequest.IdsReq) (err error) {
	err = source.DB().Delete(&[]model.SmallShopUserAddress{}, "id in ?", ids.Ids).Error
	return err
}

func UpdateAddress(address model.SmallShopUserAddress) (err error) {
	nameLen := len(address.Realname)
	if nameLen > 45 || nameLen < 3 {
		return errors.New("收货人长度错误，请输入1-15个中文字符，如其他字符则请输入3-45个")
	}
	if *address.IsDefault == true {
		err = source.DB().Model(model.SmallShopUserAddress{}).Where("`small_shop_user_id` = ?", address.SmallShopUserID).Update("is_default", 0).Error
	}
	err = source.DB().Save(&address).Error
	return err
}

func GetAddress(id uint) (err error, address model.SmallShopUserAddress) {
	err = source.DB().Where("id = ?", id).First(&address).Error
	return
}

func GetDefaultAddress(uid uint) (err error, address model.SmallShopUserAddress) {
	err = source.DB().Model(&model.SmallShopUserAddress{}).Where("small_shop_user_id = ? and is_default = ?", uid, true).First(&address).Error
	return
}

func GetAddressInfoList(info request.AddressSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.SmallShopUserAddress{}).Where("deleted_at is null")
	var addresses []model.SmallShopUserAddress
	// 如果有条件搜索 下方会自动创建搜索语句
	err = db.Count(&total).Error
	err = db.Where("small_shop_user_id = ?", info.SmallShopUserID).Limit(limit).Offset(offset).Order("created_at desc").Find(&addresses).Error
	return err, addresses, total
}

func GetUserInfoList(info request.UserSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&response.UserResponse{}).Preload(clause.Associations)
	var users []response.UserResponse
	// 会员id
	if info.Uid != 0 {
		db = db.Where("`id` = ?", info.Uid)
	}
	// 会员手机号
	if info.Mobile != "" {
		db = db.Where("`mobile` LIKE ?", "%"+info.Mobile+"%")
	}
	if info.Username != "" {
		db = db.Where("`username` LIKE ?", "%"+info.Username+"%")
	}
	// 昵称
	if info.NickName != "" {
		db = db.Where("`nick_name` LIKE ?", "%"+info.NickName+"%")
	}
	// 注册时间
	if info.StartAT != "" {
		db.Where("`created_at` >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db.Where("`created_at` <= ?", info.EndAT)
	}
	// 推荐id
	if info.ParentId != 0 {
		db = db.Where("`parent_id` = ?", info.ParentId)
	}
	// 类型
	if info.Type == 1 {
		db = db.Where("wx_openid != ?", "")
	}
	if info.Type == 2 {
		db = db.Where("wx_mini_openid != ?", "")
	}
	if info.Type == 3 {
		db = db.Where("wx_unionid != ?", "")
	}
	// 是否同步
	if info.IsSync != 0 {
		if info.IsSync == -1 {
			info.IsSync = 0
		}
		db = db.Where("`synced` = ?", info.IsSync)
	}
	// 中台会员id
	if info.SyncUID != 0 {
		db = db.Where("`sync_uid` = ?", info.SyncUID)
	}
	err = db.Count(&total).Error
	err = db.Order("created_at desc").Limit(limit).Offset(offset).Find(&users).Error
	return err, users, total
}

func ExportExportUserList(info request.UserSearch) (err error, link string) {
	info.PageSize = 5000
	// 创建db
	db := source.DB().Model(&response.UserResponse{})
	var users []response.UserResponse
	var total int64
	// 会员id
	if info.Uid != 0 {
		db = db.Where("`id` = ?", info.Uid)
	}
	// 会员手机号
	if info.Mobile != "" {
		db = db.Where("`mobile` LIKE ?", "%"+info.Mobile+"%")
	}
	// 昵称
	if info.NickName != "" {
		db = db.Where("`nick_name` LIKE ?", "%"+info.NickName+"%")
	}
	// 注册时间
	if info.StartAT != "" {
		db.Where("`created_at` >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db.Where("`created_at` <= ?", info.EndAT)
	}
	// 推荐id
	if info.ParentId != 0 {
		db = db.Where("`parent_id` = ?", info.ParentId)
	}
	// 类型
	if info.Type == 1 {
		db = db.Where("wx_openid != ?", "")
	}
	if info.Type == 2 {
		db = db.Where("wx_mini_openid != ?", "")
	}
	if info.Type == 3 {
		db = db.Where("wx_unionid != ?", "")
	}
	// 是否同步
	if info.IsSync != 0 {
		if info.IsSync == -1 {
			info.IsSync = 0
		}
		db = db.Where("`synced` = ?", info.IsSync)
	}
	// 中台会员id
	if info.SyncUID != 0 {
		db = db.Where("`sync_uid` = ?", info.SyncUID)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	excelPage := total/int64(info.PageSize) + 1
	var links []string
	timeString := time.Now().Format("20060102150405")
	path := config.Config().Local.Path + "/export_small_shop_user"
	for ei := 1; ei <= int(excelPage); ei++ {
		err = db.Preload(clause.Associations).
			Order("id DESC").Limit(info.PageSize).Offset(info.PageSize * (ei - 1)).Find(&users).Error
		if err != nil {
			return
		}
		f := excelize.NewFile()
		// 创建一个工作表
		index := f.NewSheet("Sheet1")
		// 设置单元格的值
		f.SetCellValue("Sheet1", "A1", "会员ID")
		f.SetCellValue("Sheet1", "B1", "注册时间")
		f.SetCellValue("Sheet1", "C1", "推荐人")
		f.SetCellValue("Sheet1", "D1", "会员信息")
		f.SetCellValue("Sheet1", "E1", "手机号")
		f.SetCellValue("Sheet1", "F1", "类型")
		f.SetCellValue("Sheet1", "G1", "订单数量")
		f.SetCellValue("Sheet1", "H1", "订单金额")
		f.SetCellValue("Sheet1", "I1", "小商店数量")
		f.SetCellValue("Sheet1", "J1", "状态")
		f.SetCellValue("Sheet1", "K1", "中台会员id")
		i := 2
		for _, v := range users {
			var createAt string
			if v.CreatedAt != nil {
				createAt = v.CreatedAt.Format("2006-01-02 15:04:05")
			}
			f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), v.ID)
			f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), createAt)
			f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), v.ParentUser.Username+"("+strconv.Itoa(int(v.ParentUser.ID))+")")
			f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), v.Username)
			f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), v.Mobile)
			// 类型
			f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), "")
			f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), v.OrderCount)
			var orderPriceSum decimal.Decimal
			if v.OrderPriceSum != 0 {
				orderPriceSum = decimal.NewFromInt(int64(v.OrderPriceSum)).DivRound(decimal.NewFromInt(100), 2)
			} else {
				orderPriceSum = decimal.NewFromInt(0)
			}
			f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), orderPriceSum)
			f.SetCellValue("Sheet1", "I"+strconv.Itoa(i), v.SmallShopCount)
			// 状态 -1拉黑0待审核1正常
			var statusName string
			if v.Status == 1 {
				statusName = "正常"
			} else if v.Status == 0 {
				statusName = "待审核"
			} else {
				statusName = "拉黑"
			}
			syncName := "未同步"
			if v.SyncUID != 0 {
				syncName = strconv.Itoa(int(v.SyncUID))
			}
			f.SetCellValue("Sheet1", "J"+strconv.Itoa(i), statusName)
			f.SetCellValue("Sheet1", "K"+strconv.Itoa(i), syncName)
		}
		// 设置工作簿的默认工作表
		f.SetActiveSheet(index)
		exist, _ := utils.PathExists(path)
		if !exist {
			// 创建文件夹
			err = os.Mkdir(path, os.ModePerm)
			if err != nil {
				fmt.Printf("mkdir failed![%v]\n", err)
			} else {
				fmt.Printf("mkdir success!\n")
			}
		}
		link = path + "/" + timeString + "-" + strconv.Itoa(ei) + "小商店会员导出.xlsx"
		if err = f.SaveAs(link); err != nil {
			log.Log().Error("小商店会员导出错误4：", zap.Any("err", err.Error()))
			return
		}
		links = append(links, link)
	}
	if excelPage > 1 {
		link = path + "/" + timeString + "小商店会员导出.zip"
		err = orderService.Zip(link, links)
	}
	return err, link
}

func ChangeBlack(id uint) (err error) {
	var user model.SmallShopUser
	err = source.DB().Model(&model.SmallShopUser{}).Where("id = ?", id).First(&user).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if user.Status == -1 {
		err = errors.New("该会员已在黑名单")
		return
	}
	err = source.DB().Model(&user).Where("id = ?", id).Updates(map[string]interface{}{"status": -1}).Error
	return
}

func ChangeWhite(id uint) (err error) {
	var user model.SmallShopUser
	err = source.DB().Model(&model.SmallShopUser{}).Where("id = ?", id).First(&user).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if user.Status == 1 {
		err = errors.New("该会员已在白名单")
		return
	}
	err = source.DB().Model(&user).Where("id = ?", id).Updates(map[string]interface{}{"status": 1}).Error
	return
}

func WechatH5Register(wechatRegister request.WechatRegister) (err error, userInter model.SmallShopUser) {
	//如果是绑定 就直接绑定
	if wechatRegister.IsBind != 1 {
		return errors.New("请使用公众号注册"), userInter

	}
	var saveUser model.SmallShopUser
	err = source.DB().Where("username = ?", wechatRegister.Username).First(&saveUser).Error
	if err != nil {
		err = errors.New("绑定失败:账号不存在")
		return
	}
	// 如果查询到的用户已经绑定了公众号，并且公众号openid与传入的openid不一致，则返回错误
	if saveUser.WxOpenid != "" {
		if saveUser.WxOpenid != wechatRegister.WxOpenid {
			err = errors.New("绑定失败:该账号已绑定其他微信")
			return
		}
	} else {
		// 如果查询到的用户没有绑定公众号，则直接绑定
		saveUser.WxOpenid = wechatRegister.WxOpenid
	}
	saveUser.WxUnionid = wechatRegister.WxUnionid
	err = source.DB().Save(&saveUser).Error
	if err != nil {
		err = errors.New("绑定失败")
		return
	}
	return err, saveUser
}

func WechatRegister(wechatRegister request.WechatRegister) (err error, userInter model.SmallShopUser) {
	//如果是绑定 就直接绑定
	if wechatRegister.IsBind == 1 {
		var userData model.SmallShopUser
		userDb := source.DB()
		userDb = userDb.Where("wx_mini_openid = ?", wechatRegister.WxMiniOpenid)
		userDb.First(&userData)
		if userData.ID != 0 {
			err = errors.New("微信已绑定账户")
			return
		}
		var saveUser model.SmallShopUser
		err = source.DB().Where("username = ?", wechatRegister.Username).First(&saveUser).Error
		if err != nil {
			err = errors.New("绑定失败:账号不存在")
			return
		}
		if wechatRegister.WxMiniOpenid != "" {
			saveUser.WxMiniOpenid = wechatRegister.WxMiniOpenid
		}
		saveUser.WxUnionid = wechatRegister.WxUnionid
		err = source.DB().Save(&saveUser).Error
		if err != nil {
			err = errors.New("绑定失败")
			return
		}
		return err, saveUser
	}
	// 注册流程
	var user model.SmallShopUser
	// 判断用户名是否注册
	if !errors.Is(source.DB().Where("username = ?", wechatRegister.Username).First(&user).Error, gorm.ErrRecordNotFound) {
		err = errors.New("用户名已注册")
		return
	}
	var addUser model.SmallShopUser
	// 否则 附加uuid 密码md5简单加密 注册
	if wechatRegister.Password != "" {
		addUser.Password = utils.MD5V([]byte(wechatRegister.Password))
	}
	addUser.WxUnionid = wechatRegister.WxUnionid
	addUser.WxOpenid = wechatRegister.WxOpenid
	addUser.WxMiniOpenid = wechatRegister.WxMiniOpenid
	addUser.Avatar = wechatRegister.Avatar
	addUser.NickName = wechatRegister.NickName
	addUser.Username = wechatRegister.Username
	addUser.UUID = uuid.NewV4()
	addUser.Status = 1
	err = source.DB().Create(&addUser).Error
	if err != nil {
		return
	}
	userInter = addUser
	return
}

func SyncUser() (err error) {
	var users []model.SmallShopUser
	err = source.DB().Where("wx_unionid != ? and synced = ?", "", 0).Find(&users).Error
	if err != nil || len(users) == 0 {
		return errors.New("没有可以同步的用户")
	}
	go func() {
		if syncErr := syncHandle(users); syncErr != nil {
			log.Log().Error("同步小商店会员失败", zap.Error(syncErr))
		}
	}()
	return nil
}

func syncHandle(users []model.SmallShopUser) error {
	var syncErrors []error

	for _, user := range users {
		var wechatRegister request.WechatRegister
		wechatRegister.NickName = user.NickName
		wechatRegister.Avatar = user.Avatar
		wechatRegister.WxOpenid = user.WxOpenid
		wechatRegister.WxMiniOpenid = user.WxMiniOpenid
		wechatRegister.WxUnionid = user.WxUnionid
		wechatRegister.ParentId = user.ParentId

		err, supplyUser := AddUserToSupplyUser(wechatRegister)
		if err != nil {
			log.Log().Error("同步用户失败",
				zap.Uint("user_id", user.ID),
				zap.Error(err))
			syncErrors = append(syncErrors, err)
			continue // 继续同步其他用户
		}

		if err := source.DB().Model(&model.SmallShopUser{}).
			Where("id = ?", user.ID).
			Updates(map[string]interface{}{
				"synced":   1,
				"sync_uid": supplyUser.ID,
			}).Error; err != nil {
			log.Log().Error("更新同步状态失败",
				zap.Uint("user_id", user.ID),
				zap.Error(err))
			syncErrors = append(syncErrors, err)
		}
	}

	if len(syncErrors) > 0 {
		return fmt.Errorf("同步过程中发生 %d 个错误", len(syncErrors))
	}
	return nil
}
