package service

import (
	"errors"
	uuid "github.com/satori/go.uuid"
	"github.com/silenceper/wechat/v2"
	"github.com/silenceper/wechat/v2/cache"
	"github.com/silenceper/wechat/v2/miniprogram"
	miniprogramConfig "github.com/silenceper/wechat/v2/miniprogram/config"
	"go.uber.org/zap"
	orderModel "order/model"
	payModel "payment/model"
	"small-shop/model"
	"small-shop/request"
	small_shop_order "small-shop/small-shop-order"
	"strconv"
	"time"
	"wechatmini/common"
	wechatminiCommon "wechatmini/common"
	wechatminiRequest "wechatmini/request"
	wechatminiService "wechatmini/service"
	"yz-go/component/log"
	"yz-go/config"
	"yz-go/source"
)

type WechatMiniData struct {
	IsRegister   int    `json:"is_register" form:"is_register"` //是否需要注册1是2否
	Avatar       string `json:"avatar" form:"avatar"`
	Nickname     string `json:"nickname" form:"nickname"`
	WxMiniOpenid string `json:"wx_mini_openid" form:"wx_mini_openid"`
	WxUnionid    string `json:"wx_unionid" form:"wx_unionid"`
	SID          uint   `json:"sid" form:"sid"`
}

var Miniprogram *miniprogram.MiniProgram

func GetMiniprogram() (err error, miniprogram *miniprogram.MiniProgram) {
	if Miniprogram == nil {
		var wechatMini model.WxValue
		err, wechatMini = model.GetMini()
		if err != nil {
			err = errors.New("获取微信小程序配置失败" + err.Error())
			return
		}
		if wechatMini.AppId == "" || wechatMini.AppSecret == "" {
			err = errors.New("请配置微信小程序")
			return
		}
		//微信小程序配置部分
		var addr = config.Config().Redis.Addr
		if addr == "" {
			addr = "127.0.0.1:6379"
		}
		redisOpts := &cache.RedisOpts{
			Host:        addr,
			Database:    config.Config().Redis.DB,
			MaxActive:   10,
			MaxIdle:     10,
			IdleTimeout: 60, //second
		}
		if config.Config().Redis.Password != "" {
			redisOpts.Password = config.Config().Redis.Password
		}
		redisCache := cache.NewRedis(redisOpts)
		cfg := &miniprogramConfig.Config{
			AppID:     wechatMini.AppId,
			AppSecret: wechatMini.AppSecret,
			Cache:     redisCache,
		}
		Miniprogram = wechat.NewWechat().GetMiniProgram(cfg)
	}
	return err, Miniprogram
}

func GetWxMiniUser(login request.Login) (err error, resultUser *model.SmallShopUser) {
	err, miniprogramApi := GetMiniprogram()
	if err != nil {
		log.Log().Error("获取微信配置失败!", zap.Any("err", err))
		return
	}
	//获取用户信息
	result, err := miniprogramApi.GetAuth().Code2Session(login.Code)
	if err != nil {
		return
	}
	var user model.SmallShopUser
	db := source.DB().Model(&model.SmallShopUser{}).Where("wx_mini_openid = ?", result.OpenID)
	//如果返回了Unionid 也使用Unionid 查询
	if result.UnionID != "" {
		db = db.Or("wx_unionid = ?", result.UnionID)
	}
	err = db.First(&user).Error
	//如果用户不存在则创建
	if err != nil {
		var wechatData WechatMiniData
		wechatData.Nickname = login.NickName
		wechatData.Avatar = login.AvatarUrl
		wechatData.WxMiniOpenid = result.OpenID
		wechatData.WxUnionid = result.UnionID
		wechatData.SID = login.SID
		err, user = MiniRegister(wechatData)
		resultUser = &user
		return
	}
	//如果用户存在 openid不存在就保存一下
	if user.WxMiniOpenid == "" || user.WxUnionid == "" {
		user.WxMiniOpenid = result.OpenID
		user.WxUnionid = result.UnionID
		source.DB().Where("id = ?", user.ID).Save(&user)
	}
	if user.Status == -1 {
		err = errors.New("已被拉黑,请联系管理员")
		return
	}
	resultUser = &user
	return
}

func MiniRegister(wechatData WechatMiniData) (err error, userInter model.SmallShopUser) {
	// 查询小商店
	var smallShop model.SmallShop
	err, smallShop = GetSmallShopByID(wechatData.SID)
	if err != nil {
		return err, userInter
	}
	// 注册流程
	var addUser model.SmallShopUser
	addUser.ParentId = smallShop.Uid
	addUser.WxUnionid = wechatData.WxUnionid
	addUser.WxMiniOpenid = wechatData.WxMiniOpenid
	addUser.Avatar = wechatData.Avatar
	addUser.NickName = wechatData.Nickname
	addUser.UUID = uuid.NewV4()
	addUser.Status = 1
	err = source.DB().Create(&addUser).Error
	if err != nil {
		return err, userInter
	}
	// 如果没有WxUnionid则不同步
	if addUser.WxUnionid != "" {
		// 查询小商店基础设置，查看是否开启同步会员到中台，如果开启则同步。
		var setting model.Setting
		err, setting = GetSetting()
		if err != nil {
			return err, userInter
		}
		if setting.Values.SyncUsers == 1 {
			var wechatRegister request.WechatRegister
			wechatRegister.NickName = addUser.NickName
			wechatRegister.Avatar = addUser.Avatar
			wechatRegister.WxOpenid = addUser.WxOpenid
			wechatRegister.WxMiniOpenid = addUser.WxMiniOpenid
			wechatRegister.WxUnionid = addUser.WxUnionid
			wechatRegister.ParentId = smallShop.Uid
			addError, supplyUser := AddUserToSupplyUser(wechatRegister)
			if addError != nil {
				return addError, userInter
			}
			// 同步成功后更新小商店会员为已同步
			err = source.DB().Model(&model.SmallShopUser{}).Where("id = ?", addUser.ID).Updates(map[string]interface{}{"synced": 1, "sync_uid": supplyUser.ID}).Error
			if err != nil {
				return err, userInter
			}
		}
	}
	userInter = addUser
	return err, userInter
}

// 拼接需要提交到微信小程序的发货信息
// status 1按钮提交 2 监听提交
func UploadShippingInfoData(info wechatminiRequest.UploadShippingInfoRequest, status int, rd wechatminiCommon.RequestData) (err error) {
	//未开启不推送
	if rd.Config.IsUploadShippingInfo != 1 {
		err = errors.New("小商店:未开启推送")
		return
	}
	if rd.Config.Mchid == "" {
		//log.Log().Error("微信小程序推送发货信息:请填写商户号")
		err = errors.New("小商店:请填写商户号")
		return
	}
	var orderData small_shop_order.SmallShopOrder
	err = source.DB().Model(&small_shop_order.SmallShopOrder{}).Preload("SmallShopPayInfo").Preload("SmallShopUser").Preload("OrderExpress").Preload("ShippingAddress").Where("id = ?", info.OrderId).First(&orderData).Error
	if err != nil {
		log.Log().Error("小商店:微信小程序推送发货信息:订单查询失败", zap.Any("错误信息", err))

		err = errors.New("小商店:订单查询失败" + err.Error())
		return
	}
	if orderData.PayTypeID != payModel.WXMINICODE {
		log.Log().Error("小商店:微信小程序推送发货信息:不是微信小程序支付的订单", zap.Any("orderId", orderData.ID))

		err = errors.New("小商店:不是微信小程序支付的订单")
		return
	}
	if orderData.SmallShopUser.WxMiniOpenid == "" {
		log.Log().Error("小商店:微信小程序推送发货信息:用户没有绑定微信小程序", zap.Any("orderId", orderData.ID))
		err = errors.New("小商店:用户没有绑定微信小程序")
		return
	}
	err = StartUploadShippingInfo(info, status, rd, orderData)
	if err != nil {
		orderErr := source.DB().Model(&orderModel.OrderModel{}).Where("id = ?", orderData.ID).Updates(map[string]interface{}{"is_wx_mini_send": -1}).Error
		if orderErr != nil {
			log.Log().Error("小商店:微信小程序推送发货信息:保存失败状态失败", zap.Any("orderId", orderData.ID), zap.Any("orderErr", orderErr))
		}
	} else {
		err = source.DB().Model(&orderModel.OrderModel{}).Where("id = ?", orderData.ID).Updates(map[string]interface{}{"is_wx_mini_send": 1}).Error
		if err != nil {
			log.Log().Error("小商店:微信小程序推送发货信息:保存成功状态失败", zap.Any("orderId", orderData.ID))
		}
	}
	return
}
func StartUploadShippingInfo(info wechatminiRequest.UploadShippingInfoRequest, status int, rd common.RequestData, orderData small_shop_order.SmallShopOrder) (err error) {
	var data common.UploadShippingInfoRequest
	data.OrderKey.OrderNumberType = 1
	data.OrderKey.OutTradeNo = strconv.Itoa(int(orderData.SmallShopPayInfo.PaySN))
	data.OrderKey.Mchid = rd.Config.Mchid
	data.UploadTime = time.Now()
	data.Payer.Openid = orderData.SmallShopUser.WxMiniOpenid
	data.DeliveryMode = "SPLIT_DELIVERY" //默认分批发货
	data.IsAllDelivered = true

	//如果是前端提交参数
	if status == 1 {
		if len(info.ShippingList) == 1 {
			data.DeliveryMode = "UNIFIED_DELIVERY"
		}
		data.LogisticsType = info.LogisticsType
		for _, item := range info.ShippingList {
			var shipping common.ShippingList
			shipping.TrackingNo = item.TrackingNo
			shipping.ExpressCompany = item.ExpressCompany
			shipping.ItemDesc = item.ItemDesc
			shipping.Contact.ReceiverContact = wechatminiService.MaskString(item.Contact)
			data.ShippingList = append(data.ShippingList, shipping)
		}

	} else { //如果是自动提交
		data.LogisticsType = 1
		if len(orderData.OrderExpress) == 1 {
			data.DeliveryMode = "UNIFIED_DELIVERY"
		}
		for _, item := range orderData.OrderExpress {
			var shipping common.ShippingList
			shipping.TrackingNo = item.ExpressNo
			shipping.ExpressCompany = item.CompanyCode
			shipping.ItemDesc = "商品发货"
			shipping.Contact.ReceiverContact = wechatminiService.MaskString(orderData.ShippingAddress.Mobile)
			data.ShippingList = append(data.ShippingList, shipping)
		}
	}
	//提交到微信小程序
	err = rd.UploadShippingInfo(data)
	if err != nil {
		return
	}
	return
}
