package goods

import (
	"bytes"
	catemodel "category/model"
	"encoding/json"
	"errors"
	"fmt"
	gatherSupplyRequest "gather-supply/request"
	"github.com/chenhg5/collection"
	"github.com/gogf/gf/frame/g"
	"github.com/gookit/color"
	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
	"gorm.io/gorm"
	log2 "log"
	"math"
	pmodel "product/model"
	pservice "product/service"
	callback2 "public-supply/callback"
	"public-supply/common"
	"public-supply/model"
	"public-supply/request"
	"public-supply/service"
	gsetting "public-supply/setting"
	"strconv"
	"strings"
	"sync"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

type Stbz struct {
	Dat SupplySetting
}

func (st *Stbz) ManuallyProductUpdate(productID uint) (err error) {
	//TODO implement me
	panic("implement me")
}

func (st *Stbz) SynchronizeProductsToLocal() (err error) {
	//TODO implement me
	panic("implement me")
}

var wg sync.WaitGroup

func (st *Stbz) ImportSelectGoodsRun(info model.SelectGoods) (err error, list interface{}) {
	orderPN := GetOrderNo()
	common.GlobalOrderSN = orderPN
	goodsRecord := model.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: len(info.List),
		Status:            1,
	}
	var resultInt []int
	var field string
	field = "source_goods_id"

	err = source.DB().Model(pmodel.Product{}).Where("gather_supply_id=?", info.GatherSupplyID).Pluck(field, &resultInt).Error
	if err != nil {
		log2.Println("查询供应链商品id错误", err)
		return
	}

	if len(info.List) <= 0 {
		log2.Println("导入空数据")
		err = errors.New("导入的是空数据")
		return
	}

	var idsArr []int
	idsArr = GetIdArr(info.List)

	difference := collection.Collect(idsArr).Diff(resultInt).ToIntArray()
	repeat := len(idsArr) - len(difference)
	goodsRecord.RepeatQuantity = repeat //重复数据
	if len(difference) <= 0 {
		goodsRecord.Status = 2
		goodsRecord.CompletionStatus = 1
		source.DB().Omit("goods_arr").Create(&goodsRecord) //当前没有商品可以导入
		return
	}
	source.DB().Omit("goods_arr").Create(&goodsRecord) //创建导入记录
	var goodsList []model.Goods
	for _, v := range difference {
		for _, item := range info.List {
			if item.ID == v {
				goodsList = append(goodsList, item)
			}
		}

	}
	info.List = goodsList
	arrList := splitArray(info.List, 20)
	for index, item := range arrList {
		wg.Add(1)
		fmt.Println("循环", index)
		st.RunSelectGoodsConcurrent(orderPN, item, info.Categorys, info.Key, info.GatherSupplyID)
	}
	wg.Wait()
	err = SetImportRecordCompletion(orderPN)
	fmt.Println("全部完成：")

	return
}
func (st *Stbz) RunSelectGoodsConcurrent(orderPN string, list []model.Goods, category string, key string, gatherSupplyID uint) (err error) {

	defer wg.Done()

	cateList := strings.Split(category, ",")
	var cateId1, cateId2, cateId3 int
	cateId1, err = strconv.Atoi(cateList[0])
	cateId2, err = strconv.Atoi(cateList[1])
	cateId3, err = strconv.Atoi(cateList[2])

	var listGoods []*pmodel.Product

	var recordError []model.SupplyGoodsImportRecordErrors
	err, listGoods, recordError = st.CommodityAssembly(list, cateId1, cateId2, cateId3, gatherSupplyID, 0)
	log.Log().Info(" stbz RunSelectGoodsConcurrent ", zap.Any("长度:", len(listGoods)))

	if err != nil {
		return
	}
	if len(listGoods) > 0 {
		err = service.FinalProcessing(listGoods, orderPN)
		if err != nil {
			return
		}

	}
	if len(recordError) > 0 {
		err = service.FinalProcessingError(recordError, orderPN)
		if err != nil {
			return
		}
	}
	return

}

func (st *Stbz) ValidateConfig(params gatherSupplyRequest.ValidateConfigParams) (err error) {
	stbzapi = stbz.NewConfig(params.AppKey, params.AppSecret)

	stbz.SetConfig(stbzapi)

	var result *stbz.APIResult

	result, err = stbz.API(
		stbz.Method.POST,
		string("/v2/GetSource"),
		map[string]string{},
		g.Map{"page": 1, "limit": 100},
	)

	if result.Code != 1 {
		err = errors.New(result.Msg)
		return
	}
	return
}

func (st *Stbz) DeleteGoods(id uint) (err error) {
	//TODO implement me
	return
}

func (st *Stbz) GetStbzSource() (err error, data interface{}) {

	var result *stbz.APIResult
	result, err = stbz.API(
		stbz.Method.POST,
		string("/v2/GetSource"),
		map[string]string{},
		g.Map{"page": 1, "limit": 100},
	)

	fmt.Println("返回的code", result.Data)
	aaa, _ := json.Marshal(&result.Data)
	fmt.Println(string(aaa))

	if result.Code != 1 {
		err = errors.New(result.Msg)
		return
	}

	data = result.Data

	//data = datas["list"]

	return
}

func (st *Stbz) GetSupplyBalance(GatherSupplyID uint) (err error, balance interface{}) {

	return
}

var stbzapi *stbz.Config
var dat SupplySetting
var SettingDat SupplySetting

type SupplySetting struct {
	BaseInfo       BaseInfoData            `json:"baseInfo"`
	UpdateInfo     gsetting.UpdateInfoData `json:"update"`
	Pricing        gsetting.PricingData    `json:"pricing"`
	Management     gsetting.Management     `json:"management"`
	Cloud          gsetting.Cloud          `json:"cloud"`
	GatherSupplyID uint                    `json:"gatherSupplyID"`
}

type BaseInfoData struct {
	AppKey    string `json:"appKey"`
	AppSecret string `json:"appSecret"`
}

func (st *Stbz) InitSetting(gatherSupplyID uint) (err error) {
	var setting model2.SysSetting
	err, setting = gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}
	err = json.Unmarshal([]byte(setting.Value), &dat)
	st.Dat = dat
	SettingDat = dat
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	if dat.BaseInfo.AppKey == "" && dat.BaseInfo.AppSecret == "" {
		err = errors.New("请先配置供应链key")
		return
	}

	dat.GatherSupplyID = gatherSupplyID
	stbzapi = stbz.NewConfig(dat.BaseInfo.AppKey, dat.BaseInfo.AppSecret)
	stbz.SetConfig(stbzapi)
	return
}

func (*Stbz) InitGoods() (err error) {
	return
}

func (st *Stbz) GetGoods(info request.GetGoodsSearch) (err error, data interface{}, total int64, serverRatio int) {

	fmt.Println("page", info.Page, "limit", info.Limit, "source", info.Source)
	var search = make(map[string]interface{})
	search["page"] = info.Page
	search["limit"] = info.Limit
	search["source"] = info.Source

	if info.Source != nil {
		if *info.Source == 2 {
			search["goods_type"] = info.GoodsType
		}
	}
	if info.AgreementPrice.From > 0 {
		search["agreement_price"] = info.AgreementPrice
	}
	if info.GuidePrice.From > 0 {
		search["guide_price"] = info.GuidePrice
	}
	if info.ActivityPrice.From > 0 {
		search["activity_price"] = info.ActivityPrice
	}
	if info.PromotionRate.From > 0 {
		search["promotion_rate"] = info.PromotionRate
	}
	if info.Discount.From > 0 {
		search["discount"] = info.Discount
	}
	if info.GrossProfitRate.From > 0 {
		search["gross_profit_rate"] = info.GrossProfitRate
	}

	if info.CategoryID > 0 {
		search["category_id"] = info.CategoryID
	}
	if info.IsFreeShipping > 0 {
		search["is_free_shipping"] = info.IsFreeShipping
	}
	if info.SearchWords != "" {
		search["search_words"] = info.SearchWords
	}
	if info.Recommend > 0 {
		search["recommend"] = info.Recommend
	}
	var POSTURL common.RequestUrl
	POSTURL = common.GETGOODS
	if info.Recommend > 3 {
		POSTURL = common.GETAPIGOODSLIST
		search["group_id"] = info.Recommend

	}
	if info.ShopWords != "" {
		search["shop_words"] = info.ShopWords
	}
	if info.Type != "" {

		if info.Type == "sales" {
			search["type"] = "real_month_sale"
		} else {
			search["type"] = info.Type
		}

	}
	if info.Sort != "" {
		search["sort"] = info.Sort
	}

	if info.GroupID > 0 {
		search["group_id"] = info.GroupID
	}
	//ranges := make(map[string]uint)
	//ranges["from"] = info.RangeForm
	//ranges["to"] = info.RangeTo
	//if info.RangeType != "" {
	//	search[info.RangeType] = ranges
	//
	//}

	searchText, err := json.Marshal(search)
	fmt.Println("搜索条件:", string(searchText), "请求url", POSTURL)

	var result *stbz.APIResult
	result, err = stbz.API(
		stbz.Method.POST,
		string(POSTURL),
		map[string]string{},
		search,
	)
	if result.Code != 1 {
		err = errors.New(result.Msg)
		return
	}
	var supplyGoods []model.SupplyGoods
	source.DB().Where("gather_supply_id=?", st.Dat.GatherSupplyID).Find(&supplyGoods)

	var resultInt []int
	err = source.DB().Model(pmodel.Product{}).Where("gather_supply_id=?", st.Dat.GatherSupplyID).Pluck("source_goods_id", &resultInt).Error

	datas := result.Data.(map[string]interface{})
	var count float64 = datas["count"].(float64)
	fmt.Println("查询商品返回count:", count, info.Page, info.Limit)
	data = datas["list"]
	dataList := data.([]interface{})
	var List []interface{}
	for _, item := range dataList {
		maps := item.(map[string]interface{})
		id := maps["id"].(float64)
		sourceid := maps["source"].(float64)
		third_id := maps["third_id"].(float64)

		tId := strconv.Itoa(int(third_id))
		//var supplyGoods model.SupplyGoods
		//source.DB().Where("supply_goods_id=? and source=?", int64(id), int64(sourceid)).First(&supplyGoods)
		//if supplyGoods.ID > 0 {
		if IsImport(resultInt, int(id), int(sourceid)) > 0 {
			maps["is_import"] = 1
		}
		//fmt.Println(third_id)

		var url string
		if sourceid == 2 {
			url = "https://item.jd.com/" + tId + ".html"
		} else if sourceid == 7 {
			url = "https://item.taobao.com/item.htm?ft=t&id=" + tId

		} else if sourceid == 8 {
			url = "https://product.suning.com/0000000000/" + tId + ".html"

		}

		maps["third_url"] = url

		List = append(List, maps)
	}
	data = List
	total = int64(count)
	return
}

func IsImport(list []int, goodsId, source int) (is uint) {

	for _, item := range list {
		if item == goodsId {
			is = 1
			return
		}
	}

	return
}

func (*Stbz) ImportGoodsRun(info request.GetGoodsSearch) (err error, data interface{}) {
	var search = make(map[string]interface{})
	search["page"] = info.Page
	search["source"] = info.Source
	info.Limit = 50
	search["limit"] = info.Limit
	//search["search_words"] = "祖母"

	if info.AgreementPrice.From > 0 {
		search["agreement_price"] = info.AgreementPrice
	}
	if info.GuidePrice.From > 0 {
		search["guide_price"] = info.GuidePrice
	}
	if info.ActivityPrice.From > 0 {
		search["activity_price"] = info.ActivityPrice
	}
	if info.PromotionRate.From > 0 {
		search["promotion_rate"] = info.PromotionRate
	}
	if info.Discount.From > 0 {
		search["discount"] = info.Discount
	}
	if info.GrossProfitRate.From > 0 {
		search["gross_profit_rate"] = info.GrossProfitRate
	}

	if info.CategoryID > 0 {
		search["category_id"] = info.CategoryID
	}
	if info.IsFreeShipping > 0 {
		search["is_free_shipping"] = info.IsFreeShipping
	}
	if info.SearchWords != "" {
		search["search_words"] = info.SearchWords
	}
	if info.ShopWords != "" {
		search["shop_words"] = info.ShopWords
	}
	if info.Type != "" {
		search["type"] = info.Type
	}
	if info.Sort != "" {
		search["sort"] = info.Sort
	}

	if info.GroupID > 0 {
		search["group_id"] = info.GroupID
	}

	if info.Recommend > 0 {
		search["recommend"] = info.Recommend
	}
	var POSTURL common.RequestUrl
	POSTURL = common.GETGOODS
	if info.Recommend > 3 {
		POSTURL = common.GETAPIGOODSLIST
		search["group_id"] = info.Recommend

	}
	ranges := make(map[string]uint)
	ranges["from"] = info.RangeForm
	ranges["to"] = info.RangeTo
	if info.RangeType != "" {
		search[info.RangeType] = ranges

	}

	searchText, err := json.Marshal(search)
	fmt.Println("导入第一搜索条件", string(searchText))

	var result *stbz.APIResult
	result, err = stbz.API(
		stbz.Method.POST,
		string(POSTURL),
		map[string]string{},
		search,
	)
	if result.Code != 1 {
		color.Info.Println("供应链条件搜索商品错误", result, err)
		return
	}

	datas := result.Data.(map[string]interface{})
	var count = datas["count"].(float64)
	var limit = float64(20)
	var forCount = count / limit
	var counts = int(math.Ceil(forCount))
	fmt.Println("获取回来总数：", count, "计算总页数：", counts, info.Limit, info.Page)

	orderPN := GetOrderNo()
	goodsRecord := model.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: int(count),
		Status:            1,
		SearchCriteria:    string(searchText),
	}

	if count == 0 {
		return
	}
	source.DB().Omit("goods_arr").Create(&goodsRecord)
	var wg sync.WaitGroup
	for i := 1; i <= counts; i++ {
		wg.Add(1)
		if i >= 50 {
			break
		}
		//if i%20 == 0 {
		//	time.Sleep(time.Second * 1)
		//}
		RunGoodsConcurrent(&wg, info, i, search, orderPN)
	}

	//wg.Wait()
	err = SetImportRecordCompletion(orderPN)
	if err != nil {
		fmt.Println("变更导入记录状态错误", err)

	}
	fmt.Println("导入供应链商品全部完成")
	return
}

// 获取胜天半子分类数据
func (*Stbz) GetCategory(info request.GetCategorySearch) (err error, data interface{}) {
	if err != nil {
		return
	}
	category = nil
	info.Page = 1
	info.Limit = 200
	var result *stbz.APIResult
	result, err = stbz.API(
		stbz.Method.GET,
		string(common.GETCATE_GORY),
		map[string]string{},
		g.Map{"page": 1, "limit": 1, "source": info.Source},
	)
	if err != nil {
		return
	}
	datas := result.Data.(map[string]interface{})
	var count float64 = datas["count"].(float64)
	var forCount float64 = count / float64(info.Limit)
	var page = int(math.Ceil(forCount))
	res := make(map[string]interface{})
	res["count"] = count
	res["page"] = page

	data = res
	err, _ = ImportCategory(page, info, "stbz")
	return
}

func (*Stbz) GetGroup() (err error, data interface{}) {

	if err != nil {
		return
	}
	var result *stbz.APIResult
	result, err = stbz.API(
		stbz.Method.GET,
		string(common.GET_TAG_LIST),
		map[string]string{},
		g.Map{"page": 1, "limit": 100},
	)

	fmt.Println("返回的code", result.Code)

	if result.Code != 1 {
		err = errors.New(result.Msg)
		return
	}

	datas := result.Data.(map[string]interface{})

	data = datas["list"]

	return
}

func (*Stbz) GetCategoryChild(pid int, info request.GetCategoryChild) (err error, data interface{}) {
	var result *stbz.APIResult
	result, err = stbz.API(
		stbz.Method.GET,
		"/v2/Category/GetCategory",
		map[string]string{},
		g.Map{"parent_id": pid, "source": info.Source},
	)

	data = result.Data
	aaa, _ := json.Marshal(data)
	fmt.Println("GetGetCategoryChild：", string(aaa))
	//if  result.Data!=nil{
	//
	//}

	return
}

func RunGoodsConcurrent(wg *sync.WaitGroup, info request.GetGoodsSearch, i int, search map[string]interface{}, orderPN string) (err error) {

	log.Log().Info("improt", zap.Any("info", info), zap.Any("i", i), zap.Any("search", search))
	defer wg.Done()
	searchChild := make(map[string]interface{})

	searchChild["limit"] = info.Limit

	if info.CategoryID > 0 {
		searchChild["category_id"] = info.CategoryID
	}
	if info.IsFreeShipping > 0 {
		searchChild["is_free_shipping"] = info.IsFreeShipping
	}
	if info.SearchWords != "" {
		searchChild["search_words"] = info.SearchWords
	}
	if info.ShopWords != "" {
		searchChild["shop_words"] = info.ShopWords
	}
	if info.Type != "" {
		searchChild["type"] = info.Type
	}
	if info.Sort != "" {
		searchChild["sort"] = info.Sort
	}

	if info.Recommend > 0 {
		searchChild["recommend"] = info.Recommend
	}

	var POSTURL common.RequestUrl
	POSTURL = common.GETGOODS
	if info.Recommend > 3 {
		POSTURL = common.GETAPIGOODSLIST
		searchChild["group_id"] = info.Recommend

	}

	if info.GroupID > 0 {
		searchChild["group_id"] = info.GroupID
	}
	ranges := make(map[string]uint)
	ranges["from"] = info.RangeForm
	ranges["to"] = info.RangeTo
	if info.RangeType != "" {
		searchChild[info.RangeType] = ranges

	}
	searchChild["page"] = i
	searchChild["source"] = info.Source
	//testwhere, _ := json.Marshal(searchChild)
	//
	//fmt.Println("循环查询条件A", string(testwhere), info.Page)

	var result *stbz.APIResult
	result, err = stbz.API(
		stbz.Method.POST,
		string(POSTURL),
		map[string]string{},
		searchChild,
	)
	if result == nil || result.Data == nil {
		return
	}
	log.Log().Info("循环", zap.Any("Limit", info.Limit), zap.Any("i", i), zap.Any("search", search["page"]))

	if result.Data != nil {

		cateList := strings.Split(info.Categorys, ",")
		var cateId1, cateId2, cateId3 int
		if len(cateList) >= 2 {
			cateId1, err = strconv.Atoi(cateList[0])
			cateId2, err = strconv.Atoi(cateList[1])
			cateId3, err = strconv.Atoi(cateList[2])
		}
		//cateId1, err = strconv.Atoi(cateList[0])
		//cateId2, err = strconv.Atoi(cateList[1])
		//cateId3, err = strconv.Atoi(cateList[2])

		dataMap := result.Data.(map[string]interface{})

		var Item []model.Goods

		listJson, _ := json.Marshal(dataMap["list"])

		err = json.Unmarshal(listJson, &Item)

		var resultArr []int
		err = source.DB().Model(model.SupplyGoods{}).Where("gather_supply_id = ?", info.GatherSupplyID).Pluck("supply_goods_id", &resultArr).Error
		if err != nil {
			return
		}

		if len(Item) <= 0 {
			fmt.Println("没有选择可导入的数据")

			return
		}

		idsArr := GetIdArr(Item)

		difference := collection.Collect(idsArr).Diff(resultArr).ToIntArray()
		//fmt.Println("查询到的导入数据：", idsArr)
		//fmt.Println("已经存在的数据：", resultArr)
		//fmt.Println("目前可以导入的数据：", difference)

		err = SetImportRepeat(orderPN, len(idsArr)-len(difference))

		if len(difference) <= 0 {
			fmt.Println("没有可以导入的数据")
			err = source.DB().Model(model.SupplyGoodsImportRecord{}).Where("batch=?", orderPN).Update("status", 2).Error

			err = errors.New("当前数据已经存在，请导入新数据")
			return
		}
		var goodsList []model.Goods

		for _, v := range difference {

			for _, item := range Item {
				if item.ID == v {
					item.GatherSupplyID = info.GatherSupplyID
					goodsList = append(goodsList, item)
				}
			}

		}

		var listGoods []*pmodel.Product
		var stbz = Stbz{}
		err, listGoods, _ = stbz.CommodityAssembly(goodsList, cateId1, cateId2, cateId3, info.GatherSupplyID, 0)

		if len(listGoods) > 0 {
			service.FinalProcessing(listGoods, orderPN)
		}

	} else {
		fmt.Println("获取失败的：", i, info.Limit, info.Source)
	}

	return
}

// 商品组装
func (st *Stbz) CommodityAssembly(list []model.Goods, cateId1, cateId2, cateId3 int, gatherSupplyID uint, isUpdate int) (err error, listGoods []*pmodel.Product, recordErrors []model.SupplyGoodsImportRecordErrors) {

	if err != nil {
		return
	}
	idArr := GetIdArr(list)
	listArr := SplitArray(idArr, 20)
	var detailData = make(map[int]model.GoodsDetail)
	for index, item := range listArr {
		fmt.Println("循环分割id第", index, "次:", item)
		stringIds := GetArrIds(item)
		fmt.Println("返回ids", stringIds)
		var data map[int]model.GoodsDetail
		var stbz = Stbz{}
		err, data = stbz.BatchGetGoodsDetails(stringIds)
		for detailIndex, detailItem := range data {
			detailData[detailIndex] = detailItem

		}
	}
	var applicationSource []pservice.ApplicationSource
	source.DB().Find(&applicationSource)
	//_, detailData := BatchGetGoodsDetails(ids) //批量获取详情
	log.Log().Info("准备导入stbz list", zap.Any("info", list))
	log.Log().Info("准备导入stbz detailData", zap.Any("info", detailData))
	for _, elem := range list {

		if elem.Source == 2 && len(detailData[elem.ID].Specs.Options) > 1 && st.Dat.UpdateInfo.SplitSku == 1 { //京东的多规格商品 进行 拆分成单独的 多个商品
			var GoodsItem []*pmodel.Product
			err, GoodsItem = st.SplitSkuCommodityAssembly(elem, cateId1, cateId2, cateId3, gatherSupplyID, isUpdate)
			for _, GoodsItema := range GoodsItem {
				listGoods = append(listGoods, GoodsItema)

			}
			continue
		}
		log.Log().Info("准备导入stbz  1")

		var goodsIsExist pmodel.Product

		goodsIsExistErr := source.DB().Where("source_goods_id=? and gather_supply_id=?", elem.ID, elem.GatherSupplyID).First(&goodsIsExist).Error
		if goodsIsExistErr != nil {
			fmt.Println(goodsIsExistErr)
		}
		if goodsIsExist.ID > 0 {
			log.Log().Info("stbz 导入 已经存在商品", zap.Any("info", goodsIsExist.ID))
			continue
		}
		log.Log().Info("准备导入stbz  2")

		//
		//if elem.ID == 2857025 {
		//	fmt.Println("1111")
		//}
		goods := new(pmodel.Product)
		var brand = new(catemodel.Brand)
		if elem.ThirdBrandName != "" {
			brand.Name = elem.ThirdBrandName
			brand.Source = elem.Source
			err = source.DB().Where(brand).FirstOrCreate(&brand).Error
			goods.BrandID = brand.ID
		}
		var costPrice, salePrice, guidePrice uint

		err, costPrice, salePrice, guidePrice = GetPricingPrice(elem, "gatherSupply"+strconv.Itoa(int(gatherSupplyID)))

		if elem.Source == 17 {
			i, _ := strconv.Atoi(fmt.Sprintf("%1.0f", elem.SalePrice))
			elem.MarketPrice = uint(i)
			elem.ActivityPrice = uint(i)
			elem.GuidePrice = uint(i)
		}
		if detailData[elem.ID].Status == 0 {
			log.Log().Info("stbz 导入 商品下架", zap.Any("info", detailData[elem.ID]))

			continue
		}
		log.Log().Info("准备导入stbz 3")

		goods.Title = elem.Title
		goods.OriginPrice = elem.MarketPrice
		goods.Price = salePrice
		goods.CostPrice = costPrice
		goods.ActivityPrice = elem.ActivityPrice
		goods.GuidePrice = guidePrice
		goods.Stock = elem.Stock
		if dat.UpdateInfo.Sales == 1 {
			goods.Sales = elem.Sale

		}
		goods.IsDisplay = int(detailData[elem.ID].Status)
		goods.ImageUrl = elem.Cover
		goods.Unit = elem.Unit
		goods.SourceGoodsID = uint(elem.ID)
		goods.Source = elem.Source
		var isValuation = 0
		var name string
		for _, asItem := range applicationSource {
			if asItem.SourceID == uint(goods.Source) {
				if asItem.Title == "" {
					name = asItem.Name
				} else {
					name = asItem.Title
				}
				isValuation = 1
			}
		}
		if isValuation == 0 {
			name = pservice.GetMap(uint(goods.Source))
		}
		goods.SourceName = name
		if cateId1 == 0 && cateId2 == 0 && cateId3 == 0 {
			cateList := strings.Split(elem.ThirdCategoryName, ",")
			var cate1, cate2, cate3 catemodel.Category
			display := 1
			if len(cateList) > 0 && cateList[0] != "" {

				cate1.IsDisplay = &display
				cate1.ParentID = 0
				cate1.Level = 1
				cate1.Name = cateList[0]
				source.DB().Where("name=? and level=? and parent_id=?", cateList[0], 1, 0).FirstOrCreate(&cate1)
			}

			if len(cateList) > 1 && cateList[1] != "" {
				cate2.IsDisplay = &display
				cate2.ParentID = cate1.ID
				cate2.Level = 2
				cate2.Name = cateList[1]
				source.DB().Where("name=? and level=? and parent_id=?", cateList[1], 2, cate1.ID).FirstOrCreate(&cate2)
			}

			if len(cateList) > 2 && cateList[2] != "" {
				cate3.IsDisplay = &display
				cate3.ParentID = cate2.ID
				cate3.Level = 3
				cate3.Name = cateList[2]
				source.DB().Where("name=? and level=? and parent_id=?", cateList[2], 3, cate2.ID).FirstOrCreate(&cate3)
			}

			goods.Category1ID = cate1.ID
			goods.Category2ID = cate2.ID
			goods.Category3ID = cate3.ID
			if goods.Category3ID == 0 {
				cate3.IsDisplay = &display
				cate3.ParentID = cate2.ID
				cate3.Level = 3
				if len(cateList) > 1 {
					cate3.Name = cateList[1]
				} else {
					cate3.Name = cateList[0]
				}

				source.DB().Where("name=? and level=? and parent_id=?", cate3.Name, 3, cate2.ID).FirstOrCreate(&cate3)
				goods.Category3ID = cate3.ID
			}
		} else {
			goods.Category1ID = uint(cateId1)
			goods.Category2ID = uint(cateId2)
			goods.Category3ID = uint(cateId3)
		}

		goods.FreightType = 2
		goods.GatherSupplyID = elem.GatherSupplyID

		var sku = pmodel.Sku{}
		var skuList []pmodel.Sku

		detail := detailData[elem.ID]

		//log.Log().Info("导入数据的详情", zap.Any("info", detail))

		/**
		处理轮播图
		*/
		var galleryList pmodel.Gallery
		var galleryItem pmodel.GalleryItem
		for _, gItem := range detail.Covers {
			galleryItem.Type = 1
			galleryItem.Src = gItem
			galleryList = append(galleryList, galleryItem)
		}
		goods.Gallery = galleryList
		/**
		处理轮播图结束
		*/

		for _, skuItem := range detail.Specs.Options {

			if skuItem.Status == 1 {
				var options pmodel.Options
				var option pmodel.Option
				var skuTitle string
				var skutitle string

				specValueIds := strings.Split(skuItem.SpecValueIds, "_")
				for _, item := range specValueIds {
					nameId, valueName := GetSpecValue(item, detail.Specs.Values)
					specName := GetSpecName(nameId, detail.Specs.Names)
					option.SpecName = specName
					option.SpecItemName = valueName
					skuTitle = skuTitle + valueName
					skutitle = skutitle + valueName + "/"
					options = append(options, option)
				}

				//for index, specName := range detail.Specs.Names {
				//	svids := strings.Split(skuItem.SpecValueIds, "_")
				//	if len(svids) > index {
				//		valueName := GetKeyValueB(detail.Specs.Values, svids[index], specName.ID)
				//		if valueName != "" {
				//			option.SpecName = specName.Name
				//			option.SpecItemName = valueName
				//			skuTitle = skuTitle + valueName
				//			skutitle = skutitle + valueName + "/"
				//			options = append(options, option)
				//		}
				//	}
				//}

				if skuTitle == "" {
					continue
				}

				var skuElem model.Goods

				skuElem.CostPrice = skuItem.CostPrice
				skuElem.GuidePrice = skuItem.GuidePrice
				skuElem.SalePrice = skuItem.SalePrice
				skuElem.ActivityPrice = skuItem.ActivityPrice
				skuElem.AgreementPrice = skuItem.AgreementPrice
				skuElem.MarketPrice = skuItem.MarketPrice
				skuElem.Source = goods.Source
				err, costPrice, salePrice, guidePrice = GetPricingPrice(skuElem, "gatherSupply"+strconv.Itoa(int(gatherSupplyID)))

				if elem.Source == 17 {
					//i, _ := strconv.Atoi(fmt.Sprintf("%1.0f", skuItem.SalePrice))
					skuItem.MarketPrice = skuItem.SalePrice
					skuItem.ActivityPrice = skuItem.SalePrice
					skuItem.GuidePrice = skuItem.SalePrice
				}
				sku.Title = skuTitle
				sku.Options = options
				sku.Weight = skuItem.Weight
				sku.ImageUrl = skuItem.Image
				sku.CostPrice = costPrice
				sku.Stock = skuItem.Stock
				sku.IsDisplay = skuItem.Status
				sku.Price = salePrice
				sku.OriginPrice = skuItem.MarketPrice
				sku.GuidePrice = guidePrice
				sku.ActivityPrice = skuItem.ActivityPrice
				sku.OriginalSkuID = int64(skuItem.ID)
				if skuItem.OldGoodsID > 0 && goods.Source == 2 {
					_, skuDetail := st.GetGoodsDetails(skuItem.OldGoodsID)
					sku.Describe = skuDetail.Description
				}

				skuList = append(skuList, sku)

			}

		}

		max, min := GetSkuPrice(skuList)
		goods.MinPrice = min
		goods.MaxPrice = max

		goods.Skus = skuList

		if len(goods.Skus) == 0 {
			var options pmodel.Options
			var option pmodel.Option
			option.SpecName = "规格"
			option.SpecItemName = "默认"
			options = append(options, option)
			sku.Title = "默认"
			sku.Options = options
			sku.Weight = 0
			sku.CostPrice = goods.CostPrice
			sku.Stock = int(goods.Stock)
			sku.IsDisplay = goods.IsDisplay
			sku.Price = goods.Price
			sku.OriginPrice = goods.OriginPrice
			sku.GuidePrice = goods.GuidePrice
			sku.ActivityPrice = goods.ActivityPrice
			sku.OriginalSkuID = 0
			skuList = append(skuList, sku)
			goods.Skus = skuList

		}
		log.Log().Info("准备导入stbz 4")

		if len(goods.Skus) == 1 {
			if goods.Skus[0].Stock > 0 {
				goods.Stock = uint(goods.Skus[0].Stock)

			}
			goods.SingleOption = 1
		}
		itemSku := GetMap(goods.Skus)
		log.Log().Info("准备导入stbz 5", zap.Any("info", itemSku))
		log.Log().Info("准备导入stbz 6", zap.Any("info", goods.Skus))

		if itemSku.Price == 0 {
			log.Log().Info("stbz 导入 价格0", zap.Any("info", itemSku))

			continue
		}

		goods.CostPrice = itemSku.CostPrice
		goods.GuidePrice = itemSku.GuidePrice
		goods.OriginPrice = itemSku.OriginPrice
		goods.ActivityPrice = itemSku.ActivityPrice
		goods.Price = itemSku.Price
		//处理资质json图片数组
		var qualifications pmodel.Qualifications
		goods.Qualifications = qualifications
		goods.ProfitRate = utils.ExecProfitRate(goods.GuidePrice, goods.Price)

		goods.DetailImages = detail.Description
		//--------处理详情json图片数组结束

		//处----------------理属性json数组
		var attrList pmodel.Attrs
		var attr pmodel.Attr
		for _, attrItem := range detail.Attributes {
			attr.Name = attrItem.Name
			attr.Value = attrItem.Value
			attrList = append(attrList, attr)
		}

		goods.Attrs = attrList

		//---------处理属性json数组结束
		//goods.Desc=detail.Description

		if len(goods.Skus) > 0 {
			listGoods = append(listGoods, goods)
		} else {
			log.Log().Info("stbz 导入 规格0", zap.Any("info", goods.ID))

		}

	}

	return
}

// 商品拆规格组装

func (st *Stbz) SplitSkuCommodityAssembly(list model.Goods, cateId1, cateId2, cateId3 int, gatherSupplyID uint, isUpdate int) (err error, listGoods []*pmodel.Product) {

	var data model.GoodsDetail
	var stbz = Stbz{}
	err, data = stbz.GetGoodsDetails(uint(list.ID))

	detail := data
	var applicationSource []pservice.ApplicationSource
	source.DB().Find(&applicationSource)
	for _, skuItem := range detail.Specs.Options {
		var goodsIsExist pmodel.Product
		_ = source.DB().Where("source_goods_id=? and gather_supply_id=?", skuItem.OldGoodsID, list.GatherSupplyID).First(&goodsIsExist).Error

		if goodsIsExist.ID > 0 {
			return
		}

		goods := new(pmodel.Product)
		var brand = new(catemodel.Brand)
		if list.ThirdBrandName != "" {
			brand.Name = list.ThirdBrandName
			brand.Source = list.Source
			err = source.DB().Where(brand).FirstOrCreate(&brand).Error
			goods.BrandID = brand.ID
		}
		var costPrice, salePrice, guidePrice uint

		err, costPrice, salePrice, guidePrice = GetPricingPrice(list, "gatherSupply"+strconv.Itoa(int(gatherSupplyID)))

		if list.Source == 17 {
			i, _ := strconv.Atoi(fmt.Sprintf("%1.0f", list.SalePrice))
			list.MarketPrice = uint(i)
			list.ActivityPrice = uint(i)
			list.GuidePrice = uint(i)
		}
		if list.Status == 0 {
			continue
		}
		goods.Title = list.Title
		goods.OriginPrice = list.MarketPrice
		goods.Price = salePrice
		goods.CostPrice = costPrice
		goods.ActivityPrice = list.ActivityPrice
		goods.GuidePrice = guidePrice
		goods.Stock = list.Stock
		if dat.UpdateInfo.Sales == 1 {
			goods.Sales = list.Sale

		}
		goods.IsDisplay = int(detail.Status)
		goods.Unit = list.Unit
		goods.SourceGoodsID = uint(detail.ID)
		goods.Source = list.Source
		var isValuation = 0
		var name string
		for _, asItem := range applicationSource {
			if asItem.SourceID == uint(goods.Source) {
				if asItem.Title == "" {
					name = asItem.Name
				} else {
					name = asItem.Title
				}
				isValuation = 1
			}
		}
		if isValuation == 0 {
			name = pservice.GetMap(uint(goods.Source))
		}
		goods.SourceName = name
		if cateId1 == 0 && cateId2 == 0 && cateId3 == 0 {
			cateList := strings.Split(list.ThirdCategoryName, ",")
			var cate1, cate2, cate3 catemodel.Category
			display := 1
			if len(cateList) > 0 && cateList[0] != "" {

				cate1.IsDisplay = &display
				cate1.ParentID = 0
				cate1.Level = 1
				cate1.Name = cateList[0]
				source.DB().Where("name=? and level=? and parent_id=?", cateList[0], 1, 0).FirstOrCreate(&cate1)
			}

			if len(cateList) > 1 && cateList[1] != "" {
				cate2.IsDisplay = &display
				cate2.ParentID = cate1.ID
				cate2.Level = 2
				cate2.Name = cateList[1]
				source.DB().Where("name=? and level=? and parent_id=?", cateList[1], 2, cate1.ID).FirstOrCreate(&cate2)
			}

			if len(cateList) > 2 && cateList[2] != "" {
				cate3.IsDisplay = &display
				cate3.ParentID = cate2.ID
				cate3.Level = 3
				cate3.Name = cateList[2]
				source.DB().Where("name=? and level=? and parent_id=?", cateList[2], 3, cate2.ID).FirstOrCreate(&cate3)
			}

			goods.Category1ID = cate1.ID
			goods.Category2ID = cate2.ID
			goods.Category3ID = cate3.ID
			if goods.Category3ID == 0 {
				cate3.IsDisplay = &display
				cate3.ParentID = cate2.ID
				cate3.Level = 3
				if len(cateList) > 1 {
					cate3.Name = cateList[1]
				} else {
					cate3.Name = cateList[0]
				}

				source.DB().Where("name=? and level=? and parent_id=?", cate3.Name, 3, cate2.ID).FirstOrCreate(&cate3)
				goods.Category3ID = cate3.ID
			}
		} else {
			goods.Category1ID = uint(cateId1)
			goods.Category2ID = uint(cateId2)
			goods.Category3ID = uint(cateId3)
		}
		goods.SupplierSourceID = uint(detail.ID)

		goods.FreightType = 2
		goods.GatherSupplyID = list.GatherSupplyID

		var sku = pmodel.Sku{}
		var skuList []pmodel.Sku

		//log.Log().Info("导入数据的详情", zap.Any("info", detail))

		if skuItem.Status == 1 {
			var options pmodel.Options
			var option pmodel.Option
			var skuTitle string
			var skutitle string

			specValueIds := strings.Split(skuItem.SpecValueIds, "_")
			for _, item := range specValueIds {
				nameId, valueName := GetSpecValue(item, detail.Specs.Values)
				specName := GetSpecName(nameId, detail.Specs.Names)
				option.SpecName = specName
				option.SpecItemName = valueName
				skuTitle = skuTitle + valueName
				skutitle = skutitle + valueName + "/"
				options = append(options, option)
			}

			if skuTitle == "" {
				continue
			}

			var skuElem model.Goods

			skuElem.CostPrice = skuItem.CostPrice
			skuElem.GuidePrice = skuItem.GuidePrice
			skuElem.SalePrice = skuItem.SalePrice
			skuElem.ActivityPrice = skuItem.ActivityPrice
			skuElem.AgreementPrice = skuItem.AgreementPrice
			skuElem.MarketPrice = skuItem.MarketPrice
			skuElem.Source = goods.Source
			err, costPrice, salePrice, guidePrice = GetPricingPrice(skuElem, "gatherSupply"+strconv.Itoa(int(gatherSupplyID)))

			if list.Source == 17 {
				//i, _ := strconv.Atoi(fmt.Sprintf("%1.0f", skuItem.SalePrice))
				skuItem.MarketPrice = skuItem.SalePrice
				skuItem.ActivityPrice = skuItem.SalePrice
				skuItem.GuidePrice = skuItem.SalePrice
			}
			sku.Title = skuTitle
			sku.Options = options
			sku.Weight = skuItem.Weight
			sku.CostPrice = costPrice
			sku.Stock = skuItem.Stock
			sku.IsDisplay = skuItem.Status
			sku.Price = salePrice
			sku.OriginPrice = skuItem.MarketPrice
			sku.GuidePrice = guidePrice
			sku.ActivityPrice = skuItem.ActivityPrice
			sku.OriginalSkuID = int64(skuItem.ID)
			if skuItem.OldGoodsID > 0 && goods.Source == 2 {
				//_, skuDetail := st.GetGoodsDetails(skuItem.OldGoodsID)
				//sku.Describe = detail.Description
			}

			//goods.DetailImages = detail.Description
			skuList = append(skuList, sku)
			max, min := GetSkuPrice(skuList)
			goods.MinPrice = min
			goods.MaxPrice = max
			goods.Skus = skuList
			itemSku := GetMap(goods.Skus)
			if itemSku.Price == 0 {
				return
			}

			goods.CostPrice = itemSku.CostPrice
			goods.GuidePrice = itemSku.GuidePrice
			goods.OriginPrice = itemSku.OriginPrice
			goods.ActivityPrice = itemSku.ActivityPrice
			goods.Price = itemSku.Price
			//处理资质json图片数组
			var qualifications pmodel.Qualifications
			goods.Qualifications = qualifications
			goods.ProfitRate = utils.ExecProfitRate(goods.GuidePrice, goods.Price)
			goods.SingleOption = 1

			err, data = stbz.GetGoodsDetails(skuItem.OldGoodsID)
			goods.DetailImages = data.Description
			goods.Title = data.Title
			goods.ImageUrl = data.Cover

			/**
			处理轮播图
			*/
			var galleryList pmodel.Gallery
			var galleryItem pmodel.GalleryItem
			for _, gItem := range data.Covers {
				galleryItem.Type = 1
				galleryItem.Src = gItem
				galleryList = append(galleryList, galleryItem)
			}
			goods.Gallery = galleryList
			/**
			处理轮播图结束
			*/

			//--------处理详情json图片数组结束

			//处----------------理属性json数组
			var attrList pmodel.Attrs
			var attr pmodel.Attr
			for _, attrItem := range detail.Attributes {
				attr.Name = attrItem.Name
				attr.Value = attrItem.Value
				attrList = append(attrList, attr)
			}

			goods.Attrs = attrList
			//---------处理属性json数组结束

			if len(goods.Skus) > 0 {
				listGoods = append(listGoods, goods)
			}

		}

	}

	//}

	return
}

func GetMap(skuList []pmodel.Sku) (item pmodel.Sku) {

	var maxPrice float64
	for _, sku := range skuList {
		ProfitRate := Decimal((float64(sku.GuidePrice) - float64(sku.Price)) / float64(sku.GuidePrice) * 100)
		if ProfitRate > maxPrice {
			maxPrice = ProfitRate
			item = sku
		}
	}
	if len(skuList) > 0 && maxPrice == 0 {
		item = skuList[0]
	}

	return

}

func GetMapSku(skuList []pservice.Sku) (item pservice.Sku) {

	var maxPrice float64
	for _, sku := range skuList {
		ProfitRate := Decimal((float64(sku.GuidePrice) - float64(sku.Price)) / float64(sku.GuidePrice) * 100)
		if ProfitRate > maxPrice {
			maxPrice = ProfitRate
			item = sku
		}
	}
	return

}

func Decimal(value float64) float64 {
	var err error
	value, err = strconv.ParseFloat(fmt.Sprintf("%0.2f", value), 64)
	if err != nil {
		return 0
	}
	return value
}

// 单个获取商品详情
func (*Stbz) GetGoodsDetails(id uint) (err error, detail model.GoodsDetail) {

	//var detailList =make(map[int]model.GoodsDetail)

	var result *stbz.APIResult
	result, err = stbz.API(
		stbz.Method.GET,
		string(common.GET_GOODS_DETAIL),
		map[string]string{},
		g.Map{"id": id},
	)
	datas := result.Data

	if datas == "" || datas == nil {
		return
	}
	//var encodeJson []byte

	bf := bytes.NewBuffer([]byte{})

	jsonEncoder := json.NewEncoder(bf)

	jsonEncoder.SetEscapeHTML(false)

	jsonEncoder.Encode(datas)

	//encodeJson,err=json.Marshal(datas)
	stringJson := bf.String()
	//fmt.Println("返回的商品详情", stringJson)

	err = json.Unmarshal([]byte(stringJson), &detail)

	return
}

func (*Stbz) GetXPGroup() (err error, data interface{}) {

	var result1 *stbz.APIResult
	result1, err = stbz.API(
		stbz.Method.GET,
		string("/v2/GoodsGroup/ApiLists"),
		map[string]string{},
		g.Map{"page": 1, "limit": 100},
	)
	data = result1.Data
	return
}

// 批量获取商品详情
func (*Stbz) BatchGetGoodsDetails(ids string) (err error, data map[int]model.GoodsDetail) {

	//var result1 *stbz.APIResult
	//result1, err = stbz.API(
	//	stbz.Method.POST,
	//	string("/v2/GoodsGroup/ApiLists"),
	//	map[string]string{},
	//	g.Map{"page": 1, "limit": 100},
	//)
	//datasg := result1.Data
	//fmt.Println(datasg)
	//return

	var detailList = make(map[int]model.GoodsDetail)
	var result *stbz.APIResult
	result, err = stbz.API(
		stbz.Method.POST,
		string(common.GET_GOODS_DETAILS),
		map[string]string{},
		g.Map{"ids": ids},
	)
	datas := result.Data

	if datas == "" || datas == nil {
		return
	}
	bf := bytes.NewBuffer([]byte{})
	jsonEncoder := json.NewEncoder(bf)
	jsonEncoder.SetEscapeHTML(false)
	err = jsonEncoder.Encode(datas)
	stringJson := bf.String()
	var detail []model.GoodsDetail
	err = json.Unmarshal([]byte(stringJson), &detail)
	for _, item := range detail {
		detailList[item.ID] = item
	}

	//log.Log().Info("infi", zap.Any("infi", detailList))

	data = detailList

	return
}

// 批量获取商品详情
func (*Stbz) BatchGetGoodsDetailList(ids string) (err error, data []model.GoodsDetail) {

	//var detailList = make(map[int]model.GoodsDetail)
	var result *stbz.APIResult
	result, err = stbz.API(
		stbz.Method.POST,
		string(common.GET_GOODS_DETAILS),
		map[string]string{},
		g.Map{"ids": ids},
	)
	datas := result.Data

	if datas == "" || datas == nil {
		return
	}
	bf := bytes.NewBuffer([]byte{})
	jsonEncoder := json.NewEncoder(bf)
	jsonEncoder.SetEscapeHTML(false)
	err = jsonEncoder.Encode(datas)
	stringJson := bf.String()
	//var detail []model.GoodsDetail
	err = json.Unmarshal([]byte(stringJson), &data)
	//for _, item := range detail {
	//	detailList[item.ID] = item
	//}

	//log.Log().Info("infi", zap.Any("infi", detailList))

	//data = detailList

	return
}

func (*Stbz) RunConcurrent(wg *sync.WaitGroup, info request.GetCategorySearch, i int) (err error) {

	defer wg.Done()
	var result *stbz.APIResult
	result, err = stbz.API(
		1,
		"/v2/Category/Lists",
		map[string]string{},
		g.Map{"page": i, "limit": info.Limit, "source": info.Source},
	)
	fmt.Println("循环：", i, info.Limit, info.Source)
	if result.Data != nil {
		datas := result.Data.(map[string]interface{})
		var cateItem []model.Category
		cateJson := datas["data"]
		mJson, _ := json.Marshal(cateJson)
		stringJson := string(mJson)
		err = json.Unmarshal([]byte(stringJson), &cateItem)
		mutex.Lock()
		if len(cateItem) > 0 {
			category = append(category, cateItem...)
		}
		mutex.Unlock()

	} else {
		fmt.Println("获取失败的：", i, info.Limit, info.Source)
	}

	return
}

// 选品库增加商品
func (*Stbz) GoodsStorageAdd(ids []int, supplyId uint) (err error, info interface{}) {

	var strArr []string
	for _, item := range ids {
		strArr = append(strArr, strconv.Itoa(item))
	}

	idItem := strings.Join(strArr, ",")

	var result *stbz.APIResult
	result, err = stbz.API(
		stbz.Method.GET,
		string(common.GOODSSTORAGE),
		map[string]string{},
		g.Map{"goods_ids": idItem},
	)
	if err != nil {
		return
	}

	info = result

	fmt.Println("选品库增加商品:", result)
	if result.Code == 1 {
		for _, item := range ids {

			err = source.DB().Where(model.GoodsStorage{SourceGoodsID: uint(item), SupplyID: supplyId}).FirstOrCreate(&model.GoodsStorage{SourceGoodsID: uint(item), SupplyID: supplyId}).Error
			if err != nil {
				log.Log().Error("供应链选品入库错误", zap.Any("info", info))
			}
		}
	}

	return
}

func (st *Stbz) GoodsPriceAlert(GoodsData callback2.GoodsCallBack) (err error) {

	var ids = GoodsData.Data.GoodsIds
	if len(ids) == 0 {
		return
	}
	var applicationSource []pservice.ApplicationSource
	source.DB().Find(&applicationSource)
	var data map[int]model.GoodsDetail
	stringIds := GetIds(ids)
	for _, item := range ids {
		var product pservice.ProductForUpdate

		err = source.DB().Where("status_lock =0 and source_goods_id =  ? and source in (?)", item, GoodsData.SupplierSource).First(&product).Error
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			fmt.Println("回调查询商品不存在")
			return
		}
		var stbz = Stbz{}
		err = stbz.InitSetting(product.GatherSupplyID)
		err, data = stbz.BatchGetGoodsDetails(stringIds)
		_, setting := gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(product.GatherSupplyID)))
		err = json.Unmarshal([]byte(setting.Value), &dat)
		break
	}

	for _, item := range ids {
		var product pservice.ProductForUpdate

		err = source.DB().Where("source_goods_id =  ? and source in (?)", item, GoodsData.SupplierSource).Preload("Skus").First(&product).Error
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			fmt.Println("回调查询商品不存在")
			continue
		}

		var goods model.Goods
		if data[item].CostPrice <= 0 {
			continue
		}

		goods.CostPrice = data[item].CostPrice
		goods.GuidePrice = data[item].GuidePrice
		goods.SalePrice = data[item].SalePrice
		goods.ActivityPrice = data[item].ActivityPrice
		goods.AgreementPrice = data[item].AgreementPrice
		goods.MarketPrice = data[item].MarketPrice
		goods.Source = data[item].Source
		var pcostPrice, psalePrice, pguidePrice uint
		err, pcostPrice, psalePrice, pguidePrice = GetPricingPrice(goods, "gatherSupply"+strconv.Itoa(int(product.GatherSupplyID)))

		fmt.Println(pguidePrice)
		if dat.UpdateInfo.CostPrice == 1 {
			product.CostPrice = pcostPrice
		}
		if dat.UpdateInfo.CurrentPrice == 1 {
			product.Price = psalePrice
		}
		//dat.UpdateInfo.CateGory
		if dat.UpdateInfo.BaseInfo == 1 {

			product.OriginPrice = data[item].MarketPrice
			product.GuidePrice = data[item].GuidePrice
			product.ActivityPrice = data[item].SalePrice
			var isValuation = 0
			var name string
			for _, asItem := range applicationSource {
				if asItem.SourceID == uint(product.Source) {
					if asItem.Title == "" {
						name = asItem.Name
					} else {
						name = asItem.Title
					}
					isValuation = 1
				}
			}
			if isValuation == 0 {
				name = pservice.GetMap(uint(product.Source))
			}
			product.SourceName = name
			//product.Title=data[item].
		}
		var skuList []pservice.Sku
		for _, skuItem := range data[item].Specs.Options {
			var sku pservice.Sku

			source.DB().Where("original_sku_id=? and product_id=?", skuItem.ID, product.ID).First(&sku)

			if skuItem.Status == 1 {
				var options pmodel.Options
				var option pmodel.Option
				var skuTitle string
				var skutitle string
				for index, specName := range data[item].Specs.Names {
					svids := strings.Split(skuItem.SpecValueIds, "_")
					if len(svids) > index {
						valueName := GetKeyValueB(data[item].Specs.Values, svids[index], specName.ID)
						if valueName != "" {
							option.SpecName = specName.Name
							option.SpecItemName = valueName
							skuTitle = skuTitle + valueName
							skutitle = skutitle + valueName + "/"
							options = append(options, option)
						}
					}
				}

				if skuTitle == "" {
					continue
				}

				var skuElem model.Goods

				skuElem.Source = data[item].Source
				skuElem.CostPrice = skuItem.CostPrice
				skuElem.GuidePrice = skuItem.GuidePrice
				skuElem.SalePrice = skuItem.SalePrice
				skuElem.ActivityPrice = skuItem.ActivityPrice
				skuElem.AgreementPrice = skuItem.AgreementPrice
				skuElem.MarketPrice = skuItem.MarketPrice
				var costPrice, salePrice, guidePrice uint
				err, costPrice, salePrice, guidePrice = GetPricingPrice(skuElem, "gatherSupply"+strconv.Itoa(int(product.GatherSupplyID)))

				sku.Title = skuTitle
				sku.Options = options
				sku.Weight = skuItem.Weight
				if sku.ProductID == 0 {
					sku.ProductID = product.ID
				}

				sku.CostPrice = costPrice
				sku.Stock = skuItem.Stock
				sku.IsDisplay = skuItem.Status
				sku.Price = salePrice
				sku.OriginPrice = skuItem.MarketPrice
				sku.GuidePrice = guidePrice
				sku.ActivityPrice = skuItem.ActivityPrice
				sku.OriginalSkuID = skuItem.ID

				skuList = append(skuList, sku)

			}

		}
		product.Skus = skuList
		if len(product.Skus) == 1 {
			product.SingleOption = 1
		}
		if product.Price == 0 {
			return
		}

		itemSku := GetMapSku(product.Skus)
		if itemSku.Price == 0 {
			continue
		}

		product.CostPrice = itemSku.CostPrice
		product.GuidePrice = itemSku.GuidePrice
		product.OriginPrice = itemSku.OriginPrice
		product.ActivityPrice = itemSku.ActivityPrice
		product.Price = itemSku.Price
		product.ProfitRate = Decimal((float64(product.GuidePrice) - float64(product.Price)) / float64(product.GuidePrice) * 100)

		//log.Log().Debug("stbz修改商品", zap.Any("id", product.ID))
		err = pservice.UpdateProduct(product)
		if err != nil {
			log.Log().Error("供应商商品回调修改失败", zap.Any("err", err))
			log.Log().Error("供应商商品回调修改失败", zap.Any("product", product))
			return
		}

		err = callback2.DeleteGoodsCallBackMsg(GoodsData.Type, GoodsData.MsgID, item)
		if err != nil {
			log.Log().Error("胜天半子订单发货完成删除记录err", zap.Any("info", err))
		}

	}

	return
}

func (st *Stbz) GoodsAlert(GoodsData callback2.GoodsCallBack) (err error) {

	var ids = GoodsData.Data.GoodsIds
	if len(ids) == 0 {
		return
	}
	var applicationSource []pservice.ApplicationSource
	source.DB().Find(&applicationSource)
	var data map[int]model.GoodsDetail
	stringIds := GetIds(ids)
	for _, item := range ids {
		var product pservice.ProductForUpdate
		err = source.DB().Where("source_goods_id =  ?", item).Preload("Skus").First(&product).Error
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			log.Log().Error("回调查询商品不存在", zap.Any("err", err))
			return
		}
		var stbz = Stbz{}
		err = stbz.InitSetting(product.GatherSupplyID)
		err, data = stbz.BatchGetGoodsDetails(stringIds)
		_, setting := gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(product.GatherSupplyID)))
		err = json.Unmarshal([]byte(setting.Value), &dat)
		if err != nil {
			log.Log().Error("GoodsAlert解析失败", zap.Any("err", err))
			return
		}
		break
	}

	for _, item := range ids {
		var product pservice.ProductForUpdate

		err = source.DB().Where("source_goods_id =  ?", item).Preload("Skus").First(&product).Error
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			fmt.Println("回调查询商品不存在")
			continue
		}

		var goods model.Goods

		goods.CostPrice = data[item].CostPrice
		goods.GuidePrice = data[item].GuidePrice
		goods.SalePrice = data[item].SalePrice
		goods.ActivityPrice = data[item].ActivityPrice
		goods.AgreementPrice = data[item].AgreementPrice
		goods.MarketPrice = data[item].MarketPrice
		goods.Source = data[item].Source
		var pcostPrice, psalePrice, pguidePrice uint
		err, pcostPrice, psalePrice, pguidePrice = GetPricingPrice(goods, "gatherSupply"+strconv.Itoa(int(product.GatherSupplyID)))

		fmt.Println(pguidePrice)
		if dat.UpdateInfo.CreateBrand == 1 {
			var brand = new(catemodel.Brand)
			if data[item].ThirdBrandName != "" {
				brand.Name = data[item].ThirdBrandName
				brand.Source = data[item].Source
				err = source.DB().Where(brand).FirstOrCreate(&brand).Error
				product.BrandID = brand.ID
			}
		}

		product.OriginPrice = data[item].MarketPrice
		product.CostPrice = pcostPrice
		product.Price = psalePrice
		product.Source = data[item].Source
		product.GuidePrice = data[item].GuidePrice
		//product.AgreementPrice = data[item].AgreementPrice
		product.ActivityPrice = data[item].SalePrice
		product.Title = data[item].Title
		product.Stock = data[item].Stock
		product.Sales = data[item].Sale
		product.ImageUrl = data[item].Cover
		product.Unit = data[item].Unit
		product.DetailImages = data[item].Description
		var isValuation = 0
		var name string
		for _, asItem := range applicationSource {
			if asItem.SourceID == uint(product.Source) {
				if asItem.Title == "" {
					name = asItem.Name
				} else {
					name = asItem.Title
				}
				isValuation = 1
			}
		}
		if isValuation == 0 {
			name = pservice.GetMap(uint(product.Source))
		}
		product.SourceName = name
		var skuList []pservice.Sku
		for _, skuItem := range data[item].Specs.Options {
			var sku pservice.Sku

			source.DB().Where("original_sku_id=? and product_id=?", skuItem.ID, product.ID).First(&sku)

			if skuItem.Status == 1 {
				var options pmodel.Options
				var option pmodel.Option
				var skuTitle string
				var skutitle string
				for index, specName := range data[item].Specs.Names {
					svids := strings.Split(skuItem.SpecValueIds, "_")
					if len(svids) > index {
						valueName := GetKeyValueB(data[item].Specs.Values, svids[index], specName.ID)
						if valueName != "" {
							option.SpecName = specName.Name
							option.SpecItemName = valueName
							skuTitle = skuTitle + valueName
							skutitle = skutitle + valueName + "/"
							options = append(options, option)
						}
					}
				}

				if skuTitle == "" {
					continue
				}

				var skuElem model.Goods

				skuElem.Source = data[item].Source
				skuElem.CostPrice = skuItem.CostPrice
				skuElem.GuidePrice = skuItem.GuidePrice
				skuElem.SalePrice = skuItem.SalePrice
				skuElem.ActivityPrice = skuItem.ActivityPrice
				skuElem.AgreementPrice = skuItem.AgreementPrice
				skuElem.MarketPrice = skuItem.MarketPrice
				var costPrice, salePrice, guidePrice uint
				err, costPrice, salePrice, guidePrice = GetPricingPrice(skuElem, "gatherSupply"+strconv.Itoa(int(product.GatherSupplyID)))

				sku.Title = skuTitle
				sku.Options = options
				sku.Weight = skuItem.Weight
				if sku.ProductID == 0 {
					sku.ProductID = product.ID
				}

				sku.CostPrice = costPrice
				sku.Stock = skuItem.Stock
				sku.IsDisplay = skuItem.Status
				sku.Price = salePrice
				sku.OriginPrice = skuItem.MarketPrice
				sku.GuidePrice = guidePrice
				sku.ActivityPrice = skuItem.ActivityPrice
				sku.OriginalSkuID = skuItem.ID

				if skuItem.OldGoodsID > 0 && product.Source == 2 {
					_, skuDetail := st.GetGoodsDetails(skuItem.OldGoodsID)
					sku.Describe = skuDetail.Description
				}

				skuList = append(skuList, sku)

			}

		}

		max, min := UpdasteGetSkuPrice(skuList)
		product.MinPrice = min
		product.MaxPrice = max
		product.Skus = skuList
		if len(product.Skus) == 1 {
			product.SingleOption = 1
		}

		itemSku := GetMapSku(product.Skus)
		if itemSku.Price == 0 {
			continue
		}

		product.CostPrice = itemSku.CostPrice
		product.GuidePrice = itemSku.GuidePrice
		product.OriginPrice = itemSku.OriginPrice
		product.ActivityPrice = itemSku.ActivityPrice
		product.Price = itemSku.Price
		//log.Log().Debug("stbz修改商品", zap.Any("id", product.ID))

		err = pservice.UpdateProduct(product)
		if err != nil {
			log.Log().Error("胜天半子订商品修改失败", zap.Any("err", err))
			log.Log().Error("供胜天半子订商品修改失败", zap.Any("product", product))
			return
		}

		err = callback2.DeleteGoodsMsg(GoodsData.MsgID)
		if err != nil {
			log.Log().Error("胜天半子订商品修改err", zap.Any("info", err))
		}

	}

	return
}
