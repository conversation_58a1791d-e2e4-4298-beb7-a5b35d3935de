package order

//
//import (
//	"fmt"
//	"gather-supply/request"
//	"testing"
//)
//
//func TestYzh_ImportGoodsRun(t *testing.T) {
//
//	y := &Yzh{}
//	//var info request.GetGoodsSearch
//	y.InitSetting(7)
//
//	//y.OrderDetail("7075355251")
//
//	var requestv request.RequestExpress
//	requestv.OrderSn = "7075355251"
//	y.ExpressQuery(requestv)
//
//	return
//
//	//	y.OrderDetail("6420306427")
//	var requesta request.RequestSaleBeforeCheck
//	requesta.Skus = append(requesta.Skus, request.GoodsSpu{
//		request.Sku{
//			Sku: 563784,
//		},
//		1,
//	})
//
//	var nnna request.ReceivingInformation
//
//	nnna.Phone = "18686794695"
//	nnna.Consignee = "测试人"
//	nnna.Description = "文峰北路1781号三鼎大厦15楼"
//	nnna.Province = "河南省"
//	nnna.City = "许昌市"
//	nnna.Area = "魏都区"
//	nnna.Street = "文峰街道"
//	requesta.Address = nnna
//
//	err, bbbb := y.OrderBeforeCheck(requesta)
//
//	fmt.Println(bbbb.Code, err)
//
//	return
//
//	//y.ExpressQuery("6409731791")
//	//y.OrderDetail("6409731791")
//	//AA123142353451
//	//6409731791
//	//
//	//	return
//
//	//y.GetAreaCodeByAddress("黑龙江省哈尔滨市南岗区学府经典中天城")
//	//
//	//return
//
//	var requestParam request.RequestConfirmOrder
//	var goods []request.GoodsSpu
//
//	goods = append(goods, request.GoodsSpu{
//		Sku:    request.Sku{Sku: 563615},
//		Number: 1,
//	})
//	//goods = append(goods, request.GoodsSpu{
//	//	Sku:    request.Sku{Sku: 563617},
//	//	Number: 3,
//	//})
//	//goods = append(goods, request.GoodsSpu{
//	//	Sku:    request.Sku{Sku: 563505},
//	//	Number: 4,
//	//})
//	//goods = append(goods, request.GoodsSpu{
//	//	Sku:    request.Sku{Sku: 563514},
//	//	Number: 4,
//	//})
//
//	requestParam.OrderSn.OrderSn = "AA12314235345222"
//	requestParam.Skus = goods
//
//	var nnn request.ReceivingInformation
//
//	nnn.Phone = "18686794695"
//	nnn.Consignee = "测试人"
//	nnn.Description = "黑龙江省哈尔滨市南岗区学府路1号"
//	nnn.Province = "黑龙江省"
//	nnn.City = "哈尔滨市"
//	nnn.Area = "南岗区"
//	nnn.Street = ""
//	requestParam.Address = nnn
//	//request.Skus
//
//	y.ConfirmOrder(requestParam)
//	//y.GetYzhCategoryListAll()
//
//	//time.Sleep(time.Second * 50)
//
//	fmt.Println("wanhceng")
//	//y.ImportGoodsRun(info)
//	//
//	//aaan := []uint{}
//	//_, dasta := y.GetCategoryDetail(1026, aaan)
//	//fmt.Println("aaaaaaa", dasta)
//
//}
