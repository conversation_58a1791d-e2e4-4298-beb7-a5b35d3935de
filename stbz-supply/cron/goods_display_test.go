package cron

import (
	"public-supply/request"
	"stbz-supply/component/order"
	"strconv"
	"testing"
)

func TestGoodsSaleCheck(t *testing.T) {

	//OrderDeliverCron(66)
	//
	//return
	var stbz order.Stbz

	stbz.InitSetting(76)

	var requestExpress request.RequestExpress

	requestExpress.OrderSn = strconv.Itoa(int(192243361248))
	requestExpress.Sku = strconv.Itoa(int(411642256))

	_, _ = stbz.ExpressQuery(requestExpress)

	//
	//return
	//fmt.Println()

	GoodsAlertPriceCronTask(1)
}
