package v1

import (
	v1 "gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"supplier/model"
	"supplier/request"
	"supplier/service"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
)

// @Tags 岗位
// @Summary 创建
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SupplierJob true "创建"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /adminSupplier/createSupplierJob [post]
func CreateSupplierJob(c *gin.Context) {
	var supplierJob model.SupplierJob
	var err error
	err = c.ShouldBindJSON(&supplierJob)
	if err != nil{
	    yzResponse.FailWithMessage(err.Error(), c)
	    return
	}
	serr, supplier := service.GetSupplierByUserId(v1.GetUserID(c))
	if serr != nil {
		yzResponse.FailWithMessage("供应商信息查询失败", c)
	}
	supplierJob.Sid = int(supplier.ID)
	if err := service.CreateSupplierJob(supplierJob); err != nil {
        log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// @Tags 岗位
// @Summary 删除
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SupplierJob true "删除"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /adminSupplier/deleteSupplierJob [post]
func DeleteSupplierJob(c *gin.Context) {
	var supplierJob model.SupplierJob
	var err error
    err = c.ShouldBindJSON(&supplierJob)
    if err != nil{
        yzResponse.FailWithMessage(err.Error(), c)
        return
    }
	if err := service.DeleteSupplierJob(supplierJob); err != nil {
        log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// @Tags 岗位
// @Summary 批量删除
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.IdsReq true "批量删除"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /adminSupplier/deleteSupplierJobByIds [post]
func DeleteSupplierJobByIds(c *gin.Context) {
	var IDS yzRequest.IdsReq
    var err error
    err = c.ShouldBindJSON(&IDS)
    if err != nil{
        yzResponse.FailWithMessage(err.Error(), c)
        return
    }
	if err := service.DeleteSupplierJobByIds(IDS); err != nil {
        log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	} else {
		yzResponse.OkWithMessage("批量删除成功", c)
	}
}

// @Tags 岗位
// @Summary 更新
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SupplierJob true "更新"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /adminSupplier/updateSupplierJob [post]
func UpdateSupplierJob(c *gin.Context) {
	var supplierJob model.SupplierJob
	var err error
    err = c.ShouldBindJSON(&supplierJob)
    if err != nil{
        yzResponse.FailWithMessage(err.Error(), c)
        return
    }
	if err := service.UpdateSupplierJob(supplierJob); err != nil {
        log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

// @Tags 岗位
// @Summary 用id查询
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SupplierJob true "用id查询"
// @Success 200 {object} model.SupplierJob
// @Router /adminSupplier/findSupplierJob [get]
func FindSupplierJob(c *gin.Context) {
	var supplierJob model.SupplierJob
	var err error
    err = c.ShouldBindQuery(&supplierJob)
    if err != nil{
        yzResponse.FailWithMessage(err.Error(), c)
        return
    }

	if err, resupplierJob := service.GetSupplierJob(supplierJob.ID); err != nil {
        log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"resupplierJob": resupplierJob}, c)
	}
}

// @Tags 岗位
// @Summary 分页获取列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.SupplierJobSearch true "分页获取列表"
// @Success 200 {string} string []model.SupplierJob
// @Router /adminSupplier/getSupplierJobList [get]
func GetSupplierJobList(c *gin.Context) {
	var pageInfo request.SupplierJobSearch
	var err error
    err = c.ShouldBindQuery(&pageInfo)
    if err != nil{
        yzResponse.FailWithMessage(err.Error(), c)
        return
    }
	serr, supplier := service.GetSupplierByUserId(v1.GetUserID(c))
	if serr != nil {
		yzResponse.FailWithMessage("供应商信息查询失败", c)
	}
	pageInfo.Sid = int(supplier.ID)
	if err, list, total := service.GetSupplierJobInfoList(pageInfo); err != nil {
	    log.Log().Error("获取失败", zap.Any("err", err))
       yzResponse.FailWithMessage(err.Error(), c)
    } else {
        yzResponse.OkWithDetailed(yzResponse.PageResult{
            List:     list,
            Total:    total,
            Page:     pageInfo.Page,
            PageSize: pageInfo.PageSize,
        }, "获取成功", c)
    }
}

// @Tags 岗位
// @Summary 获取菜单列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string []model.Menu
// @Router /adminSupplier/getInitJobMenus [get]
func GetInitJobMenus(c *gin.Context) {
	list := service.GetInitJobMenus()
	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List:     list,
	}, "获取成功", c)
}

// @Tags 岗位
// @Summary 获取菜单接口列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string []model.Api
// @Router /adminSupplier/GetInitJobApis [get]
func GetInitJobApis(c *gin.Context) {
	if err, list := service.GetInitJobApis(); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
		}, "获取成功", c)
	}
}
