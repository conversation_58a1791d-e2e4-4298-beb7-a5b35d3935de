package v1

import (
	request2 "finance/request"
	v1 "gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
	"strings"
	"supplier/cron"
	"supplier/model"
	"supplier/request"
	"supplier/service"
	utils2 "supplier/utils"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	service2 "yz-go/service"
	"yz-go/utils"
)

// @Tags Supplier
// @Summary 创建Supplier
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SaveSupplier true "创建Supplier"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /supplier/createSupplier [post]
func CreateSupplier(c *gin.Context) {
	var supplier model.SaveSupplier
	err := c.ShouldBindJSON(&supplier)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.CreateSupplier(supplier); err != nil {
		//log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 4, c.ClientIP(), "新增供应商'"+supplier.Name+"'")
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// @Tags Supplier
// @Summary 删除Supplier
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SaveSupplier true "删除Supplier"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /supplier/deleteSupplier [delete]
func DeleteSupplier(c *gin.Context) {
	var supplier model.SaveSupplier
	err := c.ShouldBindJSON(&supplier)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteSupplier(supplier, v1.GetUserID(c), c.ClientIP()); err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("冻结失败", c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 4, c.ClientIP(), "冻结供应商'"+supplier.Name+"'")
		yzResponse.OkWithMessage("操作成功", c)
	}
}

// @Tags Supplier
// @Summary 批量删除Supplier
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.IdsReq true "批量删除Supplier"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /supplier/deleteSupplierByIds [delete]
func DeleteSupplierByIds(c *gin.Context) {
	var IDS yzRequest.IdsReq
	var err error
	err = c.ShouldBindJSON(&IDS)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteSupplierByIds(IDS); err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	} else {
		var idsString []string
		for _, v := range IDS.Ids {
			idsString = append(idsString, strconv.Itoa(int(v)))
		}
		service2.CreateOperationRecord(v1.GetUserID(c), 4, c.ClientIP(), "批量删除供应商'"+strings.Join(idsString, ",")+"'")
		yzResponse.OkWithMessage("批量删除成功", c)
	}
}

// @Tags Supplier
// @Summary 更新Supplier
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SaveSupplier true "更新Supplier"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /supplier/updateSupplier [put]
func UpdateSupplier(c *gin.Context) {
	var supplier model.SaveSupplier
	err := c.ShouldBindJSON(&supplier)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateSupplier(supplier); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 4, c.ClientIP(), "编辑供应商"+supplier.Name)
		yzResponse.OkWithMessage("更新成功", c)
	}
}

// @Tags Supplier
// @Summary 用id查询Supplier
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SaveSupplier true "用id查询Supplier"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /supplier/findSupplier [get]
func FindSupplier(c *gin.Context) {
	var supplier model.SaveSupplier
	_ = c.ShouldBindQuery(&supplier)
	if err, resupplier := service.GetSupplier(supplier.ID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"resupplier": resupplier}, c)
	}
}

// @Tags Supplier
// @Summary 分页获取Supplier列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.SupplierSearch true "分页获取Supplier列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /supplier/getSupplierList [get]
func GetSupplierList(c *gin.Context) {
	var pageInfo request.SupplierSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetSupplierInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// @Tags Supplier
// @Summary 获取SupplierCategory列表(无分页)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.SupplierSearch true "获取SupplierCategory列表(无分页)"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /supplier/getSupplierCategoryListNotPage [get]
func GetSupplierCategoryListNotPage(c *gin.Context) {
	if err, list := service.GetSupplierCategoryInfoListNotPage(); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List: list,
		}, "获取成功", c)
	}
}

// @Tags Supplier
// @Summary 分页获取Supplier列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.SupplierSearch true "分页获取Supplier列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /supplier/getSupplierList [get]
func GetSupplierOptionList(c *gin.Context) {
	var pageInfo request.SupplierSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list := service.GetSupplierOptionList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List: list,
		}, "获取成功", c)
	}
}

func ExportSettlement(c *gin.Context) {
	var err error
	var pageInfo request2.AccountApplySearch
	err = c.ShouldBind(&pageInfo)
	if err != nil {
		log.Log().Error("解析失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var link string

	err, supplier := service.GetSupplierId(v1.GetUserID(c))
	pageInfo.SupplierID = supplier.ID
	if err, link = service.ExportSettlement(pageInfo); err != nil {
		log.Log().Error("处理失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}

func ChangePassword(c *gin.Context) {
	var req request.ChangePassword
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := utils.GVerify(req, utils2.ChangeSupplierPasswordVerify); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var supplier model.SaveSupplier
	err, supplier = service.GetSupplier(req.ID)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.ChangePassword(req, supplier.UserId); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 4, c.ClientIP(), supplier.Name+"修改密码")
		yzResponse.OkWithMessage("修改密码成功", c)
	}
}

func Statistic(c *gin.Context) {
	cron.Statistic()
}

func GetSuppliers(c *gin.Context) {
	var pageInfo request.SupplierSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, suppliers := service.GetSupplierList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(suppliers, c)
	}
}
