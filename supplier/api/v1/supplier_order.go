package v1

import (
	v1 "gin-vue-admin/admin/api/v1"
	"gin-vue-admin/admin/model"
	response2 "gin-vue-admin/admin/model/response"
	orderModel "order/model"
	service2 "order/service"
	"strconv"
	yzRequest "yz-go/request"
	service3 "yz-go/service"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	orequest "order/request"
	"supplier/request"
	"supplier/service"
	"yz-go/component/log"
	yzResponse "yz-go/response"
)

// FindOrder
// @Tags 订单
// @Summary 用id查询
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Order true "用id查询"
// @Success 200 {object} service.OrderDetail
// @Router /order/get [post]
func FindOrder(c *gin.Context) {
	var ad service.Order
	err := c.ShouldBindQuery(&ad)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, read := service.GetOrder(ad.ID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"read": read}, c)
	}
}
func UpdateNote(c *gin.Context) {
	var order orderModel.Order
	err := c.ShouldBindJSON(&order)
	if err != nil {
		log.Log().Error("备注失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.Note(order); err != nil {
		log.Log().Error("备注失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("备注失败", c)
		return
	} else {
		service3.CreateOperationRecord(v1.GetUserID(c), 3, c.ClientIP(), "订单"+strconv.Itoa(int(order.ID))+"修改用户备注")
		yzResponse.OkWithMessage("备注成功", c)
	}
}

// GetOrderList
// @Tags 订单
// @Summary 分页获取列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.OrderAdminSearch true "分页获取列表"
// @Success 200 {object} yzResponse.PageResult
// @Router /order/list [post]
func GetOrderList(c *gin.Context) {
	var pageInfo orequest.OrderAdminSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplier := service.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		log.Log().Error("您不是供应商的成员，无法管理", zap.Any("err", err))
		yzResponse.FailWithMessage("您不是供应商的成员，无法管理", c)
		return
	}
	pageInfo.SupplierID = &supplier.ID
	if err, list, total, WaitPayNum, WaitSendNum, WaitReceiveNum, CompletedNum, ClosedNum, BackNum, RefundNum := service.GetOrderInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		type PageResultWithNum struct {
			yzResponse.PageResult
			WaitPayNum     int64
			WaitSendNum    int64
			WaitReceiveNum int64
			CompletedNum   int64
			ClosedNum      int64
			BackNum        int64
			RefundNum      int64
		}
		var pageResultWithNum PageResultWithNum
		pageResultWithNum.WaitPayNum = WaitPayNum
		pageResultWithNum.WaitSendNum = WaitSendNum
		pageResultWithNum.WaitReceiveNum = WaitReceiveNum
		pageResultWithNum.CompletedNum = CompletedNum
		pageResultWithNum.ClosedNum = ClosedNum
		pageResultWithNum.BackNum = BackNum
		pageResultWithNum.RefundNum = RefundNum
		pageResultWithNum.Page = pageInfo.Page
		pageResultWithNum.PageSize = pageInfo.PageSize
		pageResultWithNum.Total = total
		pageResultWithNum.List = list
		yzResponse.OkWithDetailed(pageResultWithNum, "获取成功", c)
	}
}

func ExportOrderList(c *gin.Context) {
	var pageInfo orequest.OrderAdminSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	adminId := v1.GetUserID(c)
	err, supplier := service.GetSupplierByUserId(adminId)
	if err != nil {
		log.Log().Error("您不是供应商的成员，无法管理", zap.Any("err", err))
		yzResponse.FailWithMessage("您不是供应商的成员，无法管理", c)
		return
	}
	pageInfo.SupplierID = &supplier.ID
	pageInfo.IsSupplier = 1
	pageInfo.AdminID = adminId
	err = service2.ExportOrderInfoList(pageInfo)
	if err != nil {
		log.Log().Error("导出失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithMessage("导出程序后台处理中，预计10-20分钟导出完成，请于导出列表中查看进度", c)
	}
}
func ExportOrderRecordList(c *gin.Context) {
	var pageInfo orequest.OrderExportRecordRequest
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplier := service.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		log.Log().Error("您不是供应商的成员，无法管理", zap.Any("err", err))
		yzResponse.FailWithMessage("您不是供应商的成员，无法管理", c)
		return
	}
	pageInfo.SupplierID = supplier.ID
	err, total, data := service2.GetOrderExportRecordList(pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     data,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
func ConfirmSend(c *gin.Context) {
	var sendQuery request.SendQuery
	err := c.ShouldBindJSON(&sendQuery)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplier := service.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		log.Log().Error("您不是供应商的成员，无法管理", zap.Any("err", err))
		yzResponse.FailWithMessage("您不是供应商的成员，无法管理", c)
		return
	}
	sendQuery.UserID = v1.GetUserID(c)

	if err = service.SendNew(supplier.ID, sendQuery); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("发货成功，请到记录中查看发货详情（批量发货为异步执行,请1分钟以后刷新记录列表查看，请勿重复上传发货模板！）", c)
	}
}

func GetSendOrderRecordList(c *gin.Context) {
	var pageInfo yzRequest.PageInfo
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplier := service.GetSupplierByUserId(v1.GetUserID(c))
	//if err != nil {
	//	log.Log().Error("您不是供应商的成员，无法管理", zap.Any("err", err))
	//	yzResponse.FailWithMessage("您不是供应商的成员，无法管理", c)
	//	return
	//}
	if err, list, total := service.GetSendOrderRecordList(supplier.ID, pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func UploadFile(c *gin.Context) {
	var file model.ExaFileUploadAndDownload
	noSave := c.DefaultQuery("noSave", "0")
	_, header, err := c.Request.FormFile("file")
	if err != nil {
		log.Log().Error("接收文件失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("接收文件失败", c)
		return
	}
	UserID := v1.GetUserID(c)
	GroupId := c.Request.FormValue("groupId")
	i, _ := strconv.Atoi(GroupId)
	fileData := map[string]uint{"uid": UserID, "groupId": uint(i)}
	err, file = service.UploadFile(header, noSave, fileData) // 文件上传后拿到文件路径
	if err != nil {
		log.Log().Error("修改数据库链接失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("修改数据库链接失败", c)
		return
	}
	if c.Request.FormValue("index") != "" {
		file.Index, err = strconv.Atoi(c.Request.FormValue("index"))
	}
	if err != nil {
		log.Log().Error("修改数据库链接失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("修改数据库链接失败", c)
		return
	}
	yzResponse.OkWithDetailed(response2.ExaFileResponse{File: file}, "上传成功", c)
}

// GetSendOrderList
// @Tags GetSendOrderList
// @Summary 获取发货记录信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /order/getSendOrderList [post]
func GetSendOrderList(c *gin.Context) {
	var pageInfo request.SendOrderAdminSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetSendOrderInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetSendOrderRecordList
// @Tags GetSendOrderRecordList
// @Summary 批量发货
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /order/batchSendOrder [post]
func BatchSendOrder(c *gin.Context) {
	var sendQuery request.SendQuery
	err := c.ShouldBindJSON(&sendQuery)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	sendQuery.UserID = v1.GetUserID(c)
	if err = service.SendNew(0, sendQuery); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("发货成功，请到记录中查看发货详情（批量发货为异步执行,请1分钟以后刷新记录列表查看，请勿重复上传发货模板！）", c)
	}
}

// GetExpressInfo
// @Tags GetExpressInfo
// @Summary 修改物流信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /adminSupplier/updateOrderExpress [post]
func UpdateOrderExpress(c *gin.Context) {
	var orderExpress orequest.OrderExpress
	err := c.ShouldBindJSON(&orderExpress)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if orderExpress.ID == 0 {
		yzResponse.FailWithMessage("请选择要修改物流的id", c)
		return
	}

	if err := service2.UpdateOrderExpresse(orderExpress); err != nil {
		log.Log().Error("修改失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("修改失败", c)
		return
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}
