package v1

import (
	"errors"
	frequest "finance/request"
	fresponse "finance/response"
	fservice "finance/service"
	"fmt"
	"notification/mq"
	request3 "supplier/request"

	v12 "gin-vue-admin/admin/api/v1"
	"order/model"
	"order/request"
	"order/service"
	pservice "payment/service"
	"strconv"
	supplierModel "supplier/model"
	supplierService "supplier/service"
	model2 "user/model"
	"yz-go/component/log"
	request2 "yz-go/request"
	yzResponse "yz-go/response"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

func UpdateTradeSetting(c *gin.Context) {
	var sysSetting model.TradeSetting
	err := c.ShouldBindJSON(&sysSetting)
	if err != nil {
		return
	}
	fmt.Println(sysSetting.Value)
	serr, supplier := v12.GetSupplierByUserId(v12.GetUserID(c))
	if serr != nil {
		log.Log().Error("获取失败", zap.Any("err", serr))
		yzResponse.FailWithMessage("获取失败", c)
		return
	}
	sysSetting.Key = "supplier_bill_setting" + strconv.Itoa(supplier)
	err = service.SaveTradeSetting(sysSetting)

	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	model.ResetTrade()
	yzResponse.OkWithMessage("修改成功", c)

}

func FindTradeSetting(c *gin.Context) {
	serr, supplier := v12.GetSupplierByUserId(v12.GetUserID(c))
	if serr != nil {
		log.Log().Error("获取失败", zap.Any("err", serr))
		yzResponse.FailWithMessage("获取失败", c)
		return
	}
	err, tradeSetting := service.GetTradeSetting("supplier_bill_setting" + strconv.Itoa(supplier))
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"setting": tradeSetting}, c)

}

func GetSupplierBillList(c *gin.Context) {
	var pageInfo request.SearchBill
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	serr, supplier := v12.GetSupplierByUserId(v12.GetUserID(c))
	if serr != nil {
		log.Log().Error("获取失败", zap.Any("err", serr))
		yzResponse.FailWithMessage("获取失败", c)
		return
	}
	pageInfo.Drawer = &supplier
	if err, list, total := service.GetBillList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// CreateInvoice 创建发票记录API
func CreateInvoice(c *gin.Context) {
	var invoice supplierModel.WithdrawalInvoice
	err := c.ShouldBindJSON(&invoice)
	if err != nil {
		log.Log().Error("参数绑定失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 验证用户权限
	serr, _ := supplierService.GetSupplierByUserId(v12.GetUserID(c))
	if serr != nil {
		log.Log().Error("获取供应商信息失败", zap.Any("err", serr))
		yzResponse.FailWithMessage("获取供应商信息失败", c)
		return
	}

	// 调用service层创建发票
	err = supplierService.CreateInvoice(&invoice)
	if err != nil {
		log.Log().Error("创建发票失败", zap.Any("err", err))
		yzResponse.FailWithMessage("创建发票失败", c)
		return
	}

	yzResponse.OkWithMessage("创建发票成功", c)
}

// DeleteInvoice 删除发票记录API
func DeleteInvoice(c *gin.Context) {
	var data request3.InvoiceData
	err := c.ShouldBindQuery(&data)
	if err != nil {
		log.Log().Error("参数错误", zap.Any("err", err))
		yzResponse.FailWithMessage("参数错误", c)
		return
	}

	// 验证用户权限
	serr, _ := supplierService.GetSupplierByUserId(v12.GetUserID(c))
	if serr != nil {
		log.Log().Error("获取供应商信息失败", zap.Any("err", serr))
		yzResponse.FailWithMessage("获取供应商信息失败", c)
		return
	}

	// 调用service层删除发票
	err = supplierService.DeleteInvoice(data.ID)
	if err != nil {
		log.Log().Error("删除发票失败", zap.Any("err", err))
		yzResponse.FailWithMessage("删除发票失败", c)
		return
	}

	yzResponse.OkWithMessage("删除发票成功", c)
}

// GetInvoice 获取发票详情API
func GetInvoice(c *gin.Context) {
	var data request3.InvoiceData
	err := c.ShouldBindQuery(&data)
	if err != nil {
		log.Log().Error("参数错误", zap.Any("err", err))
		yzResponse.FailWithMessage("参数错误", c)
		return
	}

	// 验证用户权限
	serr, _ := supplierService.GetSupplierByUserId(v12.GetUserID(c))
	if serr != nil {
		log.Log().Error("获取供应商信息失败", zap.Any("err", serr))
		yzResponse.FailWithMessage("获取供应商信息失败", c)
		return
	}

	// 调用service层获取发票
	err, invoice := supplierService.GetInvoice(data.ID)
	if err != nil {
		log.Log().Error("获取发票失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取发票失败", c)
		return
	}

	yzResponse.OkWithData(invoice, c)
}

func GetSupplierBillDetail(c *gin.Context) {
	var pageInfo frequest.AccountApplySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 验证用户权限
	serr, supplierID := supplierService.GetSupplierByUserId(v12.GetUserID(c))
	if serr != nil {
		log.Log().Error("获取供应商信息失败", zap.Any("err", serr))
		yzResponse.FailWithMessage("获取供应商信息失败", c)
		return
	}

	pageInfo.SupplierID = supplierID.ID
	_, data := supplierService.GetSupplierBillDetailTotal(pageInfo)

	err, list, total := supplierService.GetSupplierBillDetail(pageInfo)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("明细获取失败", c)
		return
	}
	err, payType := pservice.GetPayType()

	if err != nil {
		yzResponse.FailWithMessage("支付方式获取失败", c)
		return
	} else {

		yzResponse.OkWithDetailed(fresponse.PageResult{
			Data:     data,
			PayType:  payType,
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// ExportSupplierBillDetail 导出供应商结算明细Excel
func ExportSupplierBillDetail(c *gin.Context) {
	var pageInfo frequest.AccountApplySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 验证用户权限
	serr, supplierID := supplierService.GetSupplierByUserId(v12.GetUserID(c))
	if serr != nil {
		log.Log().Error("获取供应商信息失败", zap.Any("err", serr))
		yzResponse.FailWithMessage("获取供应商信息失败", c)
		return
	}

	pageInfo.SupplierID = supplierID.ID
	// 设置较大的页面大小，以便导出更多数据
	pageInfo.PageSize = 5000
	pageInfo.Page = 1

	// 调用导出服务
	err, link := supplierService.ExportSupplierBillDetail(pageInfo)
	if err != nil {
		log.Log().Error("导出失败", zap.Any("err", err))
		yzResponse.FailWithMessage("导出失败", c)
		return
	}

	yzResponse.OkWithData(gin.H{"link": link}, c)
}
func GetUserBankList(c *gin.Context) {

	// 验证用户权限
	serr, supplierID := supplierService.GetSupplierByUserId(v12.GetUserID(c))
	if serr != nil {
		log.Log().Error("获取供应商信息失败", zap.Any("err", serr))
		yzResponse.FailWithMessage("获取供应商信息失败", c)
		return
	}

	uid := supplierID.Uid
	err, data := supplierService.GetUserBankList(uid)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(data, c)

}
func DeleteUserBank(c *gin.Context) {

	// 验证用户权限
	serr, supplierID := supplierService.GetSupplierByUserId(v12.GetUserID(c))
	if serr != nil {
		log.Log().Error("获取供应商信息失败", zap.Any("err", serr))
		yzResponse.FailWithMessage("获取供应商信息失败", c)
		return
	}

	var id = request2.GetById{}
	if err := c.ShouldBindJSON(&id); err != nil {
		yzResponse.FailWithMessage("参数解析失败"+err.Error(), c)
		return
	}
	uid := supplierID.Uid
	err := supplierService.DeleteUserBank(id.Id, uid)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.Ok(c)

}

func SaveUserBank(c *gin.Context) {

	// 验证用户权限
	serr, supplierID := supplierService.GetSupplierByUserId(v12.GetUserID(c))
	if serr != nil {
		log.Log().Error("获取供应商信息失败", zap.Any("err", serr))
		yzResponse.FailWithMessage("获取供应商信息失败", c)
		return
	}

	var bank model2.UserBank
	if err := c.ShouldBind(&bank); err != nil {
		yzResponse.FailWithMessage("参数解析失败"+err.Error(), c)
		return
	}
	bank.UserID = supplierID.Uid
	err := supplierService.SaveUserBank(bank)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.Ok(c)

}

func CreateBank(c *gin.Context) {

	// 验证用户权限
	serr, supplierID := supplierService.GetSupplierByUserId(v12.GetUserID(c))
	if serr != nil {
		log.Log().Error("获取供应商信息失败", zap.Any("err", serr))
		yzResponse.FailWithMessage("获取供应商信息失败", c)
		return
	}

	var userBank model2.UserBank
	var err error
	err = c.ShouldBindJSON(&userBank)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userBank.UserID = supplierID.Uid
	err = supplierService.CreateBank(userBank)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("创建成功", c)

}

func Withdraw(c *gin.Context) {

	// 验证用户权限
	serr, supplierID := supplierService.GetSupplierByUserId(v12.GetUserID(c))
	if serr != nil {
		log.Log().Error("获取供应商信息失败", zap.Any("err", serr))
		yzResponse.FailWithMessage("获取供应商信息失败", c)
		return
	}

	var err error
	var withdraw frequest.WithdrawalApply
	if err = c.ShouldBindJSON(&withdraw); err != nil {
		yzResponse.FailWithMessage("解析参数失败"+err.Error(), c)
		return
	}
	withdraw.UserId = supplierID.Uid

	err = fservice.Withdraw(withdraw)
	if err != nil {
		log.Log().Error("提现失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = mq.PublishMessage(withdraw.UserId, "withdraw", 0)
	yzResponse.OkWithData("申请成功", c)

}
