package v1

import (
	"errors"
	v1 "gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	orderModel "order/model"
	surfaceSingleModel "surface-single/model"
	surfaceSingleRequest "surface-single/request"
	surfaceSingleService "surface-single/service"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

func GetSettingKey(c *gin.Context) {
	err, supplierID := v1.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if supplierID == 0 {
		yzResponse.FailWithMessage("supplier_id为必填", c)
		return
	}
	key := surfaceSingleService.GetKey(supplierID)

	yzResponse.OkWithData(gin.H{"key": key}, c)
}

func FindSurfaceSingleSetting(c *gin.Context) {
	err, supplierID := v1.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if supplierID == 0 {
		yzResponse.FailWithMessage("supplier_id为必填", c)
		return
	}
	err, setting := surfaceSingleService.GetSetting(surfaceSingleService.GetKey(supplierID))
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取设置失败", c)
		return
	}
	var res surfaceSingleModel.KDNStatistic
	err, res = surfaceSingleService.SettingHandle(setting)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取设置失败", c)
		return
	}
	endDate := ""
	if len(res.Data.Data.Data) > 0 {
		endDate = res.Data.Data.Data[0].EndTime
	}
	statistic := make(map[string]interface{})
	statistic["apiTotalCount"] = res.Data.Statistics.ApiTotalCount
	statistic["apiTotalUsed"] = res.Data.Statistics.ApiTotalUsed
	statistic["apiTotalPrice"] = res.Data.Statistics.ApiTotalPrice
	statistic["endDate"] = endDate

	yzResponse.OkWithData(gin.H{"setting": setting, "statistic": statistic}, c)
}

func UpdateSurfaceSingleSetting(c *gin.Context) {
	var setting surfaceSingleModel.Setting
	err := c.ShouldBindJSON(&setting)
	if err != nil {
		return
	}
	if setting.Key == "" {
		err, supplierID := v1.GetSupplierByUserId(v1.GetUserID(c))
		if err != nil {
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		if supplierID == 0 {
			yzResponse.FailWithMessage("supplier_id为必填", c)
			return
		}
		setting.Key = surfaceSingleService.GetKey(supplierID)
	}
	err = surfaceSingleService.UpdateSetting(setting)
	if err == nil {
		yzResponse.OkWithMessage("修改成功", c)
	} else {
		yzResponse.FailWithMessage("修改失败", c)
	}
}

func GetPostCode(c *gin.Context) {
	var postCodeRequest surfaceSingleRequest.PostCodeRequest
	err := c.ShouldBindJSON(&postCodeRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, resPostCode := surfaceSingleService.GetPostCodeByResty(postCodeRequest)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"result": resPostCode}, c)
	}
}

func CreateSurfaceSingle(c *gin.Context) {
	var surfaceSingle surfaceSingleModel.SurfaceSingle
	err := c.ShouldBindJSON(&surfaceSingle)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplierID := v1.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if supplierID == 0 {
		yzResponse.FailWithMessage("supplier_id为必填", c)
		return
	}
	surfaceSingle.SupplierID = uint(supplierID)
	if err := surfaceSingleService.CreateSurfaceSingle(surfaceSingle); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

func DeleteSurfaceSingle(c *gin.Context) {
	var surfaceSingle surfaceSingleModel.SurfaceSingle
	err := c.ShouldBindJSON(&surfaceSingle)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplierID := v1.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if supplierID == 0 {
		yzResponse.FailWithMessage("supplier_id为必填", c)
		return
	}
	var exist surfaceSingleModel.SurfaceSingle
	err, exist = surfaceSingleService.GetSurfaceSingle(surfaceSingle.ID)
	if err != nil {
		yzResponse.FailWithMessage("查询面单错误", c)
		return
	}
	if exist.SupplierID != uint(supplierID) {
		yzResponse.FailWithMessage("面单归属错误", c)
		return
	}
	if err := surfaceSingleService.DeleteSurfaceSingle(surfaceSingle); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

func UpdateSurfaceSingle(c *gin.Context) {
	var surfaceSingle surfaceSingleModel.SurfaceSingle
	err := c.ShouldBindJSON(&surfaceSingle)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplierID := v1.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if supplierID == 0 {
		yzResponse.FailWithMessage("supplier_id为必填", c)
		return
	}
	if surfaceSingle.SupplierID != uint(supplierID) {
		yzResponse.FailWithMessage("面单归属错误", c)
		return
	}
	if err := surfaceSingleService.UpdateSurfaceSingle(surfaceSingle); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}

func FindSurfaceSingle(c *gin.Context) {
	var surfaceSingle surfaceSingleModel.SurfaceSingle
	err := c.ShouldBindJSON(&surfaceSingle)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplierID := v1.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if supplierID == 0 {
		yzResponse.FailWithMessage("supplier_id为必填", c)
		return
	}
	var exist surfaceSingleModel.SurfaceSingle
	err, exist = surfaceSingleService.GetSurfaceSingle(surfaceSingle.ID)
	if err != nil {
		yzResponse.FailWithMessage("查询面单错误", c)
		return
	}
	if exist.SupplierID != uint(supplierID) {
		yzResponse.FailWithMessage("面单归属错误", c)
		return
	}
	if err, reSurfaceSingle := surfaceSingleService.GetSurfaceSingle(surfaceSingle.ID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"surface_single": reSurfaceSingle}, c)
	}
}

func GetSurfaceSinglesList(c *gin.Context) {
	var pageInfo yzRequest.PageInfo
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplierID := v1.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if supplierID == 0 {
		yzResponse.FailWithMessage("supplier_id为必填", c)
		return
	}
	if err, list, total := surfaceSingleService.GetSurfaceSinglesList(pageInfo, uint(supplierID)); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func ChangeDefault(c *gin.Context) {
	var ssd surfaceSingleRequest.SSDefault
	err := c.ShouldBindJSON(&ssd)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := utils.Verify(ssd); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplierID := v1.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if supplierID == 0 {
		yzResponse.FailWithMessage("supplier_id为必填", c)
		return
	}
	if err := surfaceSingleService.ChangeDefault(ssd, uint(supplierID)); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}

func CreateAddresser(c *gin.Context) {
	var addresser surfaceSingleModel.Addresser
	err := c.ShouldBindJSON(&addresser)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplierID := v1.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if supplierID == 0 {
		yzResponse.FailWithMessage("supplier_id为必填", c)
		return
	}
	addresser.SupplierID = uint(supplierID)
	if err := surfaceSingleService.CreateAddresser(addresser); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

func DeleteAddresser(c *gin.Context) {
	var addresser surfaceSingleModel.Addresser
	err := c.ShouldBindJSON(&addresser)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplierID := v1.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if supplierID == 0 {
		yzResponse.FailWithMessage("supplier_id为必填", c)
		return
	}
	var exist surfaceSingleModel.Addresser
	err, exist = surfaceSingleService.GetAddresser(addresser.ID)
	if err != nil {
		yzResponse.FailWithMessage("查询发货人错误", c)
		return
	}
	if exist.SupplierID != uint(supplierID) {
		yzResponse.FailWithMessage("发货人归属错误", c)
		return
	}
	if err := surfaceSingleService.DeleteAddresser(addresser); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

func UpdateAddresser(c *gin.Context) {
	var addresser surfaceSingleModel.Addresser
	err := c.ShouldBindJSON(&addresser)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplierID := v1.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if supplierID == 0 {
		yzResponse.FailWithMessage("supplier_id为必填", c)
		return
	}
	if addresser.SupplierID != uint(supplierID) {
		yzResponse.FailWithMessage("发货人归属错误", c)
		return
	}
	if err := surfaceSingleService.UpdateAddresser(addresser); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}

func FindAddresser(c *gin.Context) {
	var addresser surfaceSingleModel.Addresser
	err := c.ShouldBindJSON(&addresser)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplierID := v1.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if supplierID == 0 {
		yzResponse.FailWithMessage("supplier_id为必填", c)
		return
	}
	var exist surfaceSingleModel.Addresser
	err, exist = surfaceSingleService.GetAddresser(addresser.ID)
	if err != nil {
		yzResponse.FailWithMessage("查询面单错误", c)
		return
	}
	if exist.SupplierID != uint(supplierID) {
		yzResponse.FailWithMessage("发货人归属错误", c)
		return
	}
	if err, reAddresser := surfaceSingleService.GetAddresser(addresser.ID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"addresser": reAddresser}, c)
	}
}

func GetAddressersList(c *gin.Context) {
	var pageInfo yzRequest.PageInfo
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplierID := v1.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if supplierID == 0 {
		yzResponse.FailWithMessage("supplier_id为必填", c)
		return
	}
	if err, list, total := surfaceSingleService.GetAddressersList(pageInfo, uint(supplierID)); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func ChangeAddresserDefault(c *gin.Context) {
	var ssd surfaceSingleRequest.SSDefault
	err := c.ShouldBindJSON(&ssd)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := utils.Verify(ssd); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplierID := v1.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if supplierID == 0 {
		yzResponse.FailWithMessage("supplier_id为必填", c)
		return
	}
	if err := surfaceSingleService.ChangeAddresserDefault(ssd, uint(supplierID)); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}

func SinglePackagePrint(c *gin.Context) {
	// 传递订单id
	var reqId surfaceSingleRequest.GetByOrderId
	err := c.ShouldBindJSON(&reqId)
	if err != nil {
		err = errors.New("参数错误")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplierID := v1.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if supplierID == 0 {
		yzResponse.FailWithMessage("supplier_id为必填", c)
		return
	}
	err, canPrint := surfaceSingleService.GetOrderPrintStatus(reqId.OrderID)
	if err != nil {
		err = errors.New("验证订单打印状态错误")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if canPrint == false {
		err = errors.New("该订单已全部打印")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 通过订单id 查询包裹
	var orderPackage surfaceSingleModel.OrderPackage
	err, orderPackage = surfaceSingleService.GetOrderPackages(reqId.OrderID)
	if err != nil {
		err = errors.New("查询订单包裹信息失败")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 通过订单id 查询收件人信息
	var address orderModel.ShippingAddress
	err, address = surfaceSingleService.GetShippingAddress(reqId.OrderID)
	if err != nil || address.ID == 0 {
		log.Log().Error("查询订单收件人信息失败", zap.Any("err", err))
		err = errors.New("查询订单收件人信息失败")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 收件人信息
	Receiver := surfaceSingleService.GetApiReceiver(address)
	// 获取默认面单模板,修改参数
	var surfaceSingle surfaceSingleModel.SurfaceSingle
	err, surfaceSingle = surfaceSingleService.GetDefaultSurfaceSingle(uint(supplierID))
	if err != nil {
		log.Log().Error("查询默认面单信息失败", zap.Any("err", err))
		err = errors.New("查询默认面单信息失败")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 获取默认发件人信息
	var addresser surfaceSingleModel.Addresser
	err, addresser = surfaceSingleService.GetDefaultAddresser(uint(supplierID))
	if err != nil {
		log.Log().Error("查询默认发件人信息失败", zap.Any("err", err))
		err = errors.New("查询默认发件人信息失败")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if surfaceSingle.ShipperCode == "EMS" || surfaceSingle.ShipperCode == "YZPY" || surfaceSingle.ShipperCode == "YZBK" {
		if addresser.PostCode == "" {
			err = errors.New("快递公司为EMS、YZPY、YZBK时,发货地邮编必填!")
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
	}
	// 发件人信息
	Sender := surfaceSingleService.GetApiSender(addresser)
	payType := 1
	if surfaceSingle.ShipperCode == "DBL" {
		payType = 3
	}
	// 获取订单
	err, order := surfaceSingleService.GetOrderModel(reqId.OrderID)
	if err != nil {
		log.Log().Error("查询订单失败", zap.Any("err", err))
		err = errors.New("查询订单失败")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//var results []model.KDNRES
	orderSn := order.OrderSN
	packageCount := 1
	err, orderPrint := surfaceSingleService.GetOrderPrintModel(reqId.OrderID, order.GoodsCount)
	err, goodsTotal, params := surfaceSingleService.GetApiParams(surfaceSingle, orderSn, payType, packageCount, Receiver, Sender, addresser, orderPackage)
	// 请求打印接口
	err, res := surfaceSingleService.Handle(params, surfaceSingleService.GetKey(supplierID))
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if res.Result != 1 {
		yzResponse.FailWithMessage(res.Msg, c)
		return
	}

	// 获取打印详情
	err, itemAttribute := surfaceSingleService.GetOrderPrintItemsAttribute(reqId.OrderID, goodsTotal, orderPrint, res.Data)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 修改打印商品数量
	orderPrint.PrintedTotal += itemAttribute.PrintedTotal
	// 增加打印详情
	orderPrint.OrderPrintItems = append(orderPrint.OrderPrintItems, itemAttribute)

	// 返回成功后,保存打印数据表与关联表, 修改包裹打印状态
	err = surfaceSingleService.UpdateOrderPackagePrintStatus(reqId.OrderID)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 返回面单信息
	//results = append(results, res)
	orderPrint.Status = 1
	// 修改保存打印信息
	if orderPrint.ID == 0 {
		err = surfaceSingleService.CreateOrderPrint(orderPrint)
	} else {
		err = surfaceSingleService.UpdateOrderPrint(orderPrint)
	}
	if err != nil {
		log.Log().Error("修改保存打印信息失败", zap.Any("err", err))
		err = errors.New("修改保存打印信息失败")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 返回面单信息 数组
	yzResponse.OkWithDetailed(gin.H{"results": res.Data}, "获取成功", c)
}

func HistoryPrint(c *gin.Context) {
	// 传递订单id
	var reqId surfaceSingleRequest.GetByOrderId
	err := c.ShouldBindJSON(&reqId)
	if err != nil {
		err = errors.New("参数错误")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var orderPrintItems []surfaceSingleModel.OrderPrintItem
	if err, orderPrintItems = surfaceSingleService.FindOrderPrintItems(reqId.OrderID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"order_print_items": orderPrintItems}, c)
	}
}

func MorePackagePrint(c *gin.Context) {
	// 传递订单id
	var reqId surfaceSingleRequest.GetByOrderId
	err := c.ShouldBindJSON(&reqId)
	if err != nil {
		log.Log().Error("参数错误", zap.Any("err", err))
		err = errors.New("参数错误")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplierID := v1.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if supplierID == 0 {
		yzResponse.FailWithMessage("supplier_id为必填", c)
		return
	}
	err, canPrint := surfaceSingleService.GetOrderPrintStatus(reqId.OrderID)
	if err != nil {
		log.Log().Error("验证订单打印状态错误", zap.Any("err", err))
		err = errors.New("验证订单打印状态错误")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if canPrint == false {
		log.Log().Error("该订单已全部打印", zap.Any("err", err))
		err = errors.New("该订单已全部打印")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 通过订单id 查询收件人信息
	var address orderModel.ShippingAddress
	err, address = surfaceSingleService.GetShippingAddress(reqId.OrderID)
	if err != nil || address.ID == 0 {
		log.Log().Error("查询订单收件人信息失败", zap.Any("err", err))
		err = errors.New("查询订单收件人信息失败")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 收件人信息
	Receiver := surfaceSingleService.GetApiReceiver(address)
	// 通过订单id 查询包裹
	var orderPackages []surfaceSingleModel.OrderPackage
	err, orderPackages = surfaceSingleService.FindOrderPackages(reqId.OrderID)
	if err != nil {
		log.Log().Error("查询订单包裹信息失败", zap.Any("err", err))
		err = errors.New("查询订单包裹信息失败")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 获取默认面单模板,修改参数
	var surfaceSingle surfaceSingleModel.SurfaceSingle
	err, surfaceSingle = surfaceSingleService.GetDefaultSurfaceSingle(uint(supplierID))
	if err != nil {
		log.Log().Error("查询默认面单信息失败", zap.Any("err", err))
		err = errors.New("查询默认面单信息失败")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 获取默认发件人信息
	var addresser surfaceSingleModel.Addresser
	err, addresser = surfaceSingleService.GetDefaultAddresser(uint(supplierID))
	if err != nil {
		log.Log().Error("查询默认发件人信息失败", zap.Any("err", err))
		err = errors.New("查询默认发件人信息失败")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if surfaceSingle.ShipperCode == "EMS" || surfaceSingle.ShipperCode == "YZPY" || surfaceSingle.ShipperCode == "YZBK" {
		if addresser.PostCode == "" {
			err = errors.New("快递公司为EMS、YZPY、YZBK时,发货地邮编必填!")
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
	}
	// 发件人信息
	Sender := surfaceSingleService.GetApiSender(addresser)
	payType := 1
	if surfaceSingle.ShipperCode == "DBL" {
		payType = 3
	}
	// 获取订单
	err, order := surfaceSingleService.GetOrderModel(reqId.OrderID)
	if err != nil {
		log.Log().Error("查询订单失败", zap.Any("err", err))
		err = errors.New("查询订单失败")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var results []surfaceSingleModel.KDN
	orderSn := order.OrderSN
	packageCount := len(orderPackages)
	err, orderPrint := surfaceSingleService.GetOrderPrintModel(reqId.OrderID, order.GoodsCount)
	for _, orderPackage := range orderPackages {
		err, goodsTotal, params := surfaceSingleService.GetApiParams(surfaceSingle, orderSn, payType, packageCount, Receiver, Sender, addresser, orderPackage)
		// 请求打印接口
		err, res := surfaceSingleService.Handle(params, surfaceSingleService.GetKey(supplierID))
		if err != nil {
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		if res.Result != 1 {
			yzResponse.FailWithMessage(res.Msg, c)
			return
		}

		// 获取打印详情
		err, itemAttribute := surfaceSingleService.GetOrderPrintItemsAttribute(reqId.OrderID, goodsTotal, orderPrint, res.Data)
		if err != nil {
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}

		// 修改打印商品数量
		orderPrint.PrintedTotal += itemAttribute.PrintedTotal
		// 增加打印详情
		orderPrint.OrderPrintItems = append(orderPrint.OrderPrintItems, itemAttribute)
		if orderPrint.ProductTotal == orderPrint.PrintedTotal {
			orderPrint.Status = 1
		}

		// 返回成功后,保存打印数据表与关联表, 修改包裹打印状态
		err = surfaceSingleService.UpdateOrderPackagePrintStatus(reqId.OrderID)
		if err != nil {
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		// 返回面单信息
		results = append(results, res.Data)
	}

	// 修改保存打印信息
	if orderPrint.ID == 0 {
		err = surfaceSingleService.CreateOrderPrint(orderPrint)
	} else {
		err = surfaceSingleService.UpdateOrderPrint(orderPrint)
	}
	if err != nil {
		log.Log().Error("修改保存打印信息失败", zap.Any("err", err))
		err = errors.New("修改保存打印信息失败")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 返回面单信息 数组
	yzResponse.OkWithDetailed(gin.H{"results": results}, "获取成功", c)
}

func CreatePackage(c *gin.Context) {
	var orderPackageItemsReq surfaceSingleRequest.OrderPackageItemsReq
	err := c.ShouldBindJSON(&orderPackageItemsReq)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var orderPackage surfaceSingleModel.OrderPackage
	orderPackage.OrderID = orderPackageItemsReq.OrderID
	orderPackage.Total = 0
	for _, orderPackageItem := range orderPackageItemsReq.OrderPackageItems {
		if orderPackageItem.Total == 0 {
			continue
		}
		// 订单商品
		var orderItem orderModel.OrderItem
		err, orderItem = surfaceSingleService.GetOrderItem(orderPackageItem.OrderItemID)
		if err != nil {
			log.Log().Error("查询订单商品失败", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		if uint(orderPackageItem.Total) > orderItem.Qty {
			log.Log().Error("包裹商品数量超过订单商品数量", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		// 已存在的包裹
		var resOrderPackageItems []surfaceSingleModel.OrderPackageItem
		err, resOrderPackageItems = surfaceSingleService.GetOrderPackageItems(orderPackageItem.OrderID, orderPackageItem.OrderItemID)
		if err != nil {
			log.Log().Error("查询包裹详情失败", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		var packTotal uint
		packTotal += uint(orderPackageItem.Total)
		for _, resOrderPackageItem := range resOrderPackageItems {
			packTotal += uint(resOrderPackageItem.Total)
		}
		if packTotal > orderItem.Qty {
			log.Log().Error("当前商品的包裹商品数量超过订单商品数量", zap.Any("err", err))
			err = errors.New("当前商品的包裹商品数量超过订单商品数量")
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		orderPackage.Total += orderPackageItem.Total
		orderPackage.OrderPackageItems = append(orderPackage.OrderPackageItems, orderPackageItem)
	}

	// 创建包裹和包裹详情
	if err = surfaceSingleService.CreateOrderPackage(orderPackage); err != nil {
		log.Log().Error("创建包裹失败1", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

func DeleteItem(c *gin.Context) {
	var orderPackageItem surfaceSingleModel.OrderPackageItem
	err := c.ShouldBindJSON(&orderPackageItem)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := surfaceSingleService.DeleteOrderPackageItem(orderPackageItem); err != nil {
		log.Log().Error("删除包裹详情失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

func DeletePackage(c *gin.Context) {
	var orderPackage surfaceSingleModel.OrderPackage
	err := c.ShouldBindJSON(&orderPackage)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := surfaceSingleService.DeleteOrderPackage(orderPackage); err != nil {
		log.Log().Error("删除包裹失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

func UpdateItem(c *gin.Context) {
	var orderPackageItem surfaceSingleModel.OrderPackageItem
	err := c.ShouldBindJSON(&orderPackageItem)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if orderPackageItem.Total < 1 {
		yzResponse.FailWithMessage("商品数量不能小于1", c)
		return
	}
	if err := surfaceSingleService.UpdateOrderPackageItem(orderPackageItem); err != nil {
		log.Log().Error("修改包裹详情失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}

func GetUnpackedProducts(c *gin.Context) {
	var reqId surfaceSingleRequest.GetByOrderId
	err := c.ShouldBindQuery(&reqId)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, order := surfaceSingleService.GetUnpackedProducts(reqId.OrderID)
	if err != nil {
		log.Log().Error("获取订单失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		for key, orderItem := range order.OrderItems {
			var packageTotal int
			for _, OrderPackageItem := range orderItem.OrderPackageItems {
				packageTotal += OrderPackageItem.Total
			}
			order.OrderItems[key].UnPackedTotal = int(orderItem.Qty) - packageTotal
		}
		yzResponse.OkWithDetailed(gin.H{"items": order.OrderItems}, "获取成功", c)
	}
}

func GetOrderPackages(c *gin.Context) {
	var reqId surfaceSingleRequest.GetByOrderId
	err := c.ShouldBindQuery(&reqId)
	if err != nil {
		log.Log().Error("参数错误", zap.Any("err", err))
		err = errors.New("参数错误")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, orderPackages := surfaceSingleService.GetAllPackages(reqId.OrderID); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"packages": orderPackages}, c)
	}
}

func AddPackageItem(c *gin.Context) {
	var items []surfaceSingleModel.OrderPackageItem
	err := c.ShouldBindJSON(&items)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = surfaceSingleService.AddItem(items); err != nil {
		log.Log().Error("创建包裹详情失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}
