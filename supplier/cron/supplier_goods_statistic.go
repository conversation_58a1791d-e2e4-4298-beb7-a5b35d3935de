package cron

import (
	"errors"
	model4 "finance/model"
	"fmt"
	"gorm.io/gorm"
	model3 "order/model"
	"product/model"
	"strconv"
	model2 "supplier/model"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/setting"
	"yz-go/source"
)

func PushSupplierGoodsStatisticHandle() {
	task := cron.Task{
		Key:  "supplierGoodsStatistic",
		Name: "供应商数据统计",
		Spec: "0 0/10 * * * *",
		Handle: func(task cron.Task) {
			Statistic()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

type Product struct {
	ID             uint `json:"id"`
	SupplierID     uint `json:"supplier_id"`
	GatherSupplyId uint `json:"gather_supply_id"`
	Sales          int  `json:"sales"`
	GoodsCount     int  `json:"goods_count" gorm:"column:goods_count;"`
	AllGoodsCount  int  `json:"all_goods_count" gorm:"column:all_goods_count;"`
}
type OrderGoodsCount struct {
	Sales          int  `json:"sales"`
	GatherSupplyId uint `json:"gather_supply_id"`
	SupplierID     uint `json:"supplier_id"`
}

type OrderAmountStatistic struct {
	OrderPriceTotal uint `json:"order_price_total"`
	GatherSupplyId  uint `json:"gather_supply_id"`
	SupplierID      uint `json:"supplier_id"`
}

type AccountBalanceStatistic struct {
	SettlementBalanceTotal uint `json:"settlement_balance_total"`
	Uid                    int  `json:"uid"`
}

func Tsss() {
	var suppliers []model.Supplier
	var err error
	err = source.DB().Model(&model.Supplier{}).Find(&suppliers).Error

	var supplierUpdate []map[string]interface{}
	for _, supplier := range suppliers {
		//跳过自营 不然会把自营更新为0
		if supplier.IsSelfSupport == 1 {
			continue
		}
		var sales = 0
		var goodsCount = 0
		supplierUpdateRow := make(map[string]interface{})
		supplierUpdateRow["hot_sale"] = sales
		supplierUpdateRow["goods_count"] = goodsCount
		supplierUpdateRow["id"] = supplier.ID
		supplierUpdate = append(supplierUpdate, supplierUpdateRow)
	}

	err = source.BatchUpdate(supplierUpdate, "suppliers", "id")
	fmt.Print(err)
}

func Statistic() {
	var products []Product
	var allProducts []Product
	var product Product
	var orderGoodsCounts []OrderGoodsCount //供应链 供应商使用 #23487 热销件数调整为所有历史订单：待发货、待收货、已完成
	var orderGoodsCount OrderGoodsCount
	var err error

	var orderStaus = []int{1, 2, 3} //统计销量的订单状态

	//自营商品总数 -- start
	//自营商品总数 --  自营来源0  供应商0  供应链 0
	err = source.DB().Model(&model.Product{}).Select("count(*) as goods_count").Where("source = 0 and supplier_id = 0 and gather_supply_id = 0").Where("is_display = 1").Where("deleted_at is null").First(&product).Error
	if err != nil {
		product.GoodsCount = 0
	}
	//查询订单销量
	err = source.DB().Model(&model3.Order{}).Where("supplier_id = 0 and gather_supply_id = 0 and status in ?", orderStaus).Pluck("sum(goods_count) as sales", &orderGoodsCount.Sales).Error
	if err != nil {
		orderGoodsCount.Sales = 0
	}
	var selfSupplier model2.Supplier
	//没有则创建记录
	err = source.DB().Where("is_self_support = 1").First(&selfSupplier).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		selfSupplier.IsSelfSupport = 1
		selfSupplier.HotSale = orderGoodsCount.Sales
		selfSupplier.GoodsCount = uint(product.GoodsCount)
		err = source.DB().Create(&selfSupplier).Error
	} else {
		err = source.DB().Model(&model2.Supplier{}).Where("is_self_support = 1").Updates(&model2.Supplier{HotSale: orderGoodsCount.Sales, GoodsCount: uint(product.GoodsCount)}).Error
	}
	if err != nil {
		log.Log().Info("自动更新自营商品总数出错：" + err.Error())
		return
	}
	//自营商品总数 -- end

	//供应链商品总数 -- start
	err = source.DB().Model(&model.Product{}).Select("gather_supply_id,count(*) as goods_count, sum(sales) as sales").Where("supplier_id = 0 and gather_supply_id > 0").Where("is_display = 1").Where("deleted_at is null").Where("is_plugin = 0").Group("gather_supply_id").Find(&products).Error
	var gatherSupplyAverageScoreSetting setting.GatherSupplyAverageScoreSetting

	if err == nil {
		source.DB().Model(&model3.Order{}).Select("gather_supply_id,sum(goods_count) as sales").Where("supplier_id = 0 and gather_supply_id > 0 and status in ?", orderStaus).Group("gather_supply_id").Find(&orderGoodsCounts)
		for _, v := range products {
			_, gatherSupplyAverageScoreSetting = setting.GetGatherSupplyAverageScoreSetting("GatherSupplyAverageScore" + strconv.Itoa(int(v.GatherSupplyId)))
			for _, orderGoodsCountItem := range orderGoodsCounts {
				if v.GatherSupplyId == orderGoodsCountItem.GatherSupplyId {
					gatherSupplyAverageScoreSetting.Value.HotSale = orderGoodsCountItem.Sales
					break
				}
			}
			gatherSupplyAverageScoreSetting.Value.GoodsCount = v.GoodsCount
			gatherSupplyAverageScoreSetting.Key = "GatherSupplyAverageScore" + strconv.Itoa(int(v.GatherSupplyId))
			_ = setting.SetGatherSupplyAverageScoreSetting(gatherSupplyAverageScoreSetting)
		}
	}

	var suppliers []model.Supplier

	err = source.DB().Model(&model.Supplier{}).Find(&suppliers).Error
	//|| len(products) <= 0 //会因为没有供应链商品导致不执行供应商统计更新

	if err != nil {
		log.Log().Info("自动更新供应商商品统计出错：没有供应商")
		return
	}
	//供应商上架商品总数 -- end
	_ = source.DB().Model(&model.Product{}).Select("supplier_id, count(*) as goods_count").Where("is_display = 1").Where("supplier_id > 0 and deleted_at is null").Group("supplier_id").Find(&products).Error
	// 供应商商品总数
	_ = source.DB().Model(&model.Product{}).Select("supplier_id, count(*) as all_goods_count").Where("supplier_id > 0 and deleted_at is null").Group("supplier_id").Find(&allProducts).Error

	err = source.DB().Model(&model3.Order{}).Select("supplier_id,sum(goods_count) as sales").Where("supplier_id > 0 and status in ?", orderStaus).Group("supplier_id").Find(&orderGoodsCounts).Error
	// 供应商已完成订单总额
	var orderAmountStatistic []OrderAmountStatistic
	err = source.DB().Model(&model3.Order{}).Select("supplier_id,sum(amount) as order_price_total").Where("supplier_id > 0 and status = ?", 3).Group("supplier_id").Find(&orderAmountStatistic).Error
	// 供应商结算余额
	var accountBalanceStatistic []AccountBalanceStatistic
	err = source.DB().Model(&model4.AccountBalance{}).Select("uid, sum(settlement_balance) as settlement_balance_total").Where("type = ?", 2).Group("uid").Find(&accountBalanceStatistic).Error

	// products 转成map
	productsMap := make(map[uint]Product)
	for _, p := range products {
		productsMap[p.SupplierID] = p
	}
	// orderGoodsCounts 转成map
	orderGoodsCountsMap := make(map[uint]OrderGoodsCount)
	for _, o := range orderGoodsCounts {
		orderGoodsCountsMap[o.SupplierID] = o
	}
	// allProducts 转成map
	allProductsMap := make(map[uint]Product)
	for _, a := range allProducts {
		allProductsMap[a.SupplierID] = a
	}
	// orderAmountStatistic 转成map
	orderAmountStatisticMap := make(map[uint]OrderAmountStatistic)
	for _, s := range orderAmountStatistic {
		orderAmountStatisticMap[s.SupplierID] = s
	}
	// accountBalanceStatistic 转成map
	accountBalanceStatisticMap := make(map[uint]AccountBalanceStatistic)
	for _, ab := range accountBalanceStatistic {
		accountBalanceStatisticMap[uint(ab.Uid)] = ab
	}

	var supplierUpdate []map[string]interface{}
	for _, supplier := range suppliers {
		//跳过自营 不然会把自营更新为0
		if supplier.IsSelfSupport == 1 {
			continue
		}
		var sales = 0
		var goodsCount = 0
		var allGoodsCount = 0
		var orderPriceTotal uint
		orderPriceTotal = 0
		var settlementBalance uint
		settlementBalance = 0
		if len(productsMap) > 0 {
			if pr, ok := productsMap[uint(supplier.ID)]; ok {
				goodsCount = pr.GoodsCount
			}
		}
		if len(orderGoodsCountsMap) > 0 {
			if og, ok := orderGoodsCountsMap[uint(supplier.ID)]; ok {
				sales = og.Sales
			}
		}
		if len(allProductsMap) > 0 {
			if ap, ok := allProductsMap[uint(supplier.ID)]; ok {
				allGoodsCount = ap.AllGoodsCount
			}
		}
		if len(orderAmountStatisticMap) > 0 {
			if oa, ok := orderAmountStatisticMap[uint(supplier.ID)]; ok {
				orderPriceTotal = oa.OrderPriceTotal
			}
		}
		if len(accountBalanceStatisticMap) > 0 {
			if ab, ok := accountBalanceStatisticMap[supplier.Uid]; ok {
				settlementBalance = ab.SettlementBalanceTotal
			}
		}
		/*for _, v := range products {
			if uint(supplier.ID) == v.SupplierID {
				goodsCount = v.GoodsCount
				//使用订单销量
				for _, order := range orderGoodsCounts {
					if v.SupplierID == order.SupplierID {
						sales = order.Sales
						break
					}
				}
			}
		}
		for _, allProduct := range allProducts {
			if uint(supplier.ID) == allProduct.SupplierID {
				allGoodsCount = allProduct.AllGoodsCount
				for _, statistic := range orderAmountStatistic {
					if allProduct.SupplierID == statistic.SupplierID {
						orderPriceTotal = statistic.OrderPriceTotal
						break
					}
				}
			}
		}*/
		supplierUpdateRow := make(map[string]interface{})
		supplierUpdateRow["hot_sale"] = sales
		supplierUpdateRow["goods_count"] = goodsCount
		supplierUpdateRow["all_goods_count"] = allGoodsCount
		supplierUpdateRow["order_price_total"] = orderPriceTotal
		supplierUpdateRow["settlement_balance"] = settlementBalance
		supplierUpdateRow["id"] = supplier.ID
		supplierUpdate = append(supplierUpdate, supplierUpdateRow)
	}

	err = source.BatchUpdate(supplierUpdate, "suppliers", "id")
	if err != nil {
		log.Log().Info("自动更新供应商商品统计出错：" + err.Error())
		return
	}

	fmt.Println("自动更新供应商商品统计完成")
}
