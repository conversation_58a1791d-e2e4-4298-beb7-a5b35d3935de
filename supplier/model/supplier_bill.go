package model

import (
	"order/model"
	yzRequest "yz-go/request"
	"yz-go/source"
)

type SupplierSettlement struct {
	source.Model
	OrderSn              uint              `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:订单号;index;"`
	OrderID              uint              `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单id;index;"`
	SupplierID           uint              `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商id;index;"`
	PayType              int               `json:"pay_type" form:"pay_type" gorm:"column:pay_type;comment:支付方式;"`
	PayTime              *source.LocalTime `json:"pay_time" form:"pay_time" gorm:"column:pay_time;"`
	SettlementTime       *source.LocalTime `json:"settlement_time" form:"settlement_time" gorm:"column:settlement_time;"`
	GoodsPrice           uint              `json:"goods_price" form:"goods_price" gorm:"column:goods_price;comment:订单商品金额;"`
	FreightPrice         uint              `json:"freight_price" form:"freight_price" gorm:"column:freight_price;comment:运费金额;"`
	WithdrawalFee        uint              `json:"withdrawal_fee" form:"withdrawal_fee" gorm:"column:withdrawal_fee;comment:提现手续费;"`
	WithdrawalRatio      int               `json:"withdrawal_ratio" form:"withdrawal_ratio" gorm:"column:withdrawal_ratio;comment:提现手续费比例;"`
	RefundAmount         uint              `json:"refund_amount" form:"refund_amount" gorm:"column:refund_amount;comment:退款金额;"`
	SettlementAmount     uint              `json:"settlement_amount" form:"settlement_amount" gorm:"column:settlement_amount;comment:结算金额;"`
	TechnicalServiceCost uint              `json:"technical_service_cost" form:"technical_service_cost" gorm:"column:technical_service_cost;comment:供应商技术服务费;"`
	TechnicalServicesFee uint              `json:"technical_services_fee" form:"technical_services_fee" gorm:"column:technical_services_fee;comment:采购技术服务费(分);"`
	Status               uint              `json:"status" form:"status" gorm:"column:status;comment:状态;"`
	Supplier             Supplier          `json:"Supplier" gorm:"foreignKey:SupplierID"`
	PayInfoID            uint              `json:"pay_info_id" form:"pay_info_id" gorm:"column:pay_info_id;comment:支付id;index;"`
	PaySn                uint              `json:"pay_sn" form:"pay_sn" gorm:"column:pay_sn;comment:支付sn;index;"`
	TaskStatus           uint              `json:"task_status" form:"task_status" gorm:"column:task_status;default:0;comment:处理状态(1已处理);"`
	Order                model.Order       `json:"order" gorm:"foreignKey:OrderID"`
	WithdrawalStatus     int               `json:"withdrawal_status"  gorm:"default:0"` //0  未提现    1 驳回     2 已申请  3 通过  4  无效
	DeductionType        uint              `json:"deduction_type"  gorm:"default:2"`
	DeductionRatio       int               `json:"deduction_ratio"  gorm:"default:0"` //  技术服务费比例
	SettlementType       int               `json:"settlement_type"  gorm:"default:0"` //   0订单   1  供货价
	SupplyAmount         uint              `json:"supply_amount" form:"supply_amount" gorm:"column:supply_amount;comment:供货金额(分);"`
	RemitStatus          int               `json:"remit_status"  gorm:"remit_status:0"` //打款状态(0待打款，1已打款,2打款中)

}

type WithdrawalInvoice struct {
	source.Model

	WithdrawalID uint   `json:"withdrawal_id" gorm:"column:withdrawal_id;comment:提现ID"`
	InvoiceUrl   string `json:"invoice_url" gorm:"column:invoice_url;comment:发票url"`
}

type AccountApplySearch struct {
	WithdrawalStatus string `json:"withdrawal_status" form:"withdrawal_status"` //withdrawal_status       0  未提现    1 驳回     2 已申请  3 通过  4  无效
	RemitStatus      string `json:"remit_status" form:"remit_status"`           // remit_status   (0待打款，1已打款,2打款中)',
	RealName         string `json:"real_name" form:"real_name" gorm:"column:real_name;comment:真实姓名;type:varchar(255);size:255;"`
	NameType         int    `json:"name_type" form:"name_type" gorm:"column:name_type;comment:名称类型;type:varchar(255);size:255;"`
	LegalType        int    `json:"legal_type" form:"legal_type" gorm:"column:legal_type;comment:法人类型;type:varchar(255);size:255;"`
	LegalPerson      string `json:"legal_person" form:"legal_person" gorm:"column:legal_person;comment:法人信息;type:varchar(255);size:255;"`
	MinBalance       int    `json:"min_balance" form:"min_balance"`
	MaxBalance       int    `json:"max_balance" form:"max_balance"`
	BusinessType     int    `json:"business_type" form:"business_type"`
	TimeS            string `json:"times" form:"times" gorm:"column:times;comment:起始时间;type:varchar(255);size:255;"`
	TimeE            string `json:"timee" form:"timee" gorm:"column:timee;comment:结束时间;type:varchar(255);size:255;"`
	Type             int    `json:"type" form:"type" gorm:"column:type;comment:类型;type:varchar(200);size:200;"`
	OrderSn          int    `json:"order_sn" form:"order_sn" gorm:"column:order_sn;"`
	Status           int    `json:"status" form:"status" gorm:"column:status;"`
	SettID           int    `json:"sett_id" form:"sett_id" gorm:"column:type;comment:结算ID;type:varchar(200);size:200;"`
	PayType          *int   `json:"pay_type" form:"pay_type" gorm:"column:type;comment:支付方式;type:varchar(200);size:200;"`
	Level            int    `json:"level" form:"level" gorm:"column:level;comment:会员等级;type:varchar(200);size:200;"`
	SupplierID       uint   `json:"supplier_id" form:"supplier_id"`
	WithdrawalID     uint   `json:"withdrawal_id" form:"withdrawal_id"` //提现id

	model.User

	yzRequest.PageInfo
}
