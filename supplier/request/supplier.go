package request

import (
	"supplier/model"
	yzRequest "yz-go/request"
)

type SupplierSearch struct {
	model.Supplier
	yzRequest.PageInfo
}

type ChangePassword struct {
	ID uint `json:"id"  form:"id" query:"id"`
	//Password    string `json:"password"  form:"password" query:"password"`
	NewPassword   string `json:"new_password"  form:"new_password" query:"new_password"`
	PasswordAgain string `json:"password_again"  form:"password_again" query:"password_again"`
}

type GetSupplierProductListSearch struct {
	model.Supplier
	yzRequest.PageInfo
	Type       int  `json:"type"`                                        // 1热销 2商品数量
	Order      int  `json:"order"`                                       // 1正序 2倒叙
	CategoryID uint `json:"category_id" form:"category_id" gorm:"index"` //分类id                                          // 一级分类
	SortType   int  `json:"sort_type"`
}

type GetGatherSupplierProductListSearch struct {
	yzRequest.PageInfo
	Category1ID uint `json:"category1_id" form:"category1_id" gorm:"index"` //分类id                                          // 一级分类
}
