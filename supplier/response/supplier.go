package response

import (
	"supplier/model"
	"yz-go/source"
)

type Supplier struct {
	source.Model
	Name string `json:"name" form:"name" gorm:"column:name;comment:供应商名称;type:varchar(255);size:255;"`
}

type SupplierProduct struct {
	ID            uint                   `json:"id" form:"id" gorm:"primarykey"`
	ShopName      string                 `json:"shop_name" form:"shop_name" gorm:"column:shop_name;comment:店铺名称;type:varchar(255);size:255;"`
	ShopLogo      string                 `json:"shop_logo" form:"shop_logo" gorm:"column:shop_logo;comment:店铺logo;type:varchar(250);size:250;"`
	GoodsCount    uint                   `json:"goods_count" form:"goods_count" gorm:"column:goods_count;comment:商品数量;"`
	HotSale       int                    `json:"hot_sale" form:"hot_sale" gorm:"column:hot_sale;comment:热销数量;"`
	Product       []Product              `json:"product" gorm:"-"` //商品--显示四个
	CategoryId    uint                   `json:"category_id" form:"category_id" gorm:"column:category_id;comment:分类id;"`
	CategoryInfo  model.SupplierCategory `json:"category_info" gorm:"foreignKey:CategoryId;references:ID"`
	IsSelfSupport int                    `json:"is_self_support"` //是否自营

}

func (SupplierProduct) TableName() string {
	return "suppliers"
}

type Product struct {
	source.Model        // id
	Title        string `json:"title" example:"标题"`                                                 // 标题
	OriginPrice  int    `json:"origin_price" example:120`                                           // 市场价、划线价（分为单位）建议零售价
	Price        int    `json:"price" example:100`                                                  // 售价、供货价（分为单位）
	ExecPrice    int    `json:"exec_price" from:"exec_price"  gorm:"column:cost_price" example:100` // 成本价（分为单位）
	MaxPrice     int    `json:"max_price" example:100`
	MinPrice     int    `json:"min_price" example:100`
	ImageUrl     string `json:"thumb" example:"https://images.renrencai.cn/1.jpg"`                                                  // 图片地址
	GuidePrice   int    `json:"guide_price" example:120`                                                                            // 指导价、划线价（分为单位）（指导价-售价 = 利润）
	Sales        int    `json:"sales" example:120`                                                                                  // 销量
	Stock        uint   `json:"stock" example:120`                                                                                  // 库存
	DeletedAt    string `json:"deleted_at"`                                                                                         //删除时间
	IsDisplay    int    `json:"is_display" form:"is_display" gorm:"column:is_display;comment:上架（1是0否）;type:smallint;size:1;index;"` // 上架（1是0否）
	Freeze       uint   `json:"freeze" form:"freeze" gorm:"column:freeze;comment:是否冻结;default:0;"`                                  // 冻结

	SupplierID uint `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商id;index;"` // 供应商id
}

type GatherSupplierProduct struct {
	source.Model
	ShopName   string    `json:"shop_name" form:"shop_name" gorm:"-"`
	ShopLogo   string    `json:"shop_logo" form:"shop_logo" gorm:"-"`
	GoodsCount uint      `json:"goods_count" form:"goods_count" gorm:"-"`
	HotSale    int       `json:"hot_sale" form:"hot_sale" gorm:"-"`
	Product    []Product `json:"product" gorm:"-"` //商品--显示四个
}
