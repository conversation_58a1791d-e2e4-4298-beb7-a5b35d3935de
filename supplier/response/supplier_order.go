package response

import (
	"yz-go/response"
)

type OrderStatistic struct {
	TotalAmount  int `json:"total_amount"`
	TotalFreight int `json:"total_freight"`
}

type PageResult struct {
	response.PageResult
	Statistic OrderStatistic `json:"statistic"`
}

type PayInfo struct {
	ID     uint   `json:"id"`
	PaySn  string `json:"pay_sn"`
	Status string `json:"status"`
}

type User struct {
	ID       uint   `json:"id"`
	NickName string `json:"nickname"`
	Mobile   string `json:"mobile"`
	Avatar   string `json:"avatar"`
	Username string `json:"username"`
}
