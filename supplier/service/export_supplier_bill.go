package service

import (
	"finance/request"
	"finance/service"
	"fmt"
	"os"
	"strconv"
	"time"
	"yz-go/config"
	"yz-go/utils"

	"github.com/360EntSecGroup-Skylar/excelize"
)

// ExportSupplierBill 导出供应商提现记录Excel
func ExportSupplierBill(info request.WithdrawSearch) (err error, link string) {
	// 获取供应商提现记录数据
	err, withdrawList, _ := GetSupplierBill(info)
	if err != nil {
		return
	}

	// 创建Excel文件
	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")
	// 设置单元格的值（表头）
	f.SetCellValue("Sheet1", "A1", "ID")
	f.SetCellValue("Sheet1", "B1", "提现时间")
	f.SetCellValue("Sheet1", "C1", "提现编号")
	f.<PERSON>ell<PERSON>("Sheet1", "D1", "提现方式")
	f.<PERSON><PERSON>("Sheet1", "E1", "提现金额")
	f.<PERSON>ellValue("Sheet1", "F1", "提现手续费")
	f.SetCellValue("Sheet1", "G1", "驳回金额")
	f.SetCellValue("Sheet1", "H1", "无效金额")
	f.SetCellValue("Sheet1", "I1", "实际打款金额")
	f.SetCellValue("Sheet1", "J1", "审核状态")
	f.SetCellValue("Sheet1", "K1", "开票状态")
	f.SetCellValue("Sheet1", "L1", "打款状态")

	// 填充数据
	for i, v := range withdrawList {
		rowIndex := i + 2 // 从第2行开始填充数据

		// 获取提现方式名称
		withdrawalModeName := ""
		switch v.WithdrawalMode {
		case 1:
			withdrawalModeName = "手动提现"
		case 2:
			withdrawalModeName = "汇聚代付"

		default:
			withdrawalModeName = "未知方式"
		}

		// 获取审核状态   0待审核，1通过，2无效
		withdrawalStatusName := ""
		switch v.WithdrawalStatus {
		case 0:
			withdrawalStatusName = "待审核"
		case 1:
			withdrawalStatusName = "通过"
		case 2:
			withdrawalStatusName = "无效"
		case 3:
			withdrawalStatusName = "驳回"
		default:
			withdrawalStatusName = "未知状态"
		}

		// 获取打款状态
		remitStatusName := ""
		switch v.RemitStatus {
		case 0:
			remitStatusName = "待打款"
		case 1:
			remitStatusName = "已打款"
		case 2:
			remitStatusName = "打款中"
		case 4:
			remitStatusName = "无需打款"
		default:
			remitStatusName = "未知状态"
		}

		// 获取开票状态
		InvoiceStatusName := ""
		switch v.InvoiceStatus {
		case 0:
			InvoiceStatusName = "无需开票"
		case 1:
			InvoiceStatusName = "待开票"
		case 2:
			InvoiceStatusName = "已开票"

		default:
			InvoiceStatusName = "未知状态"
		}
		var invalidAmount, rejectedAmount uint
		for _, item := range v.WithdrawalOperation {
			invalidAmount = invalidAmount + item.InvalidAmount
			rejectedAmount = rejectedAmount + item.RejectedAmount

		}

		// 设置单元格值
		f.SetCellValue("Sheet1", "A"+strconv.Itoa(rowIndex), v.ID)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(rowIndex), v.CreatedAt.Format("2006-01-02 15:04:05"))
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(rowIndex), v.OrderSn)
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(rowIndex), withdrawalModeName)
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(rowIndex), service.Fen2Yuan(v.WithdrawalAmount))
		f.SetCellValue("Sheet1", "F"+strconv.Itoa(rowIndex), service.Fen2Yuan(v.PoundageAmount))
		f.SetCellValue("Sheet1", "G"+strconv.Itoa(rowIndex), service.Fen2Yuan(invalidAmount))
		f.SetCellValue("Sheet1", "H"+strconv.Itoa(rowIndex), service.Fen2Yuan(rejectedAmount))
		f.SetCellValue("Sheet1", "I"+strconv.Itoa(rowIndex), service.Fen2Yuan(v.IncomeAmount))
		f.SetCellValue("Sheet1", "J"+strconv.Itoa(rowIndex), withdrawalStatusName)
		f.SetCellValue("Sheet1", "K"+strconv.Itoa(rowIndex), InvoiceStatusName)
		f.SetCellValue("Sheet1", "L"+strconv.Itoa(rowIndex), remitStatusName)
	}

	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)

	// 创建导出目录
	timeString := time.Now().Format("20060102150405")
	path := config.Config().Local.Path + "/export_supplier_bill"
	exist, _ := utils.PathExists(path)

	if !exist {
		// 创建文件夹
		err = os.Mkdir(path, os.ModePerm)
		if err != nil {
			fmt.Printf("mkdir failed![%v]\n", err)
		} else {
			fmt.Printf("mkdir success!\n")
		}
	}

	// 生成文件名
	fileName := timeString + "_供应商提现记录导出.xlsx"
	link = path + "/" + fileName

	// 保存文件
	if err = f.SaveAs(link); err != nil {
		return
	}

	return err, link
}
