package service

import (
	fservice "finance/service"
	"fmt"
	"os"
	"time"
	config2 "yz-go/config"

	"github.com/360EntSecGroup-Skylar/excelize"

	fmodel "finance/model"
	"finance/request"
	"supplier/model"
	model3 "user/model"
	"yz-go/source"
	"yz-go/utils"
)

// CreateInvoice 创建发票记录
func CreateInvoice(invoice *model.WithdrawalInvoice) (err error) {
	err = source.DB().Create(&invoice).Error
	return
}

// DeleteInvoice 删除发票记录
func DeleteInvoice(id uint) (err error) {
	err = source.DB().Where("id = ?", id).Delete(&model.WithdrawalInvoice{}).Error
	return
}

// GetInvoice 根据ID获取发票信息
func GetInvoice(id uint) (err error, invoice model.WithdrawalInvoice) {
	err = source.DB().Where("withdrawal_id = ?", id).First(&invoice).Error
	return
}

// GetInvoiceByWithdrawalID 根据提现ID获取发票信息
func GetInvoiceByWithdrawalID(withdrawalID uint) (err error, invoice model.WithdrawalInvoice) {
	err = source.DB().Where("withdrawal_id = ?", withdrawalID).First(&invoice).Error
	return
}

// GetInvoiceList 获取发票列表
func GetInvoiceList(page, pageSize int) (err error, list []model.WithdrawalInvoice, total int64) {
	db := source.DB().Model(&model.WithdrawalInvoice{})

	// 获取总数
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 分页查询
	err = db.Limit(pageSize).Offset((page - 1) * pageSize).Find(&list).Error
	return
}

func GetSupplierBillDetail(info request.AccountApplySearch) (err error, list []model.SupplierSettlement, total int64) {

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.SupplierSettlement{})
	var supplierSettlement []model.SupplierSettlement
	// 如果有条件搜索 下方会自动创建搜索语句

	if info.WithdrawalDetail == 1 {

		var Ids []uint
		err = source.DB().Model(model.SupplierSettlement{}).Where("status=1 and settlement_amount >0 and  withdrawal_status<2 and supplier_id=?", info.SupplierID).Pluck("id", &Ids).Error

		if err != nil {

		}

		if len(Ids) > 0 {
			db.Where("id in (?)", Ids)

		}

	}

	if info.WithdrawalID > 0 {
		var withdrawalDetailIds []uint
		err = source.DB().Model(&fmodel.WithdrawalDetail{}).Where("withdrawal_id =?", info.WithdrawalID).Pluck("supplier_settlement_id", &withdrawalDetailIds).Error
		if len(withdrawalDetailIds) > 0 {
			db = db.Where("supplier_settlements.id IN?", withdrawalDetailIds)
		} else {
			return
		}
	}

	var joinWhere, orderJoin string
	joinWhere = "left join suppliers on suppliers.id = supplier_settlements.supplier_id"
	if info.SupplierID > 0 {

		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id "
		db = db.Where("suppliers.id = ?", info.SupplierID)
	}

	if info.NickName != "" {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id "
		db = db.Where("suppliers.name = ?", info.NickName)
	}
	if info.Mobile != "" {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id "
		db = db.Where("suppliers.mobile = ?", info.Mobile)

	}
	if info.RealName != "" {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id "
		db = db.Where("suppliers.realname = ?", info.RealName)

	}

	if info.WithdrawalStatus != "" {
		db = db.Where("supplier_settlements.withdrawal_status = ?", info.WithdrawalStatus)
	}

	if info.RemitStatus != nil {
		db = db.Where("supplier_settlements.remit_status = ?", info.RemitStatus)
	}

	if info.SettID > 0 {
		db = db.Where("supplier_settlements.id = ?", info.SettID)
	}
	if info.OrderSn > 0 {
		db = db.Where("supplier_settlements.order_sn = ?", info.OrderSn)
	}
	if info.Status > 0 {
		db = db.Where("supplier_settlements.status = ?", info.Status-1)
	}
	if info.PayType != nil {
		db = db.Where("supplier_settlements.pay_type = ?", info.PayType)
	}

	if info.TimeS != "" && info.TimeE != "" {
		orderJoin = "INNER join orders on orders.id = supplier_settlements.order_id"
		db = db.Where("orders.created_at BETWEEN ? AND ?", info.TimeS, info.TimeE)

	}

	err = db.Joins(joinWhere).Joins(orderJoin).Count(&total).Error
	err = db.Select("supplier_settlements.*").Limit(limit).Offset(offset).Preload("Order").Preload("Supplier").Order("supplier_settlements.id desc").Find(&supplierSettlement).Error
	return err, supplierSettlement, total

}

func GetSupplierBillDetailTotal(info request.AccountApplySearch) (err error, list interface{}) {
	var dataMap map[string]interface{}
	dataMap = make(map[string]interface{})

	var orderAmount, costAmount, freight, technicalServicesFee, supplierBillAmount uint64

	var totalCount int64

	db := source.DB().Model(&model.SupplierSettlement{})

	db.Where("supplier_id=?", info.SupplierID)
	if info.WithdrawalID > 0 {

		var settlementIds []uint
		err = source.DB().Model(&fmodel.WithdrawalDetail{}).Where("withdrawal_id =?", info.WithdrawalID).Pluck("supplier_settlement_id", &settlementIds).Error

		if len(settlementIds) > 0 {
			db = db.Where("supplier_settlements.id IN ?", settlementIds)
		}

	}

	if info.WithdrawalDetail == 1 {

		var Ids []uint
		err = source.DB().Model(model.SupplierSettlement{}).Where("status=1 and settlement_amount >0 and  withdrawal_status<2 and supplier_id=?", info.SupplierID).Pluck("id", &Ids).Error

		if err != nil {

		}

		if len(Ids) > 0 {
			db.Where("id in (?)", Ids)

		}

	}

	// 如果有条件搜索 下方会自动创建搜索语句

	var joinWhere, orderJoin string
	joinWhere = "left join suppliers on suppliers.id = supplier_settlements.supplier_id"
	if info.SupplierID > 0 {

		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id "
		db = db.Where("suppliers.id = ?", info.SupplierID)
	}

	if info.NickName != "" {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id "
		db = db.Where("suppliers.name = ?", info.NickName)
	}
	if info.Mobile != "" {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id "
		db = db.Where("suppliers.mobile = ?", info.Mobile)

	}
	if info.RealName != "" {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id "
		db = db.Where("suppliers.realname = ?", info.RealName)

	}

	if info.WithdrawalStatus != "" {
		db = db.Where("supplier_settlements.withdrawal_status = ?", info.WithdrawalStatus)
	}

	if info.RemitStatus != nil {
		db = db.Where("supplier_settlements.remit_status = ?", *info.RemitStatus)
	}

	if info.SettID > 0 {
		db = db.Where("supplier_settlements.id = ?", info.SettID)
	}
	if info.OrderSn > 0 {
		db = db.Where("supplier_settlements.order_sn = ?", info.OrderSn)
	}
	if info.Status > 0 {
		db = db.Where("supplier_settlements.status = ?", info.Status-1)
	}
	if info.PayType != nil {
		db = db.Where("supplier_settlements.pay_type = ?", info.PayType)
	}
	orderJoin = "INNER join orders on orders.id = supplier_settlements.order_id"

	if info.TimeS != "" && info.TimeE != "" {
		db = db.Where("orders.created_at BETWEEN ? AND ?", info.TimeS, info.TimeE)

	}

	err = db.Joins(orderJoin).Joins(joinWhere).Count(&totalCount).Error
	err = db.Select("COALESCE(SUM(settlement_amount), 0) as amount1").First(&supplierBillAmount).Error

	err = db.Select("COALESCE(SUM(orders.amount), 0) as amount1").First(&orderAmount).Error
	err = db.Select("COALESCE(SUM(orders.cost_amount), 0) as amount1").First(&costAmount).Error
	err = db.Select("COALESCE(SUM(orders.freight), 0) as amount1").First(&freight).Error
	err = db.Select("COALESCE(SUM(orders.technical_services_fee), 0) as amount1").First(&technicalServicesFee).Error

	dataMap["orderAmount"] = orderAmount
	dataMap["costAmount"] = costAmount
	dataMap["freight"] = freight
	dataMap["technicalServicesFee"] = technicalServicesFee
	dataMap["supplierBillAmount"] = supplierBillAmount
	dataMap["total_count"] = uint64(totalCount)

	list = dataMap
	return
}

// ExportSupplierBillDetail 导出供应商结算明细Excel
func ExportSupplierBillDetail(info request.AccountApplySearch) (err error, link string) {
	// 获取供应商结算明细数据
	err, list, _ := GetSupplierBillDetail(info)
	if err != nil {
		return
	}

	// 创建Excel文件
	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")
	// 设置单元格的值（表头）
	f.SetCellValue("Sheet1", "A1", "ID")
	f.SetCellValue("Sheet1", "B1", "时间")
	f.SetCellValue("Sheet1", "C1", "订单号")
	f.SetCellValue("Sheet1", "D1", "订单金额")
	f.SetCellValue("Sheet1", "E1", "成本价")
	f.SetCellValue("Sheet1", "F1", "结算类型")
	f.SetCellValue("Sheet1", "G1", "订单结算金额")
	f.SetCellValue("Sheet1", "H1", "平台扣点比例")
	f.SetCellValue("Sheet1", "I1", "技术服务费(元)")
	f.SetCellValue("Sheet1", "J1", "账单金额(元)")
	f.SetCellValue("Sheet1", "K1", "提现状态")
	f.SetCellValue("Sheet1", "L1", "提现手续费")
	f.SetCellValue("Sheet1", "M1", "打款状态")
	f.SetCellValue("Sheet1", "N1", "打款金额")

	// 填充数据
	for i, v := range list {
		rowIndex := i + 2 // 从第2行开始填充数据

		// 获取提现状态文本
		withdrawalStatusText := "未提现"
		switch v.WithdrawalStatus {
		case 0:
			withdrawalStatusText = "未提现"
		case 1:
			withdrawalStatusText = "驳回"
		case 2:
			withdrawalStatusText = "已申请"
		case 3:
			withdrawalStatusText = "通过"
		case 4:
			withdrawalStatusText = "无效"
		}

		// 获取打款状态文本
		remitStatusText := "未打款"
		switch v.RemitStatus {
		case 0:
			remitStatusText = "待打款"
		case 1:
			remitStatusText = "已打款"
		case 2:
			remitStatusText = "打款中"
		}

		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", rowIndex), v.ID)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", rowIndex), v.CreatedAt.Format("2006-01-02 15:04:05"))
		f.SetCellValue("Sheet1", fmt.Sprintf("C%d", rowIndex), v.OrderSn)
		f.SetCellValue("Sheet1", fmt.Sprintf("D%d", rowIndex), v.Order.Amount)
		f.SetCellValue("Sheet1", fmt.Sprintf("E%d", rowIndex), fservice.Fen2Yuan(v.Order.CostAmount))
		f.SetCellValue("Sheet1", fmt.Sprintf("F%d", rowIndex), "")
		f.SetCellValue("Sheet1", fmt.Sprintf("G%d", rowIndex), fservice.Fen2Yuan(v.SettlementAmount))
		f.SetCellValue("Sheet1", fmt.Sprintf("H%d", rowIndex), v.DeductionRatio)
		f.SetCellValue("Sheet1", fmt.Sprintf("I%d", rowIndex), fservice.Fen2Yuan(v.TechnicalServiceCost))
		f.SetCellValue("Sheet1", fmt.Sprintf("J%d", rowIndex), fservice.Fen2Yuan(v.SettlementAmount))
		f.SetCellValue("Sheet1", fmt.Sprintf("K%d", rowIndex), withdrawalStatusText)
		f.SetCellValue("Sheet1", fmt.Sprintf("L%d", rowIndex), fservice.Fen2Yuan(v.WithdrawalFee))
		f.SetCellValue("Sheet1", fmt.Sprintf("M%d", rowIndex), remitStatusText)
		f.SetCellValue("Sheet1", fmt.Sprintf("N%d", rowIndex), fservice.Fen2Yuan(v.SettlementAmount))
	}

	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)

	// 创建导出目录
	timeString := time.Now().Format("20060102150405")
	path := config2.Config().Local.Path + "/export_supplier_bill_detail"
	exist, _ := utils.PathExists(path)

	if !exist {
		// 创建文件夹
		err = os.Mkdir(path, os.ModePerm)
		if err != nil {
			fmt.Printf("mkdir failed![%v]\n", err)
		} else {
			fmt.Printf("mkdir success!\n")
		}
	}

	// 生成文件名
	fileName := timeString + "_供应商结算明细导出.xlsx"
	link = path + "/" + fileName

	// 保存文件
	if err = f.SaveAs(link); err != nil {
		return
	}

	return err, link
}

func GetUserBankList(uid uint) (err error, bank []model3.UserBank) {
	err = source.DB().Where("user_id=? ", uid).Find(&bank).Error
	return

}

func DeleteUserBank(id, uid uint) (err error) {
	err = source.DB().Where("id=? and user_id=?", id, uid).Delete(&model3.UserBank{}).Error
	return

}
func SaveUserBank(bank model3.UserBank) (err error) {
	if bank.IsDefault == 1 {
		source.DB().Model(&model3.UserBank{}).Where("user_id = ?", bank.UserID).Update("is_default", 0)
	} else {
		source.DB().Model(&model3.UserBank{}).Where("id = ?", bank.ID).Update("is_default", 0)

	}
	// 手动调用钩子
	if err = bank.BeforeSave(source.DB()); err != nil {
		return err
	}
	err = source.DB().Updates(&bank).Error
	return

}

func CreateBank(bank model3.UserBank) (err error) {
	if bank.IsDefault == 1 {
		source.DB().Model(&model3.UserBank{}).Where("user_id = ?", bank.UserID).Update("is_default", 0)
	}
	err = source.DB().Create(&bank).Error
	return

}
