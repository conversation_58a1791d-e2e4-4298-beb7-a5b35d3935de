package service

import (
	"shipping/model"
	"shipping/request"
	"yz-go/source"
)

// GetExpressTemplatesInfoList
//
// @function: GetExpressTemplatesInfoList
// @description: 分页获取ExpressTemplates记录
// @param: info request.ExpressTemplatesSearch
// @return: err error, list interface{}, total int64
func GetExpressTemplatesInfoList(info request.SupplierExpressTemplatesSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.ExpressTemplate{})
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.Name != "" {
		db.Where("name like ?", "%"+info.Name+"%")
	}
	//所有运费模板都不显示给供应商  2023-12-12 - 肖韶笑
	if info.GatherSupplyID != nil {
		if *info.GatherSupplyID == 0 {
			db.Where("gather_supply_id = ? or gather_supply_id is null", info.GatherSupplyID)
		} else {
			db.Where("gather_supply_id = ?", info.GatherSupplyID)

		}
	}
	_, supplierSetting := GetSetting()
	//如果设置了供应商不显示自营运费模板
	if supplierSetting.Values.IsSelfFreightTemplate == 1 {
		db.Where("supplier_id = ?", info.SupplierID)
	} else {
		db.Where("supplier_id = ? or supplier_id = 0", info.SupplierID)
	}
	err = db.Count(&total).Error
	var expressTemplates []model.ExpressTemplate
	err = db.Limit(limit).Offset(offset).Find(&expressTemplates).Error
	return err, expressTemplates, total
}
