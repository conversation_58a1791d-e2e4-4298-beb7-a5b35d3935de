package utils

import "yz-go/utils"

var (
	//SupplierApplyVerify	   = utils.Rules{"UserID": {utils.NotEmpty()}, "Name": {utils.NotEmpty()}, "Username": {utils.NotEmpty()}, "Password": {utils.NotEmpty()}}
	SupplierApplyVerify          = utils.Rules{"Uid": {utils.NotEmpty()}, "Name": {utils.NotEmpty()}, "Username": {utils.NotEmpty()}, "Password": {utils.NotEmpty()}}
	ChangeSupplierPasswordVerify = utils.Rules{"ID": {utils.NotEmpty()}, "NewPassword": {utils.NotEmpty()}, "PasswordAgain": {utils.NotEmpty()}}
)
