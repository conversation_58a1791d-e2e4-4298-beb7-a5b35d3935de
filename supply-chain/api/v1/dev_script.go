package v1

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	migration2 "supply-chain/component/migration"
	"supply-chain/model/migration"
	"yz-go/component/log"
	yzResponse "yz-go/response"
)

type ReplaceFileUrlFieldRequest struct {
	Host string `json:"host"`
}

func ReplaceFileUrlField(c *gin.Context) {
	var replaceFileUrlFieldRequest ReplaceFileUrlFieldRequest
	_ = c.ShouldBindJSON(&replaceFileUrlFieldRequest)
	if err := migration.ReplaceFileUrlField(replaceFileUrlFieldRequest.Host); err != nil {
		log.Log().Error("执行失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("执行失败", c)
		return
	} else {
		yzResponse.OkWithMessage("执行成功", c)
	}
}

type TestMigrateRequest struct {
	OnlyRunIndex []int `json:"only_run_index"`
}

func TestMigrate(c *gin.Context) {
	var testMigrateRequest TestMigrateRequest
	_ = c.ShouldBindJ<PERSON>N(&testMigrateRequest)
	migrations := migration2.GetMigrates()
	for i, v := range migrations {
		// 如果指定了OnlyRunIndex，只执行OnlyRunIndex中的迁移
		if len(testMigrateRequest.OnlyRunIndex) > 0 {
			if !contains(testMigrateRequest.OnlyRunIndex, i) {
				continue
			}
		}
		if err := v(); err != nil {
			log.Log().Error(fmt.Sprintf("测试迁移出错%d", i), zap.Any("err", err))
			yzResponse.FailWithMessage("执行失败", c)
			return
		}
	}
	yzResponse.OkWithMessage("执行成功", c)
}
func contains(s []int, e int) bool {
	for _, a := range s {
		if a == e {
			return true
		}
	}
	return false

}
