services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.10.2
    container_name: elasticsearch
    environment:
      - discovery.type=single-node
    ports:
      - "9200:9200"
      - "9300:9300"
    privileged: true
    volumes:
      - es-data:/usr/share/elasticsearch/data
      - es-plugins:/usr/share/elasticsearch/plugins

  kibana:
    image: docker.elastic.co/kibana/kibana:7.10.2
    container_name: kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch

  redis:
    image: redis:latest
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data

  rabbitmq:
    image: rabbitmq:management
    container_name: rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      # 登陆账号
      - RABBITMQ_DEFAULT_USER=guest
      # 登陆密码
      - RABBITMQ_DEFAULT_PASS=guest
      # 增加可视化管理插件
      - RABBITMQ_MANAGEMENT_LOAD_PLUGIN=rabbitmq_management,rabbitmq_management_visualiser
    volumes:
      - rabbitmq-data:/var/lib/rabbitmq

  mysql:
    image: mysql:latest
    container_name: mysql
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=supply
      - MYSQL_USER=supply
      - MYSQL_PASSWORD=supply
    volumes:
      - mysql-data:/var/lib/mysql

volumes:
  es-data:
  es-plugins:
  mysql-data:
  redis-data:
  rabbitmq-data:
