module supply-chain

go 1.21

toolchain go1.21.0

require (
	ad v1.0.0
	after-sales v1.0.0
	ali-open v1.0.0
	ali-selected v1.0.0
	application v1.0.0
	area-agency v1.0.0
	byn-supply v1.0.0
	category v1.0.0
	cloud v1.0.0
	comment v1.0.0
	convergence v1.0.0
	course-distribution v1.0.0
	cps v1.0.0
	cross-border-supply v1.0.0
	dahang-erp v1.0.0
	distributor v1.0.0
	distributor-tool v1.0.0
	douyin-cps v1.0.0
	douyin-group v0.0.0-00010101000000-000000000000
	dwd-supply v1.0.0
	event-distribution v1.0.0
	favorite v1.0.0
	finance v1.0.0
	fulu-supply v1.0.0
	gather-supply v1.0.0
	gin-vue-admin v1.0.0
	github.com/chenhg5/collection v0.0.0-20200925143926-f403b87088f9
	github.com/fvbock/endless v0.0.0-20170109170031-447134032cb6
	github.com/gin-gonic/gin v1.7.7
	github.com/gookit/color v1.3.6
	github.com/juju/ratelimit v1.0.2
	go.uber.org/zap v1.21.0
	gongmall v1.0.0
	gorm.io/gorm v1.25.5
	hbsk v1.0.0
	hehe-supply v1.0.0
	install v1.0.0
	jd-supply v1.0.0
	jd-vop-supply v1.0.0
	jushuitan v1.0.0
	jushuitan-supply v1.0.0
	knowledge-base v1.0.0
	lakala v1.0.0
	lease v1.0.0
	lianlian v1.0.0
	lijing-supply v1.0.0
	material-distribute v1.0.0
	meituan-distributor v1.0.0
	merchant v1.0.0
	maiger-supply v1.0.0
	monitor v1.0.0
	notification v1.0.0
	operation v1.0.0
	order v1.0.0
	order-export v1.0.0
	payment v1.0.0
	plugin v1.0.0
	product v1.0.0
	product-album v1.0.0
	promotion v1.0.0
	public-supply v1.0.0
	purchase-account v1.0.0
	race-cinema-ticket v1.0.0
	region v1.0.0
	region-match v1.0.0
	sales v1.0.0
	script-distribute v1.0.0
	self-supply v1.0.0
	service-provider-system v1.0.0
	share-live v1.0.0
	shipping v1.0.0
	shop v1.0.0
	shopping-cart v1.0.0
	small-shop v1.0.0
	small-shop-video v1.0.0
	stbz-supply v1.0.0
	supplier v1.0.0
	surface-single v1.0.0
	szbao-supply v1.0.0
	thousands-prices v1.0.0
	trade v1.0.0
	uncle-cake v1.0.0
	user v1.0.0
	user-equity v1.0.0
	user-price-auth v1.0.0
	user-purchase v1.0.0
	video-distribute v1.0.0
	virtual-stock v1.0.0
	wechatmini v1.0.0
	wechatofficial v1.0.0
	weipinshang v1.0.0
	youxuan-supply v1.0.0
	yunzhonghe v1.0.0
	yunzhonghe-supply v1.0.0
	yz-go v1.0.0
)

require (
	dunion-go-sdk v1.0.0 // indirect
	github.com/360EntSecGroup-Skylar/excelize v1.4.1 // indirect
	github.com/BurntSushi/toml v1.0.0 // indirect
	github.com/Knetic/govaluate v3.0.1-0.**************-9aa49832a739+incompatible // indirect
	github.com/StackExchange/wmi v0.0.0-**************-fe8f1750fd46 // indirect
	github.com/alibabacloud-go/darabonba-openapi v0.1.7 // indirect
	github.com/alibabacloud-go/debug v0.0.0-20190504072949-9472017b5c68 // indirect
	github.com/alibabacloud-go/dysmsapi-20170525/v2 v2.0.6 // indirect
	github.com/alibabacloud-go/endpoint-util v1.1.0 // indirect
	github.com/alibabacloud-go/openapi-util v0.0.9 // indirect
	github.com/alibabacloud-go/tea v1.1.17 // indirect
	github.com/alibabacloud-go/tea-utils v1.3.9 // indirect
	github.com/aliyun/aliyun-oss-go-sdk v2.1.7+incompatible // indirect
	github.com/aliyun/credentials-go v1.1.2 // indirect
	github.com/andybalholm/brotli v1.1.0 // indirect
	github.com/bradfitz/gomemcache v0.0.0-20190913173617-a41fca850d0b // indirect
	github.com/bytedance/sonic v1.7.0 // indirect
	github.com/casbin/casbin v1.9.1 // indirect
	github.com/casbin/casbin/v2 v2.77.1 // indirect
	github.com/casbin/gorm-adapter/v3 v3.20.0 // indirect
	github.com/cespare/xxhash/v2 v2.1.2 // indirect
	github.com/chenzhuoyu/base64x v0.0.0-20221115062448-fe3a3abad311 // indirect
	github.com/clbanning/mxj v1.8.5-0.20200714211355-ff02cfb8ea28 // indirect
	github.com/cloudflare/circl v1.3.7 // indirect
	github.com/dgrijalva/jwt-go v3.2.0+incompatible // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/emirpasic/gods v1.12.0 // indirect
	github.com/fatih/color v1.12.0 // indirect
	github.com/fatih/structs v1.1.0 // indirect
	github.com/forgoer/openssl v1.2.1 // indirect
	github.com/fsnotify/fsnotify v1.4.9 // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/glebarez/go-sqlite v1.20.3 // indirect
	github.com/glebarez/sqlite v1.7.0 // indirect
	github.com/go-logr/logr v1.4.1 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-ole/go-ole v1.2.5 // indirect
	github.com/go-playground/locales v0.13.0 // indirect
	github.com/go-playground/universal-translator v0.17.0 // indirect
	github.com/go-playground/validator/v10 v10.4.1 // indirect
	github.com/go-redis/redis/v8 v8.11.5 // indirect
	github.com/go-resty/resty/v2 v2.6.0 // indirect
	github.com/go-sql-driver/mysql v1.7.1 // indirect
	github.com/go-task/slim-sprig v0.0.0-20230315185526-52ccab3ef572 // indirect
	github.com/goccy/go-json v0.10.0 // indirect
	github.com/gogf/gf v1.16.9 // indirect
	github.com/golang-sql/civil v0.0.0-20220223132316-b832511892a9 // indirect
	github.com/golang-sql/sqlexp v0.1.0 // indirect
	github.com/golang/freetype v0.0.0-20170609003504-e2365dfdc4a0 // indirect
	github.com/golang/protobuf v1.5.3 // indirect
	github.com/gomodule/redigo v2.0.0+incompatible // indirect
	github.com/google/go-querystring v1.0.0 // indirect
	github.com/google/pprof v0.0.0-20240227163752-401108e1b7e7 // indirect
	github.com/google/uuid v1.3.0 // indirect
	github.com/gorilla/websocket v1.4.2 // indirect
	github.com/grokify/html-strip-tags-go v0.0.1 // indirect
	github.com/guonaihong/gout v0.3.9 // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/hetiansu5/urlquery v1.2.4 // indirect
	github.com/huaweicloud/huaweicloud-sdk-go-obs v3.22.11+incompatible // indirect
	github.com/imroc/req/v3 v3.43.3 // indirect
	github.com/inconshreveable/mousetrap v1.0.0 // indirect
	github.com/jackc/chunkreader/v2 v2.0.1 // indirect
	github.com/jackc/pgconn v1.13.0 // indirect
	github.com/jackc/pgio v1.0.0 // indirect
	github.com/jackc/pgpassfile v1.0.0 // indirect
	github.com/jackc/pgproto3/v2 v2.3.1 // indirect
	github.com/jackc/pgservicefile v0.0.0-20200714003250-2b9c44734f2b // indirect
	github.com/jackc/pgtype v1.12.0 // indirect
	github.com/jackc/pgx/v4 v4.17.2 // indirect
	github.com/jakecoffman/cron v0.0.0-20190106200828-7e2009c226a5 // indirect
	github.com/jinzhu/copier v0.3.5 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/jordan-wright/email v4.0.1-0.20210109023952-943e75fe5223+incompatible // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/json-iterator/go v1.1.10 // indirect
	github.com/klauspost/compress v1.17.7 // indirect
	github.com/klauspost/cpuid/v2 v2.0.9 // indirect
	github.com/konsorten/go-windows-terminal-sequences v1.0.3 // indirect
	github.com/leodido/go-urn v1.2.0 // indirect
	github.com/lestrrat-go/file-rotatelogs v2.4.0+incompatible // indirect
	github.com/lestrrat-go/strftime v1.0.4 // indirect
	github.com/magiconair/properties v1.8.1 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/mattn/go-colorable v0.1.8 // indirect
	github.com/mattn/go-isatty v0.0.17 // indirect
	github.com/mattn/go-runewidth v0.0.10 // indirect
	github.com/microsoft/go-mssqldb v0.17.0 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/mitchellh/mapstructure v1.2.2 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.1 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/mojocn/base64Captcha v1.3.5 // indirect
	github.com/mozillazg/go-httpheader v0.2.1 // indirect
	github.com/nfnt/resize v0.0.0-20180221191011-83c6a9932646 // indirect
	github.com/olekukonko/tablewriter v0.0.5 // indirect
	github.com/olivere/elastic/v7 v7.0.24 // indirect
	github.com/onsi/ginkgo/v2 v2.16.0 // indirect
	github.com/pelletier/go-toml v1.6.0 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pupuk/addr v0.0.3 // indirect
	github.com/qiniu/api.v7/v7 v7.8.2 // indirect
	github.com/quic-go/qpack v0.4.0 // indirect
	github.com/quic-go/quic-go v0.41.0 // indirect
	github.com/refraction-networking/utls v1.6.3 // indirect
	github.com/remyoudompheng/bigfft v0.0.0-20230126093431-47fa9a501578 // indirect
	github.com/richardlehane/mscfb v1.0.4 // indirect
	github.com/richardlehane/msoleps v1.0.3 // indirect
	github.com/rivo/uniseg v0.1.0 // indirect
	github.com/satori/go.uuid v1.2.0 // indirect
	github.com/shirou/gopsutil v3.21.2+incompatible // indirect
	github.com/shopspring/decimal v1.3.1 // indirect
	github.com/silenceper/wechat/v2 v2.0.0 // indirect
	github.com/sirupsen/logrus v1.6.0 // indirect
	github.com/skip2/go-qrcode v0.0.0-20200617195104-da1b6568686e // indirect
	github.com/spf13/afero v1.2.2 // indirect
	github.com/spf13/cast v1.3.1 // indirect
	github.com/spf13/cobra v1.1.1 // indirect
	github.com/spf13/jwalterweatherman v1.1.0 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/spf13/viper v1.7.1 // indirect
	github.com/streadway/amqp v1.1.0 // indirect
	github.com/subosito/gotenv v1.2.0 // indirect
	github.com/tencentcloud/tencentcloud-sdk-go v1.0.125 // indirect
	github.com/tencentyun/cos-go-sdk-v5 v0.7.23 // indirect
	github.com/tidwall/gjson v1.14.4 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.0 // indirect
	github.com/tjfoc/gmsm v1.3.2 // indirect
	github.com/tklauser/go-sysconf v0.3.5 // indirect
	github.com/tklauser/numcpus v0.2.2 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/ugorji/go/codec v1.1.7 // indirect
	github.com/unrolled/secure v1.0.8 // indirect
	github.com/wechatpay-apiv3/wechatpay-go v0.2.14 // indirect
	github.com/writethesky/stbz-sdk-golang v1.0.1 // indirect
	github.com/xingliuhua/leaf v1.1.2 // indirect
	github.com/xuri/efp v0.0.0-20231025114914-d1ff6096ae53 // indirect
	github.com/xuri/excelize/v2 v2.8.1 // indirect
	github.com/xuri/nfp v0.0.0-20230919160717-d98342af3f05 // indirect
	go.opentelemetry.io/otel v1.22.0 // indirect
	go.opentelemetry.io/otel/metric v1.22.0 // indirect
	go.opentelemetry.io/otel/trace v1.22.0 // indirect
	go.uber.org/atomic v1.7.0 // indirect
	go.uber.org/mock v0.4.0 // indirect
	go.uber.org/multierr v1.6.0 // indirect
	golang.org/x/arch v0.0.0-20210923205945-b76863e36670 // indirect
	golang.org/x/crypto v0.21.0 // indirect
	golang.org/x/exp v0.0.0-20240222234643-814bf88cf225 // indirect
	golang.org/x/image v0.14.0 // indirect
	golang.org/x/mod v0.16.0 // indirect
	golang.org/x/net v0.22.0 // indirect
	golang.org/x/sync v0.6.0 // indirect
	golang.org/x/sys v0.18.0 // indirect
	golang.org/x/text v0.14.0 // indirect
	golang.org/x/time v0.0.0-20190308202827-9d24e82272b4 // indirect
	golang.org/x/tools v0.19.0 // indirect
	google.golang.org/protobuf v1.28.0 // indirect
	gopkg.in/ini.v1 v1.62.0 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.0.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	gorm.io/driver/mysql v1.5.2 // indirect
	gorm.io/driver/postgres v1.4.4 // indirect
	gorm.io/driver/sqlserver v1.4.1 // indirect
	gorm.io/plugin/dbresolver v1.3.0 // indirect
	modernc.org/libc v1.22.2 // indirect
	modernc.org/mathutil v1.5.0 // indirect
	modernc.org/memory v1.5.0 // indirect
	modernc.org/sqlite v1.20.3 // indirect
	topsdk v1.0.0 // indirect
)

replace (
	ad => ../ad
	after-sales v1.0.0 => ../after-sales
	ali-open v1.0.0 => ../ali-open
	ali-selected => ../ali-selected
	application v1.0.0 => ../application
	area-agency v1.0.0 => ../area-agency
	byn-supply v1.0.0 => ../byn-supply
	category v1.0.0 => ../category
	cloud => ../cloud
	cloud v1.0.0 => ../cloud
	comment v1.0.0 => ../comment
	convergence v1.0.0 => ../convergence-pay
	course-distribution v1.0.0 => ../course-distribution
	//wechatpay v1.0.0 => ../wechat-pay
	cps => ../cps
	cross-border-supply v1.0.0 => ../cross-border-supply
	dahang-erp => ../dahang-erp
	distributor => ../distributor
	distributor-tool => ../distributor-tool
	douyin-cps => ../douyin-cps
	douyin-group => ../douyin-group
	dunion-go-sdk => ../dunion-go-sdk
	dwd-supply v1.0.0 => ../dwd-supply
	event-distribution => ../event-distribution
	favorite => ../favorite
	finance v1.0.0 => ../finance
	fulu-supply => ../fulu-supply
	gather-supply => ../gather-supply
	gin-vue-admin v1.0.0 => ../gin-vue-admin/server
	gongmall v1.0.0 => ../gongmall
	hbsk => ../hbsk
	hehe-supply v1.0.0 => ../hehe-supply
	install v1.0.0 => ../install
	jd-supply => ../jd-supply
	jd-vop-supply => ../jd-vop-supply
	jushuitan => ../jushuitan
	jushuitan-supply => ../jushuitan-supply
	knowledge-base => ../knowledge-base
	lakala => ../lakala
	lease => ../lease
	lianlian => ../lianlian
	lijing-supply v1.0.0 => ../lijing-supply
	material-distribute v1.0.0 => ../material-distribute
	meituan-distributor => ../meituan-distributor
	merchant v1.0.0 => ../merchant
	monitor v1.0.0 => ../monitor
	maiger-supply v1.0.0 => ../maiger-supply
	notification v1.0.0 => ../notification
	operation v1.0.0 => ../operation
	order => ../order
	order-export => ../order-export
	payment => ../payment
	plugin => ../plugin
	product v1.0.0 => ../product
	product-album v1.0.0 => ../product-album
	promotion v1.0.0 => ../promotion
	public-supply v1.0.0 => ../public-supply
	purchase-account => ../purchase-account
	purchase-account v1.0.0 => ../purchase-account
	race-cinema-ticket => ../race-cinema-ticket

	region => ../region
	region-match v1.0.0 => ../region-match
	sales => ../sales
	script-distribute => ../script-distribute
	self-supply v1.0.0 => ../self-supply
	service-provider-system v1.0.0 => ../service-provider-system
	share-live => ../share-live
	shipping => ../shipping
	shop => ../shop
	shopping-cart v1.0.0 => ../shopping-cart
	small-shop v1.0.0 => ../small-shop
	small-shop-video => ../small-shop-video
	stbz-supply v1.0.0 => ../stbz-supply
	supplier v1.0.0 => ../supplier
	surface-single v1.0.0 => ../surface-single
	szbao-supply v1.0.0 => ../szbao-supply
	thousands-prices => ../thousands-prices
	topsdk v1.0.0 => ../topsdk
	trade => ../trade
	uncle-cake v1.0.0 => ../uncle-cake
	user => ../user
	user-equity => ../user-equity
	user-price-auth => ../user-price-auth
	user-purchase => ../user-purchase
	user-relation => ../user-relation
	video-distribute => ../video-distribute
	virtual-stock => ../virtual-stock
	wechatmini v1.0.0 => ../wechatmini
	wechatofficial v1.0.0 => ../wechatofficial
	wechatpay-go-main => ../wechatpay-go-main
	weipinshang v1.0.0 => ../weipinshang
	youxuan-supply => ../youxuan-supply
	yunzhonghe => ../yunzhonghe
	yunzhonghe-supply v1.0.0 => ../yunzhonghe-supply
	//wechatpay v1.0.0 => ../wechat-pay
	yz-go => ../yz-go

)
