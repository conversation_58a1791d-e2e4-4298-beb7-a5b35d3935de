package main

import (
	afterSalesCron "after-sales/cron"
	cron10 "ali-open/cron"
	listener2 "ali-open/listener"
	cron24 "ali-selected/cron"
	listener9 "ali-selected/listener"
	"application"
	cron5 "application/cron"
	alistener "application/listener"
	areacron "area-agency/cron"
	arealistener "area-agency/listener"
	bynCron "byn-supply/cron"
	cloudCron "cloud/cron"
	cloudListener "cloud/listener"
	ccron "comment/cron"
	"context"
	joinPay "convergence"
	cron18 "course-distribution/cron"
	listener5 "course-distribution/listener"
	cron14 "cps/cron"
	cron6 "cross-border-supply/cron"
	daHangErpCron "dahang-erp/cron"
	daHangErp "dahang-erp/listener"
	"distributor/cps-listener"
	"douyin-cps/initialize"
	ecInitialize "ec-cps-ctrl/initialize"
	eventDistributionListener "event-distribution/listener"
	cron33 "finance/cron"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gongmall/listen"
	cron34 "guanaitong-supply/cron"
	listener18 "guanaitong-supply/listen"
	cron35 "guangdian/cron"
	cron27 "hbsk/cron"
	cron28 "hehe-supply/cron"
	jdVopSupplyListener "jd-vop-supply/listener"
	listener14 "jushuitan-supply/listen"
	cron36 "kunsheng-supply/cron"
	listener13 "lianlian/listener"
	localLifeCron "local-life/cron"
	"local-life/service/app"
	maiGerSupplyListener "maiger-supply/listener"
	"system-monitor/abnormalMonitoring"
	cron13 "wdt-supply/cron"
	listener20 "wdt-supply/listener"
	listen2 "yiyatong/listen"

	//cron28 "meituan-distributor/cron"
	service3 "product/service"
	cron31 "race-cinema-ticket/cron"
	listener17 "service-provider-system/listener"
	cron30 "shama-supply/cron"
	listener12 "shama-supply/listener"
	local_listener "shama-supply/local-listener"
	supplyChainCron "supply-chain/cron"
	listener19 "thousands-prices/listener"
	cron29 "tianma-supply/cron"
	wechatminiListener "wechatmini/listener"
	cron26 "weipinshang/cron"
	listener15 "weipinshang/listener"
	cron32 "yijifen/cron"
	listener16 "yijifen/listener"
	yyt "yiyatong/cron"
	listener11 "youxuan-supply/listener"
	"yz-go/cron"
	"yz-go/mq"
	"yz-go/source"
	"yz-go/utils"

	distributorCron "distributor/cron"
	distributorListener "distributor/listener"
	douyinGroupCron "douyin-group/cron"
	cron9 "dwd-supply/cron"
	"encoding/json"
	eventDistributionCron "event-distribution/cron"
	flistener "finance/listener"
	"flag"
	"fmt"
	fuluCron "fulu-supply/cron"
	fuluListener "fulu-supply/listener"
	cron21 "gather-supply/cron"
	glistener "gather-supply/listener"
	service2 "gin-vue-admin/admin/service"
	"gin-vue-admin/cmd/gva"

	listenerhbsk "hbsk/listener"
	v1 "install/api/v1"
	"install/service"
	institutionCron "institution/cron"
	institutionListener "institution/listener"
	cron17 "jd-supply/cron"
	listener6 "jd-supply/listener"
	jdVopSupplyCron "jd-vop-supply/cron"
	cron22 "jushuitan-supply/cron"
	cron20 "jushuitan/cron"
	listener7 "jushuitan/listener"
	leaseListener "lease/listener"
	cron19 "lianlian/cron"
	lijingcron "lijing-supply/cron"
	listener8 "lijing-supply/listener"
	localLifeListener "local-life/listener"
	log2 "log"
	maiGerSupplyCron "maiger-supply/cron"
	materialCron "material-distribute/cron"
	merchantCron "merchant/cron"
	merchantListener "merchant/listener"
	cron23 "monitor/cron"
	cron12 "notification/cron"
	listener "notification/listen"
	operationCron "operation/cron"
	cron2 "order/cron"
	listener4 "order/listen"
	cron7 "payment/cron"
	productAlbumCron "product-album/cron"
	productAlbumListener "product-album/listener"
	cron8 "product/cron"
	racecinema "race-cinema-ticket/listener"
	cron4 "self-supply/cron"
	selfListener "self-supply/listener"
	shareLiveCron "share-live/cron"
	shareLivelistener "share-live/listener"
	videoShopListener "small-shop-video/listener"
	smallShopCron "small-shop/cron"
	smallShopListener "small-shop/listener"
	stbzcron "stbz-supply/cron"
	suppcron "supplier/cron"
	listener10 "supplier/listener"
	"supply-chain/component/migration"
	"supply-chain/component/server"
	cron3 "szbao-supply/cron"
	tcron "trade/cron"
	tlistener "trade/listener"
	cron16 "uncle-cake/cron"
	userEquityCron "user-equity/cron"
	userCron "user/cron"
	_ "wdt-supply/goods"
	cron25 "youxuan-supply/cron"
	cron15 "yunzhonghe-supply/cron"
	cron11 "yunzhonghe/cron"
	listener3 "yunzhonghe/listener"

	"yz-go/component/log"
	"yz-go/config"
)

var (
	BuildTime    string
	BuildVersion string
)

func main() {

	var err error
	var runPath string
	var serviceName string

	gva.BuildVersion = BuildVersion
	gva.BuildTime = BuildTime

	flag.StringVar(&runPath, "c", "", "配置文件目录")
	flag.StringVar(&serviceName, "s", "", "服务")
	flag.Parse()
	fmt.Printf("Version:%s\n", gva.BuildVersion)
	fmt.Printf("BuildTime:%s\n", gva.BuildTime)
	if serviceName == "version" {
		fmt.Printf("Version:%s\n", BuildVersion)
		fmt.Printf("BuildTime:%s\n", BuildTime)
		return
	}

	//wechatofficial.RunServer()
	if runPath != "" {
		fmt.Printf("您正在使用-c传递的值,运行路径为%v\n", runPath)

		utils.SetRunPath(runPath)
	}
	if serviceName == "orderSett" {

		tcron.OrderSettlement()
		return
	}

	if serviceName == "alupdate" {

		cron24.ProductUpdate(20)
		return
	}

	if serviceName == "mq" {
		if utils.LocalEnv() == true { // 正式生产环境
			AuthVersion()
		}

		log.Log().Debug("正在运行mq消费者")

		listen2.PushYytCallBackProductCustomerHandles()
		listener16.PushYjfSyncMsgCustomerHandles()

		listener16.PushYjfSyncProductCustomerHandles()
		listener13.PushLianSyncProductHandles()
		listener15.PushWpsSyncProductCustomerHandles()
		listener15.PushWpsLocalProductImportStatus()
		listener9.PushaliGoodsLocalProductImportStatus()
		listen.PushGongMallWithdrawal()
		listenerhbsk.PushHbReceivedCustomerHandles()
		listenerhbsk.PushPaidCustomerHandles()
		listener6.PushHandlesJDGoodsImport()
		application.PushCustomerHandles()
		listener2.PushAliOpenCallBackHandles()
		listener9.PushAljxCallBackHandles()
		listener9.PushAljxProductSyncHandles()
		listener4.PushCustomerHandles()
		listener4.PushUserLevelPurchaseHandles()
		listener4.PushCustomerExpressHandles()
		listener4.PushSendCustomerHandles()
		listener.PushCustomerHandles()
		flistener.PushPaidCustomerHandles()
		flistener.PushIncomeHandles()                //收入监听
		racecinema.PushCinemaTicketCustomerHandles() //电影票回调监听
		flistener.PushPaySuccessCustomerHandles()    //支付回调通知
		flistener.PushCustomerHandles()
		flistener.PushOrderRefundCustomerHandles()
		glistener.PushCustomerHandles()
		glistener.PushCustomerReconfirmHandles()
		glistener.AfterSalesDisposeHandles() //售后消息处理-- 同步到其他中台
		glistener.PushNotificationHandles()
		glistener.PushCallBackHandles()
		glistener.PushSelfCallBackHandles()
		alistener.PushRePushMessages()
		glistener.PushProductDeleteHandles()
		alistener.PushCustomerHandles()
		alistener.PushAppNotifyHandles()
		productAlbumListener.PushCustomerHandles()
		productAlbumListener.PushMaintainAlbumsHandles()
		fuluListener.PushCustomerHandles()
		alistener.AfterSalesPushCustomerHandles() //售后消息推送
		tlistener.PushCustomerHandles()
		tlistener.PushPaidCustomerHandles()
		tlistener.PushUserLevelPurchaseHandles()
		arealistener.PushOrderCreatedHandles()
		listener5.PushCourseOrderCreatedHandles()
		arealistener.PushOrderReceivedHandles()
		arealistener.PushOrderClosedHandles()
		smallShopListener.PushOrderReceivedHandles()
		smallShopListener.SmallShopKeeperStatisticListenerHandles()
		smallShopListener.PushOrderSendingHandles()
		smallShopListener.PushOrderSentHandles()
		smallShopListener.PushOrderClosedHandles()
		smallShopListener.PushAfterSalesSendHandles()
		smallShopListener.PushSmallShopAfterSalesSendHandles()
		smallShopListener.PushOrderCreatedHandles()
		smallShopListener.PushPaySuccessCustomerHandles()
		smallShopListener.SmallShopOrderListenerHandles()
		smallShopListener.PushSmallShopApplyListenerPurchaseHandles()
		videoShopListener.PushOrderSendHandles()
		videoShopListener.PushCustomerHandles()
		videoShopListener.PushSmallShopProductChangeHandles()
		localLifeListener.PushLocalLifeOrderHandles()
		localLifeListener.PushLocalLifeOrderCallBackHandles()
		localLifeListener.PushLocalLifeProductHandles()
		localLifeListener.PushLocalLifeBrandStatisticByStoreMqHandles()
		localLifeListener.LocalAfterSalesPushCustomerHandles()
		localLifeListener.PushLocalLifeBrandStatisticByProductMqHandles()
		localLifeListener.PushLocalLifeBrandStatisticByOrderMqHandles()
		localLifeListener.PushLocalLifeBatchStore()
		listener3.PushHandlesYzhNewOrderCancel()
		distributorListener.PushOrderPaidHandles()
		distributorListener.PushOrderPaidCreateAwardHandles()
		distributorListener.PushCompensateAwardHandles()
		distributorListener.PushApplicationCreatedHandles()
		cps_listener.PushCpsOrderSettleHandles()
		distributorListener.PushDouYinGroupOrderSettleHandles() //抖音团购
		distributorListener.PushMeituanDisOrderSettleHandles()
		distributorListener.PushJhCpsOrderSettleHandles()
		distributorListener.PushOrderReceivedHandles()
		distributorListener.PushOrderClosedHandles()
		distributorListener.DistributorPurchaseListener()
		distributorListener.PushAddDisTeamSma()
		institutionListener.PushInstitutionAward()
		institutionListener.PushOrderReceivedCanSettleHandles()
		institutionListener.PushInstitutionTeamDistributor()
		institutionListener.PushInstitutionAwardBySmallShopApply()
		institutionListener.PushInstitutionPurchaseByOrderReceived()
		merchantListener.PushOrderPaidHandles()
		merchantListener.PushOrderReceivedHandles()
		merchantListener.PushOrderClosedHandles()
		merchantListener.PushSupplierCreatedHandles()
		selfListener.PushCustomerHandles()
		selfListener.PushSelfBatchHandles()
		cloudListener.ProductDeleteEditListenerHandles() //云仓监听商品删除修改
		cloudListener.CloudCreateOrderListenerHandles()  //监听云仓新的订单 （新的订单有定时任务往队列里面插入）
		cloudListener.OrderSendListenerHandles()         //监听中台订单发货，同步发货云仓订单 （测试情况下屏蔽调用胜天半子发货API）
		listener2.PushAlibbOrderHandles()                //监听阿里巴巴绑定商品下单
		listener7.PushCustomerNotifyHandles()
		listener7.PushCustomerAfterSalesHandles()
		listener7.PushCustomerHandles()
		listener7.PushCustomerOrderHandles()
		listener7.PushCustomerOrderReconfirmHandles()
		leaseListener.OrderStatusListener()              //租赁监听订单
		leaseListener.LeaseOrderListener()               //租赁订单消息监听
		shareLivelistener.OrderStatusListener()          //共享直播监听订单，统计带货数量和带货金额
		shareLivelistener.ProductDeleteListenerHandles() //共享直播监听商品删除 同步删除关联商品
		//serviceProviderSystemListener.ServiceProviderSystemPushOrder() //服务商系统监听订单完成 推送订单到第三方
		listener17.ServiceProviderSystemUserCreate() //服务商系统监听订单完成 推送订单到第三方
		listener17.ServiceProviderSystemUserUpdate() //服务商系统监听订单完成 推送订单到第三方
		listener8.PushCustomerStockSyncHandles()
		listener10.PushBatchSendOrderHandles()
		daHangErp.OrderStatusListener() //大航程监听订单支付 进行标识是否需要推送
		listener11.PushHandlesYxMessage()
		listener11.PushHandlesYxGoodsMessage()
		listener11.YouxuanGoodsInitHandles()
		listener11.YouxuanDeleteHandles()
		smallShopListener.PushCustomerHandles() //监听删除之后删除小商店的商品
		smallShopListener.PushShopkeeperSyncSelectedHandles()
		eventDistributionListener.EventDistributionDeleteHandles() //团购活动监听商品删除或者下架 结束团购活动
		eventDistributionListener.EventDistributionHandles()       //监听团购活动创建下架消息
		wechatminiListener.OrderSentPushWxMini()                   //小程序监听订单发货 同步到小程序
		local_listener.PushHandlesShamaProductMessage()
		local_listener.PushHandlesShamaGoodsMessage()
		jdVopSupplyListener.ProductCustomer()
		jdVopSupplyListener.JdVopGoodsConsumer()
		maiGerSupplyListener.ProductCustomer()
		maiGerSupplyListener.MaiGerDatabaseConsumer()
		listener12.PushShamaSkuPriceChangeConsumer()
		listener12.PushShamaAfsFinishConsumer()
		listener12.PushShamaAfsCloseConsumer()
		listener12.PushShamaAfsDeliverConsumer()
		listener12.PushShamaOrderStockOutConsumer()
		listener12.PushShamaAfsWaitConsumer()
		listener12.PushShamaSkuChangeConsumer()
		listener14.JushuitanGoodsInitHandles()
		listener14.JushuitanGoodsUpdateHandles()
		//listener14.JushuitanGoodsLocalUpdateHandles()
		listener14.JushuitanGoodsLocalUpdateHandlesNew()
		listener16.PushYjfLocalProductImportStatus()
		listener18.PushCustomerGatDeleteHandles()
		listener18.PushCustomerGatProductPriceEditHandles()
		listener18.PushCustomerGatProductEditHandles()
		listener18.PushCustomerGatProductDeleteAddHandles()
		listener18.PushCustomerGatOrderSendHandles()
		listener19.PushProductDeleteHandles()
		cps_listener.InitCpsListener()
		listener20.PushWdtGoodsUpdateHandles()
		forever := make(chan bool)
		for _, consumer := range mq.GetConsumers() {
			consumer()
		}
		<-forever
	} else if serviceName == "pay" {
		joinPay.RunServer()
	} else if serviceName == "cron" {
		if utils.LocalEnv() == true { // 正式生产环境
			AuthVersion()
		}
		log.Log().Debug("正在运行cron定时任务")

		abnormalMonitoring.PushLogMonitoringHandle()
		cron35.PushGuangDianOrderHandle()
		cron36.PushKunShengProductUpdateHandle()
		cron16.PushSupplyCakeOrderDeliverHandle()  //蛋糕订单发货检测
		cron16.PushSupplyCakeProductUpdateHandle() //蛋糕商品更新
		cron35.PushGuangDianSyncRechargeHandle()
		yyt.PushYiyatongProductSaleHandle()
		cron33.PurchasingCronTask()
		cron31.PushCinemaOrderStatusCheck()
		cron32.PushYjfAfterSalesCron()
		cron32.PushYjfOrderHandle()
		yyt.PushOrderSendGoodsHandle()
		yyt.PushAfterSaleSyncHandle()
		cron29.PushtianmaProductUpdateHandle()
		cron16.PushCakeProductAutoImportHandle()
		cron24.PushAlijxOrderDeliverHandle()
		cron27.PushCronHolderStatus()
		cron16.PushCakeProductHandle()
		cron26.PushWeipinshangProductUpdateHandle()
		cron26.PushWpsOrderSendGoodsHandle()
		cron32.PushYjfProductHandle()
		cron29.PushTianmaOrderSendGoodsHandle()
		cron24.PushAlijxProductUpdateHandle()
		cron10.PushAlibbFollowHandle()
		tcron.PushCloseHandle()
		tcron.PushFixHandle()
		cron19.PushLianLianProductUpdateHandle()
		//cron19.PushLianLianProductHandle()
		cron19.PushLianLianProductUpdateDetailHandle()
		cron18.CourseOrderSettlementHandle()
		ccron.PushCommentLevelUpdateHandle()
		stbzcron.PushGoodsStorageHandle()
		cron17.JdOrderDeliverCronRun()
		//cron19.PushLianLianProductDeleteHandle()
		cron4.PushGoodsStorageHandle()
		cron4.PushMessagePoolTask()
		//cron4.PushCakeMessagePoolTask()
		cron2.BillConfirmHandle()
		cron2.RepairOrderSendStatus()
		cron2.OrderEsSyncHandle()
		cron2.BillGetHandle()
		//cron2.AutoConfirmHandle()
		cron7.StationBalancePaidHandle()
		cron7.CheckUpdatePaidHandle()
		cron6.CrossOrderDeliverCronRun()     //跨境供应链检测发货
		cron10.PushAlibbOrderDeliverHandle() //阿里巴巴发货信息检测
		cron16.PushCakeOrderDeliverHandle()  //蛋糕定时任务
		cron16.PushCakeBrandHandle()         //蛋糕品牌更新
		cron16.PushCakeShopHandle()          //蛋糕店铺更新

		cron10.PushAlibbOrderPayHandle() //检测阿里巴巴支付支付的订单
		//cron22.PushAlibbOrderDeliverHandle()
		//stbzcron.PushGoodsAlertPriceHandle() //胜天半子价格批量更新
		cron6.PushCrossGoodsAlertSaleHandle() //cross 库存检测上下架
		stbzcron.StbzOrderDeliverCronRun()

		cron15.YzhOrderDeliverCronRun() //yzh发货

		cron11.PushYzhNewGoodsAlertSaleHandle() //定时任务更新yzhnew上下架
		cron11.YzhNewOrderDeliverCronRun()      // yzhnew 检测发货
		cron11.PushYzhNewGoodsAlertHandle()     //yzhnew 商品定时更新

		stbzcron.PushStbzGoodsAlertHandle() //胜天半子商品定时更新
		stbzcron.PushStbzGoodsSaleCheckHandle()
		tcron.PushReceiveHandle()
		tcron.PushSettlementHandle()
		suppcron.PushSupplierGoodsStatisticHandle()
		suppcron.PushSupplierOrderStatisticHandle()
		institutionCron.PushSettleHandle()
		institutionCron.PushInsStatisticHandle()
		areacron.PushSettleHandle()
		localLifeCron.PushSettleHandle()
		localLifeCron.ClearLocAfterSalesMessage()
		localLifeCron.LocalAfterSalesMessageErrorHandle()
		localLifeCron.PushLocalLifeSyncProductHandle()
		smallShopCron.PushSettleHandle()
		smallShopCron.PushSmallShopProductSyncHandle()
		productAlbumCron.PushSettleHandle()
		productAlbumCron.PushInspectHandle()
		productAlbumCron.SynchroHandle()
		productAlbumCron.MaintainAlbumsCron()
		materialCron.PushSettleHandle()
		distributorCron.PushSettleHandle()
		distributorCron.PushStatisticHandle()
		merchantCron.PushSettleHandle()
		cron3.PushGoodsSzbaoHandle()
		cron3.PushGoodsSzbaoV2Handle()
		cron3.PushGoodsProductHandle()
		cron3.PushGoodsProductV2Handle()
		cron3.PushGoodsMergeHandle()
		cron9.PushGoodsDwdHandle()
		cron9.PushGoodsProductHandle()
		cron3.PushOrderSzbaoV2Handle()
		bynCron.PushBynGoodsSyncHandle()
		bynCron.PushBynBrandSyncHandle()
		fuluCron.PushFuluGetOrderInfoHandle()
		fuluCron.EquityProductSync() //权益商品定时更新 -- 雷珏需求对接
		fuluCron.PushProductSyncHandle()
		fuluCron.PushFuluSyncEquityProductHandle()
		fuluCron.ImportMillenniumProductCron()
		fuluCron.UpdateMillenniumProductCron()
		userEquityCron.PushUserEquityGetOrderInfoHandle()
		userEquityCron.PushProductSyncHandle()
		userCron.PushUserValidityHandle()
		userCron.PushCheckUserRelationsHandle()
		cloudCron.SynCloudOrderCreateOrder()    //云仓自动在中台下单
		cloudCron.ClearCloudPushGoodsMessages() //清除一个月之前的商品同步记录日志
		cloudCron.CloudWarehouseOrders()        //修复队列执行过程中 系统重启等行为导致未执行完未下的订单变为错的状态可以手动下单
		cloudCron.SynCloudOrderPayOrder()       //云仓 - 获取下单时支付失败的订单进行支付
		//cloudCron.SynCloudProductSoldOut()   //获取推送到云仓的中台下架商品，在云仓进行下架（避免因为监听出现问题而导致部分没有下架的问题）关闭这个定时任务 现在队列比较稳定很少出现不下架的情况
		//cloudCron.SynOrderCloudOrderSend()   //获取中台未发货的云仓订单状态，如果是发货则中台订单同步发货
		afterSalesCron.AfterSalesBarterSuccessCron() //换货自动完成（默认7天）
		cron5.RePushHandle()                         //定时重新推送消息给商城
		cron5.DeleteMessagePoolHandle()              //定时重新推送消息给商城
		cron5.DeleteMessagePoolBackupHandle()        //定时重新推送消息给商城
		cron5.CheckMessagePoolWhiteHandle()          //定时重新推送消息给商城
		cron5.CheckCallBackLinkHandle()              //检查采购端采购地址
		cron5.ApplicationProductCollectionHandle()   //检查采购端采购地址
		cron8.ProductSyncHandle()                    //商品自动同步
		cron8.PushBatchHandleUpdateProductExcel()
		supplyChainCron.ProductOldCountHandle() //每天执行一次保存当前的上架商品数量记作 昨日的商品数量
		//cron8.ProductEsSyncHandle()
		cron12.BalanceWatchHandle()
		//cron4.PushBatchHandle()
		cron4.PushSupplyLineHandle()
		cron4.PushOrderDeliveryHandle()
		cron21.PushSupplyLineHandle()
		initialize.InitCron()
		ecInitialize.InitCron()
		cron14.PushCpsDidiPercentageHandle()
		cron14.PushAutoCloseHandle()
		cron14.PushSyncCpsOrderHandle()
		cron14.PushSyncCpsOrderOtherHandle()
		cron14.PushElemeSyncCpsOrderHandle()
		cron28.PushHeheUpdateProductHandle()
		cron20.JstAccessTokenHandle()
		cron20.JstReconfirmHandle()
		lijingcron.PushOrderDeliveryHandle()
		lijingcron.PushStockHandle()
		lijingcron.PushCheckStockHandle()
		lijingcron.PushLijingTokenHandle()
		daHangErpCron.SynDaChangErpGoodsList() //定时同步大昌行商品
		shareLiveCron.SynShareLive()           //获取峰值和带宽
		cron22.PushGoodsJushuitanHandle()
		cron22.PushGoodsJushuitanUpdateHandle()
		cron22.PushGoodsJushuitanUnderSaleHandle()
		//cron22.PushGoodsJushuitanHandleV2()
		cron22.PushJushuitanCheckStatusHandle()
		//cron22.PushGoodsJushuitanProductUpdateHandleV2()
		//cron22.PushGoodsJushuitanHandleTruncate()
		//cron22.PushJushuitanStockHandle()
		cron22.PushJushuitanUploadHandle()
		cron22.PushGoodsJushuitanUpdateHandle()
		//cron25.PushGoodsYouxuanHandle()
		cron25.PushBatchHandle()
		cron25.HandleMessagePool()
		cron25.PushGoodsYouxuanHandleInit()
		cron25.PushGoodsYouxuanHandleGoodsCount()
		//cron28.PushSyncMeituanCpsOrderHandle()
		//cron28.PushMeituanDisPercentageHandle()
		//cron28.PushUpdateVerifyOrderHandle()
		//cron28.PushUpdateRefundOrderHandle()
		//cron28.PushUpdatePayCpsOrderHandle()
		operationCron.OperationEsSyncHandle() // 操作日志同步到ES
		cron30.PushShamaProductUpdateHandle()
		cron30.PushShamaSkuChangeHandle()
		cron30.PushShamaSkuPriceChangeHandle()
		cron23.HeartbeatHandle()
		eventDistributionCron.SynEventDistributionStatus() //团购活动状态改变
		douyinGroupCron.PushSyncDouyinGroupOrderHandle()   //抖音团购同步订单状态
		douyinGroupCron.PushDouyinGroupPercentageHandle()  //抖音团购结算订单
		jdVopSupplyCron.UpdateJdVopProductCron()
		jdVopSupplyCron.JdVopOrderTrackCron()
		maiGerSupplyCron.UpdateMaiGerDatabaseCron()
		maiGerSupplyCron.MaiGerOrderTrackCron()
		maiGerSupplyCron.MaiGerAfterSalesCron()
		cron13.PushCheckWdtStockSyncHandle()
		cron13.PushLogisticsTaskHandle()
		cron13.PushCheckWdtDeleteGoodsHandle()
		cron13.PushCheckWdtInitGoodsHandle()
		cron34.HandleMessagePool()
		forever := make(chan bool)
		err = cron.Cron()
		if err != nil {
			fmt.Println("定时任务启动失败", err.Error())
		}
		<-forever

	} else if serviceName == "init" {
		path := config.Config().Local.Path
		exist, _ := utils.PathExists(path)
		// 递归创建文件夹
		if !exist {
			err = utils.CreateDir(path)
			if err != nil {
				fmt.Printf("mkdir failed![%v]\n", err)
			} else {
				fmt.Printf("mkdir success!\n")
			}
		}

		//if !exist {
		//	// 创建文件夹
		//	err = os.Mkdir(path, os.ModePerm)
		//	if err != nil {
		//		fmt.Printf("mkdir failed![%v]\n", err)
		//	} else {
		//		fmt.Printf("mkdir success!\n")
		//	}
		//}
		errs := migration.ModuleMysqlTables() // 初始化业务表
		if len(errs) > 0 {
			for _, err2 := range errs {
				fmt.Println("初始化数据表错误", err2.Error())
			}
			return
		}

		err = gva.Mysql.InitData()
		if err != nil {
			fmt.Println("初始化数据错误", err.Error())
			return

		}
	} else {
		if utils.LocalEnv() == true { // 正式生产环境
			AuthVersion()
			// 定时缓存采购端导入的商品id记录
			service3.CronLoadStorageCache()
			go func() {
				// 定时缓存采购端导入的商品id记录 本地生活
				app.CronLoadStorageCache()
			}()
		}

		go func() {
			subMessage()
		}()
		runWeb()

	}
	// 监听者
}

func subMessage() {
	rdb := source.Redis()
	pubsub := rdb.Subscribe(context.Background(), "systemUpdate")
	_, err := pubsub.Receive(context.Background())
	if err != nil {
		panic(err)
	}

	ch := pubsub.Channel()
	for msg := range ch {
		if msg.Payload == "update" {

			fmt.Println("准备重启或更新")
			service2.ExecCmd("pkill -9 supply")

			service2.IsUpdate = false
			//	ExecCmd
		}

		fmt.Println(msg.Channel, msg.Payload)
	}
}

func runWeb() {
	if ok, _ := service.InstallStatus(); !ok {
		installServer()
	} else {
		errs := migration.ModuleMysqlTables() // 初始化业务表
		if len(errs) > 0 {
			for _, err2 := range errs {
				fmt.Println("初始化数据表错误", err2.Error())
				log.Log().Error("初始化数据错误", zap.Any("err", err2))
			}
			if utils.LocalEnv() == false {
				//测试环境迁移报错时，终止执行
				return
			}

		}
		server.RunWindowsServer()
	}
}
func installServer() {
	Router := gin.Default()
	Router.POST("/install/store", v1.InstallStore)
	Router.GET("/install/status", v1.InstallStatus)
	_ = Router.Run(fmt.Sprintf(":%d", config.Config().System.Addr))
}

func AuthVersion() {

	//err, body := utils.Get("http://193.112.77.203:8889/authVersion")
	err, body := utils.Get("https://auth-test.yunzmall.com/api/getAuth", nil)
	if err != nil {
		log2.Print("请求权限验证错误")
		return
	}
	var data AuthData
	err = json.Unmarshal(body, &data)
	if err != nil {
		log2.Print("请求权限验证，解析数据错误")
		return
	}
	if data.Code == 7 {
		log2.Println(data.Msg)
		panic(data.Msg)
	}
	data.Data.EncryptID = Encrypt("supply", 5, []int{data.Data.ID})
	log.Log().Info("initSupplyID:" + data.Data.EncryptID)
	gva.GlobalAuth = data.Data
}

func Encrypt(salt string, minLength int, params []int) string {
	hd := utils.NewData()
	hd.Salt = salt
	hd.MinLength = minLength
	h, err := utils.NewWithData(hd)
	if err == nil {
		e, err := h.Encode(params)
		if err == nil {
			return e
		}
	}
	return ""
}
func Decrypt(salt string, minLength int, hash string) []int {
	hd := utils.NewData()
	hd.Salt = salt
	hd.MinLength = minLength
	h, err := utils.NewWithData(hd)
	if err == nil {
		e, err := h.DecodeWithError(hash)
		if err == nil {
			return e
		}
	}
	return []int{}
}

type AuthData struct {
	Data gva.ResponseJson `json:"data"`
	Code int              `json:"code"`
	Msg  string           `json:"msg"`
}
