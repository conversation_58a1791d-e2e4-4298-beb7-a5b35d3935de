package migration

import (
	"fmt"
	"gin-vue-admin/admin/service"
	"regexp"
	"strings"
	"yz-go/component/log"
	"yz-go/source"
)

type Product struct {
	ID           int    `json:"id"`
	ImageUrl     string `json:"image_url"`
	Gallery      string `json:"gallery"`
	DetailImages string `json:"detail_images"`
}

func ReplaceFileUrlField(host string) (err error) {
	// 商品表
	var products []Product
	err = source.DB().Model(&Product{}).Where("gallery like ?", "%"+host+"%").Find(&products).Error
	if err != nil {
		return
	}
	for _, product := range products {
		err, product.Gallery = UploadLocalFileInTextToOss(host, product.Gallery)
		if err != nil {
			log.Log().Error(fmt.Sprintf("upload image url failed, product id: %d", product.ID))
			continue
		}

		err = source.DB().Model(&product).Save(&product).Error
	}
	err = source.DB().Model(&Product{}).Where("detail_images like ?", "%"+host+"%").Find(&products).Error
	if err != nil {
		return
	}
	for _, product := range products {
		err, product.DetailImages = UploadLocalFileInTextToOss(host, product.DetailImages)
		if err != nil {
			log.Log().Error(fmt.Sprintf("upload image url failed, product id: %d", product.ID))
			continue
		}
		err = source.DB().Model(&product).Save(&product).Error
	}

	return
}
func GetUrlsFromText(text string) []string {
	// 定义正则表达式
	urlRegex := `https?://[^\s"']+`
	re := regexp.MustCompile(urlRegex)

	// 提取 URL
	urls := re.FindAllString(text, -1)

	return urls
}
func UploadLocalFileInTextToOss(host, text string) (error, string) {
	var urlStingList []string
	var err error
	var newText string
	newText = text
	urlStingList = GetUrlsFromText(text)

	for _, url := range urlStingList {
		var fullPath string
		var ossUrl string
		// 如果url以https://www.ymcfds.cn开头，则截取域名之后的字符
		if url[:21] == host {
			fullPath = url[21:]
		}
		if fullPath == "" {
			log.Log().Info("url is not start with host")
			continue
		}
		err, ossUrl, _ = service.UploadLocalFileToOss(fullPath)
		if err != nil {
			return err, ""
		}
		// 替换原文中的url
		newText = strings.Replace(newText, url, ossUrl, -1)
	}
	return nil, newText
}
