package v1

import (
    "{{.ModuleName}}/model"
    yzRequest "yz-go/request"
    "{{.ModuleName}}/request"
    "yz-go/response"
    "{{.ModuleName}}/service"
    "github.com/gin-gonic/gin"
    "go.uber.org/zap"
)

// Create{{.StructName}}
// @Tags {{.Description}}
// @Summary 创建
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.{{.StructName}} true "创建"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/{{.Abbreviation}}/create{{.StructName}} [post]
func Create{{.StructName}}(c *gin.Context) {
	var {{.Abbreviation}} model.{{.StructName}}
	var err error
	err = c.ShouldBindJSON(&{{.Abbreviation}})
	if err != nil{
	    yzResponse.FailWithMessage(err.Error(), c)
	    return
	}
	err := service.Create{{.StructName}}({{.Abbreviation}})
	if err != nil {
        log.GVA_LOG.Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
    yzResponse.OkWithMessage("创建成功", c)
}

// Delete{{.StructName}}
// @Tags {{.Description}}
// @Summary 删除
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.{{.StructName}} true "删除"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /api/{{.Abbreviation}}/delete{{.StructName}} [post]
func Delete{{.StructName}}(c *gin.Context) {
	var {{.Abbreviation}} model.{{.StructName}}
	var err error
    err = c.ShouldBindJSON(&{{.Abbreviation}})
    if err != nil{
        yzResponse.FailWithMessage(err.Error(), c)
        return
    }
	err := service.Delete{{.StructName}}({{.Abbreviation}})
	if err != nil {
        log.GVA_LOG.Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	}
    yzResponse.OkWithMessage("删除成功", c)

}

// Delete{{.StructName}}ByIds
// @Tags {{.Description}}
// @Summary 批量删除
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.IdsReq true "批量删除"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /api/{{.Abbreviation}}/delete{{.StructName}}ByIds [post]
func Delete{{.StructName}}ByIds(c *gin.Context) {
	var IDS yzRequest.IdsReq
    var err error
    err = c.ShouldBindJSON(&{{.Abbreviation}})
    if err != nil{
        yzResponse.FailWithMessage(err.Error(), c)
        return
    }
	err := service.Delete{{.StructName}}ByIds(IDS)
	if err != nil {
        log.GVA_LOG.Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	}
    yzResponse.OkWithMessage("批量删除成功", c)
}

// Update{{.StructName}}
// @Tags {{.Description}}
// @Summary 更新
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.{{.StructName}} true "更新"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /api/{{.Abbreviation}}/update{{.StructName}} [post]
func Update{{.StructName}}(c *gin.Context) {
	var {{.Abbreviation}} model.{{.StructName}}
	var err error
    err = c.ShouldBindJSON(&{{.Abbreviation}})
    if err != nil{
        yzResponse.FailWithMessage(err.Error(), c)
        return
    }
	err := service.Update{{.StructName}}({{.Abbreviation}})
	if err != nil {
        log.GVA_LOG.Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	}
    yzResponse.OkWithMessage("更新成功", c)
}

// Find{{.StructName}}
// @Tags {{.Description}}
// @Summary 用id查询
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "用id查询"
// @Success 200 {object} model.{{.StructName}}
// @Router /api/{{.Abbreviation}}/find{{.StructName}} [post]
func Find{{.StructName}}(c *gin.Context) {
	var reqId yzRequest.GetById
    err := c.ShouldBindQuery(&reqId)
    if err != nil {
        yzResponse.FailWithMessage(err.Error(), c)
        return
    }
	err, re{{.Abbreviation}} := service.Get{{.StructName}}(reqId.Id)
	if err != nil {
        log.GVA_LOG.Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"re{{.Abbreviation}}": re{{.Abbreviation}}}, c)
}

// Get{{.StructName}}List
// @Tags {{.Description}}
// @Summary 分页获取列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.{{.StructName}}Search true "分页获取列表"
// @Success 200 {string} string []model.{{.StructName}}
// @Router /api/{{.Abbreviation}}/get{{.StructName}}List [post]
func Get{{.StructName}}List(c *gin.Context) {
	var pageInfo request.{{.StructName}}Search
	var err error
    err = c.ShouldBindJSON(&pageInfo)
    if err != nil{
        yzResponse.FailWithMessage(err.Error(), c)
        return
    }
	err, list, total := service.Get{{.StructName}}InfoList(pageInfo)
	if err != nil {
        log.GVA_LOG.Error("获取失败", zap.Any("err", err))
        yzResponse.FailWithMessage(err.Error(), c)
        return
    }
    yzResponse.OkWithDetailed(yzResponse.PageResult{
        List:     list,
        Total:    total,
        Page:     pageInfo.Page,
        PageSize: pageInfo.PageSize,
    }, "获取成功", c)
}
