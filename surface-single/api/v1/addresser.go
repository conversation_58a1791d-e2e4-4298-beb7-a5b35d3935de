package v1

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"surface-single/model"
	"surface-single/request"
	"surface-single/service"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

func CreateAddresser(c *gin.Context) {
	var addresser model.Addresser
	err := c.ShouldBindJSON(&addresser)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.CreateAddresser(addresser); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

func DeleteAddresser(c *gin.Context) {
	var addresser model.Addresser
	err := c.ShouldBindJSON(&addresser)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteAddresser(addresser); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

func UpdateAddresser(c *gin.Context) {
	var addresser model.Addresser
	err := c.ShouldBindJSON(&addresser)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateAddresser(addresser); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}

func FindAddresser(c *gin.Context) {
	var addresser model.Addresser
	err := c.ShouldBindJSON(&addresser)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, reAddresser := service.GetAddresser(addresser.ID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"addresser": reAddresser}, c)
	}
}

func GetAddressersList(c *gin.Context) {
	var pageInfo yzRequest.PageInfo
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetAddressersList(pageInfo, 0); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func ChangeAddresserDefault(c *gin.Context) {
	var ssd request.SSDefault
	err := c.ShouldBindJSON(&ssd)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := utils.Verify(ssd); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.ChangeAddresserDefault(ssd, 0); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}
