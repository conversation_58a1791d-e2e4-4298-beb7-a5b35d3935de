package model

type KDNStatistic struct {
	Result int    `json:"result"`
	Msg    string `json:"msg"`
	Data   struct {
		Statistics struct {
			ApiTotalCount string `json:"apiTotalCount"`
			ApiTotalPrice int    `json:"apiTotalPrice"`
			ApiTotalUsed  string `json:"apiTotalUsed"`
		} `json:"statistics"`
		Data struct {
			CurrentPage int `json:"current_page"`
			Data        []struct {
				Id          int    `json:"id"`
				Uniacid     int    `json:"uniacid"`
				Uid         int    `json:"uid"`
				ApiCount    int    `json:"apiCount"`
				ApiPrice    int    `json:"apiPrice"`
				OrderSn     string `json:"orderSn"`
				ApiItemId   int    `json:"apiItemId"`
				ApiItemDesc string `json:"apiItemDesc"`
				EndTime     string `json:"endTime"`
				CreatedAt   string `json:"createdAt"`
			} `json:"data"`
			FirstPageUrl string      `json:"first_page_url"`
			From         int         `json:"from"`
			LastPage     int         `json:"last_page"`
			LastPageUrl  string      `json:"last_page_url"`
			NextPageUrl  interface{} `json:"next_page_url"`
			Path         string      `json:"path"`
			PerPage      int         `json:"per_page"`
			PrevPageUrl  interface{} `json:"prev_page_url"`
			To           int         `json:"to"`
			Total        int         `json:"total"`
		} `json:"data"`
	} `json:"data"`
	ValidatePage struct {
		IsBindMobile   int           `json:"is_bind_mobile"`
		InvitePage     int           `json:"invite_page"`
		IsInvite       int           `json:"is_invite"`
		IsLogin        int           `json:"is_login"`
		InviteMobile   int           `json:"invite_mobile"`
		BindMobilePage []interface{} `json:"bind_mobile_page"`
	} `json:"validate_page"`
}

type KDNRES struct {
	Result int    `json:"result"`
	Msg    string `json:"msg"`
	Data   KDN    `json:"data"`
}

type KDN struct {
	Order                 OrderValue `json:"order"`
	PrintTemplate         string     `json:"PrintTemplate"`         // 面单打印模板内容(html格式)
	SubCount              int        `json:"SubCount"`              // 子单数量
	SubOrders             string     `json:"SubOrders"`             // 子单单号
	EBusinessID           string     `json:"EBusinessID"`           // 用户ID
	UniquerRequestNumber  string     `json:"UniquerRequestNumber"`  // 唯一标识
	ResultCode            string     `json:"ResultCode"`            // 返回编码
	SignWaybillCode       string     `json:"SignWaybillCode"`       // 签回单单号
	EstimatedDeliveryTime string     `json:"EstimatedDeliveryTime"` // 订单预计到货时间yyyy-mm-dd
	Reason                string     `json:"Reason"`                // 失败原因
	Success               bool       `json:"Success"`               // 成功与否(true/false)
}

type OrderValue struct {
	OrderCode                   string `json:"OrderCode" gorm:"type:string;"`       // 订单编号
	ShipperCode                 string `json:"ShipperCode" gorm:"type:string;"`     // 快递公司编码
	LogisticCode                string `json:"LogisticCode" gorm:"type:string;"`    // 快递单号
	MarkDestination             string `json:"MarkDestination" gorm:"type:string;"` // 大头笔
	OriginCode                  string `json:"OriginCode" gorm:"type:string;"`      // 始发地区域编码
	OriginName                  string `json:"OriginName" gorm:"type:string;"`      // 始发地/始发网点
	DestinatioCode              string `json:"DestinatioCode" gorm:"type:string;"`  // 目的地区域编码
	DestinatioName              string `json:"DestinatioName" gorm:"type:string;"`  // 目的地/到达网点
	SortingCode                 string `json:"SortingCode" gorm:"type:string;"`
	PackageCode                 string `json:"PackageCode" gorm:"type:string;"` // 集包编码
	PackageName                 string `json:"PackageName" gorm:"type:string;"` // PackageName
	KDNOrderCode                string `json:"KDNOrderCode" gorm:"type:string;"`
	DestinationAllocationCentre string `json:"DestinationAllocationCentre" gorm:"type:string;"` // 目的地分类
}
