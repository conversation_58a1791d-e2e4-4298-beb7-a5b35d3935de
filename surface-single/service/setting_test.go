package service

import (
	"fmt"
	"strings"
	"testing"
)

func TestGetSetting(t *testing.T) {
	_, _ = GetSetting("surface_single_setting")
}

func TestUpdateSetting(t *testing.T) {
	var Receiver, Sender map[string]interface{}
	Receiver = make(map[string]interface{})
	Sender = make(map[string]interface{})
	Receiver["Name"] = "张三"
	Receiver["Mobile"] = "1380"
	Sender["Name"] = "李四"
	Sender["Mobile"] = "1594"
	if rname, ok := Receiver["Name"].(string); ok && rname != "" {
		if len(rname) > 1 {
			// 将第一个字符保留，剩余的字符替换为 "*"
			Receiver["Name"] = string([]rune(rname)[0]) + "**"
		} else {
			// 如果只有一个字符，直接保留
			Receiver["Name"] = rname
		}
	}
	// Receiver["Mobile"]字段的值改为用*替换；比如：收件人手机号为"13812345678"，则改为"138****5678"
	if rmobile, ok := Receiver["Mobile"].(string); ok && rmobile != "" {
		if len(rmobile) == 11 {
			// 前三位保留，中间四位替换为 "*"，最后四位不变
			Receiver["Mobile"] = rmobile[:3] + strings.Repeat("*", 4) + rmobile[7:]
		} else {
			if len(rmobile) > 1 {
				if len(rmobile) > 5 {
					Receiver["Mobile"] = rmobile[:1] + strings.Repeat("*", len(rmobile)-2) + string(rmobile[len(rmobile)-1])
				} else {
					Receiver["Mobile"] = rmobile[:1] + strings.Repeat("*", len(rmobile)-1)
				}
			} else {
				Receiver["Mobile"] = rmobile
			}
		}
	}
	// Sender["Name"]字段的值改为用*替换；比如：发件人姓名为"张三"，则改为"张**"
	if sname, ok := Sender["Name"].(string); ok && sname != "" {
		if len(sname) > 1 {
			Sender["Name"] = string([]rune(sname)[0]) + "**"
		} else {
			Sender["Name"] = sname
		}
	}
	// Sender["Mobile"]字段的值改为用*替换；比如：发件人手机号为"13812345678"，则改为"138****5678"
	if smobile, ok := Sender["Mobile"].(string); ok && smobile != "" {
		if len(smobile) == 11 {
			// 前三位保留，中间四位替换为 "*"，最后四位不变
			Sender["Mobile"] = smobile[:3] + strings.Repeat("*", 4) + smobile[7:]
		} else {
			if len(smobile) > 1 {
				if len(smobile) > 5 {
					Sender["Mobile"] = smobile[:1] + strings.Repeat("*", len(smobile)-2) + string(smobile[len(smobile)-1])
				} else {
					Sender["Mobile"] = smobile[:1] + strings.Repeat("*", len(smobile)-1)
				}
			} else {
				Sender["Mobile"] = smobile
			}
		}
	}
	fmt.Println(Receiver)
	fmt.Println(Sender)
}
