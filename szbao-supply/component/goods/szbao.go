package goods

import (
	catemodel "category/model"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	gatherSupplyRequest "gather-supply/request"
	log2 "log"
	"math"
	url2 "net/url"
	pmodel "product/model"
	"product/mq"
	"product/other"
	"product/response"
	service2 "product/service"
	callback2 "public-supply/callback"
	"public-supply/common"
	publicModel "public-supply/model"
	"public-supply/request"
	"public-supply/service"
	"public-supply/setting"
	"strconv"
	"strings"
	"sync"
	"szbao-supply/model"
	"time"
	"youxuan-supply/component/goods"
	"yz-go/component/log"
	model2 "yz-go/model"
	request2 "yz-go/request"
	yzResponse "yz-go/response"
	"yz-go/source"
	"yz-go/utils"

	"github.com/chenhg5/collection"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type Szbao struct {
}

func (self *Szbao) ManuallyProductUpdate(productID uint) (err error) {
	//TODO implement me
	panic("implement me")
}

func (self *<PERSON><PERSON><PERSON>) SynchronizeProductsToLocal() (err error) {
	//TODO implement me
	panic("implement me")
}

var wg sync.WaitGroup

func (*Szbao) InitSetting(gatherSupplyID uint) (err error) {
	var sysSetting model2.SysSetting
	err, sysSetting = setting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		return
	}
	GatherSupplyID = gatherSupplyID
	err = json.Unmarshal([]byte(sysSetting.Value), &szbaoData)
	if err != nil {

		//log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	if szbaoData.BaseInfo.AppKey == "" || szbaoData.BaseInfo.AppSecret == "" || szbaoData.BaseInfo.Host == "" {
		err = errors.New("请先配置供应链key")
		return
	}

	return
}

// 同步永源供应链商品分类到本地数据表
// 永源供应链只有一个分类数据，不存在不同账号分类不同情况，这里采用直接清空表数据，在更新分类数据

func (self *Szbao) InitCategory() (err error) {
	url := szbaoData.BaseInfo.Host + "/open/xdxt/api/v2/goods/listGoodsCategory"

	if err, url = GetRequestParams(szbaoData, url); err != nil {
		return
	}

	header := map[string]string{
		"Content-Type": "application/json",
	}

	var requestParam = make(map[string]interface{})
	err, result := utils.Post(url, requestParam, header)
	if err != nil {
		return err
	}

	var res SzbaoCategoryResponse
	if err = json.Unmarshal(result, &res); err != nil {
		return
	}

	if res.Code != 0 {
		err = errors.New(res.Msg)
		return
	}

	// 清空 szbao_categories 表数据
	if err = source.DB().Exec("TRUNCATE TABLE szbao_categories").Error; err != nil {
		return
	}

	// 插入永源供应链商品分类数据
	return source.DB().Save(&res.Data).Error
}

func (self *Szbao) ImportSelectGoodsRun(info publicModel.SelectGoods) (err error, list interface{}) {
	orderPN := GetOrderNo()
	common.GlobalOrderSN = orderPN
	goodsRecord := publicModel.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: len(info.List),
		Status:            1,
	}
	var resultInt []int
	var field string
	field = "source_goods_id"

	err = source.DB().Model(pmodel.Product{}).Where("gather_supply_id=?", info.GatherSupplyID).Pluck(field, &resultInt).Error
	if err != nil {
		log2.Println("查询供应链商品id错误", err)
		return
	}

	if len(info.List) <= 0 {
		log2.Println("导入空数据")
		err = errors.New("导入的是空数据")
		return
	}

	var idsArr []int
	idsArr = GetIdArr(info.List)

	difference := collection.Collect(idsArr).Diff(resultInt).ToIntArray()
	repeat := len(idsArr) - len(difference)
	goodsRecord.RepeatQuantity = repeat //重复数据
	if len(difference) <= 0 {
		goodsRecord.Status = 2
		goodsRecord.CompletionStatus = 1
		source.DB().Omit("goods_arr").Create(&goodsRecord) //当前没有商品可以导入
		return
	}
	source.DB().Omit("goods_arr").Create(&goodsRecord) //创建导入记录
	var goodsList []publicModel.Goods
	for _, v := range difference {
		for _, item := range info.List {
			if item.ID == v {
				goodsList = append(goodsList, item)
			}
		}

	}
	info.List = goodsList
	arrList := splitArray(info.List, 20)
	if info.Categorys == "" {
		info.Categorys = strconv.Itoa(info.BrandID)
	} else {
		info.Categorys = strconv.Itoa(info.BrandID) + "," + info.Categorys
	}
	for index, item := range arrList {
		wg.Add(1)
		fmt.Println("循环", index)
		self.RunSelectGoodsConcurrent(orderPN, item, info.Categorys, info.Key, info.GatherSupplyID)
	}
	wg.Wait()
	err = SetImportRecordCompletion(orderPN)
	fmt.Println("全部完成：")

	return
}
func (self *Szbao) RunSelectGoodsConcurrent(orderPN string, list []publicModel.Goods, category string, key string, gatherSupplyID uint) (err error) {

	defer wg.Done()

	cateList := strings.Split(category, ",")
	var cateId1, cateId2, cateId3, brandId int
	if len(cateList) > 1 {
		cateId1, err = strconv.Atoi(cateList[1])

	}
	if len(cateList) > 2 {
		cateId2, err = strconv.Atoi(cateList[2])

	}
	if len(cateList) > 3 {
		cateId3, err = strconv.Atoi(cateList[3])

	}
	if len(cateList) > 0 {
		brandId, err = strconv.Atoi(cateList[0])

	}

	var listGoods []*pmodel.Product
	var recordError []publicModel.SupplyGoodsImportRecordErrors
	err, listGoods, recordError = self.CommodityAssemblyNew(list, cateId1, cateId2, cateId3, brandId, gatherSupplyID, 0)
	if err != nil {
		return
	}
	if len(listGoods) > 0 {
		err = service.FinalProcessing(listGoods, orderPN)
		if err != nil {
			return
		}

	}
	if len(recordError) > 0 {
		err = service.FinalProcessingError(recordError, orderPN)
		if err != nil {
			return
		}
	}
	return

}
func (st *Szbao) ValidateConfig(params gatherSupplyRequest.ValidateConfigParams) (err error) {
	//reqData := map[string]string{
	//	"appid":     params.AppKey,
	//	"nonce":     utils.GenerateSubId(16),
	//	"timestamp": strconv.Itoa(int(time.Now().Unix())),
	//}
	//
	//var signature string
	////按照字母顺序遍历
	//traverseMapInStringOrder(reqData, func(key string, value string) {
	//	signature += key + "=" + value + "&"
	//})
	//signature += "key=" + params.AppSecret
	//signature = HmacSha256(signature, params.AppSecret)
	//signature = strings.ToUpper(signature)
	//
	//url := params.Host + "/open/xdxt/api/v2/wallet/getWallet" + "?appid=" + source.Strval(reqData["appid"]) + "&nonce=" + source.Strval(reqData["nonce"]) + "&timestamp=" + source.Strval(reqData["timestamp"]) + "&signature=" + source.Strval(signature)
	//
	////var header = make(map[string]string)
	////header["Content-Type"] = "application/json"
	//
	//err, result := utils.Post(url, nil, nil)
	//if err != nil {
	//	return
	//}
	//
	//var response SzbaoBalanceResponseV2
	//
	//if err = json.Unmarshal(result, &response); err != nil {
	//	return
	//}
	//
	//if response.Code != 0 {
	//	err = errors.New(response.Msg)
	//	return
	//}
	//return

	// todo 没有测试配置，暂注释代码
	return
}

func (s *Szbao) DeleteGoods(id uint) (err error) {
	//TODO implement me
	return
}

func (s *Szbao) GetSupplyBalance(GatherSupplyID uint) (err error, balance interface{}) {

	var url = szbaoData.BaseInfo.Host + "/open/xdxt/api/v2/wallet/getWallet"
	err, url = GetRequestParams(szbaoData, url)
	if err != nil {
		return
	}
	var header = make(map[string]string)
	header["Content-Type"] = "application/json"
	err, result := utils.Post(url, nil, header)
	var response SzbaoBalanceResponseV2
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}
	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}
	balance = uint(response.Result.Balance * 100)
	return
}

type BaseInfoData struct {
	AppKey    string `json:"appKey"`
	AppSecret string `json:"appSecret"`
}

type SupplySetting struct {
	BaseInfo   BaseInfoData           `json:"baseInfo"`
	UpdateInfo setting.UpdateInfoData `json:"update"`
	Pricing    setting.PricingData    `json:"pricing"`
	Management setting.Management     `json:"management"`
}
type SzbaoSupplySetting struct {
	SupplySetting
	BaseInfo SzbaoBaseInfoData `json:"baseInfo"`
}

type SzbaoBaseInfoData struct {
	BaseInfoData
	Host    string `json:"host"`
	OssHost string `json:"ossHost"`
}

var szbaoData *SzbaoSupplySetting

type SzToken struct {
	Token  string `json:"token"`
	Expire int64  `json:"expiresAt"`
}

type SzbaoGetTokenRequest struct {
	AppKey    string `json:"app_key" validate:"required"`
	AppSecret string `json:"app_secret"  validate:"required"`
}

type SzbaoCategoryResponse struct {
	Code int                   `json:"code"`
	Msg  string                `json:"msg"`
	Data []model.SzbaoCategory `json:"result"`
}

type SzbaoBrandResponse struct {
	Code int                `json:"code"`
	Data []model.SzbaoBrand `json:"result"`
	Msg  string             `json:"msg"`
}

type SzbaoPageResult struct {
	List     []service2.ProductElasticSearch `json:"list"`
	Total    int64                           `json:"total"`
	Page     int                             `json:"page"`
	PageSize int                             `json:"pageSize"`
	NextUrl  string                          `json:"next_url"`
}
type SzbaoBatchResponse struct {
	Code   int                     `json:"code"`
	Result []model.SzbaoGoodsModel `json:"result"`
	Msg    string                  `json:"msg"`
}

type SzbaoBatchResponseV2 struct {
	Code   int    `json:"code"`
	Msg    string `json:"msg"`
	Result struct {
		Current int                       `json:"current"`
		Total   int                       `json:"total"`
		Records []model.SzbaoGoodsModelV2 `json:"records"`
	} `json:"result"`
	TraceId string `json:"traceId"`
}
type SzbaoBalanceResponseV2 struct {
	Code   int    `json:"code"`
	Msg    string `json:"msg"`
	Result struct {
		Balance           float64 `json:"balance"`
		BalanceChangeTime string  `json:"balanceChangeTime"`
	} `json:"result"`
}
type SzbaoBatchPageResult struct {
	List     []other.Product `json:"list"`
	Total    int64           `json:"total"`
	Page     int             `json:"page"`
	PageSize int             `json:"pageSize"`
	NextUrl  string          `json:"next_url"`
}

var GatherSupplyID uint

func (*Szbao) InitToken() (err error) {

	return
}

type SliceMock struct {
	addr uintptr
	len  int
	cap  int
}

func (*Szbao) InitGoods() (err error) {

	var requestParam url2.Values
	requestParam = GetRequestParamsV1(make(map[string]string), szbaoData)
	err, result := utils.PostForm(szbaoData.BaseInfo.Host+"/vip/api/goods/listGoods", requestParam, nil)
	var response SzbaoBatchResponse
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}
	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}

	var codeList []string
	var szbaoGoods []model.SzbaoGoods
	var newSzbaoGoodsMap = make(map[string]model.SzbaoGoodsModel)

	for _, v := range response.Result {
		var jsonResult []byte
		jsonResult, err = json.Marshal(v)
		h := md5.New()
		h.Write(jsonResult)
		var imgs model.Imgs
		var detailImgs model.DetailImgs
		var forbidBuyArea model.ForbidBuyArea
		err = json.Unmarshal([]byte(v.Imgs), &imgs)
		for ik, img := range imgs {
			imgs[ik] = szbaoData.BaseInfo.OssHost + img
		}
		err = json.Unmarshal([]byte(v.DetailImgs), &detailImgs)
		for dik, dimg := range detailImgs {
			detailImgs[dik] = szbaoData.BaseInfo.OssHost + dimg
		}
		err = json.Unmarshal([]byte(v.ForbidBuyArea), &forbidBuyArea)
		var c1, c2, c3 uint
		if v.CategoryGRList != nil {
			if len(v.CategoryGRList) > 0 {
				c1 = v.CategoryGRList[0].C1
				c2 = v.CategoryGRList[0].C2
				c3 = v.CategoryGRList[0].C3
			}
		}
		szbaoGoods = append(szbaoGoods, model.SzbaoGoods{
			Code:                 v.Code,
			Name:                 v.Name,
			ItemMainImg:          szbaoData.BaseInfo.OssHost + v.ItemMainImg,
			GoodsVideo:           szbaoData.BaseInfo.OssHost + v.GoodsVideo,
			ShareImg:             szbaoData.BaseInfo.OssHost + v.ShareImg,
			ShareVideo:           szbaoData.BaseInfo.OssHost + v.ShareVideo,
			MarketPrice:          uint(int(v.MarketPrice * 100)),
			MarketPriceTagImg:    szbaoData.BaseInfo.OssHost + v.MarketPriceTagImg,
			NormalPrice:          uint(int(v.NormalPrice * 100)),
			Vip1Price:            uint(int(v.Vip1Price * 100)),
			Vip2Price:            uint(int(v.Vip2Price * 100)),
			CurrVipPrice:         uint(int(v.CurrVipPrice * 100)),
			MinBuyNum:            v.MinBuyNum,
			PlusStep:             v.PlusStep,
			Unit:                 v.Unit,
			Weight:               int(v.Weight),
			Volume:               int(v.Volume),
			Stock:                v.Stock,
			State:                v.State,
			SupplierFreightPayer: v.SupplierFreightPayer,
			Imgs:                 imgs,
			DetailImgs:           detailImgs,
			ForbidBuyArea:        forbidBuyArea,
			Category1ID:          c1,
			Category2ID:          c2,
			Category3ID:          c3,
			MD5:                  hex.EncodeToString(h.Sum(nil)),
			GatherSupplyID:       GatherSupplyID,
		})
		codeList = append(codeList, v.Code)
		newSzbaoGoodsMap[v.Code] = v
	}

	//已经导入的商品
	var importGoods []pmodel.Product
	err = source.DB().Preload("Skus").Where("`gather_supply_id` = ?", GatherSupplyID).Where("`sn` in ?", codeList).Find(&importGoods).Error
	if err != nil {
		return
	}
	var importGoodsMap = make(map[string]pmodel.Product)
	for _, goods := range importGoods {
		importGoodsMap[goods.Sn] = goods
	}

	var oldSzbaoGoods []model.SzbaoGoods
	err = source.DB().Where("`gather_supply_id` = ?", GatherSupplyID).Find(&oldSzbaoGoods).Error
	if err != nil {
		return
	}
	if len(oldSzbaoGoods) > 0 {
		var oldSzbaoGoodsMap = make(map[string]model.SzbaoGoods)
		var deleteSzbaoGoodsIds []uint
		var deleteProductIds []uint
		for _, osg := range oldSzbaoGoods {
			oldSzbaoGoodsMap[osg.Code] = osg
			_, ok := newSzbaoGoodsMap[osg.Code]
			if !ok {
				//需要删除
				deleteSzbaoGoodsIds = append(deleteSzbaoGoodsIds, osg.ID)
				_, ok = importGoodsMap[osg.Code]
				if !ok {
					continue
				}
				deleteProductIds = append(deleteProductIds, importGoodsMap[osg.Code].ID)
			}
		}

		var newSzbaoGoods []model.SzbaoGoods
		var updateSzbaoGoods []map[string]interface{}
		//var needUpdateProduct []model.SzbaoGoods
		for _, item := range szbaoGoods {
			_, ok := oldSzbaoGoodsMap[item.Code]
			if !ok {
				//需要新增
				newSzbaoGoods = append(newSzbaoGoods, item)
				continue
			}
			if item.MD5 != oldSzbaoGoodsMap[item.Code].MD5 {
				//需要修改
				updateSzbaoGoodsRow := make(map[string]interface{})
				item.ID = oldSzbaoGoodsMap[item.Code].ID
				updateSzbaoGoodsRow["id"] = item.ID
				updateSzbaoGoodsRow["code"] = item.Code
				updateSzbaoGoodsRow["name"] = item.Name
				var DetailImgs []byte
				DetailImgs, err = json.Marshal(item.DetailImgs)
				if err != nil {
					continue
				}
				updateSzbaoGoodsRow["detailImgs"] = string(DetailImgs)
				var Imgs []byte
				Imgs, err = json.Marshal(item.Imgs)
				if err != nil {
					continue
				}
				updateSzbaoGoodsRow["imgs"] = string(Imgs)
				updateSzbaoGoodsRow["itemMainImg"] = item.ItemMainImg
				updateSzbaoGoodsRow["goodsVideo"] = item.GoodsVideo
				updateSzbaoGoodsRow["shareImg"] = item.ShareImg
				updateSzbaoGoodsRow["shareVideo"] = item.ShareVideo
				updateSzbaoGoodsRow["marketPrice"] = item.MarketPrice
				updateSzbaoGoodsRow["marketPriceTagImg"] = item.MarketPriceTagImg
				updateSzbaoGoodsRow["normalPrice"] = item.NormalPrice
				updateSzbaoGoodsRow["vip1Price"] = item.Vip1Price
				updateSzbaoGoodsRow["vip2Price"] = item.Vip2Price
				updateSzbaoGoodsRow["currVipPrice"] = item.CurrVipPrice
				updateSzbaoGoodsRow["minBuyNum"] = item.MinBuyNum
				updateSzbaoGoodsRow["plusStep"] = item.PlusStep
				updateSzbaoGoodsRow["unit"] = item.Unit
				updateSzbaoGoodsRow["weight"] = item.Weight
				updateSzbaoGoodsRow["volume"] = item.Volume
				updateSzbaoGoodsRow["stock"] = item.Stock
				updateSzbaoGoodsRow["state"] = item.State
				var ForbidBuyArea []byte
				ForbidBuyArea, err = json.Marshal(item.ForbidBuyArea)
				if err != nil {
					continue
				}
				updateSzbaoGoodsRow["forbidBuyArea"] = string(ForbidBuyArea)
				updateSzbaoGoodsRow["supplierFreightPayer"] = item.SupplierFreightPayer

				updateSzbaoGoodsRow["category_1_id"] = item.Category1ID
				updateSzbaoGoodsRow["category_2_id"] = item.Category2ID
				updateSzbaoGoodsRow["category_3_id"] = item.Category3ID
				updateSzbaoGoodsRow["md_5"] = item.MD5
				updateSzbaoGoodsRow["gather_supply_id"] = item.GatherSupplyID
				updateSzbaoGoodsRow["updated_at"] = time.Now().Format("2006-01-02 15:04:05")
				updateSzbaoGoods = append(updateSzbaoGoods, updateSzbaoGoodsRow)
				//_, ojk := importGoodsMap[item.Code]
				//if !ojk {
				//	continue
				//}
				//item.ID = importGoodsMap[item.Code].ID
				//needUpdateProduct = append(needUpdateProduct, item)
			}
		}

		//var updateProductMap []map[string]interface{}
		//var updateSkuMap []map[string]interface{}
		//将product数组转换成map结构以进行批量更新
		//for _, updateProduct := range needUpdateProduct {
		//	updateProductRow := make(map[string]interface{})
		//	updateProductRow["id"] = importGoodsMap[updateProduct.Code].ID
		//	if szbaoData.UpdateInfo.BaseInfo == 1 {
		//		updateProductRow["title"] = updateProduct.Name
		//		updateProductRow["is_display"] = updateProduct.State
		//		updateProductRow["image_url"] = updateProduct.ItemMainImg
		//		var detailImgs string
		//		detailImgs = "<p>"
		//		for _, detailImg := range updateProduct.DetailImgs {
		//			detailImgs += "<img src=\"" + detailImg + "\">"
		//		}
		//		detailImgs += "</p>"
		//		updateProductRow["detail_images"] = detailImgs
		//
		//		var gallery []pmodel.GalleryItem
		//		for _, img := range updateProduct.Imgs {
		//			gallery = append(gallery, pmodel.GalleryItem{
		//				Type: 1,
		//				Src:  img,
		//			})
		//		}
		//		var galleryJson []byte
		//		galleryJson, err = json.Marshal(gallery)
		//		if err != nil {
		//			updateProductRow["gallery"] = ""
		//		} else {
		//			updateProductRow["gallery"] = string(galleryJson)
		//		}
		//
		//	}
		//	updateProductRow["guide_price"] = updateProduct.MarketPrice
		//	updateProductRow["min_price"] = updateProduct.MarketPrice
		//	updateProductRow["max_price"] = updateProduct.MarketPrice
		//	var intX uint64
		//	if szbaoData.Pricing.SupplyAdvice == 1 {
		//		intX, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceGuide, 10, 32)
		//		updateProductRow["origin_price"] = updateProduct.MarketPrice * uint(intX) / 100
		//	} else if szbaoData.Pricing.SupplyAdvice == 2 {
		//		intX, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceAgreement, 10, 32)
		//		updateProductRow["origin_price"] = updateProduct.CurrVipPrice * uint(intX) / 100
		//	} else {
		//		updateProductRow["origin_price"] = updateProduct.MarketPrice
		//	}
		//	updateProductRow["updated_at"] = time.Now().Format("2006-01-02 15:04:05")
		//	//修改规格
		//	updateSkuRow := make(map[string]interface{})
		//	updateSkuRow["id"] = importSkuMap[importGoodsMap[updateProduct.Code].ID].ID
		//	updateSkuRow["guide_price"] = updateProductRow["guide_price"]
		//	updateSkuRow["origin_price"] = updateProductRow["origin_price"]
		//	updateSkuRow["updated_at"] = time.Now().Format("2006-01-02 15:04:05")
		//	if szbaoData.UpdateInfo.CostPrice == 1 {
		//		updateProductRow["cost_price"] = updateProduct.CurrVipPrice
		//		updateSkuRow["cost_price"] = updateProductRow["cost_price"]
		//
		//	}
		//	if szbaoData.UpdateInfo.CurrentPrice == 1 {
		//		if szbaoData.Pricing.SupplySales == 1 {
		//			intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesGuide, 10, 32)
		//			updateProductRow["price"] = updateProduct.MarketPrice * uint(intX) / 100
		//		} else if szbaoData.Pricing.SupplySales == 2 {
		//			intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesAgreement, 10, 32)
		//			updateProductRow["price"] = updateProduct.CurrVipPrice * uint(intX) / 100
		//		} else {
		//			updateProductRow["price"] = updateProduct.MarketPrice
		//		}
		//		updateSkuRow["price"] = updateProductRow["price"]
		//
		//	}
		//
		//	updateProductMap = append(updateProductMap, updateProductRow)
		//	updateSkuMap = append(updateSkuMap, updateSkuRow)
		//}
		if len(updateSzbaoGoods) > 0 {
			err = source.BatchUpdate(updateSzbaoGoods, "szbao_goods", "")
			if err != nil {
				return
			}
		}
		//if len(updateProductMap) > 0 {
		//	err = source.BatchUpdate(updateProductMap, "products", "")
		//	if err != nil {
		//		return
		//	}
		//	for _, updateProduct := range updateProductMap {
		//		err = mq.PublishMessage(updateProduct["id"].(uint), mq.Edit, 0)
		//		if err != nil {
		//			return
		//		}
		//	}
		//}
		//if len(updateSkuMap) > 0 {
		//	err = source.BatchUpdate(updateSkuMap, "skus", "")
		//	if err != nil {
		//		return
		//	}
		//}
		if len(newSzbaoGoods) > 0 {
			err = source.DB().CreateInBatches(&newSzbaoGoods, 500).Error
			if err != nil {
				return
			}
		}
		if len(deleteSzbaoGoodsIds) > 0 {
			err = source.DB().Delete(&model.SzbaoGoods{}, deleteSzbaoGoodsIds).Error
			if err != nil {
				return
			}
		}

		if len(deleteProductIds) > 0 {
			err = source.DB().Model(&pmodel.Product{}).Where("id in ?", deleteProductIds).Update("is_display", 0).Error
			//err = source.DB().Delete(&pmodel.Product{}, deleteProductIds).Error
			if err != nil {
				return
			}
			for _, deleteProductId := range deleteProductIds {
				err = mq.PublishMessage(deleteProductId, mq.Undercarriage, 0)
				if err != nil {
					return
				}
			}
		}

	} else {
		if len(szbaoGoods) > 0 {
			err = source.DB().CreateInBatches(&szbaoGoods, 500).Error
			if err != nil {
				return
			}
		}

	}

	return
}

type GetRequestParamsV2 struct {
	Current int `json:"current"`
	Size    int `json:"size"`
	Query   struct {
		QueryType int   `json:"query_type"`
		GoodsIds  []int `json:"goods_ids"`
	} `json:"query"`
}

func (szbao *Szbao) InitGoodsV2() (err error) {

	var requestParam = GetRequestParamsV2{}
	requestParam.Query.QueryType = 1
	requestParam.Size = 100
	requestParam.Current = 1
	var url = szbaoData.BaseInfo.Host + "/open/xdxt/api/v2/goods/listGoods"
	err, url = GetRequestParams(szbaoData, url)
	if err != nil {
		return
	}
	var header = make(map[string]string)
	header["Content-Type"] = "application/json"
	err, result := utils.Post(url, requestParam, header)
	var response SzbaoBatchResponseV2
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}
	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}
	var szbaoBrands interface{}
	if err, szbaoBrands = szbao.GetBrandList(); err != nil {
		return
	}

	var brandJsonData []byte
	if brandJsonData, err = json.Marshal(szbaoBrands); err != nil {
		return
	}

	var szbaoBrandStruct []model.SzbaoBrand
	if err = json.Unmarshal(brandJsonData, &szbaoBrandStruct); err != nil {
		return
	}

	var szbaoBrandMap = make(map[uint]string)
	for _, szbaoB := range szbaoBrandStruct {
		szbaoBrandMap[szbaoB.ID] = szbaoB.BrandName
	}
	var total = response.Result.Total
	var pages = total/100 + 1
	var codeList []string
	var szbaoGoods []model.SzbaoGoods
	var newSzbaoGoodsMap = make(map[string]model.Specification)
	for i := 1; i <= pages; i++ {
		response = SzbaoBatchResponseV2{}
		requestParam.Current = i
		err, result = utils.Post(url, requestParam, header)
		err = json.Unmarshal(result, &response)
		if err != nil {
			return
		}
		if response.Code != 0 {
			err = errors.New(response.Msg)
			return
		}

		for _, v := range response.Result.Records {
			for _, sku := range v.SpecificationList {

				var forbidBuyArea model.ForbidBuyArea

				err = json.Unmarshal([]byte(sku.ForbidBuyArea), &forbidBuyArea)
				var c1, c2, c3 uint
				if v.GoodsCategoryList != nil {
					if len(v.GoodsCategoryList) > 0 {
						c1 = uint(v.GoodsCategoryList[0].C1)
						c2 = uint(v.GoodsCategoryList[0].C2)
						c3 = uint(v.GoodsCategoryList[0].C3)
					}
				}
				var brandName string
				if _, ok := szbaoBrandMap[v.BrandId]; ok {
					brandName = szbaoBrandMap[v.BrandId]
				}

				var name string

				//var specInfoMap = make(map[string]string)
				//err = json.Unmarshal([]byte(sku.SpecInfo), &specInfoMap)
				//if err != nil {
				//	err = nil
				//}
				//var specInfo []string
				//for _, sim := range specInfoMap {
				//	specInfo = append(specInfo, sim)
				//}
				//specInfoString := strings.Join(specInfo, ";")
				name = v.Title
				var specInfoMap map[string]string
				if utils.IsJSON(sku.SpecInfo) {
					err = json.Unmarshal([]byte(sku.SpecInfo), &specInfoMap)
					if err != nil {
						continue
					}
					for _, spec := range specInfoMap {
						name += "[" + spec + "]"
					}
				}

				szbaoGood := model.SzbaoGoods{
					SpuId:                sku.GoodsId,
					Code:                 sku.Code,
					Name:                 name,
					ItemMainImg:          sku.ItemMainImg,
					GoodsVideo:           sku.Video,
					ShareImg:             "",
					ShareVideo:           "",
					MarketPrice:          uint(int(sku.MarketPrice * 100)),
					RetailPrice:          uint(int(sku.RetailPrice * 100)),
					FreightPrice:         uint(int(sku.FreightPrice * 100)),
					MarketPriceTagImg:    "",
					NormalPrice:          0,
					Vip1Price:            0,
					Vip2Price:            0,
					CurrVipPrice:         uint(int(sku.CurrVipPrice * 100)),
					MinBuyNum:            sku.MinBuyNum,
					PlusStep:             0,
					Unit:                 sku.Unit,
					Weight:               int(sku.Weight),
					Volume:               int(sku.Volume),
					Stock:                sku.StockNum,
					State:                v.State,
					SupplierFreightPayer: sku.IsFreeDelivery,
					Imgs:                 sku.Imgs,
					DetailImgs:           sku.DetailImgs,
					ForbidBuyArea:        forbidBuyArea,
					Category1ID:          c1,
					Category2ID:          c2,
					Category3ID:          c3,
					BrandId:              v.BrandId,
					BrandName:            brandName,
					GatherSupplyID:       GatherSupplyID,
					TaxCode:              sku.TaxCode,
					BillingName:          sku.BillingName,
					BillingSpecName:      sku.BillingSpecName,
					ZpTaxRate:            sku.ZpTaxRate,
				}
				var jsonResult []byte
				jsonResult, err = json.Marshal(szbaoGood)
				if err != nil {
					continue
				}
				h := md5.New()
				h.Write(jsonResult)
				szbaoGood.MD5 = hex.EncodeToString(h.Sum(nil))
				szbaoGoods = append(szbaoGoods, szbaoGood)

				codeList = append(codeList, sku.Code)
				newSzbaoGoodsMap[sku.Code] = sku
			}

		}
	}

	//已经导入的商品
	var importGoods []pmodel.Product
	err = source.DB().Preload("Skus").Where("`gather_supply_id` = ?", GatherSupplyID).Where("`sn` in ?", codeList).Find(&importGoods).Error
	if err != nil {
		return
	}
	var importGoodsMap = make(map[string]pmodel.Product)
	for _, goods := range importGoods {
		importGoodsMap[goods.Sn] = goods
	}

	var oldSzbaoGoods []model.SzbaoGoods
	err = source.DB().Where("`gather_supply_id` = ? and spu_id is null", GatherSupplyID).Find(&oldSzbaoGoods).Error
	if err != nil {
		return
	}
	if len(oldSzbaoGoods) > 0 {
		var oldSzbaoGoodsMap = make(map[string]model.SzbaoGoods)
		var deleteSzbaoGoodsIds []uint
		var deleteProductIds []uint
		for _, osg := range oldSzbaoGoods {
			oldSzbaoGoodsMap[osg.Code] = osg
			_, ok := newSzbaoGoodsMap[osg.Code]
			if !ok {
				//需要删除
				deleteSzbaoGoodsIds = append(deleteSzbaoGoodsIds, osg.ID)
				_, ok = importGoodsMap[osg.Code]
				if !ok {
					continue
				}
				deleteProductIds = append(deleteProductIds, importGoodsMap[osg.Code].ID)
			}
		}

		var newSzbaoGoods []model.SzbaoGoods
		var updateSzbaoGoods []map[string]interface{}
		//var needUpdateProduct []model.SzbaoGoods
		for _, item := range szbaoGoods {
			_, ok := oldSzbaoGoodsMap[item.Code]
			if !ok {
				//需要新增
				newSzbaoGoods = append(newSzbaoGoods, item)
				continue
			}
			if item.MD5 != oldSzbaoGoodsMap[item.Code].MD5 {
				//需要修改
				updateSzbaoGoodsRow := make(map[string]interface{})
				item.ID = oldSzbaoGoodsMap[item.Code].ID
				updateSzbaoGoodsRow["id"] = item.ID
				updateSzbaoGoodsRow["code"] = item.Code
				updateSzbaoGoodsRow["spu_id"] = item.SpuId
				updateSzbaoGoodsRow["name"] = item.Name
				var DetailImgs []byte
				DetailImgs, err = json.Marshal(item.DetailImgs)
				if err != nil {
					continue
				}
				updateSzbaoGoodsRow["detailImgs"] = string(DetailImgs)
				var Imgs []byte
				Imgs, err = json.Marshal(item.Imgs)
				if err != nil {
					continue
				}
				var brandName string
				if _, ok := szbaoBrandMap[item.BrandId]; ok {
					brandName = szbaoBrandMap[item.BrandId]
				}
				updateSzbaoGoodsRow["imgs"] = string(Imgs)
				updateSzbaoGoodsRow["itemMainImg"] = item.ItemMainImg
				updateSzbaoGoodsRow["name"] = item.Name
				updateSzbaoGoodsRow["goodsVideo"] = item.GoodsVideo
				updateSzbaoGoodsRow["shareImg"] = item.ShareImg
				updateSzbaoGoodsRow["shareVideo"] = item.ShareVideo
				updateSzbaoGoodsRow["marketPrice"] = item.MarketPrice
				updateSzbaoGoodsRow["retailPrice"] = item.RetailPrice
				updateSzbaoGoodsRow["freightPrice"] = item.FreightPrice
				updateSzbaoGoodsRow["marketPriceTagImg"] = item.MarketPriceTagImg
				updateSzbaoGoodsRow["normalPrice"] = item.NormalPrice
				updateSzbaoGoodsRow["vip1Price"] = item.Vip1Price
				updateSzbaoGoodsRow["vip2Price"] = item.Vip2Price
				updateSzbaoGoodsRow["currVipPrice"] = item.CurrVipPrice
				updateSzbaoGoodsRow["minBuyNum"] = item.MinBuyNum
				updateSzbaoGoodsRow["plusStep"] = item.PlusStep
				updateSzbaoGoodsRow["unit"] = item.Unit
				updateSzbaoGoodsRow["weight"] = item.Weight
				updateSzbaoGoodsRow["volume"] = item.Volume
				updateSzbaoGoodsRow["stock"] = item.Stock
				updateSzbaoGoodsRow["state"] = item.State
				var ForbidBuyArea []byte
				ForbidBuyArea, err = json.Marshal(item.ForbidBuyArea)
				if err != nil {
					continue
				}
				updateSzbaoGoodsRow["forbidBuyArea"] = string(ForbidBuyArea)
				updateSzbaoGoodsRow["supplierFreightPayer"] = item.SupplierFreightPayer

				updateSzbaoGoodsRow["category_1_id"] = item.Category1ID
				updateSzbaoGoodsRow["category_2_id"] = item.Category2ID
				updateSzbaoGoodsRow["category_3_id"] = item.Category3ID
				updateSzbaoGoodsRow["brandId"] = item.BrandId
				updateSzbaoGoodsRow["brandName"] = brandName
				updateSzbaoGoodsRow["md_5"] = item.MD5
				updateSzbaoGoodsRow["gather_supply_id"] = item.GatherSupplyID
				updateSzbaoGoodsRow["tax_code"] = item.TaxCode
				updateSzbaoGoodsRow["billing_name"] = item.BillingName
				updateSzbaoGoodsRow["billing_spec_name"] = item.BillingSpecName
				updateSzbaoGoodsRow["zp_tax_rate"] = item.ZpTaxRate
				updateSzbaoGoodsRow["updated_at"] = time.Now().Format("2006-01-02 15:04:05")
				updateSzbaoGoods = append(updateSzbaoGoods, updateSzbaoGoodsRow)
				//_, ojk := importGoodsMap[item.Code]
				//if !ojk {
				//	continue
				//}
				//item.ID = importGoodsMap[item.Code].ID
				//needUpdateProduct = append(needUpdateProduct, item)
			}
		}

		//var updateProductMap []map[string]interface{}
		//var updateSkuMap []map[string]interface{}
		//将product数组转换成map结构以进行批量更新
		//for _, updateProduct := range needUpdateProduct {
		//	updateProductRow := make(map[string]interface{})
		//	updateProductRow["id"] = importGoodsMap[updateProduct.Code].ID
		//	if szbaoData.UpdateInfo.BaseInfo == 1 {
		//		updateProductRow["title"] = updateProduct.Name
		//		updateProductRow["is_display"] = updateProduct.State
		//		updateProductRow["image_url"] = updateProduct.ItemMainImg
		//		var detailImgs string
		//		detailImgs = "<p>"
		//		for _, detailImg := range updateProduct.DetailImgs {
		//			detailImgs += "<img src=\"" + detailImg + "\">"
		//		}
		//		detailImgs += "</p>"
		//		updateProductRow["detail_images"] = detailImgs
		//
		//		var gallery []pmodel.GalleryItem
		//		for _, img := range updateProduct.Imgs {
		//			gallery = append(gallery, pmodel.GalleryItem{
		//				Type: 1,
		//				Src:  img,
		//			})
		//		}
		//		var galleryJson []byte
		//		galleryJson, err = json.Marshal(gallery)
		//		if err != nil {
		//			updateProductRow["gallery"] = ""
		//		} else {
		//			updateProductRow["gallery"] = string(galleryJson)
		//		}
		//
		//	}
		//	updateProductRow["guide_price"] = updateProduct.MarketPrice
		//	updateProductRow["min_price"] = updateProduct.MarketPrice
		//	updateProductRow["max_price"] = updateProduct.MarketPrice
		//	var intX uint64
		//	if szbaoData.Pricing.SupplyAdvice == 1 {
		//		intX, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceGuide, 10, 32)
		//		updateProductRow["origin_price"] = updateProduct.MarketPrice * uint(intX) / 100
		//	} else if szbaoData.Pricing.SupplyAdvice == 2 {
		//		intX, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceAgreement, 10, 32)
		//		updateProductRow["origin_price"] = updateProduct.CurrVipPrice * uint(intX) / 100
		//	} else {
		//		updateProductRow["origin_price"] = updateProduct.MarketPrice
		//	}
		//	updateProductRow["updated_at"] = time.Now().Format("2006-01-02 15:04:05")
		//	//修改规格
		//	updateSkuRow := make(map[string]interface{})
		//	updateSkuRow["id"] = importSkuMap[importGoodsMap[updateProduct.Code].ID].ID
		//	updateSkuRow["guide_price"] = updateProductRow["guide_price"]
		//	updateSkuRow["origin_price"] = updateProductRow["origin_price"]
		//	updateSkuRow["updated_at"] = time.Now().Format("2006-01-02 15:04:05")
		//	if szbaoData.UpdateInfo.CostPrice == 1 {
		//		updateProductRow["cost_price"] = updateProduct.CurrVipPrice
		//		updateSkuRow["cost_price"] = updateProductRow["cost_price"]
		//
		//	}
		//	if szbaoData.UpdateInfo.CurrentPrice == 1 {
		//		if szbaoData.Pricing.SupplySales == 1 {
		//			intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesGuide, 10, 32)
		//			updateProductRow["price"] = updateProduct.MarketPrice * uint(intX) / 100
		//		} else if szbaoData.Pricing.SupplySales == 2 {
		//			intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesAgreement, 10, 32)
		//			updateProductRow["price"] = updateProduct.CurrVipPrice * uint(intX) / 100
		//		} else {
		//			updateProductRow["price"] = updateProduct.MarketPrice
		//		}
		//		updateSkuRow["price"] = updateProductRow["price"]
		//
		//	}
		//
		//	updateProductMap = append(updateProductMap, updateProductRow)
		//	updateSkuMap = append(updateSkuMap, updateSkuRow)
		//}
		if len(updateSzbaoGoods) > 0 {
			err = source.BatchUpdate(updateSzbaoGoods, "szbao_goods", "")
			if err != nil {
				return
			}
		}
		//if len(updateProductMap) > 0 {
		//	err = source.BatchUpdate(updateProductMap, "products", "")
		//	if err != nil {
		//		return
		//	}
		//	for _, updateProduct := range updateProductMap {
		//		err = mq.PublishMessage(updateProduct["id"].(uint), mq.Edit, 0)
		//		if err != nil {
		//			return
		//		}
		//	}
		//}
		//if len(updateSkuMap) > 0 {
		//	err = source.BatchUpdate(updateSkuMap, "skus", "")
		//	if err != nil {
		//		return
		//	}
		//}
		if len(newSzbaoGoods) > 0 {
			//err = source.DB().CreateInBatches(&newSzbaoGoods, 500).Error
			//if err != nil {
			//	return
			//}
		}
		if len(deleteSzbaoGoodsIds) > 0 {
			err = source.DB().Delete(&model.SzbaoGoods{}, deleteSzbaoGoodsIds).Error
			if err != nil {
				return
			}
		}

		if len(deleteProductIds) > 0 {
			err = source.DB().Model(&pmodel.Product{}).Where("id in ?", deleteProductIds).Update("is_display", 0).Error
			//err = source.DB().Delete(&pmodel.Product{}, deleteProductIds).Error
			if err != nil {
				return
			}
			for _, deleteProductId := range deleteProductIds {
				err = mq.PublishMessage(deleteProductId, mq.Undercarriage, 0)
				if err != nil {
					return
				}
			}
		}

	} else {
		//if len(szbaoGoods) > 0 {
		//	err = source.DB().CreateInBatches(&szbaoGoods, 500).Error
		//	if err != nil {
		//		return
		//	}
		//}

	}

	return
}

func (szbao *Szbao) InitGoodsV2New() (err error) {

	var requestParam = GetRequestParamsV2{}
	requestParam.Query.QueryType = 1
	requestParam.Size = 100
	requestParam.Current = 1
	var url = szbaoData.BaseInfo.Host + "/open/xdxt/api/v2/goods/listGoods"
	err, url = GetRequestParams(szbaoData, url)
	if err != nil {
		return
	}
	var header = make(map[string]string)
	header["Content-Type"] = "application/json"
	err, result := utils.Post(url, requestParam, header)
	var response SzbaoBatchResponseV2
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}
	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}
	var szbaoBrands interface{}
	if err, szbaoBrands = szbao.GetBrandList(); err != nil {
		return
	}

	var brandJsonData []byte
	if brandJsonData, err = json.Marshal(szbaoBrands); err != nil {
		return
	}

	var szbaoBrandStruct []model.SzbaoBrand
	if err = json.Unmarshal(brandJsonData, &szbaoBrandStruct); err != nil {
		return
	}

	var szbaoBrandMap = make(map[uint]string)
	for _, szbaoB := range szbaoBrandStruct {
		szbaoBrandMap[szbaoB.ID] = szbaoB.BrandName
	}
	var total = response.Result.Total
	var pages = total/100 + 1
	var codeList []uint
	var szbaoGoods []model.SzbaoGoodsV2
	var newSzbaoGoodsMap = make(map[uint]model.SzbaoGoodsV2)
	for i := 1; i <= pages; i++ {
		response = SzbaoBatchResponseV2{}
		requestParam.Current = i
		err, result = utils.Post(url, requestParam, header)
		err = json.Unmarshal(result, &response)
		if err != nil {
			return
		}
		if response.Code != 0 {
			err = errors.New(response.Msg)
			return
		}

		for _, v := range response.Result.Records {

			var c1, c2, c3 uint
			if v.GoodsCategoryList != nil {
				if len(v.GoodsCategoryList) > 0 {
					c1 = uint(v.GoodsCategoryList[0].C1)
					c2 = uint(v.GoodsCategoryList[0].C2)
					c3 = uint(v.GoodsCategoryList[0].C3)
				}
			}
			var brandName string
			if _, ok := szbaoBrandMap[v.BrandId]; ok {
				brandName = szbaoBrandMap[v.BrandId]
			}
			var minZpTaxRate, maxZpTaxRate float64
			var minCurrentVipPrice, maxCurrentVipPrice float64
			var minMarketPrice, maxMarketPrice float64
			var minFreightPrice, maxFreightPrice float64
			var minRetailPrice, maxRetailPrice float64
			var isFreeDelivery, stock int
			for _, sku := range v.SpecificationList {
				if sku.ZpTaxRate > maxZpTaxRate {
					maxZpTaxRate = sku.ZpTaxRate
				}
				if minZpTaxRate == 0 || minZpTaxRate <= sku.ZpTaxRate {
					minZpTaxRate = sku.ZpTaxRate
				}

				if sku.CurrVipPrice > maxCurrentVipPrice {
					maxCurrentVipPrice = sku.CurrVipPrice
				}
				if minCurrentVipPrice == 0 || minCurrentVipPrice <= sku.CurrVipPrice {
					minCurrentVipPrice = sku.CurrVipPrice
				}

				if sku.MarketPrice > maxMarketPrice {
					maxMarketPrice = sku.MarketPrice
				}
				if minMarketPrice == 0 || minMarketPrice <= sku.MarketPrice {
					minMarketPrice = sku.MarketPrice
				}

				if sku.FreightPrice > maxFreightPrice {
					maxFreightPrice = sku.FreightPrice
				}
				if minFreightPrice == 0 || minFreightPrice <= sku.FreightPrice {
					minFreightPrice = sku.FreightPrice
				}

				if sku.RetailPrice > maxRetailPrice {
					maxRetailPrice = sku.RetailPrice
				}
				if minRetailPrice == 0 || minRetailPrice <= sku.RetailPrice {
					minRetailPrice = sku.RetailPrice
				}
				isFreeDelivery = sku.IsFreeDelivery
				stock += sku.StockNum
			}

			szbaoGood := model.SzbaoGoodsV2{
				Model:              source.Model{ID: uint(v.Id)},
				Title:              v.Title,
				MainImg:            v.MainImg,
				State:              v.State,
				Category1ID:        c1,
				Category2ID:        c2,
				Category3ID:        c3,
				BrandId:            v.BrandId,
				BrandName:          brandName,
				GatherSupplyID:     GatherSupplyID,
				MinZpTaxRate:       minZpTaxRate,
				MaxZpTaxRate:       maxZpTaxRate,
				MinCurrentVipPrice: minCurrentVipPrice,
				MaxCurrentVipPrice: maxCurrentVipPrice,
				MinMarketPrice:     minMarketPrice,
				MaxMarketPrice:     maxMarketPrice,
				MinFreightPrice:    minFreightPrice,
				MaxFreightPrice:    maxFreightPrice,
				MinRetailPrice:     minRetailPrice,
				MaxRetailPrice:     maxRetailPrice,
				IsFreeDelivery:     isFreeDelivery,
				Stock:              stock,
				SpecificationList:  v.SpecificationList,
			}
			var jsonResult []byte
			jsonResult, err = json.Marshal(szbaoGood)
			if err != nil {
				continue
			}
			h := md5.New()
			h.Write(jsonResult)
			szbaoGood.MD5 = hex.EncodeToString(h.Sum(nil))
			szbaoGoods = append(szbaoGoods, szbaoGood)
			codeList = append(codeList, szbaoGood.ID)
			newSzbaoGoodsMap[szbaoGood.ID] = szbaoGood

		}
	}

	//已经导入的商品
	var importGoods []pmodel.Product
	err = source.DB().Preload("Skus").Where("`gather_supply_id` = ?", GatherSupplyID).Where("`source_goods_id` in ?", codeList).Find(&importGoods).Error
	if err != nil {
		return
	}
	var importGoodsMap = make(map[uint]pmodel.Product)
	for _, goods := range importGoods {
		importGoodsMap[goods.SourceGoodsID] = goods
	}

	var oldSzbaoGoods []model.SzbaoGoodsV2
	err = source.DB().Where("`gather_supply_id` = ?", GatherSupplyID).Find(&oldSzbaoGoods).Error
	if err != nil {
		return
	}
	if len(oldSzbaoGoods) > 0 {
		var oldSzbaoGoodsMap = make(map[uint]model.SzbaoGoodsV2)
		var deleteSzbaoGoodsIds []uint
		var deleteProductIds []uint
		for _, osg := range oldSzbaoGoods {
			oldSzbaoGoodsMap[osg.ID] = osg
			_, ok := newSzbaoGoodsMap[osg.ID]
			if !ok {
				//需要删除
				deleteSzbaoGoodsIds = append(deleteSzbaoGoodsIds, osg.ID)
				_, ok = importGoodsMap[osg.ID]
				if !ok {
					continue
				}
				deleteProductIds = append(deleteProductIds, importGoodsMap[osg.ID].ID)
			}
		}

		var newSzbaoGoods []model.SzbaoGoodsV2
		var updateSzbaoGoods []model.SzbaoGoodsV2
		//var needUpdateProduct []model.SzbaoGoods
		for _, item := range szbaoGoods {
			_, ok := oldSzbaoGoodsMap[item.ID]
			if !ok {
				//需要新增
				newSzbaoGoods = append(newSzbaoGoods, item)
				continue
			}
			if item.MD5 != oldSzbaoGoodsMap[item.ID].MD5 {
				updateSzbaoGoods = append(updateSzbaoGoods, item)
			}
		}

		if len(updateSzbaoGoods) > 0 {
			for _, updateSzbaoGood := range updateSzbaoGoods {
				err = source.DB().Updates(&updateSzbaoGood).Error
				if err != nil {
					return
				}
			}

		}

		if len(newSzbaoGoods) > 0 {
			err = source.DB().CreateInBatches(&newSzbaoGoods, 500).Error
			if err != nil {
				return
			}
		}
		if len(deleteSzbaoGoodsIds) > 0 {
			err = source.DB().Delete(&model.SzbaoGoodsV2{}, deleteSzbaoGoodsIds).Error
			if err != nil {
				return
			}
			err = source.DB().Delete(&model.Specification{}, "goodsId = ?", deleteSzbaoGoodsIds).Error
			if err != nil {
				return
			}
		}

		if len(deleteProductIds) > 0 {
			err = source.DB().Model(&pmodel.Product{}).Where("id in ?", deleteProductIds).Update("is_display", 0).Error
			//err = source.DB().Delete(&pmodel.Product{}, deleteProductIds).Error
			if err != nil {
				return
			}
			for _, deleteProductId := range deleteProductIds {
				err = mq.PublishMessage(deleteProductId, mq.Undercarriage, 0)
				if err != nil {
					return
				}
			}
		}

	} else {
		if len(szbaoGoods) > 0 {
			err = source.DB().CreateInBatches(&szbaoGoods, 500).Error
			if err != nil {
				return
			}
		}

	}

	return
}

func (szbao *Szbao) InitProducts() (err error) {
	var allProducts []service2.ProductForUpdate
	if err = source.DB().Preload("Skus").Where("gather_supply_id = ? and (sn != '' or source_goods_id_string != '')", GatherSupplyID).Where("md5 != '商品已删除'").Find(&allProducts).Error; err != nil {
		return
	}

	var allSzbaoProducts []model.SzbaoGoods
	if err = source.DB().Where("gather_supply_id = ?", GatherSupplyID).Find(&allSzbaoProducts).Error; err != nil {
		return
	}

	var allSzbaoProductsMap = make(map[string]model.SzbaoGoods)
	for _, dwdProduct := range allSzbaoProducts {
		allSzbaoProductsMap[dwdProduct.Code] = dwdProduct
	}

	var szbaoBrands interface{}
	if err, szbaoBrands = szbao.GetBrandList(); err != nil {
		return
	}

	var brandJsonData []byte
	if brandJsonData, err = json.Marshal(szbaoBrands); err != nil {
		return
	}

	var szbaoBrandStruct []model.SzbaoBrand
	if err = json.Unmarshal(brandJsonData, &szbaoBrandStruct); err != nil {
		return
	}

	var szbaoBrandMap = make(map[uint]model.SzbaoBrand)
	for _, szbaoB := range szbaoBrandStruct {
		szbaoBrandMap[szbaoB.ID] = szbaoB
	}

	var updateProducts []service2.ProductForUpdate
	for _, product := range allProducts {
		if product.Sn != "" {
			_, ok := allSzbaoProductsMap[product.Sn]
			if !ok {
				product.MD5 = "商品已删除"
				product.IsDisplay = 0
				product.StatusLock = 1
				product.SourceGoodsIDString = "canmerge"

				updateProducts = append(updateProducts, product)
				continue
			}
			var detail = allSzbaoProductsMap[product.Sn]
			if detail.SpuId > 0 {
				product.MD5 = detail.MD5
				product.SourceGoodsID = uint(detail.SpuId)
				product.SourceGoodsIDString = "canmerge"

				if szbaoData.UpdateInfo.CurrentPrice == 1 {
					//是否更新供货价
					var intX uint64
					if szbaoData.Pricing.SupplySales == 1 {
						intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesGuide, 10, 32)
						product.Price = detail.MarketPrice * uint(intX) / 100
					} else if szbaoData.Pricing.SupplySales == 2 {
						if detail.ZpTaxRate == 0 || szbaoData.Pricing.SupplySalesIsTax == 0 {
							intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesAgreement, 10, 32)
							product.Price = detail.CurrVipPrice * uint(intX) / 100
						} else {
							intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesTax, 10, 32)
							product.Price = detail.CurrVipPrice * uint(intX) / 100
						}

					} else if szbaoData.Pricing.SupplySales == 3 {
						intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesMarketing, 10, 32)
						if detail.RetailPrice > 0 {
							product.Price = detail.RetailPrice * uint(intX) / 100
						} else {
							product.Price = detail.MarketPrice * uint(intX) / 100
						}
					} else if szbaoData.Pricing.SupplyGuide == 4 {
						intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesFreight, 10, 32)
						if detail.FreightPrice > 0 {
							product.Price = detail.FreightPrice * uint(intX) / 100
						} else {
							product.Price = detail.CurrVipPrice * uint(intX) / 100
						}
					} else {
						product.Price = detail.CurrVipPrice
					}

				}
				if szbaoData.UpdateInfo.CostPrice == 1 {
					//是否更新成本价
					var intXCost uint64
					if szbaoData.Pricing.SupplyCost == 1 {
						intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostGuide, 10, 32)
						product.CostPrice = detail.MarketPrice * uint(intXCost) / 100
					} else if szbaoData.Pricing.SupplyCost == 2 {
						intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostAgreement, 10, 32)
						product.CostPrice = detail.CurrVipPrice * uint(intXCost) / 100
					} else if szbaoData.Pricing.SupplyCost == 3 {
						intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostMarketing, 10, 32)
						if detail.RetailPrice > 0 {
							product.CostPrice = detail.RetailPrice * uint(intXCost) / 100
						} else {
							product.CostPrice = detail.MarketPrice * uint(intXCost) / 100
						}
					} else if szbaoData.Pricing.SupplyCost == 4 {
						intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostFreight, 10, 32)
						if detail.FreightPrice > 0 {
							product.CostPrice = detail.FreightPrice * uint(intXCost) / 100
						} else {
							product.CostPrice = detail.CurrVipPrice * uint(intXCost) / 100
						}
					} else {
						product.CostPrice = detail.CurrVipPrice
					}
				}
				if szbaoData.UpdateInfo.ActivityPrice == 1 {
					var intXActivity uint64
					if szbaoData.Pricing.SupplyActivity == 1 {
						intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityGuide, 10, 32)
						product.ActivityPrice = detail.MarketPrice * uint(intXActivity) / 100
					} else if szbaoData.Pricing.SupplyActivity == 2 {
						intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityAgreement, 10, 32)
						product.ActivityPrice = detail.CurrVipPrice * uint(intXActivity) / 100
					} else if szbaoData.Pricing.SupplyActivity == 3 {
						intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityMarketing, 10, 32)
						if detail.RetailPrice > 0 {
							product.ActivityPrice = detail.RetailPrice * uint(intXActivity) / 100
						} else {
							product.ActivityPrice = detail.MarketPrice * uint(intXActivity) / 100
						}
					} else if szbaoData.Pricing.SupplyActivity == 4 {
						intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityFreight, 10, 32)
						if detail.FreightPrice > 0 {
							product.ActivityPrice = detail.FreightPrice * uint(intXActivity) / 100
						} else {
							product.ActivityPrice = detail.CurrVipPrice * uint(intXActivity) / 100
						}
					} else {
						product.ActivityPrice = detail.MarketPrice
					}

				}
				if szbaoData.UpdateInfo.GuidePrice == 1 {
					var intXGuide uint64
					if szbaoData.Pricing.SupplyGuide == 1 {
						intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideGuide, 10, 32)
						product.GuidePrice = detail.MarketPrice * uint(intXGuide) / 100
					} else if szbaoData.Pricing.SupplyGuide == 2 {
						intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideAgreement, 10, 32)
						product.GuidePrice = detail.CurrVipPrice * uint(intXGuide) / 100
					} else if szbaoData.Pricing.SupplyGuide == 3 {
						intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideMarketing, 10, 32)
						if detail.RetailPrice > 0 {
							product.GuidePrice = detail.RetailPrice * uint(intXGuide) / 100
						} else {
							product.GuidePrice = detail.MarketPrice * uint(intXGuide) / 100
						}
					} else if szbaoData.Pricing.SupplyGuide == 4 {
						intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideFreight, 10, 32)
						if detail.FreightPrice > 0 {
							product.GuidePrice = detail.FreightPrice * uint(intXGuide) / 100
						} else {
							product.GuidePrice = detail.CurrVipPrice * uint(intXGuide) / 100
						}
					} else {
						product.GuidePrice = detail.MarketPrice
					}
				}
				if szbaoData.UpdateInfo.OriginalPrice == 1 {
					var intXAdvice uint64
					if szbaoData.Pricing.SupplyAdvice == 1 {
						intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceGuide, 10, 32)
						product.OriginPrice = detail.MarketPrice * uint(intXAdvice) / 100
					} else if szbaoData.Pricing.SupplyAdvice == 2 {
						intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceAgreement, 10, 32)
						product.OriginPrice = detail.CurrVipPrice * uint(intXAdvice) / 100
					} else if szbaoData.Pricing.SupplyAdvice == 3 {
						intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceMarketing, 10, 32)
						if detail.RetailPrice > 0 {
							product.OriginPrice = detail.RetailPrice * uint(intXAdvice) / 100
						} else {
							product.OriginPrice = detail.MarketPrice * uint(intXAdvice) / 100
						}
					} else if szbaoData.Pricing.SupplyGuide == 4 {
						intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceFreight, 10, 32)
						if detail.FreightPrice > 0 {
							product.OriginPrice = detail.FreightPrice * uint(intXAdvice) / 100
						} else {
							product.OriginPrice = detail.CurrVipPrice * uint(intXAdvice) / 100
						}
					} else {
						product.OriginPrice = detail.MarketPrice
					}
				}

				//if szbaoData.Management.ProductPriceStatus == 1 {
				//	if product.Price < product.CostPrice*(szbaoData.Management.Products/100) {
				//		product.IsDisplay = 0
				//	}
				//} else if szbaoData.Management.ProductPriceStatus == 2 {
				//	if (product.Price-product.CostPrice)/product.CostPrice < szbaoData.Management.Profit/100 {
				//		product.IsDisplay = 0
				//	}
				//}

				// 分类
				if szbaoData.UpdateInfo.CateGory == 1 {
					szCategoryIds := []uint{detail.Category1ID, detail.Category2ID, detail.Category3ID}
					// 查询永源分类数据
					var szCategories []model.SzbaoCategory
					if err = source.DB().Where("`id` IN ?", szCategoryIds).Find(&szCategories).Error; err != nil {
						return
					}

					// 组装永源分类数据 map
					szCategoryMap := make(map[uint]model.SzbaoCategory)
					for _, szCategory := range szCategories {
						szCategoryMap[szCategory.ID] = szCategory
					}

					display := 1
					// 一级分类
					var category1 catemodel.Category
					if fSzCategory, fOk := szCategoryMap[detail.Category1ID]; fOk {
						category1.IsDisplay = &display
						category1.ParentID = 0
						category1.Level = 1
						category1.Name = fSzCategory.Name
						category1.Icon = fSzCategory.Img
						category1.Image = fSzCategory.Img
						if err = source.DB().Where("name=? and level=? and parent_id=?", category1.Name, category1.Level, category1.ParentID).FirstOrCreate(&category1).Error; err != nil {
							return
						}
						// 更新图标
						if category1.Icon == "" {
							if err = source.DB().Model(&category1).Update("icon", fSzCategory.Img).Error; err != nil {
								return
							}
						}
						if category1.Image == "" {
							if err = source.DB().Model(&category1).Update("image", fSzCategory.Img).Error; err != nil {
								return
							}
						}
					} else {
						category1.IsDisplay = &display
						category1.ParentID = 0
						category1.Level = 1
						category1.Name = "默认"
						source.DB().Where("name=? and level=? and parent_id=?", category1.Name, category1.Level, category1.ParentID).FirstOrCreate(&category1)
					}

					// 二级分类
					var category2 catemodel.Category
					if sSzCategory, sOk := szCategoryMap[detail.Category2ID]; sOk {
						category2.IsDisplay = &display
						category2.ParentID = category1.ID
						category2.Level = 2
						category2.Name = sSzCategory.Name
						category2.Icon = sSzCategory.Img
						category2.Image = sSzCategory.Img
						if err = source.DB().Where("name=? and level=? and parent_id=?", category2.Name, category2.Level, category2.ParentID).FirstOrCreate(&category2).Error; err != nil {
							return
						}
						// 更新图标
						if category2.Icon == "" {
							if err = source.DB().Model(&category2).Update("icon", sSzCategory.Img).Error; err != nil {
								return
							}
						}
						if category2.Image == "" {
							if err = source.DB().Model(&category2).Update("image", sSzCategory.Img).Error; err != nil {
								return
							}
						}
					} else {
						category2.IsDisplay = &display
						category2.ParentID = category1.ID
						category2.Level = 2
						category2.Name = "默认"
						source.DB().Where("name=? and level=? and parent_id=?", category2.Name, category2.Level, category2.ParentID).FirstOrCreate(&category2)
					}

					// 三级分类
					var category3 catemodel.Category
					if tSzCategory, tOk := szCategoryMap[detail.Category1ID]; tOk {
						category3.IsDisplay = &display
						category3.ParentID = category2.ID
						category3.Level = 3
						category3.Name = tSzCategory.Name
						category3.Icon = tSzCategory.Img
						category3.Image = tSzCategory.Img
						if err = source.DB().Where("name=? and level=? and parent_id=?", category3.Name, category3.Level, category3.ParentID).FirstOrCreate(&category3).Error; err != nil {
							return
						}
						// 更新图标
						if category3.Icon == "" {
							if err = source.DB().Model(&category3).Update("icon", tSzCategory.Img).Error; err != nil {
								return
							}
						}
						if category3.Image == "" {
							if err = source.DB().Model(&category3).Update("image", tSzCategory.Img).Error; err != nil {
								return
							}
						}
					} else {
						category3.IsDisplay = &display
						category3.ParentID = category2.ID
						category3.Level = 3
						category3.Name = "默认"
						source.DB().Where("name=? and level=? and parent_id=?", category3.Name, category3.Level, category3.ParentID).FirstOrCreate(&category3)
					}

					product.Category1ID = category1.ID
					product.Category2ID = category2.ID
					product.Category3ID = category3.ID

					// 品牌
					if szbaoBrand, szbok := szbaoBrandMap[detail.BrandId]; szbok {
						var brand catemodel.Brand
						// 根据名称查询品牌记录
						if err = source.DB().Where("`name` = ?", szbaoBrand.BrandName).First(&brand).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
							return
						}

						// 如果品牌不存在，则创建
						if brand.ID == 0 {
							err = nil
							brand.Name = szbaoBrand.BrandName
							brand.Logo = szbaoBrand.BrandImg
							brand.Source = int(GatherSupplyID)
							if err = source.DB().Create(&brand).Error; err != nil {
								return
							}
						}

						// 如果品牌存在，图片不存在，更新图片信息
						if brand.Logo == "" {
							brand.Logo = szbaoBrand.BrandImg
							if err = source.DB().Model(&brand).Update("logo", brand.Logo).Error; err != nil {
								return
							}
						}

						// 赋值品牌ID
						product.BrandID = brand.ID
					}
				}

				if szbaoData.UpdateInfo.BaseInfo == 1 {
					//是否更新基本信息
					product.Title = detail.Name
					product.Stock = uint(detail.Stock)
					if product.StatusLock == 0 {
						product.IsDisplay = detail.State
					}
					product.ImageUrl = detail.ItemMainImg
					product.Sn = detail.Code
					product.Gallery = pmodel.Gallery{}
					for _, img := range detail.Imgs {
						product.Gallery = append(product.Gallery, pmodel.GalleryItem{
							Type: 1,
							Src:  img + "?x-oss-process=style/normal",
						})
					}
					product.DetailImages = "<p>"
					for _, detailImg := range detail.DetailImgs {
						product.DetailImages += "<img src=\"" + detailImg + "?x-oss-process=style/normal\">"
					}
					product.DetailImages += "</p>"
				}

				var totalStock int

				product.MinPrice = uint(int(product.Price))
				product.MaxPrice = uint(int(product.Price))
				var sku = service2.Sku{}
				var minProfitRate float64
				if len(product.Skus) == 0 {
					var options pmodel.Options
					var option pmodel.Option
					option.SpecName = "规格"
					option.SpecItemName = "默认"
					options = append(options, option)
					sku.Title = "默认"
					sku.Options = options
					sku.Weight = 0
					sku.CostPrice = product.CostPrice
					sku.Stock = int(product.Stock)
					totalStock += sku.Stock
					sku.IsDisplay = product.IsDisplay
					sku.Price = product.Price
					sku.OriginPrice = product.OriginPrice
					sku.GuidePrice = product.GuidePrice
					sku.ActivityPrice = product.ActivityPrice
					sku.OriginalSkuID = int(detail.ID)
					sku.Sn = product.Sn
					if sku.GuidePrice > 0 {
						sku.ProfitRate = utils.ExecProfitRate(sku.GuidePrice, sku.Price)
					} else {
						sku.ProfitRate = 0
					}
					minProfitRate = sku.ProfitRate
					if detail.TaxCode != "" {
						product.BillPosition = 2
						sku.TaxCode = detail.TaxCode
						sku.FreeOfTax = 1
					}
					product.Skus = append(product.Skus, sku)

				} else {
					for k, pSku := range product.Skus {
						var options pmodel.Options
						var option pmodel.Option
						option.SpecName = "规格"
						option.SpecItemName = "默认"
						options = append(options, option)
						pSku.Title = "默认"
						pSku.Options = options
						pSku.Weight = 0
						pSku.CostPrice = product.CostPrice
						pSku.Stock = int(product.Stock)
						totalStock += pSku.Stock
						pSku.IsDisplay = product.IsDisplay
						pSku.Price = product.Price
						pSku.OriginPrice = product.OriginPrice
						pSku.GuidePrice = product.GuidePrice
						pSku.ActivityPrice = product.ActivityPrice
						pSku.OriginalSkuID = int(detail.ID)
						pSku.Sn = product.Sn
						if pSku.GuidePrice > 0 {
							pSku.ProfitRate = utils.ExecProfitRate(pSku.GuidePrice, pSku.Price)
						} else {
							pSku.ProfitRate = 0
						}
						if k == 0 {
							minProfitRate = pSku.ProfitRate
						}
						if pSku.ProfitRate <= minProfitRate {
							minProfitRate = pSku.ProfitRate
						}
						if detail.TaxCode != "" {
							product.BillPosition = 2
							pSku.TaxCode = detail.TaxCode
							pSku.FreeOfTax = 1
						}
						product.Skus[k] = pSku
					}
				}
				if len(product.Skus) > 0 {
					product.ProfitRate = minProfitRate
				}
				if totalStock == 0 {
					product.IsDisplay = 0
				}
				updateProducts = append(updateProducts, product)
			}

		}
	}
	//updateGoods := szbao.ProductToGoods(needUpdateProducts, GatherSupplyID)
	//err, updateProducts = szbao.CommodityAssembly(updateGoods, 0, 0, 0, GatherSupplyID, 1)
	if err != nil {
		return
	}
	for _, updateProduct := range updateProducts {
		//log.Log().Debug("szbao修改商品", zap.Any("id", updateProduct.ID))

		err = service2.UpdateProductForce(updateProduct)
		if err != nil {
			continue
		}
	}
	return
}

func (szbao *Szbao) InitProductsV2() (err error) {
	var allProducts []service2.ProductForUpdate
	if err = source.DB().Preload("Skus").Where("gather_supply_id = ?", GatherSupplyID).Where("md5 != '商品已删除'").Where("sn = ''").Find(&allProducts).Error; err != nil {
		return
	}

	var allSzbaoProducts []model.SzbaoGoodsV2
	if err = source.DB().Where("gather_supply_id = ?", GatherSupplyID).Find(&allSzbaoProducts).Error; err != nil {
		return
	}

	var allSzbaoProductsMap = make(map[uint]model.SzbaoGoodsV2)
	for _, dwdProduct := range allSzbaoProducts {
		allSzbaoProductsMap[dwdProduct.ID] = dwdProduct
	}

	var szbaoBrands interface{}
	if err, szbaoBrands = szbao.GetBrandList(); err != nil {
		return
	}

	var brandJsonData []byte
	if brandJsonData, err = json.Marshal(szbaoBrands); err != nil {
		return
	}

	var szbaoBrandStruct []model.SzbaoBrand
	if err = json.Unmarshal(brandJsonData, &szbaoBrandStruct); err != nil {
		return
	}

	var szbaoBrandMap = make(map[uint]model.SzbaoBrand)
	for _, szbaoB := range szbaoBrandStruct {
		szbaoBrandMap[szbaoB.ID] = szbaoB
	}

	var updateProducts []service2.ProductForUpdate
	for _, product := range allProducts {
		if product.Sn == "" {
			_, ok := allSzbaoProductsMap[product.SourceGoodsID]
			if !ok {
				if product.StatusLock == 0 {
					product.MD5 = "商品已删除"
					product.IsDisplay = 0
					product.StatusLock = 1
				}
				updateProducts = append(updateProducts, product)
				continue
			}
			if product.MD5 == "" || product.MD5 != allSzbaoProductsMap[product.SourceGoodsID].MD5 || product.IsDisplay != allSzbaoProductsMap[product.SourceGoodsID].State {
				var detail = allSzbaoProductsMap[product.SourceGoodsID]
				product.MD5 = detail.MD5
				if szbaoData.UpdateInfo.CurrentPrice == 1 {
					//是否更新供货价
					var intX uint64
					if szbaoData.Pricing.SupplySales == 1 {
						intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesGuide, 10, 32)
						product.Price = uint(detail.MinMarketPrice*100) * uint(intX) / 100
					} else if szbaoData.Pricing.SupplySales == 2 {
						if detail.MinZpTaxRate == 0 || szbaoData.Pricing.SupplySalesIsTax == 0 {
							intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesAgreement, 10, 32)
							product.Price = uint(detail.MinCurrentVipPrice*100) * uint(intX) / 100
						} else {
							intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesTax, 10, 32)
							product.Price = uint(detail.MinCurrentVipPrice*100) * uint(intX) / 100
						}

					} else if szbaoData.Pricing.SupplySales == 3 {
						intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesMarketing, 10, 32)
						if detail.MinRetailPrice > 0 {
							product.Price = uint(detail.MinRetailPrice*100) * uint(intX) / 100
						} else {
							product.Price = uint(detail.MinMarketPrice*100) * uint(intX) / 100
						}
					} else if szbaoData.Pricing.SupplyGuide == 4 {
						intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesFreight, 10, 32)
						if detail.MinFreightPrice > 0 {
							product.Price = uint(detail.MinFreightPrice*100) * uint(intX) / 100
						} else {
							product.Price = uint(detail.MinCurrentVipPrice*100) * uint(intX) / 100
						}
					} else {
						product.Price = uint(detail.MinCurrentVipPrice * 100)
					}

				}
				if szbaoData.UpdateInfo.CostPrice == 1 {
					//是否更新成本价
					var intXCost uint64
					if szbaoData.Pricing.SupplyCost == 1 {
						intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostGuide, 10, 32)
						product.CostPrice = uint(detail.MinMarketPrice*100) * uint(intXCost) / 100
					} else if szbaoData.Pricing.SupplyCost == 2 {
						intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostAgreement, 10, 32)
						product.CostPrice = uint(detail.MinCurrentVipPrice*100) * uint(intXCost) / 100
					} else if szbaoData.Pricing.SupplyCost == 3 {
						intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostMarketing, 10, 32)
						if detail.MinRetailPrice > 0 {
							product.CostPrice = uint(detail.MinRetailPrice*100) * uint(intXCost) / 100
						} else {
							product.CostPrice = uint(detail.MinMarketPrice*100) * uint(intXCost) / 100
						}
					} else if szbaoData.Pricing.SupplyCost == 4 {
						intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostFreight, 10, 32)
						if detail.MinFreightPrice > 0 {
							product.CostPrice = uint(detail.MinFreightPrice*100) * uint(intXCost) / 100
						} else {
							product.CostPrice = uint(detail.MinCurrentVipPrice*100) * uint(intXCost) / 100
						}
					} else {
						product.CostPrice = uint(detail.MinCurrentVipPrice * 100)
					}
				}
				if szbaoData.UpdateInfo.ActivityPrice == 1 {
					var intXActivity uint64
					if szbaoData.Pricing.SupplyActivity == 1 {
						intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityGuide, 10, 32)
						product.ActivityPrice = uint(detail.MinMarketPrice*100) * uint(intXActivity) / 100
					} else if szbaoData.Pricing.SupplyActivity == 2 {
						intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityAgreement, 10, 32)
						product.ActivityPrice = uint(detail.MinCurrentVipPrice*100) * uint(intXActivity) / 100
					} else if szbaoData.Pricing.SupplyActivity == 3 {
						intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityMarketing, 10, 32)
						if detail.MinRetailPrice > 0 {
							product.ActivityPrice = uint(detail.MinRetailPrice*100) * uint(intXActivity) / 100
						} else {
							product.ActivityPrice = uint(detail.MinMarketPrice*100) * uint(intXActivity) / 100
						}
					} else if szbaoData.Pricing.SupplyActivity == 4 {
						intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityFreight, 10, 32)
						if detail.MinFreightPrice > 0 {
							product.ActivityPrice = uint(detail.MinFreightPrice*100) * uint(intXActivity) / 100
						} else {
							product.ActivityPrice = uint(detail.MinCurrentVipPrice*100) * uint(intXActivity) / 100
						}
					} else {
						product.ActivityPrice = uint(detail.MinMarketPrice * 100)
					}

				}
				if szbaoData.UpdateInfo.GuidePrice == 1 {
					var intXGuide uint64
					if szbaoData.Pricing.SupplyGuide == 1 {
						intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideGuide, 10, 32)
						product.GuidePrice = uint(detail.MinMarketPrice*100) * uint(intXGuide) / 100
					} else if szbaoData.Pricing.SupplyGuide == 2 {
						intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideAgreement, 10, 32)
						product.GuidePrice = uint(detail.MinCurrentVipPrice*100) * uint(intXGuide) / 100
					} else if szbaoData.Pricing.SupplyGuide == 3 {
						intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideMarketing, 10, 32)
						if detail.MinRetailPrice > 0 {
							product.GuidePrice = uint(detail.MinRetailPrice*100) * uint(intXGuide) / 100
						} else {
							product.GuidePrice = uint(detail.MinMarketPrice*100) * uint(intXGuide) / 100
						}
					} else if szbaoData.Pricing.SupplyGuide == 4 {
						intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideFreight, 10, 32)
						if detail.MinFreightPrice > 0 {
							product.GuidePrice = uint(detail.MinFreightPrice*100) * uint(intXGuide) / 100
						} else {
							product.GuidePrice = uint(detail.MinCurrentVipPrice*100) * uint(intXGuide) / 100
						}
					} else {
						product.GuidePrice = uint(detail.MinMarketPrice * 100)
					}
				}
				if szbaoData.UpdateInfo.OriginalPrice == 1 {
					var intXAdvice uint64
					if szbaoData.Pricing.SupplyAdvice == 1 {
						intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceGuide, 10, 32)
						product.OriginPrice = uint(detail.MinMarketPrice*100) * uint(intXAdvice) / 100
					} else if szbaoData.Pricing.SupplyAdvice == 2 {
						intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceAgreement, 10, 32)
						product.OriginPrice = uint(detail.MinCurrentVipPrice*100) * uint(intXAdvice) / 100
					} else if szbaoData.Pricing.SupplyAdvice == 3 {
						intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceMarketing, 10, 32)
						if detail.MinRetailPrice > 0 {
							product.OriginPrice = uint(detail.MinRetailPrice*100) * uint(intXAdvice) / 100
						} else {
							product.OriginPrice = uint(detail.MinMarketPrice*100) * uint(intXAdvice) / 100
						}
					} else if szbaoData.Pricing.SupplyGuide == 4 {
						intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceFreight, 10, 32)
						if detail.MinFreightPrice > 0 {
							product.OriginPrice = uint(detail.MinFreightPrice*100) * uint(intXAdvice) / 100
						} else {
							product.OriginPrice = uint(detail.MinCurrentVipPrice*100) * uint(intXAdvice) / 100
						}
					} else {
						product.OriginPrice = uint(detail.MinMarketPrice * 100)
					}
				}

				//if szbaoData.Management.ProductPriceStatus == 1 {
				//	if product.Price < product.CostPrice*(szbaoData.Management.Products/100) {
				//		product.IsDisplay = 0
				//	}
				//} else if szbaoData.Management.ProductPriceStatus == 2 {
				//	if (product.Price-product.CostPrice)/product.CostPrice < szbaoData.Management.Profit/100 {
				//		product.IsDisplay = 0
				//	}
				//}

				// 分类
				if szbaoData.UpdateInfo.CateGory == 1 {
					szCategoryIds := []uint{detail.Category1ID, detail.Category2ID, detail.Category3ID}
					// 查询永源分类数据
					var szCategories []model.SzbaoCategory
					if err = source.DB().Where("`id` IN ?", szCategoryIds).Find(&szCategories).Error; err != nil {
						return
					}

					// 组装永源分类数据 map
					szCategoryMap := make(map[uint]model.SzbaoCategory)
					for _, szCategory := range szCategories {
						szCategoryMap[szCategory.ID] = szCategory
					}

					display := 1
					// 一级分类
					var category1 catemodel.Category
					if fSzCategory, fOk := szCategoryMap[detail.Category1ID]; fOk {
						category1.IsDisplay = &display
						category1.ParentID = 0
						category1.Level = 1
						category1.Name = fSzCategory.Name
						category1.Icon = fSzCategory.Img
						category1.Image = fSzCategory.Img
						if err = source.DB().Where("name=? and level=? and parent_id=?", category1.Name, category1.Level, category1.ParentID).FirstOrCreate(&category1).Error; err != nil {
							return
						}
						// 更新图标
						if category1.Icon == "" {
							if err = source.DB().Model(&category1).Update("icon", fSzCategory.Img).Error; err != nil {
								return
							}
						}
						if category1.Image == "" {
							if err = source.DB().Model(&category1).Update("image", fSzCategory.Img).Error; err != nil {
								return
							}
						}
					} else {
						category1.IsDisplay = &display
						category1.ParentID = 0
						category1.Level = 1
						category1.Name = "默认"
						source.DB().Where("name=? and level=? and parent_id=?", category1.Name, category1.Level, category1.ParentID).FirstOrCreate(&category1)
					}

					// 二级分类
					var category2 catemodel.Category
					if sSzCategory, sOk := szCategoryMap[detail.Category2ID]; sOk {
						category2.IsDisplay = &display
						category2.ParentID = category1.ID
						category2.Level = 2
						category2.Name = sSzCategory.Name
						category2.Icon = sSzCategory.Img
						category2.Image = sSzCategory.Img
						if err = source.DB().Where("name=? and level=? and parent_id=?", category2.Name, category2.Level, category2.ParentID).FirstOrCreate(&category2).Error; err != nil {
							return
						}
						// 更新图标
						if category2.Icon == "" {
							if err = source.DB().Model(&category2).Update("icon", sSzCategory.Img).Error; err != nil {
								return
							}
						}
						if category2.Image == "" {
							if err = source.DB().Model(&category2).Update("image", sSzCategory.Img).Error; err != nil {
								return
							}
						}
					} else {
						category2.IsDisplay = &display
						category2.ParentID = category1.ID
						category2.Level = 2
						category2.Name = "默认"
						source.DB().Where("name=? and level=? and parent_id=?", category2.Name, category2.Level, category2.ParentID).FirstOrCreate(&category2)
					}

					// 三级分类
					var category3 catemodel.Category
					if tSzCategory, tOk := szCategoryMap[detail.Category1ID]; tOk {
						category3.IsDisplay = &display
						category3.ParentID = category2.ID
						category3.Level = 3
						category3.Name = tSzCategory.Name
						category3.Icon = tSzCategory.Img
						category3.Image = tSzCategory.Img
						if err = source.DB().Where("name=? and level=? and parent_id=?", category3.Name, category3.Level, category3.ParentID).FirstOrCreate(&category3).Error; err != nil {
							return
						}
						// 更新图标
						if category3.Icon == "" {
							if err = source.DB().Model(&category3).Update("icon", tSzCategory.Img).Error; err != nil {
								return
							}
						}
						if category3.Image == "" {
							if err = source.DB().Model(&category3).Update("image", tSzCategory.Img).Error; err != nil {
								return
							}
						}
					} else {
						category3.IsDisplay = &display
						category3.ParentID = category2.ID
						category3.Level = 3
						category3.Name = "默认"
						source.DB().Where("name=? and level=? and parent_id=?", category3.Name, category3.Level, category3.ParentID).FirstOrCreate(&category3)
					}

					product.Category1ID = category1.ID
					product.Category2ID = category2.ID
					product.Category3ID = category3.ID

					// 品牌
					if szbaoBrand, szbok := szbaoBrandMap[detail.BrandId]; szbok {
						var brand catemodel.Brand
						// 根据名称查询品牌记录
						if err = source.DB().Where("`name` = ?", szbaoBrand.BrandName).First(&brand).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
							return
						}

						// 如果品牌不存在，则创建
						if brand.ID == 0 {
							err = nil
							brand.Name = szbaoBrand.BrandName
							brand.Logo = szbaoBrand.BrandImg
							brand.Source = int(GatherSupplyID)
							if err = source.DB().Create(&brand).Error; err != nil {
								return
							}
						}

						// 如果品牌存在，图片不存在，更新图片信息
						if brand.Logo == "" {
							brand.Logo = szbaoBrand.BrandImg
							if err = source.DB().Model(&brand).Update("logo", brand.Logo).Error; err != nil {
								return
							}
						}

						// 赋值品牌ID
						product.BrandID = brand.ID
					}
				}

				if szbaoData.UpdateInfo.BaseInfo == 1 {
					//是否更新基本信息
					product.Title = detail.Title
					product.Stock = uint(detail.Stock)
					if product.StatusLock == 0 {
						product.IsDisplay = detail.State
					}
					product.ImageUrl = detail.MainImg
					product.DetailImages = "<p>"
				}

				var gallery pmodel.Gallery
				var attr, attr2 pmodel.Attr
				var minProfitRate float64
				var totalStock int
				for skuKey, sku := range detail.SpecificationList {
					if skuKey == 0 {
						if szbaoData.UpdateInfo.BaseInfo == 1 {
							for _, img := range sku.Imgs {
								gallery = append(gallery, pmodel.GalleryItem{
									Src:  img,
									Type: 1,
								})
							}
							for _, detailImg := range sku.DetailImgs {
								product.DetailImages += "<img src=\"" + detailImg + "?x-oss-process=style/normal\">"
							}
						}

					}
					var localSku = service2.Sku{}
					for _, skuold := range product.Skus {
						if skuold.Sn == sku.Code {
							localSku = skuold
							break
						}
					}
					var options pmodel.Options
					var specInfoMap map[string]string
					if utils.IsJSON(sku.SpecInfo) {
						err = json.Unmarshal([]byte(sku.SpecInfo), &specInfoMap)
						if err != nil {
							continue
						}
						for SpecName, SpecItemName := range specInfoMap {
							var option pmodel.Option
							option.SpecName = SpecName
							option.SpecItemName = SpecItemName
							options = append(options, option)
						}
					} else {
						var option pmodel.Option
						option.SpecName = "默认"
						option.SpecItemName = sku.Name
						options = append(options, option)
					}
					localSku.Options = options
					localSku.Title = sku.Name
					localSku.ImageUrl = sku.ItemMainImg
					localSku.Weight = int(sku.Weight * 1000)

					//是否更新供货价
					var intX uint64
					if szbaoData.Pricing.SupplySales == 1 {
						intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesGuide, 10, 32)
						localSku.Price = uint(sku.MarketPrice*100) * uint(intX) / 100
					} else if szbaoData.Pricing.SupplySales == 2 {
						if sku.ZpTaxRate == 0 || szbaoData.Pricing.SupplySalesIsTax == 0 {
							intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesAgreement, 10, 32)
							localSku.Price = uint(sku.CurrVipPrice*100) * uint(intX) / 100
						} else {
							intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesTax, 10, 32)
							localSku.Price = uint(sku.CurrVipPrice*100) * uint(intX) / 100
						}

					} else if szbaoData.Pricing.SupplySales == 3 {
						intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesMarketing, 10, 32)
						if sku.RetailPrice > 0 {
							localSku.Price = uint(sku.RetailPrice*100) * uint(intX) / 100
						} else {
							localSku.Price = uint(sku.MarketPrice*100) * uint(intX) / 100
						}
					} else if szbaoData.Pricing.SupplyGuide == 4 {
						intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesFreight, 10, 32)
						if sku.FreightPrice > 0 {
							localSku.Price = uint(sku.FreightPrice*100) * uint(intX) / 100
						} else {
							localSku.Price = uint(sku.CurrVipPrice*100) * uint(intX) / 100
						}
					} else {
						localSku.Price = uint(sku.CurrVipPrice * 100)
					}

					//是否更新成本价
					var intXCost uint64
					if szbaoData.Pricing.SupplyCost == 1 {
						intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostGuide, 10, 32)
						localSku.CostPrice = uint(sku.MarketPrice*100) * uint(intXCost) / 100
					} else if szbaoData.Pricing.SupplyCost == 2 {
						intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostAgreement, 10, 32)
						localSku.CostPrice = uint(sku.CurrVipPrice*100) * uint(intXCost) / 100
					} else if szbaoData.Pricing.SupplyCost == 3 {
						intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostMarketing, 10, 32)
						if sku.RetailPrice > 0 {
							localSku.CostPrice = uint(sku.RetailPrice*100) * uint(intXCost) / 100
						} else {
							localSku.CostPrice = uint(sku.MarketPrice*100) * uint(intXCost) / 100
						}
					} else if szbaoData.Pricing.SupplyCost == 4 {
						intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostFreight, 10, 32)
						if sku.FreightPrice > 0 {
							localSku.CostPrice = uint(sku.FreightPrice*100) * uint(intXCost) / 100
						} else {
							localSku.CostPrice = uint(sku.CurrVipPrice*100) * uint(intXCost) / 100
						}
					} else {
						localSku.CostPrice = uint(sku.CurrVipPrice * 100)
					}

					var intXActivity uint64
					if szbaoData.Pricing.SupplyActivity == 1 {
						intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityGuide, 10, 32)
						localSku.ActivityPrice = uint(sku.MarketPrice*100) * uint(intXActivity) / 100
					} else if szbaoData.Pricing.SupplyActivity == 2 {
						intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityAgreement, 10, 32)
						localSku.ActivityPrice = uint(sku.CurrVipPrice*100) * uint(intXActivity) / 100
					} else if szbaoData.Pricing.SupplyActivity == 3 {
						intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityMarketing, 10, 32)
						if sku.RetailPrice > 0 {
							localSku.ActivityPrice = uint(sku.RetailPrice*100) * uint(intXActivity) / 100
						} else {
							localSku.ActivityPrice = uint(sku.MarketPrice*100) * uint(intXActivity) / 100
						}
					} else if szbaoData.Pricing.SupplyActivity == 4 {
						intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityFreight, 10, 32)
						if sku.FreightPrice > 0 {
							localSku.ActivityPrice = uint(sku.FreightPrice*100) * uint(intXActivity) / 100
						} else {
							localSku.ActivityPrice = uint(sku.CurrVipPrice*100) * uint(intXActivity) / 100
						}
					} else {
						localSku.ActivityPrice = uint(sku.MarketPrice * 100)
					}

					var intXGuide uint64
					if szbaoData.Pricing.SupplyGuide == 1 {
						intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideGuide, 10, 32)
						localSku.GuidePrice = uint(sku.MarketPrice*100) * uint(intXGuide) / 100
					} else if szbaoData.Pricing.SupplyGuide == 2 {
						intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideAgreement, 10, 32)
						localSku.GuidePrice = uint(sku.CurrVipPrice*100) * uint(intXGuide) / 100
					} else if szbaoData.Pricing.SupplyGuide == 3 {
						intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideMarketing, 10, 32)
						if sku.RetailPrice > 0 {
							localSku.GuidePrice = uint(sku.RetailPrice*100) * uint(intXGuide) / 100
						} else {
							localSku.GuidePrice = uint(sku.MarketPrice*100) * uint(intXGuide) / 100
						}
					} else if szbaoData.Pricing.SupplyGuide == 4 {
						intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideFreight, 10, 32)
						if sku.FreightPrice > 0 {
							localSku.GuidePrice = uint(sku.FreightPrice*100) * uint(intXGuide) / 100
						} else {
							localSku.GuidePrice = uint(sku.CurrVipPrice*100) * uint(intXGuide) / 100
						}
					} else {
						localSku.GuidePrice = uint(sku.MarketPrice * 100)
					}
					var intXAdvice uint64
					if szbaoData.Pricing.SupplyAdvice == 1 {
						intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceGuide, 10, 32)
						localSku.OriginPrice = uint(sku.MarketPrice*100) * uint(intXAdvice) / 100
					} else if szbaoData.Pricing.SupplyAdvice == 2 {
						intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceAgreement, 10, 32)
						localSku.OriginPrice = uint(sku.CurrVipPrice*100) * uint(intXAdvice) / 100
					} else if szbaoData.Pricing.SupplyAdvice == 3 {
						intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceMarketing, 10, 32)
						if sku.RetailPrice > 0 {
							localSku.OriginPrice = uint(sku.RetailPrice*100) * uint(intXAdvice) / 100
						} else {
							localSku.OriginPrice = uint(sku.MarketPrice*100) * uint(intXAdvice) / 100
						}
					} else if szbaoData.Pricing.SupplyGuide == 4 {
						intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceFreight, 10, 32)
						if sku.FreightPrice > 0 {
							localSku.OriginPrice = uint(sku.FreightPrice*100) * uint(intXAdvice) / 100
						} else {
							localSku.OriginPrice = uint(sku.CurrVipPrice*100) * uint(intXAdvice) / 100
						}
					} else {
						localSku.OriginPrice = uint(sku.MarketPrice * 100)
					}

					localSku.Stock = sku.StockNum
					totalStock += localSku.Stock
					localSku.Sn = sku.Code
					if sku.TaxCode != "" {
						product.BillPosition = 2
						localSku.TaxCode = sku.TaxCode
						localSku.FreeOfTax = 1
					}
					attr = pmodel.Attr{
						Name:  "重量",
						Value: strconv.Itoa(int(sku.Weight)),
					}
					attr2 = pmodel.Attr{
						Name:  "体积",
						Value: strconv.Itoa(int(sku.Volume)),
					}
					if localSku.GuidePrice > 0 {
						localSku.ProfitRate = utils.ExecProfitRate(localSku.GuidePrice, localSku.Price)
					} else {
						localSku.ProfitRate = 0
					}
					if minProfitRate == 0 || localSku.ProfitRate <= minProfitRate {
						minProfitRate = localSku.ProfitRate
					}
					product.Skus = append(product.Skus, localSku)
				}
				if szbaoData.UpdateInfo.BaseInfo == 1 {
					product.Gallery = gallery

					product.DetailImages += "</p>"

				}
				product.Attrs = pmodel.Attrs{}
				product.Attrs = append(product.Attrs, attr)
				product.Attrs = append(product.Attrs, attr2)

				if len(product.Skus) > 0 {
					product.ProfitRate = minProfitRate
				}
				if totalStock == 0 {
					product.IsDisplay = 0
				}
				updateProducts = append(updateProducts, product)
			}
		}
	}
	//updateGoods := szbao.ProductToGoods(needUpdateProducts, GatherSupplyID)
	//err, updateProducts = szbao.CommodityAssembly(updateGoods, 0, 0, 0, GatherSupplyID, 1)
	if err != nil {
		return
	}
	for _, updateProduct := range updateProducts {
		//log.Log().Debug("szbao修改商品", zap.Any("id", updateProduct.ID))

		err = service2.UpdateProduct(updateProduct)
		if err != nil {
			continue
		}
	}
	return
}

func (szbao *Szbao) MergeProducts() (err error) {
	var allProducts []service2.ProductForUpdate
	if err = source.DB().Preload("Skus").Where("gather_supply_id = ? and sn != '' and source_goods_id_string = 'canmerge'", GatherSupplyID).Where("md5 != '商品已删除'").Find(&allProducts).Error; err != nil {
		return
	}

	var allSzbaoProducts []model.SzbaoGoodsV2
	if err = source.DB().Where("gather_supply_id = ?", GatherSupplyID).Find(&allSzbaoProducts).Error; err != nil {
		return
	}
	if len(allSzbaoProducts) == 0 {
		return
	}
	var productGroup = make(map[uint][]service2.ProductForUpdate)
	for _, product := range allProducts {
		productGroup[product.SourceGoodsID] = append(productGroup[product.SourceGoodsID], product)
	}
	var allSzbaoProductsMap = make(map[uint]model.SzbaoGoodsV2)
	for _, dwdProduct := range allSzbaoProducts {
		allSzbaoProductsMap[dwdProduct.ID] = dwdProduct
	}

	var deleteProductIds []uint
	var updateProducts []service2.ProductForUpdate
	for spuId, productG := range productGroup {
		for k, product := range productG {
			if len(productG) > 1 {
				fmt.Println(1)
			}
			if k == 0 {
				productMain := productG[0]
				if _, ok := allSzbaoProductsMap[spuId]; !ok {
					deleteProductIds = append(deleteProductIds, product.ID)
					continue
				}
				productMain.ImageUrl = allSzbaoProductsMap[spuId].MainImg
				productMain.Title = allSzbaoProductsMap[spuId].Title
				productMain.Sn = ""
				productMain.SourceGoodsIDString = ""
				productMain.Skus = []service2.Sku{}
				productMain.MD5 = allSzbaoProductsMap[spuId].MD5
				productMain.DetailImages = "<p>"

				var gallery pmodel.Gallery
				for skuKey, sku := range allSzbaoProductsMap[spuId].SpecificationList {
					if skuKey == 0 {
						for _, img := range sku.Imgs {
							gallery = append(gallery, pmodel.GalleryItem{
								Src:  img,
								Type: 1,
							})
						}
						for _, detailImg := range sku.DetailImgs {
							productMain.DetailImages += "<img src=\"" + detailImg + "?x-oss-process=style/normal\">"
						}
					}
					var localSku = service2.Sku{}
					var options pmodel.Options
					var specInfoMap map[string]string
					if utils.IsJSON(sku.SpecInfo) {
						err = json.Unmarshal([]byte(sku.SpecInfo), &specInfoMap)
						if err != nil {
							continue
						}
						for SpecName, SpecItemName := range specInfoMap {
							var option pmodel.Option
							option.SpecName = SpecName
							option.SpecItemName = SpecItemName
							options = append(options, option)
						}
					} else {
						var option pmodel.Option
						option.SpecName = "默认"
						option.SpecItemName = sku.Name
						options = append(options, option)
					}

					localSku.Title = sku.Name
					localSku.ImageUrl = sku.ItemMainImg
					localSku.Options = options
					localSku.Weight = 0
					//是否更新供货价
					var intX uint64
					if szbaoData.Pricing.SupplySales == 1 {
						intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesGuide, 10, 32)
						localSku.Price = uint(sku.MarketPrice*100) * uint(intX) / 100
					} else if szbaoData.Pricing.SupplySales == 2 {
						if sku.ZpTaxRate == 0 || szbaoData.Pricing.SupplySalesIsTax == 0 {
							intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesAgreement, 10, 32)
							localSku.Price = uint(sku.CurrVipPrice*100) * uint(intX) / 100
						} else {
							intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesTax, 10, 32)
							localSku.Price = uint(sku.CurrVipPrice*100) * uint(intX) / 100
						}

					} else if szbaoData.Pricing.SupplySales == 3 {
						intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesMarketing, 10, 32)
						if sku.RetailPrice > 0 {
							localSku.Price = uint(sku.RetailPrice*100) * uint(intX) / 100
						} else {
							localSku.Price = uint(sku.MarketPrice*100) * uint(intX) / 100
						}
					} else if szbaoData.Pricing.SupplyGuide == 4 {
						intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesFreight, 10, 32)
						if sku.FreightPrice > 0 {
							localSku.Price = uint(sku.FreightPrice*100) * uint(intX) / 100
						} else {
							localSku.Price = uint(sku.CurrVipPrice*100) * uint(intX) / 100
						}
					} else {
						localSku.Price = uint(sku.CurrVipPrice * 100)
					}

					//是否更新成本价
					var intXCost uint64
					if szbaoData.Pricing.SupplyCost == 1 {
						intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostGuide, 10, 32)
						localSku.CostPrice = uint(sku.MarketPrice*100) * uint(intXCost) / 100
					} else if szbaoData.Pricing.SupplyCost == 2 {
						intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostAgreement, 10, 32)
						localSku.CostPrice = uint(sku.CurrVipPrice*100) * uint(intXCost) / 100
					} else if szbaoData.Pricing.SupplyCost == 3 {
						intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostMarketing, 10, 32)
						if sku.RetailPrice > 0 {
							localSku.CostPrice = uint(sku.RetailPrice*100) * uint(intXCost) / 100
						} else {
							localSku.CostPrice = uint(sku.MarketPrice*100) * uint(intXCost) / 100
						}
					} else if szbaoData.Pricing.SupplyCost == 4 {
						intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostFreight, 10, 32)
						if sku.FreightPrice > 0 {
							localSku.CostPrice = uint(sku.FreightPrice*100) * uint(intXCost) / 100
						} else {
							localSku.CostPrice = uint(sku.CurrVipPrice*100) * uint(intXCost) / 100
						}
					} else {
						localSku.CostPrice = uint(sku.CurrVipPrice * 100)
					}

					var intXActivity uint64
					if szbaoData.Pricing.SupplyActivity == 1 {
						intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityGuide, 10, 32)
						localSku.ActivityPrice = uint(sku.MarketPrice*100) * uint(intXActivity) / 100
					} else if szbaoData.Pricing.SupplyActivity == 2 {
						intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityAgreement, 10, 32)
						localSku.ActivityPrice = uint(sku.CurrVipPrice*100) * uint(intXActivity) / 100
					} else if szbaoData.Pricing.SupplyActivity == 3 {
						intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityMarketing, 10, 32)
						if sku.RetailPrice > 0 {
							localSku.ActivityPrice = uint(sku.RetailPrice*100) * uint(intXActivity) / 100
						} else {
							localSku.ActivityPrice = uint(sku.MarketPrice*100) * uint(intXActivity) / 100
						}
					} else if szbaoData.Pricing.SupplyActivity == 4 {
						intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityFreight, 10, 32)
						if sku.FreightPrice > 0 {
							localSku.ActivityPrice = uint(sku.FreightPrice*100) * uint(intXActivity) / 100
						} else {
							localSku.ActivityPrice = uint(sku.CurrVipPrice*100) * uint(intXActivity) / 100
						}
					} else {
						localSku.ActivityPrice = uint(sku.MarketPrice * 100)
					}

					var intXGuide uint64
					if szbaoData.Pricing.SupplyGuide == 1 {
						intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideGuide, 10, 32)
						localSku.GuidePrice = uint(sku.MarketPrice*100) * uint(intXGuide) / 100
					} else if szbaoData.Pricing.SupplyGuide == 2 {
						intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideAgreement, 10, 32)
						localSku.GuidePrice = uint(sku.CurrVipPrice*100) * uint(intXGuide) / 100
					} else if szbaoData.Pricing.SupplyGuide == 3 {
						intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideMarketing, 10, 32)
						if sku.RetailPrice > 0 {
							localSku.GuidePrice = uint(sku.RetailPrice*100) * uint(intXGuide) / 100
						} else {
							localSku.GuidePrice = uint(sku.MarketPrice*100) * uint(intXGuide) / 100
						}
					} else if szbaoData.Pricing.SupplyGuide == 4 {
						intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideFreight, 10, 32)
						if sku.FreightPrice > 0 {
							localSku.GuidePrice = uint(sku.FreightPrice*100) * uint(intXGuide) / 100
						} else {
							localSku.GuidePrice = uint(sku.CurrVipPrice*100) * uint(intXGuide) / 100
						}
					} else {
						localSku.GuidePrice = uint(sku.MarketPrice * 100)
					}
					var intXAdvice uint64
					if szbaoData.Pricing.SupplyAdvice == 1 {
						intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceGuide, 10, 32)
						localSku.OriginPrice = uint(sku.MarketPrice*100) * uint(intXAdvice) / 100
					} else if szbaoData.Pricing.SupplyAdvice == 2 {
						intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceAgreement, 10, 32)
						localSku.OriginPrice = uint(sku.CurrVipPrice*100) * uint(intXAdvice) / 100
					} else if szbaoData.Pricing.SupplyAdvice == 3 {
						intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceMarketing, 10, 32)
						if sku.RetailPrice > 0 {
							localSku.OriginPrice = uint(sku.RetailPrice*100) * uint(intXAdvice) / 100
						} else {
							localSku.OriginPrice = uint(sku.MarketPrice*100) * uint(intXAdvice) / 100
						}
					} else if szbaoData.Pricing.SupplyGuide == 4 {
						intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceFreight, 10, 32)
						if sku.FreightPrice > 0 {
							localSku.OriginPrice = uint(sku.FreightPrice*100) * uint(intXAdvice) / 100
						} else {
							localSku.OriginPrice = uint(sku.CurrVipPrice*100) * uint(intXAdvice) / 100
						}
					} else {
						localSku.OriginPrice = uint(sku.MarketPrice * 100)
					}

					localSku.Stock = sku.StockNum
					localSku.Sn = sku.Code
					if sku.TaxCode != "" {
						productMain.BillPosition = 2
						localSku.TaxCode = sku.TaxCode
						localSku.FreeOfTax = 1
					}

					productMain.Skus = append(productMain.Skus, localSku)
				}
				productMain.Gallery = gallery

				productMain.DetailImages += "</p>"
				updateProducts = append(updateProducts, productMain)
			} else {
				deleteProductIds = append(deleteProductIds, product.ID)
			}
		}
	}
	for _, updateProduct := range updateProducts {

		err = service2.UpdateProductForce(updateProduct)
		if err != nil {
			continue
		}
	}
	err = service2.DeleteProductByIds(request2.IdsReq{Ids: deleteProductIds}, 0, "0.0.0.0")
	return
}

func (*Szbao) GetGoods(info request.GetGoodsSearch) (err error, data interface{}, total int64, serverRatio int) {
	limit := info.Limit
	offset := info.Limit * (info.Page - 1)
	var szbaoGoods []model.SzbaoGoodsV2
	db := source.DB().Model(&model.SzbaoGoodsV2{})
	db.Where("gather_supply_id = ?", info.GatherSupplyID)
	if info.IsFreeShipping == 1 {
		db.Where("`is_free_delivery` = 1")
	}
	if info.IsDisplay != nil {
		db.Where("`state` = ?", &info.IsDisplay)
	}
	if info.IsFreeShipping == 2 {
		db.Where("`is_free_delivery` = 0")
	}
	if info.SearchWords != "" {
		db.Where("`title` like ?", "%"+info.SearchWords+"%")
	}
	if info.BrandID > 0 {
		db.Where("`brandId` = ?", info.BrandID)
	}
	if info.IsImport > 0 {
		var jushuitanProductIds []string
		err = source.DB().Model(&pmodel.Product{}).Where("gather_supply_id = ?", GatherSupplyID).Where("deleted_at is NULL").Pluck("source_goods_id", &jushuitanProductIds).Error
		if err != nil {
			return
		}

		if info.IsImport == 1 {
			db.Where("`id` in ?", jushuitanProductIds)

		} else if info.IsImport == 2 {
			if len(jushuitanProductIds) > 0 {
				db.Where("`id` not in ?", jushuitanProductIds)
			}
		}

	}
	if info.Category1ID > 0 {
		db.Where("`category_1_id` = ?", info.Category1ID)
	}
	if info.Category2ID > 0 {
		db.Where("`category_2_id` = ?", info.Category2ID)
	}
	if info.Category3ID > 0 {
		db.Where("`category_3_id` = ?", info.Category3ID)
	}
	if info.IsFreeTax == 1 {
		db.Where("`min_zp_tax_rate` = 0")
	}
	if info.IsFreeTax == 2 {
		db.Where("`min_zp_tax_rate` > 0")
	}
	if info.RangeType != "" {
		if info.RangeType == "guide_price" {
			db.Where("`max_market_price` >= ?", info.RangeForm)
			db.Where("`min_market_price` <= ?", info.RangeTo)
		}
		if info.RangeType == "agreement_price" {
			db.Where("`max_current_vip_price` >= ?", info.RangeForm)
			db.Where("`min_current_vip_price` <= ?", info.RangeTo)
		}

	}

	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&szbaoGoods).Error

	var szbao = Szbao{}
	data = szbao.ProductToGoods(szbaoGoods, info.GatherSupplyID)
	return
}

func (*Szbao) ProductToGoods(data []model.SzbaoGoodsV2, gatherID uint) (list []publicModel.Goods) {

	for _, v := range data {
		var isImport uint
		var supplyGoods response.Product
		err := source.DB().Where("source_goods_id = ? ", v.ID).Where("gather_supply_id = ?", gatherID).First(&supplyGoods).Error
		if err == nil {
			isImport = 1
		}
		var categoryIdsString []string
		categoryIdsString = append(categoryIdsString, strconv.Itoa(int(v.Category1ID)))
		categoryIdsString = append(categoryIdsString, strconv.Itoa(int(v.Category2ID)))
		categoryIdsString = append(categoryIdsString, strconv.Itoa(int(v.Category3ID)))
		var categorys []model.SzbaoCategory
		err = source.DB().Where("`id` in ?", categoryIdsString).Order("sup_id asc").Find(&categorys).Error
		if err != nil {
			//log.Log().Info("商品格式转换时未找到分类", zap.Any("info", v))
			return
		}
		var thirdCategoryName []string
		for _, c := range categorys {
			thirdCategoryName = append(thirdCategoryName, c.Name)
		}
		var rate float64
		for _, sku := range v.SpecificationList {
			if sku.MarketPrice > sku.CurrVipPrice {
				skuRate := service2.Decimal((float64(sku.MarketPrice) - float64(sku.CurrVipPrice)) / float64(sku.MarketPrice) * 100)
				if skuRate > rate {
					rate = skuRate
				}
			}
		}

		var item publicModel.Goods
		item = publicModel.Goods{
			GatherSupplyID:    gatherID,
			ThirdCategoryName: strings.Join(thirdCategoryName, ","),
			MarketPrice:       uint(v.MinMarketPrice * 100),
			FreightPrice:      uint(v.MinFreightPrice * 100),
			ID:                int(v.ID),
			ProductID:         int(v.ID),

			Cover:          v.MainImg,
			Status:         v.State,
			IsFreeShipping: v.IsFreeDelivery,
			Stock:          uint(v.Stock),
			Title:          v.Title,
			CategoryIds:    categoryIdsString,
			CostPrice:      uint(v.MinCurrentVipPrice * 100),
			AgreementPrice: uint(v.MinCurrentVipPrice * 100),
			GuidePrice:     uint(v.MinMarketPrice * 100),
			Rate:           rate,
			IsImport:       isImport,
			ThirdBrandName: v.BrandName,
		}
		if item.MarketPrice < item.AgreementPrice {
			item.Rate = 0
		}
		list = append(list, item)
	}
	return
}
func (*Szbao) ImportGoodsRun(info request.GetGoodsSearch) (err error, data interface{}) {
	var szbao = Szbao{}
	var szbaoGoods []model.SzbaoGoodsV2
	db := source.DB().Model(&model.SzbaoGoodsV2{})
	db.Where("gather_supply_id = ?", info.GatherSupplyID)
	if info.IsFreeShipping == 1 {
		db.Where("`is_free_delivery` = 1")
	}
	if info.IsDisplay != nil {
		db.Where("`state` = ?", &info.IsDisplay)
	}
	if info.IsFreeShipping == 2 {
		db.Where("`is_free_delivery` = 0")
	}
	if info.SearchWords != "" {
		db.Where("`title` like ?", "%"+info.SearchWords+"%")
	}
	if info.BrandID > 0 {
		db.Where("`brandId` = ?", info.BrandID)
	}
	if info.IsImport > 0 {
		var jushuitanProductIds []string
		err = source.DB().Model(&pmodel.Product{}).Where("gather_supply_id = ?", GatherSupplyID).Where("deleted_at is NULL").Pluck("source_goods_id", &jushuitanProductIds).Error
		if err != nil {
			return
		}

		if info.IsImport == 1 {
			db.Where("`id` in ?", jushuitanProductIds)

		} else if info.IsImport == 2 {
			if len(jushuitanProductIds) > 0 {
				db.Where("`id` not in ?", jushuitanProductIds)
			}
		}

	}
	if info.Category1ID > 0 {
		db.Where("`category_1_id` = ?", info.Category1ID)
	}
	if info.Category2ID > 0 {
		db.Where("`category_2_id` = ?", info.Category2ID)
	}
	if info.Category3ID > 0 {
		db.Where("`category_3_id` = ?", info.Category3ID)
	}
	if info.IsFreeTax == 1 {
		db.Where("`min_zp_tax_rate` = 0")
	}
	if info.IsFreeTax == 2 {
		db.Where("`min_zp_tax_rate` > 0")
	}
	if info.RangeType != "" {
		if info.RangeType == "guide_price" {
			db.Where("`max_market_price` >= ?", info.RangeForm)
			db.Where("`min_market_price` <= ?", info.RangeTo)
		}
		if info.RangeType == "agreement_price" {
			db.Where("`max_current_vip_price` >= ?", info.RangeForm)
			db.Where("`min_current_vip_price` <= ?", info.RangeTo)
		}

	}
	var total int64
	err = db.Count(&total).Error
	searchText, err := json.Marshal(info)

	data = szbao.ProductToGoods(szbaoGoods, info.GatherSupplyID)
	var count = float64(total)
	var limit = float64(50)
	var forCount = count / limit
	var counts = int(math.Ceil(forCount))
	fmt.Println("获取回来总数：", count, "计算总页数：", counts, info.Limit, info.Page)

	orderPN := GetOrderNo()
	goodsRecord := publicModel.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: int(count),
		Status:            1,
		SearchCriteria:    string(searchText),
	}

	source.DB().Omit("goods_arr").Create(&goodsRecord)
	//var wg sync.WaitGroup
	for i := 1; i <= counts; i++ {
		//wg.Add(1)
		if i >= 50 {
			break
		}
		if i%20 == 0 {
			time.Sleep(time.Second * 1)
		}
		err = szbao.RunGoodsConcurrent(nil, info, db, i, orderPN)
		if err != nil {
			err = goods.SetImportRecordFailed(orderPN, err.Error())
			return
		}
	}

	//wg.Wait()
	err = SetImportRecordCompletion(orderPN)
	if err != nil {
		fmt.Println("变更导入记录状态错误", err)

	}
	fmt.Println("导入供应链商品全部完成")
	return

}

func (*Szbao) RunGoodsConcurrent(wg *sync.WaitGroup, info request.GetGoodsSearch, db *gorm.DB, i int, orderPN string) (err error) {

	//defer wg.Done()
	var response []model.SzbaoGoodsV2
	err = db.Limit(50).Offset((i - 1) * 50).Find(&response).Error
	if err != nil {
		return
	}
	if len(response) > 0 {

		cateList := strings.Split(info.Categorys, ",")
		var cateId1, cateId2, cateId3 int
		if len(cateList) >= 2 {
			cateId1, err = strconv.Atoi(cateList[0])
			cateId2, err = strconv.Atoi(cateList[1])
		}
		brandId := info.BrandID
		var ProductItem []model.SzbaoGoodsV2
		var Item []publicModel.Goods
		ProductItem = response

		if len(ProductItem) <= 0 {
			fmt.Println("没有选择可导入的数据")

			return
		}
		var szbao = Szbao{}
		Item = szbao.ProductToGoods(ProductItem, info.GatherSupplyID)
		idsArr := GetIdArr(Item)
		var resultArr []int
		err = source.DB().Model(pmodel.Product{}).Where("gather_supply_id = ?", info.GatherSupplyID).Where("source_goods_id in ?", idsArr).Pluck("source_goods_id", &resultArr).Error
		if err != nil {
			return
		}
		difference := collection.Collect(idsArr).Diff(resultArr).ToIntArray()
		fmt.Println("查询到的导入数据：", idsArr)
		fmt.Println("已经存在的数据：", resultArr)
		fmt.Println("目前可以导入的数据：", difference)

		err = SetImportRepeat(orderPN, len(idsArr)-len(difference))

		if len(difference) <= 0 {
			fmt.Println("没有可以导入的数据")
			err = source.DB().Model(publicModel.SupplyGoodsImportRecord{}).Where("batch=?", orderPN).Update("status", 2).Error

			err = errors.New("当前数据已经存在，请导入新数据")
			return
		}
		var goodsList []publicModel.Goods

		for _, v := range difference {

			for _, item := range Item {
				if item.ID == v {
					goodsList = append(goodsList, item)
				}
			}

		}

		var listGoods []*pmodel.Product
		var recordErrors []publicModel.SupplyGoodsImportRecordErrors
		err, listGoods, recordErrors = szbao.CommodityAssemblyNew(goodsList, cateId1, cateId2, cateId3, brandId, info.GatherSupplyID, 0)
		if err != nil {
			return
		}
		if len(listGoods) > 0 {
			err = service.FinalProcessing(listGoods, orderPN)
			if err != nil {
				return
			}
		}
		if len(recordErrors) > 0 {
			err = service.FinalProcessingError(recordErrors, orderPN)
			if err != nil {
				return
			}

		}
	} else {
		fmt.Println("获取失败的：", i, info.Limit, info.Source)
	}

	return
}

// 商品组装
func (*Szbao) CommodityAssembly(list []publicModel.Goods, cateId1, cateId2, cateId3 int, gatherSupplyID uint, isUpdate int) (err error, listGoods []*pmodel.Product, recordErrors []publicModel.SupplyGoodsImportRecordErrors) {

	//idArr := GetIdArrString(list)
	//listArr := SplitArrayString(idArr, 20)
	//var detailData = make(map[uint]model.SzbaoGoods)
	//for index, item := range listArr {
	//	fmt.Println("循环分割id第", index, "次:", item)
	//	//stringIds := service.GetArrIds(item)
	//	fmt.Println("返回ids", item)
	//	var data map[uint]model.SzbaoGoods
	//	var szbao = Szbao{}
	//	err, data = szbao.BatchGetGoodsDetails(item, isUpdate)
	//	if err != nil {
	//		return
	//	}
	//	for detailIndex, detailItem := range data {
	//		detailData[detailIndex] = detailItem
	//
	//	}
	//}
	//
	////_, detailData := BatchGetGoodsDetails(ids) //批量获取详情
	//
	//for _, elem := range list {
	//	detail, ok := detailData[uint(elem.ID)]
	//	if !ok && isUpdate == 0 {
	//		recordErrors = append(recordErrors, publicModel.SupplyGoodsImportRecordErrors{Error: err.Error(), SourceId: uint(elem.ID), Title: elem.Title})
	//		continue
	//
	//	}
	//	goods := new(pmodel.Product)
	//
	//	if isUpdate == 1 {
	//		goods.ID = uint(elem.ProductID)
	//	}
	//	goods.Title = elem.Title
	//	goods.GuidePrice = elem.MarketPrice
	//	var intXAdvice uint64
	//	if szbaoData.Pricing.SupplyAdvice == 1 {
	//		intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceGuide, 10, 32)
	//		goods.OriginPrice = detail.MarketPrice * uint(intXAdvice) / 100
	//	} else if szbaoData.Pricing.SupplyAdvice == 2 {
	//		intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceAgreement, 10, 32)
	//		goods.OriginPrice = detail.CurrVipPrice * uint(intXAdvice) / 100
	//	} else {
	//		goods.OriginPrice = detail.MarketPrice
	//	}
	//	var intX uint64
	//	if szbaoData.Pricing.SupplySales == 1 {
	//		intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesGuide, 10, 32)
	//		goods.Price = detail.MarketPrice * uint(intX) / 100
	//	} else if szbaoData.Pricing.SupplySales == 2 {
	//		intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesAgreement, 10, 32)
	//		goods.Price = detail.CurrVipPrice * uint(intX) / 100
	//	} else {
	//		goods.Price = detail.CurrVipPrice
	//	}
	//	goods.CostPrice = elem.CostPrice
	//	goods.ActivityPrice = elem.ActivityPrice
	//	goods.Stock = elem.Stock
	//	goods.IsDisplay = elem.Status
	//	goods.ImageUrl = elem.Cover + "?x-oss-process=style/normal"
	//	goods.Unit = elem.Unit
	//	goods.Sn = detail.Code
	//	goods.Source = common.SZBAO_SOURCE
	//	goods.SourceGoodsID = uint(elem.ID)
	//	goods.SourceGoodsIDString = detail.Code
	//	goods.MD5 = detail.MD5
	//	if cateId1 == 0 && cateId2 == 0 {
	//		cateList := strings.Split(elem.ThirdCategoryName, ",")
	//		var cate1, cate2, cate3 catemodel.Category
	//		display := 1
	//		if len(cateList) > 0 && cateList[0] != "" {
	//
	//			cate1.IsDisplay = &display
	//			cate1.ParentID = 0
	//			cate1.Level = 1
	//			cate1.Name = cateList[0]
	//			source.DB().Where("name=? and level=? and parent_id=?", cateList[0], 1, 0).FirstOrCreate(&cate1)
	//		} else {
	//			cate2.IsDisplay = &display
	//			cate2.ParentID = 0
	//			cate2.Level = 1
	//			cate2.Name = "默认"
	//			source.DB().Where("name=? and level=? and parent_id=?", cate1.Name, cate1.Level, cate1.ParentID).FirstOrCreate(&cate1)
	//
	//		}
	//
	//		if len(cateList) > 1 && cateList[1] != "" {
	//			cate2.IsDisplay = &display
	//			cate2.ParentID = cate1.ID
	//			cate2.Level = 2
	//			cate2.Name = cateList[1]
	//			source.DB().Where("name=? and level=? and parent_id=?", cateList[1], 2, cate1.ID).FirstOrCreate(&cate2)
	//		} else {
	//			cate2.IsDisplay = &display
	//			cate2.ParentID = cate1.ID
	//			cate2.Level = 2
	//			cate2.Name = "默认"
	//			source.DB().Where("name=? and level=? and parent_id=?", cate2.Name, cate2.Level, cate2.ParentID).FirstOrCreate(&cate2)
	//
	//		}
	//
	//		if len(cateList) > 2 && cateList[2] != "" {
	//			cate3.IsDisplay = &display
	//			cate3.ParentID = cate2.ID
	//			cate3.Level = 3
	//			cate3.Name = cateList[1]
	//			source.DB().Where("name=? and level=? and parent_id=?", cateList[1], 3, cate2.ID).FirstOrCreate(&cate3)
	//		} else {
	//			cate3.IsDisplay = &display
	//			cate3.ParentID = cate2.ID
	//			cate3.Level = 3
	//			cate3.Name = "默认"
	//			source.DB().Where("name=? and level=? and parent_id=?", cate3.Name, cate3.Level, cate3.ParentID).FirstOrCreate(&cate3)
	//		}
	//
	//		goods.Category1ID = cate1.ID
	//		goods.Category2ID = cate2.ID
	//		goods.Category3ID = cate3.ID
	//	} else {
	//		goods.Category1ID = uint(cateId1)
	//		goods.Category2ID = uint(cateId2)
	//		goods.Category3ID = uint(cateId3)
	//	}
	//	goods.FreightType = 2
	//	goods.GatherSupplyID = elem.GatherSupplyID
	//
	//	/**
	//	处理轮播图
	//	*/
	//
	//	for _, img := range detail.Imgs {
	//		goods.Gallery = append(goods.Gallery, pmodel.GalleryItem{
	//			Type: 1,
	//			Src:  img + "?x-oss-process=style/normal",
	//		})
	//	}
	//	/**
	//	处理轮播图结束
	//	*/
	//
	//	goods.MinPrice = uint(int(goods.Price))
	//	goods.MaxPrice = uint(int(goods.Price))
	//	var sku = pmodel.Sku{}
	//	if len(goods.Skus) == 0 {
	//		var options pmodel.Options
	//		var option pmodel.Option
	//		option.SpecName = "规格"
	//		option.SpecItemName = "默认"
	//		options = append(options, option)
	//		sku.Title = "默认"
	//		sku.Options = options
	//		sku.Weight = 0
	//		sku.CostPrice = goods.CostPrice
	//		sku.Stock = int(goods.Stock)
	//		sku.IsDisplay = goods.IsDisplay
	//		sku.Price = goods.Price
	//		sku.OriginPrice = goods.OriginPrice
	//		sku.GuidePrice = goods.GuidePrice
	//		sku.ActivityPrice = goods.ActivityPrice
	//		sku.OriginalSkuID = int64(elem.ID)
	//		sku.Sn = goods.Sn
	//		if detail.TaxCode != "" {
	//			goods.BillPosition = 2
	//			sku.TaxCode = detail.TaxCode
	//		}
	//
	//	}
	//	goods.Skus = append(goods.Skus, sku)
	//	//处理资质json图片数组
	//	if len(goods.Skus) > 0 {
	//		goods.ProfitRate = utils.ExecProfitRate(goods.Skus[0].GuidePrice, goods.Skus[0].Price)
	//	}
	//	goods.DetailImages = "<p>"
	//	for _, detailImg := range detail.DetailImgs {
	//		goods.DetailImages += "<img src=\"" + detailImg + "?x-oss-process=style/normal\">"
	//	}
	//	goods.DetailImages += "</p>"
	//	//--------处理详情json图片数组结束
	//
	//	//处----------------理属性json数组
	//
	//	goods.Attrs = append(goods.Attrs, pmodel.Attr{
	//		Name:  "重量",
	//		Value: strconv.Itoa(int(detail.Weight)),
	//	})
	//	goods.Attrs = append(goods.Attrs, pmodel.Attr{
	//		Name:  "体积",
	//		Value: strconv.Itoa(int(detail.Volume)),
	//	})
	//	//---------处理属性json数组结束
	//	//goods.Desc=detail.Description
	//
	//	if len(goods.Skus) > 0 {
	//		listGoods = append(listGoods, goods)
	//	} else {
	//		fmt.Println("无规格商品，不导入", goods.ID)
	//	}
	//
	//}

	return
}

func (szbao *Szbao) CommodityAssemblyNew(list []publicModel.Goods, cateId1, cateId2, cateId3, brandId int, gatherSupplyID uint, isUpdate int) (err error, listGoods []*pmodel.Product, recordErrors []publicModel.SupplyGoodsImportRecordErrors) {

	idArr := GetIdArr(list)
	listArr := SplitArray(idArr, 20)
	var detailData = make(map[uint]model.SzbaoGoodsV2)
	for index, item := range listArr {
		fmt.Println("循环分割id第", index, "次:", item)
		//stringIds := service.GetArrIds(item)
		fmt.Println("返回ids", item)
		var data map[uint]model.SzbaoGoodsV2
		var szbao = Szbao{}
		err, data = szbao.BatchGetGoodsDetails(item, isUpdate)
		if err != nil {
			return
		}
		for detailIndex, detailItem := range data {
			detailData[detailIndex] = detailItem

		}
	}
	var szbaoBrands interface{}
	err, szbaoBrands = szbao.GetBrandList()
	if err != nil {
		return
	}
	var brandJsonData []byte
	brandJsonData, err = json.Marshal(szbaoBrands)
	if err != nil {
		return
	}
	var szbaoBrandStruct []model.SzbaoBrand
	err = json.Unmarshal(brandJsonData, &szbaoBrandStruct)
	if err != nil {
		return
	}
	var szbaoBrandMap = make(map[uint]string)
	for _, szbaoB := range szbaoBrandStruct {
		szbaoBrandMap[szbaoB.ID] = szbaoB.BrandName
	}
	//_, detailData := BatchGetGoodsDetails(ids) //批量获取详情

	for _, elem := range list {
		detail, ok := detailData[uint(elem.ID)]
		if !ok && isUpdate == 0 {
			recordErrors = append(recordErrors, publicModel.SupplyGoodsImportRecordErrors{Error: err.Error(), SourceId: uint(elem.ID), Title: elem.Title})
			continue

		}
		goods := new(pmodel.Product)

		if isUpdate == 1 {
			goods.ID = uint(elem.ProductID)
		}
		goods.Title = elem.Title
		var intXAdvice uint64
		if szbaoData.Pricing.SupplyAdvice == 1 {
			intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceGuide, 10, 32)
			goods.OriginPrice = uint(detail.MinMarketPrice*100) * uint(intXAdvice) / 100
		} else if szbaoData.Pricing.SupplyAdvice == 2 {
			intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceAgreement, 10, 32)
			goods.OriginPrice = uint(detail.MinCurrentVipPrice*100) * uint(intXAdvice) / 100
		} else if szbaoData.Pricing.SupplyAdvice == 3 {
			intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceMarketing, 10, 32)
			if detail.MinRetailPrice > 0 {
				goods.OriginPrice = uint(detail.MinRetailPrice*100) * uint(intXAdvice) / 100
			} else {
				goods.OriginPrice = uint(detail.MinMarketPrice*100) * uint(intXAdvice) / 100
			}
		} else if szbaoData.Pricing.SupplyGuide == 4 {
			intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceFreight, 10, 32)
			if detail.MinFreightPrice > 0 {
				goods.OriginPrice = uint(detail.MinFreightPrice*100) * uint(intXAdvice) / 100
			} else {
				goods.OriginPrice = uint(detail.MinCurrentVipPrice*100) * uint(intXAdvice) / 100
			}
		} else {
			goods.OriginPrice = uint(detail.MinMarketPrice * 100)
		}
		var intX uint64
		if szbaoData.Pricing.SupplySales == 1 {
			intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesGuide, 10, 32)
			goods.Price = uint(detail.MinMarketPrice*100) * uint(intX) / 100
		} else if szbaoData.Pricing.SupplySales == 2 {
			if detail.MinZpTaxRate == 0 || szbaoData.Pricing.SupplySalesIsTax == 0 {
				intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesAgreement, 10, 32)
				goods.Price = uint(detail.MinCurrentVipPrice*100) * uint(intX) / 100
			} else {
				intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesTax, 10, 32)
				goods.Price = uint(detail.MinCurrentVipPrice*100) * uint(intX) / 100
			}

		} else if szbaoData.Pricing.SupplySales == 3 {
			intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesMarketing, 10, 32)
			if detail.MinRetailPrice > 0 {
				goods.Price = uint(detail.MinRetailPrice*100) * uint(intX) / 100
			} else {
				goods.Price = uint(detail.MinMarketPrice*100) * uint(intX) / 100
			}
		} else if szbaoData.Pricing.SupplyGuide == 4 {
			intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesFreight, 10, 32)
			if detail.MinFreightPrice > 0 {
				goods.Price = uint(detail.MinFreightPrice*100) * uint(intX) / 100
			} else {
				goods.Price = uint(detail.MinCurrentVipPrice*100) * uint(intX) / 100
			}
		} else {
			goods.Price = uint(detail.MinCurrentVipPrice * 100)
		}

		var intXGuide uint64
		if szbaoData.Pricing.SupplyGuide == 1 {
			intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideGuide, 10, 32)
			goods.GuidePrice = uint(detail.MinMarketPrice*100) * uint(intXGuide) / 100
		} else if szbaoData.Pricing.SupplyGuide == 2 {
			intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideAgreement, 10, 32)
			goods.GuidePrice = uint(detail.MinCurrentVipPrice*100) * uint(intXGuide) / 100
		} else if szbaoData.Pricing.SupplyGuide == 3 {
			intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideMarketing, 10, 32)
			if detail.MinRetailPrice > 0 {
				goods.GuidePrice = uint(detail.MinRetailPrice*100) * uint(intXGuide) / 100
			} else {
				goods.GuidePrice = uint(detail.MinMarketPrice*100) * uint(intXGuide) / 100
			}
		} else if szbaoData.Pricing.SupplyGuide == 4 {
			intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideFreight, 10, 32)
			if detail.MinFreightPrice > 0 {
				goods.GuidePrice = uint(detail.MinFreightPrice*100) * uint(intXGuide) / 100
			} else {
				goods.GuidePrice = uint(detail.MinCurrentVipPrice*100) * uint(intXGuide) / 100
			}
		} else {
			goods.GuidePrice = uint(detail.MinMarketPrice * 100)
		}

		var intXActivity uint64
		if szbaoData.Pricing.SupplyActivity == 1 {
			intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityGuide, 10, 32)
			goods.ActivityPrice = uint(detail.MinMarketPrice*100) * uint(intXActivity) / 100
		} else if szbaoData.Pricing.SupplyActivity == 2 {
			intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityAgreement, 10, 32)
			goods.ActivityPrice = uint(detail.MinCurrentVipPrice*100) * uint(intXActivity) / 100
		} else if szbaoData.Pricing.SupplyActivity == 3 {
			intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityMarketing, 10, 32)
			if detail.MinRetailPrice > 0 {
				goods.ActivityPrice = uint(detail.MinRetailPrice*100) * uint(intXActivity) / 100
			} else {
				goods.ActivityPrice = uint(detail.MinMarketPrice*100) * uint(intXActivity) / 100
			}
		} else if szbaoData.Pricing.SupplyActivity == 4 {
			intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityFreight, 10, 32)
			if detail.MinFreightPrice > 0 {
				goods.ActivityPrice = uint(detail.MinFreightPrice*100) * uint(intXActivity) / 100
			} else {
				goods.ActivityPrice = uint(detail.MinCurrentVipPrice*100) * uint(intXActivity) / 100
			}
		} else {
			goods.ActivityPrice = uint(detail.MinMarketPrice * 100)
		}
		var intXCost uint64
		if szbaoData.Pricing.SupplyCost == 1 {
			intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostGuide, 10, 32)
			goods.CostPrice = uint(detail.MinMarketPrice*100) * uint(intXCost) / 100
		} else if szbaoData.Pricing.SupplyCost == 2 {
			intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostAgreement, 10, 32)
			goods.CostPrice = uint(detail.MinCurrentVipPrice*100) * uint(intXCost) / 100
		} else if szbaoData.Pricing.SupplyCost == 3 {
			intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostMarketing, 10, 32)
			if detail.MinRetailPrice > 0 {
				goods.CostPrice = uint(detail.MinRetailPrice*100) * uint(intXCost) / 100
			} else {
				goods.CostPrice = uint(detail.MinMarketPrice*100) * uint(intXCost) / 100
			}
		} else if szbaoData.Pricing.SupplyCost == 4 {
			intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostFreight, 10, 32)
			if detail.MinFreightPrice > 0 {
				goods.CostPrice = uint(detail.MinFreightPrice*100) * uint(intXCost) / 100
			} else {
				goods.CostPrice = uint(detail.MinCurrentVipPrice*100) * uint(intXCost) / 100
			}
		} else {
			goods.CostPrice = uint(detail.MinCurrentVipPrice * 100)
		}
		goods.Stock = elem.Stock
		goods.IsDisplay = elem.Status
		goods.ImageUrl = elem.Cover + "?x-oss-process=style/normal"
		goods.Unit = elem.Unit
		goods.Source = common.SZBAO_SOURCE
		goods.SourceGoodsID = uint(elem.ID)
		goods.MD5 = detail.MD5
		if cateId1 == 0 && cateId2 == 0 {
			cateList := strings.Split(elem.ThirdCategoryName, ",")
			var cate1, cate2, cate3 catemodel.Category
			display := 1
			if len(cateList) > 0 && cateList[0] != "" {

				cate1.IsDisplay = &display
				cate1.ParentID = 0
				cate1.Level = 1
				cate1.Name = cateList[0]
				source.DB().Where("name=? and level=? and parent_id=?", cateList[0], 1, 0).FirstOrCreate(&cate1)
			} else {
				cate2.IsDisplay = &display
				cate2.ParentID = 0
				cate2.Level = 1
				cate2.Name = "默认"
				source.DB().Where("name=? and level=? and parent_id=?", cate1.Name, cate1.Level, cate1.ParentID).FirstOrCreate(&cate1)

			}

			if len(cateList) > 1 && cateList[1] != "" {
				cate2.IsDisplay = &display
				cate2.ParentID = cate1.ID
				cate2.Level = 2
				cate2.Name = cateList[1]
				source.DB().Where("name=? and level=? and parent_id=?", cateList[1], 2, cate1.ID).FirstOrCreate(&cate2)
			} else {
				cate2.IsDisplay = &display
				cate2.ParentID = cate1.ID
				cate2.Level = 2
				cate2.Name = "默认"
				source.DB().Where("name=? and level=? and parent_id=?", cate2.Name, cate2.Level, cate2.ParentID).FirstOrCreate(&cate2)

			}

			if len(cateList) > 2 && cateList[2] != "" {
				cate3.IsDisplay = &display
				cate3.ParentID = cate2.ID
				cate3.Level = 3
				cate3.Name = cateList[2]
				source.DB().Where("name=? and level=? and parent_id=?", cateList[2], 3, cate2.ID).FirstOrCreate(&cate3)
			} else {
				cate3.IsDisplay = &display
				cate3.ParentID = cate2.ID
				cate3.Level = 3
				cate3.Name = "默认"
				source.DB().Where("name=? and level=? and parent_id=?", cate3.Name, cate3.Level, cate3.ParentID).FirstOrCreate(&cate3)
			}

			goods.Category1ID = cate1.ID
			goods.Category2ID = cate2.ID
			goods.Category3ID = cate3.ID
		} else {
			goods.Category1ID = uint(cateId1)
			goods.Category2ID = uint(cateId2)
			goods.Category3ID = uint(cateId3)
		}
		if brandId > 0 {
			goods.BrandID = uint(brandId)
		} else {
			var brand catemodel.Brand
			if _, szbok := szbaoBrandMap[detail.BrandId]; szbok {
				brand.Name = szbaoBrandMap[detail.BrandId]
				err = source.DB().Where("`name` = ?", brand.Name).FirstOrCreate(&brand).Error
				goods.BrandID = brand.ID
			}
		}
		goods.FreightType = 2
		goods.GatherSupplyID = elem.GatherSupplyID

		goods.DetailImages = "<p>"

		var gallery pmodel.Gallery
		var attr, attr2 pmodel.Attr
		for skuKey, sku := range detail.SpecificationList {
			if skuKey == 0 {
				for _, img := range sku.Imgs {
					gallery = append(gallery, pmodel.GalleryItem{
						Src:  img,
						Type: 1,
					})
				}
				for _, detailImg := range sku.DetailImgs {
					goods.DetailImages += "<img src=\"" + detailImg + "?x-oss-process=style/normal\">"
				}
			}
			var localSku = pmodel.Sku{}
			var options pmodel.Options
			var specInfoMap map[string]string
			if utils.IsJSON(sku.SpecInfo) {
				err = json.Unmarshal([]byte(sku.SpecInfo), &specInfoMap)
				if err != nil {
					continue
				}
				for SpecName, SpecItemName := range specInfoMap {
					var option pmodel.Option
					option.SpecName = SpecName
					option.SpecItemName = SpecItemName
					options = append(options, option)
				}
			} else {
				var option pmodel.Option
				option.SpecName = "默认"
				option.SpecItemName = sku.Name
				options = append(options, option)
			}

			localSku.Title = sku.Name
			localSku.ImageUrl = sku.ItemMainImg
			localSku.Options = options
			localSku.Weight = int(sku.Weight * 1000)
			//是否更新供货价
			var intX uint64
			if szbaoData.Pricing.SupplySales == 1 {
				intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesGuide, 10, 32)
				localSku.Price = uint(sku.MarketPrice*100) * uint(intX) / 100
			} else if szbaoData.Pricing.SupplySales == 2 {
				if sku.ZpTaxRate == 0 || szbaoData.Pricing.SupplySalesIsTax == 0 {
					intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesAgreement, 10, 32)
					localSku.Price = uint(sku.CurrVipPrice*100) * uint(intX) / 100
				} else {
					intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesTax, 10, 32)
					localSku.Price = uint(sku.CurrVipPrice*100) * uint(intX) / 100
				}

			} else if szbaoData.Pricing.SupplySales == 3 {
				intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesMarketing, 10, 32)
				if sku.RetailPrice > 0 {
					localSku.Price = uint(sku.RetailPrice*100) * uint(intX) / 100
				} else {
					localSku.Price = uint(sku.MarketPrice*100) * uint(intX) / 100
				}
			} else if szbaoData.Pricing.SupplyGuide == 4 {
				intX, err = strconv.ParseUint(szbaoData.Pricing.SupplySalesFreight, 10, 32)
				if sku.FreightPrice > 0 {
					localSku.Price = uint(sku.FreightPrice*100) * uint(intX) / 100
				} else {
					localSku.Price = uint(sku.CurrVipPrice*100) * uint(intX) / 100
				}
			} else {
				localSku.Price = uint(sku.CurrVipPrice * 100)
			}

			//是否更新成本价
			var intXCost uint64
			if szbaoData.Pricing.SupplyCost == 1 {
				intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostGuide, 10, 32)
				localSku.CostPrice = uint(sku.MarketPrice*100) * uint(intXCost) / 100
			} else if szbaoData.Pricing.SupplyCost == 2 {
				intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostAgreement, 10, 32)
				localSku.CostPrice = uint(sku.CurrVipPrice*100) * uint(intXCost) / 100
			} else if szbaoData.Pricing.SupplyCost == 3 {
				intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostMarketing, 10, 32)
				if sku.RetailPrice > 0 {
					localSku.CostPrice = uint(sku.RetailPrice*100) * uint(intXCost) / 100
				} else {
					localSku.CostPrice = uint(sku.MarketPrice*100) * uint(intXCost) / 100
				}
			} else if szbaoData.Pricing.SupplyCost == 4 {
				intXCost, err = strconv.ParseUint(szbaoData.Pricing.SupplyCostFreight, 10, 32)
				if sku.FreightPrice > 0 {
					localSku.CostPrice = uint(sku.FreightPrice*100) * uint(intXCost) / 100
				} else {
					localSku.CostPrice = uint(sku.CurrVipPrice*100) * uint(intXCost) / 100
				}
			} else {
				localSku.CostPrice = uint(sku.CurrVipPrice * 100)
			}

			var intXActivity uint64
			if szbaoData.Pricing.SupplyActivity == 1 {
				intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityGuide, 10, 32)
				localSku.ActivityPrice = uint(sku.MarketPrice*100) * uint(intXActivity) / 100
			} else if szbaoData.Pricing.SupplyActivity == 2 {
				intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityAgreement, 10, 32)
				localSku.ActivityPrice = uint(sku.CurrVipPrice*100) * uint(intXActivity) / 100
			} else if szbaoData.Pricing.SupplyActivity == 3 {
				intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityMarketing, 10, 32)
				if sku.RetailPrice > 0 {
					localSku.ActivityPrice = uint(sku.RetailPrice*100) * uint(intXActivity) / 100
				} else {
					localSku.ActivityPrice = uint(sku.MarketPrice*100) * uint(intXActivity) / 100
				}
			} else if szbaoData.Pricing.SupplyActivity == 4 {
				intXActivity, err = strconv.ParseUint(szbaoData.Pricing.SupplyActivityFreight, 10, 32)
				if sku.FreightPrice > 0 {
					localSku.ActivityPrice = uint(sku.FreightPrice*100) * uint(intXActivity) / 100
				} else {
					localSku.ActivityPrice = uint(sku.CurrVipPrice*100) * uint(intXActivity) / 100
				}
			} else {
				localSku.ActivityPrice = uint(sku.MarketPrice * 100)
			}

			var intXGuide uint64
			if szbaoData.Pricing.SupplyGuide == 1 {
				intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideGuide, 10, 32)
				localSku.GuidePrice = uint(sku.MarketPrice*100) * uint(intXGuide) / 100
			} else if szbaoData.Pricing.SupplyGuide == 2 {
				intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideAgreement, 10, 32)
				localSku.GuidePrice = uint(sku.CurrVipPrice*100) * uint(intXGuide) / 100
			} else if szbaoData.Pricing.SupplyGuide == 3 {
				intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideMarketing, 10, 32)
				if sku.RetailPrice > 0 {
					localSku.GuidePrice = uint(sku.RetailPrice*100) * uint(intXGuide) / 100
				} else {
					localSku.GuidePrice = uint(sku.MarketPrice*100) * uint(intXGuide) / 100
				}
			} else if szbaoData.Pricing.SupplyGuide == 4 {
				intXGuide, err = strconv.ParseUint(szbaoData.Pricing.SupplyGuideFreight, 10, 32)
				if sku.FreightPrice > 0 {
					localSku.GuidePrice = uint(sku.FreightPrice*100) * uint(intXGuide) / 100
				} else {
					localSku.GuidePrice = uint(sku.CurrVipPrice*100) * uint(intXGuide) / 100
				}
			} else {
				localSku.GuidePrice = uint(sku.MarketPrice * 100)
			}
			var intXAdvice uint64
			if szbaoData.Pricing.SupplyAdvice == 1 {
				intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceGuide, 10, 32)
				localSku.OriginPrice = uint(sku.MarketPrice*100) * uint(intXAdvice) / 100
			} else if szbaoData.Pricing.SupplyAdvice == 2 {
				intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceAgreement, 10, 32)
				localSku.OriginPrice = uint(sku.CurrVipPrice*100) * uint(intXAdvice) / 100
			} else if szbaoData.Pricing.SupplyAdvice == 3 {
				intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceMarketing, 10, 32)
				if sku.RetailPrice > 0 {
					localSku.OriginPrice = uint(sku.RetailPrice*100) * uint(intXAdvice) / 100
				} else {
					localSku.OriginPrice = uint(sku.MarketPrice*100) * uint(intXAdvice) / 100
				}
			} else if szbaoData.Pricing.SupplyGuide == 4 {
				intXAdvice, err = strconv.ParseUint(szbaoData.Pricing.SupplyAdviceFreight, 10, 32)
				if sku.FreightPrice > 0 {
					localSku.OriginPrice = uint(sku.FreightPrice*100) * uint(intXAdvice) / 100
				} else {
					localSku.OriginPrice = uint(sku.CurrVipPrice*100) * uint(intXAdvice) / 100
				}
			} else {
				localSku.OriginPrice = uint(sku.MarketPrice * 100)
			}

			localSku.Stock = sku.StockNum
			localSku.Sn = sku.Code
			if sku.TaxCode != "" {
				goods.BillPosition = 2
				localSku.TaxCode = sku.TaxCode
				localSku.FreeOfTax = 1
			}
			attr = pmodel.Attr{
				Name:  "重量",
				Value: strconv.Itoa(int(sku.Weight)),
			}
			attr2 = pmodel.Attr{
				Name:  "体积",
				Value: strconv.Itoa(int(sku.Volume)),
			}
			goods.Skus = append(goods.Skus, localSku)
		}
		goods.Gallery = gallery

		goods.DetailImages += "</p>"
		if len(goods.Skus) > 0 {
			goods.ProfitRate = utils.ExecProfitRate(goods.Skus[0].GuidePrice, goods.Skus[0].Price)
		}
		//--------处理详情json图片数组结束

		//处----------------理属性json数组

		goods.Attrs = append(goods.Attrs, attr)
		goods.Attrs = append(goods.Attrs, attr2)
		//---------处理属性json数组结束
		//goods.Desc=detail.Description

		if len(goods.Skus) > 0 {
			listGoods = append(listGoods, goods)
		} else {
			fmt.Println("无规格商品，不导入", goods.ID)
		}

	}

	return
}

// 批量获取商品详情
func (*Szbao) BatchGetGoodsDetails(ids []int, isUpdate int) (err error, data map[uint]model.SzbaoGoodsV2) {

	var detailList = make(map[uint]model.SzbaoGoodsV2)

	fmt.Println("BatchGetGoodsDetails:", ids)
	var list []model.SzbaoGoodsV2
	err = source.DB().Where("`id` in ?", ids).Where("`gather_supply_id` = ?", GatherSupplyID).Find(&list).Error
	if err != nil {
		return
	}

	var exitsList []pmodel.Product
	err = source.DB().Where("`source_goods_id` in ?", ids).Where("`gather_supply_id` = ?", GatherSupplyID).Find(&exitsList).Error
	if err != nil {
		return
	}
	var exitsMap = make(map[uint]pmodel.Product)
	for _, e := range exitsList {
		exitsMap[e.SourceGoodsID] = e
	}
	fmt.Println("总解析数量：", len(list))
	for _, item := range list {
		if _, ok := exitsMap[item.ID]; ok && isUpdate == 0 {
			continue
		}
		detailList[item.ID] = item
	}
	fmt.Println("总解析数量1：", len(detailList))
	if len(detailList) == 0 {
		err = errors.New("导入商品数量为0")
	}
	data = detailList

	return
}

// 获取胜天半子分类数据
func (s *Szbao) GetCategory(info request.GetCategorySearch) (err error, data interface{}) {
	// 查询分类数量
	var categoryCount int64
	if err = source.DB().Model(&model.SzbaoCategory{}).Count(&categoryCount).Error; err != nil {
		return
	}

	// 如果分类数量等于0 同步分类
	if categoryCount == 0 {
		if err = s.InitCategory(); err != nil {
			return
		}
	}

	var sCategories []model.SzbaoCategory
	if err = source.DB().Find(&sCategories).Error; err != nil {
		return
	}

	var result []catemodel.Category
	for _, v := range sCategories {
		result = append(result, catemodel.Category{
			CategoryModel: catemodel.CategoryModel{
				Model:    source.Model{ID: v.ID},
				Name:     v.Name,
				ParentID: uint(v.SupID),
			},
		})
	}

	return nil, result
}

func (*Szbao) GetGroup() (err error, data interface{}) {

	return

}

func (s *Szbao) GetCategoryChild(pid int, info request.GetCategoryChild) (err error, data interface{}) {
	// 查询分类数量
	var categoryCount int64
	if err = source.DB().Model(&model.SzbaoCategory{}).Count(&categoryCount).Error; err != nil {
		return
	}

	// 如果分类数量等于0 同步分类
	if categoryCount == 0 {
		if err = s.InitCategory(); err != nil {
			return
		}
	}

	if pid == 0 {
		pid = -1
	}
	var sCategories []model.SzbaoCategory
	if err = source.DB().Where("sup_id = ?", pid).Find(&sCategories).Error; err != nil {
		return
	}

	var result []catemodel.Category
	for _, v := range sCategories {
		result = append(result, catemodel.Category{
			CategoryModel: catemodel.CategoryModel{
				Model:    source.Model{ID: v.ID},
				Name:     v.Name,
				ParentID: uint(v.SupID),
			},
		})
	}

	return nil, result
}

func (szbao *Szbao) GetBrandList() (err error, data interface{}) {
	url := szbaoData.BaseInfo.Host + "/open/xdxt/api/v2/goods/listGoodsBrand"

	if err, url = GetRequestParams(szbaoData, url); err != nil {
		return
	}

	requestParam := map[string]interface{}{}
	header := map[string]string{
		"Content-Type": "application/json",
	}

	err, result := utils.Post(url, requestParam, header)
	if err != nil {
		return
	}

	var res SzbaoBrandResponse
	if err = json.Unmarshal(result, &res); err != nil {
		return
	}

	if res.Code != 0 {
		err = errors.New(res.Msg)
		return
	}

	return nil, res.Data
}
func (*Szbao) RunConcurrent(wg *sync.WaitGroup, info request.GetCategorySearch, i int) (err error) {

	defer wg.Done()
	err, result := utils.GetWithHeader(szbaoData.BaseInfo.Host+"/app/category/lists?page="+strconv.Itoa(i)+"&pageSize="+strconv.Itoa(info.Limit), nil)
	var response yzResponse.Response
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}
	fmt.Println("循环：", i, info.Limit, info.Source)
	if response.Data != nil {
		datas := response.Data.(map[string]interface{})
		var cateItem []publicModel.Category
		cateJson := datas["list"]
		mJson, _ := json.Marshal(cateJson)
		stringJson := string(mJson)
		err = json.Unmarshal([]byte(stringJson), &cateItem)
		mutex.Lock()
		if len(cateItem) > 0 {
			category = append(category, cateItem...)
		}
		mutex.Unlock()

	} else {
		fmt.Println("获取失败的：", i, info.Limit, info.Source)
	}

	return
}

// 选品库增加商品
func (*Szbao) GoodsStorageAdd(ids []int, supplyId uint) (err error, info interface{}) {

	for _, item := range ids {

		err = source.DB().Where(publicModel.GoodsStorage{SourceGoodsID: uint(item), SupplyID: supplyId}).FirstOrCreate(&publicModel.GoodsStorage{SourceGoodsID: uint(item), SupplyID: supplyId}).Error
		if err != nil {
			log.Log().Error("供应链选品入库错误", zap.Any("info", info))
		}
	}

	return

}

func (*Szbao) GoodsPriceAlert(GoodsData callback2.GoodsCallBack) (err error) {

	return
}
