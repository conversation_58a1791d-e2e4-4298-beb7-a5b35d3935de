package goods

import "testing"

func TestSzbao_InitGoodsV2New(t *testing.T) {

	goodsClass := Szbao{}
	if err := goodsClass.InitSetting(16); err != nil {
		return
	}

	if err := goodsClass.InitGoodsV2New(); err != nil {
		return
	}

}

func TestSzbao_MergeProducts(t *testing.T) {
	goodsClass := Szbao{}
	if err := goodsClass.InitSetting(16); err != nil {
		return
	}

	if err := goodsClass.MergeProducts(); err != nil {
		return
	}
}

func TestSzbao_InitGoodsV2(t *testing.T) {
	goodsClass := Szbao{}
	if err := goodsClass.InitSetting(16); err != nil {
		return
	}

	if err := goodsClass.InitGoodsV2(); err != nil {
		return
	}
}

func TestSzbao_InitProducts(t *testing.T) {
	goodsClass := Szbao{}
	if err := goodsClass.InitSetting(16); err != nil {
		return
	}

	if err := goodsClass.InitProducts(); err != nil {
		return
	}
}
