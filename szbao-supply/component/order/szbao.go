package order

import (
	afterSalesModel "after-sales/model"
	model4 "application/model"
	_ "embed"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
	"math"
	v1 "order/api/v1"
	orderModel "order/model"
	orderRequest2 "order/request"
	model5 "product/model"
	callback2 "public-supply/callback"
	"public-supply/request"
	"public-supply/response"
	pubres "public-supply/response"
	setting2 "public-supply/setting"
	model3 "region/model"
	"strconv"
	"strings"
	szbao "szbao-supply/component/goods"
	"szbao-supply/model"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

type Szbao struct{}

func (s *Szbao) UploadGatherSupplySN(request request.UpdateData) (err error, data interface{}) {
	//TODO implement me
	panic("implement me")
}

func (s *Szbao) GetRefundTypes(orderId uint, orderItemID uint) (err error, data []afterSalesModel.AfterSalesType) {
	return
}

func (s *Szbao) GetAllAddress() (err error, data interface{}) {
	return
}

var szbaoData *szbao.SzbaoSupplySetting

type SzbaoBeforeCheckRequest struct {
	Province        string      `json:"province"`
	City            string      `json:"city"`
	District        string      `json:"district"`
	AddressDetail   string      `json:"addressDetail"`
	GoodsParamsList interface{} `json:"goodsParamsList"`
}

type SzbaoBeforeCheckRequestV2 struct {
	Province        string              `json:"province"`
	City            string              `json:"city"`
	District        string              `json:"district"`
	GoodsParamsList []GoodsParamsListV2 `json:"orderDetailsList"`
}
type GoodsParamsList struct {
	Code     string `json:"code"`
	GoodsNum int    `json:"goodsNum"`
}

type GoodsParamsListV2 struct {
	Code     string `json:"code"`
	GoodsNum int    `json:"num"`
}
type SzbaoResponse struct {
	Code   int         `json:"code"`
	Result interface{} `json:"result"`
	Msg    string      `json:"msg"`
}
type SzbaoComfirmOrderResponse struct {
	UnionId          string  `json:"unionId"`
	PayMoney         float64 `json:"payMoney"`
	GoodsMoneyAmount float64 `json:"goodsMoneyAmount"`
	ExpFeeAmount     float64 `json:"expFeeAmount"`
	ExpCode          string  `json:"expCode"`
	ExpName          string  `json:"expName"`
}

type SzbaoComfirmOrderResponseV2 struct {
	Code   int    `json:"code"`
	Msg    string `json:"msg"`
	Result struct {
		UnionNo          string  `json:"unionNo"`
		TotalNum         int     `json:"totalNum"`
		ExpFeeAmount     float64 `json:"expFeeAmount"`
		GoodsMoneyAmount float64 `json:"goodsMoneyAmount"`
		PayMoney         float64 `json:"payMoney"`
		OrderList        []struct {
			SuborderId       int     `json:"suborderId"`
			TotalGoodsAmount float64 `json:"totalGoodsAmount"`
			FreightAmount    float64 `json:"freightAmount"`
			TotalAmount      float64 `json:"totalAmount"`
			OrderDetailList  []struct {
				OrderDetailId int     `json:"orderDetailId"`
				SpecCode      string  `json:"specCode"`
				SpecName      string  `json:"specName"`
				SpecInfo      string  `json:"specInfo"`
				Num           int     `json:"num"`
				UnitPrice     float64 `json:"unitPrice"`
				TotalAmount   float64 `json:"totalAmount"`
			} `json:"orderDetailList"`
		} `json:"orderList"`
	} `json:"result"`
}
type SzbaoConfirmRequest struct {
	SzbaoBeforeCheckRequest
	CustName   string `json:"custName"`
	CustMobile string `json:"custMobile"`
}

type SzbaoConfirmRequestV2 struct {
	SzbaoBeforeCheckRequestV2
	AddressDetail   string `json:"addressDetail"`
	CustName        string `json:"custName"`
	CustMobile      string `json:"custMobile"`
	PlatformOrderNo string `json:"platformOrderNo"`
}

func (*Szbao) InitSetting(gatherSupplyID uint) (err error) {
	var setting model2.SysSetting
	err, setting = setting2.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		return
	}

	err = json.Unmarshal([]byte(setting.Value), &szbaoData)
	if err != nil {

		//log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	if szbaoData.BaseInfo.AppKey == "" || szbaoData.BaseInfo.AppSecret == "" || szbaoData.BaseInfo.Host == "" {
		err = errors.New("请先配置供应链key")
		return
	}

	return
}
func handleRegion(province, city, area string) (resultProvince, resultCity, resultArea string) {
	province = TrimString(province)
	city = TrimString(city)
	area = TrimString(area)
	if province == "重庆市" || province == "天津市" || province == "上海市" || province == "北京市" {
		resultCity = province
		resultProvince = strings.TrimRight(province, "市")
		resultArea = area
	} else {
		if province == "海南省" && city != "海口市" && city != "三亚市" && city != "儋州市" && city != "三沙市" {
			city = "省直辖县"
		}
		resultProvince = province
		resultCity = city
		resultArea = area
	}
	return
}

// 订单前置校验  返回运费
func (*Szbao) OrderBeforeCheckV1(request request.RequestSaleBeforeCheck) (err error, data response.BeforeCheck) {
	//log.Log().Info("szbao接到的参数", zap.Any("params", request))
	var province, city, county model3.Region
	err, province, city, county, _ = MatchingRegion(Address{
		Province: request.Address.Province,
		City:     request.Address.City,
		Area:     request.Address.Area,
		Street:   request.Address.Street,
	})
	var requestData SzbaoBeforeCheckRequest
	requestData.Province, requestData.City, requestData.District = handleRegion(province.Name, city.Name, county.Name)
	requestData.AddressDetail = TrimString(request.Address.Description)
	var goodsParamsList []GoodsParamsList
	for _, v := range request.Skus {
		var szGoods model.SzbaoGoods
		err = source.DB().First(&szGoods, int(v.Sku.Sku)).Error
		if err != nil {
			return
		}
		goodsParamsList = append(goodsParamsList, GoodsParamsList{
			Code:     szGoods.Code,
			GoodsNum: v.Number,
		})
	}
	var goodsParamsListJson []byte
	goodsParamsListJson, err = json.Marshal(goodsParamsList)
	requestData.GoodsParamsList = string(goodsParamsListJson)
	var jsonData []byte
	jsonData, _ = json.Marshal(&requestData)
	m := make(map[string]string)
	json.Unmarshal(jsonData, &m)
	requestParams := szbao.GetRequestParamsV1(m, szbaoData)
	log.Log().Info("szbao请求参数", zap.Any("params", requestParams))
	err, result := utils.PostForm(szbaoData.BaseInfo.Host+"/vip/api/order/orderFreightPreview", requestParams, nil)
	var szbaoResponse SzbaoResponse
	err = json.Unmarshal(result, &szbaoResponse)
	if err != nil {
		return
	}

	if szbaoResponse.Code != 0 {
		err = errors.New(szbaoResponse.Msg)
		return
	}

	interfaceData := szbaoResponse
	var resData response.SzbaoBeforeCheck

	if interfaceData.Code == 0 {
		jsonData, err = json.Marshal(interfaceData.Result)
		err = json.Unmarshal(jsonData, &resData)

		data.Freight = uint(resData.Freight) * 100
		data.Msg = szbaoResponse.Msg
		data.Code = 1
	} else {
		data.Msg = szbaoResponse.Msg
		data.Code = uint(szbaoResponse.Code)
	}

	return

}

func (*Szbao) OrderBeforeCheck(request request.RequestSaleBeforeCheck) (err error, data response.BeforeCheck) {
	if request.AppID > 0 {
		var application model4.Application
		err = source.DB().First(&application, request.AppID).Error
		if err != nil {
			return
		}
		if application.SzbaoIndependence == 1 {
			if application.SzbaoIndependenceAppKey != "" && application.SzbaoIndependenceAppSecret != "" {
				szbaoData.BaseInfo.AppKey = application.SzbaoIndependenceAppKey
				szbaoData.BaseInfo.AppSecret = application.SzbaoIndependenceAppSecret
			}
		}
	}
	//log.Log().Info("szbao接到的参数", zap.Any("params", request))
	var province, city, county model3.Region
	err, province, city, county, _ = MatchingRegion(Address{
		Province: request.Address.Province,
		City:     request.Address.City,
		Area:     request.Address.Area,
		Street:   request.Address.Street,
	})
	var requestData SzbaoBeforeCheckRequestV2
	requestData.Province, requestData.City, requestData.District = handleRegion(province.Name, city.Name, county.Name)
	for _, v := range request.LocalSkus {
		var localSKu model5.Sku
		err = source.DB().First(&localSKu, int(v.Sku.Sku)).Error
		if err != nil {
			return
		}
		requestData.GoodsParamsList = append(requestData.GoodsParamsList, GoodsParamsListV2{
			Code:     localSKu.Code,
			GoodsNum: v.Number,
		})
	}

	var url = szbaoData.BaseInfo.Host + "/open/xdxt/api/v2/order/orderFreightPreview"
	err, url = szbao.GetRequestParams(szbaoData, url)
	log.Log().Info("szbao请求参数", zap.Any("params", requestData))
	var header = make(map[string]string)
	header["Content-Type"] = "application/json"
	log.Log().Info("szbao获取运费请求参数", zap.Any("params", requestData))
	err, result := utils.Post(url, requestData, header)
	if err != nil {
		log.Log().Info("szbao获取运费请求失败", zap.Any("err", err.Error()))
		return
	}
	var szbaoResponse pubres.SzbaoBeforeCheckV2
	err = json.Unmarshal(result, &szbaoResponse)
	if err != nil {
		log.Log().Info("szbao获取运费请求解析失败", zap.Any("data", string(result)))
		return
	}
	log.Log().Info("szbao响应数据", zap.Any("response", szbaoResponse))

	if szbaoResponse.Code != 0 {
		err = errors.New(szbaoResponse.Msg)
		return
	}

	if szbaoResponse.Code == 0 {
		fmt.Println("Freight before multiplication:", szbaoResponse.Result.Freight)
		fmt.Println("Freight after multiplication:", szbaoResponse.Result.Freight*100)
		fmt.Println("Freight after conversion to uint:", uint(szbaoResponse.Result.Freight*100))
		fmt.Println("Freight after conversion to uint1:", uint(math.Round(szbaoResponse.Result.Freight*100)))

		data.Freight = uint(math.Round(szbaoResponse.Result.Freight * 100))
		data.Msg = szbaoResponse.Msg
		data.Code = 1
	} else {
		data.Msg = szbaoResponse.Msg
		data.Code = uint(szbaoResponse.Code)
	}

	return

}

// 商品是否可售前置校验
func (*Szbao) SaleBeforeCheck(request request.RequestSaleBeforeCheck) (err error, resData response.ResSaleBeforeCheck) {
	for _, v := range request.Skus {
		resData.Data.Available = append(resData.Data.Available, uint(v.Sku.Sku))
	}
	return

}

func TrimString(str string) (data string) {
	data = strings.TrimSpace(str)
	return
}

// 确认下单
func (*Szbao) ConfirmOrderV1(requestParam request.RequestConfirmOrder) (err error, info *stbz.APIResult) {

	log.Log().Info("szbao供应链商品准备下单", zap.Any("info", requestParam))
	var province, city, county model3.Region
	err, province, city, county, _ = MatchingRegion(Address{
		Province: requestParam.Address.Province,
		City:     requestParam.Address.City,
		Area:     requestParam.Address.Area,
		Street:   requestParam.Address.Street,
	})

	var requestData SzbaoConfirmRequest
	requestData.CustName = TrimString(requestParam.Address.Consignee)
	requestData.CustMobile = TrimString(requestParam.Address.Phone)
	requestData.Province, requestData.City, requestData.District = handleRegion(province.Name, city.Name, county.Name)
	requestData.AddressDetail = TrimString(requestParam.Address.Description)
	var goodsParamsList []GoodsParamsList
	for _, v := range requestParam.Skus {
		var szGoods model.SzbaoGoods
		err = source.DB().First(&szGoods, int(v.Sku.Sku)).Error
		if err != nil {
			return
		}
		goodsParamsList = append(goodsParamsList, GoodsParamsList{
			Code:     szGoods.Code,
			GoodsNum: v.Number,
		})
	}
	var goodsParamsListJson []byte
	goodsParamsListJson, err = json.Marshal(goodsParamsList)
	requestData.GoodsParamsList = string(goodsParamsListJson)
	var jsonData []byte
	jsonData, _ = json.Marshal(&requestData)
	m := make(map[string]string)
	json.Unmarshal(jsonData, &m)
	requestParams := szbao.GetRequestParamsV1(m, szbaoData)
	var response SzbaoResponse
	//log.Log().Info("szbao供应链商品下单数据1", zap.Any("info", requestParams))

	err, result := utils.PostForm(szbaoData.BaseInfo.Host+"/vip/api/order/createOrder", requestParams, nil)
	log.Log().Info("szbao供应链商品下单返回数据", zap.Any("info", result))

	resultErr := json.Unmarshal(result, &response)
	if err != nil {
		log.Log().Info("szbao供应链商品下单返回解析错误", zap.Any("info", resultErr))

		return
	}
	//log.Log().Info("szbao供应链商品下单数据", zap.Any("info", response))

	if response.Code == 0 {
		response.Code = 1
		jsonData, err = json.Marshal(response.Result)
		var responseData SzbaoComfirmOrderResponse
		err = json.Unmarshal(jsonData, &responseData)
		if err != nil {
			log.Log().Info("szbao供应链商品下单返回 Code 0 ", zap.Any("info", err))

			return
		}
		err = source.DB().Model(&orderModel.Order{}).Where("`order_sn` = ?", requestParam.OrderSn.OrderSn).Update("gather_supply_sn", responseData.UnionId).Error
		if err != nil {
			return
		}
		err = source.DB().Create(&model.SzbaoOrderExpress{
			OrderSN: requestParam.OrderSn.OrderSn,
			ExpName: responseData.ExpName,
			ExpCode: responseData.ExpCode,
		}).Error
		if err != nil {
			return
		}
	} else {
		err = source.DB().Model(&orderModel.Order{}).Where("`order_sn` = ?", requestParam.OrderSn.OrderSn).Update("gather_supply_msg", response.Msg).Error
		if err != nil {
			return
		}
	}
	info = &stbz.APIResult{
		Code: response.Code,
		Msg:  response.Msg,
		Data: response.Result,
	}

	jsonData, err = json.Marshal(info)
	return
}

func (*Szbao) ConfirmOrder(requestParam request.RequestConfirmOrder) (err error, info *stbz.APIResult) {
	if requestParam.AppID > 0 {
		var application model4.Application
		err = source.DB().First(&application, requestParam.AppID).Error
		if err != nil {
			return
		}
		if application.SzbaoIndependence == 1 {
			if application.SzbaoIndependenceAppKey != "" && application.SzbaoIndependenceAppSecret != "" {
				szbaoData.BaseInfo.AppKey = application.SzbaoIndependenceAppKey
				szbaoData.BaseInfo.AppSecret = application.SzbaoIndependenceAppSecret
			}
		}
	}
	log.Log().Info("szbao供应链商品准备下单", zap.Any("info", requestParam))
	var province, city, county model3.Region
	err, province, city, county, _ = MatchingRegion(Address{
		Province: requestParam.Address.Province,
		City:     requestParam.Address.City,
		Area:     requestParam.Address.Area,
		Street:   requestParam.Address.Street,
	})

	var requestData SzbaoConfirmRequestV2
	requestData.PlatformOrderNo = TrimString(requestParam.OrderSn.OrderSn)
	requestData.CustName = TrimString(requestParam.Address.Consignee)
	requestData.CustMobile = TrimString(requestParam.Address.Phone)
	requestData.Province, requestData.City, requestData.District = handleRegion(province.Name, city.Name, county.Name)
	requestData.AddressDetail = TrimString(requestParam.Address.Description)
	for _, v := range requestParam.LocalSkus {
		var localSKu model5.Sku
		err = source.DB().First(&localSKu, int(v.Sku.Sku)).Error
		if err != nil {
			return
		}
		requestData.GoodsParamsList = append(requestData.GoodsParamsList, GoodsParamsListV2{
			Code:     localSKu.Sn,
			GoodsNum: v.Number,
		})
	}

	var url = szbaoData.BaseInfo.Host + "/open/xdxt/api/v2/order/createOrder"
	err, url = szbao.GetRequestParams(szbaoData, url)
	var header = make(map[string]string)
	header["Content-Type"] = "application/json"
	err, result := utils.Post(url, requestData, header)
	var confirmOrderResponse SzbaoComfirmOrderResponseV2

	err = json.Unmarshal(result, &confirmOrderResponse)
	if err != nil {
		return
	}
	log.Log().Info("szbao供应链商品下单数据", zap.Any("info", string(result)))

	if confirmOrderResponse.Code == 0 {
		err = source.DB().Model(&orderModel.Order{}).Where("`order_sn` = ?", requestParam.OrderSn.OrderSn).Update("gather_supply_sn", confirmOrderResponse.Result.UnionNo).Error
		if err != nil {
			return
		}
	} else {
		err = source.DB().Model(&orderModel.Order{}).Where("`order_sn` = ?", requestParam.OrderSn.OrderSn).Update("gather_supply_msg", confirmOrderResponse.Msg).Error
		if err != nil {
			return
		}
	}
	info = &stbz.APIResult{
		Code: confirmOrderResponse.Code,
		Msg:  confirmOrderResponse.Msg,
		Data: confirmOrderResponse.Result,
	}
	var jsonData []byte
	jsonData, err = json.Marshal(info)
	fmt.Println("下单回调数据", string(jsonData))
	return
}

// 物流查询
func (*Szbao) ExpressQuery(request request.RequestExpress) (err error, info interface{}) {

	return

}

func (*Szbao) AfterSalesBeforeCheck(request request.RequestAfterSale) (err error, info interface{}) {

	return

}

func (*Szbao) AfterSalesPicture(request request.RequestAfterSalePicture) (err error, info interface{}) {

	return

}

func (*Szbao) AfterSale(request request.AfterSale) (err error, info interface{}) {

	return

}

// 发货
func (*Szbao) OrderDelivery(OrderData callback2.OrderCallBack) (err error) {

	var orderInfo orderModel.Order
	err = source.DB().Preload("OrderItems").Where("order_sn =  ?", OrderData.Data.OrderSn).First(&orderInfo).Error

	var orderRequest v1.HandleOrderRequest
	var info, jsonData interface{}
	var requestExpress request.RequestExpress
	var expressInfo Response
	requestExpress.OrderSn = OrderData.Data.OrderSn
	requestExpress.Sku = strconv.FormatInt(OrderData.Data.Sku, 10)
	var orderClass = Szbao{}
	err = orderClass.InitSetting(orderInfo.GatherSupplyID)
	if err != nil {
		log.Log().Error("前置校验失败", zap.Any("info", err.Error()))
		return
	}
	err, info = orderClass.ExpressQuery(requestExpress)
	if err != nil {
		log.Log().Error("查询物流信息错误", zap.Any("info", err.Error()))
		return
	}
	jsonData, err = json.Marshal(info)
	err = json.Unmarshal(jsonData.([]byte), &expressInfo)
	if err != nil {
		log.Log().Error("解析物流信息错误1", zap.Any("info", err.Error()))
		return
	}
	//log.Log().Info("供应链订单信息回调", zap.Any("info", expressInfo.Data))
	//log.Log().Info("供应链订单信息回调1", zap.Any("info", orderInfo.OrderItems))

	for _, v := range expressInfo.Data {
		var ids []uint
		for _, orderItem := range v.OrderItems {
			for _, localOrderItem := range orderInfo.OrderItems {

				if orderItem.SkuID == uint(localOrderItem.OriginalSkuID) {

					ids = append(ids, localOrderItem.ID)
				}
			}
		}
		orderRequest.OrderID = orderInfo.ID
		orderRequest.ExpressNo = v.ExpressNo
		orderRequest.OrderItemIDs = append(orderRequest.OrderItemIDs, orderRequest2.OrderItemSendInfo{
			ID:  0,
			Num: 0,
		})
		orderRequest.CompanyCode = v.CompanyCode
		err = ExpressSent(orderRequest)
		if err != nil {
			return
		}
		err = callback2.DeleteOrderMsg(OrderData.MsgID)
		if err != nil {
			log.Log().Error("胜天半子订单发货完成删除记录err", zap.Any("info", err))
		}
	}

	return
}

func (*Szbao) SyncOrderExpNoV1(unionIdList []string) (err error, data []pubres.SyncOrderExpNoResponse) {

	var params = make(map[string]string)
	var jsonData []byte
	jsonData, err = json.Marshal(unionIdList)
	params["unionIdList"] = string(jsonData)
	requestParams := szbao.GetRequestParamsV1(params, szbaoData)
	err, result := utils.PostForm(szbaoData.BaseInfo.Host+"/vip/api/order/syncOrderExpNo", requestParams, nil)
	var response SzbaoResponse
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}
	jsonData, err = json.Marshal(response.Result)
	err = json.Unmarshal(jsonData, &data)
	return
}

func (*Szbao) SyncOrderExpNo(unionIdList []string) (err error, data pubres.SyncOrderExpNoResponseV2) {

	var params = make(map[string]interface{})

	params["unionNos"] = unionIdList
	var url = szbaoData.BaseInfo.Host + "/open/xdxt/api/v2/order/listOrderInfo"
	err, url = szbao.GetRequestParams(szbaoData, url)
	var header = make(map[string]string)
	header["Content-Type"] = "application/json"
	err, result := utils.Post(url, params, header)
	err = json.Unmarshal(result, &data)
	if err != nil {
		return
	}

	if data.Code != 0 {
		err = errors.New(data.Msg)
		return
	}

	return
}

//go:embed szbaoRegion.json
var szbaoRegion string

func getSzbaoRegion(data request.ReceivingInformation) (err error, result request.ReceivingInformation) {
	result = data
	var province, city, area model3.Region
	err = source.DB().Where("name = ?", data.Province).Find(&province).Error
	err = source.DB().Where("name = ?", data.City).Where("parent_id = ?", province.ID).Find(&city).Error
	err = source.DB().Where("name = ?", data.Area).Where("parent_id = ?", city.ID).Find(&area).Error
	if err != nil {
		return
	}
	var szbaopRegions map[string]map[int]string
	err = json.Unmarshal([]byte(szbaoRegion), &szbaopRegions)

	result.Province = szbaopRegions["province_list"][province.ID*10000]

	result.City = szbaopRegions["city_list"][city.ID*100]

	result.Area = szbaopRegions["county_list"][area.ID]

	return
}
