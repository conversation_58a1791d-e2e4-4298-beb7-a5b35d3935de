package cron

import (
	"fmt"
	model2 "public-supply/model"
	"szbao-supply/component/goods"
	"yz-go/cron"
	"yz-go/source"
)

var productMerge *int

func PushGoodsMergeHandle() {
	task := cron.Task{
		Key:  "goodsSzbaoMerge",
		Name: "szbao供应链自动合并本地商品",
		Spec: "45 40 */1 * * *",
		Handle: func(task cron.Task) {
			GoodsMergeCron()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func GoodsMergeCron() {
	if productMerge != nil {
		fmt.Println("已经在执行中")
		return
	}

	var gatherList []model2.GatherSupply
	if err := source.DB().Where("`category_id` = ?", 3).Find(&gatherList).Error; err != nil || len(gatherList) == 0 {
		productMerge = nil
		fmt.Println("未找到szbao供应链")
		return
	}

	productMerge = new(int)
	*productMerge = 1
	for _, v := range gatherList {
		goodsClass := goods.Szbao{}
		// 初始化基础设置
		if err := goodsClass.InitSetting(v.ID); err != nil {
			continue
		}

		// 合并商品数据
		if err := goodsClass.MergeProducts(); err != nil {
			continue
		}
	}

	productMerge = nil
	fmt.Println("szbao本地商品自动合并成功")
}
