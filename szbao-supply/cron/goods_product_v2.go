package cron

import (
	"fmt"
	model2 "public-supply/model"
	"szbao-supply/component/goods"
	"yz-go/cron"
	"yz-go/source"
)

var productOpen *int

func PushGoodsProductV2Handle() {
	task := cron.Task{
		Key:  "goodsSzbaoProductV2",
		Name: "szbao供应链自动更新本地商品V2",
		Spec: "45 30 */1 * * *",
		Handle: func(task cron.Task) {
			GoodsProductV2Cron()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func GoodsProductV2Cron() {
	if productOpen != nil {
		fmt.Println("已经在执行中")
		return
	}

	var gatherList []model2.GatherSupply
	if err := source.DB().Where("`category_id` = ?", 3).Find(&gatherList).Error; err != nil || len(gatherList) == 0 {
		productOpen = nil
		fmt.Println("未找到szbao供应链")
		return
	}

	productOpen = new(int)
	*productOpen = 1
	for _, v := range gatherList {
		goodsClass := goods.Szbao{}
		// 初始化基础设置
		if err := goodsClass.InitSetting(v.ID); err != nil {
			continue
		}

		// 同步商品数据
		if err := goodsClass.InitProductsV2(); err != nil {
			continue
		}
	}

	productOpen = nil
	fmt.Println("szbao本地商品v2自动更新成功")
}
