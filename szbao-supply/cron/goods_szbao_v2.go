package cron

import (
	"fmt"
	model2 "public-supply/model"
	"szbao-supply/component/goods"
	"yz-go/cron"
	"yz-go/source"
)

var szbaoOpenV2 *int

func PushGoodsSzbaoV2Handle() {
	task := cron.Task{
		Key:  "goodsszbaov2",
		Name: "永源供应链选品库商品同步v2",
		Spec: "0 50 */1 * * *",
		Handle: func(task cron.Task) {
			GoodsSzbaoCronV2()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func GoodsSzbaoCronV2() {
	if szbaoOpenV2 != nil {
		fmt.Println("已经在执行中")
		return
	}

	var gatherList []model2.GatherSupply
	if err := source.DB().Where("`category_id` = ?", 3).Find(&gatherList).Error; err != nil || len(gatherList) == 0 {
		szbaoOpenV2 = nil
		fmt.Println("未找到永源供应链")
		return
	}

	for _, v := range gatherList {
		szbaoOpenV2 = new(int)
		*szbaoOpenV2 = 1
		goodsClass := goods.Szbao{}
		if err := goodsClass.InitSetting(v.ID); err != nil {
			continue
		}

		if err := goodsClass.InitGoodsV2New(); err != nil {
			continue
		}
	}

	szbaoOpenV2 = nil
	fmt.Println("永源商品自动同步成功")
}
