module szbao-supply

go 1.21

require (
	category v1.0.0
	gin-vue-admin v1.0.0
	github.com/chenhg5/collection v0.0.0-20200925143926-f403b87088f9
	github.com/writethesky/stbz-sdk-golang v1.0.1
	github.com/xingliuhua/leaf v1.1.2
	go.uber.org/zap v1.16.0
	gorm.io/gorm v1.25.5
	order v1.0.0
	product v1.0.0
	public-supply v1.0.0
	region v1.0.0
	yz-go v1.0.0
)

replace (
	category => ../category
	convergence => ../convergence-pay
	finance => ../finance
	gin-vue-admin v1.0.0 => ../gin-vue-admin/server
	notification => ../notification
	order v1.0.0 => ../order
	payment => ../payment
	product => ../product
	public-supply => ../public-supply
	region => ../region
	sales => ../sales
	shipping v1.0.0 => ../shipping
	shop => ../shop
	supplier => ../supplier
	user v1.0.0 => ../user
	wechatpay => ../wechat-pay
	wechatpay-go-main => ../wechatpay-go-main
	yz-go => ../yz-go
)
