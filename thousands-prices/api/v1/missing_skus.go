package v1

import (
	"github.com/gin-gonic/gin"
	"thousands-prices/service"
	"yz-go/request"
	yzResponse "yz-go/response"
)

func SetMissingSkusStatus(c *gin.Context) {
	var ids request.IdsReq
	if err := c.ShouldBindJSON(&ids); err != nil {
		yzResponse.FailWithMessage(err.<PERSON>rror(), c)
		return
	}
	err := service.SetMissingSkusStatus(ids)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("状态更新成功", c)
}
func GetMissingSkusList(c *gin.Context) {
	var params service.MissingSkusParams
	if err := c.ShouldBindJSON(&params); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	missingSkus, total, err := service.GetMissingSkus(params)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(gin.H{"data": missingSkus, "total": total}, c)
}
