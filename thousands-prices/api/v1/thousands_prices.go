package v1

import (
	"encoding/json"
	v1 "gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	request3 "product/request"
	"strconv"
	"thousands-prices/model"
	"thousands-prices/request"
	"thousands-prices/service"
	"yz-go/component/log"
	request2 "yz-go/request"
	yzResponse "yz-go/response"
	service2 "yz-go/service"
	"yz-go/source"
)

func GetThousandsPricesList(c *gin.Context) {
	var pageInfo request.ThousandsPricesSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list, total := service.GetThousandsPricesList(pageInfo); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
	return
}

func GetThousandsPrices(c *gin.Context) {

	var req request2.GetById
	err := c.ShouldBindQuery(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, brand := service.GetThousandsPrices(req.Id); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(brand, c)
	}
	return
}

func GetThousandsPricesProductCount(c *gin.Context) {

	var req request2.GetById
	err := c.ShouldBindQuery(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, count := service.GetThousandsPricesProductCount(req.Id); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"count": count}, c)
	}
	return
}
func CreateThousandsPrices(c *gin.Context) {

	var req model.ThousandsPrices
	err := c.ShouldBindJSON(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.CreateThousandsPrices(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 10, c.ClientIP(), "新增了商品池:'"+req.Name+"'")
		yzResponse.OkWithMessage("创建成功", c)
	}
	return
}

func UpdateThousandsPrices(c *gin.Context) {

	var req model.ThousandsPrices
	err := c.ShouldBindJSON(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var oldThousandsPrices model.ThousandsPrices
	err = source.DB().Where("id = ?", req.ID).First(&oldThousandsPrices).Error
	if err != nil {
		yzResponse.FailWithMessage("商品池不存在"+err.Error(), c)
		return
	}
	if err = service.UpdateThousandsPrices(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		var recordData = make(map[string]interface{})
		recordData["new_data"] = req
		recordData["old_data"] = req
		recordDataJson, err := json.Marshal(recordData)
		if err != nil {
			log.Log().Error("商品池导出日志错误!", zap.Any("err", err))
			yzResponse.FailWithMessage("日记记录错误"+err.Error(), c)
			return
		}
		service2.CreateOperationRecord(v1.GetUserID(c), 10, c.ClientIP(), "编辑了商品池:'"+strconv.Itoa(int(req.ID))+"'", string(recordDataJson))

		yzResponse.OkWithMessage("创建成功", c)
	}
	return
}

func CreateThousandsPricesUsers(c *gin.Context) {
	var req request.ThousandsPricesUserCreate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.CreateThousandsPricesUsers(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
	return
}

func DeleteThousandsPricesUsers(c *gin.Context) {
	var req request.ThousandsPricesUserCreate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteThousandsPricesUsers(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("新建成功", c)
	}
	return
}
func DeleteThousandsPrices(c *gin.Context) {
	var req request.ThousandsPricesUserCreate
	err := c.ShouldBindQuery(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteThousandsPrices(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 10, c.ClientIP(), "删除了商品池ID:'"+strconv.Itoa(int(req.ID))+"'")
		yzResponse.OkWithMessage("删除成功", c)
	}
	return
}

func Copy(c *gin.Context) {
	var req request.ThousandsPricesUserCreate
	err := c.ShouldBindQuery(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, newThousandsPrice := service.Copy(req.ID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 10, c.ClientIP(), "复制商品池Id：'"+strconv.Itoa(int(req.ID))+"',复制之后ID:"+strconv.Itoa(int(newThousandsPrice.ID)))
		yzResponse.OkWithMessage("复制成功", c)
	}
	return
}

func UpdatePrice(c *gin.Context) {
	var req request.ThousandsPricesProductsSearch
	err := c.ShouldBindQuery(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdatePrice(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 10, c.ClientIP(), "编辑了商品池价格'"+strconv.Itoa(int(req.ID))+"'")
		yzResponse.OkWithMessage("复制成功", c)
	}
	return
}

func Export(c *gin.Context) {
	var req request.ThousandsPricesUserCreate
	err := c.ShouldBindQuery(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	req.UserID = v1.GetUserID(c)
	if err = service.ExportThousandPrice(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		//保存导出条件
		var recordData = make(map[string]interface{})
		recordData["data"] = req
		recordDataJson, err := json.Marshal(recordData)
		if err != nil {
			log.Log().Error("商品池导出日志错误!", zap.Any("err", err))
			yzResponse.FailWithMessage("日记记录错误"+err.Error(), c)
			return
		}
		service2.CreateOperationRecord(v1.GetUserID(c), 10, c.ClientIP(), "执行导出商品池id:'"+strconv.Itoa(int(req.ID))+"'", string(recordDataJson))

		yzResponse.OkWithMessage("导出进行中,请于导出列表查看导出状态", c)
	}
	return
}
func ExportThousandsPricesRecordList(c *gin.Context) {
	var pageInfo request.ThousandsPricesRecordRequest
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, total, data := service.GetThousandPriceExportRecordList(pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     data,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func DeleteProductExportRecord(c *gin.Context) {
	var application model.ThousandsPricesExportRecord
	err := c.ShouldBindJSON(&application)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteThousandPriceExportRecord(application); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}
func SetAttributeStatus(c *gin.Context) {
	var req request3.ColumnStatus
	err := c.ShouldBindQuery(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, enable := service.SetAttributeStatus(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		var statusName = "关闭"
		if req.Status == 1 {
			statusName = "开启"
		}
		service2.CreateOperationRecord(v1.GetUserID(c), 10, c.ClientIP(), "变更商品池:"+strconv.Itoa(req.ID)+"状态:'"+statusName+"'")

		yzResponse.OkWithData(gin.H{"status": enable}, c)
	}
	return
}
