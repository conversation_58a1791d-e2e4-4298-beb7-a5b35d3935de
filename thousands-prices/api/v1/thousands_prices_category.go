package v1

import (
	"encoding/json"
	v1 "gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
	"thousands-prices/model"
	"thousands-prices/request"
	"thousands-prices/service"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	service2 "yz-go/service"
	"yz-go/source"
)

func GetThousandsPricesCategoryList(c *gin.Context) {
	var pageInfo request.ThousandsPricesCategorySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list, total := service.GetThousandsPricesCategoryList(pageInfo); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
	return
}

func CreateThousandsPricesCategory(c *gin.Context) {
	var req request.ThousandsPricesCategoryCreate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.CreateThousandsPricesCategory(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		var recordData = make(map[string]interface{})
		recordData["data"] = req
		recordDataJson, err := json.Marshal(recordData)
		if err != nil {
			log.Log().Error("商品池创建分类定价策略日志错误!", zap.Any("err", err))
			yzResponse.FailWithMessage("日记记录错误"+err.Error(), c)
			return
		}
		service2.CreateOperationRecord(v1.GetUserID(c), 10, c.ClientIP(), "商品池id:'"+strconv.Itoa(int(req.Data[0].ThousandsPricesID))+"',创建了分类定价策略", string(recordDataJson))

		yzResponse.OkWithMessage("创建成功", c)
	}
	return
}

func UpdateThousandsPricesCategory(c *gin.Context) {
	var req model.ThousandsPricesCategory
	err := c.ShouldBindJSON(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var oldThousandsPricesCategory model.ThousandsPricesCategory
	err = source.DB().Where("id = ?", req.ID).First(&oldThousandsPricesCategory).Error
	if err != nil {
		yzResponse.FailWithMessage("分类定价策略不存在"+err.Error(), c)
	}
	if err = service.UpdateThousandsPricesCategory(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		var recordData = make(map[string]interface{})
		recordData["new_data"] = req
		recordData["old_data"] = oldThousandsPricesCategory
		recordDataJson, err := json.Marshal(recordData)
		if err != nil {
			log.Log().Error("商品池创建分类定价策略日志错误!", zap.Any("err", err))
			yzResponse.FailWithMessage("日记记录错误"+err.Error(), c)
			return
		}
		service2.CreateOperationRecord(v1.GetUserID(c), 10, c.ClientIP(), "商品池分类定价策略id:'"+strconv.Itoa(int(req.ID))+"',修改了定价策略", string(recordDataJson))

		yzResponse.OkWithMessage("修改成功", c)
	}
	return
}

func DeleteThousandsPricesCategory(c *gin.Context) {
	var req request.ThousandsPricesCategorySearch
	err := c.ShouldBindQuery(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteThousandsPricesCategory(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 10, c.ClientIP(), "删除了商品池分类定价策略分类id:'"+strconv.Itoa(int(req.ID))+"'")

		yzResponse.OkWithMessage("新建成功", c)
	}
	return
}
