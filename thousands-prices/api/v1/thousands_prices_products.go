package v1

import (
	"encoding/json"
	v1 "gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
	"thousands-prices/model"
	"thousands-prices/request"
	"thousands-prices/service"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	service2 "yz-go/service"
	"yz-go/source"
)

func GetThousandsPricesProducts(c *gin.Context) {
	var pageInfo request.ThousandsPricesProductsSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list, total := service.GetThousandsPricesProducts(pageInfo); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
	return

}
func GetThousandsPricesProductsDetail(c *gin.Context) {
	var req request.ThousandsPricesProductsSearch
	err := c.ShouldBindQuery(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, brand := service.GetThousandsPricesProductsDetail(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(brand, c)
	}
	return
}
func UpdateThousandsPricesProductsDetail(c *gin.Context) {
	var req request.ThousandsPricesProductsUpdate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var oldThousandsPricesProducts model.ThousandsPricesProducts
	err = source.DB().Where("id = ?", req.Data[0].ID).First(&oldThousandsPricesProducts).Error
	if err != nil {
		yzResponse.FailWithMessage("商品定价策略不存在"+err.Error(), c)
	}
	if err := service.UpdateThousandsPricesProductsDetail(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		var recordData = make(map[string]interface{})
		recordData["old_data"] = oldThousandsPricesProducts
		recordData["new_data"] = req.Data[0]
		recordDataJson, err := json.Marshal(recordData)
		if err != nil {
			log.Log().Error("商品池修改日志记录错误!", zap.Any("err", err))
			yzResponse.FailWithMessage("日记记录错误"+err.Error(), c)
			return
		}
		service2.CreateOperationRecord(v1.GetUserID(c), 10, c.ClientIP(), "商品池id:'"+strconv.Itoa(int(req.Data[0].ThousandsPricesID))+"'修改商品id:"+strconv.Itoa(int(req.Data[0].ProductID)), string(recordDataJson))
		yzResponse.OkWithMessage("修改成功", c)
	}
	return
}
func CreateThousandsPricesProducts(c *gin.Context) {
	var req request.ThousandsPricesProductsCreate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.CreateThousandsPricesProducts(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		var recordData = make(map[string]interface{})
		recordData["data"] = req
		recordDataJson, err := json.Marshal(recordData)
		if err != nil {
			log.Log().Error("商品池导出日志错误!", zap.Any("err", err))
			yzResponse.FailWithMessage("日记记录错误"+err.Error(), c)
			return
		}
		service2.CreateOperationRecord(v1.GetUserID(c), 10, c.ClientIP(), "商品池id:'"+strconv.Itoa(int(req.ID))+"'添加商品", string(recordDataJson))

		yzResponse.OkWithMessage("新建成功", c)
	}
	return
}

func DeleteThousandsPricesProducts(c *gin.Context) {
	var req request.ThousandsPricesProductsSearch
	err := c.ShouldBindJSON(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteThousandsPricesProducts(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		var productIds string
		for _, item := range req.ProductIds {
			productIds += "," + strconv.Itoa(int(item))
		}
		var recordData = make(map[string]interface{})
		recordData["data"] = req
		recordDataJson, err := json.Marshal(recordData)
		if err != nil {
			log.Log().Error("商品池导出日志错误!", zap.Any("err", err))
			yzResponse.FailWithMessage("日记记录错误"+err.Error(), c)
			return
		}
		service2.CreateOperationRecord(v1.GetUserID(c), 10, c.ClientIP(), "删除了商品池id:'"+strconv.Itoa(int(req.ID))+"'的商品id:"+productIds, string(recordDataJson))

		yzResponse.OkWithMessage("新建成功", c)
	}
	return
}
func UpdateAllThousandsPricesProducts(c *gin.Context) {
	var req request.ThousandsPricesProductsSearch
	err := c.ShouldBindJSON(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateAllThousandsPricesProductsSync(req.ID, req.Column); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		if req.Column == "" {
			req.Column = "全部"
		}
		service2.CreateOperationRecord(v1.GetUserID(c), 10, c.ClientIP(), "批量编辑了商品池价格'"+strconv.Itoa(int(req.ID))+"'，字段："+req.Column)
		yzResponse.OkWithMessage("更新成功", c)
	}
	return
}
