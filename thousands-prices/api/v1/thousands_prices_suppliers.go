package v1

import (
	"encoding/json"
	v1 "gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
	"thousands-prices/model"
	"thousands-prices/request"
	"thousands-prices/service"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	service2 "yz-go/service"
	"yz-go/source"
)

func GetThousandsPricesSuppliers(c *gin.Context) {
	var pageInfo request.ThousandsPricesSupplierSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list, total := service.GetThousandsPricesSuppliers(pageInfo); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func CreateThousandsPricesSuppliers(c *gin.Context) {
	var req request.ThousandsPricesSuppliersCreate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.CreateThousandsPricesSuppliers(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		recordData := map[string]interface{}{"data": req}
		recordDataJson, err := json.Marshal(recordData)
		if err != nil {
			log.Log().Error("商品池创建供应商定价策略日志错误!", zap.Any("err", err))
			yzResponse.FailWithMessage("日记记录错误"+err.Error(), c)
			return
		}
		service2.CreateOperationRecord(v1.GetUserID(c), 10, c.ClientIP(), "商品池id:'"+strconv.Itoa(int(req.ID))+"',创建了供应商定价策略", string(recordDataJson))
		yzResponse.OkWithMessage("创建成功", c)
	}
	return
}

func UpdateThousandsPricesSuppliers(c *gin.Context) {
	var req model.ThousandsPricesSuppliers
	err := c.ShouldBindJSON(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var oldData model.ThousandsPricesSuppliers
	if err = source.DB().Where("id = ?", req.ID).First(&oldData).Error; err != nil {
		yzResponse.FailWithMessage("供应商定价策略不存在"+err.Error(), c)
		return
	}
	if err = service.UpdateThousandsPricesSuppliers(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		recordData := map[string]interface{}{"new_data": req, "old_data": oldData}
		recordDataJson, err := json.Marshal(recordData)
		if err != nil {
			log.Log().Error("商品池修改供应商定价策略日志错误!", zap.Any("err", err))
			yzResponse.FailWithMessage("日记记录错误"+err.Error(), c)
			return
		}
		service2.CreateOperationRecord(v1.GetUserID(c), 10, c.ClientIP(), "商品池供应商定价策略id:'"+strconv.Itoa(int(req.ID))+"',修改了定价策略", string(recordDataJson))

		yzResponse.OkWithMessage("修改成功", c)
	}
	return
}

func DeleteThousandsPricesSupplier(c *gin.Context) {
	var req request.ThousandsPricesSupplierSearch
	err := c.ShouldBindQuery(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.DeleteThousandsPricesSuppliers(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 10, c.ClientIP(), "删除了商品池供应商定价策略分类id:'"+strconv.Itoa(int(req.ID))+"'")
		yzResponse.OkWithMessage("删除成功", c)
	}
	return
}
