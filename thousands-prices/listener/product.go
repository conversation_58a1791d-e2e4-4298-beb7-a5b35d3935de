package listener

import (
	ProductModel "product/model"
	"product/mq"
	"product/service"
	"thousands-prices/model"
	"thousands-prices/response"
	"yz-go/source"
)

func PushProductDeleteHandles() {
	mq.PushHandles("productQThousandsPrice", 10, func(product mq.ProductMessage) error {

		//接收商品变更的队列消息 获得产品id和消息类型，从而获得产品数据
		switch product.MessageType {

		case mq.Delete:
			source.DB().Where("product_id = ?", product.ProductID).Delete(&service.ThousandsPricesProducts{})
			break
		case mq.Edit:
			var thousandsProducts []service.ThousandsPricesProducts
			source.DB().Where("product_id = ?", product.ProductID).Find(&thousandsProducts)
			var productModel ProductModel.Product
			source.DB().Preload("Skus").Where("id = ?", product.ProductID).First(&productModel)
			var thousandPriceMap = make(map[uint][]service.ThousandsPricesProducts)
			for _, tp := range thousandsProducts {
				thousandPriceMap[tp.ThousandsPricesID] = append(thousandPriceMap[tp.ThousandsPricesID], tp)
			}
			for tpId, tpProducts := range thousandPriceMap {
				// 判断 Skus 中的 ID 是否在 thousandsProducts 中
				skuIDMap := make(map[uint]bool)
				for _, tp := range tpProducts {
					skuIDMap[tp.SkuID] = true
				}

				for _, sku := range productModel.Skus {
					if !skuIDMap[sku.ID] {
						// SKU ID 不存在于 thousandsProducts 中，插入到 MissingSkus 表中
						source.DB().Where("product_id = ? and sku_id = ? and status = 0 and tp_id = ?", product.ProductID, sku.ID, tpId).FirstOrCreate(&model.MissingSkus{
							TpID:         tpId,
							ProductID:    product.ProductID,
							ProductTitle: productModel.Title,
							SkuID:        sku.ID,
							SkuTitle:     sku.Title,
						})
					}
				}
				UpdateThousandsPricesProducts(product.ProductID, tpId)
			}

			break
		}

		return nil
	})
}
func UpdateThousandsPricesProducts(id uint, tpId uint) {
	var productModel ProductModel.Product
	source.DB().Preload("Skus").Where("id = ?", id).First(&productModel)

	var thousandsProducts []service.ThousandsPricesProducts
	source.DB().Where("product_id = ? and thousands_prices_id = ?", id, tpId).Find(&thousandsProducts)

	var thousandsBase service.ThousandsPrices
	source.DB().Where("id = ?", tpId).Find(&thousandsBase)

	skuIDMap := make(map[uint]bool)
	for _, sku := range productModel.Skus {
		skuIDMap[sku.ID] = true
	}
	var tpSet service.ThousandsPricesProducts
	tskuIDMap := make(map[uint]bool)
	for _, tp := range thousandsProducts {
		tskuIDMap[tp.SkuID] = true
		if !skuIDMap[tp.SkuID] {
			// SKU 已删除，移除 ThousandsPricesProducts 中的记录
			source.DB().Delete(&tp)
		} else {
			tpSet = tp
		}
	}
	var tps []response.ThousandsPricesSuppliers
	err := source.DB().Where("thousands_prices_id = ?", tpId).Find(&tps).Error
	if err != nil {
		return
	}
	var tpsMap = make(map[uint]uint)
	var tpsGuideMap = make(map[uint]uint)
	var tpsOriginMap = make(map[uint]uint)
	for _, tpsItem := range tps {
		tpsMap[tpsItem.SupplierID] = tpsItem.Ratio
		tpsGuideMap[tpsItem.SupplierID] = tpsItem.GuideRatio
		tpsOriginMap[tpsItem.SupplierID] = tpsItem.OriginRatio
	}
	var tpc []response.ThousandsPricesCategory
	err = source.DB().Where("thousands_prices_id = ?", tpId).Find(&tpc).Error
	if err != nil {
		return
	}
	var tpcMap = make(map[uint]uint)
	var tpcGuideMap = make(map[uint]uint)
	var tpcOriginMap = make(map[uint]uint)
	for _, tpcItem := range tpc {
		tpcMap[tpcItem.Category3ID] = tpcItem.Ratio
		tpcGuideMap[tpcItem.Category3ID] = tpcItem.GuideRatio
		tpcOriginMap[tpcItem.Category3ID] = tpcItem.OriginRatio
	}

	for _, sku := range productModel.Skus {
		if _, exists := tskuIDMap[sku.ID]; !exists {
			var ratioPrice, ratioSkuPrice, ratioGuidePrice, ratioSkuGuidePrice, ratioOriginPrice, ratioSkuOriginPrice uint
			var ratio, guideRatio, originRatio uint
			if tpSet.Strategy == 1 {
				ratioPrice = productModel.CostPrice * thousandsBase.UnifyRatio / 100
				ratioSkuPrice = sku.CostPrice * thousandsBase.UnifyRatio / 100
				ratio = thousandsBase.UnifyRatio

				ratioGuidePrice = productModel.GuidePrice * thousandsBase.GuideRatio / 100
				ratioSkuGuidePrice = sku.GuidePrice * thousandsBase.GuideRatio / 100
				guideRatio = thousandsBase.GuideRatio

				ratioOriginPrice = productModel.OriginPrice * thousandsBase.OriginRatio / 100
				ratioSkuOriginPrice = sku.OriginPrice * thousandsBase.OriginRatio / 100
				originRatio = thousandsBase.OriginRatio
			} else if tpSet.Strategy == 2 {
				if _, ok := tpsMap[productModel.SupplierID]; ok {
					ratioPrice = productModel.CostPrice * tpsMap[productModel.SupplierID] / 100
					ratioSkuPrice = sku.CostPrice * tpsMap[productModel.SupplierID] / 100
					ratio = tpsMap[productModel.SupplierID]

					ratioGuidePrice = productModel.GuidePrice * tpsGuideMap[productModel.SupplierID] / 100
					ratioSkuGuidePrice = sku.GuidePrice * tpsGuideMap[productModel.SupplierID] / 100
					guideRatio = tpsGuideMap[productModel.SupplierID]

					ratioOriginPrice = productModel.OriginPrice * tpsOriginMap[productModel.SupplierID] / 100
					ratioSkuOriginPrice = sku.OriginPrice * tpsOriginMap[productModel.SupplierID] / 100
					originRatio = tpsOriginMap[productModel.SupplierID]

				}
			} else if tpSet.Strategy == 3 {
				if _, ok := tpcMap[productModel.Category3ID]; ok {
					ratioPrice = productModel.CostPrice * tpcMap[productModel.Category3ID] / 100
					ratioSkuPrice = sku.CostPrice * tpcMap[productModel.Category3ID] / 100
					ratio = tpcMap[productModel.Category3ID]

					ratioGuidePrice = productModel.GuidePrice * tpcGuideMap[productModel.Category3ID] / 100
					ratioSkuGuidePrice = sku.GuidePrice * tpcGuideMap[productModel.Category3ID] / 100
					guideRatio = tpcGuideMap[productModel.Category3ID]

					ratioOriginPrice = productModel.OriginPrice * tpcOriginMap[productModel.Category3ID] / 100
					ratioSkuOriginPrice = sku.OriginPrice * tpcOriginMap[productModel.Category3ID] / 100
					originRatio = tpcOriginMap[productModel.Category3ID]
				}
			}

			newTP := service.ThousandsPricesProducts{
				ThousandsPricesID:   tpId,
				ProductID:           productModel.ID,
				SkuID:               sku.ID,
				Price:               ratioPrice,
				SkuPrice:            ratioSkuPrice,
				GuidePrice:          ratioGuidePrice,
				SkuGuidePrice:       ratioSkuGuidePrice,
				OriginPrice:         ratioOriginPrice,
				SkuOriginPrice:      ratioSkuOriginPrice,
				Strategy:            tpSet.Strategy,
				StrategyRatio:       ratio,
				GuideStrategyRatio:  guideRatio,
				OriginStrategyRatio: originRatio,
			}
			source.DB().Create(&newTP)
		}
	}
}
