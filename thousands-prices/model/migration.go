package model

import (
	"encoding/json"
	"gin-vue-admin/admin/model"
	"yz-go/source"

	_ "embed"
)

//go:embed menu.json
var menu string

func Migrate() (err error) {
	err = source.DB().AutoMigrate(
		ThousandsPrices{},
		ThousandsPricesProducts{},
		ThousandsPricesSuppliers{},
		ThousandsPricesCategory{},
		ThousandsPricesExportRecord{},
		MissingSkus{},
	)

	if err != nil {
		return
	}
	menus := []model.SysMenu{}

	menuJson := menu
	json.Unmarshal([]byte(menuJson), &menus)
	model.GVA_MENUS = append(model.GVA_MENUS, menus...)

	return
}
