package model

import "yz-go/source"

type ThousandsPrices struct {
	source.Model
	Name        string `json:"name" form:"name"`
	Status      int    `json:"status"`
	IsFee       int    `json:"is_fee" gorm:"column:is_fee;default:0"` //是否包含技术服务费，0不包含 1包含
	UnifyRatio  uint   `json:"unify_ratio"`
	GuideRatio  uint   `json:"guide_ratio" form:"guide_ratio"`
	OriginRatio uint   `json:"origin_ratio" form:"origin_ratio"`
}

type ThousandsPricesProducts struct {
	source.Model
	ThousandsPricesID   uint `json:"thousands_prices_id"`
	ProductID           uint `json:"product_id"`
	SkuID               uint `json:"sku_id"`
	Price               uint `json:"price"`
	SkuPrice            uint `json:"sku_price"`
	GuidePrice          uint `json:"guide_price"`
	SkuGuidePrice       uint `json:"sku_guide_price"`
	OriginPrice         uint `json:"origin_price"`
	SkuOriginPrice      uint `json:"sku_origin_price"`
	Strategy            uint `json:"strategy"`              //定价策略方式 1统一定价策略  2供应商定价策略  3分类
	StrategyRatio       uint `json:"strategy_ratio"`        //定价系数
	GuideStrategyRatio  uint `json:"guide_strategy_ratio"`  //定价系数
	OriginStrategyRatio uint `json:"origin_strategy_ratio"` //定价系数
}

type ThousandsPricesSuppliers struct {
	source.Model
	ThousandsPricesID uint `json:"thousands_prices_id"`
	SupplierID        uint `json:"supplier_id" form:"supplier_id"`
	Ratio             uint `json:"ratio"`
	GuideRatio        uint `json:"guide_ratio" form:"guide_ratio"`
	OriginRatio       uint `json:"origin_ratio" form:"origin_ratio"`
}

type ThousandsPricesCategory struct {
	source.Model
	ThousandsPricesID uint `json:"thousands_prices_id"`
	Category1ID       uint `json:"category_1_id" form:"category_1_id"`
	Category2ID       uint `json:"category_2_id" form:"category_2_id"`
	Category3ID       uint `json:"category_3_id" form:"category_3_id"`
	Ratio             uint `json:"ratio" form:"ratio"`
	GuideRatio        uint `json:"guide_ratio" form:"guide_ratio"`
	OriginRatio       uint `json:"origin_ratio" form:"origin_ratio"`
}
type ThousandsPricesExportRecord struct {
	source.Model
	Link              string `json:"link"`
	ThousandsPricesID uint   `json:"thousands_prices_id"`
	ProductCount      int64  `json:"product_count"`
	StatusString      string `json:"status_string"`
	SysUserID         uint   `json:"sys_user_id"`
}

// 定义新表结构
type MissingSkus struct {
	source.Model
	TpID         uint   `json:"tp_id"`
	ProductID    uint   `json:"product_id"`
	ProductTitle string `json:"product_title"`
	SkuID        uint   `json:"sku_id"`
	SkuTitle     string `json:"sku_title"`
	Status       uint   `json:"status" gorm:"default:0;"` //1已读
}
