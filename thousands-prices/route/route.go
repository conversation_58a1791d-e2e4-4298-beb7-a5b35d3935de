package route

import (
	"github.com/gin-gonic/gin"
	v1 "thousands-prices/api/v1"
)

func InitAdminPrivateRouter(Router *gin.RouterGroup) {
	VideoRoute := Router.Group("thousandsPrices")
	{
		// 基础设置
		VideoRoute.GET("list", v1.GetThousandsPricesList)
		VideoRoute.GET("detail", v1.GetThousandsPrices)
		VideoRoute.GET("productCount", v1.GetThousandsPricesProductCount)
		VideoRoute.POST("create", v1.CreateThousandsPrices)
		VideoRoute.POST("update", v1.UpdateThousandsPrices)
		VideoRoute.GET("delete", v1.DeleteThousandsPrices)
		VideoRoute.GET("display", v1.SetAttributeStatus)
		VideoRoute.GET("export", v1.Export)
		VideoRoute.GET("exportThousandsPricesRecordList", v1.ExportThousandsPricesRecordList)
		VideoRoute.DELETE("deleteThousandPriceExportRecord", v1.DeleteProductExportRecord)
		VideoRoute.GET("copy", v1.Copy)
		VideoRoute.GET("updatePrice", v1.UpdatePrice)

		VideoRoute.POST("user/create", v1.CreateThousandsPricesUsers)
		VideoRoute.POST("user/delete", v1.DeleteThousandsPricesUsers)

		VideoRoute.POST("category/create", v1.CreateThousandsPricesCategory)
		VideoRoute.POST("category/update", v1.UpdateThousandsPricesCategory)
		VideoRoute.GET("category/list", v1.GetThousandsPricesCategoryList)
		VideoRoute.GET("category/delete", v1.DeleteThousandsPricesCategory)

		VideoRoute.POST("supplier/create", v1.CreateThousandsPricesSuppliers)
		VideoRoute.POST("supplier/update", v1.UpdateThousandsPricesSuppliers)
		VideoRoute.GET("supplier/list", v1.GetThousandsPricesSuppliers)
		VideoRoute.GET("supplier/delete", v1.DeleteThousandsPricesSupplier)

		VideoRoute.POST("product/create", v1.CreateThousandsPricesProducts)
		VideoRoute.POST("product/updateAll", v1.UpdateAllThousandsPricesProducts)
		VideoRoute.POST("product/update", v1.UpdateThousandsPricesProductsDetail)
		VideoRoute.GET("product/list", v1.GetThousandsPricesProducts)
		VideoRoute.GET("product/detail", v1.GetThousandsPricesProductsDetail)
		VideoRoute.POST("product/delete", v1.DeleteThousandsPricesProducts)

		VideoRoute.POST("missSkus/list", v1.GetMissingSkusList)
		VideoRoute.POST("missSkus/mark", v1.SetMissingSkusStatus)
	}
}
