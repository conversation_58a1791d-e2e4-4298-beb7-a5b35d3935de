package service

import (
	"errors"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"gorm.io/gorm"
	"os"
	model2 "product/model"
	request2 "product/request"
	"product/service"
	"strconv"
	"thousands-prices/model"
	"thousands-prices/request"
	"thousands-prices/response"
	"time"
	"yz-go/config"
	request3 "yz-go/request"
	service2 "yz-go/service"
	"yz-go/source"
	"yz-go/utils"
)

func GetThousandsPricesList(info request.ThousandsPricesSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&response.ThousandsPrices{})
	var applications []response.ThousandsPrices
	// 如果有条件搜索 下方会自动创建搜索语句

	if info.Name != "" {
		db.Where("name like ?", "%"+info.Name+"%")
	}
	if info.UserName != "" {
		var thousandIds []uint
		err = source.DB().Model(&response.User{}).Where("username like ?", "%"+info.UserName+"%").Pluck("thousands_prices_id", &thousandIds).Error
		if err != nil {
			return
		}
		db.Where("id in ?", thousandIds)
	}
	if info.Status != nil {
		db.Where("status = ?", info.Status)
	}
	if info.StartTime != "" {
		db.Where("created_at >= ?", info.StartTime)

	}
	if info.EndTime != "" {
		db.Where("created_at <= ?", info.EndTime)

	}
	err = db.Count(&total).Error
	//err = db.Order("created_at desc").Preload("Products").Preload("Users").Limit(limit).Offset(offset).Find(&applications).Error
	err = db.Order("created_at desc").Preload("Users").Limit(limit).Offset(offset).Find(&applications).Error
	//for k, item := range applications {
	//var productIds []uint
	//err = source.DB().Model(&model.ThousandsPricesProducts{}).Where("thousands_prices_id = ?", item.ID).Pluck("product_id", &productIds).Error
	//if err != nil {
	//	return
	//}
	//err = source.DB().Model(&model2.ProductModel{}).Where("id in ?", productIds).Count(&applications[k].ProductCount).Error
	//if err != nil {
	//	return
	//}
	//source.DB().Model(&model.ThousandsPricesProducts{}).Where("thousands_prices_id = ?", item.ID).Count(&applications[k].ProductCount)

	//}

	//for k, item := range applications {
	//	var productCount = map[uint]uint{}
	//	for _, product := range item.Products {
	//		productCount[product.ID] = product.ID
	//	}
	//	applications[k].ProductCount = len(productCount)
	//}
	return err, applications, total
}

func GetThousandsPrices(id uint) (err error, data response.ThousandsPrices) {

	// 创建db
	db := source.DB().Model(&response.ThousandsPrices{})

	err = db.Preload("Users.Application").First(&data, id).Error

	return
}

func GetThousandsPricesProductCount(id uint) (err error, count int64) {

	// 创建db
	var productIds []uint
	err = source.DB().Model(&service.ThousandsPricesProducts{}).Where("thousands_prices_id = ?", id).Pluck("product_id", &productIds).Error
	if err != nil {
		return
	}
	err = source.DB().Model(&model2.Product{}).Where("id in ?", productIds).Count(&count).Error

	return
}

func CreateThousandsPrices(info model.ThousandsPrices) (err error) {
	err = source.DB().Create(&info).Error
	if err != nil {
		return
	}
	return
}

func UpdateThousandsPrices(info model.ThousandsPrices) (err error) {
	err = source.DB().Omit("created_at").Save(&info).Error
	if err != nil {
		return
	}
	return
}

func CreateThousandsPricesUsers(info request.ThousandsPricesUserCreate) (err error) {
	err = source.DB().Model(response.User{}).Where("id in ?", info.UserIds).Update("thousands_prices_id", info.ID).Error
	if err != nil {
		return
	}
	return
}

func DeleteThousandsPricesUsers(info request.ThousandsPricesUserCreate) (err error) {
	err = source.DB().Model(response.User{}).Where("id = ?", info.UserID).Update("thousands_prices_id", 0).Error
	if err != nil {
		return
	}
	return
}

func DeleteThousandsPrices(info request.ThousandsPricesUserCreate) (err error) {
	err = source.DB().Where("id = ?", info.ID).Delete(&model.ThousandsPrices{}).Error
	if err != nil {
		return
	}
	return
}

func SetAttributeStatus(columnStatus request2.ColumnStatus) (err error, enable int) {
	var product model.ThousandsPrices
	enable = 1
	err = source.DB().Model(&product).Where("`id` = ?", columnStatus.ID).First(&product).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return err, enable
	} else {

		if product.Status == 1 {
			enable = 0
		}
		err = source.DB().Model(&product).Update("status", enable).Error

	}
	return err, enable
}
func UpdatePrice(data request.ThousandsPricesProductsSearch) (err error) {

	var thousandproducts []model.ThousandsPricesProducts
	err = source.DB().Select("id", "product_id", "sku_id", "strategy").Model(&model.ThousandsPricesProducts{}).Where("`thousands_prices_id` = ?", data.ID).Find(&thousandproducts).Error
	if err != nil {
		return
	}
	var productIds []uint
	var typeMap = make(map[uint]uint)
	for _, tp := range thousandproducts {
		productIds = append(productIds, tp.ProductID)
		typeMap[tp.SkuID] = tp.Strategy
	}
	var products []model2.Product
	err = source.DB().Preload("Skus").Where("id in ?", productIds).Find(&products).Error
	if err != nil {
		return
	}
	var tp response.ThousandsPrices
	err = source.DB().First(&tp, data.ID).Error
	if err != nil {
		return
	}

	var tps []response.ThousandsPricesSuppliers
	err = source.DB().Where("thousands_prices_id = ?", data.ID).Find(&tps).Error
	if err != nil {
		return
	}
	var tpsMap = make(map[uint]uint)
	var tpsGuideMap = make(map[uint]uint)
	var tpsOriginMap = make(map[uint]uint)
	for _, tpsItem := range tps {
		tpsMap[tpsItem.SupplierID] = tpsItem.Ratio
		tpsGuideMap[tpsItem.SupplierID] = tpsItem.GuideRatio
		tpsOriginMap[tpsItem.SupplierID] = tpsItem.OriginRatio
	}
	var tpc []response.ThousandsPricesCategory
	err = source.DB().Where("thousands_prices_id = ?", data.ID).Find(&tpc).Error
	if err != nil {
		return
	}
	var tpcMap = make(map[uint]uint)
	var tpcGuideMap = make(map[uint]uint)
	var tpcOriginMap = make(map[uint]uint)
	for _, tpcItem := range tpc {
		tpcMap[tpcItem.Category3ID] = tpcItem.Ratio
		tpcGuideMap[tpcItem.Category3ID] = tpcItem.GuideRatio
		tpcOriginMap[tpcItem.Category3ID] = tpcItem.OriginRatio
	}
	var thousandsPrice []response.ThousandsPricesProducts
	for _, product := range products {
		for _, sku := range product.Skus {
			if _, ok := typeMap[sku.ID]; !ok {
				continue
			}
			var ratioPrice, ratioSkuPrice, ratioGuidePrice, ratioSkuGuidePrice, ratioOriginPrice, ratioSkuOriginPrice uint
			var ratio, guideRatio, originRatio uint
			if typeMap[sku.ID] == 1 {
				ratioPrice = product.CostPrice * tp.UnifyRatio / 100
				ratioSkuPrice = sku.CostPrice * tp.UnifyRatio / 100
				ratio = tp.UnifyRatio

				ratioGuidePrice = product.GuidePrice * tp.GuideRatio / 100
				ratioSkuGuidePrice = sku.GuidePrice * tp.GuideRatio / 100
				guideRatio = tp.GuideRatio

				ratioOriginPrice = product.OriginPrice * tp.OriginRatio / 100
				ratioSkuOriginPrice = sku.OriginPrice * tp.OriginRatio / 100
				originRatio = tp.OriginRatio
			} else if typeMap[sku.ID] == 2 {
				if _, ok := tpsMap[product.SupplierID]; ok {
					ratioPrice = product.CostPrice * tpsMap[product.SupplierID] / 100
					ratioSkuPrice = sku.CostPrice * tpsMap[product.SupplierID] / 100
					ratio = tpsMap[product.SupplierID]

					ratioGuidePrice = product.GuidePrice * tpsGuideMap[product.SupplierID] / 100
					ratioSkuGuidePrice = sku.GuidePrice * tpsGuideMap[product.SupplierID] / 100
					guideRatio = tpsGuideMap[product.SupplierID]

					ratioOriginPrice = product.OriginPrice * tpsOriginMap[product.SupplierID] / 100
					ratioSkuOriginPrice = sku.OriginPrice * tpsOriginMap[product.SupplierID] / 100
					originRatio = tpsOriginMap[product.SupplierID]

				}
			} else if typeMap[sku.ID] == 3 {
				if _, ok := tpcMap[product.Category3ID]; ok {
					ratioPrice = product.CostPrice * tpcMap[product.Category3ID] / 100
					ratioSkuPrice = sku.CostPrice * tpcMap[product.Category3ID] / 100
					ratio = tpcMap[product.Category3ID]

					ratioGuidePrice = product.GuidePrice * tpcGuideMap[product.Category3ID] / 100
					ratioSkuGuidePrice = sku.GuidePrice * tpcGuideMap[product.Category3ID] / 100
					guideRatio = tpcGuideMap[product.Category3ID]

					ratioOriginPrice = product.OriginPrice * tpcOriginMap[product.Category3ID] / 100
					ratioSkuOriginPrice = sku.OriginPrice * tpcOriginMap[product.Category3ID] / 100
					originRatio = tpcOriginMap[product.Category3ID]
				}
			}
			thousandsPrice = append(thousandsPrice, response.ThousandsPricesProducts{
				ThousandsPricesID:   data.ID,
				ProductID:           product.ID,
				SkuID:               sku.ID,
				Price:               ratioPrice,
				SkuPrice:            ratioSkuPrice,
				GuidePrice:          ratioGuidePrice,
				SkuGuidePrice:       ratioSkuGuidePrice,
				OriginPrice:         ratioOriginPrice,
				SkuOriginPrice:      ratioSkuOriginPrice,
				Strategy:            int(typeMap[sku.ID]),
				StrategyRatio:       ratio,
				GuideStrategyRatio:  guideRatio,
				OriginStrategyRatio: originRatio,
			})
		}
	}
	err = source.DB().Unscoped().Where("thousands_prices_id = ?", data.ID).Delete(&response.ThousandsPricesProducts{}).Error
	if err != nil {
		return
	}
	err = source.DB().CreateInBatches(&thousandsPrice, 1000).Error
	return
}
func Copy(id uint) (err error, thousandsPrice model.ThousandsPrices) {
	err = source.DB().First(&thousandsPrice, id).Error
	if err != nil {
		return
	}
	var thousandsPricesProducts []service.ThousandsPricesProducts
	err = source.DB().Where("thousands_prices_id = ?", id).Find(&thousandsPricesProducts).Error
	if err != nil {
		return
	}

	var thousandsPricesSupplier []model.ThousandsPricesSuppliers
	err = source.DB().Where("thousands_prices_id = ?", id).Find(&thousandsPricesSupplier).Error
	if err != nil {
		return
	}

	var thousandsPricesCategory []model.ThousandsPricesCategory
	err = source.DB().Where("thousands_prices_id = ?", id).Find(&thousandsPricesCategory).Error
	if err != nil {
		return
	}

	thousandsPrice.ID = 0
	thousandsPrice.CreatedAt = nil
	thousandsPrice.UpdatedAt = nil
	thousandsPrice.Name = thousandsPrice.Name + "Copy"
	err = source.DB().Create(&thousandsPrice).Error
	if err != nil {
		return
	}
	if len(thousandsPricesProducts) > 0 {
		var newThousandsPriceProducts []service.ThousandsPricesProducts
		for _, tpp := range thousandsPricesProducts {
			tpp.ID = 0
			tpp.CreatedAt = nil
			tpp.UpdatedAt = nil
			tpp.ThousandsPricesID = thousandsPrice.ID
			newThousandsPriceProducts = append(newThousandsPriceProducts, tpp)
		}
		err = source.DB().Create(&newThousandsPriceProducts).Error
		if err != nil {
			return
		}
	}

	if len(thousandsPricesSupplier) > 0 {
		var newThousandsPriceSupplier []model.ThousandsPricesSuppliers
		for _, tps := range thousandsPricesSupplier {
			tps.ID = 0
			tps.CreatedAt = nil
			tps.UpdatedAt = nil
			tps.ThousandsPricesID = thousandsPrice.ID
			newThousandsPriceSupplier = append(newThousandsPriceSupplier, tps)
		}
		err = source.DB().Create(&newThousandsPriceSupplier).Error
		if err != nil {
			return
		}
	}

	if len(thousandsPricesCategory) > 0 {
		var newThousandsPriceCategory []model.ThousandsPricesCategory
		for _, tpc := range thousandsPricesCategory {
			tpc.ID = 0
			tpc.CreatedAt = nil
			tpc.UpdatedAt = nil
			tpc.ThousandsPricesID = thousandsPrice.ID
			newThousandsPriceCategory = append(newThousandsPriceCategory, tpc)
		}
		err = source.DB().Create(&newThousandsPriceCategory).Error
		if err != nil {
			return
		}
	}

	return
}
func ExportThousandPrice(info request.ThousandsPricesUserCreate) (err error) {
	go Export(info)
	return
}
func Export(info request.ThousandsPricesUserCreate) (err error, link string) {

	db := source.DB().Model(&response.ThousandsPricesProducts{}).Preload("Product").Preload("Sku")
	db = db.Where("deleted_at is NULL").Where("thousands_prices_id = ?", info.ID)
	if info.StartTime != "" {
		db = db.Where("created_at >= ?", info.StartTime)
	}
	if info.EndTime != "" {
		db = db.Where("created_at <= ?", info.EndTime)
	}
	var total int64
	err = db.Count(&total).Error
	var productExportRecord model.ThousandsPricesExportRecord
	productExportRecord.ProductCount = total
	productExportRecord.ThousandsPricesID = info.ID
	productExportRecord.StatusString = "导出中"
	if info.UserID > 0 {
		productExportRecord.SysUserID = info.UserID
	}
	err = source.DB().Create(&productExportRecord).Error
	if err != nil {
		return
	}
	excelPage := total/5000 + 1
	var links []string
	timeString := time.Now().Format("20060102150405")
	path := config.Config().Local.Path + "/export_product_pool"
	for ei := 1; ei <= int(excelPage); ei++ {
		f := excelize.NewFile()
		// 创建一个工作表
		index := f.NewSheet("Sheet1")
		// 设置单元格的值
		f.SetCellValue("Sheet1", "A1", "商品id")
		f.SetCellValue("Sheet1", "B1", "商品名称")
		f.SetCellValue("Sheet1", "C1", "供应渠道")
		f.SetCellValue("Sheet1", "D1", "状态")
		f.SetCellValue("Sheet1", "E1", "商品单位")
		f.SetCellValue("Sheet1", "F1", "商品规格标题")
		f.SetCellValue("Sheet1", "G1", "库存")
		f.SetCellValue("Sheet1", "H1", "销量")
		f.SetCellValue("Sheet1", "I1", "指导价")
		f.SetCellValue("Sheet1", "J1", "供货价")
		f.SetCellValue("Sheet1", "K1", "成本价")
		f.SetCellValue("Sheet1", "L1", "建议零售价")
		f.SetCellValue("Sheet1", "M1", "营销价")
		f.SetCellValue("Sheet1", "N1", "商品编码")
		f.SetCellValue("Sheet1", "O1", "商品条码")
		f.SetCellValue("Sheet1", "P1", "重量")
		f.SetCellValue("Sheet1", "Q1", "税收分类编码")
		f.SetCellValue("Sheet1", "R1", "税率")
		f.SetCellValue("Sheet1", "S1", "库存")
		f.SetCellValue("Sheet1", "T1", "分类")
		f.SetCellValue("Sheet1", "U1", "发票名称")
		f.SetCellValue("Sheet1", "V1", "商品属性")
		f.SetCellValue("Sheet1", "W1", "供应商商品分类")
		f.SetCellValue("Sheet1", "X1", "供应商商品来源")
		f.SetCellValue("Sheet1", "Y1", "原始协议价")
		f.SetCellValue("Sheet1", "Z1", "协议价")
		f.SetCellValue("Sheet1", "AA1", "原始指导价")
		f.SetCellValue("Sheet1", "AB1", "指导价")
		f.SetCellValue("Sheet1", "AC1", "原始销售价")
		f.SetCellValue("Sheet1", "AD1", "销售价")
		i := 2
		var tpproducts []response.ThousandsPricesProducts
		err = db.Preload("Product.Supplier").Preload("Product").Preload("Product.SupplierSource").Preload("Product.SupplierSourceCategory").Preload("Product.Brand").Preload("Product.Category1").Preload("Product.Category2").Preload("Product.Category3").Preload("Product.GatherSupply").Order("created_at desc").Limit(5000).Offset(5000 * (ei - 1)).Find(&tpproducts).Error

		for _, tpp := range tpproducts {
			v := tpp.Product
			sku := tpp.Sku
			var statusName = "下架"
			if v.IsDisplay == 1 {
				statusName = "上架"
			}
			f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), v.ID)
			f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), v.Title)
			f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), v.GatherSupply.Name+v.Supplier.Name)
			f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), statusName)
			f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), v.Unit)
			f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), sku.Title)
			f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), sku.Stock)
			f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), v.Sales)
			f.SetCellValue("Sheet1", "I"+strconv.Itoa(i), float64(sku.GuidePrice)/100)
			f.SetCellValue("Sheet1", "J"+strconv.Itoa(i), float64(sku.Price)/100)
			f.SetCellValue("Sheet1", "K"+strconv.Itoa(i), float64(sku.CostPrice)/100)
			f.SetCellValue("Sheet1", "L"+strconv.Itoa(i), float64(sku.OriginPrice)/100)
			f.SetCellValue("Sheet1", "M"+strconv.Itoa(i), float64(sku.ActivityPrice)/100)
			if sku.Sn == "" {
				f.SetCellValue("Sheet1", "N"+strconv.Itoa(i), v.Sn)
			} else {
				f.SetCellValue("Sheet1", "N"+strconv.Itoa(i), sku.Sn)
			}
			if sku.Barcode == "" {
				f.SetCellValue("Sheet1", "O"+strconv.Itoa(i), v.Barcode)
			} else {
				f.SetCellValue("Sheet1", "O"+strconv.Itoa(i), sku.Barcode)
			}
			f.SetCellValue("Sheet1", "P"+strconv.Itoa(i), sku.Weight)
			if v.BillPosition == 1 {
				f.SetCellValue("Sheet1", "Q"+strconv.Itoa(i), v.TaxCode)
				f.SetCellValue("Sheet1", "R"+strconv.Itoa(i), v.TaxRate)
			} else {
				f.SetCellValue("Sheet1", "Q"+strconv.Itoa(i), sku.TaxCode)
				f.SetCellValue("Sheet1", "R"+strconv.Itoa(i), sku.TaxRate)
			}
			f.SetCellValue("Sheet1", "S"+strconv.Itoa(i), sku.Stock)
			var categoryStr string
			categoryStr = fmt.Sprintf("%s-%s-%s", v.Category1.Name, v.Category2.Name, v.Category3.Name)
			f.SetCellValue("Sheet1", "T"+strconv.Itoa(i), categoryStr)

			f.SetCellValue("Sheet1", "U"+strconv.Itoa(i), v.TaxProductName)
			var attrsStr string
			for _, attr := range v.Attrs {
				attrsStr += fmt.Sprintf("%s:%s,", attr.Name, attr.Value)
			}
			f.SetCellValue("Sheet1", "V"+strconv.Itoa(i), attrsStr)
			f.SetCellValue("Sheet1", "W"+strconv.Itoa(i), v.SupplierSourceCategory.Name)
			f.SetCellValue("Sheet1", "X"+strconv.Itoa(i), v.SupplierSource.Name)
			f.SetCellValue("Sheet1", "Y"+strconv.Itoa(i), float64(sku.Price)/100)
			f.SetCellValue("Sheet1", "Z"+strconv.Itoa(i), float64(tpp.SkuPrice)/100)
			f.SetCellValue("Sheet1", "AA"+strconv.Itoa(i), float64(sku.GuidePrice)/100)
			f.SetCellValue("Sheet1", "AB"+strconv.Itoa(i), float64(tpp.SkuGuidePrice)/100)
			f.SetCellValue("Sheet1", "AC"+strconv.Itoa(i), float64(sku.OriginPrice)/100)
			f.SetCellValue("Sheet1", "AD"+strconv.Itoa(i), float64(tpp.SkuOriginPrice)/100)

			i++

		}
		// 设置工作簿的默认工作表
		f.SetActiveSheet(index)
		// 根据指定路径保存文件
		//year, month, day := time.Now().Format("2006-01-02 15:04:05")

		exist, _ := utils.PathExists(path)

		if !exist {
			// 创建文件夹
			err = os.Mkdir(path, os.ModePerm)
			if err != nil {
				fmt.Printf("mkdir failed![%v]\n", err)
			} else {
				fmt.Printf("mkdir success!\n")
			}
		}
		link = path + "/" + timeString + "-" + strconv.Itoa(ei) + "商品池导出.xlsx"
		if err = f.SaveAs(link); err != nil {
			return
		}
		links = append(links, link)
	}
	if excelPage > 1 {
		link = path + "/" + timeString + "商品池导出.zip"
		err = service.Zip(link, links)
	}
	productExportRecord.Link = link
	productExportRecord.StatusString = "导出完成"
	err = source.DB().Save(&productExportRecord).Error
	if err != nil {
		return
	}
	if err != nil {
		return
	}
	err = service2.PublishNotify("商品池导出完成", "共导出了"+strconv.Itoa(int(total))+"件商品")

	return err, link
}
func GetThousandPriceExportRecordList(info request.ThousandsPricesRecordRequest) (err error, total int64, list interface{}) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.ThousandsPricesExportRecord{})
	var applications []model.ThousandsPricesExportRecord
	// 总后台导出记录sys_user_id为0，供应商为supplier表的user_id
	if info.SysUserID != 0 {
		db = db.Where("`sys_user_id` = ?", info.SysUserID)
	}
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.StatusString != "" {
		db = db.Where("`status_string` LIKE ?", "%"+info.StatusString+"%")
	}

	err = db.Count(&total).Error
	err = db.Order("created_at desc").Limit(limit).Offset(offset).Find(&applications).Error

	return err, total, applications
}
func DeleteThousandPriceExportRecord(application model.ThousandsPricesExportRecord) (err error) {
	err = source.DB().Delete(&application).Error
	return err
}

// 定义参数结构体
type MissingSkusParams struct {
	Page        int
	PageSize    int
	ProductName string
	ProductID   uint
	SkuName     string
	SkuID       uint
	ID          uint
}

// 获取缺失的 SKU 列表，支持分页和筛选
func GetMissingSkus(params MissingSkusParams) ([]model.MissingSkus, int64, error) {
	var missingSkus []model.MissingSkus
	var total int64
	db := source.DB().Model(&model.MissingSkus{}).Where("status = ?", 0)
	if params.ProductName != "" {
		db = db.Where("product_title LIKE ?", "%"+params.ProductName+"%")
	}
	if params.ProductID != 0 {
		db = db.Where("product_id = ?", params.ProductID)
	}
	if params.SkuName != "" {
		db = db.Where("sku_title LIKE ?", "%"+params.SkuName+"%")
	}
	if params.SkuID != 0 {
		db = db.Where("sku_id = ?", params.SkuID)
	}
	db.Where("status = 0 and tp_id = ?", params.ID)
	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	err = db.Limit(params.PageSize).Offset((params.Page - 1) * params.PageSize).Find(&missingSkus).Error
	if err != nil {
		return nil, 0, err
	}
	return missingSkus, total, nil
}

// 批量设置缺失 SKU 的状态为已读
func SetMissingSkusStatus(req request3.IdsReq) error {
	err := source.DB().Model(&model.MissingSkus{}).Where("id IN (?)", req.Ids).Update("status", 1).Error
	if err != nil {
		return err
	}
	return nil
}
