package order

import (
	afterSalesModel "after-sales/model"
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"mime/multipart"
	model3 "order/model"
	model4 "product/model"
	callback2 "public-supply/callback"
	common2 "public-supply/common"
	"public-supply/model"
	"public-supply/request"
	"public-supply/response"
	gsetting "public-supply/setting"
	"region/mapping"
	"strconv"
	"strings"
	goods2 "tianma-supply/component/goods"
	model6 "tianma-supply/model"
	request2 "tianma-supply/request"
	response2 "tianma-supply/response"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"

	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
)

type TianMa struct {
	dat      *model.SupplySetting
	Setting  *model.SupplySetting
	SupplyID uint
	Http     string
}

func (y *TianMa) GetRefundTypes(orderId uint, orderItemID uint) (err error, data []afterSalesModel.AfterSalesType) {
	return
}

func (y *TianMa) GetAllAddress() (err error, data interface{}) {
	return
}

func (y *TianMa) GetToken() (err error) {

	return
}

func (y *TianMa) AfterSalesBeforeCheck(request request.RequestAfterSale) (err error, info interface{}) {
	return
}

func (y *TianMa) AfterSalesPicture(request request.RequestAfterSalePicture) (err error, info interface{}) {
	return
}

func (y *TianMa) AfterSale(request request.AfterSale) (err error, info interface{}) {

	url := y.Http + "/openapi/addAfterSales.do"
	var resData []byte
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)

	_ = writer.WriteField("sign", y.dat.BaseInfo.AppSecret)
	_ = writer.WriteField("order_id", request.OrderSn.OrderSn)
	_ = writer.WriteField("problem_type", "无理由退货")
	_ = writer.WriteField("image_url", "")

	if request.ReasonsDescription == "" {
		request.ReasonsDescription = "无理由退货"

	}
	_ = writer.WriteField("problem_content", request.ReasonsDescription)

	//_ = writer.WriteField("orders_info", string(reqData))
	err = writer.Close()
	if err != nil {
		return
	}
	resData, err = utils.PostFormData(url, payload, writer.FormDataContentType())
	log.Log().Info("天马 订单售后返回数据", zap.Any("info", string(resData)))

	var AfterSales AfterSales
	err = json.Unmarshal(resData, &AfterSales)
	if AfterSales.ErrorInfo != "" {
		err = errors.New(AfterSales.ErrorInfo)
		return

	}

	return
}

type AfterSales struct {
	ErrorInfo string `json:"error_info"`
	ErrorCode string `json:"error_code"`
}

func (y *TianMa) OrderDelivery(OrderData callback2.OrderCallBack) (err error) {
	return
}

func (y *TianMa) SyncOrderExpNo(unionIdList []string) (err error, data []response.SyncOrderExpNoResponse) {
	return
}

func (y *TianMa) InitSetting(gatherSupplyID uint) (err error) {
	y.SupplyID = gatherSupplyID

	var setting model2.SysSetting

	err, setting = gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}
	err = json.Unmarshal([]byte(setting.Value), &y.dat)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	y.Setting = y.dat
	if y.dat.BaseInfo.AppKey == "" && y.dat.BaseInfo.AppSecret == "" {
		err = errors.New("请先配置供应链key")
		return
	}

	y.Http = y.dat.BaseInfo.ApiUrl + "/api/"
	y.dat.BaseInfo.AppSecret = y.dat.BaseInfo.AppSecret
	y.dat.BaseInfo.UserName = y.dat.BaseInfo.UserName
	y.dat.BaseInfo.PassWord = y.dat.BaseInfo.PassWord
	//
	//y.GetToken()

	if y.Http == "" {
		y.Http = "http://253.open.test.tmyd.tianmagroup.com/api/"
	}

	return
}

func (y *TianMa) ApplyCancel(orderID string) (err error) {
	url := y.Http + "/openapi/applyCancel.do"
	var resData []byte
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)

	_ = writer.WriteField("sign", y.dat.BaseInfo.AppSecret)
	_ = writer.WriteField("name", y.dat.BaseInfo.UserName)
	_ = writer.WriteField("pwd", y.dat.BaseInfo.PassWord)
	_ = writer.WriteField("order_sn", orderID)
	_ = writer.WriteField("qxyy", "取消订单")

	err = writer.Close()
	if err != nil {
		return
	}
	resData, err = utils.PostFormData(url, payload, writer.FormDataContentType())
	log.Log().Info("天马 ApplyCancel订单售后返回数据", zap.Any("info", string(resData)))

	var errInfo string
	var applyCancel ApplyCancel
	err = json.Unmarshal(resData, &applyCancel)
	//if applyCancel.ErrorInfo != "" {
	//	err = errors.New(applyCancel.ErrorInfo)
	//	return
	//
	//}
	for _, item := range applyCancel.Data {
		if item.Status != 0 {
			errInfo = errInfo + item.Msg + "-"
		}
	}
	if errInfo != "" {
		err = errors.New(errInfo)
		return
	}
	return

}

type ApplyCancel struct {
	ErrorInfo string `json:"error_info"`
	Data      []struct {
		Msg     string `json:"msg"`
		OrderSn string `json:"order_sn"`
		Status  int    `json:"status"`
	} `json:"data"`
	ErrorCode string `json:"error_code"`
}

func (y *TianMa) CancelOrder(orderID uint) {
	return
}

func (y *TianMa) QueryGoodsSaleLimitList(request request.RequestSaleBeforeCheck) (err error) { //查询商品的限制销售地区

	return
}

// 订单前置校验  返回运费   //立即购买校验
func (y *TianMa) OrderBeforeCheck(request request.RequestSaleBeforeCheck) (err error, res response.BeforeCheck) {
	log.Log().Info("wps OrderBeforeCheck request", zap.Any("info", request))

	res.Code = 1

	var goods goods2.TianMa
	goods.InitSetting(y.SupplyID)

	var freight float64

	var isDelivery int
	for _, item := range request.LocalSkus {
		var sku model4.Sku
		err = source.DB().Where("id=?", item.Sku.Sku).Preload("Product").First(&sku).Error
		if err != nil {
			return
		}
		var inventoryListByGroup request2.InventoryListByGroup
		inventoryListByGroup.WareHouseName = sku.Product.ShopName
		inventoryListByGroup.Articleno = sku.Product.SourceGoodsIDString
		inventoryListByGroup.Sizes = sku.Describe
		GetInventoryListByGroup, _ := goods.GetInventoryListByGroup(inventoryListByGroup)
		log.Log().Info("tianma 运费计算 ，获取商品重量", zap.Any("info", GetInventoryListByGroup))
		if len(GetInventoryListByGroup.Rows) == 0 {
			res.Msg = "未查询到该商品数据"
			res.Code = 0
			return
		}
		Weight := GetInventoryListByGroup.Rows[0].Weight
		var deliveryInfo request2.WareHouse
		deliveryInfo.WareHouseName = sku.Product.ShopName
		GetDeliveryInfo, _ := goods.GetDeliveryInfo(deliveryInfo)
		log.Log().Info("tianma 运费计算 ，获取快递对应省份首重", zap.Any("info", GetDeliveryInfo))

		if len(GetDeliveryInfo.Rows) > 0 {
			for _, deliveryItem := range GetDeliveryInfo.Rows {
				if deliveryItem.DiliveryName == sku.Product.ExpressDelivery && (strings.Contains(request.Address.Province, deliveryItem.Province) || strings.Contains(deliveryItem.Province, request.Address.Province)) {
					log.Log().Info("info", zap.Any("info", deliveryItem))
					isDelivery = 1
					freight = freight + (1 * deliveryItem.FirstPostage) + ((Weight - 1) * deliveryItem.AdditionalPostage)
					break
				}
			}
		}
	}
	if isDelivery == 0 {
		res.Code = 0
		res.Msg = "未查询到" + request.Address.Province + "支持的快递"
		return
	}
	res.Freight = uint(freight * 100)

	return
}

// 商品是否可售前置校验   下单支付校验
func (y *TianMa) SaleBeforeCheck(request request.RequestSaleBeforeCheck) (err error, res response.ResSaleBeforeCheck) {
	res.Code = 0
	res.IsOriginalSkuID = 1
	var goods goods2.TianMa
	goods.InitSetting(y.SupplyID)

	for _, item := range request.LocalSkus {

		var sku model4.Sku
		err = source.DB().Where("id=?", item.Sku.Sku).Preload("Product").First(&sku).Error
		if err != nil {
			return
		}
		var inventoryListByGroup request2.InventoryListByGroup
		inventoryListByGroup.WareHouseName = sku.Product.ShopName
		inventoryListByGroup.Articleno = sku.Product.SourceGoodsIDString
		inventoryListByGroup.Sizes = sku.Describe
		GetInventoryListByGroup, _ := goods.GetInventoryListByGroup(inventoryListByGroup)
		log.Log().Info("tianma 检测是否可售 ，获取商品信息", zap.Any("info", GetInventoryListByGroup))
		if len(GetInventoryListByGroup.Rows) == 0 {
			res.Data.Ban = append(res.Data.Ban, uint(item.Sku.Sku))
			continue
		}
		InnerNum, _ := strconv.Atoi(GetInventoryListByGroup.Rows[0].InnerNum)

		if InnerNum <= 0 {
			res.Data.Ban = append(res.Data.Ban, uint(item.Sku.Sku))
			continue
		}
		res.Data.Available = append(res.Data.Available, uint(item.Sku.Sku))

		if len(res.Data.Ban) > 0 {
			res.Msg = "商品库存不足"
			res.Code = 0
		}

	}

	return

}

//var ProvinceAddress, CityAddress, AreaAddress []model5.YzhAddress

func (y *TianMa) GetAreaAddress(Area, pid string) (AreaID string, err error) {

	return

}

func (y *TianMa) GetStreetAddress(Street, pid string) (StreetID string, err error) {

	return

}

func (y *TianMa) GetGoodsPriceList(arr []string) int64 {

	return 0

}

func (y *TianMa) GetCityAddress(City, pid string) (CityID string, err error) {

	return

}

func (y *TianMa) QueryCategoryList(level, pid uint) (data interface{}) {

	return

}

func (y *TianMa) GetOrderInfo(orderList string) (err error, orderFeedBackListData response2.OrderFeedBackListData) {
	url := y.Http + "/openapi/getOrderInfo.do"
	payload := &bytes.Buffer{}
	var resData []byte
	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("sign", y.dat.BaseInfo.AppSecret)
	_ = writer.WriteField("name", y.dat.BaseInfo.UserName)
	_ = writer.WriteField("page", "1")
	_ = writer.WriteField("order_sn", orderList)
	err = writer.Close()
	if err != nil {
		return
	}
	resData, err = utils.PostFormData(url, payload, writer.FormDataContentType())
	if err != nil {
		return
	}
	err = json.Unmarshal(resData, &orderFeedBackListData)

	if err != nil {
		return
	}

	return
}

// 确认下单
func (y *TianMa) ConfirmOrder(request request.RequestConfirmOrder) (err error, info *stbz.APIResult) {

	log.Log().Info("天马 供应链商品准备下单", zap.Any("info", request))

	var updateOrder model3.Order
	err = source.DB().Where("order_sn=?", request.OrderSn.OrderSn).First(&updateOrder).Error
	if updateOrder.GatherSupplySN != "" {
		log.Log().Info("天马  ConfirmOrder供应链商品重复下单", zap.Any("info", updateOrder), zap.Any("info", request))
		return
	}

	var ordersDataInfos model6.OrdersDataInfos
	var ordersDataInfo model6.OrdersDataInfo
	ordersDataInfo.OrderSn = request.OrderSn.OrderSn
	ordersDataInfo.Name = request.Address.Consignee
	ordersDataInfo.Mobile = request.Address.Phone
	ordersDataInfo.Address = request.Address.Description
	//if request.Address.Province == "上海市" || request.Address.Province == "北京市" || request.Address.Province == "重庆市" {
	//	request.Address.City = request.Address.Province
	//}
	myAddress := request.Address.Province + "," + request.Address.City + "," + request.Address.Area

	region, _ := mapping.GetMatchingRegionsByText("tianma_region", myAddress, request.Address.Area)
	log.Log().Info("天马  ConfirmOrder 解析地址", zap.Any("myAddress", myAddress), zap.Any("region", region))

	if len(region) > 0 {

		if len(region[0].Names) == 2 {
			ordersDataInfo.Province = region[0].Names[0]
			ordersDataInfo.City = region[0].Names[1]
			ordersDataInfo.Area = "0"
		}

		if len(region[0].Names) == 3 {
			ordersDataInfo.Province = region[0].Names[0]
			ordersDataInfo.City = region[0].Names[1]
			ordersDataInfo.Area = region[0].Names[2]
		}
		ordersDataInfo.Town = request.Address.Street

	}

	for _, item := range request.LocalSkus {
		var sku model4.Sku
		err = source.DB().Where("id=?", item.Sku.Sku).Preload("Product").First(&sku).Error
		if err != nil {
			return
		}
		ordersDataInfo.Delivery = sku.Product.ExpressDelivery
		ordersDataInfo.WarehouseName = sku.Product.ShopName
		ordersDataInfo.GoodsInfos = append(ordersDataInfo.GoodsInfos, model6.GoodsInfo{
			GoodsNo: sku.Product.SourceGoodsIDString, //货号
			Size:    sku.Describe,                    //尺码
			Amount:  item.Number,                     //数量
		})
	}

	ordersDataInfos = append(ordersDataInfos, ordersDataInfo)
	url := y.Http + "/openapi/setOrderInfos.do"
	var resData []byte
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("sign", y.dat.BaseInfo.AppSecret)
	_ = writer.WriteField("name", y.dat.BaseInfo.UserName)
	_ = writer.WriteField("pwd", y.dat.BaseInfo.PassWord)
	reqData, _ := json.Marshal(&ordersDataInfos)
	log.Log().Info("天马 订单请求数据", zap.Any("info", string(reqData)))

	_ = writer.WriteField("orders_info", string(reqData))
	err = writer.Close()
	if err != nil {
		return
	}
	resData, err = utils.PostFormData(url, payload, writer.FormDataContentType())
	log.Log().Info("天马 订单返回数据", zap.Any("info", string(resData)))

	if err != nil {
		return
	}
	var orderInfo []model6.ResponseOrderInfo
	err = json.Unmarshal(resData, &orderInfo)

	if err != nil {
		return
	}
	var order model3.Order
	order.GatherSupplyType = common2.SUPPLY_TIANMA
	if len(orderInfo) > 0 {
		if orderInfo[0].Status == "0" {
			if len(orderInfo[0].Orders) > 0 {
				order.GatherSupplySN = strconv.Itoa(orderInfo[0].Orders[0].OrderId)
			} else {
				order.GatherSupplyMsg = "下单失败上游未返回订单信息"
			}

		} else {
			order.GatherSupplyMsg = orderInfo[0].Info
		}

	} else {
		order.GatherSupplyMsg = "下单失败上游未返回数据"
	}
	err = source.DB().Where("order_sn=?", request.OrderSn.OrderSn).Updates(&order).Error
	if err != nil {
		log.Log().Info("天马保存三方单号失败", zap.Any("info", err))
	}

	return

}

// 物流查询
func (y *TianMa) ExpressQuery(request request.RequestExpress) (err error, data interface{}) {
	url := string(y.Http + "/open/api/order/queryOrderLogistics")
	var resData []byte
	headerData := make(map[string]interface{})
	headerData["accessToken"] = y.dat.BaseInfo.Token
	headerData["tparOrderCode"] = request.OrderSn
	headerData["parentOrderCode"] = request.GatherSupplySn
	reqData, _ := json.Marshal(headerData)
	resData = utils.HttpPostJson(reqData, url)
	//var ResYzhOrderDetail model5.ShipmentList
	data = string(resData)
	//err = json.Unmarshal(resData, &ShipmentList)
	//
	//if err != nil {
	//	return
	//}
	//
	//fmt.Println(string(resData))

	return
	//	url := string(y.Http + common.YZH_ORDER_TRACK)
	//	var resData []byte
	//	headerData := make(map[string]string)
	//	timeUnix := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	//	reqData := url2.Values{}
	//	reqData.Add("wid", y.dat.BaseInfo.AppKey)
	//	reqData.Add("token", strings.ToUpper(utils.MD5V([]byte(y.dat.BaseInfo.AppKey+y.dat.BaseInfo.AppSecret+timeUnix))))
	//	reqData.Add("timestamp", timeUnix)
	//	reqData.Add("orderKey", request.OrderSn)
	//	err, resData = utils.PostForm(url, reqData, headerData)
	//	if err != nil {
	//		log.Log().Error("yzh 物流信息请求失败", zap.Any("info", err))
	//		return
	//	}
	//
	//	var orderTrack model.OrderTrack
	//	err = json.Unmarshal(resData, &orderTrack)
	//	fmt.Println(string(resData))
	//	if err != nil {
	//		return
	//	}
	//
	//	if orderTrack.RESPONSESTATUS == "false" {
	//		err = errors.New("物流查询返回false")
	//		log.Log().Error("yzh 获取物流信息失败", zap.Any("info", orderTrack))
	//		return
	//	}
	//	if orderTrack.RESULTDATA.ShipmentName == "" || orderTrack.RESULTDATA.ShipmentOrder == "" {
	//		err = errors.New("物流查询返回空")
	//		log.Log().Error("yzh 获取物流信息失败", zap.Any("info", orderTrack))
	//		return
	//	}
	//	name := orderTrack.RESULTDATA.ShipmentName
	//	expresInfo := make(map[string]string)
	//
	//	err, expresInfo["code"] = ExpressList(name)
	//	if err != nil {
	//		log.Log().Error("yzh 获取code失败", zap.Any("info", orderTrack))
	//		return
	//	}
	//	if expresInfo["code"] == "" {
	//		log.Log().Error("yzh name获取code失败", zap.Any("info", name))
	//	}
	//	expresInfo["no"] = orderTrack.RESULTDATA.ShipmentOrder
	//
	//	data = expresInfo
	//	return

}
