package cron

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"gorm.io/gorm"
	pmodel "product/model"
	"product/mq"
	pservice "product/service"
	"public-supply/common"
	"public-supply/model"
	setting2 "public-supply/setting"
	"strconv"
	goods2 "tianma-supply/component/goods"
	request2 "tianma-supply/request"
	"time"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
	"yz-go/utils"
)

func PushtianmaProductUpdateHandle() {

	log.Log().Info("cron  PushWeipinshangProductUpdateHandle")

	var gatherList []model.GatherSupply
	err := source.DB().Where("category_id = ?", common.TIANMA_SOURCE).Where("deleted_at is null").Find(&gatherList).Error
	if err != nil {
		return
	}

	for _, v := range gatherList {

		CreateCronTaskSale(int(v.ID))

	}

}

var datsetting model.SupplySetting

func InitSetting(taskID int) {
	err, setting := setting2.GetSetting("gatherSupply" + strconv.Itoa(taskID))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}

	err = json.Unmarshal([]byte(setting.Value), &datsetting)
	if err != nil {

		return
	}
}
func CreateCronTaskSale(taskID int) {

	InitSetting(taskID)

	var cronStr string

	cronStr = "0 */30 * * * *"

	cron.PushTask(cron.Task{
		Key:  "tianmaproductupdate" + strconv.Itoa(taskID),
		Name: "tianmaproductupdate商品更新" + strconv.Itoa(taskID),
		Spec: cronStr, //"0/3 * * * * ?"
		Handle: func(task cron.Task) {
			GoodsUpdate(uint(taskID))

		},
		Status: cron.ENABLED,
	})

}

type SendMessage struct {
	CMsgid   string `json:"c_msgid"`
	CContent struct {
		Goods []struct {
			CFatherGoodsId string `json:"c_father_goods_id"`
			CGoodsId       string `json:"c_goods_id"`
		} `json:"goods"`
		Type int    `json:"type"`
		Msg  string `json:"msg"`
		Time string `json:"time"`
	} `json:"c_content"`
	Type int    `json:"type"`
	Time string `json:"time"`
}

type Product struct {
	pservice.ProductForUpdate
	SourceGoodsIDString string `json:"source_goods_id_string" form:"source_goods_id_string"`
}

func UndercarriageProduct(productID uint) (err error) {
	var productMessageType mq.ProductMessageType //队列消息类型
	productMessageType = mq.Undercarriage
	err = mq.PublishMessage(productID, productMessageType, 0)

	var colum = make(map[string]interface{})
	colum["is_display"] = 0

	colum["updated_at"] = time.Now().Format("2006-01-02 15:04:05")

	err = source.DB().Table("products").Where("id=? and source=?", productID, common.TIANMA_SOURCE).UpdateColumns(&colum).Error

	return
}

func GoodsUpdate(supplyId uint) (err error) {
	var y = new(goods2.TianMa)
	log.Log().Info("cron tianma   GoodsUpdate", zap.Any("info", supplyId))

	err = y.InitSetting(supplyId)
	if err != nil {
		return err
	}

	var product []pservice.ProductForUpdate

	db := source.DB().Model(pservice.ProductForUpdate{})

	var updateCount int64
	db.Preload("Skus").Where("status_lock=0 and source =?    and gather_supply_id=?", common.TIANMA_SOURCE, supplyId).Order("updated_at asc ").Limit(6000).FindInBatches(&product, 500, func(tx *gorm.DB, batch int) error {
		for _, item := range product {

			updateCount += 500

			if updateCount > 6000 {
				return nil
			}

			if item.ID == 0 {
				continue
			}
			var reqData request2.InventoryListByGroup
			reqData.Articleno = item.SourceGoodsIDString
			reqData.WareHouseName = item.ShopName
			//reqData.Sizes = item.Skus[0].Describe
			details, detailErr := y.GetInventoryListByGroup(reqData)
			if detailErr != nil {
				log.Log().Error("tianma  update BatchGetGoodsDetails err", zap.Any("err", detailErr))
			}

			if len(details.Rows) == 0 {
				UndercarriageProduct(item.ID)
				continue
			}

			var updateGoods pservice.ProductForUpdate
			updateGoods = item

			detail := details.Rows[0]

			//InnerNum, _ := strconv.Atoi(detail.InnerNum)
			//if InnerNum == 0 {
			//	UndercarriageProduct(item.ID)
			//	continue
			//} else {
			//	updateGoods.IsDisplay = 1
			//}
			sourceCode := common.TIANMA_SOURCE

			//var costPrice, salePrice, originPrice, activityPrice, guidePrice uint
			//var elem model.Goods
			//SellPrice, _ := strconv.ParseFloat(detail.Marketprice, 64)
			//MarketPrice := detail.Marketprice
			//discount, _ := strconv.ParseFloat(detail.Discount, 64)
			//CostPrice := detail.Marketprice * (discount / 10)
			//OriginalPrice, _ := strconv.ParseFloat(detail.COriginalPrice, 64)

			//elem.AgreementPrice = uint(CostPrice * 100) //协议价
			//elem.MarketPrice = uint(MarketPrice * 100)  //吊牌价/市场价
			//elem.ActivityPrice = uint(MarketPrice * 100) //建议零售价
			//elem.Source = sourceCode
			//
			//err, costPrice, salePrice, originPrice, activityPrice, guidePrice = goods2.GetPricingPrice(elem, "gatherSupply"+strconv.Itoa(int(y.SupplyID)))
			//
			//updateGoods.Stock = uint(InnerNum)
			//updateGoods.OriginPrice = originPrice
			//updateGoods.Price = salePrice
			//updateGoods.CostPrice = costPrice
			//updateGoods.ActivityPrice = activityPrice
			//updateGoods.GuidePrice = guidePrice

			//updateGoods.ImageUrl = detail.PicUrl

			var sku = pservice.Sku{}
			var skuList []pservice.Sku

			//updateGoods.MinPrice = updateGoods.Price
			//updateGoods.MaxPrice = updateGoods.Price
			//
			var minProfitRate float64
			var stock int

			for _, skuItem := range details.Rows {
				innerNum, _ := strconv.Atoi(skuItem.InnerNum)
				stock = stock + innerNum

				for _, localskuitem := range item.Skus {
					md5 := skuItem.WareHouseName + skuItem.Articleno + skuItem.Size + skuItem.Colour
					md5Str := utils.MD5V([]byte(md5))
					if localskuitem.SpecId == md5Str {
						sku = localskuitem
						break
					}
				}

				var options pmodel.Options

				skuTitle := skuItem.Size + "-" + skuItem.Colour

				if skuItem.Colour != "" {
					options = append(options, pmodel.Option{SpecName: "颜色", SpecItemName: skuItem.Colour})
				}
				if skuItem.Size != "" {
					options = append(options, pmodel.Option{SpecName: "尺码", SpecItemName: skuItem.Size})
				}
				if skuItem.Sex != "" {
					options = append(options, pmodel.Option{SpecName: "性别", SpecItemName: skuItem.Sex})
				}

				var costPrice, salePrice, originPrice, activityPrice, guidePrice uint
				var elem model.Goods
				//SellPrice, _ := strconv.ParseFloat(detail.Marketprice, 64)
				MarketPrice := skuItem.Marketprice
				discount, _ := strconv.ParseFloat(skuItem.Discount, 64)
				CostPrice := skuItem.Marketprice * (discount / 10)
				//OriginalPrice, _ := strconv.ParseFloat(detail.COriginalPrice, 64)

				elem.AgreementPrice = uint(CostPrice * 100)  //协议价
				elem.MarketPrice = uint(MarketPrice * 100)   //吊牌价/市场价
				elem.ActivityPrice = uint(MarketPrice * 100) //吊牌价/市场价
				//elem.ActivityPrice = uint(MarketPrice * 100) //建议零售价
				elem.Source = sourceCode
				//
				err, costPrice, salePrice, originPrice, activityPrice, guidePrice = goods2.GetPricingPrice(elem, "gatherSupply"+strconv.Itoa(int(y.SupplyID)))
				//
				sku.Title = skuTitle
				sku.Options = options
				//sku.Weight = skuItem.Weight
				//InnerNum, _ := strconv.Atoi(skuItem.InnerNum)
				sku.Stock = innerNum
				sku.IsDisplay = 1
				if innerNum <= 0 {
					sku.IsDisplay = 0
				}
				var dat model.SupplySetting
				_, setting := setting2.GetSetting("gatherSupply" + strconv.Itoa(int(y.SupplyID)))

				err = json.Unmarshal([]byte(setting.Value), &dat)

				if dat.UpdateInfo.CostPrice == 1 { //更新成本
					sku.CostPrice = costPrice
				}
				if dat.UpdateInfo.CurrentPrice == 1 { //更新供货
					sku.Price = salePrice

				}
				if dat.UpdateInfo.GuidePrice == 1 { //更新指导价

					sku.GuidePrice = guidePrice

				}
				if dat.UpdateInfo.ActivityPrice == 1 { //更新营销价
					sku.OriginPrice = originPrice

					sku.ActivityPrice = activityPrice

				}
				sku.Describe = skuItem.Size

				md5 := skuItem.WareHouseName + skuItem.Articleno + skuItem.Size + skuItem.Colour
				md5Str := utils.MD5V([]byte(md5))
				sku.SpecId = md5Str
				if sku.GuidePrice > 0 {
					sku.ProfitRate = utils.ExecProfitRate(sku.GuidePrice, sku.Price)
				} else {
					sku.ProfitRate = 0
				}
				if sku.ProfitRate <= minProfitRate {
					minProfitRate = sku.ProfitRate
				}
				//if skuItem.OldGoodsID > 0 && goods.Source == 2 {
				//	_, skuDetail := st.GetGoodsDetails(skuItem.OldGoodsID)
				//	sku.Describe = skuDetail.Description
				//}

				skuList = append(skuList, sku)

			}
			updateGoods.Skus = skuList
			updateGoods.Stock = uint(stock)

			if len(updateGoods.Skus) == 0 {
				sku = item.Skus[0]
				var options pmodel.Options
				var option pmodel.Option
				option.SpecName = "尺码"
				option.SpecItemName = detail.Size
				options = append(options, option)

				sku.Options = options
				//sku.Weight = int(detail.GrossWeight)
				sku.CostPrice = updateGoods.CostPrice
				sku.Describe = detail.Size
				sku.Stock = int(updateGoods.Stock)
				//sku.IsDisplay = goods.IsDisplay
				sku.Price = updateGoods.Price
				sku.OriginPrice = updateGoods.OriginPrice
				sku.GuidePrice = updateGoods.GuidePrice
				sku.ActivityPrice = updateGoods.ActivityPrice
				skuList = append(skuList, sku)

				updateGoods.SingleOption = 1

			}

			if sku.GuidePrice > 0 {
				sku.ProfitRate = utils.ExecProfitRate(sku.GuidePrice, sku.Price)
			} else {
				sku.ProfitRate = 0
			}
			minProfitRate = sku.ProfitRate

			//updateGoods.Skus = skuList
			//
			if len(updateGoods.Skus) > 0 {
				updateGoods.ProfitRate = minProfitRate
			}

			err = pservice.UpdateProduct(updateGoods)
			if err != nil {
				log.Log().Error("更新商品错误 update", zap.Any("err", err))
				return err
			}

		}

		return nil
	})

	return

}
