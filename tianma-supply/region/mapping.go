package region

import (
	"bytes"
	_ "embed"
	"github.com/xuri/excelize/v2"
	"region/mapping"
	"strconv"
)

//go:embed tianma_region.xlsx
var Xlsx []byte

func LoadRegionsFromXlsx() (err error) {
	// 从 xlsx 文件中获取地区列表
	regions, err := GetRegionListFromTRegionXlsx()
	if err != nil {
		println(err)
	}
	err = mapping.SaveRegions("tianma_region", regions)
	return
}

func GetRegionListFromTRegionXlsx() (regions mapping.Regions, err error) {
	f, err := excelize.OpenReader(bytes.NewReader(Xlsx))
	if err != nil {
		return
	}

	rows, err := f.GetRows(f.GetSheetList()[0])
	if err != nil {
		return
	}

	for i, row := range rows {
		if i == 0 {
			continue // Skip the header row
		}
		var parentID, id int
		if err != nil {
			return
		}
		parentID, err = strconv.Atoi(row[1])
		id, err = strconv.Atoi(row[0])
		if err != nil {
			return
		}
		regions = append(regions, mapping.Region{
			ID:       id,
			ParentID: parentID,
			Name:     row[2],
		})
	}

	return
}
