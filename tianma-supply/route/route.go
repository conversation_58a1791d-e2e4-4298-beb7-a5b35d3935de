package route

import (
	"github.com/gin-gonic/gin"
	"tianma-supply/api"
)

func InitTianMaRouter(Router *gin.RouterGroup) {
	TianMaRouter := Router.Group("tianma")
	{

		TianMaRouter.POST("getDeliveryInfo", api.GetDeliveryInfo)                 //获取快递物流运费
		TianMaRouter.POST("getWareHouseNameInfo", api.GetWareHouseNameInfo)       //获取发货仓
		TianMaRouter.POST("getInventoryListByGroup", api.GetInventoryListByGroup) //获取商品库存
		TianMaRouter.POST("getStockListByGoodsNo", api.GetStockListByGoodsNo)     //根据货号获取商品仓库库存列表
		TianMaRouter.POST("getCommodityList", api.GetCommodityList)               //根据货号查询单条数据
		TianMaRouter.POST("getWarehouseDetail", api.GetWarehouseDetail)           //根据货源返回品牌

	}
}
