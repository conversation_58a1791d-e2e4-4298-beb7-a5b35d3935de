package response

import (
    "topsdk/defaultability/domain"
)

type AlibabaAlscUnionElemePromotionOfficialactivityGetResponse struct {

    /*
        System request id
    */
    RequestId string `json:"request_id,omitempty" `

    /*
        System body
    */
    Body string

    /*
        活动数据
    */
    Data  domain.AlibabaAlscUnionElemePromotionOfficialactivityGetActivityPromotionDto `json:"data,omitempty" `
    /*
        返回码
    */
    ResultCode  int64 `json:"result_code,omitempty" `
    /*
        返回消息
    */
    Message  string `json:"message,omitempty" `
}
