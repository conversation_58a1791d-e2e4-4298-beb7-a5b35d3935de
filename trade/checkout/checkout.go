package checkout

import (
	applicationLevel "application/level"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"gin-vue-admin/cmd/gva"
	"github.com/jinzhu/copier"
	"go.uber.org/zap"
	"gorm.io/gorm"
	model2 "order/model"
	"product/model"
	"product/stock"
	"public-supply/common"
	"shipping/freight"
	"shipping/method"
	"shipping/shipping"
	"shop/setting"
	"strconv"
	"sync"
	"trade/service"
	"user/address"
	"user/level"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/source"
)

type Checkout struct {
	BuyID          uint                    `json:"buy_id"`          // 购买id
	DefaultAddress address.Address         `json:"default_address"` // 默认收货地址
	Orders         []Order                 `json:"orders"`          // 订单组
	FailedOrders   []Order                 `json:"failed_orders"`   // 失败订单组
	AmountDetail   yzResponse.AmountDetail `json:"amount_detail"`   // 金额明细
	GoodsCount     uint                    `json:"goods_count"`     // 商品数量
	Amount         uint                    `json:"amount"`          // 总价
	ThirdOrderSn   string                  `json:"third_order_sn"`  // 第三方订单号
	Remark         string                  `json:"remark"`          // 买家备注
	Messages       []string                `json:"message"`         // 提示信息
	CanConfirm     bool                    `json:"can_confirm"`     // 可以提交
	BillType       string                  `json:"bill_type"`       // 发票可选类型

}

func (c *Checkout) Make() (err error) {
	var count uint
	var productAmount uint
	var freightAmount uint
	var fee uint
	var messages []string
	var canConfirm = true
	for _, order := range c.Orders {
		count += order.GoodsTotal
		productAmount += order.ItemAmount
		var isThousandsPrice int
		for _, amountDetail := range c.AmountDetail.AmountItems {
			if amountDetail.Title == "商品池专属价格" {
				//包含技术服务费
				isThousandsPrice = 1
			}
			if amountDetail.Title == "商品池价格" {
				//不包含技术服务费
				isThousandsPrice = 2
			}
		}
		if isThousandsPrice != 1 {
			//不包含技术服务费时才计算
			fee += order.TechnicalServicesFee
		}

		freightAmount += order.Freight
		if order.Message != "" {
			messages = append(messages, order.Message)
		}
		canConfirm = canConfirm && order.CanConfirm
	}
	c.GoodsCount = count
	c.CanConfirm = canConfirm
	c.Messages = messages
	c.AmountDetail.AddItem(yzResponse.AmountDetail{
		Title:  "商品总价",
		Amount: int(productAmount),
	})

	c.AmountDetail.AddItem(yzResponse.AmountDetail{
		Title:  "总运费",
		Amount: int(freightAmount),
	})

	c.AmountDetail.AddItem(yzResponse.AmountDetail{
		Title:  "技术服务费",
		Amount: int(fee),
	})

	err = c.AmountDetail.Make()
	if err != nil {
		return
	}
	c.AmountDetail.Title = "总金额"
	c.Amount = uint(c.AmountDetail.Amount)
	return
}

type Order struct {
	ID                   uint   `json:"-"`
	Key                  string `json:"key"`                    // 标识
	Title                string `json:"title"`                  // 标题
	Url                  string `json:"url"`                    // 跳转链接
	LogoUrl              string `json:"logo_url"`               // logo地址
	Remark               string `json:"remark"`                 // 备注
	Amount               uint   `json:"amount"`                 // 订单总价
	ItemAmount           uint   `json:"item_amount"`            // 商品金额
	SupplyAmount         uint   `json:"supply_amount"`          // 商品供货金额
	CostAmount           uint   `json:"cost_amount"`            // 成本金额
	Freight              uint   `json:"freight"`                // 运费
	GoodsTotal           uint   `json:"goods_total"`            // 商品数量
	TechnicalServicesFee uint   `json:"technical_services_fee"` // 服务费
	Message              string `json:"message"`                // 提示信息
	CanConfirm           bool   `json:"can_confirm"`            // 可以下单
	Lock                 int    `json:"lock"`                   // 锁定订单

	OrderItems      OrderItems              `json:"order_items"`                // 订单商品条目
	Address         address.Address         `json:"address"`                    //收货地址
	AmountDetail    yzResponse.AmountDetail `json:"amount_detail"`              // 金额明细
	ShippingMethods []method.ShippingMethod `json:"shipping_methods"  gorm:"-"` // 可选的配送方式
	Application     Application             `json:"application"`                // 应用信息
	User            User                    `json:"user"`                       // 用户信息

	ShippingMethodID  int  `json:"shipping_method_id"` // 配送方式id
	SupplierID        uint `json:"-"`
	GatherSupplyID    uint `json:"-"`
	ApplicationID     uint `json:"-"`
	ApplicationShopID int  `json:"-"`
	UserID            uint `json:"-"`
	IsPlugin          int  `json:"-"`
	ErrorMessage      string

	ShippingInfo        shipping.ShippingInfo    `json:"-"`
	OrderBill           model2.OrderBill         `json:"order_bill"`
	TechnologyFeeBill   model2.TechnologyFeeBill `json:"technology_fee_bill"`
	ShareLiveRoomId     uint                     `json:"share_live_room_id" gorm:"column:share_live_room_id;comment:直播间id 存在代表直播间下单;type:int;default:0;"`  // 直播间id 存在代表直播间下单
	EventDistributionId uint                     `json:"event_distribution_id" form:"event_distribution_id" gorm:"column:event_distribution_id;comment:活动id;index;"` //团购活动id
	ShoppingCarts       []ShoppingCart           `json:"shopping_carts" gorm:"-"`                                                                                      // 购物车信息
	ThirdOrderSN        string                   `json:"third_order_sn" form:"third_order_sn" gorm:"column:third_order_sn;comment:编号;index;"`                        // 第三方订单号
}

func (o *Order) Init(key string, user User, supplierID uint, gatherSupplyID uint, isPlugin int, application Application, shippingInfo shipping.ShippingInfo, address address.Address, orderItems OrderItems) (err error) {
	o.Key = key
	o.SupplierID = supplierID
	o.GatherSupplyID = gatherSupplyID
	o.ApplicationID = application.ID
	o.IsPlugin = isPlugin
	o.Application = application
	o.UserID = user.ID
	o.User = user
	o.Address = address
	o.OrderItems = orderItems
	o.ApplicationShopID = o.OrderItems.GetApplicationShopID()
	o.Title = o.OrderItems.GetTitle()
	o.LogoUrl = o.OrderItems.GetLogoUrl()
	o.GoodsTotal = o.OrderItems.GetGoodsTotal()
	o.CanConfirm = true
	o.ShippingInfo = shippingInfo
	o.ShippingMethodID = o.ShippingInfo.ShippingMethodID
	err, o.ShippingMethods = method.GetList()
	if err != nil {
		return
	}
	// 统计商品总价
	o.ItemAmount = o.OrderItems.GetProductAmount()
	o.SupplyAmount = o.OrderItems.GetSupplyAmount()
	o.CostAmount = o.OrderItems.GetCostAmount()
	o.AmountDetail.AddItem(yzResponse.AmountDetail{
		Title:  "商品总价",
		Amount: int(o.ItemAmount),
	})

	// 服务费比例
	err, percent, desc := GetFeeRatio(*o)
	// 服务费
	//err, fee, desc := GetTechnicalServicesFee(*o)
	//if err != nil {
	//	return
	//}
	//o.TechnicalServicesFee = fee
	// 服务费计算
	o.TechnicalServicesFee = o.OrderItems.GetFee(percent)
	o.AmountDetail.AddItem(
		yzResponse.AmountDetail{
			Title:  "技术服务费",
			Amount: int(o.TechnicalServicesFee),
			Desc:   desc,
		},
	)
	// 统计运费
	o.Freight = o.ShippingInfo.GetFreight()
	o.AmountDetail.AddItem(yzResponse.AmountDetail{
		Title:  "总运费",
		Amount: int(o.Freight),
		Desc:   o.ShippingInfo.GetDesc(),
	})
	return
}

type ApplicationShop struct {
	source.Model
	ApplicationID uint   `json:"application_id" form:"application_id" gorm:"column:application_id;comment:;type:int;size:10;"`
	ShopName      string `json:"shop_name"`
	CallbackLink  string `json:"callback_link"`
	AppSecret     string `json:"app_secret"`
}

type Application struct {
	source.Model
	MemberID   uint `json:"member_id"`
	AppLevelID uint `json:"app_level_id"`
}

func (i Application) TableName() string {
	return "application"
}

func GetFeeRatio(order Order) (err error, percent int, desc string) {
	err = order.AmountDetail.Make()
	if err != nil {
		return
	}
	if order.Application.AppLevelID != 0 {
		// 数字权益商品
		var gatherSupply GatherSupply
		err = source.DB().Unscoped().Where("category_id = ?", 98).First(&gatherSupply).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		// 会员权益技术服务费 else 采购端等级技术服务费
		if order.GatherSupplyID > 0 && order.GatherSupplyID == gatherSupply.ID {
			err, percent = applicationLevel.GetLevelDiscountPercentByFulu(order.Application.AppLevelID)
			if err != nil {
				return
			}
		} else {
			err, percent = applicationLevel.GetLevelDiscountPercent(order.Application.AppLevelID)
			if err != nil {
				return
			}
		}
	} else {
		var feePercent int
		// var enable int
		err, _, feePercent = level.GetLevelTechnicalServicesFeePercent(order.User.LevelID)
		if err != nil {
			return
		}
		if feePercent > 0 {
			percent = feePercent
		}
		/*if enable == 1 {
			percent = feePercent
		} else {
			var tradeSetting model.TradeValue
			err, tradeSetting = model.GetTradeSetting()
			if err != nil {
				return
			}
			percent = tradeSetting.ServerRadio
		}*/
	}
	if percent < 0 {
		percent = 0
	}
	desc = fmt.Sprintf("服务费比例%d", percent)
	return
}

func GetTechnicalServicesFee(order Order) (err error, fee uint, desc string) {
	if order.OrderItems[0].NotFee == 1 {
		return err, 0, "开通会员等级服务免服务费"
	}
	err = order.AmountDetail.Make()
	if err != nil {
		return
	}
	orderAmount := order.AmountDetail.Amount
	percent := 0
	if order.Application.AppLevelID != 0 {
		// 数字权益商品
		var gatherSupply GatherSupply
		err = source.DB().Unscoped().Where("category_id = ?", 98).First(&gatherSupply).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		var lianLianGather GatherSupply
		err = source.DB().Where("category_id=8 and deleted_at is null").First(&lianLianGather).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("周边游供应链查询错误")
			return
		}
		// 会员权益技术服务费 else 采购端等级技术服务费
		if order.GatherSupplyID > 0 && order.GatherSupplyID == gatherSupply.ID {
			err, percent = applicationLevel.GetLevelDiscountPercentByFulu(order.Application.AppLevelID)
			if err != nil {
				return
			}
		} else if order.GatherSupplyID > 0 && order.GatherSupplyID == lianLianGather.ID {
			err, percent = applicationLevel.GetLevelDiscountPercentByLianLian(order.Application.AppLevelID)
			if err != nil {
				return
			}
		} else {
			err, percent = applicationLevel.GetLevelDiscountPercent(order.Application.AppLevelID)
			if err != nil {
				return
			}
		}
	} else {
		var feePercent int
		//var enable int
		err, _, feePercent = level.GetLevelTechnicalServicesFeePercent(order.User.LevelID)
		if err != nil {
			return
		}
		if feePercent > 0 {
			percent = feePercent
		}
		/*if enable == 1 {
			percent = feePercent
		} else {
			var tradeSetting model.TradeValue
			err, tradeSetting = model.GetTradeSetting()
			if err != nil {
				return
			}
			percent = tradeSetting.ServerRadio
		}*/
	}
	if percent < 0 {
		percent = 0
	}
	// percent的单位是万分之一
	fee = uint((orderAmount * percent) / 10000)

	desc = fmt.Sprintf("服务费比例%d", percent)
	return
}

func (o *Order) Make() (err error) {
	o.AmountDetail.Title = "订单总价"
	err = o.AmountDetail.Make()
	if err != nil {
		return
	}

	o.Amount = uint(o.AmountDetail.Amount)
	return
}

type OrderItems []OrderItem

func (i OrderItems) GetTitle() (title string) {
	title = i[0].Supplier.Name
	return
}

func (i OrderItems) GetApplicationShopID() (id int) {
	id = i[0].ApplicationShopID
	return
}
func (i OrderItems) GetLogoUrl() (logoUrl string) {
	logoUrl = i[0].Supplier.ShopLogo
	return
}
func (i OrderItems) GetAmount() (amount uint) {
	for _, orderItem := range i {
		amount += orderItem.Amount
	}
	return
}
func (i OrderItems) GetIsPlugin() (isPlugin int) {
	isPlugin = i[0].IsPlugin
	return
}
func (i OrderItems) GetSupplierID() (supplierID uint) {
	supplierID = i[0].Supplier.ID
	return
}
func (i OrderItems) GetGatherSupplyID() (supplierID uint) {
	supplierID = i[0].GatherSupplyID
	return
}
func (i OrderItems) GetApplication() (application Application) {
	application = i[0].Application
	return
}
func (i OrderItems) GetUser() (user User) {
	user = i[0].User
	return
}

func (i OrderItems) GetFee(percent int) (amount uint) {
	for _, orderItem := range i {
		if orderItem.NotFee == 1 {
			continue
		}
		amount += uint((int(orderItem.PaymentAmount) * percent) / 10000)
	}
	return
}

func (i OrderItems) GetProductAmount() (amount uint) {
	for _, orderItem := range i {
		amount += orderItem.Price * orderItem.Qty
	}
	return
}
func (i OrderItems) GetSupplyAmount() (amount uint) {
	for _, orderItem := range i {
		amount += orderItem.SupplyAmount
	}
	return
}
func (i OrderItems) GetCostAmount() (amount uint) {
	for _, orderItem := range i {
		amount += orderItem.CostAmount
	}
	return
}
func (i OrderItems) GetFreight() (amount uint) {
	for _, orderItem := range i {
		amount += orderItem.Freight
	}
	return
}
func (i OrderItems) GetGoodsTotal() (qty uint) {
	for _, orderItem := range i {
		qty += orderItem.Qty
	}
	return
}

type OrderItem struct {
	Title             string       `json:"title"`               // 标题
	SkuTitle          string       `json:"sku_title"`           // sku标题
	Price             uint         `json:"price"`               // 成交价
	CostAmount        uint         `json:"cost_amount"`         // 成本金额
	SupplyAmount      uint         `json:"supply_amount"`       // sku金额
	PaymentAmount     uint         `json:"payment_amount"`      // 均摊支付金额
	Amount            uint         `json:"amount"`              // 总金额
	Qty               uint         `json:"qty"`                 // 数量
	Unit              string       `json:"unit"`                // 计量单位
	Freight           uint         `json:"freight"`             // 运费（按权重比例均摊）
	ImageUrl          string       `json:"image_url"`           // 图片地址
	Supplier          Supplier     `json:"supplier"`            // 供应商信息
	GatherSupply      GatherSupply `json:"gather_supply"`       // 供应链信息
	Options           Options      `json:"options"`             // 规格信息
	Application       Application  `json:"application"`         // 供应链信息
	ApplicationShopID int          `json:"application_shop_id"` // 供应链信息
	User              User         `json:"user"`                //用户信息

	SupplierID     uint `json:"supplier_id"`
	GatherSupplyID uint `json:"gather_supply_id"`

	UserID         uint                    `json:"-"`
	ProductID      uint                    `json:"product_id"`
	SkuID          uint                    `json:"sku_id"`
	OriginalSkuID  uint                    `json:"-"`
	ShoppingCartID uint                    `json:"shopping_cart_id"`
	IsPlugin       int                     `json:"is_plugin"`
	NotFee         int                     `json:"not_fee"`
	AmountDetail   yzResponse.AmountDetail `json:"amount_detail"` // 金额明细
}

func (i OrderItem) Init() (err error) {
	return
}
func (i OrderItem) Make() (err error) {
	err = i.AmountDetail.Make()
	if err != nil {
		return
	}

	return
}

// GetCheckedShoppingCarts
//
// @function: GetCheckedShoppingCarts
// @description: 获取选中的购物车记录
// @return: err error, list interface{}
func GetCheckedShoppingCarts(shoppingCart ShoppingCart) (err error, shoppingCartList ShoppingCarts) {
	//排除已删除的商品,避免造成用户无法购买商品
	joinWhere := "INNER join products on products.id = shopping_carts.product_id and products.deleted_at is null INNER join skus on skus.id = shopping_carts.sku_id and skus.deleted_at is null"

	db := source.DB().Joins(joinWhere).Preload("Sku").Preload("Product").
		Preload("Supplier").Preload("Address").Preload("User.Application").Preload("Application").
		Where("user_id = ?", shoppingCart.UserID).
		Where("status = ?", 0).
		Where("buy_id = ?", shoppingCart.BuyID).
		Where("checked = ?", 1)
	if shoppingCart.BuyWay == gva.BATCHORDERBUYWAY || shoppingCart.BuyWay == gva.BYNCONFIRMBUYWAY || shoppingCart.BuyWay == gva.FULUCONFIRMBUYWAY || shoppingCart.BuyWay == 4 {
		db.Where("buy_way = ?", shoppingCart.BuyWay)
	}
	err = db.Find(&shoppingCartList).Error

	if err != nil {
		return
	}
	err, shopSetting := setting.Get()
	if err != nil {
		return
	}
	defaultSupplier := Supplier{
		ID:       0,
		Name:     shopSetting.ShopName,
		ShopLogo: shopSetting.ShopLogo,
	}
	for i, cart := range shoppingCartList {
		if cart.SupplierID == 0 {
			shoppingCartList[i].Supplier = defaultSupplier
		}
	}
	return
}

// ClearCheckedShoppingCarts
//
// @function: ClearCheckedShoppingCarts
// @description: 清空购物车记录
// @return: err error, list interface{}
func ClearCheckedShoppingCarts(shoppingCart ShoppingCart) (err error) {
	err = source.DB().
		Where("user_id = ?", shoppingCart.UserID).
		Where("status = ?", 0).
		Where("buy_id = ?", shoppingCart.BuyID).
		Where("buy_way = ?", shoppingCart.BuyWay).
		Where("checked = ?", 1).Delete(&ShoppingCart{}).Error
	return
}

type CheckoutItem struct {
	CartItem     ShoppingCart
	OrderItem    OrderItem
	ShippingItem freight.Item
}

type ShoppingCart struct {
	source.SoftDel
	ID                  uint            `json:"id"`
	Qty                 uint            `json:"qty"`                 // 数量
	SkuID               uint            `json:"sku_id"`              // sku id
	ProductID           uint            `json:"product_id"`          // 产品id
	AddressID           uint            `json:"address_id"`          // 地址id
	SupplierID          uint            `json:"supplier_id"`         // 供应商id
	ShippingMethodID    int             `json:"shipping_method_id"`  // 配送方式id
	ApplicationID       int             `json:"application_id"`      // 应用id
	ApplicationShopID   int             `json:"application_shop_id"` // 应用id
	UserID              uint            `json:"-"`
	BuyID               uint            `json:"buy_id"`   // 购买id
	BuyWay              int             `json:"buy_way"`  //购买途径 0购物车 1立即购买 2接口购买
	Sku                 Sku             `json:"sku"`      // sku信息
	Product             Product         `json:"product"`  // 产品信息
	User                User            `json:"user"`     // 会员信息
	Address             address.Address `json:"address"`  // 地址信息
	Supplier            Supplier        `json:"supplier"` // 供应商信息
	Application         Application     `json:"-"`
	ShareLiveRoomId     uint            `json:"share_live_room_id" gorm:"column:share_live_room_id;comment:直播间id 存在代表直播间下单;type:int;default:0;"`   // 直播间id 存在代表直播间下单
	EventDistributionId uint            `json:"event_distribution_id" form:"event_distribution_id" gorm:"column:event_distribution_id;comment:活动id;index;"`  //团购活动id
	Checked             int             `json:"checked" form:"checked" gorm:"column:checked;comment:选中（1是0否）;default:0;type:tinyint;size:1;"`              //是否选中 1是0否
	IsExpired           int             `json:"is_expired" form:"is_expired" gorm:"column:is_expired;comment:是否过期（1是0否）;default:0;type:tinyint;size:1;"` //是否失效 1是0否
	ExpiredMessage      string          `json:"expired_message" form:"expired_message" gorm:"column:expired_message;comment:失效原因;type:varchar(255);"`      //失效原因
	ThirdOrderSN        string          `json:"third_order_sn" form:"third_order_sn" gorm:"column:third_order_sn;comment:第三方订单编号;"`
}

func (sc ShoppingCart) Check() (err error) {
	if sc.Product.IsDisplay == 0 {
		err = errors.New(fmt.Sprintf("商品(%s)已下架", sc.Product.Title))
		return
	}
	if sc.Product.Freeze == 1 {
		err = errors.New(fmt.Sprintf("商品(%s)已被冻结，无法购买", sc.Product.Title))
		return
	}
	err, stockAmount := stock.GetSkuStock(sc.SkuID)
	if stockAmount < sc.Qty {
		err = errors.New(fmt.Sprintf("商品(%s-%s)库存不足%d%s", sc.Product.Title, sc.Sku.Title, sc.Qty, sc.Product.Unit))
		return
	}
	return
}

type ShoppingCarts []ShoppingCart

func (scs ShoppingCarts) Check() (error, bool) {
	var err error
	var errs []error
	var errsMsg string
	for _, cs := range scs {
		err = cs.Check()
		if err != nil {
			errs = append(errs, err)
			err = source.DB().Model(&ShoppingCart{}).Where("id = ?", cs.ID).Updates(map[string]interface{}{"is_expired": 1, "checked": 0, "expired_message": err.Error()}).Error
			if err != nil {
				log.Log().Error("更新购物车失效失败", zap.Any("cs", cs), zap.Error(err))
			}
		}
	}
	if len(errs) > 0 {
		for _, e := range errs {
			errsMsg += e.Error() + ";"
		}
		return errors.New(errsMsg), false
	}

	return nil, true
}

type Sku struct {
	source.SoftDel
	ID            uint   `json:"id"`
	Title         string `json:"title"`        // 标题
	Price         uint   `json:"price"`        // 价格
	CostPrice     uint   `json:"cost_price"`   // 成本
	OriginPrice   uint   `json:"origin_price"` // 市场价
	Stock         int    `json:"stock"`        // 库存
	Weight        uint   `json:"weight"`       // 重量
	ImageUrl      string `json:"image_url"`    // 图片地址
	ProductID     uint   `json:"-"`
	OriginalSkuID uint   `json:"-"`

	Options Options `json:"options"`
}

type Options []Option
type Option struct {
	SpecName     string `json:"spec_name"`      // 规格名
	SpecItemName string `json:"spec_item_name"` // 规格项名
}

func (value Options) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *Options) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type User struct {
	ID          uint        `json:"id"`
	LevelID     uint        `json:"level_id"`
	Application Application `json:"application" gorm:"foreignKey:MemberID;references:id"`
}
type Product struct {
	source.SoftDel
	ID                       uint   `json:"id"`
	Title                    string `json:"title"`         // 标题
	Category1ID              int    `json:"category_1_id"` // 1级分类id
	Category2ID              int    `json:"category_2_id"` // 2级分类id
	Category3ID              int    `json:"category_3_id"` // 3级分类id
	Sales                    int    `json:"sales"`         // 销量
	Long                     uint   `json:"long"`          // 长度（mm）
	Wide                     uint   `json:"wide"`          // 宽度（mm）
	High                     uint   `json:"high"`          // 高度（mm）
	Volume                   uint   `json:"volume"`        // 体积（mm³）
	Freight                  uint   `json:"freight"`       // 运费（分）
	FreightType              int    `json:"freight_type"`  // 运费规则（0统一1模板）
	ImageUrl                 string `json:"image_url"`     // 主图地址
	IsDisplay                uint   `json:"-"`             // 1是0否
	Freeze                   uint   `json:"-"`             // 1是0否
	Unit                     string `json:"unit"`          // 单位
	IsSingleOrder            int    `json:"is_single_order"`
	FreightTemplateID        uint   `json:"-"` // 运费模板id
	SupplierID               uint   `json:"-"`
	GatherSupplyID           uint   `json:"-"`
	IsPlugin                 int    `json:"-"`         //是否为插件商品
	ShopName                 string `json:"shop_name"` //阿里店铺名称
	JushuitanDistributorCoId string `json:"jushuitan_distributor_co_id"`
	NotDiscount              int    `json:"not_discount"` // 不参与折扣
	NotFee                   int    `json:"not_fee"`      // 不参与技术服务费
	Source                   int    `json:"source"`
	// 会员价独立开关
	UserPriceSwitch int `json:"user_price_switch" gorm:"column:user_price_switch;comment:会员价独立开关;"`
	// 会员价
	UserPrice model.UserPrice `json:"user_price" gorm:"column:user_price;comment:会员价设置;"`
}

type Supplier struct {
	ID       uint   `json:"id"`
	Name     string `json:"name"`      // 名
	ShopLogo string `json:"shop_logo"` // logo
}
type GatherSupply struct {
	ID         uint   `json:"id"`
	Name       string `json:"name"`      // 名
	ShopLogo   string `json:"shop_logo"` // logo
	CategoryID uint   `json:"category_id"`
}

func ShoppingCartCheckout(userID uint, shoppingCarts ShoppingCarts) (error, Checkout) {
	var err error
	var checkout Checkout
	if len(shoppingCarts) == 0 {
		err = errors.New("请选择要下单的商品")
	}
	var ok bool
	err, ok = shoppingCarts.Check()
	if !ok {
		return err, checkout
	}
	var items []CheckoutItem
	var item CheckoutItem
	// 将购物车条目转换成订单条目和配送条目
	for _, shoppingCart := range shoppingCarts {
		item.CartItem = shoppingCart
		err, item.OrderItem = GetOrderItemFromShoppingCart(shoppingCart)
		if err != nil {
			return err, checkout
		}
		// 配送条目
		item.ShippingItem = freight.Item{
			Amount:            item.OrderItem.Amount,
			Qty:               item.CartItem.Qty,
			Title:             item.CartItem.Product.Title,
			Weight:            item.CartItem.Sku.Weight,
			Long:              item.CartItem.Product.Long,
			Wide:              item.CartItem.Product.Wide,
			High:              item.CartItem.Product.High,
			Volume:            item.CartItem.Product.Volume,
			Freight:           item.CartItem.Product.Freight,
			FreightType:       item.CartItem.Product.FreightType,
			FreightTemplateID: item.CartItem.Product.FreightTemplateID,
			GatherSupplyID:    item.CartItem.Product.GatherSupplyID,
			OriginalSkuID:     item.CartItem.Sku.OriginalSkuID,
			ID:                item.CartItem.Sku.ID,
		}
		err = copier.Copy(&item.ShippingItem.Address, item.CartItem.Address)
		if err != nil {
			return err, checkout
		}
		items = append(items, item)
	}
	// 按照条件分组
	err, itemGroups := GroupItems(items)
	if err != nil {
		return err, checkout
	}

	// 使用 WaitGroup 等待所有 goroutine 完成
	var wg sync.WaitGroup

	// 创建一个 channel 来收集成功的订单，根据 itemGroups 的数量来设置缓冲大小
	successfulOrdersChan := make(chan Order, len(itemGroups))
	// 创建一个 channel 来收集失败的订单，根据 itemGroups 的数量来设置缓冲大小
	failedOrdersChan := make(chan Order, len(itemGroups))

	// 每组分别并发结算
	for key, itemGroup := range itemGroups {
		wg.Add(1)
		go func(key string, itemGroup []CheckoutItem) {
			defer wg.Done()

			// 处理每个itemGroup的下单逻辑
			var order Order
			err, order = GetOrderFromItems(key, userID, itemGroup[0].CartItem.ShippingMethodID, itemGroup[0].CartItem.Address, itemGroup)
			for _, checkoutItem := range itemGroup {
				order.ShoppingCarts = append(order.ShoppingCarts, checkoutItem.CartItem)
			}
			order.ThirdOrderSN = itemGroup[0].CartItem.ThirdOrderSN
			if err != nil {
				// 尝试发送错误到错误 channel，如果channel已满，则忽略（避免阻塞）
				select {
				case failedOrdersChan <- order:
				}
				return
			}

			order.ShareLiveRoomId = itemGroup[0].CartItem.ShareLiveRoomId
			order.EventDistributionId = itemGroup[0].CartItem.EventDistributionId

			// 将成功创建的订单发送到订单 channel
			successfulOrdersChan <- order
		}(key, itemGroup)
	}

	// 创建一个 goroutine 来关闭订单 channel，当所有下单 goroutine 完成时
	go func() {
		wg.Wait()
		close(successfulOrdersChan)
		close(failedOrdersChan)
	}()

	// 从 failedOrdersChan 收集所有失败的订单
	var failedOrders []Order
	for order := range failedOrdersChan {
		failedOrders = append(failedOrders, order)
	}
	// 如果有失败的订单, 并将所有失败订单的提示信息合并，返回错误
	if len(failedOrders) > 0 {
		var failedShoppingCarts ShoppingCarts
		for i, order := range failedOrders {
			for _, shoppingCart := range order.ShoppingCarts {
				failedShoppingCarts = append(failedShoppingCarts, shoppingCart)
			}
			if i == 0 {
				err = errors.New(order.ErrorMessage)
			} else {
				err = errors.New(err.Error() + ";" + order.ErrorMessage)
			}
		}
		checkout.FailedOrders = failedOrders
		return err, checkout
	}
	// 从 successfulOrdersChan 收集所有订单
	for order := range successfulOrdersChan {
		checkout.Orders = append(checkout.Orders, order)
	}
	if len(checkout.Orders) == 0 {
		err = errors.New("下单失败")
		return err, checkout
	}
	// 获取结算信息
	checkout.DefaultAddress = shoppingCarts[0].Address
	// 获取发票可选类型
	err, checkout.BillType = service.GetBillType()
	if err != nil {
		return err, checkout
	}
	err = checkout.Make()
	if err != nil {
		return err, checkout
	}
	return nil, checkout
}

func GroupItems(items []CheckoutItem) (err error, itemGroups map[string][]CheckoutItem) {
	itemGroups = map[string][]CheckoutItem{}
	for _, item := range items {
		// 分单key
		key := GetItemKey(item)
		// 根据key分组
		itemGroups[key] = append(itemGroups[key], item)
	}
	return
}

func GetItemKey(item CheckoutItem) string {
	//直播间id 按照直播间进行拆分订单
	//+ "_"+ strconv.Itoa(int(item.CartItem.ShareLiveRoomId))
	// + "_" + strconv.Itoa(int(item.CartItem.EventDistributionId))  //团购活动id
	if item.CartItem.Product.Source == int(common.SUPPLY_TIANMA) {
		return strconv.Itoa(int(item.CartItem.Supplier.ID)) + "-" + strconv.Itoa(int(item.CartItem.Product.GatherSupplyID)) + "-" + strconv.Itoa(int(item.CartItem.Address.ID)) + "-" + strconv.Itoa(item.CartItem.ShippingMethodID) + "_" + strconv.Itoa(int(item.CartItem.ShareLiveRoomId)) + "_" + item.CartItem.Product.ShopName + "_" + item.CartItem.Product.JushuitanDistributorCoId + "_" + strconv.Itoa(int(item.CartItem.EventDistributionId)) + strconv.Itoa(int(item.CartItem.Product.ID))
	}
	if item.CartItem.Product.IsSingleOrder == 1 {
		return strconv.Itoa(int(item.CartItem.Supplier.ID)) + "-" + strconv.Itoa(int(item.CartItem.Product.GatherSupplyID)) + "-" + strconv.Itoa(int(item.CartItem.Address.ID)) + "-" + strconv.Itoa(item.CartItem.ShippingMethodID) + "_" + strconv.Itoa(int(item.CartItem.SkuID)) + "_" + strconv.Itoa(int(item.CartItem.ShareLiveRoomId)) + "_" + item.CartItem.Product.ShopName + "_" + item.CartItem.Product.JushuitanDistributorCoId + "_" + strconv.Itoa(int(item.CartItem.EventDistributionId))
	} else {
		return strconv.Itoa(int(item.CartItem.Supplier.ID)) + "-" + strconv.Itoa(int(item.CartItem.Product.GatherSupplyID)) + "-" + strconv.Itoa(int(item.CartItem.Address.ID)) + "-" + strconv.Itoa(item.CartItem.ShippingMethodID) + "_" + strconv.Itoa(int(item.CartItem.ShareLiveRoomId)) + "_" + item.CartItem.Product.ShopName + "_" + item.CartItem.Product.JushuitanDistributorCoId + "_" + strconv.Itoa(int(item.CartItem.EventDistributionId))
	}

}

func GetOrderItemFromShoppingCart(shoppingCart ShoppingCart) (err error, orderItem OrderItem) {
	orderItem.Qty = shoppingCart.Qty
	{

		levelDiscountPercent := 10000
		// levelDiscountPercent的单位是万分之一
		var levelDiscountPrice uint
		var tip string
		tip = "商品等级优惠"
		// 商品参与会员等级折扣
		if shoppingCart.Product.NotDiscount == 0 {
			if shoppingCart.User.LevelID > 0 {
				var calculateRes bool
				// 商品独立等级折扣设置
				if shoppingCart.Product.UserPriceSwitch == 1 {
					var childTip string
					levelDiscountPrice, childTip, calculateRes = shoppingCart.Product.UserPrice.GetProductLevelDiscountPrice(shoppingCart.Sku.Price, shoppingCart.User.LevelID)
					if childTip != "" {
						tip = tip + "|" + childTip
					}
				}
				// 商品没有设置该等级，使用默认折扣
				if calculateRes == false {
					// 等级折扣
					err, levelDiscountPercent, _ = level.GetLevelDiscountPercent(shoppingCart.User.LevelID)
					if err != nil {
						return
					}
					err, levelDiscountPrice = level.GetLevelDiscountAmount(shoppingCart.Sku.Price, shoppingCart.Sku.CostPrice, levelDiscountPercent)
					if err != nil {
						return
					}

				}
			} else {
				levelDiscountPrice = shoppingCart.Sku.Price * uint(levelDiscountPercent) / 10000
				tip = ""
			}
		} else {
			levelDiscountPrice = shoppingCart.Sku.Price
			tip = ""
		}

		//千人千价 替换价格
		thousandsPricesStatus, thousandsPrice := GetThousandsPrices(shoppingCart.UserID, shoppingCart.SkuID)
		if thousandsPrice > 0 {
			if thousandsPricesStatus == 1 {
				levelDiscountPrice = thousandsPrice
				tip = "商品池专属价格"
				//不需要单独计算技术服务费了
				shoppingCart.Product.NotFee = 1
			} else if thousandsPricesStatus == 2 {
				levelDiscountPrice = thousandsPrice
				tip = "商品池价格"
			}
		}

		if tip != "" {
			orderItem.AmountDetail.AddItem(
				yzResponse.AmountDetail{
					Title:  tip,
					Amount: -int((shoppingCart.Sku.Price - levelDiscountPrice) * shoppingCart.Qty),
				},
			)
		}

		orderItem.Price = levelDiscountPrice
		//if orderItem.Price == 0 {
		//	// 最小为1分
		//	orderItem.Price = 1
		//}

	}
	// 不计算技术服务费
	orderItem.NotFee = shoppingCart.Product.NotFee
	orderItem.CostAmount = shoppingCart.Sku.CostPrice * shoppingCart.Qty
	orderItem.SupplyAmount = shoppingCart.Sku.Price * shoppingCart.Qty

	orderItem.Amount = orderItem.Price * shoppingCart.Qty

	orderItem.PaymentAmount = orderItem.Amount // todo 暂时与金额相同
	if shoppingCart.Sku.ImageUrl != "" {
		orderItem.ImageUrl = shoppingCart.Sku.ImageUrl
	} else {
		orderItem.ImageUrl = shoppingCart.Product.ImageUrl
	}
	orderItem.Title = shoppingCart.Product.Title
	orderItem.IsPlugin = shoppingCart.Product.IsPlugin
	orderItem.Unit = shoppingCart.Product.Unit
	orderItem.SkuTitle = shoppingCart.Sku.Title
	orderItem.Options = shoppingCart.Sku.Options
	orderItem.Supplier = shoppingCart.Supplier
	orderItem.SupplierID = shoppingCart.Product.SupplierID
	orderItem.GatherSupplyID = shoppingCart.Product.GatherSupplyID
	orderItem.UserID = shoppingCart.UserID
	orderItem.ProductID = shoppingCart.ProductID
	orderItem.SkuID = shoppingCart.SkuID
	orderItem.OriginalSkuID = shoppingCart.Sku.OriginalSkuID
	orderItem.ShoppingCartID = shoppingCart.ID
	orderItem.Application = shoppingCart.User.Application
	orderItem.ApplicationShopID = shoppingCart.ApplicationShopID
	orderItem.User = shoppingCart.User
	// 记录总价
	orderItem.AmountDetail.AddItem(
		yzResponse.AmountDetail{
			Title:  "商品总价",
			Amount: int(orderItem.Amount),
		},
	)
	//
	err = orderItem.Make()
	return
}

func GetOrderFromItems(key string, userID uint, shippingMethodID int, address address.Address, items []CheckoutItem) (err error, order Order) {
	// 订单条目
	var orderItems []OrderItem
	// 配送条目
	var shippingItems []freight.Item
	for _, item := range items {
		//如果是团购活动分发的订单这里变为1
		if item.CartItem.EventDistributionId != 0 {
			item.OrderItem.IsPlugin = 1
		}
		// 订单条目初始化
		err = item.OrderItem.Init()
		if err != nil {
			order.ErrorMessage = order.ErrorMessage + ";" + err.Error()
		}
		orderItems = append(orderItems, item.OrderItem)
		shippingItems = append(shippingItems, item.ShippingItem)
		//订单条目

	}
	if order.ErrorMessage != "" {
		err = errors.New(order.ErrorMessage)
		return
	}
	var freightDetail freight.Freight
	err, freightDetail = freight.GetItemsFreightDetail(shippingItems)
	if err != nil {
		order.ErrorMessage = err.Error()
		return
	}
	// 配送信息
	shippingInfo := shipping.ShippingInfo{ShippingMethodID: shippingMethodID, Freight: freightDetail}
	// 订单初始化
	err, order = GetOrder(key, shippingInfo, address, orderItems)
	if err != nil {
		order.ErrorMessage = err.Error()
		return
	}

	return
}

func GetOrder(key string, shippingInfo shipping.ShippingInfo, address address.Address, orderItems OrderItems) (err error, order Order) {
	err = order.Init(key, orderItems.GetUser(), orderItems.GetSupplierID(), orderItems.GetGatherSupplyID(), orderItems.GetIsPlugin(), orderItems.GetApplication(), shippingInfo, address, orderItems)
	if err != nil {
		return
	}
	// 活动
	// 优惠券
	// 抵扣

	err = order.Make()
	if err != nil {
		order.Message = err.Error()
		order.CanConfirm = false
		return
	}
	return
}
