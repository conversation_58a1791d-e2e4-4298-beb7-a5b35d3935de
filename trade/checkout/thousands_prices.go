package checkout

import (
	"gin-vue-admin/cmd/gva"
	"github.com/chenhg5/collection"
	"yz-go/source"
	"yz-go/utils"
)

type UserForThousandsPrices struct {
	source.Model
	ThousandsPricesID uint            `json:"thousands_prices_id"`
	ThousandsPrices   ThousandsPrices `json:"thousands_prices"`
}

func (userForThousandsPrices UserForThousandsPrices) TableName() string {
	return "users"
}

type ThousandsPrices struct {
	source.Model
	Name                    string                      `json:"name"`
	Status                  int                         `json:"status"`
	IsFee                   int                         `json:"is_fee"` //是否包含技术服务费
	UnifyRatio              uint                        `json:"unify_ratio"`
	Products                []ProductForThousandsPrices `json:"products" gorm:"many2many:thousands_prices_products;"`
	ThousandsPricesProducts []ThousandsPricesProducts   `json:"thousands_prices_products"`
}
type ProductForThousandsPrices struct {
	source.Model
}

func (productForThousandsPrices ProductForThousandsPrices) TableName() string {
	return "products"
}

type ThousandsPricesProducts struct {
	source.Model
	ThousandsPricesID uint `json:"thousands_prices_id"`
	ProductID         uint `json:"product_id"`
	SkuID             uint `json:"sku_id"`
	Price             uint `json:"price"`
	SkuPrice          uint `json:"sku_price"`
}

func GetThousandsPrices(userID uint, skuID uint) (status int, price uint) {
	if collection.Collect(gva.GlobalAuth.ToolsPlugin).Contains(40) == false && utils.LocalEnv() == true {
		return
	}
	var user UserForThousandsPrices
	err := source.DB().Preload("ThousandsPrices").First(&user, userID).Error
	if err != nil {
		return
	}
	if user.ThousandsPrices.Status == 0 {
		return
	}
	var thousandsPricesProduct ThousandsPricesProducts
	err = source.DB().Where("thousands_prices_id = ?", user.ThousandsPricesID).Where("sku_id = ?", skuID).First(&thousandsPricesProduct).Error
	if err == nil {
		if thousandsPricesProduct.SkuPrice > 0 {
			if user.ThousandsPrices.IsFee == 1 {
				//包含技术服务费
				status = 1
			} else {
				//不包含技术服务费
				status = 2
			}
			price = thousandsPricesProduct.SkuPrice
		}

	}
	return
}
