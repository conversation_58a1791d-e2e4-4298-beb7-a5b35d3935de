package confirm

import (
	"github.com/jinzhu/copier"
	orderModel "order/model"
	"reflect"
	"testing"
	"trade/checkout"
)

func TestA(t *testing.T) {
	b := checkout.Options{}
	b = append(b, checkout.Option{SpecName: "颜色", SpecItemName: "红"})
	bb := checkout.OrderItems{}
	bbb := checkout.OrderItem{}
	bbb.Options = b
	bb = append(bb, bbb)
	var aa orderModel.OrderItems

	err := copier.CopyWithOption(&aa, bb, copier.Option{DeepCopy: true})
	println(err)
}
func TestShoppingCartConfirm(t *testing.T) {
	var err error
	type args struct {
		checkout checkout.Checkout
	}
	err, shoppingCarts := checkout.GetCheckedShoppingCarts(checkout.ShoppingCart{BuyID: 0, UserID: 87})
	if err != nil {
		t.Error(err)
	}
	err, checkoutInfo := checkout.ShoppingCartCheckout(87, shoppingCarts)
	if err != nil {
		t.Error(err)
	}
	tests := []struct {
		name        string
		args        args
		wantErr     error
		wantConfirm Confirm
	}{
		{
			name: "normal",
			args: args{
				checkout: checkoutInfo,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErr, gotConfirm := ShoppingCartConfirm(tt.args.checkout)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("ShoppingCartConfirm() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if reflect.DeepEqual(gotConfirm, tt.wantConfirm) {
				t.Errorf("ShoppingCartConfirm() gotConfirm = %v, want %v", gotConfirm, tt.wantConfirm)
			}
		})
	}
}
