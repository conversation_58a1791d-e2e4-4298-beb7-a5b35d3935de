package model

import (
	_ "embed"
	"encoding/json"
	"gin-vue-admin/admin/model"
	"yz-go/source"
)

//go:embed menu.json
var menu string

func Migrate() (err error) {
	err = source.DB().AutoMigrate(
		OrderDataRecord{},
	)
	// 菜单,权限
	menus := []model.SysMenu{}
	menuJson := menu
	json.Unmarshal([]byte(menuJson), &menus)
	model.GVA_MENUS = append(model.GVA_MENUS, menus...)
	if err != nil {
		return
	}

	//err = source.DB().Transaction(func(tx *gorm.DB) (err error) {
	//	var m model.SysBaseMenu
	//	// 删掉旧菜单
	//	err = source.DB().Unscoped().Delete(&m, "id >= ? and id<?", 900, 1000).Error
	//	if err != nil {
	//		return err
	//	}
	//	for _, v := range menus {
	//		menu := &v
	//		// 增加菜单
	//		err = service.AddMenuAuthority(menu, "888")
	//		if err != nil {
	//			return
	//		}
	//	}
	//	return
	//})
	return
}
