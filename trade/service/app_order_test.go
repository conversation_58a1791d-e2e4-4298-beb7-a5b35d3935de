package service

import (
	"reflect"
	"region/model"
	"testing"
	"trade/request"
)

func TestMatchingRegion(t *testing.T) {
	addressRequest := request.Address{
		Province:    "贵州省",
		City:        "六盘水市",
		Area:        "水城区",
		Street:      "其他",
		Description: "贵州省六盘水市水城区六盘水市红桥新区贵悦府",
	}
	// 测试地址是否匹配
	err, province, city, county, town := MatchingRegion(addressRequest)
	if err != nil {
		t.Errorf("TestMatchingRegion err:%v", err)
	}
	println("province:%v,city:%v,county:%v,town:%v", province.Name, city.Name, county.Name, town.Name)
}
func TestGetOrSetAddress(t *testing.T) {
	type args struct {
		addressRequest request.Address
	}
	tests := []struct {
		name          string
		args          args
		wantErr       error
		wantAddressId uint
	}{
		{
			name: "汤池街道",
			args: args{request.Address{
				Province: "贵州省",
				City:     "遵义市",
				Area:     "遵义县",
				Street:   "龙坑街道",
			}},
			wantErr:       nil,
			wantAddressId: 1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErr, gotAddressId := GetOrSetAddress(tt.args.addressRequest)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("GetOrSetAddress() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if gotAddressId != tt.wantAddressId {
				t.Errorf("GetOrSetAddress() gotAddressId = %v, want %v", gotAddressId, tt.wantAddressId)
			}
		})
	}
}

func TestFindOrCreateTown(t *testing.T) {
	gotErr, gotTown := FindOrCreateTown("新的街道", 110101)
	println(gotErr, gotTown.ID)

}

func TestMatchingRegion1(t *testing.T) {
	type args struct {
		addressRequest request.Address
	}
	tests := []struct {
		name         string
		args         args
		wantErr      error
		wantProvince model.Region
		wantCity     model.Region
		wantCounty   model.Region
		wantTown     model.Region
	}{
		// TODO: Add test cases.
		{
			name: "汤池街道",
			args: args{request.Address{
				Province:    "海南省",
				City:        "省直辖县级行政区划",
				Area:        "澄迈县",
				Street:      "",
				Description: "海南省澄迈县澄迈县老城镇中华坊牡丹园11号楼1201",
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErr, gotProvince, gotCity, gotCounty, gotTown := MatchingRegion(tt.args.addressRequest)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("MatchingRegion() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if !reflect.DeepEqual(gotProvince, tt.wantProvince) {
				t.Errorf("MatchingRegion() gotProvince = %v, want %v", gotProvince, tt.wantProvince)
			}
			if !reflect.DeepEqual(gotCity, tt.wantCity) {
				t.Errorf("MatchingRegion() gotCity = %v, want %v", gotCity, tt.wantCity)
			}
			if !reflect.DeepEqual(gotCounty, tt.wantCounty) {
				t.Errorf("MatchingRegion() gotCounty = %v, want %v", gotCounty, tt.wantCounty)
			}
			if !reflect.DeepEqual(gotTown, tt.wantTown) {
				t.Errorf("MatchingRegion() gotTown = %v, want %v", gotTown, tt.wantTown)
			}
		})
	}
}

func TestMatchRegionNew(t *testing.T) {
	type args struct {
		addressRequest request.Address
	}
	tests := []struct {
		name         string
		args         args
		wantErrNew   error
		wantProvince model.Region
		wantCity     model.Region
		wantCounty   model.Region
		wantTown     model.Region
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErrNew, gotProvince, gotCity, gotCounty, gotTown := MatchRegionNew(tt.args.addressRequest)
			if !reflect.DeepEqual(gotErrNew, tt.wantErrNew) {
				t.Errorf("MatchRegionNew() gotErrNew = %v, want %v", gotErrNew, tt.wantErrNew)
			}
			if !reflect.DeepEqual(gotProvince, tt.wantProvince) {
				t.Errorf("MatchRegionNew() gotProvince = %v, want %v", gotProvince, tt.wantProvince)
			}
			if !reflect.DeepEqual(gotCity, tt.wantCity) {
				t.Errorf("MatchRegionNew() gotCity = %v, want %v", gotCity, tt.wantCity)
			}
			if !reflect.DeepEqual(gotCounty, tt.wantCounty) {
				t.Errorf("MatchRegionNew() gotCounty = %v, want %v", gotCounty, tt.wantCounty)
			}
			if !reflect.DeepEqual(gotTown, tt.wantTown) {
				t.Errorf("MatchRegionNew() gotTown = %v, want %v", gotTown, tt.wantTown)
			}
		})
	}
}

func TestMatchRegion(t *testing.T) {
	type args struct {
		addressRequest request.Address
	}
	tests := []struct {
		name         string
		args         args
		wantErrNew   error
		wantProvince model.Region
		wantCity     model.Region
		wantCounty   model.Region
		wantTown     model.Region
	}{
		// TODO: Add test cases.
		{
			name: "汤池街道",
			args: args{request.Address{
				Province:    "河南省",
				City:        "郑州市",
				Area:        "郑东新区",
				Street:      "",
				Description: "金水东路88号金鹏时代小区",
			},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErrNew, gotProvince, gotCity, gotCounty, gotTown := MatchingRegion(tt.args.addressRequest)
			if !reflect.DeepEqual(gotErrNew, tt.wantErrNew) {
				t.Errorf("MatchRegionNew() gotErrNew = %v, want %v", gotErrNew, tt.wantErrNew)
			}
			if !reflect.DeepEqual(gotProvince, tt.wantProvince) {
				t.Errorf("MatchRegionNew() gotProvince = %v, want %v", gotProvince, tt.wantProvince)
			}
			if !reflect.DeepEqual(gotCity, tt.wantCity) {
				t.Errorf("MatchRegionNew() gotCity = %v, want %v", gotCity, tt.wantCity)
			}
			if !reflect.DeepEqual(gotCounty, tt.wantCounty) {
				t.Errorf("MatchRegionNew() gotCounty = %v, want %v", gotCounty, tt.wantCounty)
			}
			if !reflect.DeepEqual(gotTown, tt.wantTown) {
				t.Errorf("MatchRegionNew() gotTown = %v, want %v", gotTown, tt.wantTown)
			}
		})
	}
}
