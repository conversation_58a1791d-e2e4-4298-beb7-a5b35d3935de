package model

import (
	"time"
	"yz-go/source"
)

type CakeCity struct {
	Id          string `json:"id"`
	Name        string `json:"name"`
	FirstLetter string `json:"first_letter"`
}
type CityLists struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		CityList map[string][]CakeCity `json:"city_list"`
	} `json:"data"`
}

type PayResult struct {
	PublicRes
	Data string `json:"data"`
}

type ChannelOrderMoney struct {
	PublicRes
	Data struct {
		RemainMoney string `json:"remain_money"`
	} `json:"data"`
}

type ValidateDeliveryDates struct {
	Date                  string   `json:"date"`
	DateText              string   `json:"date_text"`
	DeliveryAmount        string   `json:"delivery_amount"`
	ValidateDeliveryTimes []string `json:"validate_delivery_times"`
}

type ValidateTakeDates struct {
	Date              string   `json:"date"`
	DateText          string   `json:"date_text"`
	ValidateTakeTimes []string `json:"validate_take_times"`
}

type DistributionRule struct {
	AddressId          string `json:"addressId"`
	DistributionRuleId string `json:"distribution_rule_id"`
	PublicRes
	Data struct {
		IsDistribution        string                  `json:"is_distribution"`
		CanTake               string                  `json:"can_take"`
		CanShip               string                  `json:"can_ship"`
		CanSame               string                  `json:"can_same"`
		ValidateDeliveryDates []ValidateDeliveryDates `json:"validate_delivery_dates"`
		ValidateTakeDates     []ValidateTakeDates     `json:"validate_take_dates"`
		ValidateSameRow       struct {
			DeliveryAmount string `json:"delivery_amount"`
		} `json:"validate_same_row"`
		DeliveryText string `json:"delivery_text"`
	} `json:"data"`
}

type UPDistributionRule struct {
	AddressId          string `json:"addressId"`
	DistributionRuleId string `json:"distribution_rule_id"`
	PublicRes
	Data struct {
		IsDistribution        string      `json:"is_distribution"`
		CanTake               string      `json:"can_take"`
		CanShip               string      `json:"can_ship"`
		CanSame               string      `json:"can_same"`
		ValidateDeliveryDates []string    `json:"validate_delivery_dates"`
		ValidateTakeDates     []string    `json:"validate_take_dates"`
		ValidateSameRow       interface{} `json:"validate_same_row"`
		DeliveryText          string      `json:"delivery_text"`
	} `json:"data"`
}

type ShipStatus struct {
	PublicRes
	Data struct {
		CanBuy int `json:"can_buy"`
	} `json:"data"`
}

type AddressInfo struct {
	PublicRes
	Data struct {
		Id         string `json:"id"`
		UserId     string `json:"user_id"`
		Name       string `json:"name"`
		Province   string `json:"province"`
		CityId     string `json:"city_id"`
		City       string `json:"city"`
		Area       string `json:"area"`
		Street     string `json:"street"`
		Addr       string `json:"addr"`
		Zip        string `json:"zip"`
		Phone      string `json:"phone"`
		ShipTime   string `json:"ship_time"`
		IsDefault  string `json:"is_default"`
		CreatedAt  string `json:"created_at"`
		LastUsedAt string `json:"last_used_at"`
		Lat        string `json:"lat"`
		Lng        string `json:"lng"`
		Landmark   string `json:"landmark"`
		Deleted    string `json:"deleted"`
		AreaCode   string `json:"area_code"`
	} `json:"data"`
}

type CakeUser struct {
	Id                  string `json:"id"`
	Username            string `json:"username"`
	Status              string `json:"status"`
	PhotoId             string `json:"photo_id"`
	MemberId            string `json:"member_id"`
	PhotoPath           string `json:"photo_path"`
	Name                string `json:"name"`
	Gender              string `json:"gender"`
	Description         string `json:"description"`
	EmailAddress        string `json:"email_address"`
	MobilePhone         string `json:"mobile_phone"`
	LoginType           string `json:"login_type"`
	Password            string `json:"password"`
	Nickname            string `json:"nickname"`
	Salt                string `json:"salt"`
	Type                string `json:"type"`
	Money               string `json:"money"`
	CakeMoney           string `json:"cake_money"`
	LifeMoney           string `json:"life_money"`
	ComplexMoney        string `json:"complex_money"`
	Points              string `json:"points"`
	FollowCount         string `json:"follow_count"`
	FollowerCount       string `json:"follower_count"`
	LikeCount           string `json:"like_count"`
	FavCount            string `json:"fav_count"`
	BirthdayType        string `json:"birthday_type"`
	BirthYear           string `json:"birth_year"`
	BirthMonth          string `json:"birth_month"`
	BirthDay            string `json:"birth_day"`
	Vcode               string `json:"vcode"`
	VcodeRetryTime      string `json:"vcode_retry_time"`
	VcodeGetTimes       string `json:"vcode_get_times"`
	VcodeTime           string `json:"vcode_time"`
	PasswordRetryTime   string `json:"password_retry_time"`
	PasswordLastRetryAt string `json:"password_last_retry_at"`
	ChannelNo           string `json:"channel_no"`
	ActivityId          string `json:"activity_id"`
	RegIp               string `json:"reg_ip"`
	Deleted             string `json:"deleted"`
	MobilePlace         string `json:"mobile_place"`
	MobileProvince      string `json:"mobile_province"`
	MobileCity          string `json:"mobile_city"`
	MobileIsp           string `json:"mobile_isp"`
	IsBind              string `json:"is_bind"`
	RedPacket           string `json:"red_packet"`
	BindTypes           string `json:"bind_types"`
	Flag                string `json:"flag"`
	Grade               string `json:"grade"`
	SaleChannel         string `json:"sale_channel"`
	CardName            string `json:"card_name"`
	PhoenixmilesCard    string `json:"phoenixmiles_card"`
	Discount            string `json:"discount"`
	Uniques             string `json:"uniques"`
	OpenId              string `json:"open_id"`
	CustomersId         string `json:"customers_id"`
	YzOpenId            string `json:"yz_open_id"`
	DistributionType    string `json:"distribution_type"`
	OpenMemberNotice    string `json:"open_member_notice"`
}

type UserInfo struct {
	PublicRes
	Data CakeUser `json:"data"`
}

type CityAreaList struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		AreaList []struct {
			Id   string `json:"id"`
			Name string `json:"name"`
		} `json:"area_list"`
	} `json:"data"`
}

type GetCityId struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Id string `json:"id"`
	} `json:"data"`
}
type BrandInfo struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Id               string `json:"id"`
		Name             string `json:"name"`
		ShortDescription string `json:"short_description"`
		Description      string `json:"description"`
		ImagePath        string `json:"image_path"`
		AlbumImagePath   string `json:"album_image_path"`
	} `json:"data"`
}

type LocalTime struct {
	time.Time
}
type CakeBrand struct {
	source.SoftDel
	ID        string            `json:"id" form:"id" `
	CreatedAt *source.LocalTime `json:"created_at"`
	UpdatedAt *source.LocalTime `json:"updated_at"`
	//Id               string `json:"id"`
	Name             string `json:"name"`
	Status           uint   `json:"status" gorm:"column:status;default:1"` //1  开启 2关闭
	CatId            string `json:"cat_id"`
	CatName          string `json:"cat_name"`
	ImagePath        string `json:"image_path"`
	AlbumImagePath   string `json:"album_image_path"`
	ShortDescription string `json:"short_description"`
	Description      string `json:"description" gorm:"column:description;type:longtext"`
	CityId           string `json:"city_id" gorm:"column:city_id;type:longtext"`
	ShopCount        int64  `json:"shop_count" gorm:"-"`
	CityCount        int    `json:"city_count" gorm:"-"`
}

//type BrandList struct {
//	Code int    `json:"code"`
//	Msg  string `json:"msg"`
//	Data struct {
//		BrandList []CakeBrand `json:"brand_list"`
//	} `json:"data"`
//}

type BrandList struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data []CakeBrand `json:"data"`
}
type CakeShop struct {
	source.Model
	ShopId    string `json:"shop_id"`
	ShopName  string `json:"shop_name"`
	Phone     string `json:"phone"`
	Lng       string `json:"lng"`
	Lat       string `json:"lat"`
	Address   string `json:"address"`
	BrandId   string `json:"brand_id"`
	BrandName string `json:"brand_name"`
	CityId    string `json:"city_id"`
	CityName  string `json:"city_name"`
	CatId     string `json:"cat_id"`
	Status    uint   `json:"status" gorm:"default:1"` //1开启 2 关闭
}

type ShopList struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		BrandId string `json:"brand_id"`
		City    struct {
			Id          string `json:"id"`
			Name        string `json:"name"`
			Code        string `json:"code"`
			FirstLetter string `json:"first_letter"`
			SortNum     string `json:"sort_num"`
			CreatedAt   string `json:"created_at"`
			Hot         string `json:"hot"`
			BrandCount  string `json:"brand_count"`
			Deleted     string `json:"deleted"`
			IsOpen      string `json:"is_open"`
			Province    string `json:"province"`
			IsXjs       string `json:"is_xjs"`
			ParentName  string `json:"parent_name"`
		} `json:"city"`
		Shops []CakeShop `json:"shops"`
	} `json:"data"`
}

type BrandCityList struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data []CakeBrand `json:"data"`
}
