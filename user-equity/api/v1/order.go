package v1

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	purchaseService "purchase-account/service"
	equity "user-equity/package/order"
	"user-equity/request"
	"user-equity/service"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

func GetPayType(c *gin.Context) {
	err, supplyID := service.GetSupplyID()
	if err != nil {
		yzResponse.FailWithMessage("查询供应链id失败", c)
		return
	}
	if err, data := purchaseService.GetSupplyPayType(supplyID); err != nil {
		yzResponse.FailWithMessage("获取支付方式失败", c)
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func GetOrderList(c *gin.Context) {
	var pageInfo request.OrderAdminSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetOrderInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func ExportOrderList(c *gin.Context) {
	var pageInfo request.OrderAdminSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, link := service.ExportOrderInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}

func RepairOrder(c *gin.Context) {
	var pageInfo yzRequest.GetById
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := utils.Verify(pageInfo); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	eq := &equity.Equity{}
	err = eq.InitSetting(0)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, _ = eq.RepairOrder(pageInfo.Id)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("提交成功", c)
}
