[{"ID": 460, "menuId": "460", "path": "userEquityIndex", "name": "userEquityIndex", "hidden": true, "parentId": "0", "component": "view/userEquity/index.vue", "meta": {"title": "会员权益", "icon": "user-solid", "defaultMenu": false, "keepAlive": false}, "sort": 40, "parameters": []}, {"ID": 461, "menuId": "461", "path": "nameuserEquityBaseIndex", "name": "nameuserEquityBaseIndex", "hidden": false, "parentId": "460", "component": "view/userEquity/base/index.vue", "meta": {"title": "基础设置", "icon": "info", "defaultMenu": false, "keepAlive": false}, "sort": 2, "parameters": []}, {"ID": 462, "menuId": "462", "path": "userEquityGoodsManageIndex", "name": "userEquityGoodsManageIndex", "hidden": false, "parentId": "460", "component": "view/userEquity/goodsManage/index.vue", "meta": {"title": "商品管理", "icon": "info", "defaultMenu": false, "keepAlive": false}, "sort": 3, "parameters": []}, {"ID": 463, "menuId": "463", "path": "userEquityOrderManageIndex", "name": "userEquityOrderManageIndex", "hidden": false, "parentId": "460", "component": "view/userEquity/orderManage/index.vue", "meta": {"title": "订单管理", "icon": "info", "defaultMenu": false, "keepAlive": false}, "sort": 3, "parameters": []}]