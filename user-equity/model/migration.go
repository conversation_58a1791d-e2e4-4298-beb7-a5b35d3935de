package model

import (
	_ "embed"
	"encoding/json"
	"errors"
	"gin-vue-admin/admin/model"
	"gin-vue-admin/cmd/gva"
	"github.com/chenhg5/collection"
	"gorm.io/gorm"
	"public-supply/common"
	publicModel "public-supply/model"
	"yz-go/source"
	"yz-go/utils"
)

//go:embed menu.json
var menu string

func Migrate() (err error) {
	err = source.DB().AutoMigrate(
		UserEquityProduct{},
		UserEquityOrder{},
		UserEquityConfirmOrderRequest{},
		UserEquityOrderInfo{},
		UserEquityHttp{},
	)
	var menus []model.SysMenu
	err = json.Unmarshal([]byte(menu), &menus)
	if collection.Collect(gva.GlobalAuth.ResourcesPlugin).Contains(19) == true || utils.LocalEnv() == false {
		model.GVA_MENUS = append(model.GVA_MENUS, menus...)
	}

	// 供应链表是否存在
	res := source.DB().Migrator().HasTable(&publicModel.GatherSupply{})
	if res == true {
		var supply publicModel.GatherSupply
		err = source.DB().Unscoped().Where("category_id = ?", common.USER_EQUITY).First(&supply).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil
			return
		}
		if supply.ID == 0 {
			supply.Name = "会员权益-private"
			supply.CategoryID = common.USER_EQUITY
			err = source.DB().Create(&supply).Error
			if err != nil {
				err = nil
				return
			}
			err = source.DB().Delete(&supply).Error
			if err != nil {
				err = nil
				return
			}
		}
	}

	return
}
