package product

import (
	categoryModel "category/model"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"github.com/go-resty/resty/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"
	productModel "product/model"
	productMq "product/mq"
	"sort"
	"strconv"
	"time"
	"user-equity/model"
	"yz-go/component/log"
	"yz-go/source"
	"yz-go/utils"
)

var (
	AppSecret       string
	AppID           string
	PricingStrategy model.PricingStrategy
	updateInfo      model.UpdateInfo
)

func ImportProductRun(supplyID uint) (err error) {
	var setting model.Setting
	err, setting = getSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("请先配置基础信息")
		return
	}
	var baseInfo model.BaseInfo
	baseInfo = setting.Values.BaseInfo
	if baseInfo.AppID == "" || baseInfo.AppSecret == "" {
		err = errors.New("请先配置基础信息")
		return
	}
	AppSecret = baseInfo.AppSecret
	AppID = baseInfo.AppID
	PricingStrategy = setting.Values.PricingStrategy
	updateInfo = setting.Values.UpdateInfo

	params := getParams()
	jsonStr, err := json.Marshal(params)
	client1 := resty.New()
	res, err := client1.R().EnableTrace().SetHeader("Content-Type", "multipart/form-data").SetBody(jsonStr).Post("http://openapi.tuikeyun.com/Recharge/productList")

	var response model.HttpResponse
	err = json.Unmarshal(res.Body(), &response)
	if err != nil {
		err = errors.New("解析请求结果错误")
		return
	}
	if response.Code != 1 {
		err = errors.New(response.Msg)
		return
	}

	var insertEquityProductList, updateEquityProductList []model.UserEquityProduct
	// 获取已经保存的商品map
	err, existedEquityProductMap := getExistedEquityProductMap()
	if err != nil {
		return
	}
	var responseEquityProductMap = make(map[int]model.HttpResponseData)
	for _, item := range response.Data {
		// api返回的商品 转 map
		responseEquityProductMap[item.Id] = item
		var jsonResult []byte
		jsonResult, err = json.Marshal(item)
		h := md5.New()
		h.Write(jsonResult)
		item.MD5 = hex.EncodeToString(h.Sum(nil))
		equityProduct := model.UserEquityProduct{}
		equityProduct.EQID = item.Id
		equityProduct.CouponName = item.CouponName
		if item.CouponNameType == "" {
			item.CouponNameType = "会员"
		}
		equityProduct.CouponNameType = item.CouponNameType
		equityProduct.CouponSuffixName = item.CouponSuffixName
		equityProduct.OriginalPrice = uint(item.OriginalPrice * 100)
		equityProduct.Prices = uint(item.Prices * 100)
		equityProduct.Zhekou = uint(item.Zhekou * 100)
		equityProduct.ProductNumberDetais = item.ProductNumberDetais
		equityProduct.Icon = item.Icon
		equityProduct.MD5 = item.MD5
		// 验证api返回的权益商品是否已经保存到UserEquityProduct表
		if _, existed := existedEquityProductMap[item.Id]; existed {
			// 验证是否相同:相同, 跳出 else 不相同, 修改
			// todo 目前商品下架 会员权益插件没有监听，没有清空md5. 先注释，都更新
			/*if item.MD5 == existedEquityProductMap[item.Id].MD5 && existedEquityProductMap[item.Id].LockStatus == 0 {
				continue
			}*/
			equityProduct.LockStatus = existedEquityProductMap[item.Id].LockStatus
			// 附加 要修改的权益商品
			updateEquityProductList = append(updateEquityProductList, equityProduct)
		} else {
			// 附加 要创建的权益商品
			insertEquityProductList = append(insertEquityProductList, equityProduct)
		}
	}

	// 获取默认三级分类
	var category1, category2, category3 categoryModel.Category
	err, category1, category2, category3 = getDefaultCategory(supplyID)
	if err != nil {
		return
	}
	// 默认品牌
	var brand categoryModel.Brand
	err, brand = getDefaultBrand(supplyID)
	if err != nil {
		return
	}
	var insertProductList []productModel.Product
	// 要修改的权益商品 map, 要修改的product map, 要修改的sku map
	var updateEquityProductMap, updateProductMap, updateSkuMap []map[string]interface{}
	var importedProductsMap map[uint][]productModel.Product
	var importedSkusMap map[uint]productModel.Sku
	err, importedProductsMap, importedSkusMap = getImportedProductsAndSkusMap(supplyID)
	if err != nil {
		return
	}
	// 组装商品
	for _, product := range insertEquityProductList {
		if _, existed := importedProductsMap[uint(product.EQID)]; existed {
			for _, item := range importedProductsMap[uint(product.EQID)] {
				productId := item.ID
				updateProductMap = append(updateProductMap, assembleProduct(product, productId, item.IsDisplay))

				skuId := importedSkusMap[item.ID].ID
				updateSkuMap = append(updateSkuMap, assembleSku(product, skuId))
			}

			continue
		}

		insertProductRow := productModel.Product{}
		insertProductRow.Title = product.CouponName + " " + product.CouponNameType + " " + product.CouponSuffixName
		insertProductRow.DetailImages = product.ProductNumberDetais
		insertProductRow.BrandID = brand.ID
		insertProductRow.Category1ID = category1.ID
		insertProductRow.Category2ID = category2.ID
		insertProductRow.Category3ID = category3.ID
		insertProductRow.Stock = 999999
		insertProductRow.IsDisplay = 1
		insertProductRow.SourceGoodsID = uint(product.EQID)
		insertProductRow.GatherSupplyID = supplyID
		insertProductRow.SingleOption = 1
		insertProductRow.IsPlugin = 1

		price, costPrice, originPrice, guidePrice, activityPrice := getPrices(product)

		insertProductRow.Unit = "件"
		insertProductRow.Price = price
		insertProductRow.CostPrice = costPrice
		insertProductRow.OriginPrice = originPrice
		insertProductRow.GuidePrice = guidePrice
		insertProductRow.ActivityPrice = activityPrice

		var sku productModel.Sku
		var options productModel.Options
		var option productModel.Option
		option.SpecName = "规格"
		option.SpecItemName = product.CouponNameType
		options = append(options, option)
		sku.Title = product.CouponSuffixName
		sku.Options = options
		sku.Weight = 0
		sku.Stock = 999999
		sku.IsDisplay = 1
		sku.Price = price
		sku.OriginPrice = originPrice
		sku.CostPrice = costPrice
		sku.GuidePrice = guidePrice
		sku.ActivityPrice = activityPrice
		sku.OriginalSkuID = int64(product.EQID)
		insertProductRow.Skus = append(insertProductRow.Skus, sku)
		insertProductList = append(insertProductList, insertProductRow)
	}
	// 批量创建权益商品
	if len(insertEquityProductList) > 0 {
		err = source.DB().CreateInBatches(insertEquityProductList, 500).Error
		if err != nil {
			return
		}
	}
	// 创建中台商品
	if len(insertProductList) > 0 {
		for _, goods := range insertProductList {
			err = source.DB().Create(&goods).Error
			if err != nil {
				log.Log().Error("创建中台商品失败")
			}
			err = productMq.PublishMessage(goods.ID, productMq.Create, 0)
			if err != nil {
				log.Log().Error("创建中台商品mq插入失败", zap.Any("product_id", goods.ID))
			}
		}
	}

	// 要修改的权益商品 map, 要修改的product map, 要修改的sku map
	for _, updateEquityProduct := range updateEquityProductList {
		updateEquityProductMap = append(updateEquityProductMap, assembleEquityProduct(updateEquityProduct))

		if _, existed := importedProductsMap[uint(updateEquityProduct.EQID)]; existed {
			for _, item := range importedProductsMap[uint(updateEquityProduct.EQID)] {
				productId := item.ID
				updateProductMap = append(updateProductMap, assembleProduct(updateEquityProduct, productId, item.IsDisplay))

				skuId := importedSkusMap[item.ID].ID
				updateSkuMap = append(updateSkuMap, assembleSku(updateEquityProduct, skuId))
			}
		}

		/*productId := importedProductsMap[uint(updateEquityProduct.EQID)].ID
		updateProductMap = append(updateProductMap, assembleProduct(updateEquityProduct, productId))

		skuId := importedSkusMap[importedProductsMap[uint(updateEquityProduct.EQID)].ID].ID
		updateSkuMap = append(updateSkuMap, assembleSku(updateEquityProduct, skuId))*/
	}
	// 要删除的权益商品ids, 要删除的中台商品ids
	var deleteEquityProductIds, deleteProductIds []uint
	// 循环已存在的权益商品数据, 对比api返回fulu商品数据, 不存在的删除
	for _, product := range existedEquityProductMap {
		if _, existed := responseEquityProductMap[product.EQID]; !existed {
			deleteEquityProductIds = append(deleteEquityProductIds, product.ID)

			if _, existed := importedProductsMap[uint(product.EQID)]; existed {
				for _, item := range importedProductsMap[uint(product.EQID)] {
					deleteProductIds = append(deleteProductIds, item.ID)
				}
			}

			//deleteProductIds = append(deleteProductIds, importedProductsMap[uint(product.EQID)].ID)
		}
	}
	// 删除权益商品
	if len(deleteEquityProductIds) > 0 {
		err = source.DB().Delete(&model.UserEquityProduct{}, deleteEquityProductIds).Error
		if err != nil {
			return
		}
	}
	// 删除中台商品 修改时间:2023年03月23日18:14:16 修改内容:不删除商品,只做下架处理 by:韦平
	if len(deleteProductIds) > 0 {
		var undercarriageProducts []map[string]interface{}
		for _, productId := range deleteProductIds {
			log.Log().Error("中台商品下架处理", zap.Any("product_id", productId))
			undercarriageProducts = append(undercarriageProducts, map[string]interface{}{"id": productId, "is_display": 0, "updated_at": time.Now().Format("2006-01-02 15:04:05")})
			err = productMq.PublishMessage(productId, productMq.Undercarriage, 0)
			if err != nil {
				log.Log().Error("删除中台商品mq插入失败", zap.Any("product_id", productId))
			}
		}
		err = source.BatchUpdate(undercarriageProducts, "products", "")
		if err != nil {
			return
		}
		/*err = source.DB().Delete(&productModel.Product{}, deleteProductIds).Error
		if err != nil {
			return
		}*/
	}
	// 批量修改权益商品
	if len(updateEquityProductMap) > 0 {
		err = source.BatchUpdate(updateEquityProductMap, "user_equity_products", "product_id")
		if err != nil {
			return
		}
	}
	//log.Log().Error("updateProductMap", zap.Any("updateProductMap", updateProductMap))
	// 批量修改中台商品
	if len(updateProductMap) > 0 {
		for _, row := range updateProductMap {
			log.Log().Error("中台商品修改处理", zap.Any("product_id", row["id"]))
			err = productMq.PublishMessage(row["id"].(uint), productMq.Edit, 0)
			if err != nil {
				log.Log().Error("中台商品修改处理mq插入失败", zap.Any("product_id", row["id"]))
			}
		}
		err = source.BatchUpdate(updateProductMap, "products", "")
		if err != nil {
			return
		}
	}
	// 批量修改中台商品规格
	if len(updateSkuMap) > 0 {
		err = source.BatchUpdate(updateSkuMap, "skus", "")
		if err != nil {
			return
		}
	}

	return
}

func getPrices(product model.UserEquityProduct) (price, costPrice, originPrice, guidePrice, activityPrice uint) {
	if PricingStrategy.Price.ComputeType == 1 {
		price = product.Prices
		if PricingStrategy.Price.PriceRatio > 0 {
			price = price * uint(PricingStrategy.Price.PriceRatio) / 10000
		}
	} else {
		price = product.OriginalPrice
		if PricingStrategy.Price.OriginalPriceRatio > 0 {
			price = price * uint(PricingStrategy.Price.OriginalPriceRatio) / 10000
		}
	}
	if PricingStrategy.CostPrice.ComputeType == 1 {
		costPrice = product.Prices
		if PricingStrategy.CostPrice.PriceRatio > 0 {
			costPrice = costPrice * uint(PricingStrategy.CostPrice.PriceRatio) / 10000
		}
	} else {
		costPrice = product.OriginalPrice
		if PricingStrategy.CostPrice.OriginalPriceRatio > 0 {
			costPrice = costPrice * uint(PricingStrategy.CostPrice.OriginalPriceRatio) / 10000
		}
	}
	if PricingStrategy.OriginPrice.ComputeType == 1 {
		originPrice = product.Prices
		if PricingStrategy.OriginPrice.PriceRatio > 0 {
			originPrice = originPrice * uint(PricingStrategy.OriginPrice.PriceRatio) / 10000
		}
	} else {
		originPrice = product.OriginalPrice
		if PricingStrategy.OriginPrice.OriginalPriceRatio > 0 {
			originPrice = originPrice * uint(PricingStrategy.OriginPrice.OriginalPriceRatio) / 10000
		}
	}
	if PricingStrategy.GuidePrice.ComputeType == 1 {
		guidePrice = product.Prices
		if PricingStrategy.GuidePrice.PriceRatio > 0 {
			guidePrice = guidePrice * uint(PricingStrategy.GuidePrice.PriceRatio) / 10000
		}
	} else {
		guidePrice = product.OriginalPrice
		if PricingStrategy.GuidePrice.OriginalPriceRatio > 0 {
			guidePrice = guidePrice * uint(PricingStrategy.GuidePrice.OriginalPriceRatio) / 10000
		}
	}
	if PricingStrategy.ActivityPrice.ComputeType == 1 {
		activityPrice = product.Prices
		if PricingStrategy.ActivityPrice.PriceRatio > 0 {
			activityPrice = activityPrice * uint(PricingStrategy.ActivityPrice.PriceRatio) / 10000
		}
	} else {
		activityPrice = product.OriginalPrice
		if PricingStrategy.ActivityPrice.OriginalPriceRatio > 0 {
			activityPrice = activityPrice * uint(PricingStrategy.ActivityPrice.OriginalPriceRatio) / 10000
		}
	}
	return
}

func assembleSku(product model.UserEquityProduct, skuId uint) (row map[string]interface{}) {
	row = make(map[string]interface{})
	row["id"] = skuId
	if updateInfo.AutoProductName == 1 {
		row["title"] = product.CouponSuffixName
	}
	if updateInfo.AutoPurchasePrice == 1 {
		price, costPrice, originPrice, guidePrice, activityPrice := getPrices(product)
		// 营销价
		row["activity_price"] = activityPrice
		// 指导价
		row["guide_price"] = guidePrice
		// 市场价
		row["origin_price"] = originPrice
		// 成本价
		row["cost_price"] = costPrice
		// 供货价
		row["price"] = price
	}
	return
}

func assembleProduct(product model.UserEquityProduct, productId uint, isDisplay int) (row map[string]interface{}) {
	row = make(map[string]interface{})
	row["id"] = productId
	if updateInfo.AutoProductName == 1 {
		row["title"] = product.CouponName + " " + product.CouponNameType + " " + product.CouponSuffixName
	}
	if updateInfo.AutoDetails == 1 {
		row["detail_images"] = product.ProductNumberDetais
	}
	if product.LockStatus == 0 {
		row["is_display"] = 1
	} else {
		row["is_display"] = isDisplay
	}
	row["single_option"] = 1
	row["is_plugin"] = 1
	if updateInfo.AutoPurchasePrice == 1 {
		price, costPrice, originPrice, guidePrice, activityPrice := getPrices(product)
		row["price"] = price
		row["cost_price"] = costPrice
		row["origin_price"] = originPrice
		row["guide_price"] = guidePrice
		row["activity_price"] = activityPrice
	}
	row["updated_at"] = time.Now().Format("2006-01-02 15:04:05")
	return
}

func assembleEquityProduct(product model.UserEquityProduct) (row map[string]interface{}) {
	row = make(map[string]interface{})
	row["product_id"] = product.EQID
	row["coupon_name"] = product.CouponName
	row["coupon_name_type"] = product.CouponNameType
	row["coupon_suffix_name"] = product.CouponSuffixName
	row["original_price"] = product.OriginalPrice
	row["prices"] = product.Prices
	row["zhekou"] = product.Zhekou
	row["product_number_detais"] = product.ProductNumberDetais
	row["icon"] = product.Icon
	row["md5"] = product.MD5
	return
}

func getImportedProductsAndSkusMap(supplyID uint) (err error, importedProductsMap map[uint][]productModel.Product, importedSkusMap map[uint]productModel.Sku) {
	importedProductsMap = make(map[uint][]productModel.Product)
	importedSkusMap = make(map[uint]productModel.Sku)
	var importedProducts []productModel.Product
	err, importedProducts = getImportedProducts(supplyID)
	if err != nil {
		return
	}
	for _, goods := range importedProducts {
		if len(goods.Skus) > 0 {
			importedProductsMap[goods.SourceGoodsID] = append(importedProductsMap[goods.SourceGoodsID], goods)
			importedSkusMap[goods.ID] = goods.Skus[0]
		}
	}
	return
}

func getImportedProducts(supplyID uint) (err error, importedProducts []productModel.Product) {
	err = source.DB().Preload("Skus").Where("`gather_supply_id` = ?", supplyID).Find(&importedProducts).Error
	return
}

func getDefaultBrand(supplyID uint) (err error, brand categoryModel.Brand) {
	brand.Name = "会员充值"
	brand.Source = int(supplyID)
	source.DB().Where("name=?", brand.Name).FirstOrCreate(&brand)
	return
}

func getDefaultCategory(supplyID uint) (err error, category1, category2, category3 categoryModel.Category) {
	var display int
	display = 1
	category1.Name = "会员充值"
	category1.Level = 1
	category1.ParentID = 0
	category1.IsDisplay = &display
	category1.Source = int(supplyID)
	source.DB().Where("name=? and level=? and parent_id=?", category1.Name, category1.Level, category1.ParentID).FirstOrCreate(&category1)
	category2.Name = "会员充值"
	category2.Level = 2
	category2.ParentID = category1.ID
	category2.IsDisplay = &display
	category2.Source = int(supplyID)
	source.DB().Where("name=? and level=? and parent_id=?", category2.Name, category2.Level, category2.ParentID).FirstOrCreate(&category2)
	category3.Name = "会员充值"
	category3.Level = 3
	category3.ParentID = category2.ID
	category3.IsDisplay = &display
	category3.Source = int(supplyID)
	source.DB().Where("name=? and level=? and parent_id=?", category3.Name, category3.Level, category3.ParentID).FirstOrCreate(&category3)
	return
}

func getExistedEquityProductMap() (err error, existedEquityProductMap map[int]model.UserEquityProduct) {
	existedEquityProductMap = make(map[int]model.UserEquityProduct)
	err, equityProducts := getExistedEquityProduct()
	if err != nil {
		return
	}
	for _, product := range equityProducts {
		existedEquityProductMap[product.EQID] = product
	}
	return
}

func getExistedEquityProduct() (err error, equityProducts []model.UserEquityProduct) {
	err = source.DB().Find(&equityProducts).Error
	return
}

func getParams() (params map[string]string) {
	params = make(map[string]string)
	// params["app_id"] = "10000010"
	params["app_id"] = AppID
	params["time"] = strconv.FormatInt(time.Now().Unix(), 10)
	sign := getSign(params)
	params["sign"] = sign
	return
}

func getSign(params map[string]string) (sign string) {

	// 排序
	var itemKeys []string
	for k := range params {
		itemKeys = append(itemKeys, k)
	}
	sort.Strings(itemKeys)
	paramsString := ""
	// 拼接
	for _, k := range itemKeys {
		if paramsString != "" {
			paramsString += "&"
		}
		paramsString += k + "=" + params[k]
	}

	//jsonStr := "app_id=10000010&time=" + strconv.FormatInt(time.Now().Unix(), 10)

	// paramsString += "&apisecret=1q8wSta4bdshpNe0DlQeLWmG0AYZtHWO"
	paramsString += "&apisecret=" + AppSecret
	sign = utils.MD5V([]byte(paramsString))
	return
}

func getSetting() (err error, setting model.Setting) {
	err = source.DB().Where("`key` = ?", "user_equity_setting").First(&setting).Error
	return
}
