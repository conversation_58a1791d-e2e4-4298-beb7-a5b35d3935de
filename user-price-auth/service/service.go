package service

import (
	"encoding/json"
	"user-price-auth/model"
	"yz-go/setting"
	"yz-go/source"
)

type UpgradeProduct struct {
	source.Model
	ProductID uint   `json:"product_id"`
	LevelID   uint   `json:"level_id"`
	Image     string `json:"image"`
}

func Update(data model.UserPriceAuth) (err error) {

	var productIds []int64

	source.DB().Model(UpgradeProduct{}).Pluck("product_id", &productIds)
	data.ProductIds = productIds
	err = setting.SetSetting("userPriceAuth", data)
	return
}
func GetSysSetting() (err error, data interface{}) {
	var dataString string
	err, dataString = setting.GetSetting("userPriceAuth")
	var UserPriceAuth model.UserPriceAuth
	json.Unmarshal([]byte(dataString), &UserPriceAuth)
	var productIds []int64

	source.DB().Model(UpgradeProduct{}).Pluck("product_id", &productIds)
	UserPriceAuth.ProductIds = productIds

	dataByte, _ := json.Marshal(&UserPriceAuth)
	data = string(dataByte)

	return
}
