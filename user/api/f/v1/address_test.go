package v1

import (
	"github.com/pupuk/addr"
	"reflect"
	"strings"
	"testing"
)

func TestSmart(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want *addr.Address
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			args: args{
				str: "安徽省合肥市蜀山区茂荫路999号御龙湾小区10栋底商菜鸟驿站",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Smart(tt.args.str); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("Smart() = %v, want %v", got, tt.want)
			}
		})
	}
}
func TestSplitStr(t *testing.T) {
	str := "安徽省合肥市蜀山区茂荫路999号御龙湾小区10栋底商菜鸟驿站"
	r := strings.Split(str, " ")
	println(r)
}
