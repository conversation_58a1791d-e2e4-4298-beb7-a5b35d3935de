package v1

import (
	"encoding/json"
	v1 "gin-vue-admin/admin/api/v1"
	"gin-vue-admin/cmd/gva"
	"github.com/chenhg5/collection"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	service3 "product/service"
	"strconv"
	"strings"
	"user/model"
	"user/permission"
	"user/request"
	"user/service"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	service2 "yz-go/service"
	"yz-go/utils"
)

// @Tags UserLevel
// @Summary 创建UserLevel
// @accept application/json
// @Produce application/json
// @Param data body model.UserLevel true "创建UserLevel"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /user/createUserLevel [post]
func CreateUserLevel(c *gin.Context) {
	var user model.UserLevelBySave
	err := c.ShouldBind<PERSON>(&user)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, reUserlevels := service.GetUserLevels(); err == nil {
		if len(reUserlevels) >= 10 {
			log.Log().Error("会员等级最多创建十个!")
			yzResponse.FailWithMessage("会员等级最多创建十个！", c)
			return
		}
	}
	if err, reUserlevel := service.GetUserLevelByLevel(user.Level); err == nil {
		log.Log().Error("等级权重已存在!", zap.Any("err", reUserlevel))
		yzResponse.FailWithMessage("等级权重已存在", c)
		return
	}

	if err, levelId := service.CreateUserLevel(user); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("创建失败|"+err.Error(), c)
		return
	} else {
		var newUserLevel model.UserLevelData
		err, newUserLevel = service.GetUserLevelData(levelId)
		if err != nil {
			yzResponse.FailWithMessage("等级不存在"+err.Error(), c)
			return
		}
		var recordData = make(map[string]interface{})
		recordData["new_data"] = newUserLevel
		recordDataJson, err := json.Marshal(recordData)
		if err != nil {
			log.Log().Error("会员等级记录日志错误!", zap.Any("err", err))
			yzResponse.FailWithMessage("日记记录错误"+err.Error(), c)
			return
		}
		service2.CreateOperationRecord(v1.GetUserID(c), 9, c.ClientIP(), "新增了会员等级'"+user.Name+"'", string(recordDataJson))
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// @Tags UserLevel
// @Summary 删除UserLevel
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.UserLevel true "删除UserLevel"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /user/deleteUserLevel [delete]
func DeleteUserLevel(c *gin.Context) {
	var user model.UserLevel
	err := c.ShouldBindQuery(&user)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err := service.DeleteUserLevel(user); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {

		service2.CreateOperationRecord(v1.GetUserID(c), 9, c.ClientIP(), "删除了会员等级id:"+strconv.Itoa(int(user.ID))+"")
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// @Tags UserLevel
// @Summary 批量删除UserLevel
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.IdsReq true "批量删除UserLevel"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /user/deleteUserLevelByIds [delete]
func DeleteUserLevelByIds(c *gin.Context) {
	var IDS yzRequest.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteUserLevelByIds(IDS); err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	} else {
		var idsString []string
		for _, v := range IDS.Ids {
			idsString = append(idsString, strconv.Itoa(int(v)))
		}
		service2.CreateOperationRecord(v1.GetUserID(c), 9, c.ClientIP(), "批量删除会员等级'"+strings.Join(idsString, ",")+"'")
		yzResponse.OkWithMessage("批量删除成功", c)
	}
}

// @Tags UserLevel
// @Summary 更新UserLevel
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.UserLevel true "更新UserLevel"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /user/updateUserLevel [put]
func UpdateUserLevel(c *gin.Context) {
	var user model.UserLevelBySave
	err := c.ShouldBindJSON(&user)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, reUserlevel := service.GetUserLevelByLevel(user.Level); err == nil && reUserlevel.ID != user.ID {
		log.Log().Error("等级权重已存在!", zap.Any("err", reUserlevel))
		yzResponse.FailWithMessage("等级权重已存在", c)
		return
	}
	var oldUserLevel model.UserLevelData
	err, oldUserLevel = service.GetUserLevelData(user.ID)
	if err != nil {
		yzResponse.FailWithMessage("等级不存在"+err.Error(), c)
		return
	}

	if err = service.UpdateUserLevel(user); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败|"+err.Error(), c)
		return
	} else {
		permission.LevelsCache = nil
		var newUserLevel model.UserLevelData
		err, newUserLevel = service.GetUserLevelData(user.ID)
		if err != nil {
			yzResponse.FailWithMessage("等级不存在"+err.Error(), c)
			return
		}
		var recordData = make(map[string]interface{})
		recordData["old_data"] = oldUserLevel
		recordData["new_data"] = newUserLevel
		recordDataJson, err := json.Marshal(recordData)
		if err != nil {
			log.Log().Error("会员等级记录日志错误!", zap.Any("err", err))
			yzResponse.FailWithMessage("日记记录错误"+err.Error(), c)
			return
		}

		if oldUserLevel.Level != newUserLevel.Level {
			syncErr := service3.Sync(0)
			if syncErr != nil {
				log.Log().Error("修改会员等级,同步es商品失败!", zap.Any("err", syncErr))
			}
		}

		service2.CreateOperationRecord(v1.GetUserID(c), 9, c.ClientIP(), "编辑了会员等级'"+user.Name+"'", string(recordDataJson))
		yzResponse.OkWithMessage("更新成功", c)
	}
}

// @Tags UserLevel
// @Summary 用id查询UserLevel
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.UserLevel true "用id查询UserLevel"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /user/findUserLevel [get]
func FindUserLevel(c *gin.Context) {
	var user model.UserLevel
	err := c.ShouldBindQuery(&user)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, reuser := service.GetUserLevel(user.ID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"reuser": reuser}, c)
	}
}

// @Tags UserLevel
// @Summary 分页获取UserLevel列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.UserLevelSearch true "分页获取UserLevel列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /user/getUserLevelList [get]
func GetUserLevelList(c *gin.Context) {
	var pageInfo request.UserLevelSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetUserLevelInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func GetDouyinCpsStatus(c *gin.Context) {
	var data = make(map[string]interface{})
	if collection.Collect(gva.GlobalAuth.ResourcesPlugin).Contains(14) != false || utils.LocalEnv() == false {
		data["cps_show"] = true
	}
	if collection.Collect(gva.GlobalAuth.ResourcesPlugin).Contains(20) != false || utils.LocalEnv() == false {
		data["jh_cps_show"] = true
	}
	if collection.Collect(gva.GlobalAuth.ResourcesPlugin).Contains(10) != false || utils.LocalEnv() == false {
		data["lian_show"] = true
	}
	yzResponse.OkWithData(data, c)

}

// @Tags UserLevel
// @Summary 分页获取UserLevel列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.UserLevelSearch true "分页获取UserLevel列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /user/getUserLevelList [get]
func GetUserLevelOptionList(c *gin.Context) {

	if err, list := service.GetUserLevelOptionList(); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List: list,
		}, "获取成功", c)
	}
}

// @Tags UserLevel
// @Summary 获取UserLevel列表无分页
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.UserLevelSearch true "分页获取UserLevel列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /user/getUserLevelList [get]
func GetUserLevelListNotPage(c *gin.Context) {
	if err, list := service.GetUserLevelInfoListNotPage(); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{
			"list": list,
		}, "获取成功", c)
	}
}
