package cron

import (
	"go.uber.org/zap"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func PushCheckUserRelationsHandle() {
	task := cron.Task{
		Key:  "checkUserRelationsCron",
		Name: "会员关系校验定时",
		// 每小时执行一次
		Spec: "0 0 */1 * * *",
		Handle: func(task cron.Task) {
			Check()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func Check() {
	// 查询会员数据，parent_id=user_id的数据
	var users []User
	err := source.DB().Where("parent_id = id").Find(&users).Error
	if err != nil {
		log.Log().Error("查询会员数据失败", zap.Any("err", err))
		return
	}
	if len(users) == 0 {
		return
	}
	log.Log().Error("查询到parent_id=user_id的数据", zap.Any("users", users))
	// 修改parent_id=user_id的数据, parent_id=0
	err = source.DB().Model(&User{}).Where("parent_id = id").Update("parent_id", 0).Error
	if err != nil {
		log.Log().Error("修改parent_id=user_id的数据失败", zap.Any("err", err))
		return
	}
}

type User struct {
	source.Model
	ParentId uint `json:"parent_id" form:"parent_id" gorm:"comment:推荐会员id;"`
}
