package middleware

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"user/permission"
	"yz-go/component/log"
	yzResponse "yz-go/response"
)

func VerifyApi() gin.HandlerFunc {
	return func(c *gin.Context) {
		userLevelID, exists := c.Get("user_level_id")
		if exists == false {
			return
		}
		err, result := permission.ApiPerm(userLevelID.(uint))
		if err != nil {
			log.Log().Error("获取会员等级权限失败", zap.Any("err", err))
		}
		if result == false {
			yzResponse.Result(yzResponse.APIPERM, gin.H{}, "该等级没有前端采购权限", c)
			c.Abort()
			return
		}
		c.Next()
	}
}

func VerifyPurchase() gin.HandlerFunc {
	return func(c *gin.Context) {
		userLevelID, exists := c.Get("user_level_id")
		if exists == false {
			return
		}

		err, result := permission.PurchasePerm(userLevelID.(uint))
		if err != nil {
			log.Log().Error("获取会员等级权限失败", zap.Any("err", err))
		}
		if result == false {
			yzResponse.Result(yzResponse.PURCHASEPERM, gin.H{}, "该等级没有api采购权限", c)
			c.Abort()
			return
		}
		c.Next()
	}
}
