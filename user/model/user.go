// 自动生成模板User
package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"github.com/satori/go.uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"public-supply/common"
	"strconv"
	pay_product "user/pay-product"
	"yz-go/component/log"
	"yz-go/component/upload"
	setting2 "yz-go/setting"
	"yz-go/source"
)

/*func init() {
	var users []User
	err := source.DB().Where("`invite_code` is NULL").Find(&users).Error;
	if err != nil {
		color.Error.Println("\n[默认邀请码失败1]")
		color.Error.Println(zap.Any("err", err))
	} else {
		for _, user := range users {
			user.InviteCode = RandStr(8);
			err := source.DB().Save(&user).Error
			if err != nil {
				color.Error.Println("\n[默认邀请码失败2]")
			}
		}
		color.Error.Println("\n[默认邀请码成功]")
	}
}

func RandStr(length int) string {
	str := "23456789ABCDEFGHJKLMNPQRSTUVWXYZ"
	bytes := []byte(str)
	result := []byte{}
	rand.Seed(time.Now().UnixNano()+ int64(rand.Intn(100)))
	for i := 0; i < length; i++ {
		result = append(result, bytes[rand.Intn(len(bytes))])
	}
	return string(result)
}*/

// 结构体基类
type Balance struct {
	PurchasingBalance uint `json:"purchasing_balance" form:"purchasing_balance" gorm:"column:purchasing_balance;comment:采购余额;type:bigint;size:100;"`
	SettlementBalance uint `json:"settlement_balance" form:"settlement_balance" gorm:"column:settlement_balance;comment:结算余额;type:bigint;size:100;"`
	Type              int  `json:"type" form:"type" gorm:"column:type;comment:余额类型(1汇聚2平台);type:smallint;size:1;"`
	Uid               int  `json:"uid" form:"uid" gorm:"column:uid;comment:会员ID;type:bigint;size:100;"`
}

// 站内账户余额
type AccountBalance struct {
	source.Model
	Balance
}

// 如果含有time.Time 请自行import time包
type User struct {
	source.Model
	CreatedAt *source.LocalTime `json:"created_at" gorm:"<-:create"`

	Mobile            string            `json:"mobile" form:"mobile" gorm:"column:mobile;comment:手机号;type:char(20);size:20;"`
	Avatar            string            `json:"avatar" form:"avatar" gorm:"column:avatar;comment:用户头像url;type:varchar(255);size:255;"`
	Username          string            `json:"username" form:"username" gorm:"comment:用户登录名"`
	Password          string            `json:"password" form:"password"  gorm:"comment:用户登录密码"`
	NickName          string            `json:"nickname" form:"nickName" gorm:"column:nick_name;comment:用户昵称;type:varchar(50);size:50;"`
	Status            int               `json:"status" form:"status" gorm:"column:status;comment:状态（-1拉黑0待审核1正常）;type:smallint;size:1;"`
	UUID              uuid.UUID         `json:"uuid" form:"uuid"`
	LevelID           uint              `json:"level_id" form:"level_id"`
	ParentId          uint              `json:"parent_id" form:"parent_id" gorm:"comment:推荐会员id;"`
	TemporaryParentId uint              `json:"temporary_parent_id" form:"temporary_parent_id" gorm:"comment:临时推荐会员id;"`
	QrCode            string            `json:"qr_code" form:"qr_code" gorm:"column:qr_code;comment:个人二维码;type:varchar(255);"`
	InviteCode        string            `json:"invite_code" gorm:"unique;comment:邀请码;type:varchar(50);"` // 允许读和创建
	WxUsername        string            `json:"wx_username" form:"wx_username" gorm:"column:wx_username;comment:微信号"`
	UserLevel         UserLevelDiscount `json:"user_level" form:"user_level" gorm:"foreignKey:LevelID;references:ID"`
	WxOpenid          string            `json:"wx_openid" form:"wx_openid" gorm:"column:wx_openid;comment:微信openid;type:varchar(255);size:255;"`
	WxMiniOpenid      string            `json:"wx_mini_openid" form:"wx_mini_openid" gorm:"column:wx_mini_openid;comment:微信小程序openid;type:varchar(255);size:255;"`
	WxUnionid         string            `json:"wx_unionid" form:"wx_unionid" gorm:"column:wx_unionid;comment:微信unionid;type:varchar(255);size:255;"`
	IsPermanent       int               `json:"is_permanent" form:"is_permanent" gorm:"column:is_permanent;comment:等级有效期是否永久有效;default:1;"`
	UpgradeAt         *source.LocalTime `json:"UpgradeAt" form:"UpgradeAt" gorm:"column:UpgradeAt;comment:升级时间;"`
	ValidityAt        *source.LocalTime `json:"validity_at" form:"validity_at" gorm:"column:validity_at;comment:有效期;"`
	Validity          string            `json:"validity" form:"validity" gorm:"-"`
	FullName          string            `json:"full_name" form:"full_name" gorm:"column:full_name;comment:姓名;type:varchar(255);size:255;"`
	IDCard            string            `json:"id_card" form:"id_card" gorm:"column:id_card;comment:身份证;type:varchar(255);size:255;"`
	AliAccount        string            `json:"ali_account" form:"ali_account" gorm:"column:ali_account;comment:支付宝账号;type:varchar(255);size:255;"`
	Remark            string            `json:"remark" form:"remark" gorm:"column:remark;comment:备注;type:varchar(255);size:255;"`
	ThousandsPricesID uint              `json:"thousands_prices_id"`
	IsFollow          int               `json:"is_follow" form:"is_follow" gorm:"column:is_follow;comment:是否关注;type:smallint;size:1;"`
}

type ChildUser struct {
	source.Model
	Mobile      string         `json:"mobile" form:"mobile" gorm:"column:mobile;comment:手机号;type:char(20);size:20;"`
	Avatar      string         `json:"avatar" form:"avatar" gorm:"column:avatar;comment:用户头像url;type:varchar(255);size:255;"`
	Username    string         `json:"username" gorm:"comment:用户登录名"`
	NickName    string         `json:"nickname" form:"nickName" gorm:"column:nick_name;comment:用户昵称;type:varchar(50);size:50;"`
	Status      int            `json:"status" form:"status" gorm:"column:status;comment:状态（-1拉黑0待审核1正常）;type:smallint;size:1;"`
	LevelID     uint           `json:"level_id" form:"uuid"`
	ParentId    uint           `json:"parent_id" form:"parent_id" gorm:"comment:推荐会员id;"`
	InviteCode  string         `json:"invite_code" gorm:"unique;comment:邀请码;type:varchar(50);"` // 允许读和创建
	Level       UserLevel      `json:"level" gorm:"foreignKey:LevelID"`
	GoinBalance AccountBalance `json:"goin_balance" gorm:"foreignKey:Uid;references:ID"`
	Balance     AccountBalance `json:"balance" gorm:"foreignKey:Uid;references:ID"`
}

func (ChildUser) TableName() string {
	return "users"
}

type ParentUser struct {
	source.Model
	Mobile     string            `json:"mobile" form:"mobile" gorm:"column:mobile;comment:手机号;type:char(20);size:20;"`
	Avatar     string            `json:"avatar" form:"avatar" gorm:"column:avatar;comment:用户头像url;type:varchar(255);size:255;"`
	Username   string            `json:"username" gorm:"comment:用户登录名"`
	NickName   string            `json:"nickname" form:"nickName" gorm:"column:nick_name;comment:用户昵称;type:varchar(50);size:50;"`
	UUID       uuid.UUID         `json:"uuid" form:"uuid"`
	LevelID    uint              `json:"level_id" form:"level_id"`
	QrCode     string            `json:"qr_code" form:"qr_code" gorm:"column:qr_code;comment:个人二维码;type:varchar(255);"`
	InviteCode string            `json:"invite_code" gorm:"unique;comment:邀请码;type:varchar(50);"` // 允许读和创建
	WxUsername string            `json:"wx_username" form:"wx_username" gorm:"column:wx_username;comment:微信号"`
	UserLevel  UserLevelDiscount `json:"user_level" form:"user_level" gorm:"foreignKey:LevelID;references:ID"`
}

func (ParentUser) TableName() string {
	return "users"
}

func (u *User) AfterCreate(tx *gorm.DB) (err error) {
	var balance = []AccountBalance{
		{Balance: Balance{Type: 1, Uid: int(u.ID)}},
		{Balance: Balance{Type: 2, Uid: int(u.ID)}},
	}
	var counts int64
	//原来这里没有model 执行到这里会警告 没有表 语句也不会执行
	tx.Model(&AccountBalance{}).Where("uid", u.ID).Count(&counts)
	if counts <= 0 {
		err = source.DB().Create(&balance).Error
	}

	return
}

func (u *User) AfterDelete(tx *gorm.DB) (err error) {

	err = tx.Delete(&AccountBalance{}, "uid=?", u.ID).Error
	if err != nil {
		log.Log().Error("删除用户余额记录出错", zap.Any("err", err))
	}

	return
}

/*
*
用户银行卡信息
*/
type UserBank struct {
	source.Model
	UserID          uint   `json:"user_id" form:"user_id" gorm:"column:user_id;"`
	BankAccount     string `json:"bank_account" form:"bank_account" gorm:"column:bank_account;comment:卡号"`
	CardID          string `json:"card_id" form:"card_id" gorm:"column:card_id;comment:身份证"`
	Area            string `json:"area" form:"area" gorm:"column:area;comment:地区"`
	BankType        uint   `json:"bank_type" form:"bank_type" gorm:"column:bank_type;default:1;comment:1对私 2对公"`
	Branch          string `json:"branch" form:"branch" gorm:"column:branch;default:1;comment:分行"`
	AccountName     string `json:"account_name" form:"account_name" gorm:"column:account_name;comment:开户名"`
	BankName        string `json:"bank_name" form:"bank_name" gorm:"column:bank_name;comment:银行名"`
	BankChannelNo   string `json:"bank_channel_no" form:"bank_channel_no" gorm:"column:bank_channel_no;comment:联行号"`
	City            string `json:"city" form:"city" gorm:"column:city;comment:城市"`
	CityID          int    `json:"city_id" form:"city_id" gorm:"column:city_id;comment:城市id"`
	Province        string `json:"province" form:"province" gorm:"column:province;comment:省"`
	ProvinceID      int    `json:"province_id" form:"province_id" gorm:"column:province_id;comment:省id"`
	County          string `json:"county" form:"county" gorm:"column:county;comment:区"`
	CountyID        int    `json:"county_id" form:"county_id" gorm:"column:county_id;comment:区id"`
	Mobile          string `json:"mobile" form:"mobile" gorm:"column:mobile;comment:区id"`
	IDCardZ         string `json:"id_card_z" form:"id_card_z" gorm:"column:id_card_z;comment:身份证正面"`
	IDCardF         string `json:"id_card_f" form:"id_card_f" gorm:"column:id_card_f;comment:身份证反面"`
	IsDefault       int    `json:"is_default" form:"is_default" gorm:"column:is_default;comment:是否默认"`
	BusinessLicense string `json:"business_license" form:"business_license" gorm:"column:business_license;type:longtext;comment:营业执照"`
}

type Favorite struct {
	ID int
}

type UserLevelDiscount struct {
	source.Model
	Discount int    `json:"discount" form:"discount" gorm:"column:discount;comment:折扣;type:smallint;size:2;"`
	Name     string `json:"name" form:"name" gorm:"column:name;comment:会员等级名称;type:char(20);size:20;"`
	Level    int    `json:"level" form:"level" gorm:"column:level;comment:等级权重;type:smallint;size:6;"`
}

func (UserLevelDiscount) TableName() string {
	return "user_levels"
}

type UserLevelMigration struct {
	source.Model
	Name               string `json:"name" form:"name" gorm:"column:name;comment:会员等级名称;type:char(20);size:20;"`
	Level              int    `json:"level" form:"level" gorm:"column:level;comment:等级权重;type:smallint;size:6;"`
	ExpireDays         int    `json:"expire_days" form:"expire_days" gorm:"column:expire_days;comment:等级有效天数;type:int(10);size:10;"`
	Discount           int    `json:"discount" form:"discount" gorm:"column:discount;comment:折扣;type:smallint;size:2;"`
	IndependentRatio   int    `json:"independent_ratio" form:"independent_ratio" gorm:"column:independent_ratio;comment:是否走独立技术服务费;type:smallint;size:2;"`
	FreightDeduction   int    `json:"freight_deduction" form:"freight_deduction" gorm:"column:freight_deduction;comment:运费减免;type:smallint;size:2;"`
	Description        string `json:"description" form:"description" gorm:"column:description;comment:权益说明;type:text;"`
	UpgradeOrderCount  int    `json:"order_count" form:"order_count" gorm:"column:order_count;comment:升级需要的订单数量;"`
	UpgradeOrderAmount int    `json:"order_amount" form:"order_amount" gorm:"column:order_amount;comment:升级需要的订单总额;"`
	IsUpdate           uint   `json:"is_update"` //0自动升级  1不自动升级
	ServerRatio        int    `json:"server_ratio" form:"server_ratio" gorm:"column:server_ratio;comment:技术服务费;type:smallint;size:2;"`
	CpsRatio           uint   `json:"cps_ratio"`
	DouyinGroupRatio   int64  `json:"douyin_group_ratio" form:"douyin_group_ratio" gorm:"column:douyin_group_ratio;comment:抖音团购分成;"`

	JhCpsRatio              uint `json:"jh_cps_ratio"`
	MeituanDistributorRatio uint `json:"meituan_distributor_ratio"`
	LianRatio               uint `json:"lian_ratio" gorm:"default:0"`
	// 是否付费升级
	PayUpgrade int `json:"pay_upgrade" form:"pay_upgrade" gorm:"column:pay_upgrade;comment:是否付费升级:1开启0关闭;"`
	// 是否选择了采购权限
	IsPurchasePerm int `json:"is_purchase_perm" form:"is_purchase_perm" gorm:"column:is_purchase_perm;comment:是否选择了采购权限:1已选择0未选择;default:0"`
	// 采购权限
	PurchasePerm PurchaseCodes `json:"purchase_perm" form:"purchase_perm" gorm:"column:purchase_perm;comment:采购权限:1前端采购2api采购;"`
	// 付费通道
	PaySwitch int `json:"pay_switch" form:"pay_switch" gorm:"column:pay_switch;comment:付费通道开关:1开启0关闭;"`
	// 权益说明
	EquityStatement string `json:"equity_statement" form:"equity_statement" gorm:"column:equity_statement;comment:权益说明;type:longtext;"`
	// 关联商品id
	PayProductID uint `json:"pay_product_id" form:"pay_product_id" gorm:"column:pay_product_id;comment:关联商品id;"`
	// 类型 1升级2续费
	PurchaseType int `json:"purchase_type" form:"purchase_type" gorm:"-"`
}

func (UserLevelMigration) TableName() string {
	return "user_levels"
}

type UserLevel struct {
	source.Model
	Name                    string           `json:"name" form:"name" gorm:"column:name;comment:会员等级名称;type:char(20);size:20;"`
	Level                   int              `json:"level" form:"level" gorm:"column:level;comment:等级权重;type:smallint;size:6;"`
	ExpireDays              int              `json:"expire_days" form:"expire_days" gorm:"column:expire_days;comment:等级有效天数;type:smallint;size:6;"`
	Discount                int              `json:"discount" form:"discount" gorm:"column:discount;comment:折扣;type:smallint;size:2;"`
	IndependentRatio        int              `json:"independent_ratio" form:"independent_ratio" gorm:"column:independent_ratio;comment:是否走独立技术服务费;type:smallint;size:2;"`
	FreightDeduction        int              `json:"freight_deduction" form:"freight_deduction" gorm:"column:freight_deduction;comment:运费减免;type:smallint;size:2;"`
	Description             string           `json:"description" form:"description" gorm:"column:description;comment:权益说明;type:text;"`
	UpgradeOrderCount       int              `json:"order_count" form:"order_count" gorm:"column:order_count;comment:升级需要的订单数量;"`
	UpgradeOrderAmount      int              `json:"order_amount" form:"order_amount" gorm:"column:order_amount;comment:升级需要的订单总额;"`
	UpgradeProduct          []UpgradeProduct `json:"upgrade_product" gorm:"foreignKey:LevelID;references:ID"`
	IsUpdate                uint             `json:"is_update"` //0自动升级  1不自动升级
	ServerRatio             int              `json:"server_ratio" form:"server_ratio" gorm:"column:server_ratio;comment:技术服务费;type:smallint;size:2;"`
	CpsRatio                uint             `json:"cps_ratio"`
	DouyinGroupRatio        int64            `json:"douyin_group_ratio" form:"douyin_group_ratio" gorm:"column:douyin_group_ratio;comment:抖音团购分成;"`
	JhCpsRatio              uint             `json:"jh_cps_ratio"`
	MeituanDistributorRatio uint             `json:"meituan_distributor_ratio"`
	LianRatio               uint             `json:"lian_ratio" gorm:"default:0"`
	// 是否付费升级
	PayUpgrade int `json:"pay_upgrade" form:"pay_upgrade" gorm:"column:pay_upgrade;comment:是否付费升级:1开启0关闭;"`
	// 是否选择了采购权限
	IsPurchasePerm int `json:"is_purchase_perm" form:"is_purchase_perm" gorm:"column:is_purchase_perm;comment:是否选择了采购权限:1已选择0未选择;default:0"`
	// 采购权限
	PurchasePerm PurchaseCodes `json:"purchase_perm" form:"purchase_perm" gorm:"column:purchase_perm;comment:采购权限:1前端采购2api采购;"`
	// 付费通道
	PaySwitch int `json:"pay_switch" form:"pay_switch" gorm:"column:pay_switch;comment:付费通道开关:1开启0关闭;"`
	// 权益说明
	EquityStatement string `json:"equity_statement" form:"equity_statement" gorm:"column:equity_statement;comment:权益说明;type:longtext;"`
	// 关联商品id
	PayProductID uint `json:"pay_product_id" form:"pay_product_id" gorm:"column:pay_product_id;comment:关联商品id;"`
	// 类型 1升级2续费
	PurchaseType int `json:"purchase_type" form:"purchase_type" gorm:"-"`
	// 关联的付费商品
	PayProduct pay_product.ProductForUpdate `json:"pay_product" gorm:"foreignKey:PayProductID"`
}

type UserLevelData struct {
	source.Model
	Name                    string           `json:"name" form:"name" gorm:"column:name;comment:会员等级名称;type:char(20);size:20;"`
	Level                   int              `json:"level" form:"level" gorm:"column:level;comment:等级权重;type:smallint;size:6;"`
	ExpireDays              int              `json:"expire_days" form:"expire_days" gorm:"column:expire_days;comment:等级有效天数;type:smallint;size:6;"`
	Discount                int              `json:"discount" form:"discount" gorm:"column:discount;comment:折扣;type:smallint;size:2;"`
	IndependentRatio        int              `json:"independent_ratio" form:"independent_ratio" gorm:"column:independent_ratio;comment:是否走独立技术服务费;type:smallint;size:2;"`
	FreightDeduction        int              `json:"freight_deduction" form:"freight_deduction" gorm:"column:freight_deduction;comment:运费减免;type:smallint;size:2;"`
	Description             string           `json:"description" form:"description" gorm:"column:description;comment:权益说明;type:text;"`
	UpgradeOrderCount       int              `json:"order_count" form:"order_count" gorm:"column:order_count;comment:升级需要的订单数量;"`
	UpgradeOrderAmount      int              `json:"order_amount" form:"order_amount" gorm:"column:order_amount;comment:升级需要的订单总额;"`
	UpgradeProduct          []UpgradeProduct `json:"upgrade_product" gorm:"foreignKey:LevelID;references:ID"`
	IsUpdate                uint             `json:"is_update"` //0自动升级  1不自动升级
	ServerRatio             int              `json:"server_ratio" form:"server_ratio" gorm:"column:server_ratio;comment:技术服务费;type:smallint;size:2;"`
	CpsRatio                uint             `json:"cps_ratio"`
	DouyinGroupRatio        int64            `json:"douyin_group_ratio" form:"douyin_group_ratio" gorm:"column:douyin_group_ratio;comment:抖音团购分成;"`
	JhCpsRatio              uint             `json:"jh_cps_ratio"`
	MeituanDistributorRatio uint             `json:"meituan_distributor_ratio"`
	LianRatio               uint             `json:"lian_ratio" gorm:"default:0"`
	// 是否付费升级
	PayUpgrade int `json:"pay_upgrade" form:"pay_upgrade" gorm:"column:pay_upgrade;comment:是否付费升级:1开启0关闭;"`
	// 是否选择了采购权限
	IsPurchasePerm int `json:"is_purchase_perm" form:"is_purchase_perm" gorm:"column:is_purchase_perm;comment:是否选择了采购权限:1已选择0未选择;default:0"`
	// 采购权限
	PurchasePerm PurchaseCodes `json:"purchase_perm" form:"purchase_perm" gorm:"column:purchase_perm;comment:采购权限:1前端采购2api采购;"`
	// 付费通道
	PaySwitch int `json:"pay_switch" form:"pay_switch" gorm:"column:pay_switch;comment:付费通道开关:1开启0关闭;"`
	// 权益说明
	EquityStatement string `json:"equity_statement" form:"equity_statement" gorm:"column:equity_statement;comment:权益说明;type:longtext;"`
	// 关联商品id
	PayProductID uint `json:"pay_product_id" form:"pay_product_id" gorm:"column:pay_product_id;comment:关联商品id;"`
	// 类型 1升级2续费
	PurchaseType int `json:"purchase_type" form:"purchase_type" gorm:"-"`
}

func (UserLevelData) TableName() string {
	return "user_levels"
}

type UserLevelBySave struct {
	UserLevel
	// 关联的付费商品
	PayProduct pay_product.ProductForUpdate `json:"pay_product" gorm:"foreignKey:PayProductID"`
}

func (UserLevelBySave) TableName() string {
	return "user_levels"
}

func (ul *UserLevelBySave) AfterSave(tx *gorm.DB) (err error) {
	// 等级未开启付费通道,或者付费金额等于0
	if ul.PaySwitch != 1 || ul.PayProduct.Price == 0 {
		return
	}
	var payProduct pay_product.ProductForUpdate
	if ul.PayProductID == 0 {
		sku := pay_product.Sku{
			Title:         "默认",
			Price:         ul.PayProduct.Price,
			CostPrice:     ul.PayProduct.Price,
			OriginPrice:   ul.PayProduct.Price,
			GuidePrice:    ul.PayProduct.Price,
			ActivityPrice: ul.PayProduct.Price,
			Stock:         99999,
			Weight:        0,
			Sn:            "",
			OriginalSkuID: 0,
			Options:       pay_product.Options{{SpecName: "规格", SpecItemName: "默认"}},
		}

		payProduct.Skus = append(payProduct.Skus, sku)
	} else {
		err = tx.Preload("Skus").Where("id = ?", ul.PayProductID).First(&payProduct).Error
		if err != nil && len(payProduct.Skus) == 0 {
			return
		}
		payProduct.Skus[0].Price = ul.PayProduct.Price
		payProduct.Skus[0].CostPrice = ul.PayProduct.Price
		payProduct.Skus[0].OriginPrice = ul.PayProduct.Price
		payProduct.Skus[0].GuidePrice = ul.PayProduct.Price
		payProduct.Skus[0].ActivityPrice = ul.PayProduct.Price
		payProduct.Skus[0].Stock = 99999
	}
	payProduct.Price = ul.PayProduct.Price
	payProduct.CostPrice = ul.PayProduct.Price
	payProduct.OriginPrice = ul.PayProduct.Price
	payProduct.GuidePrice = ul.PayProduct.Price
	payProduct.ActivityPrice = ul.PayProduct.Price
	payProduct.Title = ul.Name
	payProduct.Stock = 99999
	payProduct.IsPlugin = 1
	payProduct.IsDisplay = 1
	payProduct.NotDiscount = 1
	payProduct.NotFee = 1
	err, payProduct.GatherSupplyID = getGatherSupplyID(tx)
	if err != nil {
		return
	}

	// 更新产品并添加或删除修改的关联sku与规格、规格项
	err = tx.Model(&pay_product.ProductForUpdate{}).Session(&gorm.Session{FullSaveAssociations: true}).Where("id = ?", payProduct.ID).Save(&payProduct).Error
	if err != nil {
		return
	}
	if payProduct.Skus[0].ID != 0 {
		// 修改规格
		err = tx.Model(&pay_product.Sku{}).Where("id = ?", payProduct.Skus[0].ID).Save(&payProduct.Skus[0]).Error
		if err != nil {
			return
		}
	}

	if payProduct.ID != 0 {
		err = tx.Model(&UserLevelBySave{}).Where("id = ?", ul.ID).Update("pay_product_id", payProduct.ID).Error
		if err != nil {
			return
		}
	}
	return
}

func getGatherSupplyID(tx *gorm.DB) (err error, supplyID uint) {
	var supply GatherSupply
	err = tx.Unscoped().Model(&GatherSupply{}).Where("category_id = ?", common.LEVEL_BIND_PRODUCT).First(&supply).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err, 0
	}
	if supply.ID != 0 {
		return err, supply.ID
	}
	if supply.ID == 0 {
		supply.Name = "会员等级权益-private"
		supply.CategoryID = common.LEVEL_BIND_PRODUCT
		err = tx.Create(&supply).Error
		if err != nil {
			err = nil
			return err, 0
		}
		supplyID = supply.ID
		err = tx.Delete(&supply).Error
		if err != nil {
			err = nil
			return err, 0
		}
		var setting setting2.SysSetting
		setting.Key = "gatherSupply" + strconv.Itoa(int(supplyID))
		setting.Value = "{}"
		source.DB().Create(&setting)
	}
	return err, supply.ID
}

type GatherSupply struct {
	source.Model
	Name       string `json:"name" form:"name" gorm:"column:name;comment:名;type:varchar(255);size:255;index;"` // 名
	CategoryID uint   `json:"category_id" form:"category_id" gorm:"column:category_id;default:0;"`
}

type PurchaseCodes []PurchaseCode

type PurchaseCode struct {
	CODE int `json:"code"`
}

func (value PurchaseCodes) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *PurchaseCodes) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type UpgradeProduct struct {
	source.Model
	ProductID uint   `json:"product_id"`
	LevelID   uint   `json:"level_id"`
	Image     string `json:"image"`
}

func (ub *UserBank) AfterFind(tx *gorm.DB) (err error) {
	ossUrl := upload.NewOssUrl()

	// 处理身份证图片字段
	if ub.IDCardZ != "" {
		ub.IDCardZ = ossUrl.GetUrl(ub.IDCardZ)
	}
	if ub.IDCardF != "" {
		ub.IDCardF = ossUrl.GetUrl(ub.IDCardF)
	}

	return nil
}

func (ub *UserBank) BeforeSave(tx *gorm.DB) (err error) {
	ossUrl := upload.NewUrlConverter()

	if ub.IDCardZ != "" {
		ub.IDCardZ, err = ossUrl.ConvertToNormalUrl(ub.IDCardZ)
		if err != nil {
			return
		}
	}
	if ub.IDCardF != "" {
		ub.IDCardF, err = ossUrl.ConvertToNormalUrl(ub.IDCardF)
		if err != nil {
			return
		}
	}

	return nil
}
