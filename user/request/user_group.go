package request

type UserGroupId struct {
	ID uint `json:"id" form:"id"`
}

type UserGroupName struct {
	Name string `json:"name" form:"name"`
}

type UserGroupList struct {
	Page     int `json:"page,default=1" form:"page,default=1" query:"page,default=1"`
	PageSize int `json:"pageSize,default=100" form:"pageSize,default=10" query:"pageSize,default=10"`
}

type UserGroupUserList struct {
	Page        int    `json:"page,default=1" form:"page,default=1" query:"page,default=1"`
	PageSize    int    `json:"pageSize,default=100" form:"pageSize,default=10" query:"pageSize,default=10"`
	UserGroupId uint   `json:"user_group_id" form:"user_group_id"`
	ID          uint   `json:"id" form:"id"`               // 会员ID
	Username    string `json:"username" form:"username"`   // 会员手机号
	NickName    string `json:"nick_name" form:"nick_name"` // 会员昵称
}

type PickUsers struct {
	UserIds     []uint `json:"user_ids" form:"user_ids"`
	UserGroupId uint   `json:"user_group_id" form:"user_group_id"`
}

type PickUserGroups struct {
	UserId       uint   `json:"user_id" form:"user_id"`
	UserGroupIds []uint `json:"user_group_ids" form:"user_group_ids"`
}
