package main

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"strings"
	"version/model"
	"version/service"
	yzResponse "yz-go/response"
)

func main() {
	r := gin.Default()
	r.GET("/authVersion", func(c *gin.Context) {

		fmt.Println("ip:::", c.ClientIP())

		domain := strings.TrimSpace(c.ClientIP())
		//domain := c.ClientIP()
		menu := service.AuthVersion(domain)
		fmt.Println("menu", menu)
		if menu == nil {
			yzResponse.FailWithMessage("未识别用户", c)
			return
		}
		supply := service.AuthSupply(domain)
		marketingPlugin, resourcesPlugin, toolsPlugin := service.AuthPlugin(domain)

		yzResponse.OkWithDetailed(model.ResponseJson{
			Menu:            menu,
			Supply:          supply,
			MarketingPlugin: marketingPlugin,
			ResourcesPlugin: resourcesPlugin,
			ToolsPlugin:     toolsPlugin,
		}, "获取成功", c)
	})
	r.GET("getUser", func(context *gin.Context) {
		service.GetUser()

	})
	r.GE<PERSON>("getPlugin", func(context *gin.Context) {

	})
	//默认为监听8080端口
	r.Run(":8889")
}
