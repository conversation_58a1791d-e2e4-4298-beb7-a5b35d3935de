package model

import "yz-go/source"

type ServerUser struct {
	Domain string `json:"domain" form:"domain"`
	Status bool   `json:"status" form:"status"`
}
type Plugin struct {
	source.Model
	Name   string       `json:"name" form:"name" gorm:"column:name;comment:;type:varchar(255);size:255;"` //名称
	Status bool         `json:"status" form:"status" gorm:"-"`                                            //true 默认所有人可见   ， false  默认对所有人不可见
	Auth   []ServerUser `json:"auth" form:"auth" gorm:"-"`                                                //授权列表
}

type GatherSupplyCategory struct {
	Status bool `json:"status" form:"status"` //true 默认所有人可见   ， false  默认对所有人不可见
	source.Model
	Name string       `json:"name"`
	Key  string       `json:"key"`
	Auth []ServerUser `json:"auth" form:"auth"`
}

type ResponseJson struct {
	Mplugin         interface{}
	Rplugin         interface{}
	Menu            interface{}
	Supply          interface{}
	MarketingPlugin interface{}
	ResourcesPlugin interface{}
	ToolsPlugin     interface{}
}
