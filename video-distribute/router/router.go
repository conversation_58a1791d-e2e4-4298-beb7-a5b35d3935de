package router

import (
	"github.com/gin-gonic/gin"
	"video-distribute/api/f"
	v1 "video-distribute/api/v1"
)

// 后台私有

func InitAdminPrivateRouter(Router *gin.RouterGroup) {
	videoRouter := Router.Group("video")
	{
		videoRouter.POST("create", v1.Create)
		videoRouter.POST("update", v1.Update)
		videoRouter.POST("delete", v1.Delete)
		videoRouter.POST("savaBaseSetting", v1.SavaBaseSetting)
		videoRouter.POST("getBaseSetting", v1.GetBaseSetting)
		videoRouter.POST("list", v1.List)
		videoRouter.POST("createGroup", v1.CreateGroup)
		videoRouter.POST("updateGroup", v1.UpdateGroup)
		videoRouter.POST("deleteGroup", v1.DeleteGroup)
		videoRouter.POST("getGroup", v1.GetGroup)
		videoRouter.POST("getGatherList", v1.GetGatherList)                     // 获取供应链列表 没有分页
		videoRouter.POST("getGatherVideoList", v1.GetGatherVideoList)           // 获取供应链短视频列表(搜索)
		videoRouter.POST("getGatherVideoDetail", v1.GetGatherVideoDetail)       // 获取供应链短视频详情(查看)
		videoRouter.POST("importGatherVideo", v1.ImportGatherVideo)             // 导入短视频
		videoRouter.POST("updateGatherVideo", v1.UpdateGatherVideo)             // 更新短视频
		videoRouter.POST("getAutoSynchroSetting", v1.GetAutoSynchroSetting)     // 自动同步设置查询
		videoRouter.POST("storeAutoSynchroSetting", v1.StoreAutoSynchroSetting) // 自动同步设置提交
		videoRouter.POST("audit/list", v1.AuditList)                            // 短视频审核列表
		videoRouter.POST("audit/operate", v1.AuditOperate)                      // 短视频审核操作

	}
}

func InitPublicAppRouter(Router *gin.RouterGroup) {
	public := Router.Group("video")
	{
		public.POST("videoList", v1.AppList)                                 // 查询列表
		public.POST("videoDetail", v1.VideoDetail)                           // 查询详情
		public.POST("addStorage", v1.AddStorage)                             // 添加选品库
		public.POST("deleteStorage", v1.DeleteStorage)                       // 删除选品库
		public.POST("getAppGatherVideoList", v1.GetAppGatherVideoList)       // 短视频同步：短视频列表
		public.POST("getAppGatherVideoDetail", v1.GetAppGatherVideoDetail)   // 短视频同步：短视频详情
		public.POST("exportAppGatherVideoList", v1.ExportAppGatherVideoList) // 短视频同步：被同步接口
	}
}

// 前端公共API

func InitPublicRouter(Router *gin.RouterGroup) {
	public := Router.Group("video")
	{
		public.POST("smallVideoList", f.SmallVideoList)                    // 小视频列表
		public.POST("addForwardingNum", f.AddForwardingNum)                // 增加转发数量
		public.GET("getSmallVideoByProductId", f.GetSmallVideoByProductId) // 通过商品id获取关联的短视频
	}
}

func InitUserPrivateRouter(Router *gin.RouterGroup) {
	router := Router.Group("video")
	{
		router.POST("self/list", f.SelfList)               // 我的视频：视频列表
		router.POST("self/create", f.SelfCreate)           // 我的视频：发布视频
		router.POST("self/detail", f.SelfDetail)           // 我的视频：视频详情
		router.POST("self/replace", f.SelfReplace)         // 我的视频：更换商品
		router.POST("center/list", f.CenterList)           // 视频中心：视频列表
		router.POST("center/group", f.CenterGroup)         // 视频中心：视频分组
		router.POST("center/detail", f.CenterDetail)       // 视频中心：视频详情
		router.POST("user/addLikeNum", f.AddLikeNum)       // 增加喜欢数量
		router.POST("user/getLikeById", f.GetLikeById)     // 查询获取喜欢
		router.POST("user/reduceLikeNum", f.ReduceLikeNum) // 减少喜欢数量
	}
}

// 小商店前端私有API

func InitSmallShopPrivateRouter(Router *gin.RouterGroup) {
	public := Router.Group("video")
	{
		public.POST("addLikeNum", f.AddLikeNum)             // 增加喜欢数量
		public.POST("reduceLikeNum", f.ReduceLikeNum)       // 减少喜欢数量
		public.POST("getLikeByShortVideoId", f.GetLikeById) // 通过短视频id获取喜欢
	}
}
