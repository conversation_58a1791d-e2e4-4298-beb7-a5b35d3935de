package service

import (
	"encoding/json"
	"errors"
	"github.com/chenhg5/collection"
	"go.uber.org/zap"
	"gorm.io/gorm"
	model2 "product/model"
	model4 "user/model"
	"video-distribute/model"
	"video-distribute/request"
	"yz-go/component/log"
	model3 "yz-go/model"
	"yz-go/source"
)

func GetBaseSetting() (err error, data model.BaseSetting) {

	var setting model3.SysSetting

	err = source.DB().Where("`key`=?", "videoDistributeSetting").First(&setting).Error
	if err != nil {
		return
	}
	err = json.Unmarshal([]byte(setting.Value), &data)

	return
}

func SavaBaseSetting(request model.BaseSetting) (err error) {
	jsonData, jsonErr := json.Marshal(request)
	if jsonErr != nil {
		err = jsonErr
		return
	}
	var sysSetting model3.SysSetting
	db := source.DB().Model(&sysSetting)
	err = db.Where("`key`=?", "videoDistributeSetting").First(&sysSetting).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		sysSetting.Key = "videoDistributeSetting"
	}
	sysSetting.Value = string(jsonData)
	err = source.DB().Save(&sysSetting).Error

	return

}

func Create(video model.ShortVideo) (err error) {
	err = source.DB().Create(&video).Error
	return
}
func CreateGroup(video model.VideoGroup) (err error) {
	err = source.DB().Create(&video).Error
	return
}

func UpdateGroup(video model.VideoGroup) (err error) {
	err = source.DB().Updates(&video).Error
	return
}

func Update(video model.ShortVideo) (err error) {
	err = source.DB().Omit("Group", "like_num", "forwarding_num").Updates(&video).Error
	return
}

func Delete(video model.ShortVideo) (err error) {
	var detail model.ShortVideo
	if err = source.DB().Where("id=?", video.ID).First(&detail).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("记录不存在或已删除")
		}
		return
	}

	if detail.AuditID > 0 {
		if err = source.DB().Delete(&model.ShortVideoAudit{}, detail.AuditID).Error; err != nil {
			return
		}
	}

	return source.DB().Delete(&video, video.ID).Error
}

func DeleteGroup(video model.VideoGroup) (err error) {
	err = source.DB().Delete(&video, "id=?", video.ID).Error
	return
}

func GetGroup(info model.SearchVideo) (err error, video []model.VideoGroup, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(&video)
	if info.Title != "" {
		db.Where("title like ?", "%"+info.Title+"%")
	}

	if info.Type == 1 {
		var userIds, appIds []int64
		if info.NickName != "" {
			var appId []int64

			source.DB().Model(model4.User{}).Where("nick_name like ?", "%"+info.NickName+"%").Pluck("id", &userIds)
			source.DB().Model(model.Application{}).Where("member_id in (?)", userIds).Pluck("id", &appId)

			appIds = append(appIds, appId...)
		}
		if info.UserName != "" {
			var appId []int64

			source.DB().Model(model4.User{}).Where("mobile like ?", "%"+info.UserName+"%").Pluck("id", &userIds)
			source.DB().Model(model.Application{}).Where("member_id in (?)", userIds).Pluck("id", &appId)
			appIds = append(appIds, appId...)

		}
		if info.CompanyName != "" {
			var appId []int64
			source.DB().Model(model.Application{}).Where("company_name like ?", "%"+info.CompanyName+"%").Pluck("id", &appId)
			appIds = append(appIds, appId...)

		}
		if info.AppId > 0 {
			var appId []int64
			source.DB().Model(model.Application{}).Where("id=? ", info.AppId).Pluck("id", &appId)
			appIds = append(appIds, appId...)

		}

		var ids []int64

		source.DB().Model(&model.VideoGroupAuth{}).Where("type=?", info.Type).Where("auth_id in (?)", appIds).Pluck("group_id", &ids)
		if len(ids) > 0 {
			db.Where("id in (?)", ids)
		}

	}

	if (info.Type == 2 || info.Type == 3) && info.Auth != "" {
		var groupAuth model.VideoGroupAuth
		var ids []int64
		source.DB().Model(&groupAuth).Where("type=?", info.Type).Where("auth_id =?", info.Auth).Pluck("group_id", &ids)
		db.Where("id in (?)", ids)
	}
	if info.Type > 0 {
		db.Where("type=?", info.Type)
	}

	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Order("id desc").Find(&video).Error
	return
}

func List(info model.SearchVideo) (err error, video []model.ListShortVideo, total int64) {
	db := source.DB().Preload("User").Preload("Group").Model(&video).Preload("Product.Supplier").Preload("Product.GatherSupply")

	if info.ID > 0 {
		db.Where("id = ?", info.ID)
	}
	if info.Title != "" {
		db.Where("title like ?", "%"+info.Title+"%")
	}
	if info.GroupID > 0 {
		db.Where("group_id=?", info.GroupID)
	}
	if info.ProductID > 0 {
		db.Where("product_id=?", info.ProductID)
	}
	if info.IsDisplay != nil {
		db.Where("is_display = ?", info.IsDisplay)
	}

	// 会员信息搜索
	if info.UserID > 0 {
		db.Where("user_id = ?", info.UserID)
	}
	if info.UserLevelId > 0 || info.UserMobile != "" || info.UserNickname != "" {
		db.Joins("JOIN users ON users.id = short_videos.user_id")
		if info.UserLevelId > 0 {
			db.Where("users.level_id = ?", info.UserLevelId)
		}
		if info.UserMobile != "" {
			db.Where("users.username like ?", "%"+info.UserMobile+"%")
		}
		if info.UserNickname != "" {
			db.Where("users.nick_name like ?", "%"+info.UserNickname+"%")
		}
	}

	//搜索商品参数
	if info.ProductTitle != "" {
		var productIds []int64
		err = source.DB().Model(model2.Product{}).Where("title like ?", "%"+info.ProductTitle+"%").Pluck("id", &productIds).Error
		if len(productIds) > 0 && err == nil {
			db.Where("product_id in (?)", productIds)
		}
	}
	if info.Category1ID > 0 {
		var productIds []int64
		err = source.DB().Model(model2.Product{}).Where("category1_id = ?", info.Category1ID).Pluck("id", &productIds).Error
		if len(productIds) > 0 && err == nil {
			db.Where("product_id in (?)", productIds)
		}
	}
	if info.Category2ID > 0 {
		var productIds []int64
		err = source.DB().Model(model2.Product{}).Where("category2_id = ?", info.Category2ID).Pluck("id", &productIds).Error
		if len(productIds) > 0 && err == nil {
			db.Where("product_id in (?)", productIds)
		}
	}
	if info.Category3ID > 0 {
		var productIds []int64
		err = source.DB().Model(model2.Product{}).Where("category3_id = ?", info.Category3ID).Pluck("id", &productIds).Error
		if len(productIds) > 0 && err == nil {
			db.Where("product_id in (?)", productIds)
		}
	}

	if info.SupplierID > 0 {
		var productIds []int64
		err = source.DB().Model(model2.Product{}).Where("supplier_id = ?", info.SupplierID).Pluck("id", &productIds).Error
		if len(productIds) > 0 && err == nil {
			db.Where("product_id in (?)", productIds)
		}
	}

	if info.GatherSupplyId > 0 {
		var productIds []int64
		err = source.DB().Model(model2.Product{}).Where("gather_supply_id = ?", info.GatherSupplyId).Pluck("id", &productIds).Error
		if len(productIds) > 0 && err == nil {
			db.Where("product_id in (?)", productIds)
		}
	}

	if info.BrandID > 0 {
		var productIds []int64
		err = source.DB().Model(model2.Product{}).Where("brand_id = ?", info.BrandID).Pluck("id", &productIds).Error
		if len(productIds) > 0 && err == nil {
			db.Where("product_id in (?)", productIds)
		}
	}

	err = db.Count(&total).Error

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	err = db.Limit(limit).Offset(offset).Preload("Product").Preload("Product.Brand").Order("id desc").Find(&video).Error
	return
}
func AddStorage(data model.VideoStorage) error {
	err := source.DB().Create(&data).Error
	return err

}

func DeleteStorage(data model.DeleteStorageData) error {
	err := source.DB().Where("app_id=? and product_id in (?)", data.AppID, data.Ids).Delete(&model.VideoStorage{}).Error
	return err

}

func VideoDetail(data model.DeleteStorageData) (err error, resData model.ListShortVideo) {

	err = source.DB().Where("id in (?)", data.Ids).Preload("Product").Preload("Product.Skus").Find(&resData).Error
	return
}

func AppList(info model.SearchVideo) (err error, video []model.ListShortVideo, total int64) {

	var NotArr []uint
	err, NotArr = FindApplication(int(info.AppId))

	var videoList []model.ListShortVideo
	_, setting := GetBaseSetting()
	if setting.Open == "0" {
		err = errors.New("中台已关闭导入")
		return
	}

	limit := info.PageSize
	if limit > 100 {
		limit = 100
	}
	offset := info.PageSize * (info.Page - 1)

	db := source.DB().Model(&video)

	db.Where("is_display=?", 1)
	if len(NotArr) > 0 {
		db.Where("group_id not in(?)", NotArr)
	}

	if info.ProductID > 0 {
		db.Where("product_id=?", info.ProductID)
	}
	if info.Title != "" {
		db.Where("title like ?", "%"+info.Title+"%")
	}
	if info.IsImport == "1" {
		var storages []int64
		err = source.DB().Model(model.VideoStorage{}).Where("app_id=?", info.AppId).Pluck("product_id", &storages).Error
		if err != nil {
			return
		}
		if len(storages) > 0 {
			db.Where("id not in (?)", storages)
		}

	}
	//搜索商品参数
	if info.ProductTitle != "" {
		var productIds []int64
		err = source.DB().Model(model2.Product{}).Where("title like ?", "%"+info.ProductTitle+"%").Pluck("id", &productIds).Error
		if len(productIds) > 0 && err == nil {
			db.Where("product_id in (?)", productIds)
		}
	}
	if info.IsImport == "2" {
		var storages []int64
		err = source.DB().Model(model.VideoStorage{}).Where("app_id=?", info.AppId).Pluck("product_id", &storages).Error
		if err != nil {
			return
		}
		if len(storages) > 0 {
			db.Where("id  in (?)", storages)
		}

	}

	if info.ID > 0 {
		db.Where("id = ?", info.ID)
	}
	if info.IsDisplay != nil {
		db.Where("is_display = ?", info.IsDisplay)
	}

	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Preload("Product").Preload("Product.Skus").Order("id desc").Find(&videoList).Error

	for _, item := range videoList {
		var Storage model.VideoStorage
		source.DB().Where("app_id=?", info.AppId).Where("product_id=?", item.ID).First(&Storage)
		if Storage.ID > 0 {
			item.IsImport = 1
		}
		video = append(video, item)

	}
	return
}

func FindApplication(appId int) (err error, NotInGroupId []uint) {

	var group []model.VideoGroup
	var application model.Application
	err = source.DB().First(&application, "id=?", appId).Error
	if err != nil {
		return
	}

	err = source.DB().Find(&group).Error
	if err != nil {
		return
	}
	for _, item := range group {
		var authArr []int64
		err = json.Unmarshal([]byte(item.Auth), &authArr)
		if err != nil {
			continue
		}
		id := item.ID
		if item.Type == 1 { //采购端列表
			if !collection.Collect(authArr).Contains(appId) { //分组的 采购端不包含 当前采购端，需要排除
				NotInGroupId = append(NotInGroupId, id)
			}
		} else if item.Type == 2 { //采购端等级
			if !collection.Collect(authArr).Contains(application.AppLevelID) { //分组的 采购端不包含 当前采购端，需要排除
				NotInGroupId = append(NotInGroupId, id)
			}
		} else if item.Type == 3 { //采购端分组
			if !collection.Collect(authArr).Contains(application.GroupID) { //分组的 采购端不包含 当前采购端，需要排除
				NotInGroupId = append(NotInGroupId, id)
			}
		} else if item.Type == 4 { //不限制

		}

	}

	return

}

type MiniProductList struct {
	ID        uint   `json:"id"`
	Title     string `json:"title"`
	ImageUrl  string `json:"image_url"`
	Sales     int    `json:"sales"`
	ShopPrice uint   `json:"shop_price"`
}
type ListShortVideoFront struct {
	source.Model
	Sort          uint           `json:"sort" gorm:"index"`
	Title         string         `json:"title"`
	CoverUrl      string         `json:"cover_url"`
	VideoUrl      string         `json:"video_url"`
	IsDisplay     *uint          `json:"is_display" gorm:"default:0"`
	ProductID     uint           `json:"product_id" gorm:"index"`
	Product       model2.Product `json:"product"`
	LikeNum       uint           `json:"like_num" gorm:"default:0"`       //喜欢数量
	ForwardingNum uint           `json:"forwarding_num" gorm:"default:0"` //转发数量
	Price         uint           `json:"price" `                          //小商店商品金额

	MiniProductList MiniProductList `json:"mini_product_list" gorm:"-"`
}

// row := MiniProductList{}
// row.ID = item.ProductID
// row.ImageUrl = item.Product.ImageUrl
// row.Title = item.Product.Title
// row.Sales = int(item.Product.Sales)
// row.ShopPrice = item.Price
// list = append(list, row)
func (ListShortVideoFront) TableName() string {
	return "short_videos"
}
func SmallVideoList(info request.SmallVideoRequest) (err error, video []ListShortVideoFront, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	db := source.DB().Model(&video).Where("short_videos.is_display = 1")
	db.Joins("left join small_shop_product_sales as ssps on ssps.product_id = short_videos.product_id").Where("ssps.small_shop_id = ?", info.Sid)
	db.Where("ssps.deleted_at is null")
	if info.ProductId != 0 {
		db.Where("short_videos.product_id = ?", info.ProductId)
	}

	//如果有id则忽略其他参数就查询这个短视频
	if info.Id != 0 {
		db.Where("short_videos.id = ?", info.Id)
	} else {
		switch info.Type {
		case 1:
			db.Where("short_videos.is_recommend = 1").Order("short_videos.created_at desc")
			break
		default:
			db.Order("short_videos.created_at desc")
			break
		}

	}

	err = db.Count(&total).Error
	err = db.Limit(limit).Select("short_videos.*,ssps.price").Offset(offset).Preload("Product").Preload("Product.Brand").Order("short_videos.id desc").Find(&video).Error

	for key, item := range video {
		video[key].MiniProductList.ID = item.ProductID
		video[key].MiniProductList.ImageUrl = item.Product.ImageUrl
		video[key].MiniProductList.Title = item.Product.Title
		video[key].MiniProductList.Sales = int(item.Product.Sales)
		video[key].MiniProductList.ShopPrice = item.Price
	}
	return
}

// 增加喜欢数量
func AddLikeNum(id uint, userId uint) (err error) {
	var shortVideo model.ShortVideo
	err = source.DB().Where("id = ?", id).First(&shortVideo).Error
	if err != nil {
		err = errors.New("短视频不存在" + err.Error())
		return
	}
	var shortVideoLike model.ShortVideoLike
	err = source.DB().Where("short_video_id = ?", shortVideo.ID).Where("user_id = ?", userId).First(&shortVideoLike).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Log().Error("短视频增加喜欢失败", zap.Any("err", err))
		err = errors.New("操作失败" + err.Error())
		return
	}
	//如果存在记录代表已经增加过
	if shortVideoLike.ID != 0 {
		err = errors.New("您已标记喜欢这个短视频")
		return
	}
	err = source.DB().Model(&model.ShortVideoLike{}).Create(&model.ShortVideoLike{
		ShortVideoId: shortVideo.ID,
		UserID:       userId,
	}).Error

	if err != nil {
		log.Log().Error("短视频增加喜欢保存记录失败", zap.Any("err", err))
		err = errors.New("保存记录失败" + err.Error())
		return
	}
	err = source.DB().Model(&model.ShortVideo{}).Where("id = ?", shortVideo.ID).Updates(&model.ShortVideo{LikeNum: shortVideo.LikeNum + 1}).Error
	if err != nil {
		err = errors.New("增加喜欢数量失败" + err.Error())
		return
	}
	return
}

// 通过短视频id获取喜欢  isLike = 1喜欢 0不喜欢
func GetLikeByShortVideoId(id uint, userId uint) (err error, isLike int) {
	var shortVideo model.ShortVideo
	err = source.DB().Where("id = ?", id).First(&shortVideo).Error
	if err != nil {
		err = errors.New("短视频不存在" + err.Error())
		return
	}
	var shortVideoLike model.ShortVideoLike
	err = source.DB().Where("short_video_id = ?", shortVideo.ID).Where("user_id = ?", userId).First(&shortVideoLike).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Log().Error("短视频增加喜欢失败", zap.Any("err", err))
		err = errors.New("操作失败" + err.Error())
		return
	}
	err = nil
	//如果存在记录代表已经增加过
	if shortVideoLike.ID != 0 {
		isLike = 1
	} else {
		isLike = 0
	}
	return
}

// 减少喜欢数量
func ReduceLikeNum(id uint, userId uint) (err error) {
	var shortVideo model.ShortVideo
	err = source.DB().Where("id = ?", id).First(&shortVideo).Error
	if err != nil {
		err = errors.New("短视频不存在" + err.Error())
		return
	}
	var shortVideoLike model.ShortVideoLike
	err = source.DB().Where("short_video_id = ?", shortVideo.ID).Where("user_id = ?", userId).First(&shortVideoLike).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Log().Error("短视频增加喜欢失败", zap.Any("err", err))
		err = errors.New("操作失败" + err.Error())
		return
	}
	//如果存在记录代表已经增加过
	if shortVideoLike.ID == 0 {
		err = errors.New("您未标记喜欢这个")
		return
	}
	err = source.DB().Model(&model.ShortVideoLike{}).Where("id = ?", shortVideoLike.ID).Delete(&shortVideoLike).Error

	if err != nil {
		log.Log().Error("短视频取消喜欢保存记录失败", zap.Any("err", err))
		err = errors.New("取消记录失败" + err.Error())
		return
	}
	shortVideo.LikeNum--
	err = source.DB().Model(&model.ShortVideo{}).Where("id = ?", shortVideo.ID).Save(&shortVideo).Error
	if err != nil {
		err = errors.New("减少喜欢数量失败" + err.Error())
		return
	}
	return
}

func AddForwardingNum(id uint) (err error) {
	var shortVideo model.ShortVideo
	err = source.DB().Where("id = ?", id).First(&shortVideo).Error
	if err != nil {
		err = errors.New("短视频不存在" + err.Error())
		return
	}
	err = source.DB().Model(&model.ShortVideo{}).Where("id = ?", shortVideo.ID).Updates(&model.ShortVideo{ForwardingNum: shortVideo.ForwardingNum + 1}).Error
	if err != nil {
		err = errors.New("增加转发数量失败" + err.Error())
		return
	}
	return
}

// 通过商品id获取关联的短视频
func GetSmallVideoByProductId(product request.Product) (err error, listShortVideoFront []ListShortVideoFront) {
	err = source.DB().Model(&ListShortVideoFront{}).Where("is_display = 1").Where("product_id = ?", product.ProductId).Find(&listShortVideoFront).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("获取小视频失败" + err.Error())
		return
	}
	return
}
