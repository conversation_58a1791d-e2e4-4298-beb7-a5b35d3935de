package service

import (
	"errors"
	"fmt"
	"gorm.io/gorm"
	productModel "product/model"
	"video-distribute/model"
	"video-distribute/request"
	"yz-go/source"
)

func GetAppGatherVideoList(appId uint, pageInfo request.AppGatherVideoListSearch) (err error, list interface{}, total int64) {
	if err = validateOpen(); err != nil {
		return
	}

	db := source.DB().Model(&model.AppGatherVideo{}).
		Preload("Group").
		Preload("Product").
		Where("is_display = 1").
		Select("short_videos.*", "CASE WHEN synchronized.id IS NOT NULL THEN 1 ELSE 0 END AS import_status").
		Joins("LEFT JOIN video_storages AS synchronized ON short_videos.id = synchronized.product_id And synchronized.app_id = ?", appId)

	// 是否导入搜索：0全部 1未导入 2已导入
	if pageInfo.IsImport != 0 {
		switch pageInfo.IsImport {
		case 1:
			db.Where("synchronized.id IS NULL")
		case 2:
			db.Where("synchronized.id IS NOT NULL")
		}
	}
	// 短视频标题搜索
	if pageInfo.Title != "" {
		db.Where("title like ?", "%"+pageInfo.Title+"%")
	}
	//搜索商品参数
	if pageInfo.ProductTitle != "" {
		var productIds []int64
		err = source.DB().Model(productModel.Product{}).Where("title like ?", "%"+pageInfo.ProductTitle+"%").Pluck("id", &productIds).Error
		if len(productIds) > 0 && err == nil {
			db.Where("short_videos.product_id in (?)", productIds)
		}
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	var videos []model.AppGatherVideo
	var limit = pageInfo.PageSize
	var offset = pageInfo.PageSize * (pageInfo.Page - 1)

	err = db.Order("id desc").Limit(limit).Offset(offset).Find(&videos).Error
	if err != nil {
		return
	}

	return err, videos, total
}

func GetAppGatherVideoDetail(videoId uint) (err error, video model.ListShortVideo) {
	if err = validateOpen(); err != nil {
		return
	}

	err = source.DB().
		Preload("Group").
		Preload("Product").
		Preload("Product.Skus").
		First(&video, videoId).
		Error
	return
}

func ExportAppGatherVideoList(appId uint, vIds []uint) (err error, videos []model.GatherVideo) {
	if err = validateOpen(); err != nil {
		return
	}
	// 验证pIds
	if err = validatePIds(vIds); err != nil {
		return
	}
	// 更新被导入次数
	if err = AddImportCount(vIds); err != nil {
		return
	}
	// 创建被导入记录
	if err = createExportRelation(appId, vIds); err != nil {
		return
	}
	// 返回需要被导入的数据(短视频、短视频标签、短视频绑定的商品IDs)
	err = source.DB().
		Preload("Group").
		Where("is_display = 1").
		Where("id in ?", vIds).
		Find(&videos).
		Error
	return
}

func AddImportCount(ids []uint) error {
	return source.DB().
		Model(&model.GatherVideo{}).
		Where("id in ?", ids).
		Updates(map[string]interface{}{"import_count": gorm.Expr("import_count + ?", 1)}).
		Error
}

// 创建被导入记录
func createExportRelation(gatherId uint, pIds []uint) (err error) {
	// 获取所有现有的记录
	err, eIds := getExistingSynchroVIds(gatherId, pIds)
	if err != nil {
		return err
	}
	// 取出差集
	var relationData []model.VideoStorage
	for _, vId := range difference(pIds, eIds) {
		relationData = append(relationData, model.VideoStorage{
			AppID:     gatherId,
			ProductID: vId,
		})
	}
	// 批量创建新的关系
	return source.DB().CreateInBatches(relationData, 1000).Error
}

func getExistingSynchroVIds(gatherId uint, vIds []uint) (err error, eIds []uint) {
	err = source.DB().
		Model(model.VideoStorage{}).
		Where("product_id in ?", vIds).
		Where("app_id = ?", gatherId).
		Pluck("product_id", &eIds).
		Error
	return
}

func validateOpen() error {
	if _, setting := GetBaseSetting(); setting.Open == "0" {
		return errors.New("中台已关闭导入")
	}
	return nil
}

// 验证被导出的短视频ids参数
func validatePIds(pIds []uint) (err error) {
	// 验证参数重复
	duplicates := findDuplicates(pIds)
	if len(duplicates) > 0 {
		err = errors.New(fmt.Sprintf("短视频ID重复:%v", duplicates))
		return
	}
	// 验证参数准确
	err, vIds := getExistingVideoIds(pIds)
	if err != nil {
		return
	}

	notFoundIds := difference(pIds, vIds)
	if len(notFoundIds) > 0 {
		err = errors.New(fmt.Sprintf("短视频不存在，ID:%v", notFoundIds))
		return
	}
	return
}

// 两个切片的差集
func difference(slice1, slice2 []uint) []uint {
	// 使用map记录第二个切片中的元素
	slice2Map := make(map[uint]bool)
	for _, value := range slice2 {
		slice2Map[value] = true
	}
	// 遍历第一个切片，找出不在slice2Map中的元素
	diff := make([]uint, 0)
	for _, value := range slice1 {
		if _, found := slice2Map[value]; !found {
			diff = append(diff, value)
		}
	}
	return diff
}

// 获取存在的短视频ids
func getExistingVideoIds(pIds []uint) (err error, vIds []uint) {
	err = source.DB().
		Model(model.GatherVideo{}).
		Where("id in ?", pIds).
		Pluck("id", &vIds).
		Error
	return
}
