package v1

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"virtual-stock/model"
	request2 "virtual-stock/request"
	"virtual-stock/service"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/source"
	"yz-go/utils"
)

func GetApplicationVirtualStockList(c *gin.Context) {
	var info request2.GetApplicationVirtualStockSearch
	err := c.ShouldBindQuery(&info)
	if err != nil {
		yzResponse.FailWithMessage("获取失败", c)
		return
	}
	if err, list, total := service.GetApplicationVirtualStockList(info); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     info.Page,
			PageSize: info.PageSize,
		}, "获取成功", c)

	}
}

func GetAppVirtualStockList(c *gin.Context) {
	var info request2.GetApplicationVirtualStockSearch
	err := c.ShouldBindJSON(&info)
	if err != nil {
		yzResponse.FailWithMessage("获取失败", c)
		return
	}
	info.ApplicationID = utils.GetAppID(c)
	if err, list, total := service.GetApplicationVirtualStockList(info); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     info.Page,
			PageSize: info.PageSize,
		}, "获取成功", c)

	}
}

func GetStockChangeRecordList(c *gin.Context) {
	var info request2.GetStockChangeRecordSearch
	err := c.ShouldBindQuery(&info)
	if err != nil {
		yzResponse.FailWithMessage("获取失败", c)
		return
	}
	if err, list, total := service.GetStockChangeRecordList(info); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     info.Page,
			PageSize: info.PageSize,
		}, "获取成功", c)

	}
}

func GetAppStockChangeRecordList(c *gin.Context) {
	var info request2.GetStockChangeRecordSearch
	err := c.ShouldBindQuery(&info)
	if err != nil {
		yzResponse.FailWithMessage("获取失败", c)
		return
	}
	info.ApplicationID = utils.GetAppID(c)
	if err, list, total := service.GetStockChangeRecordList(info); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     info.Page,
			PageSize: info.PageSize,
		}, "获取成功", c)

	}
}

func DeleteApplicationVirtualStock(c *gin.Context) {
	var application model.ApplicationVirtualStock
	err := c.ShouldBindJSON(&application)
	err = source.DB().First(&application, application.ID).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteApplicationVirtualStock(application); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

func CreateApplicationVirtualStock(c *gin.Context) {
	var application model.ApplicationVirtualStock
	err := c.ShouldBindJSON(&application)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err := service.CreateApplicationVirtualStock(application); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

func UpdateApplicationVirtualStock(c *gin.Context) {
	var application request2.UpdateApplicationVirtualStock
	err := c.ShouldBindJSON(&application)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err := service.UpdateApplicationVirtualStock(application); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}
