package api

import (
	"strconv"
	"wdt-supply/component/order"
	"wdt-supply/cron"
	"yz-go/component/log"
	"yz-go/response"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// LogisticsSyncRequest 物流同步请求参数
type LogisticsSyncRequest struct {
	Sid       string `json:"sid" binding:"required"`       // WDT卖家账号
	Appkey    string `json:"appkey" binding:"required"`    // WDT接口账号
	Appsecret string `json:"appsecret" binding:"required"` // WDT接口密钥
	ShopId    string `json:"shop_id"`                      // 店铺编号(可选)
	Limit     int    `json:"limit"`                        // 查询数量限制
}

// SyncLogistics 同步WDT物流信息
// @Tags WDT物流同步
// @Summary 同步WDT物流信息
// @Description 手动触发WDT物流信息同步
// @Accept json
// @Produce json
// @Param data body LogisticsSyncRequest true "同步参数"
// @Success 200 {object} response.Response{data=order.WdtLogisticsSyncResult} "成功"
// @Router /wdt/logistics/sync [post]
func SyncLogistics(c *gin.Context) {
	var req LogisticsSyncRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Log().Error("参数绑定失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认值
	if req.Limit <= 0 || req.Limit > 100 {
		req.Limit = 100
	}

	// 创建WDT实例
	wdt := &order.Wdt{}
	wdt.WdtData.BaseInfo.Sid = req.Sid
	wdt.WdtData.BaseInfo.Appkey = req.Appkey
	wdt.WdtData.BaseInfo.Appsecret = req.Appsecret
	wdt.WdtData.BaseInfo.ShopId = req.ShopId

	// 执行同步
	result, err := wdt.SyncLogistics(req.Limit, req.ShopId)
	if err != nil {
		log.Log().Error("同步WDT物流信息失败", zap.Error(err))
		response.FailWithMessage("同步失败: "+err.Error(), c)
		return
	}

	log.Log().Info("手动同步WDT物流信息成功",
		zap.String("sid", req.Sid),
		zap.Int("total_processed", result.TotalProcessed),
		zap.Int("success_count", result.SuccessCount),
		zap.Int("failure_count", result.FailureCount),
	)

	response.OkWithData(result, c)
}

// QueryLogistics 查询WDT物流信息
// @Tags WDT物流同步
// @Summary 查询WDT物流信息
// @Description 查询待同步的WDT物流信息
// @Accept json
// @Produce json
// @Param data body LogisticsSyncRequest true "查询参数"
// @Success 200 {object} response.Response{data=order.WdtLogisticsQueryResponse} "成功"
// @Router /wdt/logistics/query [post]
func QueryLogistics(c *gin.Context) {
	var req LogisticsSyncRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Log().Error("参数绑定失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认值
	if req.Limit <= 0 || req.Limit > 100 {
		req.Limit = 100
	}

	// 创建WDT实例
	wdt := &order.Wdt{}
	wdt.WdtData.BaseInfo.Sid = req.Sid
	wdt.WdtData.BaseInfo.Appkey = req.Appkey
	wdt.WdtData.BaseInfo.Appsecret = req.Appsecret
	wdt.WdtData.BaseInfo.ShopId = req.ShopId

	// 查询物流信息
	result, err := wdt.QueryLogistics(req.Limit, req.ShopId)
	if err != nil {
		log.Log().Error("查询WDT物流信息失败", zap.Error(err))
		response.FailWithMessage("查询失败: "+err.Error(), c)
		return
	}

	log.Log().Info("查询WDT物流信息成功",
		zap.String("sid", req.Sid),
		zap.Int("total_count", result.TotalCount),
		zap.Int("trades_count", len(result.Trades)),
	)

	response.OkWithData(result, c)
}

// GetLogisticsSyncStatus 获取物流同步状态
// @Tags WDT物流同步
// @Summary 获取物流同步状态
// @Description 获取WDT物流同步的当前状态
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=map[string]interface{}} "成功"
// @Router /wdt/logistics/status [get]
func GetLogisticsSyncStatus(c *gin.Context) {
	status := cron.GetLogisticsSyncStatus()
	response.OkWithData(status, c)
}

// BatchSyncLogistics 批量同步物流信息
// @Tags WDT物流同步
// @Summary 批量同步物流信息
// @Description 批量同步所有配置的WDT物流信息
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=map[string]interface{}} "成功"
// @Router /wdt/logistics/batch-sync [post]
func BatchSyncLogistics(c *gin.Context) {
	result := cron.BatchSyncAllLogistics()
	response.OkWithData(result, c)
}

// OnDemandSyncLogistics 按需同步物流信息
// @Tags WDT物流同步
// @Summary 按需同步物流信息
// @Description 按需同步指定配置的WDT物流信息
// @Accept json
// @Produce json
// @Param data body LogisticsSyncRequest true "同步参数"
// @Success 200 {object} response.Response{data=order.WdtLogisticsSyncResult} "成功"
// @Router /wdt/logistics/on-demand-sync [post]
func OnDemandSyncLogistics(c *gin.Context) {
	var req LogisticsSyncRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Log().Error("参数绑定失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认值
	if req.Limit <= 0 || req.Limit > 100 {
		req.Limit = 100
	}

	// 执行按需同步
	result, err := cron.SyncLogisticsOnDemand(req.Sid, req.Appkey, req.Appsecret, req.ShopId, req.Limit)
	if err != nil {
		log.Log().Error("按需同步WDT物流信息失败", zap.Error(err))
		response.FailWithMessage("同步失败: "+err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}

// UpdateLogisticsSyncConfig 更新物流同步配置
// @Tags WDT物流同步
// @Summary 更新物流同步配置
// @Description 更新WDT物流同步的配置参数
// @Accept json
// @Produce json
// @Param enabled query bool false "是否启用"
// @Param interval query int false "同步间隔(分钟)"
// @Param batch_size query int false "批次大小"
// @Success 200 {object} response.Response "成功"
// @Router /wdt/logistics/config [put]
func UpdateLogisticsSyncConfig(c *gin.Context) {
	// 获取查询参数
	enabledStr := c.Query("enabled")
	intervalStr := c.Query("interval")
	batchSizeStr := c.Query("batch_size")

	// 解析参数
	enabled := true
	if enabledStr != "" {
		enabled = enabledStr == "true"
	}

	interval := 5
	if intervalStr != "" {
		if val, err := strconv.Atoi(intervalStr); err == nil && val > 0 {
			interval = val
		}
	}

	batchSize := 100
	if batchSizeStr != "" {
		if val, err := strconv.Atoi(batchSizeStr); err == nil && val > 0 && val <= 100 {
			batchSize = val
		}
	}

	// 更新配置
	err := cron.UpdateLogisticsSyncConfig(enabled, interval, batchSize)
	if err != nil {
		log.Log().Error("更新物流同步配置失败", zap.Error(err))
		response.FailWithMessage("更新配置失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("配置更新成功", c)
}

// GetLogisticsDetails 获取物流详情
// @Tags WDT物流同步
// @Summary 获取物流详情
// @Description 获取指定订单的物流详细信息
// @Accept json
// @Produce json
// @Param sid query string true "WDT卖家账号"
// @Param appkey query string true "WDT接口账号"
// @Param appsecret query string true "WDT接口密钥"
// @Param tid query string true "订单编号"
// @Success 200 {object} response.Response{data=[]order.WdtLogisticsTradeInfo} "成功"
// @Router /wdt/logistics/details [get]
func GetLogisticsDetails(c *gin.Context) {
	sid := c.Query("sid")
	appkey := c.Query("appkey")
	appsecret := c.Query("appsecret")
	tid := c.Query("tid")

	if sid == "" || appkey == "" || appsecret == "" || tid == "" {
		response.FailWithMessage("缺少必要参数", c)
		return
	}

	// 创建WDT实例
	wdt := &order.Wdt{}
	wdt.WdtData.BaseInfo.Sid = sid
	wdt.WdtData.BaseInfo.Appkey = appkey
	wdt.WdtData.BaseInfo.Appsecret = appsecret

	// 查询物流信息
	result, err := wdt.QueryLogistics(100, "")
	if err != nil {
		log.Log().Error("查询物流详情失败", zap.Error(err))
		response.FailWithMessage("查询失败: "+err.Error(), c)
		return
	}

	// 过滤指定订单的物流信息
	var details []order.WdtLogisticsTradeInfo
	for _, trade := range result.Trades {
		if trade.Tid == tid {
			details = append(details, trade)
		}
	}

	response.OkWithData(details, c)
}
