package v1

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"jushuitan-supply/model"
	"jushuitan-supply/request"
	"jushuitan-supply/service"
	"strings"
	"yz-go/component/log"
	request2 "yz-go/request"
	yzResponse "yz-go/response"
)

func Create(c *gin.Context) {
	var shop model.JushuitanShop
	err := c.ShouldBindJSON(&shop)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	shop.Name = strings.TrimSpace(shop.Name)
	err = service.Create(shop)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.Ok(c)
	}

}

func Delete(c *gin.Context) {
	var shop model.JushuitanShop
	err := c.ShouldBindJSON(&shop)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = service.Delete(shop)
	if err != nil {
		yzResponse.FailWithMessage("操作失败", c)
	} else {
		yzResponse.Ok(c)
	}
}

func List(c *gin.Context) {
	var pageInfo request.SearchShop
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.List(pageInfo); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func ListAll(c *gin.Context) {
	var pageInfo request.SearchShop
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.ListAll(pageInfo); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func UpdateImportGoods(c *gin.Context) {

	var id request2.GetById
	err := c.ShouldBindJSON(&id)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err = service.UpdateImportGoods(id.Id); err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err, c)
		return
	} else {
		yzResponse.OkWithData("成功", c)
	}

}

func TestJushuitanUpdateGoods(c *gin.Context) {

	var id request2.IdsReq
	err := c.ShouldBindJSON(&id)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var ids []int
	for _, idd := range id.Ids {
		ids = append(ids, int(idd))
	}
	if err = service.TestJushuitanUpdateGoods(ids); err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err, c)
		return
	} else {
		yzResponse.OkWithData("成功", c)
	}

}

type TestJushuitanApiRequest struct {
	Url  string      `json:"url" form:"url"`
	Data interface{} `json:"data" form:"data"`
}

func TestJushuitanApi(c *gin.Context) {

	var data TestJushuitanApiRequest
	err := c.ShouldBindJSON(&data)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, response := service.TestJushuitanApi(data.Data); err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err, c)
		return
	} else {
		yzResponse.OkWithData(response, c)
	}

}
