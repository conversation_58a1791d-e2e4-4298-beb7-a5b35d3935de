package common

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strings"
	"time"
)

const (
	GxptSandboxURL = "https://fx-test-1.huice.com/openapi/router"
	GxptProductURL = "" // 正式环境地址待定
)

type GxptRequest struct {
	AppKey     string                 `json:"app_key"`
	AppSecret  string                 `json:"-"` // 不参与JSON序列化
	Method     string                 `json:"method"`
	Timestamp  int64                  `json:"timestamp"`
	Version    string                 `json:"version"`
	Sign       string                 `json:"sign"`
	Parameters map[string]interface{} `json:"-"` // 业务参数
}

// NewGxptRequest 创建新的供销平台请求
func NewGxptRequest(appKey, appSecret, method string) *GxptRequest {
	return &GxptRequest{
		AppKey:     appKey,
		AppSecret:  appSecret,
		Method:     method,
		Timestamp:  time.Now().Unix(),
		Version:    "1.0",
		Parameters: make(map[string]interface{}),
	}
}

// SetParameter 设置业务参数
func (g *GxptRequest) SetParameter(key string, value interface{}) {
	g.Parameters[key] = value
}

// SetParameters 批量设置业务参数
func (g *GxptRequest) SetParameters(params map[string]interface{}) {
	for k, v := range params {
		g.Parameters[k] = v
	}
}

// GetSign 生成签名
func (g *GxptRequest) GetSign() string {
	// 1. 获取所有参数并按ASCII码升序排序
	params := make(map[string]string)

	// 添加公共参数
	params["app_key"] = g.AppKey
	params["method"] = g.Method
	params["timestamp"] = fmt.Sprintf("%d", g.Timestamp)
	params["version"] = g.Version

	// 添加业务参数
	for k, v := range g.Parameters {
		if v != nil {
			// 对于基本类型，直接转字符串
			if isPrimaryType(v) {
				params[k] = fmt.Sprintf("%v", v)
			} else {
				// 对于复杂类型，转为JSON字符串
				jsonBytes, _ := json.Marshal(v)
				params[k] = string(jsonBytes)
			}
		}
	}

	// 2. 获取所有键并按ASCII码升序排序
	var keys []string
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 3. 构建签名字符串：app_secret + key1value1 + key2value2 + ... + app_secret
	signStr := g.AppSecret
	for _, k := range keys {
		signStr += k + params[k]
	}
	signStr += g.AppSecret

	// 4. 计算MD5并转为大写
	hash := md5.New()
	hash.Write([]byte(signStr))
	return strings.ToUpper(hex.EncodeToString(hash.Sum(nil)))
}

// DoRequest 执行请求
func (g *GxptRequest) DoRequest(isSandbox bool) (*Response, error) {
	// 1. 构建请求URL
	baseURL := GxptProductURL
	if isSandbox {
		baseURL = GxptSandboxURL
	}

	// 2. 生成签名
	g.Sign = g.GetSign()

	// 3. 构建请求参数为JSON格式
	requestData := make(map[string]interface{})
	requestData["app_key"] = g.AppKey
	requestData["method"] = g.Method
	requestData["timestamp"] = g.Timestamp
	requestData["version"] = g.Version
	requestData["sign"] = g.Sign

	// 添加业务参数
	for k, v := range g.Parameters {
		if v != nil {
			requestData[k] = v
		}
	}

	// 4. 将请求数据序列化为JSON
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("marshal request data error: %v", err)
	}

	// 5. 发送请求
	client := &http.Client{}
	req, err := http.NewRequest("POST", baseURL, strings.NewReader(string(jsonData)))
	if err != nil {
		return nil, fmt.Errorf("create request error: %v", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("http request error: %v", err)
	}
	defer resp.Body.Close()

	// 6. 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response error: %v", err)
	}

	// 7. 解析响应
	var result Response
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("unmarshal response error: %v", err)
	}

	// 8. 检查响应状态
	if result.Code != 0 {
		return nil, fmt.Errorf("api error: %s", result.Message)
	}

	return &result, nil
}

// isPrimaryType 判断是否为基本类型
func isPrimaryType(v interface{}) bool {
	switch v.(type) {
	case string, int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64, bool:
		return true
	default:
		return false
	}
}
