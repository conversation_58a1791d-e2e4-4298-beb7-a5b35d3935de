package common

type GxptResponse struct {
	Code      int    `json:"code"`
	Msg       string `json:"msg"`
	SubCode   int    `json:"sub_code"`
	SubMsg    string `json:"sub_msg"`
	RequestId string `json:"request_id"`
	Page      struct {
		TotalRows   int `json:"total_rows"`
		CurrentPage int `json:"current_page"`
		PageRow     int `json:"page_row"`
		PageCount   int `json:"page_count"`
	} `json:"page"`
	GoodsSupplierListResponse []struct {
		GoodsId             int         `json:"goods_id"`
		ImgUrl              string      `json:"img_url"`
		GoodsName           string      `json:"goods_name"`
		GoodsSn             string      `json:"goods_sn"`
		CreateTime          string      `json:"create_time"`
		SupplierCompanyName string      `json:"supplier_company_name"`
		SupCompanyAlias     string      `json:"sup_company_alias"`
		SupplierShopId      interface{} `json:"supplier_shop_id"`
		SupplierNickNo      interface{} `json:"supplier_nick_no"`
		SupplierGoodsId     int         `json:"supplier_goods_id"`
		SupplierSysId       int         `json:"supplier_sys_id"`
		Tail                interface{} `json:"tail"`
		BrandName           string      `json:"brand_name"`
		Skus                []struct {
			SupplierItemId                 int           `json:"supplier_item_id"`
			PrdSn                          interface{}   `json:"prd_sn"`
			PrdDesc                        string        `json:"prd_desc"`
			SkuTagDetail                   string        `json:"sku_tag_detail"`
			Tail                           interface{}   `json:"tail"`
			GoodsTagList                   []interface{} `json:"goods_tag_list"`
			SpecId                         string        `json:"spec_id"`
			DistributionPrice              float64       `json:"distributionPrice"`
			DeletedStatus                  int           `json:"deletedStatus"`
			DistributionPriceChangedStatus int           `json:"distribution_price_changed_status"`
			DeletedChangedStatus           interface{}   `json:"deleted_changed_status"`
			IsSale                         bool          `json:"is_sale"`
			ControlMaxPrice                interface{}   `json:"control_max_price"`
			ControlMinPrice                interface{}   `json:"control_min_price"`
			TieredPriceList                []interface{} `json:"tiered_price_list"`
			LeftoverStockPrice             interface{}   `json:"leftover_stock_price"`
			SpecBarcode                    string        `json:"spec_barcode"`
			Weight                         float64       `json:"weight"`
			SuggestedPrice                 interface{}   `json:"suggested_price"`
		} `json:"skus"`
		GoodsTags   []interface{} `json:"goods_tags"`
		PublishShop struct {
			PublishShopNum        int `json:"publish_shop_num"`
			PublishSuccessShopNum int `json:"publish_success_shop_num"`
		} `json:"publish_shop"`
		PriceControl        bool    `json:"price_control"`
		IsSale              bool    `json:"is_sale"`
		DeleteStatus        int     `json:"delete_status"`
		MinDistributorPrice float64 `json:"min_distributor_price"`
	} `json:"goods_supplier_list_response"`
}
