package common

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"
)

type WdtRequest struct {
	Sid       string                 `json:"sid"`
	Appkey    string                 `json:"appkey"`
	Appsecret string                 `json:"appsecret"`
	Timestamp int64                  `json:"timestamp"`
	Sign      string                 `json:"sign"`
	Params    map[string]interface{} `json:"params"`
}

func (w *WdtRequest) SetParams(params map[string]interface{}) {
	w.Params = params
}

// GetSign 生成签名
func (w *WdtRequest) GetSign() string {
	// 1. 获取所有参数并排序
	params := make(map[string]string)
	params["sid"] = w.<PERSON>
	params["appkey"] = w.Appkey
	w.Timestamp = time.Now().Unix()
	params["timestamp"] = fmt.Sprintf("%d", w.Timestamp)

	// 将 params 中的参数也加入
	for k, v := range w.Params {
		params[k] = fmt.Sprintf("%v", v)
	}

	// 获取所有键并排序
	var keys []string
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 2. 构建签名字符串
	var signParts []string
	for _, k := range keys {
		v := params[k]
		// 计算键的长度
		keyLen := fmt.Sprintf("%02d", len(k))
		// 计算值的长度
		valueLen := fmt.Sprintf("%04d", len(v))
		// 构建格式：keyLen-key:valueLen-value;
		part := fmt.Sprintf("%s-%s:%s-%s", keyLen, k, valueLen, v)
		signParts = append(signParts, part)
	}

	// 3. 拼接所有部分
	signStr := strings.Join(signParts, ";")

	// 4. 添加 appsecret
	signStr += w.Appsecret

	// 5. 计算 MD5
	hash := md5.New()
	hash.Write([]byte(signStr))
	return hex.EncodeToString(hash.Sum(nil))
}
func (w *WdtRequest) DoRequest(path string, isSandbox bool) (*Response, error) {
	// 1. 构建请求URL
	baseURL := ProductURL
	if isSandbox {
		baseURL = SandboxURL
	}
	apiURL := fmt.Sprintf("%s/%s", baseURL, path)

	// 2. 生成签名
	w.Sign = w.GetSign()

	// 3. 构建请求参数
	values := url.Values{}
	values.Set("sid", w.Sid)
	values.Set("appkey", w.Appkey)
	values.Set("timestamp", fmt.Sprintf("%d", w.Timestamp))
	values.Set("sign", w.Sign)

	// 将业务参数转换为JSON字符串
	if len(w.Params) > 0 {
		for key, v := range w.Params {
			values.Set(key, fmt.Sprintf("%v", v))
		}
	}

	// 4. 发送请求
	client := &http.Client{}
	req, err := http.NewRequest("POST", apiURL, strings.NewReader(values.Encode()))
	if err != nil {
		return nil, fmt.Errorf("create request error: %v", err)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("http request error: %v", err)
	}
	defer resp.Body.Close()

	// 5. 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response error: %v", err)
	}

	// 6. 解析响应
	var result Response
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("unmarshal response error: %v", err)
	}

	// 7. 检查响应状态
	if result.Code != 0 {
		return nil, fmt.Errorf("api error: %s", result.Message)
	}

	return &result, nil
}
