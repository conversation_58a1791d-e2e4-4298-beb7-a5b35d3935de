package common

import (
	"encoding/json"
	"fmt"
)

const (
	SandboxURL = "https://openapitest.huice.com/openapi/"
	ProductURL = "https://openapi.huice.com/openapi"
)

type Response struct {
	Code     int             `json:"code"`
	Message  string          `json:"message"`
	Msg      string          `json:"msg"`
	SubMsg   string          `json:"sub_msg"`
	Total    int             `json:"total_count,omitempty"`
	NewCount int             `json:"new_count,omitempty"`
	ChgCount int             `json:"chg_count,omitempty"`
	Data     json.RawMessage `json:"-"`
}

// UnmarshalJSON 自定义JSON解析 当 json.Unmarshal 解析 JSON 数据到 Response 结构体时，会自动调用这个方法。
func (r *Response) UnmarshalJSON(data []byte) error {
	// 定义一个临时结构体来解析基础字段和数据字段
	type BaseResponse struct {
		Code    int             `json:"code"`
		Message string          `json:"message"`
		Msg     string          `json:"msg"`
		SubMsg  string          `json:"sub_msg"`
		Total   int             `json:"total_count"`
		Data    json.RawMessage `json:"data"`
	}

	var base BaseResponse
	if err := json.Unmarshal(data, &base); err != nil {
		return err
	}

	// 复制基础字段
	r.Code = base.Code
	r.Message = base.Message
	r.Msg = base.Msg
	r.SubMsg = base.SubMsg
	r.Total = base.Total

	// 如果响应中有 data 字段，只保存 data 部分；否则保存整个响应
	if len(base.Data) > 0 {
		r.Data = base.Data
	} else {
		r.Data = json.RawMessage(data)
	}

	return nil
}

// GetListData 通用的获取列表数据方法
func (r *Response) GetListData(listField string, result interface{}) error {
	// 定义一个临时的map来解析数据
	var temp map[string]json.RawMessage
	if err := json.Unmarshal(r.Data, &temp); err != nil {
		return fmt.Errorf("parse response data error: %v", err)
	}

	// 获取指定字段的数据
	if listData, ok := temp[listField]; ok {
		if err := json.Unmarshal(listData, result); err != nil {
			return fmt.Errorf("parse list data error: %v", err)
		}
		return nil
	}

	return fmt.Errorf("field %s not found in response", listField)
}

// GetCount 获取数量的通用方法
func (r *Response) GetTotalCount() (total int) {
	return r.Total
}
