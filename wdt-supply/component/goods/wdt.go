package goods

import (
	"encoding/json"
	"errors"
	"fmt"
	gatherSupplyRequest "gather-supply/request"
	"github.com/chenhg5/collection"
	"go.uber.org/zap"
	"gorm.io/gorm"
	log2 "log"
	pmodel "product/model"
	service2 "product/service"
	callback2 "public-supply/callback"
	"public-supply/common"
	publicModel "public-supply/model"
	"public-supply/request"
	service3 "public-supply/service"
	"public-supply/setting"
	"strconv"
	"strings"
	"sync"
	"wdt-supply/model"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

type Wdt struct {
	WdtData struct {
		BaseInfo struct {
			Sid       string `json:"sid"`
			Appkey    string `json:"appkey"`
			Appsecret string `json:"appsecret"`
		} `json:"baseInfo"`
		UpdateInfo setting.UpdateInfoData `json:"update"`
		Pricing    setting.PricingData    `json:"pricing"`
		Management setting.Management     `json:"management"`
	}
	GatherSupplyID uint
}

func (self *Wdt) SynchronizeProductsToLocal() (err error) {
	//TODO implement me
	panic("implement me")
}

var wg sync.WaitGroup

func (self *Wdt) ImportSelectGoodsRun(info publicModel.SelectGoods) (err error, list interface{}) {
	orderPN := GetOrderNo()
	common.GlobalOrderSN = orderPN
	goodsRecord := publicModel.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: len(info.List),
		Status:            1,
	}
	var resultInt []string
	var field string
	field = "code"

	err = source.DB().Model(pmodel.Product{}).Where("gather_supply_id=?", info.GatherSupplyID).Pluck(field, &resultInt).Error
	if err != nil {
		log2.Println("查询供应链商品id错误", err)
		return
	}

	if len(info.List) <= 0 {
		log2.Println("导入空数据")
		err = errors.New("导入的是空数据")
		return
	}

	var idsArr []string
	idsArr = GetIdArrs(info.List)

	difference := collection.Collect(idsArr).Diff(resultInt).ToStringArray()
	repeat := len(idsArr) - len(difference)
	goodsRecord.RepeatQuantity = repeat //重复数据
	if len(difference) <= 0 {
		goodsRecord.Status = 2
		goodsRecord.CompletionStatus = 1
		source.DB().Omit("goods_arr").Create(&goodsRecord) //当前没有商品可以导入
		return
	}
	source.DB().Omit("goods_arr").Create(&goodsRecord) //创建导入记录
	var goodsList []publicModel.Goods
	for _, v := range difference {
		for _, item := range info.List {
			if item.SN == v {
				goodsList = append(goodsList, item)
			}
		}

	}
	info.List = goodsList
	arrList := splitArray(info.List, 100)
	for index, item := range arrList {
		fmt.Println("循环", index)
		self.RunSelectGoodsConcurrent(orderPN, item, info.Categorys, info.Key, info.GatherSupplyID)
	}
	err = SetImportRecordCompletion(orderPN)
	fmt.Println("全部完成：")

	return
}
func (self *Wdt) RunSelectGoodsConcurrent(orderPN string, list []publicModel.Goods, category string, key string, gatherSupplyID uint) (err error) {

	defer wg.Done()

	cateList := strings.Split(category, ",")
	var cateId1, cateId2, cateId3 int
	cateId1, err = strconv.Atoi(cateList[0])
	cateId2, err = strconv.Atoi(cateList[1])
	cateId3, err = strconv.Atoi(cateList[2])

	var listGoods []*pmodel.Product
	var recordError []publicModel.SupplyGoodsImportRecordErrors
	err, listGoods, recordError = self.CommodityAssembly(list, cateId1, cateId2, cateId3, gatherSupplyID, 0)
	if err != nil {
		return
	}
	if len(listGoods) > 0 {
		err = service3.FinalProcessing(listGoods, orderPN)
		if err != nil {
			return
		}

	}
	if len(recordError) > 0 {
		err = service3.FinalProcessingError(recordError, orderPN)
		if err != nil {
			return
		}
	}
	return

}
func (y *Wdt) ValidateConfig(params gatherSupplyRequest.ValidateConfigParams) (err error) {
	// 聚水潭供应链请求接口时，未使用供应链配置信息
	// 实际使用配置存储位置在应用--工具类--聚水潭，配置项为“wdt_setting”
	return
}

func (wdt *Wdt) InitSetting(gatherSupplyID uint) (err error) {
	var sysSetting model2.SysSetting
	err, sysSetting = setting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		return
	}
	wdt.GatherSupplyID = gatherSupplyID
	err = json.Unmarshal([]byte(sysSetting.Value), &wdt.WdtData)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	if wdt.WdtData.BaseInfo.Appkey == "" || wdt.WdtData.BaseInfo.Appsecret == "" || wdt.WdtData.BaseInfo.Sid == "" {
		err = errors.New("请先配置供应链key")
		return
	}

	return
}
func (s *Wdt) DeleteGoods(id uint) (err error) {
	//TODO implement me
	return
}

func (s *Wdt) GetSupplyBalance(GatherSupplyID uint) (err error, balance interface{}) {

	return
}

func (*Wdt) InitGoods() (err error) {

	return
}

func (wdt *Wdt) GetGoods(info request.GetGoodsSearch) (err error, data interface{}, total int64, serverRatio int) {
	limit := info.Limit
	offset := info.Limit * (info.Page - 1)
	var wdtGoods []model.WdtGoodsDetail
	db := source.DB().Model(&model.WdtGoodsDetail{})
	//db.Where("gather_supply_id = ?", info.GatherSupplyID)

	if info.SearchWords != "" {
		db.Where("`goods_name` like ?", "%"+info.SearchWords+"%")
	}
	if info.IsImport > 0 {
		var wdtProductIds []string
		err = source.DB().Model(&pmodel.Product{}).Where("gather_supply_id = ?", info.GatherSupplyID).Where("deleted_at is NULL").Pluck("code", &wdtProductIds).Error
		if err != nil {
			return
		}

		if info.IsImport == 1 {
			db.Where("`goods_no` in ?", wdtProductIds)

		} else if info.IsImport == 2 {
			if len(wdtProductIds) > 0 {
				db.Where("`goods_no` not in ?", wdtProductIds)
			}
		}

	}
	if info.CategoryID > 0 {
		//db.Where("`c_id` = ?", info.CategoryID)
	}
	//if info.DistributorCoId != "" {
	//	db.Where("`distributor_co_id` = ?", info.DistributorCoId)
	//}
	if info.RangeType != "" {
		if info.RangeType == "guide_price" {
			db.Where("`retail_price` >= ?", info.RangeForm*100)
			db.Where("`retail_price` <= ?", info.RangeTo*100)
		}
		if info.RangeType == "agreement_price" {
			db.Where("`member_price` >= ?", info.RangeForm*100)
			db.Where("`member_price` <= ?", info.RangeTo*100)
		}

	}

	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&wdtGoods).Error

	data = wdt.ProductToGoods(wdtGoods, info.GatherSupplyID)
	return
}

func (wdt *Wdt) ProductToGoods(data []model.WdtGoodsDetail, gatherID uint) (list []publicModel.Goods) {
	var ids []string
	source.DB().Model(pmodel.Product{}).Where("gather_supply_id=?", wdt.GatherSupplyID).Pluck("code", &ids)
	for _, v := range data {

		var isImport = 0
		for _, id := range ids {
			if v.GoodsNo == id {
				isImport = 1
			}
		}
		var rate float64
		if len(v.SpecList) == 0 {
			continue
		}
		if v.MarketPrice > v.MemberPrice {
			rate = service2.Decimal((v.MarketPrice - v.MemberPrice) / (v.MarketPrice))
		}
		var intXS uint64
		var salePrice uint
		if wdt.WdtData.Pricing.SupplySales == 1 {
			intXS, _ = strconv.ParseUint(wdt.WdtData.Pricing.SupplySalesGuide, 10, 32)
			salePrice = uint(v.MarketPrice*100) * uint(intXS) / 100
		} else if wdt.WdtData.Pricing.SupplySales == 2 {
			intXS, _ = strconv.ParseUint(wdt.WdtData.Pricing.SupplySalesAgreement, 10, 32)
			salePrice = uint(v.MemberPrice*100) * uint(intXS) / 100
		} else {
			salePrice = uint(v.MemberPrice * 100)
		}
		var cover string

		cover = v.SpecList[0].ImgUrl
		var item publicModel.Goods
		item = publicModel.Goods{
			GatherSupplyID:    gatherID,
			ThirdCategoryName: "",
			ThirdBrandName:    "",
			IsImport:          uint(isImport),
			MarketPrice:       uint(v.MarketPrice * 100),
			ID:                int(v.ID),
			ProductID:         int(0),
			TotalStock:        0,
			Cover:             cover,
			Status:            1,
			Stock:             0,
			Title:             v.GoodsName,
			CategoryIds:       []string{},
			CostPrice:         uint(v.CostPrice * 100),
			AgreementPrice:    uint(v.MemberPrice * 100),
			GuidePrice:        uint(v.RetailPrice * 100),
			Rate:              rate,
			SalePrice:         salePrice,
			SN:                v.GoodsNo,
		}
		if item.MarketPrice < item.AgreementPrice {
			item.Rate = 0
		}
		list = append(list, item)
	}
	return

}

func (wdt *Wdt) ImportGoodsRun(info request.GetGoodsSearch) (err error, data interface{}) {
	db := source.DB().Model(&model.WdtGoodsDetail{})
	//db.Where("gather_supply_id = ?", info.GatherSupplyID)

	if info.SearchWords != "" {
		db.Where("`item_name` like ?", "%"+info.SearchWords+"%")
	}
	if info.IsImport > 0 {
		var wdtProductIds []string
		err = source.DB().Model(&pmodel.Product{}).Where("gather_supply_id = ?", info.GatherSupplyID).Where("deleted_at is NULL").Pluck("code", &wdtProductIds).Error
		if err != nil {
			return
		}

		if info.IsImport == 1 {
			db.Where("`style_code` in ?", wdtProductIds)

		} else if info.IsImport == 2 {
			if len(wdtProductIds) > 0 {
				db.Where("`style_code` not in ?", wdtProductIds)
			}
		}

	}
	if info.CategoryID > 0 {
		//db.Where("`c_id` = ?", info.CategoryID)
	}
	if info.DistributorCoId != "" {
		db.Where("`distributor_co_id` = ?", info.DistributorCoId)
	}
	if info.RangeType != "" {
		if info.RangeType == "guide_price" {
			db.Where("`sale_price` >= ?", info.RangeForm*100)
			db.Where("`sale_price` <= ?", info.RangeTo*100)
		}
		if info.RangeType == "agreement_price" {
			db.Where("`supply_price` >= ?", info.RangeForm*100)
			db.Where("`supply_price` <= ?", info.RangeTo*100)
		}

	}

	var total int64
	err = db.Count(&total).Error
	searchText, err := json.Marshal(info)

	orderPN := GetOrderNo()
	goodsRecord := publicModel.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: int(total),
		Status:            1,
		SearchCriteria:    string(searchText),
	}

	source.DB().Omit("goods_arr").CreateInBatches(&goodsRecord, 500)

	err = wdt.RunGoodsConcurrent(nil, info, db, 1, orderPN)
	if err != nil {
		return
	}
	err = SetImportRecordCompletion(orderPN)
	if err != nil {
		fmt.Println("变更导入记录状态错误", err)

	}
	fmt.Println("导入供应链商品全部完成")
	return

}

func (wdt *Wdt) RunGoodsConcurrent(wg *sync.WaitGroup, info request.GetGoodsSearch, db *gorm.DB, i int, orderPN string) (err error) {

	var ProductItem []model.WdtGoodsDetail
	err = db.Find(&ProductItem).Error
	if err != nil {
		return
	}
	if len(ProductItem) > 0 {

		cateList := strings.Split(info.Categorys, ",")
		var cateId1, cateId2, cateId3 int
		if len(cateList) >= 2 {
			cateId1, err = strconv.Atoi(cateList[0])
			cateId2, err = strconv.Atoi(cateList[1])
			cateId3, err = strconv.Atoi(cateList[2])
		}

		var Item []publicModel.Goods

		var resultArr []string
		err = source.DB().Model(pmodel.Product{}).Where("gather_supply_id = ?", info.GatherSupplyID).Pluck("code", &resultArr).Error
		if err != nil {
			return
		}

		if len(ProductItem) <= 0 {
			fmt.Println("没有选择可导入的数据")

			return
		}

		Item = wdt.ProductToGoods(ProductItem, info.GatherSupplyID)
		idsArr := GetIdArrs(Item)

		difference := collection.Collect(idsArr).Diff(resultArr).ToStringArray()

		//fmt.Println("查询到的导入数据：", idsArr)
		//fmt.Println("已经存在的数据：", resultArr)
		//fmt.Println("目前可以导入的数据：", difference)

		err = SetImportRepeat(orderPN, len(idsArr)-len(difference))

		if len(difference) <= 0 {
			fmt.Println("没有可以导入的数据")
			err = source.DB().Model(publicModel.SupplyGoodsImportRecord{}).Where("batch=?", orderPN).Update("status", 2).Error

			err = errors.New("当前数据已经存在，请导入新数据")
			return
		}
		var goodsList []publicModel.Goods

		for _, v := range difference {

			for _, item := range Item {
				if item.SN == v {
					goodsList = append(goodsList, item)
				}
			}

		}

		var listGoods []*pmodel.Product
		err, listGoods, _ = wdt.CommodityAssembly(goodsList, cateId1, cateId2, cateId3, info.GatherSupplyID, 0)

		if len(listGoods) > 0 {
			service3.FinalProcessing(listGoods, orderPN)
		}

	} else {
		fmt.Println("获取失败的：", i, info.Limit, info.Source)
	}

	return
}

// 商品组装
func (wdt *Wdt) CommodityAssembly(list []publicModel.Goods, cateId1, cateId2, cateId3 int, gatherSupplyID uint, isUpdate int) (err error, listGoods []*pmodel.Product, recordErrors []publicModel.SupplyGoodsImportRecordErrors) {
	idArr := GetIdArrs(list)
	var data map[string]model.WdtGoodsDetail
	err, data = wdt.BatchGetGoodsDetails(idArr, isUpdate)
	if err != nil {
		return
	}
	//_, detailData := BatchGetGoodsDetails(ids) //批量获取详情
	var riskManageRecord []publicModel.RiskManagementRecord

	var sourceGoodsIds []uint
	for _, elem := range list {
		detail, ok := data[elem.SN]
		if !ok && isUpdate == 0 {
			continue
		}
		goods := new(pmodel.Product)

		if isUpdate == 1 {
			goods.ID = uint(elem.ID)
		}
		goods.Title = detail.GoodsName
		var intXAdvice uint64
		if wdt.WdtData.Pricing.SupplyAdvice == 1 {
			intXAdvice, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplyAdviceGuide, 10, 32)
			goods.OriginPrice = uint(detail.MarketPrice*100) * uint(intXAdvice) / 100
		} else if wdt.WdtData.Pricing.SupplyAdvice == 2 {
			intXAdvice, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplyAdviceAgreement, 10, 32)
			goods.OriginPrice = uint(detail.MarketPrice*100) * uint(intXAdvice) / 100
		} else {
			goods.OriginPrice = uint(detail.MarketPrice * 100)
		}
		var intX uint64
		if wdt.WdtData.Pricing.SupplySales == 1 {
			intX, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplySalesGuide, 10, 32)
			goods.Price = uint(detail.MemberPrice*100) * uint(intX) / 100
		} else if wdt.WdtData.Pricing.SupplySales == 2 {
			intX, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplySalesAgreement, 10, 32)
			goods.Price = uint(detail.MemberPrice*100) * uint(intX) / 100
		} else {
			goods.Price = uint(detail.MemberPrice * 100)
		}
		goods.GuidePrice = goods.OriginPrice
		goods.CostPrice = uint(detail.MemberPrice * 100)
		goods.ActivityPrice = goods.GuidePrice
		goods.Stock = elem.Stock
		//var status int
		//if detail.ItemStatus == "cantDistribution" {
		//	status = 1
		//} else {
		//	status = 0
		//}
		goods.IsDisplay = 0
		var riskRecord publicModel.RiskManagementRecord
		riskRecord.ProductID = goods.ID
		riskRecord.SourceGoodsID = goods.SourceGoodsID
		riskRecord.GatherSupplyID = goods.GatherSupplyID
		if wdt.WdtData.Management.ProductPriceStatus == 1 {
			if goods.Price < goods.CostPrice*(wdt.WdtData.Management.Products/100) {
				goods.IsDisplay = 0
				riskManageRecord = append(riskManageRecord, riskRecord)

			}
		} else if wdt.WdtData.Management.ProductPriceStatus == 2 {
			if (goods.Price-goods.CostPrice)/goods.CostPrice < wdt.WdtData.Management.Profit/100 {
				goods.IsDisplay = 0
				riskManageRecord = append(riskManageRecord, riskRecord)

			}
		}
		goods.ImageUrl = elem.Cover
		goods.Unit = elem.Unit
		goods.Code = detail.GoodsNo
		goods.Unit = "默认"
		goods.Source = common.JUSHUITAN_SOURCE
		goods.SourceGoodsID = detail.ID
		goods.GatherSupplyID = wdt.GatherSupplyID

		if isUpdate == 0 {
			if cateId1 == 0 || cateId2 == 0 || cateId3 == 0 {
				err = errors.New("必须选择分类")
				return
			} else {
				goods.Category1ID = uint(cateId1)
				goods.Category2ID = uint(cateId2)
				goods.Category3ID = uint(cateId3)
			}
		}

		//if detail.Brand != "" {
		//	var brand model3.Brand
		//	brand.Name = detail.Brand
		//	source.DB().Where("`name` = ?", brand.Name).FirstOrCreate(&brand)
		//	goods.BrandID = brand.ID
		//	goods.FreightType = 0
		//	goods.GatherSupplyID = elem.GatherSupplyID
		//}

		/**
		处理轮播图
		*/

		/**
		处理轮播图结束
		*/

		goods.MinPrice = uint(int(goods.Price))
		goods.MaxPrice = uint(int(goods.Price))
		var totalStock int
		//var spec = make(map[string][]string)
		//var specKeys []string

		//var canFixSku = 0
		//goods.DetailImages = "<p>"
		//for _, idi := range detail.ItemPhoto.ItemDetailImages {
		//	goods.DetailImages += "<img src=\"" + idi + "\">"
		//}
		//goods.DetailImages += "</p>"
		//
		//for _, mil := range detail.ItemPhoto.MainImageList {
		//	goods.Gallery = append(goods.Gallery, pmodel.GalleryItem{
		//		Type: 1,
		//		Src:  mil,
		//	})
		//}
		var minProfitRate float64
		for sk, detailSku := range detail.SpecList {
			var sku = pmodel.Sku{}
			//canFixSku++
			sku.Options = append(sku.Options, pmodel.Option{
				SpecName:     "默认规格",
				SpecItemName: detailSku.SpecName,
			})

			//for _, skuSaleAttr := range detailSku.SaleAttribute {
			//	var option pmodel.Option
			//	option.SpecName = skuSaleAttr.AttributeName
			//	option.SpecItemName = skuSaleAttr.AttributeValue
			//	sku.Options = append(sku.Options, option)
			//
			//}
			var intXSku uint64
			if wdt.WdtData.Pricing.SupplyAdvice == 1 {
				intXSku, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplyAdviceGuide, 10, 32)
				sku.OriginPrice = uint(detailSku.MarketPrice*100) * uint(intXSku) / 100
			} else if wdt.WdtData.Pricing.SupplyAdvice == 2 {
				intXSku, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplyAdviceAgreement, 10, 32)
				sku.OriginPrice = uint(detailSku.MarketPrice*100) * uint(intXSku) / 100
			} else {
				sku.OriginPrice = uint(detailSku.MarketPrice * 100)
			}
			var intXS uint64
			if wdt.WdtData.Pricing.SupplySales == 1 {
				intXS, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplySalesGuide, 10, 32)
				sku.Price = uint(detailSku.MemberPrice*100) * uint(intXS) / 100
			} else if wdt.WdtData.Pricing.SupplySales == 2 {
				intXS, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplySalesAgreement, 10, 32)
				sku.Price = uint(detailSku.MemberPrice*100) * uint(intXS) / 100
			} else {
				sku.Price = uint(detailSku.MemberPrice * 100)
			}
			sku.Title = detailSku.SpecName

			sku.Weight = int(detailSku.Weight * 1000)
			if sku.Weight == 0 {
				sku.Weight = 300
			}
			sku.CostPrice = uint(detailSku.MemberPrice * 100)
			sku.IsDisplay = 1

			sku.Stock = int(detailSku.Stock)
			totalStock += sku.Stock
			sku.GuidePrice = sku.OriginPrice
			sku.ActivityPrice = sku.OriginPrice
			sku.OriginalSkuID = 0
			sku.Sn = detailSku.SpecId
			sku.Code = detailSku.SpecNo
			sku.ImageUrl = detailSku.ImgUrl
			if sku.GuidePrice > 0 {
				sku.ProfitRate = utils.ExecProfitRate(sku.GuidePrice, sku.Price)
			} else {
				sku.ProfitRate = 0
			}
			if sk == 0 {
				minProfitRate = sku.ProfitRate
			}
			if sku.ProfitRate <= minProfitRate {
				minProfitRate = sku.ProfitRate
			}
			//err = source.Redis().LPush(context.Background(), "wdtSkuStockSync", sku.Sn).Err()
			goods.Skus = append(goods.Skus, sku)

		}
		if len(goods.Skus) > 0 {
			goods.ProfitRate = minProfitRate
		}
		//for _, ups := range detail.Ups {
		//	goods.Attrs = append(goods.Attrs, pmodel.Attr{
		//		Name:  ups.PName,
		//		Value: ups.PvValue,
		//	})
		//}

		//if totalStock == 0 {
		//	goods.IsDisplay = 0
		//}
		//处理资质json图片数组

		//--------处理详情json图片数组结束

		//处----------------理属性json数组

		//---------处理属性json数组结束
		//goods.Desc=detail.Description

		if len(goods.Skus) > 0 {
			listGoods = append(listGoods, goods)
			//err = source.Redis().LPush(context.Background(), "wdtUploadProduct", goods.SourceGoodsID).Err()
			sourceGoodsIds = append(sourceGoodsIds, goods.SourceGoodsID)
		} else {
			fmt.Println("无规格商品，不导入", goods.ID)
		}

	}
	err = source.DB().CreateInBatches(&riskManageRecord, 1000).Error
	if err != nil {
		return
	}
	//err = service.SetUploadProductIds(sourceGoodsIds)
	return
}

// 批量获取商品详情
func (wdt *Wdt) BatchGetGoodsDetails(ids []string, isUpdate int) (err error, data map[string]model.WdtGoodsDetail) {
	var detailList = make(map[string]model.WdtGoodsDetail)

	fmt.Println("BatchGetGoodsDetails:", ids)
	var list []model.WdtGoodsDetail
	err = source.DB().Where("`goods_no` in ?", ids).Find(&list).Error
	if err != nil {
		return
	}

	var exitsList []pmodel.Product
	err = source.DB().Where("`code` in ?", ids).Where("`gather_supply_id` = ?", wdt.GatherSupplyID).Find(&exitsList).Error
	if err != nil {
		return
	}
	var exitsMap = make(map[string]pmodel.Product)
	for _, e := range exitsList {
		exitsMap[e.Code] = e
	}
	fmt.Println("总解析数量：", len(list))
	for _, item := range list {
		if _, ok := exitsMap[item.GoodsNo]; ok && isUpdate == 0 {
			continue
		}
		detailList[item.GoodsNo] = item
	}
	fmt.Println("总解析数量1：", len(detailList))

	data = detailList
	return
}

func (*Wdt) GetGroup() (err error, data interface{}) {

	return

}

func (*Wdt) GetCategory(info request.GetCategorySearch) (err error, data interface{}) {

	return
}

func (*Wdt) GetCategoryChild(pid int, info request.GetCategoryChild) (err error, data interface{}) {
	var wdtCate []model.WdtCategory
	err = source.DB().Where("parent_id = ?", pid).Find(&wdtCate).Error
	if err != nil {
		return
	}
	return nil, wdtCate
}
func (*Wdt) RunConcurrent(wg *sync.WaitGroup, info request.GetCategorySearch, i int) (err error) {

	return
}

// 选品库增加商品
func (*Wdt) GoodsStorageAdd(ids []int, supplyId uint) (err error, info interface{}) {

	return

}

func (wdt *Wdt) GoodsPriceAlert(GoodsData callback2.GoodsCallBack) (err error) {
	var allProducts []service2.ProductForUpdate
	var allJstProductsMap = make(map[string]model.WdtGoodsDetail)
	err = source.DB().Preload("Skus").Where("id in ?", GoodsData.Data.GoodsIds).Find(&allProducts).Error
	if err != nil {
		return
	}
	var idArr []string
	for _, productp := range allProducts {
		idArr = append(idArr, productp.Code)
	}

	var list []model.WdtGoodsDetail
	err = source.DB().Where("`goods_no` in ?", idArr).Where("deleted_at is null").Find(&list).Error
	if err != nil {
		return
	}
	for _, item := range list {
		allJstProductsMap[item.GoodsNo] = item
	}

	var updateProducts []service2.ProductForUpdate
	for _, product := range allProducts {

		_, ok := allJstProductsMap[product.Code]
		if !ok {
			product.IsDisplay = 0
			for key, _ := range product.Skus {
				product.Skus[key].Stock = 0
			}
			updateProducts = append(updateProducts, product)
			continue
		}

		detail := allJstProductsMap[product.Code]
		//var status int
		//if detail.ItemStatus == "cantDistribution" {
		//	status = 1
		//} else {
		//	status = 0
		//}

		product.SourceGoodsID = detail.ID
		//product.DetailImages = "<p>"
		//for _, idi := range detail.ItemPhoto.ItemDetailImages {
		//	product.DetailImages += "<img src=\"" + idi + "\">"
		//}
		//product.DetailImages += "</p>"
		//
		//for _, mil := range detail.ItemPhoto.MainImageList {
		//	// 重复图片不添加
		//	var repeated bool
		//	for _, pGallery := range product.Gallery {
		//		if pGallery.Src == mil {
		//			repeated = true
		//			break
		//		}
		//	}
		//	if repeated {
		//		continue
		//	}
		//
		//	product.Gallery = append(product.Gallery, pmodel.GalleryItem{
		//		Type: 1,
		//		Src:  mil,
		//	})
		//}
		var pCostPrice, pPrice uint

		var skuList []service2.Sku
		var minProfitRate float64
		var totalStock int
		for sk, detailSku := range detail.SpecList {
			var sku = service2.Sku{}
			for _, oldSku := range product.Skus {
				if oldSku.Code == detailSku.SpecNo {
					sku = oldSku
				}
			}

			//canFixSku++
			var options pmodel.Options
			options = append(options, pmodel.Option{
				SpecName:     "默认规格",
				SpecItemName: detailSku.SpecName,
			})
			sku.Options = options
			//for _, skuSaleAttr := range detailSku.SaleAttribute {
			//	var option pmodel.Option
			//	option.SpecName = skuSaleAttr.AttributeName
			//	option.SpecItemName = skuSaleAttr.AttributeValue
			//	sku.Options = append(sku.Options, option)
			//
			//}
			var costPrice, salePrice, originPrice, activityPrice, guidePrice uint
			var intXSku uint64
			if wdt.WdtData.Pricing.SupplyAdvice == 1 {
				intXSku, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplyAdviceGuide, 10, 32)
				originPrice = uint(detailSku.MarketPrice*100) * uint(intXSku) / 100
			} else if wdt.WdtData.Pricing.SupplyAdvice == 2 {
				intXSku, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplyAdviceAgreement, 10, 32)
				originPrice = uint(detailSku.MarketPrice*100) * uint(intXSku) / 100
			} else {
				originPrice = uint(detailSku.MarketPrice * 100)
			}
			guidePrice = sku.OriginPrice
			activityPrice = sku.OriginPrice
			var intXS uint64
			if wdt.WdtData.Pricing.SupplySales == 1 {
				intXS, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplySalesGuide, 10, 32)
				salePrice = uint(detailSku.MemberPrice*100) * uint(intXS) / 100
			} else if wdt.WdtData.Pricing.SupplySales == 2 {
				intXS, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplySalesAgreement, 10, 32)
				salePrice = uint(detailSku.MemberPrice*100) * uint(intXS) / 100
			} else {
				salePrice = uint(detailSku.MemberPrice * 100)
			}
			costPrice = uint(detailSku.MemberPrice * 100)

			if sku.ID == 0 {
				sku.OriginPrice = originPrice
				sku.GuidePrice = guidePrice
				sku.ActivityPrice = activityPrice
				sku.Price = salePrice
				sku.CostPrice = costPrice
			} else {
				sku.Price = salePrice
				sku.CostPrice = costPrice

			}

			sku.Title = detailSku.SpecName
			sku.Weight = int(detailSku.Weight * 1000)
			if sku.Weight == 0 {
				sku.Weight = 300
			}
			sku.IsDisplay = 1

			sku.OriginalSkuID = 0
			sku.Sn = detailSku.SpecId
			sku.Code = detailSku.SpecNo
			sku.ImageUrl = detailSku.ImgUrl
			if sku.GuidePrice > 0 {
				sku.ProfitRate = utils.ExecProfitRate(sku.GuidePrice, sku.Price)
			} else {
				sku.ProfitRate = 0
			}
			if sk == 0 {
				minProfitRate = sku.ProfitRate
			}
			if sku.ProfitRate <= minProfitRate {
				minProfitRate = sku.ProfitRate
			}

			if sk == 0 {
				pPrice = sku.Price
			}
			if sku.Price <= pPrice {
				pPrice = sku.Price
			}
			if sk == 0 {
				pCostPrice = sku.CostPrice
			}
			if sku.CostPrice <= pCostPrice {
				pCostPrice = sku.CostPrice
			}
			//todo 加上库存同步,等待云信本地缓存更新完成
			sku.Stock = int(detailSku.Stock)

			totalStock += sku.Stock
			skuList = append(skuList, sku)

		}
		product.Skus = skuList
		if totalStock > 0 {
			if product.StatusLock == 0 {
				product.IsDisplay = 1
			}
		}
		product.Price = pPrice
		product.CostPrice = pCostPrice
		//product.GuidePrice = pGuidePrice
		//product.OriginPrice = pOriginPrice
		//product.ActivityPrice = pActivityPrice
		//if status == 0 {
		//	product.IsDisplay = status
		//}
		//风控
		//if wdt.WdtData.Management.ProductPriceStatus == 1 {
		//	if product.Price < product.CostPrice*(wdt.WdtData.Management.Products/100) {
		//		product.IsDisplay = 0
		//	}
		//} else if wdt.WdtData.Management.ProductPriceStatus == 2 {
		//	if (product.Price-product.CostPrice)/product.CostPrice < wdt.WdtData.Management.Profit/100 {
		//		product.IsDisplay = 0
		//	}
		//}
		product.ProfitRate = minProfitRate
		//err = source.Redis().LPush(context.Background(), "wdtUploadProduct", product.SourceGoodsID).Err()
		updateProducts = append(updateProducts, product)

	}
	//updateGoods := szbao.ProductToGoods(needUpdateProducts, GatherSupplyID)
	//err, updateProducts = szbao.CommodityAssembly(updateGoods, 0, 0, 0, GatherSupplyID, 1)
	//if err != nil {
	//	return
	//}
	for _, updateProduct := range updateProducts {
		err = service2.UpdateProduct(updateProduct)
		if err != nil {
			log.Log().Info("wdt修改商品出错", zap.Any("data", updateProduct))
			continue
		}
	}
	return
}
