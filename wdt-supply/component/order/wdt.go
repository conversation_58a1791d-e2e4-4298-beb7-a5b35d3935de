package order

import (
	afterSalesModel "after-sales/model"
	afterSalesRequest "after-sales/request"
	"encoding/json"
	"errors"
	"fmt"
	v1 "order/api/v1"
	"order/express"
	orderModel "order/model"
	order2 "order/order"
	request2 "order/request"
	"product/model"
	callback2 "public-supply/callback"
	"public-supply/request"
	"public-supply/response"
	"public-supply/setting"
	express2 "shipping/express"
	"strconv"
	"strings"
	"time"
	"wdt-supply/common"
	model3 "wdt-supply/model"
	"wdt-supply/order"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"

	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
)

type Wdt struct {
	WdtData struct {
		BaseInfo struct {
			Sid                  string `json:"sid"`
			Appkey               string `json:"appkey"`
			Appsecret            string `json:"appsecret"`
			ShopId               string `json:"shop_id"`
			DistributorAppkey    string `json:"distributor_appkey"`
			DistributorAppsecret string `json:"distributor_appsecret"`
		} `json:"baseInfo"`
		UpdateInfo setting.UpdateInfoData `json:"update"`
		Pricing    setting.PricingData    `json:"pricing"`
		Management setting.Management     `json:"management"`
	}
	GatherSupplyID uint
}

func (y *Wdt) UploadGatherSupplySN(request request.UpdateData) (err error, data interface{}) {
	//TODO implement me
	panic("implement me")
}

func (hehe *Wdt) GetRefundTypes(orderId uint, orderItemID uint) (err error, data []afterSalesModel.AfterSalesType) {
	return
}

func (hehe *Wdt) GetAllAddress() (err error, data interface{}) {
	return
}
func (y *Wdt) InitSetting(gatherSupplyID uint) (err error) {

	var sysSetting model2.SysSetting
	err, sysSetting = setting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		return
	}
	y.GatherSupplyID = gatherSupplyID
	err = json.Unmarshal([]byte(sysSetting.Value), &y.WdtData)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	if y.WdtData.BaseInfo.Appkey == "" || y.WdtData.BaseInfo.Appsecret == "" {
		err = errors.New("请先配置供应链key")
		return
	}

	return
}
func (s *Wdt) DeleteGoods(id uint) (err error) {
	//TODO implement me
	return
}

func (s *Wdt) GetSupplyBalance(GatherSupplyID uint) (err error, balance interface{}) {

	return
}

func (jushuitan *Wdt) OrderBeforeCheck(request request.RequestSaleBeforeCheck) (err error, data response.BeforeCheck) {
	data.Freight = 0
	data.Code = 1
	return
}

type WdtStockResponse struct {
	Msg  string `json:"msg"`
	Code int    `json:"code"`
	Data struct {
		Total int `json:"total"`
		List  []struct {
			ItemCode        string  `json:"item_code"`
			Created         string  `json:"created"`
			StyleCode       string  `json:"style_code"`
			Weight          float64 `json:"weight"`
			ItemName        string  `json:"item_name"`
			BrandName       string  `json:"brand_name"`
			Pic             string  `json:"pic"`
			SupplyPrice     float64 `json:"supply_price"`
			SalePrice       float64 `json:"sale_price"`
			StockStr        string  `json:"stock_str"`
			PropertiesValue string  `json:"properties_value"`
			BasePrice       float64 `json:"base_price"`
			Stock           int     `json:"stock"`
		} `json:"list"`
	} `json:"data"`
	RequestId string `json:"request_id"`
}

func (jushuitan *Wdt) SaleBeforeCheck(request request.RequestSaleBeforeCheck) (err error, resData response.ResSaleBeforeCheck) {
	for _, v := range request.Skus {
		resData.Data.Available = append(resData.Data.Available, uint(v.Sku.Sku))
	}
	return
}

type ConfirmOrderRequest struct {
	GoodsId        int    `json:"goodsId"`
	GoodsNum       int    `json:"goodsNum"`
	GoodsSkuId     string `json:"goodsSkuId"`
	CusOrderNo     string `json:"cus_order_no"`
	IDCard         string `json:"IDCard"`
	UserName       string `json:"user_name"`
	Mode           string `json:"mode"`
	BuyerRemark    string `json:"buyer_remark"`
	Name           string `json:"name"`
	Phone          int64  `json:"phone"`
	ProvinceId     string `json:"province_id"`
	CityId         string `json:"city_id"`
	RegionId       string `json:"region_id"`
	Detail         string `json:"detail"`
	IdCardFrontImg string `json:"id_card_front_img"`
	IdCardBackImg  string `json:"id_card_back_img"`
}

func (wdt *Wdt) ConfirmOrder(request request.RequestConfirmOrder) (err error, info *stbz.APIResult) {
	var localOrder orderModel.Order
	err = source.DB().Preload("OrderItems.Sku.Product").Where("order_sn = ?", request.OrderSn.OrderSn).First(&localOrder).Error
	if err != nil {
		return
	}
	var wdtOrder model3.WdtOrder

	// 基础订单信息
	wdtOrder.Tid = request.OrderSn.OrderSn
	wdtOrder.TradeStatus = 30 // 已付款待发货
	wdtOrder.PayStatus = 2    // 已支付
	wdtOrder.DeliveryTerm = 1 // 款到发货
	//wdtOrder.TradeTime = time.Now().Format("2006-01-02 15:04:05")
	//wdtOrder.PayTime = time.Now().Format("2006-01-02 15:04:05")

	// 买家信息
	wdtOrder.BuyerNick = request.Address.Consignee // 必填，买家昵称用收货人名称

	// 收货人信息 (都是必填)
	wdtOrder.ReceiverName = request.Address.Consignee
	wdtOrder.ReceiverMobile = request.Address.Phone
	wdtOrder.ReceiverProvince = request.Address.Province
	wdtOrder.ReceiverCity = request.Address.City
	wdtOrder.ReceiverDistrict = request.Address.Area
	wdtOrder.ReceiverAddress = request.Address.Description

	// 金额相关 (都是必填)
	wdtOrder.PostAmount = 0  // 邮费
	wdtOrder.CodAmount = 0   // 货到付款金额
	wdtOrder.ExtCodFee = 0   // 货到付款买家费用
	wdtOrder.OtherAmount = 0 // 其它收费

	// 订单商品信息
	var orderList []model3.WdtOrderGoods
	var totalAmount float64
	for _, item := range localOrder.OrderItems {

		price := utils.Decimal(float64(item.Sku.Price) / 100) // 单价(分转元)
		num := float64(item.Qty)
		amount := utils.Decimal(price * num) // 小计金额
		totalAmount += amount

		orderGoods := model3.WdtOrderGoods{
			Oid:           fmt.Sprintf("%s_%d", request.OrderSn.OrderSn, item.ID), // 子订单号必填且唯一
			GoodsId:       fmt.Sprintf("%d", item.Sku.Product.SourceGoodsID),      // 必填
			GoodsNo:       item.Sku.Product.SourceGoodsIDString,                   // 必填
			GoodsName:     item.Sku.Product.Title,                                 // 必填
			SpecName:      item.Sku.Title,                                         // 必填
			SpecId:        item.Sku.Sn,                                            // 必填
			SpecNo:        item.Sku.Code,                                          // 必填
			Num:           num,                                                    // 必填
			Price:         price,                                                  // 必填
			Status:        30,                                                     // 必填，与主订单状态一致
			RefundStatus:  0,                                                      // 必填，无退款
			AdjustAmount:  0,                                                      // 必填
			Discount:      0,                                                      // 必填
			ShareDiscount: 0,                                                      // 必填
		}
		orderList = append(orderList, orderGoods)
	}
	wdtOrder.OrderList = orderList

	// 计算已付金额 (必填)
	// paid = Σ(price * num + adjust_amount - discount - share_discount) + post_amount + other_amount
	wdtOrder.Paid = utils.Decimal(totalAmount + wdtOrder.PostAmount + wdtOrder.OtherAmount)

	// 推送订单到旺店通
	wdtClient := &order.WdtOrder{}
	wdtClient.SetBase(wdt.WdtData.BaseInfo.Appkey, wdt.WdtData.BaseInfo.Appsecret, wdt.WdtData.BaseInfo.Sid, wdt.WdtData.BaseInfo.ShopId)
	err = wdtClient.PushOrder(wdtOrder)
	if err != nil {
		return err, nil
	}
	err = source.DB().Model(&order2.Order{}).Where("order_sn = ?", request.OrderSn.OrderSn).Update("gather_supply_sn", "已推送").Error
	if err != nil {
		return
	}
	info = &stbz.APIResult{
		Code: 1,
		Msg:  "success",
	}
	return
}

func (jushuitan *Wdt) ExpressQuery(request request.RequestExpress) (err error, info interface{}) {
	return
}

func (jushuitan *Wdt) AfterSalesBeforeCheck(request request.RequestAfterSale) (err error, info interface{}) {
	return
}

func (jushuitan *Wdt) AfterSalesPicture(request request.RequestAfterSalePicture) (err error, info interface{}) {
	return
}

func (jushuitan *Wdt) AfterSale(request request.AfterSale) (err error, info interface{}) {
	return
}

func (jushuitan *Wdt) OrderDelivery(OrderData callback2.OrderCallBack) (err error) {
	return
}
func (jushuitan *Wdt) SyncOrderExpNo(unionIdList []string) (err error, data []response.SyncOrderExpNoResponse) {
	return
}

// WdtAfterSalesResponse wdt售后响应结构体
type WdtAfterSalesResponse struct {
	Code      int    `json:"code"`
	Message   string `json:"message"`
	NewCount  int    `json:"new_count"`
	ChgCount  int    `json:"chg_count"`
	RequestId string `json:"request_id"`
}

func (wdt *Wdt) AfterSalesCreate(requestParam afterSalesRequest.AfterSales) (err error, info *stbz.APIResult) {
	info = &stbz.APIResult{}
	log.Log().Info("wdt供应链同步售后", zap.Any("info", requestParam))

	// 查询本地订单项
	var orderItem orderModel.OrderItem
	err = source.DB().First(&orderItem, requestParam.OrderItemID).Error
	if err != nil {
		log.Log().Error("wdt退货申请出错1:本地订单不存在")
		return
	}
	// 查询本地订单
	var orderData orderModel.Order
	err = source.DB().First(&orderData, requestParam.OrderID).Error
	if err != nil {
		log.Log().Error("wdt退货申请出错2:本地订单不存在1")
		return
	}

	// 构建售后请求参数
	var refundList []map[string]interface{}
	refundItem := make(map[string]interface{})

	// 平台ID固定值127
	refundItem["platform_id"] = 127
	// 店铺编号
	refundItem["shop_id"], _ = strconv.Atoi(wdt.WdtData.BaseInfo.ShopId)
	// 平台销售订单号
	refundItem["tid"] = orderData.OrderSN
	// 平台退款/退货单号 - 使用售后ID作为退款单号
	refundItem["refund_no"] = fmt.Sprintf("AS%d", requestParam.Id)

	// 映射售后类型
	var wdtType int
	switch requestParam.RefundType {
	case 0: // 仅退款
		if orderItem.SendStatus == 0 {
			wdtType = 1 // 退款(未发货退款)
		} else {
			wdtType = 4 // 退款不退货
		}
	case 1: // 退货退款
		wdtType = 2 // 退货退款
	case 2: // 换货
		wdtType = 3 // 换货
	default:
		wdtType = 1
	}
	refundItem["type"] = wdtType

	// 售后状态 - 根据当前状态设置
	refundItem["status"] = 2 // 等待退货（卖家同意后）

	// 退款金额
	if requestParam.RefundWay == 0 {
		// 整单退款
		refundItem["refund_fee"] = float64(requestParam.Amount) / 100
	} else {
		// 部分退款
		refundItem["refund_fee"] = float64(requestParam.Amount) / 100
	}

	// 买家昵称 - 使用默认值
	refundItem["buyer_nick"] = "买家"

	// 单据创建时间
	refundItem["refund_time"] = time.Now().Format("2006-01-02 15:04:05")

	// 退款原因
	if requestParam.Reason != "" {
		refundItem["reason"] = requestParam.Reason
	}

	// 备注 - 使用Description字段
	if requestParam.Description != "" {
		refundItem["desc"] = requestParam.Description
	}

	// 售后子订单
	var orderList []map[string]interface{}
	orderSubItem := make(map[string]interface{})
	orderSubItem["oid"] = strconv.Itoa(int(orderData.OrderSN)) + "_" + strconv.Itoa(int(orderItem.ID)) // 子订单编号

	// 退货货品数量
	if requestParam.RefundWay == 1 {
		orderSubItem["num"] = requestParam.Num
	} else {
		orderSubItem["num"] = orderItem.Qty
	}

	orderList = append(orderList, orderSubItem)
	refundItem["order_list"] = orderList

	refundList = append(refundList, refundItem)
	var jsonData []byte
	jsonData, err = json.Marshal(refundList)
	if err != nil {
		return
	}
	// 构建最终请求参数
	requestParams := make(map[string]interface{})
	requestParams["api_refund_list"] = string(jsonData)

	// 创建wdt请求
	wdtRequest := &common.WdtRequest{
		Sid:       wdt.WdtData.BaseInfo.Sid,
		Appkey:    wdt.WdtData.BaseInfo.Appkey,
		Appsecret: wdt.WdtData.BaseInfo.Appsecret,
	}
	wdtRequest.SetParams(requestParams)

	// 发送请求
	resp, err := wdtRequest.DoRequest("sales_refund_push.php", false)
	if err != nil {
		log.Log().Error("wdt退货申请出错3", zap.Any("err", err.Error()))
		return
	}

	// 解析响应
	var responseData WdtAfterSalesResponse
	err = json.Unmarshal(resp.Data, &responseData)
	if err != nil {
		log.Log().Error("wdt退货申请出错4", zap.Any("data", string(resp.Data)), zap.Any("err", err.Error()))
		return
	}

	if responseData.Code == 0 {
		// 成功，更新本地售后记录 - 使用生成的退款单号
		refundNo := fmt.Sprintf("AS%d", requestParam.Id)
		err = source.DB().Model(&afterSalesModel.AfterSales{}).Where("id = ?", requestParam.Id).Update("syn_after_sales_id_string", refundNo).Error
		if err != nil {
			log.Log().Error("wdt退货申请出错5", zap.Any("err", err.Error()))
			return
		}
		info.Code = 1
		info.Msg = "success"
	} else {
		info.Code = responseData.Code
		info.Msg = responseData.Message
	}

	log.Log().Info("wdt售后申请", zap.Any("response", string(resp.Data)))
	return
}
func (wdt *Wdt) AfterSalesSend(requestParam afterSalesRequest.SendRequest) (err error) {

	return
}
func (wdt *Wdt) AfterSalesUserClose(requestParam afterSalesRequest.AfterSales) (err error) {

	return
}

// WdtLogisticsTradeInfo WDT物流交易信息
type WdtLogisticsTradeInfo struct {
	RecId            string      `json:"rec_id"`
	ShopId           string      `json:"shop_id"`
	Tid              string      `json:"tid"`
	LogisticsNo      string      `json:"logistics_no"`
	LogisticsType    int         `json:"logistics_type"`
	DeliveryTerm     int         `json:"delivery_term"`
	ConsignTime      string      `json:"consign_time"`
	IsPartSync       int         `json:"is_part_sync"`
	Oids             string      `json:"oids"`
	PlatformId       int         `json:"platform_id"`
	TradeId          string      `json:"trade_id"`
	LogisticsCodeErp string      `json:"logistics_code_erp"`
	LogisticsNameErp string      `json:"logistics_name_erp"`
	LogisticsName    string      `json:"logistics_name"`
	IsNeedSync       int         `json:"is_need_sync"`
	SyncStatus       int         `json:"sync_status"`
	LogisticsId      interface{} `json:"logistics_id"`
	Description      string      `json:"description"`
	SyncTime         interface{} `json:"sync_time"`
	ErrorCode        string      `json:"error_code"`
	ErrorMsg         string      `json:"error_msg"`
	TryTimes         int         `json:"try_times"`
	Modified         string      `json:"modified"`
	Created          string      `json:"created"`
}

// WdtLogisticsQueryResponse WDT物流查询响应
type WdtLogisticsQueryResponse struct {
	Code       int                     `json:"code"`
	Message    string                  `json:"message"`
	TotalCount int                     `json:"total_count"`
	Trades     []WdtLogisticsTradeInfo `json:"trades"`
}

// WdtLogisticsAckItem WDT物流同步回写项
type WdtLogisticsAckItem struct {
	RecId   string `json:"rec_id"`  // 回写的记录id
	Status  int    `json:"status"`  // 回写状态: 0成功 1失败
	Message string `json:"message"` // 相关描述信息
}

// WdtLogisticsAckResponse WDT物流同步回写响应
type WdtLogisticsAckResponse struct {
	Code    int                    `json:"code"`
	Message string                 `json:"message"`
	Errors  []WdtLogisticsAckError `json:"errors"`
}

// WdtLogisticsAckError WDT物流同步回写错误
type WdtLogisticsAckError struct {
	RecId int64  `json:"rec_id"` // 回写的记录id
	Error string `json:"error"`  // 错误信息的描述
}

// QueryLogistics 查询物流同步信息
func (wdt *Wdt) QueryLogistics(limit int, shopId string) (*WdtLogisticsQueryResponse, error) {
	// 构建请求参数
	params := map[string]interface{}{
		"limit": limit,
	}

	if shopId != "" {
		params["shop_id"] = shopId
	}

	// 创建WDT请求
	wdtRequest := &common.WdtRequest{
		Sid:       wdt.WdtData.BaseInfo.Sid,
		Appkey:    wdt.WdtData.BaseInfo.Appkey,
		Appsecret: wdt.WdtData.BaseInfo.Appsecret,
	}
	wdtRequest.SetParams(params)

	// 发送请求
	resp, err := wdtRequest.DoRequest("logistics_sync_query.php", false)
	if err != nil {
		log.Log().Error("查询WDT物流信息失败", zap.Error(err))
		return nil, err
	}

	// 解析响应
	var result WdtLogisticsQueryResponse
	if len(resp.Data) > 0 {
		err = json.Unmarshal(resp.Data, &result)
		if err != nil {
			log.Log().Error("解析WDT物流查询响应失败", zap.Error(err))
			return nil, err
		}
	} else {
		// 如果没有data字段，构建基本响应
		result.Code = resp.Code
		result.Message = resp.Message
		result.TotalCount = resp.Total
		result.Trades = []WdtLogisticsTradeInfo{}
	}

	log.Log().Info("查询WDT物流信息成功",
		zap.Int("total_count", result.TotalCount),
		zap.Int("trades_count", len(result.Trades)),
	)

	return &result, nil
}

// AckLogistics 回写物流同步结果
func (wdt *Wdt) AckLogistics(logisticsList []WdtLogisticsAckItem) error {
	if len(logisticsList) == 0 {
		return nil
	}
	var jsonData []byte
	jsonData, err := json.Marshal(logisticsList)
	if err != nil {
		return err
	}
	// 构建请求参数
	params := map[string]interface{}{
		"logistics_list": string(jsonData),
	}

	// 创建WDT请求
	wdtRequest := &common.WdtRequest{
		Sid:       wdt.WdtData.BaseInfo.Sid,
		Appkey:    wdt.WdtData.BaseInfo.Appkey,
		Appsecret: wdt.WdtData.BaseInfo.Appsecret,
	}
	wdtRequest.SetParams(params)

	// 发送请求
	resp, err := wdtRequest.DoRequest("logistics_sync_ack.php", false)
	if err != nil {
		log.Log().Error("回写WDT物流同步结果失败", zap.Error(err))
		return err
	}

	// 解析响应
	var result WdtLogisticsAckResponse
	if len(resp.Data) > 0 {
		err = json.Unmarshal(resp.Data, &result)
		if err != nil {
			log.Log().Error("解析WDT物流回写响应失败", zap.Error(err))
			return err
		}

		// 检查是否有错误项
		if len(result.Errors) > 0 {
			log.Log().Warn("部分物流同步回写失败", zap.Any("errors", result.Errors))
		}
	}

	log.Log().Info("回写WDT物流同步结果成功",
		zap.Int("logistics_count", len(logisticsList)),
	)

	return nil
}

// WdtLogisticsSyncResult 物流同步结果
type WdtLogisticsSyncResult struct {
	TotalQueried   int                      `json:"total_queried"`   // 查询到的总数
	TotalProcessed int                      `json:"total_processed"` // 处理的总数
	SuccessCount   int                      `json:"success_count"`   // 成功数量
	FailureCount   int                      `json:"failure_count"`   // 失败数量
	Details        []WdtLogisticsSyncDetail `json:"details"`         // 详细信息
}

// WdtLogisticsSyncDetail 物流同步详情
type WdtLogisticsSyncDetail struct {
	RecId       string `json:"rec_id"`
	Tid         string `json:"tid"`
	LogisticsNo string `json:"logistics_no"`
	Status      string `json:"status"` // success, failed
	Message     string `json:"message"`
}

// SyncLogistics 同步物流信息
func (wdt *Wdt) SyncLogistics(limit int) (*WdtLogisticsSyncResult, error) {
	result := &WdtLogisticsSyncResult{}

	// 查询待同步的物流信息
	queryResp, err := wdt.QueryLogistics(limit, wdt.WdtData.BaseInfo.ShopId)
	if err != nil {
		return result, fmt.Errorf("查询物流信息失败: %v", err)
	}

	result.TotalQueried = queryResp.TotalCount
	result.TotalProcessed = len(queryResp.Trades)

	if len(queryResp.Trades) == 0 {
		log.Log().Info("没有待同步的物流信息")
		return result, nil
	}

	// 处理每条物流信息
	var ackItems []WdtLogisticsAckItem
	for _, trade := range queryResp.Trades {
		detail := WdtLogisticsSyncDetail{
			RecId:       trade.RecId,
			Tid:         trade.Tid,
			LogisticsNo: trade.LogisticsNo,
		}

		// 处理单条物流信息
		success := wdt.processSingleLogistics(trade)

		if success {
			detail.Status = "success"
			detail.Message = "同步成功"
			result.SuccessCount++
			ackItems = append(ackItems, WdtLogisticsAckItem{
				RecId:   trade.RecId,
				Status:  0, // 0表示成功
				Message: "同步成功",
			})
		} else {
			detail.Status = "failed"
			detail.Message = "同步失败"
			result.FailureCount++
			ackItems = append(ackItems, WdtLogisticsAckItem{
				RecId:   trade.RecId,
				Status:  1, // 1表示失败
				Message: "同步失败",
			})
		}

		result.Details = append(result.Details, detail)
	}

	// 回写同步结果
	if len(ackItems) > 0 {
		err = wdt.AckLogistics(ackItems)
		if err != nil {
			log.Log().Error("回写物流同步结果失败", zap.Error(err))
			// 不返回错误，因为主要的同步逻辑已经完成
		}
	}

	log.Log().Info("物流同步完成",
		zap.Int("total_processed", result.TotalProcessed),
		zap.Int("success_count", result.SuccessCount),
		zap.Int("failure_count", result.FailureCount),
	)

	return result, nil
}

// processSingleLogistics 处理单条物流信息
func (wdt *Wdt) processSingleLogistics(trade WdtLogisticsTradeInfo) bool {
	log.Log().Info("wdt处理物流信息",
		zap.String("tid", trade.Tid),
		zap.String("logistics_no", trade.LogisticsNo),
		zap.String("logistics_name", trade.LogisticsName),
		zap.String("consign_time", trade.ConsignTime),
		zap.Int("sync_status", trade.SyncStatus),
	)

	// 这里可以添加具体的业务逻辑，比如：
	// 1. 更新本地订单的物流信息
	// 2. 同步到其他系统
	// 3. 发送通知给客户
	// 4. 更新订单状态为已发货

	// 示例：根据物流信息更新订单状态
	err := wdt.updateOrderLogisticsInfo(trade)
	if err != nil {
		log.Log().Error("更新订单物流信息失败",
			zap.String("tid", trade.Tid),
			zap.Error(err),
		)
		return false
	}

	return true
}
func getCompanyCode(name string) (code string) {
	var com []express2.Company
	com = express2.GetCompanyList()
	for _, c := range com {
		if strings.Contains(c.Name, name) || strings.Contains(name, c.Name) {
			return c.Code
		}
	}
	return
}

// updateOrderLogisticsInfo 更新订单物流信息
func (wdt *Wdt) updateOrderLogisticsInfo(trade WdtLogisticsTradeInfo) (err error) {
	var orderRequest v1.HandleOrderRequest

	var orderInfo orderModel.Order
	err = source.DB().Preload("OrderItems").Where("order_sn = ?", trade.Tid).First(&orderInfo).Error
	if err != nil {
		return
	}
	//处理快递code
	var companyCode string
	companyCode = getCompanyCode(trade.LogisticsNameErp)
	var expressData = express.OrderExpress{
		OrderID:     orderInfo.ID,
		ExpressNo:   trade.LogisticsNo,
		CompanyCode: companyCode,
		CompanyName: trade.LogisticsNameErp,
	}
	var oids = strings.Split(trade.Oids, ",")
	for _, oid := range oids {
		var orderItemId []string
		orderItemId = strings.Split(oid, "_")
		if len(orderItemId) > 1 {
			var orderItemIdInt int
			orderItemIdInt, err = strconv.Atoi(orderItemId[1])
			if err != nil {
				return
			}
			for _, orderItem := range orderInfo.OrderItems {
				if orderItem.ID == uint(orderItemIdInt) {
					var sku model.Sku
					err = source.DB().Where("id = ?", orderItem.SkuID).First(&sku).Error
					if err != nil {
						return
					}
					expressData.OrderItems = append(expressData.OrderItems, express.ExpressOrderItem{
						ID:         uint(orderItem.ID),
						SkuID:      sku.ID,
						SendNum:    uint(orderItem.Qty),
						SendStatus: orderItem.SendStatus,
					})
				}
			}
		}
	}

	var ids []request2.OrderItemSendInfo
	for _, orderItem := range expressData.OrderItems {

		if orderItem.SendStatus == 0 {
			//本地的订单item发货状态必须符合要求
			ids = append(ids, request2.OrderItemSendInfo{
				ID:  orderItem.ID,
				Num: orderItem.SendNum,
			})
		}

	}
	orderRequest.OrderID = orderInfo.ID
	orderRequest.ExpressNo = expressData.ExpressNo
	orderRequest.OrderItemIDs = ids
	orderRequest.CompanyCode = expressData.CompanyCode
	orderRequest.IsEmpty = expressData.IsEmpty
	err = v1.CallBackSendOrder(orderRequest)
	if err != nil {
		log.Log().Info(strconv.Itoa(int(expressData.OrderID))+"发货error："+err.Error(), zap.Any("data", orderRequest))
		return
	}
	return
}
