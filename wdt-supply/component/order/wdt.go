package order

import (
	afterSalesModel "after-sales/model"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
	"jushuitan/common"
	"product/model"
	callback2 "public-supply/callback"
	"public-supply/request"
	"public-supply/response"
	"public-supply/setting"
	"strconv"
	"strings"
	"time"
	model3 "wdt-supply/model"
	"wdt-supply/order"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

type Wdt struct {
	WdtData struct {
		BaseInfo struct {
			Sid       string `json:"sid"`
			Appkey    string `json:"appkey"`
			Appsecret string `json:"appsecret"`
			ShopId    string `json:"shop_id"`
		} `json:"baseInfo"`
		UpdateInfo setting.UpdateInfoData `json:"update"`
		Pricing    setting.PricingData    `json:"pricing"`
		Management setting.Management     `json:"management"`
	}
	GatherSupplyID uint
}

func (hehe *Wdt) GetRefundTypes(orderId uint, orderItemID uint) (err error, data []afterSalesModel.AfterSalesType) {
	return
}

func (hehe *Wdt) GetAllAddress() (err error, data interface{}) {
	return
}
func (y *Wdt) InitSetting(gatherSupplyID uint) (err error) {

	var sysSetting model2.SysSetting
	err, sysSetting = setting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		return
	}
	GatherSupplyID = gatherSupplyID
	err = json.Unmarshal([]byte(sysSetting.Value), &WdtData)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	if WdtData.BaseInfo.AppKey == "" || WdtData.BaseInfo.AppSecret == "" {
		err = errors.New("请先配置供应链key")
		return
	}

	return
}
func (s *Wdt) DeleteGoods(id uint) (err error) {
	//TODO implement me
	return
}

func (s *Wdt) GetSupplyBalance(GatherSupplyID uint) (err error, balance interface{}) {

	return
}

func (jushuitan *Wdt) OrderBeforeCheck(request request.RequestSaleBeforeCheck) (err error, data response.BeforeCheck) {
	var localSkuIds []uint
	for _, localSku := range request.LocalSkus {
		localSkuIds = append(localSkuIds, uint(localSku.Sku.Sku))
	}
	var skus []model.Sku
	err = source.DB().Preload("Product").Where("id in ?", localSkuIds).Find(&skus).Error
	if err != nil {
		data.Msg = "获取商品信息失败"
		return
	}
	var SkuSns []string
	var checkSkuStockMap = make(map[string]int)
	var checkSkuTitleMap = make(map[string]string)
	var jushuitanCoId string
	for _, sku := range skus {
		SkuSns = append(SkuSns, sku.Sn)
		checkSkuTitleMap[sku.Sn] = sku.Product.Title
		jushuitanCoId = sku.Product.WdtDistributorCoId
		for _, localSku := range request.LocalSkus {
			if uint(localSku.Sku.Sku) == sku.ID {
				checkSkuStockMap[sku.Sn] = localSku.Number
			}
		}
	}
	if jushuitanCoId != "10419498" {
		//水滴新店铺不校验库存
		var requestParams = make(map[string]interface{})
		requestParams["page_num"] = 1
		requestParams["page_size"] = 100
		requestParams["supplier_co_id"] = jushuitanCoId
		requestParams["item_codes"] = strings.Join(SkuSns, ",")
		var requestJson []byte
		requestJson, err = json.Marshal(requestParams)
		if err != nil {
			data.Msg = "请求参数转换失败"
			return
		}
		var result []byte
		err, result = common.RequestWdt(requestJson, "https://openapi.jushuitan.com/open/api/goods/inneropen/supplier/goods/querydiserpgoodsdata")
		if err != nil {
			data.Msg = "请求接口失败"
			return
		}

		var responseData WdtStockResponse
		err = json.Unmarshal(result, &responseData)
		if err != nil {
			data.Msg = "数据解析失败"
			return
		}
		if responseData.Code != 0 {
			log.Log().Error("聚水潭请求库存出错", zap.Any("data", responseData))
			data.Msg = responseData.Msg
			return
		}
		var jstStockData = make(map[string]int)
		for _, jstSd := range responseData.Data.List {
			jstStockData[jstSd.ItemCode] = jstSd.Stock
		}
		for skuId, stock := range checkSkuStockMap {
			if _, ok := jstStockData[skuId]; ok {
				if jstStockData[skuId] < stock {
					err = errors.New("商品'" + checkSkuTitleMap[skuId] + "'库存不足")
					data.Msg = "商品'" + checkSkuTitleMap[skuId] + "'库存不足"
					return
				}
			} else {
				err = errors.New("商品'" + checkSkuTitleMap[skuId] + "'库存不足")
				data.Msg = "商品'" + checkSkuTitleMap[skuId] + "'库存不足"
				return
			}
		}
	}

	data.Freight = 0
	data.Code = 1
	return
}

type WdtStockResponse struct {
	Msg  string `json:"msg"`
	Code int    `json:"code"`
	Data struct {
		Total int `json:"total"`
		List  []struct {
			ItemCode        string  `json:"item_code"`
			Created         string  `json:"created"`
			StyleCode       string  `json:"style_code"`
			Weight          float64 `json:"weight"`
			ItemName        string  `json:"item_name"`
			BrandName       string  `json:"brand_name"`
			Pic             string  `json:"pic"`
			SupplyPrice     float64 `json:"supply_price"`
			SalePrice       float64 `json:"sale_price"`
			StockStr        string  `json:"stock_str"`
			PropertiesValue string  `json:"properties_value"`
			BasePrice       float64 `json:"base_price"`
			Stock           int     `json:"stock"`
		} `json:"list"`
	} `json:"data"`
	RequestId string `json:"request_id"`
}

func (jushuitan *Wdt) SaleBeforeCheck(request request.RequestSaleBeforeCheck) (err error, resData response.ResSaleBeforeCheck) {
	for _, v := range request.Skus {
		resData.Data.Available = append(resData.Data.Available, uint(v.Sku.Sku))
	}
	return
}

type ConfirmOrderRequest struct {
	GoodsId        int    `json:"goodsId"`
	GoodsNum       int    `json:"goodsNum"`
	GoodsSkuId     string `json:"goodsSkuId"`
	CusOrderNo     string `json:"cus_order_no"`
	IDCard         string `json:"IDCard"`
	UserName       string `json:"user_name"`
	Mode           string `json:"mode"`
	BuyerRemark    string `json:"buyer_remark"`
	Name           string `json:"name"`
	Phone          int64  `json:"phone"`
	ProvinceId     string `json:"province_id"`
	CityId         string `json:"city_id"`
	RegionId       string `json:"region_id"`
	Detail         string `json:"detail"`
	IdCardFrontImg string `json:"id_card_front_img"`
	IdCardBackImg  string `json:"id_card_back_img"`
}

func (wdt *Wdt) ConfirmOrder(request request.RequestConfirmOrder) (err error, info *stbz.APIResult) {
	var wdtOrder model3.WdtOrder

	// 基础订单信息
	wdtOrder.Tid = request.OrderSn.OrderSn
	wdtOrder.TradeStatus = 30 // 已付款待发货
	wdtOrder.PayStatus = 2    // 已支付
	wdtOrder.DeliveryTerm = 1 // 款到发货
	wdtOrder.TradeTime = time.Now().Format("2006-01-02 15:04:05")
	wdtOrder.PayTime = time.Now().Format("2006-01-02 15:04:05")

	// 买家信息
	wdtOrder.BuyerNick = request.Address.Consignee // 必填，买家昵称用收货人名称

	// 收货人信息 (都是必填)
	wdtOrder.ReceiverName = request.Address.Consignee
	wdtOrder.ReceiverMobile = request.Address.Phone
	wdtOrder.ReceiverProvince = request.Address.Province
	wdtOrder.ReceiverCity = request.Address.City
	wdtOrder.ReceiverDistrict = request.Address.Area
	wdtOrder.ReceiverAddress = request.Address.Description

	// 金额相关 (都是必填)
	wdtOrder.PostAmount = 0  // 邮费
	wdtOrder.CodAmount = 0   // 货到付款金额
	wdtOrder.ExtCodFee = 0   // 货到付款买家费用
	wdtOrder.OtherAmount = 0 // 其它收费

	// 订单商品信息
	var orderList []model3.WdtOrderGoods
	var totalAmount float64
	for i, item := range request.LocalSkus {
		// 查询SKU信息
		var sku model.Sku
		err = source.DB().Preload("Product").Where("id = ?", item.Sku.Sku).First(&sku).Error
		if err != nil {
			log.Log().Error("wdt ConfirmOrder get sku error", zap.Error(err))
			return
		}

		price := utils.Decimal(float64(sku.Price) / 100) // 单价(分转元)
		num := float64(item.Number)
		amount := utils.Decimal(price * num) // 小计金额
		totalAmount += amount

		orderGoods := model3.WdtOrderGoods{
			Oid:           fmt.Sprintf("%s_%d", request.OrderSn.OrderSn, i), // 子订单号必填且唯一
			GoodsId:       fmt.Sprintf("%d", sku.Product.SourceGoodsID),     // 必填
			GoodsName:     sku.Product.Title,                                // 必填
			SpecNo:        sku.Sn,                                           // 必填
			Num:           num,                                              // 必填
			Price:         price,                                            // 必填
			Status:        30,                                               // 必填，与主订单状态一致
			RefundStatus:  0,                                                // 必填，无退款
			AdjustAmount:  0,                                                // 必填
			Discount:      0,                                                // 必填
			ShareDiscount: 0,                                                // 必填
		}
		orderList = append(orderList, orderGoods)
	}
	wdtOrder.OrderList = orderList

	// 计算已付金额 (必填)
	// paid = Σ(price * num + adjust_amount - discount - share_discount) + post_amount + other_amount
	wdtOrder.Paid = utils.Decimal(totalAmount + wdtOrder.PostAmount + wdtOrder.OtherAmount)

	// 推送订单到旺店通
	wdtClient := &order.WdtOrder{}
	wdtClient.SetBase(wdt.WdtData.BaseInfo.Appkey, wdt.WdtData.BaseInfo.Appsecret, wdt.WdtData.BaseInfo.Sid, wdt.WdtData.BaseInfo.ShopId)
	err = wdtClient.PushOrder(wdtOrder)
	if err != nil {
		return err, nil
	}

	info = &stbz.APIResult{
		Code: 1,
		Msg:  "success",
	}
	return
}

func (jushuitan *Wdt) ExpressQuery(request request.RequestExpress) (err error, info interface{}) {
	return
}

func (jushuitan *Wdt) AfterSalesBeforeCheck(request request.RequestAfterSale) (err error, info interface{}) {
	return
}

func (jushuitan *Wdt) AfterSalesPicture(request request.RequestAfterSalePicture) (err error, info interface{}) {
	return
}

func (jushuitan *Wdt) AfterSale(request request.AfterSale) (err error, info interface{}) {
	return
}

func (jushuitan *Wdt) OrderDelivery(OrderData callback2.OrderCallBack) (err error) {
	return
}
func (jushuitan *Wdt) SyncOrderExpNo(unionIdList []string) (err error, data []response.SyncOrderExpNoResponse) {
	return
}
