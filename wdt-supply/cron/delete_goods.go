package cron

import (
	"go.uber.org/zap"
	common2 "public-supply/common"
	"public-supply/model"
	"wdt-supply/goods"
	model2 "wdt-supply/model"
	"wdt-supply/service"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func PushCheckWdtDeleteGoodsHandle() {
	task := cron.Task{
		Key:  "CheckWdtDeleteGoodsHandle",
		Name: "检查wdt商品",
		Spec: "15 10 */1 * * *",
		//Spec: "12 21 3 * * *",
		Handle: func(task cron.Task) {
			CheckDeleteGoodsHandle()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func CheckDeleteGoodsHandle() {
	var gatherSuppliers []model.GatherSupply
	err := source.DB().Where("category_id = ?", common2.SUPPLY_WDT).Find(&gatherSuppliers).Error
	if err != nil {
		return
	}
	for _, gatherSupply := range gatherSuppliers {
		var setting model2.WdtSetting
		err, setting = service.GetWdtSetting(gatherSupply.ID)
		if err != nil {
			continue
		}
		var wdt = goods.WdtGoods{}
		if err := wdt.SetBase(setting.Appkey, setting.Appsecret, setting.Sid).CheckDeletedGoods(); err != nil {
			log.Log().Info("增量获取数据失败: %v", zap.Any("err", err))
			continue
		}
	}
	return
}
