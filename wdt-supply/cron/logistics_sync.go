package cron

import (
	"public-supply/common"
	"time"
	"wdt-supply/component/order"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"

	"go.uber.org/zap"
)

func PushLogisticsTaskHandle() {
	task := cron.Task{
		Key:  "LogisticsTaskHandle",
		Name: "wdt发货",
		//Spec: "15 10 */1 * * *",
		Spec: "1 */1 * * * *",
		Handle: func(task cron.Task) {
			LogisticsSyncTask()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

// LogisticsSyncConfig 物流同步配置
type LogisticsSyncConfig struct {
	Enabled   bool `json:"enabled"`    // 是否启用
	Interval  int  `json:"interval"`   // 同步间隔(分钟)
	BatchSize int  `json:"batch_size"` // 批次大小
}

// LogisticsSyncTask 物流同步定时任务
func LogisticsSyncTask() {
	log.Log().Info("开始执行WDT物流同步定时任务")

	// 获取配置信息
	config := getLogisticsSyncConfig()
	if !config.Enabled {
		log.Log().Info("WDT物流同步已禁用")
		return
	}

	// 获取所有WDT配置
	wdtids := getAllWdtConfigs()

	totalSuccess := 0
	totalFailure := 0
	totalProcessed := 0

	for _, id := range wdtids {
		// 创建WDT实例
		wdt := &order.Wdt{}
		err := wdt.InitSetting(id)
		if err != nil {
			continue
		}
		// 执行同步
		result, err := wdt.SyncLogistics(config.BatchSize)
		if err != nil {
			log.Log().Error("WDT物流同步失败",
				zap.Uint("id", id),
				zap.Error(err),
			)
			continue
		}

		totalSuccess += result.SuccessCount
		totalFailure += result.FailureCount
		totalProcessed += result.TotalProcessed

		log.Log().Info("WDT物流同步完成",
			zap.Uint("id", id),
			zap.Int("total_processed", result.TotalProcessed),
			zap.Int("success_count", result.SuccessCount),
			zap.Int("failure_count", result.FailureCount),
		)
	}

	log.Log().Info("WDT物流同步定时任务执行完成",
		zap.Int("total_configs", len(wdtids)),
		zap.Int("total_processed", totalProcessed),
		zap.Int("total_success", totalSuccess),
		zap.Int("total_failure", totalFailure),
	)
}

// StartLogisticsSyncScheduler 启动物流同步调度器
func StartLogisticsSyncScheduler() {
	log.Log().Info("启动WDT物流同步调度器")

	//config := getLogisticsSyncConfig()
	//if !config.Enabled {
	//	log.Log().Info("WDT物流同步调度器已禁用")
	//	return
	//}

	// 创建定时器
	//interval := time.Duration(config.Interval) * time.Minute
	//ticker := time.NewTicker(interval)
	//defer ticker.Stop()

	// 立即执行一次
	go LogisticsSyncTask()

	//// 定时执行
	//for {
	//	select {
	//	case <-ticker.C:
	//		go LogisticsSyncTask()
	//	}
	//}
}

// WdtConfigInfo WDT配置信息
type WdtConfigInfo struct {
	Sid       string `json:"sid"`
	Appkey    string `json:"appkey"`
	Appsecret string `json:"appsecret"`
	ShopId    string `json:"shop_id"`
}

// getLogisticsSyncConfig 获取物流同步配置
func getLogisticsSyncConfig() LogisticsSyncConfig {
	// 这里应该从数据库或配置文件中获取配置
	// 暂时返回默认配置
	return LogisticsSyncConfig{
		Enabled:   true, // 默认启用
		Interval:  5,    // 5分钟同步一次
		BatchSize: 100,  // 每次同步100条
	}
}

// getAllWdtConfigs 获取所有WDT配置
func getAllWdtConfigs() []uint {

	var ids []uint
	err := source.DB().Where("category_id = ?", common.SUPPLY_WDT).Pluck("id", &ids).Error
	if err != nil {
		return []uint{}
	}
	return ids
}

// SyncLogisticsOnDemand 按需同步物流信息
func SyncLogisticsOnDemand(sid, appkey, appsecret, shopId string, limit int) (*order.WdtLogisticsSyncResult, error) {
	log.Log().Info("执行按需WDT物流同步",
		zap.String("sid", sid),
		zap.String("shop_id", shopId),
		zap.Int("limit", limit),
	)

	// 创建WDT实例
	wdt := &order.Wdt{}
	wdt.WdtData.BaseInfo.Sid = sid
	wdt.WdtData.BaseInfo.Appkey = appkey
	wdt.WdtData.BaseInfo.Appsecret = appsecret
	wdt.WdtData.BaseInfo.ShopId = shopId

	// 执行同步
	result, err := wdt.SyncLogistics(limit, shopId)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// GetLogisticsSyncStatus 获取物流同步状态
func GetLogisticsSyncStatus() map[string]interface{} {
	config := getLogisticsSyncConfig()
	wdtConfigs := getAllWdtConfigs()

	return map[string]interface{}{
		"enabled":        config.Enabled,
		"interval":       config.Interval,
		"batch_size":     config.BatchSize,
		"configs_count":  len(wdtConfigs),
		"last_sync_time": time.Now().Format("2006-01-02 15:04:05"),
		"status":         "running",
	}
}

// BatchSyncAllLogistics 批量同步所有物流信息
func BatchSyncAllLogistics() map[string]interface{} {
	log.Log().Info("开始批量同步所有WDT物流信息")

	wdtConfigs := getAllWdtConfigs()
	results := make(map[string]interface{})

	totalSuccess := 0
	totalFailure := 0
	totalProcessed := 0

	for _, wdtConfig := range wdtConfigs {
		// 创建WDT实例
		wdt := &order.Wdt{}
		wdt.WdtData.BaseInfo.Sid = wdtConfig.Sid
		wdt.WdtData.BaseInfo.Appkey = wdtConfig.Appkey
		wdt.WdtData.BaseInfo.Appsecret = wdtConfig.Appsecret
		wdt.WdtData.BaseInfo.ShopId = wdtConfig.ShopId

		// 执行同步
		result, err := wdt.SyncLogistics(100, wdtConfig.ShopId)
		if err != nil {
			results[wdtConfig.Sid] = map[string]interface{}{
				"status": "error",
				"error":  err.Error(),
			}
			continue
		}

		results[wdtConfig.Sid] = map[string]interface{}{
			"status":          "success",
			"total_queried":   result.TotalQueried,
			"total_processed": result.TotalProcessed,
			"success_count":   result.SuccessCount,
			"failure_count":   result.FailureCount,
		}

		totalSuccess += result.SuccessCount
		totalFailure += result.FailureCount
		totalProcessed += result.TotalProcessed
	}

	summary := map[string]interface{}{
		"total_configs":   len(wdtConfigs),
		"total_processed": totalProcessed,
		"total_success":   totalSuccess,
		"total_failure":   totalFailure,
		"details":         results,
		"sync_time":       time.Now().Format("2006-01-02 15:04:05"),
	}

	log.Log().Info("批量同步WDT物流信息完成",
		zap.Int("total_processed", totalProcessed),
		zap.Int("total_success", totalSuccess),
		zap.Int("total_failure", totalFailure),
	)

	return summary
}

// UpdateLogisticsSyncConfig 更新物流同步配置
func UpdateLogisticsSyncConfig(enabled bool, interval, batchSize int) error {
	// 这里应该更新数据库中的配置
	log.Log().Info("更新WDT物流同步配置",
		zap.Bool("enabled", enabled),
		zap.Int("interval", interval),
		zap.Int("batch_size", batchSize),
	)

	// 暂时只记录日志，实际应用中需要更新数据库
	return nil
}
