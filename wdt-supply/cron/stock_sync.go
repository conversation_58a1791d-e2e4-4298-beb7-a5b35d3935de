package cron

import (
	common2 "public-supply/common"
	"public-supply/model"
	"wdt-supply/goods"
	model2 "wdt-supply/model"
	"wdt-supply/service"
	"yz-go/cron"
	"yz-go/source"
)

func PushCheckWdtStockSyncHandle() {
	task := cron.Task{
		Key:  "CheckWdtStockSyncHandle",
		Name: "检查wdt商品",
		Spec: "15 */1 * * * *",
		//Spec: "12 21 3 * * *",
		Handle: func(task cron.Task) {
			CheckWdtStockSyncHandle()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func CheckWdtStockSyncHandle() {
	var gatherSuppliers []model.GatherSupply
	err := source.DB().Where("category_id = ?", common2.SUPPLY_WDT).Find(&gatherSuppliers).Error
	if err != nil {
		return
	}
	for _, gatherSupply := range gatherSuppliers {
		var setting model2.WdtSetting
		err, setting = service.GetWdtSetting(gatherSupply.ID)
		if err != nil {
			continue
		}
		var wdt = goods.WdtGoods{}
		wdt.SetBase(setting.Appkey, setting.Appsecret, setting.Sid).SyncStock()
	}
	return
}
