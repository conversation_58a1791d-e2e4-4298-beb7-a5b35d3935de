# WdtGoodsDetail 一对多关联最佳实践

## 为什么不需要 GORM 钩子方法？

### GORM 原生关联功能已经足够强大

1. **自动关联处理**：GORM 可以通过 `Preload`、`Joins`、`Association` 等方法自动处理关联关系
2. **事务安全**：使用事务可以确保主表和关联表的数据一致性
3. **灵活控制**：可以根据业务需求选择何时加载关联数据，避免不必要的查询

### 钩子方法的问题

1. **隐式行为**：钩子方法会在每次 CRUD 操作时自动执行，可能导致意外的数据库操作
2. **性能影响**：即使不需要处理关联数据，钩子方法也会执行
3. **调试困难**：隐式的数据库操作增加了调试的复杂性
4. **灵活性差**：无法根据具体场景选择是否处理关联数据

## 推荐的关联处理方式

### 1. 模型定义

```go
type WdtGoodsDetail struct {
    source.Model
    
    GoodsId               int                     `json:"goods_id" gorm:"column:goods_id;uniqueIndex"`
    // ... 其他字段
    
    // 一对多关联：一个商品有多个规格产品
    GoodsSpecProducts     []WdtGoodsSpecProduct   `json:"goods_spec_products,omitempty" gorm:"foreignKey:GoodsId;references:GoodsId"`
}

type WdtGoodsSpecProduct struct {
    source.Model
    GoodsId            int         `json:"goods_id" gorm:"column:goods_id;index;not null"`
    // ... 其他字段
    
    // 反向关联：规格产品属于某个商品
    GoodsDetail        *WdtGoodsDetail `json:"goods_detail,omitempty" gorm:"foreignKey:GoodsId;references:GoodsId"`
}
```

### 2. 业务方法

#### 创建商品及规格产品
```go
func (w *WdtGoodsDetail) CreateWithSpecProducts() error {
    return source.DB().Transaction(func(tx *gorm.DB) error {
        // 1. 创建商品
        if err := tx.Create(w).Error; err != nil {
            return err
        }
        
        // 2. 创建规格产品
        if len(w.GoodsSpecProducts) > 0 {
            for i := range w.GoodsSpecProducts {
                w.GoodsSpecProducts[i].GoodsId = w.GoodsId
            }
            if err := tx.Create(&w.GoodsSpecProducts).Error; err != nil {
                return err
            }
        }
        
        return nil
    })
}
```

#### 更新商品及规格产品
```go
func (w *WdtGoodsDetail) UpdateWithSpecProducts() error {
    return source.DB().Transaction(func(tx *gorm.DB) error {
        // 1. 更新商品
        if err := tx.Model(w).Updates(w).Error; err != nil {
            return err
        }
        
        // 2. 替换规格产品
        if len(w.GoodsSpecProducts) > 0 {
            // 删除现有规格产品
            tx.Where("goods_id = ?", w.GoodsId).Delete(&WdtGoodsSpecProduct{})
            
            // 创建新规格产品
            for i := range w.GoodsSpecProducts {
                w.GoodsSpecProducts[i].GoodsId = w.GoodsId
                w.GoodsSpecProducts[i].ID = 0
            }
            tx.Create(&w.GoodsSpecProducts)
        }
        
        return nil
    })
}
```

### 3. 查询方法

#### 基本查询
```go
// 查询商品及其所有规格产品
var goods WdtGoodsDetail
source.DB().Preload("GoodsSpecProducts").Where("goods_id = ?", 12345).First(&goods)

// 条件查询：只查询在售规格产品
source.DB().Preload("GoodsSpecProducts", "is_sale = ?", true).First(&goods)
```

#### 性能优化查询
```go
// 只查询需要的字段
source.DB().Select("id, goods_id, goods_name").
    Preload("GoodsSpecProducts", func(db *gorm.DB) *gorm.DB {
        return db.Select("id, goods_id, prd_sn, prd_price")
    }).Find(&goods)

// 使用 Joins 进行联表查询
source.DB().Table("wdt_goods_details").
    Select("wdt_goods_details.*, COUNT(wdt_goods_spec_products.id) as spec_count").
    Joins("LEFT JOIN wdt_goods_spec_products ON wdt_goods_details.goods_id = wdt_goods_spec_products.goods_id").
    Group("wdt_goods_details.id").
    Find(&results)
```

## 在 goods.go 中的最佳实践

### 1. 单个商品处理（fetchGoodsDetail 场景）

```go
func (wdt *WdtGoods) SaveSingleGoods(goodsDetail *model.WdtGoodsDetail) error {
    var existing model.WdtGoodsDetail
    err := source.DB().Where("goods_id = ?", goodsDetail.GoodsId).First(&existing).Error
    
    if err == gorm.ErrRecordNotFound {
        // 创建新商品
        return goodsDetail.CreateWithSpecProducts()
    } else if err != nil {
        return err
    }
    
    // 检查是否需要更新
    if needsUpdate(goodsDetail, &existing) {
        goodsDetail.ID = existing.ID
        return goodsDetail.UpdateWithSpecProducts()
    }
    
    return nil
}
```

### 2. 批量商品处理（batchSaveGoodsData 场景）

```go
func (wdt *WdtGoods) BatchSaveGoods(goodsList []*model.WdtGoodsDetail) error {
    return source.DB().Transaction(func(tx *gorm.DB) error {
        for _, goods := range goodsList {
            if err := wdt.processGoodsWithSpecs(tx, goods); err != nil {
                return err
            }
        }
        return nil
    })
}
```

## 优势总结

### 1. **明确的控制流**
- 开发者明确知道何时处理关联数据
- 可以根据业务需求选择处理方式

### 2. **更好的性能**
- 避免不必要的关联数据查询
- 可以使用 Select 指定需要的字段
- 支持条件预加载

### 3. **事务安全**
- 所有相关操作在同一事务中执行
- 确保数据一致性

### 4. **易于测试和调试**
- 显式的方法调用，便于单元测试
- 清晰的错误处理和日志记录

### 5. **灵活性**
- 可以根据不同场景选择不同的处理方式
- 支持部分更新、条件查询等复杂操作

## 使用建议

1. **创建时**：使用 `CreateWithSpecProducts()` 方法
2. **更新时**：使用 `UpdateWithSpecProducts()` 方法  
3. **查询时**：根据需要选择是否 Preload 关联数据
4. **批量操作**：使用事务确保数据一致性
5. **性能优化**：使用 Select 指定字段，使用 Joins 代替 Preload

这种方式既保持了代码的简洁性，又提供了足够的灵活性和控制力，是处理一对多关联关系的最佳实践。
