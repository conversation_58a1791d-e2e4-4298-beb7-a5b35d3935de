# 修复 Duplicate Primary Key 错误

## 问题描述

在更新商品规格产品时出现错误：
```
Error 1062 (23000): Duplicate entry '1' for key 'wdt_goods_spec_products.PRIMARY'
```

## 问题原因

当更新商品时，如果商品的 `GoodsSpecProducts` 字段包含已有的规格产品数据（带有 ID），在执行 "删除现有规格产品 -> 创建新规格产品" 的操作时，GORM 会尝试使用原有的 ID 创建新记录，导致主键冲突。

### 问题场景

1. 从数据库查询商品时，关联的规格产品会带有 ID
2. 更新商品时，这些规格产品的 ID 没有被重置
3. 执行 `tx.Create(&goods.GoodsSpecProducts)` 时，GORM 尝试使用现有 ID 创建记录
4. 导致主键重复错误

## 解决方案

### 1. 创建新的规格产品切片

不直接修改原始的 `goods.GoodsSpecProducts`，而是创建新的切片：

```go
// 创建新的规格产品切片，避免修改原始数据
var newSpecProducts []model.WdtGoodsSpecProduct
for _, spec := range goods.GoodsSpecProducts {
    newSpec := spec
    newSpec.ID = 0 // 重置ID，确保创建新记录
    newSpec.GoodsId = goods.GoodsId
    newSpec.CreatedAt = nil // 重置创建时间
    newSpec.UpdatedAt = nil // 重置更新时间
    newSpecProducts = append(newSpecProducts, newSpec)
}

// 批量创建新的规格产品
if err := tx.Create(&newSpecProducts).Error; err != nil {
    return fmt.Errorf("创建规格产品失败: %v", err)
}
```

### 2. 重置关键字段

- **ID**: 设置为 0，让 GORM 自动生成新的主键
- **GoodsId**: 确保外键正确
- **CreatedAt/UpdatedAt**: 重置为 nil，让 GORM 自动设置时间

### 3. 修复的文件

#### wdt-supply/goods/goods.go
- `updateGoodsWithSpecs` 方法

#### wdt-supply/model/wdt.go  
- `UpdateWithSpecProducts` 方法

## 修复后的代码逻辑

### 更新流程

1. **更新商品主体信息**
   ```go
   if err := tx.Model(goods).Updates(goods).Error; err != nil {
       return fmt.Errorf("更新商品失败: %v", err)
   }
   ```

2. **删除现有规格产品**
   ```go
   if err := tx.Where("goods_id = ?", goods.GoodsId).Delete(&model.WdtGoodsSpecProduct{}).Error; err != nil {
       return fmt.Errorf("删除现有规格产品失败: %v", err)
   }
   ```

3. **创建新的规格产品**
   ```go
   var newSpecProducts []model.WdtGoodsSpecProduct
   for _, spec := range goods.GoodsSpecProducts {
       newSpec := spec
       newSpec.ID = 0
       newSpec.GoodsId = goods.GoodsId
       newSpec.CreatedAt = nil
       newSpec.UpdatedAt = nil
       newSpecProducts = append(newSpecProducts, newSpec)
   }
   
   if err := tx.Create(&newSpecProducts).Error; err != nil {
       return fmt.Errorf("创建规格产品失败: %v", err)
   }
   ```

## 预防措施

### 1. 数据隔离
- 不直接修改从数据库查询出来的数据结构
- 创建新的数据结构用于插入操作

### 2. 字段重置
- 始终重置 ID 字段
- 重置时间戳字段，让 GORM 自动处理

### 3. 事务保护
- 所有相关操作在同一事务中执行
- 确保数据一致性

## 测试验证

### 测试场景

1. **创建新商品**：验证规格产品正确创建
2. **更新现有商品**：验证规格产品正确替换
3. **批量操作**：验证大量数据处理的稳定性

### 验证方法

```go
// 1. 创建商品
goods := &model.WdtGoodsDetail{
    GoodsId: 12345,
    GoodsSpecProducts: []model.WdtGoodsSpecProduct{...},
}
err := goods.CreateWithSpecProducts()

// 2. 更新商品
goods.GoodsName = "更新后的名称"
goods.GoodsSpecProducts = []model.WdtGoodsSpecProduct{...} // 新的规格产品
err = goods.UpdateWithSpecProducts()

// 3. 验证数据
var updated model.WdtGoodsDetail
source.DB().Preload("GoodsSpecProducts").Where("goods_id = ?", 12345).First(&updated)
```

## 总结

通过创建新的数据结构并重置关键字段，成功解决了主键重复的问题。这种方法：

1. **安全可靠**：避免了数据污染
2. **性能良好**：批量操作效率高
3. **易于维护**：代码逻辑清晰
4. **事务安全**：保证数据一致性

现在可以安全地进行商品及规格产品的创建和更新操作，不会再出现主键重复错误。
