package example

import (
	"fmt"
	"wdt-supply/model"

	"go.uber.org/zap"
	"yz-go/component/log"
	"yz-go/source"
)

// AssociationUsageExample 展示如何使用一对多关联关系
func AssociationUsageExample() {
	fmt.Println("=== GORM 一对多关联关系使用示例 ===")

	// 1. 创建商品详情
	goodsDetail := model.WdtGoodsDetail{
		GoodsId:   12345,
		GoodsName: "测试商品",
		GoodsSn:   "TEST001",
		GoodsPrice: 99.99,
		Description: "这是一个测试商品",
	}

	// 保存商品详情
	if err := source.DB().Create(&goodsDetail).Error; err != nil {
		log.Log().Error("创建商品详情失败", zap.Error(err))
		return
	}

	fmt.Printf("创建商品详情成功，ID: %d\n", goodsDetail.ID)

	// 2. 创建规格产品数据
	specProducts := []model.WdtGoodsSpecProduct{
		{
			GoodsId:       goodsDetail.GoodsId,
			PrdSn:         "TEST001-RED-L",
			PrdDesc:       "红色大码",
			PrdPrice:      99.99,
			SpecId:        "spec_001",
			IsSale:        true,
			PrdNumber:     100,
			ProductImages: model.StringSlice{"image1.jpg", "image2.jpg"},
		},
		{
			GoodsId:       goodsDetail.GoodsId,
			PrdSn:         "TEST001-BLUE-M",
			PrdDesc:       "蓝色中码",
			PrdPrice:      89.99,
			SpecId:        "spec_002",
			IsSale:        true,
			PrdNumber:     50,
			ProductImages: model.StringSlice{"image3.jpg", "image4.jpg"},
		},
		{
			GoodsId:       goodsDetail.GoodsId,
			PrdSn:         "TEST001-GREEN-S",
			PrdDesc:       "绿色小码",
			PrdPrice:      79.99,
			SpecId:        "spec_003",
			IsSale:        false,
			PrdNumber:     30,
			ProductImages: model.StringSlice{"image5.jpg"},
		},
	}

	// 批量创建规格产品
	if err := source.DB().Create(&specProducts).Error; err != nil {
		log.Log().Error("批量创建规格产品失败", zap.Error(err))
		return
	}

	fmt.Printf("创建 %d 个规格产品成功\n", len(specProducts))

	// 3. 使用 Preload 查询商品及其关联的规格产品
	var goodsWithSpecs model.WdtGoodsDetail
	if err := source.DB().Preload("SpecProducts").Where("goods_id = ?", goodsDetail.GoodsId).First(&goodsWithSpecs).Error; err != nil {
		log.Log().Error("查询商品及规格产品失败", zap.Error(err))
		return
	}

	fmt.Printf("\n=== 商品信息 ===\n")
	fmt.Printf("商品ID: %d\n", goodsWithSpecs.GoodsId)
	fmt.Printf("商品名称: %s\n", goodsWithSpecs.GoodsName)
	fmt.Printf("商品编号: %s\n", goodsWithSpecs.GoodsSn)
	fmt.Printf("规格产品数量: %d\n", len(goodsWithSpecs.SpecProducts))

	fmt.Printf("\n=== 规格产品列表 ===\n")
	for i, spec := range goodsWithSpecs.SpecProducts {
		fmt.Printf("%d. %s - %s (价格: %.2f, 库存: %.0f, 在售: %t)\n",
			i+1, spec.PrdSn, spec.PrdDesc, spec.PrdPrice, spec.PrdNumber, spec.IsSale)
	}

	// 4. 条件查询：只查询在售的规格产品
	var goodsWithOnSaleSpecs model.WdtGoodsDetail
	if err := source.DB().Preload("SpecProducts", "is_sale = ?", true).
		Where("goods_id = ?", goodsDetail.GoodsId).First(&goodsWithOnSaleSpecs).Error; err != nil {
		log.Log().Error("查询在售规格产品失败", zap.Error(err))
		return
	}

	fmt.Printf("\n=== 在售规格产品 ===\n")
	for i, spec := range goodsWithOnSaleSpecs.SpecProducts {
		fmt.Printf("%d. %s - %s (价格: %.2f)\n",
			i+1, spec.PrdSn, spec.PrdDesc, spec.PrdPrice)
	}

	// 5. 反向查询：从规格产品查询商品详情
	var specWithGoods model.WdtGoodsSpecProduct
	if err := source.DB().Preload("GoodsDetail").Where("spec_id = ?", "spec_001").First(&specWithGoods).Error; err != nil {
		log.Log().Error("反向查询失败", zap.Error(err))
		return
	}

	fmt.Printf("\n=== 反向查询结果 ===\n")
	fmt.Printf("规格产品: %s - %s\n", specWithGoods.PrdSn, specWithGoods.PrdDesc)
	if specWithGoods.GoodsDetail != nil {
		fmt.Printf("所属商品: %s (%s)\n", specWithGoods.GoodsDetail.GoodsName, specWithGoods.GoodsDetail.GoodsSn)
	}

	// 6. 聚合查询：统计商品的规格产品信息
	type SpecSummary struct {
		TotalSpecs   int64   `json:"total_specs"`
		OnSaleSpecs  int64   `json:"on_sale_specs"`
		MinPrice     float64 `json:"min_price"`
		MaxPrice     float64 `json:"max_price"`
		AvgPrice     float64 `json:"avg_price"`
		TotalStock   float64 `json:"total_stock"`
	}

	var summary SpecSummary
	if err := source.DB().Model(&model.WdtGoodsSpecProduct{}).
		Where("goods_id = ?", goodsDetail.GoodsId).
		Select(`
			COUNT(*) as total_specs,
			SUM(CASE WHEN is_sale = true THEN 1 ELSE 0 END) as on_sale_specs,
			MIN(prd_price) as min_price,
			MAX(prd_price) as max_price,
			AVG(prd_price) as avg_price,
			SUM(prd_number) as total_stock
		`).
		Scan(&summary).Error; err != nil {
		log.Log().Error("聚合查询失败", zap.Error(err))
		return
	}

	fmt.Printf("\n=== 商品规格统计 ===\n")
	fmt.Printf("总规格数: %d\n", summary.TotalSpecs)
	fmt.Printf("在售规格数: %d\n", summary.OnSaleSpecs)
	fmt.Printf("价格范围: %.2f - %.2f\n", summary.MinPrice, summary.MaxPrice)
	fmt.Printf("平均价格: %.2f\n", summary.AvgPrice)
	fmt.Printf("总库存: %.0f\n", summary.TotalStock)

	fmt.Println("\n=== 示例完成 ===")
}

// BatchAssociationExample 批量关联操作示例
func BatchAssociationExample() {
	fmt.Println("\n=== 批量关联操作示例 ===")

	// 1. 批量查询多个商品及其规格产品
	var goodsList []model.WdtGoodsDetail
	if err := source.DB().Preload("SpecProducts").Limit(5).Find(&goodsList).Error; err != nil {
		log.Log().Error("批量查询失败", zap.Error(err))
		return
	}

	fmt.Printf("查询到 %d 个商品:\n", len(goodsList))
	for _, goods := range goodsList {
		fmt.Printf("- %s: %d 个规格\n", goods.GoodsName, len(goods.SpecProducts))
	}

	// 2. 使用 Association 方法操作关联
	if len(goodsList) > 0 {
		goods := &goodsList[0]
		
		// 获取关联的规格产品数量
		count := source.DB().Model(goods).Association("SpecProducts").Count()
		fmt.Printf("\n商品 %s 有 %d 个规格产品\n", goods.GoodsName, count)

		// 查找特定的规格产品
		var onSaleSpecs []model.WdtGoodsSpecProduct
		if err := source.DB().Model(goods).Association("SpecProducts").Find(&onSaleSpecs, "is_sale = ?", true); err != nil {
			log.Log().Error("查找关联数据失败", zap.Error(err))
		} else {
			fmt.Printf("其中 %d 个在售\n", len(onSaleSpecs))
		}
	}

	// 3. 联表查询：查询有规格产品的商品
	var goodsWithSpecCount []struct {
		model.WdtGoodsDetail
		SpecCount int `json:"spec_count"`
	}

	if err := source.DB().Table("wdt_goods_details").
		Select("wdt_goods_details.*, COUNT(wdt_goods_spec_products.id) as spec_count").
		Joins("LEFT JOIN wdt_goods_spec_products ON wdt_goods_details.goods_id = wdt_goods_spec_products.goods_id").
		Group("wdt_goods_details.id").
		Having("spec_count > 0").
		Limit(5).
		Find(&goodsWithSpecCount).Error; err != nil {
		log.Log().Error("联表查询失败", zap.Error(err))
		return
	}

	fmt.Printf("\n=== 有规格产品的商品 ===\n")
	for _, goods := range goodsWithSpecCount {
		fmt.Printf("- %s: %d 个规格\n", goods.GoodsName, goods.SpecCount)
	}
}

// AssociationUpdateExample 关联更新示例
func AssociationUpdateExample() {
	fmt.Println("\n=== 关联更新示例 ===")

	// 查找一个商品
	var goods model.WdtGoodsDetail
	if err := source.DB().Preload("SpecProducts").First(&goods).Error; err != nil {
		log.Log().Error("查询商品失败", zap.Error(err))
		return
	}

	fmt.Printf("商品: %s, 当前规格数: %d\n", goods.GoodsName, len(goods.SpecProducts))

	// 1. 添加新的规格产品
	newSpec := model.WdtGoodsSpecProduct{
		GoodsId:   goods.GoodsId,
		PrdSn:     fmt.Sprintf("%s-NEW-%d", goods.GoodsSn, len(goods.SpecProducts)+1),
		PrdDesc:   "新增规格",
		PrdPrice:  199.99,
		SpecId:    fmt.Sprintf("spec_new_%d", len(goods.SpecProducts)+1),
		IsSale:    true,
		PrdNumber: 20,
	}

	if err := source.DB().Model(&goods).Association("SpecProducts").Append(&newSpec); err != nil {
		log.Log().Error("添加关联失败", zap.Error(err))
		return
	}

	fmt.Printf("成功添加新规格: %s\n", newSpec.PrdSn)

	// 2. 批量更新规格产品价格
	if err := source.DB().Model(&model.WdtGoodsSpecProduct{}).
		Where("goods_id = ?", goods.GoodsId).
		Update("prd_price", source.DB().Raw("prd_price * 1.1")).Error; err != nil {
		log.Log().Error("批量更新价格失败", zap.Error(err))
		return
	}

	fmt.Println("成功将所有规格产品价格上调10%")

	// 3. 删除特定规格产品
	if len(goods.SpecProducts) > 0 {
		specToDelete := goods.SpecProducts[0]
		if err := source.DB().Model(&goods).Association("SpecProducts").Delete(&specToDelete); err != nil {
			log.Log().Error("删除关联失败", zap.Error(err))
		} else {
			fmt.Printf("成功删除规格: %s\n", specToDelete.PrdSn)
		}
	}

	// 4. 清空所有关联（谨慎使用）
	// source.DB().Model(&goods).Association("SpecProducts").Clear()

	fmt.Println("关联更新示例完成")
}

// RunAllAssociationExamples 运行所有关联示例
func RunAllAssociationExamples() {
	AssociationUsageExample()
	BatchAssociationExample()
	AssociationUpdateExample()
}
