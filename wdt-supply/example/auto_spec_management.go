package example

import (
	"fmt"
	"wdt-supply/model"

	"go.uber.org/zap"
	"gorm.io/gorm"
	"yz-go/component/log"
	"yz-go/source"
)

// AutoSpecManagementExample 展示自动规格产品管理
func AutoSpecManagementExample() {
	fmt.Println("=== 自动规格产品管理示例 ===")

	// 1. 创建商品时自动创建规格产品
	fmt.Println("\n1. 创建商品时自动创建规格产品")

	goodsDetail := model.WdtGoodsDetail{
		GoodsId:     12345,
		GoodsName:   "自动管理测试商品",
		GoodsSn:     "AUTO_TEST_001",
		GoodsPrice:  99.99,
		Description: "这是一个测试自动规格管理的商品",

		// 同时定义规格产品，会在创建商品后自动创建
		GoodsSpecProducts: []model.WdtGoodsSpecProduct{
			{
				PrdSn:         "AUTO_TEST_001-RED-L",
				PrdDesc:       "红色大码",
				PrdPrice:      99.99,
				SpecId:        "auto_spec_001",
				IsSale:        true,
				PrdNumber:     100,
				ProductImages: model.StringSlice{"red_large_1.jpg", "red_large_2.jpg"},
			},
			{
				PrdSn:         "AUTO_TEST_001-BLUE-M",
				PrdDesc:       "蓝色中码",
				PrdPrice:      89.99,
				SpecId:        "auto_spec_002",
				IsSale:        true,
				PrdNumber:     50,
				ProductImages: model.StringSlice{"blue_medium_1.jpg"},
			},
			{
				PrdSn:         "AUTO_TEST_001-GREEN-S",
				PrdDesc:       "绿色小码",
				PrdPrice:      79.99,
				SpecId:        "auto_spec_003",
				IsSale:        false,
				PrdNumber:     30,
				ProductImages: model.StringSlice{"green_small_1.jpg", "green_small_2.jpg"},
			},
		},
	}

	// 创建商品（会自动创建规格产品）
	if err := source.DB().Create(&goodsDetail).Error; err != nil {
		log.Log().Error("创建商品失败", zap.Error(err))
		return
	}

	fmt.Printf("✓ 成功创建商品 ID: %d\n", goodsDetail.ID)
	fmt.Printf("✓ 自动创建了 %d 个规格产品\n", len(goodsDetail.GoodsSpecProducts))

	// 验证规格产品是否创建成功
	var createdSpecs []model.WdtGoodsSpecProduct
	if err := source.DB().Where("goods_id = ?", goodsDetail.GoodsId).Find(&createdSpecs).Error; err != nil {
		log.Log().Error("查询创建的规格产品失败", zap.Error(err))
		return
	}

	fmt.Printf("✓ 数据库中实际创建了 %d 个规格产品\n", len(createdSpecs))
	for _, spec := range createdSpecs {
		fmt.Printf("  - %s: %s (ID: %d)\n", spec.PrdSn, spec.PrdDesc, spec.ID)
	}

	// 2. 更新商品时自动更新规格产品
	fmt.Println("\n2. 更新商品时自动更新规格产品")

	// 修改商品信息和规格产品
	goodsDetail.GoodsName = "自动管理测试商品（已更新）"
	goodsDetail.GoodsPrice = 109.99

	// 更新规格产品：修改现有的，添加新的，删除一些
	goodsDetail.GoodsSpecProducts = []model.WdtGoodsSpecProduct{
		{
			PrdSn:         "AUTO_TEST_001-RED-L-V2",
			PrdDesc:       "红色大码（升级版）",
			PrdPrice:      119.99, // 价格更新
			SpecId:        "auto_spec_001_v2",
			IsSale:        true,
			PrdNumber:     80, // 库存更新
			ProductImages: model.StringSlice{"red_large_v2_1.jpg", "red_large_v2_2.jpg"},
		},
		{
			PrdSn:         "AUTO_TEST_001-YELLOW-XL",
			PrdDesc:       "黄色超大码（新增）",
			PrdPrice:      129.99,
			SpecId:        "auto_spec_004",
			IsSale:        true,
			PrdNumber:     20,
			ProductImages: model.StringSlice{"yellow_xl_1.jpg"},
		},
		// 注意：蓝色中码和绿色小码被移除了
	}

	// 更新商品（会自动删除旧规格产品，创建新规格产品）
	if err := source.DB().Save(&goodsDetail).Error; err != nil {
		log.Log().Error("更新商品失败", zap.Error(err))
		return
	}

	fmt.Printf("✓ 成功更新商品\n")
	fmt.Printf("✓ 自动更新了规格产品\n")

	// 验证更新结果
	var updatedSpecs []model.WdtGoodsSpecProduct
	if err := source.DB().Where("goods_id = ?", goodsDetail.GoodsId).Find(&updatedSpecs).Error; err != nil {
		log.Log().Error("查询更新的规格产品失败", zap.Error(err))
		return
	}

	fmt.Printf("✓ 更新后有 %d 个规格产品\n", len(updatedSpecs))
	for _, spec := range updatedSpecs {
		fmt.Printf("  - %s: %s (价格: %.2f, ID: %d)\n", spec.PrdSn, spec.PrdDesc, spec.PrdPrice, spec.ID)
	}

	// 3. 删除商品时自动删除规格产品
	fmt.Println("\n3. 删除商品时自动删除规格产品")

	// 删除商品（会自动删除关联的规格产品）
	if err := source.DB().Delete(&goodsDetail).Error; err != nil {
		log.Log().Error("删除商品失败", zap.Error(err))
		return
	}

	fmt.Printf("✓ 成功删除商品\n")

	// 验证规格产品是否被删除
	var remainingSpecs []model.WdtGoodsSpecProduct
	if err := source.DB().Where("goods_id = ?", goodsDetail.GoodsId).Find(&remainingSpecs).Error; err != nil {
		log.Log().Error("查询剩余规格产品失败", zap.Error(err))
		return
	}

	fmt.Printf("✓ 自动删除了关联的规格产品，剩余 %d 个\n", len(remainingSpecs))

	fmt.Println("\n=== 自动规格产品管理示例完成 ===")
}

// BatchAutoManagementExample 批量自动管理示例
func BatchAutoManagementExample() {
	fmt.Println("\n=== 批量自动管理示例 ===")

	// 批量创建多个商品，每个都有规格产品
	goodsList := []model.WdtGoodsDetail{
		{
			GoodsId:     20001,
			GoodsName:   "批量测试商品1",
			GoodsSn:     "BATCH_001",
			GoodsPrice:  59.99,
			GoodsSpecProducts: []model.WdtGoodsSpecProduct{
				{
					PrdSn:     "BATCH_001-A",
					PrdDesc:   "规格A",
					PrdPrice:  59.99,
					SpecId:    "batch_spec_001",
					IsSale:    true,
					PrdNumber: 100,
				},
				{
					PrdSn:     "BATCH_001-B",
					PrdDesc:   "规格B",
					PrdPrice:  69.99,
					SpecId:    "batch_spec_002",
					IsSale:    true,
					PrdNumber: 50,
				},
			},
		},
		{
			GoodsId:     20002,
			GoodsName:   "批量测试商品2",
			GoodsSn:     "BATCH_002",
			GoodsPrice:  79.99,
			GoodsSpecProducts: []model.WdtGoodsSpecProduct{
				{
					PrdSn:     "BATCH_002-X",
					PrdDesc:   "规格X",
					PrdPrice:  79.99,
					SpecId:    "batch_spec_003",
					IsSale:    true,
					PrdNumber: 30,
				},
			},
		},
	}

	// 批量创建（每个商品的规格产品都会自动创建）
	if err := source.DB().Create(&goodsList).Error; err != nil {
		log.Log().Error("批量创建商品失败", zap.Error(err))
		return
	}

	fmt.Printf("✓ 成功批量创建 %d 个商品\n", len(goodsList))

	// 统计创建的规格产品总数
	var totalSpecs int64
	goodsIds := []int{20001, 20002}
	if err := source.DB().Model(&model.WdtGoodsSpecProduct{}).Where("goods_id IN ?", goodsIds).Count(&totalSpecs).Error; err != nil {
		log.Log().Error("统计规格产品失败", zap.Error(err))
		return
	}

	fmt.Printf("✓ 自动创建了总计 %d 个规格产品\n", totalSpecs)

	// 清理测试数据
	source.DB().Where("goods_id IN ?", goodsIds).Delete(&model.WdtGoodsDetail{})
	fmt.Printf("✓ 清理测试数据完成\n")
}

// TransactionExample 事务中的自动管理示例
func TransactionExample() {
	fmt.Println("\n=== 事务中的自动管理示例 ===")

	// 在事务中创建商品
	err := source.DB().Transaction(func(tx *gorm.DB) error {
		goodsDetail := model.WdtGoodsDetail{
			GoodsId:     30001,
			GoodsName:   "事务测试商品",
			GoodsSn:     "TX_TEST_001",
			GoodsPrice:  199.99,
			GoodsSpecProducts: []model.WdtGoodsSpecProduct{
				{
					PrdSn:     "TX_TEST_001-PREMIUM",
					PrdDesc:   "高级版",
					PrdPrice:  199.99,
					SpecId:    "tx_spec_001",
					IsSale:    true,
					PrdNumber: 10,
				},
			},
		}

		// 在事务中创建商品（规格产品也会在同一事务中创建）
		if err := tx.Create(&goodsDetail).Error; err != nil {
			return err
		}

		fmt.Printf("✓ 在事务中创建商品成功\n")

		// 模拟其他业务逻辑...
		// 如果这里出错，整个事务会回滚，包括商品和规格产品的创建

		return nil
	})

	if err != nil {
		log.Log().Error("事务执行失败", zap.Error(err))
		fmt.Printf("✗ 事务回滚，商品和规格产品都未创建\n")
		return
	}

	fmt.Printf("✓ 事务提交成功，商品和规格产品都已创建\n")

	// 验证数据
	var count int64
	source.DB().Model(&model.WdtGoodsSpecProduct{}).Where("goods_id = ?", 30001).Count(&count)
	fmt.Printf("✓ 验证：创建了 %d 个规格产品\n", count)

	// 清理
	source.DB().Where("goods_id = ?", 30001).Delete(&model.WdtGoodsDetail{})
	fmt.Printf("✓ 清理测试数据完成\n")
}

// RunAutoSpecManagementExamples 运行所有自动规格管理示例
func RunAutoSpecManagementExamples() {
	AutoSpecManagementExample()
	BatchAutoManagementExample()
	TransactionExample()
}
