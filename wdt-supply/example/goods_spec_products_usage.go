package example

import (
	"fmt"
	"wdt-supply/model"

	"go.uber.org/zap"
	"yz-go/component/log"
	"yz-go/source"
)

// ExampleUsage 展示如何使用新的商品规格产品表
func ExampleUsage() {
	// 1. 创建商品详情
	goodsDetail := model.WdtGoodsDetail{
		GoodsId:   12345,
		GoodsName: "测试商品",
		GoodsSn:   "TEST001",
		// ... 其他字段
	}

	// 保存商品详情
	if err := source.DB().Create(&goodsDetail).Error; err != nil {
		log.Log().Error("创建商品详情失败", zap.Error(err))
		return
	}

	// 2. 创建商品规格产品数据
	specProducts := []model.GoodsSpecProduct{
		{
			PrdSn:       "TEST001-RED-L",
			PrdDesc:     "红色大码",
			PrdPrice:    99.99,
			SpecId:      "spec_001",
			IsSale:      true,
			PrdNumber:   100,
			ProductImages: model.StringSlice{"image1.jpg", "image2.jpg"},
		},
		{
			PrdSn:       "TEST001-BLUE-M",
			PrdDesc:     "蓝色中码",
			PrdPrice:    89.99,
			SpecId:      "spec_002",
			IsSale:      true,
			PrdNumber:   50,
			ProductImages: model.StringSlice{"image3.jpg", "image4.jpg"},
		},
	}

	// 3. 批量创建规格产品
	if err := model.BatchCreateSpecProducts(goodsDetail.GoodsId, specProducts); err != nil {
		log.Log().Error("批量创建规格产品失败", zap.Error(err))
		return
	}

	// 4. 查询商品的规格产品
	specProductList, err := goodsDetail.GetSpecProducts()
	if err != nil {
		log.Log().Error("查询规格产品失败", zap.Error(err))
		return
	}

	fmt.Printf("商品 %d 有 %d 个规格产品:\n", goodsDetail.GoodsId, len(specProductList))
	for _, sp := range specProductList {
		fmt.Printf("- %s: %s (价格: %.2f)\n", sp.PrdSn, sp.PrdDesc, sp.PrdPrice)
	}

	// 5. 更新规格产品
	updatedSpecProducts := []model.GoodsSpecProduct{
		{
			PrdSn:       "TEST001-RED-L",
			PrdDesc:     "红色大码（更新）",
			PrdPrice:    109.99, // 价格更新
			SpecId:      "spec_001",
			IsSale:      true,
			PrdNumber:   80, // 库存更新
			ProductImages: model.StringSlice{"image1_new.jpg", "image2_new.jpg"},
		},
		{
			PrdSn:       "TEST001-GREEN-S",
			PrdDesc:     "绿色小码（新增）",
			PrdPrice:    79.99,
			SpecId:      "spec_003",
			IsSale:      true,
			PrdNumber:   30,
			ProductImages: model.StringSlice{"image5.jpg"},
		},
	}

	// 更新规格产品（会删除旧的，创建新的）
	if err := model.UpdateSpecProductsByGoodsId(goodsDetail.GoodsId, updatedSpecProducts); err != nil {
		log.Log().Error("更新规格产品失败", zap.Error(err))
		return
	}

	fmt.Println("规格产品更新成功！")
}

// QueryExamples 查询示例
func QueryExamples() {
	// 1. 查询特定商品的所有规格产品
	var specProducts []model.WdtGoodsSpecProduct
	goodsId := 12345
	if err := source.DB().Where("goods_id = ?", goodsId).Find(&specProducts).Error; err != nil {
		log.Log().Error("查询规格产品失败", zap.Error(err))
		return
	}

	// 2. 查询在售的规格产品
	var onSaleProducts []model.WdtGoodsSpecProduct
	if err := source.DB().Where("goods_id = ? AND is_sale = ?", goodsId, true).Find(&onSaleProducts).Error; err != nil {
		log.Log().Error("查询在售规格产品失败", zap.Error(err))
		return
	}

	// 3. 根据规格ID查询
	var specProduct model.WdtGoodsSpecProduct
	if err := source.DB().Where("spec_id = ?", "spec_001").First(&specProduct).Error; err != nil {
		log.Log().Error("根据规格ID查询失败", zap.Error(err))
		return
	}

	// 4. 查询价格范围内的产品
	var priceRangeProducts []model.WdtGoodsSpecProduct
	if err := source.DB().Where("prd_price BETWEEN ? AND ?", 50.0, 100.0).Find(&priceRangeProducts).Error; err != nil {
		log.Log().Error("查询价格范围产品失败", zap.Error(err))
		return
	}

	// 5. 统计商品的规格产品数量
	var count int64
	if err := source.DB().Model(&model.WdtGoodsSpecProduct{}).Where("goods_id = ?", goodsId).Count(&count).Error; err != nil {
		log.Log().Error("统计规格产品数量失败", zap.Error(err))
		return
	}

	fmt.Printf("商品 %d 共有 %d 个规格产品\n", goodsId, count)
}

// BatchOperationExamples 批量操作示例
func BatchOperationExamples() {
	// 1. 批量更新价格
	goodsId := 12345
	priceIncrease := 10.0

	if err := source.DB().Model(&model.WdtGoodsSpecProduct{}).
		Where("goods_id = ?", goodsId).
		Update("prd_price", source.DB().Raw("prd_price + ?", priceIncrease)).Error; err != nil {
		log.Log().Error("批量更新价格失败", zap.Error(err))
		return
	}

	// 2. 批量设置为下架
	if err := source.DB().Model(&model.WdtGoodsSpecProduct{}).
		Where("goods_id = ?", goodsId).
		Update("is_sale", false).Error; err != nil {
		log.Log().Error("批量下架失败", zap.Error(err))
		return
	}

	// 3. 批量删除
	if err := source.DB().Where("goods_id = ? AND deleted_status = ?", goodsId, 1).
		Delete(&model.WdtGoodsSpecProduct{}).Error; err != nil {
		log.Log().Error("批量删除失败", zap.Error(err))
		return
	}

	fmt.Println("批量操作完成！")
}

// AdvancedQueryExamples 高级查询示例
func AdvancedQueryExamples() {
	// 1. 联表查询：查询商品详情及其规格产品
	type GoodsWithSpecs struct {
		model.WdtGoodsDetail
		SpecProducts []model.WdtGoodsSpecProduct `gorm:"foreignKey:GoodsId;references:GoodsId"`
	}

	var goodsWithSpecs []GoodsWithSpecs
	if err := source.DB().Preload("SpecProducts").Find(&goodsWithSpecs).Error; err != nil {
		log.Log().Error("联表查询失败", zap.Error(err))
		return
	}

	// 2. 聚合查询：按商品统计规格产品信息
	type GoodsSpecSummary struct {
		GoodsId      int     `json:"goods_id"`
		SpecCount    int     `json:"spec_count"`
		MinPrice     float64 `json:"min_price"`
		MaxPrice     float64 `json:"max_price"`
		AvgPrice     float64 `json:"avg_price"`
		TotalStock   float64 `json:"total_stock"`
		OnSaleCount  int     `json:"on_sale_count"`
	}

	var summaries []GoodsSpecSummary
	if err := source.DB().Model(&model.WdtGoodsSpecProduct{}).
		Select(`
			goods_id,
			COUNT(*) as spec_count,
			MIN(prd_price) as min_price,
			MAX(prd_price) as max_price,
			AVG(prd_price) as avg_price,
			SUM(prd_number) as total_stock,
			SUM(CASE WHEN is_sale = true THEN 1 ELSE 0 END) as on_sale_count
		`).
		Group("goods_id").
		Find(&summaries).Error; err != nil {
		log.Log().Error("聚合查询失败", zap.Error(err))
		return
	}

	for _, summary := range summaries {
		fmt.Printf("商品 %d: 规格数=%d, 价格范围=%.2f-%.2f, 平均价格=%.2f, 总库存=%.0f, 在售数=%d\n",
			summary.GoodsId, summary.SpecCount, summary.MinPrice, summary.MaxPrice,
			summary.AvgPrice, summary.TotalStock, summary.OnSaleCount)
	}
}
