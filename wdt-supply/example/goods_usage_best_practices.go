package example

import (
	"fmt"
	"time"
	"wdt-supply/model"

	"go.uber.org/zap"
	"gorm.io/gorm"
	"yz-go/component/log"
	"yz-go/source"
)

// GoodsUsageBestPractices 商品使用最佳实践
type GoodsUsageBestPractices struct{}

// 1. 单个商品的创建和更新（适用于 fetchGoodsDetail 场景）
func (g *GoodsUsageBestPractices) SaveSingleGoods(goodsDetail *model.WdtGoodsDetail) error {
	// 检查商品是否已存在
	var existing model.WdtGoodsDetail
	err := source.DB().Where("goods_id = ?", goodsDetail.GoodsId).First(&existing).Error
	
	if err == gorm.ErrRecordNotFound {
		// 商品不存在，创建新商品
		return goodsDetail.CreateWithSpecProducts()
	} else if err != nil {
		return fmt.Errorf("查询商品失败: %v", err)
	}

	// 商品已存在，检查是否需要更新
	goodsModified, _ := time.Parse("2006-01-02 15:04:05", goodsDetail.UpdateTime)
	existingModified, _ := time.Parse("2006-01-02 15:04:05", existing.UpdateTime)
	
	if goodsModified.After(existingModified) {
		goodsDetail.ID = existing.ID // 设置ID以便更新
		return goodsDetail.UpdateWithSpecProducts()
	}

	// 不需要更新
	return nil
}

// 2. 批量商品处理（适用于 batchSaveGoodsData 场景）
func (g *GoodsUsageBestPractices) BatchSaveGoods(goodsList []*model.WdtGoodsDetail) error {
	if len(goodsList) == 0 {
		return nil
	}

	// 获取所有商品ID
	var goodsIds []int
	for _, goods := range goodsList {
		goodsIds = append(goodsIds, goods.GoodsId)
	}

	// 查询已存在的商品
	var existingGoods []model.WdtGoodsDetail
	if err := source.DB().Where("goods_id IN ?", goodsIds).Find(&existingGoods).Error; err != nil {
		return err
	}

	// 创建映射
	existingMap := make(map[int]model.WdtGoodsDetail)
	for _, goods := range existingGoods {
		existingMap[goods.GoodsId] = goods
	}

	// 分离新增和更新
	var toCreate []*model.WdtGoodsDetail
	var toUpdate []*model.WdtGoodsDetail

	for _, goods := range goodsList {
		if existing, ok := existingMap[goods.GoodsId]; ok {
			// 检查修改时间
			goodsModified, _ := time.Parse("2006-01-02 15:04:05", goods.UpdateTime)
			existingModified, _ := time.Parse("2006-01-02 15:04:05", existing.UpdateTime)
			if goodsModified.After(existingModified) {
				goods.ID = existing.ID
				toUpdate = append(toUpdate, goods)
			}
		} else {
			toCreate = append(toCreate, goods)
		}
	}

	// 使用事务批量处理
	return source.DB().Transaction(func(tx *gorm.DB) error {
		// 批量创建
		for _, goods := range toCreate {
			if err := g.createGoodsWithSpecs(tx, goods); err != nil {
				return err
			}
		}

		// 批量更新
		for _, goods := range toUpdate {
			if err := g.updateGoodsWithSpecs(tx, goods); err != nil {
				return err
			}
		}

		log.Log().Info("批量保存商品完成", 
			zap.Int("新增", len(toCreate)), 
			zap.Int("更新", len(toUpdate)))
		return nil
	})
}

// 3. 查询商品及规格产品（适用于各种查询场景）
func (g *GoodsUsageBestPractices) QueryGoodsWithSpecs() {
	// 3.1 查询单个商品及其所有规格产品
	var goods model.WdtGoodsDetail
	if err := source.DB().Preload("GoodsSpecProducts").Where("goods_id = ?", 12345).First(&goods).Error; err != nil {
		log.Log().Error("查询商品失败", zap.Error(err))
		return
	}
	fmt.Printf("商品: %s, 规格数量: %d\n", goods.GoodsName, len(goods.GoodsSpecProducts))

	// 3.2 查询商品及其在售规格产品
	var goodsWithOnSaleSpecs model.WdtGoodsDetail
	if err := source.DB().Preload("GoodsSpecProducts", "is_sale = ?", true).
		Where("goods_id = ?", 12345).First(&goodsWithOnSaleSpecs).Error; err != nil {
		log.Log().Error("查询在售规格失败", zap.Error(err))
		return
	}
	fmt.Printf("在售规格数量: %d\n", len(goodsWithOnSaleSpecs.GoodsSpecProducts))

	// 3.3 批量查询多个商品及其规格产品
	var goodsList []model.WdtGoodsDetail
	if err := source.DB().Preload("GoodsSpecProducts").Limit(10).Find(&goodsList).Error; err != nil {
		log.Log().Error("批量查询失败", zap.Error(err))
		return
	}
	for _, g := range goodsList {
		fmt.Printf("商品: %s, 规格数量: %d\n", g.GoodsName, len(g.GoodsSpecProducts))
	}

	// 3.4 使用模型方法加载规格产品
	var singleGoods model.WdtGoodsDetail
	if err := source.DB().Where("goods_id = ?", 12345).First(&singleGoods).Error; err == nil {
		// 加载所有规格产品
		singleGoods.LoadSpecProducts()
		
		// 或者按条件加载
		singleGoods.LoadSpecProductsWithCondition("is_sale = ? AND prd_price > ?", true, 50.0)
	}
}

// 4. 性能优化的查询方法
func (g *GoodsUsageBestPractices) OptimizedQueries() {
	// 4.1 只查询需要的字段
	var goods []model.WdtGoodsDetail
	source.DB().Select("id, goods_id, goods_name, goods_price").
		Preload("GoodsSpecProducts", func(db *gorm.DB) *gorm.DB {
			return db.Select("id, goods_id, prd_sn, prd_desc, prd_price, is_sale")
		}).
		Limit(10).Find(&goods)

	// 4.2 使用 Joins 代替 Preload（适用于一对一或需要条件过滤的场景）
	var results []struct {
		model.WdtGoodsDetail
		SpecCount int `json:"spec_count"`
	}
	source.DB().Table("wdt_goods_details").
		Select("wdt_goods_details.*, COUNT(wdt_goods_spec_products.id) as spec_count").
		Joins("LEFT JOIN wdt_goods_spec_products ON wdt_goods_details.goods_id = wdt_goods_spec_products.goods_id").
		Group("wdt_goods_details.id").
		Find(&results)

	// 4.3 分页查询
	var pagedGoods []model.WdtGoodsDetail
	source.DB().Preload("GoodsSpecProducts").
		Offset(0).Limit(20).
		Find(&pagedGoods)
}

// 5. 辅助方法
func (g *GoodsUsageBestPractices) createGoodsWithSpecs(tx *gorm.DB, goods *model.WdtGoodsDetail) error {
	// 创建商品
	if err := tx.Create(goods).Error; err != nil {
		return fmt.Errorf("创建商品失败: %v", err)
	}

	// 如果有规格产品，创建规格产品
	if len(goods.GoodsSpecProducts) > 0 {
		for i := range goods.GoodsSpecProducts {
			goods.GoodsSpecProducts[i].GoodsId = goods.GoodsId
		}
		if err := tx.Create(&goods.GoodsSpecProducts).Error; err != nil {
			return fmt.Errorf("创建规格产品失败: %v", err)
		}
	}

	return nil
}

func (g *GoodsUsageBestPractices) updateGoodsWithSpecs(tx *gorm.DB, goods *model.WdtGoodsDetail) error {
	// 更新商品
	if err := tx.Model(goods).Updates(goods).Error; err != nil {
		return fmt.Errorf("更新商品失败: %v", err)
	}

	// 处理规格产品
	if len(goods.GoodsSpecProducts) > 0 {
		// 删除现有规格产品
		if err := tx.Where("goods_id = ?", goods.GoodsId).Delete(&model.WdtGoodsSpecProduct{}).Error; err != nil {
			return fmt.Errorf("删除现有规格产品失败: %v", err)
		}

		// 创建新规格产品
		for i := range goods.GoodsSpecProducts {
			goods.GoodsSpecProducts[i].GoodsId = goods.GoodsId
			goods.GoodsSpecProducts[i].ID = 0
		}
		if err := tx.Create(&goods.GoodsSpecProducts).Error; err != nil {
			return fmt.Errorf("创建规格产品失败: %v", err)
		}
	}

	return nil
}

// 6. 实际使用示例（模拟您的 goods.go 中的场景）
func (g *GoodsUsageBestPractices) SimulateGoodsProcessing() {
	fmt.Println("=== 模拟商品处理流程 ===")

	// 模拟从API获取的商品数据
	apiGoodsData := &model.WdtGoodsDetail{
		GoodsId:     12345,
		GoodsName:   "API获取的商品",
		GoodsSn:     "API_GOODS_001",
		GoodsPrice:  99.99,
		UpdateTime:  time.Now().Format("2006-01-02 15:04:05"),
		
		// 模拟规格产品数据
		GoodsSpecProducts: []model.WdtGoodsSpecProduct{
			{
				PrdSn:     "API_GOODS_001-RED",
				PrdDesc:   "红色",
				PrdPrice:  99.99,
				SpecId:    "spec_001",
				IsSale:    true,
				PrdNumber: 100,
			},
			{
				PrdSn:     "API_GOODS_001-BLUE",
				PrdDesc:   "蓝色",
				PrdPrice:  89.99,
				SpecId:    "spec_002",
				IsSale:    true,
				PrdNumber: 50,
			},
		},
	}

	// 1. 单个商品保存（对应 fetchGoodsDetail 的使用场景）
	if err := g.SaveSingleGoods(apiGoodsData); err != nil {
		log.Log().Error("保存单个商品失败", zap.Error(err))
		return
	}
	fmt.Println("✓ 单个商品保存成功")

	// 2. 批量商品保存（对应 batchSaveGoodsData 的使用场景）
	batchGoods := []*model.WdtGoodsDetail{apiGoodsData}
	if err := g.BatchSaveGoods(batchGoods); err != nil {
		log.Log().Error("批量保存商品失败", zap.Error(err))
		return
	}
	fmt.Println("✓ 批量商品保存成功")

	// 3. 查询验证
	g.QueryGoodsWithSpecs()
	fmt.Println("✓ 查询验证完成")

	// 清理测试数据
	source.DB().Where("goods_id = ?", 12345).Delete(&model.WdtGoodsDetail{})
	fmt.Println("✓ 清理测试数据完成")
}

// RunBestPracticesDemo 运行最佳实践演示
func RunBestPracticesDemo() {
	practices := &GoodsUsageBestPractices{}
	practices.SimulateGoodsProcessing()
	practices.OptimizedQueries()
}
