package goods

import (
	"fmt"
	"wdt-supply/common"
	"wdt-supply/model"
	"yz-go/source"
)

// GetAllBrands 获取所有品牌
func (wdt *WdtGoods) GetAllBrands() error {
	const pageSize = 100
	pageNo := 0

	// 删除所有历史数据
	if err := source.DB().Where("1 = 1").Delete(&model.WdtBrand{}).Error; err != nil {
		return fmt.Errorf("删除历史品牌数据失败: %v", err)
	}

	// 首次请求获取总数
	brands, totalCount, err := wdt.fetchBrandPage(pageNo, pageSize)
	if err != nil {
		return err
	}

	// 保存第一页数据
	if err := wdt.saveBrandData(brands); err != nil {
		return fmt.Errorf("保存品牌数据失败: %v", err)
	}

	// 计算总页数
	totalPages := (totalCount + pageSize - 1) / pageSize

	// 获取并保存剩余页面数据
	for pageNo = 1; pageNo < totalPages; pageNo++ {
		brands, _, err := wdt.fetchBrandPage(pageNo, pageSize)
		if err != nil {
			return fmt.Errorf("获取第%d页品牌失败: %v", pageNo, err)
		}

		if err := wdt.saveBrandData(brands); err != nil {
			return fmt.Errorf("保存第%d页品牌数据失败: %v", pageNo, err)
		}
	}

	return nil
}

// fetchBrandPage 获取单页品牌数据
func (wdt *WdtGoods) fetchBrandPage(pageNo, pageSize int) ([]model.WdtBrand, int, error) {
	req := &common.WdtRequest{
		Sid:       wdt.Sid,
		Appkey:    wdt.AppKey,
		Appsecret: wdt.AppSecret,
	}

	req.SetParams(map[string]interface{}{
		"page_no":   pageNo,
		"page_size": pageSize,
	})

	resp, err := req.DoRequest("goods_brand_query.php", false)
	if err != nil {
		return nil, 0, err
	}

	var brands []model.WdtBrand
	if err := resp.GetListData("brand_lists", &brands); err != nil {
		return nil, 0, err
	}

	if resp.Code != 0 {
		return nil, 0, fmt.Errorf("获取品牌失败: %s", resp.Message)
	}

	// 只有第一页返回总数
	totalCount := 0
	if pageNo == 0 {
		totalCount = resp.Total
	}

	return brands, totalCount, nil
}

// saveBrandData 保存品牌数据
func (wdt *WdtGoods) saveBrandData(brands []model.WdtBrand) error {
	if len(brands) == 0 {
		return nil
	}

	// 批量创建新数据
	return source.DB().CreateInBatches(brands, 100).Error
}

//func init() {
//	// 应用启动时立即执行一次
//	go func() {
//		var gatherSuppliers []model2.GatherSupply
//		err := source.DB().Where("category_id = ?", common2.SUPPLY_WDT).Find(&gatherSuppliers).Error
//		if err != nil {
//			return
//		}
//
//		for _, gatherSupply := range gatherSuppliers {
//			var setting model.WdtSetting
//			err, setting = service.GetWdtSetting(gatherSupply.ID)
//			if err != nil {
//				continue
//			}
//
//			// 检查是否已有数据
//			var count int64
//			if err = source.DB().Model(&model.WdtBrand{}).Count(&count).Error; err != nil {
//				log.Log().Info("检查品牌数据失败: %v", zap.Any("err", err))
//				continue
//			}
//
//			var wdt = WdtGoods{}
//			// 只有在没有数据时才执行初始化
//			if count == 0 {
//				if err = wdt.SetBase(setting.Appkey, setting.Appsecret, setting.Sid).GetAllBrands(); err != nil {
//					log.Log().Info("增量获取品牌数据失败: %v", zap.Any("err", err))
//					continue
//				}
//			}
//
//			// 设置每天凌晨 2 点执行
//			for {
//				now := time.Now()
//				next := time.Date(now.Year(), now.Month(), now.Day()+1, 2, 0, 0, 0, now.Location())
//				timer := time.NewTimer(next.Sub(now))
//				<-timer.C
//
//				if err = wdt.SetBase(setting.Appkey, setting.Appsecret, setting.Sid).GetAllBrands(); err != nil {
//					log.Log().Info("同步品牌数据失败: %v", zap.Any("err", err))
//				}
//			}
//		}
//	}()
//}
