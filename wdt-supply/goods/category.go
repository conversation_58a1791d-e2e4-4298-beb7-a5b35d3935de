package goods

import (
	"fmt"
	"wdt-supply/common"
	"wdt-supply/model"
	"yz-go/source"
)

// GetAllCategories 获取所有商品分类
func (wdt *WdtGoods) GetAllCategories() error {
	const pageSize = 100
	pageNo := 0

	// 删除所有历史数据
	if err := source.DB().Where("1 = 1").Delete(&model.WdtCategory{}).Error; err != nil {
		return fmt.Errorf("删除历史分类数据失败: %v", err)
	}

	// 首次请求获取总数
	categories, totalCount, err := wdt.fetchCategoryPage(pageNo, pageSize)
	if err != nil {
		return err
	}

	// 保存第一页数据
	if err := wdt.saveCategoryData(categories); err != nil {
		return fmt.Errorf("保存分类数据失败: %v", err)
	}

	// 计算总页数
	totalPages := (totalCount + pageSize - 1) / pageSize

	// 获取并保存剩余页面数据
	for pageNo = 1; pageNo < totalPages; pageNo++ {
		categories, _, err := wdt.fetchCategoryPage(pageNo, pageSize)
		if err != nil {
			return fmt.Errorf("获取第%d页分类失败: %v", pageNo, err)
		}

		if err := wdt.saveCategoryData(categories); err != nil {
			return fmt.Errorf("保存第%d页分类数据失败: %v", pageNo, err)
		}
	}

	return nil
}

// fetchCategoryPage 获取单页分类数据
func (wdt *WdtGoods) fetchCategoryPage(pageNo, pageSize int) ([]model.WdtCategory, int, error) {
	req := &common.WdtRequest{
		Sid:       wdt.Sid,
		Appkey:    wdt.AppKey,
		Appsecret: wdt.AppSecret,
	}

	req.SetParams(map[string]interface{}{
		"page_no":   pageNo,
		"page_size": pageSize,
	})

	resp, err := req.DoRequest("goods_class_query.php", true)
	if err != nil {
		return nil, 0, err
	}

	var categories []model.WdtCategory
	if err := resp.GetListData("goods_class", &categories); err != nil {
		return nil, 0, err
	}

	if resp.Code != 0 {
		return nil, 0, fmt.Errorf("获取分类失败: %s", resp.Message)
	}

	// 只有第一页返回总数
	totalCount := 0
	if pageNo == 0 {
		totalCount = resp.Total
	}

	return categories, totalCount, nil
}

// saveCategoryData 保存分类数据
func (wdt *WdtGoods) saveCategoryData(categories []model.WdtCategory) error {
	if len(categories) == 0 {
		return nil
	}

	// 批量创建新数据
	return source.DB().CreateInBatches(categories, 100).Error
}

//fuh
