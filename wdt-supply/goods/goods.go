package goods

import (
	"fmt"
	common2 "public-supply/common"
	model2 "public-supply/model"
	"time"
	"wdt-supply/common"
	"wdt-supply/model"
	"wdt-supply/service"
	"yz-go/component/log"
	"yz-go/source"

	"go.uber.org/zap"
)

type WdtGoods struct {
	AppKey    string `json:"app_key"`
	AppSecret string `json:"app_secret"`
	Sid       string `json:"sid"`
}

func (wdt *WdtGoods) SetBase(appKey string, appSecret string, sid string) *WdtGoods {
	wdt.AppKey = appKey
	wdt.AppSecret = appSecret
	wdt.Sid = sid
	return wdt
}
func (wdt *WdtGoods) GetGoodsDetails() {

	// 获取历史数据
	startTime := time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local)
	if err := wdt.GetHistoricalGoods(startTime); err != nil {
		log.Log().Info("获取商品历史数据失败", zap.Any("err", err))
	}
	return
}

// GetHistoricalGoods 获取历史商品数据
// GetHistoricalGoods 获取历史商品数据
func (wdt *WdtGoods) GetHistoricalGoods(startTime time.Time) error {
	endTime := time.Now()
	currentTime := endTime
	interval := 30 * 24 * time.Hour // 30天间隔

	for {
		queryStart := currentTime.Add(-interval)
		if queryStart.Before(startTime) {
			queryStart = startTime
		}

		hasData, err := wdt.fetchGoodsInTimeRange(queryStart, currentTime)
		if err != nil {
			return err
		}

		// 如果没有数据返回，说明已经获取完所有历史数据
		if !hasData {
			break
		}

		// 更新时间范围，继续往前查询
		currentTime = queryStart

		// 如果已经查询到起始时间，则退出
		if currentTime.Equal(startTime) || currentTime.Before(startTime) {
			break
		}
	}

	return nil
}

// GetIncrementalGoods 增量获取商品数据
func (wdt *WdtGoods) GetIncrementalGoods() error {
	now := time.Now()
	startTime := now.Add(-1 * time.Hour) // 获取上一小时的数据
	_, err := wdt.fetchGoodsInTimeRange(startTime, now)
	return err
}

// fetchGoodsInTimeRange 在指定时间范围内获取商品数据
// 在文件开头添加
var (
	// 每个接口的限速器
	goodsListLimiter   = time.Tick(time.Second) // 每秒1次，每分钟60次
	stockLimiter       = time.Tick(time.Second) // 每秒1次，每分钟60次
	goodsDetailLimiter = time.Tick(time.Second) // 每秒1次，每分钟60次
)

// 修改 fetchGoodsInTimeRange 方法
func (wdt *WdtGoods) fetchGoodsInTimeRange(startTime, endTime time.Time) (bool, error) {
	req := &common.WdtRequest{
		Sid:       wdt.Sid,
		Appkey:    wdt.AppKey,
		Appsecret: wdt.AppSecret,
	}

	hasData := false
	pageNo := 1
	pageSize := 100

	for {
		<-goodsListLimiter // 限速
		req.SetParams(map[string]interface{}{
			"start_time": startTime.Format("2006-01-02 15:04:05"),
			"end_time":   endTime.Format("2006-01-02 15:04:05"),
			"page_no":    pageNo,
			"page_size":  pageSize,
		})

		resp, err := req.DoRequest("vip_api_goods_query.php", true)
		if err != nil {
			return false, err
		}

		// 获取商品列表
		var goodsList []model.WdtGoodsList
		if err := resp.GetListData("goods_list", &goodsList); err != nil {
			return false, err
		}

		if len(goodsList) > 0 {
			hasData = true
			// 直接处理商品数据
			if err := wdt.processGoods(goodsList); err != nil {
				return false, err
			}
		}

		// 如果返回的数据少于页大小，说明已经是最后一页
		if len(goodsList) < pageSize {
			break
		}

		pageNo++
	}

	return hasData, nil
}

// processGoodsWithStock 处理商品数据
// batchSaveGoodsData 批量保存商品数据
func (wdt *WdtGoods) batchSaveGoodsData(goodsList []*model.WdtGoodsDetail) error {
	if len(goodsList) == 0 {
		return nil
	}

	// 获取所有商品编号
	var goodsNos []string
	for _, goods := range goodsList {
		goodsNos = append(goodsNos, goods.GoodsNo)
	}

	// 查询已存在的商品
	var existingGoods []model.WdtGoodsDetail
	if err := source.DB().Where("goods_no IN ?", goodsNos).Find(&existingGoods).Error; err != nil {
		return err
	}

	// 将已存在的商品映射到 map
	existingMap := make(map[string]model.WdtGoodsDetail)
	for _, goods := range existingGoods {
		existingMap[goods.GoodsNo] = goods
	}

	// 分离需要新增和更新的商品
	var toCreate []*model.WdtGoodsDetail
	var toUpdate []*model.WdtGoodsDetail

	for _, goods := range goodsList {
		if existing, ok := existingMap[goods.GoodsNo]; ok {
			// 检查修改时间
			goodsModified, _ := time.Parse("2006-01-02 15:04:05", goods.GoodsModified)
			existingModified, _ := time.Parse("2006-01-02 15:04:05", existing.GoodsModified)
			if goodsModified.After(existingModified) {
				goods.ID = existing.ID // 设置ID以便更新
				toUpdate = append(toUpdate, goods)
			}
		} else {
			toCreate = append(toCreate, goods)
		}
	}

	// 批量创建新商品
	if len(toCreate) > 0 {
		if err := source.DB().Create(toCreate).Error; err != nil {
			log.Log().Info("批量创建商品失败", zap.Error(err))
			return err
		}
	}

	// 批量更新已存在的商品
	for _, goods := range toUpdate {
		if err := source.DB().Model(goods).Updates(goods).Error; err != nil {
			log.Log().Info("更新商品失败",
				zap.String("goods_no", goods.GoodsNo),
				zap.Error(err))
		}
	}

	return nil
}

// processGoods 处理商品数据
func (wdt *WdtGoods) processGoods(goods []model.WdtGoodsList) error {
	// 使用 map 来去重 OuterId
	outerIdMap := make(map[string]bool)
	var uniqueGoods []model.WdtGoodsList

	for _, item := range goods {
		if item.OuterId == "" {
			continue
		}
		// 如果 OuterId 已存在，跳过
		if _, exists := outerIdMap[item.OuterId]; exists {
			continue
		}
		outerIdMap[item.OuterId] = true
		uniqueGoods = append(uniqueGoods, item)
	}
	fmt.Println("本次详情数量:", len(uniqueGoods))
	var detailsList []*model.WdtGoodsDetail
	for _, item := range uniqueGoods {
		// 获取商品详情
		details, err := wdt.fetchGoodsDetail(item.OuterId)
		if err != nil {
			log.Log().Info("获取商品详情失败",
				zap.String("outer_id", item.OuterId),
				zap.Error(err))
			continue
		}
		detailsList = append(detailsList, details)

		// 限速
		<-time.After(time.Second)
	}

	// 批量保存商品数据
	if err := wdt.batchSaveGoodsData(detailsList); err != nil {
		log.Log().Info("批量保存商品数据失败", zap.Error(err))
		return err
	}

	return nil
}

// fetchGoodsDetail 获取商品详情
func (wdt *WdtGoods) fetchGoodsDetail(goodsNo string) (*model.WdtGoodsDetail, error) {
	<-goodsDetailLimiter // 限速

	// 获取商品详情
	req := common.NewGxptRequest("180568", "aCTV1wXd7D0EXV2OluxkzGIbXRQ5QZo8", "gxpt.goods.detial")
	req.SetParameter("goods_name", "接口测试2221")
	req.SetParameter("price", 20000)
	resp, err := req.DoRequest(true)
	if err != nil {
		return nil, err
	}

	var goodsList []model.WdtGoodsDetail
	if err := resp.GetListData("goods_list", &goodsList); err != nil {
		return nil, err
	}

	if len(goodsList) == 0 {
		return nil, fmt.Errorf("商品不存在")
	}

	return &goodsList[0], nil
}

// CheckDeletedGoods 检查已删除商品
func (wdt *WdtGoods) CheckDeletedGoods() error {
	var goods []model.WdtGoodsDetail
	if err := source.DB().Find(&goods).Error; err != nil {
		return err
	}

	// 创建限速器和并发控制
	limiter := time.Tick(time.Second)
	maxConcurrent := 5
	sem := make(chan struct{}, maxConcurrent)

	for _, item := range goods {
		sem <- struct{}{} // 获取信号量
		go func(g model.WdtGoodsDetail) {
			defer func() { <-sem }() // 释放信号量
			<-limiter                // 限速

			// 检查商品是否存在
			req := &common.WdtRequest{
				Sid:       wdt.Sid,
				Appkey:    wdt.AppKey,
				Appsecret: wdt.AppSecret,
				Params: map[string]interface{}{
					"goods_no": g.GoodsNo,
				},
			}

			resp, err := req.DoRequest("goods_query.php", true)
			if err != nil {
				log.Log().Info("检查商品失败: "+g.GoodsNo, zap.Any("err", err))
				return
			}

			var goodsList []model.WdtGoodsDetail
			if err := resp.GetListData("goods_list", &goodsList); err != nil {
				log.Log().Info("解析商品数据失败: "+g.GoodsNo, zap.Any("err", err))
				return
			}

			// 如果商品不存在，标记为删除
			if len(goodsList) == 0 {
				if err := source.DB().Model(&model.WdtGoodsDetail{}).
					Where("goods_no = ?", g.GoodsNo).
					Update("deleted", true).Error; err != nil {
					log.Log().Info("标记删除商品失败: "+g.GoodsNo, zap.Any("err", err))
				}
			}
		}(item)
	}

	// 等待所有goroutine完成
	for i := 0; i < maxConcurrent; i++ {
		sem <- struct{}{}
	}

	return nil
}

// 在goods.go文件中添加init函数

func init() {
	go func() {
		var gatherSuppliers []model2.GatherSupply
		err := source.DB().Where("category_id = ?", common2.SUPPLY_WDT).Find(&gatherSuppliers).Error
		if err != nil {
			return
		}

		for _, gatherSupply := range gatherSuppliers {
			var setting model.WdtSetting
			err, setting = service.GetWdtSetting(gatherSupply.ID)
			if err != nil {
				continue
			}

			// 检查是否已有数据
			var count int64
			if err = source.DB().Model(&model.WdtGoodsDetail{}).Count(&count).Error; err != nil {
				log.Log().Info("检查商品数据失败: %v", zap.Any("err", err))
				continue
			}

			var wdt = WdtGoods{}
			// 只有在没有数据时才执行初始化
			if count == 0 {
				startTime := time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local)
				if err = wdt.SetBase(setting.Appkey, setting.Appsecret, setting.Sid).GetHistoricalGoods(startTime); err != nil {
					log.Log().Info("获取商品历史数据失败", zap.Any("err", err))
					continue
				}
			}

			// 设置每小时执行一次
			for {
				now := time.Now()
				next := time.Date(now.Year(), now.Month(), now.Day(), now.Hour()+1, 15, 0, 0, now.Location())
				timer := time.NewTimer(next.Sub(now))
				<-timer.C

				if err = wdt.SetBase(setting.Appkey, setting.Appsecret, setting.Sid).GetIncrementalGoods(); err != nil {
					log.Log().Info("同步商品数据失败: %v", zap.Any("err", err))
				}
			}
		}
	}()
}
