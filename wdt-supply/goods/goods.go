package goods

import (
	"fmt"
	"gin-vue-admin/cmd/gva"
	"github.com/chenhg5/collection"
	common2 "public-supply/common"
	model2 "public-supply/model"
	"strconv"
	"strings"
	"time"
	"wdt-supply/common"
	"wdt-supply/model"
	"wdt-supply/mq"
	"wdt-supply/service"
	"yz-go/component/log"
	"yz-go/source"
	"yz-go/utils"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type WdtGoods struct {
	AppKey               string `json:"app_key"`
	AppSecret            string `json:"app_secret"`
	Sid                  string `json:"sid"`
	DistributorAppkey    string `json:"distributor_appkey"`
	DistributorAppsecret string `json:"distributor_appsecret"`
	ShopId               string `json:"shop_id"`
}

func (wdt *WdtGoods) SetBase(appKey string, appSecret string, sid string, distributorAppkey string, distributorAppsecret string, shopId string) *WdtGoods {
	wdt.AppKey = appKey
	wdt.AppSecret = appSecret
	wdt.Sid = sid
	wdt.DistributorAppkey = distributorAppkey
	wdt.DistributorAppsecret = distributorAppsecret
	wdt.ShopId = shopId
	return wdt
}
func (wdt *WdtGoods) GetGoodsDetails() {

	// 获取历史数据
	startTime := time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local)
	if err := wdt.GetHistoricalGoods(startTime); err != nil {
		log.Log().Info("获取商品历史数据失败", zap.Any("err", err))
	}
	return
}

// GetHistoricalGoods 获取历史商品数据
// GetHistoricalGoods 获取历史商品数据
func (wdt *WdtGoods) GetHistoricalGoods(startTime time.Time) error {
	endTime := time.Now()
	currentTime := endTime
	interval := 365 * 24 * time.Hour // 30天间隔

	for {
		queryStart := currentTime.Add(-interval)
		if queryStart.Before(startTime) {
			queryStart = startTime
		}

		hasData, err := wdt.fetchGoodsInTimeRange(queryStart, currentTime)
		if err != nil {
			return err
		}

		// 如果没有数据返回，说明已经获取完所有历史数据
		if !hasData {
			break
		}

		// 更新时间范围，继续往前查询
		currentTime = queryStart

		// 如果已经查询到起始时间，则退出
		if currentTime.Equal(startTime) || currentTime.Before(startTime) {
			break
		}
	}

	return nil
}

// GetIncrementalGoods 增量获取商品数据
func (wdt *WdtGoods) GetIncrementalGoods() error {
	now := time.Now()
	startTime := now.Add(-2 * time.Hour) // 获取上一小时的数据
	_, err := wdt.fetchGoodsInTimeRange(startTime, now)
	return err
}

// fetchGoodsInTimeRange 在指定时间范围内获取商品数据
// 在文件开头添加
var (
	// 每个接口的限速器
	goodsListLimiter   = time.Tick(time.Second) // 每秒1次，每分钟60次
	stockLimiter       = time.Tick(time.Second) // 每秒1次，每分钟60次
	goodsDetailLimiter = time.Tick(time.Second) // 每秒1次，每分钟60次
)

// 修改 fetchGoodsInTimeRange 方法
func (wdt *WdtGoods) fetchGoodsInTimeRange(startTime, endTime time.Time) (bool, error) {
	req := common.NewGxptRequest(wdt.DistributorAppkey, wdt.DistributorAppsecret, "gxpt.goods.supplier.list")

	hasData := false
	pageNo := 1
	pageSize := 100

	for {
		req.SetParameters(map[string]interface{}{
			"selection_time_start": startTime.Format("2006-01-02 15:04:05"),
			"selection_time_end":   endTime.Format("2006-01-02 15:04:05"),
			"current_page":         pageNo,
			"page_rows":            pageSize,
			"tail":                 0,
		})

		resp, err := req.DoRequest(true)
		if err != nil {
			return false, err
		}

		// 获取商品列表
		var goodsList []model.WdtGoodsList
		if err := resp.GetListData("goods_supplier_list_response", &goodsList); err != nil {
			return false, err
		}

		if len(goodsList) > 0 {
			hasData = true
			// 直接处理商品数据
			if err := wdt.processGoods(goodsList); err != nil {
				return false, err
			}
		}

		// 如果返回的数据少于页大小，说明已经是最后一页
		if len(goodsList) < pageSize {
			break
		}

		pageNo++
	}

	return hasData, nil
}

// processGoodsWithStock 处理商品数据
// batchSaveGoodsData 批量保存商品数据（包含规格产品）
func (wdt *WdtGoods) batchSaveGoodsData(goodsList []*model.WdtGoodsDetail) error {
	if len(goodsList) == 0 {
		return nil
	}

	// 获取所有商品编号
	var goodsNos []string
	for _, goods := range goodsList {
		goodsNos = append(goodsNos, goods.GoodsSn)
	}

	// 查询已存在的商品
	var existingGoods []model.WdtGoodsDetail
	if err := source.DB().Where("goods_sn IN ?", goodsNos).Find(&existingGoods).Error; err != nil {
		return err
	}

	// 将已存在的商品映射到 map
	existingMap := make(map[string]model.WdtGoodsDetail)
	for _, goods := range existingGoods {
		existingMap[goods.GoodsSn] = goods
	}

	// 分离需要新增和更新的商品
	var toCreate []*model.WdtGoodsDetail
	var toUpdate []*model.WdtGoodsDetail

	// 收集所有分类信息
	categoryMap := make(map[int]model.CategoryInfo)
	// 存储分类路径信息，用于构建分类树
	categoryPathMap := make(map[int][]string)

	// 遍历所有商品，收集分类信息
	for _, goods := range goodsList {
		// 处理商品价格逻辑...
		if len(goods.GoodsSpecProducts) > 0 {
			var minPrice, minSuggestedPrice float64
			for _, sku := range goods.GoodsSpecProducts {
				if minPrice == 0 || sku.DistributionPrice < minPrice {
					minPrice = sku.DistributionPrice
					minSuggestedPrice = sku.PrdPrice
				}
			}
			goods.GoodsPrice = minPrice
			goods.SuggestedPrice = minSuggestedPrice
		}

		// 收集分类信息
		for _, category := range goods.CategoryList {
			categoryMap[category.CategoryId] = category

			// 拆分 node_name_path 获取分类路径
			pathNames := strings.Split(category.NodeNamePath, ">")
			categoryPathMap[category.CategoryId] = pathNames
		}

		// 处理商品新增/更新逻辑...
		if existing, ok := existingMap[goods.GoodsSn]; ok {
			// 检查修改时间
			goodsModified, _ := time.Parse("2006-01-02 15:04:05", goods.UpdateTime)
			existingModified, _ := time.Parse("2006-01-02 15:04:05", existing.UpdateTime)
			if goodsModified.After(existingModified) {
				goods.ID = existing.ID // 设置ID以便更新
				toUpdate = append(toUpdate, goods)
			}
		} else {
			toCreate = append(toCreate, goods)
		}
	}

	// 使用事务批量处理
	return source.DB().Transaction(func(tx *gorm.DB) error {
		// 处理分类信息
		if len(categoryMap) > 0 {
			if err := wdt.syncCategoriesWithPath(tx, categoryMap, categoryPathMap); err != nil {
				log.Log().Error("同步分类信息失败", zap.Error(err))
				return err
			}
			log.Log().Info("同步分类信息成功", zap.Int("数量", len(categoryMap)))
		}

		// 批量创建新商品及其规格产品
		if len(toCreate) > 0 {
			if err := wdt.batchCreateGoodsWithSpecs(tx, toCreate); err != nil {
				log.Log().Error("批量创建商品失败", zap.Error(err))
				return err
			}
			log.Log().Info("批量创建商品成功", zap.Int("数量", len(toCreate)))
		}

		// 批量更新已存在的商品及其规格产品
		if len(toUpdate) > 0 {
			if err := wdt.batchUpdateGoodsWithSpecs(tx, toUpdate); err != nil {
				log.Log().Error("批量更新商品失败", zap.Error(err))
				return err
			}
			log.Log().Info("批量更新商品成功", zap.Int("数量", len(toUpdate)))
		}

		return nil
	})
}

// syncCategoriesWithPath 同步分类信息到分类表，并处理分类路径
func (wdt *WdtGoods) syncCategoriesWithPath(tx *gorm.DB, categoryMap map[int]model.CategoryInfo, categoryPathMap map[int][]string) error {
	// 构建完整的分类树
	categoryTree := make(map[string]model.WdtCategory)

	// 遍历所有分类
	for categoryId, pathNames := range categoryPathMap {
		categoryInfo := categoryMap[categoryId]

		// 处理每个层级的分类
		parentId := 0
		var fullPath string

		for i, name := range pathNames {
			// 构建当前层级的路径
			if i > 0 {
				fullPath += ">" + name
			} else {
				fullPath = name
			}

			// 检查此分类是否已处理
			if _, exists := categoryTree[fullPath]; !exists {
				// 创建新的分类记录
				categoryTree[fullPath] = model.WdtCategory{
					CategoryName: name,
					ParentId:     parentId,
					Level:        i,
					FullPath:     fullPath,
				}

				// 如果是最后一级分类，设置 CategoryId
				if i == len(pathNames)-1 {
					category := categoryTree[fullPath]
					category.CategoryId = categoryInfo.CategoryId
					categoryTree[fullPath] = category
				}
			}

			// 更新父ID，用于下一级
			if cat, ok := categoryTree[fullPath]; ok {
				parentId = cat.CategoryId
			}
		}
	}

	// 将分类树转换为列表
	var categories []model.WdtCategory
	for _, category := range categoryTree {
		categories = append(categories, category)
	}

	// 查询已存在的分类
	var existingCategories []model.WdtCategory
	if err := tx.Find(&existingCategories).Error; err != nil {
		return err
	}

	// 将已存在的分类映射到 map
	existingCategoryMap := make(map[string]model.WdtCategory)
	for _, category := range existingCategories {
		existingCategoryMap[category.FullPath] = category
	}

	// 分离需要新增和更新的分类
	var toCreateCategories []model.WdtCategory
	var toUpdateCategories []model.WdtCategory

	for _, category := range categories {
		if existing, ok := existingCategoryMap[category.FullPath]; ok {
			// 更新已存在的分类
			category.ID = existing.ID
			toUpdateCategories = append(toUpdateCategories, category)
		} else {
			// 新增分类
			toCreateCategories = append(toCreateCategories, category)
		}
	}

	// 批量创建新分类
	if len(toCreateCategories) > 0 {
		if err := tx.Create(&toCreateCategories).Error; err != nil {
			return fmt.Errorf("创建分类失败: %v", err)
		}
	}

	// 批量更新已存在的分类
	for _, category := range toUpdateCategories {
		if err := tx.Model(&category).Updates(map[string]interface{}{
			"category_name": category.CategoryName,
			"parent_id":     category.ParentId,
			"level":         category.Level,
			"full_path":     category.FullPath,
		}).Error; err != nil {
			return fmt.Errorf("更新分类失败: %v", err)
		}
	}

	return nil
}

// createGoodsWithSpecs 创建商品及其规格产品
func (wdt *WdtGoods) createGoodsWithSpecs(tx *gorm.DB, goods *model.WdtGoodsDetail) error {
	// 1. 创建商品主体
	if err := tx.Create(goods).Error; err != nil {
		return fmt.Errorf("创建商品失败: %v", err)
	}

	// 2. 如果有规格产品数据，创建规格产品
	if len(goods.GoodsSpecProducts) > 0 {
		// 为每个规格产品设置商品ID
		for i := range goods.GoodsSpecProducts {
			goods.GoodsSpecProducts[i].GoodsId = goods.GoodsId
			goods.GoodsSpecProducts[i].ID = 0
		}

		// 批量创建规格产品
		if err := tx.Create(&goods.GoodsSpecProducts).Error; err != nil {
			return fmt.Errorf("创建规格产品失败: %v", err)
		}
	}

	return nil
}

// updateGoodsWithSpecs 更新商品及其规格产品
func (wdt *WdtGoods) updateGoodsWithSpecs(tx *gorm.DB, goods *model.WdtGoodsDetail) error {
	// 1. 更新商品主体信息
	if err := tx.Model(goods).Updates(goods).Error; err != nil {
		return fmt.Errorf("更新商品失败: %v", err)
	}

	// 2. 处理规格产品
	if len(goods.GoodsSpecProducts) > 0 {
		// 先删除现有的规格产品
		if err := tx.Where("goods_id = ?", goods.GoodsId).Delete(&model.WdtGoodsSpecProduct{}).Error; err != nil {
			return fmt.Errorf("删除现有规格产品失败: %v", err)
		}

		// 创建新的规格产品切片，避免修改原始数据
		var newSpecProducts []model.WdtGoodsSpecProduct
		for _, spec := range goods.GoodsSpecProducts {
			newSpec := spec
			newSpec.ID = 0 // 重置ID，确保创建新记录
			newSpec.GoodsId = goods.GoodsId
			newSpec.CreatedAt = nil // 重置创建时间
			newSpec.UpdatedAt = nil // 重置更新时间
			newSpecProducts = append(newSpecProducts, newSpec)
		}

		// 批量创建新的规格产品
		if err := tx.Create(&newSpecProducts).Error; err != nil {
			return fmt.Errorf("创建规格产品失败: %v", err)
		}
	}

	return nil
}

// processGoods 处理商品数据
func (wdt *WdtGoods) processGoods(goods []model.WdtGoodsList) error {
	// 使用 map 来去重 OuterId
	outerIdMap := make(map[int]bool)
	var uniqueGoods []model.WdtGoodsList

	for _, item := range goods {
		if item.GoodsId == 0 {
			continue
		}
		// 如果 OuterId 已存在，跳过
		if _, exists := outerIdMap[item.GoodsId]; exists {
			continue
		}
		outerIdMap[item.GoodsId] = true
		uniqueGoods = append(uniqueGoods, item)
	}
	fmt.Println("本次详情数量:", len(uniqueGoods))
	var detailsList []*model.WdtGoodsDetail
	for _, item := range uniqueGoods {
		// 获取商品详情
		details, err := wdt.fetchGoodsDetail(item.GoodsId)
		if err != nil {
			log.Log().Info("获取商品详情失败",
				zap.Int("goods_id", item.GoodsId),
				zap.Error(err))
			continue
		}
		if details.GoodsSn == "gyl-ywxxthhB003" {
			fmt.Println(111)
		}
		details.SupplierNo = item.SupplierNickNo
		detailsList = append(detailsList, details)

		// 限速
		<-time.After(time.Second)
	}

	// 批量保存商品数据
	if err := wdt.batchSaveGoodsData(detailsList); err != nil {
		log.Log().Info("批量保存商品数据失败", zap.Error(err))
		return err
	}

	return nil
}

// fetchGoodsDetail 获取商品详情
func (wdt *WdtGoods) fetchGoodsDetail(GoodsId int) (*model.WdtGoodsDetail, error) {
	<-goodsDetailLimiter // 限速

	// 获取商品详情
	req := common.NewGxptRequest(wdt.DistributorAppkey, wdt.DistributorAppsecret, "gxpt.goods.detail")
	req.SetParameter("goods_id", GoodsId)
	resp, err := req.DoRequest(true)
	if err != nil {
		return nil, err
	}

	var goodsList model.WdtGoodsDetail
	if err := resp.GetListData("goods_detail_response", &goodsList); err != nil {
		return nil, err
	}

	return &goodsList, nil
}

// CheckDeletedGoods 检查已删除商品
func (wdt *WdtGoods) CheckDeletedGoods() error {
	var goods []model.WdtGoodsDetail
	if err := source.DB().Find(&goods).Error; err != nil {
		return err
	}

	// 创建限速器和并发控制
	limiter := time.Tick(time.Second)
	maxConcurrent := 5
	sem := make(chan struct{}, maxConcurrent)

	for _, item := range goods {
		sem <- struct{}{} // 获取信号量
		go func(g model.WdtGoodsDetail) {
			defer func() { <-sem }() // 释放信号量
			<-limiter                // 限速

			// 检查商品是否存在
			req := common.NewGxptRequest(wdt.DistributorAppkey, wdt.DistributorAppsecret, "gxpt.goods.detail")

			resp, err := req.DoRequest(true)
			if err != nil {
				log.Log().Info("检查商品失败: "+strconv.Itoa(g.GoodsId), zap.Any("err", err))
				return
			}

			var goodsList []model.WdtGoodsDetail
			if err := resp.GetListData("goods_detail_response", &goodsList); err != nil {
				log.Log().Info("解析商品数据失败: "+strconv.Itoa(g.GoodsId), zap.Any("err", err))
				return
			}

			// 如果商品不存在，标记为删除
			if len(goodsList) == 0 {
				if err := source.DB().Model(&model.WdtGoodsDetail{}).
					Where("goods_id = ?", g.GoodsId).
					Update("deleted", true).Error; err != nil {
					log.Log().Info("标记删除商品失败: "+strconv.Itoa(g.GoodsId), zap.Any("err", err))
				}
			}
		}(item)
	}

	// 等待所有goroutine完成
	for i := 0; i < maxConcurrent; i++ {
		sem <- struct{}{}
	}

	return nil
}

// 在goods.go文件中添加init函数

func init() {
	go func() {
		if collection.Collect(gva.GlobalAuth.Supply).Contains(common2.SUPPLY_WDT) == true || utils.LocalEnv() != true {
			var gatherSuppliers []model2.GatherSupply
			err := source.DB().Where("category_id = ?", common2.SUPPLY_WDT).Find(&gatherSuppliers).Error
			if err != nil {
				return
			}

			for _, gatherSupply := range gatherSuppliers {
				log.Log().Info("开始执行旺店通商品初始化: %v", zap.Any("data", gatherSupply))

				var setting model.WdtSetting
				err, setting = service.GetWdtSetting(gatherSupply.ID)
				if err != nil {
					continue
				}

				//检查是否已有数据
				var count int64
				if err = source.DB().Model(&model.WdtGoodsDetail{}).Count(&count).Error; err != nil {
					log.Log().Info("检查商品数据失败: %v", zap.Any("err", err))
					continue
				}

				var wdt = WdtGoods{}
				// 只有在没有数据时才执行初始化
				if count == 0 {
					log.Log().Info("开始执行旺店通商品初始化1: %v", zap.Any("data", gatherSupply))
					startTime := time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local)
					if err = wdt.SetBase(setting.Appkey, setting.Appsecret, setting.Sid, setting.DistributorAppkey, setting.DistributorAppsecret, setting.ShopId).GetHistoricalGoods(startTime); err != nil {
						log.Log().Info("获取商品历史数据失败", zap.Any("err", err))
						continue
					}
				}

				// 设置每小时执行一次
				//for {
				//	now := time.Now()
				//	next := time.Date(now.Year(), now.Month(), now.Day(), now.Hour()+1, 15, 0, 0, now.Location())
				//	timer := time.NewTimer(next.Sub(now))
				//	<-timer.C
				//
				//	if err = wdt.SetBase(setting.Appkey, setting.Appsecret, setting.Sid, setting.DistributorAppkey, setting.DistributorAppsecret, setting.ShopId).GetIncrementalGoods(); err != nil {
				//		log.Log().Info("同步商品数据失败: %v", zap.Any("err", err))
				//	}
				//}
			}
		}
	}()
}

// batchCreateGoodsWithSpecs 批量创建商品及其规格产品
func (wdt *WdtGoods) batchCreateGoodsWithSpecs(tx *gorm.DB, goodsList []*model.WdtGoodsDetail) error {
	if len(goodsList) == 0 {
		return nil
	}

	// 设置规格产品的商品ID
	for _, goods := range goodsList {
		if len(goods.GoodsSpecProducts) > 0 {
			for i := range goods.GoodsSpecProducts {
				goods.GoodsSpecProducts[i].GoodsId = goods.GoodsId
			}
		}
	}

	// 使用 GORM 的 FullSaveAssociations 批量创建商品及其关联的规格产品
	return tx.Session(&gorm.Session{FullSaveAssociations: true}).Create(goodsList).Error
}

// batchUpdateGoodsWithSpecs 批量更新商品及其规格产品
func (wdt *WdtGoods) batchUpdateGoodsWithSpecs(tx *gorm.DB, goodsList []*model.WdtGoodsDetail) error {
	if len(goodsList) == 0 {
		return nil
	}

	// 批量更新，使用 GORM 的关联功能
	for _, goods := range goodsList {
		// 设置规格产品的商品ID并重置ID
		if len(goods.GoodsSpecProducts) > 0 {
			for i := range goods.GoodsSpecProducts {
				goods.GoodsSpecProducts[i].GoodsId = goods.GoodsId
				goods.GoodsSpecProducts[i].ID = 0 // 重置ID，确保创建新记录
				goods.GoodsSpecProducts[i].CreatedAt = nil
				goods.GoodsSpecProducts[i].UpdatedAt = nil
			}
		}

		// 更新商品主体信息
		if err := tx.Model(goods).Updates(goods).Error; err != nil {
			return fmt.Errorf("更新商品失败 (goods_id: %d): %v", goods.GoodsId, err)
		}

		// 使用 Replace 替换关联的规格产品
		if len(goods.GoodsSpecProducts) > 0 {
			if err := tx.Model(goods).Association("GoodsSpecProducts").Replace(goods.GoodsSpecProducts); err != nil {
				return fmt.Errorf("替换规格产品失败 (goods_id: %d): %v", goods.GoodsId, err)
			}
		} else {
			// 如果没有规格产品，清空关联
			if err := tx.Model(goods).Association("GoodsSpecProducts").Clear(); err != nil {
				return fmt.Errorf("清空规格产品失败 (goods_id: %d): %v", goods.GoodsId, err)
			}
		}
		mq.PublishMessage(mq.WdtBatchHandleMessage{
			Ids: []int{goods.GoodsId},
		})
	}

	return nil
}
