package goods

import (
	"fmt"
	"log"
	"wdt-supply/common"
	"wdt-supply/model"
	"yz-go/source"
)

// StockChange 库存变化记录
type StockChange struct {
	RecID            string `json:"rec_id"`
	ShopID           string `json:"shop_id"`
	GoodsID          string `json:"goods_id"`
	SpecID           string `json:"spec_id"`
	SyncStock        int    `json:"sync_stock"`
	GoodsNo          string `json:"goods_no"`
	SpecNo           string `json:"spec_no"`
	ErpSpecType      string `json:"erp_spec_type"`
	ErpSpecID        string `json:"erp_spec_id"`
	StockChangeCount string `json:"stock_change_count"`
}

// SyncStock 同步库存
func (wdt *WdtGoods) SyncStock() {
	// 每1分钟执行一次
	go func() {
		if err := wdt.processStockSync(); err != nil {
			log.Printf("库存同步失败: %v", err)
		}
	}()
}

// processStockSync 处理库存同步
func (wdt *WdtGoods) processStockSync() error {
	// 获取库存变化
	stockChanges, err := wdt.fetchStockChanges()
	if err != nil {
		return fmt.Errorf("获取库存变化失败: %v", err)
	}

	if len(stockChanges) == 0 {
		return nil
	}

	// 更新本地数据库
	if err := wdt.updateLocalStock(stockChanges); err != nil {
		return fmt.Errorf("更新本地库存失败: %v", err)
	}

	// 确认库存同步
	if err := wdt.ackStockChanges(stockChanges); err != nil {
		return fmt.Errorf("确认库存同步失败: %v", err)
	}

	return nil
}

// fetchStockChanges 获取库存变化
func (wdt *WdtGoods) fetchStockChanges() ([]StockChange, error) {
	req := &common.WdtRequest{
		Sid:       wdt.Sid,
		Appkey:    wdt.AppKey,
		Appsecret: wdt.AppSecret,
		Params: map[string]interface{}{
			"limit": 1000, // 每次获取1000条
		},
	}

	resp, err := req.DoRequest("api_goods_stock_change_query.php", false)
	if err != nil {
		return nil, err
	}

	var stockChanges []StockChange
	if err := resp.GetListData("stock_change_list", &stockChanges); err != nil {
		return nil, err
	}

	return stockChanges, nil
}

// updateLocalStock 更新本地库存
func (wdt *WdtGoods) updateLocalStock(changes []StockChange) error {
	for _, change := range changes {
		if err := source.DB().Model(&model.WdtGoodsSpecProduct{}).
			Where("spec_id = ?", change.SpecID).
			Update("prd_number", change.SyncStock).Error; err != nil {
			return err
		}
	}
	return nil
}

// ackStockChanges 确认库存同步
func (wdt *WdtGoods) ackStockChanges(changes []StockChange) error {
	// 构建确认列表
	var syncList []map[string]interface{}
	for _, change := range changes {
		syncList = append(syncList, map[string]interface{}{
			"rec_id":             change.RecID,
			"sync_stock":         change.SyncStock,
			"stock_change_count": change.StockChangeCount,
		})
	}

	// 发送确认请求
	req := &common.WdtRequest{
		Sid:       wdt.Sid,
		Appkey:    wdt.AppKey,
		Appsecret: wdt.AppSecret,
		Params: map[string]interface{}{
			"stock_sync_list": syncList,
		},
	}

	_, err := req.DoRequest("api_goods_stock_change_ack.php", false)
	return err
}
