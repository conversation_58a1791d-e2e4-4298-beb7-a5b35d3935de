goods_brand_query.php（查询货品品牌）

¥基础 

1.接口说明

1.1 接口描述：批量获取旺店通ERP内货品品牌档案信息
1.2 适用版本：客户端 V2.4.5.0及以上版本   
1.3全量获取：该接口是全量获取数据，无法按照时间段增量获取数据。
2.调用场景

2.1 暂无
3.请求参数说明

   3.1 请求地址

环境	HTTP地址
测试环境	https://sandbox.wangdian.cn/openapi2/goods_brand_query.php
正式环境	https://api.wangdian.cn/openapi2/goods_brand_query.php
3.2 公共请求参数

名称	字段	类型	长度
必须	描述
卖家账号	sid	String	
是	购买ERP时由旺店通分配给ERP购买方，请从ERP购买方获取。
接口账号	appkey	String	
是	本开放平台“自助对接”功能模块内自助申请，获取方式点击这里
时间戳	timestamp	int	
是	北京时间1970-01-01 08:00:00起至现在的总秒数，10位int值,旺店通企业版API服务端允许请求最大时间误差为5min，date.timezone = Asia/Shanghai。
签名	sign	String	
是	API输入参数签名结果，签名算法介绍单击这里
   3.3 业务请求参数

名称	字段	类型	长度	必须	描述
分页大小	page_size	int	10	否	每页返回的数据条数，输入值范围1~100，不传本参数输入值默认为40，使用举例单击这里
页号	page_no	int	10	否	不传值默认从0页开始
4.响应参数

4.1 公共响应参数

名称	字段	类型	长度
必须	描述
错误码	code	int	11	是	状态码:0表示成功,其他表示失败
错误描述	message	varchar	255	是	错误描述
总条数	total_count	varchar	255
是	符合条件的数据条数，用来分页 当page_no = 0时返回
品牌列表数据	brand_lists	data[ ]	
是	品牌列表详细数据
4.2业务响应参数

  brand_lists

名称
字段	类型	长度	必须
描述
品牌id	brand_id	varchar	4
是	品牌ID
品牌编号	brand_no	varchar	20	是	品牌编号
品牌名称	brand_name	varchar	255	是	品牌名称
备注	remark	varchar	255	是	备注
销售增长率类型	sales_rate_type	varchar	4	是	销售增长率类型
动态销售增长率	sales_rate_cycle	int	11	是	动态销售增长率计算周期
动态销售增长率计算周期（前A日）	purchase_computing_cycle	int	11	是	动态销售增长率计算周期（前A日）
动态销售增长率计算周期（前B日）	purchase_computing_cycle1	int	11	是	动态销售增长率计算周期（前B日）
固定销售增长率	sales_rate	varchar	50	是	固定销售增长率
警戒库存类型	alarm_type	varchar	50	是	警戒库存类型
警戒库存天数	alarm_days	varchar	50	是	警戒库存天数
最大警戒天数	alarm_days1	varchar	255	是	最大警戒天数
是否停用	is_disabled	varchar	32	是	是否停用，0表示正常，1表示停用
最后修改时间	modified	varchar	40	是	最后修改时间
创建时间	created	varchar	40	是	创建时间
5.请求示例


PHP
JAVA
C#
python
<?php
    require_once("../WdtClient.php");
    $c = new WdtClient;
    $c->sid = '';
    $c->appkey = '';
    $c->appsecret = '';
    $c->gatewayUrl = 'http://sandbox.wangdian.cn/openapi2/goods_brand_query.php';
     
    $c->putApiParam('page_no', 0);
    $c->putApiParam('page_size', 100);
         
    $json = $c->wdtOpenApi();
    var_dump($json);
?>
6.响应示例
   6.1 正常响应示例 
JSON
{
    "code": "0",
    "total_count": "1",
    "shoplist": [{
        "platform_id": "127",
        "sub_platform_id": "0",
        "shop_id": "66",
        "shop_no": "xyp2test",
        "shop_name": "小鹏测试店铺",
        "account_id": "",
        "account_nick": "",
        "province": "110000",
        "city": "110100",
        "district": "110108",
        "address": "花园路13号",
        "contact": "小鹏",
        "zip": "100089 ",
        "mobile": "***********",
        "telno": "010-********",
        "remark": "123"
    }]
}
   6.2 异常响应示例
JSON
{
    "code": 1007,
    "message": "接口appkey已停用【解决办法：联系商务人员，重新开启接口appkey】 "
}