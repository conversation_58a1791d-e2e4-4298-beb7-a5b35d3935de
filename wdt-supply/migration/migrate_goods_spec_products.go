package migration

import (
	"encoding/json"
	"fmt"
	"wdt-supply/model"

	"go.uber.org/zap"
	"yz-go/component/log"
	"yz-go/source"
)

// MigrateGoodsSpecProducts 迁移商品规格产品数据到独立表
func MigrateGoodsSpecProducts() error {
	log.Log().Info("开始迁移商品规格产品数据到独立表")

	// 1. 创建新表
	if err := source.DB().AutoMigrate(&model.WdtGoodsSpecProduct{}); err != nil {
		log.Log().Error("创建商品规格产品表失败", zap.Error(err))
		return err
	}

	// 2. 查询所有商品详情
	var goodsDetails []model.WdtGoodsDetail
	if err := source.DB().Find(&goodsDetails).Error; err != nil {
		log.Log().Error("查询商品详情失败", zap.Error(err))
		return err
	}

	log.Log().Info("找到商品详情记录", zap.Int("数量", len(goodsDetails)))

	// 3. 迁移数据
	migratedCount := 0
	for _, goods := range goodsDetails {
		// 从 JSON 字段中解析规格产品数据
		var specProducts []model.GoodsSpecProduct
		
		// 这里需要直接从数据库字段中读取 JSON 数据
		var rawData struct {
			GoodsSpecProducts json.RawMessage `gorm:"column:goods_spec_products"`
		}
		
		if err := source.DB().Table("wdt_goods_details").
			Select("goods_spec_products").
			Where("id = ?", goods.ID).
			Scan(&rawData).Error; err != nil {
			log.Log().Error("读取商品规格产品数据失败", 
				zap.Uint("商品ID", goods.ID), 
				zap.Error(err))
			continue
		}

		// 解析 JSON 数据
		if len(rawData.GoodsSpecProducts) > 0 {
			if err := json.Unmarshal(rawData.GoodsSpecProducts, &specProducts); err != nil {
				log.Log().Error("解析商品规格产品JSON失败", 
					zap.Uint("商品ID", goods.ID), 
					zap.Error(err))
				continue
			}
		}

		// 如果有规格产品数据，则迁移到新表
		if len(specProducts) > 0 {
			if err := model.BatchCreateSpecProducts(goods.GoodsId, specProducts); err != nil {
				log.Log().Error("批量创建商品规格产品失败", 
					zap.Int("商品ID", goods.GoodsId), 
					zap.Error(err))
				continue
			}
			migratedCount++
			log.Log().Info("成功迁移商品规格产品", 
				zap.Int("商品ID", goods.GoodsId), 
				zap.Int("规格产品数量", len(specProducts)))
		}
	}

	log.Log().Info("商品规格产品数据迁移完成", 
		zap.Int("迁移商品数量", migratedCount))

	return nil
}

// DropGoodsSpecProductsColumn 删除原表中的 goods_spec_products 列（可选）
func DropGoodsSpecProductsColumn() error {
	log.Log().Info("开始删除原表中的 goods_spec_products 列")

	// 注意：这个操作是不可逆的，请确保数据已经成功迁移
	sql := "ALTER TABLE wdt_goods_details DROP COLUMN goods_spec_products"
	if err := source.DB().Exec(sql).Error; err != nil {
		log.Log().Error("删除 goods_spec_products 列失败", zap.Error(err))
		return err
	}

	log.Log().Info("成功删除 goods_spec_products 列")
	return nil
}

// RollbackMigration 回滚迁移（将独立表的数据合并回原表）
func RollbackMigration() error {
	log.Log().Info("开始回滚商品规格产品数据迁移")

	// 1. 查询所有商品详情
	var goodsDetails []model.WdtGoodsDetail
	if err := source.DB().Find(&goodsDetails).Error; err != nil {
		log.Log().Error("查询商品详情失败", zap.Error(err))
		return err
	}

	// 2. 为每个商品重新构建 goods_spec_products 字段
	for _, goods := range goodsDetails {
		// 查询该商品的所有规格产品
		var specProducts []model.WdtGoodsSpecProduct
		if err := source.DB().Where("goods_id = ?", goods.GoodsId).Find(&specProducts).Error; err != nil {
			log.Log().Error("查询商品规格产品失败", 
				zap.Int("商品ID", goods.GoodsId), 
				zap.Error(err))
			continue
		}

		// 转换为 GoodsSpecProduct 格式
		var goodsSpecProducts []model.GoodsSpecProduct
		for _, sp := range specProducts {
			gsp := model.GoodsSpecProduct{
				PrdSn:              sp.PrdSn,
				PrdDesc:            sp.PrdDesc,
				PrdPrice:           sp.PrdPrice,
				SpecId:             sp.SpecId,
				Length:             sp.Length,
				Width:              sp.Width,
				Height:             sp.Height,
				PrdNumber:          sp.PrdNumber,
				PrdWeight:          sp.PrdWeight,
				ProductImages:      sp.ProductImages,
				IsSale:             sp.IsSale,
				UpdateTime:         sp.UpdateTime,
				SpecBarcode:        sp.SpecBarcode,
				SuggestedPrice:     sp.SuggestedPrice,
				DistributionPrice:  sp.DistributionPrice,
				DeletedStatus:      sp.DeletedStatus,
				ControlMaxPrice:    sp.ControlMaxPrice,
				ControlMinPrice:    sp.ControlMinPrice,
				LeftoverStockPrice: sp.LeftoverStockPrice,
			}
			goodsSpecProducts = append(goodsSpecProducts, gsp)
		}

		// 序列化为 JSON 并更新到原表
		if len(goodsSpecProducts) > 0 {
			jsonData, err := json.Marshal(goodsSpecProducts)
			if err != nil {
				log.Log().Error("序列化商品规格产品失败", 
					zap.Int("商品ID", goods.GoodsId), 
					zap.Error(err))
				continue
			}

			// 更新原表
			if err := source.DB().Model(&goods).Update("goods_spec_products", string(jsonData)).Error; err != nil {
				log.Log().Error("更新商品规格产品字段失败", 
					zap.Int("商品ID", goods.GoodsId), 
					zap.Error(err))
				continue
			}

			log.Log().Info("成功回滚商品规格产品数据", 
				zap.Int("商品ID", goods.GoodsId), 
				zap.Int("规格产品数量", len(goodsSpecProducts)))
		}
	}

	log.Log().Info("商品规格产品数据回滚完成")
	return nil
}

// RunMigration 执行完整的迁移流程
func RunMigration() error {
	fmt.Println("=== 商品规格产品数据迁移 ===")
	fmt.Println("1. 迁移数据到独立表")
	fmt.Println("2. 删除原表列（可选，谨慎操作）")
	fmt.Println("3. 回滚迁移（可选）")
	fmt.Println()

	// 执行迁移
	if err := MigrateGoodsSpecProducts(); err != nil {
		return fmt.Errorf("迁移失败: %v", err)
	}

	fmt.Println("迁移完成！")
	fmt.Println("如果确认数据迁移正确，可以调用 DropGoodsSpecProductsColumn() 删除原表列")
	fmt.Println("如果需要回滚，可以调用 RollbackMigration()")

	return nil
}
