package migration

import (
	"fmt"
	"wdt-supply/model"

	"go.uber.org/zap"
	"yz-go/component/log"
	"yz-go/source"
)

// SetupAssociations 设置一对多关联关系
func SetupAssociations() error {
	log.Log().Info("开始设置一对多关联关系")

	// 1. 自动迁移表结构
	if err := source.DB().AutoMigrate(&model.WdtGoodsDetail{}, &model.WdtGoodsSpecProduct{}); err != nil {
		log.Log().Error("自动迁移表结构失败", zap.Error(err))
		return err
	}

	// 2. 添加外键约束（可选，GORM 会自动处理关联）
	// 注意：在生产环境中添加外键约束需要谨慎，可能会影响性能
	if err := addForeignKeyConstraints(); err != nil {
		log.Log().Warn("添加外键约束失败，但不影响关联功能", zap.Error(err))
		// 不返回错误，因为外键约束是可选的
	}

	// 3. 添加索引优化查询性能
	if err := addIndexes(); err != nil {
		log.Log().Error("添加索引失败", zap.Error(err))
		return err
	}

	log.Log().Info("一对多关联关系设置完成")
	return nil
}

// addForeignKeyConstraints 添加外键约束
func addForeignKeyConstraints() error {
	// 检查外键是否已存在
	var count int64
	source.DB().Raw(`
		SELECT COUNT(*) 
		FROM information_schema.KEY_COLUMN_USAGE 
		WHERE TABLE_SCHEMA = DATABASE() 
		AND TABLE_NAME = 'wdt_goods_spec_products' 
		AND CONSTRAINT_NAME = 'fk_wdt_goods_spec_products_goods_id'
	`).Scan(&count)

	if count == 0 {
		// 添加外键约束
		sql := `
			ALTER TABLE wdt_goods_spec_products 
			ADD CONSTRAINT fk_wdt_goods_spec_products_goods_id 
			FOREIGN KEY (goods_id) REFERENCES wdt_goods_details(goods_id) 
			ON DELETE CASCADE ON UPDATE CASCADE
		`
		if err := source.DB().Exec(sql).Error; err != nil {
			return fmt.Errorf("添加外键约束失败: %v", err)
		}
		log.Log().Info("成功添加外键约束")
	} else {
		log.Log().Info("外键约束已存在，跳过")
	}

	return nil
}

// addIndexes 添加索引
func addIndexes() error {
	indexes := []struct {
		table string
		name  string
		sql   string
	}{
		{
			table: "wdt_goods_details",
			name:  "idx_goods_id",
			sql:   "CREATE INDEX IF NOT EXISTS idx_goods_id ON wdt_goods_details(goods_id)",
		},
		{
			table: "wdt_goods_spec_products",
			name:  "idx_goods_id",
			sql:   "CREATE INDEX IF NOT EXISTS idx_goods_id ON wdt_goods_spec_products(goods_id)",
		},
		{
			table: "wdt_goods_spec_products",
			name:  "idx_spec_id",
			sql:   "CREATE INDEX IF NOT EXISTS idx_spec_id ON wdt_goods_spec_products(spec_id)",
		},
		{
			table: "wdt_goods_spec_products",
			name:  "idx_prd_sn",
			sql:   "CREATE INDEX IF NOT EXISTS idx_prd_sn ON wdt_goods_spec_products(prd_sn)",
		},
		{
			table: "wdt_goods_spec_products",
			name:  "idx_is_sale",
			sql:   "CREATE INDEX IF NOT EXISTS idx_is_sale ON wdt_goods_spec_products(is_sale)",
		},
		{
			table: "wdt_goods_spec_products",
			name:  "idx_goods_id_is_sale",
			sql:   "CREATE INDEX IF NOT EXISTS idx_goods_id_is_sale ON wdt_goods_spec_products(goods_id, is_sale)",
		},
	}

	for _, idx := range indexes {
		if err := source.DB().Exec(idx.sql).Error; err != nil {
			log.Log().Error("创建索引失败", 
				zap.String("表名", idx.table), 
				zap.String("索引名", idx.name), 
				zap.Error(err))
			return err
		}
		log.Log().Info("成功创建索引", 
			zap.String("表名", idx.table), 
			zap.String("索引名", idx.name))
	}

	return nil
}

// ValidateAssociations 验证关联关系是否正常工作
func ValidateAssociations() error {
	log.Log().Info("开始验证关联关系")

	// 1. 创建测试数据
	testGoods := model.WdtGoodsDetail{
		GoodsId:   999999,
		GoodsName: "关联测试商品",
		GoodsSn:   "ASSOC_TEST",
		GoodsPrice: 99.99,
	}

	if err := source.DB().Create(&testGoods).Error; err != nil {
		return fmt.Errorf("创建测试商品失败: %v", err)
	}

	testSpecs := []model.WdtGoodsSpecProduct{
		{
			GoodsId:  testGoods.GoodsId,
			PrdSn:    "ASSOC_TEST_001",
			PrdDesc:  "测试规格1",
			PrdPrice: 99.99,
			SpecId:   "test_spec_001",
			IsSale:   true,
		},
		{
			GoodsId:  testGoods.GoodsId,
			PrdSn:    "ASSOC_TEST_002",
			PrdDesc:  "测试规格2",
			PrdPrice: 89.99,
			SpecId:   "test_spec_002",
			IsSale:   false,
		},
	}

	if err := source.DB().Create(&testSpecs).Error; err != nil {
		return fmt.Errorf("创建测试规格产品失败: %v", err)
	}

	// 2. 测试正向关联查询
	var goodsWithSpecs model.WdtGoodsDetail
	if err := source.DB().Preload("SpecProducts").Where("goods_id = ?", testGoods.GoodsId).First(&goodsWithSpecs).Error; err != nil {
		return fmt.Errorf("正向关联查询失败: %v", err)
	}

	if len(goodsWithSpecs.SpecProducts) != 2 {
		return fmt.Errorf("正向关联查询结果不正确，期望2个规格产品，实际%d个", len(goodsWithSpecs.SpecProducts))
	}

	// 3. 测试反向关联查询
	var specWithGoods model.WdtGoodsSpecProduct
	if err := source.DB().Preload("GoodsDetail").Where("spec_id = ?", "test_spec_001").First(&specWithGoods).Error; err != nil {
		return fmt.Errorf("反向关联查询失败: %v", err)
	}

	if specWithGoods.GoodsDetail == nil || specWithGoods.GoodsDetail.GoodsId != testGoods.GoodsId {
		return fmt.Errorf("反向关联查询结果不正确")
	}

	// 4. 测试条件关联查询
	var goodsWithOnSaleSpecs model.WdtGoodsDetail
	if err := source.DB().Preload("SpecProducts", "is_sale = ?", true).Where("goods_id = ?", testGoods.GoodsId).First(&goodsWithOnSaleSpecs).Error; err != nil {
		return fmt.Errorf("条件关联查询失败: %v", err)
	}

	if len(goodsWithOnSaleSpecs.SpecProducts) != 1 {
		return fmt.Errorf("条件关联查询结果不正确，期望1个在售规格产品，实际%d个", len(goodsWithOnSaleSpecs.SpecProducts))
	}

	// 5. 测试关联统计
	count := source.DB().Model(&testGoods).Association("SpecProducts").Count()
	if count != 2 {
		return fmt.Errorf("关联统计结果不正确，期望2个规格产品，实际%d个", count)
	}

	// 6. 清理测试数据
	source.DB().Where("goods_id = ?", testGoods.GoodsId).Delete(&model.WdtGoodsSpecProduct{})
	source.DB().Where("goods_id = ?", testGoods.GoodsId).Delete(&model.WdtGoodsDetail{})

	log.Log().Info("关联关系验证通过")
	return nil
}

// OptimizeAssociationQueries 优化关联查询性能
func OptimizeAssociationQueries() error {
	log.Log().Info("开始优化关联查询性能")

	// 1. 分析表结构
	if err := analyzeTableStructure(); err != nil {
		log.Log().Error("分析表结构失败", zap.Error(err))
		return err
	}

	// 2. 优化查询建议
	suggestions := []string{
		"使用 Preload 预加载关联数据，避免 N+1 查询问题",
		"在频繁查询的字段上添加索引，如 goods_id, spec_id, is_sale",
		"使用 Select 指定需要的字段，减少数据传输量",
		"对于大量数据，考虑使用分页查询",
		"使用 Joins 代替 Preload 进行联表查询，减少查询次数",
		"在条件查询中使用索引字段作为 WHERE 条件",
	}

	fmt.Println("\n=== 关联查询性能优化建议 ===")
	for i, suggestion := range suggestions {
		fmt.Printf("%d. %s\n", i+1, suggestion)
	}

	log.Log().Info("关联查询性能优化完成")
	return nil
}

// analyzeTableStructure 分析表结构
func analyzeTableStructure() error {
	tables := []string{"wdt_goods_details", "wdt_goods_spec_products"}

	for _, table := range tables {
		var count int64
		if err := source.DB().Table(table).Count(&count).Error; err != nil {
			return err
		}

		fmt.Printf("表 %s: %d 条记录\n", table, count)

		// 显示索引信息
		var indexes []struct {
			Table      string `json:"table"`
			NonUnique  int    `json:"non_unique"`
			KeyName    string `json:"key_name"`
			SeqInIndex int    `json:"seq_in_index"`
			ColumnName string `json:"column_name"`
		}

		if err := source.DB().Raw("SHOW INDEX FROM " + table).Scan(&indexes).Error; err != nil {
			log.Log().Warn("获取索引信息失败", zap.String("表名", table), zap.Error(err))
			continue
		}

		fmt.Printf("表 %s 的索引:\n", table)
		for _, idx := range indexes {
			fmt.Printf("  - %s (%s)\n", idx.KeyName, idx.ColumnName)
		}
		fmt.Println()
	}

	return nil
}

// RunAssociationSetup 运行完整的关联设置流程
func RunAssociationSetup() error {
	fmt.Println("=== WdtGoodsDetail 和 WdtGoodsSpecProduct 一对多关联设置 ===")

	// 1. 设置关联关系
	if err := SetupAssociations(); err != nil {
		return fmt.Errorf("设置关联关系失败: %v", err)
	}

	// 2. 验证关联关系
	if err := ValidateAssociations(); err != nil {
		return fmt.Errorf("验证关联关系失败: %v", err)
	}

	// 3. 优化查询性能
	if err := OptimizeAssociationQueries(); err != nil {
		return fmt.Errorf("优化查询性能失败: %v", err)
	}

	fmt.Println("\n=== 关联设置完成 ===")
	fmt.Println("现在可以使用以下方式查询关联数据：")
	fmt.Println("1. source.DB().Preload(\"SpecProducts\").Find(&goods)")
	fmt.Println("2. source.DB().Preload(\"GoodsDetail\").Find(&specs)")
	fmt.Println("3. source.DB().Model(&goods).Association(\"SpecProducts\").Find(&specs)")

	return nil
}
