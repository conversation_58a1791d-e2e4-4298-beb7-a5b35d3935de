package model

// WdtBrand 旺店通品牌
type WdtBrand struct {
	BrandID                 string `json:"brand_id" gorm:"column:brand_id;primaryKey"`
	BrandNo                 string `json:"brand_no" gorm:"column:brand_no"`
	BrandName               string `json:"brand_name" gorm:"column:brand_name"`
	Remark                  string `json:"remark" gorm:"column:remark"`
	SalesRateType           string `json:"sales_rate_type" gorm:"column:sales_rate_type"`
	SalesRateCycle          int    `json:"sales_rate_cycle" gorm:"column:sales_rate_cycle"`
	PurchaseComputingCycle  int    `json:"purchase_computing_cycle" gorm:"column:purchase_computing_cycle"`
	PurchaseComputingCycle1 int    `json:"purchase_computing_cycle1" gorm:"column:purchase_computing_cycle1"`
	SalesRate               string `json:"sales_rate" gorm:"column:sales_rate"`
	AlarmType               string `json:"alarm_type" gorm:"column:alarm_type"`
	AlarmDays               string `json:"alarm_days" gorm:"column:alarm_days"`
	AlarmDays1              string `json:"alarm_days1" gorm:"column:alarm_days1"`
	IsDisabled              string `json:"is_disabled" gorm:"column:is_disabled"`
	Modified                string `json:"modified" gorm:"column:modified"`
	Created                 string `json:"created" gorm:"column:created"`
}
