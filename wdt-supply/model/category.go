package model

// WdtCategory 旺店通商品分类
type WdtCategory struct {
	ClassID   string `json:"class_id" gorm:"column:class_id;primaryKey"`
	ParentID  string `json:"parent_id" gorm:"column:parent_id"`
	IsLeaf    string `json:"is_leaf" gorm:"column:is_leaf"`
	ClassName string `json:"class_name" gorm:"column:class_name"`
	Path      string `json:"path" gorm:"column:path"`
	Modified  string `json:"modified" gorm:"column:modified"`
	Created   string `json:"created" gorm:"column:created"`
}
