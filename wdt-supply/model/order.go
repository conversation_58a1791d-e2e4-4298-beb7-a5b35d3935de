package model

// WdtOrder 旺店通订单
type WdtOrder struct {
	Tid              string          `json:"tid"`               // 原始单号
	TradeStatus      int             `json:"trade_status"`      // 平台状态
	PayStatus        int             `json:"pay_status"`        // 支付状态
	DeliveryTerm     int             `json:"delivery_term"`     // 发货条件
	TradeTime        string          `json:"trade_time"`        // 下单时间
	PayTime          string          `json:"pay_time"`          // 支付时间
	FenxiaoType      int             `json:"fenxiao_type"`      // 分销类别 1:代销 2:经销
	PurchaseID       string          `json:"purchase_id"`       // 转供销情况下，采购单ID
	FenxiaoNick      string          `json:"fenxiao_nick"`      // 分销订单的分销商id,或转供销时供应商id
	BuyerNick        string          `json:"buyer_nick"`        // 客户网名
	BuyerEmail       string          `json:"buyer_email"`       // 买家email
	PayID            string          `json:"pay_id"`            // 支付单号
	PayAccount       string          `json:"pay_account"`       // 支付账号
	PayMethod        int             `json:"pay_method"`        // 支付方式
	ReceiverName     string          `json:"receiver_name"`     // 收件人
	ReceiverProvince string          `json:"receiver_province"` // 省份
	ReceiverCity     string          `json:"receiver_city"`     // 城市
	ReceiverDistrict string          `json:"receiver_district"` // 区县
	ReceiverAddress  string          `json:"receiver_address"`  // 地址详情
	ReceiverMobile   string          `json:"receiver_mobile"`   // 手机
	ReceiverTelno    string          `json:"receiver_telno"`    // 电话
	ReceiverZip      string          `json:"receiver_zip"`      // 邮编
	LogisticsType    int             `json:"logistics_type"`    // 物流方式
	InvoiceKind      int             `json:"invoice_kind"`      // 发票类别
	InvoiceTitle     string          `json:"invoice_title"`     // 发票抬头
	InvoiceContent   string          `json:"invoice_content"`   // 发票内容
	RemarkFlag       int             `json:"remark_flag"`       // 客户备注标旗
	BuyerMessage     string          `json:"buyer_message"`     // 买家备注
	SellerMemo       string          `json:"seller_memo"`       // 商家客服备注
	SellerFlag       int             `json:"seller_flag"`       // 客服标旗
	PostAmount       float64         `json:"post_amount"`       // 邮费
	CodAmount        float64         `json:"cod_amount"`        // 货到付款金额
	ExtCodFee        float64         `json:"ext_cod_fee"`       // 货到付款买家费用
	Paid             float64         `json:"paid"`              // 已付金额
	IDCardType       int             `json:"id_card_type"`      // 证件类型
	IDCard           string          `json:"id_card"`           // 证件号码
	IsAutoWms        int             `json:"is_auto_wms"`       // 是否为自动流转模式
	OrderList        []WdtOrderGoods `json:"order_list"`        // 订单商品列表
	OtherAmount      float64         `json:"other_amount"`      // 其它收费
}

// WdtOrderGoods 订单商品明细
type WdtOrderGoods struct {
	Oid           string  `json:"oid"`            // 子订单编号
	Status        int     `json:"status"`         // 状态
	RefundStatus  int     `json:"refund_status"`  // 退款状态
	GoodsId       string  `json:"goods_id"`       // 平台货品ID
	SpecId        string  `json:"spec_id"`        // 平台规格ID
	GoodsNo       string  `json:"goods_no"`       // 货品编码
	SpecNo        string  `json:"spec_no"`        // 规格编码
	GoodsName     string  `json:"goods_name"`     // 货品名称
	SpecName      string  `json:"spec_name"`      // 规格名称
	Num           float64 `json:"num"`            // 数量
	Price         float64 `json:"price"`          // 单价
	AdjustAmount  float64 `json:"adjust_amount"`  // 调整金额
	Discount      float64 `json:"discount"`       // 优惠
	ShareDiscount float64 `json:"share_discount"` // 分摊优惠
}
