package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"yz-go/source"
)

// WdtGoodsDetail 商品主表
type WdtGoodsDetail struct {
	source.Model

	GoodsId           int      `json:"goods_id"`
	ImgUrl            string   `json:"img_url"`
	GoodsName         string   `json:"goods_name"`
	GoodsSn           string   `json:"goods_sn"`
	GoodsPrice        float64  `json:"goods_price"`
	Description       string   `json:"description"`
	Summary           string   `json:"summary"`
	MaterialVideoInfo []string `json:"material_video_info"`
	CategoryList      []string `json:"category_list"`
	GoodsImages       []struct {
		ImgId  int    `json:"img_id"`
		ImgUrl string `json:"img_url"`
	} `json:"goods_images"`
	WhiteBackGroundImg []string `json:"white_back_ground_img"`
	LongGraphImg       []struct {
		ImgId  int    `json:"img_id"`
		ImgUrl string `json:"img_url"`
	} `json:"long_graph_img"`
	ThreeToFourItemImages []struct {
		ImgId  int    `json:"img_id"`
		ImgUrl string `json:"img_url"`
	} `json:"three_to_four_item_images"`
	GoodsSpecs []struct {
		SpecName    string   `json:"spec_name"`
		SpecValName []string `json:"spec_val_name"`
	} `json:"goods_specs"`
	UpdateTime        string `json:"update_time"`
	GoodsSpecProducts []struct {
		PrdSn              string   `json:"prd_sn"`
		PrdDesc            string   `json:"prd_desc"`
		PrdPrice           float64  `json:"prd_price"`
		SpecId             string   `json:"spec_id"`
		Length             float64  `json:"length"`
		Width              float64  `json:"width"`
		Height             float64  `json:"height"`
		PrdNumber          float64  `json:"prd_number"`
		PrdWeight          float64  `json:"prd_weight"`
		ProductImages      []string `json:"product_images"`
		IsSale             bool     `json:"is_sale"`
		UpdateTime         string   `json:"update_time"`
		SpecBarcode        string   `json:"spec_barcode"`
		SuggestedPrice     float64  `json:"suggested_price"`
		DistributionPrice  int      `json:"distribution_price"`
		DeletedStatus      int      `json:"deleted_status"`
		ControlMaxPrice    float64  `json:"control_max_price"`
		ControlMinPrice    float64  `json:"control_min_price"`
		LeftoverStockPrice float64  `json:"leftover_stock_price"`
	} `json:"goods_spec_products"`
}

// 添加 BeforeSave 钩子方法
//func (goods *GoodsSpecProducts) BeforeSave(tx *gorm.DB) error {
//	if len(goods.SpecList) > 0 {
//		// 初始化最小价格为第一个规格的价格
//		minPriceSpec := goods.SpecList[0]
//
//		// 遍历所有规格，找出会员价最低的规格
//		for _, spec := range goods.SpecList {
//			if spec.MemberPrice < minPriceSpec.MemberPrice {
//				minPriceSpec = spec
//			}
//		}
//
//		// 使用会员价最低的规格的价格作为商品主表价格
//		goods.LowestPrice = minPriceSpec.LowestPrice
//		goods.RetailPrice = minPriceSpec.RetailPrice
//		goods.WholesalePrice = minPriceSpec.WholesalePrice
//		goods.MemberPrice = minPriceSpec.MemberPrice
//		goods.MarketPrice = minPriceSpec.MarketPrice
//		goods.CustomPrice1 = minPriceSpec.CustomPrice1
//		goods.CustomPrice2 = minPriceSpec.CustomPrice2
//		goods.CostPrice = minPriceSpec.CostPrice
//	}
//	return nil
//}

// WdtGoodsSpec 商品规格表
type WdtGoodsSpec struct {
	PrdSn              string   `json:"prd_sn"`
	PrdDesc            string   `json:"prd_desc"`
	PrdPrice           float64  `json:"prd_price"`
	SpecId             string   `json:"spec_id"`
	Length             float64  `json:"length"`
	Width              float64  `json:"width"`
	Height             float64  `json:"height"`
	PrdNumber          float64  `json:"prd_number"`
	PrdWeight          float64  `json:"prd_weight"`
	ProductImages      []string `json:"product_images"`
	IsSale             bool     `json:"is_sale"`
	UpdateTime         string   `json:"update_time"`
	SpecBarcode        string   `json:"spec_barcode"`
	SuggestedPrice     float64  `json:"suggested_price"`
	DistributionPrice  int      `json:"distribution_price"`
	DeletedStatus      int      `json:"deleted_status"`
	ControlMaxPrice    float64  `json:"control_max_price"`
	ControlMinPrice    float64  `json:"control_min_price"`
	LeftoverStockPrice float64  `json:"leftover_stock_price"`
}

type WdtGoodsList struct {
	GoodsId             int         `json:"goods_id"`
	ImgUrl              string      `json:"img_url"`
	GoodsName           string      `json:"goods_name"`
	GoodsSn             string      `json:"goods_sn"`
	CreateTime          string      `json:"create_time"`
	SupplierCompanyName string      `json:"supplier_company_name"`
	SupCompanyAlias     string      `json:"sup_company_alias"`
	SupplierShopId      interface{} `json:"supplier_shop_id"`
	SupplierNickNo      interface{} `json:"supplier_nick_no"`
	SupplierGoodsId     int         `json:"supplier_goods_id"`
	SupplierSysId       int         `json:"supplier_sys_id"`
	Tail                interface{} `json:"tail"`
	BrandName           string      `json:"brand_name"`
	Skus                []struct {
		SupplierItemId                 int           `json:"supplier_item_id"`
		PrdSn                          interface{}   `json:"prd_sn"`
		PrdDesc                        string        `json:"prd_desc"`
		SkuTagDetail                   string        `json:"sku_tag_detail"`
		Tail                           interface{}   `json:"tail"`
		GoodsTagList                   []interface{} `json:"goods_tag_list"`
		SpecId                         string        `json:"spec_id"`
		DistributionPrice              float64       `json:"distributionPrice"`
		DeletedStatus                  int           `json:"deletedStatus"`
		DistributionPriceChangedStatus int           `json:"distribution_price_changed_status"`
		DeletedChangedStatus           interface{}   `json:"deleted_changed_status"`
		IsSale                         bool          `json:"is_sale"`
		ControlMaxPrice                interface{}   `json:"control_max_price"`
		ControlMinPrice                interface{}   `json:"control_min_price"`
		TieredPriceList                []interface{} `json:"tiered_price_list"`
		LeftoverStockPrice             interface{}   `json:"leftover_stock_price"`
		SpecBarcode                    string        `json:"spec_barcode"`
		Weight                         float64       `json:"weight"`
		SuggestedPrice                 interface{}   `json:"suggested_price"`
	} `json:"skus"`
	GoodsTags   []interface{} `json:"goods_tags"`
	PublishShop struct {
		PublishShopNum        int `json:"publish_shop_num"`
		PublishSuccessShopNum int `json:"publish_success_shop_num"`
	} `json:"publish_shop"`
	PriceControl        bool    `json:"price_control"`
	IsSale              bool    `json:"is_sale"`
	DeleteStatus        int     `json:"delete_status"`
	MinDistributorPrice float64 `json:"min_distributor_price"`
}

type WdtStock struct {
	SpecID        string  `json:"spec_id"`
	GoodsNo       string  `json:"goods_no"`
	SpecNo        string  `json:"spec_no"`
	WarehouseID   string  `json:"warehouse_id"`
	WarehouseNo   string  `json:"warehouse_no"`
	WarehouseName string  `json:"warehouse_name"`
	StockNum      float64 `json:"stock_num"`
	LockNum       float64 `json:"lock_num"`
	AvailableNum  float64 `json:"avaliable_num"`
	UnpayNum      float64 `json:"unpay_num"`
	SubscribeNum  float64 `json:"subscribe_num"`
	OrderNum      float64 `json:"order_num"`
	SendingNum    float64 `json:"sending_num"`
	PurchaseNum   float64 `json:"purchase_num"`
	TransferNum   float64 `json:"transfer_num"`
	ToTransferNum float64 `json:"to_transfer_num"`
	ToPurchaseNum float64 `json:"to_purchase_num"`
	CostPrice     float64 `json:"cost_price"`
	Modified      string  `json:"modified"`
}

// 在 WdtGoodsSpec 结构中添加库存字段
