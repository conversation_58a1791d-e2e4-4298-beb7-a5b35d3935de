package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"yz-go/source"
)

// StringSlice 自定义字符串切片类型
type StringSlice []string

// Value 实现 driver.Valuer 接口
func (s StringSlice) Value() (driver.Value, error) {
	if len(s) == 0 {
		return "[]", nil
	}
	return json.Marshal(s)
}

// Scan 实现 sql.Scanner 接口
func (s *StringSlice) Scan(value interface{}) error {
	if value == nil {
		*s = StringSlice{}
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into StringSlice", value)
	}

	return json.Unmarshal(bytes, s)
}

// ImageInfo 图片信息结构
type ImageInfo struct {
	ImgId  int    `json:"img_id"`
	ImgUrl string `json:"img_url"`
}

// ImageSlice 自定义图片切片类型
type ImageSlice []ImageInfo

// Value 实现 driver.Valuer 接口
func (i ImageSlice) Value() (driver.Value, error) {
	if len(i) == 0 {
		return "[]", nil
	}
	return json.Marshal(i)
}

// Scan 实现 sql.Scanner 接口
func (i *ImageSlice) Scan(value interface{}) error {
	if value == nil {
		*i = ImageSlice{}
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into ImageSlice", value)
	}

	return json.Unmarshal(bytes, i)
}

// ImageInfo 图片信息结构
type CategoryInfo struct {
	CategoryId   int    `json:"category_id"`
	NodeIdPath   string `json:"node_id_path"`
	NodeNamePath string `json:"node_name_path"`
	HasChildren  int    `json:"has_children"`
	Level        int    `json:"level"`
}

// ImageSlice 自定义图片切片类型
type CategorySlice []CategoryInfo

// Value 实现 driver.Valuer 接口
func (i CategorySlice) Value() (driver.Value, error) {
	if len(i) == 0 {
		return "[]", nil
	}
	return json.Marshal(i)
}

// Scan 实现 sql.Scanner 接口
func (i *CategorySlice) Scan(value interface{}) error {
	if value == nil {
		*i = CategorySlice{}
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into ImageSlice", value)
	}

	return json.Unmarshal(bytes, i)
}

// GoodsSpec 商品规格信息
type GoodsSpec struct {
	SpecName    string      `json:"spec_name"`
	SpecValName StringSlice `json:"spec_val_name"`
}

// GoodsSpecSlice 自定义商品规格切片类型
type GoodsSpecSlice []GoodsSpec

// Value 实现 driver.Valuer 接口
func (g GoodsSpecSlice) Value() (driver.Value, error) {
	if len(g) == 0 {
		return "[]", nil
	}
	return json.Marshal(g)
}

// Scan 实现 sql.Scanner 接口
func (g *GoodsSpecSlice) Scan(value interface{}) error {
	if value == nil {
		*g = GoodsSpecSlice{}
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into GoodsSpecSlice", value)
	}

	return json.Unmarshal(bytes, g)
}

// GoodsSpecProduct 商品规格产品信息
type GoodsSpecProduct struct {
	PrdSn              string     `json:"prd_sn"`
	PrdDesc            string     `json:"prd_desc"`
	PrdPrice           float64    `json:"prd_price"`
	SpecId             string     `json:"spec_id"`
	Length             float64    `json:"length"`
	Width              float64    `json:"width"`
	Height             float64    `json:"height"`
	PrdNumber          float64    `json:"prd_number"`
	PrdWeight          float64    `json:"prd_weight"`
	ProductImages      ImageSlice `json:"product_images"`
	IsSale             bool       `json:"is_sale"`
	UpdateTime         string     `json:"update_time"`
	SpecBarcode        string     `json:"spec_barcode"`
	SuggestedPrice     float64    `json:"suggested_price"`
	DistributionPrice  float64    `json:"distribution_price"`
	DeletedStatus      int        `json:"deleted_status"`
	ControlMaxPrice    float64    `json:"control_max_price"`
	ControlMinPrice    float64    `json:"control_min_price"`
	LeftoverStockPrice float64    `json:"leftover_stock_price"`
}

// GoodsSpecProductSlice 自定义商品规格产品切片类型
type GoodsSpecProductSlice []GoodsSpecProduct

// Value 实现 driver.Valuer 接口
func (g GoodsSpecProductSlice) Value() (driver.Value, error) {
	if len(g) == 0 {
		return "[]", nil
	}
	return json.Marshal(g)
}

// Scan 实现 sql.Scanner 接口
func (g *GoodsSpecProductSlice) Scan(value interface{}) error {
	if value == nil {
		*g = GoodsSpecProductSlice{}
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into GoodsSpecProductSlice", value)
	}

	return json.Unmarshal(bytes, g)
}

// WdtGoodsDetail 商品主表
type WdtGoodsDetail struct {
	source.Model

	GoodsId               int                   `json:"goods_id" gorm:"column:goods_id;index"`
	ImgUrl                string                `json:"img_url" gorm:"column:img_url;type:varchar(500)"`
	GoodsName             string                `json:"goods_name" gorm:"column:goods_name;type:varchar(255)"`
	GoodsSn               string                `json:"goods_sn" gorm:"column:goods_sn;type:varchar(100);index"`
	GoodsPrice            float64               `json:"goods_price" gorm:"column:goods_price"`
	Description           string                `json:"description" gorm:"column:description;type:text"`
	Summary               string                `json:"summary" gorm:"column:summary;type:text"`
	MaterialVideoInfo     StringSlice           `json:"material_video_info" gorm:"column:material_video_info;type:text"`
	CategoryList          CategorySlice         `json:"category_list" gorm:"column:category_list;type:text"`
	GoodsImages           ImageSlice            `json:"goods_images" gorm:"column:goods_images;type:text"`
	WhiteBackGroundImg    ImageSlice            `json:"white_back_ground_img" gorm:"column:white_back_ground_img;type:text"`
	LongGraphImg          ImageSlice            `json:"long_graph_img" gorm:"column:long_graph_img;type:text"`
	ThreeToFourItemImages ImageSlice            `json:"three_to_four_item_images" gorm:"column:three_to_four_item_images;type:text"`
	GoodsSpecs            GoodsSpecSlice        `json:"goods_specs" gorm:"column:goods_specs;type:text"`
	UpdateTime            string                `json:"update_time" gorm:"column:update_time"`
	GoodsSpecProducts     GoodsSpecProductSlice `json:"goods_spec_products" gorm:"column:goods_spec_products;type:longtext"`
}

// TableName 设置表名
func (WdtGoodsDetail) TableName() string {
	return "wdt_goods_details"
}

// 添加 BeforeSave 钩子方法
//func (goods *GoodsSpecProducts) BeforeSave(tx *gorm.DB) error {
//	if len(goods.SpecList) > 0 {
//		// 初始化最小价格为第一个规格的价格
//		minPriceSpec := goods.SpecList[0]
//
//		// 遍历所有规格，找出会员价最低的规格
//		for _, spec := range goods.SpecList {
//			if spec.MemberPrice < minPriceSpec.MemberPrice {
//				minPriceSpec = spec
//			}
//		}
//
//		// 使用会员价最低的规格的价格作为商品主表价格
//		goods.LowestPrice = minPriceSpec.LowestPrice
//		goods.RetailPrice = minPriceSpec.RetailPrice
//		goods.WholesalePrice = minPriceSpec.WholesalePrice
//		goods.MemberPrice = minPriceSpec.MemberPrice
//		goods.MarketPrice = minPriceSpec.MarketPrice
//		goods.CustomPrice1 = minPriceSpec.CustomPrice1
//		goods.CustomPrice2 = minPriceSpec.CustomPrice2
//		goods.CostPrice = minPriceSpec.CostPrice
//	}
//	return nil
//}

// WdtGoodsSpec 商品规格表
type WdtGoodsSpec struct {
	source.Model
	PrdSn              string      `json:"prd_sn" gorm:"column:prd_sn;type:varchar(100);index"`
	PrdDesc            string      `json:"prd_desc" gorm:"column:prd_desc;type:varchar(255)"`
	PrdPrice           float64     `json:"prd_price" gorm:"column:prd_price"`
	SpecId             string      `json:"spec_id" gorm:"column:spec_id;type:varchar(100);index"`
	Length             float64     `json:"length" gorm:"column:length"`
	Width              float64     `json:"width" gorm:"column:width"`
	Height             float64     `json:"height" gorm:"column:height"`
	PrdNumber          float64     `json:"prd_number" gorm:"column:prd_number"`
	PrdWeight          float64     `json:"prd_weight" gorm:"column:prd_weight"`
	ProductImages      StringSlice `json:"product_images" gorm:"column:product_images;type:text"`
	IsSale             bool        `json:"is_sale" gorm:"column:is_sale"`
	UpdateTime         string      `json:"update_time" gorm:"column:update_time"`
	SpecBarcode        string      `json:"spec_barcode" gorm:"column:spec_barcode;type:varchar(100)"`
	SuggestedPrice     float64     `json:"suggested_price" gorm:"column:suggested_price"`
	DistributionPrice  int         `json:"distribution_price" gorm:"column:distribution_price"`
	DeletedStatus      int         `json:"deleted_status" gorm:"column:deleted_status"`
	ControlMaxPrice    float64     `json:"control_max_price" gorm:"column:control_max_price"`
	ControlMinPrice    float64     `json:"control_min_price" gorm:"column:control_min_price"`
	LeftoverStockPrice float64     `json:"leftover_stock_price" gorm:"column:leftover_stock_price"`
}

// TableName 设置表名
func (WdtGoodsSpec) TableName() string {
	return "wdt_goods_specs"
}

type WdtGoodsList struct {
	GoodsId             int         `json:"goods_id"`
	ImgUrl              string      `json:"img_url"`
	GoodsName           string      `json:"goods_name"`
	GoodsSn             string      `json:"goods_sn"`
	CreateTime          string      `json:"create_time"`
	SupplierCompanyName string      `json:"supplier_company_name"`
	SupCompanyAlias     string      `json:"sup_company_alias"`
	SupplierShopId      interface{} `json:"supplier_shop_id"`
	SupplierNickNo      interface{} `json:"supplier_nick_no"`
	SupplierGoodsId     int         `json:"supplier_goods_id"`
	SupplierSysId       int         `json:"supplier_sys_id"`
	Tail                interface{} `json:"tail"`
	BrandName           string      `json:"brand_name"`
	Skus                []struct {
		SupplierItemId                 int           `json:"supplier_item_id"`
		PrdSn                          interface{}   `json:"prd_sn"`
		PrdDesc                        string        `json:"prd_desc"`
		SkuTagDetail                   string        `json:"sku_tag_detail"`
		Tail                           interface{}   `json:"tail"`
		GoodsTagList                   []interface{} `json:"goods_tag_list"`
		SpecId                         string        `json:"spec_id"`
		DistributionPrice              float64       `json:"distributionPrice"`
		DeletedStatus                  int           `json:"deletedStatus"`
		DistributionPriceChangedStatus int           `json:"distribution_price_changed_status"`
		DeletedChangedStatus           interface{}   `json:"deleted_changed_status"`
		IsSale                         bool          `json:"is_sale"`
		ControlMaxPrice                interface{}   `json:"control_max_price"`
		ControlMinPrice                interface{}   `json:"control_min_price"`
		TieredPriceList                []interface{} `json:"tiered_price_list"`
		LeftoverStockPrice             interface{}   `json:"leftover_stock_price"`
		SpecBarcode                    string        `json:"spec_barcode"`
		Weight                         float64       `json:"weight"`
		SuggestedPrice                 interface{}   `json:"suggested_price"`
	} `json:"skus"`
	GoodsTags   []interface{} `json:"goods_tags"`
	PublishShop struct {
		PublishShopNum        int `json:"publish_shop_num"`
		PublishSuccessShopNum int `json:"publish_success_shop_num"`
	} `json:"publish_shop"`
	PriceControl        bool    `json:"price_control"`
	IsSale              bool    `json:"is_sale"`
	DeleteStatus        int     `json:"delete_status"`
	MinDistributorPrice float64 `json:"min_distributor_price"`
}

type WdtStock struct {
	SpecID        string  `json:"spec_id"`
	GoodsNo       string  `json:"goods_no"`
	SpecNo        string  `json:"spec_no"`
	WarehouseID   string  `json:"warehouse_id"`
	WarehouseNo   string  `json:"warehouse_no"`
	WarehouseName string  `json:"warehouse_name"`
	StockNum      float64 `json:"stock_num"`
	LockNum       float64 `json:"lock_num"`
	AvailableNum  float64 `json:"avaliable_num"`
	UnpayNum      float64 `json:"unpay_num"`
	SubscribeNum  float64 `json:"subscribe_num"`
	OrderNum      float64 `json:"order_num"`
	SendingNum    float64 `json:"sending_num"`
	PurchaseNum   float64 `json:"purchase_num"`
	TransferNum   float64 `json:"transfer_num"`
	ToTransferNum float64 `json:"to_transfer_num"`
	ToPurchaseNum float64 `json:"to_purchase_num"`
	CostPrice     float64 `json:"cost_price"`
	Modified      string  `json:"modified"`
}

// 在 WdtGoodsSpec 结构中添加库存字段
