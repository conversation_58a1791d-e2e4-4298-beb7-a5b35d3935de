package model

import (
	"yz-go/source"
)

// WdtGoodsDetail 商品主表
type WdtGoodsDetail struct {
	source.Model

	GoodsId           int      `json:"goods_id"`
	ImgUrl            string   `json:"img_url"`
	GoodsName         string   `json:"goods_name"`
	GoodsSn           string   `json:"goods_sn"`
	GoodsPrice        float64  `json:"goods_price"`
	Description       string   `json:"description"`
	Summary           string   `json:"summary"`
	MaterialVideoInfo []string `json:"material_video_info"`
	CategoryList      []string `json:"category_list"`
	GoodsImages       []struct {
		ImgId  int    `json:"img_id"`
		ImgUrl string `json:"img_url"`
	} `json:"goods_images"`
	WhiteBackGroundImg []string `json:"white_back_ground_img"`
	LongGraphImg       []struct {
		ImgId  int    `json:"img_id"`
		ImgUrl string `json:"img_url"`
	} `json:"long_graph_img"`
	ThreeToFourItemImages []struct {
		ImgId  int    `json:"img_id"`
		ImgUrl string `json:"img_url"`
	} `json:"three_to_four_item_images"`
	GoodsSpecs []struct {
		SpecName    string   `json:"spec_name"`
		SpecValName []string `json:"spec_val_name"`
	} `json:"goods_specs"`
	UpdateTime        string `json:"update_time"`
	GoodsSpecProducts []struct {
		PrdSn              string   `json:"prd_sn"`
		PrdDesc            string   `json:"prd_desc"`
		PrdPrice           float64  `json:"prd_price"`
		SpecId             string   `json:"spec_id"`
		Length             float64  `json:"length"`
		Width              float64  `json:"width"`
		Height             float64  `json:"height"`
		PrdNumber          float64  `json:"prd_number"`
		PrdWeight          float64  `json:"prd_weight"`
		ProductImages      []string `json:"product_images"`
		IsSale             bool     `json:"is_sale"`
		UpdateTime         string   `json:"update_time"`
		SpecBarcode        string   `json:"spec_barcode"`
		SuggestedPrice     float64  `json:"suggested_price"`
		DistributionPrice  int      `json:"distribution_price"`
		DeletedStatus      int      `json:"deleted_status"`
		ControlMaxPrice    float64  `json:"control_max_price"`
		ControlMinPrice    float64  `json:"control_min_price"`
		LeftoverStockPrice float64  `json:"leftover_stock_price"`
	} `json:"goods_spec_products"`
}

// 添加 BeforeSave 钩子方法
//func (goods *GoodsSpecProducts) BeforeSave(tx *gorm.DB) error {
//	if len(goods.SpecList) > 0 {
//		// 初始化最小价格为第一个规格的价格
//		minPriceSpec := goods.SpecList[0]
//
//		// 遍历所有规格，找出会员价最低的规格
//		for _, spec := range goods.SpecList {
//			if spec.MemberPrice < minPriceSpec.MemberPrice {
//				minPriceSpec = spec
//			}
//		}
//
//		// 使用会员价最低的规格的价格作为商品主表价格
//		goods.LowestPrice = minPriceSpec.LowestPrice
//		goods.RetailPrice = minPriceSpec.RetailPrice
//		goods.WholesalePrice = minPriceSpec.WholesalePrice
//		goods.MemberPrice = minPriceSpec.MemberPrice
//		goods.MarketPrice = minPriceSpec.MarketPrice
//		goods.CustomPrice1 = minPriceSpec.CustomPrice1
//		goods.CustomPrice2 = minPriceSpec.CustomPrice2
//		goods.CostPrice = minPriceSpec.CostPrice
//	}
//	return nil
//}

// WdtGoodsSpec 商品规格表

// 在 WdtGoodsSpec 结构中添加库存字段
