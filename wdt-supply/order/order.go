package order

import (
	"bytes"
	"encoding/json"
	"fmt"
	"wdt-supply/common"
	"wdt-supply/model"
)

type WdtOrder struct {
	AppKey    string `json:"app_key"`
	AppSecret string `json:"app_secret"`
	Sid       string `json:"sid"`
	ShopId    string `json:"shop_id"`
}

func (wdt *WdtOrder) SetBase(appKey, appSecret, sid, shopId string) {
	wdt.AppKey = appKey
	wdt.AppSecret = appSecret
	wdt.Sid = sid
	wdt.ShopId = shopId
}
func (u UnescapedJSON) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	encoder := json.NewEncoder(&buf)
	encoder.SetEscapeHTML(false)
	err := encoder.Encode(u.Value)
	if err != nil {
		return nil, err
	}
	// encoder.Encode 会添加换行符，我们需要去掉它
	return buf.Bytes()[:buf.Len()-1], nil
}

// PushOrder 推送订单到旺店通
func (wdt *WdtOrder) PushOrder(order model.WdtOrder) error {
	req := &common.WdtRequest{
		Sid:       wdt.Sid,
		Appkey:    wdt.AppKey,
		Appsecret: wdt.AppSecret,
	}

	var buf bytes.Buffer
	encoder := json.NewEncoder(&buf)
	encoder.SetEscapeHTML(false)
	encoder.Encode([]model.WdtOrder{order})
	// 设置请求参数
	req.SetParams(map[string]interface{}{
		"shop_id":    wdt.ShopId,
		"trade_list": buf.String(),
	})

	// 发送请求
	resp, err := req.DoRequest("trade_push.php", true)
	if err != nil {
		return fmt.Errorf("推送订单失败: %s", resp.Message)
	}

	return nil
}
