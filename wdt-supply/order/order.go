package order

import (
	"fmt"
	"wdt-supply/common"
	"wdt-supply/model"
)

type WdtOrder struct {
	AppKey    string `json:"app_key"`
	AppSecret string `json:"app_secret"`
	Sid       string `json:"sid"`
	ShopId    string `json:"shop_id"`
}

func (wdt *WdtOrder) SetBase(appKey, appSecret, sid, shopId string) {
	wdt.AppKey = appKey
	wdt.AppSecret = appSecret
	wdt.Sid = sid
	wdt.ShopId = shopId
}

// PushOrder 推送订单到旺店通
func (wdt *WdtOrder) PushOrder(order model.WdtOrder) error {
	req := &common.WdtRequest{
		Sid:       wdt.Sid,
		Appkey:    wdt.AppKey,
		Appsecret: wdt.AppSecret,
	}

	// 设置请求参数
	req.SetParams(map[string]interface{}{
		"shop_id":    wdt.ShopId,
		"trade_list": []model.WdtOrder{order},
	})

	// 发送请求
	resp, err := req.DoRequest("trade_push.php", true)
	if err != nil {
		return fmt.Errorf("推送订单失败: %s", resp.Message)
	}

	return nil
}
