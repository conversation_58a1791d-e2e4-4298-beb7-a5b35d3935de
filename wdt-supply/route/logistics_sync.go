package route

import (
	"github.com/gin-gonic/gin"
	"wdt-supply/api"
)

// InitLogisticsSyncRouter 初始化物流同步路由
func InitLogisticsSyncRouter(Router *gin.RouterGroup) {
	logisticsRouter := Router.Group("logistics")
	{
		// 物流同步相关接口
		logisticsRouter.POST("sync", api.SyncLogistics)                   // 手动同步物流信息
		logisticsRouter.POST("query", api.QueryLogistics)                 // 查询物流信息
		logisticsRouter.GET("status", api.GetLogisticsSyncStatus)         // 获取同步状态
		logisticsRouter.POST("batch-sync", api.BatchSyncLogistics)        // 批量同步
		logisticsRouter.POST("on-demand-sync", api.OnDemandSyncLogistics) // 按需同步
		logisticsRouter.PUT("config", api.UpdateLogisticsSyncConfig)      // 更新配置
		logisticsRouter.GET("details", api.GetLogisticsDetails)           // 获取物流详情
	}
}
