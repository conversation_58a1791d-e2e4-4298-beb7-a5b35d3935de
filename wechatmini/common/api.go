package common

import (
	"bytes"
	"encoding/json"
	"errors"
	"github.com/gin-gonic/gin"
	"github.com/silenceper/wechat/v2"
	"github.com/silenceper/wechat/v2/cache"
	"github.com/silenceper/wechat/v2/miniprogram"
	miniprogramConfig "github.com/silenceper/wechat/v2/miniprogram/config"
	"github.com/silenceper/wechat/v2/util"
	"go.uber.org/zap"
	"io/ioutil"
	"net/http"
	smallShopModel "small-shop/model"
	smallshopModel "small-shop/model"
	"time"
	"wechatmini/setting"
	"yz-go/config"

	"yz-go/component/log"
)

const (
	GetDeliveryListWxMiniApi string = "https://api.weixin.qq.com/cgi-bin/express/delivery/open_msg/get_delivery_list" //获取快递公司 POST
	UploadShippingInfoApi    string = "https://api.weixin.qq.com/wxa/sec/order/upload_shipping_info"                  //上传发货信息 POST
)

type RequestData struct {
	Query          map[string]string        `json:"query"`  //请求query参数
	Body           []byte                   `json:"body"`   //请求body参数
	Url            string                   `json:"Url"`    //请求链接
	Method         string                   `json:"method"` //请求方式
	Config         setting.Value            `json:"config"`
	MiniprogramApi *miniprogram.MiniProgram `json:"miniprogramApi"`
	AccessToken    string                   `json:"accessToken"`
	Host           string                   `json:"host"` //请求链接
}

var Miniprogram *miniprogram.MiniProgram

var SmallShopMiniprogram *miniprogram.MiniProgram

// 实例化小程序API
func GetMiniprogram() (err error, miniprogram *miniprogram.MiniProgram) {
	if Miniprogram == nil {
		var wechatMini setting.Value
		err, wechatMini = setting.GetMini()
		if err != nil {
			err = errors.New("获取微信小程序配置失败" + err.Error())
			return
		}
		if wechatMini.AppId == "" || wechatMini.AppSecret == "" {
			err = errors.New("请配置微信小程序")
			return
		}
		//微信小程序配置部分
		var addr = config.Config().Redis.Addr
		if addr == "" {
			addr = "127.0.0.1:6379"
		}
		redisOpts := &cache.RedisOpts{
			Host:        addr,
			Database:    config.Config().Redis.DB,
			MaxActive:   10,
			MaxIdle:     10,
			IdleTimeout: 60, //second
		}
		if config.Config().Redis.Password != "" {
			redisOpts.Password = config.Config().Redis.Password
		}
		redisCache := cache.NewRedis(redisOpts)
		cfg := &miniprogramConfig.Config{
			AppID:     wechatMini.AppId,
			AppSecret: wechatMini.AppSecret,
			Cache:     redisCache,
		}
		Miniprogram = wechat.NewWechat().GetMiniProgram(cfg)

	}
	return err, Miniprogram
}

// 实例化小商店小程序API
func GetSmallShopMiniprogram() (err error, SmallShopMiniprogram *miniprogram.MiniProgram) {
	if SmallShopMiniprogram == nil {
		var wechatMini smallShopModel.WxSetting
		err, wechatMini = smallShopModel.GetSysMiniSetting()
		if err != nil {
			err = errors.New("获取微信小程序配置失败" + err.Error())
			return
		}
		if wechatMini.Value.AppId == "" || wechatMini.Value.AppSecret == "" {
			err = errors.New("请配置微信小程序")
			return
		}
		//微信小程序配置部分
		var addr = config.Config().Redis.Addr
		if addr == "" {
			addr = "127.0.0.1:6379"
		}
		redisOpts := &cache.RedisOpts{
			Host:        addr,
			Database:    config.Config().Redis.DB,
			MaxActive:   10,
			MaxIdle:     10,
			IdleTimeout: 60, //second
		}
		if config.Config().Redis.Password != "" {
			redisOpts.Password = config.Config().Redis.Password
		}
		redisCache := cache.NewRedis(redisOpts)
		cfg := &miniprogramConfig.Config{
			AppID:     wechatMini.Value.AppId,
			AppSecret: wechatMini.Value.AppSecret,
			Cache:     redisCache,
		}
		SmallShopMiniprogram = wechat.NewWechat().GetMiniProgram(cfg)

	}
	return err, SmallShopMiniprogram
}

// code 1 中台小程序 2小商店小程序
func Initial(code int) (err error, res RequestData) {
	switch code {
	case 1:
		err, res.Config = setting.GetMini()
		if err != nil {
			return
		}
		if res.Config.AppId == "" {
			err = errors.New("请填写APPID")
			return
		}
		if res.Config.AppSecret == "" {
			err = errors.New("请填写AppSecret")
			return
		}
		err, res.MiniprogramApi = GetMiniprogram()
		break
	case 2:
		var sysMiniSettingData smallshopModel.WxSetting
		err, sysMiniSettingData = smallshopModel.GetSysMiniSetting()
		if err != nil {
			err = errors.New("小商店小程序设置获取失败" + err.Error())
			return
		}
		res.Config.Mchid = sysMiniSettingData.Value.Mchid
		res.Config.AppId = sysMiniSettingData.Value.AppId
		res.Config.AppSecret = sysMiniSettingData.Value.AppSecret
		res.Config.IsUploadShippingInfo = sysMiniSettingData.Value.IsUploadShippingInfo
		if res.Config.AppId == "" {
			err = errors.New("请填写APPID")
			return
		}
		if res.Config.AppSecret == "" {
			err = errors.New("请填写AppSecret")
			return
		}
		err, res.MiniprogramApi = GetSmallShopMiniprogram()
		break
	default:
		err = errors.New("code类型错误")
		return
	}

	if err != nil {
		log.Log().Error("获取微信配置失败!", zap.Any("err", err))
		return
	}
	res.AccessToken, err = res.MiniprogramApi.GetContext().GetAccessToken()
	if err != nil {
		log.Log().Error("获取accessToken失败!"+err.Error(), zap.Any("err", err))
		return
	}
	return
}

type GetDeliveryListWxMiniData struct {
	util.CommonError
	DeliveryList []struct {
		DeliveryID   string `json:"delivery_id"`
		DeliveryName string `json:"delivery_name"`
	} `json:"delivery_list"`
	Count int `json:"count"`
}

func (rd RequestData) GetDeliveryListWxMini() (err error, data GetDeliveryListWxMiniData) {
	var result []byte
	rd.Url = GetDeliveryListWxMiniApi
	rd.Body, err = json.Marshal(gin.H{})
	result, err = rd.ClientPost()
	if err != nil {
		log.Log().Error("微信小程序获取物流公司失败", zap.Any("错误信息", err))
		err = errors.New("微信小程序获取物流公司失败" + err.Error())
		return
	}
	err = json.Unmarshal(result, &data)
	if err != nil {
		log.Log().Error("微信小程序获取物流公司失败列表数据失败：转结构体失败", zap.Any("错误信息", err))
		err = errors.New("微信小程序获取物流公司失败列表数据失败：转结构体失败" + err.Error())
		return
	}
	if data.ErrCode != 0 {
		log.Log().Error("微信小程序获取物流公司失败", zap.Any("错误信息", data.ErrMsg))
		err = errors.New("微信小程序获取物流公司失败列表数据失败：" + data.ErrMsg)
		return
	}
	return
}

type UploadShippingInfoRequest struct {
	OrderKey struct {
		OrderNumberType int `json:"order_number_type"` //订单单号类型，用于确认需要上传详情的订单。枚举值1，使用下单商户号和商户侧单号；枚举值2，使用微信支付单号。
		//TransactionID   string `json:"transaction_id"`    //原支付交易对应的微信订单号
		Mchid      string `json:"mchid"`        //商户号，即商户在微信商户平台的商户号，示例值: 1900000109
		OutTradeNo string `json:"out_trade_no"` //商户系统内部订单号，只能是数字、大小写字母`_-*`且在同一个商户号下唯一

	} `json:"order_key"`
	DeliveryMode   string         `json:"delivery_mode"`    //	发货模式，发货模式枚举值：1、UNIFIED_DELIVERY（统一发货）2、SPLIT_DELIVERY（分拆发货） 示例值: UNIFIED_DELIVERY
	IsAllDelivered bool           `json:"is_all_delivered"` //分拆发货模式时必填，用于标识分拆发货模式下是否已全部发货完成，只有全部发货完成的情况下才会向用户推送发货完成通知。示例值: true/false
	LogisticsType  int            `json:"logistics_type"`   //物流模式，发货方式枚举值：1、实体物流配送采用快递公司进行实体物流配送形式 2、同城配送 3、虚拟商品，虚拟商品，例如话费充值，点卡等，无实体配送形式 4、用户自提
	ShippingList   []ShippingList `json:"shipping_list"`
	UploadTime     time.Time      `json:"upload_time"` //上传时间，用于标识请求的先后顺序 示例值: `2022-12-15T13:29:35.120+08:00`
	Payer          struct {
		Openid string `json:"openid"` //用户标识，用户在小程序appid下的唯一标识。 下单前需获取到用户的Openid 示例值: oUpF8uMuAJO_M2pxb1Q9zNjWeS6o 字符字节限制: [1, 128]
	} `json:"payer"`
}
type ShippingList struct {
	TrackingNo     string `json:"tracking_no"`     //物流单号，物流快递发货时必填，示例值: 323244567777 字符字节限制: [1, 128]
	ExpressCompany string `json:"express_company"` //物流公司编码，快递公司ID，参见「查询物流公司编码列表」，物流快递发货时必填， 示例值: DHL 字符字节限制: [1, 128]
	ItemDesc       string `json:"item_desc"`       //商品信息，例如：微信红包抱枕*1个，限120个字以内
	Contact        struct {
		ReceiverContact string `json:"receiver_contact"`
	} `json:"contact"`
}

func (rd RequestData) UploadShippingInfo(info UploadShippingInfoRequest) (err error) {
	var result []byte
	//todo 参数处理拼接推送数据
	rd.Url = UploadShippingInfoApi
	rd.Body, err = json.Marshal(info)
	result, err = rd.ClientPost()
	if err != nil {
		log.Log().Error("微信小程序录入发货信息失败", zap.Any("错误信息", err))
		err = errors.New("微信小程序录入发货信息失败" + err.Error())
		return
	}
	var data util.CommonError
	err = json.Unmarshal(result, &data)
	if err != nil {
		log.Log().Error("微信小程序录入发货信息失败：转结构体失败", zap.Any("错误信息", err))
		err = errors.New("微信小程序录入发货信息失败：转结构体失败" + err.Error())
		return
	}
	if data.ErrCode != 0 {
		log.Log().Error("微信小程序录入发货信息失败", zap.Any("错误信息", data.ErrMsg))
		err = errors.New("微信小程序录入发货信息失败：" + data.ErrMsg)
		return
	}
	return
}

func (rd RequestData) ClientPost() (res []byte, err error) {
	client := &http.Client{Timeout: 5 * time.Minute}
	//if Request.Body = nil
	body := bytes.NewReader(rd.Body)

	req, _ := http.NewRequest("POST", rd.Url+"?access_token="+rd.AccessToken, body)

	req = rd.SetClentHeader(req)

	resp, err := client.Do(req)

	if err != nil {
		return
	}
	defer resp.Body.Close()

	res, _ = ioutil.ReadAll(resp.Body)
	//err = json.Unmarshal(res, &result)

	//if err != nil {
	//	log.Log().Error("JSON异常", zap.Any("err", err), zap.Any("data", rd))
	//}

	//log.Log().Info("im返回",zap.Any("Url",rd.Url),zap.Any("result",result))
	return
}

func (rd RequestData) SetClentHeader(request *http.Request) (req *http.Request) {
	request.Header.Add("Content-Type", "application/json;charset=utf-8")
	return request
}
