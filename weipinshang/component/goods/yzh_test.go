package goods

import (
	"fmt"
	"public-supply/request"
	"testing"
	"weipinshang/mq"
)

func TestYzh_ImportGoodsRunAA(t *testing.T) {
	var data mq.SendData
	data.Page = 1
	data.GatherSupplyId = 57
	mq.PublishMessage(&data)

	return
}

func TestYzh_ImportGoodsRun(t *testing.T) {

	//aaacc := "143.79"
	//SellPrice, _ := strconv.ParseFloat(aaacc, 64)
	//
	//fmt.Println(SellPrice)
	//
	//return

	//fmt.Println(runtime.NumGoroutine())
	//return

	//source.DB().Exec("DROP TABLE IF EXISTS `yzh_products`;")
	//source.DB().Exec("CREATE TABLE yzh_products  select * from yzh_temp_products  ;")

	//source.DB().Exec("alter table yzh_temp_products rename yzh_products;")

	//source.DB().Exec("DROP TABLE IF EXISTS `yzh_products`;")
	//source.DB().Exec("CREATE TABLE yzh_products  select * from yzh_temp_products  ;")
	//
	////source.DB().Exec("alter table yzh_temp_products rename yzh_products;")
	//
	//return

	y := &Wps{}
	//var info request.GetGoodsSearch
	y.InitSetting(57)

	//y.SyncGoods()
	//return
	//var info request.GetGoodsSearch
	//y.GetGoods(info)
	//return
	var info request.GetGoodsSearch
	info.Page = 1

	//y.GetGoods(info)
	//return
	var ids string
	ids = "WPS862_j06" //WPS9_1221183858598981
	_, detail := y.BatchGetGoodsDetails(ids)
	return
	//var  wmodel.GoodsSkuDetail
	y.CommodityAssemblyLocal(detail[0], 1, 2, 3)
	return

	//y.SynergeticProcess(aa)
	return
	//var info request.GetCategoryChild
	//err, list := y.GetCategoryChild(1280, info)
	//if err != nil {
	//	return
	//}
	//
	//fmt.Println(list)
	//
	//return

	var aaa = []int64{2888888889092875, 2888888889080346}
	y.SynergeticProcess(aaa)

	return

	return

	//y.GetStock(563684)

	//y.UpdateGoodsRun()

	//y.GetYzhCategoryListAll()

	//time.Sleep(time.Second * 50)

	fmt.Println("wanhceng")
	//y.ImportGoodsRun(info)
	//
	//aaan := []uint{}
	//_, dasta := y.GetCategoryDetail(1026, aaan)
	//fmt.Println("aaaaaaa", dasta)

}

func TestGetTestAC(t *testing.T) {
	GetTestAC()
}
