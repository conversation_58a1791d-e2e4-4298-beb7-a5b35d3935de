package cron

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	omodel "order/model"
	"public-supply/common"
	"public-supply/model"
	pubmodel "public-supply/model"
	setting2 "public-supply/setting"
	"strconv"
	wps2 "weipinshang/component/order"
	"yz-go/component/log"
	"yz-go/cron"

	"yz-go/source"
)

func PushWpsOrderSendGoodsHandle() {
	log.Log().Info("wps PushWpsOrderSendGoodsHandle info ", zap.Any("err", "info"))

	var gatherList []model.GatherSupply
	err := source.DB().Where("category_id = ?", common.WEIPINSHANG_SOURCE).Where("deleted_at is null").Find(&gatherList).Error
	if err != nil {
		return
	}

	for _, v := range gatherList {

		CreateCronTask(v.ID)

	}
}

func CreateCronTask(taskID uint) {
	log.Log().Info("wps CreateCronTask info ", zap.Any("err", taskID))

	var dat pubmodel.SupplySetting
	err, setting := setting2.GetSetting("gatherSupply" + strconv.Itoa(int(taskID)))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}

	err = json.Unmarshal([]byte(setting.Value), &dat)
	if err != nil {

		return
	}

	var cronStr string
	if dat.UpdateInfo.Cron != "" {
		cronStr = dat.UpdateInfo.Cron
	} else {
		cronStr = "0 */5 * * * ?"
	}
	log.Log().Info("wps cronStr info ", zap.Any("err", cronStr))

	cron.PushTask(cron.Task{
		Key:  "wpsOrderSendGoods" + strconv.Itoa(int(taskID)),
		Name: "wpsOrderSendGoods发货更新" + strconv.Itoa(int(taskID)),
		Spec: cronStr,
		Handle: func(task cron.Task) {
			TaskRun(int(taskID))

		},
		Status: cron.ENABLED,
	})

}

var taskMap = make(map[int]bool)

func TaskRun(taskID int) {
	fmt.Println("当前状态", taskMap[taskID])
	if taskMap[taskID] == false {
		taskMap[taskID] = true
		SendGoods(uint(taskID))
		taskMap[taskID] = false
	}

}

func SendGoods(gatherSupplyID uint) (err error) {
	log.Log().Info("wps SendGoods info ", zap.Any("err", gatherSupplyID))

	var wps wps2.Wps
	err = wps.InitSetting(gatherSupplyID)
	if err != nil {
		return
	}
	var deliverOrder []omodel.Order
	err = source.DB().Preload("OrderItems").Where("status=? and gather_supply_id=?", 1, gatherSupplyID).Find(&deliverOrder).Error

	if err != nil {
		return
	}

	for _, od := range deliverOrder {
		orderSN := strconv.Itoa(int(od.OrderSN))
		err = wps.DeliverGoods(orderSN)
		if err != nil {
			log.Log().Error("wps SendGoods err ", zap.Any("err", err))
		}

	}

	return

}
