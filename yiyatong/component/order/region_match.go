package order

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	publicSupplyRequest "public-supply/request"
	regiongMatchService "region-match/service"
	"strconv"
	"time"
)

const regionMatchUrl = "http://116.205.165.53:8080/api/convert"

type ConvertRequest struct {
	SourceType  string `json:"sourceType"`
	TargetType  string `json:"targetType"`
	ProvinceId  string `json:"provinceId"`
	CityId      string `json:"cityId"`
	DistrictId  string `json:"districtId"`
	StreetId    string `json:"streetId"`
	MaxDistance int    `json:"maxDistance"`
}

type ConvertResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Request ConvertRequest `json:"request"`
		IsExact bool           `json:"isExact"`
		Regions []Region       `json:"regions"`
	} `json:"data"`
}

type Region struct {
	ID           int     `json:"id"`
	ProvinceId   string  `json:"provinceId"`
	ProvinceName string  `json:"provinceName"`
	CityId       string  `json:"cityId"`
	CityName     string  `json:"cityName"`
	DistrictId   string  `json:"districtId"`
	DistrictName string  `json:"districtName"`
	StreetId     string  `json:"streetId"`
	StreetName   string  `json:"streetName"`
	Distance     float64 `json:"distance"`
}

func GetRegionMatch(address publicSupplyRequest.ReceivingInformation) (string, string) {
	if address.ProvinceId != 0 && address.CityId != 0 && address.CountyId != 0 && address.TownId != 0 {
		err, regionID, fullName := EsConvertRegion(address)
		// 匹配成功走API匹配逻辑
		if err == nil {
			return regionID, fullName
		}
	}

	// 匹配失败走ES匹配逻辑
	return GetYiyatongRegionIDsFromRequest(address.Province, address.City, address.Area, address.Street)
}

func EsConvertRegion(address publicSupplyRequest.ReceivingInformation) (error, string, string) {
	p := regiongMatchService.RegionMatchParams{
		SourceType:  "yz_lat_lon",
		TargetType:  "yyt_lat_lon",
		ProvinceId:  strconv.Itoa(address.ProvinceId),
		CityId:      strconv.Itoa(address.CityId),
		DistrictId:  strconv.Itoa(address.CountyId),
		StreetId:    strconv.Itoa(address.TownId),
		MaxDistance: "1000m",
	}

	r, err := regiongMatchService.RegionMatch(p)
	if err != nil {
		return fmt.Errorf("ES转换地址错误: %w", err), "", ""
	}

	regionID := fmt.Sprintf("%s,%s,%s,%s", r.ProvinceID, r.CityID, r.DistrictID, r.StreetID)
	fullName := fmt.Sprintf("%s,%s,%s,%s", r.ProvinceName, r.CityName, r.DistrictName, r.StreetName)

	return nil, regionID, fullName
}

func APIConvertRegion(address publicSupplyRequest.ReceivingInformation) (error, string, string) {
	// 构建请求体
	reqBody := ConvertRequest{
		SourceType:  "yz_supply_chain_regions",
		TargetType:  "yyt_supply_chain_regions",
		ProvinceId:  strconv.Itoa(address.ProvinceId),
		CityId:      strconv.Itoa(address.CityId),
		DistrictId:  strconv.Itoa(address.CountyId),
		StreetId:    strconv.Itoa(address.TownId),
		MaxDistance: 0,
	}

	// 发起请求
	resp, err := ConvertRegion(reqBody)
	if err != nil {
		return fmt.Errorf("地址转换API请求错误: %w", err), "", ""
	}

	if resp.Code != 200 {
		return fmt.Errorf("地址转换API匹配错误: %w", err), "", ""
	}

	for _, r := range resp.Data.Regions {
		score := 0
		if r.ProvinceName == address.Province {
			score += 1
		}
		if r.CityName == address.City {
			score += 1
		}
		if r.DistrictName == address.Area {
			score += 2
		}
		if r.StreetName == address.Street {
			score += 5
		}

		if score >= 8 {
			regionID := fmt.Sprintf("%s,%s,%s,%s", r.ProvinceId, r.CityId, r.DistrictId, r.StreetId)
			fullName := fmt.Sprintf("%s,%s,%s,%s", r.ProvinceName, r.CityName, r.DistrictName, r.StreetName)

			return nil, regionID, fullName
		}
	}

	return fmt.Errorf("地址匹配失败"), "", ""
}

// 发起请求的函数

func ConvertRegion(reqBody ConvertRequest) (*ConvertResponse, error) {
	// 编码为 JSON
	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// 构建请求
	client := &http.Client{Timeout: 10 * time.Second}
	req, err := http.NewRequest("POST", regionMatchUrl, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	// 执行请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	// 读取并解析响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	var response ConvertResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &response, nil
}
