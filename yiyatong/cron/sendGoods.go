package cron

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	omodel "order/model"
	"public-supply/common"
	"public-supply/model"
	pubmodel "public-supply/model"
	setting2 "public-supply/setting"
	"strconv"
	"yiyatong/component/order"
	"yz-go/component/log"
	"yz-go/cron"

	"yz-go/source"
)

func PushOrderSendGoodsHandle() {
	log.Log().Info("yyt PushOrderSendGoodsHandle info ", zap.Any("err", "info"))

	var gatherList []model.GatherSupply
	err := source.DB().Where("category_id = ?", common.YIYATONG_SOURCE).Where("deleted_at is null").Find(&gatherList).Error
	if err != nil {
		return
	}

	for _, v := range gatherList {

		CreateCronTask(v.ID)

	}
}

func CreateCronTask(taskID uint) {
	log.Log().Info("yyt CreateCronTask info ", zap.Any("err", taskID))

	var dat pubmodel.SupplySetting
	err, setting := setting2.GetSetting("gatherSupply" + strconv.Itoa(int(taskID)))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}

	err = json.Unmarshal([]byte(setting.Value), &dat)
	if err != nil {

		return
	}

	var cronStr string
	if dat.UpdateInfo.Cron != "" {
		cronStr = dat.UpdateInfo.Cron
	} else {
		cronStr = "0 */10 * * * ?"
	}
	log.Log().Info("yyt cronStr info ", zap.Any("err", cronStr))

	cron.PushTask(cron.Task{
		Key:  "yytOrderSendGoods" + strconv.Itoa(int(taskID)),
		Name: "yytOrderSendGoods发货更新" + strconv.Itoa(int(taskID)),
		Spec: cronStr,
		Handle: func(task cron.Task) {
			TaskRun(int(taskID))

		},
		Status: cron.ENABLED,
	})

}

var taskMap = make(map[int]bool)

func TaskRun(taskID int) {
	if taskMap[taskID] == false {
		taskMap[taskID] = true
		SendGoods(uint(taskID))
		taskMap[taskID] = false
	}

}

func SendGoods(gatherSupplyID uint) (err error) {
	log.Log().Info("yyt SendGoods info ", zap.Any("err", gatherSupplyID))

	var yyt order.Yyt
	err = yyt.InitSetting(gatherSupplyID)
	if err != nil {
		return
	}
	var deliverOrder []omodel.Order
	err = source.DB().Preload("OrderItems").Where("status=? and gather_supply_sn!='' and  gather_supply_id=?", 1, gatherSupplyID).Order("id desc").Find(&deliverOrder).Error

	if err != nil {
		return
	}

	for _, od := range deliverOrder {
		orderSN := strconv.Itoa(int(od.OrderSN))
		err = yyt.DeliverGoods(orderSN)
		if err != nil {
			log.Log().Error("yyt SendGoods err ", zap.Any("err", err))
		}

	}

	return

}
