package cron

import (
	"fmt"
	"public-supply/common"
	model2 "public-supply/model"
	"strconv"
	"youxuan-supply/component/goods"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func PushGoodsYouxuanHandleInit() {
	task := cron.Task{
		Key:  "goodsyouxuan",
		Name: "youxuan供应链同步商品",
		Spec: "47 20 15 11 * *",
		//Spec: "0 */1 * * * *",
		Handle: func(task cron.Task) {
			GoodsYouxuanCron()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func GoodsYouxuanCron() {
	log.Log().Info("开始处理优选商品自动同步")

	var gatherList []model2.GatherSupply
	err := source.DB().Where("`category_id` = ?", common.SUPPLY_YOUXUAN).Find(&gatherList).Error
	if err != nil || len(gatherList) == 0 {
		fmt.Println("未找到jushuitan供应链")
		return
	}

	for _, v := range gatherList {
		goodsClass := goods.Yx{}
		err = goodsClass.InitSetting(v.ID)
		if err != nil {
			return
		}
		err = goodsClass.InitGoods()
		if err != nil {
			log.Log().Info("id为" + strconv.Itoa(int(v.ID)) + "的优选供应链商品初始化失败：" + err.Error())
			continue
		}

	}

}
