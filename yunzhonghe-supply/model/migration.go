package model

import (
	_ "embed"
	"go.uber.org/zap"
	"yunzhonghe-supply/region"
	"yz-go/component/log"
	"yz-go/source"
)

func InitYzhRegion() (err error) {

	go func() {
		err = source.RunIfEsIndexCreatedAtBefore("yzh_region", 2024, 10, 15, 15, 16, func() {
			//初始化地址
			err = region.LoadRegionsFromXlsx()
			if err != nil {
				log.Log().Error("初始化地址失败", zap.Any("err", err))
			}
		})

	}()
	return
}

func Migrate() (err error) {
	go func() {
		err = InitYzhRegion()
	}()
	return
}
