package model

import (
	"strings"
	"testing"
)

func TestMigrate(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:    "case1",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := InitYzhRegion(); (err != nil) != tt.wantErr {
				t.Errorf("Migrate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMigrate2(t *testing.T) {
	a := strings.Replace("徐州经济技术开发区", "开发区", "", -1)
	println(a)
}
