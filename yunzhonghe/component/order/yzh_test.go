package order

import (
	"encoding/json"
	"fmt"
	"public-supply/request"
	"region/mapping"
	"strings"
	"testing"
	model2 "yunzhonghe/model"
)

type ATT struct {
	price string
}

func TestYzh_ImportGoodsRun(t *testing.T) {
	//var p, c, a string
	//myAddress := "广东省" + "," + "东莞市" + "," + "东莞市"
	//regionName := "东莞市"

	//广西壮族自治区 南宁市 青秀区

	myAddress := "广西壮族自治区" + "," + "南宁市" + "," + "青秀区"
	regionName := "莺歌路8号"
	//if strings.Contains(CityList, request.Address.Province) {
	//	myAddress = request.Address.Province + "," + request.Address.Area + "," + request.Address.Street
	//	regionName = request.Address.Street
	//}

	reginList, reginErr := mapping.GetMatchingRegionsByText("yzh_region", myAddress, regionName)
	fmt.Println(reginList, reginErr)
	return

	//var nnni []ATT
	//var orderAmount int64
	//nnni = append(nnni, ATT{
	//	price: "207.96",
	//})
	//nnni = append(nnni, ATT{
	//	price: "183.35",
	//})
	//nnni = append(nnni, ATT{
	//	price: "177.20",
	//})
	//
	//for _, item := range nnni {
	//	price, _ := strconv.ParseFloat(item.price, 64)
	//	SellPrice := utils.Yuan2Fen(price)
	//	orderAmount = orderAmount + SellPrice
	//
	//}
	//
	//aanng := float32(orderAmount) / 100
	//
	//fmt.Println(aanng)
	//
	//return
	//
	////source.DB().Table("orders").Where("id=?", 166).UpdateColumn("gather_supply_sn", gorm.Expr("CONCAT_WS('-',gather_supply_sn,?)", "11111"))
	////return
	//
	aaa := "{\"spu\":[{\"sku\":2883156,\"number\":1}],\"address\":{\"consignee\":\"测试\",\"phone\":\"18249104358\",\"province\":\"黑龙江省\",\"city\":\"哈尔滨市\",\"area\":\"道里区\",\"street\":\"其他\",\"description\":\"黑龙江省哈尔滨市道里区测试\"},\"gather_supply_id\":1}"
	y := &Yzh{}
	//var info request.GetGoodsSearch
	y.InitSetting(23)

	aaagn := ""

	//aadfff=append(aadfff,)

	for i := 0; i < 100; i++ {
		y.GetYzhGoodsStock(strings.Split(aaagn, ","))
		fmt.Println(i)
	}

	return

	return
	//
	//var aa []string
	//
	//aa = append(aa, "1125899906845182")
	//
	//y.GetGoodsPriceList(aa)
	//return
	//贵州省 黔南布依族苗族自治州 福泉市    湖南省 湘西土家族苗族自治州

	//海南省 省直津見级行攻区划 东方市
	jjj, kkkk, llll := y.GetProvinceAddress("海南省", "东方市", "东方市")
	fmt.Println("返回：", jjj, kkkk, llll)
	return

	//y.OrderDetail("7075355251")

	//var requestv request.RequestExpress
	//requestv.OrderSn = "7075355251"
	//y.ExpressQuery(requestv)
	//
	//return

	//	y.OrderDetail("6420306427")
	var requesta request.RequestSaleBeforeCheck

	json.Unmarshal([]byte(aaa), &requesta)
	//requesta.Skus = append(requesta.Skus, request.GoodsSpu{
	//	request.Sku{
	//		Sku: 2883156,
	//	},
	//	1,
	//})
	//
	//var nnna request.ReceivingInformation
	//
	//nnna.Phone = "18686794695"
	//nnna.Consignee = "测试人"
	//nnna.Description = "文峰北路1781号三鼎大厦15楼"
	//nnna.Province = "河南省"
	//nnna.City = "许昌市"
	//nnna.Area = "魏都区"
	//nnna.Street = "文峰街道"
	//requesta.Address = nnna

	//err, bbbb := y.GetAreaCodeByAddress("北京市海淀区白家疃东路9号院")
	//
	//fmt.Println(bbbb, err)
	//
	//return

	//y.ExpressQuery("6409731791")
	//y.OrderDetail("6409731791")
	//AA123142353451
	//6409731791
	//
	//	return

	//y.GetAreaCodeByAddress("黑龙江省哈尔滨市南岗区学府经典中天城")
	//
	//return

	var requestParam request.RequestConfirmOrder
	var goods []request.GoodsSpu

	goods = append(goods, request.GoodsSpu{
		Sku:    request.Sku{Sku: 2888888889092875},
		Number: 1,
	})
	//goods = append(goods, request.GoodsSpu{
	//	Sku:    request.Sku{Sku: 563617},
	//	Number: 3,
	//})
	//goods = append(goods, request.GoodsSpu{
	//	Sku:    request.Sku{Sku: 563505},
	//	Number: 4,
	//})
	//goods = append(goods, request.GoodsSpu{
	//	Sku:    request.Sku{Sku: 563514},
	//	Number: 4,
	//})
	var aaanb []string

	aaanb = append(aaanb, "2888888889092875")
	//y.GetAreaAddress("朝阳区")

	var request request.RequestExpress
	request.OrderSn = "182941783286"
	y.ExpressQuery(request)
	return

	//requestParam.OrderSn.OrderSn = "AA12314235345222"
	//requestParam.Skus = goods
	//
	//var nnn request.ReceivingInformation
	//
	//nnn.Phone = "18686794695"
	//nnn.Consignee = "测试人"
	//nnn.Description = "黑龙江省哈尔滨市南岗区学府路1号"
	//nnn.Province = "黑龙江省"
	//nnn.City = "哈尔滨市"
	//nnn.Area = "南岗区"
	//nnn.Street = ""
	//requestParam.Address = nnn
	//request.Skus

	y.ConfirmOrder(requestParam)

	return
	//y.GetYzhCategoryListAll()

	//time.Sleep(time.Second * 50)

	fmt.Println("wanhceng")
	//y.ImportGoodsRun(info)
	//
	//aaan := []uint{}
	//_, dasta := y.GetCategoryDetail(1026, aaan)
	//fmt.Println("aaaaaaa", dasta)

}

func TestYzh_GetYzhGoodsStock(t *testing.T) {

	y := &Yzh{}

	var skuList []string
	skuList = append(skuList, "1125899906848352")

	y.InitSetting(23)

	y.GetGoodsPriceList(skuList)
	return

	for rePost := 0; rePost < 3; rePost++ {
		StockErr, stockStatusList := y.GetYzhGoodsStock(skuList)
		fmt.Println(StockErr)
		if stockStatusList.Success == true {
			break
		}
	}

}

func TestYzh_ExpressQuery(t *testing.T) {
	y := &Yzh{}
	//var info request.GetGoodsSearch
	y.InitSetting(23)
	var request request.RequestExpress
	request.OrderSn = "182941783286"
	_, data := y.ExpressQuery(request)

	strShipmentListData := data.(string)
	var dataaa model2.ShipmentList
	err := json.Unmarshal([]byte(strShipmentListData), &dataaa)
	if err != nil {

	}
	return
}
