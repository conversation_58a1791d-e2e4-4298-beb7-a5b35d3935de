package model

type CancelOrder struct {
	Success bool   `json:"success"`
	Code    string `json:"code"`
	Desc    string `json:"desc"`
	Result  struct {
		TparOrderCode   string `json:"tparOrderCode"`
		ParentOrderCode string `json:"parentOrderCode"`
	} `json:"result"`
	Time int64 `json:"time"`
}

type ResYzhOrder struct {
	Success bool   `json:"success"`
	Code    string `json:"code"`
	Desc    string `json:"desc"`
	Result  struct {
		TparOrderCode   string `json:"tparOrderCode"`
		ParentOrderCode string `json:"parentOrderCode"`
	} `json:"result"`
	Time int64 `json:"time"`
}

type OrderEntry struct {
	ProductId int     `json:"product_id"`
	Num       int     `json:"num"`
	SoldPrice float64 `json:"sold_price"`
}

type YzhAddress struct {
	AddressCode       string `json:"addressCode"`
	AddressName       string `json:"addressName"`
	ParentAddressCode string `json:"parentAddressCode"`
	AddressLevel      int    `json:"addressLevel"`
}

type ShipmentList struct {
	Success bool   `json:"success"`
	Code    string `json:"code"`
	Desc    string `json:"desc"`
	Result  struct {
		TparOrderCode   string `json:"tparOrderCode"`
		ParentOrderCode string `json:"parentOrderCode"`
		ShipmentList    []struct {
			OrderCode       string `json:"orderCode"`
			ExpressName     string `json:"expressName"`
			ExpressCode     string `json:"expressCode"`
			ShipmentCode    string `json:"shipmentCode"`
			LogisticsStatus int    `json:"logisticsStatus"`
			ShipmentItems   []struct {
				GoodsSkuName string `json:"goodsSkuName"`
				GoodsSkuCode string `json:"goodsSkuCode"`
				Qty          int    `json:"qty"`
			} `json:"shipmentItems"`
			ShipmentTrack []struct {
				Content     string `json:"content"`
				Operator    string `json:"operator"`
				OperateTime string `json:"operateTime"`
			} `json:"shipmentTrack"`
		} `json:"shipmentList"`
	} `json:"result"`
	Time int64 `json:"time"`
}

type ResYzhOrderDetail struct {
	Success bool   `json:"success"`
	Code    string `json:"code"`
	Desc    string `json:"desc"`
	Result  []struct {
		TparOrderCode       string      `json:"tparOrderCode"`
		ParentOrderCode     string      `json:"parentOrderCode"`
		ParentOrderAmount   string      `json:"parentOrderAmount"`
		OrderFreightAmount  string      `json:"orderFreightAmount"`
		BuyerMsg            interface{} `json:"buyerMsg"`
		CreateTime          string      `json:"createTime"`
		OrderReceiveAddress struct {
			ReceiveName   string      `json:"receiveName"`
			ReceiveMobile string      `json:"receiveMobile"`
			ReceivePhone  interface{} `json:"receivePhone"`
			ProvinceId    int         `json:"provinceId"`
			ProvinceName  string      `json:"provinceName"`
			CityId        int         `json:"cityId"`
			CityName      string      `json:"cityName"`
			CountyId      int         `json:"countyId"`
			CountyName    string      `json:"countyName"`
			TownId        int         `json:"townId"`
			TownName      string      `json:"townName"`
			Address       string      `json:"address"`
		} `json:"orderReceiveAddress"`
		ChildOrderList []struct {
			OrderCode       string      `json:"orderCode"`
			OrderAmount     string      `json:"orderAmount"`
			OrderStatus     int         `json:"orderStatus"`
			LogisticsStatus interface{} `json:"logisticsStatus"`
			DeliverWay      int         `json:"deliverWay"`
			CreateTime      string      `json:"createTime"`
			DeliverTime     interface{} `json:"deliverTime"`
			ConfirmTime     interface{} `json:"confirmTime"`
			SignTime        interface{} `json:"signTime"`
			OrderGoodsList  []struct {
				GoodsSkuCode       string `json:"goodsSkuCode"`
				GoodsSkuName       string `json:"goodsSkuName"`
				GoodsImageUrl      string `json:"goodsImageUrl"`
				BrandCode          string `json:"brandCode"`
				BrandName          string `json:"brandName"`
				FirstCategoryCode  string `json:"firstCategoryCode"`
				FirstCategoryName  string `json:"firstCategoryName"`
				SecondCategoryCode string `json:"secondCategoryCode"`
				SecondCategoryName string `json:"secondCategoryName"`
				LastCategoryCode   string `json:"lastCategoryCode"`
				LastCategoryName   string `json:"lastCategoryName"`
				SellPrice          string `json:"sellPrice"`
				MarketPrice        string `json:"marketPrice"`
				SellQty            int    `json:"sellQty"`
			} `json:"orderGoodsList"`
		} `json:"childOrderList"`
	} `json:"result"`
	Time int64 `json:"time"`
}

type ResYzhStock struct {
	Success bool   `json:"success"`
	Code    string `json:"code"`
	Desc    string `json:"desc"`
	Result  []struct {
		GoodsSkuCode string `json:"goodsSkuCode"`
		StockStatus  int    `json:"stockStatus"`
		StockNum     int    `json:"stockNum"`
	} `json:"result"`
	Time int64 `json:"time"`
}

type ResYzhAddress struct {
	Success bool         `json:"success"`
	Code    string       `json:"code"`
	Desc    string       `json:"desc"`
	Result  []YzhAddress `json:"result"`
	Time    int64        `json:"time"`
}

type GoodsPrice struct {
	Success bool   `json:"success"`
	Code    string `json:"code"`
	Desc    string `json:"desc"`
	Result  []struct {
		GoodsSkuCode string `json:"goodsSkuCode"`
		SellPrice    string `json:"sellPrice"`
		MarketPrice  string `json:"marketPrice"`
	} `json:"result"`
	Time int64 `json:"time"`
}

type OrderResultData struct {
	OrderSplit        bool         `json:"order_split"`
	OrderKey          string       `json:"order_key"`
	OrderTotalPrice   float64      `json:"order_total_price"`
	OrderProductPrice float64      `json:"order_product_price"`
	OrderShipmentFee  float64      `json:"order_shipment_fee"`
	OrderEntry        []OrderEntry `json:"order_entry"`
}

type OrderRes struct {
	RESPONSESTATUS string          `json:"RESPONSE_STATUS"`
	RESULT_DATA    OrderResultData `json:"RESULT_DATA"`
}

type ReqAddress struct {
	RESPONSESTATUS string `json:"RESPONSE_STATUS"`
	RESULTDATA     struct {
		NationId   int    `json:"nationId"`
		ProvinceId int    `json:"provinceId"`
		CityId     int    `json:"cityId"`
		CountyId   int    `json:"countyId"`
		TownId     int    `json:"townId"`
		Nation     string `json:"nation"`
		Province   string `json:"province"`
		City       string `json:"city"`
		County     string `json:"county"`
		Town       string `json:"town"`
	} `json:"RESULT_DATA"`
}

type OrderDetail struct {
	RESPONSESTATUS string `json:"RESPONSE_STATUS"`
	RESULTDATA     struct {
		ThirdOrder        string  `json:"third_order"`
		Key               string  `json:"key"`
		Status            string  `json:"status"`
		OrderTotalPrice   float64 `json:"order_total_price"`
		OrderProductPrice float64 `json:"order_product_price"`
		OrderShipmentFee  float64 `json:"order_shipment_fee"`
		ReceiverName      string  `json:"receiver_name"`
		Province          int     `json:"province"`
		City              int     `json:"city"`
		County            int     `json:"county"`
		Town              int     `json:"town"`
		Address           string  `json:"address"`
		Mobile            string  `json:"mobile"`
		Email             string  `json:"email"`
		Remark            string  `json:"remark"`
		CreateTime        string  `json:"create_time"`
		OrderEntry        []struct {
			ProductId int     `json:"product_id"`
			Num       int     `json:"num"`
			SoldPrice float64 `json:"sold_price"`
		} `json:"order_entry"`
		CHILDORDERS []struct {
			ThirdOrder        string  `json:"third_order"`
			Key               string  `json:"key"`
			Status            string  `json:"status"`
			OrderTotalPrice   float64 `json:"order_total_price"`
			OrderProductPrice float64 `json:"order_product_price"`
			OrderShipmentFee  float64 `json:"order_shipment_fee"`
			ReceiverName      string  `json:"receiver_name"`
			Province          int     `json:"province"`
			City              int     `json:"city"`
			County            int     `json:"county"`
			Town              int     `json:"town"`
			Address           string  `json:"address"`
			Mobile            string  `json:"mobile"`
			Email             string  `json:"email"`
			Remark            string  `json:"remark"`
			CreateTime        string  `json:"create_time"`
			OrderEntry        []struct {
				ProductId int     `json:"product_id"`
				Num       int     `json:"num"`
				SoldPrice float64 `json:"sold_price"`
			} `json:"order_entry"`
		} `json:"CHILD_ORDERS"`
	} `json:"RESULT_DATA"`
}

type OrderTrack struct {
	RESPONSESTATUS string `json:"RESPONSE_STATUS"`
	RESULTDATA     struct {
		ThirdOrder    string `json:"third_order"`
		ShipmentName  string `json:"shipment_name"`
		ShipmentOrder string `json:"shipment_order"`
	} `json:"RESULT_DATA"`
}
