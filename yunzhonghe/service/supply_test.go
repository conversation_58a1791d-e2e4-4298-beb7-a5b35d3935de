package service

import (
	"encoding/json"
	"fmt"
	"github.com/chenhg5/collection"
	"public-supply/request"
	"testing"
)

func TestGoodsPriceAlertA(t *testing.T) {

}

func TestGoodsPriceAlert(t *testing.T) {
	//aaa:="http://localhost:8888/uploads/file/8fe3ba790ad9332e68790cf822201907_20210824101943.p12"
	//
	//compile := regexp.MustCompile(`uploads.+`)
	//params := compile.FindStringSubmatch(aaa)
	//params1 := compile.FindStringSubmatch(aaa)
	//fmt.Println("数据：",params)
	//fmt.Println("数据1：",params1)

	var aann []int64
	aann = append(aann, 1)
	aann = append(aann, 2)
	aann = append(aann, 5)
	aann = append(aann, 6)

	fmt.Println("33", collection.Collect(aann).Min())

	//GoodsPriceAlert()
}
func TestAbs(t *testing.T) {

	var requestaa request.RequestSaleBeforeCheck
	var address request.ReceivingInformation
	var skusssaaaa request.GoodsSpu
	var skusssaa request.GoodsSpus

	address.Phone = "18686794695"
	address.Province = "黑龙江省"
	address.City = "哈尔滨市"
	address.Area = "南岗区"
	address.Street = "和兴路街道"
	address.Consignee = "芸众测试"
	address.Description = "详细描述"

	skusssaaaa.Sku.Sku = 30286935
	skusssaaaa.Number = 1

	skusssaa = append(skusssaa, skusssaaaa)

	skusssaaaa.Sku.Sku = 30286934
	skusssaaaa.Number = 1

	skusssaa = append(skusssaa, skusssaaaa)

	requestaa.Address = address
	requestaa.Skus = skusssaa

	aaaan, _ := json.Marshal(requestaa)
	fmt.Println("请求数据：", string(aaaan))

	//_, aaa := SaleBeforeCheck(requestaa)

	//fmt.Println("fanhui", aaa)

	//fmt.Println("333")
	//
	//	CreateSupplyOrder(2)
}

func TestGenShortID(t *testing.T) {
	fmt.Println("3336666")

	//BatchGetGoodsDetails("2631893")
	//BatchGetGoodsDetails("1204727,1291549,1291550,1250230,1250231,1250232,1273437,1291551,1291552,1250233,1273436,1204725,1204724,1204719,1204721,1204728,1204758,1204722,1204723,1204726")

}

func TestRun(t *testing.T) {
	//var wg sync.WaitGroup
	//wg.Add(1)
	//var info request.GetGoodsSearch
	//info.Limit=2
	//info.Source=0
	//info.Page=1
	//RunGoodsConcurrent(&wg, info, 1)
	//
	//fmt.Println("33")
}

func TestCategory(t *testing.T) {
	var info = request.GetCategoryChild{}

	info.Pid = 0

	info.Source = 0
	//GetCategoryChild(181, info)
}

func TestSetting(t *testing.T) {
	var aaa request.GetCategorySearch
	aaa.Page = 1
	aaa.Limit = 50
	//GetGroup()
}

func TestOrderBeforCheck(t *testing.T) {

	//OrderBeforCheck()

}

func TestSaleBeforCheck(t *testing.T) {

	//SaleBeforCheck()

}
