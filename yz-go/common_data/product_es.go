package common_data

import (
	"context"
	"yz-go/component/log"
	"yz-go/source"
)

func GetOldProductIndex() string {
	productIndex, err := source.Redis().Get(context.Background(), "productIndex").Result()
	if err != nil {
		es, err := source.ES()
		if err != nil {
			log.Log().Info("es实例化错误")
			return productIndex
		}
		exists, err := es.IndexExists("product0").Do(context.Background())
		if err != nil {
			log.Log().Info("es获取index错误")
			return productIndex
		}
		if exists {
			//0 exists
			productIndex = "0"
			SetProductIndex(productIndex)
		} else {
			//0 not exists
			exists, err := es.IndexExists("product1").Do(context.Background())
			if err != nil {
				log.Log().Info("es获取index错误")
				return productIndex
			}
			if exists {
				//1 exists
				productIndex = "1"
				SetProductIndex(productIndex)
			}
		}
	}
	return productIndex
}
func GetNewProductIndex() string {
	productIndex, _ := source.Redis().Get(context.Background(), "productIndex").Result()
	if productIndex == "0" {
		productIndex = "1"
	} else {
		productIndex = "0"
	}
	return productIndex
}
func SetProductIndex(productIndex string) {
	source.Redis().Set(context.Background(), "productIndex", productIndex, 0)
}
