package sms

import (
	"errors"
	"yz-go/component/log"
	"yz-go/config"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	sdkError "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	sms "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/sms/v20190711"
	"go.uber.org/zap"
)

type Tencentsms struct{}

func (*Tencentsms) SendSms(params config.SmsParam) error {
	/* 必要步骤：
	 * 实例化一个认证对象，入参需要传入腾讯云账户密钥对 secretId 和 secretKey
	 */
	credential := common.NewCredential(
		// os.Getenv("TENCENTCLOUD_SECRET_ID"),
		// os.Getenv("TENCENTCLOUD_SECRET_KEY"),
		config.Config().Tencentsms.SecretId,
		config.Config().Tencentsms.SecretKey,
	)
	/* 非必要步骤:
	 * 实例化一个客户端配置对象，可以指定超时时间等配置 */
	cpf := profile.NewClientProfile()

	/* SDK 默认使用 POST 方法
	 * 如需使用 GET 方法，可以在此处设置，但 GET 方法无法处理较大的请求 */
	//cpf.HttpProfile.ReqMethod = "POST"

	/* SDK 有默认的超时时间，非必要请不要进行调整
	 * 如有需要请在代码中查阅以获取最新的默认值 */
	//cpf.HttpProfile.ReqTimeout = 5

	/* SDK 会自动指定域名，通常无需指定域名，但访问金融区的服务时必须手动指定域名
	 * 例如 SMS 的上海金融区域名为 sms.ap-shanghai-fsi.tencentcloudapi.com */
	//cpf.HttpProfile.Endpoint = "sms.tencentcloudapi.com"

	/* SDK 默认用 TC3-HMAC-SHA256 进行签名，非必要请不要修改该字段 */
	//cpf.SignMethod = "HmacSHA1"

	/* 实例化 SMS 的 client 对象
	 * 第二个参数是地域信息，可以直接填写字符串 ap-guangzhou，或者引用预设的常量 */
	client, _ := sms.NewClient(credential, config.Config().Tencentsms.RegionId, cpf)

	/* 实例化一个请求对象，根据调用的接口和实际情况，可以进一步设置请求参数
	   * 您可以直接查询 SDK 源码确定接口有哪些属性可以设置
	    * 属性可能是基本类型，也可能引用了另一个数据结构
	    * 推荐使用 IDE 进行开发，可以方便地跳转查阅各个接口和数据结构的文档说明 */
	request := sms.NewSendSmsRequest()

	/* 基本类型的设置:
	 * SDK 采用的是指针风格指定参数，即使对于基本类型也需要用指针来对参数赋值。
	 * SDK 提供对基本类型的指针引用封装函数
	 * 帮助链接：
	 * 短信控制台：https://console.cloud.tencent.com/smsv2
	 * sms helper：https://cloud.tencent.com/document/product/382/3773
	 */

	/* 短信应用 ID: 在 [短信控制台] 添加应用后生成的实际 SDKAppID，例如1400006666 */
	request.SmsSdkAppid = common.StringPtr(config.Config().Tencentsms.AppId)
	/* 短信签名内容: 使用 UTF-8 编码，必须填写已审核通过的签名，可登录 [短信控制台] 查看签名信息 */
	request.Sign = common.StringPtr(config.Config().Tencentsms.SignName)
	/* 模板参数: 若无模板参数，则设置为空*/
	request.TemplateParamSet = common.StringPtrs([]string{params.TemplateParam})
	/* 模板 ID: 必须填写已审核通过的模板 ID，可登录 [短信控制台] 查看模板 ID */
	switch params.TemplateCode {
	case "login":
		request.TemplateID = common.StringPtr(config.Config().Tencentsms.LoginTemplateCode)
		break
	case "register":
		request.TemplateID = common.StringPtr(config.Config().Tencentsms.SignTemplateCode)
		break
	case "findPassword":
		request.TemplateID = common.StringPtr(config.Config().Tencentsms.FindpasswordTemplateCode)
		break
	case "balance":
		request.TemplateID = common.StringPtr(config.Config().Tencentsms.BalanceTemplateCode)
		break
	case "send":
		request.TemplateID = common.StringPtr(config.Config().Tencentsms.SendTemplateCode)
		break
	case "recharge":
		request.TemplateID = common.StringPtr(config.Config().Tencentsms.RechargeTemplateCode)
		break
	case "withdraw":
		request.TemplateID = common.StringPtr(config.Config().Tencentsms.WithdrawTemplateCode)
		break
	case "withdrawPayment":
		request.TemplateID = common.StringPtr(config.Config().Tencentsms.WithdrawPaymentTemplateCode)
		break
	case "newOrder":
		request.TemplateID = common.StringPtr(config.Config().Tencentsms.NewOrderTemplateCode)
		break
	case "afterSaleOrder":
		request.TemplateID = common.StringPtr(config.Config().Tencentsms.AfterSaleOrderTemplateCode)
		break
	case "sign":
		request.TemplateID = common.StringPtr(config.Config().Tencentsms.SignCheckTemplateCode)
		break
	case "supplierSign":
		request.TemplateID = common.StringPtr(config.Config().Tencentsms.SupplierSignTemplateCode)
		break
	case "applicationSign":
		request.TemplateID = common.StringPtr(config.Config().Tencentsms.ApplicationSignTemplateCode)
		break
	default:
		request.TemplateID = common.StringPtr(config.Config().Tencentsms.LoginTemplateCode)
		break
	}
	if request.TemplateID == nil || *request.TemplateID == "" {
		log.Log().Error("短信发送失败，模板id未配置", zap.Any("data", request))
		return errors.New("短信发送失败，模板id未配置")
	}
	/* 下发手机号码，采用 e.164 标准，+[国家或地区码][手机号]
	 * 例如+86***********， 其中前面有一个+号 ，86为国家码，***********为手机号，最多不要超过200个手机号*/
	request.PhoneNumberSet = common.StringPtrs([]string{params.PhoneNumber})

	// 通过 client 对象调用想要访问的接口，需要传入请求对象
	_, err := client.SendSms(request)

	// 处理异常
	if _, ok := err.(*sdkError.TencentCloudSDKError); ok {
		log.Log().Error("An API error", zap.Any("err", err.Error()))
		return errors.New(err.Error())
	}
	// 非 SDK 异常，直接失败。实际代码中可以加入其他的处理
	if err != nil {
		log.Log().Error("function client.SendSms() Filed", zap.Any("err", err.Error()))
		return errors.New(err.Error())
	}

	return nil
}

func (*Tencentsms) SendOperationSms(params config.SmsParam) error {
	/* 必要步骤：
	 * 实例化一个认证对象，入参需要传入腾讯云账户密钥对 secretId 和 secretKey
	 */
	credential := common.NewCredential(
		// os.Getenv("TENCENTCLOUD_SECRET_ID"),
		// os.Getenv("TENCENTCLOUD_SECRET_KEY"),
		config.Config().Tencentsms.SecretId,
		config.Config().Tencentsms.SecretKey,
	)
	/* 非必要步骤:
	 * 实例化一个客户端配置对象，可以指定超时时间等配置 */
	cpf := profile.NewClientProfile()

	/* SDK 默认使用 POST 方法
	 * 如需使用 GET 方法，可以在此处设置，但 GET 方法无法处理较大的请求 */
	//cpf.HttpProfile.ReqMethod = "POST"

	/* SDK 有默认的超时时间，非必要请不要进行调整
	 * 如有需要请在代码中查阅以获取最新的默认值 */
	//cpf.HttpProfile.ReqTimeout = 5

	/* SDK 会自动指定域名，通常无需指定域名，但访问金融区的服务时必须手动指定域名
	 * 例如 SMS 的上海金融区域名为 sms.ap-shanghai-fsi.tencentcloudapi.com */
	//cpf.HttpProfile.Endpoint = "sms.tencentcloudapi.com"

	/* SDK 默认用 TC3-HMAC-SHA256 进行签名，非必要请不要修改该字段 */
	//cpf.SignMethod = "HmacSHA1"

	/* 实例化 SMS 的 client 对象
	 * 第二个参数是地域信息，可以直接填写字符串 ap-guangzhou，或者引用预设的常量 */
	client, _ := sms.NewClient(credential, config.Config().Tencentsms.RegionId, cpf)

	/* 实例化一个请求对象，根据调用的接口和实际情况，可以进一步设置请求参数
	   * 您可以直接查询 SDK 源码确定接口有哪些属性可以设置
	    * 属性可能是基本类型，也可能引用了另一个数据结构
	    * 推荐使用 IDE 进行开发，可以方便地跳转查阅各个接口和数据结构的文档说明 */
	request := sms.NewSendSmsRequest()

	/* 基本类型的设置:
	 * SDK 采用的是指针风格指定参数，即使对于基本类型也需要用指针来对参数赋值。
	 * SDK 提供对基本类型的指针引用封装函数
	 * 帮助链接：
	 * 短信控制台：https://console.cloud.tencent.com/smsv2
	 * sms helper：https://cloud.tencent.com/document/product/382/3773
	 */

	/* 短信应用 ID: 在 [短信控制台] 添加应用后生成的实际 SDKAppID，例如1400006666 */
	request.SmsSdkAppid = common.StringPtr(config.Config().Tencentsms.AppId)
	/* 短信签名内容: 使用 UTF-8 编码，必须填写已审核通过的签名，可登录 [短信控制台] 查看签名信息 */
	request.Sign = common.StringPtr(config.Config().Tencentsms.SignName)
	/* 模板参数: 若无模板参数，则设置为空*/
	request.TemplateParamSet = common.StringPtrs(params.TemplateTencentParam)
	/* 模板 ID: 必须填写已审核通过的模板 ID，可登录 [短信控制台] 查看模板 ID */
	switch params.TemplateCode {
	case "login":
		request.TemplateID = common.StringPtr(config.Config().Tencentsms.LoginTemplateCode)
		break
	case "register":
		request.TemplateID = common.StringPtr(config.Config().Tencentsms.SignTemplateCode)
		break
	case "findPassword":
		request.TemplateID = common.StringPtr(config.Config().Tencentsms.FindpasswordTemplateCode)
		break
	case "balance":
		request.TemplateID = common.StringPtr(config.Config().Tencentsms.BalanceTemplateCode)
		break
	case "send":
		request.TemplateID = common.StringPtr(config.Config().Tencentsms.SendTemplateCode)
		break
	case "recharge":
		request.TemplateID = common.StringPtr(config.Config().Tencentsms.RechargeTemplateCode)
		break
	case "accountRecharge":
		request.TemplateID = common.StringPtr(config.Config().Tencentsms.AccountRechargeTemplateCode)
		break
	default:
		request.TemplateID = common.StringPtr(config.Config().Tencentsms.LoginTemplateCode)
		break
	}
	if request.TemplateID == nil || *request.TemplateID == "" {
		log.Log().Error("短信发送失败，模板id未配置", zap.Any("data", request))
		return errors.New("短信发送失败，模板id未配置")
	}
	/* 下发手机号码，采用 e.164 标准，+[国家或地区码][手机号]
	 * 例如+86***********， 其中前面有一个+号 ，86为国家码，***********为手机号，最多不要超过200个手机号*/
	request.PhoneNumberSet = common.StringPtrs([]string{params.PhoneNumber})

	// 通过 client 对象调用想要访问的接口，需要传入请求对象
	_, err := client.SendSms(request)

	// 处理异常
	if _, ok := err.(*sdkError.TencentCloudSDKError); ok {
		log.Log().Error("An API error", zap.Any("err", err.Error()))
		return errors.New(err.Error())
	}
	// 非 SDK 异常，直接失败。实际代码中可以加入其他的处理
	if err != nil {
		log.Log().Error("function client.SendSms() Filed", zap.Any("err", err.Error()))
		return errors.New(err.Error())
	}

	return nil
}
