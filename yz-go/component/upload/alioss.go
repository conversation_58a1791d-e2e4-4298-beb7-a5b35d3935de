package upload

import (
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"yz-go/component/log"
	"yz-go/config"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"go.uber.org/zap"
	"mime/multipart"
	"os"
	"time"
)

type <PERSON><PERSON> struct{}

func (*<PERSON>oss) UploadLocalFile(fileName string, f *os.File) (string, string, error) {
	// 创建OSSClient实例。
	client, err := oss.New(config.Config().Alioss.EndPoint, config.Config().Alioss.AccessId, config.Config().Alioss.AccessSecret)
	if err != nil {
		fmt.Println("Error:", err)
		return "", "", errors.New("请检查配置信息是否正确, err:" + err.Error())
	}

	// 获取存储空间。
	bucket, err := client.Bucket(config.Config().Alioss.Bucket)
	if err != nil {
		fmt.Println("Error:", err)
		return "", "", errors.New("请检查配置信息是否正确, err:" + err.Error())
	}

	year, month, day := time.Now().Date()
	floder := strconv.Itoa(year) + strconv.Itoa(int(month)) + strconv.Itoa(day) + "/"
	fileKey := fmt.Sprintf("%d%s", time.Now().Unix(), fileName) // 文件名格式 自己可以改 建议保证唯一性
	fileKey = floder + fileKey
	// 上传文件流。
	err = bucket.PutObject(fileKey, f)
	if err != nil {
		log.Log().Error("function formUploader.Put() Filed", zap.Any("err", err.Error()))
		return "", "", errors.New("上传文件至对象存储时失败, err:" + err.Error())
	}
	return "https://" + config.Config().Alioss.BucketUrl + "/" + fileKey, fileKey, nil
}
func (*Alioss) UploadFile(file *multipart.FileHeader) (string, string, error) {
	// 创建OSSClient实例。
	client, err := oss.New(config.Config().Alioss.EndPoint, config.Config().Alioss.AccessId, config.Config().Alioss.AccessSecret)
	if err != nil {
		fmt.Println("Error:", err)
		return "", "", errors.New("请检查配置信息是否正确, err:" + err.Error())
	}

	// 获取存储空间。
	bucket, err := client.Bucket(config.Config().Alioss.Bucket)
	if err != nil {
		fmt.Println("Error:", err)
		return "", "", errors.New("请检查配置信息是否正确, err:" + err.Error())
	}

	// 读取本地文件。
	f, openError := file.Open()
	if openError != nil {
		log.Log().Error("function file.Open() Filed", zap.Any("err", openError.Error()))

		return "", "", errors.New("本地文件打开失败, err:" + openError.Error())
	}
	year, month, day := time.Now().Date()
	floder := strconv.Itoa(year) + strconv.Itoa(int(month)) + strconv.Itoa(day) + "/"
	fileKey := fmt.Sprintf("%d%s", time.Now().Unix(), file.Filename) // 文件名格式 自己可以改 建议保证唯一性
	fileKey = floder + fileKey
	// 上传文件流。
	err = bucket.PutObject(fileKey, f)
	if err != nil {
		log.Log().Error("function formUploader.Put() Filed", zap.Any("err", err.Error()))
		return "", "", errors.New("上传文件至对象存储时失败, err:" + err.Error())
	}
	return "https://" + config.Config().Alioss.BucketUrl + "/" + fileKey, fileKey, nil
}

func (*Alioss) DeleteFile(key string) error {
	// 创建OSSClient实例。
	client, err := oss.New(config.Config().Alioss.EndPoint, config.Config().Alioss.AccessId, config.Config().Alioss.AccessSecret)
	if err != nil {
		fmt.Println("Error:", err)
		os.Exit(-1)
	}

	bucketName := config.Config().Alioss.Bucket
	objectName := key

	// 获取存储空间。
	bucket, err := client.Bucket(bucketName)
	if err != nil {
		fmt.Println("Error:", err)
		os.Exit(-1)
	}

	// 删除单个文件。objectName表示删除OSS文件时需要指定包含文件后缀在内的完整路径，例如abc/efg/123.jpg。
	// 如需删除文件夹，请将objectName设置为对应的文件夹名称。如果文件夹非空，则需要将文件夹下的所有object删除后才能删除该文件夹。
	err = bucket.DeleteObject(objectName)
	if err != nil {
		log.Log().Error("function bucketManager.Delete() Filed", zap.Any("err", err.Error()))
		return errors.New("function bucketManager.Delete() Filed, err:" + err.Error())
	}
	return nil
}

// GeneratePresignedUrl 生成阿里云OSS临时访问链接
func (*Alioss) GeneratePresignedUrl(objectKey string, expireTime time.Duration) (string, error) {
	// 创建OSSClient实例
	client, err := oss.New(
		config.Config().Alioss.EndPoint,
		config.Config().Alioss.AccessId,
		config.Config().Alioss.AccessSecret,
	)
	if err != nil {
		log.Log().Error("创建OSS Client失败", zap.Error(err))
		return "", fmt.Errorf("创建OSS Client失败: %w", err)
	}

	// 获取存储空间
	bucket, err := client.Bucket(config.Config().Alioss.Bucket)
	if err != nil {
		log.Log().Error("获取Bucket失败", zap.Error(err))
		return "", fmt.Errorf("获取Bucket失败: %w", err)
	}

	// 生成临时URL，默认方法为GET
	signedURL, err := bucket.SignURL(objectKey, oss.HTTPGet, int64(expireTime.Seconds()))
	if err != nil {
		log.Log().Error("生成临时URL失败", zap.Error(err))
		return "", fmt.Errorf("生成临时URL失败: %w", err)
	}

	return signedURL, nil
}

func (a *Alioss) GetDomain() string {
	return config.Config().Alioss.BucketUrl
}

func (a *Alioss) IsTemporaryUrl(urlStr string) bool {
	u, err := url.Parse(urlStr)
	if err != nil {
		return false
	}

	// 阿里云临时链接特征：
	return u.Query().Get("Expires") != "" &&
		u.Query().Get("OSSAccessKeyId") != "" &&
		u.Query().Get("Signature") != ""
}

func (a *Alioss) ConvertToNormalUrl(urlStr string) (string, error) {
	if urlStr == "" {
		return "", nil
	}

	if !a.IsTemporaryUrl(urlStr) {
		return urlStr, nil
	}

	u, err := url.Parse(urlStr)
	if err != nil {
		return "", fmt.Errorf("invalid URL: %w", err)
	}

	// 提取对象键
	objectKey := strings.TrimPrefix(u.Path, "/")
	domain := a.GetDomain()

	// 如果配置了domain，直接返回永久链接
	if domain != "" {
		return fmt.Sprintf("https://%s/%s", strings.TrimRight(domain, "/"), objectKey), nil
	}

	// 否则生成新的临时链接（更长的有效期）
	return a.GeneratePresignedUrl(objectKey, time.Hour*24*7)
}
