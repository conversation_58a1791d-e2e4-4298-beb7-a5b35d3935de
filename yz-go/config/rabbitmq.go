package config

type Rabbitmq struct {
	Username string `mapstructure:"username" json:"username" yaml:"username"`
	Password string `mapstructure:"password" json:"password" yaml:"password"`
	Addr     string `mapstructure:"addr" json:"addr" yaml:"addr"`
	Host     string `mapstructure:"host" json:"host" yaml:"host"`
}

type RabbitmqRemote struct {
	Username string `mapstructure:"username" json:"username" yaml:"username"`
	Password string `mapstructure:"password" json:"password" yaml:"password"`
	Addr     string `mapstructure:"addr" json:"addr" yaml:"addr"`
	Host     string `mapstructure:"host" json:"host" yaml:"host"`
}
