package config

type Wechat struct {
	AppId          string `mapstructure:"appid" json:"appid" yaml:"appid"`
	MchId          string `mapstructure:"mchid" json:"mchid" yaml:"mchid"`
	ApiSecret          string `mapstructure:"apisecret" json:"apisecret" yaml:"apisecret"`
	Cert          string `mapstructure:"cert" json:"cert" yaml:"cert"`
	Key          string `mapstructure:"key" json:"key" yaml:"key"`
	P12          string `mapstructure:"p12" json:"p12" yaml:"p12"`
	NotifyUrl          string `mapstructure:"notifyurl" json:"notifyurl" yaml:"notifyurl"`
	RefundNotifyUrl          string `mapstructure:"refundnotifyurl" json:"refundnotifyurl" yaml:"refundnotifyurl"`
	CertPath      string `mapstructure:"certpath" json:"certpath" yaml:"certpath"`

}
