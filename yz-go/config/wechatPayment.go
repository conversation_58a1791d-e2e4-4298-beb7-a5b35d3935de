package config

type WechatPayment struct {
	IsWxPayOpen                int    `mapstructure:"iswxpayopen" json:"iswxpayopen" yaml:"iswxpayopen"`                                                       //微信支付状态1开始2关闭
	IsWxH5Open                 int    `mapstructure:"iswxh5open" json:"iswxh5open" yaml:"iswxh5open"`                                                          //微信H5状态1开始2关闭
	IsWxCodeOpen                 int    `mapstructure:"iswxcodeopen" json:"iswxcodeopen" yaml:"iswxcodeopen"`                                                  //微信扫码支付1开启2关闭
	AppId                      string `mapstructure:"appid" json:"appid" yaml:"appid"`                                                                         //AppID
	AppSecret                  string `mapstructure:"appsecret" json:"appsecret" yaml:"appsecret"`                                                             //AppSecret
	Mchid                      string `mapstructure:"mch_id" json:"mch_id" yaml:"mch_id"`                                                                      //支付商户号
	PayKey                     string `mapstructure:"pay_key" json:"pay_key" yaml:"pay_key"`                                                                   //支付密钥
	CertPath                   string `mapstructure:"cert_path" json:"cert_path" yaml:"cert_path"`                                                             //cert证书文件路径
	KeyPath                    string `mapstructure:"key_path" json:"key_path" yaml:"key_path"`                                                                //cert证书文件路径
	MchCertificateSerialNumber string `mapstructure:"mch_certificate_serial_number" json:"mch_certificate_serial_number" yaml:"mch_certificate_serial_number"` //证书编号
	Cert                       string `mapstructure:"cert" json:"cert" yaml:"cert"` // 微信平台证书                                                      //平台证书路径
	Serial                     string `mapstructure:"serial" json:"serial" yaml:"serial"` //微信平台证书序列号

}
