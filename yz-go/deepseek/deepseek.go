package deepseek

import (
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
)

type Client struct {
	APIKey string
	URL    string
}

func NewClient(apiKey string) *Client {
	return &Client{
		APIKey: apiKey,
		URL:    "https://api.deepseek.com/v1/completions",
	}
}

func (c *Client) Generate(prompt string, maxTokens int, temperature float64) (string, error) {
	url := c.URL

	payload := strings.NewReader("{\n        \"model\": \"deepseek-chat\",\n        \"messages\": [\n          {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},\n          {\"role\": \"user\", \"content\": \"" + prompt + "\"}\n        ],\n        \"stream\": false\n      }")

	//payload := gin.H{
	//	"model": "deepseek-chat",
	//	"messages": []gin.H{
	//		{"role": "system", "content": "You are a helpful assistant."},
	//		{"role": "user", "content": prompt},
	//	},
	//	"stream": false,
	//}

	req, _ := http.NewRequest("POST", url, strings.NewReader(fmt.Sprintf("%v", payload)))

	req.Header.Add("Authorization", "Bearer "+c.APIKey)
	req.Header.Add("Content-Type", "application/json")

	res, _ := http.DefaultClient.Do(req)

	defer res.Body.Close()
	body, _ := ioutil.ReadAll(res.Body)

	fmt.Println(res)
	fmt.Println(string(body))
	return string(body), nil
}
