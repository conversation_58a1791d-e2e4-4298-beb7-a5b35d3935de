package model

import "yz-go/source"

type Notification struct {
	source.Model
	Title    string `json:"title" form:"title" gorm:"column:title;"`
	Body     string `json:"body" form:"body" gorm:"column:body;type:text;"`
	Read     int    `json:"read" form:"read" gorm:"column:read;default:0;comment:0未读，1已读;type:tinyint(1)"`
	Notified int    `json:"notified" form:"notified" gorm:"column:notified;default:0;comment:0未通知，1已通知;type:tinyint(1)"`
}
