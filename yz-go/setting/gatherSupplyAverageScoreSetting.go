package setting

import (
	"database/sql/driver"
	"encoding/json"
	"yz-go/model"
	"yz-go/source"
)

type YytUpdate struct {
	GatherSupplyId uint `json:"gather_supply_id"`
}

// 供应链平均分存储
type GatherSupplyAverageScoreSetting struct {
	model.SysSetting
	Value Value `json:"value"`
}
type Value struct {
	DescribeScore                float64              `mapstructure:"describe_score" json:"describe_score" yaml:"describe_score"`
	ServiceScore                 float64              `mapstructure:"service_score" json:"service_score" yaml:"service_score"`
	ShoppingScore                float64              `mapstructure:"shopping_score" json:"shopping_score" yaml:"shopping_score"`
	GoodsCount                   int                  `mapstructure:"goods_count" json:"goods_count" yaml:"goods_count"`
	HotSale                      int                  `mapstructure:"hot_sales" json:"hot_sales" yaml:"hot_sales"`
	ShopStoreLicenses            model.JsonStringList `json:"ShopStoreLicenses"`               //营业执照
	ShopAnnouncement             string               `json:"shop_announcement"`               //店铺公告
	ShopStoreReturnsInstructions string               `json:"shop_store_returns_instructions"` //退换货说明
	ShopLogoSquare               string               `json:"shop_logo_square"`                //供应链logo 方形
}

//yzModel.JsonStringList

func (GatherSupplyAverageScoreSetting) TableName() string {
	return "sys_settings"
}
func (value Value) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *Value) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

// KEY = GatherSupplyAverageScore + 供应链id
func GetGatherSupplyAverageScoreSetting(key string) (err error, sysSetting GatherSupplyAverageScoreSetting) {
	err = source.DB().Where("`key` = ?", key).First(&sysSetting).Error
	return
}
func SetGatherSupplyAverageScoreSetting(data GatherSupplyAverageScoreSetting) (err error) {
	if data.ID == 0 {
		err = source.DB().Create(&data).Error
		if err != nil {
			return
		}
	} else {
		var _, gatherSupplyAverageScoreSetting = GetGatherSupplyAverageScoreSetting(data.Key)
		data.CreatedAt = gatherSupplyAverageScoreSetting.CreatedAt
		err = source.DB().Save(&data).Error
		if err != nil {
			return
		}
	}

	return
}
