package utils

import (
	"fmt"
	"os"
)

const (
	ConfigFile = "config.yaml"
	RunPathEnv = "GVA_RUN_PATH"
	BaseApiEnv = "BASE_API"
)

var runPath *string

func GetRunPath() string {
	if runPath == nil {
		_runPath := getRunPath()
		runPath = &_runPath
	}
	return *runPath
}
func SetRunPath(path string) {
	runPath = &path
}
func getRunPath() (runPath string) {

	runPath = os.Getenv(RunPathEnv)
	if runPath != "" {
		fmt.Printf("您正在使用env传递的值,运行路径为%v\n", runPath)

		return runPath
	}
	fmt.Printf("您正在使用默认运行路径,运行路径为%v\n", runPath)

	return runPath
}
