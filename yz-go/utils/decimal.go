package utils

import (
	"fmt"
	"strconv"
)

func Decimal(value float64) float64 {
	var err error
	value, err = strconv.ParseFloat(fmt.Sprintf("%0.2f", value), 64)
	if err != nil {
		return 0
	}
	return value
}

func DecimalPointOne(value float64) float64 {
	var err error
	value, err = strconv.ParseFloat(fmt.Sprintf("%0.1f", value), 64)
	if err != nil {
		return 0
	}
	return value
}

func ExecProfitRate(price1 uint, price2 uint) float64 {
	if price1 == 0 {
		return 0
	}
	return DecimalPointOne(((float64(price1) - float64(price2)) / float64(price1)) * 100)

}
