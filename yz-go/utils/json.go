package utils

import (
	"encoding/json"
	"strings"
)

func IsJSON(str string) bool {
	str = strings.TrimSpace(str)
	if str == "" {
		return false
	}

	// 检查是否以 { 开头和 } 结尾，或者以 [ 开头和 ] 结尾
	if !((strings.HasPrefix(str, "{") && strings.HasSuffix(str, "}")) ||
		(strings.HasPrefix(str, "[") && strings.HasSuffix(str, "]"))) {
		return false
	}

	var js json.RawMessage
	return json.Unmarshal([]byte(str), &js) == nil
}
