package utils

import (
	"github.com/hetiansu5/urlquery"
	"os"
	"strings"
)

var BaseApi string

func init() {
	if os.Getenv(BaseApiEnv) != "" {
		BaseApi = os.Getenv(BaseApiEnv)
	} else {
		BaseApi = "http://127.0.0.1:8888/"
	}
}
func Url(api string, request interface{}) (err error, url string) {
	if request == "" {
		url = BaseApi + strings.Trim(api, "/")
		return
	}
	bytes, err := urlquery.Marshal(request)
	if err != nil {
		return
	}

	url = BaseApi + strings.Trim(api, "/") + "?" + string(bytes)
	return
}

func UnSafeUrl(api string, request interface{}) string {
	if request == "" {
		return BaseApi + strings.Trim(api, "/")
	}
	bytes, _ := urlquery.Marshal(request)

	return BaseApi + strings.Trim(api, "/") + "?" + string(bytes)
}
func UnSafeUri(api string, request interface{}) string {
	if request == "" {
		return strings.Trim(api, "/")
	}
	bytes, _ := urlquery.Marshal(request)

	return strings.Trim(api, "/") + "?" + string(bytes)
}
